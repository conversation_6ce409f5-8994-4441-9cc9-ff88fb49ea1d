# Cash Flow Period Report - Dual Grouping Implementation

## Overview
Đã thay đổi logic báo cáo cash flow period để hỗ trợ **CẢ 2 CÁCH** group dữ liệu:
1. **Department-first**: Group theo `department_id` trướ<PERSON>, rồi mới tới `category_id` 
2. **Category-first**: Group theo `category_id` trước, rồi mới tới `department_id`

Điều này cho phép người dùng có thể phân tích dữ liệu từ 2 góc độ khác nhau tùy theo nhu cầu sử dụng.

## Database Schema Requirements
Sử dụng table `department` riêng biệt:
```sql
create table department
(
    id          bigint generated by default as identity primary key,
    created_at  timestamp with time zone not null,
    updated_at  timestamp with time zone not null,
    name        varchar                  not null,
    description varchar
);
```

Field `department_id` trong table `cash_flow_item` được handle với `COALESCE(cfi.department_id, 0)` để tránh NULL values.

## Files Changed

### 1. SQL Queries
- **T<PERSON><PERSON> mới**: `bob/queries/cash_flow_income_by_department.sql`
  - Group: department → category
  - Join với table `department` cho department info
  - Join với table `term` cho category info
  
- **Tạo mới**: `bob/queries/cash_flow_expense_by_department.sql`
  - Group: department → category
  - Join với table `department` cho department info
  - Join với table `term` cho category info
  
- **Tạo mới**: `bob/queries/cash_flow_income_by_category_department.sql`
  - Group: category → department
  - Join với table `term` cho category info
  - Join với table `department` cho department info
  
- **Tạo mới**: `bob/queries/cash_flow_expense_by_category_department.sql`
  - Group: category → department
  - Join với table `term` cho category info
  - Join với table `department` cho department info

### 2. Go Generated Files (từ SQL)
- **Tự động generate**: `bob/queries/cash_flow_income_by_department.bob.go`
- **Tự động generate**: `bob/queries/cash_flow_expense_by_department.bob.go`
- **Tự động generate**: `bob/queries/cash_flow_income_by_category_department.bob.go`
- **Tự động generate**: `bob/queries/cash_flow_expense_by_category_department.bob.go`

### 3. DTO Changes
**File**: `api/internal/dto/cash_flow_dto.go`

**Thêm mới cho Department-first grouping:**
```go
type DepartmentCategorySummaryOutput struct {
    CategoryID   int    `json:"category_id"`
    CategoryName string `json:"category_name"`
    Amount       int    `json:"amount"`
    Count        int    `json:"count"`
}

type DepartmentSummaryOutput struct {
    DepartmentID   int                                `json:"department_id"`
    DepartmentName string                             `json:"department_name"`
    TotalAmount    int                                `json:"total_amount"`
    TotalCount     int                                `json:"total_count"`
    Categories     []*DepartmentCategorySummaryOutput `json:"categories"`
}
```

**Thêm mới cho Category-first grouping:**
```go
type CategoryDepartmentSummaryOutput struct {
    DepartmentID   int    `json:"department_id"`
    DepartmentName string `json:"department_name"`
    Amount         int    `json:"amount"`
    Count          int    `json:"count"`
}

type CategorySummaryDepartmentOutput struct {
    CategoryID     int                                 `json:"category_id"`
    CategoryName   string                              `json:"category_name"`
    TotalAmount    int                                 `json:"total_amount"`
    TotalCount     int                                 `json:"total_count"`
    Departments    []*CategoryDepartmentSummaryOutput  `json:"departments"`
}
```

**Cập nhật PeriodSummaryOutput:**
```go
type PeriodSummaryOutput struct {
    // ... existing fields ...
    
    // Department-first grouping (department → category)
    IncomeByDepartment  []*DepartmentSummaryOutput        `json:"income_by_department"`
    ExpenseByDepartment []*DepartmentSummaryOutput        `json:"expense_by_department"`
    
    // Category-first grouping (category → department)  
    IncomeByCategory    []*CategorySummaryDepartmentOutput `json:"income_by_category"`
    ExpenseByCategory   []*CategorySummaryDepartmentOutput `json:"expense_by_category"`
}
```

### 4. Model Logic Changes
**File**: `api/internal/model/cash_flow_model.go`

**Cập nhật `generatePeriodSummary()` method:**
- Thực hiện **4 queries parallel**:
  - `CashFlowIncomeByDepartment` (department → category)
  - `CashFlowExpenseByDepartment` (department → category)
  - `CashFlowIncomeByCategoryDepartment` (category → department)
  - `CashFlowExpenseByCategoryDepartment` (category → department)
- Tạo cả 2 loại summary data structures

**Thêm mới helper methods:**
- `groupIncomeByDepartment()` - Transform department-first data
- `groupExpenseByDepartment()` - Transform department-first data
- `groupIncomeByCategoryDepartment()` - Transform category-first data
- `groupExpenseByCategoryDepartment()` - Transform category-first data

### 5. API Definition Changes  
**File**: `api/doc/cash_flow.api`

**Thêm mới types:**
```go
// Department-first grouping types
DepartmentCategorySummary {
    CategoryId int `json:"category_id"`
    CategoryName string `json:"category_name"`
    Amount int `json:"amount"`
    Count int `json:"count"`
}

DepartmentSummary {
    DepartmentId int `json:"department_id"`
    DepartmentName string `json:"department_name"`
    TotalAmount int `json:"total_amount"`
    TotalCount int `json:"total_count"`
    Categories []DepartmentCategorySummary `json:"categories"`
}

// Category-first grouping types
CategoryDepartmentSummary {
    DepartmentId int `json:"department_id"`
    DepartmentName string `json:"department_name"`
    Amount int `json:"amount"`
    Count int `json:"count"`
}

CategorySummaryDepartment {
    CategoryId int `json:"category_id"`
    CategoryName string `json:"category_name"`
    TotalAmount int `json:"total_amount"`
    TotalCount int `json:"total_count"`
    Departments []CategoryDepartmentSummary `json:"departments"`
}
```

**Cập nhật PeriodSummary:**
```go
PeriodSummary {
    // ... existing fields ...
    
    // Department-first grouping (department → category)
    IncomeByDepartment []DepartmentSummary `json:"income_by_department"`
    ExpenseByDepartment []DepartmentSummary `json:"expense_by_department"`
    
    // Category-first grouping (category → department)
    IncomeByCategory []CategorySummaryDepartment `json:"income_by_category"`
    ExpenseByCategory []CategorySummaryDepartment `json:"expense_by_category"`
}
```

### 6. Generated Types
**File**: `api/internal/types/types.go` (auto-generated)
- Tất cả types mới được generate tự động từ API definition
- Go-zero tạo ra các struct tương ứng với JSON tags

## Dual Data Structure Examples

### Department-first Structure (department → category)
```json
{
  "income_by_department": [
    {
      "department_id": 1,
      "department_name": "Phòng Kinh Doanh",
      "total_amount": 1500000,
      "total_count": 8,
      "categories": [
        {
          "category_id": 1,
          "category_name": "Doanh thu dịch vụ",
          "amount": 1000000,
          "count": 5
        },
        {
          "category_id": 2,
          "category_name": "Doanh thu khác",
          "amount": 500000,
          "count": 3
        }
      ]
    }
  ]
}
```

**Use case**: Khi muốn xem tổng quan từng phòng ban đóng góp bao nhiêu, chi tiết theo từng loại dịch vụ.

### Category-first Structure (category → department)
```json
{
  "income_by_category": [
    {
      "category_id": 1,
      "category_name": "Doanh thu dịch vụ",
      "total_amount": 1200000,
      "total_count": 7,
      "departments": [
        {
          "department_id": 1,
          "department_name": "Phòng Kinh Doanh",
          "amount": 1000000,
          "count": 5
        },
        {
          "department_id": 2,
          "department_name": "Phòng Kỹ Thuật",
          "amount": 200000,
          "count": 2
        }
      ]
    }
  ]
}
```

**Use case**: Khi muốn xem từng loại dịch vụ/chi phí có tổng doanh thu/chi phí bao nhiêu, được tạo bởi phòng ban nào.

## Complete API Response Structure
```json
{
  "current_period": {
    "period": "2024-W30",
    "period_label": "Tuần 30 (17-23/07/2024)",
    "start_date": "2024-07-17T00:00:00Z",
    "end_date": "2024-07-23T23:59:59Z",
    "total_income": 5000000,
    "total_expense": 3000000,
    "net_amount": 2000000,
    "transaction_count": 45,
    "total_cash": 1500000,
    "total_credit_card": 1000000,
    "total_mpos": 500000,
    "total_bank": 1500000,
    "total_momo": 500000,
    
    // Department-first grouping (department → category)
    "income_by_department": [...],
    "expense_by_department": [...],
    
    // Category-first grouping (category → department)  
    "income_by_category": [...],
    "expense_by_category": [...]
  },
  "previous_periods": [...],
  "period_type": "week",
  "generated_at": "2024-07-28T15:30:00Z"
}
```

## Technical Implementation Details

### SQL Query Strategy
1. **4 separate queries** chạy parallel cho performance tối ưu
2. **COALESCE handling** cho department_id NULL values
3. **Proper JOINs** với table department và term
4. **Consistent ordering** theo name và amount DESC

### Data Transformation Flow
1. Raw SQL results → Go structs (auto-generated)
2. Group by first dimension (department hoặc category)
3. Aggregate totals và collect second dimension items
4. Convert map to slice với sorted order

### Performance Considerations
- **Parallel queries**: Tất cả queries chạy đồng thời
- **Single transaction**: Đảm bảo data consistency
- **Efficient grouping**: Sử dụng maps cho O(1) lookup
- **Memory efficient**: Không duplicate data

## Impact Assessment

### ✅ Benefits
- **Dual Perspective**: Hỗ trợ cả 2 cách phân tích data
- **Flexible Reporting**: Users chọn view phù hợp với use case
- **Complete Information**: Không mất thông tin trong quá trình transform
- **Performance**: Queries chạy parallel hiệu quả

### ✅ Compatibility
- **Backward Compatible**: API endpoint giữ nguyên
- **Additive Changes**: Chỉ thêm fields mới, không xóa fields cũ
- **No Breaking Changes**: Existing clients vẫn hoạt động bình thường

### ✅ Quality Assurance
- **Code Compile**: Tất cả code build thành công
- **Type Safety**: Strong typing với Go structs
- **Error Handling**: Proper error propagation
- **SQL Injection Safe**: Parameterized queries

## Usage Guidelines

### For Frontend Developers
```javascript
// Sử dụng department-first view
const departmentAnalysis = response.current_period.income_by_department;
departmentAnalysis.forEach(dept => {
  console.log(`${dept.department_name}: ${dept.total_amount}`);
  dept.categories.forEach(cat => {
    console.log(`  - ${cat.category_name}: ${cat.amount}`);
  });
});

// Sử dụng category-first view  
const categoryAnalysis = response.current_period.income_by_category;
categoryAnalysis.forEach(cat => {
  console.log(`${cat.category_name}: ${cat.total_amount}`);
  cat.departments.forEach(dept => {
    console.log(`  - ${dept.department_name}: ${dept.amount}`);
  });
});
```

### API Endpoint
- **URL**: `POST /v1/cash-flow/period-report`
- **Request**: Unchanged
- **Response**: Enhanced với 4 fields grouping mới
- **Performance**: Optimal với parallel queries

## Conclusion
Implementation này cung cấp solution hoàn chỉnh cho dual grouping requirements, đảm bảo tính linh hoạt cao cho users trong việc phân tích dữ liệu cash flow theo nhiều góc độ khác nhau. 