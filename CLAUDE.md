# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Essential Development Commands

### Setup & Installation
```bash
# Install custom goctl (go-zero CLI)
go install github.com/blazy-vn/goctl@1.2.2
go install github.com/blazy-vn/goctl-swagger@1.0.0
```

### Code Generation
```bash
# Generate API code from .api files (primary development command)
goctl api go -api ./api/doc/bcare.api --dir ./api --style=go_zero --remote https://github.com/blazy-vn/go-zero-template

# Generate Ent ORM code (run after schema changes)
go generate ./ent

# Generate TypeScript client
goctl api ts -api ./api/doc/bcare.api --dir ./ts

# Generate Swagger documentation
goctl api plugin -plugin goctl-swagger="swagger -filename bcare.json -host localhost:8888 -basepath /" -api ./api/doc/bcare.api -dir ./swagger

# Generate Protocol Buffers
protoc --proto_path=./api/proto --go_out=./api/protobuf --go_opt=paths=source_relative --go-grpc_out=./api/protobuf --go-grpc_opt=paths=source_relative ./api/proto/*.proto
```

### Testing & Building
```bash
# Run tests
go test ./...

# Build with Docker
DOCKER_BUILDKIT=1 docker build . -t bcare

# Generate PostgreSQL models (example)
goctl model pg datasource --url="postgres://user:pass@host:port/db?sslmode=disable" --schema="core" --table="*" --dir "./api/internal/model"
```

## Architecture Overview

### Tech Stack
- **Framework**: go-zero (zeromicro/go-zero) - microservice framework
- **ORM**: Ent (entgo.io/ent) - Facebook's ORM for Go
- **Database**: PostgreSQL with `core` schema
- **Task Queue**: Asynq (Redis-based background jobs)
- **Auth**: Casbin ABAC + JWT with refresh tokens
- **Real-time**: Custom WebSocket hub implementation

### Layer Architecture (STRICT)
```
Logic Layer (API/Controller) → Model Layer (Service/Repository) → Ent Layer (ORM/DB)
```

**Critical Rules:**
- Logic layer: HTTP handling, authorization, DTO mapping only. NO business logic, NO direct ent calls
- Model layer: ALL business logic and data access. Uses DTOs, returns `*berr.CodeError`
- Never bypass layers - Logic must call Model, Model calls Ent

### Key Directories
- `api/doc/*.api` - API definitions (source of truth)
- `api/internal/handler/` - HTTP route handlers
- `api/internal/logic/` - API controllers (authorization + DTO mapping)
- `api/internal/model/` - Business logic and data access layer
- `api/internal/auth/` - Authorization logic per entity
- `api/internal/svc/` - Service context and dependency injection
- `ent/schema/` - Database entity definitions
- `common/` - Shared utilities (berr, bquery, cast, eventbus)

### Error Handling (MANDATORY)
- Use only standardized errors from `common/berr`
- Model layer must wrap all errors: `berr.ErrType.Wrap(err).Op(op)`
- Logic layer never wraps Model errors, just returns them
- Every method needs `op := "StructName.MethodName"` declaration

### Data Conversion
- Use `common/cast` helpers consistently
- Request → Input DTO: `cast.ReqToModelInput`
- Ent → Output DTO: `cast.ConvertViaJson`
- Input DTO → Ent: `cast.InputToEnt`

### Logging Rules
- Model layer: Log successful CUD operations (Info level) and conversion errors (Error level) only
- Logic layer: NO logging
- Include `op`, entity ID, user ID in success logs

### Event System
- Custom event bus in `common/eventbus/`
- Event handlers in `api/internal/eventhandler/`
- Used for entity history tracking and async processing

### Development Workflow
1. Define/modify API in `api/doc/*.api`
2. Run `goctl api go` to generate types and handlers
3. Create/update Ent schema in `ent/schema/`
4. Run `go generate ./ent` to generate ORM code
5. Implement Model layer (DTOs + business logic)
6. Add Model to ServiceContext
7. Implement Logic layer (authorization + DTO mapping)
8. Define authorization rules in `api/internal/auth/`

**IMPORTANT:** Handler routes in `api/internal/handler/routes.go` are automatically managed by code generation. DO NOT manually edit route registrations - they are auto-generated from `.api` files.

### Module Development Reference Files
- **Model Layer Example**: `api/internal/model/example.xml` (contains tag_model.go structure)
- **Logic Layer Example**: `api/internal/logic/tag/example.xml` (contains tag logic files)
- **Error Patterns**: `common/berr/errors.go`

### Important File Guidelines
- **Schema Definitions**: Only read files in `/ent/schema/` - all other `/ent/` files are auto-generated
- **API Payloads**: Check `.api` files in `/api/doc/` instead of `/api/internal/types/types.go` (auto-generated and large)
- **Auto-Generated Files to Avoid**: `/ent/` (except schema), `/api/internal/types/types.go`, `/api/internal/handler/routes.go`, handler stubs
- **Route Management**: Routes are automatically generated from `.api` files - never manually edit `routes.go`

### Code Generation Patterns
When creating new modules, follow the exact patterns from tag module:
- DTOs with Input/Output structs and Modified field for updates
- Model methods with proper op declaration, cast usage, berr error handling
- Logic methods with auth checks, DTO mapping, and no business logic
- NO comments in generated/refactored code