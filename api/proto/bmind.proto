syntax = "proto3";

package ai;
option go_package = "github.com/blazy/bcare/api/protobuf";

// Dịch vụ chung
service AIService {
  rpc GetCompletion (CompletionRequest) returns (CompletionResponse) {}
  rpc SpeechToText (SpeechToTextRequest) returns (SpeechToTextResponse) {}
}

// Dịch vụ cho OpenAI
service OpenAIService {
  rpc GetCompletion (CompletionRequest) returns (CompletionResponse) {}
  rpc SpeechToText (SpeechToTextRequest) returns (SpeechToTextResponse) {}
}

// Dịch vụ cho Anthropic
service AnthropicService {
  rpc GetCompletion (CompletionRequest) returns (CompletionResponse) {}
  // Không hỗ trợ SpeechToText cho Anthropic
}

// Dịch vụ cho Local Model
service LocalModelService {
  rpc GetCompletion (CompletionRequest) returns (CompletionResponse) {}
  rpc SpeechToText (SpeechToTextRequest) returns (SpeechToTextResponse) {}
}

message CompletionRequest {
  string prompt = 1;
  int32 max_tokens = 2;
  string model = 3;
  string provider = 4;
  string system_instruction = 5;
}

message CompletionResponse {
  int32 error = 1;
  string data = 2;
}

message SpeechToTextRequest {
  string audio_file_path = 1;
  string model = 2;
  string provider = 3;
}

message SpeechToTextResponse {
  string text = 1;
}