type (
    Operation {
        Id int `json:"id"`
        Name string `json:"name"`
        Group []string `json:"group"`
        Duration int `json:"duration"`
        Status int `json:"status"`
        Version int `json:"version"`
        CreatedAt string `json:"created_at"`
        UpdatedAt string `json:"updated_at"`
        DeletedAt string `json:"deleted_at"`
    }

    OperationAddRequest {
        Name string `json:"name"`
        Group []string `json:"group"`
        Duration int `json:"duration"`
    }

    OperationUpdateRequest {
        Id int `json:"id"`
        Name string `json:"name,optional"`
        Group []string `json:"group,optional"`
        Duration int `json:"duration,optional"`
        Modified []string `json:"modified,optional"`
    }

    OperationGetRequest {
        Id int `json:"id"`
    }

    OperationDeleteRequest {
        Id int `json:"id"`
    }

    OperationListRequest {
        PageSize int `json:"page_size,optional"`
        Page int `json:"page,optional"`
        Filter OperationFilter `json:"filter,optional"`
        Search string `json:"search,optional"`
        OrderBy string `json:"order_by,optional"`
    }

    OperationFilter {
        Name string `json:"name,optional"`
        Group string `json:"group,optional"`
        Status int `json:"status,range=[0:10],optional"`
    }

    OperationResponse {
        Operation
        ProductOperation []ProductOperation `json:"product_operation"`
        OperationMaterial []OperationMaterial `json:"operation_materials"`
    }

    OperationAllResponse {
        Operations []OperationResponse `json:"operations"`
    }

    OperationListResponse {
        Operations []OperationResponse `json:"operations"`
        Total int `json:"total"`
        TotalPage int `json:"total_page"`
    }

)

@server(
    jwt: Auth
    group: operation
    prefix: /v1/operation
)

service bcare-api {
    @handler AddHandler
    post /add (OperationAddRequest) returns (OperationResponse);

    @handler GetHandler
    post /get (OperationGetRequest) returns (OperationResponse);

    @handler ListHandler
    post /list (OperationListRequest) returns (OperationListResponse);

    @handler UpdateHandler
    post /update (OperationUpdateRequest) returns (OperationResponse);

    @handler DeleteHandler
    post /delete (OperationDeleteRequest) returns ();

    @handler AllHandler
    post /all () returns (OperationAllResponse);
}