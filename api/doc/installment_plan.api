info(
    desc: "installment plan api"
    author: "lamvh"
    email: "<EMAIL>"
)

type (
    InstallmentPlan {
        Id int `json:"id"`
        PersonId int `json:"person_id"`
        DealId int `json:"deal_id"`
        UserId int `json:"user_id"`
        BillId *int `json:"bill_id"`          // Thêm trường bill_id
        Name string `json:"name"`
        TotalAmount float64 `json:"total_amount"`
        DownPayment float64 `json:"down_payment"`
        PaidAmount float64 `json:"paid_amount"`
        TotalInstallments int `json:"total_installments"`
        DiscountAmount float64 `json:"discount_amount"`
        PaidInstallmentCount int `json:"paid_installment_count"`
        RefundAmount float64 `json:"refund_amount"`
        State string `json:"state"`
        Status int `json:"status"`
        CreatedAt string `json:"created_at"`
        UpdatedAt string `json:"updated_at"`
        DeletedAt string `json:"deleted_at"`
        Version int `json:"version"`
    }

    InstallmentPlanAddRequest {
        PersonId int `json:"person_id"`
        DealId int `json:"deal_id,optional"`
        UserId int `json:"user_id,optional"`
        BillId *int `json:"bill_id,optional"`  // Thêm trường bill_id
        Name string `json:"name,optional"`
        State string `json:"state,optional"`
        TotalAmount float64 `json:"total_amount,optional"`
        DownPayment float64 `json:"down_payment,optional"`
        TotalInstallments int `json:"total_installments,optional"`
    }

    InstallmentPlanGetRequest {
        Id int `json:"id"`
    }

    InstallmentPlanListRequest {
        PageSize int `json:"page_size,optional"`
        Page int `json:"page,optional"`
        Filter InstallmentPlanFilter `json:"filter,optional"`
        OrderBy string `json:"order_by,optional"`
    }

    InstallmentPlanFilter {
        PersonId int `json:"person_id,optional"`
        DealId int `json:"deal_id,optional"`
        UserId int `json:"user_id,optional"`
        BillId *int `json:"bill_id,optional"`  // Thêm trường bill_id
    }

    InstallmentPlanResponse {
        InstallmentPlan
        Person Person `json:"person"`
        Deal Deal `json:"deal"`
        Creator UserShort `json:"creator"`
        Bill *Bill `json:"bill"`              // Thêm trường bill
        Installment []InstallmentResponse `json:"installments"`
    }

    InstallmentPlanListResponse {
        InstallmentPlans []InstallmentPlanResponse `json:"installment_plans"`
        Total int `json:"total"`
        TotalPage int `json:"total_page"`
    }

    InstallmentPlanUpdateRequest {
        Id int `json:"id"`
        Name string `json:"name,optional"`
        State string `json:"state,optional"`
        TotalAmount float64 `json:"total_amount,optional"`
        DownPayment float64 `json:"down_payment,optional"`
        PaidAmount float64 `json:"paid_amount,optional"`
        TotalInstallments int `json:"total_installments,optional"`
        PaidInstallments int `json:"paid_installments,optional"`
        BillId *int `json:"bill_id,optional"`  // Thêm trường bill_id
        Modified []string `json:"modified,optional"`
    }

    InstallmentPlanDeleteRequest {
        Id int `json:"id"`
    }

    InstallmentPlanRefundableListRequest {
        PersonId int `json:"person_id,optional"`
    }
)

@server(
    jwt: Auth
    group: installment_plan
    prefix: /v1/installment-plan
)
service bcare-api {
    @handler AddHandler
    post /add (InstallmentPlanAddRequest) returns (InstallmentPlanResponse);

    @handler GetHandler
    post /get (InstallmentPlanGetRequest) returns (InstallmentPlanResponse);

    @handler ListHandler
    post /list (InstallmentPlanListRequest) returns (InstallmentPlanListResponse);

    @handler ListRefundableHandler
    post /list-refundable (InstallmentPlanRefundableListRequest) returns (InstallmentPlanListResponse);

    @handler UpdateHandler
    post /update (InstallmentPlanUpdateRequest) returns (InstallmentPlanResponse);

    @handler DeleteHandler
    post /delete (InstallmentPlanDeleteRequest) returns (InstallmentPlan);
}