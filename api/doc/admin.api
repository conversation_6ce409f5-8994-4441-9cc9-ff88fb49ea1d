info(
    desc: "Admin API"
    author: "Your Name"
    email: "<EMAIL>"
)

type (
    LogsRequest {
        Type string `json:"type"`
        PageSize int `json:"page_size"`
        Page int `json:"page"`
    }

    LogsResponse {
        Logs string `json:"logs"`
        Total int `json:"total"`
        TotalPage int `json:"total_page"`
    }

    MonitorRequest {
        // Add monitor request parameters if needed
    }

    MonitorResponse {
        // Add monitor response fields if needed
    }
)

@server(
    jwt: Auth
    group: admin
    prefix: /v1/admin
)
service bcare-api {
    @handler LogsHandler
    post /logs (LogsRequest) returns (LogsResponse)

    @handler MonitorHandler
    post /monitor (MonitorRequest) returns (MonitorResponse)
}
