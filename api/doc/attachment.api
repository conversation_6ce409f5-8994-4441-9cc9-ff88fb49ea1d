info(
    desc: "attachment api"
    author: "lamvh"
    email: "<EMAIL>"
)

type (
    Attachment {
        Id int `json:"id"`
        DealId int `json:"deal_id"`
        PersonId int `json:"person_id"`
        ProductId int `json:"product_id"`
        PlanId int `json:"plan_id"`
        BrandId int `json:"brand_id"`
        Quantity int `json:"quantity"`
        Price float64 `json:"price"`
        Discount float64 `json:"discount"`
        Status int `json:"status"`
        Kind string `json:"kind"`
        Note string `json:"note"`
        Title string `json:"title"`
        ParentId *int `json:"parent_id"`
        UserId int `json:"user_id"`
        TrackId int `json:"track_id"`
        CreatedAt string `json:"created_at"`
        UpdatedAt string `json:"updated_at"`
        DeletedAt string `json:"deleted_at"`
        Version int `json:"version"`
    }

    AttachmentAddRequest {
        DealId *int `json:"deal_id,optional"`
        PersonId int `json:"person_id"`
        ProductId *int `json:"product_id,optional"`
        BrandId int `json:"brand_id,optional"`
        Quantity int `json:"quantity,optional"`
        Price float64 `json:"price,optional"`
        Discount float64 `json:"discount,optional"`
        Status int `json:"status,optional"`
        Kind string `json:"kind"`
        Note string `json:"note,optional"`
        Title string `json:"title,optional"`
        ParentId *int `json:"parent_id,optional"`
        PlanId int `json:"plan_id,optional"`
        Discounts []int `json:"discounts,optional"`
    }

    AttachmentDetail {
        DealId int `json:"deal_id"`
        ProductId int `json:"product_id"`
        BrandId int `json:"brand_id"`
        Quantity int `json:"quantity"`
        Price float64 `json:"price"`
        Discount float64 `json:"discount"`
        Note string `json:"note"`
    }

    AttachmentResponse {
        Attachment
        User UserShort `json:"creator"`
        Payment float64 `json:"payment"`
        Product Product `json:"product"`
        Parent Attachment `json:"parent"`
        Children []Attachment `json:"children"`
        BillItem BillItemResponse `json:"bill_item"`
        EligibleDiscounts []EligibleDiscount `json:"eligible_discounts"`
        Data []AttachmentDataResponse `json:"data"`
    }

    AttachmentTreeResponse {
        Attachment
        User UserShort `json:"creator"`
        Payment float64 `json:"payment"`
        Product Product `json:"product"`
        Parent AttachmentResponse `json:"parent"`
        Children []AttachmentResponse `json:"children"`
        BillItem BillItemResponse `json:"bill_item"`
        EligibleDiscounts []EligibleDiscount `json:"eligible_discounts"`
        Data []AttachmentDataResponse `json:"data"`
    }

    AttachmentUpdateRequest {
        Id int `json:"id"`
        DealId *int `json:"deal_id,optional"`
        ProductId *int `json:"product_id,optional"`
        BrandId int `json:"brand_id,optional"`
        Quantity int `json:"quantity,optional"`
        Price float64 `json:"price,optional"`
        Discount float64 `json:"discount,optional"`
        Note string `json:"note,optional"`
        Kind string `json:"kind,optional"`
        Title string `json:"title,optional"`
        ParentId *int `json:"parent_id,optional"`
        PlanId int `json:"plan_id,optional"`
        Status int `json:"status,optional"`
        Discounts []int `json:"discounts,optional"`
        Modified []string `json:"modified,optional"`
    }

    AttachmentGetRequest {
        Id int `json:"id"`
    }

    AttachmentDeleteRequest {
        Id int `json:"id"`
    }

    AttachmentListRequest {
        PageSize int `json:"page_size,optional"`
        Page int `json:"page,optional"`
        Filter AttachmentFilter `json:"filter,optional"`
        ProductType string `json:"product_type,optional"`
        ProductCategory int `json:"product_category,optional"`
        AttachmentKind string `json:"attachment_kind,optional"`
        OrderBy string `json:"order_by,optional"`
    }

    AttachmentFilter {
        DealId int `json:"deal_id,optional"`
        ProductId int `json:"product_id,optional"`
        ParentId int `json:"parent_id,optional"`
        Kind string `json:"kind,optional"`
        Status int `json:"status,optional"`
        PlanId int `json:"plan_id,optional"`
        PersonId int `json:"person_id,optional"`
    }

    AttachmentListResponse {
        Attachments []AttachmentResponse `json:"attachments"`
        Total int `json:"total"`
        TotalPage int `json:"total_page"`
    }

    AttachmentOperationReportRecord {
        TreatmentDate string `json:"treatment_date"`
        FullName string `json:"full_name"`
        PersonCode string `json:"person_code"`
        ProductTitle string `json:"product_title"`
        CategoryName string `json:"category_name"`
        Operation string `json:"operation"`
        Price float64 `json:"price"`
        TreatmentSequence int `json:"treatment_sequence"`
        DoctorName string `json:"doctor_name"`
        CreatedAt string `json:"created_at"`
        BuyDate string `json:"buy_date"`
        Person Person `json:"person"`
        Doctor UserShort `json:"user"`
    }

    AttachmentDynamicQuery {
        DynamicQuery
        Export bool `json:"export,optional"`
    }
)

@server(
    jwt: Auth
    group: attachment
    prefix: /v1/attachment
)
service bcare-api {
    @handler AddHandler
    post /add (AttachmentAddRequest) returns (AttachmentResponse);

    @handler ChildrenHandler
    post /children (AttachmentGetRequest) returns ([]AttachmentResponse);

    @handler GetHandler
    post /get (AttachmentGetRequest) returns (AttachmentTreeResponse);

    @handler ListHandler
    post /list (AttachmentListRequest) returns (AttachmentListResponse);

    @handler UpdateHandler
    post /update (AttachmentUpdateRequest) returns (AttachmentResponse);

    @handler DeleteHandler
    post /delete (AttachmentDeleteRequest) returns (AttachmentResponse);

    @handler QueryHandler
    post /query (AttachmentDynamicQuery) returns (DynamicQueryResponse);

    @handler QueryOperation
    post /query-operation (AttachmentDynamicQuery) returns (DynamicQueryResponse);
}