info(
    desc: "bill api"
    author: "lamvh"
    email: "<EMAIL>"
)

type (
    Bill {
        Id int `json:"id"`
        DealId int `json:"deal_id"`
        PersonId int `json:"person_id"`
        UserId int `json:"user_id"`
        BrandId int `json:"brand_id"`
        Status int `json:"status"`
        State string `json:"state"`
        Discount float64 `json:"discount"`
        Refund float64 `json:"refund"`
        DebtRemaining float64 `json:"debt_remaining"`
        DebtPayment float64 `json:"debt_payment"`
        CreatedAt string `json:"created_at"`
        UpdatedAt string `json:"updated_at"`
        DeletedAt string `json:"deleted_at"`
        Version int `json:"version"`
    }

    BillAddRequest {
        DealId *int `json:"deal_id,optional"`
        PersonId int `json:"person_id"`
        BrandId int `json:"brand_id,optional"`
        State string `json:"state,optional"`
        Discount float64 `json:"discount,optional"`
        Refund float64 `json:"refund,optional"`
        DebtRemaining float64 `json:"debt_remaining,optional"`
        DebtPayment float64 `json:"debt_payment,optional"`
        BillItems []BillItemAddRequest `json:"bill_items"`
        Payment *PaymentAddRequest `json:"payment,optional"`
    }

    BillUpdateRequest {
        Id int `json:"id"`
        DealId *int `json:"deal_id,optional"`
        Status int `json:"status,optional"`
        State string `json:"state,optional"`
        BrandId int `json:"brand_id,optional"`
        DebtRemaining float64 `json:"debt_remaining,optional"`
        DebtPayment float64 `json:"debt_payment,optional"`
        Modified []string `json:"modified,optional"`
    }

    BillGetRequest {
        Id int `json:"id"`
    }

    BillDeleteRequest {
        Id int `json:"id"`
    }

    BillListRequest {
        PageSize int `json:"page_size,optional"`
        Page int `json:"page,optional"`
        Filter BillFilter `json:"filter,optional"`
        PersonId int `json:"person_id,optional"`
        OrderBy string `json:"order_by,optional"`
    }

    BillFilter {
        DealId int `json:"deal_id,optional"`
        UserId int `json:"user_id,optional"`
        PersonId int `json:"person_id,optional"`
        BrandId int `json:"brand_id,optional"`
        Status int `json:"status,optional"`
        State string `json:"state,optional"`
        HasInstallmentPlan *bool `json:"has_installment_plan,optional"`
    }

    BillListResponse {
        Bills []BillResponse `json:"bills"`
        Total int `json:"total"`
        TotalPage int `json:"total_page"`
    }

    BillResponse {
        Bill
        Track *Track `json:"track"`
        BillItems []BillItemResponse `json:"items"`
        Payments []PaymentResponse `json:"payments"`
        Deal Deal `json:"deal"`
        TotalValue float64 `json:"total_value"`
        DoctorName string `json:"doctor_name"`
        Data []BillDataResponse `json:"data"`
        InstallmentPlans []InstallmentPlanResponse `json:"installment_plans"`
    }

    BillDynamicQuery {
        DynamicQuery
        Person string `json:"person"`
    }

    BillAddFromDealRequest {
        DealId int `json:"deal_id"`
    }
)

@server(
    jwt: Auth
    group: bill
    prefix: /v1/bill
)
service bcare-api {
    @handler AddHandler
    post /add (BillAddRequest) returns (BillResponse);

    @handler GetHandler
    post /get (BillGetRequest) returns (BillResponse);

    @handler ListHandler
    post /list (BillListRequest) returns (BillListResponse);

    @handler QueryHandler
    post /query (BillDynamicQuery) returns (DynamicQueryResponse);

    @handler UpdateHandler
    post /update (BillUpdateRequest) returns (BillResponse);

    @handler DeleteHandler
    post /delete (BillDeleteRequest) returns (BillResponse);

    @handler AddFromDealHandler
    post /add-from-deal (BillAddFromDealRequest) returns (BillResponse);
}