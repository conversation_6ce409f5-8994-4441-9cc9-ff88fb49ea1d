info(
    desc: "notification api"
    author: "lamvh"
    email: "<EMAIL>"
)


type Notification {
    ID int `json:"id"`
    UserID int `json:"user_id"`
    Type string `json:"type"`
    Message string `json:"message"`
    EntityID *int `json:"entity_id,omitempty"`
    EntityType *string `json:"entity_type,omitempty"`
    IsRead bool `json:"is_read"`
    CreatedAt string `json:"created_at"`
    ReadAt *string `json:"read_at,omitempty"`
    SenderID *int `json:"sender_id,omitempty"`
    Metadata map[string]interface{} `json:"metadata,omitempty"`
}

type NotificationListRequest {
    UserID int `json:"user_id"`
    Page int `json:"page,optional,default=1"`
    Limit int `json:"limit,optional,default=20"`
    IsRead *bool `json:"is_read,optional"`
    SortBy string `json:"sort_by,optional,default=created_at"`
    Order string `json:"order,optional,default=desc"`
}

type NotificationListResponse {
    Data []Notification `json:"data"`
    Total int `json:"total"`
    TotalPage int `json:"total_page"`
}

type NotificationUnreadCountRequest {
    UserID int `json:"user_id"`
}

type NotificationUnreadCountResponse {
    UnreadCount int `json:"unread_count"`
}

type NotificationMarkAsReadRequest {
    UserID int `json:"user_id"`
    NotificationIDs []int `json:"notification_ids"`
}

type NotificationMarkAllAsReadRequest {
    UserID int `json:"user_id"`
}

type NotificationDeleteRequest {
    UserID int `json:"user_id"`
    NotificationID int `json:"notification_id"`
}

type NotificationDeleteResponse {
    Message string `json:"message,omitempty"`
}

type NotificationAddRequest {
    UserID int `json:"user_id"`
    Type string `json:"type"`
    Message string `json:"message"`
    EntityID int `json:"entity_id,optional"`
    EntityType string `json:"entity_type,optional"`
    SenderID int `json:"sender_id,optional"`
    Metadata map[string]interface{} `json:"metadata,optional"`
}

type NotificationResponse {
    Notification
}

type NotificationBatchAddRequest {
    UserIDs []int `json:"user_ids"`
    Type string `json:"type"`
    Message string `json:"message"`
    EntityID int `json:"entity_id,optional"`
    EntityType string `json:"entity_type,optional"`
    SenderID int `json:"sender_id,optional"`
    Metadata map[string]interface{} `json:"metadata,optional"`
}

type NotificationBroadcastAddRequest {
    Type string `json:"type"`
    Message string `json:"message"`
    EntityID int `json:"entity_id,optional"`
    EntityType string `json:"entity_type,optional"`
    SenderID int `json:"sender_id,optional"`
    Metadata map[string]interface{} `json:"metadata,optional"`
}

type NotificationGroupAddRequest {
    DepartmentIDs []int `json:"department_ids,optional"`
    Roles []string `json:"roles,optional"`
    Type string `json:"type"`
    Message string `json:"message"`
    EntityID int `json:"entity_id,optional"`
    EntityType string `json:"entity_type,optional"`
    SenderID int `json:"sender_id,optional"`
    Metadata map[string]interface{} `json:"metadata,optional"`
}

type NotificationBatchResponse {
    CreatedCount int `json:"created_count"`
    Message string `json:"message"`
}


@server(
    jwt: Auth
    group: notification
    prefix: /v1/notification
)

service bcare-api {
    @handler AddHandler
    post /add (NotificationAddRequest) returns (NotificationResponse);

    @doc "Lấy danh sách thông báo cho người dùng đã đăng nhập"
    @handler List
    post /list (NotificationListRequest) returns (NotificationListResponse)

    @handler Unread
    post /unread-count (NotificationUnreadCountRequest) returns (NotificationUnreadCountResponse)

    @handler MarkAsRead
    post /read (NotificationMarkAsReadRequest) returns ()

    @handler MarkAllAsRead
    post /read-all (NotificationMarkAllAsReadRequest) returns ()

    @handler Delete
    post /delete (NotificationDeleteRequest) returns ()

    @doc "Tạo thông báo hàng loạt cho nhiều người dùng"
    @handler AddBatch
    post /add-batch (NotificationBatchAddRequest) returns (NotificationBatchResponse)

    @doc "Phát sóng thông báo cho tất cả người dùng"
    @handler Broadcast
    post /broadcast (NotificationBroadcastAddRequest) returns (NotificationBatchResponse)

    @doc "Tạo thông báo cho nhóm người dùng theo phòng ban/vai trò"
    @handler AddGroup
    post /add-group (NotificationGroupAddRequest) returns (NotificationBatchResponse)
} 