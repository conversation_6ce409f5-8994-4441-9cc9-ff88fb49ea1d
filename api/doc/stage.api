info(
    desc: "Stage API"
    author: "Your Name"
    email: "<EMAIL>"
)

type (
    Stage {
        Id int `json:"id"`
        Name string `json:"name"`
        PipelineId int `json:"pipeline_id"`
        OrderNumber int `json:"order_number"`
        ParentStageId int `json:"parent_stage_id"`
        Status int `json:"status"`
        Meta string `json:"meta"`
        CreatedAt string `json:"created_at"`
        UpdatedAt string `json:"updated_at"`
        DeletedAt string `json:"deleted_at"`
        Version int `json:"version"`
    }

    StageAddRequest {
        Name string `json:"name"`
        PipelineId int `json:"pipeline_id"`
        OrderNumber int `json:"order_number"`
        ParentStageId *int `json:"parent_stage_id"`
        Status int `json:"status"`
    }

    StageResponse {
        Stage
        Children []Stage `json:"children"`
    }

    StageUpdateRequest {
        Id int `json:"id"`
        Name string `json:"name,optional"`
        PipelineId int `json:"pipeline_id,optional"`
        OrderNumber int `json:"order_number,optional"`
        ParentStageId *int `json:"parent_stage_id,optional"`
        Meta string `json:"meta,optional"`
        Status int `json:"status,optional"`
        Modified []string `json:"modified,optional"`
    }

    StageUpdateMetaRequest {
        Id int `json:"id"`
        MetaFieldName   string `json:"meta_field_name"`
        MetaValue       string `json:"meta_value"`
    }

    StageGetRequest {
        Id int `json:"id"`
    }

    StageDeleteRequest {
        Id int `json:"id"`
    }

    StageListRequest {
        PageSize int `json:"page_size,optional"`
        Page int `json:"page,optional"`
        Filter StageFilter `json:"filter,optional"`
        OrderBy string `json:"order_by,optional"`
    }

    StageFilter {
        PipelineId int `json:"pipeline_id,optional"`
        Name string `json:"name,optional"`
        Status int `json:"status,optional"`
    }

    StageListResponse {
        Stages []StageResponse `json:"stages"`
        Total int `json:"total"`
        TotalPage int `json:"total_page"`
    }
)

@server(
    jwt: Auth
    group: stage
    prefix: /v1/stage
)
service bcare-api {
    @handler AddHandler
    post /add (StageAddRequest) returns (StageResponse);

    @handler GetHandler
    post /get (StageGetRequest) returns (StageResponse);

    @handler ListHandler
    post /list (StageListRequest) returns (StageListResponse);

    @handler UpdateHandler
    post /update (StageUpdateRequest) returns (StageResponse);

    @handler UpdateStageHandler
    post /update-stage (StageUpdateMetaRequest) returns (StageResponse);

    @handler DeleteHandler
    post /delete (StageDeleteRequest) returns (StageResponse);
}