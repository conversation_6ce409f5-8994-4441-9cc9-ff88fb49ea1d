info(
    desc: "Personal Task Statistics API"
    author: "lamvh"
    email: "<EMAIL>"
)

type (
    // Response types cho dashboard
    TaskOverview {
        TotalTasks int `json:"total_tasks"`             // Tổng số công việc
        CompletedTasks int `json:"completed_tasks"`     // Số công việc hoàn thành
        CompletionRate float64 `json:"completion_rate"` // Tỷ lệ hoàn thành (%)
        RemainingTasks int `json:"remaining_tasks"`     // Số công việc còn lại
    }

    StatusCount {
        Status string `json:"status"`
        Count int `json:"count"`
    }



    TaskStatResponse {
        // 1. Tổng quan
        Overview TaskOverview `json:"overview"`

        // 2. Phân tích theo trạng thái
        StatusBreakdown []StatusCount `json:"status_breakdown"`

        // 3. Công việc sắp đến hạn
        UpcomingDeadlines []TaskResponse `json:"upcoming_deadlines"`
    }

    DailySchedule {
        Date string `json:"date"`
        DayOfWeek string `json:"day_of_week"`
        WorkSchedules []ScheduleResponse `json:"work_schedules"`
        Appointments []AppointmentResponse `json:"appointments"`
        TotalAppointments int `json:"total_appointments"`
    }

    Overview  {
        TodayAppointments int `json:"today_appointments"`
        UpcomingAppointments int `json:"upcoming_appointments"`
    }

    ScheduleStatResponse {
        // 1. Thông tin tổng quan
        Overview Overview `json:"overview"`

        // 2. Danh sách cuộc hẹn sắp tới
        UpcomingAppointments []AppointmentResponse `json:"upcoming_appointments"`

        // 3. Lịch trình theo ngày
        DailySchedules []DailySchedule `json:"daily_schedules"`
    }

    StatRequest {
        UserId int `json:"user_id"`
        StartDate string `json:"start_date,optional"`
        EndDate string `json:"end_date,optional"`
    }

    // Types cho Person Statistics

    PersonOverview {
        TotalPersons int `json:"total_persons"`         // Tổng số người
        NewPersons int `json:"new_persons"`             // Số người mới (30 ngày gần nhất)
    }

    PersonStatResponse {
        // 1. Tổng quan
        Overview PersonOverview `json:"overview"`

        // 2. Danh sách người được phân công gần đây (30 ngày)
        RecentPersons []PersonResponse `json:"recent_persons"`
    }

    // Types cho Performance Statistics
    PerformanceOverview {
        AppointmentCount    int     `json:"appointment_count"`     // Số lượng cuộc hẹn
        CallCount          int     `json:"call_count"`           // Số lượng cuộc gọi
        NoteCount         int     `json:"note_count"`          // Số lượng ghi chú
        MessageCount      int     `json:"message_count"`       // Số lượng tin nhắn
        DailyAverage      float64 `json:"daily_average"`       // Trung bình hoạt động mỗi ngày
    }

    TimeSeriesData {
        Date  string `json:"date"`
        Count int    `json:"count"`
    }

    ActivityBreakdown {
        Appointments []TimeSeriesData `json:"appointments"` // Dữ liệu cuộc hẹn theo thời gian
        Calls        []TimeSeriesData `json:"calls"`        // Dữ liệu cuộc gọi theo thời gian
        Notes        []TimeSeriesData `json:"notes"`        // Dữ liệu ghi chú theo thời gian
        Messages     []TimeSeriesData `json:"messages"`     // Dữ liệu tin nhắn theo thời gian
        Total     []TimeSeriesData `json:"total"`     // Dữ liệu tất cả theo thời gian
    }

    PerformanceStatResponse {
        // 1. Tổng quan hiệu suất
        Overview PerformanceOverview `json:"overview"`

        // 2. Phân tích theo thời gian
        TimeSeries ActivityBreakdown `json:"time_series"`
    }
)

@server(
    jwt: Auth
    group: user_stat
    prefix: /v1/user/stat
)
service bcare-api {
    @handler TaskStat
    post /task (StatRequest) returns (TaskStatResponse);

    @handler Schedule
    post /schedule (StatRequest) returns (ScheduleStatResponse);

    @handler PersonStat
    post /person (StatRequest) returns (PersonStatResponse);

    @handler Performance
    post /performance (StatRequest) returns (PerformanceStatResponse);
}