info(
    desc: "Issue API"
    author: "Your Name"
    email: "<EMAIL>"
)

type (
    Issue {
        Id          int    `json:"id"`
        Title       string `json:"title"`
        Description string `json:"description,optional"`
        Type        string `json:"type"`
        Progress string `json:"progress"`
        Priority    string `json:"priority"`
        PersonId    int    `json:"person_id"`
        Status      int    `json:"status"`
        CreatedAt   string `json:"created_at"`
        UpdatedAt   string `json:"updated_at"`
        DeletedAt   string `json:"deleted_at,optional"`
        Version     int    `json:"version"`
    }

    IssueAddRequest {
        Title       string `json:"title"`
        Description string `json:"description,optional"`
        Type        string `json:"type"`
        Priority    string `json:"priority,optional"`
        PersonId    int    `json:"person_id"`
    }

    IssueUpdateRequest {
        Id          int    `json:"id"`
        Title       string `json:"title,optional"`
        Description string `json:"description,optional"`
        Type        string `json:"type,optional"`
        Progress string `json:"progress,optional"`
        Priority    string `json:"priority,optional"`
        Modified []string `json:"modified,optional"`
    }

    IssueListRequest {
        PersonId    int    `json:"person_id,optional"`
        Type        string `json:"type,optional"`
        Progress string `json:"progress,optional"`
        Priority    string `json:"priority,optional"`
    }

    IssueGetRequest {
        Id int `json:"id"`
    }

    IssueCloseRequest {
        Id int `json:"id"`
    }

    IssueListResponse {
        Issues []Issue `json:"issues"`
    }
)

//@server(
//    jwt: Auth
//    group: issue
//    prefix: /v1/issue
//)
//service bcare-api {
//    @handler AddHandler
//    post /add (IssueAddRequest) returns (Issue)
//
//    @handler UpdateHandler
//    post /update (IssueUpdateRequest) returns (Issue)
//
//    @handler ListHandler
//    post /list (IssueListRequest) returns (IssueListResponse)
//
//    @handler GetHandler
//    post /get (IssueGetRequest) returns (Issue)
//
//    @handler CloseHandler
//    post /close (IssueCloseRequest) returns (Issue)
//}
