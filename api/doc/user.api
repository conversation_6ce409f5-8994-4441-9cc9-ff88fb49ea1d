info(
    desc: "user api"
    author: "lamvh"
    email: "<EMAIL>"
)

type User {
    Id int `json:"id"`
    Username string `json:"username"`
    Phone string `json:"phone"`
    Email string `json:"email"`
    EmailConfirmed bool `json:"email_confirmed"`
    Name string `json:"name"`
    Gender string `json:"gender"`
    DepartmentId int `json:"department_id"`
    DepartmentPosition string `json:"department_position"`
    ProfileImage string `json:"profile_image"`
    State string `json:"state"`
    Status int `json:"status"`
    Version int `json:"version"`
    CreatedAt string `json:"created_at"`
    SuspendedAt string `json:"suspended_at"`
    UpdatedAt string `json:"updated_at"`
    DeletedAt string `json:"deleted_at"`
}

type UserShort {
    Id int `json:"id"`
    Username string `json:"username"`
    Name string `json:"name"`
    ProfileImage string `json:"profile_image"`
    DepartmentId int `json:"department_id"`

}

type UserShortInformation {
    Id int `json:"id" db:"doctor_id"`
    Name string `json:"username" db:"doctor_name"`
}

type UserAddRequest {
    Username string `json:"username"`
    Password string `json:"password"`
    Name string `json:"name"`
    Email string `json:"email,optional"`
    Gender string `json:"gender,options=male|female|unknown"`
    Phone string `json:"phone,optional"`
    State string `json:"state,optional"`
    DepartmentId int `json:"department_id,optional"`
    DepartmentPosition string `json:"department_position,optional"`
    ProfileImage string `json:"profile_image,optional"`
    Roles []string `json:"roles"`
}

type UserResponse {
    User
    Roles []string `json:"roles"`
    Data []UserDataResponse `json:"data"`
}

type UserUpdateRequest {
    Id int `json:"id"`
    Username string `json:"username,optional"`
    Password string `json:"password,optional"`
    Name string `json:"name,optional"`
    Email string `json:"email,optional"`
    Gender string `json:"gender,options=male|female|unknown,optional"`
    Phone string `json:"phone,optional"`
    DepartmentId int `json:"department_id,optional"`
    DepartmentPosition string `json:"department_position,optional"`
    ProfileImage string `json:"profile_image,optional"`
    Status int `json:"status,range=[0:10],optional"`
    State string `json:"state,optional"`
    Roles []string `json:"roles,optional"`
    Modified []string `json:"modified,optional"`
}

type UserGetRequest {
    Id int `json:"id"`
    Username string `json:"username,optional"`
    Phone string `json:"phone,optional"`
    Email string `json:"email,optional"`
}

type UserDeleteRequest {
    Id int `json:"id"`
    Username string `json:"username,optional"`
}

type UserListRequest {
    PageSize int `json:"page_size,optional"`
    Page int `json:"page,optional"`
    Filter UserFilter `json:"filter,optional"`
    Role string `json:"role,optional"`
    Search string `json:"search,optional"`
    OrderBy string `json:"order_by,optional"`
}

type UserFilter {
    Ids []int `json:"ids,optional"`
    Username string `json:"username,optional"`
    Name string `json:"name,optional"`
    Email string `json:"email,optional"`
    Gender string `json:"gender,optional"`
    Phone string `json:"phone,optional"`
    Status int `json:"status,range=[0:10],optional"`
    State string `json:"state,optional"`
    DepartmentId int `json:"department_id,optional"`
}

type UserListResponse {
    Users []UserResponse `json:"users"`
    Total int `json:"total"`
    TotalPage int `json:"total_page"`
}


@server(
    //jwt: Auth
    jwt: Auth
    group: user
    //middleware: AuthorizerMiddleware
    prefix: /v1/user
)
service bcare-api {
    @handler AddHandler
    post /add (UserAddRequest) returns (UserResponse);

    @handler GetHandler
    //Get a single user record
    post /get (UserGetRequest) returns (UserResponse);

    @handler ListHandler
    post /list (UserListRequest) returns (UserListResponse);

    @handler UpdateHandler
    post /update (UserUpdateRequest) returns (UserResponse);

    @handler DeleteHandler
    post /delete (UserDeleteRequest) returns (UserResponse);
}
