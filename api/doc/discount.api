info(
    desc: "Discount API"
    author: "lamvh"
    email: "<EMAIL>"
)

type (
    Discount {
        Id int `json:"id"`
        Name string `json:"name"`
        Type string `json:"type"`
        Value float64 `json:"value"`
        Scope string `json:"scope"`
        Condition string `json:"condition"`
        UsageType string `json:"usage_type"`
        Description string `json:"description"`
        Meta string `json:"meta"`
        Start string `json:"start"`
        End string `json:"end"`
        Status int `json:"status"`
        CreatedAt string `json:"created_at"`
        UpdatedAt string `json:"updated_at"`
        DeletedAt string `json:"deleted_at"`
        Version int `json:"version"`
    }

    DiscountResponse {
        DiscountUsageId int `json:"discount_usage_id" db:"discount_usage_id"`
        Id int `json:"id" db:"id"`
        Name string `json:"name" db:"name"`
        Value float64 `json:"value" db:"value"`
        Scope string `json:"scope" db:"scope"`
        Description string `json:"description" db:"description"`
    }

    EligibleDiscount {
        Discount Discount `json:"discount"`
        DiscountValue float64 `json:"discount_value"`
        DiscountAmount float64 `json:"discount_amount"`
        EntityType string `json:"entity_type"`
        EntityId int `json:"entity_id"`
    }

    EligibleDiscountResponse {
        EligibleDiscount []EligibleDiscount `json:"eligible_discounts"`
    }

    DiscountAddRequest {
        Name string `json:"name"`
        Type string `json:"type"`
        Value float64 `json:"value"`
        Scope string `json:"scope"`
        Condition string `json:"condition"`
        UsageType string `json:"usage_type"`
        Description string `json:"description"`
        Meta string `json:"meta"`
        Start string `json:"start"`
        End string `json:"end"`
    }

    DiscountUpdateRequest {
        Id int `json:"id"`
        Name string `json:"name,optional"`
        Type string `json:"type,optional"`
        Value float64 `json:"value,optional"`
        Scope string `json:"scope,optional"`
        Condition string `json:"condition,optional"`
        UsageType string `json:"usage_type,optional"`
        Description string `json:"description,optional"`
        Meta string `json:"meta,optional"`
        Start string `json:"start,optional"`
        End string `json:"end,optional"`
        Status int `json:"status,optional"`
        Modified []string `json:"modified,optional"`
    }

    DiscountGetRequest {
        Id int `json:"id"`
    }

    DiscountDeleteRequest {
        Id int `json:"id"`
    }

    DiscountListRequest {
        PageSize int `json:"page_size,optional"`
        Page int `json:"page,optional"`
        Filter DiscountFilter `json:"filter,optional"`
        OrderBy string `json:"order_by,optional"`
    }

    DiscountFilter {
        Name string `json:"name,optional"`
        Type string `json:"type,optional"`
        Scope string `json:"scope,optional"`
        UsageType string `json:"usage_type,optional"`
        Status int `json:"status,optional"`
    }

    DiscountListResponse {
        Discounts []Discount `json:"discounts"`
        Total int `json:"total"`
        TotalPage int `json:"total_page"`
    }

    EligibleDiscountRequest {
        PersonId int `json:"person_id"`
        DealId  int `json:"deal_id"`
        ProductIds []int `json:"product_ids"`
    }

    DiscountDetail {
        Amount float64 `json:"amount"`
        AppliedTo string `json:"applied_to"`     // "deal" hoặc "attachment"
        AttachmentID *int `json:"attachment_id,optional"`
    }

    DiscountResult {
        DiscountID int `json:"discount_id"`
        TotalAmount float64 `json:"total_amount"`
        Details []DiscountDetail `json:"details"`
    }

    CalculateDiscountRequest {
        DealId int `json:"deal_id"`
        DiscountIds []int `json:"discount_ids"`
    }

    CalculateDiscountResponse {
        TotalDiscountAmount float64 `json:"total_discount_amount"`
        Results []DiscountResult `json:"results,optional"`
        DiscountAmounts map[int]float64 `json:"discount_amounts"`
    }

    CalculateDiscountAttachmentRequest  {
        ProductID    int     `json:"product_id"`
        Quantity     int     `json:"quantity"`
        DiscountIds  []int   `json:"discount_ids"`
    }
)

@server(
    jwt: Auth
    group: discount
    prefix: /v1/discount
)
service bcare-api {
    @handler AddHandler
    post /add (DiscountAddRequest) returns (Discount);

    @handler EligibleDiscountHandler
    post /eligible (EligibleDiscountRequest) returns (EligibleDiscountResponse);

    @handler CalculateDiscountHandler
    post /calculate (CalculateDiscountRequest) returns (CalculateDiscountResponse);

    @handler CalculateDiscountAttachmentHandler
    post /calculate-attachment (CalculateDiscountAttachmentRequest) returns (CalculateDiscountResponse);

    @handler GetHandler
    post /get (DiscountGetRequest) returns (Discount);

    @handler ListHandler
    post /list (DiscountListRequest) returns (DiscountListResponse);

    @handler UpdateHandler
    post /update (DiscountUpdateRequest) returns (Discount);

    @handler DeleteHandler
    post /delete (DiscountDeleteRequest) returns (Discount);
}