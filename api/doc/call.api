info(
    desc: "call api"
    author: "lamvh"
    email: "<EMAIL>"
)

type (
    Call {
        Id int `json:"id"`
        CallId string `json:"call_id"`
        StartTime string `json:"start_time"`
        Duration int `json:"duration"`
        Direction string `json:"direction"`
        Source string `json:"source"`
        Destination string `json:"destination"`
        RecordingFile string `json:"recording_file"`
        Score float64 `json:"score"`
        Pdd int `json:"pdd"`
        Tta int `json:"tta"`
        Feedback string `json:"feedback"`
        Kind string `json:"kind"`
        Rating int `json:"rating"`
        CallStatus string `json:"call_status"`
        Status int `json:"status"`
        PersonId int `json:"person_id"`
        UserId int `json:"user_id"`
        CreatedAt string `json:"created_at"`
        UpdatedAt string `json:"updated_at"`
        DeletedAt string `json:"deleted_at"`
        Version int `json:"version"`
        State string `json:"state"`
    }

    CallResponse {
        Call
        User UserShort `json:"user"`
        Person Person `json:"person"`
    }

    CallAddRequest {
        CallId string `json:"call_id"`
        PersonId int `json:"person_id"`
        Direction string `json:"direction,options=inbound|outbound"`
        Phone string `json:"phone,optional"`
    }

    CallUpdateRequest {
        Id int `json:"id"`
        CallId string `json:"call_id"`
        Modified []string `json:"modified,optional"`
    }

    CallUpdateFeedbackRequest {
        Id int `json:"id"`
        Feedback string `json:"feedback,optional"`
        Rating int `json:"rating,optional"`
        Kind string `json:"kind,optional"`
        Modified []string `json:"modified,optional"`
    }
    CallEndRequest {
        Id int `json:"id"`
        CallId string `json:"call_id"`
        IsMiss bool  `json:"is_miss"`
    }

    CallGetRequest {
        Id int `json:"id"`
    }

    CallDeleteRequest {
        Id int `json:"id"`
    }

    CallListRequest {
        PageSize int `json:"page_size,optional"`
        Page int `json:"page,optional"`
        Filter CallFilter `json:"filter"`
        OrderBy string `json:"order_by,optional"`
    }

    CallFilter {
        Direction string `json:"direction,optional"`
        CallStatus string `json:"call_status,optional"`
        Status int `json:"status,optional"`
        PersonId int `json:"person_id,optional"`
        UserId int `json:"user_id,optional"`
    }

    CallListResponse {
        Calls []Call `json:"calls"`
        Total int `json:"total"`
        TotalInboundMiss int `json:"total_inbound_miss"`
        TotalInbound int `json:"total_inbound"`
        TotalOutbound int `json:"total_outbound"`
        TotalOutboundMiss int `json:"total_outbound_miss"`
        TotalPage int `json:"total_page"`
    }

    CallDynamicQuery {
        DynamicQuery
        Person string `json:"person,optional"` // tên, sdt
        Creator string `json:"creator,optional"` // tên, id
        State string `json:"state,options=inbound|outbound|miss_inbound|miss_outbound|all,optional"` // state
    }
)

@server(
    jwt: Auth
    group: call
    prefix: /v1/call
)
service bcare-api {
    @handler AddHandler
    post /add (CallAddRequest) returns (Call);

    @handler AcceptHandler
    post /accept (CallUpdateRequest) returns (Call);

    @handler GetHandler
    post /get (CallUpdateRequest) returns (Call);

    @handler ListHandler
    post /list (CallListRequest) returns (CallListResponse);

    @handler QueryHandler
    post /query (CallDynamicQuery) returns (DynamicQueryResponse);

    @handler EndHandler
    post /end (CallEndRequest) returns (Call);

    @handler DeleteHandler
    post /delete (CallDeleteRequest) returns (Call);

    @handler UpdateFeedbackHandler
    post /update/feedback (CallUpdateFeedbackRequest) returns (Call);
}