info(
    desc: "Referral API"
    author: "Your Name"
    email: "<EMAIL>"
)

type (
    Referral {
        Id                  int  `json:"id"`
        ReferredPersonId    int  `json:"referred_person_id"`
        Notes               string `json:"notes"`
        EntityType          string `json:"entity_type"`
        EntityId            int  `json:"entity_id"`
        ReferrerRelationship string `json:"referrer_relationship"`
        CreatedAt           string `json:"created_at"`
    }

    ReferralAddRequest {
        ReferredPersonId    int  `json:"referred_person_id"`
        Notes               string `json:"notes"`
        EntityType          string `json:"entity_type"`
        EntityId            int  `json:"entity_id"`
        ReferrerRelationship string `json:"referrer_relationship"`
    }

    ReferralUpdateRequest {
        ReferredPersonId    int  `json:"referred_person_id"`
        Notes               string `json:"notes,optional"`
        EntityType          string `json:"entity_type,optional"`
        EntityId            int  `json:"entity_id,optional"`
        ReferrerRelationship string `json:"referrer_relationship,optional"`
    }

    ReferralDeleteRequest {
        Id int `json:"id"`
    }

    Referrer {
        Id int `json:"id" db:"id"`
        FullName string `json:"full_name" db:"full_name"`
        Phone string `json:"phone" db:"phone"`
        Email string `json:"email" db:"email"`
        EntityType string `json:"entity_type" db:"entity_type"`
        ReferrerRelationship string `json:"referrer_relationship" db:"referrer_relationship"`
    }

    SearchRequest {
        Search string `json:"search"`
    }

    SearchResponse {
        Referrers []Referrer `json:"referrers"`
    }

)

@server(
    jwt: Auth
    group: referral
    prefix: /v1/referral
)
service bcare-api {
    @handler AddHandler
    post /add (ReferralAddRequest) returns (Referral)

    @handler UpdateHandler
    post /update (ReferralUpdateRequest) returns (Referral)

    @handler DeleteHandler
    post /delete (ReferralDeleteRequest) returns (Referral)

    @handler SearchReferrerHandler
    post /search-referrer (SearchRequest) returns (SearchResponse)
}