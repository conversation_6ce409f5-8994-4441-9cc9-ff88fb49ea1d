info(
    desc: "task api"
    author: "lamvh"
    email: "<EMAIL>"
)

type (
    FieldChange {
        Field string `json:"field"`
        OldValue interface{} `json:"old_value"`
        NewValue interface{} `json:"new_value"`
    }

    HistoryEntry {
        UserID int `json:"user_id"`
        Changes []FieldChange `json:"changes"`
        Timestamp string `json:"timestamp"`
    }
    Task {
        Id int `json:"id"`
        Title string `json:"title"`
        Note string `json:"note"`
        StartDate string `json:"start_date"`
        DueDate string `json:"due_date"`
        EndDate string `json:"end_date"`
        Type string `json:"type"`
        Status int `json:"status"`
        State string `json:"state"`
        Priority int `json:"priority"`
        ParentId int `json:"parent_id"`
        PersonId int `json:"person_id"`
        DealId int `json:"deal_id"`
        AppointmentId int `json:"appointment_id"`
        DepartmentId int `json:"department_id"`
        CreatorId int `json:"creator_id"`
        Serial int `json:"serial"`
        CurrentSerial int `json:"current_serial"`
        TaskRecurringId int `json:"task_recurring_id"`
        History []HistoryEntry `json:"history"`
        CompletedAt string `json:"completed_at"`
        CreatedAt string `json:"created_at"`
        UpdatedAt string `json:"updated_at"`
        DeletedAt string `json:"deleted_at"`
        Version int `json:"version"`
    }

    TaskRecurring {
        Id int `json:"id"`
        CronExpression string `json:"cron_expression"`
        NextOccurrence string `json:"next_occurrence"`
        LastOccurrence string `json:"last_occurrence"`
    }

    BulkUpdateTasksRequest {
        IdList []int `json:"id_list"`
        State string `json:"state,optional"`
        StartDate string `json:"start_date,optional"`
        EndDate string `json:"end_date,optional"`
        DueDate string `json:"due_date,optional"`
        Modified []string `json:"modified,optional"`
    }



    BulkDeleteRequest {
        IdList []int `json:"id_list"`
    }

    TaskAddRequest {
        Title string `json:"title"`
        Note string `json:"note"`
        StartDate string `json:"start_date,optional"`
        DueDate string `json:"due_date,optional"`
        EndDate string `json:"end_date,optional"`
        Type string `json:"type,optional"`
        State string `json:"state,optional"`
        Priority int `json:"priority"`
        ParentId *int `json:"parent_id,optional"`
        PersonId *int `json:"person_id"`
        DealId *int `json:"deal_id,optional"`
        DepartmentId int `json:"department_id,optional"`
        AppointmentId *int `json:"appointment_id,optional"`
        Users []TaskAssignmentAddRequest `json:"users,optional"`
        Departments []TaskDepartmentAddRequest `json:"departments,optional"`
        CronExpression string `json:"cron_expression,optional"`
    }

    TaskUpdateRequest {
        Id int `json:"id"`
        Title string `json:"title,optional"`
        Note string `json:"note,optional"`
        ParentId int `json:"parent_id,optional"`
        Type string `json:"type,optional"`
        State string `json:"state,optional"`
        Priority int `json:"priority,optional"`
        StartDate string `json:"start_date,optional"`
        EndDate string `json:"end_date,optional"`
        DueDate string `json:"due_date,optional"`
        PersonId int `json:"person_id,optional"`
        DealId int `json:"deal_id,optional"`
        AppointmentId int `json:"appointment_id,optional"`
        DepartmentId int `json:"department_id,optional"`
        Users []TaskAssignmentAddRequest `json:"users,optional"`
        Departments []TaskDepartmentAddRequest `json:"departments,optional"`
        Modified []string `json:"modified,optional"`
    }


    TaskGetRequest {
        Id int `json:"id"`
        Serial int `json:"serial,optional"`
    }

    TaskDeleteRequest {
        Id int `json:"id"`
    }

    TaskListRequest {
        PageSize int `json:"page_size,optional"`
        Page int `json:"page,optional"`
        Filter TaskFilter `json:"filter,optional"`
        PrimaryId int `json:"primary_id,optional"`
        ContributorId int `json:"contributor_id,optional"`
        SlowProcess bool `json:"slow_process,optional"`
        OrderBy string `json:"order_by,optional"`
    }

    TaskFilter {
        PersonId int `json:"person_id,optional"`
        StartDate string `json:"start_date,optional"`
        DueDate string `json:"due_date,optional"`
        Status int `json:"status,optional"`
        Type string `json:"type,optional"`
        Priority int `json:"priority,optional"`
        CreatorId int `json:"creator_id,optional"`
    }

    TaskListResponse {
        Tasks []TaskResponse `json:"tasks"`
        Total int `json:"total"`
        TotalPage int `json:"total_page"`
    }

    TaskResponse {
        Task
        Parent *Task `json:"parent"`
        Notes []TaskNoteResponse `json:"notes"`
        Children []Task `json:"children"`
        Creator *UserShort `json:"creator"`
        Assignment []TaskAssignmentResponse `json:"assignments"`
        TaskDepartment []TaskDepartmentResponse `json:"department_assignments"`
        Person *Person `json:"person"`
    }

    TaskDynamicQuery {
        DynamicQuery
        UserId int `json:"user_id,optional"`
    }

    BulkUpdateResult {
        SuccessCount int `json:"success_count"`              // Số task update thành công
        FailCount int `json:"fail_count"`                    // Số task update thất bại
        Errors []TaskUpdateError `json:"errors"`             // Danh sách các lỗi
    }

    TaskUpdateError {
        TaskID int `json:"task_id"`
        Error string `json:"error"`
    }

    TaskIsLastResponse {
        IsLast bool `json:"is_last"`
    }
)

@server(
    //jwt: Auth
    jwt: Auth
    group: task
    //middleware: AuthorizerMiddleware
    prefix: /v1/task
)
service bcare-api {
    @handler AddHandler
    post /add (TaskAddRequest) returns (Task);

    @handler GetHandler
    post /get (TaskGetRequest) returns (TaskResponse);

    @handler ListHandler
    post /list (TaskListRequest) returns (TaskListResponse);

    @handler QueryHandler
    post /query (TaskDynamicQuery) returns (DynamicQueryResponse);

    @handler UpdateHandler
    post /update (TaskUpdateRequest) returns (Task);

    @handler BulkUpdate
    post /bulk-update (BulkUpdateTasksRequest) returns (BulkUpdateResult);

    @handler DeleteHandler
    post /delete (TaskDeleteRequest) returns (Task);

    @handler BulkDelete
    post /bulk-delete (BulkDeleteRequest) returns (Task);

    @handler TaskIsLastHandler
    post /is-last (TaskGetRequest) returns (TaskIsLastResponse);
}