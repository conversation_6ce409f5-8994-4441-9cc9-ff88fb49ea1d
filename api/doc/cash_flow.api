info(
    desc: "cashflow api"
    author: "system"
)

type (
    CashFlow {
        Id int `json:"id"`
        Type string `json:"type,options=income|expense"`
        Amount int `json:"amount"`
        Cash int `json:"cash"`
        CreditCard int `json:"credit_card"`
        Mpos int `json:"mpos"`
        Bank int `json:"bank"`
        Momo int `json:"momo"`
        Description string `json:"description"`
        CreatorId int `json:"creator_id"`
        CounterpartId int `json:"counterpart_id"`
        RecipientId *int `json:"recipient_id,optional"`
        PayerId *int `json:"payer_id,optional"`
        State string `json:"state,options=PENDING|APPROVED|REJECTED|PAID|CANCELED"`
        ApproverId *int `json:"approver_id,optional"`
        PaidAt *string `json:"paid_at,optional"`
        Version int `json:"version"`
        CreatedAt string `json:"created_at"`
        UpdatedAt string `json:"updated_at"`
        DeletedAt *string `json:"deleted_at,optional"`
    }

    CashFlowWithRelations {
        CashFlow
        Items []CashFlowItemWithRelations `json:"items"`
        Notes []CashFlowNote `json:"notes"`
        Creator *UserShort `json:"creator,optional"`
        Approver *UserShort `json:"approver,optional"`
        Counterpart *UserShort `json:"counterpart,optional"`
        Recipient *UserShort `json:"recipient,optional"`
        Payer *UserShort `json:"payer,optional"`
    }

    CashFlowAddRequest {
        Type string `json:"type,options=income|expense"`
        Items []CashFlowItemBatchItem `json:"items"`
        Cash int `json:"cash,optional"`
        CreditCard int `json:"credit_card,optional"`
        Mpos int `json:"mpos,optional"`
        Bank int `json:"bank,optional"`
        Momo int `json:"momo,optional"`
        Amount int `json:"amount"`
        Description string `json:"description,optional"`
        CounterpartId int `json:"counterpart_id,optional"`
        RecipientId *int `json:"recipient_id,optional"`
        PayerId *int `json:"payer_id,optional"`
    }

    CashFlowUpdateStateRequest {
        Id int `json:"id"`
        State string `json:"state,options=APPROVED|REJECTED|PAID|CANCELED"`
        Comment *string `json:"comment,optional"`
    }

    CashFlowUpdateRequest {
        Id int `json:"id"`
        Cash int `json:"cash,optional"`
        CreditCard int `json:"credit_card,optional"`
        Mpos int `json:"mpos,optional"`
        Bank int `json:"bank,optional"`
        Momo int `json:"momo,optional"`
        Amount int `json:"amount,optional"`
        Items []CashFlowItemBatchItem `json:"items"`
        Description string `json:"description,optional"`
        CounterpartId int `json:"counterpart_id,optional"`
        RecipientId *int `json:"recipient_id,optional"`
        Modified []string `json:"modified,optional"`
    }

    CashFlowGetRequest {
        Id int `json:"id"`
        IncludeRelation bool `json:"include_relation,optional"`
    }

    CashFlowDeleteRequest {
        Id int `json:"id"`
    }

    CashFlowListRequest {
        PageSize int `json:"page_size,optional"`
        Page int `json:"page,optional"`
        Filter CashFlowFilter `json:"filter,optional"`
        Search string `json:"search,optional"`
        OrderBy string `json:"order_by,optional"`
    }

    CashFlowFilter {
        Type string `json:"type,optional,options=income|expense"`
        State string `json:"state,optional,options=PENDING|APPROVED|REJECTED|PAID|CANCELED"`
        CreatorId int `json:"creator_id,optional"`
        CounterpartId int `json:"counterpart_id,optional"`
        CategoryId int `json:"category_id,optional"`
        ApproverId int `json:"approver_id,optional"`
        RecipientId int `json:"recipient_id,optional"`
        PayerId int `json:"payer_id,optional"`
        FromDate string `json:"from_date,optional"`
        ToDate string `json:"to_date,optional"`
    }

    CashFlowResponse {
        CashFlowWithRelations
    }

    CashFlowListResponse {
        CashFlows []CashFlowResponse `json:"cash_flows"`
        Total int `json:"total"`
        TotalPage int `json:"total_page"`
    }

    CashFlowReportRequest {
        FromDate string `json:"from_date,optional"`
        ToDate string `json:"to_date,optional"`
    }

    CashFlowSummaryReport {
        TotalIncome float64 `json:"total_income"`
        TotalExpense float64 `json:"total_expense"`
        NetAmount int `json:"net_amount"`
        PendingApprovalCount int `json:"pending_approval_count"`
        PendingApprovalAmount int `json:"pending_approval_amount"`
        // Payment methods totals (paid transactions)
        TotalCashAll int `json:"total_cash_all"`
        TotalCreditCardAll int `json:"total_credit_card_all"`
        TotalMposAll int `json:"total_mpos_all"`
        TotalBankAll int `json:"total_bank_all"`
        TotalMomoAll int `json:"total_momo_all"`
        // Payment methods for pending transactions
        PendingCash int `json:"pending_cash"`
        PendingCreditCard int `json:"pending_credit_card"`
        PendingMpos int `json:"pending_mpos"`
        PendingBank int `json:"pending_bank"`
        PendingMomo int `json:"pending_momo"`
        IncomeByCategory []CategorySummary `json:"income_by_category"`
        ExpenseByCategory []CategorySummary `json:"expense_by_category"`
    }

    CategorySummary {
        CategoryId int `json:"category_id"`
        CategoryName string `json:"category_name"`
        Amount int `json:"amount"`
        Count int `json:"count"`
    }

    // New types for department-based summary
    DepartmentCategorySummary {
        CategoryId int `json:"category_id"`
        CategoryName string `json:"category_name"`
        Amount int `json:"amount"`
        Count int `json:"count"`
    }

    DepartmentSummary {
        DepartmentId int `json:"department_id"`
        DepartmentName string `json:"department_name"`
        TotalAmount int `json:"total_amount"`
        TotalCount int `json:"total_count"`
        Categories []DepartmentCategorySummary `json:"categories"`
    }

    // New types for category-first grouping (category → department)
    CategoryDepartmentSummary {
        DepartmentId int `json:"department_id"`
        DepartmentName string `json:"department_name"`
        Amount int `json:"amount"`
        Count int `json:"count"`
    }

    CategorySummaryDepartment {
        CategoryId int `json:"category_id"`
        CategoryName string `json:"category_name"`
        TotalAmount int `json:"total_amount"`
        TotalCount int `json:"total_count"`
        Departments []CategoryDepartmentSummary `json:"departments"`
    }

    CashFlowReportResponse {
        CashFlowSummaryReport
        Period string `json:"period"`
        GeneratedAt string `json:"generated_at"`
    }

        // New types for period-based reporting
    CashFlowPeriodRequest {
        PeriodType string `json:"period_type,options=week|month|year"`
        Date string `json:"date"` // Reference date in YYYY-MM-DD format
        PeriodCount int `json:"period_count,optional"` // Number of periods to retrieve (default: 4 - current + 3 previous)
    }

    PeriodSummary {
        Period string `json:"period"`            // e.g., "2024-W30", "2024-07", "2024"
        PeriodLabel string `json:"period_label"` // e.g., "Tuần 30 (17-23/07/2024)", "Tháng 7/2024", "Năm 2024"
        StartDate string `json:"start_date"`     // Start date of the period
        EndDate string `json:"end_date"`         // End date of the period
        TotalIncome int `json:"total_income"`
        TotalExpense int `json:"total_expense"`
        NetAmount int `json:"net_amount"`
        TransactionCount int `json:"transaction_count"`
                                                 // Payment methods totals
        TotalCash int `json:"total_cash"`
        TotalCreditCard int `json:"total_credit_card"`
        TotalMpos int `json:"total_mpos"`
        TotalBank int `json:"total_bank"`
        TotalMomo int `json:"total_momo"`
        // Department-first grouping (department → category)
        IncomeByDepartment []DepartmentSummary `json:"income_by_department"`
        ExpenseByDepartment []DepartmentSummary `json:"expense_by_department"`
        // Category-first grouping (category → department)
        IncomeByCategory []CategorySummaryDepartment `json:"income_by_category"`
        ExpenseByCategory []CategorySummaryDepartment `json:"expense_by_category"`
    }

    CashFlowPeriodResponse {
        CurrentPeriod PeriodSummary `json:"current_period"`
        PreviousPeriods []PeriodSummary `json:"previous_periods"` // Previous periods based on PeriodCount (PeriodCount-1 periods before current)
        PeriodType string `json:"period_type"`
        GeneratedAt string `json:"generated_at"`
    }
)

@server(
    jwt: Auth
    group: cash_flow
    prefix: /v1/cash-flow
)
service bcare-api {
    @handler AddHandler
    post /add (CashFlowAddRequest) returns (CashFlowResponse);

    @handler GetHandler
    post /get (CashFlowGetRequest) returns (CashFlowResponse);

    @handler ListHandler
    post /list (CashFlowListRequest) returns (CashFlowListResponse);

    @handler UpdateHandler
    post /update (CashFlowUpdateRequest) returns (CashFlowResponse);

    @handler UpdateStateHandler
    post /update-state (CashFlowUpdateStateRequest) returns (CashFlowResponse);

    @handler DeleteHandler
    post /delete (CashFlowDeleteRequest) returns (CashFlowResponse);

    @handler SummaryHandler
    post /summary (CashFlowReportRequest) returns (CashFlowReportResponse);

    @handler PeriodReportHandler
    post /period-report (CashFlowPeriodRequest) returns (CashFlowPeriodResponse);

    @handler ExportHandler
    post /export (CashFlowListRequest);
}

