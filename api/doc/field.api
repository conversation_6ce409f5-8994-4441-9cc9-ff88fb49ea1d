info(
    desc: "field api"
    author: "lamvh"
    email: "<EMAIL>"
)

type CustomField {
    FieldName string `json:"field_name"`
    FieldValue string `json:"field_value"`
}

type CommonFieldConfig {

    UUID string `json:"uuid"`
    FieldName string `json:"field_name"`
    EntityType string `json:"entity_type,options=product|user|person|attachment|track|task|appointment"`
    FieldType string `json:"field_type"`

    Translatable bool `json:"translatable"`
    Langcode string `json:"langcode"`
    Status int `json:"status"`
}

type FieldConfigRequest {
    CommonFieldConfig


    Bundle string `json:"bundle"`
    Label string `json:"label"`
    Description string `json:"description"`
    Required bool `json:"required"`
    DefaultValue string `json:"default_value"`
    DefaultValueCallback string `json:"default_value_callback"`
    FieldSettings map[string]string `json:"field_settings"`


    Cardinality int `json:"cardinality"`
    StorageSettings map[string]string `json:"storage_settings"`
}

type FieldConfig {
    CommonFieldConfig


    Bundle string `json:"bundle"`
    Label string `json:"label"`
    Description string `json:"description"`
    Required bool `json:"required"`
    DefaultValue string `json:"default_value"`
    DefaultValueCallback string `json:"default_value_callback"`
    FieldSettings map[string]string `json:"field_settings"`


    Dependencies Dependencies `json:"dependencies"`
}

type FieldStorageConfig {
    CommonFieldConfig


    Cardinality int `json:"cardinality"`
    StorageSettings map[string]string `json:"storage_settings"`


    Module string `json:"module"`
    Indexes map[string]string `json:"indexes"`
    PersistWithoutFields bool `json:"persist_with_no_fields"`
    CustomStorage bool `json:"custom_storage"`
    Locked bool `json:"locked"`
    Dependencies Dependencies `json:"dependencies"`
}


type Dependencies {
    Config []string `json:"config"`
    Module []string `json:"module"`
    Theme []string `json:"theme"`
    Content []string `json:"content"`
}

type FieldSettingsReference {
    Handler string `json:"handler"`
    HandlerSettings HandlerSettings `json:"handler_settings"`
}

type Sort {
    Field string `json:"field"`
    Direction string `json:"direction,options=asc|desc"`
}

type HandlerSettings {
    TargetBundles map[string]string `json:"target_bundles"`
    Sort Sort `json:"sort"`
    AutoCreate bool `json:"auto_create"`
    AutoCreateBundle string `json:"auto_create_bundle"`
}

type BundleFieldConfigResponse {
    Id int `json:"id"`
    Name string `json:"name"`
    Data string `json:"data"`
    Collection string `json:"collection"`
    Description string `json:"description"`
    Status int `json:"status"`
    CreatedAt string `json:"created_at"`
    UpdatedAt string `json:"updated_at"`
    DeletedAt string `json:"deleted_at"`
    Version int `json:"version"`
}

type CommonConfigResponse {
    Id int `json:"id"`
    Name string `json:"name"`
    Data FieldConfigRequest `json:"data"`
    Collection string `json:"collection"`
    Description string `json:"description"`
    Status int `json:"status"`
    CreatedAt string `json:"created_at"`
    UpdatedAt string `json:"updated_at"`
    DeletedAt string `json:"deleted_at"`
    Version int `json:"version"`
}


@server(
    //jwt: Auth
    jwt: Auth
    group: field
    //middleware: AuthorizerMiddleware
    prefix: /v1/field
)
service bcare-api {
    @handler AddHandler
    post /add (FieldConfigRequest) returns (CommonConfigResponse);

    @handler UpdateHandler
    post /update (FieldConfigRequest) returns (CommonConfigResponse);
}

