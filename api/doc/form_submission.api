info(
    title: "FormSubmission API"
    desc: "API for managing form submissions and converting to persons"
    author: "BcareTeam"
    version: "1.0"
)

type (
    // Cấu trúc dữ liệu cho FormSubmission
    FormSubmission {
        Id int `json:"id"`
        FullName string `json:"full_name"`
        Phone string `json:"phone"`
        Email string `json:"email"`
        Data map[string]interface{} `json:"data"`
        SourceUrl string `json:"source_url"`
        ReferrerUrl string `json:"referrer_url"`
        SourceId int `json:"source_id"`
        FormName string `json:"form_name"`
        State string `json:"state"` // pending, approved, rejected, processing
        ProcessedAt string `json:"processed_at,optional"`
        History []RecordHistoryEntry `json:"history,optional"`
        PersonId int `json:"person_id,optional"`
        Status int `json:"status"`
        CreatedAt string `json:"created_at"`
        UpdatedAt string `json:"updated_at"`
        DeletedAt string `json:"deleted_at"`
    }

        // Cấu trúc dữ liệu cho RecordHistoryEntry
    RecordHistoryEntry {
        UserId int `json:"user_id"`
        Changes []RecordFieldChange `json:"changes"`
        Timestamp string `json:"timestamp"`
    }

        // Cấu trúc dữ liệu cho RecordFieldChange
    RecordFieldChange {
        Field string `json:"field"`
        OldValue interface{} `json:"old_value"`
        NewValue interface{} `json:"new_value"`
    }

        // Danh sách FormSubmission
    FormSubmissionDynamicQuery {
        DynamicQuery
        Person string `json:"person"`
    }

    FormSubmissionsFilter {
        PersonId int `json:"person_id,optional"`
    }
    FormSubmissionsListRequest {
        PageSize int `json:"page_size,optional"`
        Page int `json:"page,optional"`
        Filter FormSubmissionsFilter `json:"filter,optional"`
        OrderBy string `json:"order_by,optional"`
    }

    FormSubmissionListResponse {
        Total int `json:"total"`
        TotalPage int `json:"total_page"`
        FormSubmissions []FormSubmissionResponse `json:"form_submissions"`
    }

        // Chuyển FormSubmission thành Person
    ConvertToPersonRequest {
        FormSubmissionIds []int `json:"form_submission_ids"`
    }

    ConvertToPersonResponse {
        Success bool `json:"success"`
        FormSubmission FormSubmission `json:"form_submission"`
    }

        // Xóa FormSubmission
    FormSubmissionDeleteRequest {
        Ids []int `json:"ids"`
    }

    FormSubmissionDeleteResponse {
        Success bool `json:"success"`
    }

    FormSubmissionResponse {
        FormSubmission
        Person *Person `json:"person"`
    }

    FormSubmissionError {
        FormSubmissionID int `json:"form_submission_id"`
        Error string `json:"error"`
    }

    BulkUpdateFormSubmissionResult {
        SuccessCount int `json:"success_count"`
        FailCount int `json:"fail_count"`
        Errors []FormSubmissionError `json:"errors"`             // Danh sách các lỗi
    }
)

@server(
    jwt: Auth
    prefix: /v1/formsubmission
    group: form_submission
)

service bcare-api {
    @handler ListFormSubmissions
    post /list (FormSubmissionsListRequest) returns (FormSubmissionListResponse)

    @handler QueryFormSubmissions
    post /query (FormSubmissionDynamicQuery) returns (DynamicQueryResponse)

    @handler ConvertToPerson
    post /convert-to-person (ConvertToPersonRequest) returns (BulkUpdateFormSubmissionResult)

    @handler DeleteFormSubmission
    post /delete (FormSubmissionDeleteRequest) returns (FormSubmissionDeleteResponse)
}