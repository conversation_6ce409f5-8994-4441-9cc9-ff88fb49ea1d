info(
    desc: "Task Assignment API"
    author: "Your Name"
    email: "<EMAIL>"
)

type (
    TaskAssignment {
        Id int `json:"id"`
        TaskId int `json:"task_id"`
        UserId int `json:"user_id"`
        Role string `json:"role,options=primary|contributor|reviewer"`
        State string `json:"state,options=pending|completed|overdue"`
        Status int `json:"status"`
        DueAt string `json:"due_at"`
        StatedAt string `json:"stated_at"`
        CompletedAt string `json:"completed_at"`
        CreatedAt string `json:"created_at"`
        UpdatedAt string `json:"updated_at"`
        DeletedAt string `json:"deleted_at"`
        Version int `json:"version"`
    }

    TaskAssignmentAddRequest {
        TaskId int `json:"task_id,optional"`
        UserId int `json:"user_id"`
        Role string `json:"role,options=primary|contributor|reviewer"`
        StatedAt string `json:"stated_at,optional"`
    }

    TaskAssignmentUpdateRequest {
        Id int `json:"id"`
        State string `json:"state,options=pending|completed|overdue,optional"`
        UserId int `json:"user_id,optional"`
        Role string `json:"role,options=primary|contributor|reviewer,optional"`
        StatedAt string `json:"stated_at,optional"`
    }

    AssignTasksRequest {
        IdList []int `json:"id_list"`
        Users []TaskAssignmentAddRequest `json:"users,optional"`
    }

    TaskAssignmentDeleteRequest {
        Id int `json:"id"`
    }

    TaskAssignmentResponse {
        TaskAssignment
        User *UserShort `json:"user"`
    }

    TaskAssignmentDynamicQuery {
        DynamicQuery
        Priority int `json:"priority,optional"` // độ ưu tiên trong task
    }
)

@server(
    jwt: Auth
    group: task_assignment
    prefix: /v1/task_assignment
)
service bcare-api {
    @handler AddHandler
    post /add (TaskAssignmentAddRequest) returns (TaskAssignment)

    @handler UpdateHandler
    post /update (TaskAssignmentUpdateRequest) returns (TaskAssignment)

    @handler AssignTasks
    post /assign-tasks (AssignTasksRequest) returns (TaskAssignment)

    @handler DeleteHandler
    post /delete (TaskAssignmentDeleteRequest) returns (TaskAssignment)

    @handler QueryHandler
    post /query (TaskAssignmentDynamicQuery) returns (DynamicQueryResponse);

}


