info(
    desc: "location api"
    author: "lamvh"
    email: "<EMAIL>"
)

type (
    LocalProvince {
        Id   int `json:"id"`
        Name string `json:"name"`
        Code string `json:"code"`
    }
    LocalDistrict {
        Id          int `json:"id"`
        Name        string `json:"name"`
        Prefix      string `json:"prefix"`
        ProvinceId  int `json:"province_id"`
    }

    LocalWard  {
        Id        int `json:"id"`
        Name       string `json:"name"`
        Prefix     string `json:"prefix"`
        ProvinceId int `json:"province_id"`
        DistrictId int `json:"district_id"`
    }
    LocationRequest {

    }

    LocationResponse {
        Province    []LocalProvince `json:"provinces"`
        District    []LocalDistrict `json:"districts"`
        Ward        []LocalWard `json:"wards"`
    }
)

@server(
    //jwt: Auth
    jwt: Auth
    group: location
    //middleware: AuthorizerMiddleware
    prefix: /v1/location
)
service bcare-api {
    @handler GetHandler
    post / (LocationRequest) returns (LocationResponse);
}