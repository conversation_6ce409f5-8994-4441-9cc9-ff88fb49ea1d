info(
    desc: "schedule api"
    author: "lamvh"
    email: "<EMAIL>"
)

type (
    Schedule {
        Id int `json:"id" db:"id"`
        UserId int `json:"user_id" db:"user_id"`
        Status int `json:"status" db:"status"`
        StartTime string `json:"start_time" db:"start_time"`
        EndTime string `json:"end_time" db:"end_time"`
        Stage int `json:"stage" db:"stage"`
        Version int `json:"version" db:"version"`
        CreatedAt string `json:"created_at" db:"created_at"`
        UpdatedAt string `json:"updated_at" db:"updated_at"`
        DeletedAt string `json:"deleted_at" db:"deleted_at"`
    }

    ScheduleAddRequest {
        UserId int `json:"user_id" db:"user_id"`
        StartTime string `json:"start_time" db:"start_time"`
        EndTime string `json:"end_time" db:"end_time"`
    }

    ScheduleDeleteRequest {
        Id int `json:"id"`
    }

    ScheduleListRequest {
        UserId int `json:"user_id,optional"`
        StartTime string `json:"start_time,optional"`
        EndTime string `json:"end_time,optional"`
    }

    ScheduleResponse {
        Schedule
        User UserShort `json:"user"`
    }

    ScheduleListResponse {
        Schedules []ScheduleResponse `json:"schedules"`
    }


    ScheduleUpdateRequest {
        Id int `json:"id"`
        StartTime string `json:"start_time,optional"`
        EndTime string `json:"end_time,optional"`
        Stage int `json:"stage,optional"`
        Modified []string `json:"modified,optional"`
    }

    //todo: đổi tên thành ScheduleListRequest
    ScheduleRequest {
        From string `json:"from"`
        To string `json:"to"`
        UserId int `json:"user_id,optional"`
    }
)

@server(
    //jwt: Auth
    jwt: Auth
    group: schedule
    //middleware: AuthorizerMiddleware
    prefix: /v1/schedule
)

service bcare-api {
    @handler getWorkSchedule
    post /get-work-schedule (ScheduleRequest) returns (ScheduleListResponse);

    @handler AddHandler
    post /add (ScheduleAddRequest) returns (ScheduleResponse);

    @handler UpdateHandler
    post /update (ScheduleUpdateRequest) returns (Schedule);

    @handler ListHandler
    post /list (ScheduleRequest) returns (ScheduleListResponse);

    @handler DeleteHandler
    post /delete (ScheduleDeleteRequest) returns (ScheduleResponse);
}