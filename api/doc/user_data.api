info(
    desc: "User Data API"
    author: "lamvh"
    email: "<EMAIL>"
)

type (
    GetUserDataRequest {
        UserID int `json:"user_id"`
        Kind string `json:"kind,optional"`
        Key string `json:"key,optional"`
    }

    SetUserDataRequest {
        UserID int `json:"user_id"`
        Kind string `json:"kind"`
        Key string `json:"key"`
        Value string `json:"value"`
        Data map[string]interface{} `json:"data,optional"`
    }

    ClearUserDataRequest {
        UserID int `json:"user_id"`
        Kind string `json:"kind,optional"`
        Key string `json:"key,optional"`
    }

    UserDataResponse {
        Kind string `json:"kind"`
        Data map[string]interface{} `json:"data"`
    }

)

@server(
    jwt: Auth
    group: user_data
    prefix: /v1/user
)

service bcare-api {
    @handler GetData
    post /data/get (GetUserDataRequest) returns (UserDataResponse)

    @handler SetData
    post /data/set (SetUserDataRequest) returns (CommonResponse)

    @handler SetArrayData
    post /data/set-array (SetUserDataRequest) returns (CommonResponse)

    @handler ClearData
    post /data/clear (ClearUserDataRequest) returns (CommonResponse)
}