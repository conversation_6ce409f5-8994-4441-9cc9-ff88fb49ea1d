info(
    desc: "person api"
    author: "lamvh"
    email: "<EMAIL>"
)

type (
    PersonMeta {
        TreatmentId *int `json:"treatment_id,omitempty,optional"`
        TreatmentStatusId *int `json:"treatment_status_id,omitempty,optional"`
        Description *string `json:"description,omitempty,optional"`
        HasZalo string `json:"has_zalo,options=yes|no|unknown"`
        SecondaryPhone *string `json:"secondary_phone,omitempty,optional"`
        Code *string `json:"code,omitempty,optional"`
        MedicalCondition *string `json:"medical_condition,omitempty,optional"`
        SpecialNote *string `json:"special_note,omitempty,optional"`
        AccountId *string `json:"account_id,omitempty,optional"`
        PancakeLink *string `json:"pancake_link,omitempty,optional"`
    }

    Person {
        Id int `json:"id"`
        FullName string `json:"full_name"`
        DateOfBirth string `json:"date_of_birth"`
        Gender string `json:"gender"`
        ProvinceId int `json:"province_id"`
        DistrictId int `json:"district_id"`
        WardId int `json:"ward_id"`
        AddressNumber string `json:"address_number"`
        Phone string `json:"phone"`
        Email string `json:"email"`
        PhoneConfirm bool `json:"confirm_phone"`
        EmailConfirm bool `json:"confirm_mail"`
        JobId int `json:"job_id"`
        SourceId int `json:"source_id"`
        Status int `json:"status"`
        Version int `json:"version"`
        UserId int `json:"user_id"`
        CreatedAt string `json:"created_at"`
        UpdatedAt string `json:"updated_at"`
        DeletedAt string `json:"deleted_at"`
    }

    PersonSort {
        Id int `json:"id"`
        FullName string `json:"full_name"`
        Phone string `json:"phone"`
        Gender string `json:"gender"`
    }

    PersonAddRequest {
        FullName string `json:"full_name"`
        DateOfBirth string `json:"date_of_birth,optional"`
        Gender string `json:"gender,options=male|female|unknown"`
        ProvinceId int `json:"province_id,optional"`
        DistrictId int `json:"district_id,optional"`
        WardId int `json:"ward_id,optional"`
        AddressNumber string `json:"address_number,optional"`
        Phone string `json:"phone"`
        Email string `json:"email,optional"`
        JobId *int `json:"job_id,optional"`
        SourceId *int `json:"source_id,optional"`
        Status int `json:"status,range=[0:10],optional"`
        PersonField PersonMeta `json:"person_field,optional"`
        ReferredBy *ReferredData `json:"referred_by,optional"`
        IsSystemCreated bool `json:"is_system_created,optional"`
    }

    ReferredData {
        ReferrerId int `json:"referrer_id,optional"`
        Relationship string `json:"relationship,optional"`
        Note string `json:"note,optional"`
    }

    PersonResponse {
        Person
        StageName string `json:"stage_name"`
        Referrer *ReferredData `json:"referred_by"`
        PersonField PersonMeta `json:"person_field"`
        Creator *UserShort `json:"creator"`
        Sale *UserShort `json:"sale"`
        Issue []Issue `json:"issues"`
        Assignment []PersonAssignmentResponse `json:"assignment"`
        Stage []PersonStageResponse `json:"person_stages"`
    }

    PersonSortResponse {
        PersonSort
        PersonField PersonMeta `json:"person_field"`
        Issue []Issue `json:"issues"`
    }

    PersonUpdateRequest {
        Id int `json:"id"`
        FullName string `json:"full_name,optional"`
        DateOfBirth string `json:"date_of_birth,optional"`
        Gender string `json:"gender,options=male|female|unknown,optional"`
        ProvinceId int `json:"province_id,optional"`
        DistrictId int `json:"district_id,optional"`
        WardId int `json:"ward_id,optional"`
        AddressNumber string `json:"address_number,optional"`
        Phone string `json:"phone,optional"`
        Email string `json:"email,optional"`
        JobId *int `json:"job_id,optional"`
        SourceId *int `json:"source_id,optional"`
        Status int `json:"status,range=[0:10],optional"`
        PersonField PersonMeta `json:"person_field,optional"`
        ReferredBy *ReferredData `json:"referred_by,optional"`
        Modified []string `json:"modified,optional"`
    }

    PersonGetRequest {
        Id int `json:"id"`
        FullName string `json:"full_name,optional"`
        Phone string `json:"phone,optional"`
        Email string `json:"email,optional"`
        IncludeRelation bool `json:"include_relation,optional"`
    }

    PersonDeleteRequest {
        Id int `json:"id"`
        FullName string `json:"full_name"`
    }

    PersonListRequest {
        PageSize int `json:"page_size,optional"`
        Page int `json:"page,optional"`
        Filter PersonFilter `json:"filter,optional"`
        UserId int `json:"user_id,optional"`
        Search string `json:"search,optional"`
        OrderBy string `json:"order_by,optional"`
    }

    PersonFilter {
        Gender string `json:"gender,optional"`
        Phone string `json:"phone,optional"`
        Email string `json:"email,optional"`
        JobId int `json:"job_id,optional"`
        SourceId int `json:"source_id,optional"`
        Status int `json:"status,range=[0:10],optional"`
    }

    PersonFieldFilter {

    }

    PersonListResponse {
        Persons []PersonResponse `json:"persons"`
        Total int `json:"total"`
        TotalPage int `json:"total_page"`
    }

    ExpectedTasksResponse {
        ExpectedTask string `json:"expected_task"`
    }



    PersonDynamicQuery {
        DynamicQuery
        Creator string `json:"creator"` // tên, id
        Sale string `json:"sale"` // tên, id
        Doctor string `json:"doctor_id"`
        Search string `json:"search"`
        Export bool `json:"export,optional"`
    }

    PersonTimelineRequest {
        DynamicQuery
    }

    PersonTimeline{
        PersonId int `json:"person_id"`
        EntityId int `json:"entity_id"`
        Kind string `json:"kind"`
        Data map[string]interface{} `json:"data"`
        CreatedAt string `json:"created_at"`
    }

)

@server(
    //jwt: Auth
    jwt: Auth
    group: person
    //middleware: AuthorizerMiddleware
    prefix: /v1/person
)
service bcare-api {
    @handler AddHandler
    post /add (PersonAddRequest) returns (PersonResponse);

    @handler GetHandler
    post /get (PersonGetRequest) returns (PersonResponse);

    @handler ListHandler
    post /list (PersonListRequest) returns (PersonListResponse);

    @handler QueryHandler
    post /query (PersonDynamicQuery) returns (DynamicQueryResponse);

    @handler UpdateHandler
    post /update (PersonUpdateRequest) returns (PersonResponse);

    @handler DeleteHandler
    post /delete (PersonDeleteRequest) returns (PersonResponse);

    @handler GetExpectedTasks
    post /expect-task (PersonGetRequest) returns (ExpectedTasksResponse);

    @handler IsPersonInHandler
    post /is-person-in (PersonGetRequest) returns (bool);

    @handler TimelineHandler
    post /timeline (PersonTimelineRequest) returns (DynamicQueryResponse);
}
