info(
    desc: "Person Data API"
    author: "lamvh"
    email: "<EMAIL>"
)

type (
    GetDataRequest {
        PersonID int `json:"person_id"`
        Kind string `json:"kind,optional"`
        Key string `json:"key,optional"`
    }

    SetDataRequest {
        PersonID int `json:"person_id"`
        Kind string `json:"kind"`
        Key string `json:"key"`
        Value string `json:"value"`
        Data map[string]interface{} `json:"data,optional"`
    }

    ClearDataRequest {
        PersonID int `json:"person_id"`
        Kind string `json:"kind,optional"`
        Key string `json:"key,optional"`
    }

    DataResponse {
        Data map[string]interface{} `json:"data"`
    }

    CommonResponse {
        Count int `json:"count"`
    }
)

@server(
    jwt: Auth
    group: person_data
    prefix: /v1/person
)

service bcare-api {
    @handler GetData
    post /data/get (GetDataRequest) returns (DataResponse)

    @handler SetData
    post /data/set (SetDataRequest) returns (CommonResponse)

    @handler ClearData
    post /data/clear (ClearDataRequest) returns (CommonResponse)
}