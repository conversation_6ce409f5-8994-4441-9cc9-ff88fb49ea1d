info(
    desc: "file usage api"
    author: "lamvh"
    email: "<EMAIL>"
)

type (
    FileUsage {
        Id int `json:"id"`
        FileId int `json:"file_id"`
        EntityType string `json:"entity_type"`
        EntityId int `json:"entity_id"`
        UsageType string `json:"usage_type"`
        UsageMeta string `json:"usage_meta"`
        Status int `json:"status"`
        CreatedAt string `json:"created_at"`
        UpdatedAt string `json:"updated_at"`
        DeletedAt string `json:"deleted_at"`
        Version int `json:"version"`
    }

    FileUsageAddRequest {
        FileId int `json:"file_id"`
        EntityType string `json:"entity_type"`
        EntityId int `json:"entity_id"`
        UsageType string `json:"usage_type"`
        UsageMeta string `json:"usage_meta"`
        TrackId int `json:"track_id,optional"`
    }

    FileUsageResponse {
        FileUsage
        File File `json:"file"`
        Track Track `json:"track"`
    }

    FileUsageUpdateRequest {
        Id int `json:"id"`
        FileId int `json:"file_id,optional"`
        EntityType string `json:"entity_type,optional"`
        EntityId int `json:"entity_id,optional"`
        UsageType string `json:"usage_type,optional"`
        UsageMeta string `json:"usage_meta,optional"`
        Modified []string `json:"modified,optional"`
    }

    FileUsageGetRequest {
        Id int `json:"id"`
    }

    FileUsageDeleteRequest {
        Id int `json:"id"`
    }

    FileUsageListRequest {
        PageSize int `json:"page_size,optional"`
        Page int `json:"page,optional"`
        Filter FileUsageFilter `json:"filter,optional"`
        OrderBy string `json:"order_by,optional"`
    }

    FileUsageFilter {
        Search string `json:"search"`
        FileId int `json:"file_id"`
        EntityType string `json:"entity_type"`
        EntityId int `json:"entity_id"`
        UsageType string `json:"usage_type"`
    }

    FileUsageListResponse {
        FileUsages []FileUsageResponse `json:"file_usages"`
        Total int `json:"total"`
        TotalPage int `json:"total_page"`
    }
)

@server(
    jwt: Auth
    group: file_usage
    prefix: /v1/file_usage
)
service bcare-api {
    @handler AddHandler
    post /add (FileUsageAddRequest) returns (FileUsageResponse);

    @handler GetHandler
    post /get (FileUsageGetRequest) returns (FileUsageResponse);

    @handler ListHandler
    post /list (FileUsageListRequest) returns (FileUsageListResponse);

    @handler UpdateHandler
    post /update (FileUsageUpdateRequest) returns (FileUsageResponse);

    @handler DeleteHandler
    post /delete (FileUsageDeleteRequest) returns (FileUsageResponse);
}