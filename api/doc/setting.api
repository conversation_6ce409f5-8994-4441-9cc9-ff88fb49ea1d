info(
    desc: "Setting API"
    author: "Your Name"
    email: "<EMAIL>"
)

type (
    Setting {
        Id int `json:"id"`
        Category string `json:"category"`
        Name string `json:"name"`
        Value map[string]interface{} `json:"value"`
        Description string `json:"description"`
    }

    SettingAddRequest {
        Category string `json:"category"`
        Name string `json:"name"`
        Value map[string]interface{} `json:"value"`
        Description string `json:"description"`
    }

    SettingUpdateRequest {
        Id int `json:"id"`
        Category string `json:"category,optional"`
        Name string `json:"name,optional"`
        Value map[string]interface{} `json:"value,optional"`
        Description string `json:"description,optional"`
        Modified []string `json:"modified,optional"`
    }

    SettingSyncRequest {
        Category string `json:"category"`
        Name string `json:"name"`
        Value map[string]interface{} `json:"value"`
        Description string `json:"description,optional"`
    }

    SettingDeleteRequest {
        Id int `json:"id"`
    }

    SettingListRequest {
        Category string `json:"category,optional"`
        Name string `json:"name,optional"`
    }

    SettingListResponse {
        Settings []Setting `json:"settings"`
    }

    CallSetting {
        ApiKey string `json:"api_key"`
        ApiSecret string `json:"api_secret"`
        ApiUrl string `json:"api_url"`
        Uri string `json:"uri"`
        UriWs string `json:"uri_ws"`
        LineId string `json:"line_id"`
        Password string `json:"password"`
    }

    SmsSetting {
        SmsApiUrl string `json:"sms_api_url"`
        SmsUsername string `json:"sms_username"`
        SmsPassword string `json:"sms_password"`
        SmsBrandName string `json:"sms_brand_name"`
    }

    ZnsSetting {
        SecretKey string `json:"secret_key"`
        Appid string `json:"app_id"`
        UrlTemplate string `json:"url_template"`
        UrlRefreshToken string `json:"url_refresh_token"`
        AccessToken string `json:"access_token"`
        RefreshToken string `json:"refresh_token"`
    }

    PaymentTemplate {
        Content string `json:"content"`
        Name string `json:"name"`
        Params map[string]string `json:"params"`
        ZnsID string `json:"zns_id,optional"`
    }

    PaymentTemplateGroup {
        Zns PaymentTemplate `json:"zns"`
        Sms PaymentTemplate `json:"sms"`
    }

    PaymentMessageTemplates {
        WithSchedule PaymentTemplateGroup `json:"with_schedule"`
        WithoutSchedule PaymentTemplateGroup `json:"without_schedule"`
    }
)

@server(
    jwt: Auth
    group: setting
    prefix: /v1/setting
)
service bcare-api {
    @handler AddHandler
    post /add (SettingAddRequest) returns (Setting)

    @handler UpdateHandler
    post /update (SettingUpdateRequest) returns (Setting)

    @handler DeleteHandler
    post /delete (SettingDeleteRequest) returns (Setting)

    @handler ListHandler
    post /list (SettingListRequest) returns (SettingListResponse)

    @handler SyncHandler
    post /sync (SettingSyncRequest) returns (Setting)
}