info(
    desc: "activity api"
    author: "lamvh"
    email: "<EMAIL>"
)

type (

    ListRequest {
        PersonId int `json:"person_id"`
    }


    ListResponse {
        Activities []ActivityRecord `json:"activities"`
    }

    ActivityRecord {
        Type string `json:"type"`
        Data interface{} `json:"data"`
    }

    Activity {
        EntityId int `json:"entity_id"`
        Kind string `json:"kind"`
        Data map[string]interface{} `json:"record"`
        PersonId int `json:"person_id"`
        CreatedAt string `json:"created_at"`
    }

    ActivityDynamicQuery {
        DynamicQuery
    }
)

@server(
    jwt: Auth
    group: activity
    prefix: /v1/activity
)
service bcare-api {
    @handler ListHandler
    post /list (ListRequest) returns (ListResponse)

    @handler QueryHandler
    post /query (ActivityDynamicQuery) returns (DynamicQueryResponse);
}