info(
    desc: "Query API"
    author: "vhlam"
    email: "<EMAIL>"
)

type DynamicQuery {
    Table string `json:"table"`
    Selects []string `json:"selects,optional"`
    Filters []Filter `json:"filters,optional"`
    GroupBy []string `json:"group_by,optional"`
    Aggregations []Aggregation `json:"aggregations,optional"`
    Sort []SortCriteria `json:"sort,optional"`
    Limit int `json:"limit,optional"`
    Offset int `json:"offset,optional"`
    Joins []JoinInfo `json:"joins,optional"`
}

type Filter {
    Field string `json:"field,optional"`
    Operator string `json:"operator,optional,options=EQ|NEQ|GT|GTE|LT|LTE|IN|NOTIN|LIKE|NOTLIKE|BETWEEN|ISNULL|ISNOTNULL|TODAY|CONTAINS|JSON_CONTAINS_ANY"`
    Value string `json:"value,optional"`
    Logic string `json:"logic,optional,options=AND|OR"`
    Conditions []Filter `json:"conditions,optional"`
    Function string `json:"function,optional,options=TO_CHAR|DATE|YEAR|MONTH|DAY|LOWER|UPPER|UNACCENT"`
}

type Aggregation {
    Field string `json:"field"`
    Function string `json:"function,options=SUM|AVG|COUNT|MIN|MAX"`
    Alias string `json:"alias"`
    Over *WindowSpecification `json:"over,optional"`  // Optional window specification for aggregation
}

type SortCriteria {
    Field string `json:"field"`
    Order string `json:"order,options=ASC|DESC"`
}

type JoinInfo {
    Table string `json:"table"`
    Alias string `json:"alias,optional"`
    C1 string `json:"c1,optional"`
    C2 string `json:"c2,optional"`
    Type string `json:"type,optional,options=LEFT|INNER|RIGHT|FULL"`
}

type WindowSpecification {
    PartitionBy []string `json:"partition_by,optional,omitempty"`  // Fields for partitioning
    OrderBy []SortCriteria `json:"order_by,optional,omitempty"`  // Fields for ordering within the window
}

type DynamicQueryResponse {
    Result map[string]interface{} `json:"result"`
}

@server(
    group: query
    prefix: /v1/query
)

service bcare-api {
    @handler QueryHandler
    post /query (DynamicQuery) returns (DynamicQueryResponse)
}
