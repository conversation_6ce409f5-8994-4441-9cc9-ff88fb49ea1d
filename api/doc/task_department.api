info(
    desc: "Department Assignment API"
    author: "Your Name"
    email: "<EMAIL>"
)

type (
    TaskDepartment {
        Id int `json:"id"`
        TaskId int `json:"task_id"`
        DepartmentId int `json:"department_id"`
        CompleteBy int `json:"complete_by"`
        Role string `json:"role,options=primary|contributor|reviewer"`
        State string `json:"state,options=pending|completed|overdue"`
        Status int `json:"status"`
        DueAt string `json:"due_at"`
        StatedAt string `json:"stated_at"`
        CompletedAt string `json:"completed_at"`
        CreatedAt string `json:"created_at"`
        UpdatedAt string `json:"updated_at"`
        DeletedAt string `json:"deleted_at"`
        Version int `json:"version"`
    }

    TaskDepartmentAddRequest {
        TaskId int `json:"task_id,optional"`
        DepartmentId int `json:"department_id"`
        Role string `json:"role,options=primary|contributor|reviewer,optional"`
        StatedAt string `json:"stated_at,optional"`
    }

    TaskDepartmentUpdateRequest {
        Id int `json:"id"`
        State string `json:"state,options=pending|ongoing|completed,optional"`
        Role string `json:"role,options=primary|contributor|reviewer,optional"`
        DepartmentId int `json:"department_id,optional"`
        CompleteBy int `json:"complete_by,optional"`
        StatedAt string `json:"stated_at,optional"`
    }

    TaskDepartmentDeleteRequest {
        Id int `json:"id"`
    }

    TaskDepartmentResponse {
        TaskDepartment
        Department *DepartmentShort `json:"department"`
        Compeleter *UserShort `json:"compeleter"`
    }

    TaskDepartmentDynamicQuery {
        DynamicQuery
        Priority int `json:"priority,optional"` // độ ưu tiên trong task
    }
)

@server(
    jwt: Auth
    group: task_department
    prefix: /v1/task_department
)
service bcare-api {
    @handler AddHandler
    post /add (TaskDepartmentAddRequest) returns (TaskDepartment)

    @handler UpdateHandler
    post /update (TaskDepartmentUpdateRequest) returns (TaskDepartment)

    @handler DeleteHandler
    post /delete (TaskDepartmentDeleteRequest) returns (TaskDepartment)

    @handler QueryHandler
    post /query (TaskDepartmentDynamicQuery) returns (DynamicQueryResponse);
}