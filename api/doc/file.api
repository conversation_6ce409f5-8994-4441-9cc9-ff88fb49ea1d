info(
    desc: "file api"
    author: "lamvh"
    email: "<EMAIL>"
)

type (
    File {
        Id          int  `json:"id"`
        Name        string `json:"name"`
        Kind        string `json:"kind"`
        Type        string `json:"type"`
        Size        int  `json:"size"`
        Path        string `json:"path"`
        UserId      int  `json:"user_id"`
        Storage     string `json:"storage"`
        Meta        string `json:"meta"`
        Status      int    `json:"status"`
        CreatedAt   string `json:"created_at"`
        UpdatedAt   string `json:"updated_at"`
        DeletedAt   string `json:"deleted_at"`
        Version     int    `json:"version"`
    }

    FileAddRequest {
        Kind        string `form:"kind,optional"`
        Storage     string `form:"storage,optional"`
    }

    FileResponse {
        File
    }

    FileUpdateRequest {
        Id          int  `json:"id"`
        Name        string `json:"name,optional"`
        Kind        string `json:"kind,optional"`
        Type        string `json:"type,optional"`
        Size        int  `json:"size,optional"`
        Path        string `json:"path,optional"`
        UserId      int  `json:"user_id,optional"`
        Storage     string `json:"storage,optional"`
        Meta        string `json:"meta,optional"`
        Modified []string `json:"modified,optional"`
    }

    FileGetRequest {
        Id   int  `json:"id"`
        Name string `json:"name"`
    }

    FileDeleteRequest {
        Id   int  `json:"id"`
        Name string `json:"name"`
    }

    FileListRequest {
        PageSize int        `json:"page_size,optional"`
        Page     int        `json:"page,optional"`
        Filter   FileFilter `json:"filter,optional"`
        OrderBy  string       `json:"order_by,optional"`
    }

    FileFilter {
        Search      string `json:"search,optional"`
        Name        string `json:"name,optional"`
        Kind        string `json:"kind,optional"`
        Type        string `json:"type,optional"`
        UserId      int  `json:"user_id,optional"`
        Storage     string `json:"storage,optional"`
    }

    FileListResponse {
        Files []FileResponse `json:"files"`
        Total     int              `json:"total"`
        TotalPage int              `json:"total_page"`
    }
)

@server(
    jwt: Auth
    group: file
    prefix: /v1/file
    maxBytes: 20971520
)
service bcare-api {
    @handler AddHandler
    post /add (FileAddRequest) returns (FileResponse);

    @handler GetHandler
    post /get (FileGetRequest) returns (FileResponse);

    @handler ListHandler
    post /list (FileListRequest) returns (FileListResponse);

    @handler UpdateHandler
    post /update (FileUpdateRequest) returns (FileResponse);

    @handler DeleteHandler
    post /delete (FileDeleteRequest) returns (FileResponse);
}