info(
    desc: "Deal user API"
    author: "Your Name"
    email: "<EMAIL>"
)


type (
    DealUser {
        Id int `json:"id"`
        DealId int `json:"deal_id"`
        UserId int `json:"user_id"`
        Role string `json:"role"`
        Point map[string]interface{} `json:"point"`
    }

    DealUserAddRequest {
        DealId int `json:"deal_id"`
        UserId int `json:"user_id"`
        Role string `json:"role"`
    }

    DealUserUpdateRequest {
        Id int `json:"id"`
        UserId int `json:"user_id,optional"`
        Point map[string]interface{} `json:"point,optional"`
    }

    DealUserListRequest {
        DealId int `json:"deal_id"`
    }

    DealUserDeleteRequest {
        DealId int `json:"deal_id"`
        UserId int `json:"user_id"`
        Role string `json:"role"`
    }

    DealUserResponse {
        DealUser
        Name string `json:"name"`
        User *UserShort `json:"user"`
        Rating []DealUserRating `json:"ratings"`
    }

    DealUserListResponse {
        DealUsers []DealUserResponse `json:"deal_users"`
    }

    DealUserInfo {
        Id int64 `json:"id,omitempty"`
        Username string `json:"username"`
        Email string `json:"email,omitempty"`
        Name string `json:"name,omitempty"`
        DepartmentId int64 `json:"department_id,omitempty"`
        Role string `json:"role,omitempty"`
        Point map[string]interface{} `json:"point,omitempty"`
    }
)

@server(
    jwt: Auth
    group: deal_user
    prefix: /v1/deal_user
)
service bcare-api {
    @handler AddHandler
    post /add (DealUserAddRequest) returns (DealUser)

    @handler UpdateHandler
    post /update (DealUserUpdateRequest) returns (DealUser)

    @handler ListHandler
    post /list (DealUserListRequest) returns (DealUserListResponse)

    @handler DeleteHandler
    post /delete (DealUserDeleteRequest) returns (DealUser)
}