info(
    desc: "auth api"
    author: "khoapd"
    email: "<EMAIL>"
)

type (
    LoginRequest {
        Username string `json:"username"`
        Password string `json:"password"`
    }

    LoginResponse {
        User          UserResponse   `json:"user"`
        Status        string `json:"status"`
        AccessToken   string `json:"access_token"`
        RefreshToken  string `json:"refresh_token"`
        AccessExpire  int    `json:"access_expire"`
        RefreshExpire int    `json:"refresh_expire"`
        RefreshAfter  int    `json:"refresh_after"`
        Message   string `json:"message"` // "OTP sent successfully"
        SentTo    string `json:"sent_to"` // Masked email/phone
        ExpiresIn int    `json:"expires_in"` // OTP expiration in seconds
    }

    // Second step: Verify OTP and get tokens
    VerifyRequest {
        Username string `json:"username"`
        OTP      string `json:"otp"`
    }

    VerifyResponse {
        User          UserResponse   `json:"user"`
        Status        string `json:"status"`
        AccessToken   string `json:"access_token"`
        RefreshToken  string `json:"refresh_token"`
        AccessExpire  int    `json:"access_expire"`
        RefreshExpire int    `json:"refresh_expire"`
        RefreshAfter  int    `json:"refresh_after"`
    }

    RefreshTokenRequest {
        RefreshToken string `json:"refresh_token"`
        Id          int    `json:"id"`
    }

    RefreshTokenResponse {
        Id           int    `json:"id"`
        AccessToken  string `json:"access_token"`
        RefreshToken string `json:"refresh_token"`
        AccessExpire int    `json:"access_expire"`
        RefreshExpire int   `json:"refresh_expire"`
        RefreshAfter int    `json:"refresh_after"`
    }
)

@server(
    group: auth
    prefix: /v1/auth
)
service bcare-api {
    @handler LoginHandler
    post /login (LoginRequest) returns (LoginResponse)

    @handler VerifyHandler
    post /verify (VerifyRequest) returns (VerifyResponse)

    @handler RefreshHandler
    post /refresh (RefreshTokenRequest) returns (RefreshTokenResponse)
}