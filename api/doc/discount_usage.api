info(
    desc: "API sử dụng giảm giá"
    author: "lamvh"
    email: "<EMAIL>"
)

type (
    DiscountUsage {
        Id int `json:"id"`
        DiscountId int `json:"discount_id"`
        UserId *int `json:"user_id,omitempty"`
        PersonId *int `json:"person_id,omitempty"`
        AttachmentId *int `json:"attachment_id,omitempty"`
        DealId *int `json:"deal_id,omitempty"`
        Value float64 `json:"value"`
        UsageCount int `json:"usage_count"`
        CreatedAt string `json:"created_at"`
        UpdatedAt string `json:"updated_at"`
        DeletedAt string `json:"deleted_at"`
        Version int `json:"version"`
    }

    DiscountUsageAddRequest {
        DiscountId int `json:"discount_id"`
        UserId *int `json:"user_id,optional"`
        PersonId *int `json:"person_id,optional"`
        AttachmentId *int `json:"attachment_id,optional"`
        DealId *int `json:"deal_id,optional"`
        Value float64 `json:"value"`
    }

    DiscountUsageResponse {
        Id int `json:"id"`
        DiscountId int `json:"discount_id"`
        UserId *int `json:"user_id,omitempty"`
        PersonId *int `json:"person_id,omitempty"`
        AttachmentId *int `json:"attachment_id,omitempty"`
        DealId *int `json:"deal_id,omitempty"`
        UsageCount int `json:"usage_count"`
        Value float64 `json:"value"`
        Discount Discount `json:"discount"`
        CreatedAt string `json:"created_at"`
    }

    DiscountUsageDeleteRequest {
        Id int `json:"id"`
    }

    DiscountUsageListRequest {
        PageSize int `json:"page_size,optional"`
        Page int `json:"page,optional"`
        Filter DiscountUsageFilter `json:"filter,optional"`
        OrderBy string `json:"order_by,optional"`
    }

    DiscountUsageFilter {
        Search string `json:"search,optional"`
        DiscountId int `json:"discount_id,optional"`
        UserId *int `json:"user_id,optional"`
        PersonId *int `json:"person_id,optional"`
        AttachmentId *int `json:"attachment_id,optional"`
        DealId *int `json:"deal_id,optional"`
    }

    DiscountUsageListResponse {
        DiscountUsages []DiscountUsageResponse `json:"discount_usages"`
        Total int `json:"total"`
        TotalPage int `json:"total_page"`
    }
)

@server(
    jwt: Auth
    group: discount_usage
    prefix: /v1/discount_usage
)
service bcare-api {
    @handler AddHandler
    post /add (DiscountUsageAddRequest) returns (DiscountUsageResponse);

    @handler ListHandler
    post /list (DiscountUsageListRequest) returns (DiscountUsageListResponse);

    @handler DeleteHandler
    post /delete (DiscountUsageDeleteRequest) returns (DiscountUsageResponse);
}