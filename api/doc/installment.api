info(
    desc: "installment api"
    author: "lamvh"
    email: "<EMAIL>"
)

type (
    Installment {
        Id int `json:"id"`
        PlanId int `json:"plan_id"`
        InstallmentNumber int `json:"installment_number"`
        Amount float64 `json:"amount"`
        Name string `json:"name"`
        Note string `json:"note"`
        PersonId int `json:"person_id"`
        UserId int `json:"user_id"`
        Kind string `json:"kind"`
        TransactionType int `json:"transaction_type"`
        State string `json:"state"`
        Status int `json:"status"`
        PaidAt string `json:"paid_at"`
        CreatedAt string `json:"created_at"`
        UpdatedAt string `json:"updated_at"`
        Version int `json:"version"`
    }

    InstallmentAddRequest {
        PlanId int `json:"plan_id"`
        InstallmentNumber int `json:"installment_number,optional"`
        Amount float64 `json:"amount"`
        PersonId int `json:"person_id"`
        Note string `json:"note,optional"`
        Kind string `json:"kind"`
        TransactionType int `json:"transaction_type"`
        Status int `json:"status,optional"`
    }

    InstallmentGetRequest {
        Id int `json:"id"`
    }

    InstallmentListRequest {
        PageSize int `json:"page_size,optional"`
        Page int `json:"page,optional"`
        Filter InstallmentFilter `json:"filter,optional"`
        OrderBy string `json:"order_by,optional"`
    }

    InstallmentFilter {
        PlanId int `json:"plan_id,optional"`
        PersonId int `json:"person_id,optional"`
        InstallmentNumber int `json:"installment_number,optional"`
        Status int `json:"status,optional"`
    }

    InstallmentResponse {
        Installment
        User UserShort `json:"creator"`
        PaidAmount float64 `json:"paid_amount"`
        PaymentAllocations []PaymentAllocationResponse `json:"allocations"`
    }

    InstallmentListResponse {
        Installments []InstallmentResponse `json:"installments"`
        Total int `json:"total"`
        TotalPage int `json:"total_page"`
    }

    InstallmentUpdateRequest {
        Id int `json:"id"`
        Amount float64 `json:"amount,optional"`
        Note string `json:"note,optional"`
        PersonId int `json:"person_id,optional"`
        Kind string `json:"kind,optional"`
        TransactionType int `json:"transaction_type,optional"`
        Status int `json:"status,optional"`
        Modified []string `json:"modified,optional"`
    }

    InstallmentDeleteRequest {
        Id int `json:"id"`
    }

    InstallmentGetPartiallyPaidRequest {
        PersonId int `json:"person_id"`
    }
)

@server(
    jwt: Auth
    group: installment
    prefix: /v1/installment
)
service bcare-api {
    @handler AddHandler
    post /add (InstallmentAddRequest) returns (Installment);

    @handler GetHandler
    post /get (InstallmentGetRequest) returns (Installment);

    @handler ListHandler
    post /list (InstallmentListRequest) returns (InstallmentListResponse);

    @handler UpdateHandler
    post /update (InstallmentUpdateRequest) returns (Installment);

    @handler DeleteHandler
    post /delete (InstallmentDeleteRequest) returns (Installment);

    @handler GetPartiallyPaidHandler
    post /partially-paid (InstallmentGetPartiallyPaidRequest) returns (InstallmentListResponse);

    @handler GetRefundHandler
    post /refund (InstallmentGetPartiallyPaidRequest) returns (InstallmentListResponse);
}
