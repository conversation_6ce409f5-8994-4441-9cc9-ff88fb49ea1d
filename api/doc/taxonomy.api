info(
    desc: "taxonomy api"
    author: "lamvh"
    email: "<EMAIL>"
)

type (
    Term {
        Id          int   `json:"id"`
        Name        string  `json:"name"`
        Bundle      string  `json:"bundle"`
        Description string  `json:"description"`
        Body string  `json:"body"`
        Weight      int   `json:"weight"`
        Status      int   `json:"status"`
        CreatedAt   string  `json:"created_at"`
        UpdatedAt   string  `json:"updated_at"`
        DeletedAt   string  `json:"deleted_at"`
    }

    TermAddRequest {
        Name        string  `json:"name"`
        MachineName string  `json:"machine_name"`
        Bundle      string  `json:"bundle"`
        Description string  `json:"description"`
        Format      string  `json:"format"`
        Weight      int   `json:"weight"`
        Status      int   `json:"status"`
        ParentId    int   `json:"parent_id"`
    }

    TermAddResponse {
        Term
    }

    TermGetRequest {
        Id          int `json:"id,optional"`
        Bundle      string  `json:"bundle,optional"`
    }

    TermListReponse {
        Term        []Term    `json:"terms"`
    }

    TermUpdateRequest {
        Id          int   `json:"id"`
        Name        string  `json:"name,optional"`
        Description string  `json:"description,optional"`
        Format      string  `json:"format,optional"`
        Weight      int   `json:"weight,optional"`
        Status      int   `json:"status,optional"`
        ParentId    int   `json:"parent_id,optional"`
        Modified []string `json:"modified,optional"`
    }

    TermGetResponse {
        Term        Term    `json:"term"`
        Parent      Term    `json:"parent"`
        Children    []Term  `json:"children"`
    }

    TermShort {
        Id          int   `json:"id"`
        Name        string  `json:"name"`
    }
)

@server(
    //jwt: Auth
    jwt: Auth
    group: taxonomy
    //middleware: AuthorizerMiddleware
    prefix: /v1/taxonomy
)
service bcare-api {
    @handler TermAddHandler
    post /term-add (TermAddRequest) returns (TermAddResponse);

    @handler TermDeleteHandler
    post /term-delete (TermGetRequest) returns (TermAddResponse);

    @handler TermUpdateHandler
    post /term-update (TermUpdateRequest) returns (TermAddResponse);

    @handler TermGetHandler
    post /term-get (TermGetRequest) returns (TermGetResponse);

    @handler TermListHandler
    post /term-list (TermGetRequest) returns (TermListReponse);
}

