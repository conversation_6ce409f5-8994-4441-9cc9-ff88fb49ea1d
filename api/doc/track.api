info(
    desc: "Track API"
    author: "Your Name"
    email: "<EMAIL>"
)

type (
    Track {
        Id int `json:"id"`
        Weight float64 `json:"weight"`
        Begin string `json:"begin"`
        End string `json:"end"`
        Status int `json:"status"`
        PipelineId int `json:"pipeline_id"`
        DealId int `json:"deal_id"`
        PersonId int `json:"person_id"`
        StageId int `json:"stage_id"`
        StageHistory []StageHistoryEntry `json:"stage_history"`
        CreatedAt string `json:"created_at"`
        UpdatedAt string `json:"updated_at"`
        DeletedAt string `json:"deleted_at"`
        Version int `json:"version"`
    }

    TrackSort {
        Id int `json:"id"`
        Begin string `json:"begin"`
        End string `json:"end"`
        PipelineId int `json:"pipeline_id"`
        DealId int `json:"deal_id"`
        PersonId int `json:"person_id"`
        StageId int `json:"stage_id"`
        CreatedAt string `json:"created_at"`
    }

    TrackAddRequest {
        PipelineId int `json:"pipeline_id,optional"`
        Weight float64 `json:"weight,optional"`
        DealId int `json:"deal_id,optional"`
        PersonId int `json:"person_id,optional"`
        Begin string `json:"begin,optional"`
        End string `json:"end,optional"`
        StageId int `json:"stage_id,optional"`

    }

    TrackUpdateRequest {
        Id int `json:"id"`
        Weight float64 `json:"weight,optional"`
        PipelineId int `json:"pipeline_id,optional"`
        DealId int `json:"deal_id,optional"`
        Begin string `json:"begin,optional"`
        End string `json:"end,optional"`
        StageId int `json:"stage_id,optional"`
        Status int `json:"status,optional"`
        Modified []string `json:"modified,optional"`
    }

    TrackDeleteRequest {
        Id int `json:"id"`
    }

    TrackListRequest {
        PageSize int `json:"page_size,optional"`
        Page int `json:"page,optional"`
        Filter TrackFilter `json:"filter,optional"`
        OrderBy string `json:"order_by,optional"`
    }

    TrackFilter {
        PipelineId int `json:"pipeline_id,optional"`
        DealId int `json:"deal_id,optional"`
        PersonId int `json:"person_id,optional"`
        StageId int `json:"stage_id,optional"`
        Status int `json:"status,optional"`
    }

    TrackResponse{
        Track
        Appointment []Appointment `json:"appointments"`
        Person *PersonResponse `json:"person"`
        Deal *DealResponse `json:"deal"`
        Attachments []AttachmentResponse `json:"attachments"`
        BillItems []BillItemResponse `json:"bill_items"`
    }

    TrackSortResponse {
        TrackSort
        Appointment []AppointmentSort `json:"appointments"`
        Person *PersonSortResponse `json:"person"`
        Deal *DealSort `json:"deal"`
    }

    TrackListResponse {
        Tracks []TrackSortResponse `json:"tracks"`
        Total int `json:"total"`
        TotalPage int `json:"total_page"`
    }

    TrackCheckoutRequest {
        Id int `json:"id"`
    }

    ActiveTrackRequest {
        PersonId int `json:"person_id"`
    }
)

@server(
    jwt: Auth
    group: track
    prefix: /v1/track
)
service bcare-api {
    @handler AddHandler
    post /add (TrackAddRequest) returns (TrackResponse)

    @handler UpdateHandler
    post /update (TrackUpdateRequest) returns (TrackResponse)

    @handler CheckoutHandler
    post /checkout (TrackCheckoutRequest) returns (Track);

    @handler ListHandler
    post /list (TrackListRequest) returns (TrackListResponse)

    @handler DeleteHandler
    post /delete (TrackDeleteRequest) returns (Track)

    @handler ActiveTrackHandler
    post /active (ActiveTrackRequest) returns (Track)
}