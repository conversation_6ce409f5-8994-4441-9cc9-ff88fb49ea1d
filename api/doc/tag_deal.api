info(
    desc: "Tag Deal API"
    author: "Your Name"
    email: "<EMAIL>"
)

type (
    TagDeal {
        Id int `json:"id"`
        DeletedAt string `json:"deleted_at,optional"`
        Status int `json:"status"`
        Version int `json:"version"`
        CreatedAt string `json:"created_at"`
        UpdatedAt string `json:"updated_at"`
        AssignedBy int `json:"assigned_by,optional"`
        RemovedBy int `json:"removed_by,optional"`
        DealId int `json:"deal_id"`
        TagId int `json:"tag_id"`
    }

    TagDealAddRequest {
        DealId int `json:"deal_id"`
        TagId int `json:"tag_id"`
        AssignedBy string `json:"assigned_by,optional"`
    }

    TagDealUpdateRequest {
        Id int `json:"id"`
        DealId int `json:"deal_id,optional"`
        TagId int `json:"tag_id,optional"`
    }

    TagDealDeleteRequest {
        TagId int `json:"tag_id"`
        DealId int `json:"deal_id"`
    }
)

@server(
    jwt: Auth
    group: tag_deal
    prefix: /v1/tag/deal
)
service bcare-api {
    @handler AddHandler
    post /add (TagDealAddRequest) returns (TagDeal)

    @handler UpdateHandler
    post /update (TagDealUpdateRequest) returns (TagDeal)

    @handler DeleteHandler
    post /delete (TagDealDeleteRequest) returns (TagDeal)

}