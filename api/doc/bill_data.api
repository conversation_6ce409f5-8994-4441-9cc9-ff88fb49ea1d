info(
    desc: "Bill Data API"
    author: "lamvh"
    email: "<EMAIL>"
)

type (
    GetBillDataRequest {
        BillID int `json:"bill_id"`
        Kind string `json:"kind,optional"`
        Key string `json:"key,optional"`
    }

    SetBillDataRequest {
        BillID int `json:"bill_id"`
        Kind string `json:"kind"`
        Key string `json:"key"`
        Value string `json:"value"`
        Data map[string]interface{} `json:"data,optional"`
        UserID int `json:"user_id"`
    }

    ClearBillDataRequest {
        BillID int `json:"bill_id"`
        Kind string `json:"kind,optional"`
        Key string `json:"key,optional"`
    }

    BillDataResponse {
        Kind string `json:"kind"`
        Data map[string]interface{} `json:"data"`
    }
)

@server(
    jwt: Auth
    group: bill_data
    prefix: /v1/bill
)

service bcare-api {
    @handler Get
    post /data/get (GetBillDataRequest) returns (BillDataResponse)

    @handler Set
    post /data/set (SetBillDataRequest) returns (CommonResponse)

    @handler Clear
    post /data/clear (ClearBillDataRequest) returns (CommonResponse)
}