info(
    desc: "export api"
    author: "lamvh"
    email: "<EMAIL>"
)

type (
    // Cấu h<PERSON>nh header cho Excel
    HeaderConfig {
        Field string `json:"field"`
        DisplayName string `json:"display_name"`
        DataType string `json:"data_type,options=string|int|float|bool|date|datetime"`
        Formatter string `json:"formatter,optional"` // vd: "date:02/01/2006"
        Width int `json:"width,default=15"`
    }

        // Request để xuất Excel
    ExportRequest {
        SQL string `json:"sql"`
        Parameters []Parameter `json:"parameters,optional"`
        Headers []HeaderConfig `json:"headers"`
        TotalRecord int `json:"total_record"`
    }

        // Response khi bắt đầu xuất Excel
    ExportJobResponse {
        JobID string `json:"job_id,omitempty"`
        State string `json:"state"`
        EstimatedRecords int `json:"estimated_records,omitempty"`
        Message string `json:"message"`
    }

    DownloadJobRequest {
        JobID string `json:"job_id"` // Thêm validate nếu muốn
        UserId int `json:"user_id"` // Thêm validate nếu muốn
    }

    ExportProgressMessage {
        Type string `json:"type"` // e.g., "export_progress"
        Data ExportJobStatus `json:"data"`
    }

    ExportJobStatus {
        JobID int `json:"job_id"`                         // Job ID kiểu int64
        State string `json:"state"`                       // Trạng thái (pending, processing, completed, failed)
        Progress float64 `json:"progress"`                // Tiến độ (0.0 - 100.0)
        Message string `json:"message,optional"`          // Thông báo tiến trình
        DownloadURL string `json:"download_url,optional"` // Chỉ có khi state='completed'
        FileName string `json:"file_name,optional"`       // Chỉ có khi state='completed'
        Error string `json:"error,optional"`              // Chỉ có khi state='failed'
    }

    ExcelExportTaskPayload {
        JobID      int            `json:"job_id"` // ID của record ExportJob trong DB
        Headers    []HeaderConfig `json:"headers"`
        SQL        string         `json:"sql"`
        Parameters []Parameter    `json:"parameters"`
        UserID     int            `json:"user_id"` // Thêm UserID nếu worker cần kiểm tra quyền hoặc log
    }
)

@server(
    jwt: Auth
    group: export
    prefix: /v1/export
)
service bcare-api {
    @handler Export
    post / (ExportRequest) returns (ExportJobResponse)

    @handler Download
    post /download (DownloadJobRequest)
}