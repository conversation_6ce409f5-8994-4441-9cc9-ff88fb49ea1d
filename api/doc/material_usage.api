info(
    desc: "material usage api"
    author: "system"
)

type (
    MaterialUsage {
        Id int `json:"id"`
        AttachmentId int `json:"attachment_id"`
        MaterialId int `json:"material_id"`
        OperationId *int `json:"operation_id,optional"`
        OperationKey *string `json:"operation_key,optional"`
        Kind string `json:"kind,options=operation_quota|manual_addition"`
        QuotedQuantity float64 `json:"quoted_quantity"`
        UsedQuantity float64 `json:"used_quantity"`
        UserId *int `json:"user_id,optional"`
        Note *string `json:"note,optional"`
        Version int `json:"version"`
        CreatedAt string `json:"created_at"`
        UpdatedAt string `json:"updated_at"`
        DeletedAt string `json:"deleted_at"`
    }

    MaterialUsageAddRequest {
        AttachmentId int `json:"attachment_id"`
        MaterialId int `json:"material_id"`
        OperationId *int `json:"operation_id,optional"`
        OperationKey *string `json:"operation_key,optional"`
        Kind string `json:"kind,options=operation_quota|manual_addition,optional"`
        QuotedQuantity float64 `json:"quoted_quantity,optional"`
        UsedQuantity float64 `json:"used_quantity"`
        UserId *int `json:"user_id,optional"`
        Note *string `json:"note,optional"`
    }

    MaterialUsageUpdateRequest {
        Id int `json:"id"`
        AttachmentId int `json:"attachment_id,optional"`
        MaterialId int `json:"material_id,optional"`
        OperationId *int `json:"operation_id,optional"`
        OperationKey *string `json:"operation_key,optional"`
        Kind string `json:"kind,options=operation_quota|manual_addition,optional"`
        QuotedQuantity float64 `json:"quoted_quantity,optional"`
        UsedQuantity float64 `json:"used_quantity,optional"`
        UserId *int `json:"user_id,optional"`
        Note *string `json:"note,optional"`
        Modified []string `json:"modified,optional"`
    }

    MaterialUsageGetRequest {
        Id int `json:"id"`
        IncludeRelation bool `json:"include_relation,optional"`
    }

    MaterialUsageDeleteRequest {
        Id int `json:"id"`
    }

    MaterialUsageListRequest {
        PageSize int `json:"page_size,optional"`
        Page int `json:"page,optional"`
        Filter MaterialUsageFilter `json:"filter,optional"`
        Search string `json:"search,optional"`
        OrderBy string `json:"order_by,optional"`
    }

    MaterialUsageFilter {
        AttachmentId int `json:"attachment_id,optional"`
        MaterialId int `json:"material_id,optional"`
        OperationId int `json:"operation_id,optional"`
        UserId int `json:"user_id,optional"`
        Kind string `json:"kind,optional"`
    }

    MaterialUsageResponse {
        MaterialUsage
    }

    MaterialUsageListResponse {
        MaterialUsages []MaterialUsageResponse `json:"material_usages"`
        Total int `json:"total"`
        TotalPage int `json:"total_page"`
    }

    MaterialUsageAddItem {
        MaterialId int `json:"material_id"`
        OperationId *int `json:"operation_id,optional"`
        OperationKey *string `json:"operation_key,optional"`
        Kind string `json:"kind,options=operation_quota|manual_addition,optional"`
        QuotedQuantity float64 `json:"quoted_quantity,optional"`
        UsedQuantity float64 `json:"used_quantity"`
        UserId *int `json:"user_id,optional"`
        Note *string `json:"note,optional"`
    }

    MaterialUsageBulkAddRequest {
        MaterialUsageAddRequest []MaterialUsageAddRequest `json:"item"`
    }

    MaterialUsageReportRequest {
        PageSize int `json:"page_size,optional"`
        Page int `json:"page,optional"`
        Filter MaterialUsageReportFilter `json:"filter,optional"`
        OrderBy string `json:"order_by,optional"`
    }

    MaterialUsageReportFilter {
        MaterialId int `json:"material_id,optional"`
        OperationId int `json:"operation_id,optional"`
        UserId int `json:"user_id,optional"`
        PersonId int `json:"person_id,optional"`
        From string `json:"from,optional"`
        To string `json:"to,optional"`
        Search string `json:"search,optional"`
    }

    MaterialUsageReport {
        Id                  int     `json:"id"`
        Code                string  `json:"code"`
        Name                string  `json:"name"`
        Unit                string  `json:"unit"`
        TotalAttachment     int     `json:"total_attachment"`
        TotalQuotedQuantity float64 `json:"total_quoted_quantity"`
        TotalUsedQuantity   float64 `json:"total_used_quantity"`
    }

    MaterialUsageReportResponse {
        MaterialUsageReport []MaterialUsageReport `json:"material_usage_report"`
        Total int `json:"total"`
        TotalPage int `json:"total_page"`
    }

    MaterialUsageDetailReportResponse {
        Items     []MaterialUsageDetailRow `json:"items"`
        Total     int                    `json:"total"`
        TotalPage int                    `json:"total_page"`
    }

    MaterialUsageDetailRow {
        PersonName       string                  `json:"person_name"`
        OperationDetails []OperationDetailReport `json:"operation_details"`
    }

    OperationDetailReport {
        OperationName string                 `json:"operation_name"`
        UsedMaterials []MaterialDetailReport `json:"used_materials"`
    }

    MaterialDetailReport {
        MaterialName   string  `json:"material_name"`
        UsedQuantity   float64 `json:"used_quantity"`
        QuotedQuantity float64 `json:"quoted_quantity"`
        Difference     float64 `json:"difference"`
        Unit           string  `json:"unit"`
    }
)

@server(
    jwt: Auth
    group: material_usage
    prefix: /v1/material/usage
)
service bcare-api {
    @handler AddHandler
    post /add (MaterialUsageAddRequest) returns (MaterialUsage);

    @handler BulkAddHandler
    post /bulk-add (MaterialUsageBulkAddRequest) returns ();

    @handler GetHandler
    post /get (MaterialUsageGetRequest) returns (MaterialUsage);

    @handler ListHandler
    post /list (MaterialUsageListRequest) returns (MaterialUsageListResponse);

    @handler UpdateHandler
    post /update (MaterialUsageUpdateRequest) returns (MaterialUsage);

    @handler DeleteHandler
    post /delete (MaterialUsageDeleteRequest) returns (MaterialUsage);

    @handler ReportHandler
    post /report (MaterialUsageReportRequest) returns (MaterialUsageReportResponse);

    @handler DetailReportHandler
    post /report-detail (MaterialUsageReportRequest) returns (MaterialUsageDetailReportResponse);

    @handler ExportReportHandler
        post /export-report (MaterialUsageReportRequest);

    @handler ExportDetailReportHandler
    post /export-detail-report (MaterialUsageReportRequest);
}