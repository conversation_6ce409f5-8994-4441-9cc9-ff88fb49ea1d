info(
    desc: "cash flow item api"
    author: "system"
)

type (
    CashFlowItem {
        Id int `json:"id"`
        CashFlowId int `json:"cash_flow_id"`
        CategoryId int `json:"category_id"`
        DepartmentId int `json:"department_id"`
        Amount int `json:"amount"`
        Note string `json:"note"`
        Order int `json:"order"`
        HasVat bool `json:"has_vat"`
        CreatedAt string `json:"created_at"`
        UpdatedAt string `json:"updated_at"`
    }

    CashFlowItemWithRelations {
        CashFlowItem
        Category *TermShort `json:"category,optional"`
        Department *DepartmentShort `json:"department,optional"`
    }

    CashFlowItemAddRequest {
        CashFlowId int `json:"cash_flow_id"`
        CategoryId int `json:"category_id"`
        DepartmentId int `json:"department_id,optional"`
        Amount int `json:"amount"`
        Note string `json:"note,optional"`
        Order int `json:"order,optional"`
        HasVat bool `json:"has_vat,optional"`
    }

    CashFlowItemUpdateRequest {
        Id int `json:"id"`
        CategoryId int `json:"category_id,optional"`
        DepartmentId int `json:"department_id,optional"`
        Amount int `json:"amount,optional"`
        Note string `json:"note,optional"`
        Order int `json:"order,optional"`
        HasVat bool `json:"has_vat,optional"`
    }

    CashFlowItemGetRequest {
        Id int `json:"id"`
        IncludeRelation bool `json:"include_relation,optional"`
    }

    CashFlowItemDeleteRequest {
        Id int `json:"id"`
    }

    CashFlowItemListRequest {
        CashFlowId int `json:"cash_flow_id,optional"`
        CategoryId int `json:"category_id,optional"`
        DepartmentId int `json:"department_id,optional"`
        PageSize int `json:"page_size,optional"`
        Page int `json:"page,optional"`
        OrderBy string `json:"order_by,optional"`
    }

    CashFlowItemResponse {
        CashFlowItemWithRelations
    }

    CashFlowItemListResponse {
        Items []CashFlowItemResponse `json:"items"`
        Total int `json:"total"`
        TotalPage int `json:"total_page"`
    }

    CashFlowItemBatchItem {
        CategoryId int `json:"category_id"`
        DepartmentId int `json:"department_id,optional"`
        Amount int `json:"amount"`
        Note string `json:"note,optional"`
        Order int `json:"order,optional"`
        HasVat bool `json:"has_vat,optional"`
    }
)

@server(
    jwt: Auth
    group: cash_flow_item
    prefix: /v1/cash-flow/item
)

service bcare-api {
    @handler AddHandler
    post /add (CashFlowItemAddRequest) returns (CashFlowItemResponse);

    @handler GetHandler
    post /get (CashFlowItemGetRequest) returns (CashFlowItemResponse);

    @handler ListHandler
    post /list (CashFlowItemListRequest) returns (CashFlowItemListResponse);

    @handler UpdateHandler
    post /update (CashFlowItemUpdateRequest) returns (CashFlowItemResponse);

    @handler DeleteHandler
    post /delete (CashFlowItemDeleteRequest) returns (CashFlowItemResponse);
}