info(
    desc: "payment allocation api"
    author: "lamvh"
    email: "<EMAIL>"
)

type (
    PaymentAllocation {
        Id int `json:"id"`
        PaymentId int `json:"payment_id"`
        BillItemId int `json:"bill_item_id"`
        InstallmentId int `json:"installment_id"`
        Amount float64 `json:"amount"`
        Note string `json:"note"`
        State string `json:"state"`
        UserId int `json:"user_id"`
        CreatedAt string `json:"created_at"`
        UpdatedAt string `json:"updated_at"`
        DeletedAt string `json:"deleted_at"`
        Version int `json:"version"`
    }

    PaymentAllocationAddRequest {
        PaymentId int `json:"payment_id,optional"`
        BillItemId int `json:"bill_item_id,optional"`
        InstallmentId int `json:"installment_id,optional"`
        Amount float64 `json:"amount"`
        Note string `json:"note,optional"`

    }

    PaymentAllocationUpdateRequest {
        Id int `json:"id"`
        Amount float64 `json:"amount,optional"`
        Note string `json:"note,optional"`
        BillItemId int `json:"bill_item_id,optional"`
        InstallmentId int `json:"installment_id,optional"`
        Modified []string `json:"modified,optional"`
    }

    PaymentAllocationDeleteRequest {
        Id int `json:"id"`
    }

    PaymentAllocationResponse {
        PaymentAllocation
        BillItem *BillItemResponse `json:"bill_item"`
        Installment *InstallmentResponse `json:"installment"`
    }

    PaymentAllocationSum  {
        BillItemID int     `json:"bill_item_id"`
        Sum        float64 `json:"sum"`
    }
)

@server(
    jwt: Auth
    group: economy_payment_allocation
    prefix: /v1/economy/payment-allocation
)
service bcare-api {
    @handler AddHandler
    post /add (PaymentAllocationAddRequest) returns (PaymentAllocationResponse);

    @handler UpdateHandler
    post /update (PaymentAllocationUpdateRequest) returns (PaymentAllocationResponse);

    @handler DeleteHandler
    post /delete (PaymentAllocationDeleteRequest) returns (PaymentAllocationResponse);
}