info(
    desc: "sync api"
    author: "lamvh"
    email: "<EMAIL>"
)

type (
    SyncDataRequest {
        FromId int `json:"from_id"`
        ToId int `json:"to_id"`
    }
)

@server(
    group: sync
    prefix: /v1/sync
)
service bcare-api {
//    @handler  SyncDataHandler
//    post /data (SyncDataRequest) returns (PersonResponse);
//
//    @handler  SyncDataSanHandler
//    post /data-san (SyncDataRequest) returns (PersonResponse);

    @handler  SyncDataUpHandler
    post /data-up (SyncDataRequest) returns (PersonResponse);

    @handler  SyncDataGetFlyHandler
    post /data-getfly (SyncDataRequest) returns (PersonResponse);
}