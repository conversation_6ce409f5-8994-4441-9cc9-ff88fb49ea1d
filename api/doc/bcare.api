info (
	title:   "Bcare CRM"
	desc:    "Bcare API"
	author:  "lamvh"
	email:   "<EMAIL>"
	version: "1.0"
)

//import "ping.api"
import "user.api"
import "user_data.api"
import "auth.api"
import "product.api"
import "referral.api"
import "person.api"
import "appointment.api"
import "location.api"
import "taxonomy.api"
import "bundle.api"
import "schedule.api"
import "constant.api"
import "deal.api"
import "attachment.api"
import "installment.api"
import "installment_plan.api"
import "deal_user.api"
import "pipeline.api"
import "stage.api"
import "file.api"
import "file_usage.api"
import "discount.api"
import "discount_usage.api"
import "note.api"
import "bill.api"
import "bill_item.api"
import "track.api"
import "task.api"
import "task_assignment.api"
import "task_note.api"
import "casbin.api"
import "setting.api"
import "call.api"
import "person_message.api"
import "person_assignment.api"
import "person_stage.api"
import "sync.api"
import "admin.api"
import "query.api"
import "issue.api"
import "product_operation.api"
import "public.api"
import "person_data.api"
import "attachment_data.api"
import "bill_data.api"
import "activity.api"
import "operation.api"
import "raw_query.api"
import "department.api"
import "task_department.api"
import "mind.api"
import "payment.api"
import "payment_allocation.api"
import "deposit.api"
import "user_stat.api"
import "download.api"
