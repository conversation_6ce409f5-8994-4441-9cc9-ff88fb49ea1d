info(
    desc: "deposit api"
    author: "lamvh"
    email: "<EMAIL>"
)

type (
    Deposit {
        Id int `json:"id"`
        DealId int `json:"deal_id,optional"`
        TotalAmount float64 `json:"total_amount"`
        PaidAmount float64 `json:"paid_amount"`
        RemainingAmount float64 `json:"remaining_amount"`
        State string `json:"state"`  // pending, active, converted, refund, cancelled
        Description string `json:"description,optional"`
        CreatedBy int `json:"created_by,optional"`
        UpdatedBy int `json:"updated_by,optional"`
        CreatedAt string `json:"created_at"`
        UpdatedAt string `json:"updated_at"`
    }

    DepositAllocation {
        Id int `json:"id"`
        DepositId int `json:"deposit_id"`
        AttachmentId int `json:"attachment_id,optional"`
        DealId int `json:"deal_id,optional"`
        Amount float64 `json:"amount"`
        State string `json:"state"` // active, converted, refund, cancelled
        CreatedBy int `json:"created_by,optional"`
        UpdatedBy int `json:"updated_by,optional"`
        CreatedAt string `json:"created_at"`
        UpdatedAt string `json:"updated_at"`
    }

    DepositPayment {
        Id int `json:"id"`
        DepositId int `json:"deposit_id"`
        PaymentId int `json:"payment_id"`
        Amount float64 `json:"amount"`
        ConversionDate string `json:"conversion_date"`
        State string `json:"state"` // active, completed, cancelled
        Note string `json:"note,optional"`
        CreatedBy int `json:"created_by,optional"`
        UpdatedBy int `json:"updated_by,optional"`
        CreatedAt string `json:"created_at"`
        UpdatedAt string `json:"updated_at"`
    }

        // Request types
    DepositAddRequest {
        DealId int `json:"deal_id,optional"`
        TotalAmount float64 `json:"total_amount"`
        Description string `json:"description,optional"`
        Allocations []AllocationAddRequest `json:"allocations,optional"`
    }

    UpdateDepositRequest {
        Id int `json:"id"`
        TotalAmount float64 `json:"total_amount,optional"`
        State string `json:"state,optional"`
        Description string `json:"description,optional"`
        Allocations []AllocationAddRequest `json:"allocations,optional"`
        Modified []string `json:"modified,optional"`
    }

    AllocationAddRequest {
        DepositId int `json:"deposit_id"`
        AttachmentId int `json:"attachment_id,optional"`
        DealId int `json:"deal_id,optional"`
        Amount float64 `json:"amount"`
    }

    ConvertToPaymentRequest {
        DepositId int `json:"deposit_id"`
        PaymentId int `json:"payment_id"`
        Amount float64 `json:"amount"`
        Note string `json:"note,optional"`
    }

    DepositListRequest {
        PageSize int `json:"page_size,optional"`
        Page int `json:"page,optional"`
        Filter DepositFilter `json:"filter,optional"`
        OrderBy string `json:"order_by,optional"`
    }

    DepositFilter {
        DealId int `json:"deal_id,optional"`
        State string `json:"state,optional"`
        CreatedBy int `json:"created_by,optional"`
    }

    DepositListResponse {
        Deposits []DepositResponse `json:"deposits"`
        Total int `json:"total"`
        TotalPage int `json:"total_page"`
    }

    DepositResponse {
        Deposit
        Allocations []DepositAllocation `json:"allocations"`
        Payments []DepositPayment `json:"payments"`
        Deal *Deal `json:"deal,optional"`
    }

    DeleteRequest {
        Id int `json:"id"`
    }

    GetRequest {
        Id int `json:"id"`
    }
)

@server(
    jwt: Auth
    group: economy_deposit
    prefix: /v1/economy/deposit
)
service bcare-api {
    @handler AddHandler
    post /add (DepositAddRequest) returns (DepositResponse)

    @handler UpdateHandler
    post /update (UpdateDepositRequest) returns (DepositResponse)

    @handler GetHandler
    post /get (GetRequest) returns (DepositResponse)

    @handler ListHandler
    post /list (DepositListRequest) returns (DepositListResponse)

    @handler DeleteHandler
    post /delete (DeleteRequest) returns (Deposit)

    @handler AllocationAddHandler
    post /allocation/add (AllocationAddRequest) returns (DepositAllocation)

    @handler AllocationDeleteHandler
    post /allocation/delete (DeleteRequest) returns (DepositAllocation)

    @handler ConvertToPaymentHandler
    post /convert-to-payment (ConvertToPaymentRequest) returns (DepositPayment)

    @handler CancelConversionHandler
    post /cancel-conversion (DeleteRequest) returns (DepositPayment)
}