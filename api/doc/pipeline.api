info(
    desc: "pipeline api"
    author: "lamvh"
    email: "<EMAIL>"
)

type (
    Pipeline {
        Id int `json:"id"`
        Name string `json:"name"`
        Description string `json:"description"`
        UserId int `json:"user_id"`
        Status int `json:"status"`
        CreatedAt string `json:"created_at"`
        UpdatedAt string `json:"updated_at"`
        DeletedAt string `json:"deleted_at"`
        Version int `json:"version"`
    }

    PipelineAddRequest {
        Name string `json:"name"`
        Description string `json:"description"`
        UserId int `json:"user_id"`
        Status int `json:"status"`
    }

    PipelineResponse {
        Pipeline
        Stages []StageResponse `json:"stages"`
    }

    PipelineUpdateRequest {
        Id int `json:"id"`
        Name string `json:"name,optional"`
        Description string `json:"description,optional"`
        UserId int `json:"user_id,optional"`
        Status int `json:"status,optional"`
        Modified []string `json:"modified,optional"`
    }

    PipelineGetRequest {
        Id int `json:"id"`
        Name string `json:"name"`
    }

    PipelineDeleteRequest {
        Id int `json:"id"`
        Name string `json:"name"`
    }

    PipelineListRequest {
        PageSize int `json:"page_size,optional"`
        Page int `json:"page,optional"`
        OrderBy string `json:"order_by,optional"`
    }

    PipelineFilter {
        Search string `json:"search"`
        Name string `json:"name"`
        Description string `json:"description"`
        UserId int `json:"user_id"`
        Status int `json:"status"`
    }

    PipelineListResponse {
        Pipelines []PipelineResponse `json:"pipelines"`
        Total int `json:"total"`
        TotalPage int `json:"total_page"`
    }

    SwitchDealRequest {
        OldId int `json:"old_id"`
        NewId int `json:"new_id,optional"`
        TrackId int `json:"track_id"`
        PipelineId int `json:"pipeline_id,optional"`
        StageId int `json:"stage_id,optional"`
    }

    SwitchDealResponse {
        TrackResponse
    }
)

@server(
    jwt: Auth
    group: pipeline
    prefix: /v1/pipeline
)
service bcare-api {
    @handler AddHandler
    post /add (PipelineAddRequest) returns (PipelineResponse);

    @handler GetHandler
    post /get (PipelineGetRequest) returns (PipelineResponse);

    @handler ListHandler
    post /list (PipelineListRequest) returns (PipelineListResponse);

    @handler UpdateHandler
    post /update (PipelineUpdateRequest) returns (PipelineResponse);

    @handler DeleteHandler
    post /delete (PipelineDeleteRequest) returns (PipelineResponse);

    @handler SwitchDealHandler
    post /switch-deal (SwitchDealRequest) returns (SwitchDealResponse);
}