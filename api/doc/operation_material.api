info(
    desc: "operation material api"
    author: "khoapd"
)

type (
    OperationMaterial {
        Id int `json:"id"`
        OperationId int `json:"operation_id"`
        MaterialId int `json:"material_id"`
        Quantity float64 `json:"quantity"`
        Status int `json:"status"`
        Version int `json:"version"`
        CreatedAt string `json:"created_at"`
        UpdatedAt string `json:"updated_at"`
        DeletedAt string `json:"deleted_at"`
    }

    OperationMaterialAddRequest {
        OperationId int `json:"operation_id"`
        MaterialId int `json:"material_id"`
        Quantity float64 `json:"quantity"`
        Status int `json:"status,range=[0:10],optional"`
    }

    OperationMaterialUpdateRequest {
        Id int `json:"id"`
        OperationId int `json:"operation_id,optional"`
        MaterialId int `json:"material_id,optional"`
        Quantity float64 `json:"quantity,optional"`
        Status int `json:"status,range=[0:10],optional"`
        Modified []string `json:"modified,optional"`
    }

    OperationMaterialGetRequest {
        Id int `json:"id"`
        OperationId int `json:"operation_id,optional"`
        MaterialId int `json:"material_id,optional"`
    }

    OperationMaterialDeleteRequest {
        Id int `json:"id"`
        OperationId int `json:"operation_id"`
    }

    OperationMaterialListRequest {
        PageSize int `json:"page_size,optional"`
        Page int `json:"page,optional"`
        Filter OperationMaterialFilter `json:"filter,optional"`
        Search string `json:"search,optional"`
        OrderBy string `json:"order_by,optional"`
    }

    OperationMaterialFilter {
        OperationId int `json:"operation_id,optional"`
        MaterialId int `json:"material_id,optional"`
        Status int `json:"status,range=[0:10],optional"`
    }

    OperationMaterialResponse {
        OperationMaterial
    }
    OperationMaterialListResponse {
        OperationMaterials []OperationMaterialResponse `json:"operation_materials"`
        Total int `json:"total"`
        TotalPage int `json:"total_page"`
    }

    OperationMaterialSetItem {
        MaterialId int `json:"material_id"`
        Quantity float64 `json:"quantity"`
    }

    BulkSetRequest {
        OperationId int `json:"operation_id"`
        Materials []OperationMaterialSetItem `json:"materials,optional"`
    }

)

@server(
    jwt: Auth
    group: operation_material
    prefix: /v1/operation/material
)
service bcare-api {
    @handler AddHandler
    post /add (OperationMaterialAddRequest) returns (OperationMaterial);

    @handler GetHandler
    post /get (OperationMaterialGetRequest) returns (OperationMaterial);

    @handler ListHandler
    post /list (OperationMaterialListRequest) returns (OperationMaterialListResponse);

    @handler UpdateHandler
    post /update (OperationMaterialUpdateRequest) returns (OperationMaterial);

    @handler DeleteHandler
    post /delete (OperationMaterialDeleteRequest) returns ();

    @handler BulkSetHandler
    post /bulk-set (BulkSetRequest) returns ();
}