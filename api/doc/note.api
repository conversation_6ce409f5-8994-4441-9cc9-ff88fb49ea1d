info(
    desc: "Note API"
    author: "Your Name"
    email: "<EMAIL>"
)

type (
    Note {
        Id int `json:"id"`
        Body string `json:"body"`
        Type int `json:"type"`
        PersonId int `json:"person_id"`
        UserId int `json:"user_id"`
        Status int `json:"status"`
        CreatedAt string `json:"created_at"`
        UpdatedAt string `json:"updated_at"`
        DeletedAt string `json:"deleted_at"`
        History []HistoryEntry `json:"history"`
        Version int `json:"version"`
    }

    NoteAddRequest {
        Body string `json:"body"`
        Type int `json:"type"`
        PersonId int `json:"person_id"`
    }

    NoteUpdateRequest {
        Id int `json:"id"`
        Body string `json:"body,optional"`
        Type int `json:"type,optional"`
        PersonId int `json:"person_id,optional"`
        Status int `json:"status,optional"`
        Modified []string `json:"modified,optional"`
    }

    NoteDeleteRequest {
        Id int `json:"id"`
    }

    NoteListRequest {
        PageSize int `json:"page_size,optional"`
        Page int `json:"page,optional"`
        Filter NoteFilter `json:"filter,optional"`
        DepartmentId int `json:"department_id,optional"`
        OrderBy string `json:"order_by,optional"`
    }

    NoteFilter {
        PersonId int `json:"person_id,optional"`
        UserId int `json:"user_id,optional"`
        Type int `json:"type,optional"`
    }

    NoteResponese {
        Note
        Creator UserShort `json:"creator"`
        Person Person `json:"person"`
    }

    NoteListResponse {
        Notes []NoteResponese `json:"notes"`
        Total int `json:"total"`
        TotalPage int `json:"total_page"`
    }
)

@server(
    jwt: Auth
    group: note
    prefix: /v1/note
)
service bcare-api {
    @handler AddHandler
    post /add (NoteAddRequest) returns (Note)

    @handler UpdateHandler
    post /update (NoteUpdateRequest) returns (Note)

    @handler ListHandler
    post /list (NoteListRequest) returns (NoteListResponse)

    @handler DeleteHandler
    post /delete (NoteDeleteRequest) returns (Note)
}