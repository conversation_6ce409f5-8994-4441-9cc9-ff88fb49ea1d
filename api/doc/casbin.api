info(
    desc: "casbin api"
    author: "lamvh"
    email: "<EMAIL>"
)

type(
    SyncResponse {
        Perm string `json:"perm"`
    }

    Role {
        Name string `json:"name"`
        Description string  `json:"description"`
        Parent string `json:"parent"`
    }
)

@server(
    jwt: Auth
    group: casbin
    prefix: /v1/casbin
)

service bcare-api {
    @handler SyncHandler
    post /sync () returns (SyncResponse)

    @handler RoleListHandler
    post /role/list () returns ([]Role)

    @handler RoleAddHandler
    post /role/add (Role) returns (Role)

    @handler RoleDeleteHandler
    post /role/delete () returns (bool)
}