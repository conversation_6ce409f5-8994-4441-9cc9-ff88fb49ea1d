info(
    desc: "Task Note API"
    author: "Your Name"
    email: "<EMAIL>"
)

type (
    TaskNote {
        Id int `json:"id"`
        TaskId int `json:"task_id"`
        UserId int `json:"user_id"`
        Body string `json:"body"`
        Status int `json:"status"`
        CreatedAt string `json:"created_at"`
        UpdatedAt string `json:"updated_at"`
        DeletedAt string `json:"deleted_at,omitempty"`
        Version int `json:"version"`
    }

    TaskNoteAddRequest {
        TaskId int `json:"task_id"`
        Body string `json:"body"`
    }

    TaskNoteUpdateRequest {
        Id int `json:"id"`
        Body string `json:"body"`
    }

    TaskNoteDeleteRequest {
        Id int `json:"id"`
    }

    TaskNoteListRequest {
        PageSize int `json:"page_size,optional"`
        Page int `json:"page,optional"`
        Filter TaskNoteFilter `json:"filter,optional"`
        OrderBy string `json:"order_by,optional"`
    }

    TaskNoteFilter {
        TaskId int `json:"task_id"`
    }

    TaskNoteListResponse {
        Notes []TaskNoteResponse `json:"notes"`
        Total int `json:"total"`
        TotalPage int `json:"total_page"`
    }

    TaskNoteResponse {
        TaskNote
        User *UserShort `json:"creator"`
    }
)

@server(
    jwt: Auth
    group: task
    prefix: /v1/task/note
)

service bcare-api {
    @handler AddNoteHandler
    post /add (TaskNoteAddRequest) returns (TaskNote)

    @handler UpdateNoteHandler
    post /update (TaskNoteUpdateRequest) returns (TaskNote)

    @handler DeleteNoteHandler
    post /delete (TaskNoteDeleteRequest) returns (TaskNote)

    @handler ListNoteHandler
    post /list (TaskNoteListRequest) returns (TaskNoteListResponse)
}