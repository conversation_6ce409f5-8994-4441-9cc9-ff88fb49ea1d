info(
    desc: "bill item api"
    author: "lamvh"
    email: "<EMAIL>"
)

type (
    BillItem {
        Id int `json:"id"`
        BillId int `json:"bill_id"`
        AttachmentId int `json:"attachment_id"`
        Status int `json:"status"`
        Amount float64 `json:"amount"`
        Note string `json:"note"`
        UserId int `json:"user_id"`
        TrackId int `json:"track_id"`
        CreatedAt string `json:"created_at"`
        UpdatedAt string `json:"updated_at"`
        DeletedAt string `json:"deleted_at"`
        Version int `json:"version"`
    }

    BillItemAddRequest {
        BillId int `json:"bill_id,optional"`
        AttachmentId int `json:"attachment_id,optional"`
        Amount float64 `json:"amount,optional"`
        Note string `json:"note,optional"`
        PaymentId int `json:"payment_id,optional"`
        AllocateAmount float64 `json:"allocate_amount,optional"`
    }

    BillItemAddFromAttachment {
        AttachmentId int `json:"attachment_id"`
        Note string `json:"note,optional"`
    }

    BillItemUpdateRequest {
        Id int `json:"id"`
        Status int `json:"status,optional"`
        Amount float64 `json:"amount,optional"`
        Note string `json:"note"`
        Modified []string `json:"modified,optional"`
    }

    BillItemGetRequest {
        Id int `json:"id"`
    }

    BillItemDeleteRequest {
        Id int `json:"id"`
    }

    BillItemListRequest {
        PageSize int `json:"page_size,optional"`
        Page int `json:"page,optional"`
        Filter BillItemFilter `json:"filter,optional"`
        OrderBy string `json:"order_by,optional"`
    }

    BillItemFilter {
        BillId int `json:"bill_id,optional"`
    }

    BillItemListResponse {
        BillItems []BillItemResponse `json:"bill_items"`
        RemainingAmount float64 `json:"remaining_amount"`
        Total int `json:"total"`
        TotalPage int `json:"total_page"`
    }

    BillItemResponse {
        BillItem
        PaidAmount float64 `json:"paid_amount"`
        Attachment *AttachmentResponse `json:"attachment"`
        PaymentAllocations []PaymentAllocationResponse `json:"allocations"`
        User UserShort `json:"user"`
    }

    BillItemDynamicQuery {
        DynamicQuery
    }

    BillItemGetPartiallyPaidRequest {
        PersonId int `json:"person_id"`
    }

    BillItemGetPaidRequest {
        PersonId int `json:"person_id"`
        PageSize int `json:"page_size,optional"`
        Page int `json:"page,optional"`
    }

)

@server(
    jwt: Auth
    group: bill_item
    prefix: /v1/bill-item
)
service bcare-api {
    @handler AddHandler
    post /add (BillItemAddRequest) returns (BillItemResponse);

    @handler AddFromAttachmentHandler
    post /add-from-attachment (BillItemAddFromAttachment) returns (BillItemResponse);

    @handler UpdateFromAttachmentHandler
    post /update-from-attachment (BillItemAddFromAttachment) returns (BillItemResponse);

    @handler GetHandler
    post /get (BillItemGetRequest) returns (BillItemResponse);

    @handler ListHandler
    post /list (BillItemListRequest) returns (BillItemListResponse);

    @handler UpdateHandler
    post /update (BillItemUpdateRequest) returns (BillItemResponse);

    @handler DeleteHandler
    post /delete (BillItemDeleteRequest) returns (BillItemResponse);

    @handler QueryHandler
    post /query (BillItemDynamicQuery) returns (DynamicQueryResponse);

    @handler GetPartiallyPaidHandler
    post /partially-paid (BillItemGetPartiallyPaidRequest) returns (BillItemListResponse);

    @handler GetPaidHandler
    post /paid (BillItemGetPaidRequest) returns (BillItemListResponse);

}