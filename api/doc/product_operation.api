info(
    desc: "ProductOperation API"
    author: "Your Name"
    email: "<EMAIL>"
)

type (
    ProductOperation {
        ID int `json:"id"`
        ProductId int `json:"product_id"`
        OperationId int `json:"operation_id"`
        OrderSequence int `json:"order_sequence"`
        Opration Operation `json:"operation"`
    }

    ProductOperationAddRequest {
        ProductId int `json:"product_id,optional"`
        OperationId int `json:"operation_id"`
    }

    ProductOperationDeleteRequest {
        ProductId int `json:"product_id"`
        OperationId int `json:"operation_id"`
    }

    BulkSetProductOperationRequest {
        OperationId int   `json:"operationId"`
        ProductIds  []int`json:"productIds"`
    }
)

@server(
    jwt: Auth
    group: product_operation
    prefix: /v1/product_operation
)
service bcare-api {
    @handler AddHandler
    post /add (ProductOperationAddRequest) returns (ProductOperation)

    @handler DeleteHand<PERSON>
    post /delete (ProductOperationDeleteRequest) returns (ProductOperation)

    @handler BulkSetHandler
    post /bulk-set (BulkSetProductOperationRequest) returns ()
}