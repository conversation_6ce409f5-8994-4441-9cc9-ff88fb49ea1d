info(
    desc: "public api"
    author: "lamvh"
    email: "<EMAIL>"
)

type (
    FormAddRequest {
        FullName string `json:"full_name"`
        Phone string `json:"phone"`
        Email string `json:"email,optional"`
        Data map[string]interface{} `json:"data,optional"`
        SourceUrl string `json:"source_url,optional"`
        FormName string `json:"form_name,optional"`
        ReferrerUrl string `json:"referrer_url,optional"`
        SourceId int `json:"source_id,optional"`
    }

    FormResponse {
        PersonResponse
    }
)

@server(
    middleware: IpLimitMiddleware, CSRFMiddleware, RateLimitMiddleware
    group: public
    prefix: /v1/public
)
service bcare-api {
    @handler FormAddHandler
    post /form/add (FormAddRequest) returns (FormResponse);
}