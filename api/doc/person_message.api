info(
    desc: "call api"
    author: "lamvh"
    email: "<EMAIL>"
)
type (
    SendMessageRequest {
        PersonId int `json:"person_id"`
        Phone string `json:"phone, optional"`
        Email string `json:"email, optional"`
        EmailContent string `json:"email_content, optional"`
        SmsContent string `json:"sms_content, optional"`
        ZnsTemplateId string `json:"zns_template_id, optional"`
        ZnsParams string `json:"zns_params, optional"`
        ZnsContent string `json:"zns_content, optional"`
        FallbackSms bool `json:"fallback_sms, optional"`
    }

    MessageTemplateRequest {
        PersonId int `json:"person_id"`
        DealId int `json:"deal_id"`
        AppointmentId int `json:"appointment_id"`
    }

    MessageTemplateResponse {
        Sms string `json:"sms"`
        Zns string `json:"zns"`
        Email string `json:"email"`
    }

    SendMessageResponse {

    }

    HistoryRequest {
        PersonId int `json:"person_id"`
    }

    MessageHistory {
        Id int `json:"id"`
        PersonId int `json:"person_id"`
        UserId int `json:"user_id"`
        MessageId string `json:"message_id"`
        Phone string `json:"phone"`
        Type string `json:"type" enums:"sms,zns,email"`                            // sms, zns or email
        Content string `json:"content"`
        ZnsData string `json:"zns_data"`
        ErrorCode string `json:"error_code"`
        MessageStatus string `json:"message_status" enums:"sent,failed,delivered"` // sent, failed, delivered
        DeliveredAt string `json:"delivered_at"`
        Status int `json:"status"`
        CreatedAt string `json:"created_at"`
        UpdatedAt string `json:"updated_at"`
        DeletedAt string `json:"deleted_at"`
        Version int `json:"version"`
        Person *Person `json:"person"`
    }

    HistoryResponse {
        MessageHistories []MessageHistory `json:"message_histories"`
        Total int `json:"total"`
        TotalPage int `json:"total_page"`
    }
)

@server(
    jwt: Auth
    group: person
    prefix: /v1/person/message
)

service bcare-api {
    @handler SendMessageHandler
    post /send (SendMessageRequest) returns (SendMessageResponse);

    @handler MessageTemplateHandler
    post /template (MessageTemplateRequest) returns (MessageTemplateResponse)

    @handler HistoryMessageHandler
    post /history (HistoryRequest) returns (HistoryResponse)
}