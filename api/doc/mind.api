info(
    desc: "Mind API"
    author: "Your Name"
    email: "<EMAIL>"
)

type (
    CompleteRequest {
        Prompt string `json:"prompt"`
    }

    CompleteResponse {
        Result string `json:"result"`
    }

    GetCronRequest {
        Prompt string `json:"prompt"`
    }

    GetCronResponse {
        CronExpression string `json:"expression"`
    }

    ContentModificationRequest {
        Content string `json:"content"`
        Focus string `json:"focus"`
    }

    ContentModificationResponse {
        Options []string `json:"options"`
    }

    ContentCommandRequest {
        Content string `json:"content"`
        Focus string `json:"focus"`
        Command string `json:"command"`
    }

    ContentCommandResponse {
        Options []string `json:"options"`
    }
)

@server(
    jwt: Auth
    group: mind
    prefix: /v1/mind
)
service bcare-api {
    @handler CompleteHandler
    post /complete (CompleteRequest) returns (CompleteResponse)

    @handler GetCronHandler
    post /getcron (GetCronRequest) returns (GetCronResponse)

    @handler ContentShorterHandler
    post /content/shorter (ContentModificationRequest) returns (ContentModificationResponse)

    @handler ContentLongerHandler
    post /content/longer (ContentModificationRequest) returns (ContentModificationResponse)

    @handler ContentImproveHandler
    post /content/improve (ContentModificationRequest) returns (ContentModificationResponse)

    @handler ContentContinueHandler
    post /content/continue (ContentModificationRequest) returns (ContentModificationResponse)

    @handler ContentCommandHandler
    post /content/command (ContentCommandRequest) returns (ContentCommandResponse)
}