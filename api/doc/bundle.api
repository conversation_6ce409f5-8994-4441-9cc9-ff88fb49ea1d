info(
    desc: "taxonomy api"
    author: "lamvh"
    email: "<EMAIL>"
)

type (
    Bundle {
        Id          int       `json:"id"`
        MachineName string      `json:"machine_name"`
        Name        string      `json:"name"`
        Type        string      `json:"type"`
        Description string      `json:"description"`
        Status      int         `json:"status"`
        CreatedAt   string      `json:"created_at"`
        UpdatedAt   string      `json:"updated_at"`
        DeletedAt   string      `json:"deleted_at"`
    }

    BundleAddRequest {
        MachineName string      `json:"machine_name"`
        Name        string      `json:"name"`
        Type        string      `json:"type"`
        Description string      `json:"description"`
    }

    BundleResponse {
        Bundle
    }

    BundleGetRequest {
        Id int `json:"id"`
    }

    BundleUpdateRequest {
        Id          int       `json:"id"`
        Name        string      `json:"name,optional"`
        Description string      `json:"description,optional"`
        Modified []string `json:"modified,optional"`
    }

    BundleListRequest {
        PageSize    int           `json:"page_size,optional"`
        Page        int           `json:"page,optional"`
        Filter      BundleFilter    `json:"filter,optional"`
        OrderBy     string          `json:"order_by,optional"`
    }

    BundleListResponse {
        Total         int     `json:"total"`
        TotalPage     int     `json:"total_page"`
        Bundles       []Bundle  `json:"bundles"`
    }

    BundleFilter {
        Name    string      `json:"name,optional"`
        Type    string      `json:"type,optional"`
        Status  int       `json:"status,optional"`
    }
)

@server(
    //jwt: Auth
    jwt: Auth
    group: bundle
    //middleware: AuthorizerMiddleware
    prefix: /v1/bundle
)

//TODO sửa tự thêm prefix vao may cai handler nay!
service bcare-api {
    @handler AddHandler
    post /add (BundleAddRequest) returns (BundleResponse);

    @handler DeleteHandler
    post /delete (BundleGetRequest) returns (BundleResponse);

    @handler UpdateHandler
    post /update (BundleUpdateRequest) returns (BundleResponse);

    @handler GetHandler
    post /get (BundleGetRequest) returns (BundleResponse);

    @handler ListHandler
    post /list (BundleListRequest) returns (BundleListResponse);
}

