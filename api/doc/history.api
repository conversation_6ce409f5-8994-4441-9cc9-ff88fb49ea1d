syntax = "v1"

info(
    title: "History Service"
    desc: "Service for querying unified entity history"
    author: "Your Name / Team"
    email: "<EMAIL>"
    version: "1.0"
)

type (
    HistoryRecord {
        Id          int  `json:"id"`
        EntityType  string `json:"entity_type"`
        EntityId    int  `json:"entity_id"`
        Operation   string `json:"operation"`
        ChangedAt   string `json:"changed_at"`
        UserId      *int `json:"user_id,omitempty"`
        Before      map[string]interface{}   `json:"before,omitempty"`
        After       map[string]interface{}   `json:"after,omitempty"`
        RelatedId   *int `json:"related_id,omitempty"`
        Related     *string `json:"related,omitempty"`
        CreatedAt   string `json:"created_at"`
        UpdatedAt   string `json:"updated_at"`
        SourceTable string `json:"source_table"`
    }

    HistoryListRequest {
        PageSize int `json:"page_size,optional"`
        Page int `json:"page,optional"`
        Filter HistoryFilter `json:"filter,optional"`
        OrderBy string `json:"order_by,optional"`
    }

    HistoryFilter {
        EntityType string `json:"entity_type,optional"`
        EntityId int `json:"entity_id,optional"`
        UserId int `json:"status,optional"`
    }

    HistoryListResponse {
        Histories []HistoryRecord `json:"histories"`
        Total int `json:"total"`
        TotalPage int `json:"total_page"`
    }


)

@server(
    group: history
    prefix: /api/v1
    jwt: Auth
)
service bcare-api {
    @handler GetHistoryList
    post /history (HistoryListRequest) returns (HistoryListResponse)
}
