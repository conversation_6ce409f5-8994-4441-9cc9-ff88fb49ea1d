info(
    desc: "Person Assignment API"
    author: "Your Name"
    email: "<EMAIL>"
)

type (
    PersonAssignment {
        Id int `json:"id"`
        PersonId int `json:"person_id"`
        UserId int `json:"user_id"`
        Role string `json:"role,options=doctor|counselor|sale|customer_care"`
        CreatedAt string `json:"created_at"`
        UpdatedAt string `json:"updated_at"`
    }

    PersonAssignmentAddRequest {
        PersonId int `json:"person_id"`
        UserId int `json:"user_id"`
        Role string `json:"role,options=doctor|counselor|sale|customer_care"`
    }

    PersonAssignmentUpdateRequest {
        Id int `json:"id"`
        PersonId int `json:"person_id,optional"`
        UserId int `json:"user_id,optional"`
        Role string `json:"role,options=doctor|counselor|sale|customer_care,optional"`
        Modified []string `json:"modified,optional"`
    }

    AssignPersonsRequest {
        IdList []int `json:"IdList"`
        UserId int `json:"user_id"`
        Role string `json:"role,options=doctor|counselor|sale|customer_care"`
    }

    PersonAssignmentDeleteRequest {
        Id int `json:"id"`
    }

    PersonAssignmentResponse {
        Id int `json:"id"`
        PersonId int `json:"person_id"`
        UserId int `json:"user_id"`
        Role string `json:"role"`
        User *UserShort `json:"user"`
    }
)

@server(
    jwt: Auth
    group: person_assignment
    prefix: /v1/person_assignment
)
service bcare-api {
    @handler AddHandler
    post /add (PersonAssignmentAddRequest) returns (PersonAssignment)

    @handler UpdateHandler
    post /update (PersonAssignmentUpdateRequest) returns (PersonAssignment)

    @handler DeleteHandler
    post /delete (PersonAssignmentDeleteRequest) returns (PersonAssignment)
}


