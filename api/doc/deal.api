info(
    desc: "deal api"
    author: "lamvh"
    email: "<EMAIL>"
)

type (
    StageHistoryEntry {
        StageID int `json:"stage_id"`
        EnteredAt string `json:"entered_at"`
        ExitedAt string `json:"exited_at,omitempty,optional"`
        UserId int `json:"user_id,,omitempty,optional"`
        Reason string `json:"reason,,omitempty,optional"`
    }
    Deal {
        Id int `json:"id"`
        PersonId int `json:"person_id"`
        ParentDealId int `json:"parent_deal_id"`
        Name string `json:"name"`
        State string `json:"state"`
        TotalAmount float64 `json:"total_amount"`
        TotalPlanAmount float64 `json:"total_plan_amount"`
        PaidAmount float64 `json:"paid_amount"`
        DiscountAmount float64 `json:"discount_amount"`
        PaidInstallmentCount int `json:"paid_installment_count"`
        RefundAmount float64 `json:"refund_amount"`
        DepositAmount float64 `json:"deposit_amount"`
        DownPayment float64 `json:"down_payment"`
        DownPaymentReceivedAmount float64 `json:"down_payment_received_amount"`
        StageId int `json:"stage_id"`
        StageHistory []StageHistoryEntry `json:"stage_history"`
        Status int8 `json:"status"`
        CreatedAt string `json:"created_at"`
        UpdatedAt string `json:"updated_at"`
        DeletedAt string `json:"deleted_at"`
        Version int `json:"version"`
    }

    DealSort {
        Id int `json:"id"`
        PersonId int `json:"person_id"`
        Name string `json:"name"`
    }

    DealAddRequest {
        PersonId int `json:"person_id"`
        ParentDealId int `json:"parent_deal_id,optional"`
        Name string `json:"name,optional"`
        TotalAmount float64 `json:"total_amount,optional"`
        StageId *int `json:"stage_id,optional"`
        State string `json:"state,optional"`
        Status int8 `json:"status"`
    }

    DealUpdateRequest {
        Id int `json:"id"`
        ParentDealId int `json:"parent_deal_id,optional"`
        StageId *int `json:"stage_id,optional"`
        Status int8 `json:"status,optional"`
        Name string `json:"name,optional"`
        State string `json:"state,optional"`
        TotalAmount float64 `json:"total_amount,optional"`
        Modified []string `json:"modified,optional"`
        Discounts []int `json:"discounts,optional"`
    }

    DealCheckoutRequest {
        Id int `json:"id"`
    }



    DealGetRequest {
        Id int `json:"id"`
        IncludeRelation bool `json:"include_relation,optional"`
    }

    DealDeleteRequest {
        Id int `json:"id"`
    }

    DealListRequest {
        PageSize int `json:"page_size,optional"`
        Page int `json:"page,optional"`
        Filter DealFilter `json:"filter,optional"`
        PipelineId int `json:"pipeline_id,optional"`
        DoctorId int `json:"doctor_id,optional"`
        OrderBy string `json:"order_by,optional"`
        IncludeRelation bool `json:"include_relation,optional"`
    }

    DealFilter {
        PersonId int `json:"person_id,optional"`
        StageId int `json:"stage_id,optional"`
        Status int8 `json:"status,optional"`
        State string `json:"state,optional"`
    }

    DealListResponse {
        Deals []DealResponse `json:"deals"`
        Total int `json:"total"`
        TotalPage int `json:"total_page"`
    }

    DealResponse {
        Deal
        Person *PersonResponse `json:"person"`
        Assignments []DealUserResponse `json:"deal_assignment"`
        Tracks []TrackResponse `json:"tracks"`
        Attachments []AttachmentResponse `json:"attachments"`
        DiscountUsages []DiscountUsageResponse `json:"discount_usages"`
        EligibleDiscounts []EligibleDiscount `json:"eligible_discounts"`
    }
)

@server(
    jwt: Auth
    group: deal
    prefix: /v1/deal
)
service bcare-api {
    @handler AddHandler
    post /add (DealAddRequest) returns (DealResponse);

    @handler GetHandler
    post /get (DealGetRequest) returns (DealResponse);

    @handler ListHandler
    post /list (DealListRequest) returns (DealListResponse);

    @handler ListBigHandler
    post /list-big (DealListRequest) returns (DealListResponse);

    @handler UpdateHandler
    post /update (DealUpdateRequest) returns (DealResponse);

    @handler CheckoutHandler
    post /checkout (DealCheckoutRequest) returns (DealResponse);

    @handler DeleteHandler
    post /delete (DealDeleteRequest) returns (DealResponse);
}