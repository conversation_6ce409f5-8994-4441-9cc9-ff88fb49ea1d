info(
    desc: "Attachment Data API"
    author: "lamvh"
    email: "<EMAIL>"
)

type (
    GetAttachmentDataRequest {
        AttachmentID int `json:"attachment_id"`
        Kind string `json:"kind,optional"`
        Key string `json:"key,optional"`
        ParticipantID int `json:"participant_id,optional"`
    }

    SetAttachmentDataRequest {
        AttachmentID int `json:"attachment_id"`
        Kind string `json:"kind"`
        Key string `json:"key,optional"`
        Value string `json:"value,optional"`
        Data map[string]interface{} `json:"data,optional"`
        ParticipantID int `json:"participant_id,optional"`
    }

    ClearAttachmentDataRequest {
        AttachmentID int `json:"attachment_id"`
        Kind string `json:"kind,optional"`
        Key string `json:"key,optional"`
        ParticipantID int `json:"participant_id,optional"`
    }

    AttachmentDataResponse {
        Kind string `json:"kind"`
        Data map[string]interface{} `json:"data"`
        ParticipantID int `json:"participant_id,optional"`
    }
)

@server(
    jwt: Auth
    group: attachment_data
    prefix: /v1/attachment
)

service bcare-api {
    @handler Get
    post /data/get (GetAttachmentDataRequest) returns (AttachmentDataResponse)

    @handler Set
    post /data/set (SetAttachmentDataRequest) returns (CommonResponse)

    @handler Clear
    post /data/clear (ClearAttachmentDataRequest) returns (CommonResponse)
}