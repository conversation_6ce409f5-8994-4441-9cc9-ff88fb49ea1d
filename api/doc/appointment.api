info(
    desc: "appointment api"
    author: "lamvh"
    email: "<EMAIL>"
)

type (
    Appointment {
        Id int `json:"id"`
        Title string `json:"title"`
        StartTime string `json:"start_time"`
        EndTime string `json:"end_time"`
        ArrivedAt string `json:"arrived_at"`
        Notes string `json:"notes"`
        ExtraNotes string `json:"extra_notes"`
        Type int `json:"type"`
        PersonId int `json:"person_id"`
        DoctorId int `json:"doctor_id"`
        CreatorId int `json:"creator_id"`
        Status int `json:"status"`
        ReminderStatus int `json:"reminder_status"`
        History []HistoryEntry `json:"history"`
        CreatedAt string `json:"created_at"`
        UpdatedAt string `json:"updated_at"`
    }
    AppointmentSort {
        Id int `json:"id"`
        StartTime string `json:"start_time"`
        EndTime string `json:"end_time"`
        Notes string `json:"notes"`
        ExtraNotes string `json:"extra_notes"`
        DoctorId int `json:"doctor_id"`
    }

    AppointmentAddRequest {
        StartTime string `json:"start_time"`
        EndTime string `json:"end_time"`
        PersonId int `json:"person_id"`
        DoctorId int `json:"doctor_id,optional"`
        Status int `json:"status"`
        Type int `json:"type,optional"`
        Notes string `json:"notes,optional"`
        ExtraNotes string `json:"extra_notes,optional"`
    }

    AppointmentDetail {
        StartTime string `json:"start_time"`
        EndTime string `json:"end_time"`
        PersonName string `json:"person_name"`
        DoctorId int `json:"doctor_id"`
    }

    AppointmentResponse {
        Appointment
        Person *Person `json:"person"`
        Doctor *UserShort `json:"doctor"`
    }

    AppointmentUpdateRequest {
        Id int `json:"id"`
        Notes string `json:"notes,optional"`
        Status int `json:"status,optional"`
        Type int `json:"type,optional"`
        StartTime string `json:"start_time,optional"`
        EndTime string `json:"end_time,optional"`
        DoctorId int `json:"doctor_id,optional"`
        ReminderStatus int `json:"reminder_status,optional"`
        ExtraNotes string `json:"extra_notes,optional"`
        Modified []string `json:"modified,optional"`
    }

    AppointmentGetRequest {
        Id int `json:"id,optional"`
        PersonId int `json:"person_id,optional"`
    }

    AppointmentDeleteRequest {
        Id int `json:"id"`
    }

    AppointmentListRequest {
        PageSize int `json:"page_size,optional"`
        Page int `json:"page,optional"`
        Filter AppointmentFilter `json:"filter,optional"`
        FromDate string `json:"from_date,optional"`
        ToDate string `json:"to_date,optional"`
        HasDoctor string `json:"has_doctor,optional,options=yes|no|both"`
        OrderBy string `json:"order_by,optional"`
    }

    AppointmentFilter {
        PersonId int `json:"person_id,optional"`
        DoctorId int `json:"doctor_id,optional"`
        Status int `json:"status,optional"`
        Type int `json:"type,optional"`
    }

    AppointmentListResponse {
        Appointments []AppointmentResponse `json:"appointments"`
        Total int `json:"total"`
        TotalPage int `json:"total_page"`
    }


    AppointmentDynamicQuery {
        DynamicQuery
        Doctor string `json:"doctor,optional"`
        Person string `json:"person,optional"`
        Task string `json:"task,optional"`
        Note string `json:"note,optional"`
        Arrived string `json:"arrived,optional,options=yes|no|both"`
        HasDoctor string `json:"has_doctor,optional,options=yes|no|both"`
        SourceId int `json:"source_id,optional"`
        Export bool `json:"export,optional"`
    }
)

@server(
    jwt: Auth
    group: appointment
    //middleware: AuthorizerMiddleware
    prefix: /v1/appointment
)
service bcare-api {
    @handler AddHandler
    post /add (AppointmentAddRequest) returns (AppointmentResponse);

    @handler GetHandler
    post /get (AppointmentGetRequest) returns (AppointmentResponse);

    @handler GetLastedHandler
    post /get/next (AppointmentGetRequest) returns (AppointmentResponse);

    @handler ListHandler
    post /list (AppointmentListRequest) returns (AppointmentListResponse);

    @handler QueryHandler
    post /query (AppointmentDynamicQuery) returns (DynamicQueryResponse);

    @handler UpdateHandler
    post /update (AppointmentUpdateRequest) returns (AppointmentResponse);

    @handler DeleteHandler
    post /delete (AppointmentDeleteRequest) returns ();
}
