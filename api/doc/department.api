info(
    desc: "Department API"
    author: "Your Name"
    email: "<EMAIL>"
)

type (
    Department {
        Id int `json:"id"`
        Name string `json:"name"`
        Description string `json:"description,optional"`
        CreatedAt string `json:"created_at"`
        UpdatedAt string `json:"updated_at"`
    }

    DepartmentAddRequest {
        Name string `json:"name"`
        Description string `json:"description,optional"`
    }

    DepartmentUpdateRequest {
        Id int `json:"id"`
        Name string `json:"name,optional"`
        Description string `json:"description,optional"`
    }

    DepartmentDeleteRequest {
        Id int `json:"id"`
    }

    DepartmentResponse {
        Department
    }

    DepartmentListRequest {
       Name string `json:"name,optional"`
    }

    DepartmentListResponse {
        Departments []Department `json:"departments"`
    }

    DepartmentShort {
        Id int `json:"id"`
        Name string `json:"name"`
    }
)

@server(
    jwt: Auth
    group: department
    prefix: /v1/department
)
service bcare-api {


    @handler ListHandler
    post /list (DepartmentListRequest) returns (DepartmentListResponse);
}