info(
    desc: "Tag Person API"
    author: "Your Name"
    email: "<EMAIL>"
)

type (
    TagPerson {
        Id int `json:"id"`
        DeletedAt string `json:"deleted_at,optional"`
        Status int `json:"status"`
        Version int `json:"version"`
        CreatedAt string `json:"created_at"`
        UpdatedAt string `json:"updated_at"`
        AssignedBy int `json:"assigned_by,optional"`
        RemovedBy int `json:"removed_by,optional"`
        PersonId int `json:"person_id"`
        TagId int `json:"tag_id"`
    }

    TagPersonAddRequest {
        PersonId int `json:"person_id"`
        TagId int `json:"tag_id"`
        AssignedBy string `json:"assigned_by,optional"`
    }

    TagPersonUpdateRequest {
        Id int `json:"id"`
        PersonId int `json:"person_id,optional"`
        TagId int `json:"tag_id,optional"`
    }

    TagPersonDeleteRequest {
        TagId int `json:"tag_id"`
        PersonId int `json:"person_id"`
    }
)

@server(
    jwt: Auth
    group: tag_person
    prefix: /v1/tag/person
)
service bcare-api {
    @handler AddHandler
    post /add (TagPersonAddRequest) returns (TagPerson)

    @handler UpdateHandler
    post /update (TagPersonUpdateRequest) returns (TagPerson)

    @handler DeleteHandler
    post /delete (TagPersonDeleteRequest) returns (TagPerson)
}