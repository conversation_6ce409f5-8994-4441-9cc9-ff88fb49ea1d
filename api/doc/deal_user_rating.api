info(
    desc: "Deal user API"
    author: "Your Name"
    email: "<EMAIL>"
)

type (
    DealUserRating {
        Id int `json:"id"`
        DealUserId int `json:"deal_user_id"`
        Category string `json:"category"`
        Rating float64 `json:"rating"`
    }

    DealUserRatingAddRequest {
        DealUserId int `json:"deal_user_id,optional"`
        Category string `json:"category,optional"`
        Rating float64 `json:"rating,optional"`
    }

    DealUserRatingUpdateRequest {
        Id int `json:"id"`
        Category string `json:"category,optional"`
        Rating float64 `json:"rating,optional"`
    }

    DealUserRatingDeleteRequest {
        Id int `json:"id"`
    }
)

@server(
    jwt: Auth
    group: deal_user
    prefix: /v1/deal_user/rating
)
service bcare-api {
    @handler AddRatingHandler
    post /add (DealUserRatingAddRequest) returns (DealUserRating)

    @handler UpdateRatingHandler
    post /update (DealUserRatingUpdateRequest) returns (DealUserRating)

    @handler DeleteRatingHand<PERSON>
    post /delete (DealUserRatingDeleteRequest) returns (DealUserRating)
}