info(
    desc: "Raw Query API (Read-only)"
    author: "Your Name"
    email: "<EMAIL>"
)

type (
    // Định nghĩa parameter với kiểu dữ liệu rõ ràng
    Parameter {
        Type string `json:"type,options=string|int|float|bool|null|date|time|datetime|timestamp|array|json|uuid|bytea"`// - bytea: dữ liệu nhị phân
        Value string `json:"value"` // Giá trị dưới dạng string, sẽ được chuyển đổi theo Type
        ArrayElementType string `json:"arrayElementType,optional,options=string|int|float|bool"` // Chỉ dùng khi Type là "array"
    }

    RawQueryRequest {
        SQL string `json:"sql"` // Câu truy vấn SQL với placeholders $1, $2, ...
        Parameters []Parameter `json:"parameters,optional"` // Danh sách parameters có kiểu
        Timeout int `json:"timeout,optional,default=30"` // Thời gian timeout cho query (giây)
    }

    RawQueryResponse {
        Results []map[string]interface{} `json:"results"`
        RowCount int64 `json:"rowCount,optional"`
        ExecutionTime float64 `json:"executionTime,optional"` // Thời gian thực thi truy vấn (ms)
        ColumnTypes map[string]string `json:"columnTypes,optional"` // Thông tin kiểu dữ liệu của các cột
    }

    QueryError {
        Code string `json:"code"`
        Message string `json:"message"`
        Detail string `json:"detail,optional"`
    }
)

@server(
    jwt: Auth
    group: query
    prefix: /v1/query
)
service bcare-api {
    @doc(
        summary: "Raw Query"
    )
    @handler RawQueryHandler
    post /raw (RawQueryRequest) returns (RawQueryResponse)
}