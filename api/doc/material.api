info(
    desc: "material api"
    author: "lamvh"
)

type (
    Material {
        Id int `json:"id"`
        Code string `json:"code,optional"`
        Name string `json:"name"`
        Unit string `json:"unit"`
        PackagingSpecification string `json:"packaging_specification,optional"`
        Description string `json:"description,optional"`
        CostPrice float64 `json:"cost_price,optional"`
        Status int `json:"status"`
        Version int `json:"version"`
        CreatedAt string `json:"created_at"`
        UpdatedAt string `json:"updated_at"`
        DeletedAt string `json:"deleted_at"`
    }

    MaterialAddRequest {
        Code string `json:"code,optional"`
        Name string `json:"name"`
        Unit string `json:"unit"`
        PackagingSpecification string `json:"packaging_specification,optional"`
        Description string `json:"description,optional"`
        CostPrice float64 `json:"cost_price,optional"`
        Status int `json:"status,range=[0:10],optional"`
    }

    MaterialUpdateRequest {
        Id int `json:"id"`
        Code string `json:"code,optional"`
        Name string `json:"name,optional"`
        Unit string `json:"unit,optional"`
        PackagingSpecification string `json:"packaging_specification,optional"`
        Description string `json:"description,optional"`
        CostPrice float64 `json:"cost_price,optional"`
        Status int `json:"status,range=[0:10],optional"`
        Modified []string `json:"modified,optional"`
    }

    MaterialGetRequest {
        Id int `json:"id"`
        Code string `json:"code,optional"`
        Name string `json:"name,optional"`
    }

    MaterialDeleteRequest {
        Id int `json:"id"`
        Name string `json:"name"`
    }

    MaterialListRequest {
        PageSize int `json:"page_size,optional"`
        Page int `json:"page,optional"`
        Filter MaterialFilter `json:"filter,optional"`
        Search string `json:"search,optional"`
        OrderBy string `json:"order_by,optional"`
    }

    MaterialFilter {
        Code string `json:"code,optional"`
        Name string `json:"name,optional"`
        Unit string `json:"unit,optional"`
        Status int `json:"status,range=[0:10],optional"`
    }

    MaterialResponse {
        Material
    }

    MaterialListResponse {
        Materials []MaterialResponse `json:"materials"`
        Total int `json:"total"`
        TotalPage int `json:"total_page"`
    }
)

@server(
    jwt: Auth
    group: material
    prefix: /v1/material
)
service bcare-api {
    @handler AddHandler
    post /add (MaterialAddRequest) returns (Material);

    @handler GetHandler
    post /get (MaterialGetRequest) returns (Material);

    @handler ListHandler
    post /list (MaterialListRequest) returns (MaterialListResponse);

    @handler UpdateHandler
    post /update (MaterialUpdateRequest) returns (Material);

    @handler DeleteHandler
    post /delete (MaterialDeleteRequest) returns ();
} 