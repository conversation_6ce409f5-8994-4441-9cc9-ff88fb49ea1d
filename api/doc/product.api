info(
    desc: "product api"
    author: "lamvh"
    email: "<EMAIL>"
)

type (
        Product {
        Id int `json:"id"`
        Name string `json:"name"`
        Code string `json:"code"`
        Description string `json:"description"`
        Price int `json:"price"`
        Type string `json:"type"`
        Status int `json:"status"`
        Quantity int `json:"quantity"`
        SKU string `json:"sku"`
        UnitId int `json:"unit_id"`
        GroupId int `json:"group_id"`
        CategoryId int `json:"category_id"`
        Collection []string `json:"collection"`
        CreatedAt string `json:"created_at"`
        UpdatedAt string `json:"updated_at"`
        DeletedAt string `json:"deleted_at"`
        Version int `json:"version"`
    }

    ProductImage {
        MediaId int `json:"media_id"`
        Title string `json:"title"`
        Description string `json:"description"`
    }

    ProductAddRequest {
        Name string `json:"name"`
        Code string `json:"code"`
        Description string `json:"description,optional"`
        Price int `json:"price,optional"`
        Type string `json:"type"`
        Status int `json:"status,range=[0:10]"`
        Quantity int `json:"quantity,optional"`
        SKU string `json:"sku,optional"`
        UnitId *int `json:"unit_id"`
        GroupId *int `json:"group_id"`
        CategoryId *int `json:"category_id"`
        Collection []string `json:"collection,optional"`
        Operations []ProductOperationAddRequest `json:"operations,optional"`
    }

    ProductResponse {
        Product
        Category *TermShort `json:"category"`
        Operations []Operation `json:"operations"`
    }

    ProductGetRequest {
        Id int `json:"id"`
        Name string `json:"name,optional"`
        Code string `json:"code,optional"`
    }

    ProductDeleteRequest {
        Id int `json:"id"`
    }

    ProductUpdateRequest {
        Id int `json:"id"`
        Name string `json:"name,optional"`
        Code string `json:"code,optional"`
        Description string `json:"description,optional"`
        Price int `json:"price,optional"`
        Type string `json:"type,optional"`
        Status int `json:"status,range=[0:10],optional"`
        Quantity int `json:"quantity,optional"`
        SKU string `json:"sku,optional"`
        UnitId *int `json:"unit_id,optional"`
        GroupId *int `json:"group_id,optional"`
        CategoryId *int `json:"category_id,optional"`
        Collection []string `json:"collection,optional"`
        Operations []ProductOperationAddRequest `json:"operations,optional"`
        Modified []string `json:"modified,optional"`
    }

    ProductFilter {
        Name string `json:"name,optional"`
        Code string `json:"code,optional"`
        Type string `json:"type,optional"`
        Status int `json:"status,range=[0:10],optional"`
        SKU string `json:"sku,optional"`
        UnitId int `json:"unit_id,optional"`
        GroupId int `json:"group_id,optional"`
        CategoryId int `json:"category_id,optional"`
    }

    ProductListRequest {
        PageSize int `json:"page_size,optional"`
        Page int `json:"page,optional"`
        Filter ProductFilter `json:"filter,optional"`
        Search string `json:"search,optional"`
        UserCreated int `json:"user_created,optional"`
        UserManaged []int `json:"user_managed,optional"`
        OrderBy string `json:"order_by,optional"`
    }

    ProductListResponse {
        Products []ProductResponse `json:"products"`
        Total int `json:"total"`
        TotalPage int `json:"total_page"`
    }
)

@server(
    jwt: Auth
    group: product
    prefix: /v1/product
)
service bcare-api {
    @handler AddHandler
    post /add (ProductAddRequest) returns (ProductResponse);

    @handler GetHandler
    post /get (ProductGetRequest) returns (ProductResponse);

    @handler DeleteHandler
    post /delete (ProductDeleteRequest) returns (ProductResponse);

    @handler UpdateHandler
    post /update (ProductUpdateRequest) returns (ProductResponse);

    @handler ListHandler
    post /list (ProductListRequest) returns (ProductListResponse);
}