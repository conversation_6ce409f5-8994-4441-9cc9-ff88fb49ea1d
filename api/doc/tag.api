info(
    desc: "tag api"
    author: "lamvh"
    email: "<EMAIL>"
)

type Tag {
    Id int `json:"id"`
    Name string `json:"name"`
    Category string `json:"category"`
    Description string `json:"description,optional"`
    UserId int `json:"user_id,optional"`
    Status int `json:"status"`
    Version int `json:"version"`
    CreatedAt string `json:"created_at"`
    UpdatedAt string `json:"updated_at"`
    DeletedAt string `json:"deleted_at,optional"`
}

type TagInfo {
    Id int `json:"id"`
    Name string `json:"name"`
    Category string `json:"category"`
}

type TagShort {
    Id int `json:"id"`
    Name string `json:"name"`
    Category string `json:"category"`
}

type TagAddRequest {
    Name string `json:"name"`
    Category string `json:"category"`
    Description string `json:"description,optional"`
}

type TagResponse {
    Tag
}

type TagUpdateRequest {
    Id int `json:"id"`
    Name string `json:"name,optional"`
    Category string `json:"category,optional"`
    Description string `json:"description,optional"`
    Status int `json:"status,range=[0:10],optional"`
    Modified []string `json:"modified,optional"`
}

type TagGetRequest {
    Id int `json:"id"`
    Name string `json:"name,optional"`
    Category string `json:"category,optional"`
}

type TagDeleteRequest {
    Id int `json:"id"`
    Name string `json:"name,optional"`
}

type TagListRequest {
    PageSize int `json:"page_size,optional"`
    Page int `json:"page,optional"`
    Filter TagFilter `json:"filter,optional"`
    Search string `json:"search,optional"`
    OrderBy string `json:"order_by,optional"`
}

type TagFilter {
    Ids []int `json:"ids,optional"`
    Name string `json:"name,optional"`
    Category string `json:"category,optional"`
    Status int `json:"status,range=[0:10],optional"`
    UserId int `json:"user_id,optional"`
}

type TagListResponse {
    Tags []TagResponse `json:"tags"`
    Total int `json:"total"`
    TotalPage int `json:"total_page"`
}

@server(
    jwt: Auth
    group: tag
    prefix: /v1/tag
)
service bcare-api {
    @handler AddHandler
    post /add (TagAddRequest) returns (TagResponse);

    @handler GetHandler
    post /get (TagGetRequest) returns (TagResponse);

    @handler ListHandler
    post /list (TagListRequest) returns (TagListResponse);

    @handler UpdateHandler
    post /update (TagUpdateRequest) returns (TagResponse);

    @handler DeleteHandler
    post /delete (TagDeleteRequest) returns ();
}