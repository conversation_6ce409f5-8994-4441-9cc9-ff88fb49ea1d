@server(
    group: queryv2
    prefix: /v1/query-v2
)

service bcare-api {
    @handler QueryV2Handler
    post /query (DynamicQueryV2) returns (DynamicQueryV2Response)
}

type LogicalExpressionV2 {
    Type string `json:"type,options=AND|OR|CONDITION"`
    Condition *FilterConditionV2 `json:"condition,optional"`
    Expressions []LogicalExpressionV2 `json:"expressions,optional"`
}

type FilterConditionV2 {
    Field string `json:"field,optional"`
    Operator string `json:"operator,optional,options=EQ|NEQ|GT|GTE|LT|LTE|IN|NOTIN|LIKE|NOTLIKE|BETWEEN|ISNULL|ISNOTNULL|TODAY|CONTAINS|JSON_CONTAINS_ANY"`
    Value interface{} `json:"value,optional"`
    Function string `json:"function,optional"`
    GroupedIn [][]interface{} `json:"grouped_in,optional"`

    // Tách riêng field cho SQL phức tạp ở vế phải
    <PERSON> string `json:"right_field,optional"`
    RightExprRaw string `json:"right_expr_raw,optional"`
    RightExpr *ExpressionV2 `json:"right_expr,optional"`
    Subquery *DynamicQueryV2 `json:"subquery,optional"`

    // Tương tự cho left side
    LeftExprRaw string `json:"left_expr_raw,optional"`
    LeftExpr *ExpressionV2 `json:"left_expr,optional"`
}

type ExpressionV2 {
    Type string `json:"type,options=ARITHMETIC|FUNCTION|COLUMN|LITERAL"`
    LiteralType string `json:"literal_type,optional,options=STRING|NUMBER|RAW|BOOLEAN|NULL"`
    Operator string `json:"operator,optional,options=+|-|*|/"`
    Function string `json:"function,optional"`
    Arguments []ExpressionV2 `json:"arguments,optional"`
    ColumnRef string `json:"column_ref,optional"`
    Value interface{} `json:"value,optional"`
}

type DynamicQueryV2 {
    Table string `json:"table"`
    Alias string `json:"alias,optional"`
    Distinct *bool `json:"distinct,optional"`
    DistinctOn []string `json:"distinct_on,optional"`
    WhereLogic *LogicalExpressionV2 `json:"where_logic,optional"`
    HavingLogic *LogicalExpressionV2 `json:"having_logic,optional"`
    GroupBy []string `json:"group_by"`
    Aggregations []AggregationV2 `json:"aggregations"`
    Sort []SortCriteriaV2 `json:"sort"`
    Limit int `json:"limit"`
    Offset int `json:"offset"`
    Selects []SelectFieldV2 `json:"selects"`
    Joins []JoinInfoV2 `json:"joins"`
    CTEs []CTEInfoV2 `json:"ctes,optional"`
    Lock *LockInfoV2 `json:"lock,optional"`
    UnionQueries []UnionInfoV2 `json:"unions,optional"`
}

type SelectFieldV2 {
    Field string `json:"field"`
    Function string `json:"function,optional,options=COUNT|SUM|AVG|MIN|MAX|ARRAY_AGG|STRING_AGG|JSON_AGG|JSONB_AGG"`
    Alias string `json:"alias,optional"`
    Format string `json:"format,optional"`
    Case *CaseWhenV2 `json:"case,optional"`
    RawSQL string `json:"raw_sql,optional"`
}

type CaseWhenV2 {
    Conditions []CaseConditionV2 `json:"conditions"`
    Else interface{} `json:"else,optional"`
}

type CaseConditionV2 {
    When LogicalExpressionV2 `json:"when"`
    Then interface{} `json:"then"`
}

type JoinInfoV2 {
    Table string `json:"table"`
    Alias string `json:"alias,optional"`
    Type string `json:"type,options=INNER|LEFT|RIGHT|FULL|CROSS"`
    OnLogic *LogicalExpressionV2 `json:"on_logic,optional"`
    Using []string `json:"using,optional"`
    Subquery *DynamicQueryV2 `json:"subquery,optional"`
}

type AggregationV2 {
    Field string `json:"field"`
    Function string `json:"function,options=COUNT|SUM|AVG|MIN|MAX|ARRAY_AGG|STRING_AGG|JSON_AGG|JSONB_AGG"`
    Alias string `json:"alias"`
    Distinct *bool `json:"distinct,optional"`
    Over *WindowSpecificationV2 `json:"over,optional"`
}

type SortCriteriaV2 {
    Field string `json:"field"`
    Order string `json:"order,options=ASC|DESC"`
    Collate string `json:"collate,optional"`
}

type WindowSpecificationV2 {
    Name string `json:"name,optional"`
    PartitionBy []string `json:"partition_by"`
    OrderBy []SortCriteriaV2 `json:"order_by"`
    BasedOn string `json:"based_on,optional"`
}

type CTEInfoV2 {
    Name string `json:"name"`
    Columns []string `json:"columns,optional"`
    Query DynamicQueryV2 `json:"query"`
}

type LockInfoV2 {
    Type string `json:"type,options=UPDATE|SHARE"`
    Of []string `json:"of,optional"`
    WaitMode string `json:"wait_mode,options=WAIT|NOWAIT|SKIP_LOCKED"`
}

type UnionInfoV2 {
    Type string `json:"type,options=UNION|UNION_ALL|INTERSECT|EXCEPT"`
    Query DynamicQueryV2 `json:"query"`
}

type DynamicQueryV2Response {
    Result map[string]interface{} `json:"result"`
}