info(
    desc: "Cash Flow Note API"
    author: "Your Name"
    email: "<EMAIL>"
)

type (
    CashFlowNote {
        Id int `json:"id"`
        CashFlowId int `json:"cash_flow_id"`
        UserId int `json:"user_id"`
        Body string `json:"body"`
        Status int `json:"status"`
        CreatedAt string `json:"created_at"`
        UpdatedAt string `json:"updated_at"`
        DeletedAt string `json:"deleted_at,omitempty"`
        Version int `json:"version"`
    }

    CashFlowNoteAddRequest {
        CashFlowId int `json:"cash_flow_id"`
        Body string `json:"body"`
    }

    CashFlowNoteUpdateRequest {
        Id int `json:"id"`
        Body string `json:"body"`
    }

    CashFlowNoteDeleteRequest {
        Id int `json:"id"`
    }

    CashFlowNoteListRequest {
        PageSize int `json:"page_size,optional"`
        Page int `json:"page,optional"`
        Filter CashFlowNoteFilter `json:"filter,optional"`
        OrderBy string `json:"order_by,optional"`
    }

    CashFlowNoteFilter {
        CashFlowId int `json:"cash_flow_id"`
    }

    CashFlowNoteListResponse {
        Notes []CashFlowNoteResponse `json:"notes"`
        Total int `json:"total"`
        TotalPage int `json:"total_page"`
    }

    CashFlowNoteResponse {
        CashFlowNote
        User *UserShort `json:"creator"`
    }
)

@server(
    jwt: Auth
    group: cash_flow_note
    prefix: /v1/cash-flow/note
)

service bcare-api {
    @handler AddHandler
    post /add (CashFlowNoteAddRequest) returns (CashFlowNote)

    @handler UpdateHandler
    post /update (CashFlowNoteUpdateRequest) returns (CashFlowNote)

    @handler DeleteHandler
    post /delete (CashFlowNoteDeleteRequest) returns (CashFlowNote)

    @handler ListHandler
    post /list (CashFlowNoteListRequest) returns (CashFlowNoteListResponse)
} 