info(
    desc: "Deal user API"
    author: "Your Name"
    email: "<EMAIL>"
)

type (
    DealStageHistory {
        Id int64 `json:"id,omitempty"`
        DealId int64 `json:"deal_id,omitempty"`
        Before int64 `json:"before,omitempty"`
        After int64 `json:"after,omitempty"`
        UserId int64 `json:"user_id,omitempty"`
        ChangedAt string `json:"changed_at,omitempty"`
    }

    DealStageHistoryUpdateRequest {
        Id int `json:"id"`
        ChangedAt string `json:"changed_at,optional"`
    }

    DealStageHistoryResponse {
        DealStageHistory
        BeforName string `json:"before_name,omitempty"`
        AfterName string `json:"after_name,omitempty"`

        // thông tin khách
        Person *Person `json:"person,omitempty"`
        PersonSource string `json:"person_source,omitempty"`
        // thông tin người giới thiệu
        Referrer *Person `json:"referrer,omitempty"`
        // thông tin sale
        SaleUser *UserShort `json:"sale_user,omitempty"`
        // thông tin deal
        Deal *DealResponse `json:"deal,omitempty"`
    }

    DealStageHistoryDynamicQuery {
        DynamicQuery
        Export bool `json:"export,optional"`
    }

    DealStageHistoryListRequest {
        PageSize int `json:"page_size,optional"`
        Page int `json:"page,optional"`
        Filter DealStageHistoryFilter `json:"filter,optional"`
        OrderBy string `json:"order_by,optional"`
        IncludeRelation bool `json:"include_relation,optional"`
    }

    DealStageHistoryFilter {
        DealId int `json:"deal_id,optional"`
        Before int `json:"before,optional"`
        After int `json:"after,optional"`
    }

    DealStageHistoryListResponse {
        DealStageHistory []DealStageHistory `json:"stage_histories"`
        Total int `json:"total"`
        TotalPage int `json:"total_page"`
    }
)

@server(
    jwt: Auth
    group: deal_stage_history
    prefix: /v1/deal/history_stage
)

service bcare-api {
    @handler ListHandler
    post /list (DealStageHistoryListRequest) returns (DealStageHistoryListResponse)

    @handler UpdateHandler
    post /update (DealStageHistoryUpdateRequest) returns (DealStageHistory)

    @handler QueryHandler
    post /query (DealStageHistoryDynamicQuery) returns (DynamicQueryResponse)
}