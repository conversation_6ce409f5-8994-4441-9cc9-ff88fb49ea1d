info(
    desc: "payment api"
    author: "lamvh"
    email: "<EMAIL>"
)

type (
    Payment {
        Id int `json:"id"`
        TotalAmount float64 `json:"total_amount"`
        Cash float64 `json:"cash"`
        CreditCard float64 `json:"credit_card"`
        Mpos float64 `json:"mpos"`
        Bank float64 `json:"bank"`
        Momo float64 `json:"momo"`
        State string `json:"state"`
        Status int `json:"status"`
        PaymentDate string `json:"payment_date"`
        BillId int `json:"bill_id"`
        PersonId int `json:"person_id"`
        DealId int `json:"deal_id"`
        UserId int `json:"user_id"`
        CreatedAt string `json:"created_at"`
        UpdatedAt string `json:"updated_at"`
        DeletedAt string `json:"deleted_at"`
        Version int `json:"version"`
    }

    PaymentAddRequest {
        TotalAmount float64 `json:"total_amount"`
        Cash float64 `json:"cash,optional"`
        CreditCard float64 `json:"credit_card,optional"`
        Mpos float64 `json:"mpos,optional"`
        Bank float64 `json:"bank,optional"`
        Momo float64 `json:"momo,optional"`
        State string `json:"state,optional"`
        PaymentDate string `json:"payment_date,optional"`
        BillId int `json:"bill_id"`
        PersonId int `json:"person_id"`
        DealId int `json:"deal_id,optional"`
        SendSms int `json:"send_sms,optional"`
        PaymentAllocations []PaymentAllocationAddRequest `json:"allocations,optional"`
    }

    PaymentUpdateRequest {
        Id int `json:"id"`
        TotalAmount float64 `json:"total_amount,optional"`
        Cash float64 `json:"cash,optional"`
        CreditCard float64 `json:"credit_card,optional"`
        Mpos float64 `json:"mpos,optional"`
        Bank float64 `json:"bank,optional"`
        Momo float64 `json:"momo,optional"`
        State string `json:"state,optional"`
        PaymentDate string `json:"payment_date,optional"`
        PaymentAllocations []PaymentAllocationUpdateRequest `json:"allocations,optional"`
        Modified []string `json:"modified,optional"`
    }

    PaymentGetRequest {
        Id int `json:"id"`
    }

    PaymentDeleteRequest {
        Id int `json:"id"`
    }

    PaymentListRequest {
        PageSize int `json:"page_size,optional"`
        Page int `json:"page,optional"`
        Filter PaymentFilter `json:"filter,optional"`
        Kind string `json:"kind,optional,options=refund|normal|both"`
        OrderBy string `json:"order_by,optional"`
    }

    PaymentFilter {
        BillId int `json:"bill_id,optional"`
        Status int `json:"status,optional"`
        State string `json:"state,optional"`
        PersonId int `json:"person_id,optional"`
        DealId int `json:"deal_id,optional"`
    }

    PaymentListResponse {
        Payments []PaymentResponse `json:"payments"`
        Total int `json:"total"`
        TotalPage int `json:"total_page"`
    }

    PaymentResponse {
        Payment
        PaymentAllocations []PaymentAllocationResponse `json:"allocations"`
        Bill *Bill `json:"bill"`
    }


    PaymentReportRecord {
        CreatedAt string `json:"created_at"`
        Id int `json:"id"`
        PersonId int `json:"person_id"`
        PersonCode int `json:"person_code"`
        FullName string `json:"full_name"`
        Phone string `json:"phone"`
        ProductNames string `json:"product_names,optional"`
        GroupNames string `json:"group_names,optional"`
        Income float64 `json:"income"`
        Expense float64 `json:"expense"`
        TotalAmount float64 `json:"total_amount"`
        Cash float64 `json:"cash,optional"`
        Bank float64 `json:"bank,optional"`
        Momo float64 `json:"momo,optional"`
        Mpos float64 `json:"mpos,optional"`
        CreditCard float64 `json:"credit_card,optional"`
        ProductAmount float64 `json:"product_amount,optional"`                // Total amount of products
        GeneralServiceAmount float64 `json:"general_service_amount,optional"` // Total amount of general services
        GxtnAmount float64 `json:"gxtn_amount,optional"`                      // Total amount of general services
        VeneerAmount float64 `json:"veneer_amount,optional"`                  // Total amount of general services
        PhsAmount float64 `json:"phs_amount,optional"`                  // Total amount of general services
        OrthodonticAmount float64 `json:"orthodontic_amount,optional"`        // Total amount of orthodontic services
        ImplantAmount float64 `json:"implant_amount,optional"`                // Total amount of implant services
        OtherAmount float64 `json:"other_amount,optional"`                    // Total amount of other categories
        DoctorName string `json:"doctor_name,optional"`                       // tên bác sĩ phụ trách
        Person Person `json:"person"`
        Doctor UserShort `json:"user"`
    }

    PaymentReportSummary {
        TotalRecords int64 `json:"total_records"`
        TotalIncome float64 `json:"total_income"`
        TotalExpense float64 `json:"total_expense"`
        TotalRevenue float64 `json:"total_revenue"`
        // Payment Methods Summary
        TotalCash float64 `json:"total_cash"`
        TotalBank float64 `json:"total_bank"`
        TotalMomo float64 `json:"total_momo,optional"`
        TotalMpos float64 `json:"total_mpos"`
        TotalCreditCard float64 `json:"total_credit_card"`
        // Service Amounts Summary
        TotalProductAmount float64 `json:"total_product_amount"`
        TotalGxtnAmount float64 `json:"total_gxtn_amount"`
        TotalVeneerAmount float64 `json:"total_veneer_amount"`
        TotalPhsAmount float64 `json:"total_phs_amount"`
        TotalGeneralServiceAmount float64 `json:"total_general_service_amount"`
        TotalOrthodonticAmount float64 `json:"total_orthodontic_amount"`
        TotalImplantAmount float64 `json:"total_implant_amount"`
        TotalOtherAmount float64 `json:"total_other_amount"`
    }

    PaymentReportDynamicQuery {
        DynamicQuery
        Export bool `json:"export,optional"`
    }

    // Thêm các struct mới
    PaymentReportDetailResponse {
        CreatedAt string `json:"created_at"`
        Id int `json:"id"`
        Person map[string]interface{} `json:"person,optional"`
        PersonCode string `json:"person_code"`
        FullName string `json:"full_name"`
        Phone string `json:"phone"`
        Province string `json:"province,optional"`
        District string `json:"district,optional"`
        Ward string `json:"ward,optional"`
        AddressNumber string `json:"address_number"`
        ProductNames string `json:"product_names,optional"`
        GroupNames string `json:"group_names,optional"`
        TotalAmount float64 `json:"total_amount"`
        PaymentAllocations []PaymentAllocationDetail `json:"payment_allocations,optional"`
    }

    PaymentAllocationDetail {
        ProductName string `json:"product_name"`
        Category string `json:"category"`
        Price float64 `json:"price"`
        Creator *PaymentUserInfo `json:"creator,optional"`
        Payer *PaymentUserInfo `json:"payer,optional"`
        PaidAmount float64 `json:"paid_amount"`
    }

    PaymentUserInfo {
        Id int `json:"id"`
        Username string `json:"username"`
        Name string `json:"name"`
    }
)

@server(
    jwt: Auth
    group: economy_payment
    prefix: /v1/economy/payment
)
service bcare-api {
    @handler AddHandler
    post /add (PaymentAddRequest) returns (PaymentResponse);

    @handler GetHandler
    post /get (PaymentGetRequest) returns (PaymentResponse);

    @handler ListHandler
    post /list (PaymentListRequest) returns (PaymentListResponse);

    @handler UpdateHandler
    post /update (PaymentUpdateRequest) returns (PaymentResponse);

    @handler DeleteHandler
    post /delete (PaymentDeleteRequest) returns (PaymentResponse);

    @handler QueryHandler
    post /query (PaymentReportDynamicQuery) returns (DynamicQueryResponse);

    @handler QueryDetailHandler
    post /query-detail (PaymentReportDynamicQuery) returns (DynamicQueryResponse);
}