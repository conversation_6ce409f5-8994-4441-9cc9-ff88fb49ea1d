package model

import (
	"bcare/api/internal/types"
	"bcare/common/bconst"
	"bcare/common/ctxdata"
	"bcare/ent"
	"bcare/ent/installment"
	"context"
	"fmt"
	"github.com/jinzhu/copier"
)

func HandleInstallments(ctx context.Context, tx *ent.Tx, plan *ent.InstallmentPlan, req any, isAdd bool) (installments []*ent.Installment, err error) {
	newPlan := new(types.InstallmentPlan)
	_ = copier.Copy(&newPlan, req)

	// nếu là add thì ko cần check, update thì mới check
	if !isAdd {
		if plan.DownPayment > 0 && plan.TotalInstallments > 0 && (newPlan.DownPayment != plan.DownPayment || newPlan.TotalInstallments != plan.TotalInstallments || newPlan.TotalAmount != plan.TotalAmount) {
			_, err := tx.Installment.Delete().Where(installment.PlanID(plan.ID), installment.KindEQ(installment.KindSequencePayment)).Exec(ctx)
			if err != nil {
				return nil, types.ErrInstallmentDelete.Wrap(err, err.Error())
			}
		}
	}

	amount := (newPlan.TotalAmount - newPlan.DownPayment) / float64(newPlan.TotalInstallments)
	if isAdd {
		installmentDepositRecord := new(ent.Installment)
		installmentDepositRecord.Status = bconst.StatusNormal
		installmentDepositRecord.PlanID = plan.ID
		installmentDepositRecord.Name = fmt.Sprintf("Cọc %s", newPlan.Name)
		installmentDepositRecord.InstallmentNumber = 0
		installmentDepositRecord.Amount = newPlan.DownPayment
		installmentDepositRecord.Kind = installment.KindDownPayment
		installmentDepositRecord.UserID = ctxdata.GetUidFromCtx(ctx)
		save, err := tx.Installment.Create().SetInstallment(installmentDepositRecord).Save(ctx)
		if err != nil {
			return nil, types.ErrInstallmentAdd.Wrap(err)
		}
		installments = append(installments, save)
	} else {
		installmentDepositRecord, err := tx.Installment.Query().Where(installment.PlanID(plan.ID), installment.KindEQ(installment.KindDownPayment)).First(ctx)
		if err != nil && !ent.IsNotFound(err) {
			return nil, types.ErrInstallmentNotFound.Wrap(err)
		}

		if newPlan.TotalAmount != 0 {
			if installmentDepositRecord == nil {
				installmentDepositRecord = new(ent.Installment)
				installmentDepositRecord.Status = bconst.StatusNormal
				installmentDepositRecord.PlanID = plan.ID
				installmentDepositRecord.Kind = installment.KindDownPayment
				installmentDepositRecord.UserID = ctxdata.GetUidFromCtx(ctx)
				installmentDepositRecord.Name = fmt.Sprintf("Cọc %s", newPlan.Name)
				installmentDepositRecord.Amount = newPlan.DownPayment
				save, err := tx.Installment.Create().SetInstallment(installmentDepositRecord).Save(ctx)
				if err != nil {
					return nil, types.ErrInstallmentUpdate.Wrap(err)
				}
				installments = append(installments, save)
			} else {
				installmentDepositRecord.Name = fmt.Sprintf("Cọc %s", newPlan.Name)
				installmentDepositRecord.Amount = newPlan.DownPayment
				save, err := tx.Installment.UpdateOneID(installmentDepositRecord.ID).SetInstallment(installmentDepositRecord).Save(ctx)
				if err != nil {
					return nil, types.ErrInstallmentUpdate.Wrap(err)
				}
				installments = append(installments, save)
			}

		} else {
			err = tx.Installment.DeleteOne(installmentDepositRecord).Exec(ctx)
			if err != nil {
				return nil, types.ErrInstallmentUpdate.Wrap(err)
			}
		}

	}

	if isAdd || plan.DownPayment > 0 && plan.TotalInstallments > 0 && (newPlan.DownPayment != plan.DownPayment || newPlan.TotalInstallments != plan.TotalInstallments || newPlan.TotalAmount != plan.TotalAmount) {
		for i := 1; i <= newPlan.TotalInstallments; i++ {
			installmentRecord := new(ent.Installment)
			installmentRecord.Status = bconst.StatusNormal
			installmentRecord.PlanID = plan.ID
			installmentRecord.Name = fmt.Sprintf("Thu phí %s lần %d", newPlan.Name, i)
			installmentRecord.InstallmentNumber = i
			installmentRecord.Amount = amount
			installmentRecord.Kind = installment.KindSequencePayment
			installmentRecord.UserID = ctxdata.GetUidFromCtx(ctx)
			save, err := tx.Installment.Create().SetInstallment(installmentRecord).Save(ctx)
			if err != nil {
				return nil, types.ErrInstallmentAdd.Wrap(err)
			}
			installments = append(installments, save)
		}
	}
	return
}
