package model

import (
	cast2 "bcare/common/cast"
	"bcare/ent"
	"bcare/ent/bill"
	"bcare/ent/billitem"
	"bcare/ent/installment"
	"bcare/ent/installmentplan"
	"bcare/ent/payment"
	"bcare/ent/paymentallocation"
	"context"
	"fmt"
	"log"
	"math"
	"sort"
	"time"
)

// AllocateDebt allocates debt amount to appropriate bill items. dùng trong sync data
func AllocateDebt(ctx context.Context, tx *ent.Tx, personID int, debtAmount, unpaidAmount float64) error {
	log.Printf("Starting debt allocation for person_id=%d with debt_amount=%.2f\n", personID, debtAmount)

	// Find eligible bills
	bills, err := tx.Bill.Query().
		Where(
			bill.PersonID(personID),
		).
		WithItems(func(q *ent.BillItemQuery) {
			q.WithAllocations(func(pa *ent.PaymentAllocationQuery) {
				pa.WithPayment()
			})
		}).
		Order(ent.Desc(bill.FieldCreatedAt)).
		All(ctx)

	if err != nil {
		return fmt.Errorf("failed to query bills: %w", err)
	}

	log.Printf("Found %d bills\n", len(bills))

	// Find bill with total amount greater than debt
	var eligibleBill *ent.Bill
	var eligibleItems []*ent.BillItem

	for _, b := range bills {
		total := 0.0
		for _, item := range b.Edges.Items {
			total += item.Amount
		}
		log.Printf("Bill ID: %d, Total Amount: %.2f\n", b.ID, total)
		if total >= unpaidAmount {
			eligibleBill = b
			eligibleItems = b.Edges.Items
			break
		}
	}

	if eligibleBill == nil {
		return nil
	}

	log.Printf("Found eligible bill: bill_id=%d with %d items\n", eligibleBill.ID, len(eligibleItems))

	// Sort items by amount descending
	sort.Slice(eligibleItems, func(i, j int) bool {
		return eligibleItems[i].Amount < eligibleItems[j].Amount
	})

	remainingDebt := debtAmount
	paymentUpdates := make(map[int]float64)

	log.Println("\n=== Starting Bill Items Processing ===")

	for _, item := range eligibleItems {
		if remainingDebt <= 0 {
			break
		}

		log.Printf("\nProcessing Bill Item:")
		log.Printf("- Bill Item ID: %d", item.ID)
		log.Printf("- Total Amount: %.2f", item.Amount)

		// Calculate current allocated amount
		currentAllocated := 0.0
		for _, alloc := range item.Edges.Allocations {
			currentAllocated += alloc.Amount
		}
		log.Printf("- Current Allocated Amount: %.2f", currentAllocated)

		// If remaining debt >= item amount, set allocation to 0
		if remainingDebt >= item.Amount {
			log.Printf("- Debt >= Item Amount: Setting allocations to 0")
			for _, alloc := range item.Edges.Allocations {
				payment := alloc.Edges.Payment

				log.Printf("  > Deleting allocation (ID: %d) for payment_id=%d",
					alloc.ID, payment.ID)

				if err := tx.PaymentAllocation.DeleteOne(alloc).Exec(ctx); err != nil {
					return fmt.Errorf("failed to delete allocation: %w", err)
				}

				paymentUpdates[payment.ID] = paymentUpdates[payment.ID] - alloc.Amount
			}
			remainingDebt -= item.Amount
			continue // Process next item with remaining debt
		}

		// If remaining debt < item amount, update allocation
		newAllocationAmount := item.Amount - remainingDebt
		log.Printf("- New Allocation Amount: %.2f", newAllocationAmount)

		for _, alloc := range item.Edges.Allocations {
			payment := alloc.Edges.Payment

			if newAllocationAmount <= 0 {
				log.Printf("  > Deleting allocation (ID: %d) for payment_id=%d",
					alloc.ID, payment.ID)

				if err := tx.PaymentAllocation.DeleteOne(alloc).Exec(ctx); err != nil {
					return fmt.Errorf("failed to delete allocation: %w", err)
				}

				paymentUpdates[payment.ID] = paymentUpdates[payment.ID] - alloc.Amount
			} else {
				log.Printf("  > Updating allocation (ID: %d) amount to %.2f for payment_id=%d",
					alloc.ID, newAllocationAmount, payment.ID)

				if err := tx.PaymentAllocation.UpdateOne(alloc).
					SetAmount(newAllocationAmount).
					Exec(ctx); err != nil {
					return fmt.Errorf("failed to update allocation: %w", err)
				}

				paymentUpdates[payment.ID] = paymentUpdates[payment.ID] - (alloc.Amount - newAllocationAmount)
			}
		}

		remainingDebt = 0 // Debt has been fully processed
		break
	}

	log.Println("\n=== Processing Payment Updates ===")

	// Process payment updates
	for paymentID, amountChange := range paymentUpdates {
		paymentRecord, err := tx.Payment.Query().WithAllocations().Where(payment.ID(paymentID)).First(ctx)
		if err != nil {
			return fmt.Errorf("failed to get payment: %w", err)
		}

		newAmount := paymentRecord.TotalAmount + amountChange

		if newAmount <= 0 && paymentRecord.Edges.Allocations == nil {
			log.Printf("Deleting payment (ID: %d) as new amount would be zero or negative\n",
				payment.ID)

			if err := tx.Payment.DeleteOne(paymentRecord).Exec(ctx); err != nil {
				return fmt.Errorf("failed to delete payment: %w", err)
			}
		} else {
			log.Printf("Updating payment (ID: %d) amount to %.2f\n",
				paymentRecord.ID, newAmount)

			if err := tx.Payment.UpdateOne(paymentRecord).
				SetTotalAmount(newAmount).
				SetCash(newAmount).
				Exec(ctx); err != nil {
				return fmt.Errorf("failed to update payment: %w", err)
			}
		}
	}

	if remainingDebt > 0 {
		return fmt.Errorf("insufficient bill items for full debt allocation")
	}

	log.Println("\n=== Debt Allocation Completed Successfully ===")
	return nil
}

// AllocateUnpaidAmount allocates unpaid amount to appropriate payment allocations
func AllocateUnpaidAmount(ctx context.Context, tx *ent.Tx, personID int, unpaidAmount float64, paymentTime int) error {
	log.Printf("Starting unpaid amount allocation for person_id=%d with unpaid_amount=%.2f\n", personID, unpaidAmount)
	timePaymentQuery := cast2.UnixToTime(paymentTime)

	// Find payments for the person
	payments, err := tx.Payment.Query().
		Where(
			payment.PersonID(personID),
			payment.CreatedAtLTE(timePaymentQuery),
		).
		WithAllocations(func(pa *ent.PaymentAllocationQuery) {
			pa.WithBillItem(func(bi *ent.BillItemQuery) {
				bi.WithAttachment()
			})
		}).
		Order(ent.Desc(payment.FieldCreatedAt)).
		All(ctx)

	if err != nil {
		return fmt.Errorf("failed to query payments: %w", err)
	}

	log.Printf("Found %d payments\n", len(payments))

	remainingUnpaid := unpaidAmount

	log.Println("\n=== Starting Payment Allocations Processing ===")

	// Process each payment
	for _, paymentRecord := range payments {
		if remainingUnpaid <= 0 {
			break
		}

		if len(paymentRecord.Edges.Allocations) == 0 || paymentRecord.TotalAmount == 0 {
			continue
		}

		log.Printf("\nProcessing Payment ID: %d", payment.ID)

		allocations := paymentRecord.Edges.Allocations
		// Sort allocations by amount ascending
		sort.Slice(allocations, func(i, j int) bool {
			// Helper function to safely check if bill_item_id exists
			hasBillItem := func(alloc *ent.PaymentAllocation) bool {
				return alloc.BillItemID != nil && *alloc.BillItemID > 0
			}

			// Helper function to check if attachment has plan_id
			hasPlanID := func(alloc *ent.PaymentAllocation) bool {
				if alloc.Edges.BillItem.Edges.Attachment == nil {
					return false
				}
				return alloc.Edges.BillItem.Edges.Attachment.DealID != nil
			}

			allocI := allocations[i]
			allocJ := allocations[j]

			iHasBillItem := hasBillItem(allocI)
			jHasBillItem := hasBillItem(allocJ)

			// First check: If one has bill_item_id and other doesn't
			if iHasBillItem != jHasBillItem {
				return iHasBillItem // true = i comes first
			}

			// Both have bill_item_id
			if iHasBillItem && jHasBillItem {
				iHasPlanID := hasPlanID(allocI)
				jHasPlanID := hasPlanID(allocJ)

				// This is the key change - return true if i has plan_id
				return iHasPlanID && !jHasPlanID
			}

			// Neither has bill_item_id - sort by creation time (newest first)
			return allocI.CreatedAt.After(allocJ.CreatedAt)
		})

		// Process allocations for current payment
		for _, alloc := range allocations {
			if remainingUnpaid <= 0 {
				break
			}

			if alloc.Amount == 0 {
				continue
			}

			log.Printf("\nProcessing Allocation:")
			log.Printf("- Allocation ID: %d", alloc.ID)
			log.Printf("- Allocation Amount: %.2f", alloc.Amount)

			// If remaining unpaid >= allocation amount, delete allocation
			if remainingUnpaid >= alloc.Amount {
				log.Printf("- Unpaid >= Allocation Amount: Deleting allocation")

				if err := tx.PaymentAllocation.DeleteOne(alloc).Exec(ctx); err != nil {
					return fmt.Errorf("failed to delete allocation: %w", err)
				}

				remainingUnpaid -= alloc.Amount
				continue
			}

			// If remaining unpaid < allocation amount, update allocation
			newAllocationAmount := alloc.Amount - remainingUnpaid
			log.Printf("- New Allocation Amount: %.2f", newAllocationAmount)

			if err := tx.PaymentAllocation.UpdateOne(alloc).
				SetAmount(newAllocationAmount).
				Exec(ctx); err != nil {
				return fmt.Errorf("failed to update allocation: %w", err)
			}

			remainingUnpaid = 0
			break
		}

		// Recalculate total allocation amount for current payment
		updatedAllocations, err := tx.PaymentAllocation.Query().
			Where(paymentallocation.HasPaymentWith(payment.ID(paymentRecord.ID))).
			All(ctx)

		if err != nil {
			return fmt.Errorf("failed to query updated allocations: %w", err)
		}

		newTotalAmount := 0.0
		for _, alloc := range updatedAllocations {
			newTotalAmount += alloc.Amount
		}

		if newTotalAmount <= 0 && paymentRecord.Edges.Allocations == nil {
			log.Printf("Deleting payment (ID: %d) as new amount would be zero\n",
				paymentRecord.ID)

			if err := tx.Payment.DeleteOne(paymentRecord).Exec(ctx); err != nil {
				return fmt.Errorf("failed to delete payment: %w", err)
			}
		} else {
			log.Printf("Updating payment (ID: %d) amount to %.2f\n",
				payment.ID, newTotalAmount)

			if err := tx.Payment.UpdateOne(paymentRecord).
				SetTotalAmount(newTotalAmount).
				SetCash(newTotalAmount).
				Exec(ctx); err != nil {
				return fmt.Errorf("failed to update payment: %w", err)
			}
		}
	}

	if remainingUnpaid > 0 {
		return fmt.Errorf("insufficient payment allocations for full unpaid amount : %f", remainingUnpaid)
	}

	log.Println("\n=== Unpaid Amount Allocation Completed Successfully ===")
	return nil
}

// ProcessDebtPayment handles debt payment and creates corresponding payment records
func ProcessDebtPayment(ctx context.Context, tx *ent.Tx, personID int, paymentAmount float64, paymentTime int, dealTq *ent.Deal) error {
	log.Printf("Starting debt payment process for person_id=%d with amount=%.2f\n", personID, paymentAmount)

	timePayment := cast2.UnixToTime(paymentTime - 3600*18)
	timePaymentQuery := cast2.UnixToTime(paymentTime)

	// Find unpaid bill items
	items, err := tx.BillItem.Query().
		Where(
			billitem.HasBillWith(
				bill.PersonID(personID),
			),
		).
		WithAllocations(func(pa *ent.PaymentAllocationQuery) {
			pa.Where(paymentallocation.CreatedAtLT(timePaymentQuery))
		}).
		WithBill().
		WithAttachment().
		Order(ent.Desc(billitem.FieldCreatedAt)).
		All(ctx)

	if err != nil {
		return fmt.Errorf("failed to query bill items: %w", err)
	}

	// Find unpaid installments
	installments, err := tx.Installment.Query().
		Where(
			installment.HasPlanWith(installmentplan.PersonID(personID)),
			installment.HasAllocations(),
		).
		WithAllocations().
		Order(ent.Desc(installment.FieldPaidAt)).
		All(ctx)

	if err != nil {
		return fmt.Errorf("failed to query installments: %w", err)
	}

	// Filter and calculate unpaid items and installments
	type UnpaidDebt struct {
		Type            string // "bill_item" or "installment"
		BillItem        *ent.BillItem
		Attachment      *ent.Attachment
		Installment     *ent.Installment
		Amount          float64
		AllocatedAmount float64
		CreatedAt       time.Time
	}

	var unpaidDebts []UnpaidDebt

	// Process bill items
	for _, item := range items {
		if item.Edges.Attachment != nil && item.Edges.Attachment.PlanID != nil && *item.Edges.Attachment.PlanID > 0 {
			continue
		}
		allocatedAmount := 0.0
		for _, alloc := range item.Edges.Allocations {
			allocatedAmount += alloc.Amount
		}

		if allocatedAmount < item.Amount {
			unpaidDebts = append(unpaidDebts, UnpaidDebt{
				Type:            "bill_item",
				BillItem:        item,
				Attachment:      item.Edges.Attachment,
				Amount:          item.Amount,
				AllocatedAmount: allocatedAmount,
				CreatedAt:       item.Edges.Bill.CreatedAt,
			})
		}
	}

	// Process installments
	for _, inst := range installments {
		allocatedAmount := 0.0
		for _, alloc := range inst.Edges.Allocations {
			allocatedAmount += alloc.Amount
		}

		if allocatedAmount < inst.Amount {
			unpaidDebts = append(unpaidDebts, UnpaidDebt{
				Type:            "installment",
				Installment:     inst,
				Amount:          inst.Amount,
				AllocatedAmount: allocatedAmount,
				CreatedAt:       inst.CreatedAt,
			})
		}
	}

	if len(unpaidDebts) == 0 {
		return fmt.Errorf("no unpaid debts found for person_id=%d", personID)
	}

	log.Printf("Found %d unpaid debts\n", len(unpaidDebts))

	sort.Slice(unpaidDebts, func(i, j int) bool {
		// If types are different, prioritize bill_items
		if unpaidDebts[i].Type != unpaidDebts[j].Type {
			return unpaidDebts[i].Type == "bill_item"
		}

		// If both are bill_items, apply specific sorting rules
		if unpaidDebts[i].Type == "bill_item" {
			attachmentI := unpaidDebts[i].BillItem.Edges.Attachment
			attachmentJ := unpaidDebts[j].BillItem.Edges.Attachment

			// If dealInput is nil, sort by amount in descending order
			if dealTq == nil {
				return unpaidDebts[i].Amount > unpaidDebts[j].Amount
			}

			// Check if attachments match with input deal_id
			matchDealI := attachmentI != nil && attachmentI.DealID != nil && *attachmentI.DealID == dealTq.ID
			matchDealJ := attachmentJ != nil && attachmentJ.DealID != nil && *attachmentJ.DealID == dealTq.ID

			// If one matches deal_id and the other doesn't
			if matchDealI != matchDealJ {
				return matchDealI // Matching deal_id comes first
			}

			// If both match or both don't match with deal_id
			// Sort by amount in descending order
			return attachmentI.Price > attachmentJ.Price
		}

		// If both are installments, sort by creation date (newest first)
		return unpaidDebts[i].CreatedAt.After(unpaidDebts[j].CreatedAt)
	})

	// Create payment record
	paymentRecord := new(ent.Payment)
	paymentRecord.PersonID = personID
	paymentRecord.TotalAmount = paymentAmount
	paymentRecord.Cash = paymentAmount
	paymentRecord.State = payment.StateCompleted
	paymentRecord.CreatedAt = timePayment
	paymentRecord.PaymentDate = timePayment
	paymentRecord.UpdatedAt = timePayment
	paymentRecord, err = tx.Payment.Create().
		SetPayment(paymentRecord).
		SetPaymentDate(paymentRecord.PaymentDate).
		SetCreatedAt(paymentRecord.CreatedAt).
		SetUpdatedAt(paymentRecord.UpdatedAt).
		Save(ctx)

	if err != nil {
		return fmt.Errorf("failed to create payment: %w", err)
	}

	log.Printf("Created payment record with ID: %d\n", paymentRecord.ID)

	remainingAmount := paymentAmount

	log.Println("\n=== Processing Allocations ===")

	// Allocate payment to unpaid debts
	for _, debt := range unpaidDebts {
		if remainingAmount <= 0 {
			break
		}

		unpaidAmount := debt.Amount - debt.AllocatedAmount

		log.Printf("\nProcessing Debt:")
		log.Printf("- Type: %s", debt.Type)
		if debt.Type == "bill_item" {
			log.Printf("- Bill ID: %d, Item ID: %d", debt.BillItem.Edges.Bill.ID, debt.BillItem.ID)
		} else {
			log.Printf("- Installment ID: %d", debt.Installment.ID)
		}
		log.Printf("- Amount: %.2f", debt.Amount)
		log.Printf("- Currently Allocated: %.2f", debt.AllocatedAmount)
		log.Printf("- Unpaid Amount: %.2f", unpaidAmount)

		if unpaidAmount <= 0 {
			continue
		}

		allocationAmount := math.Min(remainingAmount, unpaidAmount)

		// Create payment allocation
		allocationCreator := tx.PaymentAllocation.Create().
			SetPayment(paymentRecord).
			SetAmount(allocationAmount).
			SetCreatedAt(timePayment).
			SetUpdatedAt(timePayment)

		if debt.Type == "bill_item" {
			allocationCreator.SetBillItem(debt.BillItem)
		} else {
			allocationCreator.SetInstallment(debt.Installment)
		}

		_, err := allocationCreator.Save(ctx)
		if err != nil {
			return fmt.Errorf("failed to create payment allocation: %w", err)
		}

		log.Printf("Created allocation of %.2f for %s\n", allocationAmount, debt.Type)

		remainingAmount -= allocationAmount
	}

	if remainingAmount > 0 {
		log.Printf("Warning: Remaining unallocated amount: %.2f\n", remainingAmount)

		// Update payment amount to reflect actual allocated amount
		actualPaymentAmount := paymentAmount - remainingAmount

		err := tx.Payment.UpdateOne(paymentRecord).
			SetTotalAmount(actualPaymentAmount).
			SetCash(actualPaymentAmount).
			Exec(ctx)

		if err != nil {
			return fmt.Errorf("failed to update payment amount: %w", err)
		}

		log.Printf("Updated payment amount to %.2f\n", actualPaymentAmount)
	}

	// Verify all allocations
	finalAllocations, err := tx.PaymentAllocation.Query().
		Where(
			paymentallocation.HasPaymentWith(
				payment.ID(paymentRecord.ID),
			),
		).
		All(ctx)

	if err != nil {
		return fmt.Errorf("failed to verify allocations: %w", err)
	}

	totalAllocated := 0.0
	for _, alloc := range finalAllocations {
		totalAllocated += alloc.Amount
	}

	log.Printf("\n=== Payment Processing Summary ===")
	log.Printf("Total Payment Amount: %.2f", paymentAmount)
	log.Printf("Total Allocated: %.2f", totalAllocated)
	log.Printf("Remaining Unallocated: %.2f", remainingAmount)

	if len(finalAllocations) == 0 {
		// If no allocations were created, delete the payment
		err := tx.Payment.DeleteOne(paymentRecord).Exec(ctx)
		if err != nil {
			return fmt.Errorf("failed to delete empty payment: %w", err)
		}
		return fmt.Errorf("no allocations could be created for this payment")
	}

	return nil
}

// Min Helper function Min returns the smaller of x or y
func Min(x, y float64) float64 {
	if x < y {
		return x
	}
	return y
}
