package model

import (
	"bcare/ent"
	"bcare/ent/payment"
	"bcare/ent/paymentallocation"
	"context"
	"fmt"
	"sort"
)

// MergePaymentsByDate combines all payments for a person on the same date
func MergePaymentsByDate(ctx context.Context, tx *ent.Tx, personID int) error {
	// Get all payments for the person with total_amount >= 0
	payments, err := tx.Payment.Query().
		Where(
			payment.PersonID(personID),
			payment.TotalAmountGTE(0), // Only get payments with total_amount >= 0
		).
		All(ctx)
	if err != nil {
		return fmt.Errorf("failed to query payments: %w", err)
	}

	// Group payments by date
	paymentsByDate := make(map[string][]*ent.Payment)
	for _, p := range payments {
		date := p.PaymentDate.Format("2006-01-02")
		paymentsByDate[date] = append(paymentsByDate[date], p)
	}

	// Process each date group
	for _, datePayments := range paymentsByDate {
		if len(datePayments) <= 1 {
			continue // Skip if only one payment exists for the date
		}

		// Use the first payment as the target payment to merge into
		targetPayment := datePayments[0]

		// Get all payment allocations for all payments in this date
		var paymentIDs []int
		for _, p := range datePayments {
			paymentIDs = append(paymentIDs, p.ID)
		}

		// Query all allocations for these payments
		allocations, err := tx.PaymentAllocation.Query().
			Where(paymentallocation.PaymentIDIn(paymentIDs...)).
			All(ctx)
		if err != nil {
			return fmt.Errorf("failed to query payment allocations: %w", err)
		}

		// Calculate total amount from allocations
		var totalAmountFromAllocations float64
		for _, alloc := range allocations {
			totalAmountFromAllocations += alloc.Amount
		}

		// Calculate totals from payment methods
		var totalCash, totalCreditCard, totalMpos, totalBank float64
		for _, p := range datePayments {
			totalCash += p.Cash
			totalCreditCard += p.CreditCard
			totalMpos += p.Mpos
			totalBank += p.Bank
		}

		// Calculate total from payment methods
		totalFromMethods := totalCash + totalCreditCard + totalMpos + totalBank

		// If there's a difference between allocation total and methods total
		if totalAmountFromAllocations != totalFromMethods {
			// Find the maximum payment method
			methodAmounts := []struct {
				method string
				amount float64
			}{
				{"cash", totalCash},
				{"credit_card", totalCreditCard},
				{"mpos", totalMpos},
				{"bank", totalBank},
			}

			// Sort by amount in descending order
			sort.Slice(methodAmounts, func(i, j int) bool {
				return methodAmounts[i].amount > methodAmounts[j].amount
			})

			// Calculate the difference
			difference := totalAmountFromAllocations - totalFromMethods

			// Add the difference to the largest payment method
			switch methodAmounts[0].method {
			case "cash":
				totalCash += difference
			case "credit_card":
				totalCreditCard += difference
			case "mpos":
				totalMpos += difference
			case "bank":
				totalBank += difference
			}
		}

		// Process remaining payments
		for _, p := range datePayments[1:] {
			// Update payment allocations to point to target payment
			err := tx.PaymentAllocation.Update().
				Where(
					paymentallocation.PaymentID(p.ID),
				).
				SetPaymentID(targetPayment.ID).
				Exec(ctx)
			if err != nil {
				return fmt.Errorf("failed to update payment allocations: %w", err)
			}

			// Delete the merged payment
			err = tx.Payment.DeleteOne(p).Exec(ctx)
			if err != nil {
				return fmt.Errorf("failed to delete merged payment: %w", err)
			}
		}

		// Update the target payment with combined amounts
		err = tx.Payment.UpdateOne(targetPayment).
			SetTotalAmount(totalAmountFromAllocations).
			SetCash(totalCash).
			SetCreditCard(totalCreditCard).
			SetMpos(totalMpos).
			SetBank(totalBank).
			Exec(ctx)
		if err != nil {
			return fmt.Errorf("failed to update target payment: %w", err)
		}
	}

	return nil
}
