package model

import (
	"bcare/api/internal/types"
	"bcare/common/berr"
	"bcare/common/bquery"
	"bcare/common/brestful"
	"bcare/ent"
	"bcare/ent/person"
	"bcare/ent/task"
	"bcare/ent/taskassignment"
	"bcare/ent/user"
	"context"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/logx"
)

const (
	ItemSize     = 20
	FetchExpired = 3600

	// WARNING: Hardcoded token - replace with dynamic token management if possible.
	getflyBearerToken = "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1c2VyX2lkIjoiMyIsImxvY2FsZSI6InZpIiwidG9rZW5fdHlwZSI6ImEiLCJpYXQiOjE3NDY0OTU4OTQsImV4cCI6MTc0NjQ5NjE5NH0.yHxnZom-SVj6ucNuyjsHlVgbUi97De9cUTogvJLM4KM"
	// WARNING: Hardcoded cookie - replace with dynamic session/cookie management if possible.
	getflyCookie = "_ga=GA1.2.1*********.**********; _gid=GA1.2.*********.**********; _gat=1; SVID=5b83516711d9e6ed"
)

// ReceiverIDMap defines the mapping between Getfly task receiver IDs and internal user IDs.
var ReceiverIDMap = map[string]int{
	"70": 165,
	//"14": 91,
	//"72": 172,
	//"73": 171,
	//"56": 150,
	//"62": 160,
	//"64": 163,
}

type Message struct {
	Code      int       `json:"code"`    // thanh cong 201 that bai 402
	Message   string    `json:"message"` //Status bool `json:"status"`
	Error     string    `json:"error"`
	Accounts  []Account `json:"accounts"`
	AccountId int       `json:"account_id"`
}

type Account struct {
	Id          string `json:"account_id"`
	AccountCode string `json:"account_code"`
	Name        string `json:"account_name"`
	Gender      string `json:"gender"`
	Email       string `json:"email"`
	Address     string `json:"billing_address_street"`
	Phone       string `json:"phone_office"`
	Birthday    string `json:"birthday"`
	RelationId  string `json:"relation_id"`
	Source      string `json:"account_source"`
}

type AccountDetail struct {
	Account
	Phone        string `json:"phone"`
	RelationName string `json:"relation_name"`
	SourceId     string `json:"account_source_id"`
	CustomFields
}

type SpecialAccount struct {
	Account
	CustomFields `json:"custom_fields"`
}

type CustomFields struct {
	PersonCode      string `json:"ma_ho_so"`
	PersonJob       string `json:"nghe_nghiep"`
	PersonType      string `json:"trang_thai_dieu_tri"`
	PersonCondition string `json:"loai_dieu_tri"`
	Doctor          string `json:"bac_si_phu_trach"`
	Pharmacist      string `json:"tro_ly_bac_si"`
}

type Contact struct {
	Name  string `json:"first_name"`
	Email string `json:"email"`
	Phone string `json:"phone_mobile"`
}

type Opportunity struct {
	Token  string `json:"token_api"`
	Status int    `json:"opportunity_status"`
}

type AccountCampaign struct {
	Account     Account     `json:"account"`
	Contacts    []Contact   `json:"contacts"`
	Opportunity Opportunity `json:"opportunity"`
}

type Data struct {
	Accounts []AccountDetail `json:"records"`
}

type AccountData struct {
	Account      Account      `json:"account"`
	CustomFields CustomFields `json:"custom_fields"`
}

type ObjectData struct {
	Content string `json:"content"`
}

type ContactSync struct {
	ContactID   string  `json:"contact_id"`
	FirstName   string  `json:"first_name"`
	LastName    *string `json:"last_name"`
	PhoneMobile string  `json:"phone_mobile"`
	PhoneHome   string  `json:"phone_home"`
	Email       *string `json:"email"`
	Description string  `json:"description"`
	Title       string  `json:"title"`
}

type AccountSync struct {
	AccountID              string        `json:"account_id"`
	AccountCode            string        `json:"account_code"`
	AccountName            string        `json:"account_name"`
	Address                *string       `json:"address"`
	Phone                  string        `json:"phone"`
	Email                  *string       `json:"email"`
	ManagerEmail           string        `json:"manager_email"`
	ManagerUserName        string        `json:"manager_user_name"`
	Website                *string       `json:"website"`
	Logo                   string        `json:"logo"`
	Birthday               *string       `json:"birthday"`
	SicCode                *string       `json:"sic_code"`
	Description            string        `json:"description"`
	CreatedAt              string        `json:"created_at"`
	AccountTypeID          string        `json:"account_type_id"`
	AccountType            string        `json:"account_type"`
	AccountSourceID        string        `json:"account_source_id"`
	AccountSource          string        `json:"account_source"`
	RelationID             string        `json:"relation_id"`
	RelationName           string        `json:"relation_name"`
	Gender                 *string       `json:"gender"`
	Revenue                string        `json:"revenue"`
	CountryID              string        `json:"country_id"`
	ProvinceID             string        `json:"province_id"`
	ProvinceName           *string       `json:"province_name"`
	DistrictID             string        `json:"district_id"`
	Industry               *string       `json:"industry"`
	IndustryName           *string       `json:"industry_name"`
	TenDongNieng           string        `json:"ten_dong_nieng"`
	LinkPancake            string        `json:"link_pancake"`
	MaHoSo                 string        `json:"ma_ho_so"`
	NgheNghiep             string        `json:"nghe_nghiep"`
	TrangThaiDieuTri       string        `json:"trang_thai_dieu_tri"`
	LoaiDieuTri            string        `json:"loai_dieu_tri"`
	BacSiPhuTrach          string        `json:"bac_si_phu_trach"`
	TroLyBacSi             string        `json:"tro_ly_bac_si"`
	BenhLy                 string        `json:"benh_ly"`
	NhanVienSale           string        `json:"nhan_vien_sale"`
	PhuongPhapNiengRang    string        `json:"phuong_phap_nieng_rang"`
	DaiSuGioiThieu         string        `json:"dai_su_gioi_thieu"`
	NoiDungCanTuVan        string        `json:"noi_dung_can_tu_van"`
	KinhNghiemBanMuonBiet  string        `json:"kinh_nghiem_ban_muon_biet"`
	TuVanThem              string        `json:"tu_van_them"`
	TenChuTaiKhoan         string        `json:"ten_chu_tai_khoan"`
	NganHang               string        `json:"ngan_hang"`
	SoTaiKhoanNganHang     string        `json:"so_tai_khoan_ngan_hang"`
	ChiNhanhpgdNganHang    string        `json:"chi_nhanhpgd_ngan_hang"`
	NgayChuenQualifiedLead string        `json:"ngay_chuyen_qualified_lead"`
	Contacts               []ContactSync `json:"contacts"`
}

// TaskDetail defines the structure for a single task item in the response.
type TaskDetail struct {
	TaskID       string `json:"task_id"`
	TaskName     string `json:"task_name"`
	TaskProgress string `json:"task_progress"`
	Star         string `json:"star"`
	TaskStatus   string `json:"task_status"`
	TaskWeight   string `json:"task_weight"`
	StartDate    string `json:"start_date"`
	EndDate      string `json:"end_date"`
	NameStatus   string `json:"name_status"`
}

// TaskResponse defines the structure for the overall JSON response containing tasks.
type TaskResponse struct {
	Tasks []TaskDetail `json:"tasks"`
}

// TaskInfo defines the structure for the detailed task information within the detail response.
type TaskInfo struct {
	TaskID              string      `json:"task_id"`
	TaskCode            string      `json:"task_code"`
	TaskName            string      `json:"task_name"`
	TaskDescription     string      `json:"task_description"`
	TaskObject          interface{} `json:"task_object"` // Use interface{} for potentially null/varied types
	TaskParent          string      `json:"task_parent"`
	TaskMilestone       string      `json:"task_milestone"`
	TaskDuration        string      `json:"task_duration"`
	TaskDurationType    string      `json:"task_duration_type"`
	TaskHoursWorked     string      `json:"task_hours_worked"`
	TaskStartDate       string      `json:"task_start_date"`
	TaskEndDate         string      `json:"task_end_date"`
	TaskMustEndDate     interface{} `json:"task_must_end_date"`
	TaskStatus          string      `json:"task_status"`
	TaskPercentComplete string      `json:"task_percent_complete"`
	TaskCreator         string      `json:"task_creator"`
	TaskNotify          string      `json:"task_notify"`
	SubTasksComplete    interface{} `json:"sub_tasks_complete"`
	TaskColor           string      `json:"task_color"`
	TaskCompletedDate   interface{} `json:"task_completed_date"`
	TaskReceiver        string      `json:"task_receiver"`
	QRCode              interface{} `json:"qr_code"`
	Template            string      `json:"template"`
	TaskDepends         interface{} `json:"task_depends"`
	TaskProgress        string      `json:"task_progress"`
	TaskProgressDesc    interface{} `json:"task_progress_desc"`
	TaskType            string      `json:"task_type"`
	TaskFile            interface{} `json:"task_file"`
	TaskPriority        string      `json:"task_priority"`
	ProjectID           string      `json:"project_id"`
	TaskRemainDay       string      `json:"task_remain_day"`
	TaskNumberDay       string      `json:"task_number_day"`
	Reference           interface{} `json:"reference"`
	Alias               string      `json:"alias"`
	Email               string      `json:"email"`
	AllDay              string      `json:"all_day"`
	TaskGroup           string      `json:"task_group"`
	Invalid             string      `json:"invalid"`
	Star                string      `json:"star"`
	CreatedAt           string      `json:"created_at"`
	UpdatedAt           interface{} `json:"updated_at"`
	AutoSendMail        string      `json:"auto_send_mail"`
	TaskEmoji           string      `json:"task_emoji"`
	TaskWeight          string      `json:"task_weight"`
	TaskStep            string      `json:"task_step"`
	TaskReceiverDate    string      `json:"task_receiver_date"`
	TaskOrder           interface{} `json:"task_order"`
	StartTask           string      `json:"start_task"`
	EndTask             string      `json:"end_task"`
	Rgt                 string      `json:"rgt"`
	Lft                 string      `json:"lft"`
	CreatorName         string      `json:"creator_name"`
	CreatorAvatar       string      `json:"creator_avatar"`
	ReceiverName        string      `json:"receiver_name"`
	ReceiverAvatar      string      `json:"receiver_avatar"`
	ImportantStatus     interface{} `json:"important_status"`
	TaskTypeName        string      `json:"task_type_name"`
	NameStatus          string      `json:"name_status"`
	TaskEmojiIcon       string      `json:"task_emoji_icon"`
	Meeting             []string    `json:"meeting"` // Assuming meeting is an array of strings, adjust if necessary
}

// AccountInfo defines the structure for account details associated with a task.
type AccountInfo struct {
	TaskID               string `json:"task_id"`
	AccountID            string `json:"account_id"`
	AccountName          string `json:"account_name"`
	ThumbnailLogo        string `json:"thumbnail_logo"`
	PhoneOffice          string `json:"phone_office"`
	BillingAddressStreet string `json:"billing_address_street"`
	Email                string `json:"email"`
	ContactEmail         string `json:"contact_email"`
	PhoneHome            string `json:"phone_home"`
	ContactID            string `json:"contact_id"`
	ContactName          string `json:"contact_name"`
}

// TaskDetailResponse defines the structure for the detailed task API response.
type TaskDetailResponse struct {
	TaskInfo TaskInfo      `json:"task_info"`
	Accounts []AccountInfo `json:"accounts"`
}

type ResponseSync struct {
	Records []AccountSync `json:"records"`
}

const (
	apiKey  = "aIgVT3058CbtMkuWBe0NelykXb0QaT"
	Domain  = "updental.getflycrm.com"
	BaseUrl = "https://" + Domain
	Version = "v3"

	ApiBaseUrl            = BaseUrl + "/api/" + Version
	accountsUrl           = ApiBaseUrl + "/accounts"
	accountUrl            = ApiBaseUrl + "/account"
	recentAccountsUrl     = accountsUrl + "/syncs"
	accountOpportunityUrl = ApiBaseUrl + "/opportunity"
)

var SourceUp = map[int]int{
	0:   20,
	1:   20,
	5:   21,
	47:  22,
	34:  23,
	9:   24,
	10:  25,
	11:  26,
	14:  108,
	17:  109,
	18:  110,
	33:  111,
	36:  112,
	37:  113,
	38:  114,
	40:  115,
	41:  116,
	42:  117,
	43:  118,
	44:  119,
	45:  120,
	46:  121,
	48:  122,
	50:  123,
	51:  124,
	52:  125,
	53:  126,
	54:  127,
	55:  128,
	56:  129,
	57:  130,
	58:  131,
	59:  132,
	60:  133,
	61:  134,
	62:  135,
	63:  136,
	64:  137,
	65:  138,
	66:  139,
	67:  140,
	70:  141,
	71:  142,
	72:  143,
	73:  144,
	75:  145,
	76:  146,
	83:  147,
	84:  148,
	86:  149,
	93:  150,
	94:  151,
	95:  152,
	97:  256,
	13:  257,
	26:  258,
	101: 259,
	102: 260,
	103: 261,
	104: 262,
	105: 263,
	106: 264,
}

var (
	TreatmentStringUp = map[int]string{
		1:  "Trị Tổng quát",
		2:  "Niềng răng",
		3:  "Trồng Implant",
		4:  "Trị tổng quát và trồng implant",
		10: "Phục hình sứ",
	}
	TreatmentStatusIdUp = map[int]string{
		11: "Chưa ký hợp đồng",
		12: "Đang niềng",
		13: "Đã tháo niềng",
		14: "Đã gắn mắc cài và bỏ trị",
		15: "Tạm ngưng điều trị",
		16: "Từ chối điều trị",
	}

	OldToNewStageID = map[int]int{
		2:   13, // Old stage ID 2 maps to new stage ID 13
		1:   26, // Old stage ID 1 maps to new stage ID 26
		16:  27, // Old stage ID 16 maps to new stage ID 27
		148: 28,
		162: 103,
		163: 104,
		164: 105,
		165: 106,
		166: 107,
		3:   14,
		18:  29,
		19:  30,
		132: 31,
		4:   15,
		24:  32,
		25:  33,
		26:  34,
		133: 35,
		5:   16,
		95:  36,
		96:  37,
		97:  38,
		98:  42,
		99:  39,
		124: 41,
		146: 40,
	}

	RelationshipToStageMap = map[int]int{
		1:  13,
		23: 28,
		22: 37,
		2:  14,
		3:  15,
		19: 37,
		4:  37,
	}

	OldUsernameToNewID = map[string]int{
		"nguyenthithuytrang":          38,
		"<EMAIL>": 138,
		"nhudang":                     168,
		"kimthoa":                     167,
		"huynhmytruc":                 170,
		"<EMAIL>":  166,
	}
)

var ReceiverIDMapA = map[string]int{
	"70": 165,
	"14": 91,
	"72": 172,
	"73": 171,
	"56": 150,
	"62": 160,
	"64": 163,
}

func GetAllAccounts(limit, offset int) ([]AccountSync, error) {
	var data ResponseSync
	//todo lấy theo limit offset
	fUrl := fmt.Sprintf("%s?limit=%d&page=%d", recentAccountsUrl, limit, offset)
	rs, err := brestful.Get(fUrl, nil, map[string]string{"X-API-KEY": apiKey})
	if err != nil {
		logx.Errorf("Error fetching recent accounts: %s", err)
		return nil, err
	}
	err = json.Unmarshal(rs, &data)
	if err != nil {
		logx.Errorf("Error unmarshalling recent accounts response: %s", err)
		return nil, err
	}
	return data.Records, nil
}

func GetRecentAccounts(lastSync int64, limit int, offset int) ([]AccountDetail, error) {
	var data Data
	fUrl := fmt.Sprintf("%s?last_sync=%d&limit=%d&offset=%d", recentAccountsUrl, lastSync, limit, offset)
	rs, err := brestful.Get(fUrl, nil, map[string]string{"X-API-KEY": apiKey})
	if err != nil {
		logx.Errorf("Error fetching recent accounts: %s", err)
		return nil, err
	}
	err = json.Unmarshal(rs, &data)
	if err != nil {
		logx.Errorf("Error unmarshalling recent accounts response: %s", err)
		return nil, err
	}
	return data.Accounts, nil
}

func GetAccountByPhone(phone string) (AccountDetail, error) {
	phone = strings.TrimSpace(phone)
	var account AccountDetail
	var data Data
	fUrl := fmt.Sprintf("%s?phone_office=%s&limit=%d", accountsUrl, phone, 1)
	rs, err := brestful.Get(fUrl, nil, map[string]string{"X-API-KEY": apiKey})
	if err != nil {
		logx.Errorf("Error fetching account by phone: %s", err)
		return account, err
	}
	err = json.Unmarshal(rs, &data)
	if err == nil && len(data.Accounts) > 0 {
		account = data.Accounts[0]
	}
	return account, err
}

func GetAccountById(phone string, id string) (AccountDetail, error) {
	var account AccountDetail
	var data Data
	fUrl := fmt.Sprintf("%s?phone_office=%s", accountsUrl, phone)
	rs, err := brestful.Get(fUrl, nil, map[string]string{"X-API-KEY": apiKey})
	if err != nil {
		logx.Errorf("Error fetching account by ID: %s", err)
		return account, err
	}
	err = json.Unmarshal(rs, &data)
	if err == nil && len(data.Accounts) > 0 {
		for _, a := range data.Accounts {
			if a.Id == id {
				account = a
				return account, err
			}
		}
	}
	return account, err
}

func AddAccount(account SpecialAccount) (Message, error) {
	var data AccountData
	var message Message
	fUrl := accountUrl
	data.Account = account.Account
	data.CustomFields = account.CustomFields
	reqData, err := json.Marshal(&data)
	if err != nil {
		logx.Errorf("Error marshalling account data: %s", err)
		return message, err
	}
	rs, err := brestful.Post(fUrl, reqData, map[string]string{"X-API-KEY": apiKey})
	if err != nil {
		logx.Errorf("Error adding account: %s", err)
		return message, err
	}
	err = json.Unmarshal(rs, &message)
	if err != nil {
		logx.Errorf("Error unmarshalling response after adding account: %s", err)
		return message, err
	}
	return message, nil
}

func AddComment(id string, content string) error {
	fUrl := accountUrl + "/" + id + "/comment"
	data := ObjectData{
		Content: content,
	}
	reqData, err := json.Marshal(&data)
	if err != nil {
		logx.Errorf("Error marshalling comment data: %s", err)
		return err
	}
	_, err = brestful.Post(fUrl, reqData, map[string]string{"X-API-KEY": apiKey})
	if err != nil {
		logx.Errorf("Error adding comment: %s", err)
	}
	return err
}

func UpdateAccount(id string, account SpecialAccount) (Message, error) {
	var data AccountData
	var message Message
	fUrl := accountUrl + "/" + id
	account.Id = id
	data.Account = account.Account
	data.CustomFields = account.CustomFields
	reqData, err := json.Marshal(&data)
	if err != nil {
		logx.Errorf("Error marshalling account update data: %s", err)
		return message, err
	}
	rs, err := brestful.Put(fUrl, reqData, map[string]string{"X-API-KEY": apiKey})
	if err != nil {
		logx.Errorf("Error updating account: %s", err)
		return message, err
	}
	err = json.Unmarshal(rs, &message)
	if !strings.Contains(message.Message, "thành công") {
		err = berr.NewErrMsg(message.Message)
	}
	return message, err
}

func UpdateBankInfo(account AccountSync, ctx context.Context, bEnt *ent.Client) (err error) {
	if account.AccountName == "" || account.Phone == "" {
		return nil
	}
	if account.SoTaiKhoanNganHang == "" || account.NganHang == "" || account.ChiNhanhpgdNganHang == "" {
		return nil
	}

	// Kiểm tra phone tồn tại
	existingPerson, err := bEnt.Person.Query().
		Where(person.Phone(account.Phone)).First(ctx)
	if err != nil && !ent.IsNotFound(err) {
		logx.Errorf("Error checking existing person: %s", err)
		return err
	}

	if err != nil && !ent.IsNotFound(err) {
		logx.Errorf("Error checking existing person: %s", err)
		return err
	}

	if existingPerson != nil {
		existingPerson.PersonField.BankAccountName = &account.TenChuTaiKhoan
		existingPerson.PersonField.BankAccountNumber = &account.SoTaiKhoanNganHang
		existingPerson.PersonField.BankBranch = &account.ChiNhanhpgdNganHang
		existingPerson.PersonField.Bank = &account.NganHang
		existingPerson, err = bEnt.Person.UpdateOneID(existingPerson.ID).
			SetPerson(existingPerson).
			Save(ctx)
		if err != nil {
			logx.Errorf("Error update person from getfly: %s", err)
			return err
		}
	}

	return
}

// GetTaskDetail fetches detailed information for a specific task ID.
// IMPORTANT: Uses hardcoded Bearer token and Cookie. Replace with dynamic token/session management.
func GetTaskDetail(taskID string, ctx context.Context, bEnt *ent.Client) (*TaskDetailResponse, error) {
	// Increased timeout to 30 seconds to mitigate deadline exceeded errors
	client := &http.Client{Timeout: time.Second * 30}
	url := fmt.Sprintf("https://updental.getflycrm.com/v5/tasks/detail/?task_id=%s", taskID)

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		logx.Errorf("Error creating request for task detail %s: %v", taskID, err)
		return nil, err
	}

	// Set headers based on the cURL command
	// WARNING: Hardcoded token - replace with dynamic token management if possible.
	req.Header.Set("Accept", "application/json, text/plain, */*")
	req.Header.Set("Accept-Language", "vi,en-US;q=0.9,en;q=0.8,fr;q=0.7,fr-FR;q=0.6,zh-CN;q=0.5,zh;q=0.4,ja;q=0.3")
	req.Header.Set("Connection", "keep-alive")
	// WARNING: Hardcoded cookie - replace with dynamic session/cookie management if possible.
	req.Header.Set("Cookie", getflyCookie)
	req.Header.Set("Referer", "https://updental.getflycrm.com/")
	req.Header.Set("Sec-Fetch-Dest", "empty")
	req.Header.Set("Sec-Fetch-Mode", "cors")
	req.Header.Set("Sec-Fetch-Site", "same-origin")
	req.Header.Set("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
	// WARNING: Hardcoded token - replace with dynamic token management if possible.
	req.Header.Set("X-Authorization", getflyBearerToken)
	req.Header.Set("X-Getfly-Version", "5")
	req.Header.Set("sec-ch-ua", `"Google Chrome";v="135", "Not-A.Brand";v="8", "Chromium";v="135"`)
	req.Header.Set("sec-ch-ua-mobile", "?0")
	req.Header.Set("sec-ch-ua-platform", `"macOS"`)

	resp, err := client.Do(req)
	if err != nil {
		logx.Errorf("Error executing request for task detail %s: %v", taskID, err)
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		bodyBytes, _ := ioutil.ReadAll(resp.Body)
		logx.Errorf("API request failed for task detail %s with status %d: %s", taskID, resp.StatusCode, string(bodyBytes))
		return nil, fmt.Errorf("API request failed with status %d", resp.StatusCode)
	}

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		logx.Errorf("Error reading response body for task detail %s: %v", taskID, err)
		return nil, err
	}

	var taskDetailResp TaskDetailResponse
	err = json.Unmarshal(body, &taskDetailResp)
	if err != nil {
		logx.Errorf("Error unmarshalling task detail response for task %s: %v", taskID, err)
		logx.Errorf("Problematic JSON body for task %s: %s", taskID, string(body))
		return nil, err
	}

	if len(taskDetailResp.Accounts) > 0 {
		personRecord, err := bEnt.Person.Query().
			Where(person.Phone(taskDetailResp.Accounts[0].PhoneHome)).First(ctx)
		if err != nil && !ent.IsNotFound(err) {
			logx.Errorf("Error checking existing person: %s", err)
			return nil, err
		}

		err = bquery.WithTx(ctx, bEnt, func(tx *ent.Tx) error {
			taskRecord := new(ent.Task)
			taskRecord.Title = fmt.Sprintf("GetFly - %s", taskDetailResp.TaskInfo.TaskName)
			taskRecord.Note = taskDetailResp.TaskInfo.TaskDescription
			taskRecord.StartDate, _ = parseGetflyDateTimeString(taskDetailResp.TaskInfo.TaskStartDate)
			endDate, _ := parseGetflyDateTimeString(taskDetailResp.TaskInfo.TaskEndDate)
			taskRecord.EndDate = &endDate
			taskRecord.DueDate = &endDate
			taskRecord.Type = "getfly"
			taskRecord.State = task.StateNewTask
			taskRecord.Priority = 2
			if personRecord != nil {
				taskRecord.PersonID = &personRecord.ID

			}
			if ReceiverIDMapA[taskDetailResp.TaskInfo.TaskCreator] > 0 {
				taskRecord.CreatorID = ReceiverIDMapA[taskDetailResp.TaskInfo.TaskCreator]
			} else {
				taskRecord.CreatorID = ReceiverIDMapA[taskDetailResp.TaskInfo.TaskReceiver]
			}
			taskRecord.CreatedAt, _ = parseGetflyDateTimeString(taskDetailResp.TaskInfo.CreatedAt)

			taskRecord, err = tx.Task.Create().SetTask(taskRecord).SetCreatedAt(taskRecord.CreatedAt).Save(ctx)
			if err != nil {
				return types.ErrTaskAdd.Wrap(err, err.Error())
			}

			taskAssRecord := new(ent.TaskAssignment)
			taskAssRecord.TaskID = taskRecord.ID
			taskAssRecord.UserID = ReceiverIDMapA[taskDetailResp.TaskInfo.TaskReceiver]
			taskAssRecord.Role = taskassignment.RolePrimary
			taskAssRecord.DueAt = endDate
			taskAssRecord.Serial = taskRecord.CurrentSerial
			taskAssRecord.State = taskassignment.StateNewTask
			taskAssRecord, err = tx.TaskAssignment.Create().SetTaskAssignment(taskAssRecord).SetCreatedAt(taskRecord.CreatedAt).Save(ctx)
			if err != nil {
				return types.ErrTaskAdd.Wrap(err, err.Error())
			}

			taskAssRecord1 := new(ent.TaskAssignment)
			taskAssRecord1.TaskID = taskRecord.ID
			taskAssRecord1.UserID = 91
			taskAssRecord1.Role = taskassignment.RoleReviewer
			taskAssRecord1.DueAt = endDate
			taskAssRecord1.Serial = taskRecord.CurrentSerial
			taskAssRecord1.State = taskassignment.StateNewTask
			taskAssRecord1, err = tx.TaskAssignment.Create().SetTaskAssignment(taskAssRecord1).SetCreatedAt(taskRecord.CreatedAt).Save(ctx)
			if err != nil {
				return types.ErrTaskAdd.Wrap(err, err.Error())
			}

			users, _ := tx.User.Query().Where(user.DepartmentID(2)).All(ctx)

			if len(users) > 0 {
				bulkTaskAssigns := make([]*ent.TaskAssignmentCreate, len(users))
				for i, u := range users {
					taskAssignRecord := new(ent.TaskAssignment)
					taskAssignRecord.TaskID = taskRecord.ID
					taskAssignRecord.UserID = u.ID
					taskAssignRecord.Role = taskassignment.RoleContributor
					taskAssignRecord.DueAt = endDate
					taskAssignRecord.Serial = taskRecord.CurrentSerial
					taskAssignRecord.State = taskassignment.StateNewTask

					bulkTaskAssigns[i] = tx.TaskAssignment.Create().SetTaskAssignment(taskAssignRecord)
				}
				_, err = tx.TaskAssignment.CreateBulk(bulkTaskAssigns...).Save(ctx)
				if err != nil {
					return types.ErrTaskAdd.Wrap(err, "Error creating new task assignment")
				}
			}

			return nil
		})

	}

	return &taskDetailResp, nil
}

// GetTasksForReceivers fetches tasks for a list of receiver IDs from the Getfly CRM API.
// NOTE: The Bearer token used is hardcoded and based on the expired example provided.
// You will need to implement a proper way to manage and refresh the authentication token.
func GetTasksForReceivers(receiverID string) ([]TaskDetail, error) {
	//receiverMap := ReceiverIDMap
	var allTasks []TaskDetail
	client := &http.Client{Timeout: time.Second * 10} // Add a timeout
	baseURL := "https://updental.getflycrm.com/report_task/tasks?dept_id=0&report_date=OTHERS&start_date=2025-05-08%%2000%%3A00%%3A00&end_date=2030-05-31%%2023%%3A59%%3A59&task_status=0&task_receiver=%s"
	// !!! This token is expired and needs to be replaced with a valid one !!!
	// Use the defined constant for the Bearer token

	url := fmt.Sprintf(baseURL, receiverID)
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		logx.Errorf("Error creating request for receiver %s: %v", receiverID, err)
		return allTasks, err // Skip to the next receiver on error
	}

	// Set headers based on the cURL command
	req.Header.Set("Accept", "application/json, text/plain, */*")
	req.Header.Set("Accept-Language", "vi,en-US;q=0.9,en;q=0.8,fr;q=0.7,fr-FR;q=0.6,zh-CN;q=0.5,zh;q=0.4,ja;q=0.3")
	req.Header.Set("Connection", "keep-alive")
	req.Header.Set("Referer", "https://updental.getflycrm.com/")
	req.Header.Set("Sec-Fetch-Dest", "empty")
	req.Header.Set("Sec-Fetch-Mode", "cors")
	req.Header.Set("Sec-Fetch-Site", "same-origin")
	req.Header.Set("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
	// !!! This token is expired and needs to be replaced with a valid one !!!
	// Use the defined constant for the Bearer token
	req.Header.Set("X-Authorization", getflyBearerToken)
	req.Header.Set("X-Getfly-Version", "5")
	req.Header.Set("sec-ch-ua", `"Google Chrome";v="135", "Not-A.Brand";v="8", "Chromium";v="135"`)
	req.Header.Set("sec-ch-ua-mobile", "?0")
	req.Header.Set("sec-ch-ua-platform", `"macOS"`)

	resp, err := client.Do(req)
	if err != nil {
		logx.Errorf("Error executing request for receiver %s: %v", receiverID, err)
		return allTasks, err // Skip to the next receiver on error
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		bodyBytes, _ := ioutil.ReadAll(resp.Body)
		logx.Errorf("API request failed for receiver %s with status %d: %s", receiverID, resp.StatusCode, string(bodyBytes))
		return allTasks, err // Skip to the next receiver on error
	}

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		logx.Errorf("Error reading response body for receiver %s: %v", receiverID, err)
		return allTasks, err // Skip to the next receiver on error
	}

	var taskResp TaskResponse
	err = json.Unmarshal(body, &taskResp)
	if err != nil {
		logx.Errorf("Error unmarshalling response for receiver %s: %v", receiverID, err)
		// Log the problematic body for debugging
		logx.Errorf("Problematic JSON body for receiver %s: %s", receiverID, string(body))
		return allTasks, err // Skip to the next receiver on error
	}

	allTasks = taskResp.Tasks
	// Check if any tasks were collected at all. If not, and errors occurred, return an error.
	if len(allTasks) == 0 {
		// We might want a more specific error, but for now, let's signal that nothing was fetched.
		// A better approach might be to collect errors and return them.
		logx.Errorf("No tasks were successfully fetched for any receiver: %s", "")
		// Consider returning an error here if fetching *any* task is critical
		// return nil, fmt.Errorf("failed to fetch tasks for all provided receivers")
	}

	return allTasks, nil // Currently returns successfully even if some receivers failed
}

// parseGetflyDateTimeString converts a date/time string from Getfly API format to time.Time
func parseGetflyDateTimeString(dateStr string) (time.Time, error) {
	layout := "2006-01-02 15:04:05"
	if dateStr == "" {
		// Return zero time and no error if the string is empty
		return time.Time{}, nil
	}
	t, err := time.ParseInLocation(layout, dateStr, time.Local) // Assuming Getfly times are in server's local timezone
	if err != nil {
		logx.Errorf("Error parsing date string '%s': %v", dateStr, err)
		return time.Time{}, err // Return zero time and the error
	}
	return t, nil
}
