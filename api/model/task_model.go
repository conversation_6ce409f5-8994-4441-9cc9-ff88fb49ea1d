// common/task_utils.go

package model

import (
	"bcare/api/internal/model"
	"bcare/api/internal/types"
	"bcare/common/butils"
	cast2 "bcare/common/cast"
	"bcare/common/ctxdata"
	"bcare/ent"
	"bcare/ent/task"
	"bcare/ent/taskassignment"
	"bcare/ent/taskdepartment"
	"context"
	"fmt"
	"time"

	"github.com/jinzhu/copier"
	"github.com/zeromicro/go-zero/core/logx"
)

// GetDueAt calculates the due date for a task
func GetDueAt(taskRecord *ent.Task) time.Time {
	if taskRecord.Edges.Recurring != nil {
		return *taskRecord.Edges.Recurring.NextOccurrence
	}

	if taskRecord.DueDate != nil {
		return *taskRecord.DueDate
	}

	return taskRecord.StartDate
}

// IsCompletionState checks if the given state is a completion state
func IsCompletionState(state task.State) bool {
	return state == task.StateAwaitingApproval ||
		state == task.StateCompleted ||
		state == task.StateCompletedEarly ||
		state == task.StateCancelledInProgress
}

// UpdateCompletedState updates the completion state of a task
func UpdateCompletedState(originTask, taskRecord *ent.Task, tx *ent.Tx, ctx context.Context) string {
	modified := ""
	if originTask.State != taskRecord.State && IsCompletionState(taskRecord.State) {
		now := time.Now()
		taskRecord.CompletedAt = &now
		for _, u := range taskRecord.Edges.Assignments {
			if u.State != taskassignment.StateCompleted && u.Serial == taskRecord.CurrentSerial {
				_, _ = tx.TaskAssignment.UpdateOne(u).SetState(taskassignment.State(taskRecord.State)).SetCompletedAt(time.Now()).Save(ctx)
			}
		}
	}
	if !IsCompletionState(taskRecord.State) {
		taskRecord.CompletedAt = nil
		modified = "completed_at"
		for _, u := range taskRecord.Edges.Assignments {
			if u.State != taskassignment.StateOverdue && u.Serial == taskRecord.CurrentSerial {
				_, _ = tx.TaskAssignment.UpdateOne(u).SetState(taskassignment.State(taskRecord.State)).ClearCompletedAt().Save(ctx)
			}
		}

		for _, u := range taskRecord.Edges.DepartmentAssignments {
			if u.State != taskdepartment.StateOverdue && u.Serial == taskRecord.CurrentSerial {
				_, _ = tx.TaskDepartment.UpdateOne(u).SetState(taskdepartment.State(taskRecord.State)).SetCompleteBy(ctxdata.GetUidFromCtx(ctx)).ClearCompletedAt().Save(ctx)
			}
		}

	}
	return modified
}

// SetTaskAssignmentCommonFields sets common fields for a task assignment
func SetTaskAssignmentCommonFields(assignment *ent.TaskAssignment, taskRecord *ent.Task, dueAt time.Time) {
	assignment.TaskID = taskRecord.ID
	assignment.DueAt = dueAt
	assignment.Serial = taskRecord.CurrentSerial
	assignment.State = taskassignment.StateNewTask
}

// GetTaskAssignment retrieves task assignments for a given task and serial number
func GetTaskAssignment(ctx context.Context, tx *ent.Tx, taskId, serial int) (assignments []*ent.TaskAssignment, err error) {
	assignments, err = tx.TaskAssignment.Query().
		Where(
			taskassignment.TaskID(taskId),
			taskassignment.Serial(serial)).
		All(ctx)
	return
}

// GetTaskDepartment retrieves task department assignments for a given task and serial number
func GetTaskDepartment(ctx context.Context, tx *ent.Tx, taskId, serial int) (departmentAssignments []*ent.TaskDepartment, err error) {
	departmentAssignments, err = tx.TaskDepartment.Query().
		Where(
			taskdepartment.TaskID(taskId),
			taskdepartment.Serial(serial)).
		All(ctx)
	return
}

// ValidateDates validate start_date, end_date, due_date of task
func ValidateDates(task *ent.Task) error {
	// Validate start_date < end_date
	if task.EndDate != nil {
		if task.StartDate.After(*task.EndDate) {
			return fmt.Errorf("start_date must be before end_date")
		}
	}

	// Validate due_date is within range
	if task.DueDate != nil {
		if task.DueDate.Before(task.StartDate) {
			return fmt.Errorf("due_date must be after start_date")
		}
		if task.EndDate != nil && task.DueDate.After(*task.EndDate) {
			return fmt.Errorf("due_date must be before end_date")
		}
	}

	return nil
}

// FetchTaskWithRelations retrieves task data with its relations
func FetchTaskWithRelations(ctx context.Context, tx *ent.Tx, taskID int) (*ent.Task, error) {
	taskRecord, err := tx.Task.Query().
		WithAssignments().
		WithRecurring().
		Where(task.ID(taskID)).
		Only(ctx)

	if err != nil {
		if ent.IsNotFound(err) {
			return nil, types.ErrTaskNotFound.Wrap(fmt.Errorf("task id %d not found", taskID))
		}
		return nil, err
	}
	return taskRecord, nil
}

// UpdateTaskAssignments updates the task assignments for the given task
func UpdateTaskAssignments(ctx context.Context, tx *ent.Tx, taskRecord *ent.Task, newUsers []types.TaskAssignmentAddRequest, dueAt time.Time) error {
	assignments, err := GetTaskAssignment(ctx, tx, taskRecord.ID, taskRecord.CurrentSerial)
	if err != nil {
		return err
	}

	// Create a map with composite key (userId_role)
	existingAssignments := make(map[string]*ent.TaskAssignment)
	for _, assignment := range assignments {
		// Create composite key from userId and role
		key := fmt.Sprintf("%d_%s", assignment.UserID, assignment.Role)
		existingAssignments[key] = assignment
	}

	// Update existing assignments and create new ones
	for _, newUser := range newUsers {
		// Create the same composite key format for comparison
		key := fmt.Sprintf("%d_%s", newUser.UserId, newUser.Role)

		if existingAssignment, ok := existingAssignments[key]; ok {
			if !existingAssignment.DueAt.Equal(dueAt) {
				_, err := tx.TaskAssignment.UpdateOneID(existingAssignment.ID).
					SetDueAt(dueAt).
					Save(ctx)
				if err != nil {
					return fmt.Errorf("failed to update assignment due_at: %w", err)
				}
			}

			// If exact match of userId and role exists, remove from map
			delete(existingAssignments, key)
		} else {
			// Create new assignment if combination doesn't exist
			taskAssignRecord := new(ent.TaskAssignment)
			if err := copier.Copy(taskAssignRecord, newUser); err != nil {
				logx.WithContext(ctx).Errorf("cannot copy request: %v", err)
				return types.ErrTaskAdd.Wrap(err)
			}
			SetTaskAssignmentCommonFields(taskAssignRecord, taskRecord, dueAt)
			_, err := tx.TaskAssignment.Create().
				SetTaskAssignment(taskAssignRecord).
				Save(ctx)
			if err != nil {
				return err
			}
		}
	}

	// Delete assignments that are no longer needed
	for _, assignment := range existingAssignments {
		err := tx.TaskAssignment.DeleteOne(assignment).Exec(ctx)
		if err != nil {
			return err
		}
	}

	return nil
}

// UpdateTaskDepartmentAssignments updates the task department assignments for the given task
func UpdateTaskDepartmentAssignments(ctx context.Context, tx *ent.Tx, taskRecord *ent.Task, newDepartments []types.TaskDepartmentAddRequest, dueAt time.Time) error {
	departmentAssignments, err := GetTaskDepartment(ctx, tx, taskRecord.ID, taskRecord.CurrentSerial)

	if err != nil {
		return err
	}

	existingAssignments := make(map[int]*ent.TaskDepartment)
	for _, assignment := range departmentAssignments {
		existingAssignments[assignment.DepartmentID] = assignment
	}

	// Update existing department assignments and create new ones
	for _, newDept := range newDepartments {
		if existing, ok := existingAssignments[newDept.DepartmentId]; ok {
			if existing.Role != taskdepartment.Role(newDept.Role) {
				departmentAssignRecord := existing
				err := copier.CopyWithOption(&departmentAssignRecord, newDept, cast2.PayloadToEnt)
				if err != nil {
					return err
				}
				_, err = tx.TaskDepartment.UpdateOne(departmentAssignRecord).
					SetTaskDepartment(departmentAssignRecord).
					Save(ctx)
				if err != nil {
					return err
				}
			}
			delete(existingAssignments, newDept.DepartmentId)
		} else {
			departmentAssignRecord := new(ent.TaskDepartment)
			if err := copier.Copy(departmentAssignRecord, newDept); err != nil {
				logx.WithContext(ctx).Errorf("cannot copy request: %v", err)
				return types.ErrTaskAdd.Wrap(err)
			}
			model.SetTaskDepartmentCommonFields(departmentAssignRecord, taskRecord, dueAt)
			_, err := tx.TaskDepartment.Create().
				SetTaskDepartment(departmentAssignRecord).
				Save(ctx)
			if err != nil {
				return err
			}
		}
	}

	// Delete department assignments that are no longer needed
	for _, assignment := range existingAssignments {
		err := tx.TaskDepartment.DeleteOne(assignment).Exec(ctx)
		if err != nil {
			return err
		}
	}

	return nil
}

// UpdateRecurringInfo updates recurring task information if applicable
func UpdateRecurringInfo(ctx context.Context, tx *ent.Tx, taskRecord *ent.Task, dueAt time.Time) error {
	if taskRecord.Edges.Recurring == nil || taskRecord.CurrentSerial != 1 {
		return nil
	}

	recurringTask := taskRecord.Edges.Recurring
	nextOccurrence := butils.CalculateNextRunTime(recurringTask.CronExpression, dueAt, taskRecord.EndDate)
	_, err := tx.TaskRecurring.UpdateOne(recurringTask).
		SetNextOccurrence(nextOccurrence).
		SetLastOccurrence(dueAt).
		Save(ctx)
	return err
}

func UpdateAllTaskAssignmentsDueAt(ctx context.Context, tx *ent.Tx, taskId, serial int, newDueAt time.Time) error {
	// Lấy tất cả assignments của task
	assignments, err := GetTaskAssignment(ctx, tx, taskId, serial)
	if err != nil {
		return err
	}

	// Cập nhật due_at cho tất cả assignments
	for _, assignment := range assignments {
		if !assignment.DueAt.Equal(newDueAt) {
			_, err := tx.TaskAssignment.UpdateOneID(assignment.ID).
				SetDueAt(newDueAt).
				Save(ctx)
			if err != nil {
				return fmt.Errorf("failed to update assignment due_at: %w", err)
			}
		}
	}

	return nil
}
