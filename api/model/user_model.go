package model

import (
	"bcare/ent"
	"encoding/json"
	"fmt"
	"github.com/casbin/casbin/v2"
)

type UserData struct {
	Kind string `json:"kind"`
	Data struct {
		Line string `json:"line"`
	} `json:"data"`
}

type LineWrapper struct {
	Line struct {
		Line         string `json:"line"`
		PasswordLine string `json:"password_line"`
	} `json:"line"`
}

type LineCall struct {
	Line     string `json:"lineString"`
	Password string `json:"passwordString"`
}

func GetUserRolesAndPermissions(e *casbin.Enforcer, userID string) (map[string][][]string, error) {
	// L<PERSON>y tất cả roles của user
	roles, err := e.GetRolesForUser(userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get roles: %v", err)
	}

	// Map để lưu permissions của từng role
	rolePermissions := make(map[string][][]string)

	// Lấy permissions cho user trực tiếp
	userPerms, err := e.GetPermissionsForUser(userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user permissions: %v", err)
	}
	rolePermissions["direct_permissions"] = userPerms

	// Lấy permissions cho từng role
	for _, role := range roles {
		perms, err := e.GetPermissionsForUser(role)
		if err != nil {
			return nil, fmt.Errorf("failed to get permissions for role %s: %v", role, err)
		}
		rolePermissions[role] = perms
	}

	return rolePermissions, nil
}

func GetUserRolesAndAllPermissions(e *casbin.Enforcer, userID string) (map[string][][]string, error) {
	roles, err := e.GetRolesForUser(userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get roles: %v", err)
	}

	rolePermissions := make(map[string][][]string)

	// Lấy tất cả permissions (bao gồm cả inherited) cho user
	userPerms, err := e.GetImplicitPermissionsForUser(userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user permissions: %v", err)
	}
	rolePermissions["all_permissions"] = userPerms

	// Lấy permissions riêng cho từng role
	for _, role := range roles {
		perms, err := e.GetPermissionsForUser(role)
		if err != nil {
			return nil, fmt.Errorf("failed to get permissions for role %s: %v", role, err)
		}
		rolePermissions[role] = perms
	}

	return rolePermissions, nil
}

func ParseLineCall(userDataList []*ent.UserData) (LineCall, error) {
	var lineCall LineCall
	if len(userDataList) == 0 {
		return lineCall, fmt.Errorf("user data not found")
	}

	// Find user data with kind "call_center"
	var targetUserData *ent.UserData
	for _, userData := range userDataList {
		if userData == nil || userData.Data == nil {
			continue
		}

		if userData.Kind == "call_center" {
			targetUserData = userData
			break
		}
	}

	if targetUserData == nil {
		return lineCall, fmt.Errorf("call center data not found")
	}

	// Get data.line
	lineStr, ok := targetUserData.Data["line"].(string)
	if !ok {
		return lineCall, fmt.Errorf("invalid line data")
	}

	// Parse line string
	var lineWrapper LineWrapper
	if err := json.Unmarshal([]byte(lineStr), &lineWrapper); err != nil {
		return lineCall, fmt.Errorf("failed to parse line data: %w", err)
	}

	return LineCall{
		Line:     lineWrapper.Line.Line,
		Password: lineWrapper.Line.PasswordLine,
	}, nil
}
