package model

import (
	"bcare/api/internal/types"
	"bcare/common/bconst"
	"bcare/common/ctxdata"
	"bcare/ent"
	"bcare/ent/appointment"
	"bcare/ent/track"
	"context"
	"entgo.io/ent/dialect/sql"
	"github.com/zeromicro/go-zero/core/logx"
	"time"
)

func CreateTrack(ctx context.Context, tx *ent.Tx, originDeal, newDeal *ent.Deal) error {
	// Lấy thông tin stage mới
	if newDeal.StageID == nil {
		return nil
	}
	newStage, err := tx.Stage.Get(ctx, *newDeal.StageID)
	if err != nil {
		return err
	}

	// Lấy thông tin stage cũ
	var oldStage *ent.Stage
	if originDeal.StageID != nil {
		oldStage, err = tx.Stage.Get(ctx, *originDeal.StageID)
		if err != nil {
			return err
		}
	}

	// Kiểm tra xem deal có chuyển từ pipeline này sang pipeline khác không
	if oldStage == nil || oldStage.PipelineID != newStage.PipelineID {
		logx.WithContext(ctx).Infof("Insert track for Deal: %d", newDeal.ID)

		// Tạo track mới với đầy đủ thông tin
		newTrack, err := tx.Track.Create().
			SetBegin(time.Now()).
			SetPersonID(newDeal.PersonID).
			SetDealID(newDeal.ID).
			SetUserID(ctxdata.GetUidFromCtx(ctx)).
			SetPipelineID(newStage.PipelineID).
			Save(ctx)

		if err != nil {
			return err
		}

		now := time.Now()
		startOfDay := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
		endOfDay := startOfDay.Add(24 * time.Hour)

		_, err = tx.Appointment.Update().
			Where(
				appointment.PersonID(newDeal.PersonID),
				appointment.StatusGTE(bconst.StatusNormal),
				appointment.StartTimeGTE(startOfDay),
				appointment.StartTimeLT(endOfDay),
			).
			SetArrivedAt(now).
			SetTrackID(newTrack.ID).
			SetStatus(types.AppointmentStatusOnTime).
			Save(ctx)

		if err != nil {
			logx.WithContext(ctx).Errorf("Deal stage change handle error - Deal: %d - Error: %v", newDeal.ID, err)
			return err
		}
		logx.WithContext(ctx).Infof("Deal stage change handle success - Deal: %d", newDeal.ID)
	} else {
		logx.WithContext(ctx).Infof("Deal stage change does not trigger track creation - Old pipeline: %v, New pipeline: %v", oldStage.PipelineID, newStage.PipelineID)
	}
	return nil
}

func EndTrack(ctx context.Context, tx *ent.Tx, originDeal, newDeal *ent.Deal) error {
	// Lấy thông tin stage mới
	var newStage *ent.Stage
	var err error
	if newDeal.StageID != nil {
		newStage, err = tx.Stage.Get(ctx, *newDeal.StageID)
		if err != nil {
			return err
		}
	}

	// Lấy thông tin stage cũ
	var oldStage *ent.Stage
	if originDeal.StageID != nil {
		oldStage, err = tx.Stage.Get(ctx, *originDeal.StageID)
		if err != nil {
			return err
		}
	}

	// Kiểm tra xem deal có chuyển từ pipeline này sang pipeline khác không
	if (newStage == nil && oldStage != nil) || (oldStage != nil && newStage != nil && oldStage.PipelineID != newStage.PipelineID) {
		logx.WithContext(ctx).Infof("Ending track for Deal: %d", newDeal.ID)

		trackRecord, err := tx.Track.Query().
			Where(
				track.DealID(newDeal.ID),
				track.EndIsNil(),
				track.PipelineID(oldStage.PipelineID),
			).
			Only(ctx)
		if err != nil {
			logx.WithContext(ctx).Errorf("[endTrack] Track end handle error - Deal: %d - Error: %v", newDeal.ID, err)
			return err
		}

		_, err = tx.Track.UpdateOne(trackRecord).
			SetEnd(time.Now()).
			Save(ctx)

		if err != nil {
			logx.WithContext(ctx).Errorf("[endTrack] Track end update error - Deal: %d - Error: %v", newDeal.ID, err)
			return err
		}

		logx.WithContext(ctx).Infof("[endTrack] Track end handle success - Deal: %d", newDeal.ID)
	} else {
		var oldPipelineID, newPipelineID interface{}
		if oldStage != nil {
			oldPipelineID = oldStage.PipelineID
		} else {
			oldPipelineID = "nil"
		}
		if newStage != nil {
			newPipelineID = newStage.PipelineID
		} else {
			newPipelineID = "nil"
		}
		logx.WithContext(ctx).Infof("[endTrack] Track end not triggered - Old Pipeline: %v, New Pipeline: %v",
			oldPipelineID,
			newPipelineID)
	}
	return nil
}

func GetActiveTrack(ctx context.Context, tx *ent.Tx, personId int) (*ent.Track, error) {
	activeTrack, err := tx.Track.Query().Where(track.PersonID(personId), track.EndIsNil()).Order(track.ByCreatedAt(sql.OrderDesc())).First(ctx)
	if err != nil && !ent.IsNotFound(err) {
		logx.WithContext(ctx).Errorf("track not found: %v", err)
		return nil, err
	}

	return activeTrack, nil
}
