package model

import (
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"context"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/zeromicro/go-zero/core/logx"

	"github.com/xuri/excelize/v2"
)

const defaultSheetName = "Sheet1"

// GenerateAndSaveExcelFile creates an Excel file in memory and saves it to the specified path.
func GenerateAndSaveExcelFile(
	ctx context.Context,
	jobID string,
	headers []types.HeaderConfig,
	data []map[string]interface{},
	storagePath string, // Storage directory path from config
) (filePath string, actualCount int64, err error) {

	logger := logx.WithContext(ctx).WithFields(logx.Field("job_id", jobID))
	logger.Info("Starting immediate Excel file generation and saving...")

	if storagePath == "" {
		return "", 0, fmt.Errorf("storage path configuration is empty")
	}
	if err := os.MkdirAll(storagePath, 0755); err != nil {
		logger.Errorf("Failed to create storage directory '%s': %v", storagePath, err)
		return "", 0, fmt.Errorf("failed to prepare storage directory: %w", err)
	}

	fileName := fmt.Sprintf("export_%s_%d.xlsx", jobID, time.Now().Unix())
	filePath = filepath.Join(storagePath, fileName)
	logger.Infof("Generating Excel file at: %s", filePath)

	f := excelize.NewFile()
	defer func() {
		if closeErr := f.Close(); closeErr != nil {
			logx.WithContext(context.Background()).Errorf("Error closing excelize file object: %v", closeErr)
		}
	}()

	streamWriter, err := f.NewStreamWriter(defaultSheetName)
	if err != nil {
		logger.Errorf("Failed to create stream writer: %v", err)
		return "", 0, fmt.Errorf("failed to create excel stream writer: %w", err)
	}

	// --- Write Headers ---
	headerStyle, _ := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{Bold: true}, Fill: excelize.Fill{Type: "pattern", Color: []string{"#E0E0E0"}, Pattern: 1}, Alignment: &excelize.Alignment{Horizontal: "center"},
	})
	headerRow := make([]interface{}, len(headers))
	for i, header := range headers {
		headerRow[i] = excelize.Cell{Value: header.DisplayName, StyleID: headerStyle}
		colName, _ := excelize.ColumnNumberToName(i + 1)
		width := float64(header.Width)
		if width <= 0 {
			width = 15
		}
		_ = f.SetColWidth(defaultSheetName, colName, colName, width)
	}
	if err := streamWriter.SetRow("A1", headerRow); err != nil {
		logger.Errorf("Failed to write header row to stream: %v", err)
		return "", 0, fmt.Errorf("failed to write excel header: %w", err)
	}

	// --- Write Data Rows ---
	logger.Info("Writing data rows to stream...")
	actualCount = 0
	rowIndex := 2
	styleCache := make(map[string]int)

	for _, rowMap := range data {
		currentRow := make([]interface{}, len(headers))
		for i, header := range headers {
			rawValue, _ := rowMap[header.Field]
			formattedValue, styleID := formatCellValue(f, header, rawValue, styleCache)
			currentRow[i] = excelize.Cell{Value: formattedValue, StyleID: styleID}
		}

		startCell, _ := excelize.CoordinatesToCellName(1, rowIndex)
		if err := streamWriter.SetRow(startCell, currentRow); err != nil {
			logger.Infof("Failed to write data row %d to stream: %v. Skipping row.", rowIndex, err)
			continue
		}
		actualCount++
		rowIndex++
	}
	logger.Infof("Finished writing %d data rows.", actualCount)

	// --- Finalize and Save File ---
	logger.Info("Flushing stream writer...")
	if err := streamWriter.Flush(); err != nil {
		logger.Errorf("Failed to flush stream writer: %v", err)
		_ = os.Remove(filePath) // Attempt to remove the corrupted file
		return "", actualCount, fmt.Errorf("failed to finalize excel stream: %w", err)
	}

	logger.Infof("Saving final Excel file to %s...", filePath)
	if err := f.SaveAs(filePath); err != nil {
		logger.Errorf("Failed to save excel file '%s': %v", filePath, err)
		return "", actualCount, fmt.Errorf("failed to save excel file: %w", err)
	}

	logger.Infof("Excel file generated and saved successfully: %s (%d rows)", filePath, actualCount)
	return filePath, actualCount, nil
}

// formatCellValue formats the value and returns the appropriate style ID.
func formatCellValue(f *excelize.File, header types.HeaderConfig, value interface{}, styleCache map[string]int) (interface{}, int) {
	if value == nil {
		return nil, 0 // Default style
	}

	styleKey := header.DataType + ":" + header.Formatter // Key for style caching
	var finalValue interface{} = value
	var finalStyle excelize.Style
	var styleID int = 0

	styleHandled := false

	switch header.DataType {
	case "int":
		finalStyle.NumFmt = 0 // Default integer format
		if vFloat, ok := value.(float64); ok {
			finalValue = int64(vFloat)
		} else if vStr, ok := value.(string); ok {
			if i, err := strconv.ParseInt(vStr, 10, 64); err == nil {
				finalValue = i
			} else {
				finalValue = vStr // Keep as string if parsing fails
			}
		}
		styleHandled = true

	case "float":
		customNumFmt := ""
		if header.Formatter != "" {
			parts := strings.SplitN(header.Formatter, ":", 2)
			if len(parts) == 2 && parts[0] == "float" {
				customNumFmt = parts[1]
				finalStyle.CustomNumFmt = &customNumFmt
				styleKey += customNumFmt
			}
		}
		if customNumFmt == "" {
			finalStyle.NumFmt = 2 // Default float format
		}
		if vInt, ok := value.(int64); ok {
			finalValue = float64(vInt)
		} else if vInt32, ok := value.(int32); ok {
			finalValue = float64(vInt32)
		} else if vStr, ok := value.(string); ok {
			if fVal, err := strconv.ParseFloat(vStr, 64); err == nil {
				finalValue = fVal
			} else {
				finalValue = vStr // Keep as string if parsing fails
			}
		}
		styleHandled = true

	case "bool":
		// Excel handles boolean display automatically
		styleHandled = true

	case "date", "datetime", "time", "timestamp":
		var formattedDateString string

		formatTimeToString := func(t time.Time, formatStr string) string {
			if formatStr == "" {
				switch header.DataType {
				case "date":
					return t.Format("02/01/2006")
				case "time":
					return t.Format("15:04:05")
				default: // datetime, timestamp
					return t.Format("02/01/2006 15:04:05")
				}
			}

			parts := strings.SplitN(formatStr, ":", 2)
			if len(parts) == 2 && (parts[0] == "date" || parts[0] == "datetime" || parts[0] == "time") {
				return t.Format(parts[1])
			}

			// Fallback to default datetime format if formatter is invalid
			return t.Format("02/01/2006 15:04:05")
		}

		var t time.Time
		var parseOk bool

		switch v := value.(type) {
		case time.Time:
			t = v
			parseOk = true
		case string:
			// Try various common layouts
			layouts := []string{time.RFC3339, "2006-01-02 15:04:05", "2006-01-02", "02/01/2006", "15:04:05"}
			for _, layout := range layouts {
				if parsedTime, err := time.Parse(layout, v); err == nil {
					t = parsedTime
					parseOk = true
					break
				}
			}
			if !parseOk {
				formattedDateString = v // Keep original string if parsing fails
			}
		case float64:
			// Handle Excel serial date number
			excelEpoch := time.Date(1899, 12, 30, 0, 0, 0, 0, time.UTC)
			days := int(v)
			fractionalDays := v - float64(days)
			hours := int(fractionalDays * 24)
			minutes := int(fractionalDays*24*60) % 60
			seconds := int(fractionalDays*24*60*60) % 60

			t = excelEpoch.AddDate(0, 0, days).Add(
				time.Duration(hours)*time.Hour +
					time.Duration(minutes)*time.Minute +
					time.Duration(seconds)*time.Second)
			parseOk = true
		}

		if parseOk {
			formattedDateString = formatTimeToString(t, header.Formatter)
		}

		finalValue = formattedDateString
		styleHandled = true

	case "string":
		fallthrough
	default:
		finalValue = fmt.Sprintf("%v", value)
		styleHandled = true
	}

	// Create style only if needed (custom number format)
	if styleHandled && (finalStyle.NumFmt != 0 || finalStyle.CustomNumFmt != nil) {
		cachedStyleID, foundInCache := styleCache[styleKey]
		if foundInCache {
			styleID = cachedStyleID
		} else {
			var err error
			styleID, err = f.NewStyle(&finalStyle)
			if err != nil {
				logx.WithContext(context.Background()).Infof("Failed to create style for key '%s': %v. Using default style.", styleKey, err)
				styleID = 0 // Fallback to default style
			} else {
				styleCache[styleKey] = styleID
			}
		}
	}

	return finalValue, styleID
}

// ProgressCallback defines the callback function for reporting progress.
type ProgressCallback func(processedCount int64, totalCount int64) // totalCount is the initial estimated count

// GenerateExcelStreaming writes data from a channel to an Excel file on disk using streaming.
// It returns the path of the created file, the actual number of rows written, and any error.
func GenerateExcelStreaming(
	ctx context.Context,
	jobID int,
	headers []types.HeaderConfig,
	dataChan <-chan map[string]interface{},
	estimatedTotal int64, // Initial estimate for percentage calculation
	progressCb ProgressCallback,
	storagePath string, // Directory to save the file
) (filePath string, actualCount int64, err error) {

	logger := logx.WithContext(ctx).WithFields(logx.Field("job_id", jobID))
	logger.Info("Starting streaming Excel file generation...")

	if storagePath == "" {
		return "", 0, fmt.Errorf("storage path configuration is empty")
	}
	if err := os.MkdirAll(storagePath, 0755); err != nil {
		logger.Errorf("Failed to create storage directory '%s': %v", storagePath, err)
		return "", 0, fmt.Errorf("failed to prepare storage directory: %w", err)
	}
	fileName := fmt.Sprintf("export_%d_%s.xlsx", jobID, uuid.NewString())
	filePath = filepath.Join(storagePath, fileName)
	logger.Infof("Generating Excel file at: %s", filePath)

	f := excelize.NewFile()
	defer func() {
		if closeErr := f.Close(); closeErr != nil {
			logx.WithContext(context.Background()).Errorf("Error closing excelize file object: %v", closeErr)
		}
	}()

	streamWriter, err := f.NewStreamWriter(defaultSheetName)
	if err != nil {
		logger.Errorf("Failed to create stream writer: %v", err)
		return "", 0, fmt.Errorf("failed to create excel stream writer: %w", err)
	}

	// --- Write Headers ---
	headerStyle, _ := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{Bold: true}, Fill: excelize.Fill{Type: "pattern", Color: []string{"#E0E0E0"}, Pattern: 1}, Alignment: &excelize.Alignment{Horizontal: "center"},
	})
	headerRow := make([]interface{}, len(headers))
	for i, header := range headers {
		headerRow[i] = excelize.Cell{Value: header.DisplayName, StyleID: headerStyle}
		colName, _ := excelize.ColumnNumberToName(i + 1)
		width := float64(header.Width)
		if width <= 0 {
			width = 15
		}
		_ = f.SetColWidth(defaultSheetName, colName, colName, width)
	}
	if err := streamWriter.SetRow("A1", headerRow); err != nil {
		logger.Errorf("Failed to write header row to stream: %v", err)
		return "", 0, fmt.Errorf("failed to write excel header: %w", err)
	}

	// --- Process Data Rows from Channel ---
	logger.Info("Starting to process data rows from channel...")
	actualCount = 0
	rowIndex := 2
	styleCache := make(map[string]int)
	lastProgressUpdate := time.Now()
	progressUpdateInterval := 5 * time.Second

	for rowMap := range dataChan {
		select {
		case <-ctx.Done():
			logger.Infof("Context cancelled during Excel generation after processing %d rows.", actualCount)
			_ = streamWriter.Flush() // Attempt to flush buffered data
			_ = f.Close()
			_ = os.Remove(filePath) // Attempt to remove the incomplete file
			return "", actualCount, ctx.Err()
		default:
			// Continue processing
		}

		currentRow := make([]interface{}, len(headers))
		for i, header := range headers {
			rawValue, _ := rowMap[header.Field]
			formattedValue, styleID := formatCellValue(f, header, rawValue, styleCache)
			currentRow[i] = excelize.Cell{Value: formattedValue, StyleID: styleID}
		}

		startCell, _ := excelize.CoordinatesToCellName(1, rowIndex)
		if err := streamWriter.SetRow(startCell, currentRow); err != nil {
			logger.Infof("Failed to write data row %d to stream: %v. Skipping row.", rowIndex, err)
			continue
		}

		actualCount++
		rowIndex++

		// --- Progress Update ---
		if time.Since(lastProgressUpdate) > progressUpdateInterval && progressCb != nil {
			totalForPercent := estimatedTotal
			if totalForPercent <= 0 {
				totalForPercent = actualCount // Use actual count if no estimate provided
			}
			progressCb(actualCount, totalForPercent)
			lastProgressUpdate = time.Now()
			logger.Debugf("Progress update: Processed %d rows...", actualCount)
		}
	}
	logger.Infof("Finished processing data channel. Total rows processed: %d", actualCount)

	// Final progress update
	if progressCb != nil {
		totalForPercent := estimatedTotal
		if totalForPercent <= 0 {
			totalForPercent = actualCount
		}
		progressCb(actualCount, totalForPercent)
	}

	// --- Finalize and Save File ---
	logger.Info("Flushing stream writer...")
	if err := streamWriter.Flush(); err != nil {
		logger.Errorf("Failed to flush stream writer: %v", err)
		_ = os.Remove(filePath) // Attempt to remove the corrupted file
		return "", actualCount, fmt.Errorf("failed to finalize excel stream: %w", err)
	}

	logger.Infof("Saving final Excel file to %s...", filePath)
	if err := f.SaveAs(filePath); err != nil {
		logger.Errorf("Failed to save excel file '%s': %v", filePath, err)
		return "", actualCount, fmt.Errorf("failed to save excel file: %w", err)
	}

	logger.Infof("Excel file generated successfully: %s (%d rows)", filePath, actualCount)
	return filePath, actualCount, nil
}

// FetchDataStreaming executes a SQL query and streams the results through a channel.
func FetchDataStreaming(ctx context.Context, svcCtx *svc.ServiceContext, sqlQuery string, params []types.Parameter) (<-chan map[string]interface{}, error) {
	logger := logx.WithContext(ctx)
	logger.Infof("Starting streaming data fetch for SQL: %s", sqlQuery)

	// --- Prepare Parameters ---
	args := make([]interface{}, 0, len(params))
	for _, param := range params {
		convertedValue, err := ConvertParameter(param)
		if err != nil {
			logger.Errorf("Parameter conversion error during streaming fetch: %v", err)
			return nil, fmt.Errorf("parameter conversion error: %w", err)
		}
		args = append(args, convertedValue)
	}
	logger.Infof("Prepared %d parameters for streaming query", len(args))

	// --- Execute Query ---
	// Assume dbExecutor handles the underlying database connection (e.g., sql.DB or similar)
	dbExecutor := svcCtx.Bob // Replace with your actual DB executor

	rows, err := dbExecutor.QueryContext(ctx, sqlQuery, args...)
	if err != nil {
		logger.Errorf("Failed to execute streaming query: %v", err)
		return nil, fmt.Errorf("database query execution failed: %w", err)
	}
	logger.Info("Streaming query executed successfully, processing rows...")

	dataChan := make(chan map[string]interface{}, 100) // Buffered channel

	columns, err := rows.Columns()
	if err != nil {
		_ = rows.Close()
		logger.Errorf("Failed to get columns from streaming query result: %v", err)
		return nil, fmt.Errorf("failed to read result columns: %w", err)
	}
	logger.Debugf("Streaming query columns: %v", columns)

	// Goroutine to read rows and send to channel
	go func() {
		defer close(dataChan)
		defer func() {
			if err := rows.Close(); err != nil {
				logger.Errorf("Error closing SQL rows in streaming goroutine: %v", err)
			}
			logger.Info("Finished processing and closed SQL rows.")
		}()

		rowCount := 0
		for rows.Next() {
			values := make([]interface{}, len(columns))
			pointers := make([]interface{}, len(columns))
			for i := range values {
				pointers[i] = &values[i]
			}

			if err := rows.Scan(pointers...); err != nil {
				logger.Errorf("Error scanning row %d in streaming fetch: %v", rowCount+1, err)
				// Stop processing on scan error
				return
			}

			rowMap := make(map[string]interface{})
			for i, col := range columns {
				val := pointers[i].(*interface{})
				rowMap[col] = ProcessScannedValue(*val) // Process scanned value
			}

			select {
			case dataChan <- rowMap:
				rowCount++
				if rowCount%1000 == 0 {
					logger.Debugf("Streamed %d rows from database...", rowCount)
				}
			case <-ctx.Done():
				logger.Infof("Context cancelled during data fetch streaming after %d rows. Stopping.", rowCount)
				return // Exit goroutine
			}
		}

		if err := rows.Err(); err != nil {
			logger.Errorf("Error after iterating through rows in streaming fetch: %v", err)
			return
		}

		logger.Infof("Successfully streamed %d rows from database.", rowCount)
	}()

	return dataChan, nil
}

// ConvertParameter converts a types.Parameter into its corresponding Go type.
func ConvertParameter(param types.Parameter) (interface{}, error) {
	if param.Type == "null" {
		return nil, nil
	}
	// Treat empty string as null for non-string types
	if param.Value == "" && param.Type != "string" {
		return nil, nil
	}

	switch param.Type {
	case "string":
		return param.Value, nil
	case "int":
		val, err := strconv.ParseInt(param.Value, 10, 64)
		if err != nil && param.Value != "" { // Allow empty string to become nil above
			return nil, fmt.Errorf("invalid int '%s': %w", param.Value, err)
		}
		return val, nil // Return parsed int or the error
	case "float":
		val, err := strconv.ParseFloat(param.Value, 64)
		if err != nil && param.Value != "" {
			return nil, fmt.Errorf("invalid float '%s': %w", param.Value, err)
		}
		return val, nil
	case "bool":
		val, err := strconv.ParseBool(param.Value)
		if err != nil && param.Value != "" {
			return nil, fmt.Errorf("invalid bool '%s': %w", param.Value, err)
		}
		return val, nil
	case "date":
		val, err := time.Parse("2006-01-02", param.Value)
		if err != nil && param.Value != "" {
			return nil, fmt.Errorf("invalid date format '%s': %w", param.Value, err)
		}
		return val, nil
	case "time":
		val, err := time.Parse("15:04:05", param.Value)
		if err != nil && param.Value != "" {
			return nil, fmt.Errorf("invalid time format '%s': %w", param.Value, err)
		}
		return val, nil
	case "datetime", "timestamp":
		// Try RFC3339 first, then a common format
		t, err := time.Parse(time.RFC3339, param.Value)
		if err == nil {
			return t, nil
		}
		t, err = time.Parse("2006-01-02 15:04:05", param.Value)
		if err != nil && param.Value != "" {
			return nil, fmt.Errorf("invalid datetime format '%s': %w", param.Value, err)
		}
		return t, nil
	case "json":
		var result interface{}
		// Allow empty string for JSON to be treated as null
		if param.Value == "" {
			return nil, nil
		}
		err := json.Unmarshal([]byte(param.Value), &result)
		if err != nil {
			return nil, fmt.Errorf("invalid json '%s': %w", param.Value, err)
		}
		return result, nil
	case "uuid":
		// Validate UUID format if necessary, otherwise pass as string
		// _, err := uuid.Parse(param.Value)
		// if err != nil { ... }
		return param.Value, nil // Passing UUID as string
	case "bytea":
		// Assuming the value is raw bytes represented as string
		// Decode if necessary (e.g., base64)
		return []byte(param.Value), nil
	case "array":
		var arr []interface{}
		if param.Value == "" || param.Value == "[]" { // Handle empty array string
			return []interface{}{}, nil // Return empty slice, not nil
		}
		err := json.Unmarshal([]byte(param.Value), &arr)
		if err != nil {
			return nil, fmt.Errorf("invalid array json '%s': %w", param.Value, err)
		}
		// If element type is specified, try converting each element
		if param.ArrayElementType != "" && len(arr) > 0 {
			convertedArr := make([]interface{}, len(arr))
			for i, elem := range arr {
				elemStr := fmt.Sprintf("%v", elem)
				elemParam := types.Parameter{
					Type:  param.ArrayElementType,
					Value: elemStr,
				}
				convertedElem, err := ConvertParameter(elemParam)
				if err != nil {
					return nil, fmt.Errorf("error converting array element %d ('%v'): %w", i, elem, err)
				}
				convertedArr[i] = convertedElem
			}
			return convertedArr, nil
		}
		return arr, nil // Return as []interface{} if no element type specified

	default:
		return nil, fmt.Errorf("unsupported parameter type: %s", param.Type)
	}
}

// ProcessScannedValue handles type conversions for values read from the database.
func ProcessScannedValue(value interface{}) interface{} {
	if value == nil {
		return nil
	}
	switch v := value.(type) {
	case []byte:
		// Try parsing as JSON, fallback to string
		var js interface{}
		if json.Unmarshal(v, &js) == nil {
			return js
		}
		return string(v)
	case time.Time:
		// Keep as time.Time for potential further formatting (e.g., in Excel)
		return v
	// Add other necessary type assertions/conversions here if needed
	default:
		return v
	}
}
