package model

import (
	"bcare/api/internal/types"
	"bcare/common/bconst"
	"bcare/common/butils"
	"bcare/ent"
	"bcare/ent/appointment"
	"bcare/ent/messagehistory"
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"github.com/zeromicro/go-zero/core/logx"
	"text/template"
	"time"
)

// NewMessageHistory creates a new message history record with common fields
func NewMessageHistory(personID int, phone string, userID int) *ent.MessageHistory {
	return &ent.MessageHistory{
		PersonID:      personID,
		Phone:         phone,
		UserID:        userID,
		MessageStatus: messagehistory.MessageStatusFailed,
		Status:        bconst.StatusNormal,
	}
}

// NewZnsMessageHistory creates a new ZNS message history record
func NewZnsMessageHistory(personID int, phone string, userID int, znsData, content, messageID string, errorCode int) *ent.MessageHistory {
	msg := NewMessageHistory(personID, phone, userID)
	msg.ZnsData = znsData
	msg.Content = content
	msg.Type = messagehistory.TypeZns
	msg.ErrorCode = errorCode
	msg.MessageID = messageID

	if errorCode == 0 {
		msg.MessageStatus = messagehistory.MessageStatusSent
	}

	return msg
}

// NewSmsMessageHistory creates a new SMS message history record
func NewSmsMessageHistory(personID int, phone string, userID int, content string, errorCode int) *ent.MessageHistory {
	msg := NewMessageHistory(personID, phone, userID)
	msg.Content = content
	msg.Type = messagehistory.TypeSms
	msg.ErrorCode = errorCode

	if errorCode == 0 {
		msg.MessageStatus = messagehistory.MessageStatusSent
	}

	return msg
}

// SaveMessageHistory saves the message history to database
func SaveMessageHistory(ctx context.Context, client *ent.Client, msg *ent.MessageHistory) (*ent.MessageHistory, error) {
	return client.MessageHistory.Create().
		SetMessageHistory(msg).
		Save(ctx)
}

// SendPaymentMessage sends payment notification via ZNS or SMS
func SendPaymentMessage(ctx context.Context, client *ent.Client, paymentTemplateSetting *types.PaymentMessageTemplates, paymentRecord *ent.Payment, personId int, debug bool, phoneTest string) (*types.SendMessageRequest, bool, error) {
	// Get person record
	personRecord, err := client.Person.Get(ctx, personId)
	if err != nil {
		return nil, false, fmt.Errorf("error getting person record: %w", err)
	}

	appointmentRecord, _ := client.Appointment.Query().WithPerson().WithDoctor().Where(appointment.PersonID(personId), appointment.StartTimeGT(time.Now())).Order(appointment.ByStartTime()).First(ctx)

	hasZns := *personRecord.PersonField.HasZalo == "yes"
	hasAppointment := appointmentRecord != nil

	templateMessage := getNotificationTemplate(hasZns, hasAppointment, *paymentTemplateSetting)

	// Prepare data map for template
	data := map[string]string{
		"customer_ID":           *personRecord.PersonField.Code,
		"customer_name":         personRecord.FullName,
		"price":                 butils.FormatMoney(int(paymentRecord.TotalAmount), " VND"),
		"price_zns":             fmt.Sprintf("%d", int(paymentRecord.TotalAmount)),
		"customer_payment_time": paymentRecord.CreatedAt.Format("02/01/2006"),
	}

	if hasAppointment {
		data["schedule_time"] = appointmentRecord.StartTime.Format("15:04 02/01/2006")
	}

	// Parse template to JSON
	templateBytes, err := json.Marshal(templateMessage)
	if err != nil {
		logx.WithContext(ctx).Errorf("failed to marshal template data: %v", err)
		return nil, false, fmt.Errorf("failed to marshal template: %w", err)
	}

	templateStr := string(templateBytes)

	// Create and parse template
	tpl, err := template.New("payment_message").Parse(templateStr)
	if err != nil {
		logx.WithContext(ctx).Errorf("parse %s template error %v", "payment_message", err)
		return nil, false, fmt.Errorf("failed to parse template: %w", err)
	}

	// Execute template
	var result bytes.Buffer
	err = tpl.Execute(&result, data)
	if err != nil {
		logx.WithContext(ctx).Errorf("execute %s template error %v", "payment_message", err)
		return nil, false, fmt.Errorf("failed to execute template: %w", err)
	}

	// Parse result back to templateMessage
	var updatedTemplate types.PaymentTemplateGroup
	err = json.Unmarshal(result.Bytes(), &updatedTemplate)
	if err != nil {
		logx.WithContext(ctx).Errorf("failed to unmarshal template result: %v", err)
		return nil, false, fmt.Errorf("failed to unmarshal template result: %w", err)
	}

	// Update templateMessage with parsed values
	templateMessage.Zns = updatedTemplate.Zns
	templateMessage.Sms = updatedTemplate.Sms

	// Prepare message request
	messageReq := &types.SendMessageRequest{
		PersonId: personId,
		//Phone:    personRecord.Phone,
	}
	if debug {
		messageReq.Phone = personRecord.Phone
	} else {
		messageReq.Phone = phoneTest
	}

	if hasZns {
		paramsJSON, err := json.Marshal(templateMessage.Zns.Params)
		if err != nil {
			return nil, false, fmt.Errorf("failed to marshal ZNS params: %w", err)
		}

		messageReq.ZnsTemplateId = templateMessage.Zns.ZnsID
		messageReq.ZnsParams = string(paramsJSON)
		messageReq.ZnsContent = templateMessage.Zns.Content
		messageReq.SmsContent = templateMessage.Sms.Content
		messageReq.FallbackSms = true

	} else {
		messageReq.SmsContent = templateMessage.Sms.Content
	}

	return messageReq, hasZns, nil
}

func getNotificationTemplate(hasZns bool, hasSchedule bool, templates types.PaymentMessageTemplates) types.PaymentTemplateGroup {
	if hasSchedule {
		return templates.WithSchedule
	}
	return templates.WithoutSchedule

}

func NewMessageWithZaloCheck(personID int, phone string, userID int, hasZalo bool, znsParams, znsContent, smsContent string) *ent.MessageHistory {
	msg := NewMessageHistory(personID, phone, userID)
	if hasZalo {
		msg.Type = messagehistory.TypeZns
		msg.ZnsData = znsParams
		msg.Content = znsContent
	} else {
		msg.Type = messagehistory.TypeSms
		msg.Content = smsContent
	}
	return msg
}
