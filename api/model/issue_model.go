package model

import (
	"bcare/api/internal/types"
	"bcare/ent"
	"bcare/ent/installment"
	"bcare/ent/installmentplan"
	"bcare/ent/issue"
	"bcare/ent/note"
	"context"
)

func CreateIssue(ctx context.Context, client *ent.Client, personID int, issueType, issueDescription, issueTitle string) error {
	existingIssue, _ := client.Issue.Query().
		Where(issue.PersonID(personID), issue.TypeEQ(issue.Type(issueType))).
		First(ctx)
	if existingIssue != nil {
		existingIssue.Description = issueDescription
		existingIssue.Title = issueTitle
		_, err := client.Issue.UpdateOneID(existingIssue.ID).SetIssue(existingIssue).Save(ctx)
		if err != nil {
			return types.ErrNoteAdd.Wrap(err)
		}
		return nil
	}
	issueRecord := new(ent.Issue)
	issueRecord.PersonID = personID
	issueRecord.Type = issue.Type(issueType)
	issueRecord.Progress = issue.ProgressOpen
	issueRecord.Priority = issue.PriorityMedium
	issueRecord.Status = 2
	issueRecord.Description = issueDescription
	issueRecord.Title = issueTitle
	issueRecord, err := client.Issue.Create().SetIssue(issueRecord).Save(ctx)
	if err != nil {
		return types.ErrNoteAdd.Wrap(err)
	}
	return nil
}

func DeleteIssue(ctx context.Context, client *ent.Client, personID int, issueType string) error {
	if issueType == "complain" {
		notes, _ := client.Note.Query().
			Where(note.Type(types.NoteTypeComplaint), note.PersonID(personID)).
			All(ctx)
		if len(notes) > 1 {
			return nil
		}

	} else if issueType == "refund" {
		installments, _ := client.Installment.Query().
			Where(installment.KindEQ(installment.KindRefundPayment)).
			Where(installment.HasPlanWith(installmentplan.PersonID(personID))).
			All(ctx)
		if len(installments) > 0 {
			return nil
		}
	} else {
		return nil
	}
	_, err := client.Issue.Delete().
		Where(issue.TypeEQ(issue.Type(issueType)), issue.PersonID(personID)).
		Exec(ctx)
	if err != nil {
		return err
	}
	return nil
}
