package model

import (
	"bcare/ent"
	"bcare/ent/discountusage"
	"context"
	"fmt"
	"github.com/expr-lang/expr"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"
	"math"
	"time"
)

type DiscountType string

const (
	DiscountTypePercentage DiscountType = "percent"
	DiscountTypeFixed      DiscountType = "fixed"
	DiscountTypeTiered     DiscountType = "tiered"
)

type DiscountScope string

const (
	DiscountScopeDeal    DiscountScope = "deal"
	DiscountScopeProduct DiscountScope = "product"
)

type DiscountUsageType string

const (
	DiscountUsageMultiple DiscountUsageType = "multiple"
	DiscountUsageOneTime  DiscountUsageType = "one_time"
	DiscountUsageSpecial  DiscountUsageType = "special"
)

type (
	DiscountModel interface {
		ApplyDiscounts(deal *ent.Deal, person *ent.Person, products []*ent.Product) (totalDiscount float64, err error)
	}

	Discount struct {
		ent.Discount
	}
)

type DiscountCalculator interface {
	Calculate(basePrice float64, discount *Discount, env map[string]interface{}) (discountAmount float64, afterDiscount float64)
}

type PercentageDiscount struct{}

func (pd *PercentageDiscount) Calculate(basePrice float64, discount *Discount, env map[string]interface{}) (discountAmount float64, afterDiscount float64) {
	discountAmount = basePrice * discount.Value
	afterDiscount = basePrice - discountAmount
	return
}

type FixedDiscount struct{}

func (fd *FixedDiscount) Calculate(basePrice float64, discount *Discount, env map[string]interface{}) (discountAmount float64, afterDiscount float64) {
	discountAmount = discount.Value
	afterDiscount = math.Max(basePrice-discountAmount, 0)
	return
}

type TieredDiscount struct{}

func (td *TieredDiscount) Calculate(basePrice float64, discount *Discount, env map[string]interface{}) (discountAmount float64, afterDiscount float64) {
	var discountFactor float64
	if basePrice > 200 {
		discountFactor = 0.2
	} else if basePrice > 100 {
		discountFactor = 0.1
	} else {
		discountFactor = 0
	}
	discountAmount = basePrice * discountFactor
	afterDiscount = basePrice - discountAmount
	return
}

type DiscountManager struct {
	calculators map[DiscountType]DiscountCalculator
}

func NewDiscountManager() *DiscountManager {
	return &DiscountManager{
		calculators: map[DiscountType]DiscountCalculator{
			DiscountTypePercentage: &PercentageDiscount{},
			DiscountTypeFixed:      &FixedDiscount{},
			DiscountTypeTiered:     &TieredDiscount{},
		},
	}
}

func (dm *DiscountManager) CalculateDiscount(basePrice float64, discount *Discount, env map[string]interface{}) (discountAmount float64, afterDiscount float64, err error) {
	calculator, ok := dm.calculators[DiscountType(discount.Type)]
	if !ok {
		err = errors.New("Unsupported discount type")
		logx.Error(err)
		return 0, basePrice, err
	}

	discountAmount, afterDiscount = calculator.Calculate(basePrice, discount, env)

	// TODO: Implement MaxDiscountAmount logic when supported
	/*if discount.MaxDiscountAmount > 0 && discountAmount > discount.MaxDiscountAmount {
		discountAmount = discount.MaxDiscountAmount
		afterDiscount = basePrice - discountAmount
	}*/

	return discountAmount, afterDiscount, nil
}

type DiscountPrioritizer struct {
	discounts []*Discount
}

func (dp *DiscountPrioritizer) AddDiscount(d *Discount) {
	dp.discounts = append(dp.discounts, d)
}

func (dp *DiscountPrioritizer) GetApplicableDiscounts(env map[string]interface{}) []*Discount {
	var applicable []*Discount

	for _, d := range dp.discounts {
		if d.Status != 1 {
			continue
		}

		if d.Start.After(time.Now()) || d.End.Before(time.Now()) {
			continue
		}

		compiledCond, err := expr.Compile(d.Condition, expr.Env(env))
		if err != nil {
			logx.Errorw("Failed to compile condition",
				logx.LogField{Key: "error", Value: err},
				logx.LogField{Key: "discount", Value: d.ID},
			)
			continue
		}

		output, err := expr.Run(compiledCond, env)
		if err != nil {
			logx.Errorw("Failed to run condition",
				logx.LogField{Key: "error", Value: err},
				logx.LogField{Key: "discount", Value: d.ID},
			)
			continue
		}

		if shouldApply, ok := output.(bool); ok && shouldApply {
			applicable = append(applicable, d)
		}
	}

	return applicable
}

func (d *Discount) ApplyDiscount(basePrice float64, deal *ent.Deal, person *ent.Person, product *ent.Product) (shouldApply bool, discountAmount float64, afterDiscount float64, err error) {
	now := time.Now()
	if !d.Start.IsZero() && !d.End.IsZero() && (d.Start.After(now) || d.End.Before(now)) {
		return false, 0, basePrice, nil
	}

	// Check discount scope
	switch DiscountScope(d.Scope) {
	case DiscountScopeDeal:
		if deal == nil {
			// Invalid: Deal discount requires deal information
			return false, 0, basePrice, nil
		}
		if product != nil {
			// Invalid: Deal discount should not have product information
			return false, 0, basePrice, nil
		}
	case DiscountScopeProduct:
		if product == nil {
			// Invalid: Product discount requires product information
			return false, 0, basePrice, nil
		}
	}

	env := prepareEnvironment(deal, person)
	if product != nil {
		env["product"] = product
	}
	env["contains"] = func(params ...any) (any, error) {
		if len(params) != 2 {
			return nil, fmt.Errorf("contains function requires 2 parameters")
		}

		arr, ok := params[0].([]string)
		if !ok {
			return false, fmt.Errorf("first parameter must be []string")
		}

		str, ok := params[1].(string)
		if !ok {
			return false, fmt.Errorf("second parameter must be string")
		}

		if arr == nil {
			return false, nil
		}

		for _, v := range arr {
			if v == str {
				return true, nil
			}
		}
		return false, nil
	}

	// Check if condition is empty
	if d.Condition == "" {
		// If condition is empty, we assume the discount should always apply
		shouldApply = true
	} else {
		compiledCond, err := expr.Compile(d.Condition, expr.Env(env))
		if err != nil {
			logx.Errorw("Failed to compile condition", logx.LogField{Key: "error", Value: err})
			return false, 0, basePrice, err
		}

		output, err := expr.Run(compiledCond, env)
		if err != nil {
			logx.Errorw("Failed to run condition", logx.LogField{Key: "error", Value: err})
			return false, 0, basePrice, err
		}

		shouldApply = output.(bool)
	}

	if !shouldApply {
		return false, 0, basePrice, nil
	}

	dm := NewDiscountManager()
	discountAmount, afterDiscount, err = dm.CalculateDiscount(basePrice, d, env)
	if err != nil {
		logx.Errorw("Failed to calculate discount", logx.LogField{Key: "error", Value: err})
	}
	return true, discountAmount, afterDiscount, err
}

func (d *Discount) ApplyToDeal(deal *ent.Deal, person *ent.Person) (shouldApplyDiscount bool, discountValue float64, err error) {
	shouldApply, discountAmount, _, err := d.ApplyDiscount(deal.TotalAmount, deal, person, nil)
	if err != nil {
		logx.Errorw("Failed to apply discount to deal", logx.LogField{Key: "error", Value: err})
	}
	return shouldApply, discountAmount, err
}

func (d *Discount) ApplyToProduct(product *ent.Product, deal *ent.Deal, person *ent.Person, discountUsage *ent.DiscountUsage) (shouldApplyDiscount bool, discountValue float64, err error) {
	env := prepareEnvironment(deal, person)
	env["product"] = product
	if discountUsage != nil {
		env["usage_count"] = discountUsage.UsageCount
	}

	shouldApply, discountAmount, _, err := d.ApplyDiscount(float64(product.Price), deal, person, product)
	if err != nil {
		logx.Errorw("Failed to apply discount to product", logx.LogField{Key: "error", Value: err})
	}
	return shouldApply, discountAmount, err
}

func prepareEnvironment(deal *ent.Deal, person *ent.Person) map[string]interface{} {
	env := make(map[string]interface{})

	if deal != nil {
		env["deal"] = deal
	}

	if person != nil {
		env["person"] = person
	}

	env["fn"] = map[string]interface{}{
		"contains": func(arr []string, str string) bool {
			if arr == nil {
				return false
			}
			for _, v := range arr {
				if v == str {
					return true
				}
			}
			return false
		},
	}

	return env
}

func FetchDiscountUsages(q *ent.DiscountUsageQuery, dealIDs []int, ctx context.Context) ([]*ent.DiscountUsage, error) {
	return q.
		Where(discountusage.DealIDIn(dealIDs...)).
		WithDiscount().
		All(ctx)
}
