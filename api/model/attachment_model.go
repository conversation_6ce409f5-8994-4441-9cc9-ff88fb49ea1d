package model

import (
	"bcare/common/bconst"
	"bcare/ent"
	"bcare/ent/attachment"
	"bcare/ent/deal"
	"bcare/ent/installment"
	"context"
	"fmt"
	"time"
)

func UpdateInstallmentsAndRelatedData(ctx context.Context, tx *ent.Tx, installmentIDs []int) error {
	if len(installmentIDs) == 0 {
		return nil
	}

	// Update paid_at for installments
	err := tx.Installment.Update().
		Where(installment.IDIn(installmentIDs...)).
		SetPaidAt(time.Now()).
		Exec(ctx)
	if err != nil {
		return fmt.Errorf("failed to update installments paid_at: %w", err)
	}

	// Get all unique plan IDs from installments
	plans, err := tx.Installment.Query().
		Where(installment.IDIn(installmentIDs...)).
		QueryPlan().
		WithDeal().
		All(ctx)
	if err != nil {
		return fmt.Errorf("failed to fetch installment plans: %w", err)
	}

	// Collect unique deal IDs
	dealIDs := make([]int, 0)
	for _, plan := range plans {
		if plan.Edges.Deal != nil {
			dealIDs = append(dealIDs, plan.Edges.Deal.ID)
		}
	}

	if len(dealIDs) > 0 {
		// Update deals state to active
		err = tx.Deal.Update().
			Where(deal.IDIn(dealIDs...)).
			SetState(deal.StatePaying).
			Exec(ctx)
		if err != nil {
			return fmt.Errorf("failed to update deals state: %w", err)
		}

		// Update all attachments status for these deals
		err = tx.Attachment.Update().
			Where(
				attachment.HasDealWith(deal.IDIn(dealIDs...)),
			).
			SetStatus(bconst.StatusNormal).
			Exec(ctx)
		if err != nil {
			return fmt.Errorf("failed to update attachments status: %w", err)
		}
	}

	return nil
}

func UpdateAttachmentData(ctx context.Context, tx *ent.Tx, attachmentIDs []int) error {
	return tx.Attachment.Update().
		Where(attachment.IDIn(attachmentIDs...)).
		SetStatus(bconst.StatusNormal).
		Exec(ctx)
}
