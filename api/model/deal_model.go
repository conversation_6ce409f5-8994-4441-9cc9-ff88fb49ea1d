package model

import (
	"bcare/api/internal/types"
	keylock "bcare/common/lock"
	"bcare/ent"
	"bcare/ent/deal"
	"context"
	"fmt"
	"log"
	"time"
)

func GetProductsByDeal(ctx context.Context, client *ent.Client, dealID int) ([]*ent.Product, error) {
	// Lấy deal dựa trên ID
	d, err := client.Deal.Query().
		Where(deal.ID(dealID)).
		Only(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed querying deal: %w", err)
	}

	// Lấy tất cả các attachment liên quan đến deal
	attachments, err := d.QueryAttachments().All(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed querying attachments: %w", err)
	}

	// Tạo một map để lưu trữ các sản phẩm duy nhất
	uniqueProducts := make(map[int]*ent.Product)

	// Lặp qua các attachment và lấy sản phẩm liên quan
	for _, a := range attachments {
		// Chỉ xử lý các attachment có loại là "product"
		if a.Kind == "product" {
			p, err := a.QueryProduct().Only(ctx)
			if err != nil {
				if ent.IsNotFound(err) {
					// Bỏ qua nếu không tìm thấy sản phẩm
					continue
				}
				return nil, fmt.Errorf("failed querying product for attachment: %w", err)
			}
			// Thêm sản phẩm vào map nếu chưa tồn tại
			if _, exists := uniqueProducts[p.ID]; !exists {
				uniqueProducts[p.ID] = p
			}
		}
	}

	// Chuyển đổi map thành slice
	products := make([]*ent.Product, 0, len(uniqueProducts))
	for _, p := range uniqueProducts {
		products = append(products, p)
	}

	return products, nil
}

func UpdateTotalAmount(ctx context.Context, client *ent.Client, dealID int) error {

	d, err := client.Deal.Query().
		Where(deal.ID(dealID)).
		WithAttachments().
		Only(ctx)
	if err != nil {
		return fmt.Errorf("failed querying deal: %w", err)
	}

	unlock, err := keylock.Global.Lock(1*time.Second, d.ID)
	if err != nil {
		return types.ErrCallUpdate.Wrap(err, "failed to acquire lock")
	}
	defer unlock()
	var totalAmount float64
	for _, a := range d.Edges.Attachments {
		// Chỉ xử lý các attachment có loại là "product"
		if a.Kind == "product" {
			totalAmount += (a.Price) * float64(a.Quantity)
		}
	}
	log.Println(d.ID)
	// Update totalAmount mới cho deal
	_, err = client.Deal.UpdateOne(d).SetTotalAmount(totalAmount).Save(ctx)
	log.Println(err)
	if err != nil {
		return err
	}
	return nil
}
