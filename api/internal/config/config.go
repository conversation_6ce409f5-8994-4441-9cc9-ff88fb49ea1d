package config

import (
	"github.com/zeromicro/go-zero/rest"
)

type Config struct {
	rest.RestConf
	Auth struct {
		IdentityKey   string
		AccessSecret  string
		AccessExpire  int64
		RefreshExpire int64
		OTP           struct {
			Enabled       bool
			Length        int
			ExpireMinutes int
			Receivers     struct {
				Type        string
				SingleEmail string
				EmailList   []string
			}
		}
	}
	Database struct {
		RedisPass  string
		DataSource string
		DbUser     string
		DbName     string
		DbPass     string
	}
	General struct {
		DateFormat         string
		DateTimeFormat     string
		FullDateTimeFormat string
		SqlDateFormat      string
		CORS               []string
	}
	App struct {
		DiscountDistributionStrategy string
	}
	Storage struct {
		Local struct {
			Path   string
			Prefix string
		}
	}
	SyncGetFly   bool
	Debug        bool
	PhoneTest    string
	ServicesAddr struct {
		Bmind string
	}
	ApiKey struct {
		OpenAI    string
		Anthropic string
	}
}
