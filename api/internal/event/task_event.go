package event

import (
	"bcare/api/internal/dto"
	"bcare/common/eventbus"
	"strconv"
)

type taskBase struct {
	Immutable bool
	Task      *dto.TaskOutput
	OldTask   *dto.TaskOutput
	Invoker   int
}

func (e *taskBase) ResourceType() string {
	return "Task"
}

// GetUserID returns the ID of the user who performed the action.
func (e *taskBase) GetUserID() int {
	return e.Invoker
}

func (e *taskBase) Match(c eventbus.ConstraintMatcher) bool {
	if e.Task == nil {
		return false
	}

	switch c.Name() {
	case "id":
		return c.Match(strconv.Itoa(e.Task.ID))
	case "state":
		return c.Match(e.Task.State)
	case "title":
		return c.Match(e.Task.Title)
	default:
		return false
	}
}

type TaskAfterCreate struct {
	*taskBase
}

func (e *TaskAfterCreate) EventType() string { return "afterCreate" }

type TaskAfterUpdate struct {
	*taskBase
}

func (e *TaskAfterUpdate) EventType() string { return "afterUpdate" }

type TaskBeforeDelete struct {
	*taskBase
}

func (e *TaskBeforeDelete) EventType() string { return "beforeDelete" }

func TaskAfterCreateEvent(task *dto.TaskOutput, invokerID int) *TaskAfterCreate {
	return &TaskAfterCreate{
		taskBase: &taskBase{
			Immutable: false,
			Task:      task,
			OldTask:   nil,
			Invoker:   invokerID,
		},
	}
}

func TaskAfterUpdateEvent(oldTask *dto.TaskOutput, task *dto.TaskOutput, invokerID int) *TaskAfterUpdate {
	return &TaskAfterUpdate{
		taskBase: &taskBase{
			Immutable: false,
			Task:      task,
			OldTask:   oldTask,
			Invoker:   invokerID,
		},
	}
}

func TaskBeforeDeleteEvent(taskToDelete *dto.TaskOutput, invokerID int) *TaskBeforeDelete {
	return &TaskBeforeDelete{
		taskBase: &taskBase{
			Immutable: true,
			Task:      taskToDelete,
			OldTask:   nil,
			Invoker:   invokerID,
		},
	}
}
