# Event System Architecture

## Overview
This package contains event definitions for the BCare event-driven architecture. Events are dispatched through an event bus and handled by specific handlers.

## Event Types

### 1. Generic Entity Events (`entity_change.go`)
- **Purpose**: Track history for any entity changes
- **Used by**: `generic_history.go` handler
- **Interface**: Implements `HistorizableEvent` for comprehensive history tracking
- **Resource Type**: Uses "ent_" prefix (e.g., "ent_Person", "ent_Deal")

### 2. Specialized Domain Events
These events have their own dedicated handlers and DO NOT implement `HistorizableEvent`:

- **Deal Events** (`deal.go`)
  - Used by: `stage_history.go` - tracks stage changes only
  - Direct access to `Deal` and `OldDeal` fields

- **Task Events** (`task_event.go`) 
  - Used by: `task_handler.go` - handles task state changes and notifications
  - Provides `GetUserID()` method for invoker access
  - Direct access to `Task` and `OldTask` fields

- **Notification Events** (`notification_event.go`)
  - Used by: `notification_handler.go` - sends real-time websocket updates
  - Direct access to `Notification` field only

## Design Decisions

### Minimal Interface Implementation
Domain events (Deal, Task, Notification) only implement the basic `eventbus.Event` interface:
- `ResourceType()` - returns entity type without prefix
- `EventType()` - returns event type (afterCreate, afterUpdate, beforeDelete)
- `Match()` - for event bus pattern matching

### No Redundant Methods
Previously, all events had methods like `GetResourceID()`, `GetNewRecord()`, `GetOldRecord()`, `GetChanges()`, etc. These were removed because:
1. Handlers use direct field access instead
2. Only `EntityChangeEvent` needs these for generic history tracking
3. Reduces code complexity and maintenance burden

### Direct Type Assertion
Handlers use direct type assertion instead of interfaces:
```go
// Good - direct type assertion
updateEvent, ok := ev.(*event.TaskAfterUpdate)

// Avoid - unnecessary interface
updateEvent, ok := ev.(HistorizableEvent)
```

## Maintenance Notes
- When adding new events, only implement methods that handlers actually need
- Use direct field access in handlers rather than getter methods
- Only implement `HistorizableEvent` if the event needs generic history tracking
- Keep domain events simple with just the required eventbus.Event methods