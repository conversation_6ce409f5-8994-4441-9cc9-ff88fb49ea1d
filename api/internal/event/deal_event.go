package event

import (
	"bcare/api/internal/types"
	"bcare/common/butils"
	"bcare/common/eventbus"
	"bcare/ent"

	"errors"
	"strconv"
	"time"
)

// --- dealBase: Base struct containing common data for Deal events ---
type dealBase struct {
	Immutable bool
	Deal      *ent.Deal
	OldDeal   *ent.Deal
	Invoker   int
}

// --- Implement common methods from dealBase ---

// ResourceType returns the resource type "deal".
func (e *dealBase) ResourceType() string {
	return "Deal"
}

// GetResourceID returns the ID of the affected Deal.
func (e *dealBase) GetResourceID() int {
	if e.Deal != nil {
		return e.Deal.ID
	}
	if e.OldDeal != nil {
		return e.OldDeal.ID
	}
	return 0
}

// GetUserID returns the ID of the user who performed the action.
func (e *dealBase) GetUserID() int {
	return e.Invoker
}

// Match checks basic constraints for the Deal event.
func (e *dealBase) Match(c eventbus.ConstraintMatcher) bool {
	targetDeal := e.Deal

	if targetDeal == nil {
		return false
	}

	switch c.Name() {
	case "id":
		return c.Match(strconv.Itoa(targetDeal.ID))
	case "stage_id":
		if targetDeal.StageID != nil {
			return c.Match(strconv.Itoa(*targetDeal.StageID))
		}
		return c.Match("")
	case "name":
		return c.Match(targetDeal.Name)
	default:
		return false
	}
}

func (e *dealBase) GetNewRecord() any {
	return e.Deal
}

func (e *dealBase) GetOldRecord() any {
	return e.OldDeal
}

// --- DealAfterCreate: Event after creating a Deal ---
type DealAfterCreate struct {
	*dealBase
}

// EventType returns the event type.
func (e *DealAfterCreate) EventType() string { return "afterCreate" }

// GetOperationType returns the operation type.
func (e *DealAfterCreate) GetOperationType() types.OperationType {
	return types.OpTypeCreate
}

// GetOccurredAt returns the Deal creation time.
func (e *DealAfterCreate) GetOccurredAt() time.Time {
	if e.Deal != nil {
		return e.Deal.CreatedAt
	}
	return time.Now()
}

// GetChanges returns the data of the newly created Deal.
func (e *DealAfterCreate) GetChanges() (map[string]interface{}, error) {
	if e.Deal == nil {
		return nil, errors.New("cannot get changes for create event: Deal data is nil")
	}
	return butils.StructToMap(e.Deal)
}

// --- DealAfterUpdate: Event after updating a Deal ---
type DealAfterUpdate struct {
	*dealBase
}

// EventType returns the event type.
func (e *DealAfterUpdate) EventType() string { return "afterUpdate" }

// GetOperationType returns the operation type.
func (e *DealAfterUpdate) GetOperationType() types.OperationType {
	return types.OpTypeUpdate
}

// GetOccurredAt returns the Deal update time.
func (e *DealAfterUpdate) GetOccurredAt() time.Time {
	if e.Deal != nil {
		return e.Deal.UpdatedAt
	}
	return time.Now()
}

// GetChanges calculates and returns the changed fields between OldDeal and Deal.
func (e *DealAfterUpdate) GetChanges() (map[string]interface{}, map[string]interface{}, error) {
	if e.OldDeal == nil || e.Deal == nil {
		return nil, nil, errors.New("cannot get changes for update event: OldDeal or Deal data is nil")
	}
	return butils.DiffStructs(e.OldDeal, e.Deal)
}

// DealBeforeDelete Event right before deleting a Deal ---
type DealBeforeDelete struct {
	*dealBase
}

// EventType returns the event type.
func (e *DealBeforeDelete) EventType() string { return "beforeDelete" }

// GetOperationType returns the operation type.
func (e *DealBeforeDelete) GetOperationType() types.OperationType {
	return types.OpTypeDelete
}

// GetOccurredAt returns the time the delete event was triggered.
func (e *DealBeforeDelete) GetOccurredAt() time.Time {
	return time.Now()
}

// GetChanges returns the data of the Deal about to be deleted.
func (e *DealBeforeDelete) GetChanges() (map[string]interface{}, error) {
	targetDeal := e.Deal
	if targetDeal == nil {
		return nil, errors.New("cannot get changes for delete event: Deal data is nil")
	}
	return butils.StructToMap(targetDeal)
}

// --- Factory Functions: Functions to create event objects ---

// DealAfterCreateEvent creates a DealAfterCreate event.
func DealAfterCreateEvent(deal *ent.Deal, invokerID int) *DealAfterCreate {
	return &DealAfterCreate{
		dealBase: &dealBase{
			Immutable: false,
			Deal:      deal,
			OldDeal:   nil,
			Invoker:   invokerID,
		},
	}
}

// DealAfterUpdateEvent creates a DealAfterUpdate event.
func DealAfterUpdateEvent(oldDeal *ent.Deal, deal *ent.Deal, invokerID int) *DealAfterUpdate {
	return &DealAfterUpdate{
		dealBase: &dealBase{
			Immutable: false,
			Deal:      deal,
			OldDeal:   oldDeal,
			Invoker:   invokerID,
		},
	}
}

// DealBeforeDeleteEvent creates a DealBeforeDelete event.
func DealBeforeDeleteEvent(dealToDelete *ent.Deal, invokerID int) *DealBeforeDelete {
	return &DealBeforeDelete{
		dealBase: &dealBase{
			Immutable: true,
			Deal:      dealToDelete,
			OldDeal:   nil,
			Invoker:   invokerID,
		},
	}
}
