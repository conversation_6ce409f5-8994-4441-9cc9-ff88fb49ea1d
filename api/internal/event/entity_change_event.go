package event

import (
	"errors"
	"fmt"
	"time"

	"bcare/api/internal/types"
	"bcare/common/butils"
	"bcare/common/eventbus"
)

// EntityChangeEvent is a generic event representing a change
type EntityChangeEvent struct {
	Immutable bool

	EntityType string

	ResourceID int

	Operation types.OperationType

	Invoker int

	OccurredAt time.Time

	OldValue interface{}

	NewValue interface{}
}

// ResourceType returns the name of the affected schema.
func (e *EntityChangeEvent) ResourceType() string {
	return fmt.Sprintf("ent_%s", e.EntityType)
}

// EventType returns the event type based on OperationType.
func (e *EntityChangeEvent) EventType() string {
	switch e.Operation {
	case types.OpTypeCreate:
		return "afterCreate"
	case types.OpTypeUpdate:
		return "afterUpdate"
	case types.OpTypeDelete:
		return "beforeDelete"
	default:
		return "unknown"
	}
}

// Match checks if this event matches the constraints.
func (e *EntityChangeEvent) Match(c eventbus.ConstraintMatcher) bool {
	return false
}

// GetResourceID Implement hook.HistorizableEvent
func (e *EntityChangeEvent) GetResourceID() int                    { return e.ResourceID }
func (e *EntityChangeEvent) GetUserID() int                        { return e.Invoker }
func (e *EntityChangeEvent) GetOperationType() types.OperationType { return e.Operation }
func (e *EntityChangeEvent) GetOccurredAt() time.Time              { return e.OccurredAt }
func (e *EntityChangeEvent) GetEntityType() string                 { return e.EntityType }

func (e *EntityChangeEvent) GetChanges() (map[string]interface{}, map[string]interface{}, error) {
	switch e.Operation {
	case types.OpTypeCreate:
		if e.NewValue == nil {
			return nil, nil, errors.New("cannot get changes for create: NewValue is nil")
		}
		changes, err := butils.StructToMap(e.NewValue)
		if err != nil {
			return nil, nil, fmt.Errorf("failed to convert NewValue to map for create: %w", err)
		}
		return nil, changes, nil

	case types.OpTypeUpdate:
		if e.OldValue == nil || e.NewValue == nil {
			return nil, nil, errors.New("cannot get changes for update: OldValue or NewValue is nil")
		}
		before, after, err := butils.DiffStructs(e.OldValue, e.NewValue)
		if err != nil {
			return nil, nil, fmt.Errorf("failed to diff OldValue and NewValue for update: %w", err)
		}
		return before, after, nil

	case types.OpTypeDelete:
		if e.OldValue == nil {
			return nil, nil, errors.New("cannot get changes for delete: OldValue is nil (expected pre-delete state)")
		}
		changes, err := butils.StructToMap(e.OldValue)
		if err != nil {
			return nil, nil, fmt.Errorf("failed to convert OldValue to map for delete: %w", err)
		}
		return changes, nil, nil

	default:
		return nil, nil, fmt.Errorf("unsupported operation type for GetChanges: %s", e.Operation)
	}
}

func (e *EntityChangeEvent) GetNewRecord() any { return e.NewValue }
func (e *EntityChangeEvent) GetOldRecord() any { return e.OldValue }

// NewEntityChangeEvent is a constructor function to easily create EntityChangeEvent objects.
func NewEntityChangeEvent(
	entityType string,
	resID int,
	opType types.OperationType,
	userID int,
	occurred time.Time,
	oldVal interface{},
	newVal interface{},
) *EntityChangeEvent {
	return &EntityChangeEvent{
		Immutable:  true,
		EntityType: entityType,
		ResourceID: resID,
		Operation:  opType,
		Invoker:    userID,
		OccurredAt: occurred,
		OldValue:   oldVal,
		NewValue:   newVal,
	}
}
