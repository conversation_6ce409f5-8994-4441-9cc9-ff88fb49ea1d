package event

import (
	"bcare/api/internal/dto"
	"bcare/common/eventbus"
	"strconv"
)

type notificationBase struct {
	Immutable       bool
	Notification    *dto.NotificationOutput
	OldNotification *dto.NotificationOutput
	Invoker         int
}

func (e *notificationBase) ResourceType() string {
	return "Notification"
}

func (e *notificationBase) Match(c eventbus.ConstraintMatcher) bool {
	if e.Notification == nil {
		return false
	}

	switch c.Name() {
	case "id":
		return c.Match(strconv.Itoa(e.Notification.ID))
	case "user_id":
		return c.Match(strconv.Itoa(e.Notification.UserID))
	case "type":
		return c.Match(e.Notification.Type)
	default:
		return false
	}
}

type NotificationAfterCreate struct {
	*notificationBase
}

func (e *NotificationAfterCreate) EventType() string { return "afterCreate" }

type NotificationAfterUpdate struct {
	*notificationBase
}

func (e *NotificationAfterUpdate) EventType() string { return "afterUpdate" }

type NotificationBeforeDelete struct {
	*notificationBase
}

func (e *NotificationBeforeDelete) EventType() string { return "beforeDelete" }

func NotificationAfterCreateEvent(notification *dto.NotificationOutput, invokerID int) *NotificationAfterCreate {
	return &NotificationAfterCreate{
		notificationBase: &notificationBase{
			Immutable:       false,
			Notification:    notification,
			OldNotification: nil,
			Invoker:         invokerID,
		},
	}
}

func NotificationAfterUpdateEvent(oldNotification *dto.NotificationOutput, notification *dto.NotificationOutput, invokerID int) *NotificationAfterUpdate {
	return &NotificationAfterUpdate{
		notificationBase: &notificationBase{
			Immutable:       false,
			Notification:    notification,
			OldNotification: oldNotification,
			Invoker:         invokerID,
		},
	}
}

func NotificationBeforeDeleteEvent(notificationToDelete *dto.NotificationOutput, invokerID int) *NotificationBeforeDelete {
	return &NotificationBeforeDelete{
		notificationBase: &notificationBase{
			Immutable:       true,
			Notification:    notificationToDelete,
			OldNotification: nil,
			Invoker:         invokerID,
		},
	}
}
