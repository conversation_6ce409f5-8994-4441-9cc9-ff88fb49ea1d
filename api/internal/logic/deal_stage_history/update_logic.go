package deal_stage_history

import (
	"bcare/common/bquery"
	"bcare/ent"
	"context"
	"github.com/jinzhu/copier"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateLogic(ctx context.Context, svcCtx *svc.ServiceContext) UpdateLogic {
	return UpdateLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateLogic) Update(req *types.DealStageHistoryUpdateRequest) (resp *types.DealStageHistory, err error) {
	dealStageHistoryRecord := new(ent.DealStageHistory)
	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		dealStageHistoryRecord, err = tx.DealStageHistory.Get(l.ctx, req.Id)
		if err != nil {
			return types.ErrDealUpdate.Wrap(err, err.Error())
		}
		err = copier.Copy(&dealStageHistoryRecord, req)
		if err != nil {
			return types.ErrDealUpdate.Wrap(err, err.Error())
		}

		dealStageHistoryRecord, err = tx.DealStageHistory.UpdateOneID(dealStageHistoryRecord.ID).SetDealStageHistory(dealStageHistoryRecord).Save(l.ctx)
		if err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		return nil, types.ErrPersonUpdate.Wrap(err, err.Error())
	}

	resp = new(types.DealStageHistory)
	_ = copier.Copy(&resp, dealStageHistoryRecord)
	return resp, nil
}
