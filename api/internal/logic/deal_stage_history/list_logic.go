package deal_stage_history

import (
	"bcare/common/bquery"
	cast2 "bcare/common/cast"
	"bcare/ent"
	"context"
	"entgo.io/ent/dialect/sql"
	"github.com/samber/lo"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type ListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewListLogic(ctx context.Context, svcCtx *svc.ServiceContext) ListLogic {
	return ListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ListLogic) List(req *types.DealStageHistoryListRequest) (resp *types.DealStageHistoryListResponse, err error) {
	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		limit, offset := bquery.Paging(req.PageSize, req.Page)
		order := bquery.Ordering(req.OrderBy)
		query := tx.DealStageHistory.Query().Order(order)

		filterMap := make(map[string]interface{})
		_ = cast2.AtomNoZero(&filterMap, req.Filter)

		for field, value := range filterMap {
			query = query.Where(sql.FieldEQ(field, value))
		}

		total, _ := query.Count(l.ctx)
		histories, e := query.Limit(limit).Offset(offset).All(l.ctx)

		if e != nil {
			err = types.ErrDealList.Wrap(e, err.Error())
			return err
		}

		resp = new(types.DealStageHistoryListResponse)
		resp.DealStageHistory = lo.Map(histories, func(history *ent.DealStageHistory, _ int) types.DealStageHistory {
			h := types.DealStageHistory{}
			_ = cast2.EntViewToResp(&h, history)
			return h
		})
		resp.Total = total

		return nil
	})

	return resp, err
}
