package stage

import (
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/bquery"
	cast2 "bcare/common/cast"
	"bcare/ent"
	"bcare/ent/stage"
	"context"
	"entgo.io/ent/dialect/sql"
	"github.com/jinzhu/copier"
	"github.com/zeromicro/go-zero/core/logx"
)

type ListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewListLogic(ctx context.Context, svcCtx *svc.ServiceContext) ListLogic {
	return ListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ListLogic) List(req *types.StageListRequest) (resp *types.StageListResponse, err error) {
	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		query := tx.Stage.Query().WithChildren().Order(stage.ByID())

		filterMap := make(map[string]interface{})
		_ = cast2.AtomNoZero(&filterMap, req.Filter)

		for field, value := range filterMap {
			query = query.Where(sql.FieldEQ(field, value))
		}

		stages, e := query.All(l.ctx)

		if e != nil {
			err = types.ErrStageGetList.Wrap(e)
			return err
		}

		stageResponses := make([]types.StageResponse, 0)
		for _, s := range stages {
			if (s.ParentStageID == nil) || (s.ParentStageID != nil && *s.ParentStageID == 0) {
				stageResponse := types.StageResponse{}
				_ = copier.Copy(&stageResponse, s)
				_ = copier.Copy(&stageResponse.Children, s.Edges.Children)
				stageResponses = append(stageResponses, stageResponse)
			}
		}

		resp = new(types.StageListResponse)
		_ = copier.Copy(&resp.Stages, stageResponses)
		return nil
	})

	return resp, err
}
