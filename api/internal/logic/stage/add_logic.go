package stage

import (
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/ent"
	"context"
	"github.com/jinzhu/copier"

	"github.com/zeromicro/go-zero/core/logx"
)

type AddLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAddLogic(ctx context.Context, svcCtx *svc.ServiceContext) AddLogic {
	return AddLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AddLogic) Add(req *types.StageAddRequest) (resp *types.StageResponse, err error) {
	stage := new(ent.Stage)
	_ = copier.Copy(stage, req)
	stage, err = l.svcCtx.Ent.Stage.Create().
		SetStage(stage).
		Save(l.ctx)

	if err != nil {
		err = types.ErrStageAdd.Wrap(err)
		return
	}

	resp = new(types.StageResponse)
	_ = copier.Copy(resp, stage)

	return resp, err
}
