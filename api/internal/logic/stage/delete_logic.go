package stage

import (
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/ent"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewDeleteLogic(ctx context.Context, svcCtx *svc.ServiceContext) DeleteLogic {
	return DeleteLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DeleteLogic) Delete(req *types.StageDeleteRequest) (resp *types.StageResponse, err error) {
	stage, e := l.svcCtx.Ent.Stage.Get(l.ctx, req.Id)

	if e != nil {
		if ent.IsNotFound(e) {
			err = types.ErrStageNotFound
		} else {
			err = types.ErrStageNotFound.Wrap(e)
		}
		return
	}

	e = l.svcCtx.Ent.Stage.DeleteOneID(stage.ID).
		Exec(l.ctx)
	if e != nil {
		err = types.ErrStageUpdate.Wrap(e)
		return
	}

	return
}
