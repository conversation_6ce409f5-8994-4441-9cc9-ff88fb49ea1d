package stage

import (
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/ent"
	"bcare/ent/stage"
	"context"
	"github.com/jinzhu/copier"
	"github.com/zeromicro/go-zero/core/logx"
)

type GetLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetLogic(ctx context.Context, svcCtx *svc.ServiceContext) GetLogic {
	return GetLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetLogic) Get(req *types.StageGetRequest) (resp *types.StageResponse, err error) {
	stageRecord, e := l.svcCtx.Ent.Stage.Query().Where(stage.ID(req.Id)).WithChildren().First(l.ctx)

	if e != nil {
		if ent.IsNotFound(e) {
			err = types.ErrStageNotFound
		} else {
			err = types.ErrStageNotFound.Wrap(e)
		}
		return
	}
	resp = new(types.StageResponse)
	_ = copier.Copy(&resp, stageRecord)
	_ = copier.Copy(&resp.Children, stageRecord.Edges.Children)

	return
}
