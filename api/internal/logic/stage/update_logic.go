package stage

import (
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	cast2 "bcare/common/cast"
	"bcare/ent"
	"context"
	"github.com/jinzhu/copier"

	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateLogic(ctx context.Context, svcCtx *svc.ServiceContext) UpdateLogic {
	return UpdateLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateLogic) Update(req *types.StageUpdateRequest) (resp *types.StageResponse, err error) {
	stageRecord, e := l.svcCtx.Ent.Stage.Get(l.ctx, req.Id)

	if e != nil {
		if ent.IsNotFound(e) {
			err = types.ErrStageUpdate
		} else {
			err = types.ErrStageUpdate.Wrap(e)
		}
		return
	}
	err = copier.Copy(&stageRecord, req)

	stageRecord, e = l.svcCtx.Ent.Stage.UpdateOneID(stageRecord.ID).
		SetStage(stageRecord, req.Modified...).
		Save(l.ctx)

	if e != nil {
		return
	}

	resp = new(types.StageResponse)
	eCopy := cast2.ConvertViaJson(resp, stageRecord)

	if eCopy != nil {
		err = types.ErrStageUpdate.Wrap(eCopy)
		return
	}

	return
}
