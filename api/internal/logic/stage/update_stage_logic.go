package stage

import (
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	cast2 "bcare/common/cast"
	"bcare/ent"
	"context"
	"encoding/json"
	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateStageLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateStageLogic(ctx context.Context, svcCtx *svc.ServiceContext) UpdateStageLogic {
	return UpdateStageLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateStageLogic) UpdateStage(req *types.StageUpdateMetaRequest) (resp *types.StageResponse, err error) {
	// Tìm Stage theo ID
	stageRecord, e := l.svcCtx.Ent.Stage.Get(l.ctx, req.Id)

	if e != nil {
		if ent.IsNotFound(e) {
			err = types.ErrStageUpdate
		} else {
			err = types.ErrStageUpdate.Wrap(e)
		}
		return nil, err
	}

	// Cập nhật trường meta
	var meta map[string]interface{}
	if stageRecord.Meta != "" {
		if err := json.Unmarshal([]byte(stageRecord.Meta), &meta); err != nil {
			return nil, types.ErrStageUpdate.Wrap(err)
		}
	}
	if meta == nil {
		meta = make(map[string]interface{})
	}

	// Cập nhật hoặc thêm trường mới vào meta
	meta[req.MetaFieldName] = req.MetaValue

	// Chuyển đổi lại meta thành JSON
	updatedMeta, err := json.Marshal(meta)
	if err != nil {
		return nil, types.ErrStageUpdate.Wrap(err)
	}

	stageRecord, e = l.svcCtx.Ent.Stage.UpdateOneID(stageRecord.ID).
		SetMeta(string(updatedMeta)).
		Save(l.ctx)
	if e != nil {
		return
	}

	resp = new(types.StageResponse)
	eCopy := cast2.ConvertViaJson(resp, stageRecord)

	if eCopy != nil {
		err = types.ErrStageUpdate.Wrap(eCopy)
		return
	}

	return
}
