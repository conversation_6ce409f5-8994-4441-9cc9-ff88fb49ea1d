package setting

import (
	"bcare/common/bquery"
	keylock "bcare/common/lock"
	"bcare/ent"
	"bcare/ent/setting"
	"context"
	"github.com/jinzhu/copier"
	"time"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type SyncLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewSyncLogic(ctx context.Context, svcCtx *svc.ServiceContext) SyncLogic {
	return SyncLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *SyncLogic) Sync(req *types.SettingSyncRequest) (resp *types.Setting, err error) {
	var settingRecord *ent.Setting

	// Create lock based on category and name
	unlock, err := keylock.Global.Lock(5*time.Second, req.Category, req.Name)
	if err != nil {
		return nil, types.ErrSettingAdd.Wrap(err, "failed to acquire lock")
	}
	defer unlock()

	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		// Find setting based on category and name
		settingRecord, err = tx.Setting.Query().
			Where(
				setting.Category(req.Category),
				setting.Name(req.Name),
			).
			Only(l.ctx)

		// If not found, create new
		if err != nil {
			if ent.IsNotFound(err) {
				settingRecord, err = tx.Setting.Create().
					SetCategory(req.Category).
					SetName(req.Name).
					SetValue(req.Value).
					SetDescription(req.Description).
					Save(l.ctx)
				if err != nil {
					return types.ErrSettingAdd.Wrap(err, "failed to create setting")
				}
			} else {
				return types.ErrSettingAdd.Wrap(err, "failed to query setting")
			}
		} else {
			// If setting exists, update it
			settingRecord, err = settingRecord.Update().
				SetValue(req.Value).
				SetDescription(req.Description).
				Save(l.ctx)
			if err != nil {
				return types.ErrSettingAdd.Wrap(err, "failed to update setting")
			}
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	// Convert saved setting to response
	resp = new(types.Setting)
	_ = copier.Copy(&resp, settingRecord)
	return resp, nil
}
