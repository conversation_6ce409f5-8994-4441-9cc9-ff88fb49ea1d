package setting

import (
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/bquery"
	"bcare/ent"
	"context"
	"github.com/jinzhu/copier"
	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateLogic(ctx context.Context, svcCtx *svc.ServiceContext) UpdateLogic {
	return UpdateLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateLogic) Update(req *types.SettingUpdateRequest) (resp *types.Setting, err error) {
	settingRecord := new(ent.Setting)

	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		settingRecord, err = tx.Setting.Get(l.ctx, req.Id)
		if err != nil {
			return err
		}

		err = copier.Copy(&settingRecord, req)
		if err != nil {
			logx.WithContext(l.ctx).Errorf("cannot copy request: %v", err)
			return types.ErrSettingUpdate.Wrap(err)
		}

		settingRecord, err = tx.Setting.UpdateOne(settingRecord).SetSetting(settingRecord, req.Modified...).Save(l.ctx)
		if err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		return nil, types.ErrSettingUpdate.Wrap(err)
	}

	if settingRecord.Category == "call" ||
		settingRecord.Category == "sms" ||
		settingRecord.Category == "zns" ||
		settingRecord.Category == "otp" ||
		settingRecord.Category == "sms_zns_payment" {
		l.svcCtx.Setting.ReloadAll()
		l.svcCtx.Setting.ReloadAllSvcSetting()
	}

	resp = new(types.Setting)
	_ = copier.Copy(&resp, settingRecord)
	return resp, nil
}
