package setting

import (
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/bquery"
	"bcare/ent"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewDeleteLogic(ctx context.Context, svcCtx *svc.ServiceContext) DeleteLogic {
	return DeleteLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DeleteLogic) Delete(req *types.SettingDeleteRequest) (resp *types.Setting, err error) {
	// todo: add your logic here and delete this line

	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		settingRecord, err := tx.Setting.Get(l.ctx, req.Id)

		if err != nil && !ent.IsNotFound(err) {
			return err
		}

		if settingRecord == nil {
			logx.Errorf("setting not found")
			return types.ErrSettingDelete
		}
		err = tx.Setting.DeleteOne(settingRecord).Exec(l.ctx)
		if err != nil {
			return types.ErrSettingDelete.Wrap(err)
		}
		return nil
	})

	return
}
