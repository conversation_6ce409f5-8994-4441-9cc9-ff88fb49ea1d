package setting

import (
	"bcare/common/bquery"
	"bcare/ent"
	"context"
	"github.com/jinzhu/copier"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type AddLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAddLogic(ctx context.Context, svcCtx *svc.ServiceContext) AddLogic {
	return AddLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AddLogic) Add(req *types.SettingAddRequest) (resp *types.Setting, err error) {
	settingRecord := new(ent.Setting)
	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		err = copier.Copy(&settingRecord, req)
		if err != nil {
			logx.WithContext(l.ctx).Errorf("cannot copy request: %v", err)
			return types.ErrSettingAdd.Wrap(err)
		}
		save, err := tx.Setting.Create().SetSetting(settingRecord).Save(l.ctx)
		if err != nil {
			return err
		}
		settingRecord = save

		return nil
	})

	if err != nil {
		return nil, types.ErrSettingAdd.Wrap(err)
	}

	resp = new(types.Setting)
	_ = copier.Copy(&resp, settingRecord)
	return resp, nil

}
