package setting

import (
	"bcare/ent"
	"bcare/ent/predicate"
	"bcare/ent/setting"
	"context"
	"fmt"
	"github.com/jinzhu/copier"
	"github.com/samber/lo"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type ListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewListLogic(ctx context.Context, svcCtx *svc.ServiceContext) ListLogic {
	return ListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ListLogic) List(req *types.SettingListRequest) (resp *types.SettingListResponse, err error) {
	var predicates []predicate.Setting
	if req.Category != "" {
		predicates = append(predicates, setting.CategoryEQ(req.Category))
	}
	if req.Name != "" {
		predicates = append(predicates, setting.NameEQ(req.Name))
	}

	// Truy vấn danh sách cài đặt với các điều kiện đã xác định
	settings, err := l.svcCtx.Ent.Setting.Query().Where(predicates...).All(l.ctx)
	if err != nil {
		l.Logger.Errorf("Failed to query settings: %v", err)
		return nil, fmt.Errorf("error querying settings: %w", err)
	}

	resp = new(types.SettingListResponse)
	// Sao chép dữ liệu từ settings sang resp.Settings
	resp.Settings = lo.Map(settings, func(setting *ent.Setting, _ int) types.Setting {
		s := types.Setting{}
		if copyErr := copier.Copy(&s, setting); copyErr != nil {
			l.Logger.Errorf("Failed to copy setting: %v", copyErr)
			return types.Setting{}
		}
		return s
	})

	return resp, nil
}
