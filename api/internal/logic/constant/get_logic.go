package constant

import (
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/api/model"
	"bcare/common/ctxdata"
	"bcare/ent/user"
	"context"
	"fmt"
	"github.com/zeromicro/go-zero/core/logx"
)

type GetLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetLogic {
	return &GetLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetLogic) Get(req *types.ConstantRequest) (*types.ConstantResponse, error) {
	resp := &types.ConstantResponse{
		CallSetting:               types.CallSetting{},
		AppointmentStatus:         types.AppointmentStatus,
		AppointmentTypes:          types.AppointmentTypes,
		AppointmentReminderStatus: types.AppointmentReminderStatus,
		ProductType:               types.ProductType,
		DealStatus:                types.DealStatus,
		DealUserRoles:             types.DealUserRoles,
		NoteTypes:                 types.NoteTypes,
		ProductPositons:           types.ProductPosition,
		AttachmentTypes:           types.AttachmentType,
		TransactionType:           types.TransactionTypes,
		TaskPriorities:            types.TaskPriorities,
		UserDepartmentPosition:    types.UserDepartmentPosition,
		TaskStates:                types.TaskStates,
		TaskTypes:                 types.TaskTypes,
		TaskAssignmentState:       types.TaskAssignmentState,
	}

	// Get current user with data
	currentUser, err := l.svcCtx.Ent.User.Query().
		WithData().
		Where(user.ID(ctxdata.GetUidFromCtx(l.ctx))).
		First(l.ctx)

	if err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	// Copy call settings if available
	if l.svcCtx.Setting != nil && l.svcCtx.Setting.Call != nil {
		resp.CallSetting = *l.svcCtx.Setting.Call
	}

	// Parse line call data
	if currentUser.Edges.Data != nil {
		lineCall, err := model.ParseLineCall(currentUser.Edges.Data)
		if err != nil {
			logx.Errorf("failed to parse line call: %v", err)
			// Continue execution even if parsing fails
		} else {
			resp.CallSetting.Password = lineCall.Password
			resp.CallSetting.LineId = lineCall.Line
		}
	}

	return resp, nil
}
