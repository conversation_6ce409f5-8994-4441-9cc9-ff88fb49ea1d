package attachment_data

import (
	"bcare/common/bquery"
	"bcare/common/ctxdata"
	"bcare/ent"
	"bcare/ent/attachmentdata"
	"context"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type SetLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewSetLogic(ctx context.Context, svcCtx *svc.ServiceContext) SetLogic {
	return SetLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *SetLogic) Set(req *types.SetAttachmentDataRequest) (resp *types.CommonResponse, err error) {
	var count int
	userID := ctxdata.GetUidFromCtx(l.ctx)

	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		// Tìm hoặc tạo mới AttachmentData
		attachmentData, err := tx.AttachmentData.Query().
			Where(
				attachmentdata.AttachmentID(req.AttachmentID),
				attachmentdata.Kind(req.Kind),
			).
			Only(l.ctx)

		if err != nil {
			if ent.IsNotFound(err) {
				// Nếu không tìm thấy, tạo mới
				createQuery := tx.AttachmentData.Create().
					SetAttachmentID(req.AttachmentID).
					SetKind(req.Kind).
					SetUserID(userID)

				if req.ParticipantID != 0 {
					createQuery = createQuery.SetParticipantID(req.ParticipantID)
				}

				// Chỉ set data nếu có dữ liệu
				if len(req.Data) > 0 {
					createQuery = createQuery.SetData(req.Data)
				} else if req.Key != "" {
					createQuery = createQuery.SetData(map[string]interface{}{req.Key: req.Value})
				}

				attachmentData, err = createQuery.Save(l.ctx)
				if err != nil {
					return types.ErrAttachmentData.Wrap(err, "failed to create attachment data")
				}
				count = 1
			} else {
				return types.ErrAttachmentData.Wrap(err, "failed to query attachment data")
			}
		} else {
			// Cập nhật data
			updateQuery := attachmentData.Update()

			// Chỉ cập nhật data nếu có dữ liệu mới
			if len(req.Data) > 0 {
				updateQuery = updateQuery.SetData(req.Data)
			} else if req.Key != "" {
				currentData := attachmentData.Data
				if currentData == nil {
					currentData = make(map[string]interface{})
				}
				currentData[req.Key] = req.Value
				updateQuery = updateQuery.SetData(currentData)
			}

			// Cập nhật user_id và participant_id nếu cần
			updateQuery = updateQuery.SetUserID(userID)
			if req.ParticipantID != 0 {
				updateQuery = updateQuery.SetParticipantID(req.ParticipantID)
			}

			// Chỉ lưu cập nhật nếu có thay đổi
			if len(updateQuery.Mutation().Fields()) > 0 {
				_, err = updateQuery.Save(l.ctx)
				if err != nil {
					return types.ErrAttachmentData.Wrap(err, "failed to update attachment data")
				}
				count = 1
			}
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return &types.CommonResponse{Count: count}, nil
}
