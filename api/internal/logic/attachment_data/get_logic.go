package attachment_data

import (
	"bcare/ent"
	"bcare/ent/attachmentdata"
	"context"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetLogic(ctx context.Context, svcCtx *svc.ServiceContext) GetLogic {
	return GetLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetLogic) Get(req *types.GetAttachmentDataRequest) (resp *types.AttachmentDataResponse, err error) {
	query := l.svcCtx.Ent.AttachmentData.Query().Where(attachmentdata.AttachmentID(req.AttachmentID))

	// Nếu kind được cung cấp, thêm điều kiện kind vào truy vấn
	if req.Kind != "" {
		query = query.Where(attachmentdata.Kind(req.Kind))
	}

	// Nếu participant_id được cung cấp, thêm điều kiện participant_id vào truy vấn
	if req.ParticipantID != 0 {
		query = query.Where(attachmentdata.ParticipantID(req.ParticipantID))
	}

	attachmentDataList, err := query.All(l.ctx)
	if err != nil {
		if ent.IsNotFound(err) {
			return &types.AttachmentDataResponse{Data: make(map[string]interface{})}, nil
		}
		return nil, types.ErrAttachmentData.Wrap(err, "failed to query attachment data")
	}

	result := make(map[string]interface{})

	// Trường hợp 1: Chỉ có attachment_id (và có thể có participant_id)
	if req.Kind == "" && req.Key == "" {
		for _, ad := range attachmentDataList {
			if ad.Data != nil {
				result[ad.Kind] = ad.Data
			} else {
				result[ad.Kind] = ad.ParticipantID
			}
		}
		return &types.AttachmentDataResponse{Data: result}, nil
	}

	// Trường hợp 2: Có attachment_id, kind (và có thể có participant_id)
	if req.Kind != "" && req.Key == "" {
		for _, ad := range attachmentDataList {
			if ad.Data != nil {
				return &types.AttachmentDataResponse{Data: ad.Data}, nil
			}
		}
		return &types.AttachmentDataResponse{Data: make(map[string]interface{})}, nil
	}

	// Trường hợp 3: Có attachment_id, kind và key (và có thể có participant_id)
	if req.Kind != "" && req.Key != "" {
		for _, ad := range attachmentDataList {
			if ad.Data != nil {
				if value, exists := ad.Data[req.Key]; exists {
					if mapValue, ok := value.(map[string]interface{}); ok {
						return &types.AttachmentDataResponse{Data: mapValue}, nil
					} else {
						// Nếu giá trị không phải là map, bọc nó trong một map
						return &types.AttachmentDataResponse{Data: map[string]interface{}{"raw": value}}, nil
					}
				}
			}
		}
	}

	return &types.AttachmentDataResponse{Data: make(map[string]interface{})}, nil
}
