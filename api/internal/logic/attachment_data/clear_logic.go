package attachment_data

import (
	"bcare/common/bquery"
	"bcare/ent"
	"bcare/ent/attachmentdata"
	"context"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type ClearLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewClearLogic(ctx context.Context, svcCtx *svc.ServiceContext) ClearLogic {
	return ClearLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ClearLogic) Clear(req *types.ClearAttachmentDataRequest) (resp *types.CommonResponse, err error) {
	var count int
	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		query := tx.AttachmentData.Query().Where(attachmentdata.AttachmentID(req.AttachmentID))

		if req.Kind != "" {
			query = query.Where(attachmentdata.Kind(req.Kind))
		}

		if req.ParticipantID != 0 {
			query = query.Where(attachmentdata.ParticipantID(req.ParticipantID))
		}

		attachmentDataList, err := query.All(l.ctx)
		if err != nil {
			return types.ErrAttachmentData.Wrap(err, "failed to query attachment data")
		}

		for _, attachmentData := range attachmentDataList {
			if req.Key != "" {
				// Clear specific key
				currentData := attachmentData.Data
				if currentData != nil {
					delete(currentData, req.Key)
					_, err = attachmentData.Update().SetData(currentData).Save(l.ctx)
					if err != nil {
						return types.ErrAttachmentData.Wrap(err, "failed to update attachment data")
					}
				}
			} else {
				// Clear all data for the given kind
				_, err = attachmentData.Update().ClearData().ClearParticipantID().Save(l.ctx)
				if err != nil {
					return types.ErrAttachmentData.Wrap(err, "failed to clear attachment data")
				}
			}
			count++
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return &types.CommonResponse{
		Count: count,
	}, nil
}
