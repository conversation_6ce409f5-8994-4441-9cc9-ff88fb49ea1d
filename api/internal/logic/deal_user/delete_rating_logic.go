package deal_user

import (
	"bcare/common/bquery"
	"bcare/ent"
	"context"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteRatingLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewDeleteRatingLogic(ctx context.Context, svcCtx *svc.ServiceContext) DeleteRatingLogic {
	return DeleteRatingLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DeleteRatingLogic) DeleteRating(req *types.DealUserRatingDeleteRequest) (resp *types.DealUserRating, err error) {
	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		dealUserRatingRecord, err := tx.DealUserRating.Get(l.ctx, req.Id)

		if err != nil && ent.IsNotFound(err) {
			return err
		}

		if dealUserRatingRecord == nil {
			logx.Errorf("deal user rating not found")
			return types.ErrPersonDelete
		}

		err = tx.DealUserRating.DeleteOne(dealUserRatingRecord).Exec(l.ctx)
		if err != nil {
			return types.ErrDealUserDelete.Wrap(err)
		}
		return nil
	})

	return
}
