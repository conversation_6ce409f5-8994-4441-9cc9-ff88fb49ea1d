package deal_user

import (
	"bcare/common/bquery"
	"bcare/ent"
	"context"
	"github.com/jinzhu/copier"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateRatingLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateRatingLogic(ctx context.Context, svcCtx *svc.ServiceContext) UpdateRatingLogic {
	return UpdateRatingLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateRatingLogic) UpdateRating(req *types.DealUserRatingUpdateRequest) (resp *types.DealUserRating, err error) {
	dealUserRatingRecord := new(ent.DealUserRating)
	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		dealUserRatingRecord, err = tx.DealUserRating.Get(l.ctx, req.Id)
		if err != nil {
			return types.ErrDealUserUpdate.Wrap(err, err.Error())
		}
		err = copier.Copy(&dealUserRatingRecord, req)
		if err != nil {
			return types.ErrDealUserUpdate.Wrap(err, err.Error())
		}

		dealUserRatingRecord, err = tx.DealUserRating.UpdateOneID(dealUserRatingRecord.ID).SetDealUserRating(dealUserRatingRecord).Save(l.ctx)
		if err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		return nil, types.ErrPersonUpdate.Wrap(err, err.Error())
	}

	resp = new(types.DealUserRating)
	_ = copier.Copy(&resp, dealUserRatingRecord)
	return resp, nil
}
