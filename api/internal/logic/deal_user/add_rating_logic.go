package deal_user

import (
	"bcare/common/bquery"
	"bcare/ent"
	"context"
	"github.com/jinzhu/copier"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type AddRatingLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAddRatingLogic(ctx context.Context, svcCtx *svc.ServiceContext) AddRatingLogic {
	return AddRatingLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AddRatingLogic) AddRating(req *types.DealUserRatingAddRequest) (resp *types.DealUserRating, err error) {
	dealUserRatingRecord := new(ent.DealUserRating)
	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		err = copier.Copy(&dealUserRatingRecord, req)
		if err != nil {
			logx.WithContext(l.ctx).Errorf("cannot copy request: %v", err)
			return types.ErrPersonAdd.Wrap(err, err.Error())
		}

		dealUserRatingRecord, err = tx.DealUserRating.Create().SetDealUserRating(dealUserRatingRecord).Save(l.ctx)
		if err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		return nil, types.ErrDealUserAdd.Wrap(err, err.Error())
	}

	resp = new(types.DealUserRating)
	_ = copier.Copy(&resp, dealUserRatingRecord)
	return resp, nil
}
