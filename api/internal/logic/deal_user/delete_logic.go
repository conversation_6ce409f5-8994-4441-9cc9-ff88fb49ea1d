package deal_user

import (
	"bcare/api/internal/auth"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/ent/dealuser"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewDeleteLogic(ctx context.Context, svcCtx *svc.ServiceContext) DeleteLogic {
	return DeleteLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DeleteLogic) Delete(req *types.DealUserDeleteRequest) (resp *types.DealUser, err error) {
	//kiểm tra xem có quyền ko
	if !l.svcCtx.Auth.DealUser.CanDelete(l.ctx, nil) {
		logx.WithContext(l.ctx).Infof("Bạn ko có quyền")
		return nil, auth.ErrDealUserDeleteDenied
	}

	dealUserRecord, err := l.svcCtx.Ent.DealUser.Query().Where(dealuser.DealID(req.DealId), dealuser.RoleEQ(dealuser.Role(req.Role))).First(l.ctx)

	if err != nil {
		return nil, nil
	}
	err = l.svcCtx.Ent.DealUser.DeleteOne(dealUserRecord).Exec(l.ctx)
	if err != nil {
		return nil, types.ErrDealUserDelete.Wrap(err, err.Error())
	}
	return
}
