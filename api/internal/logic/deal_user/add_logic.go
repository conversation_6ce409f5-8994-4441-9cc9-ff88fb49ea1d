package deal_user

import (
	"bcare/api/internal/auth"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/bquery"
	"bcare/ent"
	"bcare/ent/dealuser"
	"context"
	"github.com/jinzhu/copier"
	"github.com/zeromicro/go-zero/core/logx"
)

type AddLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAddLogic(ctx context.Context, svcCtx *svc.ServiceContext) AddLogic {
	return AddLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AddLogic) Add(req *types.DealUserAddRequest) (resp *types.DealUser, err error) {
	//kiểm tra xem có quyền ko
	if !l.svcCtx.Auth.DealUser.CanAdd(l.ctx, nil) {
		logx.WithContext(l.ctx).Infof("Bạn ko có quyền")
		return nil, auth.ErrDealUserAddDenied
	}

	dealUserRecord := new(ent.DealUser)
	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		dealUserRecord, err = tx.DealUser.Query().Where(dealuser.DealID(req.DealId), dealuser.RoleEQ(dealuser.Role(req.Role))).First(l.ctx)
		if dealUserRecord != nil && err == nil {
			dealUserRecord.UserID = req.UserId
			_, e := tx.DealUser.UpdateOneID(dealUserRecord.ID).SetDealUser(dealUserRecord).Save(l.ctx)
			if e != nil {
				return types.ErrDealUserUpdate.Wrap(e, "Error updating existing DealUser")
			}
		} else {
			dealUserRecord = new(ent.DealUser)
			err = copier.Copy(&dealUserRecord, req)
			if err != nil {
				logx.WithContext(l.ctx).Errorf("cannot copy request: %v", err)
				return types.ErrDealUserAdd.Wrap(err, err.Error())
			}
			dealUserRecord, err = tx.DealUser.Create().SetDealUser(dealUserRecord).Save(l.ctx)
			if err != nil {
				return types.ErrDealUserAdd.Wrap(err, err.Error())
			}
		}

		return nil
	})

	if err != nil {
		return nil, types.ErrDealUserAdd.Wrap(err, "Error processing DealUser addition")
	}

	resp = new(types.DealUser)
	_ = copier.Copy(&resp, dealUserRecord)
	return resp, nil
}
