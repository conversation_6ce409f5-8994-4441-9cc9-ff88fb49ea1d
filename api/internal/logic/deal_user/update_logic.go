package deal_user

import (
	"bcare/api/internal/auth"
	"context"
	"github.com/jinzhu/copier"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateLogic(ctx context.Context, svcCtx *svc.ServiceContext) UpdateLogic {
	return UpdateLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateLogic) Update(req *types.DealUserUpdateRequest) (resp *types.DealUser, err error) {
	dealUserRecord, err := l.svcCtx.Ent.DealUser.Get(l.ctx, req.Id)
	if err != nil {
		return nil, types.ErrDealUserNotFound.Wrap(err, req.Id)
	}

	//kiểm tra xem có quyền ko
	if !l.svcCtx.Auth.DealUser.CanUpdate(l.ctx, nil) {
		logx.WithContext(l.ctx).Infof("Bạn ko có quyền")
		return nil, auth.ErrDealUserUpdateDenied
	}

	dealUserRecord.UserID = req.UserId
	dealUserRecord.Point = req.Point
	dealUserRecord, err = l.svcCtx.Ent.DealUser.UpdateOneID(dealUserRecord.ID).SetDealUser(dealUserRecord).Save(l.ctx)
	if err != nil {
		return nil, types.ErrDealUserUpdate.Wrap(err, err.Error())
	}

	resp = new(types.DealUser)
	_ = copier.Copy(&resp, dealUserRecord)
	return resp, nil
}
