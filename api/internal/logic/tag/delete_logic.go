package tag

import (
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/berr"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewDeleteLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteLogic {
	return &DeleteLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DeleteLogic) Delete(req *types.TagDeleteRequest) (err error) {
	existingTag, err := l.svcCtx.TagModel.GetTag(l.ctx, req.Id)
	if err != nil {
		return err // Reverted: Original error handling
	}

	if !l.svcCtx.Auth.Tag.CanDelete(l.ctx, existingTag) {
		return berr.ErrPermissionDenied.Wrap(nil)
	}

	err = l.svcCtx.TagModel.DeleteTag(l.ctx, req.Id)
	if err != nil {
		return err // Reverted: Original error handling
	}

	return nil
}
