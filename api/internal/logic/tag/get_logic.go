package tag

import (
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/berr"
	"bcare/common/cast"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetLogic {
	return &GetLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetLogic) Get(req *types.TagGetRequest) (resp *types.TagResponse, err error) {
	tagOutput, err := l.svcCtx.TagModel.GetTag(l.ctx, req.Id)
	if err != nil {
		return nil, err
	}

	if !l.svcCtx.Auth.Tag.CanGet(l.ctx, tagOutput) {
		return nil, berr.ErrPermissionDenied.Wrap(nil)
	}

	resp = new(types.TagResponse)
	err = cast.ConvertViaJson(resp, tagOutput)
	if err != nil {
		return nil, err
	}

	return resp, nil
}
