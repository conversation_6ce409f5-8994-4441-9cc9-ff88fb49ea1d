This file is a merged representation of the entire codebase, combined into a single document by <PERSON>omix.

<file_summary>
    This section contains a summary of this file.

    <purpose>
        This file contains a packed representation of the entire repository's contents.
        It is designed to be easily consumable by AI systems for analysis, code review,
        or other automated processes.
    </purpose>

    <file_format>
        The content is organized as follows:
        1. This summary section
        2. Repository information
        3. Directory structure
        4. Repository files, each consisting of:
        - File path as an attribute
        - Full contents of the file
    </file_format>

    <usage_guidelines>
        - This file should be treated as read-only. Any changes should be made to the
        original repository files, not this packed version.
        - When processing this file, use the file path to distinguish
        between different files in the repository.
        - Be aware that this file may contain sensitive information. Handle it with
        the same level of security as you would the original repository.
    </usage_guidelines>

    <notes>
        - Some files may have been excluded based on .gitignore rules and Repomix's configuration
        - Binary files are not included in this packed representation. Please refer to the Repository Structure section for a complete list of file paths, including binary files
        - Files matching patterns in .gitignore are excluded
        - Files matching default ignore patterns are excluded
        - Files are sorted by Git change count (files with more changes are at the bottom)
    </notes>

    <additional_info>

    </additional_info>

</file_summary>

<directory_structure>
add_logic.go
delete_logic.go
get_logic.go
list_logic.go
update_logic.go
</directory_structure>

<files>
This section contains the contents of the repository's files.

<file path="add_logic.go">
    package tag

    import (
    "bcare/api/internal/model"
    "bcare/api/internal/svc"
    "bcare/api/internal/types"
    "bcare/common/berr"
    "bcare/common/cast"
    "context"

    "github.com/zeromicro/go-zero/core/logx"
    )

    type AddLogic struct {
    logx.Logger
    ctx    context.Context
    svcCtx *svc.ServiceContext
    }

    func NewAddLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AddLogic {
    return &AddLogic{
    Logger: logx.WithContext(ctx),
    ctx:    ctx,
    svcCtx: svcCtx,
    }
    }

    func (l *AddLogic) Add(req *types.TagAddRequest) (resp *types.TagResponse, err error) {
    if !l.svcCtx.Auth.Tag.CanAdd(l.ctx) {
    return nil, berr.ErrPermissionDenied.Wrap(nil)
    }

    input := new(model.CreateTagInput)
    err = cast.ReqToModelInput(input, req)
    if err != nil {
    return nil, err
    }

    tagOutput, err := l.svcCtx.TagModel.CreateTag(l.ctx, input)
    if err != nil {
    return nil, err
    }

    resp = new(types.TagResponse)
    err = cast.ConvertViaJson(resp, tagOutput)
    if err != nil {
    return nil, err
    }

    return resp, nil
    }
</file>

<file path="delete_logic.go">
    package tag

    import (
    "bcare/api/internal/svc"
    "bcare/api/internal/types"
    "bcare/common/berr"
    "context"

    "github.com/zeromicro/go-zero/core/logx"
    )

    type DeleteLogic struct {
    logx.Logger
    ctx    context.Context
    svcCtx *svc.ServiceContext
    }

    func NewDeleteLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteLogic {
    return &DeleteLogic{
    Logger: logx.WithContext(ctx),
    ctx:    ctx,
    svcCtx: svcCtx,
    }
    }

    func (l *DeleteLogic) Delete(req *types.TagDeleteRequest) (err error) {
    existingTag, err := l.svcCtx.TagModel.GetTag(l.ctx, req.Id)
    if err != nil {
    return err // Reverted: Original error handling
    }

    if !l.svcCtx.Auth.Tag.CanDelete(l.ctx, existingTag) {
    return berr.ErrPermissionDenied.Wrap(nil)
    }

    err = l.svcCtx.TagModel.DeleteTag(l.ctx, req.Id)
    if err != nil {
    return err // Reverted: Original error handling
    }

    return nil
    }
</file>

<file path="get_logic.go">
    package tag

    import (
    "bcare/api/internal/svc"
    "bcare/api/internal/types"
    "bcare/common/berr"
    "bcare/common/cast"
    "context"

    "github.com/zeromicro/go-zero/core/logx"
    )

    type GetLogic struct {
    logx.Logger
    ctx    context.Context
    svcCtx *svc.ServiceContext
    }

    func NewGetLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetLogic {
    return &GetLogic{
    Logger: logx.WithContext(ctx),
    ctx:    ctx,
    svcCtx: svcCtx,
    }
    }

    func (l *GetLogic) Get(req *types.TagGetRequest) (resp *types.TagResponse, err error) {
    tagOutput, err := l.svcCtx.TagModel.GetTag(l.ctx, req.Id)
    if err != nil {
    return nil, err
    }

    if !l.svcCtx.Auth.Tag.CanGet(l.ctx, tagOutput) {
    return nil, berr.ErrPermissionDenied.Wrap(nil)
    }

    resp = new(types.TagResponse)
    err = cast.ConvertViaJson(resp, tagOutput)
    if err != nil {
    return nil, err
    }

    return resp, nil
    }
</file>

<file path="list_logic.go">
    package tag

    import (
    "bcare/api/internal/model"
    "bcare/api/internal/svc"
    "bcare/api/internal/types"
    "bcare/common/berr"
    "bcare/common/cast"
    "context"
    "github.com/zeromicro/go-zero/core/logx"
    )

    type ListLogic struct {
    logx.Logger
    ctx    context.Context
    svcCtx *svc.ServiceContext
    }

    func NewListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ListLogic {
    return &ListLogic{
    Logger: logx.WithContext(ctx),
    ctx:    ctx,
    svcCtx: svcCtx,
    }
    }

    func (l *ListLogic) List(req *types.TagListRequest) (resp *types.TagListResponse, err error) {
    if !l.svcCtx.Auth.Tag.CanList(l.ctx) {
    return nil, berr.ErrPermissionDenied.Wrap(nil)
    }

    input := new(model.ListTagsInput)
    err = cast.ReqToModelInput(input, req)
    if err != nil {
    return nil, err
    }

    listOutput, err := l.svcCtx.TagModel.ListTags(l.ctx, *input)
    if err != nil {
    return nil, err
    }

    resp = &types.TagListResponse{
    Tags:  make([]types.TagResponse, 0, len(listOutput.Tags)),
    Total: listOutput.Total,
    }

    err = cast.ConvertViaJson(&resp.Tags, listOutput.Tags)
    if err != nil {
    return nil, err
    }

    return resp, nil
    }
</file>

<file path="update_logic.go">
    package tag

    import (
    "bcare/api/internal/model"
    "bcare/api/internal/svc"
    "bcare/api/internal/types"
    "bcare/common/berr"
    "bcare/common/cast"
    "context"

    "github.com/zeromicro/go-zero/core/logx"
    )

    type UpdateLogic struct {
    logx.Logger
    ctx    context.Context
    svcCtx *svc.ServiceContext
    }

    func NewUpdateLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateLogic {
    return &UpdateLogic{
    Logger: logx.WithContext(ctx),
    ctx:    ctx,
    svcCtx: svcCtx,
    }
    }

    func (l *UpdateLogic) Update(req *types.TagUpdateRequest) (resp *types.TagResponse, err error) {
    existingTag, err := l.svcCtx.TagModel.GetTag(l.ctx, req.Id)
    if err != nil {
    return nil, err // Reverted: Original error handling
    }

    if !l.svcCtx.Auth.Tag.CanUpdate(l.ctx, existingTag) {
    return nil, berr.ErrPermissionDenied.Wrap(nil)
    }

    input := new(model.UpdateTagInput)
    err = cast.ReqToModelInput(input, req)
    if err != nil {
    return nil, err // Reverted: Original error handling
    }

    tagOutput, err := l.svcCtx.TagModel.UpdateTag(l.ctx, input)
    if err != nil {
    return nil, err // Reverted: Original error handling
    }

    resp = new(types.TagResponse)
    err = cast.ConvertViaJson(resp, tagOutput)
    if err != nil {
    return nil, err
    }

    return resp, nil
    }
</file>

</files>
