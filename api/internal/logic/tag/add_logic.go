package tag

import (
	"bcare/api/internal/dto"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/berr"
	"bcare/common/cast"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type AddLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAddLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AddLogic {
	return &AddLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AddLogic) Add(req *types.TagAddRequest) (resp *types.TagResponse, err error) {
	if !l.svcCtx.Auth.Tag.CanAdd(l.ctx) {
		return nil, berr.ErrPermissionDenied.Wrap(nil)
	}

	input := new(dto.CreateTagInput)
	err = cast.ReqToModelInput(input, req)
	if err != nil {
		return nil, err
	}

	tagOutput, err := l.svcCtx.TagModel.CreateTag(l.ctx, input)
	if err != nil {
		return nil, err
	}

	resp = new(types.TagResponse)
	err = cast.ConvertViaJson(resp, tagOutput)
	if err != nil {
		return nil, err
	}

	return resp, nil
}
