package tag

import (
	"bcare/api/internal/dto"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/berr"
	"bcare/common/cast"
	"context"
	"github.com/zeromicro/go-zero/core/logx"
)

type ListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ListLogic {
	return &ListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ListLogic) List(req *types.TagListRequest) (resp *types.TagListResponse, err error) {
	if !l.svcCtx.Auth.Tag.CanList(l.ctx) {
		return nil, berr.ErrPermissionDenied.Wrap(nil)
	}

	input := new(dto.ListTagsInput)
	err = cast.ReqToModelInput(input, req)
	if err != nil {
		return nil, err
	}

	listOutput, err := l.svcCtx.TagModel.ListTags(l.ctx, *input)
	if err != nil {
		return nil, err
	}

	resp = &types.TagListResponse{
		Tags:  make([]types.TagResponse, 0, len(listOutput.Tags)),
		Total: listOutput.Total,
	}

	err = cast.ConvertViaJson(&resp.Tags, listOutput.Tags)
	if err != nil {
		return nil, err
	}

	return resp, nil
}
