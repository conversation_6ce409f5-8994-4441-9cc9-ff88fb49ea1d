package tag

import (
	"bcare/api/internal/dto"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/berr"
	"bcare/common/cast"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateLogic {
	return &UpdateLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateLogic) Update(req *types.TagUpdateRequest) (resp *types.TagResponse, err error) {
	existingTag, err := l.svcCtx.TagModel.GetTag(l.ctx, req.Id)
	if err != nil {
		return nil, err // Reverted: Original error handling
	}

	if !l.svcCtx.Auth.Tag.CanUpdate(l.ctx, existingTag) {
		return nil, berr.ErrPermissionDenied.Wrap(nil)
	}

	input := new(dto.UpdateTagInput)
	err = cast.ReqToModelInput(input, req)
	if err != nil {
		return nil, err // Reverted: Original error handling
	}

	tagOutput, err := l.svcCtx.TagModel.UpdateTag(l.ctx, input)
	if err != nil {
		return nil, err // Reverted: Original error handling
	}

	resp = new(types.TagResponse)
	err = cast.ConvertViaJson(resp, tagOutput)
	if err != nil {
		return nil, err
	}

	return resp, nil
}
