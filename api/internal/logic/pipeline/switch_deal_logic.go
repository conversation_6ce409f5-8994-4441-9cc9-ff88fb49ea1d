package pipeline

import (
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/bquery"
	cast2 "bcare/common/cast"
	"bcare/ent"
	"bcare/ent/track"
	"context"
	"github.com/zeromicro/go-zero/core/logx"
)

type SwitchDealLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewSwitchDealLogic(ctx context.Context, svcCtx *svc.ServiceContext) SwitchDealLogic {
	return SwitchDealLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *SwitchDealLogic) SwitchDeal(req *types.SwitchDealRequest) (resp *types.SwitchDealResponse, err error) {
	// Khởi tạo transaction
	trackRecord := new(ent.Track)
	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {

		// Query track chưa end tương ướng với track_id truyền lên
		latestTrack, err := tx.Track.Query().Where(track.ID(req.TrackId), track.EndIsNil()).First(l.ctx)
		if err != nil {
			return types.ErrTrackNotFound.Wrap(err)
		}

		// Kiểm tra pipeline_id nếu có
		if req.PipelineId != 0 && latestTrack.PipelineID != nil && req.PipelineId != *latestTrack.PipelineID {
			return types.ErrInvalidPipelineID.Wrap(nil)
		}

		// Cập nhật deal_id của track mới nhất
		_, err = tx.Track.UpdateOneID(latestTrack.ID).SetDealID(req.NewId).Save(l.ctx)
		if err != nil {
			return types.ErrTrackUpdate.Wrap(err)
		}

		// Lấy thông tin mới nhất của track
		trackRecord, err = l.svcCtx.Ent.Track.Query().
			WithStage().
			WithDeal().
			WithPerson(func(q *ent.PersonQuery) {
				q.WithIssues()
			}).Where(
			track.ID(latestTrack.ID),
			track.EndIsNil(),
		).First(l.ctx)

		if err != nil {
			return types.ErrTrackNotFound.Wrap(err)
		}

		return nil
	})

	if err != nil {
		return nil, types.ErrSwitchDeal.Wrap(err)
	}

	resp = new(types.SwitchDealResponse)
	_ = cast2.ConvertViaJson(&resp, trackRecord)
	return resp, nil
}
