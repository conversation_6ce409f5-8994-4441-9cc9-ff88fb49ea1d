package pipeline

import (
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/bquery"
	cast2 "bcare/common/cast"
	"bcare/ent"
	"bcare/ent/stage"
	"context"
	"fmt"
	"github.com/samber/lo"
	"time"

	"github.com/zeromicro/go-zero/core/logx"
)

type ListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewListLogic(ctx context.Context, svcCtx *svc.ServiceContext) ListLogic {
	return ListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ListLogic) List(req *types.PipelineListRequest) (resp *types.PipelineListResponse, err error) {
	limit, offset := bquery.Paging(req.PageSize, req.Page)
	order := bquery.Ordering(req.OrderBy)
	query := l.svcCtx.Ent.Pipeline.Query().Order(order)
	total, _ := query.Count(l.ctx)

	cacheKey := generateCacheKey(req)

	// get value from cache
	cached, ok := svc.Cache.Get(cacheKey)
	if ok {
		if cachedResp, ok := cached.(*types.PipelineListResponse); ok {
			return cachedResp, nil
		}
	}

	pipelines, e := query.WithStages(func(query *ent.StageQuery) {
		query.WithChildren(func(query *ent.StageQuery) {
			query.Order(stage.ByOrderNumber())
		}).Order(stage.ByOrderNumber())
	}).Limit(limit).Offset(offset).All(l.ctx)

	if e != nil {
		err = types.ErrPipelineGetList.Wrap(e)
		return
	}
	if len(pipelines) == 0 {
		err = types.ErrPipelineNotFound
		return
	}

	resp = new(types.PipelineListResponse)
	resp.Pipelines = lo.Map(pipelines, func(pipeline *ent.Pipeline, _ int) types.PipelineResponse {
		p := types.PipelineResponse{}
		_ = cast2.ConvertViaJson(&p, pipeline)
		return p
	})

	resp.Total = total

	svc.Cache.Set(cacheKey, resp, 24*time.Hour)

	return resp, err
}

func generateCacheKey(req *types.PipelineListRequest) string {
	return fmt.Sprintf("PipelineList:%d:%d:%s",
		req.Page,
		req.PageSize,
		req.OrderBy)
}
