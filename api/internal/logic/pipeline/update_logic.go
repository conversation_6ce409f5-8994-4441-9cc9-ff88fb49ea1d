package pipeline

import (
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/bquery"
	"bcare/ent"
	"context"
	"github.com/jinzhu/copier"

	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateLogic(ctx context.Context, svcCtx *svc.ServiceContext) UpdateLogic {
	return UpdateLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateLogic) Update(req *types.PipelineUpdateRequest) (resp *types.PipelineResponse, err error) {
	pipeline, e := l.svcCtx.Ent.Pipeline.Get(l.ctx, req.Id)

	if e != nil && !ent.IsNotFound(e) {
		err = types.ErrPipelineUpdate.Wrap(e)
		return
	}

	if pipeline == nil {
		err = types.ErrPipelineUpdate
		return
	}

	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		err = copier.Copy(&pipeline, req)
		pipeline, e = tx.Pipeline.UpdateOneID(pipeline.ID).
			SetPipeline(pipeline, req.Modified...).
			Save(l.ctx)
		if e != nil {
			err = types.ErrPipelineUpdate.Wrap(e)
			return err
		}
		return nil
	})

	if err != nil {
		err = types.ErrPipelineUpdate.Wrap(err)
		return
	}

	resp = new(types.PipelineResponse)
	_ = copier.Copy(resp, pipeline)

	return resp, err
}
