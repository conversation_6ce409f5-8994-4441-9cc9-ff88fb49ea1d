package pipeline

import (
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/ent"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewDeleteLogic(ctx context.Context, svcCtx *svc.ServiceContext) DeleteLogic {
	return DeleteLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DeleteLogic) Delete(req *types.PipelineDeleteRequest) (resp *types.PipelineResponse, err error) {
	pipeline, e := l.svcCtx.Ent.Pipeline.Get(l.ctx, req.Id)

	if e != nil && !ent.IsNotFound(e) {
		err = types.ErrPipelineNotFound.Wrap(e)
		return
	}

	if pipeline == nil {
		err = types.ErrPipelineNotFound
		return
	}

	e = l.svcCtx.Ent.Pipeline.DeleteOne(pipeline).Exec(l.ctx)
	if e != nil {
		err = types.ErrPipelineUpdate.Wrap(e)
		return
	}

	return
}
