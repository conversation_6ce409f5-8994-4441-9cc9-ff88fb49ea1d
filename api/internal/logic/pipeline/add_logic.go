package pipeline

import (
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/bquery"
	"bcare/ent"
	"context"
	"github.com/jinzhu/copier"

	"github.com/zeromicro/go-zero/core/logx"
)

type AddLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAddLogic(ctx context.Context, svcCtx *svc.ServiceContext) AddLogic {
	return AddLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AddLogic) Add(req *types.PipelineAddRequest) (resp *types.PipelineResponse, err error) {
	pipeline := new(ent.Pipeline)
	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		_ = copier.Copy(pipeline, req)

		pipeline, err = tx.Pipeline.Create().
			SetName(pipeline.Name).
			SetDescription(pipeline.Description).
			SetStatus(pipeline.Status).
			Save(l.ctx)
		if err != nil {
			err = types.ErrPipelineAdd.Wrap(err)
			return err
		}
		return nil
	})

	if err != nil {
		err = types.ErrPipelineAdd.Wrap(err)
		return
	}
	resp = new(types.PipelineResponse)
	_ = copier.Copy(resp, pipeline)

	return resp, err
}
