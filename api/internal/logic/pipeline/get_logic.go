package pipeline

import (
	"bcare/ent"
	"context"
	"github.com/jinzhu/copier"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetLogic(ctx context.Context, svcCtx *svc.ServiceContext) GetLogic {
	return GetLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetLogic) Get(req *types.PipelineGetRequest) (resp *types.PipelineResponse, err error) {
	pipeline, e := l.svcCtx.Ent.Pipeline.Get(l.ctx, req.Id)

	if e != nil && !ent.IsNotFound(e) {
		err = types.ErrPipelineNotFound.Wrap(e)
		return
	}

	if pipeline == nil {
		err = types.ErrPipelineNotFound
		return
	}

	resp = new(types.PipelineResponse)
	_ = copier.Copy(resp, pipeline)

	return
}
