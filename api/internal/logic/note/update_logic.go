package note

import (
	"bcare/api/internal/dto"
	"bcare/api/model"
	"bcare/ent/issue"
	"context"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/berr"
	"bcare/common/cast"

	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateLogic {
	return &UpdateLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateLogic) Update(req *types.NoteUpdateRequest) (resp *types.Note, err error) {
	existingNoteModelOutput, err := l.svcCtx.NoteModel.GetNote(l.ctx, req.Id)
	if err != nil {
		return nil, err
	}

	if !l.svcCtx.Auth.Note.CanUpdate(l.ctx, existingNoteModelOutput) {
		return nil, berr.ErrPermissionDenied
	}

	updateInput := new(dto.UpdateNoteInput)
	err = cast.ReqToModelInput(updateInput, req)
	if err != nil {
		return nil, err
	}

	updatedNoteModelOutput, err := l.svcCtx.NoteModel.UpdateNote(l.ctx, updateInput)
	if err != nil {
		return nil, err
	}

	if updatedNoteModelOutput.Type == types.NoteTypeComplaint && existingNoteModelOutput.Type != updatedNoteModelOutput.Type {
		_ = model.CreateIssue(l.ctx, l.svcCtx.Ent, *updatedNoteModelOutput.PersonID, issue.TypeComplain.String(), updatedNoteModelOutput.Body, "Phàn nàn")
	}
	resp = new(types.Note)
	err = cast.ConvertViaJson(resp, updatedNoteModelOutput)
	if err != nil {
		return nil, err
	}

	return resp, nil
}
