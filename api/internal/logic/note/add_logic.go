package note

import (
	"bcare/api/internal/dto"
	"bcare/api/model"
	"bcare/ent/issue"
	"context"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/berr"
	"bcare/common/cast"

	"github.com/zeromicro/go-zero/core/logx"
)

type AddLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAddLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AddLogic {
	return &AddLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AddLogic) Add(req *types.NoteAddRequest) (resp *types.Note, err error) {
	if !l.svcCtx.Auth.Note.CanAdd(l.ctx) {
		return nil, berr.ErrPermissionDenied
	}

	input := new(dto.CreateNoteInput)
	err = cast.ReqToModelInput(input, req)
	if err != nil {
		return nil, err
	}

	noteModelOutput, err := l.svcCtx.NoteModel.CreateNote(l.ctx, input)
	if err != nil {
		return nil, err
	}

	if noteModelOutput.Type == types.NoteTypeComplaint {
		_ = model.CreateIssue(l.ctx, l.svcCtx.Ent, *noteModelOutput.PersonID, issue.TypeComplain.String(), noteModelOutput.Body, "Phàn nàn")
	}
	resp = new(types.Note)
	err = cast.ConvertViaJson(resp, noteModelOutput)
	if err != nil {
		return nil, err
	}

	return resp, nil
}
