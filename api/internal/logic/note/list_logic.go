package note

import (
	"bcare/api/internal/dto"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/berr"
	"bcare/common/cast"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type ListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ListLogic {
	return &ListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ListLogic) List(req *types.NoteListRequest) (resp *types.NoteListResponse, err error) {
	if !l.svcCtx.Auth.Note.CanList(l.ctx) {
		return nil, berr.ErrPermissionDenied
	}

	listInput := new(dto.ListNotesInput)
	err = cast.ReqToModelInput(listInput, req)
	if err != nil {
		return nil, err
	}
	listModelOutput, err := l.svcCtx.NoteModel.ListNotes(l.ctx, *listInput)
	if err != nil {
		return nil, err
	}

	resp = &types.NoteListResponse{
		Notes: make([]types.NoteResponese, 0, len(listModelOutput.Notes)),
		Total: listModelOutput.Total,
	}

	if req.PageSize > 0 {
		resp.TotalPage = (listModelOutput.Total + req.PageSize - 1) / req.PageSize
	} else if listModelOutput.Total > 0 {
		resp.TotalPage = 1
	} else {
		resp.TotalPage = 0
	}

	err = cast.ConvertViaJson(&resp.Notes, listModelOutput.Notes)
	if err != nil {
		return nil, err
	}

	return resp, nil
}
