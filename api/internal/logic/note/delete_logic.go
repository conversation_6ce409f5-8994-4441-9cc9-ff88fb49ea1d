package note

import (
	"bcare/api/model"
	"bcare/ent/issue"
	"context"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/berr"
	"bcare/common/cast"

	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewDeleteLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteLogic {
	return &DeleteLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DeleteLogic) Delete(req *types.NoteDeleteRequest) (resp *types.Note, err error) {
	noteToDeleteModelOutput, modelErr := l.svcCtx.NoteModel.GetNote(l.ctx, req.Id)
	if modelErr != nil {
		return nil, modelErr
	}

	if !l.svcCtx.Auth.Note.CanDelete(l.ctx, noteToDeleteModelOutput) {
		return nil, berr.ErrPermissionDenied
	}

	err = l.svcCtx.NoteModel.DeleteNote(l.ctx, req.Id)
	if err != nil {
		return nil, err
	}

	resp = new(types.Note)
	err = cast.ConvertViaJson(resp, noteToDeleteModelOutput)
	if err != nil {
		l.Logger.Errorf("DeleteLogic: failed to convert deleted note data to response: %v, for note ID: %d", err, req.Id)
		return nil, err
	}

	if noteToDeleteModelOutput.Type == types.NoteTypeComplaint {
		_ = model.DeleteIssue(l.ctx, l.svcCtx.Ent, *noteToDeleteModelOutput.PersonID, issue.TypeComplain.String())
	}

	return resp, nil
}
