package track

import (
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/bquery"
	"bcare/common/butils"
	cast2 "bcare/common/cast"
	"bcare/ent"
	"bcare/ent/schema"
	"context"
	"fmt"
	"github.com/jinzhu/copier"
	"github.com/samber/lo"
	"github.com/xuri/excelize/v2"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/logx"
)

type QueryLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewQueryLogic(ctx context.Context, svcCtx *svc.ServiceContext) QueryLogic {
	return QueryLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *QueryLogic) Query(req *types.TrackDynamicQuery) (res *types.DynamicQueryResponse, err error) {
	dQuery := &bquery.DynamicQuery{}
	err = copier.Copy(dQuery, req)
	if err != nil {
		logx.WithContext(l.ctx).Errorf("unable to copy request: %v", err)
		return nil, err
	}

	query := l.svcCtx.Ent.NewTracksReportView.Query().Modify(bquery.ApplyDynamicQuery(dQuery))

	result := map[string]any{}
	aliases := make(map[string]bool)
	for _, agg := range req.Aggregations {
		if agg.Alias != "" {
			aliases[agg.Alias] = true
		}
	}

	skipSoftDelete := schema.SkipSoftDelete(l.ctx)

	if !req.Export {
		if req.Limit <= 1 {
			r, err := query.First(skipSoftDelete)
			if err != nil && !ent.IsNotFound(err) {
				logx.WithContext(l.ctx).Errorf("unable to fetch Task record: %v", err)
				return nil, err
			}

			if r != nil {
				for alias := range aliases {
					result[alias], _ = r.Value(alias)
				}
			}
		} else {
			rows, err := query.All(skipSoftDelete)
			if err != nil {
				logx.WithContext(l.ctx).Errorf("unable to fetch Task records: %v", err)
				return nil, err
			}

			resultList := make([]map[string]any, len(rows))
			for i, r := range rows {
				rowResult := make(map[string]any)
				rowResult["record"] = cast2.Atom(&rowResult, r)
				for alias := range aliases {
					rowResult[alias], _ = r.Value(alias)
				}
				resultList[i] = rowResult
			}
			result["rows"] = resultList
		}
	} else {
		rows, err := query.All(skipSoftDelete)
		if err != nil {
			logx.WithContext(l.ctx).Errorf("unable to fetch payment records: %v", err)
			return res, err
		}
		records := make([]types.NewTrackReportResponse, len(rows))
		records = lo.Map(rows, func(r *ent.NewTracksReportView, _ int) types.NewTrackReportResponse {
			a := types.NewTrackReportResponse{}
			err = cast2.EntViewToResp(&a, r)
			return a
		})
		//
		fileUrl, err := l.exportToExcel(records)
		if err != nil {
			return nil, fmt.Errorf("failed to export excel: %v", err)
		}
		result["file_url"] = fileUrl
	}

	res = new(types.DynamicQueryResponse)
	res.Result = result

	return
}

func (l *QueryLogic) exportToExcel(records []types.NewTrackReportResponse) (string, error) {
	f := excelize.NewFile()
	defer f.Close()

	sheetName := "Track Report"
	f.SetSheetName("Sheet1", sheetName)

	// Set headers
	headers := []string{
		"NGÀY ĐẾN TƯ VẤN", "TÊN KHÁCH HÀNG", "ID", "NGUỒN KH", "SĐT",
		"TÊN DEAL", "TELESALE", "BÁC SĨ TƯ VẤN", "ĐIỂM", "BÁC SĨ NIỀNG", "TRỢ LÝ BÁC SĨ", "ĐIỂM",
		"TÌNH TRẠNG CHỐT SALE", "NHÓM DEAL", "TÊN NGƯỜI GIỚI THIỆU", "ID NGƯỜI GIỚI THIỆU",
	}

	// Set header style
	headerStyle, _ := f.NewStyle(&excelize.Style{
		Font:      &excelize.Font{Bold: true},
		Fill:      excelize.Fill{Type: "pattern", Color: []string{"#CCCCCC"}, Pattern: 1},
		Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center"},
	})

	// Write headers
	for i, header := range headers {
		cell := fmt.Sprintf("%c1", 'A'+i)
		f.SetCellValue(sheetName, cell, header)
		f.SetCellStyle(sheetName, cell, cell, headerStyle)
	}

	// Write data
	for i, record := range records {
		rowIndex := i + 2 // Start from row 2

		// Format date from Begin field (convert to dd/mm/yyyy)
		var formattedDate string
		if record.Begin != "" {
			if date, err := time.Parse(time.RFC3339, record.Begin); err == nil {
				formattedDate = date.Format("02/01/2006")
			} else {
				formattedDate = record.Begin
			}
		}

		// Get person info
		var personName, personCode, personPhone string
		if record.Person != nil {
			personName = record.Person.FullName
			personPhone = butils.MaskPhone(record.Person.Phone, l.ctx, l.svcCtx.Enforcer)

			if record.Person.PersonField.Code != nil {
				personCode = *record.Person.PersonField.Code
			}
		}

		// Get deal info
		var dealName, bsTvName, bsTvScore, bsNiengName, troLyName, troLyScore, tagNames string
		if record.Deal != nil {
			dealName = record.Deal.Name

			// Get tag names
			var tagList []string
			for _, tag := range record.Deal.Tags {
				tagList = append(tagList, tag.Name)
			}
			tagNames = strings.Join(tagList, ", ")

			// Get assignment info
			for _, assignment := range record.Deal.Assignments {
				switch assignment.Role {
				case "consultant_doctor":
					bsTvName = assignment.Name
					bsTvScore = fmt.Sprintf("%.1f", assignment.Point["averageScore"])
				case "treatment_doctor":
					bsNiengName = assignment.Name
				case "doctor_assistant":
					troLyName = assignment.Name
					troLyScore = fmt.Sprintf("%.1f", assignment.Point["averageScore"])
				}
			}
		}

		// Get referrer info
		var referrerName, referrerCode string
		if record.Referrer != nil {
			referrerName = record.Referrer.FullName

			if record.Referrer.PersonField.Code != nil {
				referrerCode = *record.Referrer.PersonField.Code
			}
		}

		// Get sale user name
		var saleUserName string
		if record.SaleUser != nil {
			saleUserName = record.SaleUser.Name
		}

		// Row data
		rowData := []interface{}{
			formattedDate,        // NGÀY ĐẾN TƯ VẤN
			personName,           // TÊN KHÁCH HÀNG
			personCode,           // ID
			record.PersonSource,  // NGUỒN KH
			personPhone,          // SĐT
			dealName,             // TÊN DEAL
			saleUserName,         // TELESALE
			bsTvName,             // BÁC SĨ TƯ VẤN
			bsTvScore,            // ĐIỂM BÁC SĨ TƯ VẤN
			bsNiengName,          // BÁC SĨ NIỀNG
			troLyName,            // TRỢ LÝ BÁC SĨ
			troLyScore,           // ĐIỂM TRỢ LÝ BÁC SĨ
			record.DealStageName, // TÌNH TRẠNG CHỐT SALE
			tagNames,             // NHÓM DEAL
			referrerName,         // TÊN NGƯỜI GIỚI THIỆU
			referrerCode,         // ID NGƯỜI GIỚI THIỆU
		}

		// Write row data
		for j, value := range rowData {
			cell := fmt.Sprintf("%c%d", 'A'+j, rowIndex)
			f.SetCellValue(sheetName, cell, value)
		}
	}

	// Auto-fit column width
	for i := range headers {
		col := string(rune('A' + i))
		f.SetColWidth(sheetName, col, col, 15)
	}

	// Create directory and save file
	dirPath, err := createDirStructure(l.svcCtx.Config.Storage.Local.Path, "reports")
	if err != nil {
		return "", fmt.Errorf("failed to create directory structure: %v", err)
	}

	timestamp := time.Now().Format("20060102_150405")
	fileName := fmt.Sprintf("track_report_%s.xlsx", timestamp)
	filePath := filepath.Join(dirPath, fileName)

	if err := f.SaveAs(filePath); err != nil {
		return "", fmt.Errorf("failed to save excel file: %v", err)
	}

	publicUrl := fmt.Sprintf("/reports/%s/%s",
		time.Now().Format("2006_01_02"),
		fileName)

	return publicUrl, nil
}

func createDirStructure(basePath, kind string) (string, error) {
	now := time.Now()
	dirPath := filepath.Join(basePath, kind, fmt.Sprintf("%d_%02d_%02d", now.Year(), now.Month(), now.Day()))

	err := os.MkdirAll(dirPath, 0755)
	if err != nil {
		return "", err
	}

	return dirPath, nil
}
