package track

import (
	"bcare/common/bquery"
	"bcare/common/cast"
	"bcare/ent"
	"bcare/ent/track"
	"context"
	"github.com/jinzhu/copier"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateLogic(ctx context.Context, svcCtx *svc.ServiceContext) UpdateLogic {
	return UpdateLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateLogic) Update(req *types.TrackUpdateRequest) (resp *types.TrackResponse, err error) {
	trackRecord := new(ent.Track)

	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		trackRecord, err = tx.Track.Get(l.ctx, req.Id)
		if err != nil {
			if ent.IsNotFound(err) {
				return types.ErrTrackNotFound.Wrap(nil)
			}
			return types.ErrTrackUpdate.Wrap(err)
		}
		_ = copier.CopyWithOption(trackRecord, req, cast.PayloadToEnt)

		// Update the bundle
		trackRecord, err = tx.Track.UpdateOneID(trackRecord.ID).
			SetTrack(trackRecord, req.Modified...).
			Save(l.ctx)

		if err != nil {
			return err
		}
		return nil
	})
	trackRecord, _ = l.svcCtx.Ent.Track.Query().
		WithStage().
		WithDeal().
		WithAppointments().
		WithPerson(func(q *ent.PersonQuery) {
			q.WithIssues()
		}).Where(track.ID(trackRecord.ID)).Only(l.ctx)

	resp = new(types.TrackResponse)

	err = cast.ConvertViaJson(&resp, trackRecord)
	if err != nil {
		err = types.ErrTrackUpdate.Wrap(err)
		return
	}

	return
}
