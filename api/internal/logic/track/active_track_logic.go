package track

import (
	"bcare/common/bquery"
	cast2 "bcare/common/cast"
	"bcare/ent"
	"bcare/ent/track"
	"context"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type ActiveTrackLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewActiveTrackLogic(ctx context.Context, svcCtx *svc.ServiceContext) ActiveTrackLogic {
	return ActiveTrackLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ActiveTrackLogic) ActiveTrack(req *types.ActiveTrackRequest) (resp *types.Track, err error) {
	trackRecord := new(ent.Track)
	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		// Query track đang active của person (end == nil)
		trackRecord, err = tx.Track.Query().
			WithStage(). // Load stage relationship
			Where(
				track.And(
					track.PersonID(req.PersonId),
					track.EndIsNil(),
				),
			).
			Only(l.ctx)

		if err != nil && !ent.IsNotFound(err) {
			logx.WithContext(l.ctx).Errorf("active track not found for person ID %d", req.PersonId)
			return nil
		}
		return nil
	})

	if err != nil {
		return nil, err
	}

	resp = new(types.Track)
	_ = cast2.EntToResp(&resp, trackRecord)

	return resp, nil
}
