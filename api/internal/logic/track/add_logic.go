package track

import (
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/bquery"
	cast2 "bcare/common/cast"
	"bcare/common/ctxdata"
	"bcare/ent"
	"bcare/ent/appointment"
	"bcare/ent/deal"
	"bcare/ent/track"
	"context"
	"github.com/jinzhu/copier"
	"time"

	"github.com/zeromicro/go-zero/core/logx"
)

type AddLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAddLogic(ctx context.Context, svcCtx *svc.ServiceContext) AddLogic {
	return AddLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AddLogic) Add(req *types.TrackAddRequest) (resp *types.TrackResponse, err error) {
	trackRecord := new(ent.Track)
	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		err = copier.Copy(trackRecord, req)
		if err != nil {
			return types.ErrTrackAdd.Wrap(err)
		}

		// If deal_id is provided, use it directly
		if req.DealId > 0 {
			trackRecord.DealID = &req.DealId
		} else {
			// If deal_id is not provided, try to find the latest deal for the person
			dealRecord, err := tx.Deal.Query().
				Where(
					deal.PersonID(req.PersonId),
					deal.DeletedAtIsNil(),
				).
				Order(ent.Asc(deal.FieldCreatedAt)).
				First(l.ctx)

			if err != nil {
				if !ent.IsNotFound(err) {
					return types.ErrTrackAdd.Wrap(err)
				}
				// If no deal exists, create a new one
				dealRecord, err = tx.Deal.Create().
					SetPersonID(req.PersonId).
					SetState(deal.StateActive).
					SetName("Niềng răng").
					Save(l.ctx)
				if err != nil {
					return types.ErrTrackAdd.Wrap(err)
				}
			}
			trackRecord.DealID = &dealRecord.ID
		}

		stageRecord, err := tx.Stage.Get(l.ctx, req.StageId)
		if err != nil {
			return types.ErrTrackAdd.Wrap(err)
		}

		// Create track record
		trackRecord, err = tx.Track.Create().
			SetTrack(trackRecord).
			SetPipelineID(stageRecord.PipelineID).
			SetUserID(ctxdata.GetUidFromCtx(l.ctx)).
			SetBegin(time.Now()).
			// Add other necessary fields here
			Save(l.ctx)
		if err != nil {
			return types.ErrTrackAdd.Wrap(err)
		}

		// Update appointment if exists
		now := time.Now()
		startOfDay := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
		endOfDay := startOfDay.Add(24 * time.Hour)

		_, err = tx.Appointment.Update().
			Where(
				appointment.PersonID(trackRecord.PersonID),
				appointment.StatusGTE(types.AppointmentStatusActive),
				appointment.StartTimeGTE(startOfDay),
				appointment.StartTimeLT(endOfDay),
			).
			SetArrivedAt(now).
			SetTrackID(trackRecord.ID).
			SetStatus(types.AppointmentStatusOnTime).
			Save(l.ctx)

		if err != nil {
			return types.ErrTrackAdd.Wrap(err)
		}
		return nil
	})
	if err != nil {
		return nil, err
	}

	trackRecord, _ = l.svcCtx.Ent.Track.Query().
		WithDeal().
		WithAppointments().
		WithPerson(func(q *ent.PersonQuery) {
			q.WithIssues()
		}).Where(track.ID(trackRecord.ID)).Only(l.ctx)

	resp = new(types.TrackResponse)

	err = cast2.ConvertViaJson(&resp, trackRecord)
	if err != nil {
		err = types.ErrTrackAdd.Wrap(err)
		return
	}

	return
}
