package track

import (
	"bcare/common/bconst"
	"bcare/common/bquery"
	cast2 "bcare/common/cast"
	"bcare/ent"
	"bcare/ent/attachment"
	"bcare/ent/person"
	"bcare/ent/track"
	"context"
	"entgo.io/ent/dialect/sql"
	"github.com/samber/lo"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type ListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewListLogic(ctx context.Context, svcCtx *svc.ServiceContext) ListLogic {
	return ListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ListLogic) List(req *types.TrackListRequest) (resp *types.TrackListResponse, err error) {
	limit, offset := bquery.Paging(req.<PERSON>ize, req.Page)
	order := bquery.Ordering(req.OrderBy)
	query := l.svcCtx.Ent.Track.Query().
		WithAppointments().
		WithDeal().
		WithPerson(func(q *ent.PersonQuery) {
			q.WithIssues()
		}).
		Order(order)

	if req.IncludeRelation {
		query = query.WithAttachments(func(aq *ent.AttachmentQuery) {
			aq.Where(
				attachment.Or(
					attachment.KindEQ(attachment.KindOperation),
					attachment.And(
						attachment.KindEQ(attachment.KindProduct),
						attachment.StatusNEQ(bconst.StatusTemp),
					),
				),
			).WithProduct()
		})
	}

	query = query.Where(track.HasPersonWith(person.StatusNEQ(bconst.StatusDeleted), person.DeletedAtIsNil()))
	filterMap := make(map[string]interface{})
	_ = cast2.AtomNoZero(&filterMap, req.Filter)

	for field, value := range filterMap {
		query = query.Where(sql.FieldEQ(field, value))
	}

	total, err := query.Count(l.ctx)
	if err != nil {
		return nil, types.ErrTrackGetList.Wrap(err)
	}

	query = query.Limit(limit).Offset(offset)
	tracks, err := query.All(l.ctx)
	if err != nil {
		return nil, types.ErrTrackGetList.Wrap(err)
	}

	resp = new(types.TrackListResponse)
	resp.Tracks = lo.Map(tracks, func(trackRecord *ent.Track, _ int) types.TrackSortResponse {
		t := types.TrackSortResponse{}
		_ = cast2.ConvertViaJson(&t, trackRecord)
		return t
	})
	resp.Total = total

	return resp, nil
}
