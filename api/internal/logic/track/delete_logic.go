package track

import (
	"context"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewDeleteLogic(ctx context.Context, svcCtx *svc.ServiceContext) DeleteLogic {
	return DeleteLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DeleteLogic) Delete(req *types.TrackDeleteRequest) (resp *types.Track, err error) {
	e := l.svcCtx.Ent.Track.DeleteOneID(req.Id).Exec(l.ctx)
	if e != nil {
		err = types.ErrTrackDelete.Wrap(e)
		return
	}
	return
}
