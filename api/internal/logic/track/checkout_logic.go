package track

import (
	"bcare/common/bquery"
	"bcare/ent"
	"bcare/ent/track"
	"context"
	"time"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type CheckoutLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewCheckoutLogic(ctx context.Context, svcCtx *svc.ServiceContext) CheckoutLogic {
	return CheckoutLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *CheckoutLogic) Checkout(req *types.TrackCheckoutRequest) (resp *types.Track, err error) {
	trackRecord := new(ent.Track)
	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		trackRecord, err = tx.Track.Query().
			WithStage().
			Where(track.ID(req.Id)).
			Only(l.ctx)

		if err != nil {
			if ent.IsNotFound(err) {
				return types.ErrTrackNotFound.Wrap(err, "track ID %d not found", req.Id)
			}
			return types.ErrTrackNotFound.Wrap(err)
		}

		// Kiểm tra xem track có stage không
		if trackRecord.Edges.Stage == nil {
			// Deal đã ở trạng thái checkout, không cần thay đổi
			return nil
		}

		// Cập nhật track, set stage_id thành nil, set end thành time.Now()
		trackRecord, err = tx.Track.UpdateOne(trackRecord).
			SetEnd(time.Now()).
			ClearPipelineID().
			ClearStage().
			Save(l.ctx)

		return nil
	})
	return
}
