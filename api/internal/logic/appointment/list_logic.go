package appointment

import (
	"bcare/api/internal/auth"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/bquery"
	"bcare/common/butils"
	cast2 "bcare/common/cast"
	"bcare/ent"
	"bcare/ent/appointment"
	"context"
	"entgo.io/ent/dialect/sql"
	"github.com/samber/lo"
	"github.com/zeromicro/go-zero/core/logx"
)

type ListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewListLogic(ctx context.Context, svcCtx *svc.ServiceContext) ListLogic {
	return ListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ListLogic) List(req *types.AppointmentListRequest) (resp *types.AppointmentListResponse, err error) {
	//kiểm tra xem có quyền ko
	if !l.svcCtx.Auth.Appointment.CanList(l.ctx, nil) {
		logx.WithContext(l.ctx).Infof("Bạn ko có quyền")
		return nil, auth.ErrAppointmentListDenied

	}

	limit, offset := bquery.Paging(req.PageSize, req.Page)
	order := bquery.Ordering(req.OrderBy)
	query := l.svcCtx.Ent.Appointment.Query().WithPerson().WithDoctor().Order(order)

	filterMap := make(map[string]interface{})
	_ = cast2.AtomNoZero(&filterMap, req.Filter)

	for field, value := range filterMap {
		query = query.Where(sql.FieldEQ(field, value))
	}
	//query = query.Where(appointment.StatusNEQ(types.AppointmentStatusDeActive))
	if req.FromDate != "" && req.ToDate != "" {
		fromDate, toDate := butils.GetDateRange(req.FromDate, req.ToDate)

		if !fromDate.IsZero() && !toDate.IsZero() {
			query = query.Where(
				appointment.StartTimeGTE(fromDate),
				appointment.StartTimeLT(toDate),
			)
		}
	}

	if req.HasDoctor == "yes" {
		query = query.Where(appointment.DoctorIDNotNil())
	} else if req.HasDoctor == "no" {
		query = query.Where(appointment.DoctorIDIsNil())
	}

	total, _ := query.Count(l.ctx)
	query = query.Limit(limit).Offset(offset)
	appointments, e := query.All(l.ctx)

	if e != nil {
		err = types.ErrAppointmentGetList.Wrap(e, e.Error())
		return nil, err
	}

	resp = new(types.AppointmentListResponse)
	resp.Appointments = lo.Map(appointments, func(appointment *ent.Appointment, _ int) types.AppointmentResponse {
		a := types.AppointmentResponse{}
		_ = cast2.EntToResp(&a, appointment)
		return a
	})
	resp.Total = total

	return resp, err

}
