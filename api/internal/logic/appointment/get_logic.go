package appointment

import (
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/berr"
	"bcare/common/cast"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetLogic {
	return &GetLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetLogic) Get(req *types.AppointmentGetRequest) (resp *types.AppointmentResponse, err error) {
	appointmentOutput, err := l.svcCtx.AppointmentModel.GetAppointment(l.ctx, req.Id)
	if err != nil {
		return nil, err
	}

	if !l.svcCtx.Auth.Appointment.CanGet(l.ctx, nil) {
		return nil, berr.ErrPermissionDenied.Wrap(nil)
	}

	resp = new(types.AppointmentResponse)
	err = cast.ConvertViaJson(resp, appointmentOutput)
	if err != nil {
		return nil, err
	}

	return resp, nil
}
