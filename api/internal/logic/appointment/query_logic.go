package appointment

import (
	"bcare/api/internal/auth"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/bquery"
	"bcare/common/butils"
	cast2 "bcare/common/cast"
	"bcare/ent"
	"bcare/ent/appointment"
	"bcare/ent/person"
	"bcare/ent/schema"
	"bcare/ent/user"
	"context"
	"encoding/json"
	"entgo.io/ent/dialect/sql"
	"fmt"
	"github.com/jinzhu/copier"
	"github.com/xuri/excelize/v2"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/logx"
)

type QueryLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewQueryLogic(ctx context.Context, svcCtx *svc.ServiceContext) QueryLogic {
	return QueryLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *QueryLogic) Query(req *types.AppointmentDynamicQuery) (res *types.DynamicQueryResponse, err error) {
	res = new(types.DynamicQueryResponse)

	if !l.svcCtx.Auth.Appointment.CanQuery(l.ctx) {
		logx.WithContext(l.ctx).Infof("Permission denied")
		return nil, auth.ErrAppointmentQueryDenied
	}

	dQuery := &bquery.DynamicQuery{}
	err = copier.Copy(dQuery, req)
	if err != nil {
		logx.WithContext(l.ctx).Errorf("unable to copy request: %v", err)
		return res, err
	}

	query := l.svcCtx.Ent.Appointment.Query().Modify(bquery.ApplyDynamicQuery(dQuery)).WithPerson().WithDoctor()

	if req.Doctor != "" {
		id, e := strconv.Atoi(req.Doctor)
		if e != nil {
			id = 0
		}
		query = query.Where(appointment.HasDoctorWith(user.Or(user.NameContainsFold(req.Doctor), user.ID(id))))
	}
	if req.Person != "" {
		query = query.Where(
			appointment.HasPersonWith(
				person.Or(
					func(s *sql.Selector) {
						s.Where(sql.P(func(b *sql.Builder) {
							b.WriteString("f_unaccent(full_name) LIKE '%' || f_unaccent(")
							b.Arg(req.Person)
							b.WriteString(") || '%'")
						}))
					},
					person.PhoneContains(req.Person),
					func(s *sql.Selector) {
						s.Where(sql.P(func(b *sql.Builder) {
							b.WriteString("f_unaccent(person_field->>'code') =  f_unaccent(")
							b.Arg(req.Person)
							b.WriteString(") ")
						}))
					},
				)))
	}
	if req.Note != "" {
		query = query.Where(appointment.NotesContainsFold(req.Note))
	}
	if req.Task != "" {
		query = query.Where(appointment.ExtraNotesContainsFold(req.Task))
	}

	if req.Arrived == "yes" {
		query = query.Where(appointment.ArrivedAtNotNil())
	} else if req.Arrived == "no" {
		query = query.Where(appointment.ArrivedAtIsNil())
	}

	if req.SourceId > 0 {
		query = query.Where(
			appointment.HasPersonWith(
				person.SourceID(req.SourceId),
			),
		)
	}

	if req.HasDoctor == "yes" {
		query = query.Where(appointment.DoctorIDNotNil())
	} else if req.HasDoctor == "no" {
		query = query.Where(appointment.DoctorIDIsNil())
	}

	result := map[string]any{}
	aliases := make(map[string]bool)
	for _, agg := range req.Aggregations {
		if agg.Alias != "" {
			aliases[agg.Alias] = true
		}
	}

	skipSoftDelete := schema.SkipSoftDelete(l.ctx)

	if !req.Export {
		if req.Limit <= 1 {
			r, err := query.First(skipSoftDelete)
			if err != nil && !ent.IsNotFound(err) {
				logx.WithContext(l.ctx).Errorf("unable to fetch Appointment record: %v", err)
				return res, err
			}

			if r != nil {
				for alias := range aliases {
					result[alias], _ = r.Value(alias)
				}
			}
		} else {
			rows, err := query.All(skipSoftDelete)
			if err != nil {
				logx.WithContext(l.ctx).Errorf("unable to fetch Appointment records: %v", err)
				return res, err
			}

			resultList := make([]map[string]any, len(rows))
			for i, r := range rows {
				rowResult := make(map[string]any)
				rowResult["record"] = cast2.Atom(&rowResult, r)
				for alias := range aliases {
					rowResult[alias], _ = r.Value(alias)
				}
				resultList[i] = rowResult
			}
			result["rows"] = resultList
		}
	} else {
		rows, err := query.All(skipSoftDelete)
		if err != nil {
			logx.WithContext(l.ctx).Errorf("unable to fetch appointment records: %v", err)
			return res, err
		}

		fileUrl, err := l.exportAppointmentsToExcel(rows)
		if err != nil {
			return nil, fmt.Errorf("failed to export excel: %v", err)
		}
		result["file_url"] = fileUrl
	}

	res.Result = result
	return
}

func (l *QueryLogic) exportAppointmentsToExcel(records []*ent.Appointment) (string, error) {
	// Create new Excel file in memory
	f := excelize.NewFile()
	defer f.Close()

	// Create a new sheet
	sheetName := "Appointments"
	f.SetSheetName("Sheet1", sheetName)

	// Set headers
	headers := []string{
		"Ngày tạo", "Tên KH", "ID KH", "SĐT", "Bác sĩ", "Loại hẹn",
		"Thời gian hẹn", "Đã đến", "Ghi chú", "Công việc",
		"Giới tính", "Ngày sinh", "Người tạo",
	}

	for i, header := range headers {
		cell := fmt.Sprintf("%c1", 'A'+i)
		f.SetCellValue(sheetName, cell, header)
		// Set header style
		style, _ := f.NewStyle(&excelize.Style{
			Font: &excelize.Font{Bold: true},
			Fill: excelize.Fill{Type: "pattern", Color: []string{"#CCCCCC"}, Pattern: 1},
		})
		f.SetCellStyle(sheetName, cell, cell, style)
	}

	// Write data
	for i, record := range records {
		row := i + 2 // Start from row 2 (after headers)
		doctorName := ""
		if record.Edges.Doctor != nil {
			doctorName = record.Edges.Doctor.Name
		}
		personName := ""
		phone := ""
		birthday := ""
		code := ""
		if record.Edges.Person != nil {
			personName = record.Edges.Person.FullName
			phone = record.Edges.Person.Phone
			if record.Edges.Person.DateOfBirth != nil {
				birthday = record.Edges.Person.DateOfBirth.Format(l.svcCtx.Config.General.DateFormat)
			}
			if record.Edges.Person.PersonField.Code != nil {
				code = *record.Edges.Person.PersonField.Code
			}

			phone = butils.MaskPhone(phone, l.ctx, l.svcCtx.Enforcer)
		}

		arrivedAt := ""
		if record.ArrivedAt != nil {
			arrivedAt = record.ArrivedAt.Format(l.svcCtx.Config.General.DateTimeFormat)
		}

		creatorName := ""
		if record.Edges.Creator != nil {
			creatorName = record.Edges.Creator.Username
		}

		// Parse extra notes JSON
		tasks := parseExtraNotes(record.ExtraNotes)

		data := []interface{}{
			record.CreatedAt.Format(l.svcCtx.Config.General.DateTimeFormat),
			personName,
			code,
			phone,
			doctorName,
			types.AppointmentTypes[int(record.Type)],
			record.StartTime.Format(l.svcCtx.Config.General.DateTimeFormat),
			arrivedAt,
			record.Notes,
			tasks,
			types.Gender[record.Edges.Person.Gender],
			birthday,
			creatorName,
		}

		for j, value := range data {
			cell := fmt.Sprintf("%c%d", 'A'+j, row)
			f.SetCellValue(sheetName, cell, value)
		}
	}

	// Auto-fit column width
	for i := range headers {
		col := string(rune('A' + i))
		f.SetColWidth(sheetName, col, col, 20) // Set width to 20 for better readability
	}

	// Create directory structure based on date
	dirPath, err := createDirStructure(l.svcCtx.Config.Storage.Local.Path, "reports")
	if err != nil {
		return "", fmt.Errorf("failed to create directory structure: %v", err)
	}

	// Generate filename with timestamp
	timestamp := time.Now().Format("20060102_150405")
	fileName := fmt.Sprintf("appointments_%s.xlsx", timestamp)
	filePath := filepath.Join(dirPath, fileName)

	// Save Excel file to disk
	if err := f.SaveAs(filePath); err != nil {
		return "", fmt.Errorf("failed to save excel file: %v", err)
	}

	// Generate public URL for download
	publicUrl := fmt.Sprintf("/reports/%s/%s",
		time.Now().Format("2006_01_02"),
		fileName)

	return publicUrl, nil
}

func createDirStructure(basePath, kind string) (string, error) {
	now := time.Now()
	dirPath := filepath.Join(basePath, kind, fmt.Sprintf("%d_%02d_%02d", now.Year(), now.Month(), now.Day()))

	err := os.MkdirAll(dirPath, 0755)
	if err != nil {
		return "", err
	}

	return dirPath, nil
}

// Helper function to parse extra notes JSON
func parseExtraNotes(extraNotes *string) string {
	if extraNotes == nil || *extraNotes == "" {
		return ""
	}

	var data map[string]interface{}
	err := json.Unmarshal([]byte(*extraNotes), &data)
	if err != nil {
		return *extraNotes // Return original string if parsing fails
	}

	var tasks []string

	// Get expected_task value
	if task, ok := data["expected_task"].(string); ok && task != "" {
		tasks = append(tasks, task)
	}

	// Get expected_task_other value
	if otherTask, ok := data["expected_task_other"].(string); ok && otherTask != "" {
		tasks = append(tasks, otherTask)
	}

	// Join all tasks with comma
	return strings.Join(tasks, ", ")
}
