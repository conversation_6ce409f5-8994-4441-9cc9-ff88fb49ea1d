package appointment

import (
	"bcare/api/internal/auth"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/bquery"
	cast2 "bcare/common/cast"
	"bcare/common/ctxdata"
	"bcare/ent"
	"bcare/ent/appointment"
	"context"
	"github.com/jinzhu/copier"
	"github.com/zeromicro/go-zero/core/logx"
	"time"
)

type AddLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAddLogic(ctx context.Context, svcCtx *svc.ServiceContext) AddLogic {
	return AddLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AddLogic) Add(req *types.AppointmentAddRequest) (resp *types.AppointmentResponse, err error) {
	appointmentRecord := new(ent.Appointment)
	err = copier.CopyWithOption(&appointmentRecord, req, cast2.PayloadToEnt)
	if err != nil {
		logx.WithContext(l.ctx).<PERSON>rrorf("cannot copy request: %v", err)
		return nil, types.ErrAppointmentCopyFailed.Wrap(err, err.Error())
	}
	//kiểm tra xem có quyền ko
	if !l.svcCtx.Auth.Appointment.CanAdd(l.ctx, appointmentRecord) {
		logx.WithContext(l.ctx).Infof("Bạn ko có quyền")
		return nil, auth.ErrAppointmentAddDenied

	} // Kiểm tra xem có lịch hẹn nào đã tồn tại trong khoảng thời gian này không
	existingAppointments := new(ent.Appointment)
	if req.DoctorId > 0 {
		existingAppointments, _ = l.svcCtx.Ent.Debug().Appointment.
			Query().
			Where(
				appointment.And(
					appointment.StatusNEQ(types.AppointmentStatusDeActive),
					appointment.DoctorID(appointmentRecord.DoctorID),
					// Điều kiện kiểm tra chồng chéo thời gian
					appointment.And(
						appointment.StartTimeLT(appointmentRecord.EndTime.Truncate(time.Minute)),
						appointment.EndTimeGT(appointmentRecord.StartTime.Truncate(time.Minute)),
					),
				),
			).
			First(l.ctx)
		if existingAppointments != nil {
			return nil, types.ErrDoctorHasAppointment.Wrap(nil, appointmentRecord.DoctorID)
		}
	}
	currentUserId := ctxdata.GetUidFromCtx(l.ctx)
	appointmentRecord.CreatorID = &currentUserId

	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		save, err := tx.Appointment.Create().SetAppointment(appointmentRecord).Save(l.ctx)
		if err != nil {
			return types.ErrAppointmentAdd.Wrap(err, "Error saving appointment record")
		}
		appointmentRecord = save
		return nil
	})

	if err != nil {
		return nil, err
	}

	resp = new(types.AppointmentResponse)
	_ = cast2.EntToResp(&resp, appointmentRecord)

	return resp, nil
}
