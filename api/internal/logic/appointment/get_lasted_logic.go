package appointment

import (
	cast2 "bcare/common/cast"
	"bcare/ent"
	"bcare/ent/appointment"
	"context"
	"time"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetLastedLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetLastedLogic(ctx context.Context, svcCtx *svc.ServiceContext) GetLastedLogic {
	return GetLastedLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetLastedLogic) GetLasted(req *types.AppointmentGetRequest) (resp *types.AppointmentResponse, err error) {
	appointmentRecord, err := l.svcCtx.Ent.Appointment.Query().With<PERSON>erson().WithDoctor().Where(appointment.PersonID(req.PersonId), appointment.StartTimeGT(time.Now())).Order(appointment.ByStartTime()).First(l.ctx)
	if (err != nil || appointmentRecord == nil) && !ent.IsNotFound(err) {
		logx.WithContext(l.ctx).Errorf("unable to query appointment: %v", err)
		return nil, nil
	}
	resp = new(types.AppointmentResponse)
	_ = cast2.EntToResp(&resp, appointmentRecord)

	return resp, nil
}
