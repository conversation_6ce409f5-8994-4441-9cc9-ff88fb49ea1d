package appointment

import (
	"bcare/api/internal/auth"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/api/model"
	"bcare/common/bquery"
	cast2 "bcare/common/cast"
	"bcare/ent"
	"bcare/ent/appointment"
	"context"
	"errors"
	"github.com/jinzhu/copier"
	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateLogic(ctx context.Context, svcCtx *svc.ServiceContext) UpdateLogic {
	return UpdateLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateLogic) Update(req *types.AppointmentUpdateRequest) (resp *types.AppointmentResponse, err error) {
	appointmentRecord := new(ent.Appointment)
	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		appointmentRecord, err = tx.Appointment.Query().WithDoctor().WithPerson().Where(appointment.ID(req.Id)).First(l.ctx)

		if err != nil {
			if errors.Is(err, model.ErrNotFound) {
				return types.ErrAppointmentNotFound.Wrap(err, req.Id, err.Error())
			}
			return types.ErrAppointmentUpdate.Wrap(err, "Error retrieving appointment")
		}

		if appointmentRecord == nil {
			logx.Errorf("appointment not found")
			return types.ErrAppointmentNotFound.Wrap(nil, req.Id, err.Error())
		}

		//kiểm tra xem có quyền ko
		if !l.svcCtx.Auth.Appointment.CanUpdate(l.ctx, appointmentRecord) {
			logx.WithContext(l.ctx).Infof("Bạn ko có quyền")
			return auth.ErrAppointmentUpdateDenied

		}
		originAppointment := *appointmentRecord

		err = copier.CopyWithOption(&appointmentRecord, req, cast2.PayloadToEnt)

		if err != nil {
			logx.Errorf("copier copy appointment failed, err:%v", err)
			return types.ErrAppointmentUpdate.Wrap(err, "Error copying appointment data")
		}

		if req.ExtraNotes == "" {
			appointmentRecord.ExtraNotes = nil
		}

		appointmentRecord, err = tx.Appointment.UpdateOneID(req.Id).SetAppointment(appointmentRecord, req.Modified...).Save(l.ctx)
		if err != nil {
			return types.ErrAppointmentUpdate.Wrap(err, "Error updating appointment record")
		}
		appointmentRecord.Edges.Doctor = originAppointment.Edges.Doctor
		appointmentRecord.Edges.Person = originAppointment.Edges.Person
		return nil
	})

	if err != nil {
		return nil, err
	}

	resp = new(types.AppointmentResponse)
	_ = cast2.EntToResp(&resp, appointmentRecord)

	return resp, nil
}
