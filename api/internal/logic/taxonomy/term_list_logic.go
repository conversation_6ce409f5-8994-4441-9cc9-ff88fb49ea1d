package taxonomy

import (
	cast2 "bcare/common/cast"
	"bcare/ent"
	"context"
	"entgo.io/ent/dialect/sql"
	"github.com/samber/lo"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type TermListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewTermListLogic(ctx context.Context, svcCtx *svc.ServiceContext) TermListLogic {
	return TermListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *TermListLogic) TermList(req *types.TermGetRequest) (resp *types.TermListReponse, err error) {
	query := l.svcCtx.Ent.Term.Query()

	filterMap := make(map[string]interface{})
	_ = cast2.AtomNoZero(&filterMap, req)

	for field, value := range filterMap {
		query = query.Where(sql.FieldEQ(field, value))
	}
	terms, e := query.All(l.ctx)
	if e != nil {
		err = types.ErrTermGetList.Wrap(e)
		return
	}

	resp = new(types.TermListReponse)
	resp.Term = lo.Map(terms, func(term *ent.Term, _ int) types.Term {
		t := types.Term{}
		_ = cast2.ConvertViaJson(&t, term)
		return t
	})

	return resp, err

}
