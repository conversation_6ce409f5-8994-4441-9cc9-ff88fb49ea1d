package taxonomy

import (
	"bcare/ent"
	"context"
	"github.com/jinzhu/copier"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type TermGetLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewTermGetLogic(ctx context.Context, svcCtx *svc.ServiceContext) TermGetLogic {
	return TermGetLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *TermGetLogic) TermGet(req *types.TermGetRequest) (resp *types.TermGetResponse, err error) {
	term, err := l.svcCtx.Ent.Term.Get(l.ctx, req.Id)
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, types.ErrTermNotFound
		}
		return nil, types.ErrTermNotFound.Wrap(err)
	}

	resp = new(types.TermGetResponse)
	err = copier.Copy(&resp.Term, term)
	if err != nil {
		return nil, types.ErrTermNotFound.Wrap(err)
	}

	return resp, nil
}
