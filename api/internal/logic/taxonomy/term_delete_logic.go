package taxonomy

import (
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/bquery"
	"bcare/ent"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type TermDeleteLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewTermDeleteLogic(ctx context.Context, svcCtx *svc.ServiceContext) TermDeleteLogic {
	return TermDeleteLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *TermDeleteLogic) TermDelete(req *types.TermGetRequest) (resp *types.TermAddResponse, err error) {
	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		term, err := tx.Term.Get(l.ctx, req.Id)
		if err != nil {
			if ent.IsNotFound(err) {
				return types.ErrTermNotFound
			}
			return types.ErrTermNotFound.Wrap(err)
		}

		err = tx.Term.DeleteOne(term).Exec(l.ctx)
		if err != nil {
			return types.ErrTermDelete.Wrap(err)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return resp, nil
}
