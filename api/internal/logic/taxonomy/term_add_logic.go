package taxonomy

import (
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/bquery"
	cast2 "bcare/common/cast"
	"bcare/ent"
	"bcare/ent/bundle"
	"context"
	"fmt"
	"github.com/jinzhu/copier"

	"github.com/zeromicro/go-zero/core/logx"
)

type TermAddLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewTermAddLogic(ctx context.Context, svcCtx *svc.ServiceContext) TermAddLogic {
	return TermAddLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *TermAddLogic) TermAdd(req *types.TermAddRequest) (resp *types.TermAddResponse, err error) {
	termRecord := new(ent.Term)

	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		if req.Bundle != "" {
			// Kiểm tra bundle tồn tại
			exists, err := tx.Bundle.Query().Where(bundle.MachineName(req.Bundle)).Exist(l.ctx)
			if err != nil {
				return err
			}
			if !exists {
				return fmt.Errorf("bundle not found")
			}
		}
		err = copier.CopyWithOption(&termRecord, req, cast2.PayloadToEnt)
		// Tạo term mới
		termRecord, err = tx.Term.Create().
			SetTerm(termRecord).
			Save(l.ctx)

		if err != nil {
			return err
		}

		resp = new(types.TermAddResponse)
		_ = copier.Copy(resp, termRecord)

		return nil
	})

	if err != nil {
		return nil, err
	}

	return resp, nil
}
