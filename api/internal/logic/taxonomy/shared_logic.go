package taxonomy

import (
	"context"

	"bcare/api/internal/svc"
	"github.com/zeromicro/go-zero/core/logx"
)

// SharedLogic struct
type SharedLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// NewSharedLogic constructor
func NewSharedLogic(ctx context.Context, svcCtx *svc.ServiceContext) SharedLogic {
	return SharedLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}
