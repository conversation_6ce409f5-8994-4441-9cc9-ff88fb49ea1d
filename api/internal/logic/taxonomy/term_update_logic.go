package taxonomy

import (
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/bquery"
	cast2 "bcare/common/cast"
	"bcare/ent"
	"context"
	"github.com/jinzhu/copier"
	"github.com/zeromicro/go-zero/core/logx"
)

type TermUpdateLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewTermUpdateLogic(ctx context.Context, svcCtx *svc.ServiceContext) TermUpdateLogic {
	return TermUpdateLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *TermUpdateLogic) TermUpdate(req *types.TermUpdateRequest) (resp *types.TermAddResponse, err error) {
	termRecord := new(ent.Term)

	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		termRecord, err = tx.Term.Get(l.ctx, req.Id)
		if err != nil {
			if ent.IsNotFound(err) {
				return types.ErrTermNotFound
			}
			return types.ErrTermNotFound.Wrap(err)
		}

		err = copier.CopyWithOption(termRecord, req, cast2.PayloadToEnt)
		if err != nil {
			logx.Errorf("copier copy term failed, err:%v", err)
			return types.ErrTermUpdate.Wrap(err, "Error copying term data")
		}

		termRecord, err = termRecord.Update().
			SetTerm(termRecord, req.Modified...).Save(l.ctx)
		if err != nil {
			return types.ErrTermUpdate.Wrap(err)
		}

		resp = new(types.TermAddResponse)
		err = copier.Copy(&resp, termRecord)
		if err != nil {
			return types.ErrTermNotFound.Wrap(err)
		}

		return nil
	})

	if err != nil {
		return nil, types.ErrTermAdd.Wrap(err)
	}

	return resp, nil

}
