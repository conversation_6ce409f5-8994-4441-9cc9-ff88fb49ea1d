package person

import (
	"bcare/api/internal/auth"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/bquery"
	"bcare/common/butils"
	cast2 "bcare/common/cast"
	"bcare/ent"
	"bcare/ent/schema"
	"context"
	"fmt"
	"github.com/jinzhu/copier"
	"github.com/xuri/excelize/v2"
	"os"
	"path/filepath"
	"time"

	"github.com/zeromicro/go-zero/core/logx"
)

type QueryLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewQueryLogic(ctx context.Context, svcCtx *svc.ServiceContext) QueryLogic {
	return QueryLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *QueryLogic) Query(req *types.PersonDynamicQuery) (res *types.DynamicQueryResponse, err error) {
	//kiểm tra xem có quyền ko
	if !l.svcCtx.Auth.Person.CanQuery(l.ctx, nil) {
		logx.WithContext(l.ctx).Infof("Bạn ko có quyền")
		return nil, auth.ErrPersonQueryDenied
	}

	dQuery := &bquery.DynamicQuery{}
	err = copier.Copy(dQuery, req)
	if err != nil {
		logx.WithContext(l.ctx).Errorf("unable to copy request: %v", err)
		return nil, err
	}

	query := l.svcCtx.Ent.PersonQueryView.Query().Modify(bquery.ApplyDynamicQuery(dQuery))

	result := map[string]any{}
	aliases := make(map[string]bool)
	for _, agg := range req.Aggregations {
		if agg.Alias != "" {
			aliases[agg.Alias] = true
		}
	}

	skipSoftDelete := schema.SkipSoftDelete(l.ctx)

	if !req.Export {
		if req.Limit <= 1 {
			r, err := query.First(skipSoftDelete)
			if err != nil && !ent.IsNotFound(err) {
				logx.WithContext(l.ctx).Errorf("unable to fetch Task record: %v", err)
				return nil, err
			}

			if r != nil {
				for alias := range aliases {
					result[alias], _ = r.Value(alias)
				}
			}
		} else {
			rows, err := query.All(skipSoftDelete)
			if err != nil {
				logx.WithContext(l.ctx).Errorf("unable to fetch Task records: %v", err)
				return nil, err
			}

			resultList := make([]map[string]any, len(rows))
			for i, r := range rows {
				rowResult := make(map[string]any)
				rowResult["record"] = cast2.Atom(&rowResult, r)
				for alias := range aliases {
					rowResult[alias], _ = r.Value(alias)
				}
				resultList[i] = rowResult
			}
			result["rows"] = resultList
		}
	} else {
		rows, err := query.All(skipSoftDelete)
		if err != nil {
			logx.WithContext(l.ctx).Errorf("unable to fetch payment records: %v", err)
			return res, err
		}

		fileUrl, err := l.exportToExcel(rows)
		if err != nil {
			return nil, fmt.Errorf("failed to export excel: %v", err)
		}
		result["file_url"] = fileUrl
	}

	res = new(types.DynamicQueryResponse)
	res.Result = result

	return
}

func (l *QueryLogic) exportToExcel(records []*ent.PersonQueryView) (fileUrl string, err error) {
	// Create new Excel file in memory
	f := excelize.NewFile()
	defer f.Close()

	// Create a new sheet
	sheetName := "Customers"
	f.SetSheetName("Sheet1", sheetName)

	// Set headers
	headers := []string{
		"ID", "Mã hồ sơ", "Họ tên", "Số điện thoại", "Nguồn", "Loại điều trị", "Sales online",
		"Stage", "Bác sĩ chỉnh nha", "Lịch hẹn gần nhất", "Người tạo", "Ngày giờ tạo",
		"Tên tài khoản", "Số tài khoản", "Ngân hàng", "Chi nhánh",
	}

	// Set headers and style
	for i, header := range headers {
		cell := fmt.Sprintf("%c1", 'A'+i)
		f.SetCellValue(sheetName, cell, header)

		// Set header style
		style, _ := f.NewStyle(&excelize.Style{
			Font: &excelize.Font{Bold: true},
			Fill: excelize.Fill{Type: "pattern", Color: []string{"#CCCCCC"}, Pattern: 1},
		})
		f.SetCellStyle(sheetName, cell, cell, style)
	}

	// Write data
	for i, record := range records {
		row := i + 2 // Start from row 2 (after headers)

		// Get source name

		// Get sale name from Sale map
		saleName := ""
		if record.Sale != nil {
			if name, ok := record.Sale["name"].(string); ok {
				saleName = name
			}
		}
		appointmentTime := ""
		if !record.AppointmentTime.IsZero() {
			appointmentTime = record.AppointmentTime.Format("2006-01-02 15:04:05")
		}

		data := []interface{}{
			record.ID,
			record.PersonCode,
			record.FullName,
			butils.MaskPhone(record.Phone, l.ctx, l.svcCtx.Enforcer),
			record.PersonSource,
			record.TreatmentName,
			saleName,
			record.StageName,
			record.DoctorName,
			appointmentTime,
			record.CreatorName,
			record.CreatedAt.Format("02/01/2006 15:04:05"),
			record.BankAccountName,
			record.BankAccountNumber,
			record.Bank,
			record.BankBranch,
		}

		for j, value := range data {
			cell := fmt.Sprintf("%c%d", 'A'+j, row)
			f.SetCellValue(sheetName, cell, value)
		}
	}

	// Auto-fit column width
	for i := range headers {
		col := string(rune('A' + i))
		f.SetColWidth(sheetName, col, col, 20) // Set width to 20 for better readability
	}

	// Create directory structure based on date
	dirPath, err := createDirStructure(l.svcCtx.Config.Storage.Local.Path, "reports")
	if err != nil {
		return "", fmt.Errorf("failed to create directory structure: %v", err)
	}

	// Generate filename with timestamp
	timestamp := time.Now().Format("20060102_150405")
	fileName := fmt.Sprintf("customers_%s.xlsx", timestamp)
	filePath := filepath.Join(dirPath, fileName)

	// Save Excel file to disk
	if err := f.SaveAs(filePath); err != nil {
		return "", fmt.Errorf("failed to save excel file: %v", err)
	}

	// Generate public URL for download
	publicUrl := fmt.Sprintf("/reports/%s/%s",
		time.Now().Format("2006_01_02"),
		fileName)

	return publicUrl, nil
}

// createDirStructure creates the directory structure for storing files
func createDirStructure(basePath, kind string) (string, error) {
	now := time.Now()
	dirPath := filepath.Join(basePath, kind, fmt.Sprintf("%d_%02d_%02d", now.Year(), now.Month(), now.Day()))

	err := os.MkdirAll(dirPath, 0755)
	if err != nil {
		return "", err
	}

	return dirPath, nil
}
