package person

import (
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/bquery"
	"bcare/ent"
	"bcare/ent/attachment"
	"bcare/ent/attachmentdata"
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetExpectedTasksLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetExpectedTasksLogic(ctx context.Context, svcCtx *svc.ServiceContext) GetExpectedTasksLogic {
	return GetExpectedTasksLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetExpectedTasksLogic) GetExpectedTasks(req *types.PersonGetRequest) (*types.ExpectedTasksResponse, error) {
	var nextOperations []string
	err := bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		// First, get the latest date that has operation attachments
		latestDate, err := tx.Attachment.Query().
			Where(
				attachment.PersonID(req.Id),
				attachment.KindEQ(attachment.KindOperation)).
			Order(ent.Desc(attachment.FieldCreatedAt)).
			Select(attachment.FieldCreatedAt).
			First(l.ctx)
		if err != nil {
			if ent.IsNotFound(err) {
				return nil
			}
			return fmt.Errorf("querying latest date: %w", err)
		}

		// Then get all attachments from that date
		results, err := l.svcCtx.Ent.Attachment.Query().
			Where(
				attachment.PersonID(req.Id),
				attachment.KindEQ(attachment.KindOperation),
				attachment.CreatedAtGTE(beginningOfDay(latestDate.CreatedAt)),
				attachment.CreatedAtLT(beginningOfDay(latestDate.CreatedAt.Add(24*time.Hour))),
			).
			WithData(
				func(q *ent.AttachmentDataQuery) {
					q.Where(attachmentdata.Kind("meta"))
				}).
			Order(ent.Asc(attachment.FieldCreatedAt)).
			All(l.ctx)
		if err != nil {
			return fmt.Errorf("querying attachments: %w", err)
		}
		for _, r := range results {
			if r.Edges.Data != nil {
				for _, data := range r.Edges.Data {
					if data.Data != nil {
						// Cast data.Data to map[string]interface{}
						metaData := data.Data

						// Check if next_operation exists
						nextOpRaw, exists := metaData["next_operation"]
						if !exists {
							continue
						}

						// Convert next_operation to string
						nextOpStr, ok := nextOpRaw.(string)
						if !ok {
							logx.Errorf("Failed to cast next_operation to string: %v", nextOpRaw)
							continue
						}

						// Parse the next_operation JSON string
						var nextOpMap map[string]string
						if err := json.Unmarshal([]byte(nextOpStr), &nextOpMap); err != nil {
							logx.Errorf("Failed to unmarshal next_operation: %v", err)
							continue
						}

						// Get the first value from the map
						for _, value := range nextOpMap {
							nextOperations = append(nextOperations, value)
						}
					}
				}
			}
		}
		return nil
	})

	resp := new(types.ExpectedTasksResponse)
	resp.ExpectedTask = strings.Join(nextOperations, "; ")
	return resp, err
}

// Helper function to get beginning of day in Vietnam timezone
func beginningOfDay(t time.Time) time.Time {
	// Load Asia/Ho_Chi_Minh location
	loc, err := time.LoadLocation("Asia/Ho_Chi_Minh")
	if err != nil {
		loc = time.FixedZone("UTC+7", 7*60*60)
	}

	// Convert time to Vietnam timezone
	t = t.In(loc)

	// Get year, month, day in Vietnam timezone
	year, month, day := t.Date()

	// Return start of day in Vietnam timezone
	return time.Date(year, month, day, 0, 0, 0, 0, loc)
}
