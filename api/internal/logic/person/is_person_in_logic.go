package person

import (
	"bcare/ent/person"
	"bcare/ent/track"
	"context"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type IsPersonInLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewIsPersonInLogic(ctx context.Context, svcCtx *svc.ServiceContext) IsPersonInLogic {
	return IsPersonInLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *IsPersonInLogic) IsPersonIn(req *types.PersonGetRequest) (resp bool, err error) {
	personRecord, _ := l.svcCtx.Ent.Person.Query().
		Where(person.ID(req.Id), person.HasTracksWith(track.EndIsNil())).Only(l.ctx)

	if personRecord != nil {
		return true, nil
	}
	return false, nil

}
