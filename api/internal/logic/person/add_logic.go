package person

import (
	"bcare/api/internal/auth"
	"bcare/api/internal/svc"
	"bcare/api/internal/task"
	"bcare/api/internal/task/tasks"
	"bcare/api/internal/types"
	"bcare/common/bconst"
	"bcare/common/bquery"
	cast2 "bcare/common/cast"
	"bcare/common/ctxdata"
	"bcare/ent"
	"bcare/ent/deal"
	"context"
	"github.com/jinzhu/copier"
	"github.com/zeromicro/go-zero/core/logx"
	"strings"
)

type AddLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAddLogic(ctx context.Context, svcCtx *svc.ServiceContext) AddLogic {
	return AddLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AddLogic) Add(req *types.PersonAddRequest) (resp *types.PersonResponse, err error) {
	personRecord := new(ent.Person)
	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		code := ""
		if req.PersonField.Code != nil {
			code = strings.TrimSpace(*req.PersonField.Code)
			req.PersonField.Code = &code
		}

		req.Phone = strings.TrimSpace(req.Phone)
		duplicated, err := NewSharedLogic(l.ctx, l.svcCtx).checkDuplicated(req.Phone, code, 0)

		if err != nil {
			return types.ErrPersonExist.Wrap(err, "Error checking for duplicate person")
		}

		if duplicated {
			return types.ErrPersonExist.Wrap(nil, "Person with phone %s or code %s already exists", req.Phone, req.PersonField.Code)
		}

		err = copier.CopyWithOption(personRecord, req, cast2.PayloadToEnt)
		if err != nil {
			logx.WithContext(l.ctx).Errorf("cannot copy person: %v", err)
			return types.ErrPersonCopy.Wrap(err, "Error copying person data")
		}

		//kiểm tra xem có quyền ko
		if !l.svcCtx.Auth.Person.CanAdd(l.ctx, personRecord) {
			logx.WithContext(l.ctx).Infof("Bạn ko có quyền")
			return auth.ErrPersonAddDenied
		}

		personRecord.Status = bconst.StatusNormal
		if !req.IsSystemCreated {
			personRecord.UserID = ctxdata.GetUidFromCtx(l.ctx)
		}

		personRecord, err = tx.Person.Create().SetPerson(personRecord).Save(l.ctx)
		if err != nil {
			return types.ErrPersonCreate.Wrap(err, "Error creating person record")
		}

		// Kiểm tra và thêm dữ liệu referred_by
		if req.ReferredBy != nil && req.ReferredBy.ReferrerId > 0 {
			referralRecord := new(ent.PersonReferral)
			referralRecord.ReferredID = personRecord.ID
			referralRecord.ReferrerID = req.ReferredBy.ReferrerId
			referralRecord.Relationship = req.ReferredBy.Relationship
			referralRecord.Note = req.ReferredBy.Note

			_, err = tx.PersonReferral.Create().SetPersonReferral(referralRecord).Save(l.ctx)
			if err != nil {
				return types.ErrPersonReferralCreate.Wrap(err)
			}
		}

		if l.svcCtx.Config.SyncGetFly {
			t, err := tasks.NewGetFlyCreateUpdateTask(personRecord.ID)
			if err != nil {
				logx.WithContext(l.ctx).Errorf("could not create task: %v", err)
			}

			_, err = task.Client.Enqueue(t)
			if err != nil {
				logx.WithContext(l.ctx).Errorf("could not enqueue task: %v", err)
			}
		}

		_, err = tx.Deal.Create().
			SetPersonID(personRecord.ID).
			SetState(deal.StateDraft).
			SetName("Niềng răng").
			SetStageID(13).
			Save(l.ctx)
		if err != nil {
			return types.ErrPersonCreate.Wrap(err)
		}

		return nil
	})
	if err != nil {
		return nil, err
	}

	resp = new(types.PersonResponse)
	_ = cast2.EntToResp(&resp, personRecord)
	return resp, nil
}
