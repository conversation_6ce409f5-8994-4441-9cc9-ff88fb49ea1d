package person

import (
	"bcare/api/internal/auth"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/bquery"
	cast2 "bcare/common/cast"
	"bcare/ent"
	"bcare/ent/person"
	"bcare/ent/personassignment"
	"context"
	"entgo.io/ent/dialect/sql"
	"github.com/samber/lo"
	"github.com/zeromicro/go-zero/core/logx"
	"strings"
)

type ListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewListLogic(ctx context.Context, svcCtx *svc.ServiceContext) ListLogic {
	return ListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ListLogic) List(req *types.PersonListRequest) (resp *types.PersonListResponse, err error) {
	//kiểm tra xem có quyền ko
	if !l.svcCtx.Auth.Person.CanList(l.ctx, nil) {
		logx.WithContext(l.ctx).Infof("Bạn ko có quyền")
		return nil, auth.ErrPersonListDenied
	}

	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		limit, offset := bquery.Paging(req.PageSize, req.Page)
		order := bquery.Ordering(req.OrderBy)

		query := tx.Person.Query().
			WithAssignees().
			WithAssignment(func(q *ent.PersonAssignmentQuery) { q.WithUser() }).
			WithIssues().Order(order)

		filterMap := make(map[string]interface{})
		_ = cast2.AtomNoZero(&filterMap, req.Filter)

		for field, value := range filterMap {
			query = query.Where(sql.FieldEQ(field, value))
		}

		req.Search = strings.TrimSpace(req.Search)
		if req.Search != "" {
			query = query.Where(
				person.Or(
					person.PhoneHasPrefix(req.Search),
					person.Phone("0"+req.Search),
					func(s *sql.Selector) {
						s.Where(sql.P(func(b *sql.Builder) {
							b.WriteString("f_unaccent(full_name) LIKE '%' || f_unaccent(")
							b.Arg(req.Search)
							b.WriteString(") || '%'")
						}))
					},
					func(s *sql.Selector) {
						s.Where(sql.P(func(b *sql.Builder) {
							b.WriteString("f_unaccent(person_field->>'code') =  f_unaccent(")
							b.Arg(req.Search)
							b.WriteString(") ")
						}))
					},
				),
			)
		}

		if req.UserId > 0 {
			query = query.Where(person.HasAssignmentWith(personassignment.UserID(req.UserId)))

		}

		total, _ := query.Count(l.ctx)
		query = query.Limit(limit).Offset(offset)
		persons, e := query.All(l.ctx)

		if e != nil {
			return types.ErrPersonGetList.Wrap(e)
		}
		resp = new(types.PersonListResponse)
		resp.Persons = lo.Map(persons, func(person *ent.Person, _ int) types.PersonResponse {
			p := types.PersonResponse{}
			_ = cast2.EntToResp(&p, person)
			return p
		})
		resp.Total = total
		return nil
	})

	return resp, err
}
