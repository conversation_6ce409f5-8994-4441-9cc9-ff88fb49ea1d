package person

import (
	"bcare/api/internal/auth"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	cast2 "bcare/common/cast"
	"bcare/ent"
	"bcare/ent/person"
	"bcare/ent/phoneviewhistory"
	"bcare/ent/predicate"
	"context"
	"entgo.io/ent/dialect/sql"
	"fmt"
	"github.com/zeromicro/go-zero/core/logx"
)

type GetLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetLogic(ctx context.Context, svcCtx *svc.ServiceContext) GetLogic {
	return GetLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetLogic) Get(req *types.PersonGetRequest) (resp *types.PersonResponse, err error) {
	conditions := []predicate.Person{
		person.ID(req.Id),
	}

	// Only add third condition if req.Phone is not empty
	if req.Phone != "" {
		conditions = append(conditions, func(s *sql.Selector) {
			s.Where(sql.P(func(b *sql.Builder) {
				b.WriteString("person_field->>'secondary_phone' = ")
				b.WriteString(fmt.Sprintf("'%s'", req.Phone))
			}))
		})
		conditions = append(conditions, person.Phone(req.Phone))
	}
	query := l.svcCtx.Ent.Person.Query().
		Where(person.Or(conditions...))

	if req.IncludeRelation {
		query = query.
			WithAssignees().
			WithAssignment(func(q *ent.PersonAssignmentQuery) { q.WithUser() }).
			WithIssues().
			WithReferredBy().
			WithFormSubmissions().
			WithTags()
	}

	personRecord, err := query.First(l.ctx)
	if err != nil && ent.IsNotFound(err) && req.Id > 0 {
		return nil, types.ErrPersonNotFound.Wrap(err, req.Id, err.Error())
	}
	if personRecord == nil {
		return nil, nil
	}

	//kiểm tra xem có quyền ko
	if !l.svcCtx.Auth.Person.CanGet(l.ctx, personRecord) {
		logx.WithContext(l.ctx).Infof("Bạn ko có quyền")
		return nil, auth.ErrPersonGetDenied
	}
	resp = new(types.PersonResponse)
	err = cast2.ConvertViaJson(&resp, personRecord)
	if err != nil {
		return nil, err
	}

	phoneViewHistories, _ := l.svcCtx.Ent.PhoneViewHistory.Query().Where(phoneviewhistory.PersonID(personRecord.ID)).All(l.ctx)

	_ = cast2.EntViewToResp(&resp.PhoneViewHistory, phoneViewHistories)
	return resp, nil
}
