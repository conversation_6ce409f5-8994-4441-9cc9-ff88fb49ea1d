package person

import (
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/ent"
	"bcare/ent/setting"
	"bytes"
	"context"
	"encoding/json"
	"strings"
	"text/template"

	"github.com/zeromicro/go-zero/core/logx"
)

type MessageTemplateLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewMessageTemplateLogic(ctx context.Context, svcCtx *svc.ServiceContext) MessageTemplateLogic {
	return MessageTemplateLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *MessageTemplateLogic) MessageTemplate(req *types.MessageTemplateRequest) (resp *types.MessageTemplateResponse, err error) {
	var person *ent.Person
	var appointment *ent.Appointment
	resp = new(types.MessageTemplateResponse)

	if req.PersonId != 0 {
		person, _ = l.svcCtx.Ent.Person.Get(l.ctx, req.PersonId)
	}
	if req.AppointmentId != 0 {
		appointment, _ = l.svcCtx.Ent.Appointment.Get(l.ctx, req.AppointmentId)
	}

	data := map[string]string{
		"customer_name":      "Nguyễn Văn A",
		"customer_ID":        "920102",
		"schedule_time":      "ngay_hen",
		"price_need_to_paid": "500000",
	}

	if person != nil {
		data["customer_name"] = person.FullName
		data["customer_ID"] = *person.PersonField.Code
	}

	if appointment != nil {
		data["schedule_time"] = appointment.StartTime.Format("15:04 02-01-2006")
	}

	resp.Sms = l.executeTemplate("sms", data)
	resp.Email = l.executeTemplate("email", data)
	resp.Zns = l.executeTemplate("zns", data)
	return resp, nil
}

func (l *MessageTemplateLogic) executeTemplate(name string, data map[string]string) string {
	tplSetting, err := l.svcCtx.Ent.Setting.Query().
		Where(setting.Category("sms_zns_email"), setting.Name(name)).First(l.ctx)
	if err != nil {
		logx.WithContext(l.ctx).Errorf("get %s template error %v.", name, err)
		return ""
	}

	// Lấy template string từ Value map
	templateData, ok := tplSetting.Value["data"].([]interface{})
	if !ok {
		logx.WithContext(l.ctx).Errorf("invalid template format for %s", name)
		return ""
	}

	// Convert []interface{} to string using json.Marshal
	templateBytes, err := json.Marshal(templateData)
	if err != nil {
		logx.WithContext(l.ctx).Errorf("failed to marshal template data: %v", err)
		return ""
	}

	// Convert json bytes to string
	templateStr := string(templateBytes)

	//return ""

	tpl, err := template.New(name).Parse(templateStr)
	if err != nil {
		logx.WithContext(l.ctx).Errorf("parse %s template error %v", name, err)
		return ""
	}

	var result bytes.Buffer
	err = tpl.Execute(&result, data)
	if err != nil {
		logx.WithContext(l.ctx).Errorf("execute %s template error %v", name, err)
		return ""
	}

	// Clean up escaped characters
	finalResult := strings.ReplaceAll(result.String(), "\\u003c", "<")
	finalResult = strings.ReplaceAll(finalResult, "\\u003e", ">")
	finalResult = strings.ReplaceAll(finalResult, "\t", "")

	return finalResult
}
