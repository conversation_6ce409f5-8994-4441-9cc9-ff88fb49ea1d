// Package person contains logic for person-related messaging operations
//
// Email Messaging Feature:
// This logic now supports sending emails in addition to SMS and ZNS messages.
//
// To send an email:
// 1. Set req.Email to the recipient's email address
// 2. Set req.EmailContent to a JSON string containing subject and body:
//    {"subject": "Email subject", "body": "Email body content"}
// 3. The system will parse the JSON to extract subject and body
// 4. If parsing fails, default subject "Thông báo từ BCare" will be used
// 5. Message history will be saved with type "email" (storing original JSON)
//
// Priority order for message types:
// 1. Email (if both Email and EmailContent are provided)
// 2. ZNS (if ZnsTemplateId is provided)
// 3. SMS (default fallback)
//
// Example payload for email:
// {
//   "person_id": 123,
//   "email": "<EMAIL>",
//   "email_content": "{\"subject\":\"Nha khoa Up Dental gửi Hình <PERSON>nh <PERSON>-quang\",\"body\":\"<PERSON><PERSON>h gửi <PERSON>hoa-It,\\n\\nCảm ơn Anh/Chị đã tin tưởng...\"}"
// }

package person

import (
	"bcare/api/internal/task"
	"bcare/api/internal/task/tasks"
	"bcare/api/model"
	"bcare/common/bsms"
	"bcare/common/ctxdata"
	"bcare/ent/messagehistory"
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/hibiken/asynq"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

// EmailContent represents the structure of email_content JSON
type EmailContent struct {
	Subject string `json:"subject"`
	Body    string `json:"body"`
}

type SendMessageLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewSendMessageLogic(ctx context.Context, svcCtx *svc.ServiceContext) SendMessageLogic {
	return SendMessageLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *SendMessageLogic) SendZns(req *types.SendMessageRequest) (err error, errCode int, msgId string) {
	var params bsms.TemplateData
	err = json.Unmarshal([]byte(req.ZnsParams), &params)
	if err != nil {
		return
	}

	znsTpl := &bsms.ZnsTemplate{
		Phone:        req.Phone,
		TemplateID:   req.ZnsTemplateId,
		TemplateData: params,
	}
	return l.svcCtx.Zns.SendMessage(znsTpl)
}

func (l *SendMessageLogic) SendMessage(req *types.SendMessageRequest) (resp *types.SendMessageResponse, err error) {
	userID := ctxdata.GetUidFromCtx(l.ctx)

	// Handle Email message
	// Email takes priority if both email and other message types are provided
	// Requires: req.Email (recipient email) and req.EmailContent (message content)
	if req.EmailContent != "" && req.Email != "" {
		return l.sendEmailMessage(req.PersonId, req.Email, userID, req.EmailContent)
	}

	// Handle ZNS message
	if req.ZnsTemplateId != "" {
		err, errCode, msgId := l.SendZns(req)

		// Create and save ZNS message history
		msgHistory := model.NewZnsMessageHistory(
			req.PersonId,
			req.Phone,
			userID,
			req.ZnsParams,
			req.ZnsContent,
			msgId,
			errCode,
		)

		msgHistory, _ = model.SaveMessageHistory(l.ctx, l.svcCtx.Ent, msgHistory)

		// Handle successful ZNS message
		if msgHistory.MessageStatus == messagehistory.MessageStatusSent {
			if err := l.scheduleZnsUpdateTask(msgHistory.MessageID, req.Phone); err != nil {
				logx.WithContext(l.ctx).Errorf("Failed to schedule ZNS update task: %v", err)
			}
		}

		// Handle fallback to SMS if ZNS fails
		if err != nil && req.FallbackSms {
			return l.sendSmsMessage(req.PersonId, req.Phone, userID, req.SmsContent)
		}

		if err == nil {
			return &types.SendMessageResponse{}, nil
		}
	} else {
		// Handle SMS message directly
		return l.sendSmsMessage(req.PersonId, req.Phone, userID, req.SmsContent)
	}

	return nil, err
}

// parseEmailContent safely parses the email_content JSON and returns subject and body
func (l *SendMessageLogic) parseEmailContent(content string) (subject, body string) {
	var emailContent EmailContent

	// Try to parse as JSON first
	if err := json.Unmarshal([]byte(content), &emailContent); err != nil {
		logx.WithContext(l.ctx).Infof("Email content is not valid JSON, using as plain text: %v", err)
		// If not valid JSON, treat entire content as body with default subject
		return "Thông báo từ BCare", content
	}

	// Use default subject if empty
	if emailContent.Subject == "" {
		emailContent.Subject = "Thông báo từ BCare"
	}

	// Use original content as body if body is empty
	if emailContent.Body == "" {
		emailContent.Body = content
	}

	return emailContent.Subject, emailContent.Body
}

// Helper function to handle SMS sending
func (l *SendMessageLogic) sendSmsMessage(personID int, phone string, userID int, content string) (*types.SendMessageResponse, error) {
	err, errCode := l.svcCtx.Sms.SendMessage(phone, content)

	msgHistory := model.NewSmsMessageHistory(
		personID,
		phone,
		userID,
		content,
		errCode,
	)

	_, err = model.SaveMessageHistory(l.ctx, l.svcCtx.Ent, msgHistory)
	if err != nil {
		return nil, err
	}

	return &types.SendMessageResponse{}, nil
}

// Helper function to handle Email sending
func (l *SendMessageLogic) sendEmailMessage(personID int, email string, userID int, content string) (*types.SendMessageResponse, error) {
	// Validate email
	if !l.svcCtx.Email.ValidateEmail(email) {
		errorCode := 1 // Invalid email error code
		msgHistory := model.NewEmailMessageHistory(
			personID,
			email,
			userID,
			content,
			errorCode,
		)

		_, err := model.SaveMessageHistory(l.ctx, l.svcCtx.Ent, msgHistory)
		if err != nil {
			logx.WithContext(l.ctx).Errorf("Failed to save email message history: %v", err)
		}

		return nil, fmt.Errorf("invalid email address: %s", email)
	}

	// Parse email content JSON to extract subject and body
	subject, body := l.parseEmailContent(content)

	// Send email with parsed subject and body
	err := l.svcCtx.Email.SendSimpleEmail(email, subject, body)
	errorCode := 0
	if err != nil {
		errorCode = 2 // Email sending failed error code
		logx.WithContext(l.ctx).Errorf("Failed to send email to %s: %v", email, err)
	}

	// Create and save email message history (store original content)
	msgHistory := model.NewEmailMessageHistory(
		personID,
		email,
		userID,
		content, // Store original JSON content
		errorCode,
	)

	_, saveErr := model.SaveMessageHistory(l.ctx, l.svcCtx.Ent, msgHistory)
	if saveErr != nil {
		logx.WithContext(l.ctx).Errorf("Failed to save email message history: %v", saveErr)
		return nil, saveErr
	}

	if err != nil {
		return nil, fmt.Errorf("failed to send email: %v", err)
	}

	logx.WithContext(l.ctx).Infof("Successfully sent email to %s for person %d with subject: %s", email, personID, subject)
	return &types.SendMessageResponse{}, nil
}

// Helper function to schedule ZNS update task
func (l *SendMessageLogic) scheduleZnsUpdateTask(messageID, phone string) error {
	t, err := tasks.NewZnsUpdateTask(messageID, bsms.FormatPhone(phone))
	if err != nil {
		return err
	}

	_, err = task.Client.Enqueue(t, asynq.ProcessIn(5*time.Minute), asynq.Queue(task.QueueName))
	return err
}
