package person

import (
	"bcare/api/internal/svc"
	"bcare/ent"
	"bcare/ent/person"
	"bcare/ent/predicate"
	"context"
	"entgo.io/ent/dialect/sql"
	"github.com/zeromicro/go-zero/core/logx"
)

type SharedLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewSharedLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SharedLogic {
	return &SharedLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// checkDuplicated Check if phone  is duplicated
func (l *SharedLogic) checkDuplicated(phone, code string, id int) (bool, error) {
	if phone == "" && code == "" {
		return false, nil
	}

	query := l.svcCtx.Ent.Person.Query()

	conditions := make([]predicate.Person, 0)
	if phone != "" {
		conditions = append(conditions, person.Phone(phone))
	}
	if code != "" {
		conditions = append(conditions, func(s *sql.Selector) {
			s.Where(sql.P(func(b *sql.Builder) {
				b.WriteString("f_unaccent(person_field->>'code') =  f_unaccent(")
				b.Arg(code)
				b.WriteString(") ")
			}))
		})

	}

	personRecord, err := query.Where(
		person.Or(conditions...),
		person.IDNEQ(id),
	).First(l.ctx)

	if ent.IsNotFound(err) {
		return false, nil
	}

	if err == nil && personRecord.ID != id {
		return true, nil
	}

	return false, err
}
