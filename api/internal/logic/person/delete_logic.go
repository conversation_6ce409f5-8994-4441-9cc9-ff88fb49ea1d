package person

import (
	"bcare/api/internal/auth"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/bconst"
	"bcare/common/bquery"
	"bcare/ent"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewDeleteLogic(ctx context.Context, svcCtx *svc.ServiceContext) DeleteLogic {
	return DeleteLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DeleteLogic) Delete(req *types.PersonDeleteRequest) (resp *types.PersonResponse, err error) {
	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		personRecord, err := tx.Person.Get(l.ctx, req.Id)

		if err != nil && !ent.IsNotFound(err) {
			return types.ErrPersonNotFound.Wrap(err, req.Id, err.Error())
		}

		if personRecord == nil {
			logx.Errorf("person not found")
			return types.ErrPersonNotFound.Wrap(err, req.Id, err.Error())
		}

		//kiểm tra xem có quyền ko
		if !l.svcCtx.Auth.Person.CanDelete(l.ctx, personRecord) {
			logx.WithContext(l.ctx).Infof("Bạn ko có quyền")
			return auth.ErrPersonDeleteDenied
		}

		personRecord.Status = bconst.StatusDeleted

		err = tx.Person.DeleteOne(personRecord).Exec(l.ctx)
		if err != nil {
			return types.ErrPersonDelete.Wrap(err)
		}
		return nil
	})

	return resp, err
}
