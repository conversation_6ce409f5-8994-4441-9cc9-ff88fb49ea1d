package person

import (
	"bcare/api/internal/auth"
	"bcare/api/internal/svc"
	"bcare/api/internal/task"
	"bcare/api/internal/task/tasks"
	"bcare/api/internal/types"
	"bcare/common/bquery"
	"bcare/common/butils"
	cast2 "bcare/common/cast"
	"bcare/ent"
	"bcare/ent/personreferral"
	"context"
	"github.com/hibiken/asynq"
	"github.com/jinzhu/copier"
	"github.com/samber/lo"
	"github.com/zeromicro/go-zero/core/logx"
	"strings"
	"time"
)

type UpdateLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateLogic(ctx context.Context, svcCtx *svc.ServiceContext) UpdateLogic {
	return UpdateLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateLogic) Update(req *types.PersonUpdateRequest) (resp *types.PersonResponse, err error) {
	if req.Phone == "" && butils.Contains(req.Modified, "phone") {
		return nil, types.ErrPersonUpdate.Wrap(nil, "phone should not be empty")
	}

	personRecord, err := l.svcCtx.Ent.Person.Get(l.ctx, req.Id)
	if err != nil && ent.IsNotFound(err) {
		return nil, types.ErrPersonNotFound.Wrap(err, req.Id, err.Error())
	}

	if personRecord == nil {
		logx.Errorf("person not found")
		return nil, types.ErrPersonNotFound.Wrap(err, req.Id)
	}

	//kiểm tra xem có quyền ko
	if !l.svcCtx.Auth.Person.CanUpdate(l.ctx, personRecord) {
		logx.WithContext(l.ctx).Infof("Bạn ko có quyền")
		return nil, auth.ErrPersonUpdateDenied
	}
	req.Phone = strings.TrimSpace(req.Phone)
	code := ""
	if req.PersonField.Code != nil {
		code = strings.TrimSpace(*req.PersonField.Code)
		req.PersonField.Code = &code
	}
	duplicated, err := NewSharedLogic(l.ctx, l.svcCtx).checkDuplicated(req.Phone, code, personRecord.ID)
	if err != nil {
		return nil, types.ErrPersonExist.Wrap(err, "Error checking for duplicate person")
	}

	if duplicated {
		return nil, types.ErrPersonExist.Wrap(nil, "Person with phone %s or code %s already exists", req.Phone, req.PersonField.Code)

	}

	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		err = copier.CopyWithOption(personRecord, req, cast2.PayloadToEnt)
		if err != nil {
			logx.Errorf("copier copy person failed, err:%v", err)
			return types.ErrPersonCopy.Wrap(err, "Error copying person data")
		}

		updateQuery := tx.Person.UpdateOneID(req.Id).SetPerson(personRecord, req.Modified...)

		personRecord, err = updateQuery.Save(l.ctx)
		if err != nil {
			return types.ErrPersonUpdate.Wrap(err, err.Error())
		}

		// Xử lý ReferredBy
		if req.ReferredBy != nil && req.ReferredBy.ReferrerId > 0 {
			referralRecord, err := tx.PersonReferral.Query().Where(personreferral.ReferredID(req.Id)).Only(l.ctx)
			if err != nil && ent.IsNotFound(err) {
				// Tạo mới dữ liệu referred_by nếu không tồn tại
				referralRecord, err = tx.PersonReferral.Create().
					SetReferredID(req.Id).
					SetReferrerID(req.ReferredBy.ReferrerId).
					SetRelationship(req.ReferredBy.Relationship).
					SetNote(req.ReferredBy.Note).
					Save(l.ctx)
				if err != nil {
					return types.ErrPersonReferralCreate.Wrap(err)
				}
			} else if err == nil {
				// Cập nhật dữ liệu referred_by nếu đã tồn tại
				_, err = tx.PersonReferral.UpdateOneID(referralRecord.ID).
					SetReferrerID(req.ReferredBy.ReferrerId).
					SetRelationship(req.ReferredBy.Relationship).
					SetNote(req.ReferredBy.Note).
					Save(l.ctx)
				if err != nil {
					return types.ErrPersonReferralUpdate.Wrap(err)
				}
			}
		} else if (req.ReferredBy == nil || req.ReferredBy.ReferrerId == 0) && lo.Contains(req.Modified, "referred_by") {
			_, err = tx.PersonReferral.Delete().Where(personreferral.ReferredID(req.Id)).Exec(l.ctx)
			if err != nil {
				return types.ErrPersonReferralUpdate.Wrap(err)
			}
		}

		if l.svcCtx.Config.SyncGetFly {
			t, err := tasks.NewGetFlyCreateUpdateTask(personRecord.ID)
			if err != nil {
				logx.WithContext(l.ctx).Errorf("could not create task: %v", err)
			}

			_, err = task.Client.Enqueue(t, asynq.ProcessIn(1*time.Minute))
			if err != nil {
				logx.WithContext(l.ctx).Errorf("could not enqueue task: %v", err)
			}
		}

		return nil
	})
	if err != nil {
		return nil, err
	}
	resp = new(types.PersonResponse)
	_ = cast2.EntToResp(&resp, personRecord)

	return resp, nil
}
