package query

import (
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"github.com/zeromicro/go-zero/core/logx"
)

type RawQueryLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewRawQueryLogic(ctx context.Context, svcCtx *svc.ServiceContext) *RawQueryLogic {
	return &RawQueryLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// convertParameter chuyển đổi Parameter thành giá trị thích hợp dựa trên Type
func convertParameter(param types.Parameter) (interface{}, error) {
	if param.Type == "null" {
		return nil, nil
	}

	if param.Value == "" && param.Type != "string" {
		return nil, nil
	}

	switch param.Type {
	case "string":
		return param.Value, nil
	case "int":
		return strconv.ParseInt(param.Value, 10, 64)
	case "float":
		return strconv.ParseFloat(param.Value, 64)
	case "bool":
		return strconv.ParseBool(param.Value)
	case "date":
		return time.Parse("2006-01-02", param.Value)
	case "time":
		return time.Parse("15:04:05", param.Value)
	case "datetime", "timestamp":
		return time.Parse("2006-01-02 15:04:05", param.Value)
	case "json":
		var result interface{}
		err := json.Unmarshal([]byte(param.Value), &result)
		return result, err
	case "uuid":
		return param.Value, nil // Truyền UUID dưới dạng string
	case "bytea":
		return []byte(param.Value), nil
	case "array":
		// Xử lý mảng dựa trên ArrayElementType
		var arr []interface{}
		err := json.Unmarshal([]byte(param.Value), &arr)
		if err != nil {
			return nil, err
		}

		// Nếu không có ArrayElementType, trả về mảng nguyên bản
		if param.ArrayElementType == "" {
			return arr, nil
		}

		// Chuyển đổi từng phần tử trong mảng
		for i, elem := range arr {
			elemStr, ok := elem.(string)
			if !ok {
				elemStr = fmt.Sprintf("%v", elem)
			}

			// Tạo Parameter tạm thời cho phần tử
			elemParam := types.Parameter{
				Type:  param.ArrayElementType,
				Value: elemStr,
			}

			// Chuyển đổi phần tử
			convertedElem, err := convertParameter(elemParam)
			if err != nil {
				return nil, fmt.Errorf("error converting array element %d: %w", i, err)
			}

			arr[i] = convertedElem
		}

		return arr, nil
	default:
		return nil, fmt.Errorf("unsupported parameter type: %s", param.Type)
	}
}

func (l *RawQueryLogic) RawQuery(req *types.RawQueryRequest) (*types.RawQueryResponse, error) {
	// Kiểm tra quyền truy cập (nếu cần)
	// TODO: Thêm logic kiểm tra quyền ở đây

	// Thiết lập timeout nếu được chỉ định
	var cancel context.CancelFunc
	if req.Timeout > 0 {
		l.ctx, cancel = context.WithTimeout(l.ctx, time.Duration(req.Timeout)*time.Second)
		defer cancel()
	}

	startTime := time.Now()

	// Chuẩn bị tham số
	args := make([]interface{}, 0, len(req.Parameters))
	for _, param := range req.Parameters {
		convertedValue, err := convertParameter(param)
		if err != nil {
			return nil, fmt.Errorf("parameter conversion error: %w", err)
		}
		args = append(args, convertedValue)
	}

	// Sử dụng QueryService từ ServiceContext để xác thực và chuẩn bị truy vấn
	query, queryArgs, err := l.svcCtx.QueryService.PrepareQuery(req.SQL, args...)
	if err != nil {
		return nil, err
	}

	// Thực thi truy vấn với bob executor
	rows, err := l.svcCtx.Bob.QueryContext(l.ctx, query, queryArgs...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	// Xử lý kết quả
	columns, err := rows.Columns()
	if err != nil {
		return nil, err
	}

	var results []map[string]interface{}
	var rowCount int64

	for rows.Next() {
		rowCount++

		// Tạo slice chứa các con trỏ để scan dữ liệu vào
		values := make([]interface{}, len(columns))
		pointers := make([]interface{}, len(columns))
		for i := range values {
			pointers[i] = &values[i]
		}

		// Scan dữ liệu từ row hiện tại
		if err := rows.Scan(pointers...); err != nil {
			return nil, err
		}

		// Tạo map cho row hiện tại
		rowMap := make(map[string]interface{})
		for i, col := range columns {
			val := pointers[i].(*interface{})
			rowMap[col] = *val
		}

		// Xử lý các kiểu dữ liệu đặc biệt
		for k, v := range rowMap {
			switch value := v.(type) {
			case []byte:
				// Thử parse dưới dạng JSON
				var js interface{}
				if json.Unmarshal(value, &js) == nil {
					rowMap[k] = js
				} else {
					// Nếu không phải JSON, giữ nguyên dạng string
					rowMap[k] = string(value)
				}
			case time.Time:
				// Format thời gian thành chuỗi ISO
				rowMap[k] = value.Format(time.RFC3339)
			}
		}

		results = append(results, rowMap)
	}

	if err := rows.Err(); err != nil {
		return nil, err
	}

	executionTime := time.Since(startTime).Milliseconds()

	return &types.RawQueryResponse{
		Results:       results,
		RowCount:      rowCount,
		ExecutionTime: float64(executionTime),
	}, nil
}
