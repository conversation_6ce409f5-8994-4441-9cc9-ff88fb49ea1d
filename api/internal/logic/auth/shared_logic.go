package auth

import (
	"bcare/api/internal/svc"
	"context"
	"github.com/golang-jwt/jwt/v4"
	"github.com/zeromicro/go-zero/core/logx"
)

type SharedLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewSharedLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SharedLogic {
	return &SharedLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *SharedLogic) generateToken(secretKey string, iat, expired int64, payload any, isRefresh bool) (tokenStr string, err error) {
	tokenClaims := make(jwt.MapClaims)
	tokenClaims["exp"] = iat + expired
	tokenClaims["iat"] = iat
	tokenClaims["id"] = payload
	if isRefresh {
		tokenClaims["is_refresh_token"] = true // Đ<PERSON>y là một refresh token
	} else {
		tokenClaims[l.svcCtx.Config.Auth.IdentityKey] = payload
	}

	token := jwt.New(jwt.SigningMethodHS256)
	token.Claims = tokenClaims

	tokenStr, err = token.SignedString([]byte(secretKey))
	if err != nil {
		return "", err
	}
	return
}
