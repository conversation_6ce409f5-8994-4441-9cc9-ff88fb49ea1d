package auth

import (
	"bcare/api/internal/middleware"
	"context"
	"net/http"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

const (
	csrfCookieName = "X-CSRF-Token"
)

type GenerateCsrfLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
	writer http.ResponseWriter // Thêm response writer
}

func NewGenerateCsrfLogic(ctx context.Context, w http.ResponseWriter, svcCtx *svc.ServiceContext) GenerateCsrfLogic {
	return GenerateCsrfLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
		writer: w, // Truyền response writer từ handler
	}
}

func (l *GenerateCsrfLogic) GenerateCsrf() (resp *types.CsrfTokenResponse, err error) {
	// Lấy token mới từ TokenStore
	token := middleware.GetCSRFToken(l.svcCtx.CSRFTokenStore)

	// Trả về token và thời gian hết hạn cho frontend
	return &types.CsrfTokenResponse{
		Token: token,
	}, nil
}
