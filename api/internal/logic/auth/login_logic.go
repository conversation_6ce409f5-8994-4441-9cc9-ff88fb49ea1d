package auth

import (
	"bcare/api/model"
	"bcare/common/bconst"
	cast2 "bcare/common/cast"
	"bcare/ent"
	"bcare/ent/user"
	"context"
	"fmt"
	"github.com/jinzhu/copier"
	"golang.org/x/crypto/bcrypt"
	"strconv"
	"strings"
	"time"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type LoginLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewLoginLogic(ctx context.Context, svcCtx *svc.ServiceContext) LoginLogic {
	return LoginLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *LoginLogic) Login(req *types.LoginRequest, clientIP string) (resp *types.LoginResponse, err error) {
	// Find and validate user
	userRecord, e := l.svcCtx.Ent.User.Query().Where(user.Username(req.Username), user.StatusNEQ(int8(bconst.StatusDeActive))).First(l.ctx)
	if e != nil && !ent.IsNotFound(e) {
		err = types.ErrWrongUserName.Wrap(e)
		return
	}

	if userRecord == nil {
		err = types.ErrWrongUserName
		return
	}

	// Check password
	e = bcrypt.CompareHashAndPassword([]byte(userRecord.Password), []byte(req.Password))
	if e != nil {
		err = types.ErrWrongPassword.Wrap(e)
		return
	}

	settingOtp := l.svcCtx.Setting.Otp

	// Check if client IP is in whitelist
	ipInWhitelist := false
	for _, whitelistedIP := range settingOtp.WhitelistIp {
		if whitelistedIP == clientIP {
			ipInWhitelist = true
			break
		}
	}

	if !ipInWhitelist {
		for _, whitelistedIP := range settingOtp.DynamicIpAllowlist {
			if whitelistedIP == clientIP {
				ipInWhitelist = true
				break
			}
		}
	}

	// Check if OTP is enabled
	if !settingOtp.Enabled || ipInWhitelist {
		// If OTP is disabled, generate tokens directly
		now := time.Now().Unix()
		accessExpired := l.svcCtx.Config.Auth.AccessExpire
		refreshExpired := l.svcCtx.Config.Auth.RefreshExpire

		accessToken, err := NewSharedLogic(l.ctx, l.svcCtx).generateToken(
			l.svcCtx.Config.Auth.AccessSecret,
			now,
			accessExpired,
			userRecord.ID,
			false,
		)
		if err != nil {
			return nil, types.ErrGenerateToken
		}

		refreshToken, err := NewSharedLogic(l.ctx, l.svcCtx).generateToken(
			l.svcCtx.Config.Auth.AccessSecret,
			now,
			refreshExpired,
			userRecord.ID,
			true,
		)
		if err != nil {
			return nil, types.ErrGenerateToken
		}

		resp = &types.LoginResponse{
			Status:        "success",
			Message:       "Login successful",
			AccessExpire:  int(now + accessExpired),
			RefreshAfter:  int(now + accessExpired/2),
			RefreshExpire: int(now + refreshExpired),
			AccessToken:   accessToken,
			RefreshToken:  refreshToken,
		}
		roles, err := l.svcCtx.Enforcer.GetRolesForUser(strconv.Itoa(userRecord.ID))
		err = copier.Copy(&resp.User, userRecord)
		_ = copier.CopyWithOption(&resp.User.Roles, roles, cast2.TimeToString)

		return resp, nil
	}

	// Get receiver emails based on config
	var receiverEmails []string
	if settingOtp.Receivers.Type == "single" {
		if settingOtp.Receivers.SingleEmail == "" {
			return nil, fmt.Errorf("no OTP receiver email configured")
		}
		receiverEmails = []string{settingOtp.Receivers.SingleEmail}
	} else {
		if len(settingOtp.Receivers.EmailList) == 0 {
			return nil, fmt.Errorf("no OTP receiver emails configured")
		}
		receiverEmails = settingOtp.Receivers.EmailList
	}

	// Generate OTP
	otp := model.GenerateOTP(settingOtp.Length)
	expiresAt := time.Now().Add(time.Duration(settingOtp.ExpireMinutes) * time.Minute)

	// Save ONE OTP record for this login attempt
	_, err = l.svcCtx.Ent.OTP.Create().
		SetUserID(userRecord.ID).
		SetCode(otp).
		SetPurpose("login").
		SetExpiresAt(expiresAt).
		SetSentTo(strings.Join(receiverEmails, ",")). // Store all recipient emails
		Save(l.ctx)

	if err != nil {
		logx.Errorf("failed to save OTP: %v", err)
		return nil, fmt.Errorf("failed to save OTP")
	}

	for _, email := range receiverEmails {
		go func(emailAddr string) {
			if err := l.svcCtx.Email.SendEmailOtp(
				emailAddr,
				otp,
				userRecord.Username,
				clientIP,
			); err != nil {
				logx.Errorf("failed to send OTP to %s: %v", emailAddr, err)
			}
		}(email)
	}

	// Prepare masked emails for response
	var maskedEmails []string
	if settingOtp.Receivers.Type == "single" {
		maskedEmails = []string{maskSensitiveInfo(settingOtp.Receivers.SingleEmail)}
	} else {
		maskedEmails = make([]string, len(receiverEmails))
		for i, email := range receiverEmails {
			maskedEmails[i] = maskSensitiveInfo(email)
		}
	}

	resp = &types.LoginResponse{
		Status:    "pending_verification",
		Message:   "OTP has been sent to administrators",
		SentTo:    strings.Join(maskedEmails, ", "),
		ExpiresIn: settingOtp.ExpireMinutes * 60,
	}

	return
}

// Helper function to mask sensitive information
func maskSensitiveInfo(info string) string {
	if strings.Contains(info, "@") {
		// Mask email
		parts := strings.Split(info, "@")
		if len(parts[0]) > 3 {
			return parts[0][:3] + "***@" + parts[1]
		}
		return parts[0][:1] + "***@" + parts[1]
	}
	// Mask phone number
	if len(info) > 4 {
		return "***" + info[len(info)-4:]
	}
	return "***" + info
}
