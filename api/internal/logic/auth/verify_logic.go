package auth

import (
	cast2 "bcare/common/cast"
	"bcare/ent/otp"
	"bcare/ent/user"
	"context"
	"fmt"
	"github.com/jinzhu/copier"
	"strconv"
	"time"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type VerifyLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewVerifyLogic(ctx context.Context, svcCtx *svc.ServiceContext) VerifyLogic {
	return VerifyLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *VerifyLogic) Verify(req *types.VerifyRequest) (resp *types.VerifyResponse, err error) {
	// Find user
	userRecord, err := l.svcCtx.Ent.User.Query().Where(user.Username(req.Username)).First(l.ctx)
	if err != nil {
		return nil, types.ErrWrongUserName
	}

	// Verify OTP
	otpRecord, err := l.svcCtx.Ent.OTP.Query().
		Where(
			otp.UserID(userRecord.ID),
			otp.Code(req.OTP),
			otp.Purpose("login"),
			otp.IsUsed(false),
			otp.ExpiresAtGT(time.Now()),
		).First(l.ctx)
	if err != nil {
		return nil, fmt.Errorf("invalid or expired OTP")
	}

	// Mark OTP as used
	_, err = l.svcCtx.Ent.OTP.UpdateOne(otpRecord).
		SetIsUsed(true).
		Save(l.ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to update OTP status: %v", err)
	}

	// Update user state
	_, err = l.svcCtx.Ent.User.UpdateOne(userRecord).
		SetState(user.StateOnline).
		Save(l.ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to update user state: %v", err)
	}

	// Generate tokens
	now := time.Now().Unix()
	accessExpired := l.svcCtx.Config.Auth.AccessExpire
	refreshExpired := l.svcCtx.Config.Auth.RefreshExpire

	accessToken, err := NewSharedLogic(l.ctx, l.svcCtx).generateToken(
		l.svcCtx.Config.Auth.AccessSecret,
		now,
		accessExpired,
		userRecord.ID,
		false,
	)
	if err != nil {
		return nil, types.ErrGenerateToken
	}

	refreshToken, err := NewSharedLogic(l.ctx, l.svcCtx).generateToken(
		l.svcCtx.Config.Auth.AccessSecret,
		now,
		refreshExpired,
		userRecord.ID,
		true,
	)
	if err != nil {
		return nil, types.ErrGenerateToken
	}

	roles, err := l.svcCtx.Enforcer.GetRolesForUser(strconv.Itoa(userRecord.ID))

	// Prepare response
	resp = new(types.VerifyResponse)
	err = copier.Copy(&resp.User, userRecord)
	_ = copier.CopyWithOption(&resp.User.Roles, roles, cast2.TimeToString)

	if err != nil {
		return nil, fmt.Errorf("failed to copy user data: %v", err)
	}

	resp.Status = "success"
	resp.AccessToken = accessToken
	resp.RefreshToken = refreshToken
	resp.AccessExpire = int(now + accessExpired)
	resp.RefreshAfter = int(now + accessExpired/2)
	resp.RefreshExpire = int(now + refreshExpired)

	return
}
