package auth

import (
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"context"
	"fmt"
	"github.com/golang-jwt/jwt/v4"
	"time"

	"github.com/zeromicro/go-zero/core/logx"
)

type RefreshLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewRefreshLogic(ctx context.Context, svcCtx *svc.ServiceContext) RefreshLogic {
	return RefreshLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *RefreshLogic) Refresh(req *types.RefreshTokenRequest) (resp *types.RefreshTokenResponse, err error) {
	now := time.Now().Unix()
	expired := l.svcCtx.Config.Auth.AccessExpire
	accessToken, id, e := l.renewAccessToken(l.svcCtx.Config.Auth.AccessSecret, req.RefreshToken, now, expired)
	if e != nil {
		err = types.ErrGenerateToken.Wrap(e)
		return
	}

	resp = &types.RefreshTokenResponse{
		Id:           id,
		AccessToken:  accessToken,
		RefreshToken: req.RefreshToken,
		AccessExpire: int(now + expired),
		RefreshAfter: int(now + expired/2),
	}

	return

}

func (l *RefreshLogic) renewAccessToken(secretKey, refreshTokenStr string, iat, accessTokenExp int64) (string, int, error) {
	refreshToken, err := jwt.Parse(refreshTokenStr, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("invalid signing method: %v", token.Header["alg"])
		}
		return []byte(secretKey), nil
	})
	if err != nil {
		return "", 0, err
	}

	if claims, ok := refreshToken.Claims.(jwt.MapClaims); ok && refreshToken.Valid {
		if isRefreshToken, ok := claims["is_refresh_token"].(bool); ok && isRefreshToken {
			userId, ok := claims["id"].(float64)
			if !ok {
				return "", 0, fmt.Errorf("invalid user id in token")
			}

			accessToken, e := NewSharedLogic(l.ctx, l.svcCtx).generateToken(secretKey, iat, accessTokenExp, int(userId), false)

			if e != nil {
				return "", 0, e
			}

			return accessToken, int(userId), nil
		}
	}

	return "", 0, fmt.Errorf("invalid refresh token")
}
