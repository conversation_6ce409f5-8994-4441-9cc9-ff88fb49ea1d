package material

import (
	"bcare/api/internal/dto"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/berr"
	"bcare/common/cast"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateLogic {
	return &UpdateLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateLogic) Update(req *types.MaterialUpdateRequest) (resp *types.MaterialResponse, err error) {
	existingMaterial, err := l.svcCtx.MaterialModel.GetMaterial(l.ctx, &dto.GetMaterialInput{ID: req.Id})
	if err != nil {
		return nil, err
	}

	if !l.svcCtx.Auth.Material.CanUpdate(l.ctx, existingMaterial) {
		return nil, berr.ErrPermissionDenied.Wrap(nil)
	}

	input := new(dto.UpdateMaterialInput)
	err = cast.ReqToModelInput(input, req)
	if err != nil {
		return nil, err
	}

	materialOutput, err := l.svcCtx.MaterialModel.UpdateMaterial(l.ctx, input)
	if err != nil {
		return nil, err
	}

	resp = new(types.MaterialResponse)
	err = cast.ConvertViaJson(resp, materialOutput)
	if err != nil {
		return nil, err
	}

	return resp, nil
}
