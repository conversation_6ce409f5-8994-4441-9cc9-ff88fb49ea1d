package material

import (
	"bcare/api/internal/dto"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/berr"
	"bcare/common/cast"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type ListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ListLogic {
	return &ListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ListLogic) List(req *types.MaterialListRequest) (resp *types.MaterialListResponse, err error) {
	if !l.svcCtx.Auth.Material.CanList(l.ctx) {
		return nil, berr.ErrPermissionDenied.Wrap(nil)
	}

	input := new(dto.ListMaterialInput)
	err = cast.ReqToModelInput(input, req)
	if err != nil {
		return nil, err // Simplified error handling
	}

	listOutput, err := l.svcCtx.MaterialModel.ListMaterials(l.ctx, input)
	if err != nil {
		return nil, err
	}

	resp = &types.MaterialListResponse{
		Materials: make([]types.MaterialResponse, 0, len(listOutput.Materials)),
		Total:     listOutput.Total,
		TotalPage: listOutput.TotalPage,
	}

	err = cast.ConvertViaJson(&resp.Materials, listOutput.Materials)
	if err != nil {
		return nil, err
	}

	return resp, nil
}
