package material

import (
	"bcare/api/internal/dto"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/berr"
	"bcare/common/cast"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetLogic {
	return &GetLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetLogic) Get(req *types.MaterialGetRequest) (resp *types.MaterialResponse, err error) {
	input := &dto.GetMaterialInput{
		ID:   req.Id,
		Code: req.Code,
		Name: req.Name,
	}

	materialOutput, err := l.svcCtx.MaterialModel.GetMaterial(l.ctx, input)
	if err != nil {
		return nil, err
	}

	if !l.svcCtx.Auth.Material.CanGet(l.ctx, materialOutput) {
		return nil, berr.ErrPermissionDenied.Wrap(nil)
	}

	resp = new(types.MaterialResponse)
	err = cast.ConvertViaJson(resp, materialOutput)
	if err != nil {
		return nil, err
	}

	return resp, nil
}
