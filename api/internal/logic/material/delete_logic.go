package material

import (
	"bcare/api/internal/dto"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/berr"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewDeleteLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteLogic {
	return &DeleteLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DeleteLogic) Delete(req *types.MaterialDeleteRequest) (err error) {
	existingMaterial, err := l.svcCtx.MaterialModel.GetMaterial(l.ctx, &dto.GetMaterialInput{ID: req.Id})
	if err != nil {
		return err
	}

	if !l.svcCtx.Auth.Material.CanDelete(l.ctx, existingMaterial) {
		return berr.ErrPermissionDenied.Wrap(nil)
	}

	input := &dto.DeleteMaterialInput{
		ID:   req.Id,
		Name: req.Name,
	}

	err = l.svcCtx.MaterialModel.DeleteMaterial(l.ctx, input)
	if err != nil {
		return err
	}

	return nil
}
