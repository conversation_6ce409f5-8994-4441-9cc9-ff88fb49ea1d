package public

import (
	"bcare/common/bconst"
	"bcare/common/bquery"
	cast2 "bcare/common/cast"
	"bcare/ent"
	"bcare/ent/deal"
	"bcare/ent/formsubmission"
	"bcare/ent/person"
	types2 "bcare/ent/types"
	"context"
	"github.com/jinzhu/copier"
	"strings"
	"time"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type FormAddLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewFormAddLogic(ctx context.Context, svcCtx *svc.ServiceContext) FormAddLogic {
	return FormAddLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *FormAddLogic) FormAdd(req *types.FormAddRequest) (resp *types.FormResponse, err error) {
	if len(req.Phone) != 10 || !strings.HasPrefix(req.Phone, "0") {
		return nil, types.ErrPersonAdd.Wrap(nil, "Phone number must be 10 digits and start with 0")
	}

	// Check existing person
	existingPerson, err := l.svcCtx.Ent.Person.Query().
		Where(person.Phone(req.Phone)).
		First(l.ctx)
	if err != nil && !ent.IsNotFound(err) {
		return nil, types.ErrPersonAdd.Wrap(err, "Failed to check existing person")
	}

	formSubmissionRecord := new(ent.FormSubmission)
	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		err = copier.CopyWithOption(&formSubmissionRecord, req, cast2.PayloadToEnt)
		if err != nil {
			logx.WithContext(l.ctx).Errorf("cannot copy person: %v", err)
			return types.ErrPersonAdd.Wrap(err, err.Error())
		}

		// Nếu tìm thấy person, gán person_id vào form submission
		if existingPerson != nil {
			formSubmissionRecord.PersonID = &existingPerson.ID
			formSubmissionRecord.State = formsubmission.StateApproved
			now := time.Now()
			formSubmissionRecord.ProcessedAt = &now
		} else {
			personRecord := new(ent.Person)
			personRecord.FullName = formSubmissionRecord.FullName
			personRecord.Phone = formSubmissionRecord.Phone
			personRecord.Email = formSubmissionRecord.Email
			if formSubmissionRecord.SourceID > 0 {
				personRecord.SourceID = &formSubmissionRecord.SourceID
			}
			personRecord.Status = bconst.StatusNormal
			personField := new(types2.PersonMeta)
			personField.SourceChannel = &formSubmissionRecord.ReferrerURL
			personField.FormSource = &formSubmissionRecord.FormName
			personField.LandingPageURL = &formSubmissionRecord.SourceURL
			zalo := "unknown"
			personField.HasZalo = &zalo
			personRecord.PersonField = personField
			personRecord.Gender = "unknown"

			// Lưu Person mới
			personRecord, err = tx.Person.Create().SetPerson(personRecord).Save(l.ctx)
			if err != nil {
				return types.ErrPersonAdd.Wrap(err, err.Error())
			}
			_, err = tx.Deal.Create().
				SetPersonID(personRecord.ID).
				SetState(deal.StateDraft).
				SetName("Niềng răng").
				SetStageID(13).
				Save(l.ctx)
			if err != nil {
				return types.ErrPersonCreate.Wrap(err)
			}

			formSubmissionRecord.PersonID = &personRecord.ID
			formSubmissionRecord.State = formsubmission.StateApproved
			now := time.Now()
			formSubmissionRecord.ProcessedAt = &now
		}

		formSubmissionRecord, err = tx.FormSubmission.Create().SetFormSubmission(formSubmissionRecord).Save(l.ctx)
		if err != nil {
			return types.ErrPersonAdd.Wrap(err, err.Error())
		}

		return nil
	})
	if err != nil {
		return nil, types.ErrPersonAdd.Wrap(err, err.Error())
	}

	resp = new(types.FormResponse)
	_ = cast2.ConvertViaJson(&resp, formSubmissionRecord)
	return resp, nil

}
