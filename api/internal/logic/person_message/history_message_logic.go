package person_message

import (
	cast2 "bcare/common/cast"
	"bcare/ent"
	"bcare/ent/messagehistory"
	"context"
	"github.com/samber/lo"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type HistoryMessageLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewHistoryMessageLogic(ctx context.Context, svcCtx *svc.ServiceContext) HistoryMessageLogic {
	return HistoryMessageLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *HistoryMessageLogic) HistoryMessage(req *types.HistoryRequest) (resp *types.HistoryResponse, err error) {

	query := l.svcCtx.Ent.MessageHistory.Query().Where(messagehistory.PersonID(req.PersonId)).With<PERSON>erson()

	total, _ := query.Count(l.ctx)
	messageHistories, e := query.All(l.ctx)

	if e != nil {
		return nil, e
	}

	resp = new(types.HistoryResponse)
	resp.MessageHistories = lo.Map(messageHistories, func(msgHistory *ent.MessageHistory, _ int) types.MessageHistory {
		t := types.MessageHistory{}
		_ = cast2.ConvertViaJson(&t, msgHistory)
		return t
	})
	resp.Total = total

	return resp, err
}
