package person_message

import (
	"bcare/api/internal/task"
	"bcare/api/internal/task/tasks"
	"bcare/common/bconst"
	"bcare/common/bsms"
	"bcare/common/ctxdata"
	"bcare/ent"
	"bcare/ent/messagehistory"
	"context"
	"encoding/json"
	"time"

	"github.com/hibiken/asynq"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type SendMessageLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewSendMessageLogic(ctx context.Context, svcCtx *svc.ServiceContext) SendMessageLogic {
	return SendMessageLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *SendMessageLogic) SendZns(req *types.SendMessageRequest) (err error, errCode int, msgId string) {
	var params bsms.TemplateData
	err = json.Unmarshal([]byte(req.ZnsParams), &params)
	if err != nil {
		return
	}

	znsTpl := &bsms.ZnsTemplate{
		Phone:        req.Phone,
		TemplateID:   req.ZnsTemplateId,
		TemplateData: params,
	}
	return l.svcCtx.Zns.SendMessage(znsTpl)
}

func (l *SendMessageLogic) SendMessage(req *types.SendMessageRequest) (resp *types.SendMessageResponse, err error) {
	//TODO mail
	var msgHistoryRecord *ent.MessageHistory
	var errCode int
	var msgId string
	if req.ZnsTemplateId != "" {
		err, errCode, msgId = l.SendZns(req)
		msgHistoryRecord = &ent.MessageHistory{
			PersonID:      req.PersonId,
			Phone:         req.Phone,
			UserID:        ctxdata.GetUidFromCtx(l.ctx),
			ZnsData:       req.ZnsParams,
			Content:       req.ZnsContent,
			Type:          messagehistory.TypeZns,
			ErrorCode:     errCode,
			MessageID:     msgId,
			MessageStatus: messagehistory.MessageStatusFailed,
			Status:        bconst.StatusNormal,
		}
		if err == nil && errCode == 0 {
			msgHistoryRecord.MessageStatus = messagehistory.MessageStatusSent
		}
		msgHistoryRecord, _ = l.svcCtx.Ent.MessageHistory.Create().SetMessageHistory(msgHistoryRecord).Save(l.ctx)
		if msgHistoryRecord.MessageStatus == messagehistory.MessageStatusSent {
			t, err := tasks.NewZnsUpdateTask(msgHistoryRecord.MessageID, bsms.FormatPhone(req.Phone))
			if err != nil {
				logx.WithContext(l.ctx).Errorf("could not create task: %v", err)
			}

			_, err = task.Client.Enqueue(t, asynq.ProcessIn(5*time.Minute), asynq.Queue(task.QueueName))
			if err != nil {
				logx.WithContext(l.ctx).Errorf("could not enqueue task: %v", err)
			}
		}

		if err != nil && req.FallbackSms {
			err, errCode = l.svcCtx.Sms.SendMessage(req.Phone, req.SmsContent)
			msgHistoryRecord = &ent.MessageHistory{
				PersonID:      req.PersonId,
				Phone:         req.Phone,
				UserID:        ctxdata.GetUidFromCtx(l.ctx),
				Content:       req.SmsContent,
				Type:          messagehistory.TypeSms,
				ErrorCode:     errCode,
				MessageID:     "",
				MessageStatus: messagehistory.MessageStatusFailed,
				Status:        bconst.StatusNormal,
			}
			if err == nil && errCode == 0 {
				msgHistoryRecord.MessageStatus = messagehistory.MessageStatusSent
			}
			_, _ = l.svcCtx.Ent.MessageHistory.Create().SetMessageHistory(msgHistoryRecord).Save(l.ctx)
			return nil, err
		}

		if err == nil {
			return &types.SendMessageResponse{}, nil
		}
	} else {
		err, errCode = l.svcCtx.Sms.SendMessage(req.Phone, req.SmsContent)
		msgHistoryRecord = &ent.MessageHistory{
			PersonID:      req.PersonId,
			Phone:         req.Phone,
			UserID:        ctxdata.GetUidFromCtx(l.ctx),
			Content:       req.SmsContent,
			Type:          messagehistory.TypeSms,
			ErrorCode:     errCode,
			MessageID:     "",
			MessageStatus: messagehistory.MessageStatusFailed,
			Status:        bconst.StatusNormal,
		}
		if err == nil && errCode == 0 {
			msgHistoryRecord.MessageStatus = messagehistory.MessageStatusSent
		}
		_, err = l.svcCtx.Ent.MessageHistory.Create().SetMessageHistory(msgHistoryRecord).Save(l.ctx)
		if err != nil {
			return nil, err
		}
		return &types.SendMessageResponse{}, nil
	}

	return nil, err
}
