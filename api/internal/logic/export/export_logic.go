package export

import (
	"bcare/api/model"
	"bcare/common/bquery"
	"bcare/common/butils"
	"bcare/common/ctxdata"
	"bcare/ent"
	"bcare/ent/userdata"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"time"

	"github.com/google/uuid"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

const asyncThreshold = 10000

type ExportLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewExportLogic(ctx context.Context, svcCtx *svc.ServiceContext) ExportLogic {
	return ExportLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ExportLogic) Export(req *types.ExportRequest) (resp *types.ExportJobResponse, err error) {
	l.<PERSON>gger.Infof("Received export request: SQL=%s, Headers=%d, TotalRecord=%d", req.SQL, len(req.Headers), req.TotalRecord)

	userId := ctxdata.GetUidFromCtx(l.ctx)
	startTime := time.Now()

	if req.SQL == "" {
		return nil, fmt.Errorf("bad request: sql query cannot be empty")
	}
	if len(req.Headers) == 0 {
		return nil, fmt.Errorf("bad request: headers configuration cannot be empty")
	}

	isAsync := req.TotalRecord > asyncThreshold

	internalJobID := uuid.NewString()
	newJobData := types.ExportJobData{
		InternalID:       internalJobID,
		State:            types.StatePending,
		EstimatedRecords: req.TotalRecord,
		StartedAt:        startTime.Format("2006-01-02 15:04:05"),
	}

	var userDataID int
	var finalState string

	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		ud, err := tx.UserData.Query().
			Where(
				userdata.UserID(userId),
				userdata.Kind(types.UserDataKindExportJobs),
			).
			Only(l.ctx)

		var currentJobs types.UserDataExportJobs

		if err != nil {
			if ent.IsNotFound(err) {
				l.Logger.Infof("No existing UserData record found for user %d and kind %s. Creating new one.", userId, types.UserDataKindExportJobs)
				currentJobs.Jobs = make([]types.ExportJobData, 0, 1)
				currentJobs.Jobs = append(currentJobs.Jobs, newJobData)

				jobsMap, mapErr := butils.StructToMap(currentJobs)
				if mapErr != nil {
					return fmt.Errorf("failed to marshal initial jobs data: %w", mapErr)
				}

				newUd, createErr := tx.UserData.Create().
					SetUserID(userId).
					SetKind(types.UserDataKindExportJobs).
					SetData(jobsMap).
					Save(l.ctx)
				if createErr != nil {
					return fmt.Errorf("failed to create new UserData record: %w", createErr)
				}
				userDataID = newUd.ID
				l.Logger.Infof("Created new UserData record ID: %d", userDataID)
			} else {
				return fmt.Errorf("failed to query UserData record: %w", err)
			}
		} else {
			userDataID = ud.ID
			l.Logger.Infof("Found existing UserData record ID: %d. Appending new job %s.", userDataID, internalJobID)

			if ud.Data != nil {
				currentDataBytes, jsonErr := json.Marshal(ud.Data)
				if jsonErr != nil {
					return fmt.Errorf("failed to marshal existing data map: %w", jsonErr)
				}
				if unmarshalErr := json.Unmarshal(currentDataBytes, &currentJobs); unmarshalErr != nil {
					l.Logger.Errorf("Failed to unmarshal existing jobs data for UserData ID %d: %v. Overwriting with new job list.", userDataID, unmarshalErr)
					currentJobs.Jobs = make([]types.ExportJobData, 0, 1)
				}
			} else {
				currentJobs.Jobs = make([]types.ExportJobData, 0, 1)
			}

			currentJobs.Jobs = append(currentJobs.Jobs, newJobData)

			updatedJobsMap, mapErr := butils.StructToMap(currentJobs)
			if mapErr != nil {
				return fmt.Errorf("failed to marshal updated jobs data: %w", mapErr)
			}

			_, updateErr := tx.UserData.UpdateOneID(userDataID).
				SetData(updatedJobsMap).
				Save(l.ctx)
			if updateErr != nil {
				return fmt.Errorf("failed to update UserData record %d: %w", userDataID, updateErr)
			}
			l.Logger.Infof("Appended job %s to UserData record ID: %d", internalJobID, userDataID)
		}
		finalState = types.StatePending
		return nil
	})

	if err != nil {
		l.Logger.Errorf("Transaction failed when adding export job: %v", err)
		return nil, fmt.Errorf("failed to submit export job: %w", err)
	}

	if err != nil {
		l.Logger.Errorf("Failed to create initial export job record for user %d: %v", userId, err)
		return nil, fmt.Errorf("failed to initialize export job: %w", err)
	}

	if isAsync {
		resp = &types.ExportJobResponse{
			JobID:   internalJobID,
			State:   finalState,
			Message: "Async export job submitted (implementation pending). Check status later.",
		}
		return resp, nil
	} else {
		l.Logger.Infof("Handling export synchronously...")
		syncResp, syncErr := l.handleSyncExportAndUpdate(l.ctx, req, userDataID, internalJobID, startTime)
		if syncErr != nil {
			l.Logger.Errorf("Failed to handle sync export: %v", syncErr)
			return nil, fmt.Errorf("failed to generate excel file synchronously: %w", syncErr)
		}
		return syncResp, nil
	}
}

func findAndUpdateJobInSlice(jobs []types.ExportJobData, internalJobID string, updateFunc func(job *types.ExportJobData)) ([]types.ExportJobData, bool) {
	found := false
	for i := range jobs {
		if jobs[i].InternalID == internalJobID {
			updateFunc(&jobs[i])
			found = true
			break
		}
	}
	return jobs, found
}

func (l *ExportLogic) handleSyncExportAndUpdate(ctx context.Context, req *types.ExportRequest, userDataID int, internalJobID string, jobStartTime time.Time) (*types.ExportJobResponse, error) {
	l.Logger.Infof("Starting sync process for job %s in UserData %d", internalJobID, userDataID)

	txErr := bquery.WithTx(ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		ud, err := tx.UserData.Query().
			Where(userdata.ID(userDataID)).
			Only(ctx)
		if err != nil {
			return fmt.Errorf("failed to get UserData %d for processing update: %w", userDataID, err)
		}

		var currentJobs types.UserDataExportJobs
		if ud.Data != nil {
			dataBytes, _ := json.Marshal(ud.Data)
			json.Unmarshal(dataBytes, &currentJobs)
		} else {
			currentJobs.Jobs = make([]types.ExportJobData, 0)
		}

		updatedJobs, found := findAndUpdateJobInSlice(currentJobs.Jobs, internalJobID, func(job *types.ExportJobData) {
			job.State = types.StateProcessing
			l.Logger.Infof("Updating job %s state to %s", internalJobID, types.StateProcessing)
		})

		if !found {
			return fmt.Errorf("job %s not found in UserData %d during processing update", internalJobID, userDataID)
		}
		currentJobs.Jobs = updatedJobs

		updatedJobsMap, mapErr := butils.StructToMap(currentJobs)
		if mapErr != nil {
			return fmt.Errorf("failed to marshal jobs for processing update: %w", mapErr)
		}

		_, updateErr := tx.UserData.UpdateOneID(userDataID).SetData(updatedJobsMap).Save(ctx)
		if updateErr != nil {
			return fmt.Errorf("failed to save processing update for UserData %d: %w", userDataID, updateErr)
		}

		return nil
	})

	if txErr != nil {
		l.Logger.Errorf("Transaction failed updating job %s to processing: %v", internalJobID, txErr)
		return nil, txErr
	}

	var fetchedResults []map[string]interface{}
	var actualCount int64

	readErr := func() error {
		udRead, err := l.svcCtx.Ent.UserData.Get(ctx, userDataID)
		if err != nil {
			return fmt.Errorf("failed to re-read UserData %d to get job details: %w", userDataID, err)
		}
		var jobsRead types.UserDataExportJobs
		if udRead.Data != nil {
			dataBytes, _ := json.Marshal(udRead.Data)
			json.Unmarshal(dataBytes, &jobsRead)
		}
		found := false
		for _, job := range jobsRead.Jobs {
			if job.InternalID == internalJobID {
				found = true
				break
			}
		}
		if !found {
			return fmt.Errorf("job %s not found in UserData %d when retrieving details", internalJobID, userDataID)
		}
		return nil
	}()
	if readErr != nil {
		_ = l.updateJobFailed(userDataID, internalJobID, fmt.Sprintf("Failed to retrieve job details: %v", readErr))
		return nil, readErr
	}

	l.Logger.Infof("Job %s: Fetching data...", internalJobID)
	fetchStartTime := time.Now()
	args := make([]interface{}, 0, len(req.Parameters))
	for _, param := range req.Parameters {
		convertedValue, err := model.ConvertParameter(param)
		if err != nil {
			errMsg := fmt.Sprintf("parameter conversion error: %v", err)
			_ = l.updateJobFailed(userDataID, internalJobID, errMsg)
			return nil, errors.New(errMsg)
		}
		args = append(args, convertedValue)
	}
	query, queryArgs, err := l.svcCtx.QueryService.PrepareQuery(req.SQL, args...)
	if err != nil {
		errMsg := fmt.Sprintf("query preparation failed: %v", err)
		_ = l.updateJobFailed(userDataID, internalJobID, errMsg)
		return nil, errors.New(errMsg)
	}
	rows, err := l.svcCtx.Bob.QueryContext(ctx, query, queryArgs...)
	if err != nil {
		errMsg := fmt.Sprintf("database query execution failed: %v", err)
		_ = l.updateJobFailed(userDataID, internalJobID, errMsg)
		return nil, errors.New(errMsg)
	}
	defer rows.Close()
	columns, err := rows.Columns()
	if err != nil {
		errMsg := fmt.Sprintf("failed to read result columns: %v", err)
		_ = l.updateJobFailed(userDataID, internalJobID, errMsg)
		return nil, errors.New(errMsg)
	}

	for rows.Next() {
		actualCount++
		values := make([]interface{}, len(columns))
		pointers := make([]interface{}, len(columns))
		for i := range values {
			pointers[i] = &values[i]
		}
		if err := rows.Scan(pointers...); err != nil {
			errMsg := fmt.Sprintf("error scanning row: %v", err)
			_ = l.updateJobFailed(userDataID, internalJobID, errMsg)
			return nil, errors.New(errMsg)
		}
		rowMap := make(map[string]interface{})
		for i, col := range columns {
			val := pointers[i].(*interface{})
			rowMap[col] = model.ProcessScannedValue(*val)
		}
		fetchedResults = append(fetchedResults, rowMap)
	}
	if err := rows.Err(); err != nil {
		errMsg := fmt.Sprintf("error after iterating rows: %v", err)
		_ = l.updateJobFailed(userDataID, internalJobID, errMsg)
		return nil, errors.New(errMsg)
	}
	fetchDuration := time.Since(fetchStartTime)
	l.Logger.Infof("Job %s: Data fetched (%d records) in %v", internalJobID, actualCount, fetchDuration)

	l.Logger.Infof("Job %s: Generating and saving Excel file...", internalJobID)
	now := time.Now()
	storagePath := fmt.Sprintf("%s/%s/%s", l.svcCtx.Config.Storage.Local.Path, "reports", fmt.Sprintf("%d_%02d_%02d", now.Year(), now.Month(), now.Day()))
	filePath, _, genErr := model.GenerateAndSaveExcelFile(ctx, internalJobID, req.Headers, fetchedResults, storagePath)
	if genErr != nil {
		errMsg := fmt.Sprintf("Excel generation/save failed: %v", genErr)
		_ = l.updateJobFailed(userDataID, internalJobID, errMsg)
		return nil, errors.New(errMsg)
	}
	l.Logger.Infof("Job %s: Excel file saved to %s", internalJobID, filePath)

	var finalState string
	completedAt := time.Now()
	fileExpirationDuration := 48 * time.Hour
	expiresAt := completedAt.Add(fileExpirationDuration)

	txErr = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		ud, err := tx.UserData.Query().
			Where(userdata.ID(userDataID)).
			Only(ctx)
		if err != nil {
			return fmt.Errorf("failed to get UserData %d for processing update: %w", userDataID, err)
		}

		var currentJobs types.UserDataExportJobs
		if ud.Data != nil {
			dataBytes, _ := json.Marshal(ud.Data)
			json.Unmarshal(dataBytes, &currentJobs)
		} else {
			return fmt.Errorf("critical: UserData %d has nil data during completion update", userDataID)
		}

		updatedJobs, found := findAndUpdateJobInSlice(currentJobs.Jobs, internalJobID, func(job *types.ExportJobData) {
			job.State = types.StateCompleted
			job.FilePath = filePath
			job.ActualRecords = int(actualCount)
			job.Progress = 100.0
			job.CompletedAt = completedAt.Format("2006-01-02 15:04:05")
			job.ExpiresAt = expiresAt.Format("2006-01-02 15:04:05")
			job.ErrorMessage = ""
			l.Logger.Infof("Updating job %s state to %s, FilePath: %s", internalJobID, types.StateCompleted, filePath)
		})

		if !found {
			return fmt.Errorf("job %s not found in UserData %d during completion update", internalJobID, userDataID)
		}
		currentJobs.Jobs = updatedJobs
		finalState = types.StateCompleted

		updatedJobsMap, mapErr := butils.StructToMap(currentJobs)
		if mapErr != nil {
			return fmt.Errorf("failed to marshal jobs for completion update: %w", mapErr)
		}

		_, updateErr := tx.UserData.UpdateOneID(userDataID).SetData(updatedJobsMap).Save(ctx)
		if updateErr != nil {
			return updateErr
		}

		return nil
	})

	if txErr != nil {
		l.Logger.Errorf("Transaction failed updating job %s to completed: %v", internalJobID, txErr)
		return nil, fmt.Errorf("file generated for job %s but failed to update final status: %w", internalJobID, txErr)
	}

	l.Logger.Infof("Job %s: Synchronous export completed successfully.", internalJobID)
	resp := &types.ExportJobResponse{
		JobID:   internalJobID,
		State:   finalState,
		Message: "Export completed successfully and file is ready for download.",
	}
	return resp, nil
}

func (l *ExportLogic) updateJobFailed(userDataID int, internalJobID string, errMsg string) error {
	l.Logger.Errorf("Updating job %s in UserData %d to failed. Reason: %s", internalJobID, userDataID, errMsg)
	ctx, cancel := context.WithTimeout(context.Background(), 15*time.Second)
	defer cancel()
	completedAt := time.Now()

	txErr := bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		ud, err := tx.UserData.Query().
			Where(userdata.ID(userDataID)).
			Only(ctx)
		if err != nil {
			if ent.IsNotFound(err) {
				l.Logger.Errorf("Cannot update job %s to failed: UserData record %d not found.", internalJobID, userDataID)
				return nil
			}
			return fmt.Errorf("failed to get UserData %d for failure update: %w", userDataID, err)
		}

		var currentJobs types.UserDataExportJobs
		if ud.Data != nil {
			dataBytes, _ := json.Marshal(ud.Data)
			json.Unmarshal(dataBytes, &currentJobs)
		} else {
			log.Printf("UserData %d has nil data when trying to mark job %s as failed.", userDataID, internalJobID)
			currentJobs.Jobs = make([]types.ExportJobData, 0)
		}

		updatedJobs, found := findAndUpdateJobInSlice(currentJobs.Jobs, internalJobID, func(job *types.ExportJobData) {
			if job.State != types.StateCompleted && job.State != types.StateFailed {
				job.State = types.StateFailed
				job.ErrorMessage = errMsg
				job.CompletedAt = completedAt.Format("2006-01-02 15:04:05")
				job.Progress = 0
				l.Logger.Infof("Updating job %s state to %s", internalJobID, types.StateFailed)
			}
		})

		if !found {
			return nil
		}
		currentJobs.Jobs = updatedJobs

		updatedJobsMap, mapErr := butils.StructToMap(currentJobs)
		if mapErr != nil {
			return fmt.Errorf("failed to marshal jobs for failure update: %w", mapErr)
		}

		_, updateErr := tx.UserData.UpdateOneID(userDataID).SetData(updatedJobsMap).Save(ctx)
		if updateErr != nil {
			return fmt.Errorf("failed to save failure update for UserData %d: %w", userDataID, updateErr)
		}

		return nil
	})

	if txErr != nil {
		l.Logger.Errorf("Transaction failed updating job %s to failed: %v", internalJobID, txErr)
	}
	return txErr
}
