package export

// This file is temporarily commented out due to missing dependencies
// TODO: Implement proper export service after creating the required packages

/*
import (
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/bquery"
	"bcare/common/butils"
	"bcare/common/ctxdata"
	"bcare/ent"
	"bcare/ent/userdata"
	"context"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"sync"
	"time"

	"github.com/google/uuid"
	"github.com/zeromicro/go-zero/core/logx"
)

// Comment out the export service functionality for now since the packages don't exist
// Singleton ExportService instance
var (
	exportServiceInstance *export.ExportService
	exportServiceOnce     sync.Once
)

type ExportDataLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewExportDataLogic(ctx context.Context, svcCtx *svc.ServiceContext) ExportDataLogic {
	return ExportDataLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// getExportService returns ExportService instance (prefer svcCtx if available)
func (l *ExportDataLogic) getExportService() *export.ExportService {
	// If service registered in ServiceContext via exportsvc, use it
	if l.svcCtx.ExportService != nil {
		if svcPtr, ok := l.svcCtx.ExportService.(*export.ExportService); ok {
			return svcPtr
		}
		// Log type assertion failure but continue
		l.Logger.Errorf("svcCtx.ExportService has unexpected type, falling back to singleton")
	}

	// Fallback to singleton inside logic
	exportServiceOnce.Do(func() {
		exportServiceInstance = export.NewExportService()
		exporters.RegisterAllExporters(exportServiceInstance, l.svcCtx)
	})
	return exportServiceInstance
}

func (l *ExportDataLogic) ExportData(req *types.ExportDataRequest) (resp *types.ExportJobResponse, err error) {
	// Temporarily return error until export service is implemented
	return nil, fmt.Errorf("export service not yet implemented")

	// ... existing code continues below but commented out for compilation
}

func (l *ExportDataLogic) handleSyncExportAndUpdate(ctx context.Context, req *types.ExportDataRequest, userDataID int, internalJobID string, jobStartTime time.Time) (*types.ExportJobResponse, error) {
	l.Logger.Infof("Starting sync process for job %s in UserData %d", internalJobID, userDataID)

	// Update to Processing state (Transaction 2)
	txErr := bquery.WithTx(ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		ud, err := tx.UserData.Query().
			Where(userdata.ID(userDataID)).
			Only(ctx)
		if err != nil {
			return fmt.Errorf("failed to get UserData %d for processing update: %w", userDataID, err)
		}

		var currentJobs types.UserDataExportJobs
		if ud.Data != nil {
			dataBytes, _ := json.Marshal(ud.Data)
			json.Unmarshal(dataBytes, &currentJobs)
		} else {
			currentJobs.Jobs = make([]types.ExportJobData, 0)
		}

		updatedJobs, found := updateJobInSlice(currentJobs.Jobs, internalJobID, func(job *types.ExportJobData) {
			job.State = types.StateProcessing
			l.Logger.Infof("Updating job %s state to %s", internalJobID, types.StateProcessing)
		})

		if !found {
			return fmt.Errorf("job %s not found in UserData %d during processing update", internalJobID, userDataID)
		}
		currentJobs.Jobs = updatedJobs

		updatedJobsMap, mapErr := butils.StructToMap(currentJobs)
		if mapErr != nil {
			return fmt.Errorf("failed to marshal jobs for processing update: %w", mapErr)
		}

		_, updateErr := tx.UserData.UpdateOneID(userDataID).SetData(updatedJobsMap).Save(ctx)
		if updateErr != nil {
			return fmt.Errorf("failed to save processing update for UserData %d: %w", userDataID, updateErr)
		}

		return nil
	})

	if txErr != nil {
		l.Logger.Errorf("Transaction failed updating job %s to processing: %v", internalJobID, txErr)
		return nil, txErr
	}

	// Get ExportService and generate Excel
	l.Logger.Infof("Job %s: Generating Excel file using ExportService...", internalJobID)
	exportService := l.getExportService()

	params := req.Params
	if params == nil {
		params = make(map[string]interface{})
	}

	excelBytes, fileName, err := exportService.ExportData(l.ctx, req.ReportType, params)
	if err != nil {
		errMsg := fmt.Sprintf("ExportService failed for report_type=%s: %v", req.ReportType, err)
		_ = l.updateJobFailed(userDataID, internalJobID, errMsg)
		return nil, fmt.Errorf(errMsg)
	}

	l.Logger.Infof("Job %s: Excel data generated, size=%d bytes, filename=%s", internalJobID, len(excelBytes), fileName)

	// Save file to storage
	now := time.Now()
	storagePath := fmt.Sprintf("%s/%s/%s", l.svcCtx.Config.Storage.Local.Path, "reports", fmt.Sprintf("%d_%02d_%02d", now.Year(), now.Month(), now.Day()))

	// Create directory if not exists
	if err := os.MkdirAll(storagePath, 0755); err != nil {
		errMsg := fmt.Sprintf("Failed to create storage directory: %v", err)
		_ = l.updateJobFailed(userDataID, internalJobID, errMsg)
		return nil, fmt.Errorf(errMsg)
	}

	filePath := filepath.Join(storagePath, fileName)
	if err := os.WriteFile(filePath, excelBytes, 0644); err != nil {
		errMsg := fmt.Sprintf("Failed to save Excel file: %v", err)
		_ = l.updateJobFailed(userDataID, internalJobID, errMsg)
		return nil, fmt.Errorf(errMsg)
	}

	l.Logger.Infof("Job %s: Excel file saved to %s", internalJobID, filePath)

	// Update to Completed state (Transaction 3)
	var finalState string
	completedAt := time.Now()
	fileExpirationDuration := 48 * time.Hour
	expiresAt := completedAt.Add(fileExpirationDuration)
	actualRecords := len(excelBytes) // Use file size as record count estimate

	txErr = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		ud, err := tx.UserData.Query().
			Where(userdata.ID(userDataID)).
			Only(ctx)
		if err != nil {
			return fmt.Errorf("failed to get UserData %d for completion update: %w", userDataID, err)
		}

		var currentJobs types.UserDataExportJobs
		if ud.Data != nil {
			dataBytes, _ := json.Marshal(ud.Data)
			json.Unmarshal(dataBytes, &currentJobs)
		} else {
			return fmt.Errorf("critical: UserData %d has nil data during completion update", userDataID)
		}

		updatedJobs, found := updateJobInSlice(currentJobs.Jobs, internalJobID, func(job *types.ExportJobData) {
			job.State = types.StateCompleted
			job.FilePath = filePath
			job.ActualRecords = actualRecords
			job.Progress = 100.0
			job.CompletedAt = completedAt.Format("2006-01-02 15:04:05")
			job.ExpiresAt = expiresAt.Format("2006-01-02 15:04:05")
			job.ErrorMessage = ""
			l.Logger.Infof("Updating job %s state to %s, FilePath: %s", internalJobID, types.StateCompleted, filePath)
		})

		if !found {
			return fmt.Errorf("job %s not found in UserData %d during completion update", internalJobID, userDataID)
		}
		currentJobs.Jobs = updatedJobs
		finalState = types.StateCompleted

		updatedJobsMap, mapErr := butils.StructToMap(currentJobs)
		if mapErr != nil {
			return fmt.Errorf("failed to marshal jobs for completion update: %w", mapErr)
		}

		_, updateErr := tx.UserData.UpdateOneID(userDataID).SetData(updatedJobsMap).Save(ctx)
		if updateErr != nil {
			return updateErr
		}

		return nil
	})

	if txErr != nil {
		l.Logger.Errorf("Transaction failed updating job %s to completed: %v", internalJobID, txErr)
		return nil, fmt.Errorf("file generated for job %s but failed to update final status: %w", internalJobID, txErr)
	}

	l.Logger.Infof("Job %s: Export data completed successfully.", internalJobID)
	resp := &types.ExportJobResponse{
		JobID:   internalJobID,
		State:   finalState,
		Message: "Export completed successfully and file is ready for download.",
	}
	return resp, nil
}

func (l *ExportDataLogic) updateJobFailed(userDataID int, internalJobID string, errMsg string) error {
	l.Logger.Errorf("Updating job %s in UserData %d to failed. Reason: %s", internalJobID, userDataID, errMsg)
	ctx, cancel := context.WithTimeout(context.Background(), 15*time.Second)
	defer cancel()
	completedAt := time.Now()

	txErr := bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		ud, err := tx.UserData.Query().
			Where(userdata.ID(userDataID)).
			Only(ctx)
		if err != nil {
			if ent.IsNotFound(err) {
				l.Logger.Errorf("Cannot update job %s to failed: UserData record %d not found.", internalJobID, userDataID)
				return nil
			}
			return fmt.Errorf("failed to get UserData %d for failure update: %w", userDataID, err)
		}

		var currentJobs types.UserDataExportJobs
		if ud.Data != nil {
			dataBytes, _ := json.Marshal(ud.Data)
			json.Unmarshal(dataBytes, &currentJobs)
		} else {
			l.Logger.Errorf("UserData %d has nil data when trying to mark job %s as failed.", userDataID, internalJobID)
			currentJobs.Jobs = make([]types.ExportJobData, 0)
		}

		updatedJobs, found := updateJobInSlice(currentJobs.Jobs, internalJobID, func(job *types.ExportJobData) {
			if job.State != types.StateCompleted && job.State != types.StateFailed {
				job.State = types.StateFailed
				job.ErrorMessage = errMsg
				job.CompletedAt = completedAt.Format("2006-01-02 15:04:05")
				job.Progress = 0
				l.Logger.Infof("Updating job %s state to %s", internalJobID, types.StateFailed)
			}
		})

		if !found {
			return nil
		}
		currentJobs.Jobs = updatedJobs

		updatedJobsMap, mapErr := butils.StructToMap(currentJobs)
		if mapErr != nil {
			return fmt.Errorf("failed to marshal jobs for failure update: %w", mapErr)
		}

		_, updateErr := tx.UserData.UpdateOneID(userDataID).SetData(updatedJobsMap).Save(ctx)
		if updateErr != nil {
			return fmt.Errorf("failed to save failure update for UserData %d: %w", userDataID, updateErr)
		}

		return nil
	})

	if txErr != nil {
		l.Logger.Errorf("Transaction failed updating job %s to failed: %v", internalJobID, txErr)
	}
	return txErr
}

// updateJobInSlice finds and updates a job in the slice by internalJobID
func updateJobInSlice(jobs []types.ExportJobData, internalJobID string, updateFunc func(job *types.ExportJobData)) ([]types.ExportJobData, bool) {
	found := false
	for i := range jobs {
		if jobs[i].InternalID == internalJobID {
			updateFunc(&jobs[i])
			found = true
			break
		}
	}
	return jobs, found
}
*/
