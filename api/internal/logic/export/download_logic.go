package export

import (
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/ent"
	"bcare/ent/userdata"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"os"
	"path/filepath"

	"github.com/zeromicro/go-zero/core/logx"
)

type DownloadLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewDownloadLogic(ctx context.Context, svcCtx *svc.ServiceContext) DownloadLogic {
	return DownloadLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DownloadLogic) Download(req *types.DownloadJobRequest) (filePath string, downloadFileName string, err error) {
	userId := req.UserId       // L<PERSON><PERSON> từ request
	internalJobID := req.JobID // <PERSON><PERSON><PERSON> từ request
	l.Logger.Infof("Processing download request for UserDataID: %d, InternalJobID: %s", userId, internalJobID)

	// Fetch UserData record by UserDataID and Kind
	ud, err := l.svcCtx.Ent.UserData.Query().
		Where(
			userdata.UserID(userId),
			userdata.Kind(types.UserDataKindExportJobs), // Đảm bảo đúng kind
		).
		Only(l.ctx)

	if err != nil {
		if ent.IsNotFound(err) {
			l.Logger.Infof("UserData record not found for kind '%s' and ID: %d", types.UserDataKindExportJobs, userId)
			return "", "", fmt.Errorf("job record container not found")
		}
		l.Logger.Errorf("Database error fetching UserData record %d: %v", userId, err)
		return "", "", fmt.Errorf("internal server error retrieving job details: %w", err)
	}

	// Unmarshal the data field
	var currentJobs types.UserDataExportJobs
	if ud.Data == nil {
		l.Logger.Errorf("UserData record %d found, but 'data' field is nil.", userId)
		return "", "", errors.New("internal error: job data container is missing")
	}
	dataBytes, jsonErr := json.Marshal(ud.Data)
	if jsonErr != nil {
		l.Logger.Errorf("UserData %d: Failed to marshal 'data' field: %v", userId, jsonErr)
		return "", "", errors.New("internal error: failed to process job data")
	}
	if err := json.Unmarshal(dataBytes, &currentJobs); err != nil {
		l.Logger.Errorf("UserData %d: Failed to unmarshal 'data' field into job list: %v", userId, err)
		return "", "", errors.New("internal error: failed to read job data structure")
	}

	// Find the specific job in the list
	var targetJob *types.ExportJobData
	for i := range currentJobs.Jobs {
		if currentJobs.Jobs[i].InternalID == internalJobID {
			targetJob = &currentJobs.Jobs[i] // Lấy con trỏ đến job tìm thấy
			break
		}
	}

	if targetJob == nil {
		l.Logger.Infof("Job with InternalID '%s' not found within UserData record %d", internalJobID, userId)
		return "", "", fmt.Errorf("specific job not found")
	}

	// Check job state from the found job data
	if targetJob.State != types.StateCompleted {
		l.Logger.Infof("Job %s download attempt failed because state is '%s', not '%s'", internalJobID, targetJob.State, types.StateCompleted)
		errMsg := fmt.Sprintf("job not completed yet (status: %s)", targetJob.State)
		if targetJob.State == types.StateFailed {
			errMsg = fmt.Sprintf("job failed and cannot be downloaded (reason: %s)", targetJob.ErrorMessage)
		}

		return "", "", errors.New(errMsg)
	}

	// Get file path from data
	if targetJob.FilePath == "" {
		l.Logger.Errorf("File path is missing or empty in data for completed job %s (UserData %d)", internalJobID, userId)
		return "", "", errors.New("internal error: export file path not found for this job")
	}
	filePath = targetJob.FilePath

	// --- File existence and type check (giữ nguyên) ---
	fileInfo, err := os.Stat(filePath)
	if err != nil {
		if errors.Is(err, os.ErrNotExist) {
			l.Logger.Infof("Physical file not found at path '%s' for job %s (UserData %d). Record exists.", filePath, internalJobID, userId)
			// TODO: Cập nhật trạng thái job thành failed/expired?
			return "", "", fmt.Errorf("exported file not found on storage, it might have expired or been cleaned up")
		}
		l.Logger.Errorf("Error accessing file '%s' for job %s: %v", filePath, internalJobID, err)
		return "", "", fmt.Errorf("internal server error accessing export file: %w", err)
	}
	if fileInfo.IsDir() {
		l.Logger.Errorf("Expected a file but found a directory at path '%s' for job %s.", filePath, internalJobID)
		return "", "", errors.New("internal error: invalid export file entry")
	}

	downloadFileName = filepath.Base(filePath)
	l.Logger.Infof("Download validation successful for job %s (UserData %d). Serving file: %s as %s", internalJobID, userId, filePath, downloadFileName)
	return filePath, downloadFileName, nil
}
