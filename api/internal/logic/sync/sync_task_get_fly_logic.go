package sync

import (
	"bcare/api/model"
	"context"
	"log"
	"sync"
	"time"

	"bcare/api/internal/svc"

	"github.com/zeromicro/go-zero/core/logx"
)

const (
	// maxConcurrentDetailFetches limits the number of concurrent GetTaskDetail calls.
	maxConcurrentDetailFetches = 10
)

type SyncTaskGetFlyLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewSyncTaskGetFlyLogic(ctx context.Context, svcCtx *svc.ServiceContext) SyncTaskGetFlyLogic {
	return SyncTaskGetFlyLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *SyncTaskGetFlyLogic) SyncTaskGetFly() error {
	var wg sync.WaitGroup

	for receiverID := range model.ReceiverIDMap {
		wg.Add(1)

		go func(id string) {
			defer wg.Done()

			tasks, err := model.GetTasksForReceivers(id)
			if err != nil {
				l.<PERSON>("Error fetching tasks for receiver %s: %v", id, err)
				return
			}
			l.Infof("Successfully fetched %d tasks for receiver %s", len(tasks), id)

			var detailWg sync.WaitGroup

			// Create a semaphore channel to limit concurrent GetTaskDetail calls
			sem := make(chan struct{}, maxConcurrentDetailFetches)

			// Now, iterate through the fetched tasks and get details for each concurrently
			for _, task := range tasks {
				if task.TaskStatus == "10" || task.TaskStatus == "11" || task.TaskStatus == "101" {
					// Increment detailWg for each task detail goroutine
					detailWg.Add(1)

					// Introduce a small delay before attempting to acquire semaphore and launch goroutine
					time.Sleep(600 * time.Millisecond)

					// Acquire a spot in the semaphore before launching the goroutine
					sem <- struct{}{}

					// Launch a goroutine to fetch details for this specific task
					go func(t model.TaskDetail) {
						// Release the spot in the semaphore when done, and decrement WaitGroup
						defer func() {
							<-sem // Release semaphore spot
							detailWg.Done()
						}()

						taskDetail, err := model.GetTaskDetail(t.TaskID, l.ctx, l.svcCtx.Ent)
						if err != nil {
							l.Errorf("Error fetching detail for task %s (Receiver %s): %v", t.TaskID, id, err)
							return
						}

						if taskDetail != nil && len(taskDetail.Accounts) > 0 {
							l.Infof("Task Detail for %s: Name='%s', Account='%s'", taskDetail.TaskInfo.TaskID, taskDetail.TaskInfo.TaskName, taskDetail.Accounts[0].AccountName)
							log.Printf("Task Detail for %s: Name='%s', Account='%s'", taskDetail.TaskInfo.TaskID, taskDetail.TaskInfo.TaskName, taskDetail.Accounts[0].AccountName)
						} else if taskDetail != nil {
							l.Infof("Task Detail for %s: Name='%s', No associated account", taskDetail.TaskInfo.TaskID, taskDetail.TaskInfo.TaskName)
						} else {
							l.Errorf("Received nil task detail struct for task %s without json unmarshal error", t.TaskID)
						}

					}(task)
				}

			}

			detailWg.Wait()

			log.Println("Finished processing tasks for receiver:", id)

		}(receiverID)
	}

	wg.Wait()

	l.Info("Finished processing all receivers.")
	return nil
}
