package sync

import (
	"bcare/api/internal/task"
	"bcare/api/internal/task/tasks"
	"context"
	"sync"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type SyncDataUpLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewSyncDataUpLogic(ctx context.Context, svcCtx *svc.ServiceContext) SyncDataUpLogic {
	return SyncDataUpLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *SyncDataUpLogic) SyncDataUp(req *types.SyncDataRequest) (resp *types.PersonResponse, err error) {
	var wg sync.WaitGroup
	var sem = make(chan struct{}, 50) // <PERSON><PERSON><PERSON><PERSON> hạn số lượng goroutine đồng thời là 10

	for i := req.FromId; i <= req.ToId; i++ {
		sem <- struct{}{} // Acquire a semaphore
		wg.Add(1)
		go func(id int) {
			defer func() {
				<-sem // Release the semaphore
				wg.Done()
			}()

			t, err := tasks.NewSyncDataUpTask(id)
			if err != nil {
				logx.WithContext(l.ctx).Errorf("could not create task: %v", err)
				return
			}

			_, err = task.Client.Enqueue(t)
			if err != nil {
				logx.WithContext(l.ctx).Errorf("could not enqueue task: %v", err)
			}
		}(i)
	}

	wg.Wait()

	return
}
