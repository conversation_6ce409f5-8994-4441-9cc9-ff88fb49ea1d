package sync

import (
	"bcare/api/internal/svc"
	"bcare/api/internal/task"
	"bcare/api/internal/task/tasks"
	"bcare/api/internal/types"
	"bcare/api/model"
	"context"
	"sync"

	"github.com/hibiken/asynq"
	"github.com/zeromicro/go-zero/core/logx"
)

type SyncDataGetFlyLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewSyncDataGetFlyLogic(ctx context.Context, svcCtx *svc.ServiceContext) SyncDataGetFlyLogic {
	return SyncDataGetFlyLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *SyncDataGetFlyLogic) SyncDataGetFly(req *types.SyncDataRequest) (resp *types.PersonResponse, err error) {
	batchSize := 2000
	currentID := req.FromId
	totalProcessed := 0
	batchCount := 0
	page := req.FromId/batchSize + 1

	for currentID <= req.ToId {
		//Lấy batch hiện tại
		data, err := model.GetAllAccounts(batchSize, page)
		if err != nil {
			logx.WithContext(l.ctx).Errorf("Error fetching batch starting from ID %d: %v", currentID, err)
			return nil, err
		}

		// Check if no more data
		if len(data) == 0 {
			logx.WithContext(l.ctx).Infof("No more data found after processing %d records in %d batches", totalProcessed, batchCount)
			break
		}

		batchCount++
		logx.WithContext(l.ctx).Infof("Processing batch %d with %d records, starting from ID %d", batchCount, len(data), currentID)

		// Process batch hiện tại
		var wg sync.WaitGroup
		var sem = make(chan struct{}, 50)

		for i, v := range data {
			sem <- struct{}{} // Acquire semaphore
			wg.Add(1)
			go func(id int, account model.AccountSync) {
				defer func() {
					<-sem // Release semaphore
					wg.Done()
				}()

				t, err := tasks.NewGetFlySyncAllTask(account)
				if err != nil {
					logx.WithContext(l.ctx).Errorf("could not create task for account %v: %v", account.AccountID, err)
					return
				}

				_, err = task.Client.Enqueue(t, asynq.Queue(task.QueueName))
				if err != nil {
					logx.WithContext(l.ctx).Errorf("could not enqueue task for account %v: %v", account.AccountID, err)
				}
			}(i, v)
		}

		// Đợi batch hiện tại hoàn thành
		wg.Wait()

		// Update counters
		totalProcessed += len(data)
		logx.WithContext(l.ctx).Infof("Completed batch %d, total processed: %d records", batchCount, totalProcessed)

		//// Move to next batch
		currentID += batchSize
		page += 1
	}

	// Final summary log
	logx.WithContext(l.ctx).Infof("Processing completed. Total records: %d, Total batches: %d", totalProcessed, batchCount)

	return
}
