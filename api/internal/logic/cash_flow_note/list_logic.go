package cash_flow_note

import (
	"bcare/api/internal/dto"
	"context"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/berr"
	"bcare/common/cast"

	"github.com/zeromicro/go-zero/core/logx"
)

type ListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewListLogic(ctx context.Context, svcCtx *svc.ServiceContext) ListLogic {
	return ListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ListLogic) List(req *types.CashFlowNoteListRequest) (resp *types.CashFlowNoteListResponse, err error) {
	if !l.svcCtx.Auth.CashFlowNote.CanList(l.ctx) {
		return nil, berr.ErrPermissionDenied.Wrap(nil)
	}

	input := new(dto.ListCashFlowNoteInput)
	err = cast.ReqToModelInput(input, req)
	if err != nil {
		return nil, err
	}

	listOutput, err := l.svcCtx.CashFlowNoteModel.List(l.ctx, *input)
	if err != nil {
		return nil, err
	}

	resp = &types.CashFlowNoteListResponse{
		Notes: make([]types.CashFlowNoteResponse, 0, len(listOutput.CashFlowNotes)),
		Total: listOutput.Total,
	}

	err = cast.ConvertViaJson(&resp.Notes, listOutput.CashFlowNotes)
	if err != nil {
		return nil, err
	}

	return resp, nil
}
