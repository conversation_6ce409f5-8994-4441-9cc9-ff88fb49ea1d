package cash_flow_note

import (
	"bcare/common/berr"
	"context"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewDeleteLogic(ctx context.Context, svcCtx *svc.ServiceContext) DeleteLogic {
	return DeleteLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DeleteLogic) Delete(req *types.CashFlowNoteDeleteRequest) (resp *types.CashFlowNote, err error) {
	existingNote, err := l.svcCtx.CashFlowNoteModel.Get(l.ctx, req.Id)
	if err != nil {
		return nil, err
	}

	if !l.svcCtx.Auth.CashFlowNote.CanDelete(l.ctx, existingNote) {
		return nil, berr.ErrPermissionDenied.Wrap(nil)
	}

	err = l.svcCtx.CashFlowNoteModel.Delete(l.ctx, req.Id)
	if err != nil {
		return nil, err
	}

	resp = new(types.CashFlowNote)
	return resp, nil
}
