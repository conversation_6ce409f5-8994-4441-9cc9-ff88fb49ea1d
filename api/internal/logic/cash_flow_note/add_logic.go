package cash_flow_note

import (
	"bcare/api/internal/dto"
	"context"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/berr"
	"bcare/common/cast"

	"github.com/zeromicro/go-zero/core/logx"
)

type AddLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAddLogic(ctx context.Context, svcCtx *svc.ServiceContext) AddLogic {
	return AddLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AddLogic) Add(req *types.CashFlowNoteAddRequest) (resp *types.CashFlowNote, err error) {
	if !l.svcCtx.Auth.CashFlowNote.CanAdd(l.ctx) {
		return nil, berr.ErrPermissionDenied.Wrap(nil)
	}

	input := new(dto.CreateCashFlowNoteInput)
	err = cast.ReqToModelInput(input, req)
	if err != nil {
		return nil, err
	}

	noteOutput, err := l.svcCtx.CashFlowNoteModel.Create(l.ctx, input)
	if err != nil {
		return nil, err
	}

	resp = new(types.CashFlowNote)
	err = cast.ConvertViaJson(resp, noteOutput)
	if err != nil {
		return nil, err
	}

	return resp, nil
}
