package cash_flow_note

import (
	"bcare/api/internal/dto"
	"context"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/berr"
	"bcare/common/cast"

	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateLogic(ctx context.Context, svcCtx *svc.ServiceContext) UpdateLogic {
	return UpdateLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateLogic) Update(req *types.CashFlowNoteUpdateRequest) (resp *types.CashFlowNote, err error) {
	existingNote, err := l.svcCtx.CashFlowNoteModel.Get(l.ctx, req.Id)
	if err != nil {
		return nil, err
	}

	if !l.svcCtx.Auth.CashFlowNote.CanUpdate(l.ctx, existingNote) {
		return nil, berr.ErrPermissionDenied.Wrap(nil)
	}

	input := new(dto.UpdateCashFlowNoteInput)
	err = cast.ReqToModelInput(input, req)
	if err != nil {
		return nil, err
	}

	noteOutput, err := l.svcCtx.CashFlowNoteModel.Update(l.ctx, input)
	if err != nil {
		return nil, err
	}

	resp = new(types.CashFlowNote)
	err = cast.ConvertViaJson(resp, noteOutput)
	if err != nil {
		return nil, err
	}

	return resp, nil
}
