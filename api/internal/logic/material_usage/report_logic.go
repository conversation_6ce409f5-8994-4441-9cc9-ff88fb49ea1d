package material_usage

import (
	"bcare/api/internal/dto"
	"bcare/common/cast"
	"context"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type ReportLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewReportLogic(ctx context.Context, svcCtx *svc.ServiceContext) ReportLogic {
	return ReportLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ReportLogic) Report(req *types.MaterialUsageReportRequest) (resp *types.MaterialUsageReportResponse, err error) {
	input := new(dto.ReportMaterialUsageInput)
	err = cast.ReqToModelInput(input, req)
	if err != nil {
		return nil, err
	}

	listOutput, err := l.svcCtx.MaterialUsageModel.GetMaterialUsageReport(l.ctx, *input)
	if err != nil {
		return nil, err
	}

	resp = &types.MaterialUsageReportResponse{
		MaterialUsageReport: make([]types.MaterialUsageReport, 0, len(listOutput.MaterialUsageReport)),
		Total:               listOutput.Total,
	}

	err = cast.ConvertViaJson(&resp.MaterialUsageReport, listOutput.MaterialUsageReport)
	if err != nil {
		return nil, err
	}

	return resp, nil
}
