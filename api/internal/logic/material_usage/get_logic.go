package material_usage

import (
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/berr"
	"bcare/common/cast"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetLogic {
	return &GetLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetLogic) Get(req *types.MaterialUsageGetRequest) (resp *types.MaterialUsageResponse, err error) {
	materialUsageOutput, err := l.svcCtx.MaterialUsageModel.GetMaterialUsage(l.ctx, req.Id)
	if err != nil {
		return nil, err
	}

	if !l.svcCtx.Auth.MaterialUsage.CanGet(l.ctx, materialUsageOutput) {
		return nil, berr.ErrPermissionDenied.Wrap(nil)
	}

	resp = new(types.MaterialUsageResponse)
	err = cast.ConvertViaJson(resp, materialUsageOutput)
	if err != nil {
		return nil, err
	}

	return resp, nil
}
