package material_usage

import (
	"context"

	"bcare/api/internal/dto"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/berr"
	"bcare/common/cast"

	"github.com/zeromicro/go-zero/core/logx"
)

type BulkAddLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewBulkAddLogic(ctx context.Context, svcCtx *svc.ServiceContext) BulkAddLogic {
	return BulkAddLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *BulkAddLogic) BulkAdd(req *types.MaterialUsageBulkAddRequest) error {
	if !l.svcCtx.Auth.MaterialUsage.CanAdd(l.ctx) {
		return berr.ErrPermissionDenied.Wrap(nil)
	}

	input := new(dto.MaterialUsageBulkInput)
	err := cast.ConvertViaJson(input, req)
	if err != nil {
		return err
	}

	err = l.svcCtx.MaterialUsageModel.CreateBulkMaterialUsage(l.ctx, input)
	if err != nil {
		return err
	}

	return nil
}
