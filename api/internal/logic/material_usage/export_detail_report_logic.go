package material_usage

import (
	"bcare/api/internal/dto"
	"bcare/api/internal/model"
	"bcare/common/cast"
	"bcare/common/excelutil"
	"context"
	"fmt"
	"net/http"
	"time"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type ExportDetailReportLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewExportDetailReportLogic(ctx context.Context, svcCtx *svc.ServiceContext) ExportDetailReportLogic {
	return ExportDetailReportLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ExportDetailReportLogic) ExportDetailReport(w http.ResponseWriter, req *types.MaterialUsageReportRequest) error {
	input := new(dto.ReportMaterialUsageInput)
	err := cast.ReqToModelInput(input, req)
	if err != nil {
		return err
	}

	listOutput, err := l.svcCtx.MaterialUsageModel.GetMaterialUsageDetailReport(l.ctx, *input)
	if err != nil {
		return err
	}

	var details []types.MaterialUsageDetailRow
	err = cast.ConvertViaJson(&details, listOutput.Items)
	if err != nil {
		return err
	}

	exporter := model.NewMaterialUsageDetailReportExporter(details)
	now := time.Now()
	storagePath := fmt.Sprintf("%s/%s/%s", l.svcCtx.Config.Storage.Local.Path, "reports", fmt.Sprintf("%d_%02d_%02d", now.Year(), now.Month(), now.Day()))
	excelutil.WriteConfigurableExcelResponse(w, nil, exporter, storagePath)

	l.Logger.WithContext(l.ctx).Infof("Material Usage Detail Report exported successfully, total records: %d", len(details))
	return nil
}
