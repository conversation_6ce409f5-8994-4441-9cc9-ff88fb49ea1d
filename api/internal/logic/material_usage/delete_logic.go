package material_usage

import (
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/berr"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewDeleteLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteLogic {
	return &DeleteLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DeleteLogic) Delete(req *types.MaterialUsageDeleteRequest) (err error) {
	existingMaterialUsage, err := l.svcCtx.MaterialUsageModel.GetMaterialUsage(l.ctx, req.Id)
	if err != nil {
		return err
	}

	if !l.svcCtx.Auth.MaterialUsage.CanDelete(l.ctx, existingMaterialUsage) {
		return berr.ErrPermissionDenied.Wrap(nil)
	}

	err = l.svcCtx.MaterialUsageModel.DeleteMaterialUsage(l.ctx, req.Id)
	if err != nil {
		return err
	}

	return nil
}
