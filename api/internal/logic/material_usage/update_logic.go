package material_usage

import (
	"bcare/api/internal/dto"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/berr"
	"bcare/common/cast"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateLogic {
	return &UpdateLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateLogic) Update(req *types.MaterialUsageUpdateRequest) (resp *types.MaterialUsageResponse, err error) {
	existingMaterialUsage, err := l.svcCtx.MaterialUsageModel.GetMaterialUsage(l.ctx, req.Id)
	if err != nil {
		return nil, err
	}

	if !l.svcCtx.Auth.MaterialUsage.CanUpdate(l.ctx, existingMaterialUsage) {
		return nil, berr.ErrPermissionDenied.Wrap(nil)
	}

	input := new(dto.UpdateMaterialUsageInput)
	err = cast.ReqToModelInput(input, req)
	if err != nil {
		return nil, err
	}

	materialUsageOutput, err := l.svcCtx.MaterialUsageModel.UpdateMaterialUsage(l.ctx, input)
	if err != nil {
		return nil, err
	}

	resp = new(types.MaterialUsageResponse)
	err = cast.ConvertViaJson(resp, materialUsageOutput)
	if err != nil {
		return nil, err
	}

	return resp, nil
}
