package material_usage

import (
	"bcare/api/internal/dto"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/berr"
	"bcare/common/cast"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type ListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ListLogic {
	return &ListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ListLogic) List(req *types.MaterialUsageListRequest) (resp *types.MaterialUsageListResponse, err error) {
	if !l.svcCtx.Auth.MaterialUsage.CanList(l.ctx) {
		return nil, berr.ErrPermissionDenied.Wrap(nil)
	}

	input := new(dto.ListMaterialUsageInput)
	err = cast.ReqToModelInput(input, req)
	if err != nil {
		return nil, err
	}

	listOutput, err := l.svcCtx.MaterialUsageModel.ListMaterialUsages(l.ctx, *input)
	if err != nil {
		return nil, err
	}

	resp = &types.MaterialUsageListResponse{
		MaterialUsages: make([]types.MaterialUsageResponse, 0, len(listOutput.MaterialUsages)),
		Total:          listOutput.Total,
	}

	err = cast.ConvertViaJson(&resp.MaterialUsages, listOutput.MaterialUsages)
	if err != nil {
		return nil, err
	}

	return resp, nil
}
