package material_usage

import (
	"bcare/api/internal/dto"
	"bcare/common/cast"
	"context"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type DetailReportLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewDetailReportLogic(ctx context.Context, svcCtx *svc.ServiceContext) DetailReportLogic {
	return DetailReportLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DetailReportLogic) DetailReport(req *types.MaterialUsageReportRequest) (resp *types.MaterialUsageDetailReportResponse, err error) {
	input := new(dto.ReportMaterialUsageInput)
	err = cast.ReqToModelInput(input, req)
	if err != nil {
		return nil, err
	}

	listOutput, err := l.svcCtx.MaterialUsageModel.GetMaterialUsageDetailReport(l.ctx, *input)
	if err != nil {
		return nil, err
	}

	resp = &types.MaterialUsageDetailReportResponse{
		Items: make([]types.MaterialUsageDetailRow, 0, len(listOutput.Items)),
		Total: listOutput.Total,
	}

	err = cast.ConvertViaJson(&resp.Items, listOutput.Items)
	if err != nil {
		return nil, err
	}

	return resp, nil
}
