package product

import (
	"bcare/api/internal/auth"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/bquery"
	cast2 "bcare/common/cast"
	"bcare/ent"
	"bcare/ent/product"
	"context"
	"entgo.io/ent/dialect/sql"
	"github.com/samber/lo"
	"github.com/zeromicro/go-zero/core/logx"
)

type ListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewListLogic(ctx context.Context, svcCtx *svc.ServiceContext) ListLogic {
	return ListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ListLogic) List(req *types.ProductListRequest) (resp *types.ProductListResponse, err error) {
	//kiểm tra xem có quyền ko
	if !l.svcCtx.Auth.Product.CanList(l.ctx, nil) {
		logx.WithContext(l.ctx).Infof("Bạn ko có quyền")
		return nil, auth.ErrProductListDenied
	}

	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		limit, offset := bquery.Paging(req.PageSize, req.Page)
		order := bquery.Ordering(req.OrderBy)
		query := tx.Product.Query().WithCategory().WithOperations().Order(order)

		if req.Search != "" {
			query = query.Where(product.NameContainsFold(req.Search))
		}

		filterMap := make(map[string]interface{})
		_ = cast2.AtomNoZero(&filterMap, req.Filter)

		for field, value := range filterMap {
			query = query.Where(sql.FieldEQ(field, value))
		}

		total, _ := query.Count(l.ctx)
		products, e := query.Limit(limit).Offset(offset).All(l.ctx) // Giả sử limit là 10 và offset là 5

		if e != nil {
			err = types.ErrProductGetList.Wrap(e, err.Error())
			return err
		}

		resp = new(types.ProductListResponse)
		resp.Products = lo.Map(products, func(product *ent.Product, _ int) types.ProductResponse {
			p := types.ProductResponse{}
			_ = cast2.EntToResp(&p, product)
			return p
		})
		resp.Total = total

		return nil
	})

	return resp, err

}
