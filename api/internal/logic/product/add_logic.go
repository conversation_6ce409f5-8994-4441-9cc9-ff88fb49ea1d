package product

import (
	"bcare/api/internal/dto"
	"context"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/berr"
	"bcare/common/cast"

	"github.com/zeromicro/go-zero/core/logx"
)

type AddLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAddLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AddLogic {
	return &AddLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AddLogic) Add(req *types.ProductAddRequest) (*types.ProductResponse, error) {
	if !l.svcCtx.Auth.Product.CanAdd(l.ctx) {
		return nil, berr.ErrPermissionDenied.Wrap(nil)
	}

	createInput := &dto.CreateProductInput{}
	if err := cast.ReqToModelInput(createInput, req); err != nil {
		return nil, err
	}

	productOutput, err := l.svcCtx.ProductModel.Create(l.ctx, createInput)
	if err != nil {
		return nil, err
	}

	resp := &types.ProductResponse{}
	if err := cast.ConvertViaJson(resp, productOutput); err != nil {
		return nil, err
	}

	return resp, nil
}
