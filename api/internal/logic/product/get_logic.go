package product

import (
	"bcare/api/internal/dto"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/berr"
	"bcare/common/cast"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetLogic {
	return &GetLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetLogic) Get(req *types.ProductGetRequest) (*types.ProductResponse, error) {
	input := new(dto.GetProductInput)
	if err := cast.ReqToModelInput(input, req); err != nil {
		return nil, err
	}
	productOutput, err := l.svcCtx.ProductModel.Get(l.ctx, input)
	if err != nil {
		return nil, err
	}

	if !l.svcCtx.Auth.Product.CanGet(l.ctx, productOutput) {
		return nil, berr.ErrPermissionDenied.Wrap(nil)
	}

	resp := new(types.ProductResponse)
	if err := cast.ConvertViaJson(resp, productOutput); err != nil {
		return nil, err
	}

	return resp, nil
}
