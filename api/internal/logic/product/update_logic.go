package product

import (
	"bcare/api/internal/dto"
	"context"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/berr"
	"bcare/common/cast"

	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateLogic {
	return &UpdateLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateLogic) Update(req *types.ProductUpdateRequest) (*types.ProductResponse, error) {
	updateInput := &dto.UpdateProductInput{}
	if err := cast.ReqToModelInput(updateInput, req); err != nil {
		return nil, err
	}

	if updateInput.ID == 0 {
		return nil, berr.ErrRequestParam.Op("UpdateLogic.Update")
	}

	getProductInput := &dto.GetProductInput{ID: updateInput.ID}
	existingProductOutput, err := l.svcCtx.ProductModel.Get(l.ctx, getProductInput)
	if err != nil {
		return nil, err
	}

	if !l.svcCtx.Auth.Product.CanUpdate(l.ctx, existingProductOutput) {
		return nil, berr.ErrPermissionDenied.Wrap(nil)
	}

	updatedProductOutput, err := l.svcCtx.ProductModel.Update(l.ctx, updateInput)
	if err != nil {
		return nil, err
	}

	resp := &types.ProductResponse{}
	if err := cast.ConvertViaJson(resp, updatedProductOutput); err != nil {
		return nil, err
	}

	return resp, nil
}
