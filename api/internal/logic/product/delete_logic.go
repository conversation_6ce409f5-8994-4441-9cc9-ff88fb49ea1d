package product

import (
	"bcare/api/internal/dto"
	"context"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/berr"
	"bcare/common/cast"

	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewDeleteLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteLogic {
	return &DeleteLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DeleteLogic) Delete(req *types.ProductDeleteRequest) (*types.ProductResponse, error) {
	productIDForGet := req.Id
	getProductInput := &dto.GetProductInput{ID: productIDForGet}
	productToDeleteOutput, err := l.svcCtx.ProductModel.Get(l.ctx, getProductInput)
	if err != nil {
		return nil, err
	}

	if !l.svcCtx.Auth.Product.CanDelete(l.ctx, productToDeleteOutput) {
		return nil, berr.ErrPermissionDenied.Wrap(nil)
	}

	_, err = l.svcCtx.ProductModel.DeleteProduct(l.ctx, req.Id)
	if err != nil {
		return nil, err
	}

	resp := &types.ProductResponse{}
	if err := cast.ConvertViaJson(resp, productToDeleteOutput); err != nil {
		return nil, err
	}

	return resp, nil
}
