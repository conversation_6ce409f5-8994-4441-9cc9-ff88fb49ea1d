package deal

import (
	"bcare/api/internal/dto"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/berr"
	"bcare/common/cast"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type ListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewListLogic(ctx context.Context, svcCtx *svc.ServiceContext) ListLogic {
	return ListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ListLogic) List(req *types.DealListRequest) (*types.DealListResponse, error) {
	if !l.svcCtx.Auth.Deal.CanList(l.ctx, nil) {
		return nil, berr.ErrPermissionDenied.Wrap(nil)
	}

	input := dto.ListDealInput{
		PageSize:        req.PageSize,
		Page:            req.<PERSON>,
		OrderBy:         req.OrderBy,
		IncludeRelation: req.IncludeRelation,
	}

	// Handle filter conversion
	if req.Filter.PersonId != 0 {
		input.Filter.PersonID = &req.Filter.PersonId
	}
	if req.Filter.StageId != 0 {
		input.Filter.StageID = &req.Filter.StageId
	}
	if req.PipelineId != 0 {
		input.Filter.PipelineID = &req.PipelineId
	}
	if req.Filter.Status != 0 {
		input.Filter.Status = &req.Filter.Status
	}
	if req.Filter.State != "" {
		input.Filter.State = &req.Filter.State
	}

	// Get deals from DealModel
	listOutput, err := l.svcCtx.DealModel.ListDeals(l.ctx, input)
	if err != nil {
		return nil, err
	}

	// If no relations needed or no deals, return early
	if !req.IncludeRelation || len(listOutput.Deals) == 0 {
		resp := &types.DealListResponse{
			Deals: make([]types.DealResponse, 0, len(listOutput.Deals)),
			Total: listOutput.Total,
		}

		err = cast.ConvertViaJson(&resp.Deals, listOutput.Deals)
		if err != nil {
			return nil, berr.ErrServerCommon.Wrap(err)
		}

		return resp, nil
	}

	// Orchestrate related data fetching for enhanced deals
	enhancedDeals, err := l.enrichDealsWithRelations(listOutput.Deals)
	if err != nil {
		return nil, err
	}

	resp := &types.DealListResponse{
		Deals: make([]types.DealResponse, 0, len(enhancedDeals)),
		Total: listOutput.Total,
	}

	err = cast.ConvertViaJson(&resp.Deals, enhancedDeals)
	if err != nil {
		return nil, berr.ErrServerCommon.Wrap(err)
	}

	return resp, nil
}

// enrichDealsWithRelations orchestrates fetching of tracks and discount usages for deals
func (l *ListLogic) enrichDealsWithRelations(deals []dto.DealOutput) ([]dto.DealOutput, error) {
	if len(deals) == 0 {
		return deals, nil
	}

	dealIDs := make([]int, len(deals))
	for i, deal := range deals {
		dealIDs[i] = deal.ID
	}

	discountUsages, discountErr := l.svcCtx.DiscountUsageModel.ListByDealIDs(l.ctx, dealIDs)

	if discountErr != nil {
		return nil, berr.ErrServerCommon.Wrap(discountErr)
	}

	// Reuse existing MapDealsWithRelations for financial calculations
	err := l.svcCtx.DealModel.MapDealsWithRelations(deals, discountUsages)
	if err != nil {
		return nil, berr.ErrServerCommon.Wrap(err)
	}

	return deals, nil
}
