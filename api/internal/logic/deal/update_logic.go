package deal

import (
	"bcare/api/internal/dto"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/berr"
	"bcare/common/bquery"
	"bcare/common/cast"
	"bcare/ent"
	"context"
	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateLogic {
	return &UpdateLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateLogic) Update(req *types.DealUpdateRequest) (resp *types.DealResponse, err error) {
	if !l.svcCtx.Auth.Deal.CanUpdate(l.ctx, nil) {
		return nil, berr.ErrPermissionDenied.Wrap(nil)
	}

	input := new(dto.UpdateDealInput)
	err = cast.ReqToModelInput(input, req)
	if err != nil {
		return nil, berr.ErrRequestParam.Wrap(err)
	}

	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		dealOutput, err := l.svcCtx.DealModel.UpdateDeal(l.ctx, input)
		if err != nil {
			return err
		}

		// Handle discounts only if provided
		if input.Discounts != nil {
			err = l.applyDiscountsToAttachments(tx, input.ID, input.Discounts)
			if err != nil {
				return err
			}
		}

		// Check if deal state is won or active and create bill items for attachments if needed
		if dealOutput.State == "won" || dealOutput.State == "paying" {
			err = l.createBillItemsForDeal(tx, input.ID)
			if err != nil {
				return err
			}
		}

		// Update the final response
		resp = new(types.DealResponse)
		err = cast.ConvertViaJson(resp, dealOutput)
		if err != nil {
			return berr.ErrServerCommon.Wrap(err)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return resp, nil
}

// applyDiscountsToAttachments orchestrates discount application to deal attachments
func (l *UpdateLogic) applyDiscountsToAttachments(tx *ent.Tx, dealID int, discountIDs []int) error {
	if len(discountIDs) == 0 {
		return nil
	}

	// Delete old discount usages for the deal
	err := l.svcCtx.DiscountUsageModel.DeleteByDealID(l.ctx, dealID)
	if err != nil {
		return err
	}

	// Reset all attachment discounts to 0
	err = l.svcCtx.DealModel.ResetAttachmentDiscounts(l.ctx, tx, dealID)
	if err != nil {
		return err
	}

	// Get all attachments for the deal to distribute discounts
	attachments, err := l.svcCtx.DealModel.GetAttachmentsForDeal(l.ctx, tx, dealID)
	if err != nil {
		return err
	}

	if len(attachments) == 0 {
		return nil
	}

	// Calculate total attachment amount for discount distribution
	var totalAttachmentAmount float64
	for _, att := range attachments {
		totalAttachmentAmount += att.Price * float64(att.Quantity)
	}

	// Process each discount
	attachmentDiscounts := make(map[int]float64)

	for _, discountID := range discountIDs {
		// Get discount details
		discountOutput, err := l.svcCtx.DiscountModel.GetDiscount(l.ctx, discountID)
		if err != nil {
			continue // Skip invalid discounts
		}

		// Calculate discount amount based on discount scope and type
		discountAmount := l.calculateDiscountAmount(discountOutput, totalAttachmentAmount)
		// Create discount usage for the deal
		_, err = l.svcCtx.DiscountUsageModel.CreateDiscountUsage(l.ctx, &dto.CreateDiscountUsageInput{
			DiscountID: discountID,
			DealID:     &dealID,
			PersonID:   &attachments[0].PersonID, // All attachments in a deal have same PersonID
			Value:      discountAmount,
			UsageCount: 1,
		})
		if err != nil {
			continue // Skip if can't create usage
		}

		// Distribute discount amount to attachments
		distributedDiscounts := l.svcCtx.DealModel.DistributeDiscountToAttachments(attachments, discountAmount, totalAttachmentAmount)

		// Accumulate discounts for each attachment
		for attachmentID, amount := range distributedDiscounts {
			attachmentDiscounts[attachmentID] += amount
		}
	}

	// Apply accumulated discounts to attachments
	err = l.svcCtx.DealModel.ApplyDiscountDistribution(l.ctx, tx, attachmentDiscounts)
	if err != nil {
		return err
	}

	return nil
}

// createBillItemsForDeal orchestrates bill item creation for deal attachments
func (l *UpdateLogic) createBillItemsForDeal(tx *ent.Tx, dealID int) error {
	// Get attachments that don't have bill items yet
	attachments, err := l.svcCtx.DealModel.GetAttachmentsWithoutBillItems(l.ctx, tx, dealID)
	if err != nil {
		return err
	}

	if len(attachments) == 0 {
		return nil // No attachments to create bill items for
	}

	// Find installment plans for this deal
	installmentPlans, err := l.svcCtx.InstallmentPlanModel.GetInstallmentPlansForDeal(l.ctx, tx, dealID)
	if err != nil {
		return err
	}

	// If there are installment plans, update attachments with the first plan's ID
	if len(installmentPlans) > 0 {
		firstPlan := installmentPlans[0]

		// Use DealModel to update attachments plan_id (following guideline)
		err = l.svcCtx.DealModel.UpdateAttachmentsPlanID(l.ctx, tx, attachments, firstPlan.ID)
		if err != nil {
			return err
		}
	}

	// Convert ent.Attachment to dto.AttachmentOutput for BillItemModel
	var attachmentOutputs []dto.AttachmentOutput
	err = cast.ConvertViaJson(&attachmentOutputs, attachments)
	if err != nil {
		return berr.ErrCopyFailed.Wrap(err)
	}

	// Create bill items for all attachments
	err = l.svcCtx.BillItemModel.CreateForAttachments(l.ctx, dealID, attachmentOutputs)
	if err != nil {
		return err
	}

	return nil
}

// calculateDiscountAmount calculates discount amount based on discount configuration
func (l *UpdateLogic) calculateDiscountAmount(discount *dto.DiscountOutput, totalAmount float64) float64 {
	switch discount.Type {
	case "percent":
		return totalAmount * discount.Value
	case "fixed":
		return discount.Value
	default:
		return 0.0
	}
}
