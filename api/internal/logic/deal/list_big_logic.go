package deal

import (
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"context"
	"github.com/zeromicro/go-zero/core/logx"
)

type ListBigLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewListBigLogic(ctx context.Context, svcCtx *svc.ServiceContext) ListBigLogic {
	return ListBigLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ListBigLogic) ListBig(req *types.DealListRequest) (resp *types.DealListResponse, err error) {

	return resp, err
}
