package deal

import (
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/berr"
	"bcare/common/cast"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type CheckoutLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewCheckoutLogic(ctx context.Context, svcCtx *svc.ServiceContext) CheckoutLogic {
	return CheckoutLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *CheckoutLogic) Checkout(req *types.DealCheckoutRequest) (resp *types.DealResponse, err error) {
	if !l.svcCtx.Auth.Deal.CanCheckout(l.ctx, nil) {
		return nil, berr.ErrPermissionDenied.Wrap(nil)
	}

	dealOutput, err := l.svcCtx.DealModel.CheckoutDeal(l.ctx, req.Id)
	if err != nil {
		return nil, err
	}

	resp = new(types.DealResponse)
	err = cast.ConvertViaJson(resp, dealOutput)
	if err != nil {
		return nil, berr.ErrServerCommon.Wrap(err)
	}

	return resp, nil
}
