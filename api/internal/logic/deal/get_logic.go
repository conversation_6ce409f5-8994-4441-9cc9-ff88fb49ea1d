package deal

import (
	"bcare/api/internal/dto"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/berr"
	"bcare/common/cast"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetLogic(ctx context.Context, svcCtx *svc.ServiceContext) GetLogic {
	return GetLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// Get retrieves a single deal with optional related data.
func (l *GetLogic) Get(req *types.DealGetRequest) (*types.DealResponse, error) {
	input := &dto.GetDealInput{
		ID:              req.Id,
		IncludeRelation: req.IncludeRelation,
	}

	dealOutput, err := l.svcCtx.DealModel.GetDealWithFullRelations(l.ctx, input)
	if err != nil {
		return nil, err
	}

	if req.IncludeRelation {
		err = l.enrichDealWithRelations(dealOutput)
		if err != nil {
			return nil, err
		}
	}

	resp := new(types.DealResponse)
	err = cast.ConvertViaJson(resp, dealOutput)
	if err != nil {
		return nil, berr.ErrServerCommon.Wrap(err)
	}

	return resp, nil
}

func (l *GetLogic) enrichDealWithRelations(dealOutput *dto.DealOutput) error {
	dealIDs := []int{dealOutput.ID}

	discountUsages, discountUsagesErr := l.svcCtx.DiscountUsageModel.ListByDealIDs(l.ctx, dealIDs)

	if discountUsagesErr != nil {
		return discountUsagesErr
	}

	err := cast.ConvertViaJson(&dealOutput.DiscountUsages, discountUsages)
	if err != nil {
		return berr.ErrCopyFailed.Wrap(err)
	}

	for _, du := range discountUsages {
		dealOutput.DiscountAmount += du.Value * float64(du.UsageCount)
	}

	return nil
}
