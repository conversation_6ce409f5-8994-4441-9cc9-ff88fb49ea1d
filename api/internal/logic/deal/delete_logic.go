package deal

import (
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/berr"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewDeleteLogic(ctx context.Context, svcCtx *svc.ServiceContext) DeleteLogic {
	return DeleteLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DeleteLogic) Delete(req *types.DealDeleteRequest) (resp *types.DealResponse, err error) {
	if !l.svcCtx.Auth.Deal.CanDelete(l.ctx, nil) {
		return nil, berr.ErrPermissionDenied.Wrap(nil)
	}

	err = l.svcCtx.DealModel.DeleteDeal(l.ctx, req.Id)
	if err != nil {
		return nil, err
	}

	return resp, nil
}
