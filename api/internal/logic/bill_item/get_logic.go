package bill_item

import (
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	cast2 "bcare/common/cast"
	"bcare/ent"
	"bcare/ent/billitem"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetLogic(ctx context.Context, svcCtx *svc.ServiceContext) GetLogic {
	return GetLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetLogic) Get(req *types.BillItemGetRequest) (resp *types.BillItemResponse, err error) {
	// Truy vấn dữ liệu BillItem với các quan hệ liên kết
	billItemRecord, err := l.svcCtx.Ent.BillItem.Query().
		WithAttachment(func(q *ent.AttachmentQuery) {
			q.WithProduct().
				WithCreator()
		}).
		WithAllocations().
		Where(billitem.ID(req.Id)).
		Only(l.ctx)

	// Xử lý lỗi không tìm thấy
	if err != nil && ent.IsNotFound(err) {
		return nil, types.ErrBillItemNotFound.Wrap(err, req.Id)
	}

	if billItemRecord == nil {
		return nil, types.ErrBillItemNotFound.Wrap(err, req.Id)
	}

	// Khởi tạo response
	resp = new(types.BillItemResponse)

	// Chuyển đổi từ ent sang response
	err = cast2.ConvertViaJson(resp, billItemRecord)
	if err != nil {
		return nil, types.ErrBillItemNotFound.Wrap(err)
	}

	return resp, nil
}
