package bill_item

import (
	"bcare/api/internal/auth"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/bquery"
	cast2 "bcare/common/cast"
	"bcare/ent"
	"context"
	"entgo.io/ent/dialect/sql"
	"github.com/samber/lo"
	"github.com/zeromicro/go-zero/core/logx"
)

type ListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewListLogic(ctx context.Context, svcCtx *svc.ServiceContext) ListLogic {
	return ListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}
func (l *ListLogic) List(req *types.BillItemListRequest) (resp *types.BillItemListResponse, err error) {
	//kiểm tra xem có quyền ko
	if !l.svcCtx.Auth.BillItem.CanList(l.ctx, nil) {
		logx.WithContext(l.ctx).Infof("Bạn ko có quyền")
		return nil, auth.ErrBillItemListDenied
	}

	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		limit, offset := bquery.Paging(req.PageSize, req.Page)
		order := bquery.Ordering(req.OrderBy)

		query := tx.BillItem.Query().
			WithAttachment(func(q *ent.AttachmentQuery) { q.WithProduct().WithCreator() }).
			WithInstallment().
			WithAllocations().
			Order(order)

		filterMap := make(map[string]interface{})
		_ = cast2.AtomNoZero(&filterMap, req.Filter)

		for field, value := range filterMap {
			query = query.Where(sql.FieldEQ(field, value))
		}

		total, _ := query.Count(l.ctx)
		billItems, e := query.Limit(limit).Offset(offset).All(l.ctx) // Giả sử limit là 10 và offset là 5

		if e != nil {
			err = types.ErrBillItemGetList.Wrap(e)
			return err
		}

		resp = new(types.BillItemListResponse)
		resp.BillItems = lo.Map(billItems, func(bi *ent.BillItem, _ int) types.BillItemResponse {
			b := types.BillItemResponse{}
			_ = cast2.ConvertViaJson(&b, bi)
			return b
		})
		resp.Total = total
		return nil
	})

	return resp, err
}
