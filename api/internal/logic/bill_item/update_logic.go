package bill_item

import (
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/bquery"
	cast2 "bcare/common/cast"
	"bcare/ent"
	"bcare/ent/attachment"
	"context"
	"github.com/jinzhu/copier"

	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateLogic(ctx context.Context, svcCtx *svc.ServiceContext) UpdateLogic {
	return UpdateLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateLogic) Update(req *types.BillItemUpdateRequest) (resp *types.BillItemResponse, err error) {
	billItemRecord := new(ent.BillItem)
	var attachmentRecord *ent.Attachment

	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		billItemRecord, err = tx.BillItem.Get(l.ctx, req.Id)
		if err != nil && ent.IsNotFound(err) {
			return types.ErrBillItemNotFound.Wrap(err, "Bill item ID %d not found", req.Id)
		}

		if billItemRecord == nil {
			return types.ErrBillItemNotFound.Wrap(nil, "Bill item record is nil for ID %d", req.Id)
		}

		err = copier.CopyWithOption(&billItemRecord, req, cast2.PayloadToEnt)
		if err != nil {
			return types.ErrBillItemUpdate.Wrap(err, "Error copying bill item data")
		}

		billItemRecord, err = tx.BillItem.UpdateOneID(req.Id).SetBillItem(billItemRecord, req.Modified...).Save(l.ctx)
		if err != nil {
			return types.ErrBillItemUpdate.Wrap(err, "Error updating bill item record with ID %d", req.Id)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	attachmentRecord, _ = l.svcCtx.Ent.Attachment.Query().
		WithProduct().
		Where(attachment.ID(*billItemRecord.AttachmentID)).First(l.ctx)

	billItemRecord.Edges.Attachment = attachmentRecord

	resp = new(types.BillItemResponse)
	_ = cast2.ConvertViaJson(&resp, billItemRecord)

	return resp, nil
}
