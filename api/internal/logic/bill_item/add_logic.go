package bill_item

import (
	"bcare/api/internal/auth"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/api/model"
	"bcare/common/bquery"
	cast2 "bcare/common/cast"
	"bcare/common/ctxdata"
	"bcare/ent"
	"bcare/ent/attachment"
	"context"
	"github.com/jinzhu/copier"
	"github.com/zeromicro/go-zero/core/logx"
)

type AddLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAddLogic(ctx context.Context, svcCtx *svc.ServiceContext) AddLogic {
	return AddLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AddLogic) Add(req *types.BillItemAddRequest) (resp *types.BillItemResponse, err error) {
	// Check permissions
	if !l.svcCtx.Auth.BillItem.CanAdd(l.ctx, nil) {
		logx.WithContext(l.ctx).Infof("Permission denied")
		return nil, auth.ErrBillItemAddDenied
	}

	if req.AttachmentId == 0 {
		return nil, types.ErrBillItemInvalid.Wrap(nil, "Either Attachment ID or Installment ID must be provided")
	}

	attachmentRecord, err := l.svcCtx.Ent.Attachment.Query().
		WithProduct().
		Where(attachment.ID(req.AttachmentId)).First(l.ctx)

	if err != nil {
		return nil, types.ErrAttachmentNotFound.Wrap(nil, "Attachment not found")
	}

	// Wrap trong transaction để đảm bảo tính nhất quán
	var billItem *ent.BillItem
	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		// Copy request data
		billItem = new(ent.BillItem)
		err = copier.Copy(billItem, req)
		if err != nil {
			return types.ErrBillItemCopyFailed.Wrap(err, "Error copying request data")
		}

		trackRecord, _ := model.GetActiveTrack(l.ctx, tx, attachmentRecord.PersonID)
		if trackRecord != nil {
			billItem.TrackID = trackRecord.ID
		}
		billItem.UserID = ctxdata.GetUidFromCtx(l.ctx)
		billItem.Amount = (attachmentRecord.Price)*float64(attachmentRecord.Quantity) - attachmentRecord.Discount

		// Create bill item
		billItem, err = tx.BillItem.Create().
			SetBillItem(billItem).
			Save(l.ctx)
		if err != nil {
			return types.ErrBillItemAdd.Wrap(err, "Error saving bill item record")
		}

		// Nếu có payment_id, tạo payment allocation
		if req.PaymentId != 0 {
			allocateAmount := req.Amount // Mặc định là full amount
			if req.AllocateAmount != 0 {
				allocateAmount = req.AllocateAmount
			}

			// Create payment allocation
			_, err = tx.PaymentAllocation.Create().
				SetPaymentID(req.PaymentId).
				SetBillItemID(billItem.ID).
				SetAmount(allocateAmount).
				Save(l.ctx)
			if err != nil {
				return types.ErrPaymentAllocationAdd.Wrap(err, "Error creating payment allocation")
			}
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	billItem.Edges.Attachment = attachmentRecord

	// Prepare response
	resp = new(types.BillItemResponse)
	_ = cast2.ConvertViaJson(resp, billItem)

	return resp, nil
}
