package bill_item

import (
	"bcare/api/internal/auth"
	"bcare/api/model"
	"bcare/common/bquery"
	cast2 "bcare/common/cast"
	"bcare/common/ctxdata"
	"bcare/ent"
	"bcare/ent/attachment"
	"bcare/ent/attachmentdata"
	"bcare/ent/billitem"
	"context"
	"encoding/json"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateFromAttachmentLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateFromAttachmentLogic(ctx context.Context, svcCtx *svc.ServiceContext) UpdateFromAttachmentLogic {
	return UpdateFromAttachmentLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateFromAttachmentLogic) UpdateFromAttachment(req *types.BillItemAddFromAttachment) (resp *types.BillItemResponse, err error) {
	// Check permissions
	if !l.svcCtx.Auth.BillItem.CanUpdate(l.ctx, nil) {
		logx.WithContext(l.ctx).Infof("Permission denied")
		return nil, auth.ErrBillItemUpdateDenied
	}

	var billItemRecord *ent.BillItem
	var attachmentRecord *ent.Attachment
	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		// Get attachment to fetch amount
		attachmentRecord, err = tx.Attachment.Query().
			WithProduct().
			WithData(func(query *ent.AttachmentDataQuery) {
				query.Where(attachmentdata.KindEQ("meta"))
			}).
			Where(attachment.ID(req.AttachmentId)).
			First(l.ctx)
		if err != nil {
			if ent.IsNotFound(err) {
				return types.ErrAttachmentNotFound.Wrap(err, "Attachment not found")
			}
			return types.ErrAttachmentNotFound.Wrap(err, "Error getting attachment")
		}

		currentUserId := ctxdata.GetUidFromCtx(l.ctx)
		amount := (attachmentRecord.Price)*float64(attachmentRecord.Quantity) - attachmentRecord.Discount
		trackRecord, _ := model.GetActiveTrack(l.ctx, tx, attachmentRecord.PersonID)

		// Generate note from teeth data
		note := ""
		if len(attachmentRecord.Edges.Data) > 0 {
			for _, data := range attachmentRecord.Edges.Data {
				if teethStr, ok := data.Data["teeth"].(string); ok {
					var teethMap map[string]interface{}
					if err := json.Unmarshal([]byte(teethStr), &teethMap); err == nil {
						note = convertTeethToNote(teethMap)
						break
					}
				}
			}
		}
		if req.Note != "" {
			if note != "" {
				note += ", "
			}
			note += req.Note
		}

		// Check if bill item exists for this attachment
		existingBillItem, err := tx.BillItem.Query().
			Where(billitem.AttachmentID(req.AttachmentId)).
			First(l.ctx)

		if err != nil {
			if !ent.IsNotFound(err) {
				return types.ErrBillItemInvalid.Wrap(err, "Error querying bill item")
			}

			// If not found, create new bill item
			createQuery := tx.BillItem.Create().
				SetAttachmentID(req.AttachmentId).
				SetAmount(amount).
				SetNote(note).
				SetUserID(currentUserId)

			if trackRecord != nil {
				createQuery = createQuery.SetTrackID(trackRecord.ID)
			}

			billItemRecord, err = createQuery.Save(l.ctx)
			if err != nil {
				return types.ErrBillItemAdd.Wrap(err, "Error saving bill item record")
			}
		} else {
			// If found, update existing bill item
			updateQuery := existingBillItem.Update().
				SetAmount(amount).
				SetNote(note)

			if trackRecord != nil {
				updateQuery = updateQuery.SetTrackID(trackRecord.ID)
			}

			billItemRecord, err = updateQuery.Save(l.ctx)
			if err != nil {
				return types.ErrBillItemUpdate.Wrap(err, "Error updating bill item record")
			}
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	// Prepare response
	resp = new(types.BillItemResponse)
	_ = cast2.EntToResp(&resp, billItemRecord)
	_ = cast2.EntToResp(&resp.Attachment, attachmentRecord)

	return resp, nil
}
