package bill_item

import (
	"bcare/api/internal/auth"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/bconst"
	"bcare/common/bquery"
	cast2 "bcare/common/cast"
	"bcare/ent"
	"bcare/ent/attachment"
	"bcare/ent/billitem"
	"bcare/ent/deal"
	"context"
	"entgo.io/ent/dialect/sql"
	"github.com/samber/lo"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetPartiallyPaidLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetPartiallyPaidLogic(ctx context.Context, svcCtx *svc.ServiceContext) GetPartiallyPaidLogic {
	return GetPartiallyPaidLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetPartiallyPaidLogic) GetPartiallyPaid(req *types.BillItemGetPartiallyPaidRequest) (resp *types.BillItemListResponse, err error) {
	// Check permission
	if !l.svcCtx.Auth.BillItem.CanList(l.ctx, nil) {
		logx.WithContext(l.ctx).Infof("Permission denied")
		return nil, auth.ErrBillItemListDenied
	}

	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		// Build query with necessary relations
		query := tx.BillItem.Query().
			WithAttachment(func(q *ent.AttachmentQuery) {
				q.Where(
					// Add condition for plan_id is null
					attachment.PlanIDIsNil(),
				).
					WithProduct().
					WithCreator().
					WithData().
					WithDeal()

			}).
			WithUser().
			WithAllocations().Order(billitem.ByCreatedAt(sql.OrderDesc()))

		query = query.Where(billitem.HasAttachmentWith(attachment.PersonID(req.PersonId), attachment.StatusNEQ(bconst.StatusDeleted), attachment.DeletedAtIsNil()))
		// Get bill items that have allocations sum < amount
		billItems, e := query.All(l.ctx)

		if e != nil {
			return types.ErrBillItemGetList.Wrap(e)
		}

		// First, filter the valid bill items and calculate paid amounts
		billItemPaidAmounts := make(map[int]float64) // Store paid amounts by bill item ID
		validBillItems := lo.Filter(billItems, func(bi *ent.BillItem, _ int) bool {
			// Check deal state
			if bi.Edges.Attachment == nil {
				return false
			}

			paidAmount := 0.0
			hasNegativeAllocation := false
			hasAllocations := len(bi.Edges.Allocations) > 0

			// Check all allocations
			for _, allocation := range bi.Edges.Allocations {
				if allocation.Amount < 0 {
					hasNegativeAllocation = true
					break // Exit early if found negative allocation
				}
				paidAmount += allocation.Amount
			}

			// Store the paid amount for later use
			billItemPaidAmounts[bi.ID] = paidAmount

			// Case 1: Amount = 0 and no allocations
			if bi.Amount == 0 {
				return !hasAllocations &&
					bi.Edges.Attachment != nil &&
					bi.Edges.Attachment.PlanID == nil
			}

			if bi.Edges.Attachment.Edges.Deal != nil {
				dealRecord := bi.Edges.Attachment.Edges.Deal
				if dealRecord.State == deal.StateLost || dealRecord.State == deal.StateCancelled {
					return false
				}
			}
			// Case 2: Normal case - amount > 0
			return !hasNegativeAllocation &&
				paidAmount < bi.Amount &&
				bi.Edges.Attachment != nil &&
				bi.Edges.Attachment.PlanID == nil
		})

		// Then map the filtered items to response
		resp = new(types.BillItemListResponse)
		resp.BillItems = lo.Map(validBillItems, func(bi *ent.BillItem, _ int) types.BillItemResponse {
			var billItemResp types.BillItemResponse
			_ = cast2.ConvertViaJson(&billItemResp, bi)

			// Add paid amount to response
			billItemResp.PaidAmount = billItemPaidAmounts[bi.ID]
			resp.RemainingAmount += billItemResp.Amount - billItemResp.PaidAmount
			// You can also calculate remaining amount if needed

			return billItemResp
		})

		return nil
	})

	return resp, err
}
