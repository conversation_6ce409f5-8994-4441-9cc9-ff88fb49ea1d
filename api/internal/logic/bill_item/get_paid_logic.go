package bill_item

import (
	"bcare/api/internal/auth"
	"bcare/common/bconst"
	"bcare/common/bquery"
	cast2 "bcare/common/cast"
	"bcare/ent"
	"bcare/ent/attachment"
	"bcare/ent/billitem"
	"context"
	"entgo.io/ent/dialect/sql"
	"github.com/samber/lo"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetPaidLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetPaidLogic(ctx context.Context, svcCtx *svc.ServiceContext) GetPaidLogic {
	return GetPaidLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetPaidLogic) GetPaid(req *types.BillItemGetPaidRequest) (resp *types.BillItemListResponse, err error) {
	// Check permission
	if !l.svcCtx.Auth.BillItem.CanList(l.ctx, nil) {
		logx.WithContext(l.ctx).Infof("Permission denied")
		return nil, auth.ErrBillItemListDenied
	}

	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		// Build query with necessary relations
		query := tx.BillItem.Query().
			WithAttachment(func(q *ent.AttachmentQuery) {
				q.Where(
					// Add condition for plan_id is null
					attachment.PlanIDIsNil(),
				).
					WithProduct().
					WithCreator().WithData()
			}).
			WithUser().
			WithAllocations().Order(billitem.ByCreatedAt(sql.OrderDesc()))

		query = query.Where(billitem.HasAttachmentWith(attachment.PersonID(req.PersonId), attachment.StatusNEQ(bconst.StatusDeleted), attachment.DeletedAtIsNil()))
		// Get bill items that have allocations sum < amount
		billItems, e := query.All(l.ctx)

		if e != nil {
			return types.ErrBillItemGetList.Wrap(e)
		}

		// First, filter the valid bill items and calculate paid amounts
		billItemPaidAmounts := make(map[int]float64) // Store paid amounts by bill item ID
		validBillItems := lo.Filter(billItems, func(bi *ent.BillItem, _ int) bool {
			paidAmount := 0.0
			hasNegativeAllocation := false

			// Check all allocations
			for _, allocation := range bi.Edges.Allocations {
				// Check for negative allocation
				if allocation.Amount < 0 {
					hasNegativeAllocation = true
					break // Exit early if found negative allocation
				}
				paidAmount += allocation.Amount
			}

			// Store the paid amount for later use (if needed)
			billItemPaidAmounts[bi.ID] = paidAmount

			return !hasNegativeAllocation && // Must not have negative allocation
				paidAmount > 0 // Must have at least one allocation with total paid amount > 0
		})

		// Then map the filtered items to response
		resp = new(types.BillItemListResponse)
		resp.BillItems = lo.Map(validBillItems, func(bi *ent.BillItem, _ int) types.BillItemResponse {
			var billItemResp types.BillItemResponse
			_ = cast2.EntToResp(&billItemResp, bi)

			// Add paid amount to response
			billItemResp.PaidAmount = billItemPaidAmounts[bi.ID]
			// You can also calculate remaining amount if needed

			return billItemResp
		})

		return nil
	})

	return resp, err
}
