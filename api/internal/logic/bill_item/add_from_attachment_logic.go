package bill_item

import (
	"bcare/api/internal/auth"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/api/model"
	"bcare/common/bquery"
	cast2 "bcare/common/cast"
	"bcare/common/ctxdata"
	"bcare/ent"
	"bcare/ent/attachment"
	"bcare/ent/attachmentdata"
	"context"
	"encoding/json"
	"strings"

	"github.com/zeromicro/go-zero/core/logx"
)

type AddFromAttachmentLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAddFromAttachmentLogic(ctx context.Context, svcCtx *svc.ServiceContext) AddFromAttachmentLogic {
	return AddFromAttachmentLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AddFromAttachmentLogic) AddFromAttachment(req *types.BillItemAddFromAttachment) (resp *types.BillItemResponse, err error) {
	// Check permissions
	if !l.svcCtx.Auth.BillItem.CanAdd(l.ctx, nil) {
		logx.WithContext(l.ctx).Infof("Permission denied")
		return nil, auth.ErrBillItemAddDenied
	}

	var billItemRecord *ent.BillItem
	var attachmentRecord *ent.Attachment
	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {

		// Get attachment to fetch amount
		attachmentRecord, err = tx.Attachment.Query().
			WithProduct().
			WithData(func(query *ent.AttachmentDataQuery) {
				query.Where(attachmentdata.KindEQ("meta"))
			}).
			Where(attachment.ID(req.AttachmentId)).First(l.ctx)
		if err != nil {
			if ent.IsNotFound(err) {
				return types.ErrAttachmentNotFound.Wrap(err, "Attachment not found")
			}
			return types.ErrAttachmentNotFound.Wrap(err, "Error getting attachment")
		}

		currentUserId := ctxdata.GetUidFromCtx(l.ctx)

		amount := (attachmentRecord.Price)*float64(attachmentRecord.Quantity) - attachmentRecord.Discount
		trackRecord, _ := model.GetActiveTrack(l.ctx, tx, attachmentRecord.PersonID)

		// Generate note from teeth data
		note := ""
		if len(attachmentRecord.Edges.Data) > 0 {
			for _, data := range attachmentRecord.Edges.Data {
				if teethStr, ok := data.Data["teeth"].(string); ok {
					var teethMap map[string]interface{}
					if err := json.Unmarshal([]byte(teethStr), &teethMap); err == nil {
						note = convertTeethToNote(teethMap)
						break
					}
				}
			}
		}
		if req.Note != "" {
			if note != "" {
				note += ", "
			}
			note += req.Note
		}

		// Create bill item
		createQuery := tx.BillItem.Create().
			SetAttachmentID(req.AttachmentId).
			SetAmount(amount).
			SetNote(note).
			SetUserID(currentUserId)

		if trackRecord != nil {
			createQuery = createQuery.SetTrackID(trackRecord.ID)
		}

		billItemRecord, err = createQuery.Save(l.ctx)
		if err != nil {
			return types.ErrBillItemAdd.Wrap(err, "Error saving bill item record")
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	// Prepare response
	resp = new(types.BillItemResponse)
	_ = cast2.ConvertViaJson(&resp, billItemRecord)
	_ = cast2.ConvertViaJson(&resp.Attachment, attachmentRecord)

	return resp, nil
}

// convertTeethToNote converts teeth data to a formatted note string
// convertTeethToNote converts teeth data to a formatted note string
func convertTeethToNote(teethMap map[string]interface{}) string {
	var upperTeeth []string
	hasUpper := false
	hasLower := false

	for tooth, value := range teethMap {
		// Check if value is "true" (as string or boolean)
		isTrue := false
		switch v := value.(type) {
		case string:
			isTrue = v == "true"
		case bool:
			isTrue = v
		}

		if !isTrue {
			continue
		}

		switch tooth {
		case "upper":
			hasUpper = true
		case "lower":
			hasLower = true
		default:
			upperTeeth = append(upperTeeth, "R"+tooth)
		}
	}

	var parts []string

	if hasUpper {
		parts = append(parts, "hàm trên")
	}

	if len(upperTeeth) > 0 {
		parts = append(parts, strings.Join(upperTeeth, ", "))
	}

	if hasLower {
		parts = append(parts, "hàm dưới")
	}

	return strings.Join(parts, ", ")
}
