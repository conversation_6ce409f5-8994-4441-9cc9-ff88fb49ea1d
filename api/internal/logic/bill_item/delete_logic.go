package bill_item

import (
	"bcare/api/internal/auth"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/bconst"
	"bcare/common/bquery"
	"bcare/ent"
	"bcare/ent/billitem"
	"context"
	"fmt"
	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewDeleteLogic(ctx context.Context, svcCtx *svc.ServiceContext) DeleteLogic {
	return DeleteLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DeleteLogic) Delete(req *types.BillItemDeleteRequest) (resp *types.BillItemResponse, err error) {
	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		billItemRecord, err := tx.BillItem.Query().WithAllocations().Where(billitem.ID(req.Id)).First(l.ctx)

		if err != nil && ent.IsNotFound(err) {
			return types.ErrBillItemNotFound.Wrap(err)
		}

		if len(billItemRecord.Edges.Allocations) > 0 {
			return types.ErrBillItemDelete.Wrap(fmt.Errorf("cannot delete bill item: contains existing allocations"))
		}
		//kiểm tra xem có quyền ko
		if !l.svcCtx.Auth.BillItem.CanDelete(l.ctx, billItemRecord) {
			logx.WithContext(l.ctx).Infof("Bạn ko có quyền")
			return auth.ErrBillItemDeleteDenied
		}

		billItemRecord.Status = bconst.StatusDeleted

		err = tx.BillItem.DeleteOne(billItemRecord).Exec(l.ctx)
		if err != nil {
			return types.ErrBillDelete.Wrap(err)
		}

		err = tx.Attachment.DeleteOneID(*billItemRecord.AttachmentID).Exec(l.ctx)
		if err != nil {
			return types.ErrBillDelete.Wrap(err)
		}
		return nil
	})

	resp = new(types.BillItemResponse)
	return
}
