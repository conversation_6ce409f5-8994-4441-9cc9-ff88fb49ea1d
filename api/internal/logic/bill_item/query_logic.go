package bill_item

import (
	"bcare/common/bquery"
	cast2 "bcare/common/cast"
	"bcare/ent"
	"bcare/ent/schema"
	"context"
	"encoding/json"
	"github.com/jinzhu/copier"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type QueryLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewQueryLogic(ctx context.Context, svcCtx *svc.ServiceContext) QueryLogic {
	return QueryLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *QueryLogic) Query(req *types.BillItemDynamicQuery) (resp string, err error) {
	// todo: add your logic here and delete this line

	dQuery := &bquery.DynamicQuery{}
	err = copier.Copy(dQuery, req)
	if err != nil {
		logx.WithContext(l.ctx).<PERSON>rrorf("unable to copy request: %v", err)
		return "", err
	}

	query := l.svcCtx.Ent.BillItem.Query().Modify(bquery.ApplyDynamicQuery(dQuery)).WithAttachment(func(q *ent.AttachmentQuery) { q.WithProduct().WithCreator() }).
		WithInstallment()
	result := map[string]any{}
	aliases := make(map[string]bool)
	for _, agg := range req.Aggregations {
		if agg.Alias != "" {
			aliases[agg.Alias] = true
		}
	}

	skipSoftDelete := schema.SkipSoftDelete(l.ctx)

	if req.Limit <= 1 {
		r, err := query.First(skipSoftDelete)
		if err != nil && !ent.IsNotFound(err) {
			logx.WithContext(l.ctx).Errorf("unable to fetch Appointment record: %v", err)
			return "", err
		}

		if r != nil {
			for alias := range aliases {
				result[alias], _ = r.Value(alias)
			}
		}
	} else {
		rows, err := query.All(skipSoftDelete)
		if err != nil {
			logx.WithContext(l.ctx).Errorf("unable to fetch Appointment records: %v", err)
			return "", err
		}

		resultList := make([]map[string]any, len(rows))
		for i, r := range rows {
			rowResult := make(map[string]any)
			rowResult["record"] = cast2.Atom(&rowResult, r)
			for alias := range aliases {
				rowResult[alias], _ = r.Value(alias)
			}
			resultList[i] = rowResult
		}
		result["rows"] = resultList
	}

	jsonByte, _ := json.Marshal(result)
	return string(jsonByte), nil
}
