package activity

import (
	"bcare/common/bquery"
	"bcare/ent"
	"bcare/ent/call"
	"bcare/ent/messagehistory"
	"bcare/ent/note"
	"bcare/ent/task"
	"context"
	"sort"
	"time"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type ListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewListLogic(ctx context.Context, svcCtx *svc.ServiceContext) ListLogic {
	return ListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// Hàm sắp xếp các bản ghi theo trường created_at
func sortRecordsByCreatedAt(records []types.ActivityRecord) []types.ActivityRecord {
	sort.Slice(records, func(i, j int) bool {
		var timeI, timeJ time.Time
		switch v := records[i].Data.(type) {
		case *ent.Call:
			timeI = v.CreatedAt
		case *ent.MessageHistory:
			timeI = v.CreatedAt
		case *ent.Note:
			timeI = v.CreatedAt
		case *ent.Task:
			timeI = v.CreatedAt
		}
		switch v := records[j].Data.(type) {
		case *ent.Call:
			timeJ = v.CreatedAt
		case *ent.MessageHistory:
			timeJ = v.CreatedAt
		case *ent.Note:
			timeJ = v.CreatedAt
		case *ent.Task:
			timeJ = v.CreatedAt
		}
		return timeI.After(timeJ)
	})
	return records
}

func (l *ListLogic) List(req *types.ListRequest) (resp *types.ListResponse, err error) {
	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		var combinedRecords []types.ActivityRecord

		// Truy vấn Call
		calls, err := tx.Call.Query().
			Where(call.PersonID(req.PersonId)).
			Order(ent.Desc(call.FieldCreatedAt)).
			All(l.ctx)
		if err != nil {
			return types.ErrCallGetList.Wrap(err)
		}

		for _, c := range calls {
			combinedRecords = append(combinedRecords, types.ActivityRecord{Type: "Call", Data: c})
		}
		// Truy vấn SendMessageRequest
		messages, err := tx.MessageHistory.Query().
			Where(messagehistory.PersonID(req.PersonId)).
			Order(ent.Desc(messagehistory.FieldCreatedAt)).
			All(l.ctx)
		if err != nil {
			return err
		}
		for _, message := range messages {
			combinedRecords = append(combinedRecords, types.ActivityRecord{Type: "MessageHistory", Data: message})
		}

		// Truy vấn Note
		notes, err := tx.Note.Query().
			Where(note.PersonID(req.PersonId)).
			Order(ent.Desc(note.FieldCreatedAt)).
			All(l.ctx)
		if err != nil {
			return types.ErrNoteGetList.Wrap(err)
		}

		for _, n := range notes {
			combinedRecords = append(combinedRecords, types.ActivityRecord{Type: "Note", Data: n})
		}

		// Truy vấn Task
		tasks, err := tx.Task.Query().
			Where(task.PersonID(req.PersonId)).
			Order(ent.Desc(task.FieldCreatedAt)).
			All(l.ctx)
		if err != nil {
			return types.ErrTaskGetList.Wrap(err)
		}
		for _, t := range tasks {
			combinedRecords = append(combinedRecords, types.ActivityRecord{Type: "Task", Data: t})
		}
		// Sắp xếp tất cả bản ghi theo created_at
		sortedRecords := sortRecordsByCreatedAt(combinedRecords)

		// Tạo response
		resp = &types.ListResponse{
			Activities: sortedRecords,
		}
		return nil
	})

	return resp, err
}
