package person_data

import (
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/ent"
	"bcare/ent/persondata"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetDataLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetDataLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetDataLogic {
	return &GetDataLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetDataLogic) GetData(req *types.GetDataRequest) (resp *types.DataResponse, err error) {
	query := l.svcCtx.Ent.PersonData.Query().Where(persondata.PersonID(req.PersonID))

	// Nếu kind được cung cấp, thêm điều kiện kind vào truy vấn
	if req.Kind != "" {
		query = query.Where(persondata.Kind(req.Kind))
	}

	personDataList, err := query.All(l.ctx)
	if err != nil {
		if ent.IsNotFound(err) {
			return &types.DataResponse{Data: make(map[string]interface{})}, nil
		}
		return nil, types.ErrPersonData.Wrap(err, "failed to query person data")
	}

	result := make(map[string]interface{})

	// Trường hợp 1: Chỉ có person_id
	if req.Kind == "" && req.Key == "" {
		for _, pd := range personDataList {
			if pd.Data != nil {
				result[pd.Kind] = pd.Data
			}
		}
		return &types.DataResponse{Data: result}, nil
	}

	// Trường hợp 2: Có person_id và kind
	if req.Kind != "" && req.Key == "" {
		for _, pd := range personDataList {
			if pd.Data != nil {
				return &types.DataResponse{Data: pd.Data}, nil
			}
		}
		return &types.DataResponse{Data: make(map[string]interface{})}, nil
	}

	// Trường hợp 3: Có person_id, kind và key
	if req.Kind != "" && req.Key != "" {
		for _, pd := range personDataList {
			if pd.Data != nil {
				if value, exists := pd.Data[req.Key]; exists {
					if mapValue, ok := value.(map[string]interface{}); ok {
						return &types.DataResponse{Data: mapValue}, nil
					} else {
						// Nếu giá trị không phải là map, bọc nó trong một map
						return &types.DataResponse{Data: map[string]interface{}{"raw": value}}, nil
					}
				}
			}
		}
	}

	return &types.DataResponse{Data: make(map[string]interface{})}, nil
}
