package person_data

import (
	"bcare/common/bquery"
	keylock "bcare/common/lock"
	"bcare/ent"
	"bcare/ent/persondata"
	"context"
	"time"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type SetDataLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewSetDataLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SetDataLogic {
	return &SetDataLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *SetDataLogic) SetData(req *types.SetDataRequest) (resp int, err error) {
	var personID int

	// Tạo khóa dựa trên person_id và kind
	unlock, err := keylock.Global.Lock(5*time.Second, req.PersonID, req.Kind)
	if err != nil {
		return 0, types.ErrPersonData.Wrap(err, "failed to acquire lock")
	}
	defer unlock()

	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		// Tìm hoặc tạo mới PersonData
		personData, err := tx.PersonData.Query().
			Where(
				persondata.PersonID(req.PersonID),
				persondata.Kind(req.Kind),
			).
			Only(l.ctx)

		if err != nil {
			if ent.IsNotFound(err) {
				// Nếu không tìm thấy, tạo mới
				createQuery := tx.PersonData.Create().
					SetPersonID(req.PersonID).
					SetKind(req.Kind)

				if len(req.Data) > 0 {
					createQuery = createQuery.SetData(req.Data)
				} else {
					createQuery = createQuery.SetData(map[string]interface{}{req.Key: req.Value})
				}

				personData, err = createQuery.Save(l.ctx)
				if err != nil {
					return types.ErrPersonData.Wrap(err, "failed to create person data")
				}
			} else {
				return types.ErrPersonData.Wrap(err, "failed to query person data")
			}
		} else {
			// Cập nhật data
			updateQuery := personData.Update()

			if len(req.Data) > 0 {
				updateQuery = updateQuery.SetData(req.Data)
			} else {
				currentData := personData.Data
				if currentData == nil {
					currentData = make(map[string]interface{})
				}
				currentData[req.Key] = req.Value
				updateQuery = updateQuery.SetData(currentData)
			}

			// Lưu cập nhật
			_, err = updateQuery.Save(l.ctx)
			if err != nil {
				return types.ErrPersonData.Wrap(err, "failed to update person data")
			}
		}

		personID = req.PersonID
		return nil
	})

	if err != nil {
		return 0, err
	}

	return personID, nil
}
