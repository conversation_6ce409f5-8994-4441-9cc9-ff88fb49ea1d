package person_data

import (
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/bquery"
	"bcare/ent"
	"bcare/ent/persondata"
	"context"
	"github.com/zeromicro/go-zero/core/logx"
)

type ClearDataLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewClearDataLogic(ctx context.Context, svcCtx *svc.ServiceContext) ClearDataLogic {
	return ClearDataLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ClearDataLogic) ClearData(req *types.ClearDataRequest) (resp *types.CommonResponse, err error) {
	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		query := tx.PersonData.Query().Where(persondata.PersonID(req.PersonID))

		if req.Kind != "" {
			query = query.Where(persondata.Kind(req.Kind))
		}

		personDataList, err := query.All(l.ctx)
		if err != nil {
			return types.ErrPersonData.Wrap(err, "failed to query person data")
		}

		for _, personData := range personDataList {
			if req.Key != "" {
				// Clear specific key
				currentData := personData.Data
				if currentData != nil {
					delete(currentData, req.Key)
					_, err = personData.Update().SetData(currentData).Save(l.ctx)
					if err != nil {
						return types.ErrPersonData.Wrap(err, "failed to update person data")
					}
				}
			} else {
				// Clear all data for the given kind
				_, err = personData.Update().ClearData().Save(l.ctx)
				if err != nil {
					return types.ErrPersonData.Wrap(err, "failed to clear person data")
				}
			}
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return &types.CommonResponse{
		Count: req.PersonID,
	}, nil
}
