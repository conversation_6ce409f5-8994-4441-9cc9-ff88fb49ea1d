package user_data

import (
	"bcare/api/internal/dto"
	"bcare/common/berr"
	"bcare/common/bquery"
	"bcare/ent"
	"context"
	"errors"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type SetArrayDataLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewSetArrayDataLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SetArrayDataLogic {
	return &SetArrayDataLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

const maxArraySize = 100

func (l *SetArrayDataLogic) SetArrayData(req *types.SetUserDataRequest) (resp *types.CommonResponse, err error) {
	if req.Key == "" {
		return nil, berr.ErrRequestParam.Wrap(errors.New("Key is required for SetArrayData"))
	}
	if req.Data == nil {
		return nil, berr.ErrRequestParam.Wrap(errors.New("Data field (element to append) is required for SetArrayData"))
	}

	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		getInput := &dto.GetUserDataInput{
			UserID: req.UserID,
			Kind:   req.Kind,
		}
		currentUserDataOutput, modelErr := l.svcCtx.UserDataModel.GetUserData(l.ctx, getInput)

		var currentDataMap map[string]interface{}

		if modelErr != nil {
			if errors.Is(modelErr, berr.ErrNotFound) {
				currentDataMap = make(map[string]interface{})
			} else {
				return modelErr
			}
		} else {
			currentDataMap = currentUserDataOutput.Data
			if currentDataMap == nil {
				currentDataMap = make(map[string]interface{})
			}
		}

		var history []map[string]interface{}
		if existingArrayData, ok := currentDataMap[req.Key]; ok {
			if existingArray, okAssert := existingArrayData.([]interface{}); okAssert {
				for _, item := range existingArray {
					if mapItem, okMap := item.(map[string]interface{}); okMap {
						history = append(history, mapItem)
					} else {
						l.Logger.Infof("SetArrayDataLogic: Item in existing array for key '%s' is not a map[string]interface{}. Item: %v", req.Key, item)
					}
				}
			} else if existingArrayData != nil {
				return berr.ErrServerCommon.Wrap(errors.New("existing data for key '" + req.Key + "' is not an array as expected"))
			}
		}
		if history == nil {
			history = []map[string]interface{}{}
		}

		history = append([]map[string]interface{}{req.Data}, history...)

		if len(history) > maxArraySize {
			history = history[:maxArraySize]
		}

		currentDataMap[req.Key] = history

		setDataInput := &dto.SetUserDataInput{
			UserID: req.UserID,
			Kind:   req.Kind,
			Data:   currentDataMap,
		}

		_, modelSetErr := l.svcCtx.UserDataModel.SetUserData(l.ctx, setDataInput)
		if modelSetErr != nil {
			return modelSetErr
		}
		return nil
	})

	if err != nil {
		return nil, err
	}

	return &types.CommonResponse{Count: 1}, nil
}
