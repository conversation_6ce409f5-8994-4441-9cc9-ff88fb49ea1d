package user_data

import (
	"bcare/api/internal/dto"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/berr"
	"bcare/common/cast"
	"context"
	"errors"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetDataLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetDataLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetDataLogic {
	return &GetDataLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetDataLogic) GetData(req *types.GetUserDataRequest) (resp *types.UserDataResponse, err error) {
	if req.Kind == "" {
		listInput := &dto.ListUserDataInput{
			Filter: &dto.UserDataFilter{
				UserID: &req.UserID,
			},
		}
		listOutput, modelErr := l.svcCtx.UserDataModel.ListUserData(l.ctx, listInput)
		if modelErr != nil {
			return nil, modelErr
		}

		aggregatedData := make(map[string]interface{})
		if listOutput != nil {
			for _, ud := range listOutput.UserData {
				if ud.Data != nil {
					aggregatedData[ud.Kind] = ud.Data
				}
			}
		}
		return &types.UserDataResponse{Data: aggregatedData}, nil

	} else {
		modelInput := &dto.GetUserDataInput{}
		modelInput.UserID = req.UserID
		modelInput.Kind = req.Kind
		if req.Key != "" {
			modelInput.Key = &req.Key
		}

		modelOutput, modelErr := l.svcCtx.UserDataModel.GetUserData(l.ctx, modelInput)
		if modelErr != nil {
			if errors.Is(modelErr, berr.ErrNotFound) {
				return &types.UserDataResponse{Data: make(map[string]interface{})}, nil
			}
			return nil, modelErr
		}

		if modelOutput == nil {
			return &types.UserDataResponse{Data: make(map[string]interface{})}, nil
		}

		apiResponse := &types.UserDataResponse{}
		err = cast.ConvertViaJson(apiResponse, modelOutput)
		if err != nil {
			l.Logger.Errorf("GetDataLogic: Failed to cast UserDataDetailOutput to UserDataResponse: %v", err)
			return nil, berr.ErrServerCommon.Wrap(err)
		}
		apiResponse.Kind = modelOutput.Kind

		return apiResponse, nil
	}
}
