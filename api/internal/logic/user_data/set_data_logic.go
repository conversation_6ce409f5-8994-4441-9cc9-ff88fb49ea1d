package user_data

import (
	"bcare/api/internal/dto"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/berr"
	"bcare/common/cast"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type SetDataLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewSetDataLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SetDataLogic {
	return &SetDataLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *SetDataLogic) SetData(req *types.SetUserDataRequest) (resp *types.CommonResponse, err error) {
	modelInput := &dto.SetUserDataInput{}
	if castErr := cast.ReqToModelInput(modelInput, req); castErr != nil {
		l.<PERSON><PERSON>.<PERSON>rrorf("SetDataLogic: Failed to cast SetUserDataRequest to SetUserDataInput: %v", castErr)
		return nil, berr.ErrRequestParam.Wrap(castErr)
	}

	_, modelErr := l.svcCtx.UserDataModel.SetUserData(l.ctx, modelInput)
	if modelErr != nil {
		return nil, modelErr
	}

	return &types.CommonResponse{Count: 1}, nil
}
