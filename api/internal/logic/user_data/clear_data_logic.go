package user_data

import (
	"bcare/api/internal/dto"
	"context"
	"errors"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/berr"

	"github.com/zeromicro/go-zero/core/logx"
)

type ClearDataLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewClearDataLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ClearDataLogic {
	return &ClearDataLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ClearDataLogic) ClearData(req *types.ClearUserDataRequest) (resp *types.CommonResponse, err error) {
	modelInput := dto.ClearUserDataInput{
		UserID: req.UserID,
	}

	if req.Kind != "" {
		modelInput.Kind = &req.Kind
	}
	if req.Key != "" {
		modelInput.Key = &req.Key
	}

	modelErr := l.svcCtx.UserDataModel.ClearUserData(l.ctx, &modelInput)
	if modelErr != nil {
		if errors.Is(modelErr, berr.ErrNotFound) {
		}
		return nil, modelErr
	}

	return &types.CommonResponse{Count: 1}, nil
}
