package history

import (
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/bquery"
	cast2 "bcare/common/cast"
	"bcare/ent"
	"context"
	"entgo.io/ent/dialect/sql"
	"github.com/samber/lo"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetHistoryListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetHistoryListLogic(ctx context.Context, svcCtx *svc.ServiceContext) GetHistoryListLogic {
	return GetHistoryListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetHistoryListLogic) GetHistoryList(req *types.HistoryListRequest) (resp *types.HistoryListResponse, err error) {
	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		limit, offset := bquery.Paging(req.PageSize, req.Page)
		order := bquery.Ordering(req.OrderBy)

		query := tx.UnifiedHistoryView.Query().Order(order)

		filterMap := make(map[string]interface{})
		_ = cast2.AtomNoZero(&filterMap, req.Filter)

		for field, value := range filterMap {
			query = query.Where(sql.FieldEQ(field, value))
		}

		total, _ := query.Count(l.ctx)
		query = query.Limit(limit).Offset(offset)
		histories, e := query.All(l.ctx)

		if e != nil {
			return types.ErrHistoryGetList.Wrap(e)
		}
		resp = new(types.HistoryListResponse)
		resp.Histories = lo.Map(histories, func(h *ent.UnifiedHistoryView, _ int) types.HistoryRecord {
			p := types.HistoryRecord{}
			_ = cast2.EntViewToResp(&p, h)
			return p
		})
		resp.Total = total
		return nil
	})

	return resp, err
}
