package discount_usage

import (
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/berr"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewDeleteLogic(ctx context.Context, svcCtx *svc.ServiceContext) DeleteLogic {
	return DeleteLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DeleteLogic) Delete(req *types.DiscountUsageDeleteRequest) (resp *types.DiscountUsageResponse, err error) {
	_, err = l.svcCtx.DiscountUsageModel.GetDiscountUsage(l.ctx, req.Id)
	if err != nil {
		return nil, err
	}

	if !l.svcCtx.Auth.DiscountUsage.CanDelete(l.ctx, nil) {
		return nil, berr.ErrPermissionDenied.Wrap(nil)
	}

	err = l.svcCtx.DiscountUsageModel.DeleteDiscountUsage(l.ctx, req.Id)
	if err != nil {
		return nil, err
	}

	return nil, nil
}
