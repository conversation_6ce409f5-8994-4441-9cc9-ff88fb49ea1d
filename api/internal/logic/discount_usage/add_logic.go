package discount_usage

import (
	"bcare/api/internal/dto"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/berr"
	"bcare/common/cast"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type AddLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAddLogic(ctx context.Context, svcCtx *svc.ServiceContext) AddLogic {
	return AddLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AddLogic) Add(req *types.DiscountUsageAddRequest) (resp *types.DiscountUsageResponse, err error) {
	if !l.svcCtx.Auth.DiscountUsage.CanAdd(l.ctx, nil) {
		return nil, berr.ErrPermissionDenied.Wrap(nil)
	}

	input := new(dto.CreateDiscountUsageInput)
	err = cast.ReqToModelInput(input, req)
	if err != nil {
		return nil, err
	}

	discountUsageOutput, err := l.svcCtx.DiscountUsageModel.CreateDiscountUsage(l.ctx, input)
	if err != nil {
		return nil, err
	}

	resp = new(types.DiscountUsageResponse)
	err = cast.ConvertViaJson(resp, discountUsageOutput)
	if err != nil {
		return nil, err
	}

	return resp, nil
}
