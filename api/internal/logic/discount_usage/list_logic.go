package discount_usage

import (
	"bcare/api/internal/dto"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/berr"
	"bcare/common/cast"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type ListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewListLogic(ctx context.Context, svcCtx *svc.ServiceContext) ListLogic {
	return ListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ListLogic) List(req *types.DiscountUsageListRequest) (resp *types.DiscountUsageListResponse, err error) {
	if !l.svcCtx.Auth.DiscountUsage.CanList(l.ctx, nil) {
		return nil, berr.ErrPermissionDenied.Wrap(nil)
	}

	input := new(dto.ListDiscountUsageInput)
	err = cast.ReqToModelInput(input, req)
	if err != nil {
		return nil, err
	}

	listOutput, err := l.svcCtx.DiscountUsageModel.ListDiscountUsages(l.ctx, *input)
	if err != nil {
		return nil, err
	}

	resp = &types.DiscountUsageListResponse{
		DiscountUsages: make([]types.DiscountUsageResponse, 0, len(listOutput.DiscountUsages)),
		Total:          listOutput.Total,
	}

	err = cast.ConvertViaJson(&resp.DiscountUsages, listOutput.DiscountUsages)
	if err != nil {
		return nil, err
	}

	return resp, nil
}
