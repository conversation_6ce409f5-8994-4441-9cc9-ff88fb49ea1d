package mind

import (
	"context"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type ContentLongerLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewContentLongerLogic(ctx context.Context, svcCtx *svc.ServiceContext) ContentLongerLogic {
	return ContentLongerLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ContentLongerLogic) ContentLonger(req *types.ContentModificationRequest) (resp *types.ContentModificationResponse, err error) {
	// todo: add your logic here and delete this line

	return
}
