package mind

import (
	"context"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type ContentContinueLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewContentContinueLogic(ctx context.Context, svcCtx *svc.ServiceContext) ContentContinueLogic {
	return ContentContinueLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ContentContinueLogic) ContentContinue(req *types.ContentModificationRequest) (resp *types.ContentModificationResponse, err error) {
	// todo: add your logic here and delete this line

	return
}
