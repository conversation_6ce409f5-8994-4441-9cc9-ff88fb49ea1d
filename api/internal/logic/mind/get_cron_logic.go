package mind

import (
	"context"
	"errors"
	"github.com/openai/openai-go"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetCronLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetCronLogic(ctx context.Context, svcCtx *svc.ServiceContext) GetCronLogic {
	return GetCronLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetCronLogic) GetCron(req *types.GetCronRequest) (resp *types.GetCronResponse, err error) {
	model := "ft:gpt-4o-mini-2024-07-18:blazy::AGY407vB"
	systemInstruction := "Converts text into cron expressions. Respond JSON object containing error (0 for success, 1 for error) and data (the cron expression)."

	messages := []openai.ChatCompletionMessageParamUnion{
		openai.SystemMessage(systemInstruction),
		openai.UserMessage(req.Prompt),
	}

	// Tạo CompletionParams
	params := svc.CompletionParams{
		Messages: messages,
		Model:    model,
		MaxToken: 250,
	}

	// Gọi GenerateCompletion
	result, err := l.svcCtx.Openai.GenerateCompletion(l.ctx, params)
	if err != nil {
		return nil, types.ErrMind.Wrap(err, "Error generating cron expression")
	}

	// Xử lý kết quả
	resp = &types.GetCronResponse{}
	if result.Error == 0 {
		resp.CronExpression = result.Data
	} else {
		return nil, types.ErrMind.Wrap(errors.New("invalid prompt"))
	}

	return resp, nil
}
