package mind

import (
	"context"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type ContentCommandLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewContentCommandLogic(ctx context.Context, svcCtx *svc.ServiceContext) ContentCommandLogic {
	return ContentCommandLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ContentCommandLogic) ContentCommand(req *types.ContentCommandRequest) (resp *types.ContentCommandResponse, err error) {
	// todo: add your logic here and delete this line

	return
}
