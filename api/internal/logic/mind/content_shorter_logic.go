package mind

import (
	"context"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type ContentShorterLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewContentShorterLogic(ctx context.Context, svcCtx *svc.ServiceContext) ContentShorterLogic {
	return ContentShorterLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ContentShorterLogic) ContentShorter(req *types.ContentModificationRequest) (resp *types.ContentModificationResponse, err error) {
	// todo: add your logic here and delete this line

	return
}
