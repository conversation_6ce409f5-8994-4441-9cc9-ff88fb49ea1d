package task_department

import (
	"bcare/common/berr"
	"context"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewDeleteLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteLogic {
	return &DeleteLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DeleteLogic) Delete(req *types.TaskDepartmentDeleteRequest) (resp *types.TaskDepartment, err error) {
	existing, err := l.svcCtx.TaskDepartmentModel.Get(l.ctx, req.Id)
	if err != nil {
		return nil, err
	}

	if !l.svcCtx.Auth.TaskDepartment.CanDelete(l.ctx, existing) {
		return nil, berr.ErrPermissionDenied.Wrap(nil)
	}

	_, err = l.svcCtx.TaskDepartmentModel.Delete(l.ctx, req.Id)
	if err != nil {
		return nil, err
	}

	resp = new(types.TaskDepartment)
	return resp, nil
}
