package task_department

import (
	"bcare/api/internal/dto"
	"context"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/berr"
	"bcare/common/cast"

	"github.com/zeromicro/go-zero/core/logx"
)

type QueryLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewQueryLogic(ctx context.Context, svcCtx *svc.ServiceContext) *QueryLogic {
	return &QueryLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *QueryLogic) Query(req *types.TaskDepartmentDynamicQuery) (resp *types.DynamicQueryResponse, err error) {

	input := new(dto.ListTaskDepartmentInput)
	err = cast.ReqToModelInput(input, req)
	if err != nil {
		return nil, err
	}

	output, err := l.svcCtx.TaskDepartmentModel.List(l.ctx, input)
	if err != nil {
		return nil, err
	}

	resp = &types.DynamicQueryResponse{}
	if err := cast.ModelOutputToResp(resp, output); err != nil {
		return nil, berr.ErrServerCommon.Wrap(err)
	}

	return resp, nil
}
