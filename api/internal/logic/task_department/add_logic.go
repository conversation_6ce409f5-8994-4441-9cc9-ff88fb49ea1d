package task_department

import (
	"bcare/api/internal/dto"
	"context"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/berr"
	"bcare/common/cast"

	"github.com/zeromicro/go-zero/core/logx"
)

type AddLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAddLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AddLogic {
	return &AddLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AddLogic) Add(req *types.TaskDepartmentAddRequest) (resp *types.TaskDepartment, err error) {
	if !l.svcCtx.Auth.TaskDepartment.CanAdd(l.ctx) {
		return nil, berr.ErrPermissionDenied.Wrap(nil)
	}

	input := new(dto.CreateTaskDepartmentInput)
	err = cast.ReqToModelInput(input, req)
	if err != nil {
		return nil, err
	}

	output, err := l.svcCtx.TaskDepartmentModel.Create(l.ctx, input)
	if err != nil {
		return nil, err
	}

	resp = new(types.TaskDepartment)
	err = cast.ConvertViaJson(resp, output)
	if err != nil {
		return nil, err
	}

	return resp, nil
}
