package task_department

import (
	"bcare/api/internal/dto"
	"bcare/common/berr"
	"bcare/common/cast"
	"context"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateLogic {
	return &UpdateLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateLogic) Update(req *types.TaskDepartmentUpdateRequest) (resp *types.TaskDepartment, err error) {
	existing, err := l.svcCtx.TaskDepartmentModel.Get(l.ctx, req.Id)
	if err != nil {
		return nil, err
	}

	if !l.svcCtx.Auth.TaskDepartment.CanUpdate(l.ctx, existing) {
		return nil, berr.ErrPermissionDenied.Wrap(nil)
	}

	input := new(dto.UpdateTaskDepartmentInput)
	err = cast.ReqToModelInput(input, req)
	if err != nil {
		return nil, err
	}

	output, err := l.svcCtx.TaskDepartmentModel.Update(l.ctx, input)
	if err != nil {
		return nil, err
	}

	resp = new(types.TaskDepartment)
	err = cast.ConvertViaJson(resp, output)
	if err != nil {
		return nil, err
	}

	return resp, nil
}
