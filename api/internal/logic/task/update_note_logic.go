package task

import (
	"bcare/api/internal/dto"
	"context"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/berr"
	"bcare/common/cast"

	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateNoteLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateNoteLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateNoteLogic {
	return &UpdateNoteLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateNoteLogic) UpdateNote(req *types.TaskNoteUpdateRequest) (resp *types.TaskNote, err error) {
	existingNote, err := l.svcCtx.TaskNoteModel.Get(l.ctx, req.Id)
	if err != nil {
		return nil, err
	}

	if !l.svcCtx.Auth.TaskNote.CanUpdate(l.ctx, existingNote) {
		return nil, berr.ErrPermissionDenied.Wrap(nil)
	}

	input := new(dto.UpdateTaskNoteInput)
	err = cast.ReqToModelInput(input, req)
	if err != nil {
		return nil, err
	}

	noteOutput, err := l.svcCtx.TaskNoteModel.Update(l.ctx, input)
	if err != nil {
		return nil, err
	}

	resp = new(types.TaskNote)
	err = cast.ConvertViaJson(resp, noteOutput)
	if err != nil {
		return nil, err
	}

	return resp, nil
}
