package task

import (
	"bcare/api/internal/auth"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/bquery"
	cast2 "bcare/common/cast"
	"bcare/ent"
	"bcare/ent/schema"
	"context"
	"entgo.io/ent/dialect/sql"
	"fmt"
	"github.com/jinzhu/copier"
	"github.com/zeromicro/go-zero/core/logx"
)

type QueryLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewQueryLogic(ctx context.Context, svcCtx *svc.ServiceContext) QueryLogic {
	return QueryLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *QueryLogic) Query(req *types.TaskDynamicQuery) (res *types.DynamicQueryResponse, err error) {
	res = new(types.DynamicQueryResponse)

	//kiểm tra xem có quyền ko
	if !l.svcCtx.Auth.Task.CanQuery(l.ctx, nil) {
		logx.WithContext(l.ctx).Infof("Bạn ko có quyền")
		return nil, auth.ErrTaskQueryDenied
	}

	dQuery := &bquery.DynamicQuery{}
	err = copier.Copy(dQuery, req)
	if err != nil {
		logx.WithContext(l.ctx).Errorf("unable to copy request: %v", err)
		return res, err
	}

	query := l.svcCtx.Ent.TaskSerialView.Query().Modify(bquery.ApplyDynamicQuery(dQuery)).Where()

	if req.UserId > 0 {
		// Create the JSON string for the assignments condition
		assignmentsJSON := fmt.Sprintf(`[{"user_id": %d}]`, req.UserId)
		query = query.Where(func(s *sql.Selector) {
			s.Where(
				sql.Or(
					sql.EQ("creator_id", req.UserId),
					sql.ExprP(fmt.Sprintf("assignments::jsonb @> '%s'::jsonb", assignmentsJSON)),
				),
			)
		})
	}

	result := map[string]any{}
	aliases := make(map[string]bool)
	for _, agg := range req.Aggregations {
		if agg.Alias != "" {
			aliases[agg.Alias] = true
		}
	}

	skipSoftDelete := schema.SkipSoftDelete(l.ctx)

	if req.Limit <= 1 {
		r, err := query.First(skipSoftDelete)
		if err != nil && !ent.IsNotFound(err) {
			logx.WithContext(l.ctx).Errorf("unable to fetch Task record: %v", err)
			return res, err
		}

		if r != nil {
			for alias := range aliases {
				result[alias], _ = r.Value(alias)
			}
		}
	} else {
		rows, err := query.All(skipSoftDelete)
		if err != nil {
			logx.WithContext(l.ctx).Errorf("unable to fetch Task records: %v", err)
			return res, err
		}
		resultList := make([]map[string]any, len(rows))
		for i, r := range rows {
			rowResult := make(map[string]any)
			rowResult["record"] = cast2.Atom(&rowResult, r)
			for alias := range aliases {
				rowResult[alias], _ = r.Value(alias)
			}
			resultList[i] = rowResult
		}
		result["rows"] = resultList
	}
	res.Result = result

	return
}
