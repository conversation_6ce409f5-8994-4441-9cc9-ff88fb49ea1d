package task

import (
	"bcare/api/internal/auth"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type BulkDeleteLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewBulkDeleteLogic(ctx context.Context, svcCtx *svc.ServiceContext) *BulkDeleteLogic {
	return &BulkDeleteLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *BulkDeleteLogic) BulkDelete(req *types.BulkDeleteRequest) (*types.Task, error) {
	// Check authorization
	if !l.svcCtx.Auth.Task.CanBulkDelete(l.ctx) {
		return nil, auth.ErrTaskBulkDeleteDenied
	}

	// Convert request IDs to slice
	ids := make([]int, len(req.IdList))
	for i, id := range req.IdList {
		ids[i] = id
	}

	// Delete task
	err := l.svcCtx.TaskModel.BulkDelete(l.ctx, ids)
	if err != nil {
		return nil, err
	}

	// Return empty task since it's deleted
	return &types.Task{}, nil
}
