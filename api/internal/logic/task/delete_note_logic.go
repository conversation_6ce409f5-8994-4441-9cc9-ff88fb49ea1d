package task

import (
	"bcare/common/berr"
	"context"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteNoteLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewDeleteNoteLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteNoteLogic {
	return &DeleteNoteLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DeleteNoteLogic) DeleteNote(req *types.TaskNoteDeleteRequest) (resp *types.TaskNote, err error) {
	existingNote, err := l.svcCtx.TaskNoteModel.Get(l.ctx, req.Id)
	if err != nil {
		return nil, err
	}

	if !l.svcCtx.Auth.TaskNote.CanDelete(l.ctx, existingNote) {
		return nil, berr.ErrPermissionDenied.Wrap(nil)
	}

	err = l.svcCtx.TaskNoteModel.Delete(l.ctx, req.Id)
	if err != nil {
		return nil, err
	}

	resp = new(types.TaskNote)
	return resp, nil
}
