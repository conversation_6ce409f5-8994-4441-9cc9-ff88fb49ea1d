package task

import (
	"bcare/api/internal/dto"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/berr"
	"bcare/common/cast"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type AddLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAddLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AddLogic {
	return &AddLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AddLogic) Add(req *types.TaskAddRequest) (*types.Task, error) {

	if !l.svcCtx.Auth.Task.CanAdd(l.ctx) {
		return nil, berr.ErrPermissionDenied.Wrap(nil)
	}

	input := new(dto.CreateTaskInput)
	if errMap := cast.ReqToModelInput(input, req); errMap != nil {
		return nil, berr.ErrRequestParam.Wrap(errMap)
	}
	task, err := l.svcCtx.TaskModel.Create(l.ctx, input)
	if err != nil {
		return nil, err
	}

	resp := new(types.Task)
	if err := cast.ModelOutputToResp(resp, task); err != nil {
		return nil, berr.ErrServerCommon.Wrap(err)
	}

	return resp, nil
}
