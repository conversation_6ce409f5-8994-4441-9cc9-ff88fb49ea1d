package task

import (
	"bcare/api/internal/dto"
	"context"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/berr"
	"bcare/common/cast"

	"github.com/zeromicro/go-zero/core/logx"
)

type ListNoteLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewListNoteLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ListNoteLogic {
	return &ListNoteLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ListNoteLogic) ListNote(req *types.TaskNoteListRequest) (resp *types.TaskNoteListResponse, err error) {
	if !l.svcCtx.Auth.TaskNote.CanList(l.ctx) {
		return nil, berr.ErrPermissionDenied.Wrap(nil)
	}

	input := new(dto.ListTaskNoteInput)
	err = cast.ReqToModelInput(input, req)
	if err != nil {
		return nil, err
	}

	listOutput, err := l.svcCtx.TaskNoteModel.List(l.ctx, *input)
	if err != nil {
		return nil, err
	}

	resp = &types.TaskNoteListResponse{
		Notes: make([]types.TaskNoteResponse, 0, len(listOutput.TaskNotes)),
		Total: listOutput.Total,
	}

	err = cast.ConvertViaJson(&resp.Notes, listOutput.TaskNotes)
	if err != nil {
		return nil, err
	}

	return resp, nil
}
