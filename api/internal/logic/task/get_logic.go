package task

import (
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/berr"
	"bcare/common/cast"
	"context"
	"github.com/zeromicro/go-zero/core/logx"
)

type GetLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetLogic {
	return &GetLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetLogic) Get(req *types.TaskGetRequest) (*types.TaskResponse, error) {
	task, err := l.svcCtx.TaskModel.Get(l.ctx, req.Id)
	if err != nil {
		return nil, err
	}

	if !l.svcCtx.Auth.Task.CanGet(l.ctx, task) {
		return nil, berr.ErrPermissionDenied.Wrap(nil)
	}
	resp := new(types.TaskResponse)
	if err := cast.ConvertViaJson(resp, task); err != nil {
		return nil, berr.ErrServerCommon.Wrap(err)
	}

	return resp, nil
}
