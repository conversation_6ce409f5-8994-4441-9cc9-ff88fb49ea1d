package task

import (
	"bcare/api/internal/dto"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/berr"
	"bcare/common/cast"
	"context"
	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateLogic {
	return &UpdateLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateLogic) Update(req *types.TaskUpdateRequest) (*types.Task, error) {
	task, err := l.svcCtx.TaskModel.Get(l.ctx, req.Id)
	if err != nil {
		return nil, err
	}

	if !l.svcCtx.Auth.Task.CanUpdate(l.ctx, task) {
		return nil, berr.ErrPermissionDenied.Wrap(nil)
	}

	input := new(dto.UpdateTaskInput)
	if err := cast.ReqToModelInput(input, req); err != nil {
		return nil, berr.ErrRequestParam.Wrap(err)
	}
	task, err = l.svcCtx.TaskModel.Update(l.ctx, input)
	if err != nil {
		return nil, err
	}

	resp := new(types.Task)
	if err := cast.ModelOutputToResp(resp, task); err != nil {
		return nil, berr.ErrServerCommon.Wrap(err)
	}

	return resp, nil
}
