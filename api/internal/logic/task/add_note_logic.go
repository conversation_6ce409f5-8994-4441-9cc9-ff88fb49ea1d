package task

import (
	"bcare/api/internal/dto"
	"context"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/berr"
	"bcare/common/cast"

	"github.com/zeromicro/go-zero/core/logx"
)

type AddNoteLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAddNoteLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AddNoteLogic {
	return &AddNoteLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AddNoteLogic) AddNote(req *types.TaskNoteAddRequest) (resp *types.TaskNote, err error) {
	if !l.svcCtx.Auth.TaskNote.CanAdd(l.ctx) {
		return nil, berr.ErrPermissionDenied.Wrap(nil)
	}

	input := new(dto.CreateTaskNoteInput)
	err = cast.ReqToModelInput(input, req)
	if err != nil {
		return nil, err
	}

	noteOutput, err := l.svcCtx.TaskNoteModel.Create(l.ctx, input)
	if err != nil {
		return nil, err
	}

	resp = new(types.TaskNote)
	err = cast.ConvertViaJson(resp, noteOutput)
	if err != nil {
		return nil, err
	}

	return resp, nil
}
