package task

import (
	"bcare/api/internal/auth"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/bquery"
	cast2 "bcare/common/cast"
	"bcare/ent"
	"bcare/ent/task"
	"bcare/ent/taskassignment"
	"context"
	"entgo.io/ent/dialect/sql"
	"github.com/samber/lo"
	"github.com/zeromicro/go-zero/core/logx"
	"time"
)

type ListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewListLogic(ctx context.Context, svcCtx *svc.ServiceContext) ListLogic {
	return ListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ListLogic) List(req *types.TaskListRequest) (resp *types.TaskListResponse, err error) {
	//kiểm tra xem có quyền ko
	if !l.svcCtx.Auth.Task.CanList(l.ctx, nil) {
		logx.WithContext(l.ctx).Infof("Bạn ko có quyền")
		return nil, auth.ErrTaskListDenied
	}

	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		limit, offset := bquery.Paging(req.PageSize, req.Page)
		order := bquery.Ordering(req.OrderBy)

		query := tx.Task.Query().
			WithAssignments(func(q *ent.TaskAssignmentQuery) { q.WithUser() }).
			WithDepartmentAssignments(func(q *ent.TaskDepartmentQuery) { q.WithDepartment().WithCompleter() }).
			WithCreator().WithChildren().WithParent().WithPerson().Order(order)

		if req.PrimaryId > 0 {
			query = query.Where(task.HasAssignmentsWith(taskassignment.RoleEQ("primary"), taskassignment.UserID(req.PrimaryId)))
		}

		if req.ContributorId > 0 {
			query = query.Where(task.Or(task.CreatorID(req.ContributorId), task.HasAssignmentsWith(taskassignment.RoleEQ("contributor"), taskassignment.UserID(req.ContributorId))))
		}

		if req.SlowProcess {
			query = query.Where(
				task.DueDateLT(time.Now()),
				task.EndDateIsNil(),
			)
		}

		filterMap := make(map[string]interface{})
		_ = cast2.AtomNoZero(&filterMap, req.Filter)

		for field, value := range filterMap {
			query = query.Where(sql.FieldEQ(field, value))
		}

		total, _ := query.Count(l.ctx)
		tasks, e := query.Limit(limit).Offset(offset).All(l.ctx) // Giả sử limit là 10 và offset là 5

		if e != nil {
			err = types.ErrTaskGetList.Wrap(e)
			return err
		}

		resp = new(types.TaskListResponse)
		resp.Tasks = lo.Map(tasks, func(task *ent.Task, _ int) types.TaskResponse {
			t := types.TaskResponse{}
			_ = cast2.EntToResp(&t, task)
			return t
		})
		resp.Total = total

		return nil
	})

	return resp, err
}
