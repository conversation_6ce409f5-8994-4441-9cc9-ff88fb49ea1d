package task

import (
	"bcare/api/internal/auth"
	"bcare/api/internal/dto"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/cast"
	"context"
	"github.com/zeromicro/go-zero/core/logx"
)

type BulkUpdateLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// NewBulkUpdateLogic initializes a new BulkUpdateLogic instance
func NewBulkUpdateLogic(ctx context.Context, svcCtx *svc.ServiceContext) *BulkUpdateLogic {
	return &BulkUpdateLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// BulkUpdate handles bulk update operations for multiple tasks
// It processes each task update within a transaction and returns success/failure counts
func (l *BulkUpdateLogic) BulkUpdate(req *types.BulkUpdateTasksRequest) (*types.BulkUpdateResult, error) {
	// Check authorization
	if !l.svcCtx.Auth.Task.CanUpdate(l.ctx, nil) {
		return nil, auth.ErrTaskUpdateDenied
	}

	result := &types.BulkUpdateResult{
		SuccessCount: 0,
		Errors:       make([]types.TaskUpdateError, 0, len(req.IdList)), // Pre-allocate capacity
	}

	// Process each task update in parallel using goroutines
	type updateResult struct {
		taskID int
		err    error
	}

	resultChan := make(chan updateResult, len(req.IdList))

	// Process tasks in batch to avoid overwhelming the system
	const batchSize = 5
	for i := 0; i < len(req.IdList); i += batchSize {
		end := i + batchSize
		if end > len(req.IdList) {
			end = len(req.IdList)
		}

		// Process batch of tasks
		for _, taskID := range req.IdList[i:end] {
			go func(id int) {
				// Map input
				input := new(dto.UpdateTaskInput)
				err := cast.ReqToModelInput(input, req)
				input.ID = id

				// Update task
				_, err = l.svcCtx.TaskModel.Update(l.ctx, input)
				resultChan <- updateResult{taskID: id, err: err}
			}(taskID)
		}
	}

	// Collect results
	for i := 0; i < len(req.IdList); i++ {
		res := <-resultChan
		if res.err != nil {
			result.Errors = append(result.Errors, types.TaskUpdateError{
				TaskID: res.taskID,
				Error:  res.err.Error(),
			})
			result.FailCount++
		} else {
			result.SuccessCount++
		}
	}

	if result.SuccessCount == 0 {
		return nil, types.ErrTaskBulkUpdate.Wrap(nil)
	}

	return result, nil
}

// processTaskUpdate handles the update operation for a single task
