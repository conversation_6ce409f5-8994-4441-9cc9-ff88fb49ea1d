package task

import (
	"context"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type TaskIsLastLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewTaskIsLastLogic(ctx context.Context, svcCtx *svc.ServiceContext) *TaskIsLastLogic {
	return &TaskIsLastLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *TaskIsLastLogic) TaskIsLast(req *types.TaskGetRequest) (resp *types.TaskIsLastResponse, err error) {
	// Get the task to check its person ID
	taskOutput, err := l.svcCtx.TaskModel.Get(l.ctx, req.Id)
	if err != nil {
		return nil, err
	}

	// If no person is associated, it's not related to any person so FALSE
	if taskOutput.PersonID == nil {
		return &types.TaskIsLastResponse{IsLast: false}, nil
	}

	// Count active tasks for this person excluding the current one
	activeTaskCount, err := l.svcCtx.TaskModel.CountActiveTasksForPersonExcludingCurrent(l.ctx, *taskOutput.PersonID, req.Id)
	if err != nil {
		return nil, err
	}

	isLast := activeTaskCount == 0
	return &types.TaskIsLastResponse{IsLast: isLast}, nil
}
