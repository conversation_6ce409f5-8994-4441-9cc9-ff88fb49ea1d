package referral

import (
	"bcare/ent/organization"
	"bcare/ent/person"
	"context"
	"entgo.io/ent/dialect/sql"
	"fmt"
	"github.com/jinzhu/copier"
	"sync"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type SearchReferrerLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewSearchReferrerLogic(ctx context.Context, svcCtx *svc.ServiceContext) SearchReferrerLogic {
	return SearchReferrerLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *SearchReferrerLogic) SearchReferrer(req *types.SearchRequest) (resp *types.SearchResponse, err error) {
	var wg sync.WaitGroup
	persons := []*types.Referrer{}
	organizations := []*types.Referrer{}

	wg.Add(2) // Tăng số lượng goroutine cần đợi lên 2

	getPersons := func() []*types.Referrer {
		defer wg.Done()
		result := []*types.Referrer{}
		query := l.svcCtx.Ent.Person.Query().
			Where(
				person.Or(
					person.PhoneContains(req.Search),
					person.FullNameContainsFold(req.Search),
					func(s *sql.Selector) {
						s.Where(sql.P(func(b *sql.Builder) {
							b.WriteString("person_field->>'code' = ")
							b.WriteString(fmt.Sprintf("'%s'", req.Search))
						}))
					},
				),
			)
		persons, e := query.Limit(10).Offset(0).All(l.ctx)
		if e != nil {
			return nil
		}
		e = copier.Copy(&result, persons)
		if e != nil {
			return nil
		}
		for i, _ := range result {
			result[i].EntityType = "person"
		}
		return result
	}

	getOrganizations := func() []*types.Referrer {
		defer wg.Done()
		result := []*types.Referrer{}
		query := l.svcCtx.Ent.Organization.Query().
			Where(organization.Or(organization.PhoneContainsFold(req.Search), organization.FullNameContains(req.Search)))
		organizations, e := query.Limit(10).Offset(0).All(l.ctx)
		if e != nil {
			return nil
		}
		e = copier.Copy(&result, organizations)
		if e != nil {
			return nil
		}
		for i, _ := range result {
			result[i].EntityType = "organization"
		}
		return result
	}

	personsChan := make(chan []*types.Referrer)
	organizationsChan := make(chan []*types.Referrer)

	go func() {
		personsChan <- getPersons()
	}()

	go func() {
		organizationsChan <- getOrganizations()
	}()

	persons = <-personsChan
	organizations = <-organizationsChan

	wg.Wait()

	personResp := []types.Referrer{}
	organizationResp := []types.Referrer{}
	if persons != nil {
		_ = copier.Copy(&personResp, persons)
	}

	if organizationResp != nil {
		_ = copier.Copy(&organizationResp, organizations)
	}

	resp = &types.SearchResponse{
		Referrers: append(personResp, organizationResp...),
	}

	return
}
