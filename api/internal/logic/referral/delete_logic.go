package referral

import (
	"context"
	"github.com/jinzhu/copier"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewDeleteLogic(ctx context.Context, svcCtx *svc.ServiceContext) DeleteLogic {
	return DeleteLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DeleteLogic) Delete(req *types.ReferralDeleteRequest) (resp *types.Referral, err error) {
	referral, err := l.svcCtx.Ent.Referral.Get(l.ctx, req.Id)
	if err != nil {
		return nil, types.ErrReferralDelete.Wrap(err)
	}

	err = l.svcCtx.Ent.Referral.DeleteOne(referral).Exec(l.ctx)
	if err != nil {
		return nil, types.ErrReferralDelete.Wrap(err)
	}

	resp = new(types.Referral)
	eCopy := copier.Copy(resp, referral)
	if eCopy != nil {
		err = types.ErrReferralDelete.Wrap(eCopy)
		return
	}

	return
}
