package referral

import (
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/ent"
	"bcare/ent/referral"
	"context"
	"github.com/jinzhu/copier"

	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateLogic(ctx context.Context, svcCtx *svc.ServiceContext) UpdateLogic {
	return UpdateLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateLogic) Update(req *types.ReferralUpdateRequest) (resp *types.Referral, err error) {
	referralRecord, err := l.svcCtx.Ent.Referral.Query().
		Where(referral.ReferredPersonID(req.ReferredPersonId)).
		Only(l.ctx)

	if err != nil {
		if ent.IsNotFound(err) {
			return nil, types.ErrReferralUpdate
		}
		return nil, types.ErrReferralUpdate.Wrap(err)
	}

	referralRecord, err = referralRecord.Update().
		SetNotes(req.Notes).
		SetEntityType(req.EntityType).
		SetEntityID(req.EntityId).
		SetReferrerRelationship(req.ReferrerRelationship).
		Save(l.ctx)

	if err != nil {
		return nil, types.ErrReferralUpdate.Wrap(err)
	}

	resp = new(types.Referral)
	eCopy := copier.Copy(resp, referralRecord)
	if eCopy != nil {
		err = types.ErrReferralUpdate.Wrap(eCopy)
		return
	}

	return
}
