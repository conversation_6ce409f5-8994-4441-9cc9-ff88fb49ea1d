package referral

import (
	cast2 "bcare/common/cast"
	"bcare/ent"
	"context"
	"github.com/jinzhu/copier"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type AddLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAddLogic(ctx context.Context, svcCtx *svc.ServiceContext) AddLogic {
	return AddLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AddLogic) Add(req *types.ReferralAddRequest) (resp *types.Referral, err error) {
	referral := new(ent.Referral)
	_ = copier.CopyWithOption(referral, req, cast2.PayloadToEnt)

	referral, err = l.svcCtx.Ent.Referral.Create().
		SetReferral(referral).
		Save(l.ctx)

	if err != nil {
		return nil, types.ErrReferralAdd.Wrap(err)
	}

	resp = new(types.Referral)
	eCopy := copier.Copy(&resp, referral)
	if eCopy != nil {
		err = types.ErrReferralAdd.Wrap(eCopy)
		return
	}

	return
}
