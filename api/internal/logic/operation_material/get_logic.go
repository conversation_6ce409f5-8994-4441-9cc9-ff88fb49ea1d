package operation_material

import (
	"bcare/api/internal/dto"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/berr"
	"bcare/common/cast"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetLogic {
	return &GetLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetLogic) Get(req *types.OperationMaterialGetRequest) (resp *types.OperationMaterialResponse, err error) {
	input := &dto.GetOperationMaterialInput{
		ID:          req.Id,
		OperationID: req.OperationId,
		MaterialID:  req.MaterialId,
	}

	operationMaterialOutput, err := l.svcCtx.OperationMaterialModel.GetOperationMaterial(l.ctx, input)
	if err != nil {
		return nil, err
	}

	if !l.svcCtx.Auth.OperationMaterial.CanGet(l.ctx, operationMaterialOutput) {
		return nil, berr.ErrPermissionDenied.Wrap(nil)
	}

	resp = new(types.OperationMaterialResponse)
	err = cast.ConvertViaJson(resp, operationMaterialOutput)
	if err != nil {
		return nil, err
	}

	return resp, nil
}
