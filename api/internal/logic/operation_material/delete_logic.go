package operation_material

import (
	"bcare/api/internal/dto"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/berr"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewDeleteLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteLogic {
	return &DeleteLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DeleteLogic) Delete(req *types.OperationMaterialDeleteRequest) (err error) {
	existingOperationMaterial, err := l.svcCtx.OperationMaterialModel.GetOperationMaterial(l.ctx, &dto.GetOperationMaterialInput{ID: req.Id})
	if err != nil {
		return err
	}

	if !l.svcCtx.Auth.OperationMaterial.CanDelete(l.ctx, existingOperationMaterial) {
		return berr.ErrPermissionDenied.Wrap(nil)
	}

	input := &dto.DeleteOperationMaterialInput{
		ID:          req.Id,
		OperationID: req.OperationId,
	}

	err = l.svcCtx.OperationMaterialModel.DeleteOperationMaterial(l.ctx, input)
	if err != nil {
		return err
	}

	return nil
}
