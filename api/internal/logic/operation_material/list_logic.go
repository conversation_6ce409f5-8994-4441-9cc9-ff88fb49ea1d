package operation_material

import (
	"bcare/api/internal/dto"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/berr"
	"bcare/common/cast"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type ListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ListLogic {
	return &ListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ListLogic) List(req *types.OperationMaterialListRequest) (resp *types.OperationMaterialListResponse, err error) {
	if !l.svcCtx.Auth.OperationMaterial.CanList(l.ctx) {
		return nil, berr.ErrPermissionDenied.Wrap(nil)
	}

	input := &dto.ListOperationMaterialInput{
		PageSize: req.PageSize,
		Page:     req.Page,
		Search:   req.Search,
		OrderBy:  req.OrderBy,
		Filter: dto.OperationMaterialFilterInput{
			OperationID: req.Filter.OperationId,
			MaterialID:  req.Filter.MaterialId,
			Status:      req.Filter.Status,
		},
	}

	listOutput, err := l.svcCtx.OperationMaterialModel.ListOperationMaterials(l.ctx, input)
	if err != nil {
		return nil, err
	}

	resp = &types.OperationMaterialListResponse{
		OperationMaterials: make([]types.OperationMaterialResponse, 0, len(listOutput.OperationMaterials)),
		Total:              listOutput.Total,
		TotalPage:          listOutput.TotalPage,
	}

	err = cast.ConvertViaJson(&resp.OperationMaterials, listOutput.OperationMaterials)
	if err != nil {
		return nil, err
	}

	return resp, nil
}
