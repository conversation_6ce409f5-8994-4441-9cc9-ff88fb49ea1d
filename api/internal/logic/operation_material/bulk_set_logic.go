package operation_material

import (
	"bcare/api/internal/dto"
	"bcare/common/cast"

	// "bcare/common/cast" // Removed unused import
	"context"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type BulkSetLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewBulkSetLogic(ctx context.Context, svcCtx *svc.ServiceContext) *BulkSetLogic {
	return &BulkSetLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *BulkSetLogic) BulkSet(req *types.BulkSetRequest) error {
	input := new(dto.BulkSetInput)
	err := cast.ReqToModelInput(input, req)
	if err != nil {
		return err
	}

	err = l.svcCtx.OperationMaterialModel.BulkSetOperationMaterials(l.ctx, input)
	if err != nil {
		return err
	}

	return nil
}
