package operation_material

import (
	"bcare/api/internal/dto"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/berr"
	"bcare/common/cast"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateLogic {
	return &UpdateLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateLogic) Update(req *types.OperationMaterialUpdateRequest) (resp *types.OperationMaterialResponse, err error) {
	existingOperationMaterial, err := l.svcCtx.OperationMaterialModel.GetOperationMaterial(l.ctx, &dto.GetOperationMaterialInput{
		ID: req.Id,
	})
	if err != nil {
		return nil, err
	}

	if existingOperationMaterial == nil {
		return nil, berr.ErrNotFound.Wrap(nil)
	}

	if !l.svcCtx.Auth.OperationMaterial.CanUpdate(l.ctx, existingOperationMaterial) {
		return nil, berr.ErrPermissionDenied.Wrap(nil)
	}

	operationId := req.OperationId
	materialId := req.MaterialId
	quantity := req.Quantity
	status := req.Status

	input := &dto.UpdateOperationMaterialInput{
		ID:          req.Id,
		OperationID: &operationId,
		MaterialID:  &materialId,
		Quantity:    &quantity,
		Status:      &status,
	}

	output, err := l.svcCtx.OperationMaterialModel.UpdateOperationMaterial(l.ctx, input)
	if err != nil {
		return nil, err
	}

	resp = &types.OperationMaterialResponse{}
	err = cast.ConvertViaJson(resp, output)
	if err != nil {
		return nil, err
	}

	return resp, nil
}
