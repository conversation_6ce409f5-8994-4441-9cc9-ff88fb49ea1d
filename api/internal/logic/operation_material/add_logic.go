package operation_material

import (
	"bcare/api/internal/dto"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/berr"
	"bcare/common/cast"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type AddLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAddLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AddLogic {
	return &AddLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AddLogic) Add(req *types.OperationMaterialAddRequest) (resp *types.OperationMaterialResponse, err error) {
	if !l.svcCtx.Auth.OperationMaterial.CanAdd(l.ctx) {
		return nil, berr.ErrPermissionDenied.Wrap(nil)
	}

	input := new(dto.CreateOperationMaterialInput)
	err = cast.ReqToModelInput(input, req)
	if err != nil {
		return nil, err
	}

	operationMaterialOutput, err := l.svcCtx.OperationMaterialModel.CreateOperationMaterial(l.ctx, input)
	if err != nil {
		return nil, err
	}

	resp = new(types.OperationMaterialResponse)
	err = cast.ConvertViaJson(resp, operationMaterialOutput)
	if err != nil {
		return nil, err
	}

	return resp, nil
}
