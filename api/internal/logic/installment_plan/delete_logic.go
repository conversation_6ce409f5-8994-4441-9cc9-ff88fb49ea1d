package installment_plan

import (
	"bcare/common/bconst"
	"bcare/common/bquery"
	"bcare/ent"
	"context"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewDeleteLogic(ctx context.Context, svcCtx *svc.ServiceContext) DeleteLogic {
	return DeleteLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DeleteLogic) Delete(req *types.InstallmentPlanDeleteRequest) (resp *types.InstallmentPlan, err error) {
	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		planRecord, err := tx.InstallmentPlan.Get(l.ctx, req.Id)
		if err != nil && ent.IsNotFound(err) {
			return types.ErrPlanDelete.Wrap(err, err.Error())
		}

		planRecord.Status = bconst.StatusDeleted
		err = tx.InstallmentPlan.DeleteOne(planRecord).Exec(l.ctx)
		if err != nil {
			return types.ErrPlanDelete.Wrap(err, err.Error())
		}
		return nil
	})

	return resp, err
}
