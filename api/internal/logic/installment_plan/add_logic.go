package installment_plan

import (
	"bcare/api/model"
	"bcare/common/bquery"
	cast2 "bcare/common/cast"
	"bcare/common/ctxdata"
	"bcare/ent"
	"bcare/ent/installmentplan"
	"context"
	"github.com/jinzhu/copier"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type AddLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAddLogic(ctx context.Context, svcCtx *svc.ServiceContext) AddLogic {
	return AddLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AddLogic) Add(req *types.InstallmentPlanAddRequest) (resp *types.InstallmentPlanResponse, err error) {
	planRecord := new(ent.InstallmentPlan)
	installments := []*ent.Installment{}
	if req.TotalInstallments == 0 {
		return nil, nil
	}

	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		// Kiểm tra xem deal_id đã có installment_plan chưa
		existingPlan, err := tx.InstallmentPlan.Query().
			Where(installmentplan.DealID(req.DealId)).
			First(l.ctx)

		if err != nil && !ent.IsNotFound(err) {
			return err
		}

		// Nếu đã có installment plan, thực hiện update
		if existingPlan != nil {
			updateReq := &types.InstallmentPlanUpdateRequest{
				Id:                existingPlan.ID,
				Name:              req.Name,
				State:             req.State,
				TotalAmount:       req.TotalAmount,
				DownPayment:       req.DownPayment,
				TotalInstallments: req.TotalInstallments,
				Modified:          []string{"name", "state", "total_amount", "down_payment", "total_installments"},
			}

			installments, err = model.HandleInstallments(l.ctx, tx, existingPlan, updateReq, false)
			if err != nil {
				return err
			}

			e := copier.Copy(existingPlan, updateReq)
			if e != nil {
				logx.Errorf("copier copy deal failed, err:%v", e)
				return types.ErrPlanCopyFailed.Wrap(e, e.Error())
			}

			planRecord, err = tx.InstallmentPlan.UpdateOneID(existingPlan.ID).
				SetInstallmentPlan(existingPlan, updateReq.Modified...).
				Save(l.ctx)
			if err != nil {
				return types.ErrPlanUpdate.Wrap(err, err.Error())
			}
		} else {
			err = copier.CopyWithOption(&planRecord, req, cast2.PayloadToEnt)
			if err != nil {
				logx.WithContext(l.ctx).Errorf("cannot copy request: %v", err)
				return types.ErrPlanCopyFailed.Wrap(err, err.Error())
			}
			planRecord.UserID = ctxdata.GetUidFromCtx(l.ctx)
			planRecord, err = tx.InstallmentPlan.Create().SetInstallmentPlan(planRecord).Save(l.ctx)
			if err != nil {
				return types.ErrPlanAdd.Wrap(err, err.Error())
			}

			installments, err = model.HandleInstallments(l.ctx, tx, planRecord, req, true)
			if err != nil {
				return err
			}
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	resp = new(types.InstallmentPlanResponse)
	_ = cast2.ConvertViaJson(&resp, planRecord)
	_ = copier.Copy(&resp.Installment, installments)
	return resp, nil
}
