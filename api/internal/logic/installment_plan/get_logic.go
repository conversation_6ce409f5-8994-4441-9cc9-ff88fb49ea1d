package installment_plan

import (
	cast2 "bcare/common/cast"
	"bcare/ent"
	"bcare/ent/installmentplan"
	"context"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetLogic(ctx context.Context, svcCtx *svc.ServiceContext) GetLogic {
	return GetLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetLogic) Get(req *types.InstallmentPlanGetRequest) (resp *types.InstallmentPlanResponse, err error) {
	planRecord, err := l.svcCtx.Ent.InstallmentPlan.Query().
		WithPerson().
		WithCreator().
		WithAttachments().
		WithDeal().
		Where(installmentplan.ID(req.Id)).First(l.ctx)

	if err != nil && ent.IsNotFound(err) {
		return nil, types.ErrPlanNotFound.Wrap(err, err.Error())
	}

	resp = new(types.InstallmentPlanResponse)
	err = cast2.ConvertViaJson(&resp, planRecord)
	return resp, nil
}
