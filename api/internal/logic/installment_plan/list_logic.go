package installment_plan

import (
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/api/model"
	"bcare/common/bquery"
	cast2 "bcare/common/cast"
	"bcare/ent"
	"bcare/ent/installment"
	"context"
	"entgo.io/ent/dialect/sql"
	"github.com/samber/lo"
	"github.com/zeromicro/go-zero/core/logx"
)

type ListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewListLogic(ctx context.Context, svcCtx *svc.ServiceContext) ListLogic {
	return ListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ListLogic) List(req *types.InstallmentPlanListRequest) (resp *types.InstallmentPlanListResponse, err error) {
	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		limit, offset := bquery.Paging(req.PageSize, req.Page)
		order := bquery.Ordering(req.OrderBy)
		query := tx.InstallmentPlan.Query().
			WithPerson().
			WithCreator().
			WithInstallments(func(q *ent.InstallmentQuery) { q.WithAllocations() }).
			Limit(limit).Offset(offset).Order(order)

		filterMap := make(map[string]interface{})
		_ = cast2.AtomNoZero(&filterMap, req.Filter)

		for field, value := range filterMap {
			query = query.Where(sql.FieldEQ(field, value))
		}

		total, e := query.Count(l.ctx)
		if e != nil {
			return types.ErrPlanGetList.Wrap(e, e.Error())
		}

		plans, e := query.All(l.ctx)
		if e != nil {
			return types.ErrPlanGetList.Wrap(e, e.Error())
		}

		dealIDs := make([]int, 1)
		dealIDs[0] = req.Filter.DealId
		discountUsages, discountErr := model.FetchDiscountUsages(l.svcCtx.Ent.DiscountUsage.Query(), dealIDs, l.ctx)
		if discountErr != nil {
			return types.ErrDiscountUsageGetList.Wrap(discountErr)
		}

		resp = new(types.InstallmentPlanListResponse)
		resp.Total = total
		resp.InstallmentPlans = lo.Map(plans, func(plan *ent.InstallmentPlan, _ int) types.InstallmentPlanResponse {
			p := types.InstallmentPlanResponse{}
			_ = cast2.ConvertViaJson(&p, plan)

			var totalPaidAmount float64
			var paidInstallmentCount int
			var refundAmount float64
			var discountAmount float64

			p.Installment = lo.Map(plan.Edges.Installments, func(i *ent.Installment, _ int) types.InstallmentResponse {
				var installmentResp types.InstallmentResponse
				_ = cast2.ConvertViaJson(&installmentResp, i)
				paidAmount := 0.0
				for _, allocation := range i.Edges.Allocations {
					paidAmount += allocation.Amount
				}

				if paidAmount == i.Amount {
					installmentResp.State = "paid"
				} else if paidAmount < i.Amount && paidAmount > 0 {
					installmentResp.State = "partially_paid"
				} else if i.PaidAt != nil {
					installmentResp.State = "paid"
				} else {
					installmentResp.State = "unpaid"
				}
				switch i.Kind {
				case installment.KindSequencePayment:
					if installmentResp.State == "paid" {
						paidInstallmentCount++
					}
					if i.PaidAt != nil {
						totalPaidAmount += paidAmount
					}
				case installment.KindRefundPayment:
					refundAmount += i.Amount
				}
				return installmentResp
			})

			for _, du := range discountUsages {
				discountAmount += du.Value * float64(du.UsageCount)
			}

			p.PaidAmount = totalPaidAmount
			p.PaidInstallmentCount = paidInstallmentCount
			p.RefundAmount = refundAmount
			p.DiscountAmount = discountAmount
			return p
		})
		return nil
	})

	return resp, err
}
