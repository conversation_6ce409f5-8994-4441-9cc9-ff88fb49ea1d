package installment_plan

import (
	"bcare/api/model"
	"bcare/common/bquery"
	cast2 "bcare/common/cast"
	"bcare/ent"
	"context"
	"github.com/jinzhu/copier"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateLogic(ctx context.Context, svcCtx *svc.ServiceContext) UpdateLogic {
	return UpdateLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateLogic) Update(req *types.InstallmentPlanUpdateRequest) (resp *types.InstallmentPlanResponse, err error) {
	planRecord := new(ent.InstallmentPlan)
	installments := []*ent.Installment{}
	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		planRecord, err = tx.InstallmentPlan.Get(l.ctx, req.Id)
		if err != nil && ent.IsNotFound(err) {
			return types.ErrPlanNotFound.Wrap(err, err.Error())
		}
		if planRecord == nil {
			logx.Errorf("plan not found")
			return types.ErrPlanNotFound
		}

		// Xử lý việc tạo hoặc xóa các bản ghi trong bảng Installment
		installments, err = model.HandleInstallments(l.ctx, tx, planRecord, req, false)
		if err != nil {
			return err
		}
		e := copier.Copy(planRecord, req)
		if e != nil {
			logx.Errorf("copier copy deal failed, err:%v", e)
			return types.ErrPlanCopyFailed.Wrap(e, e.Error())
		}

		planRecord, e = tx.InstallmentPlan.UpdateOneID(req.Id).SetInstallmentPlan(planRecord, req.Modified...).Save(l.ctx)
		if e != nil {
			return types.ErrPlanUpdate.Wrap(e, e.Error())
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	resp = new(types.InstallmentPlanResponse)

	_ = cast2.ConvertViaJson(&resp, planRecord)
	_ = copier.Copy(&resp.Installment, installments)

	return resp, nil
}
