package installment_plan

import (
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/bconst"
	"bcare/common/bquery"
	cast2 "bcare/common/cast"
	"bcare/ent"
	"bcare/ent/deal"
	"bcare/ent/installment"
	"bcare/ent/installmentplan"
	"context"
	"github.com/samber/lo"
	"github.com/zeromicro/go-zero/core/logx"
)

type ListRefundableLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewListRefundableLogic(ctx context.Context, svcCtx *svc.ServiceContext) ListRefundableLogic {
	return ListRefundableLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ListRefundableLogic) ListRefundable(req *types.InstallmentPlanRefundableListRequest) (resp *types.InstallmentPlanListResponse, err error) {
	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		query := tx.InstallmentPlan.Query().
			WithPerson().
			WithCreator().
			WithInstallments(func(q *ent.InstallmentQuery) { q.WithAllocations() })
		query = query.Where(
			installmentplan.PersonID(req.PersonId),
			installmentplan.StateNEQ(installmentplan.StateCancelled),
			installmentplan.HasDealWith(deal.StatusNEQ(bconst.StatusDeleted),
				deal.DeletedAtIsNil(),
				deal.StateNEQ(deal.StateLost)),
			installmentplan.Not(
				installmentplan.HasInstallmentsWith(
					installment.KindEQ(installment.KindRefundPayment),
					installment.StatusNEQ(bconst.StatusDeleted),
				),
			),
		)

		plans, e := query.All(l.ctx)
		if e != nil {
			return types.ErrPlanGetList.Wrap(e, e.Error())
		}

		resp = new(types.InstallmentPlanListResponse)
		// First, process all plans
		processedPlans := lo.Map(plans, func(plan *ent.InstallmentPlan, _ int) types.InstallmentPlanResponse {
			p := types.InstallmentPlanResponse{}
			_ = cast2.EntToResp(&p, plan)

			var totalPaidAmount float64
			var paidInstallmentCount int

			p.Installment = lo.Map(plan.Edges.Installments, func(i *ent.Installment, _ int) types.InstallmentResponse {
				var installmentResp types.InstallmentResponse
				_ = cast2.EntToResp(&installmentResp, i)
				paidAmount := 0.0
				for _, allocation := range i.Edges.Allocations {
					paidAmount += allocation.Amount
				}

				if paidAmount == i.Amount {
					installmentResp.State = "paid"
				} else if paidAmount < i.Amount && paidAmount > 0 {
					installmentResp.State = "partially_paid"
				} else {
					installmentResp.State = "unpaid"
				}
				switch i.Kind {
				case installment.KindSequencePayment:
					if installmentResp.State == "paid" {
						paidInstallmentCount++
					}
				}
				totalPaidAmount += paidAmount
				return installmentResp
			})

			p.PaidAmount = totalPaidAmount
			p.PaidInstallmentCount = paidInstallmentCount
			return p
		})

		// Filter out plans with totalPaidAmount = 0
		resp.InstallmentPlans = lo.Filter(processedPlans, func(plan types.InstallmentPlanResponse, _ int) bool {
			return plan.PaidAmount > 0
		})

		return nil
	})

	return resp, err
}
