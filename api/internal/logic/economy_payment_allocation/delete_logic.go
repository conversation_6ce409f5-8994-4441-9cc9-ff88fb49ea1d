package economy_payment_allocation

import (
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/bconst"
	"bcare/common/bquery"
	"bcare/ent"
	"context"
	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewDeleteLogic(ctx context.Context, svcCtx *svc.ServiceContext) DeleteLogic {
	return DeleteLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DeleteLogic) Delete(req *types.PaymentAllocationDeleteRequest) (resp *types.PaymentAllocationResponse, err error) {
	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		allocationRecord, err := tx.PaymentAllocation.Get(l.ctx, req.Id)

		if err != nil && ent.IsNotFound(err) {
			return types.ErrPaymentAllocationNotFound.Wrap(err)
		}

		//if !l.svcCtx.Auth.PaymentAllocation.CanDelete(l.ctx, allocationRecord) {
		//	logx.WithContext(l.ctx).Infof("Permission denied")
		//	return auth.ErrPaymentAllocationDeleteDenied
		//}

		allocationRecord.Status = bconst.StatusDeleted

		err = tx.PaymentAllocation.DeleteOne(allocationRecord).Exec(l.ctx)
		if err != nil {
			return types.ErrPaymentAllocationDelete.Wrap(err)
		}

		return nil
	})

	resp = new(types.PaymentAllocationResponse)
	return
}
