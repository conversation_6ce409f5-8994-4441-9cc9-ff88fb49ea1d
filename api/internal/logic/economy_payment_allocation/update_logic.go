package economy_payment_allocation

import (
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/bquery"
	"bcare/common/butils"
	cast2 "bcare/common/cast"
	"bcare/ent"
	"bcare/ent/paymentallocation"
	"context"
	"fmt"
	"github.com/jinzhu/copier"

	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateLogic(ctx context.Context, svcCtx *svc.ServiceContext) UpdateLogic {
	return UpdateLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateLogic) Update(req *types.PaymentAllocationUpdateRequest) (resp *types.PaymentAllocationResponse, err error) {
	allocationRecord := new(ent.PaymentAllocation)
	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		// Get current allocation record
		allocationRecord, err = tx.PaymentAllocation.Get(l.ctx, req.Id)
		if err != nil && ent.IsNotFound(err) {
			return types.ErrPaymentAllocationNotFound.Wrap(err, "PaymentAllocation ID %d not found", req.Id)
		}

		if allocationRecord == nil {
			return types.ErrPaymentAllocationNotFound.Wrap(nil, "PaymentAllocation record is nil for ID %d", req.Id)
		}

		// If amount is being modified, perform validations
		if butils.Contains(req.Modified, paymentallocation.FieldAmount) {
			// 1. Validate against payment total_amount
			payment, err := tx.Payment.Get(l.ctx, allocationRecord.PaymentID)
			if err != nil {
				return types.ErrPaymentNotFound.Wrap(err, "Payment not found with ID: %d", allocationRecord.PaymentID)
			}

			// Get sum of other allocations for this payment (excluding current)
			existingPaymentAllocationsTotal, err := tx.PaymentAllocation.Query().
				Where(
					paymentallocation.And(
						paymentallocation.PaymentID(allocationRecord.PaymentID),
						paymentallocation.IDNEQ(req.Id),
					),
				).
				Aggregate(
					ent.Sum(paymentallocation.FieldAmount),
				).
				Float64(l.ctx)
			if err != nil {
				existingPaymentAllocationsTotal = 0
			}

			newPaymentAllocationsTotal := existingPaymentAllocationsTotal + req.Amount
			if newPaymentAllocationsTotal > payment.TotalAmount {
				return types.ErrPaymentAllocationUpdate.Wrap(nil,
					fmt.Sprintf("Total allocations (%f) would exceed payment total amount (%f)",
						newPaymentAllocationsTotal, payment.TotalAmount))
			}

			// 2. Validate against bill item amount
			if allocationRecord.BillItemID != nil {
				billItem, err := tx.BillItem.Get(l.ctx, *allocationRecord.BillItemID)
				if err != nil {
					return types.ErrBillItemNotFound.Wrap(err, "Bill item not found with ID: %d", allocationRecord.BillItemID)
				}

				if billItem.AttachmentID != nil {
					attachmentRecord, _ := tx.Attachment.Get(l.ctx, req.Id)
					if attachmentRecord != nil {
						attachmentRecord.Status = 2
						attachmentRecord, _ = tx.Attachment.UpdateOneID(req.Id).SetStatus(2).Save(l.ctx)
					}
				}

				// Get sum of other allocations for this bill item (excluding current)
				existingBillItemAllocationsTotal, err := tx.PaymentAllocation.Query().
					Where(
						paymentallocation.And(
							paymentallocation.BillItemID(*allocationRecord.BillItemID),
							paymentallocation.IDNEQ(req.Id),
						),
					).
					Aggregate(
						ent.Sum(paymentallocation.FieldAmount),
					).
					Float64(l.ctx)
				if err != nil {
					existingBillItemAllocationsTotal = 0
				}

				newBillItemAllocationsTotal := existingBillItemAllocationsTotal + req.Amount
				if newBillItemAllocationsTotal > billItem.Amount {
					return types.ErrPaymentAllocationUpdate.Wrap(nil,
						fmt.Sprintf("Total allocations (%f) would exceed bill item amount (%f)",
							newBillItemAllocationsTotal, billItem.Amount))
				}
			}
		}

		// Copy request data to allocation record
		err = copier.CopyWithOption(&allocationRecord, req, cast2.PayloadToEnt)
		if err != nil {
			return types.ErrPaymentAllocationUpdate.Wrap(err, "Error copying payment allocation data")
		}

		allocationRecord, err = tx.PaymentAllocation.UpdateOneID(req.Id).
			SetPaymentAllocation(allocationRecord, req.Modified...).
			Save(l.ctx)
		if err != nil {
			return types.ErrPaymentAllocationUpdate.Wrap(err, "Error updating payment allocation record with ID %d", req.Id)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	resp = new(types.PaymentAllocationResponse)
	_ = cast2.ConvertViaJson(&resp, allocationRecord)

	return resp, nil
}
