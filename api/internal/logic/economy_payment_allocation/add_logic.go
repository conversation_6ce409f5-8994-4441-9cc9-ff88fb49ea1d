package economy_payment_allocation

import (
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/bquery"
	cast2 "bcare/common/cast"
	"bcare/common/ctxdata"
	"bcare/ent"
	"bcare/ent/paymentallocation"
	"context"
	"fmt"
	"github.com/jinzhu/copier"

	"github.com/zeromicro/go-zero/core/logx"
)

type AddLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAddLogic(ctx context.Context, svcCtx *svc.ServiceContext) AddLogic {
	return AddLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AddLogic) Add(req *types.PaymentAllocationAddRequest) (resp *types.PaymentAllocationResponse, err error) {
	allocation := new(ent.PaymentAllocation)
	err = copier.Copy(allocation, req)
	if err != nil {
		return nil, types.ErrPaymentAllocationCopyFailed.Wrap(err, "Error copying request data")
	}

	//if !l.svcCtx.Auth.PaymentAllocation.CanAdd(l.ctx, nil) {
	//	logx.WithContext(l.ctx).Infof("Permission denied")
	//	return nil, auth.ErrPaymentAllocationAddDenied
	//}

	// Validate within transaction to ensure consistency
	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		// 1. Validate against payment total_amount
		payment, err := tx.Payment.Get(l.ctx, req.PaymentId)
		if err != nil {
			return types.ErrPaymentNotFound.Wrap(err, "Payment not found with ID: %d", req.PaymentId)
		}

		existingPaymentAllocationsTotal, err := tx.PaymentAllocation.Query().
			Where(
				paymentallocation.PaymentID(req.PaymentId),
			).
			Aggregate(
				ent.Sum(paymentallocation.FieldAmount),
			).
			Float64(l.ctx)
		if err != nil {
			existingPaymentAllocationsTotal = 0
		}

		newPaymentAllocationsTotal := existingPaymentAllocationsTotal + req.Amount
		if newPaymentAllocationsTotal > payment.TotalAmount {
			return types.ErrPaymentAllocationAdd.Wrap(nil,
				fmt.Sprintf("Total allocations (%f) would exceed payment total amount (%f)",
					newPaymentAllocationsTotal, payment.TotalAmount))
		}

		// 2. Validate allocation target (either bill_item or installment, not both)
		if req.BillItemId > 0 && req.InstallmentId > 0 {
			return types.ErrPaymentAllocationAdd.Wrap(nil,
				"Cannot allocate to both bill item and installment simultaneously")
		}

		// 3. Validate against bill item amount
		if req.BillItemId > 0 {
			billItem, err := tx.BillItem.Get(l.ctx, req.BillItemId)
			if err != nil {
				return types.ErrBillItemNotFound.Wrap(err, "Bill item not found with ID: %d", req.BillItemId)
			}

			existingBillItemAllocationsTotal, err := tx.PaymentAllocation.Query().
				Where(
					paymentallocation.BillItemID(req.BillItemId),
				).
				Aggregate(
					ent.Sum(paymentallocation.FieldAmount),
				).
				Float64(l.ctx)
			if err != nil {
				existingBillItemAllocationsTotal = 0
			}

			newBillItemAllocationsTotal := existingBillItemAllocationsTotal + req.Amount
			if newBillItemAllocationsTotal > billItem.Amount {
				return types.ErrPaymentAllocationAdd.Wrap(nil,
					fmt.Sprintf("Total allocations (%f) would exceed bill item amount (%f)",
						newBillItemAllocationsTotal, billItem.Amount))
			}
		}

		// 4. Validate against installment amount
		if req.InstallmentId > 0 {
			installment, err := tx.Installment.Get(l.ctx, req.InstallmentId)
			if err != nil {
				return types.ErrInstallmentNotFound.Wrap(err, "Installment not found with ID: %d", req.InstallmentId)
			}

			existingInstallmentAllocationsTotal, err := tx.PaymentAllocation.Query().
				Where(
					paymentallocation.InstallmentID(req.InstallmentId),
				).
				Aggregate(
					ent.Sum(paymentallocation.FieldAmount),
				).
				Float64(l.ctx)
			if err != nil {
				existingInstallmentAllocationsTotal = 0
			}

			newInstallmentAllocationsTotal := existingInstallmentAllocationsTotal + req.Amount
			if newInstallmentAllocationsTotal > installment.Amount {
				return types.ErrPaymentAllocationAdd.Wrap(nil,
					fmt.Sprintf("Total allocations (%f) would exceed installment amount (%f)",
						newInstallmentAllocationsTotal, installment.Amount))
			}
		}

		allocationRecord := new(ent.PaymentAllocation)
		_ = copier.Copy(&allocationRecord, allocation)
		allocationRecord.UserID = ctxdata.GetUidFromCtx(l.ctx)

		// 5. Create payment allocation if all validations pass
		allocation, err = tx.PaymentAllocation.Create().
			SetPaymentAllocation(allocationRecord).
			SetCreatedAt(payment.CreatedAt).
			Save(l.ctx)

		if err != nil {
			return types.ErrPaymentAllocationAdd.Wrap(err, "Error saving payment allocation record")
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	resp = new(types.PaymentAllocationResponse)
	eCopy := cast2.ConvertViaJson(&resp, allocation)
	if eCopy != nil {
		return nil, types.ErrPaymentAllocationCopyFailed.Wrap(eCopy, "Error copying payment allocation response")
	}

	return resp, nil
}
