package schedule

import (
	"bcare/api/internal/auth"
	"bcare/common/bquery"
	"bcare/ent"
	"context"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewDeleteLogic(ctx context.Context, svcCtx *svc.ServiceContext) DeleteLogic {
	return DeleteLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DeleteLogic) Delete(req *types.ScheduleDeleteRequest) (resp *types.ScheduleResponse, err error) {
	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		scheduleRecord, err := tx.Schedule.Get(l.ctx, req.Id)

		if err != nil && ent.IsNotFound(err) {
			return err
		}

		if scheduleRecord == nil {
			logx.Errorf("schedule not found")
			return types.ErrScheduleDelete
		}

		//kiểm tra xem có quyền ko
		if !l.svcCtx.Auth.Schedule.CanDelete(l.ctx, nil) {
			logx.WithContext(l.ctx).Infof("Bạn ko có quyền")
			return auth.ErrScheduleDeleteDenied
		}

		err = tx.Schedule.DeleteOne(scheduleRecord).Exec(l.ctx)
		if err != nil {
			return types.ErrScheduleDelete.Wrap(err)
		}
		return nil
	})

	return
}
