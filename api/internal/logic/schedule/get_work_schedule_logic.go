package schedule

import (
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/bquery"
	"bcare/common/butils"
	cast2 "bcare/common/cast"
	"bcare/ent"
	"bcare/ent/schedule"
	"bcare/ent/user"
	"context"
	"github.com/samber/lo"
	"github.com/zeromicro/go-zero/core/logx"
)

type GetWorkScheduleLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetWorkScheduleLogic(ctx context.Context, svcCtx *svc.ServiceContext) GetWorkScheduleLogic {
	return GetWorkScheduleLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetWorkScheduleLogic) GetWorkSchedule(req *types.ScheduleRequest) (resp *types.ScheduleListResponse, err error) {
	from, to := butils.GetDateRange(req.From, req.To)
	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		query := l.svcCtx.Ent.Schedule.Query().
			Where(
				schedule.StartTimeGTE(from),
				schedule.EndTimeLT(to),
			).
			WithUser()

		if req.UserId > 0 {
			query = query.Where(schedule.HasUserWith(user.IDEQ(req.UserId)))
		}

		schedules, err := query.All(l.ctx)
		if err != nil {
			return err
		}

		resp = new(types.ScheduleListResponse)
		resp.Schedules = lo.Map(schedules, func(schedule *ent.Schedule, _ int) types.ScheduleResponse {
			s := types.ScheduleResponse{}
			_ = cast2.ConvertViaJson(&s, schedule)
			return s
		})

		return nil
	})

	return resp, err
}
