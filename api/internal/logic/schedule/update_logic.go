package schedule

import (
	"bcare/api/internal/auth"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	cast2 "bcare/common/cast"
	"bcare/ent"
	"context"
	"github.com/jinzhu/copier"

	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateLogic(ctx context.Context, svcCtx *svc.ServiceContext) UpdateLogic {
	return UpdateLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateLogic) Update(req *types.ScheduleUpdateRequest) (resp *types.Schedule, err error) {
	scheduleRecord, err := l.svcCtx.Ent.Schedule.Get(l.ctx, req.Id)
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, types.ErrScheduleUpdate.Wrap(err)
		}
		return nil, types.ErrScheduleUpdate.Wrap(err)
	}
	//kiểm tra xem có quyền ko
	if !l.svcCtx.Auth.Schedule.CanUpdate(l.ctx, nil) {
		logx.WithContext(l.ctx).Infof("Bạn ko có quyền")
		return nil, auth.ErrScheduleUpdateDenied
	}

	err = copier.CopyWithOption(&scheduleRecord, req, cast2.PayloadToEnt)
	if err != nil {
		logx.WithContext(l.ctx).Errorf("cannot copy request: %v", err)
		return nil, types.ErrScheduleUpdate.Wrap(err)
	}

	scheduleRecord, err = l.svcCtx.Ent.Schedule.UpdateOneID(req.Id).SetSchedule(scheduleRecord, req.Modified...).Save(l.ctx)
	if err != nil {
		err = types.ErrScheduleUpdate.Wrap(err)
		return
	}

	resp = new(types.Schedule)
	_ = cast2.ConvertViaJson(&resp, scheduleRecord)

	return
}
