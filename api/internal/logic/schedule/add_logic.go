package schedule

import (
	"bcare/api/internal/auth"
	"bcare/common/bquery"
	cast2 "bcare/common/cast"
	"bcare/ent"
	"context"
	"github.com/jinzhu/copier"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type AddLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAddLogic(ctx context.Context, svcCtx *svc.ServiceContext) AddLogic {
	return AddLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AddLogic) Add(req *types.ScheduleAddRequest) (resp *types.ScheduleResponse, err error) {
	scheduleRecord := new(ent.Schedule)
	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		err = copier.CopyWithOption(&scheduleRecord, req, cast2.PayloadToEnt)
		if err != nil {
			logx.WithContext(l.ctx).Errorf("cannot copy request: %v", err)
			return types.ErrScheduleAdd.Wrap(err)
		}

		//kiểm tra xem có quyền ko
		if !l.svcCtx.Auth.Schedule.CanAdd(l.ctx, nil) {
			logx.WithContext(l.ctx).Infof("Bạn ko có quyền")
			return auth.ErrScheduleAddDenied
		}

		scheduleRecord, err = tx.Schedule.Create().SetSchedule(scheduleRecord).Save(l.ctx)
		if err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		return nil, types.ErrScheduleAdd.Wrap(err)
	}

	resp = new(types.ScheduleResponse)
	_ = cast2.ConvertViaJson(&resp, scheduleRecord)
	return resp, nil
}
