package schedule

import (
	"bcare/api/internal/auth"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/bquery"
	"bcare/common/butils"
	cast2 "bcare/common/cast"
	"bcare/ent"
	"bcare/ent/schedule"
	"context"
	"github.com/samber/lo"

	"github.com/zeromicro/go-zero/core/logx"
)

type ListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewListLogic(ctx context.Context, svcCtx *svc.ServiceContext) ListLogic {
	return ListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ListLogic) List(req *types.ScheduleRequest) (resp *types.ScheduleListResponse, err error) {
	//kiểm tra xem có quyền ko
	if !l.svcCtx.Auth.Schedule.CanList(l.ctx, nil) {
		logx.WithContext(l.ctx).Infof("Bạn ko có quyền")
		return nil, auth.ErrScheduleListDenied
	}

	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		query := tx.Schedule.Query().WithUser().Where(schedule.UserID(req.UserId))

		if req.From != "" && req.To != "" {
			fromDate, toDate := butils.GetDateRange(req.From, req.To)

			if !fromDate.IsZero() && !toDate.IsZero() {
				query = query.Where(
					schedule.StartTimeGTE(fromDate),
					schedule.StartTimeLT(toDate),
				)
			}
		}
		schedules, e := query.All(l.ctx)

		if e != nil {
			err = types.ErrFileUsageGetList.Wrap(e)
			return err
		}

		resp = new(types.ScheduleListResponse)
		resp.Schedules = lo.Map(schedules, func(schedule *ent.Schedule, _ int) types.ScheduleResponse {
			s := types.ScheduleResponse{}
			_ = cast2.ConvertViaJson(&s, schedule)
			return s
		})

		return nil
	})

	return resp, err
}
