package admin

import (
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"context"
	"encoding/json"

	"github.com/zeromicro/go-zero/core/logx"
)

type LogsLogic struct {
	logx.Logger
	ctx        context.Context
	svcCtx     *svc.ServiceContext
	logScanner *svc.LogService
}

func NewLogsLogic(ctx context.Context, svcCtx *svc.ServiceContext) LogsLogic {
	return LogsLogic{
		logScanner: svc.NewLogService(svcCtx.Config),
		Logger:     logx.WithContext(ctx),
		ctx:        ctx,
		svcCtx:     svcCtx,
	}
}

func (l *LogsLogic) Logs(req *types.LogsRequest) (resp *types.LogsResponse, err error) {
	var page = req.Page
	var limit = req.PageSize
	if page == 0 {
		page = 1
	}
	if limit == 0 {
		limit = 100
	}

	logs, err := l.logScanner.LogRead(req.Type, page, limit)
	if err != nil {
		l.Logger.<PERSON>("error when read logs: %v", err)
		return nil, nil
	}

	logsByte, _ := json.<PERSON>(logs)

	resp = &types.LogsResponse{
		Logs:      string(logsByte),
		Total:     0, //0 xác định số lượng log
		TotalPage: 0, //0 xác định số lượng page
	}

	return
}
