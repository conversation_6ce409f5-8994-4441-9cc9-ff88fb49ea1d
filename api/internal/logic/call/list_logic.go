package call

import (
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/bquery"
	cast2 "bcare/common/cast"
	"bcare/ent"
	"bcare/ent/call"
	"context"
	"entgo.io/ent/dialect/sql"
	"github.com/samber/lo"
	"github.com/zeromicro/go-zero/core/logx"
)

type ListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewListLogic(ctx context.Context, svcCtx *svc.ServiceContext) ListLogic {
	return ListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ListLogic) List(req *types.CallListRequest) (resp *types.CallListResponse, err error) {
	limit, offset := bquery.Paging(req.PageSize, req.Page)
	order := bquery.Ordering(req.OrderBy)

	query := l.svcCtx.Ent.Call.Query().WithPerson().WithUser().Where(call.DestinationNotNil()).Order(order)

	filterMap := make(map[string]interface{})
	_ = cast2.AtomNoZero(&filterMap, req.Filter)

	for field, value := range filterMap {
		query = query.Where(sql.FieldEQ(field, value))
	}

	total, _ := query.Clone().Count(l.ctx)
	totalInbound, _ := query.Clone().Where(call.Direction("inbound")).Count(l.ctx)
	totalInboundMiss, _ := query.Clone().Where(call.Direction("inbound"), call.Tta(0), call.RecordingFileIsNil(), call.UserIDIsNil()).Count(l.ctx)
	totalOutbound, _ := query.Clone().Where(call.Direction("outbound")).Count(l.ctx)
	totalOutboundMiss, _ := query.Clone().Where(call.Direction("outbound"), call.Tta(0), call.RecordingFileIsNil()).Count(l.ctx)
	query = query.Limit(limit).Offset(offset)
	calls, e := query.All(l.ctx)
	if e != nil {
		err = types.ErrCallGetList.Wrap(e, e.Error())
		return
	}

	resp = new(types.CallListResponse)
	resp.Calls = lo.Map(calls, func(call *ent.Call, _ int) types.Call {
		c := types.Call{}
		_ = cast2.ConvertViaJson(&c, call)
		c.State = GetState(call)
		return c
	})
	resp.Total = total
	resp.TotalInbound = totalInbound
	resp.TotalInboundMiss = totalInboundMiss
	resp.TotalOutbound = totalOutbound
	resp.TotalInboundMiss = totalOutboundMiss

	return resp, err
}

func GetState(call *ent.Call) string {
	var state string

	if call.Direction == "outbound" && call.RecordingFile != "" {
		state = "outbound_answered" // khách nghe máy
	} else if call.Direction == "outbound" && call.Tta == 0 && call.RecordingFile == "" {
		state = "outbound_missed" // khách không nghe máy
	} else if call.Direction == "inbound" && call.Tta == 0 && call.RecordingFile != "" && call.UserID > 0 {
		state = "inbound_answered" // nhân viên nghe máy
	} else if call.Direction == "inbound" && call.Tta == 0 && call.RecordingFile == "" && call.UserID == 0 {
		state = "inbound_missed" // nhân viên bị nhỡ
	} else {
		state = "undefined"
	}

	return state
}
