package call

import (
	"bcare/api/model"
	"bcare/common/bconst"
	"bcare/common/bquery"
	"bcare/ent"
	"context"
	"errors"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewDeleteLogic(ctx context.Context, svcCtx *svc.ServiceContext) DeleteLogic {
	return DeleteLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DeleteLogic) Delete(req *types.CallDeleteRequest) (resp *types.Call, err error) {
	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		callRecord, err := tx.Call.Get(l.ctx, req.Id)

		if err != nil && errors.Is(err, model.ErrNotFound) {
			return err
		}

		if callRecord == nil {
			logx.Errorf("call not found")
			return types.ErrCallDelete
		}

		callRecord.Status = bconst.StatusDeleted

		err = tx.Call.DeleteOne(callRecord).Exec(l.ctx)
		if err != nil {
			return types.ErrCallDelete.Wrap(err)
		}
		return nil
	})

	return resp, err
}
