package call

import (
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/bquery"
	cast2 "bcare/common/cast"
	"bcare/ent"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateFeedbackLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateFeedbackLogic(ctx context.Context, svcCtx *svc.ServiceContext) UpdateFeedbackLogic {
	return UpdateFeedbackLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateFeedbackLogic) UpdateFeedback(req *types.CallUpdateFeedbackRequest) (resp *types.Call, err error) {
	callRecord := new(ent.Call)
	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		callRecord, err = tx.Call.Get(l.ctx, req.Id)

		if err != nil && ent.IsNotFound(err) {
			logx.Errorf("call not found")
			return types.ErrCallNotFound.Wrap(err, req.Id, err.Error())
		}

		if callRecord == nil {
			logx.Errorf("call not found")
			return types.ErrCallNotFound.Wrap(nil, req.Id)
		}

		callRecord.Feedback = req.Feedback
		callRecord.Rating = req.Rating
		callRecord.Kind = req.Kind
		callRecord, err = tx.Call.UpdateOneID(req.Id).SetCall(callRecord, req.Modified...).Save(l.ctx)
		if err != nil {
			return types.ErrCallUpdate.Wrap(err, "Error updating call feedback for ID %d", req.Id)
		}
		return nil
	})
	if err != nil {
		return nil, err
	}
	resp = new(types.Call)
	_ = cast2.ConvertViaJson(&resp, callRecord)

	return resp, nil

}
