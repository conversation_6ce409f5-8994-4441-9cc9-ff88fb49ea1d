package call

import (
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/api/model"
	"bcare/common/bconst"
	"bcare/common/bquery"
	cast2 "bcare/common/cast"
	"bcare/common/ctxdata"
	"bcare/ent"
	"bcare/ent/call"
	"bcare/ent/user"
	"context"
	"github.com/zeromicro/go-zero/core/logx"
	"time"
)

type AddLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAddLogic(ctx context.Context, svcCtx *svc.ServiceContext) AddLogic {
	return AddLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AddLogic) Add(req *types.CallAddRequest) (resp *types.Call, err error) {
	callRecord := new(ent.Call)

	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		callRecord.CallID = req.CallId
		callRecord.PersonID = req.PersonId
		callRecord.Direction = req.Direction
		if req.Direction == "inbound" {
			if req.Phone != "" {
				callRecord.Source = req.Phone
			}
			callRecord.Status = bconst.StatusNormal
		} else {
			if req.Phone != "" {
				callRecord.Destination = req.Phone
			}
			callRecord.Status = bconst.StatusDeActive
			callRecord.UserID = ctxdata.GetUidFromCtx(l.ctx)

			// Get current user with data
			currentUser, _ := l.svcCtx.Ent.User.Query().
				WithData().
				Where(user.ID(ctxdata.GetUidFromCtx(l.ctx))).
				First(l.ctx)

			if currentUser.Edges.Data != nil {
				lineCall, err := model.ParseLineCall(currentUser.Edges.Data)
				if err != nil {
					logx.Errorf("failed to parse line call: %v", err)
					// Continue execution even if parsing fails
				} else {
					callRecord.Source = lineCall.Line
				}
			}

		}
		callRecord.StartTime = time.Now()

		save, err := tx.Call.Create().SetCall(callRecord).Save(l.ctx)
		if err != nil {
			return types.ErrCallAdd.Wrap(err, "Error saving call record")
		}
		callRecord = save

		return nil
	})

	if err != nil {
		callRecord, err = l.svcCtx.Ent.Call.Query().Where(call.CallID(req.CallId)).First(l.ctx)
		if err != nil {
			return nil, err
		}
	}

	resp = new(types.Call)
	_ = cast2.ConvertViaJson(&resp, callRecord)
	return resp, nil
}
