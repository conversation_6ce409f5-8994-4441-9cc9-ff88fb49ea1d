package call

import (
	"bcare/common/bquery"
	cast2 "bcare/common/cast"
	"bcare/ent"
	"bcare/ent/call"
	"bcare/ent/person"
	"bcare/ent/schema"
	"bcare/ent/user"
	"context"
	"entgo.io/ent/dialect/sql"
	"github.com/jinzhu/copier"
	"strconv"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type QueryLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewQueryLogic(ctx context.Context, svcCtx *svc.ServiceContext) QueryLogic {
	return QueryLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *QueryLogic) Query(req *types.CallDynamicQuery) (res *types.DynamicQueryResponse, err error) {
	dQuery := &bquery.DynamicQuery{}
	err = copier.Copy(dQuery, req)
	if err != nil {
		logx.WithContext(l.ctx).Errorf("unable to copy request: %v", err)
		return nil, err
	}

	query := l.svcCtx.Ent.Call.Query().Modify(bquery.ApplyDynamicQuery(dQuery)).
		WithPerson().
		WithUser().
		Where(call.DestinationNotNil())

	if req.Creator != "" {
		id, e := strconv.Atoi(req.Creator)
		if e != nil {
			id = 0
		}
		query = query.Where(
			call.Or(
				call.HasUserWith(user.Or(user.NameContainsFold(req.Creator), user.ID(id))),
				call.SourceContains(req.Creator),
				call.DestinationContains(req.Creator),
			),
		)
	}

	if req.Person != "" {
		query = query.Where(
			call.Or(
				call.HasPersonWith(person.Or(
					func(s *sql.Selector) {
						s.Where(sql.P(func(b *sql.Builder) {
							b.WriteString("f_unaccent(full_name) LIKE '%' || f_unaccent(")
							b.Arg(req.Person)
							b.WriteString(") || '%'")
						}))
					},
					person.PhoneContains(req.Person))),
				call.SourceContains(req.Person),
				call.DestinationContains(req.Person),
			),
		)
	}

	switch req.State {
	case "inbound":
		query = query.Where(call.Direction("inbound"))
	case "outbound":
		query = query.Where(call.Direction("outbound"))
	case "miss_inbound":
		query = query.Where(call.Direction("inbound"), call.Tta(0), call.RecordingFileIsNil(), call.UserIDIsNil())
	case "miss_outbound":
		query = query.Where(call.Direction("outbound"), call.Tta(0), call.RecordingFileIsNil())
	}

	result := map[string]any{}
	aliases := make(map[string]bool)
	for _, agg := range req.Aggregations {
		if agg.Alias != "" {
			aliases[agg.Alias] = true
		}
	}

	skipSoftDelete := schema.SkipSoftDelete(l.ctx)

	if req.Limit <= 1 {
		r, err := query.First(skipSoftDelete)
		if err != nil && !ent.IsNotFound(err) {
			logx.WithContext(l.ctx).Errorf("unable to fetch Task record: %v", err)
			return nil, err
		}

		if r != nil {
			for alias := range aliases {
				result[alias], _ = r.Value(alias)
			}
		}
	} else {
		rows, err := query.All(skipSoftDelete)
		if err != nil {
			logx.WithContext(l.ctx).Errorf("unable to fetch Task records: %v", err)
			return nil, err
		}

		resultList := make([]map[string]any, len(rows))
		for i, r := range rows {
			rowResult := make(map[string]any)
			c := types.CallResponse{}
			_ = cast2.ConvertViaJson(&c, r)
			c.State = GetState(r)
			rowResult["record"] = cast2.Atom(&rowResult, c)
			for alias := range aliases {
				rowResult[alias], _ = r.Value(alias)
			}
			resultList[i] = rowResult
		}
		result["rows"] = resultList
	}

	res = new(types.DynamicQueryResponse)
	res.Result = result

	return
}
