package call

import (
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/api/model"
	"bcare/common/bconst"
	"bcare/common/bquery"
	cast2 "bcare/common/cast"
	"bcare/common/ctxdata"
	keylock "bcare/common/lock"
	"bcare/ent"
	"bcare/ent/call"
	"bcare/ent/schema"
	"bcare/ent/user"
	"context"
	"github.com/zeromicro/go-zero/core/logx"
	"time"
)

type AcceptLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAcceptLogic(ctx context.Context, svcCtx *svc.ServiceContext) AcceptLogic {
	return AcceptLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AcceptLogic) Accept(req *types.CallUpdateRequest) (resp *types.Call, err error) {

	callRecord := new(ent.Call)
	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		skipSoftDelete := schema.SkipSoftDelete(l.ctx)
		callRecord, err = tx.Call.Query().Where(call.ID(req.Id)).First(skipSoftDelete)
		if err != nil && !ent.IsNotFound(err) {
			return types.ErrCallUpdate.Wrap(err, "Error retrieving call record")
		}
		unlock, err := keylock.Global.Lock(1*time.Second, callRecord.ID)
		if err != nil {
			return types.ErrCallUpdate.Wrap(err, "failed to acquire lock")
		}
		defer unlock()

		if callRecord == nil {
			logx.Errorf("call not found")
			return types.ErrCallNotFound.Wrap(nil, req.Id)
		}

		if callRecord.Status == bconst.StatusDeleted && !callRecord.DeletedAt.IsZero() {
			callRecord.Status = bconst.StatusNormal
		}

		callRecord.UserID = ctxdata.GetUidFromCtx(l.ctx)
		// Get current user with data
		currentUser, _ := l.svcCtx.Ent.User.Query().
			WithData().
			Where(user.ID(ctxdata.GetUidFromCtx(l.ctx))).
			First(l.ctx)

		if currentUser.Edges.Data != nil {
			lineCall, err := model.ParseLineCall(currentUser.Edges.Data)
			if err != nil {
				logx.Errorf("failed to parse line call: %v", err)
				// Continue execution even if parsing fails
			} else {
				callRecord.Destination = lineCall.Line
			}
		}

		callRecord, err = tx.Call.UpdateOneID(req.Id).SetCall(callRecord).ClearDeletedAt().Save(skipSoftDelete)
		if err != nil {
			return types.ErrCallUpdate.Wrap(err, "Error updating call record with ID %d", req.Id)
		}
		return nil
	})
	if err != nil {
		return nil, err
	}
	resp = new(types.Call)
	_ = cast2.ConvertViaJson(&resp, callRecord)

	return resp, nil
}
