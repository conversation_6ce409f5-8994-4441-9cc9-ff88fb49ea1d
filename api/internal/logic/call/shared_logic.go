package call

import (
	"bcare/api/internal/svc"
	"bcare/ent"
	"bcare/ent/setting"
	"bytes"
	"context"
	"encoding/json"
	"io"
	"net/http"
	"strconv"
	"time"

	"github.com/zeromicro/go-zero/core/logx"
)

type Response struct {
	Response []struct {
		Cdr struct {
			Version       string  `json:"@version"`
			Pdd           string  `json:"pdd"`
			StartEpoch    int     `json:"start_epoch"`
			EndEpoch      int     `json:"end_epoch"`
			Mos           float64 `json:"mos"`
			UUID          string  `json:"uuid"`
			Direction     string  `json:"direction"`
			CidName       string  `json:"cid_name"`
			Destination   string  `json:"destination"`
			Source        string  `json:"source"`
			CallID        string  `json:"call_id"`
			DomainUUID    string  `json:"domain_uuid"`
			Duration      string  `json:"duration"`
			Recording     string  `json:"recording"`
			Tta           string  `json:"tta"`
			Timestamp     string  `json:"@timestamp"`
			Domain        string  `json:"domain"`
			Start         string  `json:"start"`
			Status        string  `json:"status"`
			RecordingFile string  `json:"recording_file"`
		} `json:"Cdr"`
	} `json:"response"`
}

type SharedLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewSharedLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SharedLogic {
	return &SharedLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// get detail call from api
func (l *SharedLogic) getCallDetail(callId string) (response *Response, err error) {
	s, err := l.svcCtx.Ent.Setting.Query().Where(setting.Category("call")).First(l.ctx)
	if err != nil {
		return nil, err
	}

	apiKey := s.Value["api_key"].(string)
	apiSecret := s.Value["api_secret"].(string)
	apiUrl := s.Value["api_url"].(string)

	type Request struct {
		Submission struct {
			ApiKey    string `json:"api_key"`
			ApiSecret string `json:"api_secret"`
			CallId    string `json:"call_id"`
		} `json:"submission"`
	}

	var rq Request
	rq.Submission.CallId = callId
	rq.Submission.ApiKey = apiKey
	rq.Submission.ApiSecret = apiSecret

	requestJSON, _ := json.Marshal(rq)

	req, err := http.NewRequest("POST", apiUrl, bytes.NewBuffer(requestJSON))
	if err != nil {
		return nil, err
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	response = new(Response)
	err = json.Unmarshal(body, response)
	if err != nil {
		return nil, err
	}
	return response, nil
}

// parse result to ent.Call
func (l *SharedLogic) parseResultToCall(callRecord *ent.Call, result *Response) *ent.Call {
	cdr := result.Response[0].Cdr

	callRecord.CallID = cdr.CallID
	callRecord.Direction = cdr.Direction
	callRecord.Source = cdr.Source
	callRecord.Destination = cdr.Destination
	callRecord.RecordingFile = cdr.RecordingFile
	callRecord.Pdd, _ = strconv.Atoi(cdr.Pdd)
	callRecord.Tta, _ = strconv.Atoi(cdr.Tta)
	callRecord.CallStatus = cdr.Status
	callRecord.Score = float32(cdr.Mos)
	callRecord.StartTime, _ = time.Parse(time.RFC3339, cdr.Timestamp)
	t, _ := time.Parse("15:04:05", cdr.Duration)
	seconds := t.Hour()*3600 + t.Minute()*60 + t.Second()
	callRecord.Duration = seconds

	return callRecord
}
