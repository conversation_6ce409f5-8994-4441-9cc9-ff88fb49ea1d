package call

import (
	"bcare/api/internal/svc"
	"bcare/api/internal/task"
	"bcare/api/internal/task/tasks"
	"bcare/api/internal/types"
	"bcare/common/bconst"
	"bcare/common/bquery"
	cast2 "bcare/common/cast"
	keylock "bcare/common/lock"
	"bcare/ent"
	"bcare/ent/call"
	"bcare/ent/schema"
	"context"
	"time"

	"github.com/hibiken/asynq"

	"github.com/zeromicro/go-zero/core/logx"
)

type EndLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewEndLogic(ctx context.Context, svcCtx *svc.ServiceContext) EndLogic {
	return EndLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *EndLogic) End(req *types.CallEndRequest) (resp *types.Call, err error) {
	callRecord := new(ent.Call)
	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		skipSoftDelete := schema.SkipSoftDelete(l.ctx)
		callRecord, err = tx.Call.Query().Where(call.ID(req.Id)).First(skipSoftDelete)
		if err != nil {
			logx.WithContext(l.ctx).Errorf("callRecord not found by %v ID: %d", err, req.Id)
			return types.ErrCallNotFound.Wrap(err, req.Id, "")
		}

		unlock, err := keylock.Global.Lock(1*time.Second, callRecord.ID)
		if err != nil {
			return types.ErrCallUpdate.Wrap(err, "failed to acquire lock")
		}
		defer unlock()

		// Update waiting time for the remaining call
		callRecord, err = tx.Call.UpdateOneID(req.Id).
			SetWattingTime(int(time.Now().Unix() - callRecord.CreatedAt.Unix())).
			Save(skipSoftDelete)

		if callRecord.Status != bconst.StatusDeleted {
			logx.WithContext(l.ctx).Infof("Creating CallUpdateTask with callID: %s", callRecord.CallID)
			t, err := tasks.NewCallUpdateTask(callRecord.CallID)
			if err != nil {
				logx.WithContext(l.ctx).Errorf("could not create task: %v", err)
			}

			logx.WithContext(l.ctx).Infof("Enqueuing task: %s", t.Type())
			_, err = task.Client.Enqueue(t, asynq.ProcessIn(time.Duration(10)*time.Minute), asynq.Queue(task.QueueName))
			if err != nil {
				logx.WithContext(l.ctx).Errorf("could not enqueue task: %v", err)
			} else {
				logx.WithContext(l.ctx).Infof("Successfully enqueued task: %s", t.Type())
			}
		}

		if callRecord.Direction == "inbound" {
			calls, err := l.svcCtx.Ent.Call.Query().
				Where(
					call.And(
						call.Source(callRecord.Source),
						call.CreatedAtGTE(time.Now().Add(-1*time.Minute)),
						call.CreatedAtLTE(time.Now().Add(1*time.Minute)),
					),
				).
				Order(ent.Asc(call.FieldID)). // Order by ID ascending
				All(l.ctx)
			if err != nil {
				return nil
			}

			// Find if there's any call with valid user_id
			var validUserCall *ent.Call
			for _, c := range calls {
				if c.UserID > 0 {
					validUserCall = c
					break
				}
			}

			// Delete calls based on conditions
			for _, c := range calls {
				if validUserCall != nil {
					// If we found a call with valid user_id, delete all other calls
					if c.ID != validUserCall.ID {
						_ = tx.Call.DeleteOne(c).Exec(l.ctx)
						logx.WithContext(l.ctx).Infof("Deleted duplicate call ID: %d", c.ID)
					}
				} else {
					// If all calls have no valid user_id, keep only the one with smallest ID
					if c.ID != calls[0].ID {
						_ = tx.Call.DeleteOne(c).Exec(l.ctx)
						logx.WithContext(l.ctx).Infof("Deleted duplicate call ID: %d", c.ID)
					}
				}
			}
		}

		return nil
	})

	if err != nil {
		return nil, err
	}
	resp = new(types.Call)
	_ = cast2.ConvertViaJson(&resp, callRecord)

	return resp, nil
}
