package call

import (
	"bcare/ent/call"
	"context"
	"github.com/jinzhu/copier"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetLogic(ctx context.Context, svcCtx *svc.ServiceContext) GetLogic {
	return GetLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetLogic) Get(req *types.CallGetRequest) (resp *types.Call, err error) {
	callRecord, err := l.svcCtx.Ent.Call.Query().Where(call.ID(req.Id)).Only(l.ctx)
	if err != nil {
		return nil, err
	}

	resp = new(types.Call)
	_ = copier.Copy(&resp, callR<PERSON>ord)

	return resp, nil
}
