package operation

import (
	"bcare/common/berr"
	"context"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/cast"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetLogic {
	return &GetLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetLogic) Get(req *types.OperationGetRequest) (resp *types.OperationResponse, err error) {
	operationOutput, err := l.svcCtx.OperationModel.GetOperation(l.ctx, req.Id)
	if err != nil {
		return nil, err
	}

	if !l.svcCtx.Auth.Operation.CanGet(l.ctx, operationOutput) { // Placeholder for authorization
		return nil, berr.ErrPermissionDenied.Wrap(nil)
	}

	resp = new(types.OperationResponse)
	err = cast.ConvertViaJson(resp, operationOutput)
	if err != nil {
		return nil, err // Simplified error handling
	}

	return resp, nil
}
