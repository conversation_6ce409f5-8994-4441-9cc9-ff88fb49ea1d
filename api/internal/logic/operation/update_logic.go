package operation

import (
	"bcare/api/internal/dto"
	"bcare/api/internal/svc"
	"bcare/api/internal/types" // Assuming berr.ErrPermissionDenied exists
	"bcare/common/berr"
	"bcare/common/cast"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateLogic {
	return &UpdateLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateLogic) Update(req *types.OperationUpdateRequest) (resp *types.OperationResponse, err error) {
	existingOperation, err := l.svcCtx.OperationModel.GetOperation(l.ctx, req.Id)
	if err != nil {
		return nil, err
	}

	if !l.svcCtx.Auth.Operation.CanUpdate(l.ctx, existingOperation) { // Placeholder for authorization
		return nil, berr.ErrPermissionDenied.Wrap(nil)
	}

	input := new(dto.UpdateOperationInput)
	err = cast.ReqToModelInput(input, req)
	if err != nil {
		return nil, err // Simplified error handling
	}

	operationOutput, err := l.svcCtx.OperationModel.UpdateOperation(l.ctx, input)
	if err != nil {
		return nil, err
	}

	resp = new(types.OperationResponse)
	err = cast.ConvertViaJson(resp, operationOutput)
	if err != nil {
		return nil, err // Simplified error handling
	}

	return resp, nil
}
