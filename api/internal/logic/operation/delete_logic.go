package operation

import (
	"bcare/common/berr"
	"context"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	// Assuming berr.ErrPermissionDenied exists
	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewDeleteLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteLogic {
	return &DeleteLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DeleteLogic) Delete(req *types.OperationDeleteRequest) (err error) {
	existingOperation, err := l.svcCtx.OperationModel.GetOperation(l.ctx, req.Id) // Fetch for auth check if needed
	if err != nil {
		return err
	}

	if !l.svcCtx.Auth.Operation.CanDelete(l.ctx, existingOperation) { // Placeholder for authorization
		return berr.ErrPermissionDenied.Wrap(nil)
	}

	err = l.svcCtx.OperationModel.DeleteOperation(l.ctx, req.Id)
	if err != nil {
		return err
	}

	return nil
}
