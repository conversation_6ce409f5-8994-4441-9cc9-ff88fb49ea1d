package operation

import (
	"bcare/api/internal/dto"
	"bcare/common/berr"
	"bcare/common/cast"
	"context"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type ListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ListLogic {
	return &ListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ListLogic) List(req *types.OperationListRequest) (resp *types.OperationListResponse, err error) {
	if !l.svcCtx.Auth.Operation.CanList(l.ctx) {
		return nil, berr.ErrPermissionDenied.Wrap(nil)
	}

	input := new(dto.ListOperationsInput)
	err = cast.ReqToModelInput(input, req)
	if err != nil {
		return nil, err // Simplified error handling
	}

	listOutput, err := l.svcCtx.OperationModel.ListOperations(l.ctx, input)
	if err != nil {
		return nil, err
	}

	resp = &types.OperationListResponse{
		Operations: make([]types.OperationResponse, 0, len(listOutput.Operations)),
		Total:      listOutput.Total,
	}

	err = cast.ConvertViaJson(&resp.Operations, listOutput.Operations)
	if err != nil {
		return nil, err
	}

	return resp, nil
}
