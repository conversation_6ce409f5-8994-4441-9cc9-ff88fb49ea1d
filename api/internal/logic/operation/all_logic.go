package operation

import (
	cast2 "bcare/common/cast"
	"bcare/ent"
	"bcare/ent/operation"
	"context"
	"github.com/samber/lo"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type AllLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAllLogic(ctx context.Context, svcCtx *svc.ServiceContext) AllLogic {
	return AllLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AllLogic) All() (resp *types.OperationAllResponse, err error) {
	// todo: add your logic here and delete this line
	operations, e := l.svcCtx.Ent.Operation.Query().WithProductOperation().WithOperationMaterials().Order(operation.ByName()).All(l.ctx)

	if e != nil {
		err = types.ErrOperationAll.Wrap(e, "Can't get all operations")
		return nil, err
	}

	resp = new(types.OperationAllResponse)
	resp.Operations = lo.Map(operations, func(op *ent.Operation, _ int) types.OperationResponse {
		o := types.OperationResponse{}
		_ = cast2.ConvertViaJson(&o, op)
		return o
	})

	return
}
