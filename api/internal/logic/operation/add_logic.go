package operation

import (
	"bcare/api/internal/dto"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/berr"
	"bcare/common/cast"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type AddLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAddLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AddLogic {
	return &AddLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AddLogic) Add(req *types.OperationAddRequest) (resp *types.OperationResponse, err error) {
	if !l.svcCtx.Auth.Operation.CanAdd(l.ctx) { // Placeholder for authorization
		return nil, berr.ErrPermissionDenied.Wrap(nil)
	}

	input := new(dto.CreateOperationInput)
	err = cast.ReqToModelInput(input, req)
	if err != nil {
		return nil, err // Simplified error handling
	}

	operationOutput, err := l.svcCtx.OperationModel.CreateOperation(l.ctx, input)
	if err != nil {
		return nil, err
	}

	resp = new(types.OperationResponse)
	err = cast.ConvertViaJson(resp, operationOutput)
	if err != nil {
		return nil, err // Simplified error handling
	}

	return resp, nil
}
