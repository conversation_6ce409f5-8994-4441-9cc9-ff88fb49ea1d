package installment

import (
	"bcare/api/internal/auth"
	"bcare/common/bconst"
	"bcare/common/bquery"
	cast2 "bcare/common/cast"
	"bcare/ent"
	"bcare/ent/deal"
	"bcare/ent/installment"
	"bcare/ent/installmentplan"
	"context"
	"github.com/samber/lo"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetRefundLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetRefundLogic(ctx context.Context, svcCtx *svc.ServiceContext) GetRefundLogic {
	return GetRefundLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetRefundLogic) GetRefund(req *types.InstallmentGetPartiallyPaidRequest) (resp *types.InstallmentListResponse, err error) {
	if !l.svcCtx.Auth.Installment.CanList(l.ctx, nil) {
		logx.WithContext(l.ctx).Infof("Permission denied")
		return nil, auth.ErrInstallmentListDenied
	}

	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		// Build query with necessary relations
		query := tx.Installment.Query().WithCreator().WithAllocations().WithPlan().Order(ent.Asc(installment.FieldInstallmentNumber))

		query = query.Where(
			installment.KindEQ(installment.KindRefundPayment),
			installment.HasPlanWith(installmentplan.PersonID(req.PersonId),
				installmentplan.StateNEQ(installmentplan.StateCancelled),
				installmentplan.HasDealWith(deal.StatusNEQ(bconst.StatusDeleted),
					deal.DeletedAtIsNil(),
					deal.StateNEQ(deal.StateLost)),
			),
		)

		// Get installment that have allocations sum < amount
		installments, e := query.All(l.ctx)

		if e != nil {
			return types.ErrInstallmentGetList.Wrap(e)
		}

		// First, filter the valid installments and calculate paid amounts
		installmentPaidAmounts := make(map[int]float64) // Store paid amounts by installment ID
		validInstallments := lo.Filter(installments, func(i *ent.Installment, _ int) bool {
			return i.Edges.Allocations == nil
		})

		// Then map the filtered items to response
		resp = new(types.InstallmentListResponse)
		resp.Installments = lo.Map(validInstallments, func(i *ent.Installment, _ int) types.InstallmentResponse {
			var installmentResp types.InstallmentResponse
			_ = cast2.EntToResp(&installmentResp, i)

			// Add paid amount to response
			installmentResp.PaidAmount = installmentPaidAmounts[i.ID]
			// You can also calculate remaining amount if needed

			return installmentResp
		})

		return nil
	})

	return resp, err
}
