package installment

import (
	"bcare/api/internal/auth"
	"bcare/common/bquery"
	cast2 "bcare/common/cast"
	"bcare/common/ctxdata"
	"bcare/ent"
	"context"
	"github.com/jinzhu/copier"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateLogic(ctx context.Context, svcCtx *svc.ServiceContext) UpdateLogic {
	return UpdateLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateLogic) Update(req *types.InstallmentUpdateRequest) (resp *types.Installment, err error) {
	installmentRecord := new(ent.Installment)
	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		installmentRecord, err = tx.Installment.Get(l.ctx, req.Id)

		if err != nil && ent.IsNotFound(err) {
			return types.ErrInstallmentNotFound.Wrap(err)
		}

		if installmentRecord == nil {
			logx.Errorf("Installment not found")
			return types.ErrInstallmentNotFound
		}

		//kiểm tra xem có quyền ko
		if !l.svcCtx.Auth.Installment.CanUpdate(l.ctx, nil) {
			logx.WithContext(l.ctx).Infof("Bạn ko có quyền")
			return auth.ErrInstallmentUpdateDenied
		}

		err = copier.CopyWithOption(&installmentRecord, req, cast2.PayloadToEnt)

		if err != nil {
			logx.Errorf("copier copy task failed, err:%v", err)
			return types.ErrInstallmentUpdate.Wrap(err, "copier copy task failed")
		}
		installmentRecord.UserID = ctxdata.GetUidFromCtx(l.ctx)
		installmentRecord, err = tx.Installment.UpdateOneID(req.Id).SetInstallment(installmentRecord, req.Modified...).Save(l.ctx)
		if err != nil {
			return types.ErrInstallmentUpdate.Wrap(err, err.Error())
		}
		return nil
	})
	if err != nil {
		return nil, err
	}
	resp = new(types.Installment)
	_ = cast2.ConvertViaJson(&resp, installmentRecord)

	return resp, nil
}
