package installment

import (
	"bcare/api/internal/auth"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/api/model"
	"bcare/common/bconst"
	"bcare/common/bquery"
	cast2 "bcare/common/cast"
	"bcare/common/ctxdata"
	"bcare/ent"
	"bcare/ent/installment"
	"bcare/ent/issue"
	"context"
	"fmt"
	"github.com/jinzhu/copier"
	"github.com/zeromicro/go-zero/core/logx"
)

type AddLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAddLogic(ctx context.Context, svcCtx *svc.ServiceContext) AddLogic {
	return AddLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AddLogic) Add(req *types.InstallmentAddRequest) (resp *types.Installment, err error) {
	installmentRecord := new(ent.Installment)
	planRecord := new(ent.InstallmentPlan)
	if req.PlanId > 0 {
		planRecord, _ = l.svcCtx.Ent.InstallmentPlan.Get(l.ctx, req.PlanId)
	}
	err = copier.Copy(&installmentRecord, req)
	if err != nil {
		logx.WithContext(l.ctx).Errorf("cannot copy request: %v", err)
		return nil, types.ErrInstallmentAdd.Wrap(err, "cannot copy request")
	}

	//kiểm tra xem có quyền ko
	if !l.svcCtx.Auth.Installment.CanAdd(l.ctx, nil) {
		logx.WithContext(l.ctx).Infof("Bạn ko có quyền")
		return nil, auth.ErrInstallmentAddDenied
	}

	installmentRecord.Status = bconst.StatusNormal
	if installmentRecord.Kind == installment.KindDownPayment {
		installmentRecord.Name = fmt.Sprintf("Cọc %s", planRecord.Name)
	} else if installmentRecord.Kind == installment.KindSequencePayment {
		installmentRecord.Name = fmt.Sprintf("Thu phí %s", planRecord.Name)
	} else {
		installmentRecord.Name = fmt.Sprintf("Hoàn phí %s", planRecord.Name)
	}
	installmentRecord.UserID = ctxdata.GetUidFromCtx(l.ctx)
	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		installmentRecord, err = tx.Installment.Create().SetInstallment(installmentRecord).Save(l.ctx)
		if err != nil {
			return types.ErrInstallmentAdd.Wrap(err, err.Error())
		}

		if installmentRecord.Kind == installment.KindRefundPayment {
			_ = model.CreateIssue(l.ctx, l.svcCtx.Ent, planRecord.PersonID, issue.TypeRefund.String(), fmt.Sprintf("Hoàn phí %s", planRecord.Name), "Hoàn phí")
		}
		return nil
	})

	resp = new(types.Installment)
	_ = cast2.EntToResp(&resp, installmentRecord)
	return resp, nil

}
