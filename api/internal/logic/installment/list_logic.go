package installment

import (
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/bquery"
	cast2 "bcare/common/cast"
	"bcare/ent"
	"context"
	"entgo.io/ent/dialect/sql"
	"github.com/samber/lo"
	"github.com/zeromicro/go-zero/core/logx"
)

type ListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewListLogic(ctx context.Context, svcCtx *svc.ServiceContext) ListLogic {
	return ListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ListLogic) List(req *types.InstallmentListRequest) (resp *types.InstallmentListResponse, err error) {
	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		limit, offset := bquery.Paging(req.PageSize, req.Page)
		order := bquery.Ordering(req.OrderBy)

		query := tx.Installment.Query().WithCreator().WithAllocations().WithPlan().Order(order)

		filterMap := make(map[string]interface{})
		_ = cast2.AtomNoZero(&filterMap, req.Filter)

		for field, value := range filterMap {
			query = query.Where(sql.FieldEQ(field, value))
		}

		total, _ := query.Count(l.ctx)
		installments, e := query.Limit(limit).Offset(offset).All(l.ctx) // Giả sử limit là 10 và offset là 5

		if e != nil {
			err = types.ErrInstallmentGetList.Wrap(e, e.Error())
			return err
		}

		resp = new(types.InstallmentListResponse)
		resp.Installments = lo.Map(installments, func(installment *ent.Installment, _ int) types.InstallmentResponse {
			i := types.InstallmentResponse{}
			_ = cast2.ConvertViaJson(&i, installment)
			return i
		})
		resp.Total = total

		return nil
	})

	return resp, err
}
