package installment

import (
	"bcare/api/internal/auth"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/api/model"
	"bcare/common/bconst"
	"bcare/common/bquery"
	"bcare/ent"
	"bcare/ent/installment"
	"bcare/ent/issue"
	"context"
	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewDeleteLogic(ctx context.Context, svcCtx *svc.ServiceContext) DeleteLogic {
	return DeleteLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DeleteLogic) Delete(req *types.InstallmentDeleteRequest) (resp *types.Installment, err error) {
	installmentRecord := new(ent.Installment)
	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		installmentRecord, err := tx.Installment.Get(l.ctx, req.Id)

		//kiểm tra xem có quyền ko
		if !l.svcCtx.Auth.Installment.CanDelete(l.ctx, nil) {
			logx.WithContext(l.ctx).Infof("Bạn ko có quyền")
			return auth.ErrInstallmentDeleteDenied
		}

		if err != nil && ent.IsNotFound(err) {
			return err
		}

		if installmentRecord == nil {
			logx.Errorf("installment not found")
			return types.ErrInstallmentNotFound
		}

		installmentRecord.Status = bconst.StatusDeleted

		err = tx.Installment.DeleteOne(installmentRecord).Exec(l.ctx)
		if err != nil {
			return types.ErrInstallmentUpdate.Wrap(err, err.Error())
		}

		return nil
	})

	if installmentRecord.Kind == installment.KindRefundPayment {
		planRecord, _ := l.svcCtx.Ent.InstallmentPlan.Get(l.ctx, installmentRecord.PlanID)
		_ = model.DeleteIssue(l.ctx, l.svcCtx.Ent, planRecord.PersonID, issue.TypeRefund.String())
	}

	return resp, err
}
