package location

import (
	"bcare/common/berr"
	"context"
	"github.com/jinzhu/copier"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

var (
	ErrLocationGet = berr.NewErrCodeMsg(9001, "Can not get location")
)

func NewGetLogic(ctx context.Context, svcCtx *svc.ServiceContext) GetLogic {
	return GetLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetLogic) Get(req *types.LocationRequest) (resp *types.LocationResponse, err error) {
	provinces, e := l.svcCtx.Ent.LocalProvince.Query().All(l.ctx)
	if e != nil {
		err = ErrLocationGet.Wrap(e)
	}
	districts, e := l.svcCtx.Ent.LocalDistrict.Query().All(l.ctx)
	if e != nil {
		err = ErrLocationGet.Wrap(e)
	}

	wards, e := l.svcCtx.Ent.LocalWard.Query().All(l.ctx)
	if e != nil {
		err = ErrLocationGet.Wrap(e)
	}

	resp = new(types.LocationResponse)
	_ = copier.Copy(&resp.Province, provinces)
	_ = copier.Copy(&resp.District, districts)
	_ = copier.Copy(&resp.Ward, wards)
	return resp, err
}
