package queryv2

import (
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/bquery"
	bqueryv2 "bcare/common/bquery/v2"
	"context"
	"database/sql"
	"encoding/json"
	"fmt"

	"github.com/stephenafamo/scan"
	"github.com/zeromicro/go-zero/core/logx"
)

type QueryV2Logic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewQueryV2Logic(ctx context.Context, svcCtx *svc.ServiceContext) QueryV2Logic {
	return QueryV2Logic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *QueryV2Logic) QueryV2(req *bqueryv2.DynamicQuery) (resp *types.DynamicQueryV2Response, err error) {
	// Build the query using bob.go implementation
	bobQuery := req.BuildQuery()

	// Get the SQL string and args for logging
	sqlString, args, err := bquery.GetQueryString(l.ctx, bobQuery)
	if err != nil {
		return nil, err
	}
	logx.WithContext(l.ctx).Infof("Executing SQL: %s with args: %v", sqlString, args)

	// Trực tiếp sử dụng database/sql để truy vấn
	db := l.svcCtx.Bob
	rows, err := db.QueryContext(l.ctx, sqlString, args...)
	if err != nil {
		return nil, fmt.Errorf("failed to execute query: %w", err)
	}
	defer rows.Close()

	// Process the rows using scan.Rows instead of sql.Rows
	resultMap, err := l.processScanRows(rows)
	if err != nil {
		return nil, err
	}

	// Return the query response
	resp = &types.DynamicQueryV2Response{
		Result: resultMap,
	}

	return resp, nil
}

// Hàm mới để xử lý scan.Rows thay vì sql.Rows
func (l *QueryV2Logic) processScanRows(rows scan.Rows) (map[string]interface{}, error) {
	var resultList []map[string]interface{}

	// Lấy tên các cột
	columnNames, err := rows.Columns()
	if err != nil {
		return nil, fmt.Errorf("failed to get column names: %w", err)
	}

	// Duyệt qua từng dòng kết quả
	for rows.Next() {
		// Tạo một map để lưu dữ liệu của dòng hiện tại
		rowMap := make(map[string]interface{})

		// Tạo slice để lưu trữ các giá trị của dòng hiện tại
		values := make([]interface{}, len(columnNames))
		valuePtrs := make([]interface{}, len(columnNames))

		for i := range values {
			valuePtrs[i] = &values[i]
		}

		// Scan dòng hiện tại vào các biến
		if err := rows.Scan(valuePtrs...); err != nil {
			return nil, fmt.Errorf("failed to scan row: %w", err)
		}

		// Gán giá trị vào map với tên cột tương ứng
		for i, colName := range columnNames {
			val := values[i]

			// Xử lý giá trị NULL
			if val == nil {
				rowMap[colName] = nil
				continue
			}

			// Xử lý các kiểu dữ liệu khác nhau
			switch v := val.(type) {
			case []byte:
				// Thử phân tích như JSON trước
				var jsonData interface{}
				if json.Unmarshal(v, &jsonData) == nil {
					rowMap[colName] = jsonData
				} else {
					// Nếu không, sử dụng như string
					rowMap[colName] = string(v)
				}
			default:
				rowMap[colName] = val
			}
		}

		resultList = append(resultList, rowMap)
	}

	// Kiểm tra lỗi sau khi duyệt qua các dòng
	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating through rows: %w", err)
	}

	// Trả về kết quả
	return map[string]interface{}{
		"data":  resultList,
		"total": len(resultList),
	}, nil
}

// Giữ hàm cũ để tham khảo hoặc sử dụng với sql.Rows nếu cần
func (l *QueryV2Logic) processRows(rows *sql.Rows) (map[string]interface{}, error) {
	// Get column names
	columns, err := rows.Columns()
	if err != nil {
		return nil, fmt.Errorf("failed to get column names: %w", err)
	}

	// Prepare data structure to receive the results
	resultMap := make(map[string]interface{})

	// Prepare a list to hold all row data
	var resultList []map[string]interface{}

	// Process each row
	for rows.Next() {
		// Create a slice of interface{} to store the row values
		values := make([]interface{}, len(columns))
		valuePtrs := make([]interface{}, len(columns))

		// Initialize pointers to each column value
		for i := range columns {
			valuePtrs[i] = &values[i]
		}

		// Scan the row into the slice of interface{}
		if err := rows.Scan(valuePtrs...); err != nil {
			return nil, fmt.Errorf("failed to scan row: %w", err)
		}

		// Create a map to hold the row data
		rowData := make(map[string]interface{})
		for i, colName := range columns {
			val := values[i]

			// Handle SQL NULL values
			if val == nil {
				rowData[colName] = nil
			} else {
				// Try to convert the value to a specific type
				switch v := val.(type) {
				case []byte:
					// Try to parse as JSON first
					var jsonData interface{}
					if json.Unmarshal(v, &jsonData) == nil {
						rowData[colName] = jsonData
					} else {
						// Otherwise, use as string
						rowData[colName] = string(v)
					}
				default:
					rowData[colName] = val
				}
			}
		}

		// Add the row data to the list
		resultList = append(resultList, rowData)
	}

	// Check for errors after iterating through rows
	if err = rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating through rows: %w", err)
	}

	// Add the result list to the result map
	resultMap["data"] = resultList
	resultMap["total"] = len(resultList)

	return resultMap, nil
}
