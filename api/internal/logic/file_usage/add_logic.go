package file_usage

import (
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/bquery"
	"bcare/ent"
	"bcare/ent/track"
	"context"
	"entgo.io/ent/dialect/sql"
	"github.com/jinzhu/copier"
	"github.com/zeromicro/go-zero/core/logx"
)

type AddLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAddLogic(ctx context.Context, svcCtx *svc.ServiceContext) AddLogic {
	return AddLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AddLogic) Add(req *types.FileUsageAddRequest) (resp *types.FileUsageResponse, err error) {
	fileUsageRecord := new(ent.FileUsage)
	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		err = copier.Copy(&fileUsageRecord, req)
		if err != nil {
			logx.WithContext(l.ctx).Errorf("cannot copy request: %v", err)
			return types.ErrFileUsageAdd.Wrap(err)
		}
		query := tx.Track.Query().Order(track.ByCreatedAt(sql.OrderDesc()))
		if fileUsageRecord.EntityType == "person" {
			query = query.Where(track.PersonID(fileUsageRecord.EntityID))
		}
		if req.TrackId == 0 {
			activeTrack, _ := query.First(l.ctx)
			if activeTrack != nil {
				fileUsageRecord.TrackID = activeTrack.ID
			}
		}

		fileUsageRecord, err = tx.FileUsage.Create().SetFileUsage(fileUsageRecord).Save(l.ctx)
		if err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		return nil, types.ErrFileUsageAdd.Wrap(err)
	}

	resp = new(types.FileUsageResponse)
	_ = copier.Copy(&resp, fileUsageRecord)
	return resp, nil

}
