package file_usage

import (
	"bcare/api/internal/auth"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/berr"
	"bcare/common/cast"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewDeleteLogic(ctx context.Context, svcCtx *svc.ServiceContext) DeleteLogic {
	return DeleteLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DeleteLogic) Delete(req *types.FileUsageDeleteRequest) (resp *types.FileUsageResponse, err error) {
	if !l.svcCtx.Auth.File.CanDelete(l.ctx, nil) {
		return nil, auth.ErrFileDeleteDenied
	}

	if req.Id <= 0 {
		return nil, berr.ErrRequestParam.Wrap(nil).Op("FileUsageDeleteLogic.Delete")
	}

	// Get file usage before deletion to return in response
	fileUsageOutput, err := l.svcCtx.FileUsageModel.GetFileUsage(l.ctx, req.Id)
	if err != nil {
		return nil, err
	}

	// Call model to delete file usage
	err = l.svcCtx.FileUsageModel.DeleteFileUsage(l.ctx, req.Id)
	if err != nil {
		return nil, err
	}

	// Map output to response
	resp = new(types.FileUsageResponse)
	err = cast.ConvertViaJson(resp, fileUsageOutput)
	if err != nil {
		return nil, berr.ErrServerCommon.Wrap(err)
	}

	return resp, nil
}
