package file_usage

import (
	"bcare/api/internal/auth"
	"bcare/api/internal/dto"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/berr"
	"bcare/common/cast"
	"context"
	"math"

	"github.com/zeromicro/go-zero/core/logx"
)

type ListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewListLogic(ctx context.Context, svcCtx *svc.ServiceContext) ListLogic {
	return ListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ListLogic) List(req *types.FileUsageListRequest) (resp *types.FileUsageListResponse, err error) {
	if !l.svcCtx.Auth.File.CanList(l.ctx, nil) {
		return nil, auth.ErrFileListDenied
	}

	// Map request to input
	input := dto.ListFileUsagesInput{
		Page:     req.Page,
		PageSize: req.PageSize,
		OrderBy:  req.OrderBy,
	}

	// Map filter
	if req.Filter != nil {
		input.Filter = dto.FileUsageInputFilter{
			FileID:     req.Filter.FileId,
			EntityType: req.Filter.EntityType,
			EntityID:   req.Filter.EntityId,
			UsageType:  req.Filter.UsageType,
			TrackID:    req.Filter.TrackId,
		}
	}

	// Call model to list file usages
	output, err := l.svcCtx.FileUsageModel.ListFileUsages(l.ctx, input)
	if err != nil {
		return nil, err
	}

	// Map output to response
	resp = &types.FileUsageListResponse{
		FileUsages: make([]types.FileUsageResponse, 0),
		Total:      output.Total,
	}

	// Calculate total pages
	if req.PageSize > 0 {
		resp.TotalPage = int(math.Ceil(float64(output.Total) / float64(req.PageSize)))
	} else {
		resp.TotalPage = 1
	}

	// Map file usages
	err = cast.ConvertViaJson(&resp.FileUsages, output.FileUsages)
	if err != nil {
		return nil, berr.ErrServerCommon.Wrap(err)
	}

	return resp, nil
}
