package file_usage

import (
	"bcare/api/internal/auth"
	"bcare/api/internal/dto"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/berr"
	"bcare/common/cast"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateLogic(ctx context.Context, svcCtx *svc.ServiceContext) UpdateLogic {
	return UpdateLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateLogic) Update(req *types.FileUsageUpdateRequest) (resp *types.FileUsageResponse, err error) {
	if !l.svcCtx.Auth.File.CanUpdate(l.ctx, nil) {
		return nil, auth.ErrFileUpdateDenied
	}

	if req.Id <= 0 {
		return nil, berr.ErrRequestParam.Wrap(nil).Op("FileUsageUpdateLogic.Update")
	}

	// Map request to input
	input := &dto.UpdateFileUsageInput{
		ID:         req.Id,
		FileID:     req.FileId,
		EntityType: req.EntityType,
		EntityID:   req.EntityId,
		UsageType:  req.UsageType,
		Modified:   req.Modified,
	}

	// Call model to update file usage
	fileUsageOutput, err := l.svcCtx.FileUsageModel.UpdateFileUsage(l.ctx, input)
	if err != nil {
		return nil, err
	}

	// Map output to response
	resp = new(types.FileUsageResponse)
	err = cast.ConvertViaJson(resp, fileUsageOutput)
	if err != nil {
		return nil, berr.ErrServerCommon.Wrap(err)
	}

	return resp, nil
}
