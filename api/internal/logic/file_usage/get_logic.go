package file_usage

import (
	"bcare/api/internal/auth"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/berr"
	"bcare/common/cast"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetLogic(ctx context.Context, svcCtx *svc.ServiceContext) GetLogic {
	return GetLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetLogic) Get(req *types.FileUsageGetRequest) (resp *types.FileUsageResponse, err error) {
	if !l.svcCtx.Auth.File.CanGet(l.ctx, nil) {
		return nil, auth.ErrFileGetDenied
	}

	if req.Id <= 0 {
		return nil, berr.ErrRequestParam.Wrap(nil).Op("FileUsageGetLogic.Get")
	}

	// Call model to get file usage
	fileUsageOutput, err := l.svcCtx.FileUsageModel.GetFileUsage(l.ctx, req.Id)
	if err != nil {
		return nil, err
	}

	// Map output to response
	resp = new(types.FileUsageResponse)
	err = cast.ConvertViaJson(resp, fileUsageOutput)
	if err != nil {
		return nil, berr.ErrServerCommon.Wrap(err)
	}

	return resp, nil
}
