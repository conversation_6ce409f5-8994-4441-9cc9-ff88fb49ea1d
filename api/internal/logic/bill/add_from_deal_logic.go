package bill

import (
	"bcare/api/internal/auth"
	"bcare/api/model"
	"bcare/common/bconst"
	"bcare/common/bquery"
	cast2 "bcare/common/cast"
	"bcare/common/ctxdata"
	"bcare/ent"
	"bcare/ent/attachment"
	"bcare/ent/deal"
	"bcare/ent/installmentplan"
	"context"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type AddFromDealLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAddFromDealLogic(ctx context.Context, svcCtx *svc.ServiceContext) AddFromDealLogic {
	return AddFromDealLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AddFromDealLogic) AddFromDeal(req *types.BillAddFromDealRequest) (resp *types.BillResponse, err error) {
	billRecord := new(ent.Bill)
	if !l.svcCtx.Auth.Bill.CanUpdate(l.ctx, billRecord) {
		logx.WithContext(l.ctx).Infof("Permission denied")
		return nil, auth.ErrBillAddDenied
	}
	// Get deal first to verify it exists
	dealRecord, err := l.svcCtx.Ent.Deal.Query().
		Where(deal.ID(req.DealId)).
		First(l.ctx)
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, types.ErrBillAdd.Wrap(err, "Deal not found")
		}
		return nil, types.ErrBillAdd.Wrap(err, "Failed to get deal")
	}

	// Get attachments that don't have bill items yet
	attachments, err := l.svcCtx.Ent.Attachment.Query().
		Where(
			attachment.DealID(dealRecord.ID),
			// Only get attachments that don't have bill items
			attachment.Not(
				attachment.HasBillItemWith(),
			),
		).All(l.ctx)
	if err != nil {
		return nil, types.ErrBillAdd.Wrap(err, "Failed to get attachments")
	}

	if len(attachments) == 0 {
		return nil, types.ErrBillAdd.Wrap(nil, "No available attachments found for billing")
	}

	// Check if deal has installment plans
	installmentPlans, err := l.svcCtx.Ent.InstallmentPlan.Query().
		Where(
			installmentplan.DealID(dealRecord.ID),
			installmentplan.BillIDIsNil(),
		).All(l.ctx)
	if err != nil {
		return nil, types.ErrBillAdd.Wrap(err, "Failed to get installment plans")
	}

	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		// 1. Create Bill
		billRecord.DealID = &req.DealId
		billRecord.PersonID = dealRecord.PersonID // Get PersonID from deal
		billRecord.UserID = ctxdata.GetUidFromCtx(l.ctx)
		billRecord.Status = bconst.StatusNormal

		billRecord, err = tx.Bill.Create().
			SetBill(billRecord).
			Save(l.ctx)
		if err != nil {
			return types.ErrBillAdd.Wrap(err, "Failed to save bill record")
		}

		// Update attachments with first installment plan if exists
		if len(installmentPlans) > 0 {
			firstPlan := installmentPlans[0]
			if firstPlan.TotalInstallments > 0 {
				for _, at := range attachments {
					_, err = tx.Attachment.UpdateOne(at).
						SetPlanID(firstPlan.ID).
						Save(l.ctx)
					if err != nil {
						return types.ErrBillAdd.Wrap(err, "Failed to update attachment plan_id")
					}
				}
			}
			// Update all attachments to link with first installment plan

		}

		// 2. Create BillItems for each attachment
		trackRecord, _ := model.GetActiveTrack(l.ctx, tx, dealRecord.PersonID)

		for _, at := range attachments {

			amount := at.Price*float64(at.Quantity) - at.Discount
			billItemRecord := &ent.BillItem{
				BillID: &billRecord.ID,
				Status: bconst.StatusNormal,
				UserID: ctxdata.GetUidFromCtx(l.ctx),
				Amount: amount, // Assuming attachment has Amount field
			}
			if trackRecord != nil {
				billItemRecord.TrackID = trackRecord.ID
			}

			_, err = tx.BillItem.Create().
				SetBillItem(billItemRecord).
				SetAttachment(at).
				Save(l.ctx)
			if err != nil {
				return types.ErrBillItemAdd.Wrap(err, "Failed to save bill item record")
			}
			_, err = tx.Attachment.UpdateOneID(at.ID).SetStatus(bconst.StatusNormal).Save(l.ctx)
			if err != nil {
				return types.ErrAttachmentAdd.Wrap(err, "Failed to save attachment record")
			}
		}

		// 3. Update InstallmentPlans if exists
		if len(installmentPlans) > 0 {
			for _, plan := range installmentPlans {
				_, err = tx.InstallmentPlan.UpdateOne(plan).
					SetBillID(billRecord.ID).
					SetState(installmentplan.StateActive).
					Save(l.ctx)
				if err != nil {
					return types.ErrBillAdd.Wrap(err, "Failed to update installment plan")
				}
			}
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	// Prepare response
	resp = new(types.BillResponse)
	_ = cast2.ConvertViaJson(&resp, billRecord)

	return resp, nil
}
