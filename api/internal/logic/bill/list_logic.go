package bill

import (
	"bcare/api/internal/auth"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/bconst"
	"bcare/common/bquery"
	cast2 "bcare/common/cast"
	"bcare/ent"
	"bcare/ent/bill"
	"bcare/ent/billitem"
	"context"
	"entgo.io/ent/dialect/sql"
	"github.com/samber/lo"
	"github.com/zeromicro/go-zero/core/logx"
)

type ListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewListLogic(ctx context.Context, svcCtx *svc.ServiceContext) ListLogic {
	return ListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ListLogic) List(req *types.BillListRequest) (resp *types.BillListResponse, err error) {
	//kiểm tra xem có quyền ko
	if !l.svcCtx.Auth.Bill.CanList(l.ctx, nil) {
		logx.WithContext(l.ctx).Infof("Bạn ko có quyền")
		return nil, auth.ErrBillListDenied
	}

	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		limit, offset := bquery.Paging(req.PageSize, req.Page)
		order := bquery.Ordering(req.OrderBy)

		query := tx.Bill.Query().WithDeal().WithTrack().
			WithPayments(func(q *ent.PaymentQuery) {
				q.WithAllocations()
			}).
			WithItems(func(q *ent.BillItemQuery) {
				q.WithAttachment()
				q.WithInstallment()
				q.WithAllocations()
				q.Where(billitem.StatusEQ(bconst.StatusNormal))
				q.Select(billitem.FieldID, billitem.FieldAmount)
			}).
			WithInstallmentPlans(func(q *ent.InstallmentPlanQuery) {
				q.WithInstallments().Limit(1)
			}).
			Limit(limit).Offset(offset).Order(order)

		filterMap := make(map[string]interface{})
		_ = cast2.AtomNoZero(&filterMap, req.Filter)

		for field, value := range filterMap {
			query = query.Where(sql.FieldEQ(field, value))
		}

		if req.PersonId > 0 {
			query = query.Where(bill.PersonID(req.PersonId))
		}

		var total int
		if req.PageSize != 0 {
			total, _ = query.Count(l.ctx)
		} else {
			//Nếu lấy all thì khỏi đếm
			total = 0
		}

		bills, e := query.All(l.ctx)

		if e != nil {
			err = types.ErrBillGetList.Wrap(e)
			return err
		}

		resp = new(types.BillListResponse)

		resp.Bills = lo.Map(bills, func(bill *ent.Bill, _ int) types.BillResponse {
			b := types.BillResponse{}
			_ = cast2.ConvertViaJson(&b, bill)
			// Tính tổng bill value từ BillItems
			totalValue := 0.0
			for _, billItem := range bill.Edges.Items {
				totalValue += billItem.Amount
			}
			b.TotalValue = totalValue
			return b
		})

		resp.Total = total
		return nil
	})

	return resp, err
}
