package bill

import (
	"bcare/api/internal/auth"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/api/model"
	"bcare/common/bconst"
	"bcare/common/bquery"
	cast2 "bcare/common/cast"
	"bcare/common/ctxdata"
	"bcare/ent"
	"context"
	"github.com/jinzhu/copier"
	"github.com/zeromicro/go-zero/core/logx"
	"time"
)

type AddLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAddLogic(ctx context.Context, svcCtx *svc.ServiceContext) AddLogic {
	return AddLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AddLogic) Add(req *types.BillAddRequest) (resp *types.BillResponse, err error) {
	billRecord := new(ent.Bill)
	var paymentRecord *ent.Payment

	// Validate total bill items amount
	totalBillItemsAmount := 0.0
	for _, item := range req.BillItems {
		totalBillItemsAmount += item.Amount
	}

	// Validate payment amount if payment exists
	if req.Payment != nil {
		if req.Payment.TotalAmount > totalBillItemsAmount {
			return nil, types.ErrBillAdd.Wrap(nil, "Payment total amount cannot exceed total bill items amount")
		}

		// Validate payment allocations
		totalAllocationsAmount := 0.0
		for _, item := range req.BillItems {
			if item.AllocateAmount > 0 {
				// Check if allocation amount exceeds bill item amount
				if item.AllocateAmount > item.Amount {
					return nil, types.ErrBillAdd.Wrap(nil,
						"Payment allocation amount cannot exceed bill item amount")
				}
				totalAllocationsAmount += item.AllocateAmount
			}
		}

		// Validate total allocations against payment amount
		if totalAllocationsAmount > req.Payment.TotalAmount {
			return nil, types.ErrBillAdd.Wrap(nil,
				"Total payment allocations cannot exceed payment total amount")
		}
	}

	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		// 1. Create Bill
		_ = cast2.ConvertViaJson(req, billRecord)
		if !l.svcCtx.Auth.Bill.CanUpdate(l.ctx, billRecord) {
			logx.WithContext(l.ctx).Infof("Permission denied")
			return auth.ErrBillAddDenied
		}

		billRecord.UserID = ctxdata.GetUidFromCtx(l.ctx)
		trackRecord, _ := model.GetActiveTrack(l.ctx, tx, billRecord.PersonID)
		if trackRecord != nil {
			billRecord.TrackID = trackRecord.ID
		}

		billRecord, err = tx.Bill.Create().SetBill(billRecord).Save(l.ctx)
		if err != nil {
			return types.ErrBillAdd.Wrap(err, "Failed to save bill record")
		}

		// 2. Create Payment if provided
		if req.Payment != nil {
			err = copier.CopyWithOption(&paymentRecord, req, cast2.PayloadToEnt)
			paymentRecord.PaymentDate = time.Now()
			paymentRecord.BillID = billRecord.ID
			paymentRecord.UserID = ctxdata.GetUidFromCtx(l.ctx)
			paymentRecord, err = tx.Payment.Create().
				SetPayment(paymentRecord).
				Save(l.ctx)

			if err != nil {
				return types.ErrPaymentAdd.Wrap(err, "Failed to save payment record")
			}
		}

		// 3. Create BillItems and PaymentAllocations
		for _, item := range req.BillItems {
			// Validate
			if item.AttachmentId == 0 {
				return types.ErrBillItemAdd.Wrap(nil, "Either Attachment ID or Installment ID must be provided")
			}

			// Create BillItem
			billItemRecord := new(ent.BillItem)
			_ = copier.CopyWithOption(&billItemRecord, item, cast2.ConvertInt)
			billItemRecord.BillID = &billRecord.ID
			billItemRecord.Status = bconst.StatusNormal
			billItemRecord.UserID = ctxdata.GetUidFromCtx(l.ctx)

			billItemRecord, err = tx.BillItem.Create().
				SetBillItem(billItemRecord).
				Save(l.ctx)
			if err != nil {
				return types.ErrBillItemAdd.Wrap(err, "Failed to save bill item record")
			}

			// Create PaymentAllocation if payment exists and amount is provided
			if paymentRecord != nil && item.AllocateAmount > 0 {
				_, err = tx.PaymentAllocation.Create().
					SetUserID(ctxdata.GetUidFromCtx(l.ctx)).
					SetPaymentID(paymentRecord.ID).
					SetBillItemID(billItemRecord.ID).
					SetAmount(item.AllocateAmount).
					Save(l.ctx)
				if err != nil {
					return types.ErrPaymentAllocationAdd.Wrap(err, "Failed to create payment allocation")
				}
			}

		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	resp = new(types.BillResponse)
	_ = cast2.ConvertViaJson(&resp, billRecord)

	return resp, nil
}

// Helper functions
func floatPtr(f float64) *float64 {
	if f == 0 {
		return nil
	}
	return &f
}
