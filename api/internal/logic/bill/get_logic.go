package bill

import (
	"bcare/api/internal/auth"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/bconst"
	"bcare/common/bquery"
	cast2 "bcare/common/cast"
	"bcare/ent"
	"bcare/ent/bill"
	"bcare/ent/billitem"
	"context"
	"github.com/zeromicro/go-zero/core/logx"
)

type GetLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetLogic(ctx context.Context, svcCtx *svc.ServiceContext) GetLogic {
	return GetLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetLogic) Get(req *types.BillGetRequest) (resp *types.BillResponse, err error) {
	// Kiểm tra quyền
	if !l.svcCtx.Auth.Bill.CanGet(l.ctx, nil) {
		logx.WithContext(l.ctx).Infof("Bạn không có quyền")
		return nil, auth.ErrBillGetDenied
	}

	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		// Query bill với các relationship
		billRecord, e := tx.Bill.Query().
			WithDeal().
			WithTrack().
			WithItems(func(q *ent.BillItemQuery) {
				q.WithAttachment()
				q.WithInstallment()
				q.WithAllocations()
				q.Where(billitem.StatusEQ(bconst.StatusNormal))
			}).
			WithInstallmentPlans(func(q *ent.InstallmentPlanQuery) {
				q.WithInstallments().Limit(1)
			}).
			WithPayments().
			WithData().
			Where(bill.ID(req.Id)).
			Only(l.ctx)

		if e != nil {
			err = types.ErrBillGetList.Wrap(e)
			return err
		}

		resp = new(types.BillResponse)
		_ = cast2.ConvertViaJson(resp, billRecord)

		// Tính tổng bill value từ BillItems
		totalValue := 0.0
		for _, billItem := range billRecord.Edges.Items {
			totalValue += billItem.Amount
		}
		resp.TotalValue = totalValue

		return nil
	})

	return resp, err
}
