package bill

import (
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/bquery"
	cast2 "bcare/common/cast"
	"bcare/common/ctxdata"
	"bcare/ent"
	"context"
	"github.com/jinzhu/copier"

	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateLogic(ctx context.Context, svcCtx *svc.ServiceContext) UpdateLogic {
	return UpdateLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateLogic) Update(req *types.BillUpdateRequest) (resp *types.BillResponse, err error) {
	billRecord := new(ent.Bill)
	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		billRecord, err = tx.Bill.Get(l.ctx, req.Id)
		if err != nil && ent.IsNotFound(err) {
			return types.ErrBillNotFound.Wrap(err, "Bill ID %d not found", req.Id)
		}

		if billRecord == nil {
			logx.Errorf("attachment not found")
			return types.ErrBillNotFound.Wrap(nil, "Bill record is nil")
		}

		err = copier.CopyWithOption(&billRecord, req, cast2.PayloadToEnt)
		if err != nil {
			logx.Errorf("copier copy attachment failed, err:%v", err)
			return types.ErrBillUpdate.Wrap(err, "Failed to copy data from request")
		}
		billRecord.UserID = ctxdata.GetUidFromCtx(l.ctx)
		billRecord, err = tx.Bill.UpdateOneID(req.Id).SetBill(billRecord, req.Modified...).Save(l.ctx)

		if err != nil {
			return types.ErrBillUpdate.Wrap(err, "Failed to update bill record")
		}

		return nil
	})
	if err != nil {
		return nil, err
	}
	resp = new(types.BillResponse)
	_ = cast2.ConvertViaJson(&resp, billRecord)

	return resp, nil
}
