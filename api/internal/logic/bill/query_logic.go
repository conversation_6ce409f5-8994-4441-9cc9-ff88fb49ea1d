package bill

import (
	"bcare/api/internal/auth"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/bquery"
	cast2 "bcare/common/cast"
	"bcare/ent"
	"bcare/ent/bill"
	"bcare/ent/deal"
	"bcare/ent/person"
	"bcare/ent/schema"
	"context"
	"github.com/jinzhu/copier"
	"github.com/zeromicro/go-zero/core/logx"
)

type QueryLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewQueryLogic(ctx context.Context, svcCtx *svc.ServiceContext) QueryLogic {
	return QueryLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *QueryLogic) Query(req *types.BillDynamicQuery) (res *types.DynamicQueryResponse, err error) {
	//kiểm tra xem có quyền ko
	if !l.svcCtx.Auth.Bill.CanQuery(l.ctx, nil) {
		logx.WithContext(l.ctx).Infof("Bạn ko có quyền")
		return nil, auth.ErrBillQueryDenied
	}

	dQuery := &bquery.DynamicQuery{}
	err = copier.Copy(dQuery, req)
	if err != nil {
		logx.WithContext(l.ctx).Errorf("unable to copy request: %v", err)
		return nil, err
	}

	query := l.svcCtx.Ent.Bill.Query().
		Modify(bquery.ApplyDynamicQuery(dQuery)).
		WithDeal(func(q *ent.DealQuery) {
			q.WithPerson()
		}).
		WithItems(func(q *ent.BillItemQuery) {
			q.WithAttachment(func(q *ent.AttachmentQuery) {
				q.WithProduct().
					WithCreator()
				//WithDoctor()
			}).
				WithInstallment()
		})

	if req.Person != "" {
		query = query.Where(bill.HasDealWith(deal.HasPersonWith(person.Or(person.FullNameContainsFold(req.Person), person.PhoneContains(req.Person)))))
	}

	result := map[string]any{}
	aliases := make(map[string]bool)
	for _, agg := range req.Aggregations {
		if agg.Alias != "" {
			aliases[agg.Alias] = true
		}
	}

	skipSoftDelete := schema.SkipSoftDelete(l.ctx)

	if req.Limit <= 1 {
		r, err := query.First(skipSoftDelete)
		if err != nil && !ent.IsNotFound(err) {
			logx.WithContext(l.ctx).Errorf("unable to fetch bill record: %v", err)
			return nil, err
		}

		if r != nil {
			for alias := range aliases {
				result[alias], _ = r.Value(alias)
			}
		}
	} else {
		rows, err := query.All(skipSoftDelete)
		if err != nil {
			logx.WithContext(l.ctx).Errorf("unable to fetch bill records: %v", err)
			return nil, err
		}

		resultList := make([]map[string]any, len(rows))
		for i, r := range rows {
			totalPayment := 0.0
			for _, billItem := range r.Edges.Items {
				totalPayment += billItem.Amount
			}

			rowResult := make(map[string]any)
			rowResult["record"] = cast2.Atom(&rowResult, r)
			for alias := range aliases {
				rowResult[alias], _ = r.Value(alias)
			}
			rowResult["payment"] = totalPayment
			resultList[i] = rowResult
		}
		result["rows"] = resultList
	}

	res = new(types.DynamicQueryResponse)
	res.Result = result

	return
}
