package bill

import (
	"bcare/api/internal/auth"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/bconst"
	"bcare/common/bquery"
	"bcare/ent"
	"bcare/ent/billitem"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewDeleteLogic(ctx context.Context, svcCtx *svc.ServiceContext) DeleteLogic {
	return DeleteLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DeleteLogic) Delete(req *types.BillDeleteRequest) (resp *types.BillResponse, err error) {
	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		billRecord, err := tx.Bill.Get(l.ctx, req.Id)
		if err != nil && ent.IsNotFound(err) {
			return types.ErrDealNotFound.Wrap(err)
		}

		//kiểm tra xem có quyền ko
		if !l.svcCtx.Auth.Bill.CanDelete(l.ctx, billRecord) {
			logx.WithContext(l.ctx).Infof("Bạn ko có quyền")
			return auth.ErrBillDeleteDenied
		}

		// Delete associated bill items first
		_, err = tx.BillItem.Delete().
			Where(
				billitem.BillID(billRecord.ID),
			).Exec(l.ctx)
		if err != nil {
			return types.ErrBillItemDelete.Wrap(err)
		}

		billRecord.Status = bconst.StatusDeleted

		err = tx.Bill.DeleteOne(billRecord).Exec(l.ctx)
		if err != nil {
			return types.ErrBillUpdate.Wrap(err)
		}
		return nil
	})

	return resp, err

}
