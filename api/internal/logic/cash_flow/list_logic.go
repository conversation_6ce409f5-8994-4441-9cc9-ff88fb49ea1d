package cash_flow

import (
	"bcare/api/internal/dto"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/berr"
	"bcare/common/cast"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type ListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ListLogic {
	return &ListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ListLogic) List(req *types.CashFlowListRequest) (resp *types.CashFlowListResponse, err error) {
	if !l.svcCtx.Auth.CashFlow.CanList(l.ctx) {
		return nil, berr.ErrPermissionDenied.Wrap(nil)
	}

	input := new(dto.ListCashFlowsInput)
	err = cast.ReqToModelInput(input, req)
	if err != nil {
		return nil, err
	}

	listOutput, err := l.svcCtx.CashFlowModel.ListCashFlows(l.ctx, *input)
	if err != nil {
		return nil, err
	}

	resp = &types.CashFlowListResponse{
		CashFlows: make([]types.CashFlowResponse, 0, len(listOutput.CashFlows)),
		Total:     listOutput.Total,
		TotalPage: listOutput.TotalPage,
	}

	err = cast.ConvertViaJson(&resp.CashFlows, listOutput.CashFlows)
	if err != nil {
		return nil, err
	}

	return resp, nil
}
