package cash_flow

import (
	"bcare/api/internal/dto"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/berr"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewDeleteLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteLogic {
	return &DeleteLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DeleteLogic) Delete(req *types.CashFlowDeleteRequest) (resp *types.CashFlowResponse, err error) {
	// Get existing cashflow for permission check
	existingCashFlow, err := l.svcCtx.CashFlowModel.GetCashFlow(l.ctx, dto.GetCashFlowInput{ID: req.Id})
	if err != nil {
		return nil, err
	}

	if !l.svcCtx.Auth.CashFlow.CanDelete(l.ctx, &existingCashFlow.CashFlowOutput) {
		return nil, berr.ErrPermissionDenied.Wrap(nil)
	}

	err = l.svcCtx.CashFlowModel.DeleteCashFlow(l.ctx, dto.DeleteCashFlowInput{ID: req.Id})
	if err != nil {
		return nil, err
	}

	// Return empty response for successful deletion
	resp = new(types.CashFlowResponse)
	return resp, nil
}
