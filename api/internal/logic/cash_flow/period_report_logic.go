package cash_flow

import (
	"bcare/api/internal/dto"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/berr"
	"bcare/common/cast"
	"context"
	"fmt"
	"time"

	"github.com/zeromicro/go-zero/core/logx"
)

type PeriodReportLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewPeriodReportLogic(ctx context.Context, svcCtx *svc.ServiceContext) PeriodReportLogic {
	return PeriodReportLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *PeriodReportLogic) PeriodReport(req *types.CashFlowPeriodRequest) (resp *types.CashFlowPeriodResponse, err error) {
	// Check permission
	if !l.svcCtx.Auth.CashFlow.CanGetReport(l.ctx) {
		return nil, berr.ErrPermissionDenied.Wrap(nil)
	}

	// Parse date from string
	date, err := time.Parse("2006-01-02", req.Date)
	if err != nil {
		return nil, berr.ErrValidationFailed.Wrap(err)
	}

	// Validate period type
	if req.PeriodType != "week" && req.PeriodType != "month" && req.PeriodType != "year" {
		return nil, berr.ErrValidationFailed.Wrap(fmt.Errorf("period_type must be one of: week, month, year"))
	}

	// Set default period count if not provided or validate bounds
	periodCount := req.PeriodCount
	if periodCount == 0 {
		periodCount = 4 // Default: current period + 3 previous periods
	}
	if periodCount < 1 || periodCount > 50 {
		return nil, berr.ErrValidationFailed.Wrap(fmt.Errorf("period_count must be between 1 and 50"))
	}

	// Create input for model
	input := dto.CashFlowPeriodInput{
		PeriodType:  req.PeriodType,
		Date:        date,
		PeriodCount: periodCount,
	}

	// Get period report from model
	periodOutput, err := l.svcCtx.CashFlowModel.GetCashFlowPeriodReport(l.ctx, input)
	if err != nil {
		return nil, err
	}

	// Convert to response type
	resp = new(types.CashFlowPeriodResponse)
	err = cast.ConvertViaJson(resp, periodOutput)
	if err != nil {
		return nil, err
	}

	return resp, nil
}
