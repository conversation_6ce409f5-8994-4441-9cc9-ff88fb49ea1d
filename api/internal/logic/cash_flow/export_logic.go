package cash_flow

import (
	"bcare/api/internal/dto"
	"bcare/api/internal/model"
	"bcare/common/berr"
	"bcare/common/cast"
	"bcare/common/excelutil"
	"context"
	"fmt"
	"net/http"
	"time"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type ExportLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewExportLogic(ctx context.Context, svcCtx *svc.ServiceContext) ExportLogic {
	return ExportLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ExportLogic) Export(w http.ResponseWriter, req *types.CashFlowListRequest) error {
	// Check permission
	if !l.svcCtx.Auth.CashFlow.CanList(l.ctx) {
		return berr.ErrPermissionDenied.Wrap(nil)
	}

	// Convert request to input
	input := new(dto.ListCashFlowsInput)
	err := cast.ReqToModelInput(input, req)
	if err != nil {
		return err
	}

	// Remove pagination for export - get all records
	input.PageSize = 0
	input.Page = 0

	// Get data from database using ListCashFlows
	listOutput, err := l.svcCtx.CashFlowModel.ListCashFlows(l.ctx, *input)
	if err != nil {
		return err
	}

	// Convert to response format
	var cashFlows []types.CashFlowResponse
	err = cast.ConvertViaJson(&cashFlows, listOutput.CashFlows)
	if err != nil {
		return err
	}

	// Create exporter
	exporter := model.NewCashFlowExporter(cashFlows)

	// Create storage path
	now := time.Now()
	storagePath := fmt.Sprintf("%s/%s/%s", l.svcCtx.Config.Storage.Local.Path, "reports", fmt.Sprintf("%d_%02d_%02d", now.Year(), now.Month(), now.Day()))

	// Generate and return Excel file
	excelutil.WriteConfigurableExcelResponse(w, nil, exporter, storagePath)

	l.Logger.WithContext(l.ctx).Infof("Cash Flow List exported successfully, total records: %d", len(cashFlows))
	return nil
}
