package cash_flow

import (
	"bcare/api/internal/dto"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/berr"
	"bcare/common/butils"
	"bcare/common/cast"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateLogic {
	return &UpdateLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateLogic) Update(req *types.CashFlowUpdateRequest) (resp *types.CashFlowResponse, err error) {
	existingCashFlow, err := l.svcCtx.CashFlowModel.GetCashFlow(l.ctx, dto.GetCashFlowInput{ID: req.Id})
	if err != nil {
		return nil, err
	}

	if !l.svcCtx.Auth.CashFlow.CanUpdate(l.ctx, &existingCashFlow.CashFlowOutput) {
		return nil, berr.ErrPermissionDenied.Wrap(nil)
	}

	input := new(dto.UpdateCashFlowInput)
	err = cast.ReqToModelInput(input, req)
	if err != nil {
		return nil, err
	}

	cashFlowOutput, err := l.svcCtx.CashFlowModel.UpdateCashFlow(l.ctx, input)
	if err != nil {
		return nil, err
	}

	if len(input.Items) > 0 && butils.Contains(input.Modified, "items") {
		existingItems, err := l.svcCtx.CashFlowItemModel.ListCashFlowItemsByCashFlowIDs(l.ctx, []int{cashFlowOutput.ID})
		if err != nil {
			return nil, err
		}

		for _, existingItem := range existingItems {
			if err := l.svcCtx.CashFlowItemModel.DeleteCashFlowItem(l.ctx, dto.DeleteCashFlowItemInput{ID: existingItem.ID}); err != nil {
				return nil, err
			}
		}

		itemInputs := make([]dto.CreateCashFlowItemInput, len(input.Items))
		for i, item := range input.Items {
			itemInputs[i] = dto.CreateCashFlowItemInput{
				CashFlowID: cashFlowOutput.ID,
				CategoryID: item.CategoryID,
				Amount:     item.Amount,
				Note:       item.Note,
				Order:      item.Order,
				HasVat:     item.HasVat,
			}
		}

		items, err := l.svcCtx.CashFlowItemModel.CreateCashFlowItems(l.ctx, cashFlowOutput.ID, itemInputs)
		if err != nil {
			return nil, err
		}

		cashFlowOutput.Items = make([]dto.CashFlowItemWithRelationsOutput, len(items))
		for i, item := range items {
			cashFlowOutput.Items[i] = *item
		}
	}

	resp = new(types.CashFlowResponse)
	err = cast.ConvertViaJson(resp, cashFlowOutput)
	if err != nil {
		return nil, err
	}

	return resp, nil
}
