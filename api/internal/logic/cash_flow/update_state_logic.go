package cash_flow

import (
	"bcare/api/internal/dto"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/berr"
	"bcare/common/cast"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateStateLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateStateLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateStateLogic {
	return &UpdateStateLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateStateLogic) UpdateState(req *types.CashFlowUpdateStateRequest) (resp *types.CashFlowResponse, err error) {
	// Get existing cashflow for permission check
	existingCashFlow, err := l.svcCtx.CashFlowModel.GetCashFlow(l.ctx, dto.GetCashFlowInput{ID: req.Id})
	if err != nil {
		return nil, err
	}

	if !l.svcCtx.Auth.CashFlow.CanUpdateState(l.ctx, &existingCashFlow.CashFlowOutput) {
		return nil, berr.ErrPermissionDenied.Wrap(nil)
	}

	input := new(dto.UpdateCashFlowStateInput)
	err = cast.ReqToModelInput(input, req)
	if err != nil {
		return nil, err
	}

	cashFlowOutput, err := l.svcCtx.CashFlowModel.UpdateCashFlowState(l.ctx, input)
	if err != nil {
		return nil, err
	}

	resp = new(types.CashFlowResponse)
	err = cast.ConvertViaJson(resp, cashFlowOutput)
	if err != nil {
		return nil, err
	}

	return resp, nil
}
