package cash_flow

import (
	"bcare/api/internal/dto"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/berr"
	"bcare/common/cast"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetLogic {
	return &GetLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetLogic) Get(req *types.CashFlowGetRequest) (resp *types.CashFlowResponse, err error) {
	input := dto.GetCashFlowInput{
		ID:              req.Id,
		IncludeRelation: req.IncludeRelation,
	}

	cashFlowOutput, err := l.svcCtx.CashFlowModel.GetCashFlow(l.ctx, input)
	if err != nil {
		return nil, err
	}

	if !l.svcCtx.Auth.CashFlow.CanGet(l.ctx, &cashFlowOutput.CashFlowOutput) {
		return nil, berr.ErrPermissionDenied.Wrap(nil)
	}

	resp = new(types.CashFlowResponse)
	err = cast.ConvertViaJson(resp, cashFlowOutput)
	if err != nil {
		return nil, err
	}

	return resp, nil
}
