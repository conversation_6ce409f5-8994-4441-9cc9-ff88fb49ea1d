package cash_flow

import (
	"bcare/api/internal/dto"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/berr"
	"bcare/common/cast"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type AddLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAddLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AddLogic {
	return &AddLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AddLogic) Add(req *types.CashFlowAddRequest) (resp *types.CashFlowResponse, err error) {
	if !l.svcCtx.Auth.CashFlow.CanAdd(l.ctx) {
		return nil, berr.ErrPermissionDenied.Wrap(nil)
	}

	input := new(dto.CreateCashFlowInput)
	err = cast.ReqToModelInput(input, req)
	if err != nil {
		return nil, err
	}

	cashFlowOutput, err := l.svcCtx.CashFlowModel.CreateCashFlow(l.ctx, input)
	if err != nil {
		return nil, err
	}

	if len(input.Items) > 0 {
		itemInputs := make([]dto.CreateCashFlowItemInput, len(input.Items))
		for i, item := range input.Items {
			itemInputs[i] = dto.CreateCashFlowItemInput{
				CashFlowID:   cashFlowOutput.ID,
				CategoryID:   item.CategoryID,
				Amount:       item.Amount,
				Note:         item.Note,
				Order:        item.Order,
				HasVat:       item.HasVat,
				DepartmentID: item.DepartmentID,
			}
		}

		items, err := l.svcCtx.CashFlowItemModel.CreateCashFlowItems(l.ctx, cashFlowOutput.ID, itemInputs)
		if err != nil {
			return nil, err
		}

		cashFlowOutput.Items = make([]dto.CashFlowItemWithRelationsOutput, len(items))
		for i, item := range items {
			cashFlowOutput.Items[i] = *item
		}
	}

	resp = new(types.CashFlowResponse)
	err = cast.ConvertViaJson(resp, cashFlowOutput)
	if err != nil {
		return nil, err
	}

	return resp, nil
}
