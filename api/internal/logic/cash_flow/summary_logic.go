package cash_flow

import (
	"bcare/api/internal/dto"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/berr"
	"bcare/common/cast"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type SummaryLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewSummaryLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SummaryLogic {
	return &SummaryLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *SummaryLogic) Summary(req *types.CashFlowReportRequest) (resp *types.CashFlowReportResponse, err error) {
	if !l.svcCtx.Auth.CashFlow.CanGetReport(l.ctx) {
		return nil, berr.ErrPermissionDenied.Wrap(nil)
	}

	input := new(dto.CashFlowReportInput)
	err = cast.ReqToModelInput(input, req)
	if err != nil {
		return nil, err
	}

	reportOutput, err := l.svcCtx.CashFlowModel.GetCashFlowReport(l.ctx, *input)
	if err != nil {
		return nil, err
	}

	resp = new(types.CashFlowReportResponse)
	err = cast.ConvertViaJson(resp, reportOutput)
	if err != nil {
		return nil, err
	}

	return resp, nil
}
