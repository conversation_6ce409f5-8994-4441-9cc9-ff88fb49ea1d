package department

import (
	cast2 "bcare/common/cast"
	"bcare/ent"
	"context"
	"entgo.io/ent/dialect/sql"
	"github.com/samber/lo"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type ListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewListLogic(ctx context.Context, svcCtx *svc.ServiceContext) ListLogic {
	return ListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ListLogic) List(req *types.DepartmentListRequest) (resp *types.DepartmentListResponse, err error) {
	query := l.svcCtx.Ent.Department.Query()

	filterMap := make(map[string]interface{})
	_ = cast2.AtomNoZero(&filterMap, req)

	for field, value := range filterMap {
		query = query.Where(sql.FieldEQ(field, value))
	}
	departments, e := query.All(l.ctx)
	if e != nil {
		err = types.ErrTermGetList.Wrap(e)
		return
	}

	resp = new(types.DepartmentListResponse)
	resp.Departments = lo.Map(departments, func(d *ent.Department, _ int) types.Department {
		dep := types.Department{}
		_ = cast2.ConvertViaJson(&dep, d)
		return dep
	})

	return resp, err
}
