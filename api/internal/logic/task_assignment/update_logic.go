package task_assignment

import (
	"bcare/api/internal/dto"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/berr"
	"bcare/common/cast"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateLogic {
	return &UpdateLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateLogic) Update(req *types.TaskAssignmentUpdateRequest) (resp *types.TaskAssignment, err error) {
	existing, err := l.svcCtx.TaskAssignmentModel.Get(l.ctx, req.Id)
	if err != nil {
		return nil, err
	}

	if !l.svcCtx.Auth.TaskAssignment.CanUpdate(l.ctx, existing) {
		return nil, berr.ErrPermissionDenied.Wrap(nil)
	}

	input := new(dto.UpdateTaskAssignmentInput)
	err = cast.ReqToModelInput(input, req)
	if err != nil {
		return nil, err
	}

	output, err := l.svcCtx.TaskAssignmentModel.Update(l.ctx, input)
	if err != nil {
		return nil, err
	}

	resp = new(types.TaskAssignment)
	err = cast.ConvertViaJson(resp, output)
	if err != nil {
		return nil, err
	}

	return resp, nil
}
