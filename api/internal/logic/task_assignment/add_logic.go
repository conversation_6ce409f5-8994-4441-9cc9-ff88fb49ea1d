package task_assignment

import (
	"bcare/api/internal/dto"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/berr"
	"bcare/common/cast"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type AddLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAddLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AddLogic {
	return &AddLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AddLogic) Add(req *types.TaskAssignmentAddRequest) (resp *types.TaskAssignment, err error) {
	if !l.svcCtx.Auth.TaskAssignment.CanAdd(l.ctx) {
		return nil, berr.ErrPermissionDenied.Wrap(nil)
	}

	input := new(dto.CreateTaskAssignmentInput)
	err = cast.ReqToModelInput(input, req)
	if err != nil {
		return nil, err
	}

	output, err := l.svcCtx.TaskAssignmentModel.Create(l.ctx, input)
	if err != nil {
		return nil, err
	}

	resp = new(types.TaskAssignment)
	err = cast.ConvertViaJson(resp, output)
	if err != nil {
		return nil, err
	}

	return resp, nil
}
