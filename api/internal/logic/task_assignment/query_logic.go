package task_assignment

import (
	"bcare/api/internal/dto"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/berr"
	"bcare/common/cast"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type QueryLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewQueryLogic(ctx context.Context, svcCtx *svc.ServiceContext) QueryLogic {
	return QueryLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *QueryLogic) Query(req *types.TaskAssignmentDynamicQuery) (res *types.DynamicQueryResponse, err error) {
	input := &dto.ListTaskAssignmentInput{}
	if err := cast.ReqToModelInput(input, req); err != nil {
		return nil, berr.ErrRequestParam.Wrap(err)
	}

	result, err := l.svcCtx.TaskAssignmentModel.List(l.ctx, *input)
	if err != nil {
		return nil, err
	}

	res = &types.DynamicQueryResponse{}
	if err := cast.ModelOutputToResp(res, result); err != nil {
		return nil, berr.ErrServerCommon.Wrap(err)
	}

	return res, nil
}
