package task_assignment

import (
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/berr"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewDeleteLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteLogic {
	return &DeleteLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DeleteLogic) Delete(req *types.TaskAssignmentDeleteRequest) (resp *types.TaskAssignment, err error) {
	existing, err := l.svcCtx.TaskAssignmentModel.Get(l.ctx, req.Id)
	if err != nil {
		return nil, err
	}

	if !l.svcCtx.Auth.TaskAssignment.CanDelete(l.ctx, existing) {
		return nil, berr.ErrPermissionDenied.Wrap(nil)
	}

	_, err = l.svcCtx.TaskAssignmentModel.Delete(l.ctx, req.Id)
	if err != nil {
		return nil, err
	}

	resp = new(types.TaskAssignment)
	return resp, nil
}
