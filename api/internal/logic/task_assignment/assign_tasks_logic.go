package task_assignment

import (
	"bcare/api/internal/types"
	"bcare/api/model"
	"bcare/common/bquery"
	keylock "bcare/common/lock"
	"bcare/ent"
	"bcare/ent/taskassignment"
	"context"
	"fmt"
	"time"

	"bcare/api/internal/svc"
	"github.com/zeromicro/go-zero/core/logx"
)

type AssignTasksLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAssignTasksLogic(ctx context.Context, svcCtx *svc.ServiceContext) AssignTasksLogic {
	return AssignTasksLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AssignTasksLogic) AssignTasks(req *types.AssignTasksRequest) (resp *types.BulkUpdateResult, err error) {
	result := &types.BulkUpdateResult{
		SuccessCount: 0,
		Errors:       make([]types.TaskUpdateError, 0, len(req.IdList)), // Pre-allocate capacity
	}

	// Process each task update in parallel using goroutines
	type updateResult struct {
		taskID int
		err    error
	}

	resultChan := make(chan updateResult, len(req.IdList))

	// Process tasks in batch to avoid overwhelming the system
	const batchSize = 5
	for i := 0; i < len(req.IdList); i += batchSize {
		end := i + batchSize
		if end > len(req.IdList) {
			end = len(req.IdList)
		}

		// Process batch of tasks
		for _, taskID := range req.IdList[i:end] {
			go func(id int) {
				err := l.processAssignment(id, req)
				resultChan <- updateResult{taskID: id, err: err}
			}(taskID)
		}
	}

	// Collect results
	for i := 0; i < len(req.IdList); i++ {
		res := <-resultChan
		if res.err != nil {
			result.Errors = append(result.Errors, types.TaskUpdateError{
				TaskID: res.taskID,
				Error:  res.err.Error(),
			})
			result.FailCount++
		} else {
			result.SuccessCount++
		}
	}

	if result.SuccessCount == 0 {
		return nil, types.ErrTaskBulkUpdate.Wrap(nil)
	}

	return result, nil

}

// processTaskUpdate handles the update operation for a single task
func (l *AssignTasksLogic) processAssignment(taskID int, req *types.AssignTasksRequest) error {
	unlock, err := keylock.Global.Lock(5*time.Second, taskID)
	if err != nil {
		return types.ErrPersonData.Wrap(err, "failed to acquire lock")
	}
	defer unlock()

	return bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		// Fetch task with related data
		taskRecord, err := model.FetchTaskWithRelations(l.ctx, tx, taskID)
		if err != nil {
			return err
		}

		dueAt := model.GetDueAt(taskRecord)

		if len(req.Users) > 0 {
			// Separate users by role
			primaryUsers, contributorUsers, reviewerUsers := separateUsersByRole(req.Users)

			// Handle primary assignments if there are new primary users
			if len(primaryUsers) > 0 {
				if err := replaceRoleAssignments(l.ctx, tx, taskRecord, primaryUsers, "primary", dueAt); err != nil {
					return types.ErrTaskUpdate.Wrap(err, "Error updating primary assignments")
				}
			}

			// Handle contributor assignments if there are new contributor users
			if len(contributorUsers) > 0 {
				if err := replaceRoleAssignments(l.ctx, tx, taskRecord, contributorUsers, "contributor", dueAt); err != nil {
					return types.ErrTaskUpdate.Wrap(err, "Error updating contributor assignments")
				}
			}

			// Handle reviewer assignments if there are new reviewer users
			if len(reviewerUsers) > 0 {
				if err := replaceRoleAssignments(l.ctx, tx, taskRecord, reviewerUsers, "reviewer", dueAt); err != nil {
					return types.ErrTaskUpdate.Wrap(err, "Error updating reviewer assignments")
				}
			}
		}

		return nil
	})
}

func separateUsersByRole(users []types.TaskAssignmentAddRequest) (primary, contributor, reviewer []types.TaskAssignmentAddRequest) {
	for _, user := range users {
		switch user.Role {
		case "primary":
			primary = append(primary, user)
		case "contributor":
			contributor = append(contributor, user)
		case "reviewer":
			reviewer = append(reviewer, user)
		}
	}
	return primary, contributor, reviewer
}

func replaceRoleAssignments(ctx context.Context, tx *ent.Tx, taskRecord *ent.Task, newUsers []types.TaskAssignmentAddRequest, role string, dueAt time.Time) error {
	// Delete all existing assignments for this role
	_, err := tx.TaskAssignment.Delete().
		Where(
			taskassignment.And(
				taskassignment.TaskID(taskRecord.ID),
				taskassignment.Serial(taskRecord.CurrentSerial),
				taskassignment.RoleEQ(taskassignment.Role(role)),
			),
		).Exec(ctx)
	if err != nil {
		return fmt.Errorf("failed to delete existing %s assignments: %w", role, err)
	}

	// Create new assignments in bulk
	bulkCreate := make([]*ent.TaskAssignmentCreate, len(newUsers))
	for i, newUser := range newUsers {
		taskAssignRecord := &ent.TaskAssignment{
			TaskID: taskRecord.ID,
			UserID: newUser.UserId,
			Role:   taskassignment.Role(role),
			DueAt:  dueAt,
			Serial: taskRecord.CurrentSerial,
		}

		bulkCreate[i] = tx.TaskAssignment.Create().
			SetTaskAssignment(taskAssignRecord)
	}

	// Execute bulk create
	if len(bulkCreate) > 0 {
		err = tx.TaskAssignment.CreateBulk(bulkCreate...).Exec(ctx)
		if err != nil {
			return fmt.Errorf("failed to create new %s assignments: %w", role, err)
		}
	}

	return nil
}
