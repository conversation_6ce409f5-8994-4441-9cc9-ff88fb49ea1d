package economy_payment

import (
	"bcare/api/internal/auth"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/bconst"
	"bcare/common/bquery"
	cast2 "bcare/common/cast"
	"bcare/common/ctxdata"
	"bcare/ent"
	"bcare/ent/bill"
	"bcare/ent/billitem"
	"bcare/ent/installment"
	"bcare/ent/paymentallocation"
	"context"
	"fmt"
	"github.com/jinzhu/copier"
	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateLogic(ctx context.Context, svcCtx *svc.ServiceContext) UpdateLogic {
	return UpdateLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateLogic) Update(req *types.PaymentUpdateRequest) (resp *types.PaymentResponse, err error) {
	var paymentRecord *ent.Payment
	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		// Get existing payment
		paymentRecord, err = tx.Payment.Get(l.ctx, req.Id)
		if err != nil {
			if ent.IsNotFound(err) {
				return types.ErrPaymentNotFound.Wrap(err, "Payment ID %d not found", req.Id)
			}
			return err
		}

		// Check permissions
		if !l.svcCtx.Auth.EconomyPayment.CanUpdate(l.ctx, paymentRecord) {
			logx.WithContext(l.ctx).Infof("Permission denied")
			return auth.ErrAppointmentUpdateDenied
		}

		// If there are allocation updates todo : làm sau
		//if len(req.PaymentAllocations) > 0 && butils.Contains(req.Modified, "allocations") {
		//	// Get all required data
		//	attachmentIDs, billItemsData, installmentsData, err := l.fetchPaymentData(tx, req.PaymentAllocations)
		//	if err != nil {
		//		return err
		//	}
		//
		//	// Validate new allocations
		//	if err := l.validateAllocations(billItemsData, installmentsData, req.PaymentAllocations); err != nil {
		//		return err
		//	}
		//
		//	// Delete existing allocations
		//	if _, err := tx.PaymentAllocation.Delete().
		//		Where(paymentallocation.PaymentID(req.Id)).
		//		Exec(l.ctx); err != nil {
		//		return types.ErrPaymentAllocationDelete.Wrap(err, "Failed to delete existing allocations")
		//	}
		//
		//	// Create new allocations
		//	installmentIDs, err := l.createAllocations(tx, req.Id, req.PaymentAllocations)
		//	if err != nil {
		//		return err
		//	}
		//
		//	// Update related data
		//	if len(installmentIDs) > 0 {
		//		if err := model.UpdateInstallmentsAndRelatedData(l.ctx, tx, installmentIDs); err != nil {
		//			return err
		//		}
		//	}
		//
		//	if len(attachmentIDs) > 0 {
		//		if err := model.UpdateAttachmentData(l.ctx, tx, attachmentIDs); err != nil {
		//			return err
		//		}
		//	}
		//
		//	// Handle bill updates if needed
		//	if err := l.handleBillCreation(tx, billItemsData); err != nil {
		//		return err
		//	}
		//}

		originPayment := new(ent.Payment)
		_ = copier.Copy(originPayment, paymentRecord)

		err = copier.CopyWithOption(&paymentRecord, req, cast2.PayloadToEnt)
		if err != nil {
			return types.ErrPaymentUpdate.Wrap(err, "Error copying payment  data")
		}
		if (paymentRecord.Cash + paymentRecord.Bank + paymentRecord.CreditCard + paymentRecord.Mpos + paymentRecord.Momo) != originPayment.TotalAmount {
			return types.ErrPaymentUpdate.Wrap(nil, "Tổng phương thức thanh toán khác thành tiền hoá đơn")
		}
		// Update payment record
		paymentRecord, err = tx.Payment.UpdateOneID(req.Id).SetPayment(paymentRecord, req.Modified...).Save(l.ctx)
		if err != nil {
			return types.ErrPaymentUpdate.Wrap(err, "Failed to update payment")
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	resp = new(types.PaymentResponse)
	_ = cast2.ConvertViaJson(&resp, paymentRecord)
	return resp, nil
}

func (l *UpdateLogic) fetchPaymentData(tx *ent.Tx, allocations []types.PaymentAllocationUpdateRequest) ([]int, map[int]*billItemData, map[int]*installmentData, error) {
	// Collect all IDs
	billItemIDs := make([]int, 0)
	installmentIDs := make([]int, 0)
	for _, allocation := range allocations {
		if allocation.BillItemId > 0 {
			billItemIDs = append(billItemIDs, allocation.BillItemId)
		}
		if allocation.InstallmentId > 0 {
			installmentIDs = append(installmentIDs, allocation.InstallmentId)
		}
	}

	// Fetch bill items data
	billItemsData, attachmentIds, err := l.fetchBillItemsData(tx, billItemIDs)
	if err != nil {
		return nil, nil, nil, err
	}

	// Fetch installments data
	installmentsData, err := l.fetchInstallmentsData(tx, installmentIDs)
	if err != nil {
		return nil, nil, nil, err
	}

	return attachmentIds, billItemsData, installmentsData, nil
}

func (l *UpdateLogic) validateAllocations(
	itemsData map[int]*billItemData,
	installmentsData map[int]*installmentData,
	allocations []types.PaymentAllocationUpdateRequest,
) error {
	for _, allocation := range allocations {
		// Validate bill item allocation
		if allocation.BillItemId > 0 {
			itemData, exists := itemsData[allocation.BillItemId]
			if !exists {
				return types.ErrBillItemNotFound.Wrap(nil, fmt.Sprintf("Bill item not found: %d", allocation.BillItemId))
			}

			if itemData.TotalAllocated+allocation.Amount > itemData.Amount {
				return types.ErrPaymentAllocationAdd.Wrap(nil,
					fmt.Sprintf("Total allocations (%f) would exceed bill item amount (%f) for item %d",
						itemData.TotalAllocated+allocation.Amount, itemData.Amount, itemData.ID))
			}
		}

		// Validate installment allocation
		if allocation.InstallmentId > 0 {
			instData, exists := installmentsData[allocation.InstallmentId]
			if !exists {
				return types.ErrInstallmentNotFound.Wrap(nil, fmt.Sprintf("Installment not found: %d", allocation.InstallmentId))
			}

			if instData.TotalAllocated+allocation.Amount > instData.Amount {
				return types.ErrPaymentAllocationAdd.Wrap(nil,
					fmt.Sprintf("Total allocations (%f) would exceed installment amount (%f) for installment %d",
						instData.TotalAllocated+allocation.Amount, instData.Amount, instData.ID))
			}
		}

		if (allocation.BillItemId > 0 && allocation.InstallmentId > 0) ||
			(allocation.BillItemId == 0 && allocation.InstallmentId == 0) {
			return types.ErrPaymentAllocationAdd.Wrap(nil,
				"Must provide either bill_item_id or installment_id, but not both")
		}
	}
	return nil
}

func (l *UpdateLogic) createAllocations(tx *ent.Tx, paymentID int, allocations []types.PaymentAllocationUpdateRequest) (installmentIDs []int, err error) {
	builders := make([]*ent.PaymentAllocationCreate, len(allocations))
	userID := ctxdata.GetUidFromCtx(l.ctx)

	for i, allocation := range allocations {
		allocationRecord := new(ent.PaymentAllocation)
		_ = copier.Copy(&allocationRecord, allocation)

		allocationRecord.UserID = userID
		allocationRecord.PaymentID = paymentID
		builders[i] = tx.PaymentAllocation.Create().
			SetPaymentAllocation(allocationRecord)
		if allocationRecord.InstallmentID != nil && *allocationRecord.InstallmentID != 0 {
			installmentIDs = append(installmentIDs, *allocationRecord.InstallmentID)
		}
	}

	return installmentIDs, tx.PaymentAllocation.CreateBulk(builders...).Exec(l.ctx)
}

func (l *UpdateLogic) handleBillCreation(tx *ent.Tx, itemsData map[int]*billItemData) error {
	// Group items without bills by person
	itemsByPerson := make(map[int][]int)
	for id, item := range itemsData {
		if item.BillID == nil {
			itemsByPerson[item.PersonID] = append(itemsByPerson[item.PersonID], id)
		}
	}

	// Create bills for each person's items
	userID := ctxdata.GetUidFromCtx(l.ctx)
	for personID, itemIDs := range itemsByPerson {
		// Calculate bill state
		state := l.calculateBillState(itemsData, itemIDs)

		// Create bill
		newBill, err := tx.Bill.Create().
			SetUserID(userID).
			SetPersonID(personID).
			SetState(state).
			Save(l.ctx)
		if err != nil {
			return types.ErrBillAdd.Wrap(err, "Error creating new bill")
		}

		// Bulk update bill items
		err = tx.BillItem.Update().
			Where(billitem.IDIn(itemIDs...)).
			SetBillID(newBill.ID).
			Exec(l.ctx)
		if err != nil {
			return types.ErrBillAdd.Wrap(err, "Error updating bill items")
		}
	}

	return nil
}

func (l *UpdateLogic) fetchBillItemsData(tx *ent.Tx, billItemIDs []int) (map[int]*billItemData, []int, error) {
	if len(billItemIDs) == 0 {
		return make(map[int]*billItemData), nil, nil
	}

	// Fetch bill items with attachments in one query
	billItems, err := tx.BillItem.Query().
		Where(billitem.IDIn(billItemIDs...)).
		WithAttachment().
		All(l.ctx)
	if err != nil {
		return nil, nil, types.ErrBillItemNotFound.Wrap(err, "Failed to fetch bill items")
	}

	// Fetch existing allocations in one query
	type sumResult struct {
		BillItemID int     `sql:"bill_item_id"`
		Sum        float64 `sql:"sum"`
	}
	var allocSums []sumResult
	err = tx.PaymentAllocation.Query().
		Where(paymentallocation.BillItemIDIn(billItemIDs...)).
		GroupBy(paymentallocation.FieldBillItemID).
		Aggregate(
			ent.As(ent.Sum(paymentallocation.FieldAmount), "sum"),
		).
		Scan(l.ctx, &allocSums)
	if err != nil && !ent.IsNotFound(err) {
		return nil, nil, types.ErrPaymentAllocationGetList.Wrap(err, "Failed to fetch allocations")
	}

	// Build result map
	result := make(map[int]*billItemData)
	allocSumsMap := make(map[int]float64)
	for _, sum := range allocSums {
		allocSumsMap[sum.BillItemID] = sum.Sum
	}

	attachmentIDs := make([]int, 0)
	for _, item := range billItems {
		if item.Edges.Attachment == nil {
			continue
		}

		result[item.ID] = &billItemData{
			BillItem:       item,
			TotalAllocated: allocSumsMap[item.ID],
			PersonID:       item.Edges.Attachment.PersonID,
		}

		// Only collect attachment IDs with status greater than normal
		if item.Edges.Attachment.Status > bconst.StatusNormal {
			attachmentIDs = append(attachmentIDs, item.Edges.Attachment.ID)
		}
	}

	return result, attachmentIDs, nil
}

func (l *UpdateLogic) fetchInstallmentsData(tx *ent.Tx, installmentIDs []int) (map[int]*installmentData, error) {
	if len(installmentIDs) == 0 {
		return make(map[int]*installmentData), nil
	}

	// Fetch installments
	installments, err := tx.Installment.Query().
		Where(installment.IDIn(installmentIDs...)).
		All(l.ctx)
	if err != nil {
		return nil, types.ErrInstallmentNotFound.Wrap(err, "Failed to fetch installments")
	}

	// Fetch existing allocations for installments
	type sumResult struct {
		InstallmentID int     `sql:"installment_id"`
		Sum           float64 `sql:"sum"`
	}
	var allocSums []sumResult
	err = tx.PaymentAllocation.Query().
		Where(paymentallocation.InstallmentIDIn(installmentIDs...)).
		GroupBy(paymentallocation.FieldInstallmentID).
		Aggregate(
			ent.As(ent.Sum(paymentallocation.FieldAmount), "sum"),
		).
		Scan(l.ctx, &allocSums)
	if err != nil && !ent.IsNotFound(err) {
		return nil, types.ErrPaymentAllocationGetList.Wrap(err, "Failed to fetch installment allocations")
	}

	// Build result map
	result := make(map[int]*installmentData)
	allocSumsMap := make(map[int]float64)
	for _, sum := range allocSums {
		allocSumsMap[sum.InstallmentID] = sum.Sum
	}

	for _, inst := range installments {
		result[inst.ID] = &installmentData{
			Installment:    inst,
			TotalAllocated: allocSumsMap[inst.ID],
		}
	}

	return result, nil
}

func (l *UpdateLogic) calculateBillState(itemsData map[int]*billItemData, itemIDs []int) bill.State {
	var totalAmount, totalAllocated float64
	for _, id := range itemIDs {
		if item, exists := itemsData[id]; exists {
			totalAmount += item.Amount
			totalAllocated += item.TotalAllocated
		}
	}

	if totalAllocated >= totalAmount {
		return bill.StatePaid
	} else if totalAllocated > 0 {
		return bill.StatePartiallyPaid
	}
	return bill.StateActive
}
