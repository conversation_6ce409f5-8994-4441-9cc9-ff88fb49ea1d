package economy_payment

import (
	"bcare/api/internal/auth"
	"bcare/api/internal/svc"
	"bcare/api/internal/task"
	"bcare/api/internal/task/tasks"
	"bcare/api/internal/types"
	"bcare/api/model"
	"bcare/common/bconst"
	"bcare/common/bquery"
	"bcare/common/bsms"
	cast2 "bcare/common/cast"
	"bcare/common/ctxdata"
	"bcare/ent"
	"bcare/ent/bill"
	"bcare/ent/billitem"
	"bcare/ent/installment"
	"bcare/ent/messagehistory"
	"bcare/ent/paymentallocation"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"github.com/hibiken/asynq"
	"github.com/jinzhu/copier"

	"github.com/zeromicro/go-zero/core/logx"
)

type AddLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAddLogic(ctx context.Context, svcCtx *svc.ServiceContext) AddLogic {
	return AddLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// Helper structs
type billItemData struct {
	*ent.BillItem
	TotalAllocated float64
	PersonID       int
	AttachmentId   int
	HasAllocations bool // Thêm trường để đánh dấu item đã có payment_allocation trước đó chưa
}

type installmentData struct {
	*ent.Installment
	TotalAllocated float64
	HasAllocations bool // Thêm trường để đánh dấu installment đã có payment_allocation trước đó chưa
}

func (l *AddLogic) Add(req *types.PaymentAddRequest) (*types.PaymentResponse, error) {
	//kiểm tra xem có quyền ko
	if !l.svcCtx.Auth.EconomyPayment.CanAdd(l.ctx, nil) {
		logx.WithContext(l.ctx).Infof("Bạn ko có quyền")
		return nil, auth.ErrAppointmentAddDenied
	}

	var paymentRecord *ent.Payment
	err := bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		var err error

		activeTrack, err := model.GetActiveTrack(l.ctx, tx, req.PersonId)
		if err != nil || activeTrack == nil {
			return errors.New(fmt.Sprintf("active track not found for person ID %d", req.PersonId))
		}
		// Get all required data in one go
		attachmentIDs, billItemsData, installmentsData, err := l.fetchPaymentData(tx, req.PaymentAllocations)
		if err != nil {
			return err
		}

		// Validate allocations
		if len(req.PaymentAllocations) > 0 {
			if err := l.validateAllocations(billItemsData, installmentsData, req.PaymentAllocations); err != nil {
				return err
			}
		}

		// Create payment
		paymentRecord, err = l.createPayment(tx, req)
		if err != nil {
			return err
		}

		// Create allocations
		var installmentIDs []int
		if len(req.PaymentAllocations) > 0 {
			if installmentIDs, err = l.createAllocations(tx, paymentRecord.ID, req.PaymentAllocations, billItemsData, installmentsData); err != nil {
				return err
			}
		}

		if len(installmentIDs) > 0 {
			if err := model.UpdateInstallmentsAndRelatedData(l.ctx, tx, installmentIDs); err != nil {
				return err
			}
		}

		if len(attachmentIDs) > 0 {
			if err := model.UpdateAttachmentData(l.ctx, tx, attachmentIDs); err != nil {
				return err
			}
		}
		// Handle bill creation if needed
		if err := l.handleBillCreation(tx, billItemsData); err != nil {
			return err
		}

		if req.SendSms == 0 {
			paymentMsg := l.svcCtx.Setting.PaymentMessage
			messageReq, hasZalo, err := model.SendPaymentMessage(l.ctx, l.svcCtx.Ent, paymentMsg, paymentRecord, paymentRecord.PersonID, l.svcCtx.Config.Env, l.svcCtx.Config.PhoneTest)
			if err == nil {
				userID := ctxdata.GetUidFromCtx(l.ctx)

				// Handle message based on Zalo availability
				_, err = l.handleMessageWithZaloCheck(messageReq, userID, hasZalo)
			}
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	resp := new(types.PaymentResponse)
	_ = cast2.ConvertViaJson(&resp, paymentRecord)
	return resp, nil
}

func (l *AddLogic) fetchPaymentData(tx *ent.Tx, allocations []types.PaymentAllocationAddRequest) ([]int, map[int]*billItemData, map[int]*installmentData, error) {
	// Collect all IDs
	billItemIDs := make([]int, 0)
	installmentIDs := make([]int, 0)
	for _, allocation := range allocations {
		if allocation.BillItemId > 0 {
			billItemIDs = append(billItemIDs, allocation.BillItemId)
		}
		if allocation.InstallmentId > 0 {
			installmentIDs = append(installmentIDs, allocation.InstallmentId)
		}
	}

	// Fetch bill items data
	billItemsData, attachmentIds, err := l.fetchBillItemsData(tx, billItemIDs)
	if err != nil {
		return nil, nil, nil, err
	}

	// Fetch installments data
	installmentsData, err := l.fetchInstallmentsData(tx, installmentIDs)
	if err != nil {
		return nil, nil, nil, err
	}

	return attachmentIds, billItemsData, installmentsData, nil
}

func (l *AddLogic) fetchBillItemsData(tx *ent.Tx, billItemIDs []int) (map[int]*billItemData, []int, error) {
	if len(billItemIDs) == 0 {
		return make(map[int]*billItemData), nil, nil
	}

	// Fetch bill items with attachments in one query
	billItems, err := tx.BillItem.Query().
		Where(billitem.IDIn(billItemIDs...)).
		WithAttachment().
		All(l.ctx)
	if err != nil {
		return nil, nil, types.ErrBillItemNotFound.Wrap(err, "Failed to fetch bill items")
	}

	// Fetch existing allocations in one query
	type sumResult struct {
		BillItemID int     `sql:"bill_item_id"`
		Sum        float64 `sql:"sum"`
	}
	var allocSums []sumResult
	err = tx.PaymentAllocation.Query().
		Where(paymentallocation.BillItemIDIn(billItemIDs...)).
		GroupBy(paymentallocation.FieldBillItemID).
		Aggregate(
			ent.As(ent.Sum(paymentallocation.FieldAmount), "sum"),
		).
		Scan(l.ctx, &allocSums)
	if err != nil && !ent.IsNotFound(err) {
		return nil, nil, types.ErrPaymentAllocationGetList.Wrap(err, "Failed to fetch allocations")
	}

	// Kiểm tra xem bill item nào đã có payment allocations trước đó
	billItemWithAllocations := make(map[int]bool)
	existingAllocations, err := tx.PaymentAllocation.Query().
		Where(paymentallocation.BillItemIDIn(billItemIDs...)).
		All(l.ctx)
	if err != nil && !ent.IsNotFound(err) {
		return nil, nil, types.ErrPaymentAllocationGetList.Wrap(err, "Failed to check existing allocations")
	}

	for _, alloc := range existingAllocations {
		if alloc.BillItemID != nil {
			billItemWithAllocations[*alloc.BillItemID] = true
		}
	}

	// Build result map
	result := make(map[int]*billItemData)
	allocSumsMap := make(map[int]float64)
	for _, sum := range allocSums {
		allocSumsMap[sum.BillItemID] = sum.Sum
	}

	attachmentIDs := make([]int, 0)
	for _, item := range billItems {
		if item.Edges.Attachment == nil {
			continue
		}

		result[item.ID] = &billItemData{
			BillItem:       item,
			TotalAllocated: allocSumsMap[item.ID],
			PersonID:       item.Edges.Attachment.PersonID,
			HasAllocations: billItemWithAllocations[item.ID], // Đánh dấu đã có payment allocation trước đó chưa
		}

		// Only collect attachment IDs with status greater than normal
		if item.Edges.Attachment.Status > bconst.StatusNormal {
			attachmentIDs = append(attachmentIDs, item.Edges.Attachment.ID)
		}
	}

	return result, attachmentIDs, nil
}

func (l *AddLogic) fetchInstallmentsData(tx *ent.Tx, installmentIDs []int) (map[int]*installmentData, error) {
	if len(installmentIDs) == 0 {
		return make(map[int]*installmentData), nil
	}

	// Fetch installments
	installments, err := tx.Installment.Query().
		Where(installment.IDIn(installmentIDs...)).
		All(l.ctx)
	if err != nil {
		return nil, types.ErrInstallmentNotFound.Wrap(err, "Failed to fetch installments")
	}

	// Fetch existing allocations for installments
	type sumResult struct {
		InstallmentID int     `sql:"installment_id"`
		Sum           float64 `sql:"sum"`
	}
	var allocSums []sumResult
	err = tx.PaymentAllocation.Query().
		Where(paymentallocation.InstallmentIDIn(installmentIDs...)).
		GroupBy(paymentallocation.FieldInstallmentID).
		Aggregate(
			ent.As(ent.Sum(paymentallocation.FieldAmount), "sum"),
		).
		Scan(l.ctx, &allocSums)
	if err != nil && !ent.IsNotFound(err) {
		return nil, types.ErrPaymentAllocationGetList.Wrap(err, "Failed to fetch installment allocations")
	}

	// Kiểm tra xem installment nào đã có payment allocations trước đó
	installmentWithAllocations := make(map[int]bool)

	// Lấy danh sách các installment đã có payment_allocation
	existingAllocations, err := tx.PaymentAllocation.Query().
		Where(paymentallocation.InstallmentIDIn(installmentIDs...)).
		All(l.ctx)
	if err != nil && !ent.IsNotFound(err) {
		return nil, types.ErrPaymentAllocationGetList.Wrap(err, "Failed to check existing allocations")
	}

	// Đánh dấu các installment đã có payment_allocation
	for _, alloc := range existingAllocations {
		if alloc.InstallmentID != nil {
			installmentWithAllocations[*alloc.InstallmentID] = true
		}
	}

	// Build result map
	result := make(map[int]*installmentData)
	allocSumsMap := make(map[int]float64)
	for _, sum := range allocSums {
		allocSumsMap[sum.InstallmentID] = sum.Sum
	}

	for _, inst := range installments {
		result[inst.ID] = &installmentData{
			Installment:    inst,
			TotalAllocated: allocSumsMap[inst.ID],
			HasAllocations: installmentWithAllocations[inst.ID], // Đánh dấu đã có payment allocation trước đó chưa
		}
	}

	return result, nil
}

func (l *AddLogic) validateAllocations(
	itemsData map[int]*billItemData,
	installmentsData map[int]*installmentData,
	allocations []types.PaymentAllocationAddRequest,
) error {
	for _, allocation := range allocations {
		// Validate bill item allocation
		if allocation.BillItemId > 0 {
			itemData, exists := itemsData[allocation.BillItemId]
			if !exists {
				return types.ErrBillItemNotFound.Wrap(nil, fmt.Sprintf("Bill item not found: %d", allocation.BillItemId))
			}

			if itemData.TotalAllocated+allocation.Amount > itemData.Amount {
				return types.ErrPaymentAllocationAdd.Wrap(nil,
					fmt.Sprintf("Total allocations (%f) would exceed bill item amount (%f) for item %d",
						itemData.TotalAllocated+allocation.Amount, itemData.Amount, itemData.ID))
			}
		}

		// Validate installment allocation
		if allocation.InstallmentId > 0 {
			instData, exists := installmentsData[allocation.InstallmentId]
			if !exists {
				return types.ErrInstallmentNotFound.Wrap(nil, fmt.Sprintf("Installment not found: %d", allocation.InstallmentId))
			}

			if instData.TotalAllocated+allocation.Amount > instData.Amount {
				return types.ErrPaymentAllocationAdd.Wrap(nil,
					fmt.Sprintf("Total allocations (%f) would exceed installment amount (%f) for installment %d",
						instData.TotalAllocated+allocation.Amount, instData.Amount, instData.ID))
			}
		}

		// Validate that either bill_item_id or installment_id is provided, but not both
		if (allocation.BillItemId > 0 && allocation.InstallmentId > 0) ||
			(allocation.BillItemId == 0 && allocation.InstallmentId == 0) {
			return types.ErrPaymentAllocationAdd.Wrap(nil,
				"Must provide either bill_item_id or installment_id, but not both")
		}
	}
	return nil
}

func (l *AddLogic) createPayment(tx *ent.Tx, req *types.PaymentAddRequest) (*ent.Payment, error) {
	payment := new(ent.Payment)
	if err := copier.CopyWithOption(&payment, req, cast2.PayloadToEnt); err != nil {
		return nil, types.ErrPaymentCopyFailed.Wrap(err, "Error copying request data")
	}

	if (payment.Cash + payment.Bank + payment.CreditCard + payment.Mpos + payment.Momo) != payment.TotalAmount {
		return nil, types.ErrPaymentUpdate.Wrap(nil, "Tổng phương thức thanh toán khác thành tiền hoá đơn")
	}

	if payment.PaymentDate.IsZero() {
		payment.PaymentDate = time.Now()
	}
	payment.UserID = ctxdata.GetUidFromCtx(l.ctx)

	return tx.Payment.Create().
		SetPayment(payment).
		Save(l.ctx)
}

func (l *AddLogic) createAllocations(
	tx *ent.Tx,
	paymentID int,
	allocations []types.PaymentAllocationAddRequest,
	billItemsData map[int]*billItemData,
	installmentsData map[int]*installmentData,
) (installmentIDs []int, err error) {
	builders := make([]*ent.PaymentAllocationCreate, len(allocations))
	userID := ctxdata.GetUidFromCtx(l.ctx)

	for i, allocation := range allocations {
		allocationRecord := new(ent.PaymentAllocation)
		_ = copier.Copy(&allocationRecord, allocation)

		allocationRecord.UserID = userID
		allocationRecord.PaymentID = paymentID

		// Xác định state của payment allocation dựa trên việc đã có thanh toán trước đó chưa
		if allocation.BillItemId > 0 {
			itemData, exists := billItemsData[allocation.BillItemId]
			if exists {
				if itemData.HasAllocations {
					allocationRecord.State = "subsequent" // Đã có thanh toán trước đó
				} else {
					allocationRecord.State = "initial" // Đây là thanh toán đầu tiên
				}
			}
		} else if allocation.InstallmentId > 0 {
			instData, exists := installmentsData[allocation.InstallmentId]
			if exists {
				if instData.HasAllocations {
					allocationRecord.State = "subsequent" // Đã có thanh toán trước đó
				} else {
					allocationRecord.State = "initial" // Đây là thanh toán đầu tiên
				}
			}
		}

		builders[i] = tx.PaymentAllocation.Create().
			SetPaymentAllocation(allocationRecord)
		if allocationRecord.InstallmentID != nil && *allocationRecord.InstallmentID != 0 {
			installmentIDs = append(installmentIDs, *allocationRecord.InstallmentID)
		}
	}

	return installmentIDs, tx.PaymentAllocation.CreateBulk(builders...).Exec(l.ctx)
}

func (l *AddLogic) handleBillCreation(tx *ent.Tx, itemsData map[int]*billItemData) error {
	// Group items without bills by person
	itemsByPerson := make(map[int][]int)
	for id, item := range itemsData {
		if item.BillID == nil {
			itemsByPerson[item.PersonID] = append(itemsByPerson[item.PersonID], id)
		}
	}

	// Create bills for each person's items
	userID := ctxdata.GetUidFromCtx(l.ctx)
	for personID, itemIDs := range itemsByPerson {
		// Calculate bill state
		state := l.calculateBillState(itemsData, itemIDs)

		// Create bill
		newBill, err := tx.Bill.Create().
			SetUserID(userID).
			SetPersonID(personID).
			SetState(state).
			Save(l.ctx)
		if err != nil {
			return types.ErrBillAdd.Wrap(err, "Error creating new bill")
		}

		// Bulk update bill items
		err = tx.BillItem.Update().
			Where(billitem.IDIn(itemIDs...)).
			SetBillID(newBill.ID).
			Exec(l.ctx)
		if err != nil {
			return types.ErrBillAdd.Wrap(err, "Error updating bill items")
		}
	}

	return nil
}

func (l *AddLogic) calculateBillState(itemsData map[int]*billItemData, itemIDs []int) bill.State {
	var totalAmount, totalAllocated float64
	for _, id := range itemIDs {
		if item, exists := itemsData[id]; exists {
			totalAmount += item.Amount
			totalAllocated += item.TotalAllocated
		}
	}

	if totalAllocated >= totalAmount {
		return bill.StatePaid
	} else if totalAllocated > 0 {
		return bill.StatePartiallyPaid
	}
	return bill.StateActive
}

func (l *AddLogic) SendZns(req *types.SendMessageRequest) (err error, errCode int, msgId string) {
	var params bsms.TemplateData
	err = json.Unmarshal([]byte(req.ZnsParams), &params)
	if err != nil {
		return
	}

	znsTpl := &bsms.ZnsTemplate{
		Phone:        req.Phone,
		TemplateID:   req.ZnsTemplateId,
		TemplateData: params,
	}
	return l.svcCtx.Zns.SendMessage(znsTpl)
}

// New helper function to handle message with Zalo check
func (l *AddLogic) handleMessageWithZaloCheck(req *types.SendMessageRequest, userID int, hasZalo bool) (*types.SendMessageResponse, error) {
	if hasZalo {
		// Try sending ZNS first
		err, errCode, msgId := l.SendZns(req)
		logx.WithContext(l.ctx).Infof("ZNS sending result - Error: %v, ErrorCode: %d, MsgId: %s", err, errCode, msgId)

		// Create and save ZNS message history
		msgHistory := model.NewZnsMessageHistory(
			req.PersonId,
			req.Phone,
			userID,
			req.ZnsParams,
			req.ZnsContent,
			msgId,
			errCode,
		)

		msgHistory, _ = model.SaveMessageHistory(l.ctx, l.svcCtx.Ent, msgHistory)

		// Schedule update task if message sent successfully
		if msgHistory.MessageStatus == messagehistory.MessageStatusSent {
			if err := l.scheduleZnsUpdateTask(msgHistory.MessageID, req.Phone); err != nil {
				logx.WithContext(l.ctx).Errorf("Failed to schedule ZNS update task: %v", err)
			}
			return &types.SendMessageResponse{}, nil
		}

		// Fallback to SMS if ZNS fails
		//if err != nil {
		//	logx.WithContext(l.ctx).Infof("ZNS failed, falling back to SMS for phone: %s", req.Phone)
		//	return l.sendSmsMessage(req.PersonId, req.Phone, userID, req.SmsContent)
		//}
	}

	// Send SMS if no Zalo or ZNS failed
	return l.sendSmsMessage(req.PersonId, req.Phone, userID, req.SmsContent)
}

// Updated helper function for SMS
func (l *AddLogic) sendSmsMessage(personID int, phone string, userID int, content string) (*types.SendMessageResponse, error) {
	err, errCode := l.svcCtx.Sms.SendMessage(phone, content)
	logx.WithContext(l.ctx).Infof("SMS sending result - Error: %v, ErrorCode: %d", err, errCode)

	msgHistory := model.NewSmsMessageHistory(
		personID,
		phone,
		userID,
		content,
		errCode,
	)

	_, err = model.SaveMessageHistory(l.ctx, l.svcCtx.Ent, msgHistory)
	if err != nil {
		return nil, err
	}

	return &types.SendMessageResponse{}, nil
}

// Helper function to schedule ZNS update task
func (l *AddLogic) scheduleZnsUpdateTask(messageID, phone string) error {
	t, err := tasks.NewZnsUpdateTask(messageID, phone)
	if err != nil {
		return err
	}

	_, err = task.Client.Enqueue(t, asynq.ProcessIn(5*time.Minute))
	return err
}
