package economy_payment

import (
	"bcare/common/bquery"
	cast2 "bcare/common/cast"
	"bcare/ent"
	"bcare/ent/payment"
	"bcare/ent/product"
	"context"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetLogic(ctx context.Context, svcCtx *svc.ServiceContext) GetLogic {
	return GetLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetLogic) Get(req *types.PaymentGetRequest) (resp *types.PaymentResponse, err error) {
	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		// Query payment with relationships
		paymentRecord, e := tx.Payment.Query().
			WithBill().
			WithAllocations(
				func(q *ent.PaymentAllocationQuery) {
					q.WithInstallment().WithBillItem(func(q *ent.BillItemQuery) {
						q.WithUser().WithAllocations().WithAttachment(func(q *ent.AttachmentQuery) {
							q.WithProduct(func(q *ent.ProductQuery) {
								q.Select(product.FieldID, product.FieldName) // Only select ID and Name fields
							})
						})
					})
				}).
			Where(payment.ID(req.Id)).
			Only(l.ctx)

		if e != nil {
			err = types.ErrPaymentGetList.Wrap(e)
			return err
		}

		resp = new(types.PaymentResponse)
		_ = cast2.ConvertViaJson(resp, paymentRecord)

		//for _, a := range resp.PaymentAllocations {
		//	if a.BillItem.PaymentAllocations[0].Id == a.Id {
		//		a.State = "new"
		//	} else {
		//		a.State = "old"
		//	}
		//}
		return nil
	})

	return resp, err
}
