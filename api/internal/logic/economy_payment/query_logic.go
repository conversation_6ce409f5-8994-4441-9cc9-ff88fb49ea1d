package economy_payment

import (
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/bquery"
	"bcare/common/butils"
	cast2 "bcare/common/cast"
	"bcare/ent"
	"bcare/ent/schema"
	"context"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/jinzhu/copier"
	"github.com/xuri/excelize/v2"

	"github.com/zeromicro/go-zero/core/logx"
)

type QueryLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewQueryLogic(ctx context.Context, svcCtx *svc.ServiceContext) QueryLogic {
	return QueryLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *QueryLogic) Query(req *types.PaymentReportDynamicQuery) (resp *types.DynamicQueryResponse, err error) {
	resp = new(types.DynamicQueryResponse)

	dQuery := &bquery.DynamicQuery{}
	err = copier.Copy(dQuery, req)
	if err != nil {
		logx.WithContext(l.ctx).Errorf("unable to copy request: %v", err)
		return resp, err
	}

	query := l.svcCtx.Ent.PaymentReportView.Query().Modify(bquery.ApplyDynamicQuery(dQuery))

	result := map[string]any{}
	aliases := make(map[string]bool)
	for _, agg := range req.Aggregations {
		if agg.Alias != "" {
			aliases[agg.Alias] = true
		}
	}

	skipSoftDelete := schema.SkipSoftDelete(l.ctx)
	if !req.Export {
		if req.Limit == 1 {
			r, err := query.First(skipSoftDelete)
			if err != nil && !ent.IsNotFound(err) {
				logx.WithContext(l.ctx).Errorf("unable to fetch activity record: %v", err)
				return resp, err
			}

			if r != nil {

				for alias := range aliases {
					result[alias], _ = r.Value(alias)
				}
			}
		} else {
			rows, err := query.All(skipSoftDelete)
			if err != nil {
				logx.WithContext(l.ctx).Errorf("unable to fetch payment records: %v", err)
				return resp, err
			}
			resultList := make([]map[string]any, len(rows))
			for i, r := range rows {
				rowResult := make(map[string]any)
				rowResult["record"] = cast2.Atom(&rowResult, r)
				for alias := range aliases {
					rowResult[alias], _ = r.Value(alias)
				}
				resultList[i] = rowResult
			}
			result["rows"] = resultList
		}
	} else {
		rows, err := query.All(skipSoftDelete)
		if err != nil {
			logx.WithContext(l.ctx).Errorf("unable to fetch payment records: %v", err)
			return resp, err
		}

		fileUrl, err := l.exportToExcel(rows)
		if err != nil {
			return nil, fmt.Errorf("failed to export excel: %v", err)
		}
		result["file_url"] = fileUrl
	}

	resp.Result = result

	return

}

// indexToExcelColumn converts 0-based column index to Excel column name (A, B, ..., Z, AA, AB, ...)
func indexToExcelColumn(index int) string {
	if index < 26 {
		return string(rune('A' + index))
	}

	// For columns beyond Z, use AA, AB, AC, etc.
	firstChar := rune('A' + (index / 26) - 1)
	secondChar := rune('A' + (index % 26))
	return string(firstChar) + string(secondChar)
}

func (l *QueryLogic) exportToExcel(records []*ent.PaymentReportView) (string, error) {
	// Create new Excel file in memory
	f := excelize.NewFile()
	defer f.Close()

	// Create a new sheet
	sheetName := "Payment Report"
	f.SetSheetName("Sheet1", sheetName)

	// Create default style with Times New Roman
	defaultStyle, _ := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Family: "Times New Roman",
			Size:   12,
		},
	})

	// Create style for money columns
	moneyStyle, _ := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Family: "Times New Roman",
			Size:   12,
		},
		NumFmt: 3, // Format for thousand separator
	})

	// Create header style
	headerStyle, _ := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Family: "Times New Roman",
			Size:   12,
		},
		Fill: excelize.Fill{Type: "pattern", Color: []string{"#CCCCCC"}, Pattern: 1},
	})

	// Create summary style
	summaryStyle, _ := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Family: "Times New Roman",
			Size:   12,
		},
		Fill:   excelize.Fill{Type: "pattern", Color: []string{"#FFEB9C"}, Pattern: 1},
		NumFmt: 3,
	})

	// Set headers
	headers := []string{
		"Ngày tạo", "Mã hồ sơ", "Tên KH", "SĐT", "Địa chỉ", "Năm sinh", "Nghề nghiệp",
		"Nội dung thu phí", "Nhóm", "Thu", "Chi", "Tổng tiền", "Tiền mặt",
		"Chuyển khoản", "Mpos", "Thẻ", "Momo", "Sản phẩm",
		"Tổng quát", "Chỉnh nha",
		"Implant", "Gxtn", "Veneer", "Phục hình sứ", "Khác", "Bác sĩ phụ trách", "Người tạo",
	}

	// Apply default style to whole sheet
	lastCol := indexToExcelColumn(len(headers) - 1)
	f.SetCellStyle(sheetName, "A1", fmt.Sprintf("%s%d", lastCol, len(records)+2), defaultStyle)

	// Set headers and header style
	for i, header := range headers {
		cell := fmt.Sprintf("%s1", indexToExcelColumn(i))
		f.SetCellValue(sheetName, cell, header)
		f.SetCellStyle(sheetName, cell, cell, headerStyle)
	}

	// Write data
	for i, record := range records {
		row := i + 2 // Start from row 2 (after headers)
		doctorName := ""
		if record.DoctorName != nil {
			doctorName = *record.DoctorName
		}

		creatorName := ""
		if record.CreatorName != nil {
			creatorName = *record.CreatorName
		}

		// Handle address formatting
		address := ""
		addressParts := make([]string, 0)

		if record.AddressNumber != "" {
			addressParts = append(addressParts, record.AddressNumber)
		}
		if record.Ward != "" {
			addressParts = append(addressParts, record.Ward)
		}
		if record.District != "" {
			addressParts = append(addressParts, record.District)
		}
		if record.Province != "" {
			addressParts = append(addressParts, record.Province)
		}

		if len(addressParts) > 0 {
			address = strings.Join(addressParts, ", ")
		}

		data := []interface{}{
			record.CreatedAt.Format(l.svcCtx.Config.General.DateTimeFormat),
			record.PersonCode,
			record.FullName,
			butils.MaskPhone(record.Phone, l.ctx, l.svcCtx.Enforcer),
			address,
			record.Birthday,
			record.JobName,
			record.ProductNames,
			record.GroupNames,
			record.Income,
			record.Expense,
			record.TotalAmount,
			record.Cash,
			record.Bank,
			record.Mpos,
			record.CreditCard,
			record.Momo,
			record.ProductAmount,
			record.GeneralServiceAmount,
			record.OrthodonticAmount,
			record.ImplantAmount,
			record.GxtnAmount,
			record.VeneerAmount,
			record.PhsAmount,
			record.OtherAmount,
			doctorName,
			creatorName,
		}

		for j, value := range data {
			cell := fmt.Sprintf("%s%d", indexToExcelColumn(j), row)
			f.SetCellValue(sheetName, cell, value)

			// Apply money style for columns H to V (index 7 to 21)
			if j >= 9 && j <= 24 {
				f.SetCellStyle(sheetName, cell, cell, moneyStyle)
			}
		}
	}

	// Calculate and add summary row
	summaryRow := len(records) + 2

	// Add "Tổng cộng" label
	f.SetCellValue(sheetName, fmt.Sprintf("A%d", summaryRow), "Tổng cộng")

	// Calculate sums for specific columns
	sumColumns := map[int]float64{}
	for _, record := range records {
		values := []float64{
			record.Income,
			record.Expense,
			record.TotalAmount,
			record.Cash,
			record.Bank,
			record.Mpos,
			record.CreditCard,
			record.Momo,
			record.ProductAmount,
			record.GeneralServiceAmount,
			record.OrthodonticAmount,
			record.ImplantAmount,
			record.GxtnAmount,
			record.VeneerAmount,
			record.PhsAmount,
			record.OtherAmount,
		}

		for j, value := range values {
			sumColumns[j+9] += value
		}
	}

	// Write sums to the summary row
	for colIndex, sum := range sumColumns {
		cell := fmt.Sprintf("%s%d", indexToExcelColumn(colIndex), summaryRow)
		f.SetCellValue(sheetName, cell, sum)
		f.SetCellStyle(sheetName, cell, cell, summaryStyle)
	}

	// Style the "Tổng cộng" cell
	f.SetCellStyle(sheetName, fmt.Sprintf("A%d", summaryRow), fmt.Sprintf("A%d", summaryRow), summaryStyle)

	// Auto-fit column width
	for i := range headers {
		col := indexToExcelColumn(i)
		f.SetColWidth(sheetName, col, col, 20)
	}

	// Create directory structure based on date
	dirPath, err := createDirStructure(l.svcCtx.Config.Storage.Local.Path, "reports")
	if err != nil {
		return "", fmt.Errorf("failed to create directory structure: %v", err)
	}

	// Generate filename with timestamp
	timestamp := time.Now().Format("20060102_150405")
	fileName := fmt.Sprintf("payment_report_%s.xlsx", timestamp)
	filePath := filepath.Join(dirPath, fileName)

	// Save Excel file to disk
	if err := f.SaveAs(filePath); err != nil {
		return "", fmt.Errorf("failed to save excel file: %v", err)
	}

	// Generate public URL for download
	publicUrl := fmt.Sprintf("/reports/%s/%s",
		time.Now().Format("2006_01_02"),
		fileName)

	return publicUrl, nil
}

func createDirStructure(basePath, kind string) (string, error) {
	now := time.Now()
	dirPath := filepath.Join(basePath, kind, fmt.Sprintf("%d_%02d_%02d", now.Year(), now.Month(), now.Day()))

	err := os.MkdirAll(dirPath, 0755)
	if err != nil {
		return "", err
	}

	return dirPath, nil
}
