package economy_payment

import (
	"bcare/api/internal/auth"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/bconst"
	"bcare/common/bquery"
	"bcare/ent"
	"bcare/ent/billitem"
	"bcare/ent/paymentallocation"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewDeleteLogic(ctx context.Context, svcCtx *svc.ServiceContext) DeleteLogic {
	return DeleteLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DeleteLogic) Delete(req *types.PaymentDeleteRequest) (resp *types.PaymentResponse, err error) {
	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		paymentRecord, err := tx.Payment.Get(l.ctx, req.Id)
		if err != nil && ent.IsNotFound(err) {
			return types.ErrPaymentNotFound.Wrap(err)
		}

		if !l.svcCtx.Auth.EconomyPayment.CanDelete(l.ctx, paymentRecord) {
			logx.WithContext(l.ctx).Infof("Bạn ko có quyền")
			return auth.ErrAppointmentAddDenied
		}

		// Lưu lại danh sách bill_item_id và installment_id trước khi xóa allocation
		billItemIDs, err := tx.PaymentAllocation.Query().
			Where(
				paymentallocation.PaymentID(req.Id),
				paymentallocation.BillItemIDNotNil(),
			).
			Select(paymentallocation.FieldBillItemID).
			Ints(l.ctx)
		if err != nil {
			return err
		}

		// Lấy danh sách installment_id và state để kiểm tra rollback paid_at
		installmentAllocations, err := tx.PaymentAllocation.Query().
			Where(
				paymentallocation.PaymentID(req.Id),
				paymentallocation.InstallmentIDNotNil(),
			).
			Select(paymentallocation.FieldInstallmentID, paymentallocation.FieldState).
			All(l.ctx)
		if err != nil {
			return err
		}

		paymentRecord.Status = bconst.StatusDeleted
		err = tx.Payment.DeleteOne(paymentRecord).Exec(l.ctx)
		if err != nil {
			return types.ErrPaymentDelete.Wrap(err)
		}

		// Delete existing allocations
		if _, err := tx.PaymentAllocation.Delete().
			Where(paymentallocation.PaymentID(req.Id)).
			Exec(l.ctx); err != nil {
			return types.ErrPaymentAllocationDelete.Wrap(err, "Failed to delete existing allocations")
		}

		// Handle installment rollback nếu cần
		if err := l.handleInstallmentRollback(tx, installmentAllocations); err != nil {
			return err
		}

		// Update attachment status sau khi đã xóa allocation
		if err := l.updateAttachmentStatus(tx, billItemIDs); err != nil {
			return err
		}

		return nil
	})

	resp = new(types.PaymentResponse)
	return
}

// Thêm function mới để handle rollback installment
func (l *DeleteLogic) handleInstallmentRollback(tx *ent.Tx, installmentAllocations []*ent.PaymentAllocation) error {
	if len(installmentAllocations) == 0 {
		return nil
	}

	// Collect installment IDs từ allocations đã xóa
	installmentIDs := make([]int, 0)
	initialStateInstallments := make([]int, 0) // Những installment có state = "initial"

	for _, alloc := range installmentAllocations {
		if alloc.InstallmentID != nil {
			installmentIDs = append(installmentIDs, *alloc.InstallmentID)

			// Nếu allocation này là "initial", cần rollback paid_at
			if alloc.State == "initial" {
				initialStateInstallments = append(initialStateInstallments, *alloc.InstallmentID)
			}
		}
	}

	// Kiểm tra từng installment xem còn allocation nào khác không
	for _, instID := range initialStateInstallments {
		// Đếm số allocation còn lại cho installment này (đã xóa allocation của payment rồi)
		count, err := tx.PaymentAllocation.Query().
			Where(paymentallocation.InstallmentID(instID)).
			Count(l.ctx)
		if err != nil {
			return err
		}

		// Nếu không còn allocation nào, rollback paid_at về null
		if count == 0 {
			err = tx.Installment.UpdateOneID(instID).
				ClearPaidAt().
				Exec(l.ctx)
			if err != nil {
				return err
			}
		}
	}

	return nil
}

// Function để update deal state và attachment khi rollback installment

func (l *DeleteLogic) updateAttachmentStatus(tx *ent.Tx, billItemIDs []int) error {
	if len(billItemIDs) == 0 {
		return nil
	}

	// Lấy attachments từ bill items
	attachments, err := tx.BillItem.Query().
		Where(billitem.IDIn(billItemIDs...)).
		QueryAttachment().
		All(l.ctx)
	if err != nil {
		return err
	}

	for _, attachment := range attachments {
		count, err := tx.PaymentAllocation.Query().
			Where(
				paymentallocation.HasBillItemWith(
					billitem.AttachmentID(attachment.ID),
				),
			).
			Count(l.ctx)
		if err != nil {
			return err
		}

		// Nếu không còn allocation nào, set status = 22
		if count == 0 {
			err = tx.Attachment.UpdateOne(attachment).
				SetStatus(bconst.StatusUnpaid).
				Exec(l.ctx)
			if err != nil {
				return err
			}
		}
	}

	return nil
}
