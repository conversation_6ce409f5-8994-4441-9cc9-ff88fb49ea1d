package economy_payment

import (
	"bcare/common/bquery"
	"bcare/common/butils"
	cast2 "bcare/common/cast"
	"bcare/ent"
	"bcare/ent/schema"
	"context"
	"fmt"
	"github.com/jinzhu/copier"
	"github.com/xuri/excelize/v2"
	"path/filepath"
	"time"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type QueryDetailLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewQueryDetailLogic(ctx context.Context, svcCtx *svc.ServiceContext) QueryDetailLogic {
	return QueryDetailLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *QueryDetailLogic) QueryDetail(req *types.PaymentReportDynamicQuery) (resp *types.DynamicQueryResponse, err error) {
	resp = new(types.DynamicQueryResponse)

	dQuery := &bquery.DynamicQuery{}
	err = copier.Copy(dQuery, req)
	if err != nil {
		logx.WithContext(l.ctx).Errorf("unable to copy request: %v", err)
		return resp, err
	}

	query := l.svcCtx.Ent.PaymentReportDetailView.Query().Modify(bquery.ApplyDynamicQuery(dQuery))

	result := map[string]any{}
	aliases := make(map[string]bool)
	for _, agg := range req.Aggregations {
		if agg.Alias != "" {
			aliases[agg.Alias] = true
		}
	}

	skipSoftDelete := schema.SkipSoftDelete(l.ctx)
	if !req.Export {
		if req.Limit == 1 {
			r, err := query.First(skipSoftDelete)
			if err != nil && !ent.IsNotFound(err) {
				logx.WithContext(l.ctx).Errorf("unable to fetch activity record: %v", err)
				return resp, err
			}

			if r != nil {

				for alias := range aliases {
					result[alias], _ = r.Value(alias)
				}
			}
		} else {
			rows, err := query.All(skipSoftDelete)
			if err != nil {
				logx.WithContext(l.ctx).Errorf("unable to fetch payment records: %v", err)
				return resp, err
			}
			resultList := make([]map[string]any, len(rows))
			for i, r := range rows {
				rowResult := make(map[string]any)
				rowResult["record"] = cast2.Atom(&rowResult, r)
				for alias := range aliases {
					rowResult[alias], _ = r.Value(alias)
				}
				resultList[i] = rowResult
			}
			result["rows"] = resultList
		}
	} else {
		rows, err := query.All(skipSoftDelete)
		if err != nil {
			logx.WithContext(l.ctx).Errorf("unable to fetch payment records: %v", err)
			return resp, err
		}

		fileUrl, err := l.exportToExcel(rows)
		if err != nil {
			return nil, fmt.Errorf("failed to export excel: %v", err)
		}
		result["file_url"] = fileUrl
	}

	resp.Result = result

	return
}

func (l *QueryDetailLogic) exportToExcel(records []*ent.PaymentReportDetailView) (string, error) {
	f := excelize.NewFile()
	defer f.Close()

	sheetName := "Payment Report"
	f.SetSheetName("Sheet1", sheetName)

	// Set headers
	headers := []string{
		"Ngày tạo", "Mã hồ sơ", "Tên KH", "SĐT", "Tỉnh/TP", "Quận/Huyện", "Phường/Xã", "Địa chỉ",
		"Tổng tiền", "Nội dung", "Mã sp", "Nhóm dịch vụ", "Số lượng", "Đơn giá", "Giảm giá", "Thành tiền",
		"Đã thanh toán", "Người tạo", "Người thu tiền",
	}

	// Set header style
	headerStyle, _ := f.NewStyle(&excelize.Style{
		Font:      &excelize.Font{Bold: true},
		Fill:      excelize.Fill{Type: "pattern", Color: []string{"#CCCCCC"}, Pattern: 1},
		Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center"},
	})

	// Write headers
	for i, header := range headers {
		cell := fmt.Sprintf("%c1", 'A'+i)
		f.SetCellValue(sheetName, cell, header)
		f.SetCellStyle(sheetName, cell, cell, headerStyle)
	}

	// Write data with merged cells for common information
	currentRow := 2
	for _, record := range records {
		allocations := record.PaymentAllocations
		if len(allocations) == 0 {
			// If no allocations, write single row
			writeBasicInfo(f, sheetName, currentRow, record, l.ctx, l.svcCtx)
			currentRow++
			continue
		}

		// Write rows for each allocation
		startRow := currentRow
		for _, allocation := range allocations {
			writeBasicInfo(f, sheetName, currentRow, record, l.ctx, l.svcCtx)
			startCol := 'J'
			creatorName := ""
			if allocation.Creator != nil {
				creatorName = allocation.Creator.Name
			}
			payerName := ""
			if allocation.Payer != nil {
				payerName = allocation.Payer.Name
			}
			cells := []interface{}{
				allocation.ProductName,
				allocation.Code,
				allocation.Category,
				allocation.Quantity,
				allocation.Price,
				allocation.Discount,
				allocation.Amount,
				allocation.PaidAmount,
				creatorName,
				payerName,
			}

			for i, value := range cells {
				cell := fmt.Sprintf("%c%d", startCol+rune(i), currentRow)
				f.SetCellValue(sheetName, cell, value)
			}
			currentRow++
		}

		// Merge cells for common information if multiple allocations
		if len(allocations) > 1 {
			mergeCells(f, sheetName, startRow, currentRow-1)
		}
	}

	// Auto-fit column width
	for i := range headers {
		col := string(rune('A' + i))
		f.SetColWidth(sheetName, col, col, 15)
	}

	// Create directory and save file
	dirPath, err := createDirStructure(l.svcCtx.Config.Storage.Local.Path, "reports")
	if err != nil {
		return "", fmt.Errorf("failed to create directory structure: %v", err)
	}

	timestamp := time.Now().Format("20060102_150405")
	fileName := fmt.Sprintf("payment_report_detail_%s.xlsx", timestamp)
	filePath := filepath.Join(dirPath, fileName)

	if err := f.SaveAs(filePath); err != nil {
		return "", fmt.Errorf("failed to save excel file: %v", err)
	}

	publicUrl := fmt.Sprintf("/reports/%s/%s",
		time.Now().Format("2006_01_02"),
		fileName)

	return publicUrl, nil
}

func writeBasicInfo(f *excelize.File, sheet string, row int, record *ent.PaymentReportDetailView, ctx context.Context, svc *svc.ServiceContext) {

	cells := []interface{}{
		record.CreatedAt.Format("2006-01-02 15:04:05"),
		record.PersonCode,
		record.FullName,
		butils.MaskPhone(record.Phone, ctx, svc.Enforcer),
		record.Province,
		record.District,
		record.Ward,
		record.AddressNumber,
		record.TotalAmount,
	}

	for i, value := range cells {
		cell := fmt.Sprintf("%c%d", 'A'+i, row)
		f.SetCellValue(sheet, cell, value)
	}
}

func mergeCells(f *excelize.File, sheet string, startRow, endRow int) {
	// Merge cells for common information (columns A to I)
	for col := 'A'; col <= 'I'; col++ {
		startCell := fmt.Sprintf("%c%d", col, startRow)
		endCell := fmt.Sprintf("%c%d", col, endRow)
		f.MergeCell(sheet, startCell, endCell)
	}
}
