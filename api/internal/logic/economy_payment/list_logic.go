package economy_payment

import (
	"bcare/common/bquery"
	cast2 "bcare/common/cast"
	"bcare/ent"
	"bcare/ent/payment"
	"bcare/ent/paymentallocation"
	"bcare/ent/product"
	"context"
	"entgo.io/ent/dialect/sql"
	"github.com/samber/lo"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type ListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewListLogic(ctx context.Context, svcCtx *svc.ServiceContext) ListLogic {
	return ListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ListLogic) List(req *types.PaymentListRequest) (resp *types.PaymentListResponse, err error) {
	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		limit, offset := bquery.Paging(req.PageSize, req.Page)
		order := bquery.Ordering(req.OrderBy)

		query := tx.Payment.Query().
			WithBill().
			WithAllocations(
				func(q *ent.PaymentAllocationQuery) {
					q.WithInstallment().WithBillItem(func(q *ent.BillItemQuery) {
						q.WithUser().
							WithAttachment(func(q *ent.AttachmentQuery) {
								q.WithProduct(func(q *ent.ProductQuery) {
									q.Select(product.FieldID, product.FieldName) // Only select ID and Name fields
								})
							})
					})
				})

		// Handle refund filter
		if req.Kind != "" {
			switch req.Kind {
			case "refund":
				// Get refund payments (negative allocations)
				query = query.Where(
					payment.HasAllocationsWith(
						paymentallocation.AmountLT(0),
					),
					payment.TotalAmountLT(0),
				)
			case "normal":
				// Get normal payments (non-negative allocations)
				query = query.Where(
					payment.Not(
						payment.And(
							payment.HasAllocationsWith(
								paymentallocation.AmountLT(0),
							),
							payment.TotalAmountLT(0),
						),
					),
				)
			}
		}

		// Apply limit, offset and ordering
		query = query.
			Limit(limit).
			Offset(offset).
			Order(order)

		filterMap := make(map[string]interface{})
		_ = cast2.AtomNoZero(&filterMap, req.Filter)

		for field, value := range filterMap {
			query = query.Where(sql.FieldEQ(field, value))
		}

		// Count total if pagination is used
		var total int
		if req.PageSize != 0 {
			total, _ = query.Count(l.ctx)
		}

		payments, e := query.All(l.ctx)
		if e != nil {
			err = types.ErrPaymentGetList.Wrap(e)
			return err
		}

		resp = new(types.PaymentListResponse)
		resp.Payments = lo.Map(payments, func(payment *ent.Payment, _ int) types.PaymentResponse {
			p := types.PaymentResponse{}
			_ = cast2.ConvertViaJson(&p, payment)
			return p
		})

		resp.Total = total
		if req.PageSize > 0 {
			resp.TotalPage = (total + req.PageSize - 1) / req.PageSize
		}

		return nil
	})

	return resp, err
}
