package form_submission

import (
	"bcare/common/bquery"
	cast2 "bcare/common/cast"
	"bcare/ent"
	"context"
	"entgo.io/ent/dialect/sql"
	"github.com/samber/lo"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type ListFormSubmissionsLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewListFormSubmissionsLogic(ctx context.Context, svcCtx *svc.ServiceContext) ListFormSubmissionsLogic {
	return ListFormSubmissionsLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ListFormSubmissionsLogic) ListFormSubmissions(req *types.FormSubmissionsListRequest) (resp *types.FormSubmissionListResponse, err error) {
	limit, offset := bquery.Paging(req.PageSize, req.Page)
	order := bquery.Ordering(req.OrderBy)

	query := l.svcCtx.Ent.FormSubmission.Query().WithPerson().Order(order)

	filterMap := make(map[string]interface{})
	_ = cast2.AtomNoZero(&filterMap, req.Filter)

	for field, value := range filterMap {
		query = query.Where(sql.FieldEQ(field, value))
	}

	total, _ := query.Count(l.ctx)
	query = query.Limit(limit).Offset(offset)
	formSubmissions, e := query.All(l.ctx)

	if e != nil {
		return nil, e
	}

	resp = new(types.FormSubmissionListResponse)
	resp.FormSubmissions = lo.Map(formSubmissions, func(f *ent.FormSubmission, _ int) types.FormSubmissionResponse {
		p := types.FormSubmissionResponse{}
		_ = cast2.ConvertViaJson(&p, f)
		return p
	})
	resp.Total = total

	return resp, err
}
