package form_submission

import (
	"bcare/ent/formsubmission"
	"context"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteFormSubmissionLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewDeleteFormSubmissionLogic(ctx context.Context, svcCtx *svc.ServiceContext) DeleteFormSubmissionLogic {
	return DeleteFormSubmissionLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DeleteFormSubmissionLogic) DeleteFormSubmission(req *types.FormSubmissionDeleteRequest) (resp *types.FormSubmissionDeleteResponse, err error) {
	ids := make([]int, len(req.Ids))
	for i, id := range req.Ids {
		ids[i] = id
	}

	query := l.svcCtx.Ent.FormSubmission.Delete().
		Where(formsubmission.IDIn(ids...))

	_, err = query.Exec(l.ctx)
	if err != nil {
		return nil, err
	}

	return nil, nil
}
