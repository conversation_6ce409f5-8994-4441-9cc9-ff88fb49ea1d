package form_submission

import (
	"bcare/common/bquery"
	cast2 "bcare/common/cast"
	"bcare/ent"
	"bcare/ent/schema"
	"context"
	"github.com/jinzhu/copier"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type QueryFormSubmissionsLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewQueryFormSubmissionsLogic(ctx context.Context, svcCtx *svc.ServiceContext) QueryFormSubmissionsLogic {
	return QueryFormSubmissionsLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *QueryFormSubmissionsLogic) QueryFormSubmissions(req *types.FormSubmissionDynamicQuery) (res *types.DynamicQueryResponse, err error) {
	res = new(types.DynamicQueryResponse)

	dQuery := &bquery.DynamicQuery{}
	err = copier.Copy(dQuery, req)

	if err != nil {
		logx.WithContext(l.ctx).Errorf("unable to copy request: %v", err)
		return res, err
	}

	query := l.svcCtx.Ent.FormSubmission.Query().Modify(bquery.ApplyDynamicQuery(dQuery)).WithPerson()

	result := map[string]any{}
	aliases := make(map[string]bool)
	for _, agg := range req.Aggregations {
		if agg.Alias != "" {
			aliases[agg.Alias] = true
		}
	}

	skipSoftDelete := schema.SkipSoftDelete(l.ctx)
	if req.Limit <= 1 {
		r, err := query.First(skipSoftDelete)
		if err != nil && !ent.IsNotFound(err) {
			logx.WithContext(l.ctx).Errorf("unable to fetch Appointment record: %v", err)
			return res, err
		}

		if r != nil {
			for alias := range aliases {
				result[alias], _ = r.Value(alias)
			}
		}
	} else {
		rows, err := query.All(skipSoftDelete)
		if err != nil {
			logx.WithContext(l.ctx).Errorf("unable to fetch Appointment records: %v", err)
			return res, err
		}

		resultList := make([]map[string]any, len(rows))
		for i, r := range rows {
			rowResult := make(map[string]any)
			rowResult["record"] = cast2.Atom(&rowResult, r)
			for alias := range aliases {
				rowResult[alias], _ = r.Value(alias)
			}
			resultList[i] = rowResult
		}
		result["rows"] = resultList
	}

	res.Result = result
	return
}
