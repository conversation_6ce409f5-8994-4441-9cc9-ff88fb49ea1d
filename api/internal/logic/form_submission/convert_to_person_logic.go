package form_submission

import (
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/bconst"
	"bcare/common/bquery"
	"bcare/ent"
	"bcare/ent/deal"
	"bcare/ent/formsubmission"
	"bcare/ent/person"
	types2 "bcare/ent/types"
	"time"

	"context"
	"fmt"

	"github.com/zeromicro/go-zero/core/logx"
)

type ConvertToPersonLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewConvertToPersonLogic(ctx context.Context, svcCtx *svc.ServiceContext) ConvertToPersonLogic {
	return ConvertToPersonLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ConvertToPersonLogic) ConvertToPerson(req *types.ConvertToPersonRequest) (resp *types.BulkUpdateFormSubmissionResult, err error) {
	result := &types.BulkUpdateFormSubmissionResult{
		SuccessCount: 0,
		Errors:       make([]types.FormSubmissionError, 0, len(req.FormSubmissionIds)),
	}

	type conversionResult struct {
		formSubmissionID int
		err              error
	}

	resultChan := make(chan conversionResult, len(req.FormSubmissionIds))

	// Process form submissions in batches
	const batchSize = 5
	for i := 0; i < len(req.FormSubmissionIds); i += batchSize {
		end := i + batchSize
		if end > len(req.FormSubmissionIds) {
			end = len(req.FormSubmissionIds)
		}

		// Process batch of form submissions
		for _, formID := range req.FormSubmissionIds[i:end] {
			go func(id int) {
				err := l.processConversion(id)
				resultChan <- conversionResult{formSubmissionID: id, err: err}
			}(formID)
		}
	}

	// Collect results
	for i := 0; i < len(req.FormSubmissionIds); i++ {
		res := <-resultChan
		if res.err != nil {
			result.Errors = append(result.Errors, types.FormSubmissionError{
				FormSubmissionID: res.formSubmissionID,
				Error:            res.err.Error(),
			})
			result.FailCount++
		} else {
			result.SuccessCount++
		}
	}

	if result.SuccessCount == 0 {
		return nil, nil
	}

	return result, nil
}

func (l *ConvertToPersonLogic) processConversion(formSubmissionID int) error {
	formSubmission, err := l.svcCtx.Ent.FormSubmission.Get(l.ctx, formSubmissionID)
	if err != nil {
		if ent.IsNotFound(err) {
			return fmt.Errorf("form submission not found")
		}
		return fmt.Errorf("failed to get form submission")
	}

	if formSubmission.PersonID != nil {
		return nil // Already converted, consider it a success
	}

	existingPerson, _ := l.svcCtx.Ent.Person.Query().
		Where(
			person.Phone(formSubmission.Phone),
		).
		First(l.ctx)

	return bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		var personID int
		if existingPerson != nil {
			personID = existingPerson.ID
		} else {
			personRecord := new(ent.Person)
			personRecord.FullName = formSubmission.FullName
			personRecord.Phone = formSubmission.Phone
			personRecord.Email = formSubmission.Email
			if formSubmission.SourceID > 0 {
				personRecord.SourceID = &formSubmission.SourceID
			}
			personRecord.Status = bconst.StatusNormal
			personField := new(types2.PersonMeta)
			personField.SourceChannel = &formSubmission.ReferrerURL
			personField.FormSource = &formSubmission.FormName
			personField.LandingPageURL = &formSubmission.SourceURL
			zalo := "unknown"
			personField.HasZalo = &zalo
			personRecord.PersonField = personField
			personRecord.Gender = "unknown"

			personRecord, err = tx.Person.Create().SetPerson(personRecord).Save(l.ctx)
			if err != nil {
				return types.ErrPersonAdd.Wrap(err, err.Error())
			}

			_, err = tx.Deal.Create().
				SetPersonID(personRecord.ID).
				SetState(deal.StateDraft).
				SetName("Niềng răng").
				SetStageID(13).
				Save(l.ctx)
			if err != nil {
				return types.ErrPersonCreate.Wrap(err)
			}

			personID = personRecord.ID
		}

		_, err = tx.FormSubmission.UpdateOne(formSubmission).
			SetPersonID(personID).
			SetState(formsubmission.StateApproved).
			SetProcessedAt(time.Now()).
			Save(l.ctx)
		if err != nil {
			return fmt.Errorf("failed to update form submission")
		}
		return nil
	})
}
