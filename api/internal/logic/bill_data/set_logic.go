package bill_data

import (
	"bcare/common/bquery"
	"bcare/ent"
	"bcare/ent/billdata"
	"context"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type SetLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewSetLogic(ctx context.Context, svcCtx *svc.ServiceContext) SetLogic {
	return SetLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *SetLogic) Set(req *types.SetBillDataRequest) (resp *types.CommonResponse, err error) {
	// todo: add your logic here and delete this line

	var count int
	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		// Tìm hoặc tạo mới ErrBillData
		billData, err := tx.BillData.Query().
			Where(
				billdata.BillID(req.BillID),
				billdata.Kind(req.Kind),
			).
			Only(l.ctx)

		if err != nil {
			if ent.IsNotFound(err) {
				// Nếu không tìm thấy, tạo mới
				createQuery := tx.BillData.Create().
					SetBillID(req.BillID).
					SetKind(req.Kind).
					SetUserID(req.UserID)

				if len(req.Data) > 0 {
					createQuery = createQuery.SetData(req.Data)
				} else {
					createQuery = createQuery.SetData(map[string]interface{}{req.Key: req.Value})
				}

				billData, err = createQuery.Save(l.ctx)
				if err != nil {
					return types.ErrBillData.Wrap(err, "failed to create bill data")
				}
				count = 1
			} else {
				return types.ErrBillData.Wrap(err, "failed to query bill data")
			}
		} else {
			// Cập nhật data
			updateQuery := billData.Update()

			if len(req.Data) > 0 {
				updateQuery = updateQuery.SetData(req.Data)
			} else {
				currentData := billData.Data
				if currentData == nil {
					currentData = make(map[string]interface{})
				}
				currentData[req.Key] = req.Value
				updateQuery = updateQuery.SetData(currentData)
			}

			// Cập nhật user_id và participant_id nếu cần
			if req.UserID != 0 {
				updateQuery = updateQuery.SetUserID(req.UserID)
			}

			// Lưu cập nhật
			_, err = updateQuery.Save(l.ctx)
			if err != nil {
				return types.ErrBillData.Wrap(err, "failed to update bill data")
			}
			count = 1
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return &types.CommonResponse{Count: count}, nil
}
