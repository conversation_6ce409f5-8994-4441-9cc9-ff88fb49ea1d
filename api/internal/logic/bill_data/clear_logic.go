package bill_data

import (
	"bcare/common/bquery"
	"bcare/ent"
	"bcare/ent/billdata"
	"context"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type ClearLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewClearLogic(ctx context.Context, svcCtx *svc.ServiceContext) ClearLogic {
	return ClearLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ClearLogic) Clear(req *types.ClearBillDataRequest) (resp *types.CommonResponse, err error) {
	// todo: add your logic here and delete this line

	var count int
	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		query := tx.BillData.Query().Where(billdata.BillID(req.BillID))

		if req.Kind != "" {
			query = query.Where(billdata.Kind(req.Kind))
		}

		billDataList, err := query.All(l.ctx)
		if err != nil {
			return types.ErrBillData.Wrap(err, "failed to query attachment data")
		}

		for _, billData := range billDataList {
			if req.Key != "" {
				// Clear specific key
				currentData := billData.Data
				if currentData != nil {
					delete(currentData, req.Key)
					_, err = billData.Update().SetData(currentData).Save(l.ctx)
					if err != nil {
						return types.ErrBillData.Wrap(err, "failed to update attachment data")
					}
				}
			} else {
				// Clear all data for the given kind
				_, err = billData.Update().ClearData().Save(l.ctx)
				if err != nil {
					return types.ErrBillData.Wrap(err, "failed to clear attachment data")
				}
			}
			count++
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return &types.CommonResponse{
		Count: count,
	}, nil
}
