package bill_data

import (
	"bcare/ent"
	"bcare/ent/billdata"
	"context"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetLogic(ctx context.Context, svcCtx *svc.ServiceContext) GetLogic {
	return GetLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetLogic) Get(req *types.GetBillDataRequest) (resp *types.BillDataResponse, err error) {
	query := l.svcCtx.Ent.BillData.Query().Where(billdata.BillID(req.BillID))

	// Nếu kind được cung cấp, thêm điều kiện kind vào truy vấn
	if req.Kind != "" {
		query = query.Where(billdata.Kind(req.Kind))
	}

	billDataList, err := query.All(l.ctx)
	if err != nil {
		if ent.IsNotFound(err) {
			return &types.BillDataResponse{Data: make(map[string]interface{})}, nil
		}
		return nil, types.ErrBillData.Wrap(err, "failed to query bill data")
	}

	result := make(map[string]interface{})

	// Trường hợp 1: Chỉ có bill_id (và có thể có participant_id)
	if req.Kind == "" && req.Key == "" {
		for _, ad := range billDataList {
			if ad.Data != nil {
				result[ad.Kind] = ad.Data
			}
		}
		return &types.BillDataResponse{Data: result}, nil
	}

	// Trường hợp 2: Có bill_id, kind (và có thể có participant_id)
	if req.Kind != "" && req.Key == "" {
		for _, ad := range billDataList {
			if ad.Data != nil {
				return &types.BillDataResponse{Data: ad.Data}, nil
			}
		}
		return &types.BillDataResponse{Data: make(map[string]interface{})}, nil
	}

	// Trường hợp 3: Có bill_id, kind và key (và có thể có participant_id)
	if req.Kind != "" && req.Key != "" {
		for _, ad := range billDataList {
			if ad.Data != nil {
				if value, exists := ad.Data[req.Key]; exists {
					if mapValue, ok := value.(map[string]interface{}); ok {
						return &types.BillDataResponse{Data: mapValue}, nil
					} else {
						// Nếu giá trị không phải là map, bọc nó trong một map
						return &types.BillDataResponse{Data: map[string]interface{}{"raw": value}}, nil
					}
				}
			}
		}
	}

	return &types.BillDataResponse{Data: make(map[string]interface{})}, nil
}
