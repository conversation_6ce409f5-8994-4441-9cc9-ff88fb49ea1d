package user_stat

import (
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/butils"
	cast2 "bcare/common/cast"
	"bcare/ent"
	"bcare/ent/predicate"
	"bcare/ent/task"
	"bcare/ent/taskassignment"
	"context"
	"fmt"
	"github.com/samber/lo"
	"github.com/zeromicro/go-zero/core/logx"
	"golang.org/x/sync/errgroup"
	"time"
)

type TaskStatLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewTaskStatLogic(ctx context.Context, svcCtx *svc.ServiceContext) TaskStatLogic {
	return TaskStatLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *TaskStatLogic) TaskStat(req *types.StatRequest) (resp *types.TaskStatResponse, err error) {
	cacheKey := generateCacheKey(req)

	// get value from cache
	cached, ok := svc.Cache.Get(cacheKey)
	if ok {
		if cachedResp, ok := cached.(*types.TaskStatResponse); ok {
			return cachedResp, nil
		}
	}
	// Create predicates for filtering
	predicates := []predicate.TaskAssignment{
		taskassignment.UserIDEQ(req.UserId),
		taskassignment.RoleEQ("primary"),
	}

	// Add date filters if provided
	startDate, endDate := butils.GetDateRange(req.StartDate, req.EndDate)
	if !startDate.IsZero() {
		predicates = append(predicates, taskassignment.CreatedAtGTE(startDate))
	}
	if !endDate.IsZero() {
		predicates = append(predicates, taskassignment.CreatedAtLTE(endDate))
	}

	// Create error group for concurrent execution
	g, _ := errgroup.WithContext(l.ctx)

	var overview *types.TaskOverview
	var statusBreakdown []types.StatusCount
	var upcomingTasks []types.TaskResponse

	// Get task overview concurrently
	g.Go(func() error {
		var err error
		overview, err = l.getTaskOverview(req.UserId, predicates)
		return err
	})

	// Get status breakdown concurrently
	g.Go(func() error {
		var err error
		statusBreakdown, err = l.getStatusBreakdown(req.UserId, predicates)
		return err
	})

	// Get upcoming tasks concurrently
	g.Go(func() error {
		var err error
		upcomingTasks, err = l.getUpcomingTasks(req.UserId)
		return err
	})

	// Wait for all goroutines to complete and check for errors
	if err := g.Wait(); err != nil {
		return nil, err
	}

	// Create response
	resp = &types.TaskStatResponse{
		Overview:          *overview,
		StatusBreakdown:   statusBreakdown,
		UpcomingDeadlines: upcomingTasks,
	}

	// Set cache with the new response
	svc.Cache.Set(cacheKey, resp, 24*time.Hour)

	return resp, nil
}

func (l *TaskStatLogic) getTaskOverview(userId int, predicates []predicate.TaskAssignment) (*types.TaskOverview, error) {
	g, ctx := errgroup.WithContext(l.ctx)

	var totalTasks, completedTasks int

	// Get total tasks concurrently
	g.Go(func() error {
		var err error
		totalTasks, err = l.svcCtx.Ent.TaskAssignment.Query().
			Where(taskassignment.And(predicates...)).
			Count(ctx)
		return err
	})

	// Get completed tasks concurrently
	g.Go(func() error {
		var err error
		completedPredicates := append(predicates, taskassignment.StateEQ("completed"))
		completedTasks, err = l.svcCtx.Ent.TaskAssignment.Query().
			Where(taskassignment.And(completedPredicates...)).
			Count(ctx)
		return err
	})

	if err := g.Wait(); err != nil {
		return nil, err
	}

	var completionRate float64
	if totalTasks > 0 {
		completionRate = float64(completedTasks) / float64(totalTasks) * 100
	}

	return &types.TaskOverview{
		TotalTasks:     totalTasks,
		CompletedTasks: completedTasks,
		CompletionRate: completionRate,
		RemainingTasks: totalTasks - completedTasks,
	}, nil
}

func (l *TaskStatLogic) getStatusBreakdown(userId int, predicates []predicate.TaskAssignment) ([]types.StatusCount, error) {
	type GroupCount struct {
		State string `json:"state"`
		Count int    `json:"count"`
	}

	var results []GroupCount
	err := l.svcCtx.Ent.TaskAssignment.Query().
		Where(taskassignment.And(predicates...)).
		GroupBy(taskassignment.FieldState).
		Aggregate(
			ent.Count(),
		).
		Scan(l.ctx, &results)
	if err != nil {
		return nil, err
	}

	statusCounts := make([]types.StatusCount, len(results))
	for i, result := range results {
		statusCounts[i] = types.StatusCount{
			Status: result.State,
			Count:  result.Count,
		}
	}

	return statusCounts, nil
}

func (l *TaskStatLogic) getUpcomingTasks(userId int) ([]types.TaskResponse, error) {
	// Get tasks due in next 7 days
	tasks, err := l.svcCtx.Ent.Task.Query().
		WithAssignments(func(q *ent.TaskAssignmentQuery) { q.WithUser() }).
		WithCreator().
		WithPerson().
		Where(
			task.HasAssignmentsWith(
				taskassignment.And(
					taskassignment.UserIDEQ(userId),
					taskassignment.RoleEQ("primary"),
					taskassignment.DueAtGTE(time.Now()),
					taskassignment.DueAtLTE(time.Now().AddDate(0, 0, 7)),
					taskassignment.StateNEQ("completed"),
					taskassignment.StateNEQ("cancelled"),
				),
			),
		).
		Order(ent.Asc(task.FieldStartDate)).
		Limit(5).
		All(l.ctx)
	if err != nil {
		return nil, err
	}

	result := lo.Map(tasks, func(task *ent.Task, _ int) types.TaskResponse {
		t := types.TaskResponse{}
		_ = cast2.EntToResp(&t, task)
		return t
	})
	return result, nil
}

func generateCacheKey(req *types.StatRequest) string {
	return fmt.Sprintf("TaskStat:%d:%s:%s",
		req.UserId,
		req.StartDate,
		req.EndDate)
}
