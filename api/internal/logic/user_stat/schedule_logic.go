package user_stat

import (
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/butils"
	cast2 "bcare/common/cast"
	"bcare/ent"
	"bcare/ent/appointment"
	"bcare/ent/schedule"
	"context"
	"fmt"
	"github.com/zeromicro/go-zero/core/logx"
	"golang.org/x/sync/errgroup"
	"sort"
	"time"
)

type ScheduleLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewScheduleLogic(ctx context.Context, svcCtx *svc.ServiceContext) ScheduleLogic {
	return ScheduleLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func generateScheduleCacheKey(req *types.StatRequest) string {
	return fmt.Sprintf("Schedule:%d:%s:%s",
		req.UserId,
		req.StartDate,
		req.EndDate)
}

func (l *ScheduleLogic) Schedule(req *types.StatRequest) (resp *types.ScheduleStatResponse, err error) {
	// Try to get from cache first
	cacheKey := generateScheduleCacheKey(req)
	if cached, ok := svc.Cache.Get(cacheKey); ok {
		if cachedResp, ok := cached.(*types.ScheduleStatResponse); ok {
			return cachedResp, nil
		}
	}

	startDate := time.Time{}
	endDate := time.Time{}
	if req.StartDate != "" && req.EndDate != "" {
		startDate, endDate = butils.GetDateRange(req.StartDate, req.EndDate)
	} else {
		now := time.Now().UTC()
		startDate = now.Truncate(24 * time.Hour)
		endDate = now.AddDate(0, 0, 7).Truncate(24 * time.Hour) // Default: next 7 days
	}

	// Create error group for concurrent execution
	g, _ := errgroup.WithContext(l.ctx)

	var appointments []*ent.Appointment
	var schedules []*ent.Schedule

	// Get appointments concurrently
	g.Go(func() error {
		var err error
		appointments, err = l.getAppointments(req.UserId, startDate, endDate)
		return err
	})

	// Get schedules concurrently
	g.Go(func() error {
		var err error
		schedules, err = l.getSchedules(req.UserId, startDate, endDate)
		return err
	})

	// Wait for data fetching to complete
	if err := g.Wait(); err != nil {
		return nil, err
	}

	// Create new error group for processing
	g, _ = errgroup.WithContext(l.ctx)

	var overview types.Overview
	var dailySchedules []types.DailySchedule
	var upcomingAppointments []types.AppointmentResponse

	// Process overview concurrently
	g.Go(func() error {
		overview = l.calculateOverview(appointments)
		return nil
	})

	// Process daily schedules concurrently
	g.Go(func() error {
		dailySchedules = l.processDailySchedules(appointments, schedules)
		return nil
	})

	// Process upcoming appointments concurrently
	g.Go(func() error {
		upcomingAppointments = make([]types.AppointmentResponse, len(appointments))
		for i, apt := range appointments {
			var aptResp types.AppointmentResponse
			if err := cast2.EntToResp(&aptResp, apt); err != nil {
				return err
			}
			upcomingAppointments[i] = aptResp
		}
		return nil
	})

	// Wait for processing to complete
	if err := g.Wait(); err != nil {
		return nil, err
	}

	// Create response
	resp = &types.ScheduleStatResponse{
		Overview:             overview,
		DailySchedules:       dailySchedules,
		UpcomingAppointments: upcomingAppointments,
	}

	// Cache the response
	svc.Cache.Set(cacheKey, resp, 24*time.Hour)

	return resp, nil
}

// Rest of the helper functions remain the same
func (l *ScheduleLogic) getAppointments(userId int, startDate, endDate time.Time) ([]*ent.Appointment, error) {
	return l.svcCtx.Ent.Appointment.Query().
		Where(
			appointment.DoctorID(userId),
			appointment.StartTimeGTE(startDate),
			appointment.StartTimeLTE(endDate),
		).
		WithPerson().
		WithDoctor().
		Order(ent.Asc(appointment.FieldStartTime)).
		All(l.ctx)
}

// getSchedules retrieves schedules for the given user and date range
func (l *ScheduleLogic) getSchedules(userId int, startDate, endDate time.Time) ([]*ent.Schedule, error) {
	return l.svcCtx.Ent.Schedule.Query().
		Where(
			schedule.UserID(userId),
			schedule.StartTimeGTE(startDate),
			schedule.StartTimeLTE(endDate),
		).
		WithUser().
		Order(ent.Asc(schedule.FieldStartTime)).
		All(l.ctx)
}

// calculateOverview calculates overview statistics
func (l *ScheduleLogic) calculateOverview(appointments []*ent.Appointment) types.Overview {
	now := time.Now().UTC()
	todayEnd := now.Truncate(24 * time.Hour).Add(24 * time.Hour)

	var todayCount, upcomingCount int
	for _, apt := range appointments {
		if apt.StartTime.Before(todayEnd) {
			todayCount++
		}
		upcomingCount++
	}

	return types.Overview{
		TodayAppointments:    todayCount,
		UpcomingAppointments: upcomingCount,
	}
}

// processDailySchedules groups appointments and schedules by date
func (l *ScheduleLogic) processDailySchedules(appointments []*ent.Appointment, schedules []*ent.Schedule) []types.DailySchedule {
	dailySchedules := make(map[string]*types.DailySchedule)

	// Use errgroup for concurrent processing
	g, _ := errgroup.WithContext(l.ctx)

	// Process appointments
	g.Go(func() error {
		for _, apt := range appointments {
			dateStr := apt.StartTime.Format("2006-01-02")
			if _, exists := dailySchedules[dateStr]; !exists {
				dailySchedules[dateStr] = &types.DailySchedule{
					Date:      dateStr,
					DayOfWeek: apt.StartTime.Format("Monday"),
				}
			}

			var aptResp types.AppointmentResponse
			if err := cast2.EntToResp(&aptResp, apt); err != nil {
				return err
			}

			dailySchedules[dateStr].Appointments = append(
				dailySchedules[dateStr].Appointments,
				aptResp,
			)
			dailySchedules[dateStr].TotalAppointments++
		}
		return nil
	})

	// Process schedules
	g.Go(func() error {
		for _, sch := range schedules {
			dateStr := sch.StartTime.Format("2006-01-02")
			if _, exists := dailySchedules[dateStr]; !exists {
				dailySchedules[dateStr] = &types.DailySchedule{
					Date:      dateStr,
					DayOfWeek: sch.StartTime.Format("Monday"),
				}
			}

			var schResp types.ScheduleResponse
			if err := cast2.EntToResp(&schResp, sch); err != nil {
				return err
			}

			dailySchedules[dateStr].WorkSchedules = append(
				dailySchedules[dateStr].WorkSchedules,
				schResp,
			)
		}
		return nil
	})

	// Wait for all processing to complete
	_ = g.Wait()

	return sortDailySchedules(dailySchedules)
}

// sortDailySchedules converts map to sorted slice
func sortDailySchedules(scheduleMap map[string]*types.DailySchedule) []types.DailySchedule {
	var sorted []types.DailySchedule
	for _, s := range scheduleMap {
		sorted = append(sorted, *s)
	}

	sort.Slice(sorted, func(i, j int) bool {
		return sorted[i].Date < sorted[j].Date
	})

	return sorted
}
