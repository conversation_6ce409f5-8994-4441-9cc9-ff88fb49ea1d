package user_stat

import (
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	cast2 "bcare/common/cast"
	"bcare/ent"
	"bcare/ent/person"
	"bcare/ent/personassignment"
	"bcare/ent/user"
	"context"
	"fmt"
	"github.com/samber/lo"
	"github.com/zeromicro/go-zero/core/logx"
	"golang.org/x/sync/errgroup"
	"time"
)

type PersonStatLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewPersonStatLogic(ctx context.Context, svcCtx *svc.ServiceContext) PersonStatLogic {
	return PersonStatLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *PersonStatLogic) PersonStat(req *types.StatRequest) (*types.PersonStatResponse, error) {
	// Try to get from cache first
	cacheKey := l.generateCacheKey(req)
	if cached, ok := svc.Cache.Get(cacheKey); ok {
		if cachedResp, ok := cached.(*types.PersonStatResponse); ok {
			return cachedResp, nil
		}
	}

	// Create error group for concurrent execution
	g, ctx := errgroup.WithContext(l.ctx)

	resp := &types.PersonStatResponse{}
	var totalPatients, newPatients int
	var recentPersons []*ent.Person

	// Get total patients concurrently
	g.Go(func() error {
		var err error
		totalPatients, err = l.getTotalPatients(ctx, req.UserId)
		return err
	})

	// Get new patients concurrently
	g.Go(func() error {
		var err error
		newPatients, err = l.getNewPatients(ctx, req.UserId)
		return err
	})

	// Get recent patients concurrently
	g.Go(func() error {
		var err error
		recentPersons, err = l.getRecentPatients(ctx, req.UserId)
		return err
	})

	// Wait for all goroutines to complete
	if err := g.Wait(); err != nil {
		return nil, fmt.Errorf("error fetching stats: %v", err)
	}

	// Combine results
	resp.Overview = types.PersonOverview{
		TotalPersons: totalPatients,
		NewPersons:   newPatients,
	}

	resp.RecentPersons = l.mapPersonsToResponse(recentPersons)

	// Cache the response
	svc.Cache.Set(cacheKey, resp, 1*time.Hour)

	return resp, nil
}

func (l *PersonStatLogic) getTotalPatients(ctx context.Context, userId int) (int, error) {
	return l.svcCtx.Ent.PersonAssignment.Query().
		Where(
			personassignment.UserID(userId),
			personassignment.HasPersonWith(person.DeletedAtIsNil()),
		).
		Count(ctx)
}

func (l *PersonStatLogic) getNewPatients(ctx context.Context, userId int) (int, error) {
	thirtyDaysAgo := time.Now().AddDate(0, 0, -30)
	return l.svcCtx.Ent.PersonAssignment.Query().
		Where(
			personassignment.UserID(userId),
			personassignment.CreatedAtGT(thirtyDaysAgo),
			personassignment.HasPersonWith(person.DeletedAtIsNil()),
		).
		Count(ctx)
}

func (l *PersonStatLogic) getRecentPatients(ctx context.Context, userId int) ([]*ent.Person, error) {
	return l.svcCtx.Ent.Person.Query().
		Where(
			person.HasAssigneesWith(user.ID(userId)),
			person.DeletedAtIsNil(),
		).
		WithCreator().
		WithAssignment(func(q *ent.PersonAssignmentQuery) { q.WithUser() }).
		WithIssues().
		Order(ent.Desc(person.FieldCreatedAt)).
		Limit(10).
		All(ctx)
}

func (l *PersonStatLogic) mapPersonsToResponse(persons []*ent.Person) []types.PersonResponse {
	return lo.Map(persons, func(person *ent.Person, _ int) types.PersonResponse {
		p := types.PersonResponse{}
		_ = cast2.EntToResp(&p, person)
		return p
	})
}

func (l *PersonStatLogic) generateCacheKey(req *types.StatRequest) string {
	return fmt.Sprintf("PersonStat:%d", req.UserId)
}
