package user_stat

import (
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/butils"
	"bcare/ent/appointment"
	"bcare/ent/call"
	"bcare/ent/messagehistory"
	"bcare/ent/note"
	"context"
	"fmt"
	"github.com/zeromicro/go-zero/core/logx"
	"golang.org/x/sync/errgroup"
	"time"
)

type PerformanceLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewPerformanceLogic(ctx context.Context, svcCtx *svc.ServiceContext) PerformanceLogic {
	return PerformanceLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *PerformanceLogic) Performance(req *types.StatRequest) (*types.PerformanceStatResponse, error) {
	cacheKey := l.generateCacheKey(req)

	//// Try to get from cache
	if cached, ok := svc.Cache.Get(cacheKey); ok {
		if cachedResp, ok := cached.(*types.PerformanceStatResponse); ok {
			return cachedResp, nil
		}
	}

	// Get date range from request (assuming you have these fields in StatRequest)
	startDate := time.Time{}
	endDate := time.Time{}
	if req.StartDate != "" && req.EndDate != "" {
		startDate, endDate = butils.GetDateRange(req.StartDate, req.EndDate)
	} else {
		now := time.Now().UTC()
		// Đặt endDate là 0h ngày mai
		endDate = now.AddDate(0, 0, 1).Truncate(24 * time.Hour)
		// Đặt startDate là 0h của 7 ngày trước
		startDate = now.AddDate(0, 0, -6).Truncate(24 * time.Hour)
	}

	// Initialize response
	resp := &types.PerformanceStatResponse{}

	// Create error group for concurrent execution
	g, _ := errgroup.WithContext(l.ctx)

	// Get overview data concurrently
	g.Go(func() error {
		overview, err := l.getOverviewData(req.UserId, startDate, endDate)
		if err != nil {
			return err
		}
		resp.Overview = overview
		return nil
	})

	// Get time series data concurrently
	g.Go(func() error {
		timeSeries, err := l.getTimeSeriesData(req.UserId, startDate, endDate)
		if err != nil {
			return err
		}
		resp.TimeSeries = timeSeries
		return nil
	})

	// Wait for all goroutines to complete
	if err := g.Wait(); err != nil {
		return nil, err
	}

	// Cache the response
	svc.Cache.Set(cacheKey, resp, 24*time.Hour)

	return resp, nil
}

func (l *PerformanceLogic) getOverviewData(userID int, startDate, endDate time.Time) (types.PerformanceOverview, error) {
	g, _ := errgroup.WithContext(l.ctx)

	var appointmentCount, callCount, noteCount, messageCount int

	// Get counts concurrently
	g.Go(func() error {
		var err error
		appointmentCount, err = l.getAppointmentCount(userID, startDate, endDate)
		return err
	})

	g.Go(func() error {
		var err error
		callCount, err = l.getCallCount(userID, startDate, endDate)
		return err
	})

	g.Go(func() error {
		var err error
		noteCount, err = l.getNoteCount(userID, startDate, endDate)
		return err
	})

	g.Go(func() error {
		var err error
		messageCount, err = l.getMessageCount(userID, startDate, endDate)
		return err
	})

	if err := g.Wait(); err != nil {
		return types.PerformanceOverview{}, err
	}

	totalDays := endDate.Sub(startDate).Hours() / 24
	totalActivities := appointmentCount + callCount + noteCount + messageCount
	dailyAverage := float64(totalActivities) / totalDays

	return types.PerformanceOverview{
		AppointmentCount: appointmentCount,
		CallCount:        callCount,
		NoteCount:        noteCount,
		MessageCount:     messageCount,
		DailyAverage:     dailyAverage,
	}, nil
}

func (l *PerformanceLogic) getDailyActivityCounts(userID int, currentDate, nextDate time.Time) (map[string]int, error) {
	g, ctx := errgroup.WithContext(l.ctx)

	counts := make(map[string]int)

	g.Go(func() error {
		count, err := l.svcCtx.Ent.Appointment.Query().
			Where(
				appointment.CreatorIDEQ(userID),
				appointment.CreatedAtGTE(currentDate),
				appointment.CreatedAtLT(nextDate),
			).Count(ctx)
		counts["appointments"] = count
		return err
	})

	g.Go(func() error {
		count, err := l.svcCtx.Ent.Call.Query().
			Where(
				call.UserIDEQ(userID),
				call.CreatedAtGTE(currentDate),
				call.CreatedAtLT(nextDate),
			).Count(ctx)
		counts["calls"] = count
		return err
	})

	g.Go(func() error {
		count, err := l.svcCtx.Ent.Note.Query().
			Where(
				note.UserIDEQ(userID),
				note.CreatedAtGTE(currentDate),
				note.CreatedAtLT(nextDate),
			).Count(ctx)
		counts["notes"] = count
		return err
	})

	g.Go(func() error {
		count, err := l.svcCtx.Ent.MessageHistory.Query().
			Where(
				messagehistory.UserIDEQ(userID),
				messagehistory.CreatedAtGTE(currentDate),
				messagehistory.CreatedAtLT(nextDate),
			).Count(ctx)
		counts["messages"] = count
		return err
	})

	if err := g.Wait(); err != nil {
		return nil, err
	}

	return counts, nil
}

func (l *PerformanceLogic) getDateRange(req *types.StatRequest) (time.Time, time.Time) {
	if req.StartDate != "" && req.EndDate != "" {
		return butils.GetDateRange(req.StartDate, req.EndDate)
	}
	now := time.Now().UTC()
	return now.AddDate(0, 0, -7), now
}

func (l *PerformanceLogic) generateCacheKey(req *types.StatRequest) string {
	return fmt.Sprintf("performance_stat:%d:%s:%s",
		req.UserId,
		req.StartDate,
		req.EndDate)
}

func (l *PerformanceLogic) getAppointmentCount(userID int, startDate, endDate time.Time) (int, error) {
	return l.svcCtx.Ent.Appointment.Query().
		Where(
			appointment.CreatorIDEQ(userID),
			appointment.CreatedAtGTE(startDate),
			appointment.CreatedAtLTE(endDate),
		).Count(l.ctx)
}

func (l *PerformanceLogic) getCallCount(userID int, startDate, endDate time.Time) (int, error) {
	return l.svcCtx.Ent.Call.Query().
		Where(
			call.UserIDEQ(userID),
			call.CreatedAtGTE(startDate),
			call.CreatedAtLTE(endDate),
		).Count(l.ctx)
}

func (l *PerformanceLogic) getNoteCount(userID int, startDate, endDate time.Time) (int, error) {
	return l.svcCtx.Ent.Note.Query().
		Where(
			note.UserIDEQ(userID),
			note.CreatedAtGTE(startDate),
			note.CreatedAtLTE(endDate),
		).Count(l.ctx)
}

func (l *PerformanceLogic) getMessageCount(userID int, startDate, endDate time.Time) (int, error) {
	return l.svcCtx.Ent.MessageHistory.Query().
		Where(
			messagehistory.UserIDEQ(userID),
			messagehistory.CreatedAtGTE(startDate),
			messagehistory.CreatedAtLTE(endDate),
		).Count(l.ctx)
}

func (l *PerformanceLogic) getTimeSeriesData(userID int, startDate, endDate time.Time) (types.ActivityBreakdown, error) {
	result := types.ActivityBreakdown{
		Appointments: []types.TimeSeriesData{},
		Calls:        []types.TimeSeriesData{},
		Notes:        []types.TimeSeriesData{},
		Messages:     []types.TimeSeriesData{},
		Total:        []types.TimeSeriesData{},
	}

	currentDate := startDate
	for currentDate.Before(endDate) {
		nextDate := currentDate.AddDate(0, 0, 1)
		dateStr := currentDate.Format("2006-01-02")

		counts, err := l.getDailyActivityCounts(userID, currentDate, nextDate)
		if err != nil {
			return types.ActivityBreakdown{}, err
		}

		result.Appointments = append(result.Appointments, types.TimeSeriesData{Date: dateStr, Count: counts["appointments"]})
		result.Calls = append(result.Calls, types.TimeSeriesData{Date: dateStr, Count: counts["calls"]})
		result.Notes = append(result.Notes, types.TimeSeriesData{Date: dateStr, Count: counts["notes"]})
		result.Messages = append(result.Messages, types.TimeSeriesData{Date: dateStr, Count: counts["messages"]})
		result.Total = append(result.Total, types.TimeSeriesData{Date: dateStr, Count: counts["messages"] + counts["notes"] + counts["calls"] + counts["appointments"]})

		currentDate = nextDate
	}

	return result, nil
}
