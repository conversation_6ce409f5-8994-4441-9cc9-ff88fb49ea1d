package notification

import (
	"context"

	"bcare/api/internal/dto"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/cast"

	"github.com/zeromicro/go-zero/core/logx"
)

type AddLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAddLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AddLogic {
	return &AddLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AddLogic) Add(req *types.NotificationAddRequest) (resp *types.NotificationResponse, err error) {
	input := &dto.CreateNotificationInput{
		UserID:   req.UserID,
		Type:     req.Type,
		Message:  req.Message,
		IsRead:   false,
		Metadata: req.Metadata,
	}

	// Handle optional fields
	if req.EntityID != 0 {
		input.EntityID = &req.EntityID
	}
	if req.EntityType != "" {
		input.EntityType = &req.EntityType
	}
	if req.SenderID != 0 {
		input.SenderID = &req.SenderID
	}

	notificationOutput, err := l.svcCtx.NotificationModel.Create(l.ctx, input)
	if err != nil {
		return nil, err
	}

	resp = new(types.NotificationResponse)
	err = cast.ConvertViaJson(resp, notificationOutput)
	if err != nil {
		return nil, err
	}

	return resp, nil
}
