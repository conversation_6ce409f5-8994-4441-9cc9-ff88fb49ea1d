package notification

import (
	"context"

	"bcare/api/internal/dto"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type AddBatchLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAddBatchLogic(ctx context.Context, svcCtx *svc.ServiceContext) AddBatchLogic {
	return AddBatchLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AddBatchLogic) AddBatch(req *types.NotificationBatchAddRequest) (resp *types.NotificationBatchResponse, err error) {
	input := &dto.CreateBatchNotificationInput{
		UserIDs: req.UserIDs,
		Type:    req.Type,
		Message: req.Message,
	}

	if req.EntityID != 0 {
		input.EntityID = &req.EntityID
	}
	if req.EntityType != "" {
		input.EntityType = &req.EntityType
	}
	if req.SenderID != 0 {
		input.SenderID = &req.SenderID
	}
	if req.Metadata != nil {
		input.Metadata = req.Metadata
	}

	result, err := l.svcCtx.NotificationModel.CreateBatch(l.ctx, input)
	if err != nil {
		return nil, err
	}

	return &types.NotificationBatchResponse{
		CreatedCount: result.CreatedCount,
		Message:      result.Message,
	}, nil
}
