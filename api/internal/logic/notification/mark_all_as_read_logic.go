package notification

import (
	"context"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type MarkAllAsReadLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewMarkAllAsReadLogic(ctx context.Context, svcCtx *svc.ServiceContext) MarkAllAsReadLogic {
	return MarkAllAsReadLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *MarkAllAsReadLogic) MarkAllAsRead(req *types.NotificationMarkAllAsReadRequest) error {
	_, err := l.svcCtx.NotificationModel.MarkAllAsRead(l.ctx, req.UserID) // Đảm bảo NotificationModel có trong svcCtx
	if err != nil {
		return err
	}

	return nil
}
