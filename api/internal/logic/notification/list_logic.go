package notification

import (
	"bcare/api/internal/dto"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/cast"
	"context"
	"github.com/zeromicro/go-zero/core/logx"
)

type ListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewListLogic(ctx context.Context, svcCtx *svc.ServiceContext) ListLogic {
	return ListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ListLogic) List(req *types.NotificationListRequest) (resp *types.NotificationListResponse, err error) {
	listInput := &dto.ListNotificationsInput{
		Filter: dto.NotificationInputFilter{
			UserID: req.UserID,
			IsRead: req.IsRead,
		},
		Page:     req.Page,
		PageSize: req.Limit,
		OrderBy:  req.SortBy + " " + req.Order,
	}

	listOutput, err := l.svcCtx.NotificationModel.List(l.ctx, listInput)
	if err != nil {
		return nil, err
	}

	resp = &types.NotificationListResponse{
		Data:  make([]types.Notification, 0, len(listOutput.Notifications)),
		Total: listOutput.Total,
	}

	if err := cast.ConvertViaJson(&resp.Data, listOutput.Notifications); err != nil {
		return nil, err
	}

	return resp, nil
}
