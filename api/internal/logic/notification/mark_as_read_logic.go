package notification

import (
	"context"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type MarkAsReadLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewMarkAsReadLogic(ctx context.Context, svcCtx *svc.ServiceContext) MarkAsReadLogic {
	return MarkAsReadLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *MarkAsReadLogic) MarkAsRead(req *types.NotificationMarkAsReadRequest) error {
	_, err := l.svcCtx.NotificationModel.MarkAsRead(l.ctx, req.NotificationIDs)
	if err != nil {
		return err
	}

	return nil
}
