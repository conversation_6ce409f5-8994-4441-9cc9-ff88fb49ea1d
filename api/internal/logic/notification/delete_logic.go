package notification

import (
	"context"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewDeleteLogic(ctx context.Context, svcCtx *svc.ServiceContext) DeleteLogic {
	return DeleteLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DeleteLogic) Delete(req *types.NotificationDeleteRequest) error {
	// todo: add your logic here and delete this line

	notif, err := l.svcCtx.NotificationModel.GetByID(l.ctx, req.NotificationID)
	if err != nil {
		return err
	}

	err = l.svcCtx.NotificationModel.Delete(l.ctx, notif.ID)
	if err != nil {
		return err
	}

	return nil
}
