package notification

import (
	"context"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type UnreadLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUnreadLogic(ctx context.Context, svcCtx *svc.ServiceContext) UnreadLogic {
	return UnreadLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UnreadLogic) Unread(req *types.NotificationUnreadCountRequest) (resp *types.NotificationUnreadCountResponse, err error) {
	unreadCountOutput, err := l.svcCtx.NotificationModel.GetUnreadCount(l.ctx, req.UserID)
	if err != nil {
		return nil, err
	}

	resp = &types.NotificationUnreadCountResponse{
		UnreadCount: unreadCountOutput.UnreadCount,
	}

	return resp, nil
}
