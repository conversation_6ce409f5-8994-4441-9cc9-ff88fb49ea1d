package bundle

import (
	"bcare/ent"
	"context"
	"github.com/jinzhu/copier"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetLogic(ctx context.Context, svcCtx *svc.ServiceContext) GetLogic {
	return GetLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetLogic) Get(req *types.BundleGetRequest) (resp *types.BundleResponse, err error) {
	bundle, err := l.svcCtx.Ent.Bundle.Get(l.ctx, req.Id)
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, types.ErrBundleNotFound
		}
		return nil, types.ErrBundleNotFound.Wrap(err)
	}

	resp = new(types.BundleResponse)
	err = copier.Copy(resp, bundle)
	if err != nil {
		return nil, types.ErrBundleNotFound.Wrap(err)
	}

	return resp, nil
}
