package bundle

import (
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/bquery"
	"bcare/ent"
	"context"
	"github.com/jinzhu/copier"

	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewDeleteLogic(ctx context.Context, svcCtx *svc.ServiceContext) DeleteLogic {
	return DeleteLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DeleteLogic) Delete(req *types.BundleGetRequest) (resp *types.BundleResponse, err error) {
	bundleRecord := new(ent.Bundle)

	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		bundleRecord, err = tx.Bundle.Get(l.ctx, req.Id)
		if err != nil {
			if ent.IsNotFound(err) {
				return types.ErrBundleNotFound
			}
			return err
		}

		// Perform soft delete
		err = tx.Bundle.DeleteOne(bundleRecord).Exec(l.ctx)

		if err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		return nil, types.ErrBundleUpdate.Wrap(err)
	}

	// Xử lý trả resp ở ngoài transaction
	resp = new(types.BundleResponse)
	err = copier.Copy(resp, bundleRecord)
	if err != nil {
		return nil, types.ErrBundleUpdate.Wrap(err)
	}

	return resp, nil
}
