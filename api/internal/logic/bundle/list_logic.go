package bundle

import (
	"bcare/common/bquery"
	"bcare/ent/bundle"
	"context"
	"github.com/jinzhu/copier"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type ListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewListLogic(ctx context.Context, svcCtx *svc.ServiceContext) ListLogic {
	return ListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ListLogic) List(req *types.BundleListRequest) (resp *types.BundleListResponse, err error) {
	limit, offset := bquery.Paging(req.PageSize, req.Page)
	order := bquery.Ordering(req.OrderBy)
	query := l.svcCtx.Ent.Bundle.Query().Order(order)
	if req.Filter.Name != "" {
		query = query.Where(bundle.NameContainsFold(req.Filter.Name))
	}

	if req.Filter.Type != "" {
		query = query.Where(bundle.TypeContainsFold(req.Filter.Type))
	}

	if req.Filter.Status != 0 {
		query = query.Where(bundle.Status(int8(req.Filter.Status)))
	}

	total, err := query.Count(l.ctx)
	if err != nil {
		return nil, types.ErrBundleGetList.Wrap(err)
	}

	query = query.Limit(limit).Offset(offset)
	bundles, err := query.All(l.ctx)
	if err != nil {
		return nil, types.ErrBundleGetList.Wrap(err)
	}

	if len(bundles) == 0 {
		return nil, types.ErrBundleNotFound
	}

	resp = new(types.BundleListResponse)
	err = copier.Copy(&resp.Bundles, bundles)
	if err != nil {
		return nil, types.ErrBundleGetList.Wrap(err)
	}

	resp.Total = total

	return resp, nil
}
