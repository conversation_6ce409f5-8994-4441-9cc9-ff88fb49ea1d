package bundle

import (
	"bcare/common/bquery"
	"bcare/ent"
	"context"
	"github.com/blazy-vn/slug"
	"github.com/jinzhu/copier"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateLogic(ctx context.Context, svcCtx *svc.ServiceContext) UpdateLogic {
	return UpdateLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateLogic) Update(req *types.BundleUpdateRequest) (resp *types.BundleResponse, err error) {
	bundleRecord := new(ent.Bundle)

	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		bundleRecord, err = tx.Bundle.Get(l.ctx, req.Id)
		if err != nil {
			if ent.IsNotFound(err) {
				return types.ErrBundleUpdate
			}
			return err
		}
		bundleRecord.Name = req.Name
		bundleRecord.Description = req.Description

		if bundleRecord.MachineName == "" {
			bundleRecord.MachineName = slug.Make(bundleRecord.Name)
		}

		bundleRecord, err = tx.Bundle.UpdateOne(bundleRecord).
			SetBundle(bundleRecord, req.Modified...).
			Save(l.ctx)

		if err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		return nil, types.ErrBundleUpdate.Wrap(err)
	}

	resp = new(types.BundleResponse)
	err = copier.Copy(resp, bundleRecord)
	if err != nil {
		return nil, types.ErrBundleUpdate.Wrap(err)
	}

	return resp, nil
}
