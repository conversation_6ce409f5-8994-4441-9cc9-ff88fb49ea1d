package bundle

import (
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/bquery"
	cast2 "bcare/common/cast"
	"bcare/ent"
	"context"
	"github.com/blazy-vn/slug"
	"github.com/jinzhu/copier"

	"github.com/zeromicro/go-zero/core/logx"
)

type AddLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAddLogic(ctx context.Context, svcCtx *svc.ServiceContext) AddLogic {
	return AddLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AddLogic) Add(req *types.BundleAddRequest) (resp *types.BundleResponse, err error) {
	bundleRecord := new(ent.Bundle)

	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		err = copier.CopyWithOption(&bundleRecord, req, cast2.PayloadToEnt)
		if err != nil {
			return err
		}

		if bundleRecord.MachineName == "" {
			bundleRecord.MachineName = slug.Make(bundleRecord.Name)
		}

		bundleRecord, err = tx.Bundle.Create().
			SetBundle(bundleRecord).
			Save(l.ctx)

		if err != nil {
			return err
		}

		resp = new(types.BundleResponse)
		_ = copier.Copy(resp, bundleRecord)

		return nil
	})

	if err != nil {
		return nil, types.ErrBundleAdd.Wrap(err)
	}

	return resp, nil
}
