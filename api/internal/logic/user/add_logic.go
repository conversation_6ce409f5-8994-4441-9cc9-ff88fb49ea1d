package user

import (
	"bcare/api/internal/dto"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/berr"
	"bcare/common/bquery"
	"bcare/common/cast"
	"bcare/ent"
	"context"
	"strconv"

	"github.com/zeromicro/go-zero/core/logx"
)

type AddLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAddLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AddLogic {
	return &AddLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AddLogic) Add(req *types.UserAddRequest) (resp *types.UserResponse, err error) {
	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		createUserInput := new(dto.CreateUserInput)
		if errMap := cast.ReqToModelInput(createUserInput, req); errMap != nil {
			return berr.ErrRequestParam.Wrap(errMap)
		}

		if !l.svcCtx.Auth.User.CanAdd(l.ctx) {
			return berr.ErrPermissionDenied
		}

		createdUserOutput, modelErr := l.svcCtx.UserModel.CreateUser(l.ctx, createUserInput)
		if modelErr != nil {
			return modelErr
		}

		if len(req.Roles) > 0 {
			_, roleErr := l.svcCtx.Enforcer.AddRolesForUser(strconv.Itoa(createdUserOutput.ID), req.Roles)
			if roleErr != nil {
				return berr.ErrServerCommon.Wrap(roleErr).Op("Enforcer.AddRolesForUser")
			}
		}

		resp = new(types.UserResponse)
		if errMapResp := cast.ModelOutputToResp(resp, createdUserOutput); errMapResp != nil {
			return berr.ErrServerCommon.Wrap(errMapResp)
		}
		resp.Roles = req.Roles

		return nil
	})

	if err != nil {
		return nil, err
	}

	return resp, nil
}
