package user

import (
	"bcare/api/internal/dto"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/berr"
	"bcare/common/cast"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type ListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ListLogic {
	return &ListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ListLogic) List(req *types.UserListRequest) (resp *types.UserListResponse, err error) {
	if !l.svcCtx.Auth.User.CanList(l.ctx) {
		return nil, berr.ErrPermissionDenied.Wrap(nil)
	}

	input := new(dto.ListUsersInput)
	err = cast.ReqToModelInput(input, req)
	if err != nil {
		return nil, err
	}

	listOutput, err := l.svcCtx.UserModel.ListUsers(l.ctx, *input)
	if err != nil {
		return nil, err
	}

	resp = &types.UserListResponse{
		Users: make([]types.UserResponse, 0, len(listOutput.Users)),
		Total: listOutput.Total,
	}

	err = cast.ConvertViaJson(&resp.Users, listOutput.Users)
	if err != nil {
		return nil, err
	}

	return resp, nil
}
