package user

import (
	"bcare/api/internal/auth"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/berr"
	"bcare/common/bquery"
	"bcare/ent"
	"context"
	"strconv"

	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewDeleteLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteLogic {
	return &DeleteLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DeleteLogic) Delete(req *types.UserDeleteRequest) (err error) {
	userOutputForAuth, getErr := l.svcCtx.UserModel.GetUser(l.ctx, req.Id)
	if getErr != nil {
		return getErr
	}

	if !l.svcCtx.Auth.User.CanDelete(l.ctx, userOutputForAuth) {
		return auth.ErrUserDeleteDenied
	}

	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		modelErr := l.svcCtx.UserModel.DeleteUser(l.ctx, req.Id)
		if modelErr != nil {
			return modelErr
		}

		_, rolesErr := l.svcCtx.Enforcer.DeleteRolesForUser(strconv.Itoa(req.Id))
		if rolesErr != nil {
			return berr.ErrServerCommon.Wrap(rolesErr)
		}

		return nil
	})

	if err != nil {
		return err
	}

	return nil
}
