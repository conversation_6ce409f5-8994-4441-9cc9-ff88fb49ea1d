package user

import (
	"bcare/api/internal/auth"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/cast"
	"context"
	"strconv"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetLogic {
	return &GetLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetLogic) Get(req *types.UserGetRequest) (resp *types.UserResponse, err error) {
	userOutput, getErr := l.svcCtx.UserModel.GetUser(l.ctx, req.Id)
	if getErr != nil {
		return nil, getErr
	}

	if !l.svcCtx.Auth.User.CanUpdate(l.ctx, userOutput) {
		return nil, auth.ErrUserUpdateDenied
	}

	roles, roleErr := l.svcCtx.Enforcer.GetRolesForUser(strconv.Itoa(userOutput.ID))
	if roleErr != nil {
		return nil, err
	}

	resp = new(types.UserResponse)
	if mapErr := cast.ModelOutputToResp(resp, userOutput); mapErr != nil {
		return nil, err
	}
	resp.Roles = roles
	if resp.Roles == nil {
		resp.Roles = []string{}
	}

	return resp, nil
}
