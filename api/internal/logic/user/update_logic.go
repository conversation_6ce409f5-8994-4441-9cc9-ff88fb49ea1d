package user

import (
	"bcare/api/internal/auth"
	"bcare/api/internal/dto"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/berr"
	"bcare/common/bquery"
	"bcare/common/butils"
	"bcare/common/cast"
	"bcare/ent"
	"context"
	"strconv"

	"github.com/zeromicro/go-zero/core/logx"
	// "bcare/ent" // No longer needed for Get in Tx
)

type UpdateLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateLogic {
	return &UpdateLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateLogic) Update(req *types.UserUpdateRequest) (resp *types.UserResponse, err error) {
	var updatedUserOutput *dto.UserOutput

	currentUserOutput, getErr := l.svcCtx.UserModel.GetUser(l.ctx, req.Id)
	if getErr != nil {
		// If user not found, return the error (likely berr.ErrNotFound from GetUser)
		return nil, getErr
	}

	if !l.svcCtx.Auth.User.CanUpdate(l.ctx, currentUserOutput) {
		return nil, auth.ErrUserUpdateDenied
	}

	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		updateUserInput := new(dto.UpdateUserInput)
		if errMap := cast.ReqToModelInput(updateUserInput, req); errMap != nil {
			return berr.ErrRequestParam.Wrap(errMap)
		}
		updateUserInput.ID = req.Id

		var modelErr error
		updatedUserOutput, modelErr = l.svcCtx.UserModel.UpdateUser(l.ctx, updateUserInput)
		if modelErr != nil {
			return modelErr
		}

		if len(req.Roles) > 0 || butils.Contains(req.Modified, "roles") {
			_, delRolesErr := l.svcCtx.Enforcer.DeleteRolesForUser(strconv.Itoa(updatedUserOutput.ID))
			if delRolesErr != nil {
				return berr.ErrServerCommon.Wrap(delRolesErr)
			}
			_, addRolesErr := l.svcCtx.Enforcer.AddRolesForUser(strconv.Itoa(updatedUserOutput.ID), req.Roles)
			if addRolesErr != nil {
				return berr.ErrServerCommon.Wrap(addRolesErr)
			}
		}

		if policyErr := l.svcCtx.Enforcer.LoadPolicy(); policyErr != nil {
			return berr.ErrServerCommon.Wrap(policyErr)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	resp = new(types.UserResponse)
	if updatedUserOutput == nil {
		// This case should ideally not happen if transaction succeeded
		return nil, berr.ErrServerCommon
	}
	if mapErr := cast.ModelOutputToResp(resp, updatedUserOutput); mapErr != nil {
		return nil, berr.ErrServerCommon.Wrap(mapErr)
	}

	finalRoles, rolesErr := l.svcCtx.Enforcer.GetRolesForUser(strconv.Itoa(updatedUserOutput.ID))
	if rolesErr != nil {
		resp.Roles = []string{}
	} else {
		resp.Roles = finalRoles
	}
	if resp.Roles == nil {
		resp.Roles = []string{}
	}

	return resp, nil
}
