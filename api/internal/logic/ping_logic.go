package logic

//
//import (
//	"bcare/api/internal/eventbus/event"
//	"bcare/api/internal/svc"
//	"bcare/api/internal/task"
//	"bcare/api/internal/task/tasks"
//	"bcare/api/internal/types"
//	"context"
//	"github.com/hibiken/asynq"
//	"time"
//
//	"github.com/zeromicro/go-zero/core/logx"
//)
//
//type PingLogic struct {
//	logx.Logger
//	ctx    context.Context
//	svcCtx *svc.ServiceContext
//}
//
//func NewPingLogic(ctx context.Context, svcCtx *svc.ServiceContext) PingLogic {
//	return PingLogic{
//		Logger: logx.WithContext(ctx),
//		ctx:    ctx,
//		svcCtx: svcCtx,
//	}
//}
//
//func (l *PingLogic) Ping(req *types.PingReq) (resp *types.PongResp, err error) {
//	resp = &types.PongResp{Pong: "pong"}
//
//	t, err := tasks.NewPingTask(event.Ping{Hello: "Lamdaica"})
//	if err != nil {
//		logx.WithContext(l.ctx).Errorf("could not create task: %v", err)
//	}
//
//	_, err = task.Client.Enqueue(t, asynq.ProcessIn(5*time.Second))
//	if err != nil {
//		logx.WithContext(l.ctx).Errorf("could not enqueue task: %v", err)
//	}
//
//	if err != nil {
//		l.Logger.Errorf("eventbus.Eventbus.Publish err: %v", err)
//		return nil, err
//	}
//
//	return
//}
