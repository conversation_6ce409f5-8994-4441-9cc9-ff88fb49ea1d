package cash_flow_item

import (
	"bcare/api/internal/dto"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/berr"
	"bcare/common/cast"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type ListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewListLogic(ctx context.Context, svcCtx *svc.ServiceContext) ListLogic {
	return ListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ListLogic) List(req *types.CashFlowItemListRequest) (resp *types.CashFlowItemListResponse, err error) {
	if !l.svcCtx.Auth.CashFlowItem.CanList(l.ctx) {
		return nil, berr.ErrPermissionDenied.Wrap(nil)
	}

	input := new(dto.ListCashFlowItemsInput)
	err = cast.ReqToModelInput(input, req)
	if err != nil {
		return nil, err
	}

	listOutput, err := l.svcCtx.CashFlowItemModel.ListCashFlowItems(l.ctx, *input)
	if err != nil {
		return nil, err
	}

	resp = &types.CashFlowItemListResponse{
		Items:     make([]types.CashFlowItemResponse, 0, len(listOutput.Items)),
		Total:     listOutput.Total,
		TotalPage: listOutput.TotalPage,
	}

	err = cast.ConvertViaJson(&resp.Items, listOutput.Items)
	if err != nil {
		return nil, err
	}

	return resp, nil
}
