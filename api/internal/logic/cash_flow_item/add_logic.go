package cash_flow_item

import (
	"bcare/api/internal/dto"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/berr"
	"bcare/common/cast"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type AddLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAddLogic(ctx context.Context, svcCtx *svc.ServiceContext) AddLogic {
	return AddLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AddLogic) Add(req *types.CashFlowItemAddRequest) (resp *types.CashFlowItemResponse, err error) {
	if !l.svcCtx.Auth.CashFlowItem.CanAdd(l.ctx) {
		return nil, berr.ErrPermissionDenied.Wrap(nil)
	}

	input := new(dto.CreateCashFlowItemInput)
	err = cast.ReqToModelInput(input, req)
	if err != nil {
		return nil, err
	}

	itemOutput, err := l.svcCtx.CashFlowItemModel.CreateCashFlowItem(l.ctx, input)
	if err != nil {
		return nil, err
	}

	resp = new(types.CashFlowItemResponse)
	err = cast.ConvertViaJson(resp, itemOutput)
	if err != nil {
		return nil, err
	}

	return resp, nil
}
