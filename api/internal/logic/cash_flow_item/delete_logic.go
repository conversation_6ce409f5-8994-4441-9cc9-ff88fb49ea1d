package cash_flow_item

import (
	"bcare/api/internal/dto"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/berr"
	"bcare/common/cast"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewDeleteLogic(ctx context.Context, svcCtx *svc.ServiceContext) DeleteLogic {
	return DeleteLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DeleteLogic) Delete(req *types.CashFlowItemDeleteRequest) (resp *types.CashFlowItemResponse, err error) {
	existingItem, err := l.svcCtx.CashFlowItemModel.GetCashFlowItem(l.ctx, dto.GetCashFlowItemInput{ID: req.Id})
	if err != nil {
		return nil, err
	}

	if !l.svcCtx.Auth.CashFlowItem.CanDelete(l.ctx, existingItem) {
		return nil, berr.ErrPermissionDenied.Wrap(nil)
	}

	err = l.svcCtx.CashFlowItemModel.DeleteCashFlowItem(l.ctx, dto.DeleteCashFlowItemInput{ID: req.Id})
	if err != nil {
		return nil, err
	}

	resp = new(types.CashFlowItemResponse)
	err = cast.ConvertViaJson(resp, existingItem)
	if err != nil {
		return nil, err
	}

	return resp, nil
}
