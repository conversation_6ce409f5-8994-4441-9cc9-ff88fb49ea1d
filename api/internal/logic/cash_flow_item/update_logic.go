package cash_flow_item

import (
	"bcare/api/internal/dto"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/berr"
	"bcare/common/cast"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateLogic(ctx context.Context, svcCtx *svc.ServiceContext) UpdateLogic {
	return UpdateLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateLogic) Update(req *types.CashFlowItemUpdateRequest) (resp *types.CashFlowItemResponse, err error) {
	existingItem, err := l.svcCtx.CashFlowItemModel.GetCashFlowItem(l.ctx, dto.GetCashFlowItemInput{ID: req.Id})
	if err != nil {
		return nil, err
	}

	if !l.svcCtx.Auth.CashFlowItem.CanUpdate(l.ctx, existingItem) {
		return nil, berr.ErrPermissionDenied.Wrap(nil)
	}

	input := new(dto.UpdateCashFlowItemInput)
	err = cast.ReqToModelInput(input, req)
	if err != nil {
		return nil, err
	}

	itemOutput, err := l.svcCtx.CashFlowItemModel.UpdateCashFlowItem(l.ctx, input)
	if err != nil {
		return nil, err
	}

	resp = new(types.CashFlowItemResponse)
	err = cast.ConvertViaJson(resp, itemOutput)
	if err != nil {
		return nil, err
	}

	return resp, nil
}
