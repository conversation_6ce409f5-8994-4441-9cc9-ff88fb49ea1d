package cash_flow_item

import (
	"bcare/api/internal/dto"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/berr"
	"bcare/common/cast"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetLogic(ctx context.Context, svcCtx *svc.ServiceContext) GetLogic {
	return GetLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetLogic) Get(req *types.CashFlowItemGetRequest) (resp *types.CashFlowItemResponse, err error) {
	input := dto.GetCashFlowItemInput{
		ID:              req.Id,
		IncludeRelation: req.IncludeRelation,
	}

	itemOutput, err := l.svcCtx.CashFlowItemModel.GetCashFlowItem(l.ctx, input)
	if err != nil {
		return nil, err
	}

	if !l.svcCtx.Auth.CashFlowItem.CanGet(l.ctx, itemOutput) {
		return nil, berr.ErrPermissionDenied.Wrap(nil)
	}

	resp = new(types.CashFlowItemResponse)
	err = cast.ConvertViaJson(resp, itemOutput)
	if err != nil {
		return nil, err
	}

	return resp, nil
}
