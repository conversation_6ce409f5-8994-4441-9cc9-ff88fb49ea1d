package download

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"path/filepath"
	"strings"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type DownloadFileLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewDownloadFileLogic(ctx context.Context, svcCtx *svc.ServiceContext) DownloadFileLogic {
	return DownloadFileLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DownloadFileLogic) DownloadFile(w http.ResponseWriter, r *http.Request, req *types.DownloadFileRequest) error {
	// Sanitize file path
	filePath := filepath.Clean(req.Path)
	if strings.Contains(filePath, "..") {
		//return errors.New("invalid file path")
		return fmt.Errorf("invalid file path")
	}

	// Combine với base path từ config
	fullPath := filepath.Join(l.svcCtx.Config.Storage.Local.Path, filePath)
	// Kiểm tra file có tồn tại
	if _, err := os.Stat(fullPath); os.IsNotExist(err) {
		return fmt.Errorf("file not found")
	}

	// Mở file
	file, err := os.Open(fullPath)
	if err != nil {
		return fmt.Errorf("failed to open file: %v", err)
	}
	defer file.Close()

	// Lấy thông tin file
	fileInfo, err := file.Stat()
	if err != nil {
		return fmt.Errorf("failed to get file info: %v", err)
	}

	// Set headers
	w.Header().Set("Content-Disposition", fmt.Sprintf("attachment; filename=%s", filepath.Base(filePath)))
	w.Header().Set("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	w.Header().Set("Content-Length", fmt.Sprintf("%d", fileInfo.Size()))

	// Copy file to response writer
	http.ServeFile(w, r, fullPath)
	return nil
}
