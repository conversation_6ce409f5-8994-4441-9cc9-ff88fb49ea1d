package tag_deal

import (
	"bcare/common/bquery"
	cast2 "bcare/common/cast"
	"bcare/ent"
	"context"
	"github.com/jinzhu/copier"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateLogic(ctx context.Context, svcCtx *svc.ServiceContext) UpdateLogic {
	return UpdateLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateLogic) Update(req *types.TagDealUpdateRequest) (resp *types.TagDeal, err error) {
	tagDealRecord := new(ent.TagDeal)
	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		tagDealRecord, err = tx.TagDeal.Get(l.ctx, req.Id)
		if err != nil {
			return err
		}

		err = copier.CopyWithOption(&tagDealRecord, req, cast2.PayloadToEnt)
		if err != nil {
			return err
		}

		tagDealRecord, err = tx.TagDeal.UpdateOneID(tagDealRecord.ID).SetTagDeal(tagDealRecord).Save(l.ctx)

		if err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	resp = new(types.TagDeal)
	_ = copier.Copy(&resp, tagDealRecord)
	return resp, nil
}
