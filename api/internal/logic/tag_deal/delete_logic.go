package tag_deal

import (
	"bcare/common/bquery"
	"bcare/ent"
	"bcare/ent/tagdeal"
	"context"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewDeleteLogic(ctx context.Context, svcCtx *svc.ServiceContext) DeleteLogic {
	return DeleteLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DeleteLogic) Delete(req *types.TagDealDeleteRequest) (resp *types.TagDeal, err error) {
	var tagDealRecord *ent.TagDeal

	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		tagDealRecord, err = tx.TagDeal.Query().Where(tagdeal.TagID(req.TagId), tagdeal.DealID(req.DealId)).First(l.ctx)

		if err != nil && ent.IsNotFound(err) {
			return err
		}

		err = tx.TagDeal.DeleteOne(tagDealRecord).Exec(l.ctx)
		if err != nil {
			return err
		}
		return nil
	})

	if err != nil {
		return nil, err
	}

	return resp, nil
}
