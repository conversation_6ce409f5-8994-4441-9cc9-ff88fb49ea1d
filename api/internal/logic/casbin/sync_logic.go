package casbin

import (
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/ctxdata"
	"context"
	"github.com/casbin/casbin/v2"
	"github.com/zeromicro/go-zero/core/logx"
	"strconv"
)

type SyncLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewSyncLogic(ctx context.Context, svcCtx *svc.ServiceContext) SyncLogic {
	return SyncLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *SyncLogic) Sync() (resp *types.SyncResponse, err error) {
	userID := strconv.FormatInt(int64(ctxdata.GetUidFromCtx(l.ctx)), 10)

	perm, err := casbin.CasbinJsGetPermissionForUser(l.svcCtx.Enforcer, userID)

	if err != nil {
		logx.WithContext(l.ctx).Errorf("Casbin sync error: %v", err)
		return nil, err
	}

	resp = new(types.SyncResponse)
	resp.Perm = perm
	return resp, nil
}
