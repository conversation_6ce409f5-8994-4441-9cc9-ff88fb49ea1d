package casbin

import (
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/ent/setting"
	"context"
	"github.com/zeromicro/go-zero/core/logx"
)

type RoleListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewRoleListLogic(ctx context.Context, svcCtx *svc.ServiceContext) RoleListLogic {
	return RoleListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *RoleListLogic) RoleList() (resp []types.Role, err error) {
	roles, err := l.svcCtx.Ent.Setting.Query().Where(setting.Category("roles")).All(l.ctx)
	if err != nil {
		return nil, err
	}

	resp = make([]types.Role, 0)
	for _, roleData := range roles {
		role := types.Role{
			Name:        roleData.Value["name"].(string),
			Description: roleData.Value["description"].(string),
			Parent:      roleData.Value["parent"].(string),
		}
		resp = append(resp, role)
	}

	return resp, nil
}
