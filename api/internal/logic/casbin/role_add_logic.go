package casbin

import (
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/ent"
	"bcare/ent/setting"
	"context"
	"fmt"
	"strings"

	"github.com/zeromicro/go-zero/core/logx"
)

type RoleAddLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewRoleAddLogic(ctx context.Context, svcCtx *svc.ServiceContext) RoleAddLogic {
	return RoleAddLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *RoleAddLogic) RoleAdd(req *types.Role) (resp *types.Role, err error) {
	role, e := l.svcCtx.Ent.Setting.Query().
		Where(setting.Category("roles"), setting.Name(req.Name)).Only(l.ctx)

	if e == nil && role != nil {
		return nil, fmt.<PERSON><PERSON><PERSON>("role %s existed", req.Name)
	}

	req.Name = strings.ToLower(req.Name)
	req.Parent = strings.ToLower(req.Parent)

	// Convert req to map[string]interface{}
	roleData := map[string]interface{}{
		"name":        req.Name,
		"parent":      req.Parent,
		"description": req.Description,
	}

	e = l.svcCtx.Ent.Setting.Create().SetSetting(&ent.Setting{
		Category: "roles",
		Name:     req.Name,
		Value:    roleData,
	}).Exec(l.ctx)

	if e != nil {
		return nil, e
	}

	return req, nil
}
