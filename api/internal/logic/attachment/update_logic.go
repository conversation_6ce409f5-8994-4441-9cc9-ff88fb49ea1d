package attachment

import (
	"bcare/api/internal/auth"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/api/model"
	"bcare/common/bconst"
	"bcare/common/bquery"
	cast2 "bcare/common/cast"
	"bcare/common/ctxdata"
	"bcare/ent"
	"bcare/ent/attachment"
	"bcare/ent/discountusage"
	"bcare/ent/installmentplan"
	"context"
	"github.com/jinzhu/copier"
	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateLogic(ctx context.Context, svcCtx *svc.ServiceContext) UpdateLogic {
	return UpdateLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateLogic) Update(req *types.AttachmentUpdateRequest) (resp *types.AttachmentResponse, err error) {
	var attachmentRecord *ent.Attachment

	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		// Get the parent attachment
		attachmentRecord, err = tx.Attachment.Get(l.ctx, req.Id)
		if err != nil && ent.IsNotFound(err) {
			return types.ErrAttachmentNotFound.Wrap(err, req.Id)
		}

		if attachmentRecord == nil {
			return types.ErrAttachmentNotFound.Wrap(nil, req.Id)
		}
		//kiểm tra xem có quyền ko
		if !l.svcCtx.Auth.Attachment.CanUpdate(l.ctx, attachmentRecord) {
			logx.WithContext(l.ctx).Infof("Bạn ko có quyền")
			return auth.ErrAttachmentUpdateDenied

		}
		originAttachment := new(ent.Attachment)
		_ = copier.Copy(originAttachment, attachmentRecord)

		// Copy updated fields to the attachment record
		err = copier.CopyWithOption(&attachmentRecord, req, cast2.PayloadToEnt)
		if err != nil {
			return types.ErrAttachmentCopyFailed.Wrap(err, err.Error())
		}

		// Set user ID
		attachmentRecord.UserID = ctxdata.GetUidFromCtx(l.ctx)

		if originAttachment.Status == bconst.StatusTemp && attachmentRecord.Status == bconst.StatusUnpaid {
			discountAmount := fetchAttachmentDiscount(l.svcCtx.Ent.DiscountUsage.Query(), attachmentRecord.ID, l.ctx)
			if discountAmount > 0 {
				attachmentRecord.Discount = discountAmount * float64(originAttachment.Quantity)
			}
		}

		if attachmentRecord.DealID != nil && attachmentRecord.PlanID == nil {
			plan, _ := tx.InstallmentPlan.Query().Where(installmentplan.DealID(*attachmentRecord.DealID)).First(l.ctx)
			if plan != nil {
				attachmentRecord.PlanID = &plan.ID
			}
		}
		// Update the parent attachment
		attachmentRecord, err = tx.Attachment.UpdateOneID(req.Id).
			SetAttachment(attachmentRecord, req.Modified...).
			Save(l.ctx)
		if err != nil {
			return types.ErrAttachmentUpdate.Wrap(err, err.Error())
		}

		// If attachment has a deal_id, directly update all child attachments in a single query
		if attachmentRecord.DealID != nil {
			_, err = tx.Attachment.Update().
				Where(attachment.ParentIDEQ(req.Id)).
				SetDealID(*attachmentRecord.DealID).
				Save(l.ctx)
			if err != nil {
				return types.ErrAttachmentUpdate.Wrap(err, "failed to update child attachments")
			}

		}

		return nil
	})
	if err != nil {
		return nil, err
	}

	if attachmentRecord.DealID != nil {
		_ = model.UpdateTotalAmount(l.ctx, l.svcCtx.Ent, *attachmentRecord.DealID)
	}

	// Prepare response
	resp = new(types.AttachmentResponse)
	_ = cast2.EntToResp(&resp, attachmentRecord)

	// Include product information if available
	if attachmentRecord.ProductID != nil {
		productRecord, _ := l.svcCtx.Ent.Product.Get(l.ctx, *attachmentRecord.ProductID)
		_ = cast2.EntToResp(&resp.Product, productRecord)
	}

	return resp, nil
}

func fetchAttachmentDiscount(q *ent.DiscountUsageQuery, attachmentID int, ctx context.Context) float64 {
	discountAmount := 0.0
	discountUsages, err := q.
		Where(discountusage.AttachmentID(attachmentID)).
		All(ctx)
	if err != nil {
		return discountAmount
	}
	for _, du := range discountUsages {
		discountAmount += du.Value
	}
	return discountAmount
}
