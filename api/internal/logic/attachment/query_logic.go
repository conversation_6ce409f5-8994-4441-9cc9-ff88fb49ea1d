package attachment

import (
	"bcare/common/bconst"
	"bcare/common/bquery"
	cast2 "bcare/common/cast"
	"bcare/ent"
	"bcare/ent/billitem"
	"bcare/ent/product"
	"bcare/ent/schema"
	"context"
	"github.com/jinzhu/copier"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type QueryLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewQueryLogic(ctx context.Context, svcCtx *svc.ServiceContext) QueryLogic {
	return QueryLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *QueryLogic) Query(req *types.AttachmentDynamicQuery) (resp *types.DynamicQueryResponse, err error) {
	resp = new(types.DynamicQueryResponse)

	dQuery := &bquery.DynamicQuery{}
	err = copier.Copy(dQuery, req)
	if err != nil {
		logx.WithContext(l.ctx).Errorf("unable to copy request: %v", err)
		return resp, err
	}

	query := l.svcCtx.Ent.Attachment.Query().Modify(bquery.ApplyDynamicQuery(dQuery)).
		WithParent().
		WithData().
		WithProduct(func(q *ent.ProductQuery) {
			q.Select(product.FieldID, product.FieldName) // Only select ID and Name fields
		}).
		WithBillItem(func(q *ent.BillItemQuery) {
			q.Where(billitem.StatusEQ(bconst.StatusNormal))
			q.Select(billitem.FieldID, billitem.FieldAmount) // Chỉ lấy các trường cần thiết
		})

	result := map[string]any{}
	aliases := make(map[string]bool)
	for _, agg := range req.Aggregations {
		if agg.Alias != "" {
			aliases[agg.Alias] = true
		}
	}

	skipSoftDelete := schema.SkipSoftDelete(l.ctx)

	if req.Limit <= 1 {
		r, err := query.First(skipSoftDelete)
		if err != nil && !ent.IsNotFound(err) {
			logx.WithContext(l.ctx).Errorf("unable to fetch attachment record: %v", err)
			return resp, err
		}

		if r != nil {
			for alias := range aliases {
				result[alias], _ = r.Value(alias)
			}
		}
	} else {
		rows, err := query.All(skipSoftDelete)
		if err != nil {
			logx.WithContext(l.ctx).Errorf("unable to fetch attachment records: %v", err)
			return resp, err
		}

		resultList := make([]map[string]any, len(rows))
		for i, r := range rows {
			//totalPayment := 0.0
			//for _, billItem := range r.Edges.BillItems {
			//	totalPayment += billItem.Amount
			//}

			rowResult := make(map[string]any)
			rowResult["record"] = cast2.Atom(&rowResult, r)
			for alias := range aliases {
				rowResult[alias], _ = r.Value(alias)
			}
			//rowResult["payment"] = totalPayment
			resultList[i] = rowResult
		}
		result["rows"] = resultList
	}

	resp.Result = result
	return
}
