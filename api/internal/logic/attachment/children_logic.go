package attachment

import (
	"bcare/common/bquery"
	cast2 "bcare/common/cast"
	"bcare/ent"
	"bcare/ent/attachment"
	"context"
	"github.com/samber/lo"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type ChildrenLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewChildrenLogic(ctx context.Context, svcCtx *svc.ServiceContext) ChildrenLogic {
	return ChildrenLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ChildrenLogic) Children(req *types.AttachmentGetRequest) (resp []types.AttachmentResponse, err error) {
	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		query := tx.Attachment.Query().
			WithData().
			WithProduct().
			WithBillItem().
			WithCreator().
			Where(attachment.ParentID(req.Id))
		attachments, e := query.All(l.ctx)

		if e != nil {
			err = types.ErrAttachmentGetChildren.Wrap(e, e.Error())
			return err
		}
		resp = lo.Map(attachments, func(at *ent.Attachment, _ int) types.AttachmentResponse {
			a := types.AttachmentResponse{}
			_ = cast2.ConvertViaJson(&a, at)
			return a
		})
		return nil
	})

	if err != nil {
		return nil, err
	}
	return resp, nil
}
