package attachment

import (
	"bcare/api/internal/auth"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/api/model"
	"bcare/common/bconst"
	"bcare/common/bquery"
	"bcare/ent"
	"bcare/ent/attachment"
	"bcare/ent/billitem"
	"context"
	"fmt"

	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewDeleteLogic(ctx context.Context, svcCtx *svc.ServiceContext) DeleteLogic {
	return DeleteLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DeleteLogic) Delete(req *types.AttachmentDeleteRequest) (resp *types.AttachmentResponse, err error) {
	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		// Get attachment record within transaction
		attachmentRecord, err := tx.Attachment.Get(l.ctx, req.Id)
		if err != nil && !ent.IsNotFound(err) {
			return types.ErrAttachmentNotFound.Wrap(err, req.Id)
		}

		if attachmentRecord == nil {
			logx.Errorf("attachment not found")
			return types.ErrAttachmentNotFound.Wrap(err, req.Id)
		}

		// Check permission
		if !l.svcCtx.Auth.Attachment.CanDelete(l.ctx, attachmentRecord) {
			logx.WithContext(l.ctx).Infof("Permission denied")
			return auth.ErrAttachmentDeleteDenied
		}

		// Query and delete related bill item within transaction
		billItemRecord, err := tx.BillItem.Query().WithAllocations().
			Where(billitem.AttachmentID(attachmentRecord.ID)).
			First(l.ctx)

		if billItemRecord != nil {
			if len(billItemRecord.Edges.Allocations) > 0 {
				return types.ErrBillItemDelete.Wrap(fmt.Errorf("cannot delete bill item: contains existing allocations"))
			}
			err = tx.BillItem.DeleteOneID(billItemRecord.ID).Exec(l.ctx)
			if err != nil {
				return types.ErrAttachmentUpdate.Wrap(err)
			}
		}

		// Query child operation attachments
		childAttachments, err := tx.Attachment.Query().
			Where(
				attachment.ParentID(attachmentRecord.ID),
				attachment.KindEQ(attachment.KindOperation),
			).All(l.ctx)

		if err != nil && !ent.IsNotFound(err) {
			return types.ErrAttachmentUpdate.Wrap(err, "failed to query child attachments")
		}

		// Delete child operation attachments and their related bill items
		for _, childAttachment := range childAttachments {
			// Delete related bill item for child attachment
			childBillItem, err := tx.BillItem.Query().
				Where(billitem.AttachmentID(childAttachment.ID)).
				First(l.ctx)
			if err != nil && !ent.IsNotFound(err) {
				return types.ErrAttachmentUpdate.Wrap(err, "failed to query child bill item")
			}

			if childBillItem != nil {
				err = tx.BillItem.DeleteOneID(childBillItem.ID).Exec(l.ctx)
				if err != nil {
					return types.ErrAttachmentUpdate.Wrap(err, "failed to delete child bill item")
				}
			}

			// Delete child attachment
			err = tx.Attachment.DeleteOneID(childAttachment.ID).Exec(l.ctx)
			if err != nil {
				return types.ErrAttachmentUpdate.Wrap(err, "failed to delete child attachment")
			}
		}

		attachmentRecord.Status = bconst.StatusDeleted

		// Delete main attachment within transaction
		err = tx.Attachment.DeleteOne(attachmentRecord).Exec(l.ctx)
		if err != nil {
			return types.ErrAttachmentUpdate.Wrap(err, err.Error())
		}
		// Update total amount if deal ID exists
		if attachmentRecord.DealID != nil {
			_ = model.UpdateTotalAmount(l.ctx, tx.Client(), *attachmentRecord.DealID)
		}

		return nil
	})

	resp = new(types.AttachmentResponse)
	return resp, err
}
