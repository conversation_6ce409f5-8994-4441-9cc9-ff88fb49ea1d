package attachment

import (
	"bcare/common/bquery"
	cast2 "bcare/common/cast"
	"bcare/ent"
	"context"
	"fmt"
	"github.com/jinzhu/copier"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type ForceUpdateCreatedAtLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewForceUpdateCreatedAtLogic(ctx context.Context, svcCtx *svc.ServiceContext) ForceUpdateCreatedAtLogic {
	return ForceUpdateCreatedAtLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ForceUpdateCreatedAtLogic) ForceUpdateCreatedAt(req *types.AttachmenForcetUpdateRequest) (resp *types.AttachmentResponse, err error) {
	var attachmentRecord *ent.Attachment
	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		attachmentRecord, err = tx.Attachment.Get(l.ctx, req.Id)
		if err != nil && ent.IsNotFound(err) {
			return types.ErrAttachmentNotFound.Wrap(err, req.Id)
		}
		if attachmentRecord == nil {
			return types.ErrAttachmentNotFound.Wrap(nil, req.Id)
		}
		err = copier.CopyWithOption(&attachmentRecord, req, cast2.PayloadToEnt)
		if err != nil {
			return types.ErrAttachmentCopyFailed.Wrap(err, err.Error())
		}

		_, err := l.svcCtx.Ent.ExecContext(l.ctx,
			"UPDATE attachment SET created_at = $1 WHERE id = $2",
			attachmentRecord.CreatedAt, attachmentRecord.ID)
		if err != nil {
			return types.ErrAttachmentUpdate.Wrap(err, fmt.Errorf("cannot update created_at: %w", err))

		}
		return nil
	})
	if err != nil {
		return nil, err
	}
	resp = new(types.AttachmentResponse)
	_ = cast2.ConvertViaJson(&resp, attachmentRecord)

	// Include product information if available
	if attachmentRecord.ProductID != nil {
		productRecord, _ := l.svcCtx.Ent.Product.Get(l.ctx, *attachmentRecord.ProductID)
		_ = cast2.ConvertViaJson(&resp.Product, productRecord)
	}

	return resp, nil
}
