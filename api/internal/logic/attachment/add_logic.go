package attachment

import (
	"bcare/api/internal/auth"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/api/model"
	"bcare/common/bconst"
	"bcare/common/bquery"
	cast2 "bcare/common/cast"
	"bcare/common/ctxdata"
	"bcare/ent"
	"bcare/ent/deal"
	"bcare/ent/discountusage"
	"context"
	"github.com/jinzhu/copier"
	"github.com/zeromicro/go-zero/core/logx"
)

type AddLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAddLogic(ctx context.Context, svcCtx *svc.ServiceContext) AddLogic {
	return AddLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

type DiscountToApply struct {
	discountID     int
	discountAmount float64
}

func (l *AddLogic) Add(req *types.AttachmentAddRequest) (resp *types.AttachmentResponse, err error) {
	attachmentRecord := new(ent.Attachment)
	productRecord := new(ent.Product)
	var discountsToApply []DiscountToApply

	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		// Copy request data to attachment record
		err = copier.CopyWithOption(&attachmentRecord, req, cast2.PayloadToEnt)
		if err != nil {
			logx.WithContext(l.ctx).Errorf("cannot copy attachment: %v", err)
			return types.ErrAttachmentCopyFailed.Wrap(err, "Error copying attachment data")
		}

		// Check permissions
		if !l.svcCtx.Auth.Attachment.CanAdd(l.ctx, attachmentRecord) {
			logx.WithContext(l.ctx).Infof("Permission denied")
			return auth.ErrAttachmentAddDenied
		}

		if attachmentRecord.ProductID != nil {
			productRecord, _ = l.svcCtx.Ent.Product.Get(l.ctx, *attachmentRecord.ProductID)
			if attachmentRecord.Price == 0 {
				attachmentRecord.Price = float64(productRecord.Price)
			}
		}

		// Set default status if not provided
		if attachmentRecord.Status == 0 {
			attachmentRecord.Status = bconst.StatusNormal
		}

		// Process discounts if no discount is set
		if attachmentRecord.Discount == 0 {
			discountsToApply, err = l.processDiscounts(tx, attachmentRecord, productRecord)
			if err != nil {
				return err
			}
		}

		trackRecord, _ := model.GetActiveTrack(l.ctx, tx, attachmentRecord.PersonID)
		if trackRecord != nil {
			attachmentRecord.TrackID = trackRecord.ID
		}
		// Set user ID and create attachment
		attachmentRecord.UserID = ctxdata.GetUidFromCtx(l.ctx)
		attachmentRecord, err = tx.Attachment.Create().
			SetAttachment(attachmentRecord).
			Save(l.ctx)
		if err != nil {
			return types.ErrAttachmentAdd.Wrap(err, "Error saving attachment record")
		}

		// Create discount usages after attachment is created
		// Bulk create discount usages
		if err := l.bulkCreateDiscountUsages(tx, discountsToApply, attachmentRecord); err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	// Update total amount if deal exists
	if attachmentRecord.DealID != nil {
		_ = model.UpdateTotalAmount(l.ctx, l.svcCtx.Ent, *attachmentRecord.DealID)
	}

	resp = new(types.AttachmentResponse)
	_ = cast2.EntToResp(&resp, attachmentRecord)
	_ = cast2.EntToResp(&resp.Product, productRecord)
	return resp, nil
}

func (l *AddLogic) processDiscounts(
	tx *ent.Tx,
	attachmentRecord *ent.Attachment,
	productRecord *ent.Product,
) ([]DiscountToApply, error) {
	var discountsToApply []DiscountToApply
	var maxDiscount float64 = 0 // Track the maximum discount amount

	dealRecord := new(ent.Deal)
	if attachmentRecord.DealID != nil {
		dealRecord, _ = tx.Deal.Get(l.ctx, *attachmentRecord.DealID)
	}
	personRecord, _ := tx.Person.Get(l.ctx, attachmentRecord.PersonID)

	discountUsages, err := fetchDiscountUsages(l.svcCtx.Ent.DiscountUsage.Query(), attachmentRecord.PersonID, l.ctx)
	if err != nil {
		return discountsToApply, nil
	}

	var bestDiscount *DiscountToApply // Track the best discount

	for _, du := range discountUsages {
		discountRecord := new(model.Discount)
		if err := copier.Copy(discountRecord, du.Edges.Discount); err != nil {
			continue
		}

		switch discountRecord.Scope {
		case string(model.DiscountScopeProduct):
			if discountRecord.UsageType == "multiple" && du.EntityType != "attachment" {
				eligible, discountAmount, _, err := discountRecord.ApplyDiscount(
					float64(productRecord.Price),
					dealRecord,
					personRecord,
					productRecord,
				)
				if err != nil {
					continue
				}

				if eligible {
					// Calculate total discount amount for current quantity
					totalDiscountAmount := discountAmount * float64(attachmentRecord.Quantity)

					// Update best discount if current discount is larger
					if totalDiscountAmount > maxDiscount {
						maxDiscount = totalDiscountAmount
						bestDiscount = &DiscountToApply{
							discountID:     discountRecord.ID,
							discountAmount: discountAmount,
						}
					}
				}
			}
		}
	}

	// Only append the best discount if we found one
	if bestDiscount != nil {
		discountsToApply = append(discountsToApply, *bestDiscount)
		// Update attachment discount with the maximum discount found
		attachmentRecord.Discount = maxDiscount
	}

	return discountsToApply, nil
}

func (l *AddLogic) bulkCreateDiscountUsages(
	tx *ent.Tx,
	discountsToApply []DiscountToApply,
	attachmentRecord *ent.Attachment,
) error {
	bulk := make([]*ent.DiscountUsageCreate, len(discountsToApply))
	for i, discount := range discountsToApply {
		bulk[i] = tx.DiscountUsage.Create().
			SetDiscountID(discount.discountID).
			SetPersonID(attachmentRecord.PersonID).
			SetAttachmentID(attachmentRecord.ID).
			SetUsageCount(1).
			SetEntityType("attachment").
			SetValue(discount.discountAmount)
	}

	if len(bulk) > 0 {
		if _, err := tx.DiscountUsage.CreateBulk(bulk...).Save(l.ctx); err != nil {
			return types.ErrDiscountUsageAdd.Wrap(err)
		}
	}
	return nil
}
func fetchDiscountUsages(q *ent.DiscountUsageQuery, personId int, ctx context.Context) ([]*ent.DiscountUsage, error) {
	return q.
		Where(
			discountusage.PersonID(personId),
			discountusage.HasDealWith(
				deal.StatusNEQ(bconst.StatusDeleted),
				deal.DeletedAtIsNil(),
				//deal.StateNEQ("lost"),
			)).
		WithDiscount().
		All(ctx)
}
