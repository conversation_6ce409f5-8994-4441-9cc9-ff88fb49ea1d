package attachment

import (
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	cast2 "bcare/common/cast"
	"bcare/ent"
	"bcare/ent/attachment"
	"context"
	"github.com/zeromicro/go-zero/core/logx"
)

type GetLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetLogic(ctx context.Context, svcCtx *svc.ServiceContext) GetLogic {
	return GetLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetLogic) Get(req *types.AttachmentGetRequest) (resp *types.AttachmentTreeResponse, err error) {
	attachmentRecord, err := l.svcCtx.Ent.Attachment.Query().
		WithProduct().
		WithData().
		// Query children with their product and data
		WithChildren(func(q *ent.AttachmentQuery) {
			q.WithProduct().
				WithData()
		}).
		// Query parent with its product and data
		WithParent(func(q *ent.AttachmentQuery) {
			q.WithProduct().
				WithData()
		}).
		WithBillItem().
		Where(attachment.ID(req.Id)).
		Only(l.ctx)

	if err != nil && ent.IsNotFound(err) {
		return nil, types.ErrAttachmentNotFound.Wrap(err, req.Id)
	}

	if attachmentRecord == nil {
		return nil, types.ErrAttachmentNotFound.Wrap(err, req.Id)
	}

	resp = new(types.AttachmentTreeResponse)
	err = cast2.EntToResp(&resp, attachmentRecord)

	return resp, nil
}
