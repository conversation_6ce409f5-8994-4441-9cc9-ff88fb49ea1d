package attachment

import (
	"bcare/common/bquery"
	cast2 "bcare/common/cast"
	"bcare/ent"
	"bcare/ent/schema"
	"context"
	"fmt"
	"github.com/jinzhu/copier"
	"github.com/xuri/excelize/v2"
	"os"
	"path/filepath"
	"time"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type QueryOperationLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewQueryOperationLogic(ctx context.Context, svcCtx *svc.ServiceContext) QueryOperationLogic {
	return QueryOperationLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *QueryOperationLogic) QueryOperation(req *types.AttachmentDynamicQuery) (resp *types.DynamicQueryResponse, err error) {
	resp = new(types.DynamicQueryResponse)

	dQuery := &bquery.DynamicQuery{}
	err = copier.Copy(dQuery, req)
	if err != nil {
		logx.WithContext(l.ctx).Errorf("unable to copy request: %v", err)
		return resp, err
	}

	query := l.svcCtx.Ent.AttachmentOperationReportView.Query().Modify(bquery.ApplyDynamicQuery(dQuery))

	result := map[string]any{}
	aliases := make(map[string]bool)
	for _, agg := range req.Aggregations {
		if agg.Alias != "" {
			aliases[agg.Alias] = true
		}
	}

	skipSoftDelete := schema.SkipSoftDelete(l.ctx)
	if !req.Export {
		if req.Limit == 1 {
			r, err := query.First(skipSoftDelete)
			if err != nil && !ent.IsNotFound(err) {
				logx.WithContext(l.ctx).Errorf("unable to fetch activity record: %v", err)
				return resp, err
			}

			if r != nil {
				for alias := range aliases {
					result[alias], _ = r.Value(alias)
				}
			}
		} else {
			rows, err := query.All(skipSoftDelete)
			if err != nil {
				logx.WithContext(l.ctx).Errorf("unable to fetch payment records: %v", err)
				return resp, err
			}
			resultList := make([]map[string]any, len(rows))
			for i, r := range rows {
				rowResult := make(map[string]any)
				rowResult["record"] = cast2.Atom(&rowResult, r)
				for alias := range aliases {
					rowResult[alias], _ = r.Value(alias)
				}
				resultList[i] = rowResult
			}
			result["rows"] = resultList
		}
	} else {
		rows, err := query.All(skipSoftDelete)
		if err != nil {
			logx.WithContext(l.ctx).Errorf("unable to fetch records: %v", err)
			return resp, err
		}

		fileUrl, err := l.exportToExcel(rows)
		if err != nil {
			return nil, fmt.Errorf("failed to export excel: %v", err)
		}
		result["file_url"] = fileUrl
	}

	resp.Result = result

	return
}

func (l *QueryOperationLogic) exportToExcel(records []*ent.AttachmentOperationReportView) (string, error) {
	// Create new Excel file in memory
	f := excelize.NewFile()
	defer f.Close()

	// Create a new sheet
	sheetName := "Operation Report"
	f.SetSheetName("Sheet1", sheetName)

	// Create default style with Times New Roman
	defaultStyle, _ := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Family: "Times New Roman",
			Size:   12,
		},
		Alignment: &excelize.Alignment{
			Vertical: "center",
		},
	})

	// Create header style
	headerStyle, _ := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Family: "Times New Roman",
			Size:   12,
		},
		Fill: excelize.Fill{Type: "pattern", Color: []string{"#CCCCCC"}, Pattern: 1},
		Alignment: &excelize.Alignment{
			Horizontal: "center",
			Vertical:   "center",
		},
	})

	// Create money style for price column
	moneyStyle, _ := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Family: "Times New Roman",
			Size:   12,
		},
		NumFmt: 3, // Built-in format code for "#,##0"
		Alignment: &excelize.Alignment{
			Horizontal: "right",
			Vertical:   "center",
		},
	})

	// Set headers
	headers := []string{
		"Ngày điều trị",
		"Tên khách hàng",
		"Mã khách hàng",
		"Tên sản phẩm",
		"Danh mục",
		"Nội dung điều trị",
		"Vị trí răng",
		"Doanh thu",
		"Đã thanh toán",
		//"Số lần điều trị",
		"Bác sĩ",
		"Ngày mua",
	}

	// Apply default style to whole sheet
	f.SetCellStyle(sheetName, "A1", fmt.Sprintf("%c%d", 'A'+len(headers)-1, len(records)+1), defaultStyle)

	// Write headers and set header style
	for i, header := range headers {
		cell := fmt.Sprintf("%c1", 'A'+i)
		f.SetCellValue(sheetName, cell, header)
		f.SetCellStyle(sheetName, cell, cell, headerStyle)
	}

	// Write data
	for i, record := range records {
		row := i + 2 // Start from row 2 (after headers)

		buyDateStr := ""
		if !record.BuyDate.IsZero() {
			buyDateStr = record.BuyDate.Format(l.svcCtx.Config.General.DateFormat)
		}

		price := 0.0
		paidAmount := 0.0
		if record.CategoryID != 28 {
			price = record.Price
			paidAmount = record.PaidAmount
		}
		data := []interface{}{
			record.CreatedAt.Format(l.svcCtx.Config.General.DateTimeFormat),
			record.FullName,
			record.PersonCode,
			record.ProductTitle,
			record.CategoryName,
			record.Operation,
			record.PositionTooth,
			price,
			paidAmount,
			//record.TreatmentSequence,
			record.DoctorName,
			buyDateStr,
		}

		for j, value := range data {
			cell := fmt.Sprintf("%c%d", 'A'+j, row)
			f.SetCellValue(sheetName, cell, value)

			// Apply money style for price column (column H, index 7)
			if j == 7 || j == 8 {
				f.SetCellStyle(sheetName, cell, cell, moneyStyle)
			}
		}
	}

	// Set column widths
	columnWidths := map[string]float64{
		"A": 15, // Treatment Date
		"B": 25, // Full Name
		"C": 15, // Person Code
		"D": 30, // Product Title
		"E": 20, // Category Name
		"F": 25, // Operation
		"G": 15, // Position Tooth
		"H": 15, // Price
		"J": 15, // Price
		//"I": 15, // Treatment Sequence
		"I": 20, // Doctor Name
		"K": 15, // Buy Date
	}

	for col, width := range columnWidths {
		f.SetColWidth(sheetName, col, col, width)
	}

	// Create directory structure based on date
	dirPath, err := createDirStructure(l.svcCtx.Config.Storage.Local.Path, "reports")
	if err != nil {
		return "", fmt.Errorf("failed to create directory structure: %v", err)
	}

	// Generate filename with timestamp
	timestamp := time.Now().Format("20060102_150405")
	fileName := fmt.Sprintf("operation_report_%s.xlsx", timestamp)
	filePath := filepath.Join(dirPath, fileName)

	// Save Excel file to disk
	if err := f.SaveAs(filePath); err != nil {
		return "", fmt.Errorf("failed to save excel file: %v", err)
	}

	// Generate public URL for download
	publicUrl := fmt.Sprintf("/reports/%s/%s",
		time.Now().Format("2006_01_02"),
		fileName)

	return publicUrl, nil
}

func createDirStructure(basePath string, reportType string) (string, error) {
	currentDate := time.Now().Format("2006_01_02")
	dirPath := filepath.Join(basePath, reportType, currentDate)

	err := os.MkdirAll(dirPath, 0755)
	if err != nil {
		return "", err
	}

	return dirPath, nil
}
