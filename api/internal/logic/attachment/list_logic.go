package attachment

import (
	"bcare/api/internal/auth"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/bconst"
	"bcare/common/bquery"
	cast2 "bcare/common/cast"
	"bcare/ent"
	"bcare/ent/attachment"
	"bcare/ent/billitem"
	"bcare/ent/product"
	"context"
	"entgo.io/ent/dialect/sql"
	"github.com/zeromicro/go-zero/core/logx"
)

type ListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewListLogic(ctx context.Context, svcCtx *svc.ServiceContext) ListLogic {
	return ListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ListLogic) List(req *types.AttachmentListRequest) (resp *types.AttachmentListResponse, err error) {
	//kiểm tra xem có quyền ko
	if !l.svcCtx.Auth.Attachment.CanDelete(l.ctx, nil) {
		logx.WithContext(l.ctx).Infof("Bạn ko có quyền")
		return nil, auth.ErrAttachmentListDenied

	}
	limit, offset := bquery.Paging(req.PageSize, req.Page)
	order := bquery.Ordering(req.OrderBy)

	query := l.svcCtx.Ent.Attachment.Query().
		WithParent().
		WithData().
		WithProduct(func(q *ent.ProductQuery) {
			q.Select(product.FieldID, product.FieldName) // Only select ID and Name fields
		}).
		WithBillItem(func(q *ent.BillItemQuery) {
			q.WithAllocations().Where(billitem.StatusEQ(bconst.StatusNormal))
			q.Select(billitem.FieldID, billitem.FieldAmount) // Chỉ lấy các trường cần thiết
		}).
		Order(order)

	filterMap := make(map[string]interface{})
	_ = cast2.AtomNoZero(&filterMap, req.Filter)

	for field, value := range filterMap {
		query = query.Where(sql.FieldEQ(field, value))
	}

	if req.AttachmentKind != "" {
		query = query.Where(attachment.KindEQ(attachment.Kind(req.AttachmentKind)))
	} else {
		query = query.Where(attachment.KindNEQ(attachment.KindProposal))
	}

	if req.ProductType != "" {
		if req.ProductType == "service" {
			// Nếu là service thì lấy cả những attachment không gắn với product nào
			query = query.Where(
				attachment.Or(
					attachment.HasProductWith(product.Type(req.ProductType)),
					attachment.ProductIDIsNil(),
				),
			)
		} else {
			// Các ProductType khác thì chỉ lấy attachment có gắn với product
			query = query.Where(attachment.HasProductWith(product.Type(req.ProductType)))
		}
	}

	if req.ProductCategory != 0 {
		// lọc attachment và attachment cha theo productCategory truyền vào
		query = query.Where(
			attachment.Or(
				attachment.HasProductWith(product.CategoryID(req.ProductCategory)),
				attachment.HasParentWith(
					attachment.HasProductWith(product.CategoryID(req.ProductCategory)),
				),
			),
		)
	}

	total, _ := query.Count(l.ctx)
	attachments, e := query.Limit(limit).Offset(offset).All(l.ctx)

	if e != nil {
		err = types.ErrAttachmentGetList.Wrap(e, e.Error())
		return nil, err
	}

	resp = new(types.AttachmentListResponse)
	resp.Attachments = make([]types.AttachmentResponse, len(attachments))
	for i, at := range attachments {
		a := types.AttachmentResponse{}
		_ = cast2.EntToResp(&a, at)

		resp.Attachments[i] = a
	}
	resp.Total = total

	return resp, err
}
