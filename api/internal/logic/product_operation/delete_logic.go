package product_operation

import (
	"bcare/common/bquery"
	"bcare/ent"
	"bcare/ent/productoperation"
	"context"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewDeleteLogic(ctx context.Context, svcCtx *svc.ServiceContext) DeleteLogic {
	return DeleteLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DeleteLogic) Delete(req *types.ProductOperationDeleteRequest) (resp *types.ProductOperation, err error) {
	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		operationRecord, err := tx.ProductOperation.Query().Where(productoperation.ProductID(req.ProductId), productoperation.OperationID(req.OperationId)).Only(l.ctx)

		if err != nil && ent.IsNotFound(err) {
			return err
		}

		if operationRecord == nil {
			return types.ErrProductDelete
		}
		err = tx.ProductOperation.DeleteOne(operationRecord).Exec(l.ctx)
		if err != nil {
			return types.ErrProductDelete.Wrap(err, err.Error())
		}
		return nil
	})

	return
}
