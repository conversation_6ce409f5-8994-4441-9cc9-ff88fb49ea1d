package product_operation

import (
	"context"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type AddLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAddLogic(ctx context.Context, svcCtx *svc.ServiceContext) AddLogic {
	return AddLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AddLogic) Add(req *types.ProductOperationAddRequest) (resp *types.ProductOperation, err error) {
	if _, err = l.svcCtx.Ent.ProductOperation.Create().
		SetProductID(req.ProductId).
		SetOperationID(req.OperationId).Save(l.ctx); err != nil {
		return nil, types.ErrProductAdd.Wrap(err, err.<PERSON><PERSON><PERSON>())
	}

	return nil, err
}
