package product_operation

import (
	"bcare/common/cast"
	"context"

	"bcare/api/internal/dto"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type BulkSetProductOperationLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewBulkSetLogic(ctx context.Context, svcCtx *svc.ServiceContext) *BulkSetProductOperationLogic {
	return &BulkSetProductOperationLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *BulkSetProductOperationLogic) BulkSet(req *types.BulkSetProductOperationRequest) error {
	input := new(dto.BulkSetProductOperationInput)
	err := cast.ReqToModelInput(input, req)
	if err != nil {
		return err
	}

	err = l.svcCtx.ProductOperationModel.BulkSetProductOperations(l.ctx, input)
	if err != nil {
		return err
	}

	return nil
}
