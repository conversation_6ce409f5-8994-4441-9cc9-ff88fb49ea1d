package person_assignment

import (
	"bcare/api/internal/auth"
	"bcare/common/bquery"
	"bcare/ent"
	"context"
	"github.com/jinzhu/copier"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type AddLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAddLogic(ctx context.Context, svcCtx *svc.ServiceContext) AddLogic {
	return AddLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AddLogic) Add(req *types.PersonAssignmentAddRequest) (resp *types.PersonAssignment, err error) {
	personAssignRecord := new(ent.PersonAssignment)
	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		err = copier.Copy(&personAssignRecord, req)
		if err != nil {
			logx.WithContext(l.ctx).<PERSON><PERSON><PERSON>("cannot copy request: %v", err)
			return types.ErrPersonAdd.Wrap(err, err.Error())
		}

		//kiểm tra xem có quyền ko
		if !l.svcCtx.Auth.PersonAssignment.CanAdd(l.ctx, personAssignRecord) {
			logx.WithContext(l.ctx).Infof("Bạn ko có quyền")
			return auth.ErrPersonAssignmentAddDenied
		}

		personAssignRecord, err = tx.PersonAssignment.Create().SetPersonAssignment(personAssignRecord).Save(l.ctx)
		if err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		return nil, types.ErrPersonAdd.Wrap(err, err.Error())
	}

	resp = new(types.PersonAssignment)
	_ = copier.Copy(&resp, personAssignRecord)
	return resp, nil
}
