package person_assignment

import (
	"bcare/api/internal/auth"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/bquery"
	"bcare/ent"
	"context"
	"github.com/jinzhu/copier"

	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateLogic(ctx context.Context, svcCtx *svc.ServiceContext) UpdateLogic {
	return UpdateLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateLogic) Update(req *types.PersonAssignmentUpdateRequest) (resp *types.PersonAssignment, err error) {
	personAssignRecord := new(ent.PersonAssignment)
	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		personAssignRecord, err = tx.PersonAssignment.Get(l.ctx, req.Id)
		if err != nil {
			return types.ErrPersonUpdate.Wrap(err, err.Error())
		}
		err = copier.Copy(&personAssignRecord, req)
		if err != nil {
			return types.ErrPersonCopy.Wrap(err, err.Error())
		}

		//kiểm tra xem có quyền ko
		if !l.svcCtx.Auth.PersonAssignment.CanUpdate(l.ctx, personAssignRecord) {
			logx.WithContext(l.ctx).Infof("Bạn ko có quyền")
			return auth.ErrPersonAssignmentUpdateDenied
		}

		personAssignRecord, err = tx.PersonAssignment.UpdateOneID(personAssignRecord.ID).SetPersonAssignment(personAssignRecord).Save(l.ctx)
		if err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		return nil, types.ErrPersonUpdate.Wrap(err, err.Error())
	}

	resp = new(types.PersonAssignment)
	_ = copier.Copy(&resp, personAssignRecord)
	return resp, nil
}
