package person_assignment

import (
	"bcare/api/internal/auth"
	"bcare/common/bquery"
	"bcare/ent"
	"context"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewDeleteLogic(ctx context.Context, svcCtx *svc.ServiceContext) DeleteLogic {
	return DeleteLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DeleteLogic) Delete(req *types.PersonAssignmentDeleteRequest) (resp *types.PersonAssignment, err error) {
	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		personAssignRecord, err := tx.PersonAssignment.Get(l.ctx, req.Id)

		if err != nil && ent.IsNotFound(err) {
			return err
		}

		if personAssignRecord == nil {
			logx.Errorf("person assignment not found")
			return types.ErrPersonDelete
		}

		//kiểm tra xem có quyền ko
		if !l.svcCtx.Auth.PersonAssignment.CanDelete(l.ctx, personAssignRecord) {
			logx.WithContext(l.ctx).Infof("Bạn ko có quyền")
			return auth.ErrPersonAssignmentDeleteDenied
		}

		err = tx.PersonAssignment.DeleteOne(personAssignRecord).Exec(l.ctx)
		if err != nil {
			return types.ErrPersonDelete.Wrap(err)
		}
		return nil
	})

	return
}
