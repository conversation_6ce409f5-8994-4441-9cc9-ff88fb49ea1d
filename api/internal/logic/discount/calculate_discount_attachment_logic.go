package discount

import (
	"bcare/api/internal/dto"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/berr"
	"bcare/common/cast"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type CalculateDiscountAttachmentLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewCalculateDiscountAttachmentLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CalculateDiscountAttachmentLogic {
	return &CalculateDiscountAttachmentLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *CalculateDiscountAttachmentLogic) CalculateDiscountAttachment(req *types.CalculateDiscountAttachmentRequest) (resp *types.CalculateDiscountResponse, err error) {
	if !l.svcCtx.Auth.Discount.CanCalculateDiscount(l.ctx) {
		return nil, berr.ErrPermissionDenied.Wrap(nil)
	}

	input := new(dto.CalculateDiscountAttachmentInput)
	err = cast.ReqToModelInput(input, req)
	if err != nil {
		return nil, berr.ErrRequestParam.Wrap(err)
	}

	productInput := &dto.GetProductInput{
		ID: input.ProductID,
	}
	productOutput, err := l.svcCtx.ProductModel.Get(l.ctx, productInput)
	if err != nil {
		return nil, err
	}

	calculateOutput, err := l.svcCtx.DiscountModel.CalculateDiscountForProduct(l.ctx, input, productOutput)
	if err != nil {
		return nil, err
	}

	resp = new(types.CalculateDiscountResponse)
	err = cast.ConvertViaJson(resp, calculateOutput)
	if err != nil {
		return nil, berr.ErrServerCommon.Wrap(err)
	}

	return resp, nil
}
