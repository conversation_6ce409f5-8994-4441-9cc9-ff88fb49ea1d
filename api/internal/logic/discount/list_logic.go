package discount

import (
	"bcare/api/internal/dto"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/berr"
	"bcare/common/cast"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type ListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ListLogic {
	return &ListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ListLogic) List(req *types.DiscountListRequest) (resp *types.DiscountListResponse, err error) {
	if !l.svcCtx.Auth.Discount.CanList(l.ctx) {
		return nil, berr.ErrPermissionDenied.Wrap(nil)
	}

	input := new(dto.ListDiscountInput)
	err = cast.ReqToModelInput(input, req)
	if err != nil {
		return nil, berr.ErrRequestParam.Wrap(err)
	}

	listOutput, err := l.svcCtx.DiscountModel.ListDiscounts(l.ctx, input)
	if err != nil {
		return nil, err
	}

	resp = &types.DiscountListResponse{
		Discounts: make([]types.Discount, 0, len(listOutput.Discounts)),
		Total:     listOutput.Total,
	}

	err = cast.ConvertViaJson(&resp.Discounts, listOutput.Discounts)
	if err != nil {
		return nil, berr.ErrServerCommon.Wrap(err)
	}

	return resp, nil
}
