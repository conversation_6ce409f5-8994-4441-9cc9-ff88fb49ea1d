package discount

import (
	"bcare/api/internal/dto"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/berr"
	"bcare/common/cast"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateLogic {
	return &UpdateLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateLogic) Update(req *types.DiscountUpdateRequest) (resp *types.DiscountResponse, err error) {
	existingDiscount, err := l.svcCtx.DiscountModel.GetDiscount(l.ctx, req.Id)
	if err != nil {
		return nil, err
	}

	if !l.svcCtx.Auth.Discount.CanUpdate(l.ctx, existingDiscount) {
		return nil, berr.ErrPermissionDenied.Wrap(nil)
	}

	input := new(dto.UpdateDiscountInput)
	err = cast.ReqToModelInput(input, req)
	if err != nil {
		return nil, berr.ErrRequestParam.Wrap(err)
	}

	discountOutput, err := l.svcCtx.DiscountModel.UpdateDiscount(l.ctx, input)
	if err != nil {
		return nil, err
	}

	resp = new(types.DiscountResponse)
	err = cast.ConvertViaJson(resp, discountOutput)
	if err != nil {
		return nil, berr.ErrServerCommon.Wrap(err)
	}

	return resp, nil
}
