package discount

import (
	"bcare/api/internal/dto"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/berr"
	"bcare/common/cast"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type EligibleDiscountLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewEligibleDiscountLogic(ctx context.Context, svcCtx *svc.ServiceContext) *EligibleDiscountLogic {
	return &EligibleDiscountLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *EligibleDiscountLogic) EligibleDiscount(req *types.EligibleDiscountRequest) (resp *types.EligibleDiscountResponse, err error) {
	if !l.svcCtx.Auth.Discount.CanEligibleDiscount(l.ctx) {
		return nil, berr.ErrPermissionDenied.Wrap(nil)
	}

	input := new(dto.EligibleDiscountInput)
	err = cast.ReqToModelInput(input, req)
	if err != nil {
		return nil, berr.ErrRequestParam.Wrap(err)
	}

	var personOutput *dto.PersonOutput
	if input.PersonID > 0 {
		personInput := &dto.GetPersonInput{
			ID:              input.PersonID,
			IncludeRelation: true,
		}
		personOutput, err = l.svcCtx.PersonModel.Get(l.ctx, personInput)
		if err != nil {
			return nil, err
		}
	}

	var dealOutput *dto.DealOutput
	if input.DealID > 0 {
		dealInput := &dto.GetDealInput{
			ID:              input.DealID,
			IncludeRelation: true,
		}
		dealOutput, err = l.svcCtx.DealModel.Get(l.ctx, dealInput)
		if err != nil {
			return nil, err
		}
	}

	var productOutputs []dto.ProductOutput
	if len(input.ProductIDs) > 0 {
		productOutputs, err = l.svcCtx.ProductModel.GetByIDs(l.ctx, input.ProductIDs)
		if err != nil {
			return nil, err
		}
	}

	eligibleOutputs, err := l.svcCtx.DiscountModel.GetEligibleDiscounts(l.ctx, input, personOutput, dealOutput, productOutputs)
	if err != nil {
		return nil, err
	}

	resp = new(types.EligibleDiscountResponse)
	err = cast.ConvertViaJson(resp, eligibleOutputs)
	if err != nil {
		return nil, berr.ErrServerCommon.Wrap(err)
	}

	return resp, nil
}
