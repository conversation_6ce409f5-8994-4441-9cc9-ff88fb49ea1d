package discount

import (
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/berr"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewDeleteLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteLogic {
	return &DeleteLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DeleteLogic) Delete(req *types.DiscountDeleteRequest) (err error) {
	existingDiscount, err := l.svcCtx.DiscountModel.GetDiscount(l.ctx, req.Id)
	if err != nil {
		return err
	}

	if !l.svcCtx.Auth.Discount.CanDelete(l.ctx, existingDiscount) {
		return berr.ErrPermissionDenied.Wrap(nil)
	}

	err = l.svcCtx.DiscountModel.DeleteDiscount(l.ctx, req.Id)
	if err != nil {
		return err
	}

	return nil
}
