package discount

import (
	"bcare/api/internal/dto"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/berr"
	"bcare/common/cast"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type AddLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAddLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AddLogic {
	return &AddLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AddLogic) Add(req *types.DiscountAddRequest) (resp *types.DiscountResponse, err error) {
	if !l.svcCtx.Auth.Discount.CanAdd(l.ctx) {
		return nil, berr.ErrPermissionDenied.Wrap(nil)
	}

	input := new(dto.CreateDiscountInput)
	err = cast.ReqToModelInput(input, req)
	if err != nil {
		return nil, berr.ErrRequestParam.Wrap(err)
	}

	discountOutput, err := l.svcCtx.DiscountModel.CreateDiscount(l.ctx, input)
	if err != nil {
		return nil, err
	}

	resp = new(types.DiscountResponse)
	err = cast.ConvertViaJson(resp, discountOutput)
	if err != nil {
		return nil, berr.ErrServerCommon.Wrap(err)
	}

	return resp, nil
}
