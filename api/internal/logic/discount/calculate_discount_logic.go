package discount

import (
	"bcare/api/internal/dto"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/berr"
	"bcare/common/cast"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type CalculateDiscountLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewCalculateDiscountLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CalculateDiscountLogic {
	return &CalculateDiscountLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *CalculateDiscountLogic) CalculateDiscount(req *types.CalculateDiscountRequest) (resp *types.CalculateDiscountResponse, err error) {
	if !l.svcCtx.Auth.Discount.CanCalculateDiscount(l.ctx) {
		return nil, berr.ErrPermissionDenied.Wrap(nil)
	}

	input := new(dto.CalculateDiscountInput)
	err = cast.ReqToModelInput(input, req)
	if err != nil {
		return nil, berr.ErrRequestParam.Wrap(err)
	}

	dealInput := &dto.GetDealInput{
		ID:              input.DealID,
		IncludeRelation: true,
	}
	dealOutput, err := l.svcCtx.DealModel.Get(l.ctx, dealInput)
	if err != nil {
		return nil, err
	}

	var personOutput *dto.PersonOutput
	if dealOutput.PersonID > 0 {
		personInput := &dto.GetPersonInput{
			ID:              dealOutput.PersonID,
			IncludeRelation: true,
		}
		personOutput, err = l.svcCtx.PersonModel.Get(l.ctx, personInput)
		if err != nil {
			return nil, err
		}
	}

	calculateOutput, err := l.svcCtx.DiscountModel.CalculateDiscountForDeal(l.ctx, input, dealOutput, personOutput)
	if err != nil {
		return nil, err
	}

	resp = new(types.CalculateDiscountResponse)
	err = cast.ConvertViaJson(resp, calculateOutput)
	if err != nil {
		return nil, berr.ErrServerCommon.Wrap(err)
	}

	return resp, nil
}

// CalculateDiscountInternal calculates discounts for internal use by other modules
func (l *CalculateDiscountLogic) CalculateDiscountInternal(dealID int, discountIDs []int) (map[int][]dto.DiscountDetail, interface{}, map[int]float64, error) {
	input := &dto.CalculateDiscountInput{
		DealID:      dealID,
		DiscountIDs: discountIDs,
	}

	// Fetch deal data first
	dealInput := &dto.GetDealInput{
		ID:              dealID,
		IncludeRelation: true,
	}
	dealOutput, err := l.svcCtx.DealModel.Get(l.ctx, dealInput)
	if err != nil {
		return nil, nil, nil, err
	}

	// Fetch person data if deal has a person
	var personOutput *dto.PersonOutput
	if dealOutput.PersonID > 0 {
		personInput := &dto.GetPersonInput{
			ID:              dealOutput.PersonID,
			IncludeRelation: true,
		}
		personOutput, err = l.svcCtx.PersonModel.Get(l.ctx, personInput)
		if err != nil {
			return nil, nil, nil, err
		}
	}

	calculateOutput, err := l.svcCtx.DiscountModel.CalculateDiscountForDeal(l.ctx, input, dealOutput, personOutput)
	if err != nil {
		return nil, nil, nil, err
	}

	// Build the return data structures expected by deal logic
	discountDetails := make(map[int][]dto.DiscountDetail)
	discountMap := make(map[int]float64)

	for _, result := range calculateOutput.Results {
		discountDetails[result.DiscountID] = result.Details
		discountMap[result.DiscountID] = result.TotalAmount
	}

	return discountDetails, nil, discountMap, nil
}
