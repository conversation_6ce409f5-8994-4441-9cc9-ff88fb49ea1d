package file

import (
	"bcare/api/internal/auth"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/berr"
	"bcare/common/cast"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetLogic(ctx context.Context, svcCtx *svc.ServiceContext) GetLogic {
	return GetLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetLogic) Get(req *types.FileGetRequest) (resp *types.FileResponse, err error) {
	if !l.svcCtx.Auth.File.CanGet(l.ctx, nil) {
		return nil, auth.ErrFileGetDenied
	}

	if req.Id <= 0 {
		return nil, berr.ErrRequestParam.Wrap(nil).Op("FileGetLogic.Get")
	}

	// Call model to get file
	fileOutput, err := l.svcCtx.FileModel.GetFile(l.ctx, req.Id)
	if err != nil {
		return nil, err
	}

	// Map output to response
	resp = new(types.FileResponse)
	err = cast.ConvertViaJson(resp, fileOutput)
	if err != nil {
		return nil, berr.ErrServerCommon.Wrap(err)
	}

	return resp, nil
}
