package file

import (
	"bcare/api/internal/auth"
	"bcare/api/internal/dto"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/berr"
	"bcare/common/cast"
	"context"
	"math"

	"github.com/zeromicro/go-zero/core/logx"
)

type ListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewListLogic(ctx context.Context, svcCtx *svc.ServiceContext) ListLogic {
	return ListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ListLogic) List(req *types.FileListRequest) (resp *types.FileListResponse, err error) {
	if !l.svcCtx.Auth.File.CanList(l.ctx, nil) {
		return nil, auth.ErrFileListDenied
	}

	// Map request to input
	input := dto.ListFilesInput{
		Page:     req.Page,
		PageSize: req.PageSize,
		OrderBy:  req.OrderBy,
	}

	// Map filter
	if req.Filter != nil {
		input.Filter = &dto.FileFilter{
			Name:    req.Filter.Name,
			Kind:    req.Filter.Kind,
			Type:    req.Filter.Type,
			UserID:  req.Filter.UserId,
			Storage: req.Filter.Storage,
		}
		input.Search = req.Filter.Search
	}

	// Call model to list files
	output, err := l.svcCtx.FileModel.ListFiles(l.ctx, input)
	if err != nil {
		return nil, err
	}

	// Map output to response
	resp = &types.FileListResponse{
		Files: make([]types.FileResponse, 0),
		Total: output.Total,
	}

	// Calculate total pages
	if req.PageSize > 0 {
		resp.TotalPage = int(math.Ceil(float64(output.Total) / float64(req.PageSize)))
	} else {
		resp.TotalPage = 1
	}

	// Map files
	err = cast.ConvertViaJson(&resp.Files, output.Files)
	if err != nil {
		return nil, berr.ErrServerCommon.Wrap(err)
	}

	return resp, nil
}
