package file

import (
	"bcare/api/internal/auth"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/berr"
	"bcare/common/cast"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewDeleteLogic(ctx context.Context, svcCtx *svc.ServiceContext) DeleteLogic {
	return DeleteLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DeleteLogic) Delete(req *types.FileDeleteRequest) (resp *types.FileResponse, err error) {
	if !l.svcCtx.Auth.File.CanDelete(l.ctx, nil) {
		return nil, auth.ErrFileDeleteDenied
	}

	if req.Id <= 0 {
		return nil, berr.ErrRequestParam.Wrap(nil).Op("FileDeleteLogic.Delete")
	}

	// Get file before deletion to return in response
	fileOutput, err := l.svcCtx.FileModel.GetFile(l.ctx, req.Id)
	if err != nil {
		return nil, err
	}

	// Call model to delete file
	err = l.svcCtx.FileModel.DeleteFile(l.ctx, req.Id)
	if err != nil {
		return nil, err
	}

	// Map output to response
	resp = new(types.FileResponse)
	err = cast.ConvertViaJson(resp, fileOutput)
	if err != nil {
		return nil, berr.ErrServerCommon.Wrap(err)
	}

	return resp, nil
}
