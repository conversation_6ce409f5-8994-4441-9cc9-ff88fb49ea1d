package file

import (
	"bcare/api/internal/auth"
	"bcare/api/internal/dto"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/berr"
	"bcare/common/bfile"
	"bcare/common/butils"
	"bcare/common/cast"
	"context"
	"fmt"
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/logx"
)

const maxFileSize = 20 << 20 // 20 MB

type AddLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
	r      *http.Request
}

func NewAddLogic(ctx context.Context, r *http.Request, svcCtx *svc.ServiceContext) AddLogic {
	return AddLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
		r:      r,
	}
}

func (l *AddLogic) Add(req *types.FileAddRequest) (resp *types.FileResponse, err error) {
	if !l.svcCtx.Auth.File.CanAdd(l.ctx, nil) {
		return nil, auth.ErrFileAddDenied
	}

	err = l.r.ParseMultipartForm(maxFileSize)
	if err != nil {
		return nil, berr.ErrRequestParam.Wrap(err)
	}

	tempFile, header, err := l.r.FormFile("file_upload")
	if err != nil {
		return nil, berr.ErrRequestParam.Wrap(err)
	}
	defer tempFile.Close()

	// Validate file type
	err = bfile.ValidateFileExtension(header.Filename)
	if err != nil {
		return nil, berr.ErrValidationFailed.Wrap(err)
	}

	// Validate mime type
	mimeType, err := bfile.ValidateFileType(&tempFile, header)
	if err != nil {
		return nil, berr.ErrValidationFailed.Wrap(err)
	}

	// Generate KSUID for file name
	generatedName := butils.NewID()
	filePath := ""

	// Create directory structure based on date
	dirPath, err := createDirStructure(l.svcCtx.Config.Storage.Local.Path, bfile.GetKindFromMIMEType(mimeType))
	if err != nil {
		return nil, berr.ErrServerCommon.Wrap(err)
	}

	// Save file to disk
	if bfile.IsImageFile(&tempFile, header, mimeType) {
		filePath = filepath.Join(dirPath, generatedName+".jpg")
		err = bfile.OptimizeImage(tempFile, filePath)
		if err != nil {
			return nil, berr.ErrServerCommon.Wrap(err)
		}
	} else {
		fName := generatedName[len(generatedName)-5:] + "_" + butils.NormalizeFilename(header.Filename)
		filePath = filepath.Join(dirPath, fName)
		err = bfile.SaveFileToDisk(tempFile, filePath)
		if err != nil {
			return nil, berr.ErrServerCommon.Wrap(err)
		}
	}

	// Create file input for model
	fileInput := &dto.CreateFileInput{
		Name:    header.Filename,
		Size:    int(header.Size),
		Type:    mimeType,
		Path:    strings.TrimPrefix(filePath, "storage"),
		Storage: req.Storage,
		Kind:    req.Kind,
	}

	// Call model to create file
	fileOutput, err := l.svcCtx.FileModel.CreateFile(l.ctx, fileInput)
	if err != nil {
		return nil, err
	}

	// Map output to response
	resp = new(types.FileResponse)
	err = cast.ConvertViaJson(resp, fileOutput)
	if err != nil {
		return nil, berr.ErrServerCommon.Wrap(err)
	}

	return resp, nil
}

func createDirStructure(basePath, kind string) (string, error) {
	now := time.Now()
	dirPath := filepath.Join(basePath, kind, fmt.Sprintf("%d_%02d_%02d", now.Year(), now.Month(), now.Day()))

	err := os.MkdirAll(dirPath, 0755)
	if err != nil {
		return "", err
	}

	return dirPath, nil
}
