package file

import (
	"bcare/api/internal/auth"
	"bcare/api/internal/dto"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/berr"
	"bcare/common/cast"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateLogic(ctx context.Context, svcCtx *svc.ServiceContext) UpdateLogic {
	return UpdateLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateLogic) Update(req *types.FileUpdateRequest) (resp *types.FileResponse, err error) {
	if !l.svcCtx.Auth.File.CanUpdate(l.ctx, nil) {
		return nil, auth.ErrFileUpdateDenied
	}

	if req.Id <= 0 {
		return nil, berr.ErrRequestParam.Wrap(nil).Op("FileUpdateLogic.Update")
	}

	// Map request to input
	input := &dto.UpdateFileInput{
		ID:       req.Id,
		Name:     req.Name,
		Kind:     req.Kind,
		Type:     req.Type,
		Size:     req.Size,
		Path:     req.Path,
		UserID:   req.UserId,
		Storage:  req.Storage,
		Modified: req.Modified,
	}

	// Call model to update file
	fileOutput, err := l.svcCtx.FileModel.UpdateFile(l.ctx, input)
	if err != nil {
		return nil, err
	}

	// Map output to response
	resp = new(types.FileResponse)
	err = cast.ConvertViaJson(resp, fileOutput)
	if err != nil {
		return nil, berr.ErrServerCommon.Wrap(err)
	}

	return resp, nil
}
