package economy_deposit

import (
	"bcare/common/bquery"
	"bcare/common/butils"
	"bcare/common/cast"
	"bcare/common/ctxdata"
	"bcare/ent"
	"bcare/ent/deposit"
	"bcare/ent/depositallocation"
	"context"
	"fmt"
	"github.com/jinzhu/copier"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateLogic(ctx context.Context, svcCtx *svc.ServiceContext) UpdateLogic {
	return UpdateLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateLogic) Update(req *types.UpdateDepositRequest) (resp *types.DepositResponse, err error) {
	depositRecord, err := l.svcCtx.Ent.Deposit.Query().
		Where(deposit.ID(req.Id)).First(l.ctx)
	if err != nil && ent.IsNotFound(err) {
		return nil, types.ErrDepositNotFound.Wrap(err, err.Error())
	}

	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		// update deposit
		if err := copier.CopyWithOption(&depositRecord, req, cast.PayloadToEnt); err != nil {
			return types.ErrDepositCopyFailed.Wrap(err, "Error copying task data")
		}
		depositRecord.UpdatedBy = ctxdata.GetUidFromCtx(l.ctx)

		// update allocations if any
		if len(req.Allocations) > 0 || butils.Contains(req.Modified, "allocations") {
			if err := l.UpdateAllocations(tx, depositRecord, req.Allocations); err != nil {
				return err
			}
		}
		depositRecord, err = tx.Deposit.UpdateOneID(req.Id).SetDeposit(depositRecord, req.Modified...).Save(l.ctx)
		if err != nil {
			return types.ErrTaskUpdate.Wrap(err, err.Error())
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	// Query lại deposit với các relations để trả về response đầy đủ
	updatedDeposit, err := l.svcCtx.Ent.Deposit.Query().
		WithAllocations().
		WithPayments().
		WithDeal().
		Where(deposit.ID(req.Id)).
		First(l.ctx)
	if err != nil {
		return nil, types.ErrDepositNotFound.Wrap(err, err.Error())
	}

	// Convert to DepositResponse
	resp = new(types.DepositResponse)
	err = cast.ConvertViaJson(&resp, updatedDeposit)
	return resp, err
}

func (l *UpdateLogic) UpdateAllocations(tx *ent.Tx, depositRecord *ent.Deposit, newAllocations []types.AllocationAddRequest) error {
	// Get existing allocations
	existingAllocations, err := tx.DepositAllocation.Query().
		Where(depositallocation.DepositID(depositRecord.ID)).
		All(l.ctx)
	if err != nil {
		return types.ErrDepositUpdate.Wrap(err, "Error querying existing allocations")
	}

	// Create map of existing allocations using composite key
	existingMap := make(map[string]*ent.DepositAllocation)
	for _, allocation := range existingAllocations {
		key := fmt.Sprintf("%d-%d", allocation.AttachmentID, allocation.DealID)
		existingMap[key] = allocation
	}

	// Process new allocations
	for _, newAllocation := range newAllocations {
		key := fmt.Sprintf("%d-%d", newAllocation.AttachmentId, newAllocation.DealId)

		if existing, ok := existingMap[key]; ok {
			// Update existing allocation if needed
			if existing.Amount != newAllocation.Amount {
				_, err := tx.DepositAllocation.UpdateOne(existing).
					SetAmount(newAllocation.Amount).
					SetUpdatedBy(ctxdata.GetUidFromCtx(l.ctx)).
					Save(l.ctx)
				if err != nil {
					return types.ErrDepositUpdate.Wrap(err, "Error updating allocation")
				}
			}
			delete(existingMap, key)
		} else {
			allocationRecord := new(ent.DepositAllocation)

			// Copy fields from request to entity
			if err := copier.CopyWithOption(&allocationRecord, newAllocation, cast.PayloadToEnt); err != nil {
				return types.ErrDepositCopyFailed.Wrap(err, "failed to copy allocation data")
			}

			// Set additional fields
			allocationRecord.DepositID = depositRecord.ID
			allocationRecord.CreatedBy = ctxdata.GetUidFromCtx(l.ctx)

			_, err := tx.DepositAllocation.Create().
				SetDepositAllocation(allocationRecord).
				Save(l.ctx)
			if err != nil {
				return types.ErrDepositUpdate.Wrap(err, "Error creating allocation")
			}
		}
	}

	// Delete remaining allocations that weren't in the new set
	for _, allocation := range existingMap {
		err := tx.DepositAllocation.DeleteOne(allocation).Exec(l.ctx)
		if err != nil {
			return types.ErrDepositUpdate.Wrap(err, "Error deleting allocation")
		}
	}

	return nil
}
