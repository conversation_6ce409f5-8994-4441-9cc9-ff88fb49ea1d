package economy_deposit

import (
	"bcare/common/bquery"
	"bcare/common/cast"
	"bcare/common/ctxdata"
	"bcare/ent"
	"context"
	"github.com/jinzhu/copier"
	"time"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type ConvertToPaymentLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewConvertToPaymentLogic(ctx context.Context, svcCtx *svc.ServiceContext) ConvertToPaymentLogic {
	return ConvertToPaymentLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ConvertToPaymentLogic) ConvertToPayment(req *types.ConvertToPaymentRequest) (resp *types.DepositPayment, err error) {
	depositPaymentRecord := new(ent.DepositPayment)
	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {

		// Copy fields from request to entity
		if err := copier.CopyWithOption(&depositPaymentRecord, req, cast.PayloadToEnt); err != nil {
			return types.ErrDepositCopyFailed.Wrap(err, "failed to copy allocation data")
		}

		// Set additional fields
		depositPaymentRecord.ConversionDate = time.Now()
		depositPaymentRecord.CreatedBy = ctxdata.GetUidFromCtx(l.ctx)

		depositPaymentRecord, err = tx.DepositPayment.Create().SetDepositPayment(depositPaymentRecord).Save(l.ctx)

		if err != nil {
			return types.ErrDepositAdd.Wrap(err, "Error checking deal existence")
		}
		return nil
	})

	if err != nil {
		return nil, err
	}

	// Convert to response type
	resp = new(types.DepositPayment)
	if err := cast.ConvertViaJson(&resp, depositPaymentRecord); err != nil {
		return nil, types.ErrDepositAdd.Wrap(err)
	}

	return resp, nil
}
