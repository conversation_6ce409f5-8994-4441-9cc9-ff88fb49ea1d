package economy_deposit

import (
	"bcare/ent/depositpayment"
	"context"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type CancelConversionLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewCancelConversionLogic(ctx context.Context, svcCtx *svc.ServiceContext) CancelConversionLogic {
	return CancelConversionLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *CancelConversionLogic) CancelConversion(req *types.DeleteRequest) (resp *types.DepositPayment, err error) {
	_, err = l.svcCtx.Ent.DepositPayment.Delete().Where(depositpayment.ID(req.Id)).Exec(l.ctx)
	if err != nil {
		return nil, types.ErrDepositDelete.Wrap(err)
	}
	return
}
