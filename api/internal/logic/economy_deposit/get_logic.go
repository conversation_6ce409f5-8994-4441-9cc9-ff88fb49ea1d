package economy_deposit

import (
	cast2 "bcare/common/cast"
	"bcare/ent"
	"bcare/ent/deposit"
	"context"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetLogic(ctx context.Context, svcCtx *svc.ServiceContext) GetLogic {
	return GetLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetLogic) Get(req *types.GetRequest) (resp *types.DepositResponse, err error) {
	depositRecord, err := l.svcCtx.Ent.Deposit.Query().
		WithAllocations().
		WithPayments().
		WithDeal().
		Where(deposit.ID(req.Id)).First(l.ctx)

	if err != nil && ent.IsNotFound(err) {
		return nil, types.ErrDepositNotFound.Wrap(err, err.Error())
	}

	resp = new(types.DepositResponse)
	err = cast2.ConvertViaJson(&resp, depositRecord)
	return resp, nil
}
