package economy_deposit

import (
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/bquery"
	"bcare/common/cast"
	"bcare/common/ctxdata"
	"bcare/ent"
	"context"
	"github.com/jinzhu/copier"
	"github.com/zeromicro/go-zero/core/logx"
)

type AddLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAddLogic(ctx context.Context, svcCtx *svc.ServiceContext) AddLogic {
	return AddLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AddLogic) Add(req *types.DepositAddRequest) (resp *types.DepositResponse, err error) {
	var depositRecord *ent.Deposit
	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		// Create main deposit
		depositRecord, err = l.createDeposit(tx, req)
		if err != nil {
			return err
		}

		// Create allocations if any
		if len(req.Allocations) > 0 {
			if err := l.createAllocations(tx, depositRecord, req.Allocations); err != nil {
				return err
			}
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	// Convert ent.Deposit to types.Deposit
	resp = new(types.DepositResponse)
	err = cast.ConvertViaJson(&resp, depositRecord)
	return resp, err
}

// createDeposit creates a new deposit record
func (l *AddLogic) createDeposit(tx *ent.Tx, req *types.DepositAddRequest) (*ent.Deposit, error) {
	depositRecord := new(ent.Deposit)

	// Copy fields from request to entity
	if err := copier.CopyWithOption(&depositRecord, req, cast.PayloadToEnt); err != nil {
		return nil, types.ErrDepositCopyFailed.Wrap(err, "failed to copy request data")
	}

	// Set default values and computed fields
	depositRecord.RemainingAmount = depositRecord.TotalAmount
	depositRecord.CreatedBy = ctxdata.GetUidFromCtx(l.ctx)

	// Validate total amount
	if depositRecord.TotalAmount <= 0 {
		return nil, types.ErrInvalidAmount.Wrap(nil)
	}

	result, err := tx.Deposit.Create().
		SetDeposit(depositRecord).
		Save(l.ctx)

	if err != nil {
		return nil, types.ErrDepositAdd.Wrap(err, "Error creating new deposit")
	}

	return result, nil
}

// createAllocations creates deposit allocations
func (l *AddLogic) createAllocations(tx *ent.Tx, deposit *ent.Deposit, allocations []types.AllocationAddRequest) error {
	// Validate total allocation amount doesn't exceed deposit amount
	var totalAllocation float64
	for _, alloc := range allocations {
		totalAllocation += alloc.Amount
	}
	if totalAllocation > deposit.TotalAmount {
		return types.ErrDepositInvalid.Wrap(nil, "total allocation amount exceeds deposit amount")
	}

	// Create bulk allocations
	bulkAllocations := make([]*ent.DepositAllocationCreate, len(allocations))
	for i, alloc := range allocations {
		allocationRecord := new(ent.DepositAllocation)

		// Copy fields from request to entity
		if err := copier.CopyWithOption(&allocationRecord, alloc, cast.PayloadToEnt); err != nil {
			return types.ErrDepositCopyFailed.Wrap(err, "failed to copy allocation data")
		}

		// Set additional fields
		allocationRecord.DepositID = deposit.ID
		allocationRecord.CreatedBy = ctxdata.GetUidFromCtx(l.ctx)

		bulkAllocations[i] = tx.DepositAllocation.Create().SetDepositAllocation(allocationRecord)
	}

	_, err := tx.DepositAllocation.CreateBulk(bulkAllocations...).Save(l.ctx)
	if err != nil {
		return types.ErrDepositAdd.Wrap(err, "Error creating allocations")
	}

	return nil
}
