package economy_deposit

import (
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/bquery"
	"bcare/common/cast"
	"bcare/common/ctxdata"
	"bcare/ent"
	"context"
	"github.com/jinzhu/copier"

	"github.com/zeromicro/go-zero/core/logx"
)

type AllocationAddLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAllocationAddLogic(ctx context.Context, svcCtx *svc.ServiceContext) AllocationAddLogic {
	return AllocationAddLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AllocationAddLogic) AllocationAdd(req *types.AllocationAddRequest) (resp *types.DepositAllocation, err error) {
	allocationRecord := new(ent.DepositAllocation)
	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {

		// Copy fields from request to entity
		if err := copier.CopyWithOption(&allocationRecord, req, cast.PayloadToEnt); err != nil {
			return types.ErrDepositCopyFailed.Wrap(err, "failed to copy allocation data")
		}

		// Set additional fields
		allocationRecord.CreatedBy = ctxdata.GetUidFromCtx(l.ctx)

		allocationRecord, err = tx.DepositAllocation.Create().SetDepositAllocation(allocationRecord).Save(l.ctx)

		if err != nil {
			return types.ErrDepositAdd.Wrap(err, "Error checking deal existence")
		}
		return nil
	})

	if err != nil {
		return nil, err
	}

	// Convert to response type
	resp = new(types.DepositAllocation)
	if err := cast.ConvertViaJson(&resp, allocationRecord); err != nil {
		return nil, types.ErrDepositAdd.Wrap(err)
	}

	return resp, nil
}
