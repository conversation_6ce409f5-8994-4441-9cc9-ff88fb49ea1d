package economy_deposit

import (
	"bcare/ent/deposit"
	"context"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewDeleteLogic(ctx context.Context, svcCtx *svc.ServiceContext) DeleteLogic {
	return DeleteLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DeleteLogic) Delete(req *types.DeleteRequest) (resp *types.Deposit, err error) {
	_, err = l.svcCtx.Ent.Deposit.Delete().Where(deposit.ID(req.Id)).Exec(l.ctx)
	if err != nil {
		return nil, types.ErrDepositDelete.Wrap(err)
	}
	return

}
