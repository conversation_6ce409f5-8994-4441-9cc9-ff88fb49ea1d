package economy_deposit

import (
	"bcare/common/bquery"
	cast2 "bcare/common/cast"
	"bcare/ent"
	"context"
	"entgo.io/ent/dialect/sql"
	"github.com/samber/lo"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type ListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewListLogic(ctx context.Context, svcCtx *svc.ServiceContext) ListLogic {
	return ListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ListLogic) List(req *types.DepositListRequest) (resp *types.DepositListResponse, err error) {
	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		limit, offset := bquery.Paging(req.PageSize, req.Page)
		order := bquery.Ordering(req.OrderBy)
		query := tx.Deposit.Query().
			WithAllocations().
			WithPayments().
			WithDeal().
			Order(order)

		filterMap := make(map[string]interface{})
		_ = cast2.AtomNoZero(&filterMap, req.Filter)

		for field, value := range filterMap {
			query = query.Where(sql.FieldEQ(field, value))
		}

		total, _ := query.Count(l.ctx)
		deposits, e := query.Limit(limit).Offset(offset).All(l.ctx) // Giả sử limit là 10 và offset là 5

		if e != nil {
			err = types.ErrDepositGetList.Wrap(e, err.Error())
			return err
		}

		resp = new(types.DepositListResponse)
		resp.Deposits = lo.Map(deposits, func(d *ent.Deposit, _ int) types.DepositResponse {
			r := types.DepositResponse{}
			_ = cast2.ConvertViaJson(&r, d)
			return r
		})
		resp.Total = total

		return nil
	})

	return resp, err

}
