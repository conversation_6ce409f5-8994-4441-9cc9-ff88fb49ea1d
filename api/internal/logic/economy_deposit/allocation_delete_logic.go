package economy_deposit

import (
	"bcare/ent/depositallocation"
	"context"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type AllocationDeleteLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAllocationDeleteLogic(ctx context.Context, svcCtx *svc.ServiceContext) AllocationDeleteLogic {
	return AllocationDeleteLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AllocationDeleteLogic) AllocationDelete(req *types.DeleteRequest) (resp *types.DepositAllocation, err error) {
	_, err = l.svcCtx.Ent.DepositAllocation.Delete().Where(depositallocation.ID(req.Id)).Exec(l.ctx)
	if err != nil {
		return nil, types.ErrDepositDelete.Wrap(err)
	}
	return
}
