package tag_person

import (
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/bquery"
	"bcare/ent"
	"bcare/ent/tagperson"
	"context"
	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewDeleteLogic(ctx context.Context, svcCtx *svc.ServiceContext) DeleteLogic {
	return DeleteLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DeleteLogic) Delete(req *types.TagPersonDeleteRequest) (resp *types.TagPerson, err error) {
	var tagPersonRecord *ent.TagPerson

	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		tagPersonRecord, err = tx.TagPerson.Query().Where(tagperson.TagID(req.TagId), tagperson.PersonID(req.PersonId)).First(l.ctx)

		if err != nil && ent.IsNotFound(err) {
			return err
		}
		err = tx.TagPerson.DeleteOneID(tagPersonRecord.ID).Exec(l.ctx)
		if err != nil {
			return err
		}
		return nil
	})

	if err != nil {
		return nil, err
	}

	return resp, nil
}
