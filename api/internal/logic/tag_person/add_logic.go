package tag_person

import (
	"bcare/common/bquery"
	"bcare/common/ctxdata"
	"bcare/ent"
	"context"
	"github.com/jinzhu/copier"
	"github.com/pkg/errors"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type AddLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAddLogic(ctx context.Context, svcCtx *svc.ServiceContext) AddLogic {
	return AddLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AddLogic) Add(req *types.TagPersonAddRequest) (resp *types.TagPerson, err error) {
	tagPersonRecord := new(ent.TagPerson)
	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		err = copier.Copy(&tagPersonRecord, req)
		if err != nil {
			err = errors.Wrapf(err, "cannot copy reques: %v", req)
			return err
		}
		tagPersonRecord.AssignedBy = ctxdata.GetUidFromCtx(l.ctx)

		tagPersonRecord, err = tx.TagPerson.Create().
			SetTagPerson(tagPersonRecord).
			Save(l.ctx)
		if err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	resp = new(types.TagPerson)
	_ = copier.Copy(&resp, tagPersonRecord)
	return resp, nil
}
