package tag_person

import (
	"bcare/common/bquery"
	cast2 "bcare/common/cast"
	"bcare/ent"
	"context"
	"github.com/jinzhu/copier"

	"bcare/api/internal/svc"
	"bcare/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateLogic(ctx context.Context, svcCtx *svc.ServiceContext) UpdateLogic {
	return UpdateLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateLogic) Update(req *types.TagPersonUpdateRequest) (resp *types.TagPerson, err error) {
	tagPersonRecord := new(ent.TagPerson)
	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		tagPersonRecord, err = tx.TagPerson.Get(l.ctx, req.Id)
		if err != nil {
			return err
		}

		err = copier.CopyWithOption(&tagPersonRecord, req, cast2.PayloadToEnt)
		if err != nil {
			return err
		}

		tagPersonRecord, err = tx.TagPerson.UpdateOneID(tagPersonRecord.ID).SetTagPerson(tagPersonRecord).Save(l.ctx)

		if err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	resp = new(types.TagPerson)
	_ = copier.Copy(&resp, tagPersonRecord)
	return resp, nil
}
