package middleware

import (
	"encoding/json"
	"net"
	"net/http"
	"strings"
	"sync"
	"time"

	"github.com/zeromicro/go-zero/core/logx"
)

const (
	// ipLimitPrefix     = "ip_limit:" // No longer needed with map
	requestsPerIPDay = 3
	cleanupInterval  = 1 * time.Hour // How often to clean up old entries
)

type ipInfo struct {
	count      int
	lastAccess time.Time // Used for cleanup, not strict expiry
	resetTime  time.Time // When the count should reset (midnight)
}

// IPLimitMiddleware restricts requests per IP per day using an in-memory store.
type IPLimitMiddleware struct {
	ipData sync.Map     // Map[string]*ipInfo
	mu     sync.RWMutex // Protects the map during cleanup
}

// NewIPLimitMiddleware creates a new instance of IPLimitMiddleware using in-memory storage.
func NewIPLimitMiddleware() *IPLimitMiddleware {
	m := &IPLimitMiddleware{}
	// Start a background goroutine to clean up old entries
	go m.cleanupLoop()
	return m
}

// cleanupLoop periodically removes entries that haven't been accessed recently
// and whose reset time has passed significantly.
func (m *IPLimitMiddleware) cleanupLoop() {
	ticker := time.NewTicker(cleanupInterval)
	defer ticker.Stop()

	for range ticker.C {
		m.mu.Lock() // Lock for writing (deletion)
		now := time.Now()
		m.ipData.Range(func(key, value interface{}) bool {
			info := value.(*ipInfo)
			// Remove if reset time is past and not accessed for a while (e.g., > 1 day old)
			if now.After(info.resetTime) && now.Sub(info.lastAccess) > 24*time.Hour {
				m.ipData.Delete(key)
				logx.Infof("Cleaned up expired IP limit entry for %s", key.(string))
			}
			return true // continue iteration
		})
		m.mu.Unlock()
	}
}

// GetClientIP extracts the client IP address from the request headers or remote address.
func GetClientIP(r *http.Request) string {
	ip := r.Header.Get("X-Forwarded-For")
	if ip != "" {
		ips := strings.Split(ip, ",")
		if len(ips) > 0 {
			// The first IP in X-Forwarded-For is often the original client IP
			return strings.TrimSpace(ips[0])
		}
	}

	ip = r.Header.Get("X-Real-IP")
	if ip != "" {
		return ip
	}

	// Fallback to RemoteAddr, attempting to split host and port
	ip, _, err := net.SplitHostPort(r.RemoteAddr)
	if err == nil {
		return ip
	}

	// If splitting fails (e.g., IPv6 without port), return the original RemoteAddr
	return r.RemoteAddr
}

// Handle is the middleware handler function
func (m *IPLimitMiddleware) Handle(next http.HandlerFunc) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Use the GetClientIP function
		ip := GetClientIP(r)
		logx.Infof("Client IP: %s", ip) // Keep one log line for the IP
		now := time.Now()

		m.mu.RLock() // Read lock to access map
		v, exists := m.ipData.Load(ip)
		m.mu.RUnlock()

		var currentInfo *ipInfo

		if !exists {
			// First request for this IP or entry expired/cleaned up
			midnight := time.Date(now.Year(), now.Month(), now.Day()+1, 0, 0, 0, 0, now.Location())
			currentInfo = &ipInfo{
				count:      1,
				lastAccess: now,
				resetTime:  midnight,
			}
		} else {
			// Entry exists
			currentInfo = v.(*ipInfo)

			// Check if reset time has passed
			if now.After(currentInfo.resetTime) {
				// Reset the count and update reset time
				midnight := time.Date(now.Year(), now.Month(), now.Day()+1, 0, 0, 0, 0, now.Location())
				currentInfo.count = 1
				currentInfo.resetTime = midnight
			} else {
				// Still within the same day limit
				currentInfo.count++
			}
			currentInfo.lastAccess = now
		}

		// Check limit
		logx.Infof("IP limit exceeded for %s (count: %d)", ip, currentInfo.count)

		if currentInfo.count > requestsPerIPDay {
			logx.Infof("IP limit exceeded for %s (count: %d)", ip, currentInfo.count)
			w.Header().Set("Content-Type", "application/json")
			w.WriteHeader(http.StatusTooManyRequests)
			json.NewEncoder(w).Encode(map[string]interface{}{
				"error":   "rate_limit_exceeded",
				"message": "Too Many Requests",
				"status":  http.StatusTooManyRequests,
			})
			return
		}

		// Store updated info
		m.mu.Lock() // Lock for writing
		m.ipData.Store(ip, currentInfo)
		m.mu.Unlock()

		logx.Infof("IP %s request count: %d/%d (reset at %s)", ip, currentInfo.count, requestsPerIPDay, currentInfo.resetTime.Format(time.RFC3339))
		next(w, r)
	}
}
