package middleware

import (
	"bcare/common/bconst"
	"context"
	"crypto/rand"
	"encoding/base64"
	"encoding/json"
	"net/http"
	"sync"
	"time"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/rest"
)

const (
	csrfHeaderName = "X-CSRF-Token"
)

// TokenStore interface để có thể thay đổi implementation
type TokenStoreInterface interface {
	GenerateToken() string
	ValidateToken(token string) bool
}

// MemoryTokenStore implementation
type MemoryTokenStore struct {
	validTokens map[string]time.Time
	usedTokens  map[string]time.Time
	mutex       sync.RWMutex
	expiration  time.Duration
}

func NewMemoryTokenStore(expiration time.Duration) *MemoryTokenStore {
	store := &MemoryTokenStore{
		validTokens: make(map[string]time.Time),
		usedTokens:  make(map[string]time.Time),
		expiration:  expiration,
	}

	go store.cleanup()

	return store
}

func (s *MemoryTokenStore) GenerateToken() string {
	b := make([]byte, bconst.CsrfTokenLength)
	rand.Read(b)
	token := base64.StdEncoding.EncodeToString(b)

	s.mutex.Lock()
	s.validTokens[token] = time.Now()
	s.mutex.Unlock()

	return token
}

func (s *MemoryTokenStore) ValidateToken(token string) bool {
	if token == "" {
		return false
	}

	s.mutex.RLock()
	_, tokenUsed := s.usedTokens[token]
	createdTime, tokenValid := s.validTokens[token]
	s.mutex.RUnlock()

	if tokenUsed {
		return false
	}

	if !tokenValid {
		return false
	}

	// Kiểm tra token đã hết hạn chưa
	if time.Since(createdTime) > s.expiration {
		logx.WithContext(context.Background()).Infof("CSRF: Token expired: %s", token)
		s.mutex.Lock()
		delete(s.validTokens, token)
		s.mutex.Unlock()
		return false
	}

	// Đánh dấu token đã sử dụng
	s.mutex.Lock()
	delete(s.validTokens, token)
	s.usedTokens[token] = time.Now()
	s.mutex.Unlock()

	return true
}

func (s *MemoryTokenStore) cleanup() {
	ticker := time.NewTicker(10 * time.Minute)
	defer ticker.Stop()

	for range ticker.C {
		bgCtx := context.Background()
		now := time.Now()
		s.mutex.Lock()

		// Xóa token hết hạn khỏi danh sách token hợp lệ
		deletedValid := 0
		for token, createdAt := range s.validTokens {
			if now.Sub(createdAt) > s.expiration {
				delete(s.validTokens, token)
				deletedValid++
			}
		}

		// Xóa token cũ khỏi danh sách token đã sử dụng
		deletedUsed := 0
		for token, usedAt := range s.usedTokens {
			if now.Sub(usedAt) > s.expiration {
				delete(s.usedTokens, token)
				deletedUsed++
			}
		}

		s.mutex.Unlock()
		if deletedValid > 0 || deletedUsed > 0 {
			logx.WithContext(bgCtx).Infof("CSRF Cleanup: Removed %d expired valid tokens, %d expired used tokens.", deletedValid, deletedUsed)
		}
	}
}

func (s *MemoryTokenStore) DumpTokenInfo() {
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	bgCtx := context.Background()

	logx.WithContext(bgCtx).Infof("===== CSRF Token Store Info ====")
	logx.WithContext(bgCtx).Infof("Valid tokens count: %d", len(s.validTokens))
	logx.WithContext(bgCtx).Infof("Used tokens count: %d", len(s.usedTokens))

	for token, t := range s.validTokens {
		tokenPrefix := token
		if len(tokenPrefix) > 10 {
			tokenPrefix = token[:10] + "..."
		}
		logx.WithContext(bgCtx).Infof("  Valid token: %s created at: %s", tokenPrefix, t)
	}

	for token, t := range s.usedTokens {
		tokenPrefix := token
		if len(tokenPrefix) > 10 {
			tokenPrefix = token[:10] + "..."
		}
		logx.WithContext(bgCtx).Infof("  Used token: %s used at: %s", tokenPrefix, t)
	}

	logx.WithContext(bgCtx).Infof("=================================")
}

// CSRF Middleware
func NewCSRFMiddleware(store TokenStoreInterface) rest.Middleware {
	return func(next http.HandlerFunc) http.HandlerFunc {
		return func(w http.ResponseWriter, r *http.Request) {
			// Bỏ qua các method an toàn
			if r.Method == http.MethodGet || r.Method == http.MethodHead || r.Method == http.MethodOptions {
				next(w, r)
				return
			}

			// Lấy token từ header
			token := r.Header.Get(csrfHeaderName)

			// Xác thực token
			if !store.ValidateToken(token) {
				w.Header().Set("Content-Type", "application/json")
				w.WriteHeader(http.StatusForbidden)
				json.NewEncoder(w).Encode(map[string]interface{}{
					"error":   "invalid_token",
					"message": "Invalid or used CSRF token",
					"status":  403,
				})
				return
			}

			next(w, r)
		}
	}
}

// API endpoint để tạo CSRF token
func GetCSRFToken(store TokenStoreInterface) string {
	token := store.GenerateToken()
	return token
}
