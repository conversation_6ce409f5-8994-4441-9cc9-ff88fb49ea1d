package middleware

import (
	"bcare/common/berr"
	"github.com/zeromicro/go-zero/rest/httpx"
	"golang.org/x/time/rate"
	"net/http"
)

type RateLimitMiddleware struct {
	limiter *rate.Limiter
}

// NewRateLimitMiddleware tạo một middleware mới để giới hạn tỷ lệ request
func NewRateLimitMiddleware(rps int) func(http.HandlerFunc) http.HandlerFunc {
	m := &RateLimitMiddleware{
		limiter: rate.NewLimiter(rate.Limit(rps), rps*2),
	}

	return m.Handle
}

func (m *RateLimitMiddleware) Handle(next http.HandlerFunc) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		if !m.limiter.Allow() {
			httpx.Error(w, berr.NewErrCodeMsg(berr.TooManyRequest, "Too many request"))
			return
		}
		next(w, r)
	}
}
