package middleware

import (
	"bcare/common/bauth"
	"bcare/common/berr"
	"bcare/common/bresult"
	"github.com/casbin/casbin/v2"
	"github.com/zeromicro/go-zero/core/logx"
	"net/http"
)

type (
	// AuthorizerMiddleware stores the casbin handler
	AuthorizerMiddleware struct {
		enforcer *casbin.Enforcer
		uidField string
		domain   string
	}
	// AuthorizerMiddlewareOption represents an option.
	AuthorizerMiddlewareOption func(opt *AuthorizerMiddleware)
)

// WithUidField returns a custom user unique identity option.
func WithUidField(uidField string) AuthorizerMiddlewareOption {
	return func(opt *AuthorizerMiddleware) {
		opt.uidField = uidField
	}
}

// WithDomain returns a custom domain option.
func WithDomain(domain string) AuthorizerMiddlewareOption {
	return func(opt *AuthorizerMiddleware) {
		opt.domain = domain
	}
}

// NewAuthorizerMiddleware returns the authorizer, uses a Casbin enforcer as input
func NewAuthorizerMiddleware(auth *bauth.Authorizer, opts ...AuthorizerMiddlewareOption) *AuthorizerMiddleware {
	a := &AuthorizerMiddleware{enforcer: auth.Enforcer}
	// init an AuthorizerMiddleware
	a.init(opts...)

	return a
}

func (m *AuthorizerMiddleware) Handle(next http.HandlerFunc) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		if !m.CheckPermission(r) {
			bresult.HttpResult(r, w, nil, berr.NewErrCodeMsg(berr.PermissionDenied, "you are not authorized to perform this action"))
			return
		}
		next(w, r)
	}
}

func (m *AuthorizerMiddleware) init(opts ...AuthorizerMiddlewareOption) {
	m.uidField = "id"
	m.domain = "domain"
	for _, opt := range opts {
		opt(m)
	}
}

// GetUid gets the uid from the JWT Claims.
func (m *AuthorizerMiddleware) GetUid(r *http.Request) (string, bool) {
	uid, ok := r.Context().Value(m.uidField).(string)
	return uid, ok
}

// GetDomain returns the domain from the request.
func (m *AuthorizerMiddleware) GetDomain(r *http.Request) (string, bool) {
	domain, ok := r.Context().Value(m.domain).(string)
	return domain, ok
}

// CheckPermission checks the user/method/path combination from the request.
// Returns true (permission granted) or false (permission forbidden)
func (m *AuthorizerMiddleware) CheckPermission(r *http.Request) bool {
	uid, ok := m.GetUid(r)
	if !ok {
		return false
	}
	method := r.Method
	path := r.URL.Path
	var (
		allowed = false
		err     error
	)
	domain, withDomain := m.GetDomain(r)

	if withDomain {
		allowed, err = m.enforcer.Enforce(uid, domain, path, method)
	} else {
		allowed, err = m.enforcer.Enforce(uid, path, method)
	}

	if err != nil {
		logx.WithContext(r.Context()).Errorf("[AUTHORIZER] enforce err %s", err.Error())
	}
	return allowed
}

// RequirePermission returns the 403 Forbidden to the client.
func (m *AuthorizerMiddleware) RequirePermission(writer http.ResponseWriter) {
	writer.WriteHeader(http.StatusForbidden)
}
