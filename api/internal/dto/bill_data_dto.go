package dto

type CreateBillDataInput struct {
	BillID int                    `json:"bill_id"`
	Kind   string                 `json:"kind"`
	Data   map[string]interface{} `json:"data"`
	UserID int                    `json:"user_id"`
}

type UpdateBillDataInput struct {
	ID       int                    `json:"id"`
	BillID   int                    `json:"bill_id"`
	Kind     string                 `json:"kind"`
	Data     map[string]interface{} `json:"data"`
	Modified []string               `json:"modified"`
}

type BillDataOutput struct {
	Kind string                 `json:"kind"`
	Data map[string]interface{} `json:"data"`
}

type ListBillDataInput struct {
	PageSize int            `json:"page_size,omitempty"`
	Page    int            `json:"page,omitempty"`
	Filter  BillDataFilter `json:"filter,omitempty"`
	OrderBy string         `json:"order_by,omitempty"`
}

type BillDataFilter struct {
	BillID int    `json:"bill_id,omitempty"`
	Kind   string `json:"kind,omitempty"`
}

type ListBillDataOutput struct {
	BillData  []BillDataOutput `json:"bill_data"`
	Total     int              `json:"total"`
	TotalPage int              `json:"total_page"`
}
