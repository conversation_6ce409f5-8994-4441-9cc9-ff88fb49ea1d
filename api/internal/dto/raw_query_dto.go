package dto

// Parameter represents a parameter with a specific data type
type Parameter struct {
	Type             string `json:"type"` // string|int|float|bool|null|date|time|datetime|timestamp|array|json|uuid|bytea
	Value            string `json:"value"`
	ArrayElementType string `json:"arrayElementType,omitempty"` // Only used when Type is "array"
}

// RawQueryInput represents the request for executing a raw SQL query
type RawQueryInput struct {
	SQL        string      `json:"sql"`                          // SQL query with placeholders $1, $2, ...
	Parameters []Parameter `json:"parameters,omitempty"`         // List of typed parameters
	Timeout    int         `json:"timeout,omitempty,default=30"` // Query timeout in seconds
}

// RawQueryOutput represents the response for a raw SQL query execution
type RawQueryOutput struct {
	Results       []map[string]interface{} `json:"results"`
	RowCount      int64                    `json:"rowCount,omitempty"`
	ExecutionTime float64                  `json:"executionTime,omitempty"` // Query execution time in ms
	ColumnTypes   map[string]string        `json:"columnTypes,omitempty"`   // Column data type information
}

// QueryError represents an error that occurred during query execution
type QueryError struct {
	Code    string `json:"code"`
	Message string `json:"message"`
	Detail  string `json:"detail,omitempty"`
}
