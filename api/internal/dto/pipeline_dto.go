package dto

import (
	"time"
)

type PipelineOutput struct {
	ID          int                   `json:"id"`
	Name        string                `json:"name"`
	Description string                `json:"description,omitempty"`
	UserID      int                   `json:"user_id"`
	CreatedAt   time.Time             `json:"created_at"`
	UpdatedAt   time.Time             `json:"updated_at"`
	DeletedAt   string                `json:"deleted_at,omitempty"`
	Version int             `json:"version"`
	User    UserShortOutput `json:"user"`
	Stages  []StageOutput   `json:"stages,omitempty"`
}

type CreatePipelineInput struct {
	Name        string `json:"name"`
	Description string `json:"description,omitempty"`
}

type UpdatePipelineInput struct {
	ID          int      `json:"id"`
	Name        *string  `json:"name,omitempty"`
	Description *string  `json:"description,omitempty"`
	Modified    []string `json:"modified,omitempty"`
}

type ListPipelineInput struct {
	PageSize int            `json:"page_size,omitempty"`
	Page    int            `json:"page,omitempty"`
	Filter  PipelineFilter `json:"filter,omitempty"`
	OrderBy string         `json:"order_by,omitempty"`
	Search   string         `json:"search,omitempty"`
}

type PipelineFilter struct {
	IDs    []int  `json:"ids,omitempty"`
	Name   string `json:"name,omitempty"`
	UserID int    `json:"user_id,omitempty"`
}

type ListPipelineOutput struct {
	Pipelines []PipelineOutput `json:"pipelines"`
	Total     int              `json:"total"`
	TotalPage int              `json:"total_page"`
}
