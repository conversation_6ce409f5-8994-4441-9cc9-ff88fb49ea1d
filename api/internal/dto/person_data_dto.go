package dto

type GetPersonDataInput struct {
	PersonID int     `json:"person_id"`
	Kind     *string `json:"kind,omitempty"`
	Key      *string `json:"key,omitempty"`
}

type SetPersonDataInput struct {
	PersonID int                    `json:"person_id"`
	Kind     string                 `json:"kind"`
	Key      string                 `json:"key"`
	Value    string                 `json:"value"`
	Data     map[string]interface{} `json:"data,omitempty"`
}

type ClearPersonDataInput struct {
	PersonID int     `json:"person_id"`
	Kind     *string `json:"kind,omitempty"`
	Key      *string `json:"key,omitempty"`
}

type PersonDataOutput struct {
	Data map[string]interface{} `json:"data"`
}

type CommonResponse struct {
	Count int `json:"count"`
}
