package dto

import "time"

type CreateAppointmentInput struct {
	Title          string    `json:"title"`
	StartTime      time.Time `json:"start_time"`
	EndTime        time.Time `json:"end_time"`
	DoctorID       *int      `json:"doctor_id,omitempty"`
	PersonID       int       `json:"person_id"`
	DealID         *int      `json:"deal_id,omitempty"`
	Notes          *string   `json:"notes,omitempty"`
	ExtraNotes     *string   `json:"extra_notes,omitempty"`
	ReminderStatus *int      `json:"reminder_status,omitempty"`
	Status         *int      `json:"status,omitempty"`
	Type           *int      `json:"type,omitempty"`
}

type UpdateAppointmentInput struct {
	ID             int        `json:"id"`
	Title          *string    `json:"title,omitempty"`
	StartTime      *time.Time `json:"start_time,omitempty"`
	EndTime        *time.Time `json:"end_time,omitempty"`
	DoctorID       *int       `json:"doctor_id,omitempty"`
	PersonID       *int       `json:"person_id,omitempty"`
	DealID         *int       `json:"deal_id,omitempty"`
	Notes          *string    `json:"notes,omitempty"`
	ExtraNotes     *string    `json:"extra_notes,omitempty"`
	ReminderStatus *int       `json:"reminder_status,omitempty"`
	Status         *int       `json:"status,omitempty"`
	Type           *int       `json:"type,omitempty"`
	Modified       []string   `json:"modified,omitempty"`
}

type AppointmentOutput struct {
	ID             int              `json:"id"`
	Title          string           `json:"title"`
	StartTime      time.Time        `json:"start_time"`
	EndTime        time.Time        `json:"end_time"`
	ArrivedAt      *time.Time       `json:"arrived_at"`
	DoctorID       *int             `json:"doctor_id"`
	PersonID       int              `json:"person_id"`
	DealID         *int             `json:"deal_id"`
	Notes          *string          `json:"notes"`
	ExtraNotes     *string          `json:"extra_notes"`
	History        []HistoryEntry   `json:"history"`
	ReminderStatus int              `json:"reminder_status"`
	Status         int              `json:"status"`
	Type           int              `json:"type"`
	UserID         int              `json:"user_id"`
	TrackID        *int             `json:"track_id"`
	CreatedAt      time.Time        `json:"created_at"`
	UpdatedAt      time.Time        `json:"updated_at"`
	DeletedAt      *time.Time       `json:"deleted_at"`
	CreatorId      int              `json:"creator_id"`
	Version        int              `json:"version"`
	Doctor         *UserShortOutput `json:"doctor"`
	Person         PersonOutput     `json:"person"`
	Deal           *DealOutput      `json:"deal"`
	User           UserShortOutput  `json:"user"`
	Track          *TrackOutput     `json:"track"`
}

type ListAppointmentInput struct {
	PageSize  int                `json:"page_size,omitempty"`
	Page      int                `json:"page,omitempty"`
	Filter    *AppointmentFilter `json:"filter,omitempty"`
	FromDate  *time.Time         `json:"from_date,omitempty"`
	ToDate    *time.Time         `json:"to_date,omitempty"`
	HasDoctor string             `json:"has_doctor,omitempty,options=yes|no|both"`
	OrderBy   string             `json:"order_by,omitempty"`
}

type AppointmentFilter struct {
	PersonID *int `json:"person_id,omitempty"`
	DoctorID *int `json:"doctor_id,omitempty"`
	Status   *int `json:"status,omitempty"`
	Type     *int `json:"type,omitempty"`
}

type ListAppointmentOutput struct {
	Appointments []AppointmentOutput `json:"appointments"`
	Total        int                 `json:"total"`
	TotalPage    int                 `json:"total_page"`
}
