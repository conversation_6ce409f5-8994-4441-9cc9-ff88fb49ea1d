package dto

import "time"

type CreateDiscountUsageInput struct {
	DiscountID   int     `json:"discount_id"`
	UserID       *int    `json:"user_id,omitempty"`
	PersonID     *int    `json:"person_id,omitempty"`
	AttachmentID *int    `json:"attachment_id,omitempty"`
	DealID       *int    `json:"deal_id,omitempty"`
	UsageCount   int     `json:"usage_count,omitempty"`
	Value        float64 `json:"value"`
}

type UpdateDiscountUsageInput struct {
	ID           int      `json:"id"`
	DiscountID   *int     `json:"discount_id,omitempty"`
	UserID       *int     `json:"user_id,omitempty"`
	PersonID     *int     `json:"person_id,omitempty"`
	AttachmentID *int     `json:"attachment_id,omitempty"`
	DealID       *int     `json:"deal_id,omitempty"`
	Value        *float64 `json:"value,omitempty"`
	UsageCount   *int     `json:"usage_count,omitempty"`
	Status       *int8    `json:"status,omitempty"`
	Modified     []string `json:"modified,omitempty"`
}

type ListDiscountUsageInput struct {
	PageSize int                 `json:"page_size,omitempty"`
	Page     int                 `json:"page,omitempty"`
	Filter   DiscountUsageFilter `json:"filter,omitempty"`
	OrderBy  string              `json:"order_by,omitempty"`
}

type DiscountUsageFilter struct {
	Search       *string `json:"search,omitempty"`
	DiscountID   *int    `json:"discount_id,omitempty"`
	UserID       *int    `json:"user_id,omitempty"`
	PersonID     *int    `json:"person_id,omitempty"`
	AttachmentID *int    `json:"attachment_id,omitempty"`
	DealID       *int    `json:"deal_id,omitempty"`
	Status       *int8   `json:"status,omitempty"`
}

type DiscountUsageOutput struct {
	ID           int             `json:"id"`
	DiscountID   int             `json:"discount_id"`
	UserID       *int            `json:"user_id,omitempty"`
	PersonID     *int            `json:"person_id,omitempty"`
	AttachmentID *int            `json:"attachment_id,omitempty"`
	DealID       *int            `json:"deal_id,omitempty"`
	UsageCount   int             `json:"usage_count"`
	Value        float64         `json:"value"`
	Status       int8            `json:"status"`
	Version      int             `json:"version"`
	CreatedAt    time.Time       `json:"created_at"`
	UpdatedAt    time.Time       `json:"updated_at"`
	DeletedAt    *time.Time      `json:"deleted_at,omitempty"`
	Discount     *DiscountOutput `json:"discount,omitempty"`
}

type ListDiscountUsageOutput struct {
	DiscountUsages []DiscountUsageOutput `json:"discount_usages"`
	Total          int                   `json:"total"`
	TotalPage      int                   `json:"total_page"`
}
