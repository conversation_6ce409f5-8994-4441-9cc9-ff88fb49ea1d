package dto

type CreateAttachmentDataInput struct {
	AttachmentID  int                    `json:"attachment_id"`
	Kind          string                 `json:"kind"`
	Data          map[string]interface{} `json:"data"`
	ParticipantID int                    `json:"participant_id"`
	UserID        int                    `json:"user_id"`
}

type UpdateAttachmentDataInput struct {
	ID            int                    `json:"id"`
	AttachmentID  int                    `json:"attachment_id"`
	Kind          string                 `json:"kind"`
	Data          map[string]interface{} `json:"data"`
	ParticipantID int                    `json:"participant_id"`
	Modified      []string               `json:"modified"`
}

type ListAttachmentDataInput struct {
	PageSize int                  `json:"page_size,omitempty"`
	Page    int                  `json:"page,omitempty"`
	Filter  AttachmentDataFilter `json:"filter,omitempty"`
	OrderBy string               `json:"order_by,omitempty"`
}

type AttachmentDataFilter struct {
	AttachmentID  int    `json:"attachment_id,omitempty"`
	Kind          string `json:"kind,omitempty"`
	ParticipantID int    `json:"participant_id,omitempty"`
}

type ListAttachmentDataOutput struct {
	AttachmentData []AttachmentDataOutput `json:"attachment_data"`
	Total          int                    `json:"total"`
	TotalPage      int                    `json:"total_page"`
}

type AttachmentDataOutput struct {
	Kind          string                 `json:"kind"`
	Data          map[string]interface{} `json:"data"`
	ParticipantID int                    `json:"participant_id,omitempty"`
}
