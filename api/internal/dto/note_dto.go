package dto

import (
	"time"
)

type NoteOutput struct {
	ID        int            `json:"id"`
	Body      string         `json:"body"`
	Type      int            `json:"type"`
	PersonID  *int           `json:"person_id,omitempty"`
	DealID    *int           `json:"deal_id,omitempty"`
	UserID    int            `json:"user_id"`
	Status    int8           `json:"status"`
	Version   int            `json:"version"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt string         `json:"deleted_at,omitempty"`
	History   []HistoryEntry `json:"history,omitempty"`

	Creator *UserShortOutput `json:"creator,omitempty"`
	Person  *PersonOutput    `json:"person,omitempty"`
}

type CreateNoteInput struct {
	Body     string `json:"body"`
	Type     int    `json:"type,omitempty"`
	PersonID *int   `json:"person_id,omitempty"`
	DealID   *int   `json:"deal_id,omitempty"`
}

type UpdateNoteInput struct {
	ID       int      `json:"id"`
	Body     *string  `json:"body,omitempty"`
	Type     *int     `json:"type,omitempty"`
	PersonID *int     `json:"person_id,omitempty"`
	DealID   *int     `json:"deal_id,omitempty"`
	Status   *int8    `json:"status,omitempty"`
	Modified []string `json:"modified,omitempty"`
}

type ListNotesInput struct {
	Filter       NoteInputFilter `json:"filter,omitempty"`
	Page         int             `json:"page,omitempty"`
	PageSize     int             `json:"page_size,omitempty"`
	OrderBy      string          `json:"order_by,omitempty"`
	Search       string          `json:"search,omitempty"`
	DepartmentID *int            `json:"department_id,omitempty"`
}

type NoteInputFilter struct {
	IDs      []int  `json:"ids,omitempty"`
	Body     string `json:"body,omitempty"`
	Type     *int   `json:"type,omitempty"`
	PersonID *int   `json:"person_id,omitempty"`
	DealID   *int   `json:"deal_id,omitempty"`
	Status   *int8  `json:"status,omitempty"`
	UserID   *int   `json:"user_id,omitempty"`
}

type ListNotesOutput struct {
	Notes     []*NoteOutput `json:"notes"`
	Total     int           `json:"total"`
	TotalPage int           `json:"total_page"`
}
