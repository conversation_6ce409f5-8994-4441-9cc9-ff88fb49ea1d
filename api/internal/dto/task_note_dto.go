package dto

import "time"

type TaskNoteOutput struct {
	ID        int             `json:"id"`
	TaskID    int             `json:"task_id"`
	Body      string          `json:"body"`
	UserID    int             `json:"user_id"`
	CreatedAt time.Time       `json:"created_at"`
	UpdatedAt time.Time       `json:"updated_at"`
	DeletedAt *time.Time      `json:"deleted_at,omitempty"`
	Version   int             `json:"version"`
	User      UserShortOutput `json:"creator"`
}

type CreateTaskNoteInput struct {
	TaskID int    `json:"task_id"`
	Body   string `json:"body"`
}

type UpdateTaskNoteInput struct {
	ID       int      `json:"id"`
	TaskID   int      `json:"task_id"`
	Body     string   `json:"body"`
	Modified []string `json:"modified,omitempty"`
}

type ListTaskNoteInput struct {
	PageSize int            `json:"page_size,omitempty"`
	Page     int            `json:"page,omitempty"`
	Filter   TaskNoteFilter `json:"filter,omitempty"`
	OrderBy  string         `json:"order_by,omitempty"`
}

type TaskNoteFilter struct {
	TaskID int `json:"task_id,omitempty"`
	UserID int `json:"user_id,omitempty"`
}

type ListTaskNoteOutput struct {
	TaskNotes []TaskNoteOutput `json:"task_notes"`
	Total     int              `json:"total"`
	TotalPage int              `json:"total_page"`
}
