package dto

import "time"

type FieldChange struct {
	Field    string      `json:"field"`
	OldValue interface{} `json:"old_value"`
	NewValue interface{} `json:"new_value"`
}

type HistoryEntry struct {
	Timestamp time.Time     `json:"timestamp"`
	UserID    int           `json:"user_id,omitempty"`
	Changes   []FieldChange `json:"changes"`
}
type HistoryRecord struct {
	ID          int                    `json:"id"`
	EntityType  string                 `json:"entity_type"`
	EntityID    int                    `json:"entity_id"`
	Operation   string                 `json:"operation"`
	ChangedAt   string                 `json:"changed_at"`
	UserID      *int                   `json:"user_id,omitempty"`
	Before      map[string]interface{} `json:"before,omitempty"`
	After       map[string]interface{} `json:"after,omitempty"`
	RelatedID   *int                   `json:"related_id,omitempty"`
	Related     *string                `json:"related,omitempty"`
	CreatedAt   string                 `json:"created_at"`
	UpdatedAt   string                 `json:"updated_at"`
	SourceTable string                 `json:"source_table"`
}

type HistoryListInput struct {
	PageSize int          `json:"page_size,omitempty"`
	Page     int          `json:"page,omitempty"`
	Filter   HistoryFilter `json:"filter,omitempty"`
	OrderBy  string        `json:"order_by,omitempty"`
}

type HistoryFilter struct {
	EntityType string `json:"entity_type,omitempty"`
	EntityID   *int   `json:"entity_id,omitempty"`
	UserID     *int   `json:"user_id,omitempty"`
}

type HistoryListOutput struct {
	Histories []*HistoryRecord `json:"histories"`
	Total     int              `json:"total"`
	TotalPage int              `json:"total_page"`
}
