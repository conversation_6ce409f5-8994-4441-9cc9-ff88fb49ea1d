package dto

type LocalProvince struct {
	ID   int    `json:"id"`
	Name string `json:"name"`
	Code string `json:"code"`
}

type LocalDistrict struct {
	ID         int    `json:"id"`
	Name       string `json:"name"`
	Prefix     string `json:"prefix"`
	ProvinceID int    `json:"province_id"`
}

type LocalWard struct {
	ID         int    `json:"id"`
	Name       string `json:"name"`
	Prefix     string `json:"prefix"`
	ProvinceID int    `json:"province_id"`
	DistrictID int    `json:"district_id"`
}

type LocationResponse struct {
	Province []LocalProvince `json:"provinces"`
	District []LocalDistrict `json:"districts"`
	Ward     []LocalWard     `json:"wards"`
}
