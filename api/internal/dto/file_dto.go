package dto

import "time"

type FileOutput struct {
	ID        int                    `json:"id"`
	Name      string                 `json:"name"`
	Kind      string                 `json:"kind"`
	Type      string                 `json:"type"`
	Size      int                    `json:"size"`
	Path      string                 `json:"path"`
	UserID    int                    `json:"user_id"`
	Storage   string                 `json:"storage"`
	Meta      map[string]interface{} `json:"meta"`
	Status    int                    `json:"status"`
	Version   int                    `json:"version"`
	CreatedAt time.Time              `json:"created_at"`
	UpdatedAt time.Time              `json:"updated_at"`
	DeletedAt string                 `json:"deleted_at,omitempty"`
}

type CreateFileInput struct {
	Name    string                 `json:"name"`
	Kind    string                 `json:"kind"`
	Type    string                 `json:"type"`
	Size    int                    `json:"size"`
	Path    string                 `json:"path"`
	UserID  int                    `json:"user_id"`
	Storage string                 `json:"storage"`
	Meta    map[string]interface{} `json:"meta,omitempty"`
}

type UpdateFileInput struct {
	ID       int                    `json:"id"`
	Name     string                 `json:"name,omitempty"`
	Kind     string                 `json:"kind,omitempty"`
	Type     string                 `json:"type,omitempty"`
	Size     int                    `json:"size,omitempty"`
	Path     string                 `json:"path,omitempty"`
	UserID   int                    `json:"user_id,omitempty"`
	Storage  string                 `json:"storage,omitempty"`
	Meta     map[string]interface{} `json:"meta,omitempty"`
	Status   *int8                  `json:"status,omitempty"`
	Modified []string               `json:"modified,omitempty"`
}

type GetFileInput struct {
	ID int `json:"id"`
}

type ListFilesInput struct {
	Filter   *FileFilter `json:"filter,omitempty"`
	Page     int         `json:"page,omitempty"`
	PageSize int         `json:"page_size,omitempty"`
	OrderBy  string      `json:"order_by,omitempty"`
	Search   string      `json:"search,omitempty"`
}

type FileFilter struct {
	Name    string `json:"name,omitempty"`
	Kind    string `json:"kind,omitempty"`
	Type    string `json:"type,omitempty"`
	UserID  int    `json:"user_id,omitempty"`
	Storage string `json:"storage,omitempty"`
}

type ListFilesOutput struct {
	Files []*FileOutput
	Total int
}
