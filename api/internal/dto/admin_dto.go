package dto

// LogsInput represents the request for retrieving logs
type LogsInput struct {
	Type     string `json:"type"`
	PageSize int    `json:"page_size"`
	Page     int    `json:"page"`
}

// LogsOutput represents the response for logs retrieval
type LogsOutput struct {
	Logs      string `json:"logs"`
	Total     int    `json:"total"`
	TotalPage int    `json:"total_page"`
}

// MonitorInput represents the request for monitoring
type MonitorInput struct {
	// Add monitor request parameters if needed
}

// MonitorOutput represents the response for monitoring
type MonitorOutput struct {
	// Add monitor response fields if needed
}
