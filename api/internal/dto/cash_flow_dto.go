package dto

import "time"

type CashFlowOutput struct {
	ID            int        `json:"id"`
	Type          string     `json:"type"`
	Amount        int        `json:"amount"`
	Cash          int        `json:"cash"`
	CreditCard    int        `json:"credit_card"`
	Mpos          int        `json:"mpos"`
	Bank          int        `json:"bank"`
	Momo          int        `json:"momo"`
	Description   string     `json:"description"`
	CreatorID     int        `json:"creator_id"`
	CounterpartID int        `json:"counterpart_id"`
	RecipientID   *int       `json:"recipient_id,omitempty"`
	PayerID       *int       `json:"payer_id,omitempty"`
	State         string     `json:"state"`
	ApproverID    *int       `json:"approver_id,omitempty"`
	PaidAt        *time.Time `json:"paid_at,omitempty"`
	Version       int        `json:"version"`
	CreatedAt     time.Time  `json:"created_at"`
	UpdatedAt     time.Time  `json:"updated_at"`
	DeletedAt     *time.Time `json:"deleted_at,omitempty"`
}

type CashFlowWithRelationsOutput struct {
	CashFlowOutput
	Items       []CashFlowItemWithRelationsOutput `json:"items,omitempty"`
	Notes       []CashFlowNoteOutput              `json:"notes,omitempty"`
	Creator     *UserShortOutput                  `json:"creator,omitempty"`
	Approver    *UserShortOutput                  `json:"approver,omitempty"`
	Counterpart *UserShortOutput                  `json:"counterpart,omitempty"`
	Recipient   *UserShortOutput                  `json:"recipient,omitempty"`
	Payer       *UserShortOutput                  `json:"payer,omitempty"`
}

type CashFlowItemForAdd struct {
	CategoryID   int    `json:"category_id"`
	Amount       int    `json:"amount"`
	Note         string `json:"note"`
	Order        int    `json:"order"`
	HasVat       bool   `json:"has_vat"`
	DepartmentID int    `json:"department_id"`
}

type CreateCashFlowInput struct {
	Type          string               `json:"type"`
	Items         []CashFlowItemForAdd `json:"items"`
	Cash          int                  `json:"cash"`
	CreditCard    int                  `json:"credit_card"`
	Mpos          int                  `json:"mpos"`
	Bank          int                  `json:"bank"`
	Momo          int                  `json:"momo"`
	Amount        int                  `json:"amount"`
	Description   string               `json:"description"`
	CounterpartID int                  `json:"counterpart_id"`
	RecipientID   *int                 `json:"recipient_id,omitempty"`
}

type UpdateCashFlowInput struct {
	ID            int                  `json:"id"`
	Items         []CashFlowItemForAdd `json:"items,omitempty"`
	Cash          *int                 `json:"cash"`
	CreditCard    *int                 `json:"credit_card"`
	Mpos          *int                 `json:"mpos"`
	Bank          *int                 `json:"bank"`
	Momo          *int                 `json:"momo"`
	Amount        *int                 `json:"amount"`
	Description   *string              `json:"description,omitempty"`
	CounterpartID *int                 `json:"counterpart_id,omitempty"`
	RecipientID   *int                 `json:"recipient_id,omitempty"`
	Modified      []string             `json:"modified,omitempty"`
}

type UpdateCashFlowStateInput struct {
	ID      int     `json:"id"`
	State   string  `json:"state"`
	Comment *string `json:"comment,omitempty"`
}

type GetCashFlowInput struct {
	ID              int  `json:"id"`
	IncludeRelation bool `json:"include_relation,omitempty"`
}

type DeleteCashFlowInput struct {
	ID int `json:"id"`
}

type ListCashFlowsInput struct {
	PageSize int                 `json:"page_size,omitempty"`
	Page     int                 `json:"page,omitempty"`
	Filter   CashFlowFilterInput `json:"filter,omitempty"`
	Search   string              `json:"search,omitempty"`
	OrderBy  string              `json:"order_by,omitempty"`
}

type CashFlowFilterInput struct {
	Type          *string    `json:"type,omitempty"`
	State         *string    `json:"state,omitempty"`
	CreatorID     *int       `json:"creator_id,omitempty"`
	CounterpartID *int       `json:"counterpart_id,omitempty"`
	CategoryID    *int       `json:"category_id,omitempty"`
	ApproverID    *int       `json:"approver_id,omitempty"`
	RecipientID   *int       `json:"recipient_id,omitempty"`
	PayerID       *int       `json:"payer_id,omitempty"`
	FromDate      *time.Time `json:"from_date,omitempty"`
	ToDate        *time.Time `json:"to_date,omitempty"`
}

type ListCashFlowsOutput struct {
	CashFlows []*CashFlowWithRelationsOutput `json:"cash_flows"`
	Total     int                            `json:"total"`
	TotalPage int                            `json:"total_page"`
}

type CashFlowReportInput struct {
	FromDate time.Time `json:"from_date,omitempty"`
	ToDate   time.Time `json:"to_date,omitempty"`
}

type CategorySummaryOutput struct {
	CategoryID   int    `json:"category_id"`
	CategoryName string `json:"category_name"`
	Amount       int    `json:"amount"`
	Count        int    `json:"count"`
}

// New DTO for department-based summary
type DepartmentCategorySummaryOutput struct {
	CategoryID   int    `json:"category_id"`
	CategoryName string `json:"category_name"`
	Amount       int    `json:"amount"`
	Count        int    `json:"count"`
}

type DepartmentSummaryOutput struct {
	DepartmentID   int                                `json:"department_id"`
	DepartmentName string                             `json:"department_name"`
	TotalAmount    int                                `json:"total_amount"`
	TotalCount     int                                `json:"total_count"`
	Categories     []*DepartmentCategorySummaryOutput `json:"categories"`
}

// New DTO for category-first grouping (category → department)
type CategoryDepartmentSummaryOutput struct {
	DepartmentID   int    `json:"department_id"`
	DepartmentName string `json:"department_name"`
	Amount         int    `json:"amount"`
	Count          int    `json:"count"`
}

type CategorySummaryDepartmentOutput struct {
	CategoryID   int                                `json:"category_id"`
	CategoryName string                             `json:"category_name"`
	TotalAmount  int                                `json:"total_amount"`
	TotalCount   int                                `json:"total_count"`
	Departments  []*CategoryDepartmentSummaryOutput `json:"departments"`
}

type CashFlowSummaryReportOutput struct {
	TotalIncome           int `json:"total_income"`
	TotalExpense          int `json:"total_expense"`
	NetAmount             int `json:"net_amount"`
	PendingApprovalCount  int `json:"pending_approval_count"`
	PendingApprovalAmount int `json:"pending_approval_amount"`
	// Payment methods totals (paid transactions)
	TotalCashAll       int `json:"total_cash_all"`
	TotalCreditCardAll int `json:"total_credit_card_all"`
	TotalMposAll       int `json:"total_mpos_all"`
	TotalBankAll       int `json:"total_bank_all"`
	TotalMomoAll       int `json:"total_momo_all"`
	// Payment methods for pending transactions
	PendingCash       int                      `json:"pending_cash"`
	PendingCreditCard int                      `json:"pending_credit_card"`
	PendingMpos       int                      `json:"pending_mpos"`
	PendingBank       int                      `json:"pending_bank"`
	PendingMomo       int                      `json:"pending_momo"`
	IncomeByCategory  []*CategorySummaryOutput `json:"income_by_category"`
	ExpenseByCategory []*CategorySummaryOutput `json:"expense_by_category"`
}

type CashFlowReportOutput struct {
	CashFlowSummaryReportOutput
	Period      string    `json:"period"`
	GeneratedAt time.Time `json:"generated_at"`
}

// New types for period-based reporting
type CashFlowPeriodInput struct {
	PeriodType  string    `json:"period_type"`
	Date        time.Time `json:"date"`
	PeriodCount int       `json:"period_count"`
}

type PeriodSummaryOutput struct {
	Period           string    `json:"period"`
	PeriodLabel      string    `json:"period_label"`
	StartDate        time.Time `json:"start_date"`
	EndDate          time.Time `json:"end_date"`
	TotalIncome      int       `json:"total_income"`
	TotalExpense     int       `json:"total_expense"`
	NetAmount        int       `json:"net_amount"`
	TransactionCount int       `json:"transaction_count"`
	// Payment methods totals
	TotalCash       int `json:"total_cash"`
	TotalCreditCard int `json:"total_credit_card"`
	TotalMpos       int `json:"total_mpos"`
	TotalBank       int `json:"total_bank"`
	TotalMomo       int `json:"total_momo"`
	// Department-first grouping (department → category)
	IncomeByDepartment  []*DepartmentSummaryOutput `json:"income_by_department"`
	ExpenseByDepartment []*DepartmentSummaryOutput `json:"expense_by_department"`
	// Category-first grouping (category → department)
	IncomeByCategory  []*CategorySummaryDepartmentOutput `json:"income_by_category"`
	ExpenseByCategory []*CategorySummaryDepartmentOutput `json:"expense_by_category"`
}

type CashFlowPeriodOutput struct {
	CurrentPeriod   PeriodSummaryOutput   `json:"current_period"`
	PreviousPeriods []PeriodSummaryOutput `json:"previous_periods"`
	PeriodType      string                `json:"period_type"`
	GeneratedAt     time.Time             `json:"generated_at"`
}

type Period struct {
	Period      string
	PeriodLabel string
	StartDate   time.Time
	EndDate     time.Time
}
