package dto

type CreateTaskRecurringInput struct {
	CronExpression string `json:"cron_expression"`
	NextOccurrence string `json:"next_occurrence,omitempty"`
	LastOccurrence string `json:"last_occurrence,omitempty"`
}

type UpdateTaskRecurringInput struct {
	CronExpression *string `json:"cron_expression,omitempty"`
	NextOccurrence string  `json:"next_occurrence,omitempty"`
	LastOccurrence string  `json:"last_occurrence,omitempty"`
}
