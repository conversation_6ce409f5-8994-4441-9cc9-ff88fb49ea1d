package dto

import (
	"time"
)

type TaskAssignmentOutput struct {
	ID          int             `json:"id"`
	TaskID      int             `json:"task_id"`
	UserID      int             `json:"user_id"`
	Role        string          `json:"role"`
	CreatedAt   time.Time       `json:"created_at"`
	UpdatedAt   time.Time       `json:"updated_at"`
	DueAt       time.Time       `json:"due_at"`
	CompletedAt time.Time       `json:"completed_at"`
	DeletedAt   *time.Time      `json:"deleted_at,omitempty"`
	Version     int             `json:"version"`
	User        UserShortOutput `json:"user"`
}

type CreateTaskAssignmentInput struct {
	TaskID int    `json:"task_id"`
	UserID int    `json:"user_id"`
	Role   string `json:"role"`
}

type UpdateTaskAssignmentInput struct {
	ID       int      `json:"id"`
	TaskID   int      `json:"task_id"`
	UserID   int      `json:"user_id"`
	Role     string   `json:"role"`
	Modified []string `json:"modified,omitempty"`
}

type ListTaskAssignmentInput struct {
	PageSize int                  `json:"page_size,omitempty"`
	Page     int                  `json:"page,omitempty"`
	Filter   TaskAssignmentFilter `json:"filter,omitempty"`
	OrderBy  string               `json:"order_by,omitempty"`
}

type TaskAssignmentFilter struct {
	TaskID int    `json:"task_id,omitempty"`
	UserID int    `json:"user_id,omitempty"`
	Role   string `json:"role,omitempty"`
}

type ListTaskAssignmentOutput struct {
	TaskAssignments []TaskAssignmentOutput `json:"task_assignments"`
	Total           int                    `json:"total"`
	TotalPage       int                    `json:"total_page"`
}

type DeleteTaskAssignmentInput struct {
	ID int `json:"id"`
}

type AssignTasksInput struct {
	IDList []int                       `json:"id_list"`
	Users  []CreateTaskAssignmentInput `json:"users"`
}

type AssignTasksOutput struct {
	SuccessCount int               `json:"success_count"`
	FailCount    int               `json:"fail_count"`
	Errors       []TaskUpdateError `json:"errors"`
}
