package dto

type CreateBillInput struct {
	DealID        int                      `json:"deal_id"`
	PersonID      int                      `json:"person_id"`
	BrandID       int                      `json:"brand_id"`
	State         string                   `json:"state"`
	Discount      float64                  `json:"discount"`
	Refund        float64                  `json:"refund"`
	DebtRemaining float64                  `json:"debt_remaining"`
	DebtPayment   float64                  `json:"debt_payment"`
	BillItems []CreateBillItemInput `json:"bill_items"`
	Payment   CreatePaymentInput    `json:"payment"`
}

type UpdateBillInput struct {
	ID            int      `json:"id"`
	DealID        int      `json:"deal_id"`
	Status        int      `json:"status"`
	State         string   `json:"state"`
	BrandID       int      `json:"brand_id"`
	DebtRemaining float64  `json:"debt_remaining"`
	DebtPayment   float64  `json:"debt_payment"`
	Modified      []string `json:"modified"`
}

type BillOutput struct {
	ID               int                     `json:"id"`
	DealID           int                     `json:"deal_id"`
	PersonID         int                     `json:"person_id"`
	UserID           int                     `json:"user_id"`
	BrandID          int                     `json:"brand_id"`
	Status           int                     `json:"status"`
	State            string                  `json:"state"`
	Discount         float64                 `json:"discount"`
	Refund           float64                 `json:"refund"`
	DebtRemaining    float64                 `json:"debt_remaining"`
	DebtPayment      float64                 `json:"debt_payment"`
	CreatedAt        string                  `json:"created_at"`
	UpdatedAt        string                  `json:"updated_at"`
	DeletedAt        string                  `json:"deleted_at"`
	Version   int              `json:"version"`
	Track     TrackOutput      `json:"track"`
	BillItems []BillItemOutput `json:"bill_items"`
	Payments         []PaymentOutput  `json:"payments"`
	Deal             DealOutput       `json:"deal"`
	TotalValue       float64                       `json:"total_value"`
	DoctorName       string                        `json:"doctor_name"`
	Data             []BillDataOutput        `json:"data"`
	InstallmentPlans []InstallmentPlanOutput `json:"installment_plans"`
}

type ListBillInput struct {
	PageSize int        `json:"page_size,omitempty"`
	Page     int        `json:"page,omitempty"`
	Filter   BillFilter `json:"filter,omitempty"`
	PersonID int        `json:"person_id,omitempty"`
	OrderBy  string     `json:"order_by,omitempty"`
}

type BillFilter struct {
	DealID    int    `json:"deal_id,omitempty"`
	PersonID  int    `json:"person_id,omitempty"`
	BrandID   int    `json:"brand_id,omitempty"`
	Status    int    `json:"status,omitempty"`
	State     string `json:"state,omitempty"`
	StartDate string `json:"start_date,omitempty"`
	EndDate   string `json:"end_date,omitempty"`
}

type ListBillOutput struct {
	Bills     []BillOutput `json:"bills"`
	Total     int          `json:"total"`
	TotalPage int          `json:"total_page"`
}
