package dto

import "time"

type UserOutput struct {
	ID                 int               `json:"id"`
	Username           string            `json:"username"`
	Phone              string            `json:"phone,omitempty"`
	Email              string            `json:"email,omitempty"`
	EmailConfirmed     bool              `json:"email_confirmed"`
	Name               string            `json:"name,omitempty"`
	Gender             string            `json:"gender"`
	DepartmentID       int               `json:"department_id,omitempty"`
	DepartmentPosition string            `json:"department_position,omitempty"`
	ProfileImage       string            `json:"profile_image,omitempty"`
	State              string            `json:"state"`
	Status             int               `json:"status"`
	Version            int               `json:"version"`
	SuspendedAt        string            `json:"suspended_at,omitempty"`
	CreatedAt          time.Time         `json:"created_at"`
	UpdatedAt          time.Time         `json:"updated_at"`
	DeletedAt          string            `json:"deleted_at,omitempty"`
	Data               []*UserDataOutput `json:"data,omitempty"`
	Roles              []string          `json:"roles"`
}

type CreateUserInput struct {
	Username           string   `json:"username"`
	Password           string   `json:"password"`
	Name               string   `json:"name,omitempty"`
	Email              string   `json:"email,omitempty"`
	Gender             string   `json:"gender"`
	Phone              string   `json:"phone,omitempty"`
	State              string   `json:"state,omitempty"`
	DepartmentID       int      `json:"department_id,omitempty"`
	DepartmentPosition string   `json:"department_position,omitempty"`
	ProfileImage       string   `json:"profile_image,omitempty"`
	Roles              []string `json:"roles"`
}

type UpdateUserInput struct {
	ID                 int      `json:"id"`
	Username           *string  `json:"username,omitempty"`
	Password           *string  `json:"password,omitempty"`
	Name               *string  `json:"name,omitempty"`
	Email              *string  `json:"email,omitempty"`
	Gender             *string  `json:"gender,omitempty"`
	Phone              *string  `json:"phone,omitempty"`
	DepartmentID       *int     `json:"department_id,omitempty"`
	DepartmentPosition *string  `json:"department_position,omitempty"`
	ProfileImage       *string  `json:"profile_image,omitempty"`
	Status             *int     `json:"status,omitempty"`
	State              *string  `json:"state,omitempty"`
	Roles              []string `json:"roles,omitempty"`
	Modified           []string `json:"modified,omitempty"`
}

type ListUsersInput struct {
	Filter UserInputFilter `json:"filter,omitempty"`
	Page   int             `json:"page,omitempty"`
	PageSize int             `json:"page_size,omitempty"`
	OrderBy  string          `json:"order_by,omitempty"`
	Search   string          `json:"search,omitempty"`
	Role     string          `json:"role,omitempty"`
}

type UserInputFilter struct {
	Ids          []int  `json:"ids,omitempty"`
	Username     string `json:"username,omitempty"`
	Name         string `json:"name,omitempty"`
	Email        string `json:"email,omitempty"`
	Gender       string `json:"gender,omitempty"`
	Phone        string `json:"phone,omitempty"`
	Status       int    `json:"status,omitempty"`
	State        string `json:"state,omitempty"`
	DepartmentID int    `json:"department_id,omitempty"`
}

type ListUsersOutput struct {
	Users []*UserOutput `json:"users"`
	Total int           `json:"total"`
}

type UserShortOutput struct {
	Id           int    `json:"id"`
	Username     string `json:"username,omitempty"`
	Name         string `json:"name,omitempty"`
	ProfileImage string `json:"profile_image,omitempty"`
	DepartmentId int    `json:"department_id,omitempty"`
}
