package dto

import "time"

type UserDataDetailOutput struct {
	ID        int                    `json:"id"`
	UserID    int                    `json:"user_id"`
	Kind      string                 `json:"kind"`
	Data      map[string]interface{} `json:"data"`
	CreatedAt time.Time              `json:"created_at"`
	UpdatedAt time.Time              `json:"updated_at"`
}

type GetUserDataInput struct {
	UserID int     `json:"user_id"`
	Kind   string  `json:"kind"`
	Key    *string `json:"key,omitempty"`
}

type SetUserDataInput struct {
	UserID int                    `json:"user_id"`
	Kind   string                 `json:"kind"`
	Key    string                 `json:"key"`
	Value  string                 `json:"value"`
	Data   map[string]interface{} `json:"data"`
}

type UpdateUserDataInput struct {
	UserID   int                     `json:"user_id"`
	Kind     string                  `json:"kind"`
	Data     *map[string]interface{} `json:"data,omitempty"`
	Modified []string                `json:"modified,omitempty"`
}

type ClearUserDataInput struct {
	UserID int     `json:"user_id"`
	Kind   *string `json:"kind,omitempty"`
	Key    *string `json:"key,omitempty"`
}

type ListUserDataInput struct {
	Filter   *UserDataFilter `json:"filter,omitempty"`
	Page     int             `json:"page,omitempty"`
	PageSize int             `json:"page_size,omitempty"`
	OrderBy  string          `json:"order_by,omitempty"`
}

type UserDataFilter struct {
	UserID *int    `json:"user_id,omitempty"`
	Kind   *string `json:"kind,omitempty"`
}

type ListUserDataOutput struct {
	UserData []*UserDataDetailOutput `json:"user_data"`
	Total    int                     `json:"total"`
}

type UserDataOutput struct {
	Kind string                 `json:"kind"`
	Data map[string]interface{} `json:"data"`
}
