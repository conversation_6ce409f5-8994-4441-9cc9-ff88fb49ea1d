package dto

import "time"

type CreatePersonAssignmentInput struct {
	PersonID int    `json:"person_id"`
	UserID   int    `json:"user_id"`
	Role     string `json:"role"` // doctor, counselor, sale, customer_care
}

type UpdatePersonAssignmentInput struct {
	ID       int      `json:"id"`
	PersonID *int     `json:"person_id,omitempty"`
	UserID   *int     `json:"user_id,omitempty"`
	Role     *string  `json:"role,omitempty"` // doctor, counselor, sale, customer_care
	Modified []string `json:"modified,omitempty"`
}

type AssignPersonsInput struct {
	IDList []int  `json:"id_list"`
	UserID int    `json:"user_id"`
	Role   string `json:"role"` // doctor, counselor, sale, customer_care
}

type DeletePersonAssignmentInput struct {
	ID int `json:"id"`
}

type PersonAssignmentOutput struct {
	ID        int       `json:"id"`
	PersonID  int       `json:"person_id"`
	UserID    int       `json:"user_id"`
	Role      string    `json:"role"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}
