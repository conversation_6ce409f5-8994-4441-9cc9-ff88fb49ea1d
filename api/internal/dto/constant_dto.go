package dto

type ConstantInput struct {
}

type ConstantOutput struct {
	AppointmentStatus         map[int]string    `json:"appointment_status"`
	AppointmentTypes          map[int]string    `json:"appointment_type"`
	AppointmentReminderStatus map[int]string    `json:"appointment_reminder_status"`
	ProductType               map[string]string `json:"product_type"`
	DealStatus                map[int]string    `json:"deal_status"`
	DealUserRoles             map[string]string `json:"deal_user_role"`
	NoteTypes                 map[int]string    `json:"note_type"`
	ProductPositons           map[int]string    `json:"product_position"`
	AttachmentTypes           map[int]string    `json:"attachment_type"`
	TransactionType           map[int]string    `json:"transaction_type"`
	TaskPriorities            map[int]string    `json:"task_priority"`
	TaskTypes                 map[string]string `json:"task_type"`
	TaskStates                map[string]string `json:"task_state"`
	TaskAssignmentState       map[string]string `json:"task_assignment_state"`
	UserDepartmentPosition map[string]string `json:"user_department_position"`
	CallSetting            CallSetting       `json:"call_setting"`
}
