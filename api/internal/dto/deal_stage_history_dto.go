package dto

import (
	"time"
)

type UpdateDealStageHistoryInput struct {
	ID        int       `json:"id"`
	ChangedAt time.Time `json:"changed_at,omitempty"`
}

type ListDealStageHistoryInput struct {
	PageSize int                    `json:"page_size,omitempty"`
	Page     int                    `json:"page,omitempty"`
	Filter   DealStageHistoryFilter `json:"filter,omitempty"`
	OrderBy  string                 `json:"order_by,omitempty"`
	IncludeRelation bool                   `json:"include_relation,omitempty"`
}

type DealStageHistoryFilter struct {
	DealID *int `json:"deal_id,omitempty"`
	Before *int `json:"before,omitempty"`
	After  *int `json:"after,omitempty"`
}

type DealStageHistoryOutput struct {
	ID           int64            `json:"id,omitempty"`
	DealID       int64            `json:"deal_id,omitempty"`
	Before       int64            `json:"before,omitempty"`
	After        int64            `json:"after,omitempty"`
	UserID       int64            `json:"user_id,omitempty"`
	ChangedAt    time.Time        `json:"changed_at,omitempty"`
	BeforeName   string           `json:"before_name,omitempty"`
	AfterName    string                 `json:"after_name,omitempty"`
	Person       *PersonOutput          `json:"person,omitempty"`
	PersonSource string                 `json:"person_source,omitempty"`
	Referrer     *PersonOutput    `json:"referrer,omitempty"`
	SaleUser     *UserShortOutput `json:"sale_user,omitempty"`
	Deal         *DealOutput      `json:"deal,omitempty"`
}

type ListDealStageHistoryOutput struct {
	StageHistories []DealStageHistoryOutput `json:"stage_histories"`
	Total          int                      `json:"total"`
	TotalPage      int                      `json:"total_page"`
}
