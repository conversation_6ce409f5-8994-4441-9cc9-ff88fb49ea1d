package dto

import "time"

type CreateTagDealInput struct {
	DealID     int    `json:"deal_id"`
	TagID      int    `json:"tag_id"`
	AssignedBy string `json:"assigned_by,omitempty"`
}

type UpdateTagDealInput struct {
	ID     int  `json:"id"`
	DealID *int `json:"deal_id,omitempty"`
	TagID  *int `json:"tag_id,omitempty"`
}

type DeleteTagDealInput struct {
	TagID  int `json:"tag_id"`
	DealID int `json:"deal_id"`
}

type TagDealOutput struct {
	ID         int       `json:"id"`
	DeletedAt  string    `json:"deleted_at,omitempty"`
	Status     int       `json:"status"`
	Version    int       `json:"version"`
	CreatedAt  time.Time `json:"created_at"`
	UpdatedAt  time.Time `json:"updated_at"`
	AssignedBy int       `json:"assigned_by,omitempty"`
	RemovedBy  int       `json:"removed_by,omitempty"`
	DealID     int       `json:"deal_id"`
	TagID      int       `json:"tag_id"`
}
