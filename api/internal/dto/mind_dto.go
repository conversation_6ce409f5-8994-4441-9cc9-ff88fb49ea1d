package dto

type CompleteInput struct {
	Prompt string `json:"prompt"`
}

type CompleteOutput struct {
	Result string `json:"result"`
}

type GetCronInput struct {
	Prompt string `json:"prompt"`
}

type GetCronOutput struct {
	CronExpression string `json:"expression"`
}

type ContentModificationInput struct {
	Content string `json:"content"`
	Focus   string `json:"focus"`
}

type ContentModificationOutput struct {
	Options []string `json:"options"`
}

type ContentCommandInput struct {
	Content string `json:"content"`
	Focus   string `json:"focus"`
	Command string `json:"command"`
}

type ContentCommandOutput struct {
	Options []string `json:"options"`
}
