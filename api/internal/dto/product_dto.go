package dto

type CreateProductInput struct {
	Name        string                        `json:"name"`
	Code        string                        `json:"code"`
	Description string                        `json:"description,optional"`
	Price       int                           `json:"price,optional"`
	Type        string                        `json:"type"`
	Status      int                           `json:"status,optional"`
	Quantity    int                           `json:"quantity,optional"`
	SKU         string                        `json:"sku,optional"`
	UnitID      int                           `json:"unit_id,optional"`
	GroupID     int                           `json:"group_id,optional"`
	CategoryID  int                           `json:"category_id,optional"`
	Collection  []string                      `json:"collection,optional"`
	Operations  []CreateProductOperationInput `json:"operations,optional"`
}

type ProductOutput struct {
	ID          int               `json:"id"`
	Name        string            `json:"name"`
	Code        string            `json:"code"`
	Description string            `json:"description"`
	Price       int               `json:"price"`
	Type        string            `json:"type"`
	Status      int               `json:"status"`
	Quantity    int               `json:"quantity"`
	SKU         string            `json:"sku"`
	UnitID      int               `json:"unit_id"`
	GroupID     int               `json:"group_id"`
	CategoryID  int               `json:"category_id"`
	Collection  []string          `json:"collection"`
	CreatedAt   string            `json:"created_at"`
	UpdatedAt   string            `json:"updated_at"`
	Version    int               `json:"version"`
	Category   TermShortOutput   `json:"category"`
	Operations []OperationOutput `json:"operations"`
}

type TermShortOutput struct {
	ID   int    `json:"id"`
	Name string `json:"name"`
}

type UpdateProductInput struct {
	ID          int                            `json:"id"`
	Name        *string                        `json:"name,optional"`
	Code        *string                        `json:"code,optional"`
	Description *string                        `json:"description,optional"`
	Price       *int                           `json:"price,optional"`
	Type        *string                        `json:"type,optional"`
	Status      *int                           `json:"status,optional"`
	Quantity    *int                           `json:"quantity,optional"`
	SKU         *string                        `json:"sku,optional"`
	UnitID      *int                           `json:"unit_id,optional"`
	GroupID     *int                           `json:"group_id,optional"`
	CategoryID  *int                           `json:"category_id,optional"`
	Collection  []string                       `json:"collection,optional"`
	Operations  []*CreateProductOperationInput `json:"operations,optional"`
	Modified    []string                       `json:"modified,optional"`
}

type ListProductInput struct {
	PageSize    int                `json:"page_size,optional"`
	Page   int                `json:"page,optional"`
	Filter ProductFilterInput `json:"filter,optional"`
	Search string             `json:"search,optional"`
	UserCreated int                `json:"user_created,optional"`
	UserManaged []int              `json:"user_managed,optional"`
	OrderBy     string             `json:"order_by,optional"`
}

type ProductFilterInput struct {
	Name       string `json:"name,optional"`
	Code       string `json:"code,optional"`
	Type       string `json:"type,optional"`
	Status     int    `json:"status,optional"`
	SKU        string `json:"sku,optional"`
	UnitID     int    `json:"unit_id,optional"`
	GroupID    int    `json:"group_id,optional"`
	CategoryID int    `json:"category_id,optional"`
}

type ListProductOutput struct {
	Products  []ProductOutput `json:"products"`
	Total     int             `json:"total"`
	TotalPage int             `json:"total_page"`
}

type GetProductInput struct {
	ID   int    `json:"id,optional"`
	Name string `json:"name,optional"`
	Code string `json:"code,optional"`
}

type DeleteProductInput struct {
	ID int `json:"id"`
}
