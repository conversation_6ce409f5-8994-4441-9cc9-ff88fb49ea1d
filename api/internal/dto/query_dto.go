package dto

type DynamicQuery struct {
	Table        string         `json:"table"`
	Selects      []string       `json:"selects,omitempty"`
	Filters      []Filter       `json:"filters,omitempty"`
	GroupBy      []string       `json:"group_by,omitempty"`
	Aggregations []Aggregation  `json:"aggregations,omitempty"`
	Sort         []SortCriteria `json:"sort,omitempty"`
	Limit        int            `json:"limit,omitempty"`
	Offset       int            `json:"offset,omitempty"`
	Joins        []JoinInfo     `json:"joins,omitempty"`
}

type Filter struct {
	Field      string   `json:"field,omitempty"`
	Operator   string   `json:"operator,omitempty"`
	Value      string   `json:"value,omitempty"`
	Logic      string   `json:"logic,omitempty"`
	Conditions []Filter `json:"conditions,omitempty"`
	Function   string   `json:"function,omitempty"`
}

type Aggregation struct {
	Field    string               `json:"field"`
	Function string               `json:"function"`
	Alias    string               `json:"alias"`
	Over     *WindowSpecification `json:"over,omitempty"`
}

type SortCriteria struct {
	Field string `json:"field"`
	Order string `json:"order"`
}

type JoinInfo struct {
	Table string `json:"table"`
	Alias string `json:"alias,omitempty"`
	C1    string `json:"c1,omitempty"`
	C2    string `json:"c2,omitempty"`
	Type  string `json:"type,omitempty"`
}

type WindowSpecification struct {
	PartitionBy []string       `json:"partition_by,omitempty"`
	OrderBy     []SortCriteria `json:"order_by,omitempty"`
}

type DynamicQueryOutput struct {
	Result map[string]interface{} `json:"result"`
}
