package dto

type MaterialOutput struct {
	ID                     int     `json:"id"`
	Code                   string  `json:"code"`
	Name                   string  `json:"name"`
	Unit                   string  `json:"unit"`
	PackagingSpecification string  `json:"packaging_specification"`
	Description            string  `json:"description"`
	CostPrice              float64 `json:"cost_price"`
	Status                 int     `json:"status"`
	Version                int     `json:"version"`
	CreatedAt              string  `json:"created_at"`
	UpdatedAt              string  `json:"updated_at"`
	DeletedAt              string  `json:"deleted_at,omitempty"`
}

type CreateMaterialInput struct {
	Code                   string  `json:"code,omitempty"`
	Name                   string  `json:"name"`
	Unit                   string  `json:"unit"`
	PackagingSpecification string  `json:"packaging_specification,omitempty"`
	Description            string  `json:"description,omitempty"`
	CostPrice              float64 `json:"cost_price,omitempty"`
	Status                 int     `json:"status,omitempty"`
}

type UpdateMaterialInput struct {
	ID                     int      `json:"id"`
	Code                   *string  `json:"code,omitempty"`
	Name                   *string  `json:"name,omitempty"`
	Unit                   *string  `json:"unit,omitempty"`
	PackagingSpecification *string  `json:"packaging_specification,omitempty"`
	Description            *string  `json:"description,omitempty"`
	CostPrice              *float64 `json:"cost_price,omitempty"`
	Status                 *int     `json:"status,omitempty"`
	Modified               []string `json:"modified,omitempty"`
}

type GetMaterialInput struct {
	ID   int    `json:"id"`
	Code string `json:"code,omitempty"`
	Name string `json:"name,omitempty"`
}

type DeleteMaterialInput struct {
	ID   int    `json:"id"`
	Name string `json:"name"`
}

type ListMaterialInput struct {
	PageSize int                 `json:"page_size,omitempty"`
	Page     int                 `json:"page,omitempty"`
	Filter   MaterialFilterInput `json:"filter,omitempty"`
	Search   string              `json:"search,omitempty"`
	OrderBy  string              `json:"order_by,omitempty"`
}

type MaterialFilterInput struct {
	Code   string `json:"code,omitempty"`
	Name   string `json:"name,omitempty"`
	Unit   string `json:"unit,omitempty"`
	Status int    `json:"status,omitempty"`
}

type ListMaterialOutput struct {
	Materials []MaterialOutput `json:"materials"`
	Total     int              `json:"total"`
	TotalPage int              `json:"total_page"`
}
