package dto

type OperationMaterialOutput struct {
	ID          int     `json:"id"`
	OperationID int     `json:"operation_id"`
	MaterialID  int     `json:"material_id"`
	Quantity    float64 `json:"quantity"`
	Status      int     `json:"status"`
	Version     int     `json:"version"`
	CreatedAt   string  `json:"created_at"`
	UpdatedAt   string  `json:"updated_at"`
	DeletedAt   string  `json:"deleted_at,omitempty"`
}

type CreateOperationMaterialInput struct {
	OperationID int     `json:"operation_id"`
	MaterialID  int     `json:"material_id"`
	Quantity    float64 `json:"quantity"`
	Status      int     `json:"status,omitempty"`
}

type UpdateOperationMaterialInput struct {
	ID          int      `json:"id"`
	OperationID *int     `json:"operation_id,omitempty"`
	MaterialID  *int     `json:"material_id,omitempty"`
	Quantity    *float64 `json:"quantity,omitempty"`
	Status      *int     `json:"status,omitempty"`
	Modified    []string `json:"modified,omitempty"`
}

type GetOperationMaterialInput struct {
	ID          int `json:"id"`
	OperationID int `json:"operation_id,omitempty"`
	MaterialID  int `json:"material_id,omitempty"`
}

type DeleteOperationMaterialInput struct {
	ID          int `json:"id"`
	OperationID int `json:"operation_id"`
}

type ListOperationMaterialInput struct {
	PageSize int                          `json:"page_size,omitempty"`
	Page     int                          `json:"page,omitempty"`
	Filter   OperationMaterialFilterInput `json:"filter,omitempty"`
	Search   string                       `json:"search,omitempty"`
	OrderBy  string                       `json:"order_by,omitempty"`
}

type OperationMaterialFilterInput struct {
	OperationID int `json:"operation_id,omitempty"`
	MaterialID  int `json:"material_id,omitempty"`
	Status      int `json:"status,omitempty"`
}

type ListOperationMaterialOutput struct {
	OperationMaterials []OperationMaterialOutput `json:"operation_materials"`
	Total              int                       `json:"total"`
	TotalPage          int                       `json:"total_page"`
}

type OperationMaterialSetItem struct {
	MaterialId int     `json:"material_id"`
	Quantity   float64 `json:"quantity"`
}

type BulkSetInput struct {
	OperationId int                        `json:"operation_id"`
	Materials   []OperationMaterialSetItem `json:"materials,optional"`
}
