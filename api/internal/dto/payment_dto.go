package dto

type CreatePaymentInput struct {
	TotalAmount float64                        `json:"total_amount"`
	Cash        float64                        `json:"cash"`
	CreditCard  float64                        `json:"credit_card"`
	Mpos        float64                        `json:"mpos"`
	Bank        float64                        `json:"bank"`
	Momo        float64                        `json:"momo"`
	State       string                         `json:"state"`
	PaymentDate string                         `json:"payment_date"`
	BillID      int                            `json:"bill_id"`
	PersonID    int                            `json:"person_id"`
	DealID      int                            `json:"deal_id"`
	SendSms     bool                           `json:"send_sms"`
	Allocations []CreatePaymentAllocationInput `json:"allocations"`
}

type UpdatePaymentInput struct {
	ID          int      `json:"id"`
	TotalAmount float64  `json:"total_amount"`
	Cash        float64  `json:"cash"`
	CreditCard  float64  `json:"credit_card"`
	Mpos        float64  `json:"mpos"`
	Bank        float64  `json:"bank"`
	Momo        float64  `json:"momo"`
	State       string   `json:"state"`
	PaymentDate string   `json:"payment_date"`
	BillID      int      `json:"bill_id"`
	PersonID    int      `json:"person_id"`
	DealID      int      `json:"deal_id"`
	SendSms     bool     `json:"send_sms"`
	Modified    []string `json:"modified,omitempty"`
}

type PaymentOutput struct {
	ID          int                       `json:"id"`
	TotalAmount float64                   `json:"total_amount"`
	Cash        float64                   `json:"cash"`
	CreditCard  float64                   `json:"credit_card"`
	Mpos        float64                   `json:"mpos"`
	Bank        float64                   `json:"bank"`
	Momo        float64                   `json:"momo"`
	State       string                    `json:"state"`
	PaymentDate string                    `json:"payment_date"`
	BillID      int                       `json:"bill_id"`
	PersonID    int                       `json:"person_id"`
	DealID      int                       `json:"deal_id"`
	UserID      int                       `json:"user_id"`
	CreatedAt   string                    `json:"created_at"`
	UpdatedAt   string                    `json:"updated_at"`
	DeletedAt   string                    `json:"deleted_at"`
	Version     int                       `json:"version"`
	User        UserShortOutput           `json:"user"`
	Allocations []PaymentAllocationOutput `json:"allocations"`
}

type ListPaymentInput struct {
	PageSize int           `json:"page_size,omitempty"`
	Page    int           `json:"page,omitempty"`
	Filter  PaymentFilter `json:"filter,omitempty"`
	OrderBy string        `json:"order_by,omitempty"`
}

type PaymentFilter struct {
	BillID    int    `json:"bill_id,omitempty"`
	PersonID  int    `json:"person_id,omitempty"`
	DealID    int    `json:"deal_id,omitempty"`
	State     string `json:"state,omitempty"`
	StartDate string `json:"start_date,omitempty"`
	EndDate   string `json:"end_date,omitempty"`
}

type ListPaymentOutput struct {
	Payments  []PaymentOutput `json:"payments"`
	Total     int             `json:"total"`
	TotalPage int             `json:"total_page"`
}
