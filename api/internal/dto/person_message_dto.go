package dto

import "time"

type SendMessageInput struct {
	PersonID      int    `json:"person_id"`
	Phone         string `json:"phone"`
	Email         string `json:"email"`
	EmailContent  string `json:"email_content"`
	SmsContent    string `json:"sms_content"`
	ZnsTemplateID string `json:"zns_template_id"`
	ZnsParams     string `json:"zns_params"`
	ZnsContent    string `json:"zns_content"`
	FallbackSms   bool   `json:"fallback_sms"`
}

type MessageTemplateInput struct {
	PersonID      int `json:"person_id"`
	DealID        int `json:"deal_id"`
	AppointmentID int `json:"appointment_id"`
}

type MessageTemplateOutput struct {
	Sms   string `json:"sms"`
	Zns   string `json:"zns"`
	Email string `json:"email"`
}

type SendMessageOutput struct {
	// Empty for now as per API definition
}

type MessageHistoryInput struct {
	PersonID int `json:"person_id"`
}

type MessageHistoryOutput struct {
	ID            int           `json:"id"`
	PersonID      int           `json:"person_id"`
	UserID        int           `json:"user_id"`
	MessageID     string        `json:"message_id"`
	Phone         string        `json:"phone"`
	Type          string        `json:"type"` // sms or zns
	Content       string        `json:"content"`
	ZnsData       string        `json:"zns_data"`
	ErrorCode     string        `json:"error_code"`
	MessageStatus string        `json:"message_status"` // sent, failed, delivered
	DeliveredAt   string        `json:"delivered_at"`
	Status        int           `json:"status"`
	CreatedAt     time.Time     `json:"created_at"`
	UpdatedAt     time.Time     `json:"updated_at"`
	DeletedAt     string        `json:"deleted_at,omitempty"`
	Version       int           `json:"version"`
	Person        *PersonOutput `json:"person,omitempty"`
}

type ListMessageHistoryOutput struct {
	MessageHistories []MessageHistoryOutput `json:"message_histories"`
	Total            int                    `json:"total"`
	TotalPage        int                    `json:"total_page"`
}
