package dto

import (
	"time"
)

type CreateTrackInput struct {
	PipelineID *int     `json:"pipeline_id,omitempty"`
	Weight     *float64 `json:"weight,omitempty"`
	DealID     *int     `json:"deal_id,omitempty"`
	PersonID   *int     `json:"person_id,omitempty"`
	Begin      *string  `json:"begin,omitempty"`
	End        *string  `json:"end,omitempty"`
	StageID    *int     `json:"stage_id,omitempty"`
	State      *string  `json:"state,omitempty"`
}

type UpdateTrackInput struct {
	ID           int      `json:"id"`
	Weight       *float64 `json:"weight,omitempty"`
	PipelineID   *int     `json:"pipeline_id,omitempty"`
	DealID       *int     `json:"deal_id,omitempty"`
	Begin        *string  `json:"begin,omitempty"`
	End          *string  `json:"end,omitempty"`
	StageID      *int     `json:"stage_id,omitempty"`
	Status       *int     `json:"status,omitempty"`
	State        *string  `json:"state,omitempty"`
	Kind         *string  `json:"kind,omitempty"`
	RefundReason *string  `json:"refund_reason,omitempty"`
	DealStageID  *int     `json:"deal_stage_id,omitempty"`
	Modified     []string `json:"modified,omitempty"`
}

type ListTrackInput struct {
	PageSize        int         `json:"page_size,omitempty"`
	Page    int         `json:"page,omitempty"`
	Filter  TrackFilter `json:"filter,omitempty"`
	OrderBy string      `json:"order_by,omitempty"`
	IncludeRelation bool        `json:"include_relation,omitempty"`
}

type TrackFilter struct {
	PipelineID *int `json:"pipeline_id,omitempty"`
	DealID     *int `json:"deal_id,omitempty"`
	PersonID   *int `json:"person_id,omitempty"`
	StageID    *int `json:"stage_id,omitempty"`
	Status     *int `json:"status,omitempty"`
}

type TrackOutput struct {
	ID           int64               `json:"id"`
	Weight       float64             `json:"weight"`
	Begin        time.Time           `json:"begin"`
	End          time.Time           `json:"end"`
	Status       int                 `json:"status"`
	PipelineID   int                 `json:"pipeline_id"`
	DealID       int                 `json:"deal_id"`
	PersonID     int                 `json:"person_id"`
	StageID      int                 `json:"stage_id"`
	StageHistory []StageHistoryEntry `json:"stage_history"`
	State        string              `json:"state"`
	Kind         string              `json:"kind"`
	RefundReason string              `json:"refund_reason"`
	DealStageID  int                 `json:"deal_stage_id"`
	CreatedAt    time.Time           `json:"created_at"`
	UpdatedAt    time.Time           `json:"updated_at"`
	DeletedAt    string              `json:"deleted_at"`
	Version      int                 `json:"version"`
	Appointments []AppointmentOutput `json:"appointments"`
	Person       *PersonOutput       `json:"person"`
	Deal         *DealOutput         `json:"deal"`
	Attachments  []AttachmentOutput  `json:"attachments"`
	BillItems    []BillItemOutput    `json:"bill_items"`
}

type ListTrackOutput struct {
	Tracks    []TrackOutput `json:"tracks"`
	Total     int           `json:"total"`
	TotalPage int           `json:"total_page"`
}

type TrackCheckoutInput struct {
	ID int `json:"id"`
}

type ActiveTrackInput struct {
	PersonID int `json:"person_id"`
}
