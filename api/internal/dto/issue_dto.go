package dto

import "time"

// CreateIssueInput represents the input for creating a new issue
type CreateIssueInput struct {
	Title       string  `json:"title"`
	Description *string `json:"description,omitempty"`
	Type        string  `json:"type"`               // complain, refund, warranty, inquiry, orther
	Priority    *string `json:"priority,omitempty"` // low, medium, high
	PersonID    int     `json:"person_id"`
}

// UpdateIssueInput represents the input for updating an existing issue
type UpdateIssueInput struct {
	ID          int      `json:"id"`
	Title       *string  `json:"title,omitempty"`
	Description *string  `json:"description,omitempty"`
	Type        *string  `json:"type,omitempty"`     // complain, refund, warranty, inquiry, orther
	Progress    *string  `json:"progress,omitempty"` // open, in_progress, resolved, closed
	Priority    *string  `json:"priority,omitempty"` // low, medium, high
	Modified    []string `json:"modified,omitempty"`
}

// ListIssueInput represents the input for listing issues
type ListIssueInput struct {
	PersonID *int    `json:"person_id,omitempty"`
	Type     *string `json:"type,omitempty"`
	Progress *string `json:"progress,omitempty"`
	Priority *string `json:"priority,omitempty"`
}

// GetIssueInput represents the input for getting an issue
type GetIssueInput struct {
	ID int `json:"id"`
}

// CloseIssueInput represents the input for closing an issue
type CloseIssueInput struct {
	ID int `json:"id"`
}

// IssueOutput represents the output for issue data
type IssueOutput struct {
	ID          int       `json:"id"`
	Title       string    `json:"title"`
	Description *string   `json:"description,omitempty"`
	Type        string    `json:"type"`
	Progress    string    `json:"progress"`
	Priority    string    `json:"priority"`
	PersonID    int       `json:"person_id"`
	Status      int       `json:"status"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	DeletedAt   string    `json:"deleted_at,omitempty"`
	Version     int       `json:"version"`
}

// ListIssueOutput represents the output for listing issues
type ListIssueOutput struct {
	Issues []IssueOutput `json:"issues"`
}
