package dto

type LogicalExpressionV2 struct {
	Type        string                `json:"type"`
	Condition   *FilterConditionV2    `json:"condition,omitempty"`
	Expressions []LogicalExpressionV2 `json:"expressions,omitempty"`
}

type FilterConditionV2 struct {
	Field        string          `json:"field,omitempty"`
	Operator     string          `json:"operator,omitempty"`
	Value        interface{}     `json:"value,omitempty"`
	Function     string          `json:"function,omitempty"`
	GroupedIn    [][]interface{} `json:"grouped_in,omitempty"`
	RightField   string          `json:"right_field,omitempty"`
	RightExprRaw string          `json:"right_expr_raw,omitempty"`
	RightExpr    *ExpressionV2   `json:"right_expr,omitempty"`
	Subquery     *DynamicQueryV2 `json:"subquery,omitempty"`
	LeftExprRaw  string          `json:"left_expr_raw,omitempty"`
	LeftExpr     *ExpressionV2   `json:"left_expr,omitempty"`
}

type ExpressionV2 struct {
	Type        string         `json:"type"`
	LiteralType string         `json:"literal_type,omitempty"`
	Operator    string         `json:"operator,omitempty"`
	Function    string         `json:"function,omitempty"`
	Arguments   []ExpressionV2 `json:"arguments,omitempty"`
	ColumnRef   string         `json:"column_ref,omitempty"`
	Value       interface{}    `json:"value,omitempty"`
}

type DynamicQueryV2 struct {
	Table        string               `json:"table"`
	Alias        string               `json:"alias,omitempty"`
	Distinct     *bool                `json:"distinct,omitempty"`
	DistinctOn   []string             `json:"distinct_on,omitempty"`
	WhereLogic   *LogicalExpressionV2 `json:"where_logic,omitempty"`
	HavingLogic  *LogicalExpressionV2 `json:"having_logic,omitempty"`
	GroupBy      []string             `json:"group_by"`
	Aggregations []AggregationV2      `json:"aggregations"`
	Sort         []SortCriteriaV2     `json:"sort"`
	Limit        int                  `json:"limit"`
	Offset       int                  `json:"offset"`
	Selects      []SelectFieldV2      `json:"selects"`
	Joins        []JoinInfoV2         `json:"joins"`
	CTEs         []CTEInfoV2          `json:"ctes,omitempty"`
	Lock         *LockInfoV2          `json:"lock,omitempty"`
	UnionQueries []UnionInfoV2        `json:"unions,omitempty"`
}

type SelectFieldV2 struct {
	Field    string      `json:"field"`
	Function string      `json:"function,omitempty"`
	Alias    string      `json:"alias,omitempty"`
	Format   string      `json:"format,omitempty"`
	Case     *CaseWhenV2 `json:"case,omitempty"`
	RawSQL   string      `json:"raw_sql,omitempty"`
}

type CaseWhenV2 struct {
	Conditions []CaseConditionV2 `json:"conditions"`
	Else       interface{}       `json:"else,omitempty"`
}

type CaseConditionV2 struct {
	When LogicalExpressionV2 `json:"when"`
	Then interface{}         `json:"then"`
}

type JoinInfoV2 struct {
	Table    string               `json:"table"`
	Alias    string               `json:"alias,omitempty"`
	Type     string               `json:"type"`
	OnLogic  *LogicalExpressionV2 `json:"on_logic,omitempty"`
	Using    []string             `json:"using,omitempty"`
	Subquery *DynamicQueryV2      `json:"subquery,omitempty"`
}

type AggregationV2 struct {
	Field    string                 `json:"field"`
	Function string                 `json:"function"`
	Alias    string                 `json:"alias"`
	Distinct *bool                  `json:"distinct,omitempty"`
	Over     *WindowSpecificationV2 `json:"over,omitempty"`
}

type SortCriteriaV2 struct {
	Field   string `json:"field"`
	Order   string `json:"order"`
	Collate string `json:"collate,omitempty"`
}

type WindowSpecificationV2 struct {
	Name        string           `json:"name,omitempty"`
	PartitionBy []string         `json:"partition_by"`
	OrderBy     []SortCriteriaV2 `json:"order_by"`
	BasedOn     string           `json:"based_on,omitempty"`
}

type CTEInfoV2 struct {
	Name    string         `json:"name"`
	Columns []string       `json:"columns,omitempty"`
	Query   DynamicQueryV2 `json:"query"`
}

type LockInfoV2 struct {
	Type     string   `json:"type"`
	Of       []string `json:"of,omitempty"`
	WaitMode string   `json:"wait_mode"`
}

type UnionInfoV2 struct {
	Type  string         `json:"type"`
	Query DynamicQueryV2 `json:"query"`
}

type DynamicQueryV2Output struct {
	Result map[string]interface{} `json:"result"`
}
