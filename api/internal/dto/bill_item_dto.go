package dto

type CreateBillItemInput struct {
	BillID         int     `json:"bill_id"`
	AttachmentID   int     `json:"attachment_id"`
	Amount         float64 `json:"amount"`
	Note           string  `json:"note"`
	PaymentID      int     `json:"payment_id"`
	AllocateAmount float64 `json:"allocate_amount"`
}

type UpdateBillItemInput struct {
	ID             int      `json:"id"`
	BillID         int      `json:"bill_id"`
	AttachmentID   int      `json:"attachment_id"`
	Amount         float64  `json:"amount"`
	Note           string   `json:"note"`
	PaymentID      int      `json:"payment_id"`
	AllocateAmount float64  `json:"allocate_amount"`
	Modified       []string `json:"modified"`
}

type BillItemOutput struct {
	ID                 int                       `json:"id"`
	BillID             int                       `json:"bill_id"`
	AttachmentID       int                       `json:"attachment_id"`
	Status             int                       `json:"status"`
	Amount             float64                   `json:"amount"`
	Note               string                    `json:"note"`
	UserID             int                       `json:"user_id"`
	TrackID            int                       `json:"track_id"`
	CreatedAt          string                    `json:"created_at"`
	UpdatedAt          string                    `json:"updated_at"`
	DeletedAt          string                    `json:"deleted_at"`
	Version            int                       `json:"version"`
	PaidAmount         float64                         `json:"paid_amount"`
	Attachment         *AttachmentOutput         `json:"attachment"`
	PaymentAllocations []PaymentAllocationOutput `json:"allocations"`
	User               UserShortOutput           `json:"user"`
}

type ListBillItemInput struct {
	PageSize int            `json:"page_size,omitempty"`
	Page    int            `json:"page,omitempty"`
	Filter  BillItemFilter `json:"filter,omitempty"`
	OrderBy string         `json:"order_by,omitempty"`
}

type BillItemFilter struct {
	BillID       int `json:"bill_id,omitempty"`
	AttachmentID int `json:"attachment_id,omitempty"`
	PaymentID    int `json:"payment_id,omitempty"`
}

type ListBillItemOutput struct {
	BillItems []BillItemOutput `json:"bill_items"`
	Total     int              `json:"total"`
	TotalPage int              `json:"total_page"`
}
