package dto

import "time"

// NotificationOutput là DTO cho output của notification
type NotificationOutput struct {
	ID         int                    `json:"id"`
	UserID     int                    `json:"user_id"`
	Type       string                 `json:"type"`
	Message    string                 `json:"message"`
	EntityID   *int                   `json:"entity_id,omitempty"`
	EntityType *string                `json:"entity_type,omitempty"`
	IsRead     bool                   `json:"is_read"`
	ReadAt     *time.Time             `json:"read_at,omitempty"`
	SenderID   *int                   `json:"sender_id,omitempty"`
	Metadata   map[string]interface{} `json:"metadata,omitempty"`
	Status     int                    `json:"status"`
	Version    int                    `json:"version"`
	CreatedAt  time.Time              `json:"created_at"`
	UpdatedAt  time.Time              `json:"updated_at"`
	DeletedAt  string                 `json:"deleted_at,omitempty"`
}

// CreateNotificationInput là DTO cho input tạo notification
type CreateNotificationInput struct {
	UserID     int                    `json:"user_id"`
	Type       string                 `json:"type"`
	Message    string                 `json:"message"`
	EntityID   *int                   `json:"entity_id,omitempty"`
	EntityType *string                `json:"entity_type,omitempty"`
	IsRead     bool                   `json:"is_read"`
	SenderID   *int                   `json:"sender_id,omitempty"`
	Metadata   map[string]interface{} `json:"metadata,omitempty"`
}

// UpdateNotificationInput là DTO cho input cập nhật notification
type UpdateNotificationInput struct {
	ID       int      `json:"id"`
	IsRead   bool     `json:"is_read,omitempty"`
	Status   *int8    `json:"status,omitempty"`
	Modified []string `json:"modified,omitempty"`
}

// ListNotificationsInput là DTO cho input lấy danh sách notifications
type ListNotificationsInput struct {
	Filter   NotificationInputFilter `json:"filter,omitempty"`
	Page     int                     `json:"page,omitempty"`
	PageSize int                     `json:"page_size,omitempty"`
	OrderBy  string                  `json:"order_by,omitempty"`
	Search   string                  `json:"search,omitempty"`
}

// NotificationInputFilter là DTO cho filter khi lấy danh sách notifications
type NotificationInputFilter struct {
	Ids    []int  `json:"ids,omitempty"`
	UserID int    `json:"user_id,omitempty"`
	IsRead *bool  `json:"is_read,omitempty"`
	Type   string `json:"type,omitempty"`
	Status int8   `json:"status,range=[0:10],omitempty"`
}

// ListNotificationsOutput là DTO cho output danh sách notifications
type ListNotificationsOutput struct {
	Notifications []*NotificationOutput
	Total         int
}

// UnreadCountOutput là DTO cho output số lượng notifications chưa đọc
type UnreadCountOutput struct {
	UnreadCount int `json:"unread_count"`
}

// MarkAsReadOutput là DTO cho output khi đánh dấu notification đã đọc
type MarkAsReadOutput struct {
	Message      string              `json:"message"`
	Notification *NotificationOutput `json:"notification"`
}

// MarkAllAsReadOutput là DTO cho output khi đánh dấu tất cả notifications đã đọc
type MarkAllAsReadOutput struct {
	Message      string `json:"message"`
	UpdatedCount int    `json:"updated_count"`
}

// CreateBatchNotificationInput là DTO cho input tạo notification hàng loạt
type CreateBatchNotificationInput struct {
	UserIDs    []int                  `json:"user_ids"`
	Type       string                 `json:"type"`
	Message    string                 `json:"message"`
	EntityID   *int                   `json:"entity_id,omitempty"`
	EntityType *string                `json:"entity_type,omitempty"`
	SenderID   *int                   `json:"sender_id,omitempty"`
	Metadata   map[string]interface{} `json:"metadata,omitempty"`
}

// CreateBroadcastNotificationInput là DTO cho input phát sóng notification cho tất cả user
type CreateBroadcastNotificationInput struct {
	Type       string                 `json:"type"`
	Message    string                 `json:"message"`
	EntityID   *int                   `json:"entity_id,omitempty"`
	EntityType *string                `json:"entity_type,omitempty"`
	SenderID   *int                   `json:"sender_id,omitempty"`
	Metadata   map[string]interface{} `json:"metadata,omitempty"`
}

// CreateGroupNotificationInput là DTO cho input tạo notification cho nhóm user
type CreateGroupNotificationInput struct {
	DepartmentIDs []int                  `json:"department_ids,omitempty"`
	Roles         []string               `json:"roles,omitempty"`
	Type          string                 `json:"type"`
	Message       string                 `json:"message"`
	EntityID      *int                   `json:"entity_id,omitempty"`
	EntityType    *string                `json:"entity_type,omitempty"`
	SenderID      *int                   `json:"sender_id,omitempty"`
	Metadata      map[string]interface{} `json:"metadata,omitempty"`
}

// CreateBatchNotificationOutput là DTO cho output tạo notification hàng loạt
type CreateBatchNotificationOutput struct {
	CreatedCount int    `json:"created_count"`
	Message      string `json:"message"`
}
