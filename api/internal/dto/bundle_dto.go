package dto

import "time"

type BundleOutput struct {
	ID          int       `json:"id"`
	MachineName string    `json:"machine_name"`
	Name        string    `json:"name"`
	Type        string    `json:"type"`
	Description string    `json:"description"`
	Status      int       `json:"status"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	DeletedAt   string    `json:"deleted_at,omitempty"`
	Version     int       `json:"version"`
}

type CreateBundleInput struct {
	MachineName string `json:"machine_name"`
	Name        string `json:"name"`
	Type        string `json:"type"`
	Description string `json:"description"`
}

type UpdateBundleInput struct {
	ID          int      `json:"id"`
	Name        string   `json:"name,omitempty"`
	Description string   `json:"description,omitempty"`
	Modified    []string `json:"modified,omitempty"`
}

type ListBundlesInput struct {
	Filter BundleInputFilter `json:"filter,omitempty"`
	Page   int               `json:"page,omitempty"`
	PageSize int               `json:"page_size,omitempty"`
	OrderBy  string            `json:"order_by,omitempty"`
}

type BundleInputFilter struct {
	Name   string `json:"name,omitempty"`
	Type   string `json:"type,omitempty"`
	Status int    `json:"status,omitempty"`
}

type ListBundlesOutput struct {
	Bundles []*BundleOutput
	Total   int
}
