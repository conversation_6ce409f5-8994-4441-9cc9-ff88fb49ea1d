package dto

import "time"

type FileUsageOutput struct {
	ID         int                    `json:"id"`
	FileID     int                    `json:"file_id"`
	EntityType string                 `json:"entity_type"`
	EntityID   int                    `json:"entity_id"`
	UsageType  string                 `json:"usage_type"`
	UsageMeta  map[string]interface{} `json:"usage_meta"`
	TrackID    int                    `json:"track_id"`
	Status     int                    `json:"status"`
	Version    int                    `json:"version"`
	CreatedAt  time.Time              `json:"created_at"`
	UpdatedAt  time.Time              `json:"updated_at"`
	DeletedAt  string                 `json:"deleted_at,omitempty"`
	File       *FileOutput            `json:"file,omitempty"`
	Track      *TrackOutput           `json:"track"`
}

type CreateFileUsageInput struct {
	FileID     int                    `json:"file_id"`
	EntityType string                 `json:"entity_type"`
	EntityID   int                    `json:"entity_id"`
	UsageType  string                 `json:"usage_type,omitempty"`
	UsageMeta  map[string]interface{} `json:"usage_meta,omitempty"`
	TrackID    int                    `json:"track_id,omitempty"`
}

type UpdateFileUsageInput struct {
	ID         int                    `json:"id"`
	FileID     int                    `json:"file_id,omitempty"`
	EntityType string                 `json:"entity_type,omitempty"`
	EntityID   int                    `json:"entity_id,omitempty"`
	UsageType  string                 `json:"usage_type,omitempty"`
	UsageMeta  map[string]interface{} `json:"usage_meta,omitempty"`
	TrackID    int                    `json:"track_id,omitempty"`
	Status     *int8                  `json:"status,omitempty"`
	Modified   []string               `json:"modified,omitempty"`
}

type GetFileUsageInput struct {
	ID int `json:"id"`
}

type ListFileUsagesInput struct {
	Filter   FileUsageInputFilter `json:"filter,omitempty"`
	Page     int                  `json:"page,omitempty"`
	PageSize int                  `json:"page_size,omitempty"`
	OrderBy  string               `json:"order_by,omitempty"`
	Search   string               `json:"search,omitempty"`
}

type FileUsageInputFilter struct {
	FileID     int    `json:"file_id,omitempty"`
	EntityType string `json:"entity_type,omitempty"`
	EntityID   int    `json:"entity_id,omitempty"`
	UsageType  string `json:"usage_type,omitempty"`
	TrackID    int    `json:"track_id,omitempty"`
}

type ListFileUsagesOutput struct {
	FileUsages []*FileUsageOutput
	Total      int
}
