package dto

import (
	"time"
)

// PersonMeta represents metadata for a person
type PersonMeta struct {
	TreatmentID       *int    `json:"treatment_id,omitempty"`
	TreatmentStatusID *int    `json:"treatment_status_id,omitempty"`
	Description       *string `json:"description,omitempty"`
	HasZalo           string  `json:"has_zalo"`
	SecondaryPhone    *string `json:"secondary_phone,omitempty"`
	Code              *string `json:"code,omitempty"`
	MedicalCondition  *string `json:"medical_condition,omitempty"`
	SpecialNote       *string `json:"special_note,omitempty"`
	AccountID         *string `json:"account_id,omitempty"`
	PancakeLink       *string `json:"pancake_link,omitempty"`
	SourceChannel     *string `json:"source_channel,omitempty"`
	FormSource        *string `json:"form_source,omitempty"`
	LandingPageURL    *string `json:"landing_page_url,omitempty"`
	FormPosition      *string `json:"form_position,omitempty"`
	BankAccountName   *string `json:"bank_account_name,omitempty"`
	BankAccountNumber *string `json:"bank_account_number,omitempty"`
	Bank              *string `json:"bank,omitempty"`
	BankBranch        *string `json:"bank_branch,omitempty"`
}

// RecordHistoryEntry represents a history entry
type RecordHistoryEntry struct {
	UserID    int                 `json:"user_id"`
	Changes   []RecordFieldChange `json:"changes"`
	Timestamp time.Time           `json:"timestamp"`
}

// RecordFieldChange represents a field change in history
type RecordFieldChange struct {
	Field    string      `json:"field"`
	OldValue interface{} `json:"old_value"`
	NewValue interface{} `json:"new_value"`
}

// Issue represents an issue
type Issue struct {
	ID          int       `json:"id"`
	Title       string    `json:"title"`
	Description *string   `json:"description,omitempty"`
	Type        string    `json:"type"`
	Progress    string    `json:"progress"`
	Priority    string    `json:"priority"`
	PersonID    int       `json:"person_id"`
	Status      int       `json:"status"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	DeletedAt   string    `json:"deleted_at,omitempty"`
	Version     int       `json:"version"`
}

// PersonAssignmentResponse represents person assignment response
type PersonAssignmentResponse struct {
	ID       int              `json:"id"`
	PersonID int              `json:"person_id"`
	UserID   int              `json:"user_id"`
	Role     string           `json:"role"`
	User     *UserShortOutput `json:"user,omitempty"`
}

// CreatePersonInput represents the input for creating a new person
type CreatePersonInput struct {
	FullName        string        `json:"full_name"`
	DateOfBirth     string        `json:"date_of_birth,omitempty"`
	Gender          *string       `json:"gender,omitempty"`
	ProvinceID      *int          `json:"province_id,omitempty"`
	DistrictID      *int          `json:"district_id,omitempty"`
	WardID          *int          `json:"ward_id,omitempty"`
	AddressNumber   *string       `json:"address_number,omitempty"`
	Phone           string        `json:"phone"`
	Email           *string       `json:"email,omitempty"`
	JobID           *int          `json:"job_id,omitempty"`
	SourceID        *int          `json:"source_id,omitempty"`
	Status          *int          `json:"status,omitempty"`
	PersonField     *PersonMeta   `json:"person_field,omitempty"`
	ReferredBy      *ReferredData `json:"referred_by,omitempty"`
	IsSystemCreated *bool         `json:"is_system_created,omitempty"`
}

// UpdatePersonInput represents the input for updating an existing person
type UpdatePersonInput struct {
	ID            int           `json:"id"`
	FullName      *string       `json:"full_name,omitempty"`
	DateOfBirth   string        `json:"date_of_birth,omitempty"`
	Gender        *string       `json:"gender,omitempty"`
	ProvinceID    *int          `json:"province_id,omitempty"`
	DistrictID    *int          `json:"district_id,omitempty"`
	WardID        *int          `json:"ward_id,omitempty"`
	AddressNumber *string       `json:"address_number,omitempty"`
	Phone         *string       `json:"phone,omitempty"`
	Email         *string       `json:"email,omitempty"`
	JobID         *int          `json:"job_id,omitempty"`
	SourceID      *int          `json:"source_id,omitempty"`
	Status        *int          `json:"status,omitempty"`
	PersonField   *PersonMeta   `json:"person_field,omitempty"`
	ReferredBy    *ReferredData `json:"referred_by,omitempty"`
	Modified      []string      `json:"modified,omitempty"`
}

// PersonOutput represents the output for person data
type PersonOutput struct {
	ID               int                        `json:"id"`
	FullName         string                     `json:"full_name"`
	DateOfBirth      string                     `json:"date_of_birth,omitempty"`
	Gender           *string                    `json:"gender,omitempty"`
	ProvinceID       *int                       `json:"province_id,omitempty"`
	DistrictID       *int                       `json:"district_id,omitempty"`
	WardID           *int                       `json:"ward_id,omitempty"`
	AddressNumber    *string                    `json:"address_number,omitempty"`
	Phone            string                     `json:"phone"`
	Email            *string                    `json:"email,omitempty"`
	PhoneConfirm     bool                       `json:"confirm_phone"`
	EmailConfirm     bool                       `json:"confirm_mail"`
	JobID            *int                       `json:"job_id,omitempty"`
	SourceID         *int                       `json:"source_id,omitempty"`
	Status           int                        `json:"status"`
	Version          int                        `json:"version"`
	UserID           int                        `json:"user_id"`
	CreatedAt        time.Time                  `json:"created_at"`
	UpdatedAt        time.Time                  `json:"updated_at"`
	DeletedAt        string                     `json:"deleted_at,omitempty"`
	PersonField      PersonMeta                 `json:"person_field"`
	PhoneViewHistory []PhoneViewerInfo          `json:"phone_view_history,omitempty"`
	Tags             []TagInfoOutput            `json:"tags,omitempty"`
	StageName        string                     `json:"stage_name,omitempty"`
	ReferredBy       *ReferredData              `json:"referred_by,omitempty"`
	FormSubmissions  []FormSubmissionOutput     `json:"form_submissions,omitempty"`
	Creator          *UserShortOutput           `json:"creator,omitempty"`
	Sale             *UserShortOutput           `json:"sale,omitempty"`
	Issues           []IssueOutput              `json:"issues,omitempty"`
	Assignment       []PersonAssignmentResponse `json:"assignment,omitempty"`
}

// ListPersonInput represents the input for listing persons
type ListPersonInput struct {
	PageSize int          `json:"page_size,omitempty"`
	Page     int          `json:"page,omitempty"`
	Filter   PersonFilter `json:"filter,omitempty"`
	UserID   int          `json:"user_id,omitempty"`
	Search   string       `json:"search,omitempty"`
	OrderBy  string       `json:"order_by,omitempty"`
}

// PersonFilter represents the filter criteria for listing persons
type PersonFilter struct {
	Gender   *string `json:"gender,omitempty"`
	Phone    *string `json:"phone,omitempty"`
	Email    *string `json:"email,omitempty"`
	JobID    *int    `json:"job_id,omitempty"`
	SourceID *int    `json:"source_id,omitempty"`
	Status   *int    `json:"status,omitempty"`
}

// ListPersonOutput represents the output for listing persons
type ListPersonOutput struct {
	Persons   []PersonOutput `json:"persons"`
	Total     int            `json:"total"`
	TotalPage int            `json:"total_page"`
}

// ReferredData represents referral information
type ReferredData struct {
	ReferrerID   int     `json:"referrer_id,omitempty"`
	Relationship *string `json:"relationship,omitempty"`
	Note         *string `json:"note,omitempty"`
}

// PhoneViewerInfo represents phone view history information
type PhoneViewerInfo struct {
	UserID   int       `json:"user_id"`
	UserName string    `json:"user_name"`
	ViewedAt time.Time `json:"viewed_at"`
}

// GetPersonInput represents the input for getting a person by ID
type GetPersonInput struct {
	ID              int  `json:"id"`
	IncludeRelation bool `json:"include_relation,omitempty"`
}
