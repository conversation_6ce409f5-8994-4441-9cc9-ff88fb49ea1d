package dto

import "time"

type InstallmentOutput struct {
	ID                int                       `json:"id"`
	PlanID            int                       `json:"plan_id"`
	InstallmentNumber int                       `json:"installment_number"`
	Amount            float64                   `json:"amount"`
	Name              string                    `json:"name"`
	Note              string                    `json:"note"`
	PersonID          int                       `json:"person_id"`
	UserID            int                       `json:"user_id"`
	Kind              string                    `json:"kind"`
	TransactionType   int                       `json:"transaction_type"`
	State             string                    `json:"state"`
	Status            int                       `json:"status"`
	PaidAt            *time.Time                `json:"paid_at,omitempty"`
	CreatedAt         time.Time                 `json:"created_at"`
	UpdatedAt         time.Time                 `json:"updated_at"`
	DeletedAt         string                    `json:"deleted_at,omitempty"`
	Version           int                       `json:"version"`
	Allocations       []PaymentAllocationOutput `json:"allocations"`
	PaidAmount        float64                   `json:"paid_amount"`
	Creator           UserShortOutput           `json:"creator"`
}

type CreateInstallmentInput struct {
	PlanID            int     `json:"plan_id"`
	InstallmentNumber int     `json:"installment_number,omitempty"`
	Amount            float64 `json:"amount"`
	PersonID          int     `json:"person_id"`
	Note              string  `json:"note,omitempty"`
	Kind              string  `json:"kind"`
	TransactionType   int     `json:"transaction_type"`
	Status            int     `json:"status,omitempty"`
}

type UpdateInstallmentInput struct {
	ID              int      `json:"id"`
	Amount          float64  `json:"amount,omitempty"`
	Note            string   `json:"note,omitempty"`
	PersonID        int      `json:"person_id,omitempty"`
	Kind            string   `json:"kind,omitempty"`
	TransactionType int      `json:"transaction_type,omitempty"`
	Status          int      `json:"status,omitempty"`
	Modified        []string `json:"modified,omitempty"`
}

type ListInstallmentsInput struct {
	Filter   InstallmentInputFilter `json:"filter,omitempty"`
	Page     int                    `json:"page,omitempty"`
	PageSize int                    `json:"page_size,omitempty"`
	OrderBy  string                 `json:"order_by,omitempty"`
}

type InstallmentInputFilter struct {
	PlanID            int `json:"plan_id,omitempty"`
	PersonID          int `json:"person_id,omitempty"`
	InstallmentNumber int `json:"installment_number,omitempty"`
	Status            int `json:"status,omitempty"`
}

type ListInstallmentsOutput struct {
	Installments []*InstallmentOutput
	Total        int
}
