package dto

import "time"

type CashFlowItemOutput struct {
	ID           int       `json:"id"`
	CashFlowID   int       `json:"cash_flow_id"`
	CategoryID   int       `json:"category_id"`
	DepartmentID *int      `json:"department_id"`
	Amount       int       `json:"amount"`
	Note         string    `json:"note"`
	Order        int       `json:"order"`
	HasVat       bool      `json:"has_vat"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
}

type CashFlowItemWithRelationsOutput struct {
	CashFlowItemOutput
	Category   *TermShortOutput       `json:"category,omitempty"`
	Department *DepartmentShortOutput `json:"department,omitempty"`
}

type DepartmentShortOutput struct {
	ID   int    `json:"id"`
	Name string `json:"name"`
}

type CreateCashFlowItemInput struct {
	CashFlowID   int    `json:"cash_flow_id"`
	CategoryID   int    `json:"category_id"`
	DepartmentID int    `json:"department_id"`
	Amount       int    `json:"amount"`
	Note         string `json:"note"`
	Order        int    `json:"order"`
	HasVat       bool   `json:"has_vat"`
}

type UpdateCashFlowItemInput struct {
	ID           int      `json:"id"`
	CategoryID   *int     `json:"category_id,omitempty"`
	DepartmentID *int     `json:"department_id,omitempty"`
	Amount       *int     `json:"amount,omitempty"`
	Note         *string  `json:"note,omitempty"`
	Order        *int     `json:"order,omitempty"`
	HasVat       *bool    `json:"has_vat,omitempty"`
	Modified     []string `json:"modified,omitempty"`
}

type GetCashFlowItemInput struct {
	ID              int  `json:"id"`
	IncludeRelation bool `json:"include_relation,omitempty"`
}

type DeleteCashFlowItemInput struct {
	ID int `json:"id"`
}

type ListCashFlowItemsInput struct {
	CashFlowID   int    `json:"cash_flow_id,omitempty"`
	CategoryID   int    `json:"category_id,omitempty"`
	DepartmentID int    `json:"department_id,omitempty"`
	PageSize     int    `json:"page_size,omitempty"`
	Page         int    `json:"page,omitempty"`
	OrderBy      string `json:"order_by,omitempty"`
}

type ListCashFlowItemsOutput struct {
	Items     []*CashFlowItemWithRelationsOutput `json:"items"`
	Total     int                                `json:"total"`
	TotalPage int                                `json:"total_page"`
}
