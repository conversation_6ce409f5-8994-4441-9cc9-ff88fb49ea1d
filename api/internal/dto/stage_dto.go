package dto

import (
	"time"
)

type StageOutput struct {
	ID          int                   `json:"id"`
	Name        string                `json:"name"`
	PipelineID  int                   `json:"pipeline_id"`
	Order       int                   `json:"order"`
	Description string                `json:"description,omitempty"`
	UserID      int                   `json:"user_id"`
	CreatedAt   time.Time             `json:"created_at"`
	UpdatedAt   time.Time             `json:"updated_at"`
	DeletedAt   string          `json:"deleted_at,omitempty"`
	Version int             `json:"version"`
	User    UserShortOutput `json:"user"`
}

type CreateStageInput struct {
	Name        string `json:"name"`
	PipelineID  int    `json:"pipeline_id"`
	Order       int    `json:"order"`
	Description string `json:"description,omitempty"`
}

type UpdateStageInput struct {
	ID          int      `json:"id"`
	Name        *string  `json:"name,omitempty"`
	PipelineID  *int     `json:"pipeline_id,omitempty"`
	Order       *int     `json:"order,omitempty"`
	Description *string  `json:"description,omitempty"`
	Modified    []string `json:"modified,omitempty"`
}

type ListStageInput struct {
	PageSize int         `json:"page_size,omitempty"`
	Page    int         `json:"page,omitempty"`
	Filter  StageFilter `json:"filter,omitempty"`
	OrderBy string      `json:"order_by,omitempty"`
	Search   string      `json:"search,omitempty"`
}

type StageFilter struct {
	IDs        []int  `json:"ids,omitempty"`
	Name       string `json:"name,omitempty"`
	PipelineID int    `json:"pipeline_id,omitempty"`
	UserID     int    `json:"user_id,omitempty"`
}

type ListStageOutput struct {
	Stages    []StageOutput `json:"stages"`
	Total     int           `json:"total"`
	TotalPage int           `json:"total_page"`
}
