package dto

type LoginInput struct {
	Username string `json:"username"`
	Password string `json:"password"`
}

type LoginOutput struct {
	User          *UserOutput `json:"user"`
	Status        string      `json:"status"`
	AccessToken   string            `json:"access_token"`
	RefreshToken  string            `json:"refresh_token"`
	AccessExpire  int         `json:"access_expire"`
	RefreshExpire int         `json:"refresh_expire"`
	RefreshAfter  int         `json:"refresh_after"`
	Message       string      `json:"message"`
	SentTo        string      `json:"sent_to"`
	ExpiresIn     int         `json:"expires_in"`
}

type VerifyInput struct {
	Username string `json:"username"`
	OTP      string `json:"otp"`
}

type VerifyOutput struct {
	User          *UserOutput `json:"user"`
	Status        string      `json:"status"`
	AccessToken   string      `json:"access_token"`
	RefreshToken  string      `json:"refresh_token"`
	AccessExpire  int         `json:"access_expire"`
	RefreshExpire int         `json:"refresh_expire"`
	RefreshAfter  int         `json:"refresh_after"`
}

type RefreshTokenInput struct {
	RefreshToken string `json:"refresh_token"`
	ID           int    `json:"id"`
}

type RefreshTokenOutput struct {
	ID            int    `json:"id"`
	AccessToken   string `json:"access_token"`
	RefreshToken  string `json:"refresh_token"`
	AccessExpire  int    `json:"access_expire"`
	RefreshExpire int    `json:"refresh_expire"`
	RefreshAfter  int    `json:"refresh_after"`
}

type CsrfTokenOutput struct {
	Token string `json:"token"`
}
