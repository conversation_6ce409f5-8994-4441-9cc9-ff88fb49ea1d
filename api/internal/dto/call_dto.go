package dto

import "time"

type CallOutput struct {
	ID            int       `json:"id"`
	CallID        string    `json:"call_id"`
	StartTime     time.Time `json:"start_time"`
	Duration      int       `json:"duration"`
	WattingTime   int       `json:"watting_time"`
	Direction     string    `json:"direction"`
	Source        string    `json:"source"`
	Destination   string    `json:"destination"`
	RecordingFile string    `json:"recording_file"`
	UUID          string    `json:"uuid"`
	Kind          string    `json:"kind"`
	Score         float32   `json:"score"`
	PDD           int       `json:"pdd"`
	TTA           int       `json:"tta"`
	Rating        int       `json:"rating"`
	Feedback      string    `json:"feedback"`
	CallStatus    string    `json:"call_status"`
	PersonID      int       `json:"person_id"`
	UserID        int       `json:"user_id"`
	Status        int       `json:"status"`
	CreatedAt     time.Time `json:"created_at"`
	UpdatedAt     time.Time `json:"updated_at"`
	DeletedAt     string    `json:"deleted_at,omitempty"`
	Version       int       `json:"version"`
}

type CreateCallInput struct {
	CallID        string  `json:"call_id"`
	StartTime     string  `json:"start_time"`
	Duration      int     `json:"duration,omitempty"`
	WattingTime   int     `json:"watting_time,omitempty"`
	Direction     string  `json:"direction,omitempty"`
	Source        string  `json:"source,omitempty"`
	Destination   string  `json:"destination,omitempty"`
	RecordingFile string  `json:"recording_file,omitempty"`
	UUID          string  `json:"uuid,omitempty"`
	Kind          string  `json:"kind,omitempty"`
	Score         float32 `json:"score,omitempty"`
	PDD           int     `json:"pdd,omitempty"`
	TTA           int     `json:"tta,omitempty"`
	Rating        int     `json:"rating,omitempty"`
	Feedback      string  `json:"feedback,omitempty"`
	CallStatus    string  `json:"call_status,omitempty"`
	PersonID      int     `json:"person_id"`
	UserID        int     `json:"user_id,omitempty"`
}

type UpdateCallInput struct {
	ID            int      `json:"id"`
	Duration      int      `json:"duration,omitempty"`
	WattingTime   int      `json:"watting_time,omitempty"`
	Direction     string   `json:"direction,omitempty"`
	Source        string   `json:"source,omitempty"`
	Destination   string   `json:"destination,omitempty"`
	RecordingFile string   `json:"recording_file,omitempty"`
	Kind          string   `json:"kind,omitempty"`
	Score         float32  `json:"score,omitempty"`
	PDD           int      `json:"pdd,omitempty"`
	TTA           int      `json:"tta,omitempty"`
	Rating        int      `json:"rating,omitempty"`
	Feedback      string   `json:"feedback,omitempty"`
	CallStatus    string   `json:"call_status,omitempty"`
	PersonID      int      `json:"person_id,omitempty"`
	UserID        int      `json:"user_id,omitempty"`
	Modified      []string `json:"modified,omitempty"`
}

type ListCallsInput struct {
	Filter CallInputFilter `json:"filter,omitempty"`
	Page   int             `json:"page,omitempty"`
	PageSize int             `json:"page_size,omitempty"`
	OrderBy  string          `json:"order_by,omitempty"`
}

type CallInputFilter struct {
	CallID    string `json:"call_id,omitempty"`
	UUID      string `json:"uuid,omitempty"`
	PersonID  int    `json:"person_id,omitempty"`
	UserID    int    `json:"user_id,omitempty"`
	Status    int    `json:"status,omitempty"`
	StartTime string `json:"start_time,omitempty"`
	EndTime   string `json:"end_time,omitempty"`
}

type ListCallsOutput struct {
	Calls []*CallOutput
	Total int
}
