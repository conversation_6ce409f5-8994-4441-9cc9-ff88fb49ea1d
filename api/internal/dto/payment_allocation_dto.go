package dto

type CreatePaymentAllocationInput struct {
	PaymentID     int     `json:"payment_id"`
	BillItemID    int     `json:"bill_item_id"`
	InstallmentID int     `json:"installment_id"`
	Amount        float64 `json:"amount"`
	Note          string  `json:"note"`
}

type UpdatePaymentAllocationInput struct {
	ID            int      `json:"id"`
	PaymentID     int      `json:"payment_id"`
	BillItemID    int      `json:"bill_item_id"`
	InstallmentID int      `json:"installment_id"`
	Amount        float64  `json:"amount"`
	Note          string   `json:"note"`
	State         string   `json:"state"`
	Modified      []string `json:"modified,omitempty"`
}

type PaymentAllocationOutput struct {
	ID            int     `json:"id"`
	PaymentID     int     `json:"payment_id"`
	BillItemID    int     `json:"bill_item_id"`
	InstallmentID int     `json:"installment_id"`
	Amount        float64 `json:"amount"`
	Note          string  `json:"note"`
	State         string  `json:"state"`
	UserID        int     `json:"user_id"`
	CreatedAt     string  `json:"created_at"`
	UpdatedAt     string  `json:"updated_at"`
	DeletedAt     string  `json:"deleted_at"`
	Version       int     `json:"version"`
}

type ListPaymentAllocationInput struct {
	PageSize int                     `json:"page_size,omitempty"`
	Page    int                     `json:"page,omitempty"`
	Filter  PaymentAllocationFilter `json:"filter,omitempty"`
	OrderBy string                  `json:"order_by,omitempty"`
}

type PaymentAllocationFilter struct {
	PaymentID     int    `json:"payment_id,omitempty"`
	BillItemID    int    `json:"bill_item_id,omitempty"`
	InstallmentID int    `json:"installment_id,omitempty"`
	State         string `json:"state,omitempty"`
}

type ListPaymentAllocationOutput struct {
	PaymentAllocations []PaymentAllocationOutput `json:"payment_allocations"`
	Total              int                       `json:"total"`
	TotalPage          int                       `json:"total_page"`
}
