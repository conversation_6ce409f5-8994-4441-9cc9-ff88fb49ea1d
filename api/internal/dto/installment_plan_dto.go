package dto

import "time"

type InstallmentPlanOutput struct {
	ID                   int                 `json:"id"`
	PersonID             int                 `json:"person_id"`
	DealID               int                 `json:"deal_id"`
	UserID               int                 `json:"user_id"`
	BillID               *int                `json:"bill_id"`
	Name                 string              `json:"name"`
	TotalAmount          float64             `json:"total_amount"`
	DownPayment          float64             `json:"down_payment"`
	PaidAmount           float64             `json:"paid_amount"`
	TotalInstallments    int                 `json:"total_installments"`
	DiscountAmount       float64             `json:"discount_amount"`
	PaidInstallmentCount int                 `json:"paid_installment_count"`
	RefundAmount         float64             `json:"refund_amount"`
	State                string              `json:"state"`
	Status               int                 `json:"status"`
	CreatedAt            time.Time           `json:"created_at"`
	UpdatedAt            time.Time           `json:"updated_at"`
	DeletedAt            string              `json:"deleted_at,omitempty"`
	Version              int                 `json:"version"`
	Installments         []InstallmentOutput `json:"installments"`
}

type CreateInstallmentPlanInput struct {
	PersonID          int     `json:"person_id"`
	DealID            int     `json:"deal_id,omitempty"`
	UserID            int     `json:"user_id,omitempty"`
	BillID            *int    `json:"bill_id,omitempty"`
	Name              string  `json:"name,omitempty"`
	State             string  `json:"state,omitempty"`
	TotalAmount       float64 `json:"total_amount,omitempty"`
	DownPayment       float64 `json:"down_payment,omitempty"`
	TotalInstallments int     `json:"total_installments,omitempty"`
}

type UpdateInstallmentPlanInput struct {
	ID                int      `json:"id"`
	Name              string   `json:"name,omitempty"`
	State             string   `json:"state,omitempty"`
	TotalAmount       float64  `json:"total_amount,omitempty"`
	DownPayment       float64  `json:"down_payment,omitempty"`
	PaidAmount        float64  `json:"paid_amount,omitempty"`
	TotalInstallments int      `json:"total_installments,omitempty"`
	PaidInstallments  int      `json:"paid_installments,omitempty"`
	BillID            *int     `json:"bill_id,omitempty"`
	Modified          []string `json:"modified,omitempty"`
}

type ListInstallmentPlansInput struct {
	Filter   InstallmentPlanInputFilter `json:"filter,omitempty"`
	Page     int                        `json:"page,omitempty"`
	PageSize int                        `json:"page_size,omitempty"`
	OrderBy  string                     `json:"order_by,omitempty"`
}

type InstallmentPlanInputFilter struct {
	PersonID int  `json:"person_id,omitempty"`
	DealID   int  `json:"deal_id,omitempty"`
	UserID   int  `json:"user_id,omitempty"`
	BillID   *int `json:"bill_id,omitempty"`
}

type ListInstallmentPlansOutput struct {
	InstallmentPlans []*InstallmentPlanOutput
	Total            int
}
