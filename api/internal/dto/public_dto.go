package dto

type CreateFormInput struct {
	FullName    string                 `json:"full_name"`
	Phone       string                 `json:"phone"`
	Email       string                 `json:"email,omitempty"`
	Data        map[string]interface{} `json:"data,omitempty"`
	SourceURL   string                 `json:"source_url,omitempty"`
	FormName    string                 `json:"form_name,omitempty"`
	ReferrerURL string                 `json:"referrer_url,omitempty"`
	SourceID    int                    `json:"source_id,omitempty"`
}

type FormOutput struct {
	Person *PersonOutput `json:"person"`
}
