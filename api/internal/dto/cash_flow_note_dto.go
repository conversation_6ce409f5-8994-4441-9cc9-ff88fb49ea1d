package dto

import "time"

type CashFlowNoteOutput struct {
	ID         int             `json:"id"`
	CashFlowID int             `json:"cash_flow_id"`
	Body       string          `json:"body"`
	UserID     int             `json:"user_id"`
	CreatedAt  time.Time       `json:"created_at"`
	UpdatedAt  time.Time       `json:"updated_at"`
	DeletedAt  *time.Time      `json:"deleted_at,omitempty"`
	Version    int             `json:"version"`
	User       UserShortOutput `json:"creator"`
}

type CreateCashFlowNoteInput struct {
	CashFlowID int    `json:"cash_flow_id"`
	Body       string `json:"body"`
}

type UpdateCashFlowNoteInput struct {
	ID         int      `json:"id"`
	CashFlowID int      `json:"cash_flow_id"`
	Body       string   `json:"body"`
	Modified   []string `json:"modified,omitempty"`
}

type ListCashFlowNoteInput struct {
	PageSize int                `json:"page_size,omitempty"`
	Page     int                `json:"page,omitempty"`
	Filter   CashFlowNoteFilter `json:"filter,omitempty"`
	OrderBy  string             `json:"order_by,omitempty"`
}

type CashFlowNoteFilter struct {
	CashFlowID int `json:"cash_flow_id,omitempty"`
	UserID     int `json:"user_id,omitempty"`
}

type ListCashFlowNoteOutput struct {
	CashFlowNotes []CashFlowNoteOutput `json:"cash_flow_notes"`
	Total         int                  `json:"total"`
	TotalPage     int                  `json:"total_page"`
}
