package dto

import "time"

type ProductOperationOutput struct {
	ID            int       `json:"id"`
	OrderSequence int       `json:"order_sequence,omitempty"`
	ProductID     int       `json:"product_id"`
	OperationID   int       `json:"operation_id"`
	CreatedAt     time.Time `json:"created_at"`
	UpdatedAt     time.Time `json:"updated_at"`
	DeletedAt     string    `json:"deleted_at,omitempty"`
}

type CreateProductOperationInput struct {
	OrderSequence int `json:"order_sequence,omitempty"`
	ProductID     int `json:"product_id"`
	OperationID   int `json:"operation_id"`
}

type UpdateProductOperationInput struct {
	ID            int      `json:"id"`
	OrderSequence *int     `json:"order_sequence,omitempty"`
	ProductID     *int     `json:"product_id,omitempty"`
	OperationID   *int     `json:"operation_id,omitempty"`
	Modified      []string `json:"modified,omitempty"`
}

type ListProductOperationsInput struct {
	Filter   ProductOperationInputFilter `json:"filter,omitempty"`
	Page     int                         `json:"page,omitempty"`
	PageSize int                         `json:"page_size,omitempty"`
	OrderBy  string                      `json:"order_by,omitempty"`
}

type ProductOperationInputFilter struct {
	ProductID   int `json:"product_id,omitempty"`
	OperationID int `json:"operation_id,omitempty"`
}

type ListProductOperationsOutput struct {
	ProductOperations []*ProductOperationOutput `json:"product_operations"`
	Total             int                       `json:"total"`
	TotalPage         int                       `json:"total_page"`
}

type BulkSetProductOperationInput struct {
	OperationID int   `json:"operation_id"`
	ProductIDs  []int `json:"product_ids,omitempty"`
}
