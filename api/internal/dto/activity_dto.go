package dto

type ActivityListInput struct {
	PersonID int `json:"person_id"`
}

type ActivityListOutput struct {
	Activities []ActivityRecord `json:"activities"`
}

type ActivityRecord struct {
	Type string      `json:"type"`
	Data interface{} `json:"data"`
}

type Activity struct {
	EntityID  int                    `json:"entity_id"`
	Kind      string                 `json:"kind"`
	Data      map[string]interface{} `json:"record"`
	PersonID  int                    `json:"person_id"`
	CreatedAt string                 `json:"created_at"`
}

type ActivityDynamicQuery struct {
	DynamicQuery
}
