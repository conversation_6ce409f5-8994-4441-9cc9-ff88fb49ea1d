package dto

type CreateDealUserInput struct {
	DealID int    `json:"deal_id"`
	UserID int    `json:"user_id"`
	Role   string `json:"role"`
}

type UpdateDealUserInput struct {
	ID     int                    `json:"id"`
	UserID *int                   `json:"user_id,omitempty"`
	Point  map[string]interface{} `json:"point,omitempty"`
}

type ListDealUserInput struct {
	DealID int `json:"deal_id"`
}

type DeleteDealUserInput struct {
	DealID int    `json:"deal_id"`
	UserID int    `json:"user_id"`
	Role   string `json:"role"`
}

type DealUserOutput struct {
	ID     int                    `json:"id"`
	DealID int                    `json:"deal_id"`
	UserID int                    `json:"user_id"`
	Role   string                 `json:"role"`
	Point  map[string]interface{} `json:"point"`
	Name   string           `json:"name"`
	User   *UserShortOutput `json:"user"`
	Rating []DealUserInfo   `json:"ratings"`
}

type ListDealUserOutput struct {
	DealUsers []DealUserOutput `json:"deal_users"`
}

type DealUserInfo struct {
	ID           int64                  `json:"id,omitempty"`
	Username     string                 `json:"username"`
	Email        string                 `json:"email,omitempty"`
	Name         string                 `json:"name,omitempty"`
	DepartmentID int64                  `json:"department_id,omitempty"`
	Role         string                 `json:"role,omitempty"`
	Point        map[string]interface{} `json:"point,omitempty"`
}
