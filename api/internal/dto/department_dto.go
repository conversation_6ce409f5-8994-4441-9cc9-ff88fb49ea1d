package dto

import "time"

type DepartmentOutput struct {
	ID          int       `json:"id"`
	Name        string    `json:"name"`
	Description string    `json:"description"`
	Status      int       `json:"status"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	DeletedAt   string    `json:"deleted_at,omitempty"`
	Version     int       `json:"version"`
}

type CreateDepartmentInput struct {
	Name        string `json:"name"`
	Description string `json:"description,omitempty"`
}

type UpdateDepartmentInput struct {
	ID          int      `json:"id"`
	Name        string   `json:"name,omitempty"`
	Description string   `json:"description,omitempty"`
	Modified    []string `json:"modified,omitempty"`
}

type ListDepartmentsInput struct {
	Filter DepartmentInputFilter `json:"filter,omitempty"`
	Page   int                   `json:"page,omitempty"`
	PageSize int                   `json:"page_size,omitempty"`
	OrderBy  string                `json:"order_by,omitempty"`
}

type DepartmentInputFilter struct {
	Name   string `json:"name,omitempty"`
	Status int    `json:"status,omitempty"`
}

type ListDepartmentsOutput struct {
	Departments []*DepartmentOutput
	Total       int
}
