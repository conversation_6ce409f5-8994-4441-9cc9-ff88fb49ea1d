package dto

type Setting struct {
	ID          int                    `json:"id"`
	Category    string                 `json:"category"`
	Name        string                 `json:"name"`
	Value       map[string]interface{} `json:"value"`
	Description string                 `json:"description"`
}

type CreateSettingInput struct {
	Category    string                 `json:"category"`
	Name        string                 `json:"name"`
	Value       map[string]interface{} `json:"value"`
	Description string                 `json:"description"`
}

type UpdateSettingInput struct {
	ID          int                    `json:"id"`
	Category    string                 `json:"category,omitempty"`
	Name        string                 `json:"name,omitempty"`
	Value       map[string]interface{} `json:"value,omitempty"`
	Description string                 `json:"description,omitempty"`
	Modified    []string               `json:"modified,omitempty"`
}

type SyncSettingInput struct {
	Category    string                 `json:"category"`
	Name        string                 `json:"name"`
	Value       map[string]interface{} `json:"value"`
	Description string                 `json:"description,omitempty"`
}

type DeleteSettingInput struct {
	ID int `json:"id"`
}

type ListSettingInput struct {
	Category string `json:"category,omitempty"`
	Name     string `json:"name,omitempty"`
}

type ListSettingOutput struct {
	Settings []*Setting `json:"settings"`
}

type CallSetting struct {
	ApiKey    string `json:"api_key"`
	ApiSecret string `json:"api_secret"`
	ApiUrl    string `json:"api_url"`
	Uri       string `json:"uri"`
	UriWs     string `json:"uri_ws"`
	LineId    string `json:"line_id"`
	Password  string `json:"password"`
}

type SmsSetting struct {
	SmsApiUrl    string `json:"sms_api_url"`
	SmsUsername  string `json:"sms_username"`
	SmsPassword  string `json:"sms_password"`
	SmsBrandName string `json:"sms_brand_name"`
}

type ZnsSetting struct {
	SecretKey       string `json:"secret_key"`
	Appid           string `json:"app_id"`
	UrlTemplate     string `json:"url_template"`
	UrlRefreshToken string `json:"url_refresh_token"`
	AccessToken     string `json:"access_token"`
	RefreshToken    string `json:"refresh_token"`
}

type PaymentTemplate struct {
	Content string            `json:"content"`
	Name    string            `json:"name"`
	Params  map[string]string `json:"params"`
	ZnsID   string            `json:"zns_id,omitempty"`
}

type PaymentTemplateGroup struct {
	Zns PaymentTemplate `json:"zns"`
	Sms PaymentTemplate `json:"sms"`
}

type PaymentMessageTemplates struct {
	WithSchedule    PaymentTemplateGroup `json:"with_schedule"`
	WithoutSchedule PaymentTemplateGroup `json:"without_schedule"`
}
