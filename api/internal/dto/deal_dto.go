package dto

import (
	"time"
)

type CreateDealInput struct {
	PersonID     int      `json:"person_id"`
	ParentDealID *int     `json:"parent_deal_id,omitempty"`
	Name         *string  `json:"name,omitempty"`
	TotalAmount  *float64 `json:"total_amount,omitempty"`
	StageID      *int     `json:"stage_id,omitempty"`
	State        *string  `json:"state,omitempty"`
	Status       int8     `json:"status"`
}

type UpdateDealInput struct {
	ID           int      `json:"id"`
	ParentDealID *int     `json:"parent_deal_id,omitempty"`
	StageID      *int     `json:"stage_id,omitempty"`
	Status       *int8    `json:"status,omitempty"`
	Name         *string  `json:"name,omitempty"`
	State        *string  `json:"state,omitempty"`
	TotalAmount  *float64 `json:"total_amount,omitempty"`
	Modified     []string `json:"modified,omitempty"`
	Discounts    []int    `json:"discounts,omitempty"`
}

type ListDealInput struct {
	PageSize        int        `json:"page_size,omitempty"`
	Page            int        `json:"page,omitempty"`
	Filter          DealFilter `json:"filter,omitempty"`
	PipelineID      int        `json:"pipeline_id,omitempty"`
	DoctorID        int        `json:"doctor_id,omitempty"`
	OrderBy         string     `json:"order_by,omitempty"`
	IncludeRelation bool       `json:"include_relation,omitempty"`
}

type DealFilter struct {
	PersonID   *int    `json:"person_id,omitempty"`
	StageID    *int    `json:"stage_id,omitempty"`
	PipelineID *int    `json:"pipeline_id,omitempty"`
	Status     *int8   `json:"status,omitempty"`
	State      *string `json:"state,omitempty"`
}

type DealOutput struct {
	ID                        int                      `json:"id"`
	PersonID                  int                      `json:"person_id"`
	ParentDealID              *int                     `json:"parent_deal_id"`
	Name                      string                   `json:"name"`
	State                     string                   `json:"state"`
	TotalAmount               float64                  `json:"total_amount"`
	TotalPlanAmount           float64                  `json:"total_plan_amount"`
	PaidAmount                float64                  `json:"paid_amount"`
	DiscountAmount            float64                  `json:"discount_amount"`
	PaidInstallmentCount      int                      `json:"paid_installment_count"`
	RefundAmount              float64                  `json:"refund_amount"`
	DepositAmount             float64                  `json:"deposit_amount"`
	DownPayment               float64                  `json:"down_payment"`
	DownPaymentReceivedAmount float64                  `json:"down_payment_received_amount"`
	StageID                   int                      `json:"stage_id"`
	StageHistory              []StageHistoryEntry      `json:"stage_history"`
	Status                    int8                     `json:"status"`
	CreatedAt                 time.Time                `json:"created_at"`
	UpdatedAt                 time.Time                `json:"updated_at"`
	DeletedAt                 string                   `json:"deleted_at"`
	Version                   int                      `json:"version"`
	Tags                      []TagInfoOutput          `json:"tags"`
	Person                    *PersonOutput            `json:"person"`
	Assignments               []DealUserOutput         `json:"deal_assignment"`
	Tracks                    []TrackOutput            `json:"tracks"`
	Attachments               []AttachmentOutput       `json:"attachments"`
	Plans                     []InstallmentPlanOutput  `json:"plans"`
	DiscountUsages            []DiscountUsageOutput    `json:"discount_usages"`
	EligibleDiscounts         []EligibleDiscountOutput `json:"eligible_discounts"`
}

type StageHistoryEntry struct {
	StageID   int       `json:"stage_id"`
	EnteredAt time.Time `json:"entered_at"`
	ExitedAt  string    `json:"exited_at,omitempty"`
	UserID    *int      `json:"user_id,omitempty"`
	Reason    *string   `json:"reason,omitempty"`
}

type ListDealOutput struct {
	Deals     []DealOutput `json:"deals"`
	Total     int          `json:"total"`
	TotalPage int          `json:"total_page"`
}

type GetDealInput struct {
	ID              int  `json:"id"`
	IncludeRelation bool `json:"include_relation,omitempty"`
}
