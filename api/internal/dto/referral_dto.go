package dto

import "time"

type ReferralOutput struct {
	ID                   int       `json:"id"`
	ReferredPersonID     int       `json:"referred_person_id"`
	Notes                string    `json:"notes,omitempty"`
	EntityType           string    `json:"entity_type,omitempty"`
	EntityID             int       `json:"entity_id,omitempty"`
	ReferrerRelationship string    `json:"referrer_relationship,omitempty"`
	CreatedAt            time.Time `json:"created_at"`
	UpdatedAt            time.Time `json:"updated_at"`
	DeletedAt            string    `json:"deleted_at,omitempty"`
}

type CreateReferralInput struct {
	ReferredPersonID     int    `json:"referred_person_id"`
	Notes                string `json:"notes,omitempty"`
	EntityType           string `json:"entity_type,omitempty"`
	EntityID             int    `json:"entity_id,omitempty"`
	ReferrerRelationship string `json:"referrer_relationship,omitempty"`
}

type UpdateReferralInput struct {
	ID                   int      `json:"id"`
	Notes                *string  `json:"notes,omitempty"`
	EntityType           *string  `json:"entity_type,omitempty"`
	EntityID             *int     `json:"entity_id,omitempty"`
	ReferrerRelationship *string  `json:"referrer_relationship,omitempty"`
	Modified             []string `json:"modified,omitempty"`
}

type ListReferralsInput struct {
	Filter ReferralInputFilter `json:"filter,omitempty"`
	Page   int                 `json:"page,omitempty"`
	PageSize int                 `json:"page_size,omitempty"`
	OrderBy  string              `json:"order_by,omitempty"`
}

type ReferralInputFilter struct {
	ReferredPersonID     int    `json:"referred_person_id,omitempty"`
	EntityType           string `json:"entity_type,omitempty"`
	EntityID             int    `json:"entity_id,omitempty"`
	ReferrerRelationship string `json:"referrer_relationship,omitempty"`
}

type ListReferralsOutput struct {
	Referrals []*ReferralOutput `json:"referrals"`
	Total     int               `json:"total"`
	TotalPage int               `json:"total_page"`
}
