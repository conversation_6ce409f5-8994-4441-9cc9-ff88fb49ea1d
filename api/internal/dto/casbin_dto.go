package dto

import "time"

type CasbinRuleOutput struct {
	ID        int       `json:"id"`
	Ptype     string    `json:"ptype"`
	V0        string    `json:"v0"`
	V1        string    `json:"v1"`
	V2        string    `json:"v2"`
	V3        string    `json:"v3"`
	V4        string    `json:"v4"`
	V5        string    `json:"v5"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	DeletedAt string    `json:"deleted_at,omitempty"`
}

type CreateCasbinRuleInput struct {
	Ptype string `json:"ptype"`
	V0    string `json:"v0"`
	V1    string `json:"v1"`
	V2    string `json:"v2"`
	V3    string `json:"v3"`
	V4    string `json:"v4"`
	V5    string `json:"v5"`
}

type UpdateCasbinRuleInput struct {
	ID       int      `json:"id"`
	Ptype    *string  `json:"ptype,omitempty"`
	V0       *string  `json:"v0,omitempty"`
	V1       *string  `json:"v1,omitempty"`
	V2       *string  `json:"v2,omitempty"`
	V3       *string  `json:"v3,omitempty"`
	V4       *string  `json:"v4,omitempty"`
	V5       *string  `json:"v5,omitempty"`
	Modified []string `json:"modified,omitempty"`
}

type ListCasbinRulesInput struct {
	Filter CasbinRuleInputFilter `json:"filter,omitempty"`
	Page   int                   `json:"page,omitempty"`
	PageSize int                   `json:"page_size,omitempty"`
	OrderBy  string                `json:"order_by,omitempty"`
}

type CasbinRuleInputFilter struct {
	Ptype string `json:"ptype,omitempty"`
	V0    string `json:"v0,omitempty"`
	V1    string `json:"v1,omitempty"`
	V2    string `json:"v2,omitempty"`
	V3    string `json:"v3,omitempty"`
	V4    string `json:"v4,omitempty"`
	V5    string `json:"v5,omitempty"`
}

type ListCasbinRulesOutput struct {
	Rules     []*CasbinRuleOutput `json:"rules"`
	Total     int                 `json:"total"`
	TotalPage int                 `json:"total_page"`
}
