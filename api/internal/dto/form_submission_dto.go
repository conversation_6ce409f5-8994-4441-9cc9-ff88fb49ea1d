package dto

import (
	"time"
)

// FormSubmissionOutput represents the output for form submission data
type FormSubmissionOutput struct {
	ID          int                        `json:"id"`
	FullName    string                     `json:"full_name"`
	Phone       string                     `json:"phone"`
	Email       *string                    `json:"email,omitempty"`
	Data        map[string]interface{}     `json:"data"`
	SourceURL   string                     `json:"source_url"`
	ReferrerURL *string                    `json:"referrer_url,omitempty"`
	SourceID    int                    `json:"source_id"`
	FormName    string                 `json:"form_name"`
	State       string                 `json:"state"` // pending, approved, rejected, processing
	ProcessedAt string               `json:"processed_at,omitempty"`
	History     []RecordHistoryEntry `json:"history,omitempty"`
	PersonID    *int                 `json:"person_id,omitempty"`
	Status      int                    `json:"status"`
	CreatedAt   time.Time              `json:"created_at"`
	UpdatedAt   time.Time              `json:"updated_at"`
	DeletedAt   string                 `json:"deleted_at,omitempty"`
}

// FormSubmissionResponse represents the response for form submission data
type FormSubmissionResponse struct {
	FormSubmissionOutput
	Person *PersonOutput `json:"person,omitempty"`
}

// ListFormSubmissionInput represents the input for listing form submissions
type ListFormSubmissionInput struct {
	PageSize int                  `json:"page_size,omitempty"`
	Page    int                  `json:"page,omitempty"`
	Filter  FormSubmissionFilter `json:"filter,omitempty"`
	OrderBy string               `json:"order_by,omitempty"`
}

// FormSubmissionFilter represents the filter criteria for listing form submissions
type FormSubmissionFilter struct {
	PersonID *int `json:"person_id,omitempty"`
}

// ListFormSubmissionOutput represents the output for listing form submissions
type ListFormSubmissionOutput struct {
	Total           int                      `json:"total"`
	TotalPage       int                      `json:"total_page"`
	FormSubmissions []FormSubmissionResponse `json:"form_submissions"`
}

// ConvertToPersonInput represents the input for converting form submission to person
type ConvertToPersonInput struct {
	FormSubmissionIDs []int `json:"form_submission_ids"`
}

// ConvertToPersonOutput represents the output for converting form submission to person
type ConvertToPersonOutput struct {
	Success        bool                 `json:"success"`
	FormSubmission FormSubmissionOutput `json:"form_submission"`
}

// DeleteFormSubmissionInput represents the input for deleting form submissions
type DeleteFormSubmissionInput struct {
	IDs []int `json:"ids"`
}

// DeleteFormSubmissionOutput represents the output for deleting form submissions
type DeleteFormSubmissionOutput struct {
	Success bool `json:"success"`
}

// FormSubmissionError represents an error in form submission processing
type FormSubmissionError struct {
	FormSubmissionID int    `json:"form_submission_id"`
	Error            string `json:"error"`
}

// BulkUpdateFormSubmissionOutput represents the output for bulk updating form submissions
type BulkUpdateFormSubmissionOutput struct {
	SuccessCount int                   `json:"success_count"`
	FailCount    int                   `json:"fail_count"`
	Errors       []FormSubmissionError `json:"errors"`
}
