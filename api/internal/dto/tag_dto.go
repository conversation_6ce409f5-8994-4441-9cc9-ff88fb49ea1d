package dto

import "time"

type TagOutput struct {
	ID          int       `json:"id"`
	Name        string    `json:"name"`
	Category    string    `json:"category"`
	Description string    `json:"description"`
	Color       string    `json:"color"`
	UserID      int       `json:"user_id"`
	Status      int       `json:"status"`
	Version     int       `json:"version"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	DeletedAt   string    `json:"deleted_at,omitempty"`
}

type CreateTagInput struct {
	Name        string `json:"name"`
	Category    string `json:"category"`
	Description string `json:"description,omitempty"`
	Color       string `json:"color"`
}

type UpdateTagInput struct {
	ID          int      `json:"id"`
	Name        string   `json:"name,omitempty"`
	Category    string   `json:"category,omitempty"`
	Description string   `json:"description,omitempty"`
	Color       string   `json:"color,omitempty"`
	Status      *int8    `json:"status,omitempty"`
	Modified    []string `json:"modified,omitempty"`
}

type ListTagsInput struct {
	Filter   *TagFilter `json:"filter,omitempty"`
	Page     int        `json:"page,omitempty"`
	PageSize int        `json:"page_size,omitempty"`
	OrderBy  string     `json:"order_by,omitempty"`
	Search   string     `json:"search,omitempty"`
}

type TagFilter struct {
	Ids      []int  `json:"ids,omitempty"`
	Name     string `json:"name,omitempty"`
	Category string `json:"category,omitempty"`
	Status   int8   `json:"status,range=[0:10],omitempty"`
	UserId   int    `json:"user_id,omitempty"`
}

type ListTagsOutput struct {
	Tags  []*TagOutput
	Total int
}

type TagInfoOutput struct {
	ID   int    `json:"id"`
	Name string `json:"name"`
}
