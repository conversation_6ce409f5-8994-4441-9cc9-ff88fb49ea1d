package dto

import (
	"time"
)

type TaskOutput struct {
	ID                    int                    `json:"id"`
	Title                 string                 `json:"title"`
	Note                  string                 `json:"note,omitempty"`
	StartDate             time.Time              `json:"start_date,omitempty"`
	DueDate               *time.Time             `json:"due_date,omitempty"`
	EndDate               *time.Time             `json:"end_date,omitempty"`
	Type                  string                 `json:"type,omitempty"`
	Priority              int                    `json:"priority,omitempty"`
	CurrentSerial         int                    `json:"current_serial"`
	ParentID              *int                   `json:"parent_id,omitempty"`
	PersonID              *int                   `json:"person_id,omitempty"`
	DealID                *int                   `json:"deal_id,omitempty"`
	AppointmentID         *int                   `json:"appointment_id,omitempty"`
	DepartmentID          *int                   `json:"department_id,omitempty"`
	CreatorID             int                    `json:"creator_id"`
	State                 string                 `json:"state"`
	CompletedAt           *time.Time             `json:"completed_at,omitempty"`
	History               []HistoryEntry         `json:"history,omitempty"`
	UserID                int                    `json:"user_id"`
	CreatedAt             time.Time              `json:"created_at"`
	UpdatedAt             time.Time              `json:"updated_at"`
	DeletedAt             string                 `json:"deleted_at,omitempty"`
	Version               int                    `json:"version"`
	Creator               UserShortOutput        `json:"creator"`
	Assignments           []TaskAssignmentOutput `json:"assignments,omitempty"`
	DepartmentAssignments []TaskDepartmentOutput `json:"department_assignments,omitempty"`
	Notes                 []TaskNoteOutput       `json:"notes,omitempty"`
	Recurring             *TaskRecurringOutput   `json:"recurring,omitempty"`
	Person                *PersonOutput          `json:"person,omitempty"`
}

type CreateTaskInput struct {
	Title          string                      `json:"title"`
	Note           string                      `json:"note"`
	StartDate      time.Time                   `json:"start_date,optional"`
	DueDate        *time.Time                  `json:"due_date,optional"`
	EndDate        *time.Time                  `json:"end_date,optional"`
	Type           string                      `json:"type,optional"`
	Priority       int                         `json:"priority,optional"`
	ParentID       *int                        `json:"parent_id,optional"`
	PersonID       *int                        `json:"person_id,optional"`
	DealID         *int                        `json:"deal_id,optional"`
	AppointmentID  *int                        `json:"appointment_id,optional"`
	DepartmentID   *int                        `json:"department_id,optional"`
	State          string                      `json:"state"`
	Users          []CreateTaskAssignmentInput `json:"users,optional"`
	Departments    []CreateTaskDepartmentInput `json:"departments,optional"`
	CronExpression *string                     `json:"cron_expression,optional"`
}

type UpdateTaskInput struct {
	ID            int                         `json:"id"`
	Title         *string                     `json:"title,omitempty"`
	Note          *string                     `json:"note,omitempty"`
	StartDate     *time.Time                  `json:"start_date,omitempty"`
	DueDate       *time.Time                  `json:"due_date,omitempty"`
	EndDate       *time.Time                  `json:"end_date,omitempty"`
	Type          *string                     `json:"type,omitempty"`
	Priority      *int                        `json:"priority,omitempty"`
	ParentID      *int                        `json:"parent_id,omitempty"`
	PersonID      *int                        `json:"person_id,omitempty"`
	DealID        *int                        `json:"deal_id,omitempty"`
	AppointmentID *int                        `json:"appointment_id,omitempty"`
	DepartmentID  *int                        `json:"department_id,omitempty"`
	State         *string                     `json:"state,omitempty"`
	Users         []CreateTaskAssignmentInput `json:"users,omitempty"`
	Departments   []CreateTaskDepartmentInput `json:"departments,omitempty"`
	Modified      []string                    `json:"modified,omitempty"`
}

type ListTaskInput struct {
	PageSize int        `json:"page_size,omitempty"`
	Page     int        `json:"page,omitempty"`
	Filter   TaskFilter `json:"filter,omitempty"`
	OrderBy  string     `json:"order_by,omitempty"`
	Search   string     `json:"search,omitempty"`
}

type TaskFilter struct {
	IDs             []int      `json:"ids,omitempty"`
	Title           *string    `json:"title,omitempty"`
	Type            *string    `json:"type,omitempty"`
	Priority        *int       `json:"priority,omitempty"`
	ParentID        *int       `json:"parent_id,omitempty"`
	PersonID        *int       `json:"person_id,omitempty"`
	DealID          *int       `json:"deal_id,omitempty"`
	AppointmentID   *int       `json:"appointment_id,omitempty"`
	DepartmentID    *int       `json:"department_id,omitempty"`
	CreatorID       *int       `json:"creator_id,omitempty"`
	State           *string    `json:"state,omitempty"`
	StartDateFrom   *time.Time `json:"start_date_from,omitempty"`
	StartDateTo     *time.Time `json:"start_date_to,omitempty"`
	DueDateFrom     *time.Time `json:"due_date_from,omitempty"`
	DueDateTo       *time.Time `json:"due_date_to,omitempty"`
	CompletedAtFrom *time.Time `json:"completed_at_from,omitempty"`
	CompletedAtTo   *time.Time `json:"completed_at_to,omitempty"`
}

type ListTaskOutput struct {
	Tasks     []TaskOutput `json:"tasks"`
	Total     int          `json:"total"`
	TotalPage int          `json:"total_page"`
}

type TaskRecurringOutput struct {
	ID             int        `json:"id"`
	CronExpression string     `json:"cron_expression"`
	NextOccurrence *time.Time `json:"next_occurrence"`
	LastOccurrence *time.Time `json:"last_occurrence"`
}

type GetTaskInput struct {
	ID     int `json:"id"`
	Serial int `json:"serial,omitempty"`
}

type DeleteTaskInput struct {
	ID int `json:"id"`
}

type BulkUpdateResult struct {
	SuccessCount int               `json:"success_count"`
	FailCount    int               `json:"fail_count"`
	Errors       []TaskUpdateError `json:"errors,omitempty"`
}

type TaskUpdateError struct {
	TaskID int    `json:"task_id"`
	Error  string `json:"error"`
}

type TaskIsLastOutput struct {
	IsLast bool `json:"is_last"`
}
