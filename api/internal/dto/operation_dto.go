package dto

import "time"

type OperationOutput struct {
	ID                int                       `json:"id"`
	Name              string                    `json:"name"`
	Description       string                    `json:"description,omitempty"`
	Group             []string                  `json:"group"`
	Duration          int                       `json:"duration"`
	Status            int                       `json:"status"`
	Version           int                       `json:"version"`
	CreatedAt         time.Time                 `json:"created_at"`
	UpdatedAt         time.Time                 `json:"updated_at"`
	DeletedAt         string                    `json:"deleted_at,omitempty"`
	ProductOperation  []ProductOperationOutput  `json:"product_operation,omitempty"`
	OperationMaterial []OperationMaterialOutput `json:"operation_materials,omitempty"`
}

type CreateOperationInput struct {
	Name        string   `json:"name"`
	Description string   `json:"description,omitempty"`
	Group       []string `json:"group"`
	Duration    int      `json:"duration"`
}

type UpdateOperationInput struct {
	ID          int      `json:"id"`
	Name        string   `json:"name,omitempty"`
	Description string   `json:"description,omitempty"`
	Group       []string `json:"group,omitempty"`
	Duration    *int     `json:"duration,omitempty"`
	Status      *int8    `json:"status,omitempty"`
	Modified    []string `json:"modified,omitempty"`
}

type ListOperationsInput struct {
	Filter   OperationInputFilter `json:"filter,omitempty"`
	Page     int                  `json:"page,omitempty"`
	PageSize int                  `json:"page_size,omitempty"`
	OrderBy  string               `json:"order_by,omitempty"`
	Search   string               `json:"search,omitempty"`
}

type OperationInputFilter struct {
	Name   string `json:"name,omitempty"`
	Group  string `json:"group,omitempty"`
	Status int8   `json:"status,range=[0:10],omitempty"`
}

type ListOperationsOutput struct {
	Operations []*OperationOutput `json:"operations"`
	Total      int                `json:"total"`
}

// Note: OperationGetRequest and OperationDeleteRequest from operation.api are simple ID requests.
// These are typically handled directly in the model/handler and may not need separate DTOs if they only involve an ID.
// OperationResponse and OperationAllResponse are mapped to OperationOutput and ListOperationsOutput respectively.
