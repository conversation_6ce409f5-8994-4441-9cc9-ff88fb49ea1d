package dto

import (
	"time"
)

type DepositOutput struct {
	ID              int       `json:"id"`
	DealID          int       `json:"deal_id,omitempty"`
	TotalAmount     float64   `json:"total_amount"`
	PaidAmount      float64   `json:"paid_amount"`
	RemainingAmount float64   `json:"remaining_amount"`
	State           string    `json:"state"`
	Description     string    `json:"description,omitempty"`
	CreatedBy       int       `json:"created_by,omitempty"`
	UpdatedBy       int       `json:"updated_by,omitempty"`
	CreatedAt       time.Time `json:"created_at"`
	UpdatedAt       time.Time `json:"updated_at"`
	DeletedAt       string    `json:"deleted_at,omitempty"`
}

type CreateDepositInput struct {
	DealID      int                            `json:"deal_id,omitempty"`
	TotalAmount float64                        `json:"total_amount"`
	Description string                         `json:"description,omitempty"`
	Allocations []CreatePaymentAllocationInput `json:"allocations,omitempty"`
}

type UpdateDepositInput struct {
	ID          int                            `json:"id"`
	TotalAmount *float64                       `json:"total_amount,omitempty"`
	State       *string                        `json:"state,omitempty"`
	Description *string                        `json:"description,omitempty"`
	Allocations []CreatePaymentAllocationInput `json:"allocations,omitempty"`
	Modified    []string                       `json:"modified,omitempty"`
}

type ConvertToPaymentInput struct {
	DepositID int     `json:"deposit_id"`
	PaymentID int     `json:"payment_id"`
	Amount    float64 `json:"amount"`
	Note      string  `json:"note,omitempty"`
}

type ListDepositsInput struct {
	Filter DepositInputFilter `json:"filter,omitempty"`
	Page   int                `json:"page,omitempty"`
	PageSize int                `json:"page_size,omitempty"`
	OrderBy  string             `json:"order_by,omitempty"`
}

type DepositInputFilter struct {
	DealID    int    `json:"deal_id,omitempty"`
	State     string `json:"state,omitempty"`
	CreatedBy int    `json:"created_by,omitempty"`
}

type ListDepositsOutput struct {
	Deposits  []*DepositOutput `json:"deposits"`
	Total     int              `json:"total"`
	TotalPage int              `json:"total_page"`
}

type DepositAllocationOutput struct {
	ID           int       `json:"id"`
	DepositID    int       `json:"deposit_id"`
	AttachmentID int       `json:"attachment_id,omitempty"`
	DealID       int       `json:"deal_id,omitempty"`
	Amount       float64   `json:"amount"`
	State        string    `json:"state"`
	CreatedBy    int       `json:"created_by,omitempty"`
	UpdatedBy    int       `json:"updated_by,omitempty"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
	DeletedAt    string    `json:"deleted_at,omitempty"`
}

type DepositPaymentOutput struct {
	ID             int       `json:"id"`
	DepositID      int       `json:"deposit_id"`
	PaymentID      int       `json:"payment_id"`
	Amount         float64   `json:"amount"`
	ConversionDate time.Time `json:"conversion_date"`
	State          string    `json:"state"`
	Note           string    `json:"note,omitempty"`
	CreatedBy      int       `json:"created_by,omitempty"`
	UpdatedBy      int       `json:"updated_by,omitempty"`
	CreatedAt      time.Time `json:"created_at"`
	UpdatedAt      time.Time `json:"updated_at"`
	DeletedAt      string    `json:"deleted_at,omitempty"`
}
