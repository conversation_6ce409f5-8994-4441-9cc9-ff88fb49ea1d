package dto

import "time"

type CreateDiscountInput struct {
	Name        string  `json:"name"`
	Type        string  `json:"type"`
	Value       float64 `json:"value"`
	Scope       string  `json:"scope"`
	Condition   string  `json:"condition"`
	UsageType   string  `json:"usage_type"`
	Description string  `json:"description"`
	Meta        string  `json:"meta"`
	Start       string  `json:"start"`
	End         string  `json:"end"`
}

type UpdateDiscountInput struct {
	ID          int      `json:"id"`
	Name        *string  `json:"name,omitempty"`
	Type        *string  `json:"type,omitempty"`
	Value       *float64 `json:"value,omitempty"`
	Scope       *string  `json:"scope,omitempty"`
	Condition   *string  `json:"condition,omitempty"`
	UsageType   *string  `json:"usage_type,omitempty"`
	Description *string  `json:"description,omitempty"`
	Meta        *string  `json:"meta,omitempty"`
	Start       *string  `json:"start,omitempty"`
	End         *string  `json:"end,omitempty"`
	Status      *int     `json:"status,omitempty"`
	Modified    []string `json:"modified,omitempty"`
}

type ListDiscountInput struct {
	PageSize int            `json:"page_size,omitempty"`
	Page     int            `json:"page,omitempty"`
	Filter   DiscountFilter `json:"filter,omitempty"`
	OrderBy  string         `json:"order_by,omitempty"`
}

type DiscountFilter struct {
	Name      *string `json:"name,omitempty"`
	Type      *string `json:"type,omitempty"`
	Scope     *string `json:"scope,omitempty"`
	UsageType *string `json:"usage_type,omitempty"`
	Status    *int    `json:"status,omitempty"`
}

type DiscountOutput struct {
	ID          int                    `json:"id"`
	Name        string                 `json:"name"`
	Type        string                 `json:"type"`
	Value       float64                `json:"value"`
	Scope       string                 `json:"scope"`
	Condition   string                 `json:"condition"`
	UsageType   string                 `json:"usage_type"`
	Description string                 `json:"description"`
	Meta        map[string]interface{} `json:"meta"`
	Start       string                 `json:"start"`
	End         string                 `json:"end"`
	Status      int                    `json:"status"`
	CreatedAt   time.Time              `json:"created_at"`
	UpdatedAt   time.Time              `json:"updated_at"`
	DeletedAt   string                 `json:"deleted_at"`
	Version     int                    `json:"version"`
}

type ListDiscountOutput struct {
	Discounts []DiscountOutput `json:"discounts"`
	Total     int              `json:"total"`
	TotalPage int              `json:"total_page"`
}

type EligibleDiscountInput struct {
	PersonID   int   `json:"person_id"`
	DealID     int   `json:"deal_id"`
	ProductIDs []int `json:"product_ids"`
}

type EligibleDiscountsOutput struct {
	EligibleDiscount []EligibleDiscountOutput `json:"eligible_discounts"`
}

type EligibleDiscountOutput struct {
	Discount       DiscountOutput `json:"discount"`
	DiscountID     int            `json:"discount_id"`
	DiscountValue  float64        `json:"discount_value"`
	DiscountAmount float64        `json:"discount_amount"`
	EntityType     string         `json:"entity_type"`
	EntityID       int            `json:"entity_id"`
}

type DiscountDetail struct {
	Amount       float64 `json:"amount"`
	AppliedTo    string  `json:"applied_to"`
	AttachmentID *int    `json:"attachment_id,omitempty"`
}

type DiscountResult struct {
	DiscountID  int              `json:"discount_id"`
	TotalAmount float64          `json:"total_amount"`
	Details     []DiscountDetail `json:"details"`
}

type CalculateDiscountInput struct {
	DealID      int   `json:"deal_id"`
	DiscountIDs []int `json:"discount_ids"`
}

type CalculateDiscountOutput struct {
	TotalDiscountAmount float64          `json:"total_discount_amount"`
	Results             []DiscountResult `json:"results,omitempty"`
	DiscountAmounts     map[int]float64  `json:"discount_amounts"`
}

type CalculateDiscountAttachmentInput struct {
	ProductID   int   `json:"product_id"`
	Quantity    int   `json:"quantity"`
	DiscountIDs []int `json:"discount_ids"`
}
