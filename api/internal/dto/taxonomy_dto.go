package dto

import "time"

type TaxonomyOutput struct {
	ID          int              `json:"id"`
	Name        string           `json:"name"`
	Type        string           `json:"type"`
	Description string           `json:"description,omitempty"`
	ParentID    *int             `json:"parent_id,omitempty"`
	UserID      int              `json:"user_id"`
	CreatedAt   time.Time        `json:"created_at"`
	UpdatedAt   time.Time        `json:"updated_at"`
	DeletedAt   string           `json:"deleted_at,omitempty"`
	Version  int              `json:"version"`
	User     UserShortOutput  `json:"user"`
	Parent   *TaxonomyOutput  `json:"parent,omitempty"`
	Children []TaxonomyOutput `json:"children,omitempty"`
}

type CreateTaxonomyInput struct {
	Name        string `json:"name"`
	Type        string `json:"type"`
	Description string `json:"description,omitempty"`
	ParentID    *int   `json:"parent_id,omitempty"`
}

type UpdateTaxonomyInput struct {
	ID          int      `json:"id"`
	Name        *string  `json:"name,omitempty"`
	Type        *string  `json:"type,omitempty"`
	Description *string  `json:"description,omitempty"`
	ParentID    *int     `json:"parent_id,omitempty"`
	Modified    []string `json:"modified,omitempty"`
}

type ListTaxonomyInput struct {
	PageSize int            `json:"page_size,omitempty"`
	Page    int            `json:"page,omitempty"`
	Filter  TaxonomyFilter `json:"filter,omitempty"`
	OrderBy string         `json:"order_by,omitempty"`
	Search   string         `json:"search,omitempty"`
}

type TaxonomyFilter struct {
	IDs      []int  `json:"ids,omitempty"`
	Name     string `json:"name,omitempty"`
	Type     string `json:"type,omitempty"`
	ParentID *int   `json:"parent_id,omitempty"`
	UserID   int    `json:"user_id,omitempty"`
}

type ListTaxonomyOutput struct {
	Taxonomies []TaxonomyOutput `json:"taxonomies"`
	Total      int              `json:"total"`
	TotalPage  int              `json:"total_page"`
}
