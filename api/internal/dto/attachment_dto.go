package dto

type CreateAttachmentInput struct {
	DealID      *int    `json:"deal_id,omitempty"`
	PersonID    int     `json:"person_id"`
	ProductID   *int    `json:"product_id,omitempty"`
	BrandID     int     `json:"brand_id,omitempty"`
	Quantity    int     `json:"quantity,omitempty"`
	Price       float64 `json:"price,omitempty"`
	Discount    float64 `json:"discount,omitempty"`
	Status      int     `json:"status,omitempty"`
	Kind        string  `json:"kind"`
	Note        string  `json:"note,omitempty"`
	Title       string  `json:"title,omitempty"`
	ParentID    *int    `json:"parent_id,omitempty"`
	PlanID      int     `json:"plan_id,omitempty"`
	DiscountIDs []int   `json:"discounts,omitempty"`
}

type UpdateAttachmentInput struct {
	ID          int      `json:"id"`
	DealID      *int     `json:"deal_id,omitempty"`
	ProductID   *int     `json:"product_id,omitempty"`
	BrandID     int      `json:"brand_id,omitempty"`
	Quantity    int      `json:"quantity,omitempty"`
	Price       float64  `json:"price,omitempty"`
	Discount    float64  `json:"discount,omitempty"`
	Note        string   `json:"note,omitempty"`
	Kind        string   `json:"kind,omitempty"`
	Title       string   `json:"title,omitempty"`
	ParentID    *int     `json:"parent_id,omitempty"`
	PlanID      int      `json:"plan_id,omitempty"`
	Status      int      `json:"status,omitempty"`
	DiscountIDs []int    `json:"discounts,omitempty"`
	Modified    []string `json:"modified,omitempty"`
}

type AttachmentOutput struct {
	ID                int                      `json:"id"`
	DealID            int                      `json:"deal_id"`
	PersonID          int                      `json:"person_id"`
	ProductID         int                      `json:"product_id"`
	PlanID            int                      `json:"plan_id"`
	BrandID           int                      `json:"brand_id"`
	Quantity          int                      `json:"quantity"`
	Price             float64                  `json:"price"`
	Discount          float64                  `json:"discount"`
	Status            int                      `json:"status"`
	Kind              string                   `json:"kind"`
	Note              string                   `json:"note"`
	Title             string                   `json:"title"`
	ParentID          *int                     `json:"parent_id"`
	UserID            int                      `json:"user_id"`
	TrackID           int                      `json:"track_id"`
	CreatedAt         string                   `json:"created_at"`
	UpdatedAt         string                   `json:"updated_at"`
	DeletedAt         string                   `json:"deleted_at"`
	Version           int                      `json:"version"`
	Creator           UserShortOutput          `json:"creator"`
	Payment           float64                  `json:"payment"`
	Product           ProductOutput            `json:"product"`
	Parent            *AttachmentOutput        `json:"parent"`
	Children          []AttachmentOutput       `json:"children"`
	BillItem          *BillItemOutput          `json:"bill_item"`
	EligibleDiscounts []EligibleDiscountOutput `json:"eligible_discounts"`
	Data              []AttachmentDataOutput   `json:"data"`
}

type ListAttachmentInput struct {
	PageSize        int              `json:"page_size,omitempty"`
	Page            int              `json:"page,omitempty"`
	Filter          AttachmentFilter `json:"filter,omitempty"`
	ProductType     string           `json:"product_type,omitempty"`
	ProductCategory int              `json:"product_category,omitempty"`
	AttachmentKind  string           `json:"attachment_kind,omitempty"`
	OrderBy         string           `json:"order_by,omitempty"`
}

type AttachmentFilter struct {
	DealID    int    `json:"deal_id,omitempty"`
	ProductID int    `json:"product_id,omitempty"`
	ParentID  int    `json:"parent_id,omitempty"`
	Kind      string `json:"kind,omitempty"`
	Status    int    `json:"status,omitempty"`
	PlanID    int    `json:"plan_id,omitempty"`
	PersonID  int    `json:"person_id,omitempty"`
}

type ListAttachmentOutput struct {
	Attachments []AttachmentOutput `json:"attachments"`
	Total       int                `json:"total"`
	TotalPage   int                `json:"total_page"`
}
