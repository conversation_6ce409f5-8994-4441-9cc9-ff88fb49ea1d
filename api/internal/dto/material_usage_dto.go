package dto

import "time"

type MaterialUsage struct {
	ID             int     `json:"id"`
	AttachmentID   int     `json:"attachment_id"`
	MaterialID     int     `json:"material_id"`
	OperationID    *int    `json:"operation_id,omitempty"`
	OperationKey   *string `json:"operation_key,omitempty"`
	Kind           string  `json:"kind"`
	QuotedQuantity float64 `json:"quoted_quantity"`
	UsedQuantity   float64 `json:"used_quantity"`
	UserID         *int    `json:"user_id,omitempty"`
	Note           *string `json:"note,omitempty"`
	Version        int     `json:"version"`
	CreatedAt      string  `json:"created_at"`
	UpdatedAt      string  `json:"updated_at"`
	DeletedAt      string  `json:"deleted_at,omitempty"`
}

type CreateMaterialUsageInput struct {
	AttachmentID   int     `json:"attachment_id"`
	MaterialID     int     `json:"material_id"`
	OperationID    *int    `json:"operation_id,omitempty"`
	OperationKey   *string `json:"operation_key,omitempty"`
	Kind           string  `json:"kind,omitempty"`
	QuotedQuantity float64 `json:"quoted_quantity,omitempty"`
	UsedQuantity   float64 `json:"used_quantity"`
	UserID         *int    `json:"user_id,omitempty"`
	Note           *string `json:"note,omitempty"`
}

type UpdateMaterialUsageInput struct {
	ID             int      `json:"id"`
	AttachmentID   int      `json:"attachment_id,omitempty"`
	MaterialID     int      `json:"material_id,omitempty"`
	OperationID    *int     `json:"operation_id,omitempty"`
	OperationKey   *string  `json:"operation_key,omitempty"`
	Kind           string   `json:"kind,omitempty"`
	QuotedQuantity float64  `json:"quoted_quantity,omitempty"`
	UsedQuantity   float64  `json:"used_quantity,omitempty"`
	UserID         *int     `json:"user_id,omitempty"`
	Note           *string  `json:"note,omitempty"`
	Modified       []string `json:"modified,omitempty"`
}

type ListMaterialUsageInput struct {
	PageSize int                 `json:"page_size,omitempty"`
	Page     int                 `json:"page,omitempty"`
	Filter   MaterialUsageFilter `json:"filter,omitempty"`
	Search   string              `json:"search,omitempty"`
	OrderBy  string              `json:"order_by,omitempty"`
}

type MaterialUsageFilter struct {
	AttachmentID int    `json:"attachment_id,omitempty"`
	MaterialID   int    `json:"material_id,omitempty"`
	OperationID  int    `json:"operation_id,omitempty"`
	UserID       int    `json:"user_id,omitempty"`
	Kind         string `json:"kind,omitempty"`
}

type ListMaterialUsageOutput struct {
	MaterialUsages []MaterialUsage `json:"material_usages"`
	Total          int             `json:"total"`
	TotalPage      int             `json:"total_page"`
}

type MaterialUsageBulkInput struct {
	MaterialUsageAddItem []CreateMaterialUsageInput `json:"item"`
}

type ReportMaterialUsageInput struct {
	PageSize int                       `json:"page_size,omitempty"`
	Page     int                       `json:"page,omitempty"`
	Filter   MaterialUsageReportFilter `json:"filter,omitempty"`
	Search   string                    `json:"search,omitempty"`
	OrderBy  string                    `json:"order_by,omitempty"`
}

type MaterialUsageReportFilter struct {
	PersonId    int       `json:"person_id,omitempty"`
	OperationId int       `json:"operation_id,omitempty"`
	MaterialId  int       `json:"material_id,omitempty"`
	From        time.Time `json:"from,omitempty"`
	To          time.Time `json:"to,omitempty"`
	Search      string    `json:"search,omitempty"`
}

type ReportMaterialUsage struct {
	Id                  int     `json:"id,omitempty"`
	Code                string  `json:"code,omitempty"`
	Name                string  `json:"name,omitempty"`
	Unit                string  `json:"unit,omitempty"`
	TotalAttachment     int     `json:"total_attachment,omitempty"`
	TotalQuotedQuantity float64 `json:"total_quoted_quantity,omitempty"`
	TotalUsedQuantity   float64 `json:"total_used_quantity,omitempty"`
}

type ReportMaterialUsageOutput struct {
	MaterialUsageReport []ReportMaterialUsage `json:"material_usage_report"`
	Total               int                   `json:"total"`
	TotalPage           int                   `json:"total_page"`
}

type DetailReportMaterialUsageOutput struct {
	Items     []MaterialUsageDetail `json:"items"`
	Total     int                   `json:"total"`
	TotalPage int                   `json:"total_page"`
}

type MaterialUsageDetail struct {
	No               int64                   `json:"no"`
	PersonName       string                  `json:"person_name"`
	OperationDetails []OperationDetailReport `json:"operation_details"`
}

type OperationDetailReport struct {
	OperationName string                 `json:"operation_name"`
	UsedMaterials []MaterialDetailReport `json:"used_materials"`
}

type MaterialDetailReport struct {
	MaterialName   string  `json:"material_name"`
	UsedQuantity   float64 `json:"used_quantity"`
	QuotedQuantity float64 `json:"quoted_quantity"`
	Difference     float64 `json:"difference"`
	Unit           string  `json:"unit"`
}
