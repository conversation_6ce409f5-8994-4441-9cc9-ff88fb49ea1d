package dto

import (
	"time"
)

type TaskDepartmentOutput struct {
	ID           int              `json:"id"`
	TaskID       int              `json:"task_id"`
	DepartmentID int              `json:"department_id"`
	Role         string           `json:"role"`
	UserID       int              `json:"user_id"`
	CreatedAt    time.Time        `json:"created_at"`
	UpdatedAt    time.Time        `json:"updated_at"`
	DeletedAt    *time.Time       `json:"deleted_at,omitempty"`
	DueAt        time.Time        `json:"due_at"`
	CompletedAt  time.Time        `json:"completed_at"`
	Version      int              `json:"version"`
	Department   DepartmentOutput `json:"department"`
}

type CreateTaskDepartmentInput struct {
	TaskID       int    `json:"task_id"`
	DepartmentID int    `json:"department_id"`
	Role         string `json:"role"`
}

type UpdateTaskDepartmentInput struct {
	ID           int      `json:"id"`
	TaskID       int      `json:"task_id"`
	DepartmentID int      `json:"department_id"`
	Role         string   `json:"role"`
	Modified     []string `json:"modified,omitempty"`
}

type ListTaskDepartmentInput struct {
	PageSize int                  `json:"page_size,omitempty"`
	Page     int                  `json:"page,omitempty"`
	Filter   TaskDepartmentFilter `json:"filter,omitempty"`
	OrderBy  string               `json:"order_by,omitempty"`
}

type TaskDepartmentFilter struct {
	TaskID       int    `json:"task_id,omitempty"`
	DepartmentID int    `json:"department_id,omitempty"`
	Role         string `json:"role,omitempty"`
}

type ListTaskDepartmentOutput struct {
	TaskDepartments []TaskDepartmentOutput `json:"task_departments"`
	Total           int                    `json:"total"`
	TotalPage       int                    `json:"total_page"`
}

type DeleteTaskDepartmentInput struct {
	ID int `json:"id"`
}
