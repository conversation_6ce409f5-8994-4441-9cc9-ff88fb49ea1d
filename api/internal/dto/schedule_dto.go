package dto

import "time"

type ScheduleOutput struct {
	ID          int       `json:"id"`
	Name        string    `json:"name"`
	Description string    `json:"description"`
	StartTime   time.Time `json:"start_time"`
	EndTime     time.Time `json:"end_time"`
	Status      int       `json:"status"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	DeletedAt   string    `json:"deleted_at,omitempty"`
	Version     int       `json:"version"`
}

type CreateScheduleInput struct {
	Name        string    `json:"name"`
	Description string    `json:"description,omitempty"`
	StartTime   time.Time `json:"start_time"`
	EndTime     time.Time `json:"end_time"`
}

type UpdateScheduleInput struct {
	ID          int       `json:"id"`
	Name        string    `json:"name,omitempty"`
	Description string    `json:"description,omitempty"`
	StartTime   time.Time `json:"start_time,omitempty"`
	EndTime     time.Time `json:"end_time,omitempty"`
	Modified    []string  `json:"modified,omitempty"`
}

type ListSchedulesInput struct {
	Filter ScheduleInputFilter `json:"filter,omitempty"`
	Page   int                 `json:"page,omitempty"`
	PageSize int                 `json:"page_size,omitempty"`
	OrderBy  string              `json:"order_by,omitempty"`
}

type ScheduleInputFilter struct {
	Name      string    `json:"name,omitempty"`
	StartTime time.Time `json:"start_time,omitempty"`
	EndTime   time.Time `json:"end_time,omitempty"`
	Status    int       `json:"status,omitempty"`
}

type ListSchedulesOutput struct {
	Schedules []*ScheduleOutput
	Total     int
}
