package dto

type HeaderConfig struct {
	Field       string `json:"field"`
	DisplayName string `json:"display_name"`
	DataType    string `json:"data_type"`
	Formatter   string `json:"formatter,omitempty"`
	Width       int    `json:"width,default=15"`
}

type ExportInput struct {
	SQL         string         `json:"sql"`
	Parameters  []Parameter    `json:"parameters,omitempty"`
	Headers     []HeaderConfig `json:"headers"`
	TotalRecord int               `json:"total_record"`
}

type ExportJobOutput struct {
	JobID            string `json:"job_id,omitempty"`
	State            string `json:"state"`
	EstimatedRecords int    `json:"estimated_records,omitempty"`
	Message          string `json:"message"`
}

type DownloadJobInput struct {
	JobID  string `json:"job_id"`
	UserID int    `json:"user_id"`
}

type ExportProgressMessage struct {
	Type string          `json:"type"`
	Data ExportJobStatus `json:"data"`
}

type ExportJobStatus struct {
	JobID       int     `json:"job_id"`
	State       string  `json:"state"`
	Progress    float64 `json:"progress"`
	Message     string  `json:"message,omitempty"`
	DownloadURL string  `json:"download_url,omitempty"`
	FileName    string  `json:"file_name,omitempty"`
	Error       string  `json:"error,omitempty"`
}

type ExcelExportTaskPayload struct {
	JobID      int               `json:"job_id"`
	Headers    []HeaderConfig    `json:"headers"`
	SQL        string      `json:"sql"`
	Parameters []Parameter `json:"parameters"`
	UserID     int         `json:"user_id"`
}
