package auth

import (
	"bcare/common/bauth"
	"bcare/ent"
	"context"
	"github.com/zeromicro/go-zero/core/logx"
)

type IFileAuth interface {
	CanAdd(ctx context.Context, r *ent.File) bool
	CanDelete(ctx context.Context, r *ent.File) bool
	CanGet(ctx context.Context, r *ent.File) bool
	CanList(ctx context.Context, r *ent.File) bool
	CanUpdate(ctx context.Context, r *ent.File) bool
}

type FileAuth struct {
	authSvc    *bauth.Authorizer
	identityFn func(ctx context.Context) string
}

func (a FileAuth) CanAdd(ctx context.Context, r *ent.File) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "file::add", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce file::add fail: %v", err)
		return false
	}
	return can
}

func (a FileAuth) CanDelete(ctx context.Context, r *ent.File) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "file::delete", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce file::delete fail: %v", err)
		return false
	}
	return can
}

func (a FileAuth) CanGet(ctx context.Context, r *ent.File) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "file::get", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce file::get fail: %v", err)
		return false
	}
	return can
}

func (a FileAuth) CanList(ctx context.Context, r *ent.File) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "file::list", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce file::list fail: %v", err)
		return false
	}
	return can
}

func (a FileAuth) CanUpdate(ctx context.Context, r *ent.File) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "file::update", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce file::update fail: %v", err)
		return false
	}
	return can
}

func NewFileAuth(auth *bauth.Authorizer, iFn func(ctx context.Context) string) IFileAuth {
	return &FileAuth{authSvc: auth, identityFn: iFn}
}
