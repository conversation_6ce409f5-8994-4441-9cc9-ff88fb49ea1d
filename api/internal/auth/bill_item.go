package auth

import (
	"bcare/common/bauth"
	"bcare/ent"
	"context"
	"github.com/zeromicro/go-zero/core/logx"
)

type IBillItemAuth interface {
	CanAdd(ctx context.Context, r *ent.BillItem) bool
	CanDelete(ctx context.Context, r *ent.BillItem) bool
	CanGet(ctx context.Context, r *ent.BillItem) bool
	CanList(ctx context.Context, r *ent.BillItem) bool
	Can<PERSON>uery(ctx context.Context, r *ent.BillItem) bool
	CanUpdate(ctx context.Context, r *ent.BillItem) bool
}

type BillItemAuth struct {
	authSvc    *bauth.Authorizer
	identityFn func(ctx context.Context) string
}

func (a BillItemAuth) CanAdd(ctx context.Context, r *ent.BillItem) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "bill_item::add", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce bill_item::add fail: %v", err)
		return false
	}
	return can
}

func (a BillItemAuth) CanDelete(ctx context.Context, r *ent.BillItem) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "bill_item::delete", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce bill_item::delete fail: %v", err)
		return false
	}
	return can
}

func (a BillItemAuth) CanGet(ctx context.Context, r *ent.BillItem) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "bill_item::get", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce bill_item::get fail: %v", err)
		return false
	}
	return can
}

func (a BillItemAuth) CanList(ctx context.Context, r *ent.BillItem) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "bill_item::list", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce bill_item::list fail: %v", err)
		return false
	}
	return can
}

func (a BillItemAuth) CanQuery(ctx context.Context, r *ent.BillItem) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "bill_item::query", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce bill_item::query fail: %v", err)
		return false
	}
	return can
}

func (a BillItemAuth) CanUpdate(ctx context.Context, r *ent.BillItem) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "bill_item::update", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce bill_item::update fail: %v", err)
		return false
	}
	return can
}

func NewBillItemAuth(auth *bauth.Authorizer, iFn func(ctx context.Context) string) IBillItemAuth {
	return &BillItemAuth{authSvc: auth, identityFn: iFn}
}
