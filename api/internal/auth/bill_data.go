package auth

import (
	"bcare/common/bauth"
	"bcare/ent"
	"context"
	"github.com/zeromicro/go-zero/core/logx"
)

type IBillDataAuth interface {
	CanClear(ctx context.Context, r *ent.BillData) bool
	CanGet(ctx context.Context, r *ent.BillData) bool
	CanSet(ctx context.Context, r *ent.BillData) bool
}

type BillDataAuth struct {
	authSvc    *bauth.Authorizer
	identityFn func(ctx context.Context) string
}

func (a BillDataAuth) CanClear(ctx context.Context, r *ent.BillData) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "bill_data::clear", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce bill_data::clear fail: %v", err)
		return false
	}
	return can
}

func (a BillDataAuth) CanGet(ctx context.Context, r *ent.BillData) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "bill_data::get", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce bill_data::get fail: %v", err)
		return false
	}
	return can
}

func (a BillDataAuth) CanSet(ctx context.Context, r *ent.BillData) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "bill_data::set", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce bill_data::set fail: %v", err)
		return false
	}
	return can
}

func NewBillDataAuth(auth *bauth.Authorizer, iFn func(ctx context.Context) string) IBillDataAuth {
	return &BillDataAuth{authSvc: auth, identityFn: iFn}
}
