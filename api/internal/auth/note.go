package auth

import (
	"bcare/api/internal/dto"
	"bcare/common/bauth"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

// INoteAuth defines the authorization interface for the note module.
type INoteAuth interface {
	CanAdd(ctx context.Context) bool
	CanGet(ctx context.Context, note *dto.NoteOutput) bool
	CanList(ctx context.Context) bool
	CanUpdate(ctx context.Context, note *dto.NoteOutput) bool
	CanDelete(ctx context.Context, note *dto.NoteOutput) bool
}

// NoteAuth implements INoteAuth.
type NoteAuth struct {
	authSvc    *bauth.Authorizer
	identityFn func(ctx context.Context) string // Function to get user identity from context
}

// NewNoteAuth creates a new NoteAuth instance.
func NewNoteAuth(authSvc *bauth.Authorizer, identityFn func(ctx context.Context) string) INoteAuth {
	return &NoteAuth{
		authSvc:    authSvc,
		identityFn: identityFn,
	}
}

// CanAdd checks if the current user can add a note.
// Example: Enforce(user, "note::add", "*")
func (a *NoteAuth) CanAdd(ctx context.Context) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "note::add", "*") // Replace "*" if specific resource check needed
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce note::add fail: %v", err)
		return false
	}
	return can
}

// CanGet checks if the current user can get a specific note.
// Example: Enforce(user, "note::get", noteResource)
func (a *NoteAuth) CanGet(ctx context.Context, note *dto.NoteOutput) bool {
	// noteResource can be the note object itself, its ID, or a constructed string/object for Casbin.
	// If Casbin model uses attributes of the note (e.g., note.UserID == currentUser), pass `note` directly.
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "note::get", note)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce note::get fail: %v for note ID %d", err, note.ID)
		return false
	}
	return can
}

// CanList checks if the current user can list notes.
// Example: Enforce(user, "note::list", "*")
func (a *NoteAuth) CanList(ctx context.Context) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "note::list", "*")
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce note::list fail: %v", err)
		return false
	}
	return can
}

// CanUpdate checks if the current user can update a specific note.
// Example: Enforce(user, "note::update", noteResource)
func (a *NoteAuth) CanUpdate(ctx context.Context, note *dto.NoteOutput) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "note::update", note)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce note::update fail: %v for note ID %d", err, note.ID)
		return false
	}
	return can
}

// CanDelete checks if the current user can delete a specific note.
// Example: Enforce(user, "note::delete", noteResource)
func (a *NoteAuth) CanDelete(ctx context.Context, note *dto.NoteOutput) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "note::delete", note)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce note::delete fail: %v for note ID %d", err, note.ID)
		return false
	}
	return can
}
