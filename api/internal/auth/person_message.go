package auth

import (
	"bcare/common/bauth"
	"bcare/ent"
	"context"
	"github.com/zeromicro/go-zero/core/logx"
)

type IPersonMessageAuth interface {
	CanHistoryMessage(ctx context.Context, r *ent.MessageHistory) bool
	CanSendMessage(ctx context.Context, r *ent.MessageHistory) bool
	CanMessageTemplate(ctx context.Context, r *ent.MessageHistory) bool
}

type PersonMessageAuth struct {
	authSvc    *bauth.Authorizer
	identityFn func(ctx context.Context) string
}

func (a PersonMessageAuth) CanHistoryMessage(ctx context.Context, r *ent.MessageHistory) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), r, "person_message::history_message")
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce person_message::history_message fail: %v", err)
		return false
	}
	return can
}

func (a PersonMessageAuth) CanSendMessage(ctx context.Context, r *ent.MessageHistory) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), r, "person_message::send_message")
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce person_message::send_message fail: %v", err)
		return false
	}
	return can
}

func (a PersonMessageAuth) CanMessageTemplate(ctx context.Context, r *ent.MessageHistory) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), r, "person_message::message_template")
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce person_message::message_template fail: %v", err)
		return false
	}
	return can
}

func NewPersonMessageAuth(auth *bauth.Authorizer, iFn func(ctx context.Context) string) IPersonMessageAuth {
	return &PersonMessageAuth{authSvc: auth, identityFn: iFn}
}
