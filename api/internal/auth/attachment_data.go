package auth

import (
	"bcare/common/bauth"
	"bcare/ent"
	"context"
	"github.com/zeromicro/go-zero/core/logx"
)

type IAttachmentDataAuth interface {
	CanClear(ctx context.Context, r *ent.AttachmentData) bool
	CanGet(ctx context.Context, r *ent.AttachmentData) bool
	CanSet(ctx context.Context, r *ent.AttachmentData) bool
}

type AttachmentDataAuth struct {
	authSvc    *bauth.Authorizer
	identityFn func(ctx context.Context) string
}

func (a AttachmentDataAuth) CanClear(ctx context.Context, r *ent.AttachmentData) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "attachment_data::clear", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce attachment_data::clear fail: %v", err)
		return false
	}
	return can
}

func (a AttachmentDataAuth) CanGet(ctx context.Context, r *ent.AttachmentData) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "attachment_data::get", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce attachment_data::get fail: %v", err)
		return false
	}
	return can
}

func (a AttachmentDataAuth) CanSet(ctx context.Context, r *ent.AttachmentData) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "attachment_data::set", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce attachment_data::set fail: %v", err)
		return false
	}
	return can
}

func NewAttachmentDataAuth(auth *bauth.Authorizer, iFn func(ctx context.Context) string) IAttachmentDataAuth {
	return &AttachmentDataAuth{authSvc: auth, identityFn: iFn}
}
