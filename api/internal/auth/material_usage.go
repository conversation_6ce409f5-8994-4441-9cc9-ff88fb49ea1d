package auth

import (
	"bcare/api/internal/dto"
	"bcare/common/bauth"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type IMaterialUsageAuth interface {
	CanAdd(ctx context.Context) bool
	CanDelete(ctx context.Context, r *dto.MaterialUsage) bool
	CanGet(ctx context.Context, r *dto.MaterialUsage) bool
	CanList(ctx context.Context) bool
	CanUpdate(ctx context.Context, r *dto.MaterialUsage) bool
}

type MaterialUsageAuth struct {
	authSvc    *bauth.Authorizer
	identityFn func(ctx context.Context) string
}

func (a MaterialUsageAuth) CanAdd(ctx context.Context) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "material_usage::add", "*")
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce material_usage::add fail: %v", err)
		return false
	}
	return can
}

func (a MaterialUsageAuth) CanDelete(ctx context.Context, r *dto.MaterialUsage) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "material_usage::delete", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce material_usage::delete fail: %v", err)
		return false
	}
	return can
}

func (a MaterialUsageAuth) CanGet(ctx context.Context, r *dto.MaterialUsage) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "material_usage::get", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce material_usage::get fail: %v", err)
		return false
	}
	return can
}

func (a MaterialUsageAuth) CanList(ctx context.Context) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "material_usage::list", "*")
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce material_usage::list fail: %v", err)
		return false
	}
	return can
}

func (a MaterialUsageAuth) CanUpdate(ctx context.Context, r *dto.MaterialUsage) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "material_usage::update", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce material_usage::update fail: %v", err)
		return false
	}
	return can
}

func NewMaterialUsageAuth(auth *bauth.Authorizer, iFn func(ctx context.Context) string) IMaterialUsageAuth {
	return &MaterialUsageAuth{authSvc: auth, identityFn: iFn}
}
