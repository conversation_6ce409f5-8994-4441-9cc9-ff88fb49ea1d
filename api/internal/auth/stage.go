package auth

import (
	"bcare/common/bauth"
	"bcare/ent"
	"context"
	"github.com/zeromicro/go-zero/core/logx"
)

type IStageAuth interface {
	CanAdd(ctx context.Context, r *ent.Stage) bool
	CanDelete(ctx context.Context, r *ent.Stage) bool
	CanGet(ctx context.Context, r *ent.Stage) bool
	CanList(ctx context.Context, r *ent.Stage) bool
	CanUpdate(ctx context.Context, r *ent.Stage) bool
	CanUpdateStage(ctx context.Context, r *ent.Stage) bool
}

type StageAuth struct {
	authSvc    *bauth.Authorizer
	identityFn func(ctx context.Context) string
}

func (a StageAuth) CanAdd(ctx context.Context, r *ent.Stage) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "stage::add", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce stage::add fail: %v", err)
		return false
	}
	return can
}

func (a StageAuth) CanDelete(ctx context.Context, r *ent.Stage) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "stage::delete", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce stage::delete fail: %v", err)
		return false
	}
	return can
}

func (a StageAuth) CanGet(ctx context.Context, r *ent.Stage) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "stage::get", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce stage::get fail: %v", err)
		return false
	}
	return can
}

func (a StageAuth) CanList(ctx context.Context, r *ent.Stage) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "stage::list", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce stage::list fail: %v", err)
		return false
	}
	return can
}

func (a StageAuth) CanUpdate(ctx context.Context, r *ent.Stage) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "stage::update", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce stage::update fail: %v", err)
		return false
	}
	return can
}

func (a StageAuth) CanUpdateStage(ctx context.Context, r *ent.Stage) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "stage::update_stage", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce stage::update_stage fail: %v", err)
		return false
	}
	return can
}

func NewStageAuth(auth *bauth.Authorizer, iFn func(ctx context.Context) string) IStageAuth {
	return &StageAuth{authSvc: auth, identityFn: iFn}
}
