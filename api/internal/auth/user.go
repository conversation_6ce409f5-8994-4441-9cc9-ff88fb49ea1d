package auth

import (
	"bcare/api/internal/dto"
	"bcare/common/bauth"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

// IUserAuth defines the interface for user authorization checks.
// Uses model DTOs instead of ent.User.
type IUserAuth interface {
	CanAdd(ctx context.Context) bool                          // Changed: No input DTO needed based on tag example
	CanDelete(ctx context.Context, user *dto.UserOutput) bool // User being deleted
	CanGet(ctx context.Context, user *dto.UserOutput) bool    // User being fetched
	CanList(ctx context.Context) bool                         // No specific user object needed for list
	CanUpdate(ctx context.Context, user *dto.UserOutput) bool // User being updated (current state)
}

// UserAuth implements IUserAuth.
type UserAuth struct {
	authSvc    *bauth.Authorizer
	identityFn func(ctx context.Context) string // Added identityFn field
}

// NewUserAuth creates a new UserAuth instance, accepting identityFn.
func NewUserAuth(auth *bauth.Authorizer, iFn func(ctx context.Context) string) IUserAuth { // Added iFn parameter
	return &UserAuth{
		authSvc:    auth,
		identityFn: iFn, // Store identityFn
	}
}

// CanAdd checks if the current user can add any user.
func (a *UserAuth) CanAdd(ctx context.Context) bool {
	subject := a.identityFn(ctx) // Use identityFn passed in
	// Policy check for generic "user::add" action, object is wildcard "*"
	can, err := a.authSvc.Enforcer.Enforce(subject, "user::add", "*") // Changed object to "*"
	if err != nil {
		logx.WithContext(ctx).Errorf("Enforce user::add failed for subject '%s': %v", subject, err)
		return false
	}
	return can
}

func (a *UserAuth) CanDelete(ctx context.Context, user *dto.UserOutput) bool {
	if user == nil {
		logx.WithContext(ctx).Errorf("UserAuth.CanDelete called with nil user output")
		return false
	}
	subject := a.identityFn(ctx) // Use identityFn passed in
	// Pass model.UserOutput as the object 'obj' for policy checks
	can, err := a.authSvc.Enforcer.Enforce(subject, "user::delete", user)
	if err != nil {
		logx.WithContext(ctx).Errorf("Enforce user::delete failed for subject '%s' on user %d: %v", subject, user.ID, err)
		return false
	}
	return can
}

func (a *UserAuth) CanGet(ctx context.Context, user *dto.UserOutput) bool {
	if user == nil {
		logx.WithContext(ctx).Errorf("UserAuth.CanGet called with nil user output")
		return false
	}
	subject := a.identityFn(ctx) // Use identityFn passed in
	// Pass model.UserOutput as the object 'obj'
	can, err := a.authSvc.Enforcer.Enforce(subject, "user::get", user)
	if err != nil {
		logx.WithContext(ctx).Errorf("Enforce user::get failed for subject '%s' on user %d: %v", subject, user.ID, err)
		return false
	}
	return can
}

func (a *UserAuth) CanList(ctx context.Context) bool { // Removed unused 'r' parameter
	subject := a.identityFn(ctx) // Use identityFn passed in
	// Placeholder logic: Action "user::list", Object "*"
	can, err := a.authSvc.Enforcer.Enforce(subject, "user::list", "*")
	if err != nil {
		logx.WithContext(ctx).Errorf("Enforce user::list failed for subject '%s': %v", subject, err)
		return false
	}
	return can
}

func (a *UserAuth) CanUpdate(ctx context.Context, user *dto.UserOutput) bool {
	if user == nil {
		logx.WithContext(ctx).Errorf("UserAuth.CanUpdate called with nil user output")
		return false
	}
	subject := a.identityFn(ctx) // Use identityFn passed in
	// Pass model.UserOutput as the object 'obj' (representing the user state *before* update)
	can, err := a.authSvc.Enforcer.Enforce(subject, "user::update", user)
	if err != nil {
		logx.WithContext(ctx).Errorf("Enforce user::update failed for subject '%s' on user %d: %v", subject, user.ID, err)
		return false
	}
	return can
}
