package auth

import (
	"bcare/common/bauth"
	"bcare/ent"
	"context"
	"github.com/zeromicro/go-zero/core/logx"
)

type IInstallmentPlanAuth interface {
	CanAdd(ctx context.Context, r *ent.InstallmentPlan) bool
	CanDelete(ctx context.Context, r *ent.InstallmentPlan) bool
	CanGet(ctx context.Context, r *ent.InstallmentPlan) bool
	CanList(ctx context.Context, r *ent.InstallmentPlan) bool
	CanUpdate(ctx context.Context, r *ent.InstallmentPlan) bool
}

type InstallmentPlanAuth struct {
	authSvc    *bauth.Authorizer
	identityFn func(ctx context.Context) string
}

func (a InstallmentPlanAuth) CanAdd(ctx context.Context, r *ent.InstallmentPlan) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "installment_plan::add", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce installment_plan::add fail: %v", err)
		return false
	}
	return can
}

func (a InstallmentPlanAuth) CanDelete(ctx context.Context, r *ent.InstallmentPlan) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "installment_plan::delete", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce installment_plan::delete fail: %v", err)
		return false
	}
	return can
}

func (a InstallmentPlanAuth) CanGet(ctx context.Context, r *ent.InstallmentPlan) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "installment_plan::get", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce installment_plan::get fail: %v", err)
		return false
	}
	return can
}

func (a InstallmentPlanAuth) CanList(ctx context.Context, r *ent.InstallmentPlan) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "installment_plan::list", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce installment_plan::list fail: %v", err)
		return false
	}
	return can
}

func (a InstallmentPlanAuth) CanUpdate(ctx context.Context, r *ent.InstallmentPlan) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "installment_plan::update", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce installment_plan::update fail: %v", err)
		return false
	}
	return can
}

func NewInstallmentPlanAuth(auth *bauth.Authorizer, iFn func(ctx context.Context) string) IInstallmentPlanAuth {
	return &InstallmentPlanAuth{authSvc: auth, identityFn: iFn}
}
