package auth

import (
	"bcare/api/internal/dto"
	"bcare/common/bauth"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type ITaskAuth interface {
	CanAdd(ctx context.Context) bool
	CanBulkDelete(ctx context.Context) bool
	CanBulkUpdateStatus(ctx context.Context) bool
	CanDelete(ctx context.Context, r *dto.TaskOutput) bool
	CanGet(ctx context.Context, r *dto.TaskOutput) bool
	CanList(ctx context.Context) bool
	CanQuery(ctx context.Context, r *dto.TaskOutput) bool
	CanUpdate(ctx context.Context, r *dto.TaskOutput) bool
}

type TaskAuth struct {
	authSvc    *bauth.Authorizer
	identityFn func(ctx context.Context) string
}

func (a TaskAuth) CanAdd(ctx context.Context) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "task::add", "*")
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce task::add fail: %v", err)
		return false
	}
	return can
}

func (a TaskAuth) CanBulkDelete(ctx context.Context) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "task::bulk_delete", "*")
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce task::bulk_delete fail: %v", err)
		return false
	}
	return can
}

func (a TaskAuth) CanBulkUpdateStatus(ctx context.Context) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "task::bulk_update_status", "*")
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce task::bulk_update_status fail: %v", err)
		return false
	}
	return can
}

func (a TaskAuth) CanDelete(ctx context.Context, r *dto.TaskOutput) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "task::delete", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce task::delete fail: %v", err)
		return false
	}
	return can
}

func (a TaskAuth) CanGet(ctx context.Context, r *dto.TaskOutput) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "task::get", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce task::get fail: %v", err)
		return false
	}
	return can
}

func (a TaskAuth) CanList(ctx context.Context) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "task::list", "*")
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce task::list fail: %v", err)
		return false
	}
	return can
}

func (a TaskAuth) CanQuery(ctx context.Context, r *dto.TaskOutput) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "task::query", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce task::query fail: %v", err)
		return false
	}
	return can
}

func (a TaskAuth) CanUpdate(ctx context.Context, r *dto.TaskOutput) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "task::update", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce task::update fail: %v", err)
		return false
	}
	return can
}

func NewTaskAuth(auth *bauth.Authorizer, iFn func(ctx context.Context) string) ITaskAuth {
	return &TaskAuth{authSvc: auth, identityFn: iFn}
}
