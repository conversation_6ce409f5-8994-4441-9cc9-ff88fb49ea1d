package auth

import (
	"bcare/common/bauth"
	"bcare/ent"
	"context"
	"github.com/zeromicro/go-zero/core/logx"
)

type IDiscountUsageAuth interface {
	CanAdd(ctx context.Context, r *ent.DiscountUsage) bool
	CanDelete(ctx context.Context, r *ent.DiscountUsage) bool
	CanList(ctx context.Context, r *ent.DiscountUsage) bool
}

type DiscountUsageAuth struct {
	authSvc    *bauth.Authorizer
	identityFn func(ctx context.Context) string
}

func (a DiscountUsageAuth) CanAdd(ctx context.Context, r *ent.DiscountUsage) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "discount_usage::add", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce discount_usage::add fail: %v", err)
		return false
	}
	return can
}

func (a DiscountUsageAuth) CanDelete(ctx context.Context, r *ent.DiscountUsage) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "discount_usage::delete", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce discount_usage::delete fail: %v", err)
		return false
	}
	return can
}

func (a DiscountUsageAuth) CanList(ctx context.Context, r *ent.DiscountUsage) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "discount_usage::list", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce discount_usage::list fail: %v", err)
		return false
	}
	return can
}

func NewDiscountUsageAuth(auth *bauth.Authorizer, iFn func(ctx context.Context) string) IDiscountUsageAuth {
	return &DiscountUsageAuth{authSvc: auth, identityFn: iFn}
}
