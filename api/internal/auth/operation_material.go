package auth

import (
	"bcare/api/internal/dto"
	"bcare/common/bauth"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type IOperationMaterialAuth interface {
	CanAdd(ctx context.Context) bool
	CanDelete(ctx context.Context, r *dto.OperationMaterialOutput) bool
	CanGet(ctx context.Context, r *dto.OperationMaterialOutput) bool
	CanList(ctx context.Context) bool
	CanUpdate(ctx context.Context, r *dto.OperationMaterialOutput) bool
}

type OperationMaterialAuth struct {
	authSvc    *bauth.Authorizer
	identityFn func(ctx context.Context) string
}

func (a OperationMaterialAuth) CanAdd(ctx context.Context) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "operation_material::add", "*")
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce operation_material::add fail: %v", err)
		return false
	}
	return can
}

func (a OperationMaterialAuth) CanDelete(ctx context.Context, r *dto.OperationMaterialOutput) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "operation_material::delete", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce operation_material::delete fail: %v", err)
		return false
	}
	return can
}

func (a OperationMaterialAuth) CanGet(ctx context.Context, r *dto.OperationMaterialOutput) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "operation_material::get", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce operation_material::get fail: %v", err)
		return false
	}
	return can
}

func (a OperationMaterialAuth) CanList(ctx context.Context) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "operation_material::list", "*")
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce operation_material::list fail: %v", err)
		return false
	}
	return can
}

func (a OperationMaterialAuth) CanUpdate(ctx context.Context, r *dto.OperationMaterialOutput) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "operation_material::update", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce operation_material::update fail: %v", err)
		return false
	}
	return can
}

func NewOperationMaterialAuth(auth *bauth.Authorizer, iFn func(ctx context.Context) string) IOperationMaterialAuth {
	return &OperationMaterialAuth{authSvc: auth, identityFn: iFn}
}
