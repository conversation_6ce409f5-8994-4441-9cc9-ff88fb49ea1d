package auth

import (
	"bcare/common/berr"
)

var (
	ErrActivityListDenied = berr.NewErrCodeMsg(2001, "You do not have permission to perform this action: activity::list")

	ErrAdminLogsDenied    = berr.NewErrCodeMsg(2101, "You do not have permission to perform this action: admin::logs")
	ErrAdminMonitorDenied = berr.NewErrCodeMsg(2102, "You do not have permission to perform this action: admin::monitor")

	ErrAppointmentAddDenied       = berr.NewErrCodeMsg(2201, "You do not have permission to perform this action: appointment::add")
	ErrAppointmentDeleteDenied    = berr.NewErrCodeMsg(2202, "You do not have permission to perform this action: appointment::delete")
	ErrAppointmentGetDenied       = berr.NewErrCodeMsg(2203, "You do not have permission to perform this action: appointment::get")
	ErrAppointmentGetLastedDenied = berr.NewErrCodeMsg(2204, "You do not have permission to perform this action: appointment::get_lasted")
	ErrAppointmentListDenied      = berr.NewErrCodeMsg(2205, "You do not have permission to perform this action: appointment::list")
	ErrAppointmentQueryDenied     = berr.NewErrCodeMsg(2206, "You do not have permission to perform this action: appointment::query")
	ErrAppointmentUpdateDenied    = berr.NewErrCodeMsg(2207, "You do not have permission to perform this action: appointment::update")

	ErrAttachmentAddDenied      = berr.NewErrCodeMsg(2301, "You do not have permission to perform this action: attachment::add")
	ErrAttachmentChildrenDenied = berr.NewErrCodeMsg(2302, "You do not have permission to perform this action: attachment::children")
	ErrAttachmentDeleteDenied   = berr.NewErrCodeMsg(2303, "You do not have permission to perform this action: attachment::delete")
	ErrAttachmentGetDenied      = berr.NewErrCodeMsg(2304, "You do not have permission to perform this action: attachment::get")
	ErrAttachmentListDenied     = berr.NewErrCodeMsg(2305, "You do not have permission to perform this action: attachment::list")
	ErrAttachmentQueryDenied    = berr.NewErrCodeMsg(2306, "You do not have permission to perform this action: attachment::query")
	ErrAttachmentUpdateDenied   = berr.NewErrCodeMsg(2307, "You do not have permission to perform this action: attachment::update")

	ErrAttachmentDataClearDenied = berr.NewErrCodeMsg(2401, "You do not have permission to perform this action: attachment_data::clear")
	ErrAttachmentDataGetDenied   = berr.NewErrCodeMsg(2402, "You do not have permission to perform this action: attachment_data::get")
	ErrAttachmentDataSetDenied   = berr.NewErrCodeMsg(2403, "You do not have permission to perform this action: attachment_data::set")

	ErrAuthLoginDenied   = berr.NewErrCodeMsg(2501, "You do not have permission to perform this action: auth::login")
	ErrAuthRefreshDenied = berr.NewErrCodeMsg(2502, "You do not have permission to perform this action: auth::refresh")

	ErrBillAddDenied    = berr.NewErrCodeMsg(2601, "You do not have permission to perform this action: bill::add")
	ErrBillDeleteDenied = berr.NewErrCodeMsg(2602, "You do not have permission to perform this action: bill::delete")
	ErrBillGetDenied    = berr.NewErrCodeMsg(2603, "You do not have permission to perform this action: bill::get")
	ErrBillListDenied   = berr.NewErrCodeMsg(2604, "You do not have permission to perform this action: bill::list")
	ErrBillQueryDenied  = berr.NewErrCodeMsg(2605, "You do not have permission to perform this action: bill::query")
	ErrBillUpdateDenied = berr.NewErrCodeMsg(2606, "You do not have permission to perform this action: bill::update")

	ErrBillDataClearDenied = berr.NewErrCodeMsg(2701, "You do not have permission to perform this action: bill_data::clear")
	ErrBillDataGetDenied   = berr.NewErrCodeMsg(2702, "You do not have permission to perform this action: bill_data::get")
	ErrBillDataSetDenied   = berr.NewErrCodeMsg(2703, "You do not have permission to perform this action: bill_data::set")

	ErrBillItemAddDenied    = berr.NewErrCodeMsg(2801, "You do not have permission to perform this action: bill_item::add")
	ErrBillItemDeleteDenied = berr.NewErrCodeMsg(2802, "You do not have permission to perform this action: bill_item::delete")
	ErrBillItemGetDenied    = berr.NewErrCodeMsg(2803, "You do not have permission to perform this action: bill_item::get")
	ErrBillItemListDenied   = berr.NewErrCodeMsg(2804, "You do not have permission to perform this action: bill_item::list")
	ErrBillItemQueryDenied  = berr.NewErrCodeMsg(2805, "You do not have permission to perform this action: bill_item::query")
	ErrBillItemUpdateDenied = berr.NewErrCodeMsg(2806, "You do not have permission to perform this action: bill_item::update")

	ErrBundleAddDenied    = berr.NewErrCodeMsg(2901, "You do not have permission to perform this action: bundle::add")
	ErrBundleDeleteDenied = berr.NewErrCodeMsg(2902, "You do not have permission to perform this action: bundle::delete")
	ErrBundleGetDenied    = berr.NewErrCodeMsg(2903, "You do not have permission to perform this action: bundle::get")
	ErrBundleListDenied   = berr.NewErrCodeMsg(2904, "You do not have permission to perform this action: bundle::list")
	ErrBundleUpdateDenied = berr.NewErrCodeMsg(2905, "You do not have permission to perform this action: bundle::update")

	ErrCallAcceptDenied         = berr.NewErrCodeMsg(3001, "You do not have permission to perform this action: call::accept")
	ErrCallAddDenied            = berr.NewErrCodeMsg(3002, "You do not have permission to perform this action: call::add")
	ErrCallDeleteDenied         = berr.NewErrCodeMsg(3003, "You do not have permission to perform this action: call::delete")
	ErrCallEndDenied            = berr.NewErrCodeMsg(3004, "You do not have permission to perform this action: call::end")
	ErrCallGetDenied            = berr.NewErrCodeMsg(3005, "You do not have permission to perform this action: call::get")
	ErrCallListDenied           = berr.NewErrCodeMsg(3006, "You do not have permission to perform this action: call::list")
	ErrCallQueryDenied          = berr.NewErrCodeMsg(3007, "You do not have permission to perform this action: call::query")
	ErrCallUpdateFeedbackDenied = berr.NewErrCodeMsg(3008, "You do not have permission to perform this action: call::update_feedback")

	ErrCasbinRoleAddDenied    = berr.NewErrCodeMsg(3101, "You do not have permission to perform this action: casbin::role_add")
	ErrCasbinRoleDeleteDenied = berr.NewErrCodeMsg(3102, "You do not have permission to perform this action: casbin::role_delete")
	ErrCasbinRoleListDenied   = berr.NewErrCodeMsg(3103, "You do not have permission to perform this action: casbin::role_list")
	ErrCasbinSyncDenied       = berr.NewErrCodeMsg(3104, "You do not have permission to perform this action: casbin::sync")

	ErrConstantGetDenied = berr.NewErrCodeMsg(3201, "You do not have permission to perform this action: constant::get")

	ErrDealAddDenied      = berr.NewErrCodeMsg(3301, "You do not have permission to perform this action: deal::add")
	ErrDealCheckoutDenied = berr.NewErrCodeMsg(3302, "You do not have permission to perform this action: deal::checkout")
	ErrDealDeleteDenied   = berr.NewErrCodeMsg(3303, "You do not have permission to perform this action: deal::delete")
	ErrDealGetDenied      = berr.NewErrCodeMsg(3304, "You do not have permission to perform this action: deal::get")
	ErrDealListDenied     = berr.NewErrCodeMsg(3305, "You do not have permission to perform this action: deal::list")
	ErrDealListBigDenied  = berr.NewErrCodeMsg(3306, "You do not have permission to perform this action: deal::list_big")
	ErrDealUpdateDenied   = berr.NewErrCodeMsg(3307, "You do not have permission to perform this action: deal::update")

	ErrDealUserAddDenied    = berr.NewErrCodeMsg(3401, "You do not have permission to perform this action: deal_user::add")
	ErrDealUserDeleteDenied = berr.NewErrCodeMsg(3402, "You do not have permission to perform this action: deal_user::delete")
	ErrDealUserListDenied   = berr.NewErrCodeMsg(3403, "You do not have permission to perform this action: deal_user::list")
	ErrDealUserUpdateDenied = berr.NewErrCodeMsg(3404, "You do not have permission to perform this action: deal_user::update")

	ErrDepartmentListDenied = berr.NewErrCodeMsg(3501, "You do not have permission to perform this action: department::list")

	ErrDiscountAddDenied               = berr.NewErrCodeMsg(3601, "You do not have permission to perform this action: discount::add")
	ErrDiscountCalculateDiscountDenied = berr.NewErrCodeMsg(3602, "You do not have permission to perform this action: discount::calculate_discount")
	ErrDiscountDeleteDenied            = berr.NewErrCodeMsg(3603, "You do not have permission to perform this action: discount::delete")
	ErrDiscountEligibleDiscountDenied  = berr.NewErrCodeMsg(3604, "You do not have permission to perform this action: discount::eligible_discount")
	ErrDiscountGetDenied               = berr.NewErrCodeMsg(3605, "You do not have permission to perform this action: discount::get")
	ErrDiscountListDenied              = berr.NewErrCodeMsg(3606, "You do not have permission to perform this action: discount::list")
	ErrDiscountUpdateDenied            = berr.NewErrCodeMsg(3607, "You do not have permission to perform this action: discount::update")

	ErrDiscountUsageAddDenied    = berr.NewErrCodeMsg(3701, "You do not have permission to perform this action: discount_usage::add")
	ErrDiscountUsageDeleteDenied = berr.NewErrCodeMsg(3702, "You do not have permission to perform this action: discount_usage::delete")
	ErrDiscountUsageListDenied   = berr.NewErrCodeMsg(3703, "You do not have permission to perform this action: discount_usage::list")

	ErrFileAddDenied    = berr.NewErrCodeMsg(3801, "You do not have permission to perform this action: file::add")
	ErrFileDeleteDenied = berr.NewErrCodeMsg(3802, "You do not have permission to perform this action: file::delete")
	ErrFileGetDenied    = berr.NewErrCodeMsg(3803, "You do not have permission to perform this action: file::get")
	ErrFileListDenied   = berr.NewErrCodeMsg(3804, "You do not have permission to perform this action: file::list")
	ErrFileUpdateDenied = berr.NewErrCodeMsg(3805, "You do not have permission to perform this action: file::update")

	ErrFileUsageAddDenied    = berr.NewErrCodeMsg(3901, "You do not have permission to perform this action: file_usage::add")
	ErrFileUsageDeleteDenied = berr.NewErrCodeMsg(3902, "You do not have permission to perform this action: file_usage::delete")
	ErrFileUsageGetDenied    = berr.NewErrCodeMsg(3903, "You do not have permission to perform this action: file_usage::get")
	ErrFileUsageListDenied   = berr.NewErrCodeMsg(3904, "You do not have permission to perform this action: file_usage::list")
	ErrFileUsageUpdateDenied = berr.NewErrCodeMsg(3905, "You do not have permission to perform this action: file_usage::update")

	ErrInstallmentAddDenied    = berr.NewErrCodeMsg(4001, "You do not have permission to perform this action: installment::add")
	ErrInstallmentDeleteDenied = berr.NewErrCodeMsg(4002, "You do not have permission to perform this action: installment::delete")
	ErrInstallmentGetDenied    = berr.NewErrCodeMsg(4003, "You do not have permission to perform this action: installment::get")
	ErrInstallmentListDenied   = berr.NewErrCodeMsg(4004, "You do not have permission to perform this action: installment::list")
	ErrInstallmentUpdateDenied = berr.NewErrCodeMsg(4005, "You do not have permission to perform this action: installment::update")

	ErrInstallmentPlanAddDenied    = berr.NewErrCodeMsg(4101, "You do not have permission to perform this action: installment_plan::add")
	ErrInstallmentPlanDeleteDenied = berr.NewErrCodeMsg(4102, "You do not have permission to perform this action: installment_plan::delete")
	ErrInstallmentPlanGetDenied    = berr.NewErrCodeMsg(4103, "You do not have permission to perform this action: installment_plan::get")
	ErrInstallmentPlanListDenied   = berr.NewErrCodeMsg(4104, "You do not have permission to perform this action: installment_plan::list")
	ErrInstallmentPlanUpdateDenied = berr.NewErrCodeMsg(4105, "You do not have permission to perform this action: installment_plan::update")

	ErrLocationGetDenied = berr.NewErrCodeMsg(4201, "You do not have permission to perform this action: location::get")

	ErrMindCompleteDenied        = berr.NewErrCodeMsg(4301, "You do not have permission to perform this action: mind::complete")
	ErrMindContentCommandDenied  = berr.NewErrCodeMsg(4302, "You do not have permission to perform this action: mind::content_command")
	ErrMindContentContinueDenied = berr.NewErrCodeMsg(4303, "You do not have permission to perform this action: mind::content_continue")
	ErrMindContentImproveDenied  = berr.NewErrCodeMsg(4304, "You do not have permission to perform this action: mind::content_improve")
	ErrMindContentLongerDenied   = berr.NewErrCodeMsg(4305, "You do not have permission to perform this action: mind::content_longer")
	ErrMindContentShorterDenied  = berr.NewErrCodeMsg(4306, "You do not have permission to perform this action: mind::content_shorter")
	ErrMindGetCronDenied         = berr.NewErrCodeMsg(4307, "You do not have permission to perform this action: mind::get_cron")

	ErrNoteAddDenied    = berr.NewErrCodeMsg(4401, "You do not have permission to perform this action: note::add")
	ErrNoteDeleteDenied = berr.NewErrCodeMsg(4402, "You do not have permission to perform this action: note::delete")
	ErrNoteListDenied   = berr.NewErrCodeMsg(4403, "You do not have permission to perform this action: note::list")
	ErrNoteUpdateDenied = berr.NewErrCodeMsg(4404, "You do not have permission to perform this action: note::update")

	ErrOperationAllDenied = berr.NewErrCodeMsg(4501, "You do not have permission to perform this action: operation::all")

	ErrPersonAddDenied              = berr.NewErrCodeMsg(4601, "You do not have permission to perform this action: person::add")
	ErrPersonDeleteDenied           = berr.NewErrCodeMsg(4602, "You do not have permission to perform this action: person::delete")
	ErrPersonGetExpectedTasksDenied = berr.NewErrCodeMsg(4603, "You do not have permission to perform this action: person::get_expected_tasks")
	ErrPersonGetDenied              = berr.NewErrCodeMsg(4604, "You do not have permission to perform this action: person::get")
	ErrPersonIsPersonInDenied       = berr.NewErrCodeMsg(4605, "You do not have permission to perform this action: person::is_person_in")
	ErrPersonListDenied             = berr.NewErrCodeMsg(4606, "You do not have permission to perform this action: person::list")
	ErrPersonQueryDenied            = berr.NewErrCodeMsg(4607, "You do not have permission to perform this action: person::query")
	ErrPersonUpdateDenied           = berr.NewErrCodeMsg(4608, "You do not have permission to perform this action: person::update")

	ErrPersonAddPersonStageDenied    = berr.NewErrCodeMsg(4701, "You do not have permission to perform this action: person::add_person_stage")
	ErrPersonDeletePersonStageDenied = berr.NewErrCodeMsg(4702, "You do not have permission to perform this action: person::delete_person_stage")
	ErrPersonGetPersonStageDenied    = berr.NewErrCodeMsg(4703, "You do not have permission to perform this action: person::get_person_stage")
	ErrPersonUpdatePersonStageDenied = berr.NewErrCodeMsg(4704, "You do not have permission to perform this action: person::update_person_stage")

	ErrPersonHistoryMessageDenied  = berr.NewErrCodeMsg(4801, "You do not have permission to perform this action: person::history_message")
	ErrPersonSendMessageDenied     = berr.NewErrCodeMsg(4802, "You do not have permission to perform this action: person::send_message")
	ErrPersonMessageTemplateDenied = berr.NewErrCodeMsg(4803, "You do not have permission to perform this action: person::message_template")

	ErrPersonAssignmentAddDenied    = berr.NewErrCodeMsg(4901, "You do not have permission to perform this action: person_assignment::add")
	ErrPersonAssignmentDeleteDenied = berr.NewErrCodeMsg(4902, "You do not have permission to perform this action: person_assignment::delete")
	ErrPersonAssignmentUpdateDenied = berr.NewErrCodeMsg(4903, "You do not have permission to perform this action: person_assignment::update")

	ErrPersonDataClearDataDenied = berr.NewErrCodeMsg(5001, "You do not have permission to perform this action: person_data::clear_data")
	ErrPersonDataGetDataDenied   = berr.NewErrCodeMsg(5002, "You do not have permission to perform this action: person_data::get_data")
	ErrPersonDataSetDataDenied   = berr.NewErrCodeMsg(5003, "You do not have permission to perform this action: person_data::set_data")

	ErrPipelineAddDenied        = berr.NewErrCodeMsg(5101, "You do not have permission to perform this action: pipeline::add")
	ErrPipelineDeleteDenied     = berr.NewErrCodeMsg(5102, "You do not have permission to perform this action: pipeline::delete")
	ErrPipelineGetDenied        = berr.NewErrCodeMsg(5103, "You do not have permission to perform this action: pipeline::get")
	ErrPipelineListDenied       = berr.NewErrCodeMsg(5104, "You do not have permission to perform this action: pipeline::list")
	ErrPipelineSwitchDealDenied = berr.NewErrCodeMsg(5105, "You do not have permission to perform this action: pipeline::switch_deal")
	ErrPipelineUpdateDenied     = berr.NewErrCodeMsg(5106, "You do not have permission to perform this action: pipeline::update")

	ErrProductAddDenied    = berr.NewErrCodeMsg(5201, "You do not have permission to perform this action: product::add")
	ErrProductDeleteDenied = berr.NewErrCodeMsg(5202, "You do not have permission to perform this action: product::delete")
	ErrProductGetDenied    = berr.NewErrCodeMsg(5203, "You do not have permission to perform this action: product::get")
	ErrProductListDenied   = berr.NewErrCodeMsg(5204, "You do not have permission to perform this action: product::list")
	ErrProductUpdateDenied = berr.NewErrCodeMsg(5205, "You do not have permission to perform this action: product::update")

	ErrProductOperationAddDenied    = berr.NewErrCodeMsg(5301, "You do not have permission to perform this action: product_operation::add")
	ErrProductOperationDeleteDenied = berr.NewErrCodeMsg(5302, "You do not have permission to perform this action: product_operation::delete")

	ErrProductPriceAddDenied    = berr.NewErrCodeMsg(5401, "You do not have permission to perform this action: product_price::add")
	ErrProductPriceDeleteDenied = berr.NewErrCodeMsg(5402, "You do not have permission to perform this action: product_price::delete")
	ErrProductPriceUpdateDenied = berr.NewErrCodeMsg(5403, "You do not have permission to perform this action: product_price::update")

	ErrPublicFromAddDenied = berr.NewErrCodeMsg(5501, "You do not have permission to perform this action: public::from_add")

	ErrQueryQueryDenied = berr.NewErrCodeMsg(5601, "You do not have permission to perform this action: query::query")

	ErrQueryRawQueryDenied = berr.NewErrCodeMsg(5701, "You do not have permission to perform this action: query::raw_query")

	ErrQuoteAddDenied    = berr.NewErrCodeMsg(5801, "You do not have permission to perform this action: quote::add")
	ErrQuoteDeleteDenied = berr.NewErrCodeMsg(5802, "You do not have permission to perform this action: quote::delete")
	ErrQuoteListDenied   = berr.NewErrCodeMsg(5803, "You do not have permission to perform this action: quote::list")
	ErrQuoteUpdateDenied = berr.NewErrCodeMsg(5804, "You do not have permission to perform this action: quote::update")

	ErrQuoteItemAddDenied    = berr.NewErrCodeMsg(5901, "You do not have permission to perform this action: quote_item::add")
	ErrQuoteItemDeleteDenied = berr.NewErrCodeMsg(5902, "You do not have permission to perform this action: quote_item::delete")
	ErrQuoteItemListDenied   = berr.NewErrCodeMsg(5903, "You do not have permission to perform this action: quote_item::list")
	ErrQuoteItemUpdateDenied = berr.NewErrCodeMsg(5904, "You do not have permission to perform this action: quote_item::update")

	ErrReferralAddDenied            = berr.NewErrCodeMsg(6001, "You do not have permission to perform this action: referral::add")
	ErrReferralDeleteDenied         = berr.NewErrCodeMsg(6002, "You do not have permission to perform this action: referral::delete")
	ErrReferralSearchReferrerDenied = berr.NewErrCodeMsg(6003, "You do not have permission to perform this action: referral::search_referrer")
	ErrReferralUpdateDenied         = berr.NewErrCodeMsg(6004, "You do not have permission to perform this action: referral::update")

	ErrScheduleAddDenied             = berr.NewErrCodeMsg(6101, "You do not have permission to perform this action: schedule::add")
	ErrScheduleDeleteDenied          = berr.NewErrCodeMsg(6102, "You do not have permission to perform this action: schedule::delete")
	ErrScheduleGetWorkScheduleDenied = berr.NewErrCodeMsg(6103, "You do not have permission to perform this action: schedule::get_work_schedule")
	ErrScheduleListDenied            = berr.NewErrCodeMsg(6104, "You do not have permission to perform this action: schedule::list")
	ErrScheduleUpdateDenied          = berr.NewErrCodeMsg(6105, "You do not have permission to perform this action: schedule::update")

	ErrSettingAddDenied    = berr.NewErrCodeMsg(6201, "You do not have permission to perform this action: setting::add")
	ErrSettingDeleteDenied = berr.NewErrCodeMsg(6202, "You do not have permission to perform this action: setting::delete")
	ErrSettingListDenied   = berr.NewErrCodeMsg(6203, "You do not have permission to perform this action: setting::list")
	ErrSettingUpdateDenied = berr.NewErrCodeMsg(6204, "You do not have permission to perform this action: setting::update")

	ErrStageAddDenied         = berr.NewErrCodeMsg(6301, "You do not have permission to perform this action: stage::add")
	ErrStageDeleteDenied      = berr.NewErrCodeMsg(6302, "You do not have permission to perform this action: stage::delete")
	ErrStageGetDenied         = berr.NewErrCodeMsg(6303, "You do not have permission to perform this action: stage::get")
	ErrStageListDenied        = berr.NewErrCodeMsg(6304, "You do not have permission to perform this action: stage::list")
	ErrStageUpdateDenied      = berr.NewErrCodeMsg(6305, "You do not have permission to perform this action: stage::update")
	ErrStageUpdateStageDenied = berr.NewErrCodeMsg(6306, "You do not have permission to perform this action: stage::update_stage")

	ErrSyncSyncDataDenied    = berr.NewErrCodeMsg(6401, "You do not have permission to perform this action: sync::sync_data")
	ErrSyncSyncDataSanDenied = berr.NewErrCodeMsg(6402, "You do not have permission to perform this action: sync::sync_data_san")
	ErrSyncSyncDataUpDenied  = berr.NewErrCodeMsg(6403, "You do not have permission to perform this action: sync::sync_data_up")

	ErrTaskCreateDenied           = berr.NewErrCodeMsg(6501, "You do not have permission to perform this action: task::add")
	ErrTaskBulkDeleteDenied       = berr.NewErrCodeMsg(6502, "You do not have permission to perform this action: task::bulk_delete")
	ErrTaskBulkUpdateStatusDenied = berr.NewErrCodeMsg(6503, "You do not have permission to perform this action: task::bulk_update_status")
	ErrTaskDeleteDenied           = berr.NewErrCodeMsg(6504, "You do not have permission to perform this action: task::delete")
	ErrTaskGetDenied              = berr.NewErrCodeMsg(6505, "You do not have permission to perform this action: task::get")
	ErrTaskListDenied             = berr.NewErrCodeMsg(6506, "You do not have permission to perform this action: task::list")
	ErrTaskQueryDenied            = berr.NewErrCodeMsg(6507, "You do not have permission to perform this action: task::query")
	ErrTaskUpdateDenied           = berr.NewErrCodeMsg(6508, "You do not have permission to perform this action: task::update")

	ErrTaskAddNoteDenied    = berr.NewErrCodeMsg(6601, "You do not have permission to perform this action: task::add_note")
	ErrTaskDeleteNoteDenied = berr.NewErrCodeMsg(6602, "You do not have permission to perform this action: task::delete_note")
	ErrTaskUpdateNoteDenied = berr.NewErrCodeMsg(6603, "You do not have permission to perform this action: task::update_note")

	ErrTaskAssignmentAddDenied         = berr.NewErrCodeMsg(6701, "You do not have permission to perform this action: task_assignment::add")
	ErrTaskAssignmentAssignTasksDenied = berr.NewErrCodeMsg(6702, "You do not have permission to perform this action: task_assignment::assign_tasks")
	ErrTaskAssignmentDeleteDenied      = berr.NewErrCodeMsg(6703, "You do not have permission to perform this action: task_assignment::delete")
	ErrTaskAssignmentQueryDenied       = berr.NewErrCodeMsg(6704, "You do not have permission to perform this action: task_assignment::query")
	ErrTaskAssignmentUpdateDenied      = berr.NewErrCodeMsg(6705, "You do not have permission to perform this action: task_assignment::update")

	ErrTaskDepartmentAddDenied    = berr.NewErrCodeMsg(6801, "You do not have permission to perform this action: task_department::add")
	ErrTaskDepartmentDeleteDenied = berr.NewErrCodeMsg(6802, "You do not have permission to perform this action: task_department::delete")
	ErrTaskDepartmentQueryDenied  = berr.NewErrCodeMsg(6803, "You do not have permission to perform this action: task_department::query")
	ErrTaskDepartmentUpdateDenied = berr.NewErrCodeMsg(6804, "You do not have permission to perform this action: task_department::update")

	ErrTaxonomyTermAddDenied    = berr.NewErrCodeMsg(6901, "You do not have permission to perform this action: taxonomy::term_add")
	ErrTaxonomyTermDeleteDenied = berr.NewErrCodeMsg(6902, "You do not have permission to perform this action: taxonomy::term_delete")
	ErrTaxonomyTermGetDenied    = berr.NewErrCodeMsg(6903, "You do not have permission to perform this action: taxonomy::term_get")
	ErrTaxonomyTermListDenied   = berr.NewErrCodeMsg(6904, "You do not have permission to perform this action: taxonomy::term_list")
	ErrTaxonomyTermUpdateDenied = berr.NewErrCodeMsg(6905, "You do not have permission to perform this action: taxonomy::term_update")

	ErrTrackAddDenied    = berr.NewErrCodeMsg(7001, "You do not have permission to perform this action: track::add")
	ErrTrackDeleteDenied = berr.NewErrCodeMsg(7002, "You do not have permission to perform this action: track::delete")
	ErrTrackListDenied   = berr.NewErrCodeMsg(7003, "You do not have permission to perform this action: track::list")
	ErrTrackUpdateDenied = berr.NewErrCodeMsg(7004, "You do not have permission to perform this action: track::update")

	ErrUserAddDenied    = berr.NewErrCodeMsg(7101, "You do not have permission to perform this action: user::add")
	ErrUserDeleteDenied = berr.NewErrCodeMsg(7102, "You do not have permission to perform this action: user::delete")
	ErrUserGetDenied    = berr.NewErrCodeMsg(7103, "You do not have permission to perform this action: user::get")
	ErrUserListDenied   = berr.NewErrCodeMsg(7104, "You do not have permission to perform this action: user::list")
	ErrUserUpdateDenied = berr.NewErrCodeMsg(7105, "You do not have permission to perform this action: user::update")

	ErrUserDataClearDataDenied = berr.NewErrCodeMsg(7201, "You do not have permission to perform this action: user_data::clear_data")
	ErrUserDataGetDataDenied   = berr.NewErrCodeMsg(7202, "You do not have permission to perform this action: user_data::get_data")
	ErrUserDataSetDataDenied   = berr.NewErrCodeMsg(7203, "You do not have permission to perform this action: user_data::set_data")
)
