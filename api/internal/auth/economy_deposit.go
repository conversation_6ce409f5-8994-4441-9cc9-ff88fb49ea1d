package auth

import (
	"bcare/common/bauth"
	"bcare/ent"
	"context"
	"github.com/zeromicro/go-zero/core/logx"
)

type IEconomyDepositAuth interface {
	CanAdd(ctx context.Context, r *ent.Deposit) bool
	CanAllocationAdd(ctx context.Context, r *ent.Deposit) bool
	CanAllocationDelete(ctx context.Context, r *ent.Deposit) bool
	CanCancelConversion(ctx context.Context, r *ent.Deposit) bool
	CanConvertToPayment(ctx context.Context, r *ent.Deposit) bool
	CanDelete(ctx context.Context, r *ent.Deposit) bool
	CanGet(ctx context.Context, r *ent.Deposit) bool
	CanList(ctx context.Context, r *ent.Deposit) bool
	CanUpdate(ctx context.Context, r *ent.Deposit) bool
}

type EconomyDepositAuth struct {
	authSvc    *bauth.Authorizer
	identityFn func(ctx context.Context) string
}

func (a EconomyDepositAuth) CanAdd(ctx context.Context, r *ent.Deposit) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "economy_deposit::add", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce economy_deposit::add fail: %v", err)
		return false
	}
	return can
}

func (a EconomyDepositAuth) CanAllocationAdd(ctx context.Context, r *ent.Deposit) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "economy_deposit::allocation_add", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce economy_deposit::allocation_add fail: %v", err)
		return false
	}
	return can
}

func (a EconomyDepositAuth) CanAllocationDelete(ctx context.Context, r *ent.Deposit) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "economy_deposit::allocation_delete", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce economy_deposit::allocation_delete fail: %v", err)
		return false
	}
	return can
}

func (a EconomyDepositAuth) CanCancelConversion(ctx context.Context, r *ent.Deposit) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "economy_deposit::cancel_conversion", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce economy_deposit::cancel_conversion fail: %v", err)
		return false
	}
	return can
}

func (a EconomyDepositAuth) CanConvertToPayment(ctx context.Context, r *ent.Deposit) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "economy_deposit::convert_to_payment", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce economy_deposit::convert_to_payment fail: %v", err)
		return false
	}
	return can
}

func (a EconomyDepositAuth) CanDelete(ctx context.Context, r *ent.Deposit) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "economy_deposit::delete", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce economy_deposit::delete fail: %v", err)
		return false
	}
	return can
}

func (a EconomyDepositAuth) CanGet(ctx context.Context, r *ent.Deposit) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "economy_deposit::get", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce economy_deposit::get fail: %v", err)
		return false
	}
	return can
}

func (a EconomyDepositAuth) CanList(ctx context.Context, r *ent.Deposit) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "economy_deposit::list", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce economy_deposit::list fail: %v", err)
		return false
	}
	return can
}

func (a EconomyDepositAuth) CanUpdate(ctx context.Context, r *ent.Deposit) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "economy_deposit::update", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce economy_deposit::update fail: %v", err)
		return false
	}
	return can
}

func NewEconomyDepositAuth(auth *bauth.Authorizer, iFn func(ctx context.Context) string) IEconomyDepositAuth {
	return &EconomyDepositAuth{authSvc: auth, identityFn: iFn}
}
