package auth

import (
	"bcare/common/bauth"
	"bcare/ent"
	"context"
	"github.com/zeromicro/go-zero/core/logx"
)

type ITrackAuth interface {
	CanAdd(ctx context.Context, r *ent.Track) bool
	CanDelete(ctx context.Context, r *ent.Track) bool
	CanList(ctx context.Context, r *ent.Track) bool
	CanUpdate(ctx context.Context, r *ent.Track) bool
}

type TrackAuth struct {
	authSvc    *bauth.Authorizer
	identityFn func(ctx context.Context) string
}

func (a TrackAuth) CanAdd(ctx context.Context, r *ent.Track) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "track::add", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce track::add fail: %v", err)
		return false
	}
	return can
}

func (a TrackAuth) CanDelete(ctx context.Context, r *ent.Track) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "track::delete", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce track::delete fail: %v", err)
		return false
	}
	return can
}

func (a TrackAuth) CanList(ctx context.Context, r *ent.Track) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "track::list", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce track::list fail: %v", err)
		return false
	}
	return can
}

func (a TrackAuth) CanUpdate(ctx context.Context, r *ent.Track) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "track::update", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce track::update fail: %v", err)
		return false
	}
	return can
}

func NewTrackAuth(auth *bauth.Authorizer, iFn func(ctx context.Context) string) ITrackAuth {
	return &TrackAuth{authSvc: auth, identityFn: iFn}
}
