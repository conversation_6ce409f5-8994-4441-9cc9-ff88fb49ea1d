package auth

import (
	"bcare/api/internal/dto"
	"bcare/common/bauth"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type IDiscountAuth interface {
	CanAdd(ctx context.Context) bool
	CanCalculateDiscount(ctx context.Context) bool
	CanDelete(ctx context.Context, r *dto.DiscountOutput) bool
	CanEligibleDiscount(ctx context.Context) bool
	CanGet(ctx context.Context, r *dto.DiscountOutput) bool
	CanList(ctx context.Context) bool
	CanUpdate(ctx context.Context, r *dto.DiscountOutput) bool
}

type DiscountAuth struct {
	authSvc    *bauth.Authorizer
	identityFn func(ctx context.Context) string
}

func (a DiscountAuth) CanAdd(ctx context.Context) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "discount::add", "*")
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce discount::add fail: %v", err)
		return false
	}
	return can
}

func (a DiscountAuth) CanCalculateDiscount(ctx context.Context) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "discount::calculate_discount", "*")
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce discount::calculate_discount fail: %v", err)
		return false
	}
	return can
}

func (a DiscountAuth) CanDelete(ctx context.Context, r *dto.DiscountOutput) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "discount::delete", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce discount::delete fail: %v", err)
		return false
	}
	return can
}

func (a DiscountAuth) CanEligibleDiscount(ctx context.Context) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "discount::eligible_discount", "*")
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce discount::eligible_discount fail: %v", err)
		return false
	}
	return can
}

func (a DiscountAuth) CanGet(ctx context.Context, r *dto.DiscountOutput) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "discount::get", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce discount::get fail: %v", err)
		return false
	}
	return can
}

func (a DiscountAuth) CanList(ctx context.Context) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "discount::list", "*")
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce discount::list fail: %v", err)
		return false
	}
	return can
}

func (a DiscountAuth) CanUpdate(ctx context.Context, r *dto.DiscountOutput) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "discount::update", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce discount::update fail: %v", err)
		return false
	}
	return can
}

func NewDiscountAuth(auth *bauth.Authorizer, iFn func(ctx context.Context) string) IDiscountAuth {
	return &DiscountAuth{authSvc: auth, identityFn: iFn}
}
