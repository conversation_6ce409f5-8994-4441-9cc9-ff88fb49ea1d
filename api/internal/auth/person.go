package auth

import (
	"bcare/common/bauth"
	"bcare/ent"
	"context"
	"github.com/zeromicro/go-zero/core/logx"
)

type IPersonAuth interface {
	CanAdd(ctx context.Context, r *ent.Person) bool
	CanDelete(ctx context.Context, r *ent.Person) bool
	CanGetExpectedTasks(ctx context.Context, r *ent.Person) bool
	CanGet(ctx context.Context, r *ent.Person) bool
	CanIsPersonIn(ctx context.Context, r *ent.Person) bool
	CanList(ctx context.Context, r *ent.Person) bool
	CanQuery(ctx context.Context, r *ent.Person) bool
	CanUpdate(ctx context.Context, r *ent.Person) bool
}

type PersonAuth struct {
	authSvc    *bauth.Authorizer
	identityFn func(ctx context.Context) string
}

func (a PersonAuth) CanAdd(ctx context.Context, r *ent.Person) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "person::add", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce person::add fail: %v", err)
		return false
	}
	return can
}

func (a PersonAuth) CanDelete(ctx context.Context, r *ent.Person) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "person::delete", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce person::delete fail: %v", err)
		return false
	}
	return can
}

func (a PersonAuth) CanGetExpectedTasks(ctx context.Context, r *ent.Person) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "person::get_expected_tasks", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce person::get_expected_tasks fail: %v", err)
		return false
	}
	return can
}

func (a PersonAuth) CanGet(ctx context.Context, r *ent.Person) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "person::get", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce person::get fail: %v", err)
		return false
	}
	return can
}

func (a PersonAuth) CanIsPersonIn(ctx context.Context, r *ent.Person) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "person::is_person_in", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce person::is_person_in fail: %v", err)
		return false
	}
	return can
}

func (a PersonAuth) CanList(ctx context.Context, r *ent.Person) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "person::list", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce person::list fail: %v", err)
		return false
	}
	return can
}

func (a PersonAuth) CanQuery(ctx context.Context, r *ent.Person) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "person::query", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce person::query fail: %v", err)
		return false
	}
	return can
}

func (a PersonAuth) CanUpdate(ctx context.Context, r *ent.Person) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "person::update", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce person::update fail: %v", err)
		return false
	}
	return can
}

func NewPersonAuth(auth *bauth.Authorizer, iFn func(ctx context.Context) string) IPersonAuth {
	return &PersonAuth{authSvc: auth, identityFn: iFn}
}
