package auth

import (
	"bcare/common/bauth"
	"bcare/ent"
	"context"
	"github.com/zeromicro/go-zero/core/logx"
)

type IPersonDataAuth interface {
	CanClearData(ctx context.Context, r *ent.PersonData) bool
	CanGetData(ctx context.Context, r *ent.PersonData) bool
	CanSetData(ctx context.Context, r *ent.PersonData) bool
}

type PersonDataAuth struct {
	authSvc    *bauth.Authorizer
	identityFn func(ctx context.Context) string
}

func (a PersonDataAuth) CanClearData(ctx context.Context, r *ent.PersonData) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "person_data::clear_data", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce person_data::clear_data fail: %v", err)
		return false
	}
	return can
}

func (a PersonDataAuth) CanGetData(ctx context.Context, r *ent.PersonData) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "person_data::get_data", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce person_data::get_data fail: %v", err)
		return false
	}
	return can
}

func (a PersonDataAuth) CanSetData(ctx context.Context, r *ent.PersonData) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "person_data::set_data", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce person_data::set_data fail: %v", err)
		return false
	}
	return can
}

func NewPersonDataAuth(auth *bauth.Authorizer, iFn func(ctx context.Context) string) IPersonDataAuth {
	return &PersonDataAuth{authSvc: auth, identityFn: iFn}
}
