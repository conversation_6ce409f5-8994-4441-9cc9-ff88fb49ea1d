package auth

import (
	"bcare/common/bauth"
	"bcare/ent"
	"context"
	"github.com/zeromicro/go-zero/core/logx"
)

type IAttachmentAuth interface {
	CanAdd(ctx context.Context, r *ent.Attachment) bool
	CanChildren(ctx context.Context, r *ent.Attachment) bool
	CanDelete(ctx context.Context, r *ent.Attachment) bool
	CanGet(ctx context.Context, r *ent.Attachment) bool
	CanList(ctx context.Context, r *ent.Attachment) bool
	CanQuery(ctx context.Context, r *ent.Attachment) bool
	CanUpdate(ctx context.Context, r *ent.Attachment) bool
}

type AttachmentAuth struct {
	authSvc    *bauth.Authorizer
	identityFn func(ctx context.Context) string
}

func (a AttachmentAuth) CanAdd(ctx context.Context, r *ent.Attachment) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "attachment::add", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce attachment::add fail: %v", err)
		return false
	}
	return can
}

func (a AttachmentAuth) CanChildren(ctx context.Context, r *ent.Attachment) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "attachment::children", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce attachment::children fail: %v", err)
		return false
	}
	return can
}

func (a AttachmentAuth) CanDelete(ctx context.Context, r *ent.Attachment) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "attachment::delete", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce attachment::delete fail: %v", err)
		return false
	}
	return can
}

func (a AttachmentAuth) CanGet(ctx context.Context, r *ent.Attachment) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "attachment::get", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce attachment::get fail: %v", err)
		return false
	}
	return can
}

func (a AttachmentAuth) CanList(ctx context.Context, r *ent.Attachment) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "attachment::list", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce attachment::list fail: %v", err)
		return false
	}
	return can
}

func (a AttachmentAuth) CanQuery(ctx context.Context, r *ent.Attachment) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "attachment::query", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce attachment::query fail: %v", err)
		return false
	}
	return can
}

func (a AttachmentAuth) CanUpdate(ctx context.Context, r *ent.Attachment) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "attachment::update", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce attachment::update fail: %v", err)
		return false
	}
	return can
}

func NewAttachmentAuth(auth *bauth.Authorizer, iFn func(ctx context.Context) string) IAttachmentAuth {
	return &AttachmentAuth{authSvc: auth, identityFn: iFn}
}
