package auth

import (
	"bcare/common/bauth"
	"bcare/ent"
	"context"
	"github.com/zeromicro/go-zero/core/logx"
)

type ICallAuth interface {
	CanAccept(ctx context.Context, r *ent.Call) bool
	CanAdd(ctx context.Context, r *ent.Call) bool
	CanDelete(ctx context.Context, r *ent.Call) bool
	CanEnd(ctx context.Context, r *ent.Call) bool
	CanGet(ctx context.Context, r *ent.Call) bool
	CanList(ctx context.Context, r *ent.Call) bool
	CanQuery(ctx context.Context, r *ent.Call) bool
	CanUpdateFeedback(ctx context.Context, r *ent.Call) bool
}

type CallAuth struct {
	authSvc    *bauth.Authorizer
	identityFn func(ctx context.Context) string
}

func (a CallAuth) CanAccept(ctx context.Context, r *ent.Call) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "call::accept", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce call::accept fail: %v", err)
		return false
	}
	return can
}

func (a CallAuth) CanAdd(ctx context.Context, r *ent.Call) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "call::add", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce call::add fail: %v", err)
		return false
	}
	return can
}

func (a CallAuth) CanDelete(ctx context.Context, r *ent.Call) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "call::delete", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce call::delete fail: %v", err)
		return false
	}
	return can
}

func (a CallAuth) CanEnd(ctx context.Context, r *ent.Call) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "call::end", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce call::end fail: %v", err)
		return false
	}
	return can
}

func (a CallAuth) CanGet(ctx context.Context, r *ent.Call) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "call::get", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce call::get fail: %v", err)
		return false
	}
	return can
}

func (a CallAuth) CanList(ctx context.Context, r *ent.Call) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "call::list", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce call::list fail: %v", err)
		return false
	}
	return can
}

func (a CallAuth) CanQuery(ctx context.Context, r *ent.Call) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "call::query", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce call::query fail: %v", err)
		return false
	}
	return can
}

func (a CallAuth) CanUpdateFeedback(ctx context.Context, r *ent.Call) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "call::update_feedback", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce call::update_feedback fail: %v", err)
		return false
	}
	return can
}

func NewCallAuth(auth *bauth.Authorizer, iFn func(ctx context.Context) string) ICallAuth {
	return &CallAuth{authSvc: auth, identityFn: iFn}
}
