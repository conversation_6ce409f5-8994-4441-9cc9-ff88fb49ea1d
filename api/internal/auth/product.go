package auth

import (
	"bcare/api/internal/dto"
	"bcare/common/bauth"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type IProductAuth interface {
	CanAdd(ctx context.Context) bool
	CanDelete(ctx context.Context, r *dto.ProductOutput) bool
	CanGet(ctx context.Context, r *dto.ProductOutput) bool
	CanList(ctx context.Context) bool
	CanUpdate(ctx context.Context, r *dto.ProductOutput) bool
}

type ProductAuth struct {
	authSvc    *bauth.Authorizer
	identityFn func(ctx context.Context) string
}

func (a ProductAuth) CanAdd(ctx context.Context) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "product::add", "*")
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce product::add fail: %v", err)
		return false
	}
	return can
}

func (a ProductAuth) CanDelete(ctx context.Context, r *dto.ProductOutput) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "product::delete", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce product::delete fail: %v", err)
		return false
	}
	return can
}

func (a ProductAuth) CanGet(ctx context.Context, r *dto.ProductOutput) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "product::get", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce product::get fail: %v", err)
		return false
	}
	return can
}

func (a ProductAuth) CanList(ctx context.Context) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "product::list", "*")
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce product::list fail: %v", err)
		return false
	}
	return can
}

func (a ProductAuth) CanUpdate(ctx context.Context, r *dto.ProductOutput) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "product::update", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce product::update fail: %v", err)
		return false
	}
	return can
}

func NewProductAuth(auth *bauth.Authorizer, iFn func(ctx context.Context) string) IProductAuth {
	return &ProductAuth{authSvc: auth, identityFn: iFn}
}
