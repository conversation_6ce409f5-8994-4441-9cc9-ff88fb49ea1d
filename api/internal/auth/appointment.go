package auth

import (
	"bcare/api/internal/dto"
	"bcare/common/bauth"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type IAppointmentAuth interface {
	CanAdd(ctx context.Context) bool
	CanDelete(ctx context.Context, r *dto.AppointmentOutput) bool
	CanGet(ctx context.Context, r *dto.AppointmentOutput) bool
	CanGetLasted(ctx context.Context, r *dto.AppointmentOutput) bool
	CanList(ctx context.Context) bool
	CanQuery(ctx context.Context) bool
	CanUpdate(ctx context.Context, r *dto.AppointmentOutput) bool
}

type AppointmentAuth struct {
	authSvc    *bauth.Authorizer
	identityFn func(ctx context.Context) string
}

func (a AppointmentAuth) CanAdd(ctx context.Context) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "appointment::add", "*")
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce appointment::add fail: %v", err)
		return false
	}
	return can
}

func (a AppointmentAuth) CanDelete(ctx context.Context, r *dto.AppointmentOutput) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "appointment::delete", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce appointment::delete fail: %v", err)
		return false
	}
	return can
}

func (a AppointmentAuth) CanGet(ctx context.Context, r *dto.AppointmentOutput) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "appointment::get", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce appointment::get fail: %v", err)
		return false
	}
	return can
}

func (a AppointmentAuth) CanGetLasted(ctx context.Context, r *dto.AppointmentOutput) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "appointment::get_lasted", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce appointment::get_lasted fail: %v", err)
		return false
	}
	return can
}

func (a AppointmentAuth) CanList(ctx context.Context) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "appointment::list", "*")
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce appointment::list fail: %v", err)
		return false
	}
	return can
}

func (a AppointmentAuth) CanQuery(ctx context.Context) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "appointment::query", "*")
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce appointment::query fail: %v", err)
		return false
	}
	return can
}

func (a AppointmentAuth) CanUpdate(ctx context.Context, r *dto.AppointmentOutput) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "appointment::update", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce appointment::update fail: %v", err)
		return false
	}
	return can
}

func NewAppointmentAuth(auth *bauth.Authorizer, iFn func(ctx context.Context) string) IAppointmentAuth {
	return &AppointmentAuth{authSvc: auth, identityFn: iFn}
}
