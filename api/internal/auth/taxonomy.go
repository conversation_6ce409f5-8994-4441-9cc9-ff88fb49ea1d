package auth

import (
	"bcare/common/bauth"
	"bcare/ent"
	"context"
	"github.com/zeromicro/go-zero/core/logx"
)

type ITermAuth interface {
	CanTermAdd(ctx context.Context, r *ent.Term) bool
	CanTermDelete(ctx context.Context, r *ent.Term) bool
	CanTermGet(ctx context.Context, r *ent.Term) bool
	CanTermList(ctx context.Context, r *ent.Term) bool
	CanTermUpdate(ctx context.Context, r *ent.Term) bool
}

type TermAuth struct {
	authSvc    *bauth.Authorizer
	identityFn func(ctx context.Context) string
}

func (a TermAuth) CanTermAdd(ctx context.Context, r *ent.Term) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), r, "Term::term_add")
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce Term::term_add fail: %v", err)
		return false
	}
	return can
}

func (a TermAuth) CanTermDelete(ctx context.Context, r *ent.Term) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), r, "Term::term_delete")
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce Term::term_delete fail: %v", err)
		return false
	}
	return can
}

func (a TermAuth) CanTermGet(ctx context.Context, r *ent.Term) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), r, "Term::term_get")
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce Term::term_get fail: %v", err)
		return false
	}
	return can
}

func (a TermAuth) CanTermList(ctx context.Context, r *ent.Term) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), r, "Term::term_list")
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce Term::term_list fail: %v", err)
		return false
	}
	return can
}

func (a TermAuth) CanTermUpdate(ctx context.Context, r *ent.Term) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), r, "Term::term_update")
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce Term::term_update fail: %v", err)
		return false
	}
	return can
}

func NewTermAuth(auth *bauth.Authorizer, iFn func(ctx context.Context) string) ITermAuth {
	return &TermAuth{authSvc: auth, identityFn: iFn}
}
