package auth

import (
	"bcare/api/internal/dto"
	"bcare/common/bauth"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type ICashFlowItemAuth interface {
	CanAdd(ctx context.Context) bool
	CanDelete(ctx context.Context, r *dto.CashFlowItemWithRelationsOutput) bool
	CanGet(ctx context.Context, r *dto.CashFlowItemWithRelationsOutput) bool
	CanList(ctx context.Context) bool
	CanUpdate(ctx context.Context, r *dto.CashFlowItemWithRelationsOutput) bool
}

type CashFlowItemAuth struct {
	authSvc    *bauth.Authorizer
	identityFn func(ctx context.Context) string
}

func (a CashFlowItemAuth) CanAdd(ctx context.Context) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "cashflow::add_item", "*")
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce cashflow_item::add fail: %v", err)
		return false
	}
	return can
}

func (a CashFlowItemAuth) CanDelete(ctx context.Context, r *dto.CashFlowItemWithRelationsOutput) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "cashflow::delete_item", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce cashflow_item::delete fail: %v", err)
		return false
	}
	return can
}

func (a CashFlowItemAuth) CanGet(ctx context.Context, r *dto.CashFlowItemWithRelationsOutput) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "cashflow::get_item", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce cashflow_item::get fail: %v", err)
		return false
	}
	return can
}

func (a CashFlowItemAuth) CanList(ctx context.Context) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "cashflow::list_item", "*")
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce cashflow_item::list fail: %v", err)
		return false
	}
	return can
}

func (a CashFlowItemAuth) CanUpdate(ctx context.Context, r *dto.CashFlowItemWithRelationsOutput) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "cashflow::update_item", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce cashflow_item::update fail: %v", err)
		return false
	}
	return can
}

func NewCashFlowItemAuth(auth *bauth.Authorizer, iFn func(ctx context.Context) string) ICashFlowItemAuth {
	return &CashFlowItemAuth{authSvc: auth, identityFn: iFn}
}
