package auth

import (
	"bcare/common/bauth"
	"bcare/ent"
	"context"
	"github.com/zeromicro/go-zero/core/logx"
)

type IEconomyPaymentAuth interface {
	CanAdd(ctx context.Context, r *ent.Payment) bool
	CanDelete(ctx context.Context, r *ent.Payment) bool
	CanGet(ctx context.Context, r *ent.Payment) bool
	CanList(ctx context.Context, r *ent.Payment) bool
	CanUpdate(ctx context.Context, r *ent.Payment) bool
}

type EconomyPaymentAuth struct {
	authSvc    *bauth.Authorizer
	identityFn func(ctx context.Context) string
}

func (a EconomyPaymentAuth) CanAdd(ctx context.Context, r *ent.Payment) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "economy_payment::add", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce economy_payment::add fail: %v", err)
		return false
	}
	return can
}

func (a EconomyPaymentAuth) CanDelete(ctx context.Context, r *ent.Payment) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "economy_payment::delete", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce economy_payment::delete fail: %v", err)
		return false
	}
	return can
}

func (a EconomyPaymentAuth) CanGet(ctx context.Context, r *ent.Payment) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "economy_payment::get", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce economy_payment::get fail: %v", err)
		return false
	}
	return can
}

func (a EconomyPaymentAuth) CanList(ctx context.Context, r *ent.Payment) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "economy_payment::list", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce economy_payment::list fail: %v", err)
		return false
	}
	return can
}

func (a EconomyPaymentAuth) CanUpdate(ctx context.Context, r *ent.Payment) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "economy_payment::update", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce economy_payment::update fail: %v", err)
		return false
	}
	return can
}

func NewEconomyPaymentAuth(auth *bauth.Authorizer, iFn func(ctx context.Context) string) IEconomyPaymentAuth {
	return &EconomyPaymentAuth{authSvc: auth, identityFn: iFn}
}
