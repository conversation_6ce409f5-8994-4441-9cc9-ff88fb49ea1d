package auth

import (
	"bcare/common/bauth"
	"bcare/ent"
	"context"
	"github.com/zeromicro/go-zero/core/logx"
)

type IDealUserAuth interface {
	CanAdd(ctx context.Context, r *ent.DealUser) bool
	CanDelete(ctx context.Context, r *ent.DealUser) bool
	CanList(ctx context.Context, r *ent.DealUser) bool
	CanUpdate(ctx context.Context, r *ent.DealUser) bool
}

type DealUserAuth struct {
	authSvc    *bauth.Authorizer
	identityFn func(ctx context.Context) string
}

func (a DealUserAuth) CanAdd(ctx context.Context, r *ent.DealUser) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "deal_user::add", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce deal_user::add fail: %v", err)
		return false
	}
	return can
}

func (a DealUserAuth) CanDelete(ctx context.Context, r *ent.DealUser) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "deal_user::delete", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce deal_user::delete fail: %v", err)
		return false
	}
	return can
}

func (a DealUserAuth) CanList(ctx context.Context, r *ent.DealUser) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "deal_user::list", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce deal_user::list fail: %v", err)
		return false
	}
	return can
}

func (a DealUserAuth) CanUpdate(ctx context.Context, r *ent.DealUser) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "deal_user::update", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce deal_user::update fail: %v", err)
		return false
	}
	return can
}

func NewDealUserAuth(auth *bauth.Authorizer, iFn func(ctx context.Context) string) IDealUserAuth {
	return &DealUserAuth{authSvc: auth, identityFn: iFn}
}
