package auth

import (
	"bcare/common/bauth"
	"bcare/ent"
	"context"
	"github.com/zeromicro/go-zero/core/logx"
)

type ISettingAuth interface {
	CanAdd(ctx context.Context, r *ent.Setting) bool
	CanDelete(ctx context.Context, r *ent.Setting) bool
	CanList(ctx context.Context, r *ent.Setting) bool
	CanUpdate(ctx context.Context, r *ent.Setting) bool
}

type SettingAuth struct {
	authSvc    *bauth.Authorizer
	identityFn func(ctx context.Context) string
}

func (a SettingAuth) CanAdd(ctx context.Context, r *ent.Setting) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "setting::add", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce setting::add fail: %v", err)
		return false
	}
	return can
}

func (a SettingAuth) CanDelete(ctx context.Context, r *ent.Setting) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "setting::delete", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce setting::delete fail: %v", err)
		return false
	}
	return can
}

func (a SettingAuth) CanList(ctx context.Context, r *ent.Setting) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "setting::list", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce setting::list fail: %v", err)
		return false
	}
	return can
}

func (a SettingAuth) CanUpdate(ctx context.Context, r *ent.Setting) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "setting::update", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce setting::update fail: %v", err)
		return false
	}
	return can
}

func NewSettingAuth(auth *bauth.Authorizer, iFn func(ctx context.Context) string) ISettingAuth {
	return &SettingAuth{authSvc: auth, identityFn: iFn}
}
