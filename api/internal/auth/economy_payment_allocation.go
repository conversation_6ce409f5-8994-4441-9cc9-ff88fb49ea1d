package auth

import (
	"bcare/common/bauth"
	"bcare/ent"
	"context"
	"github.com/zeromicro/go-zero/core/logx"
)

type IEconomyPaymentAllocationAuth interface {
	CanAdd(ctx context.Context, r *ent.PaymentAllocation) bool
	CanDelete(ctx context.Context, r *ent.PaymentAllocation) bool
	CanUpdate(ctx context.Context, r *ent.PaymentAllocation) bool
}

type EconomyPaymentAllocationAuth struct {
	authSvc    *bauth.Authorizer
	identityFn func(ctx context.Context) string
}

func (a EconomyPaymentAllocationAuth) CanAdd(ctx context.Context, r *ent.PaymentAllocation) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "economy_payment_allocation::add", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce economy_payment_allocation::add fail: %v", err)
		return false
	}
	return can
}

func (a EconomyPaymentAllocationAuth) CanDelete(ctx context.Context, r *ent.PaymentAllocation) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "economy_payment_allocation::delete", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce economy_payment_allocation::delete fail: %v", err)
		return false
	}
	return can
}

func (a EconomyPaymentAllocationAuth) CanUpdate(ctx context.Context, r *ent.PaymentAllocation) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "economy_payment_allocation::update", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce economy_payment_allocation::update fail: %v", err)
		return false
	}
	return can
}

func NewEconomyPaymentAllocationAuth(auth *bauth.Authorizer, iFn func(ctx context.Context) string) IEconomyPaymentAllocationAuth {
	return &EconomyPaymentAllocationAuth{authSvc: auth, identityFn: iFn}
}
