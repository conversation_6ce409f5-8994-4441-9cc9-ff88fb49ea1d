package auth

import (
	"bcare/api/internal/dto"
	"bcare/common/bauth"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type ITaskDepartmentAuth interface {
	CanAdd(ctx context.Context) bool
	CanUpdate(ctx context.Context, taskDepartment *dto.TaskDepartmentOutput) bool
	CanDelete(ctx context.Context, taskDepartment *dto.TaskDepartmentOutput) bool
	CanQuery(ctx context.Context) bool
}

type TaskDepartmentAuth struct {
	authorizer *bauth.Authorizer
	identityFn func(ctx context.Context) string
}

func NewTaskDepartmentAuth(authorizer *bauth.Authorizer, identityFn func(ctx context.Context) string) ITaskDepartmentAuth {
	return &TaskDepartmentAuth{
		authorizer: authorizer,
		identityFn: identityFn,
	}
}

func (a *TaskDepartmentAuth) CanAdd(ctx context.Context) bool {
	can, err := a.authorizer.Enforcer.Enforce(a.identityFn(ctx), "task_department:add", "*")
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce task_department:add fail: %v", err)
		return false
	}
	return can
}

func (a *TaskDepartmentAuth) CanUpdate(ctx context.Context, taskDepartment *dto.TaskDepartmentOutput) bool {
	can, err := a.authorizer.Enforcer.Enforce(a.identityFn(ctx), "task_department:update", taskDepartment)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce task_department:update fail: %v", err)
		return false
	}
	return can
}

func (a *TaskDepartmentAuth) CanDelete(ctx context.Context, taskDepartment *dto.TaskDepartmentOutput) bool {
	can, err := a.authorizer.Enforcer.Enforce(a.identityFn(ctx), "task_department:delete", taskDepartment)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce task_department:delete fail: %v", err)
		return false
	}
	return can
}

func (a *TaskDepartmentAuth) CanQuery(ctx context.Context) bool {
	can, err := a.authorizer.Enforcer.Enforce(a.identityFn(ctx), "task_department:query", "*")
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce task_department:query fail: %v", err)
		return false
	}
	return can
}

func (a TaskDepartmentAuth) CanGet(ctx context.Context, r *dto.TaskDepartmentOutput) bool {
	can, err := a.authorizer.Enforcer.Enforce(a.identityFn(ctx), "task_department::get", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce task_department::get fail: %v", err)
		return false
	}
	return can
}

func (a TaskDepartmentAuth) CanList(ctx context.Context) bool {
	can, err := a.authorizer.Enforcer.Enforce(a.identityFn(ctx), "task_department::list", "*")
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce task_department::list fail: %v", err)
		return false
	}
	return can
}
