package auth

import (
	"bcare/api/internal/dto"
	"bcare/common/bauth"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type IDealAuth interface {
	CanAdd(ctx context.Context, r *dto.DealOutput) bool
	CanCheckout(ctx context.Context, r *dto.DealOutput) bool
	CanDelete(ctx context.Context, r *dto.DealOutput) bool
	CanGet(ctx context.Context, r *dto.DealOutput) bool
	CanList(ctx context.Context, r *dto.DealOutput) bool
	CanListBig(ctx context.Context, r *dto.DealOutput) bool
	CanUpdate(ctx context.Context, r *dto.DealOutput) bool
}

type DealAuth struct {
	authSvc    *bauth.Authorizer
	identityFn func(ctx context.Context) string
}

func (a DealAuth) CanAdd(ctx context.Context, r *dto.DealOutput) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "deal::add", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce deal::add fail: %v", err)
		return false
	}
	return can
}

func (a DealAuth) CanCheckout(ctx context.Context, r *dto.DealOutput) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "deal::checkout", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce deal::checkout fail: %v", err)
		return false
	}
	return can
}

func (a DealAuth) CanDelete(ctx context.Context, r *dto.DealOutput) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "deal::delete", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce deal::delete fail: %v", err)
		return false
	}
	return can
}

func (a DealAuth) CanGet(ctx context.Context, r *dto.DealOutput) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "deal::get", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce deal::get fail: %v", err)
		return false
	}
	return can
}

func (a DealAuth) CanList(ctx context.Context, r *dto.DealOutput) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "deal::list", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce deal::list fail: %v", err)
		return false
	}
	return can
}

func (a DealAuth) CanListBig(ctx context.Context, r *dto.DealOutput) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "deal::list_big", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce deal::list_big fail: %v", err)
		return false
	}
	return can
}

func (a DealAuth) CanUpdate(ctx context.Context, r *dto.DealOutput) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "deal::update", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce deal::update fail: %v", err)
		return false
	}
	return can
}

func NewDealAuth(auth *bauth.Authorizer, iFn func(ctx context.Context) string) IDealAuth {
	return &DealAuth{authSvc: auth, identityFn: iFn}
}
