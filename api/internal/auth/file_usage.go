package auth

import (
	"bcare/common/bauth"
	"bcare/ent"
	"context"
	"github.com/zeromicro/go-zero/core/logx"
)

type IFileUsageAuth interface {
	CanAdd(ctx context.Context, r *ent.FileUsage) bool
	CanDelete(ctx context.Context, r *ent.FileUsage) bool
	CanGet(ctx context.Context, r *ent.FileUsage) bool
	CanList(ctx context.Context, r *ent.FileUsage) bool
	CanUpdate(ctx context.Context, r *ent.FileUsage) bool
}

type FileUsageAuth struct {
	authSvc    *bauth.Authorizer
	identityFn func(ctx context.Context) string
}

func (a FileUsageAuth) CanAdd(ctx context.Context, r *ent.FileUsage) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "file_usage::add", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce file_usage::add fail: %v", err)
		return false
	}
	return can
}

func (a FileUsageAuth) CanDelete(ctx context.Context, r *ent.FileUsage) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "file_usage::delete", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce file_usage::delete fail: %v", err)
		return false
	}
	return can
}

func (a FileUsageAuth) CanGet(ctx context.Context, r *ent.FileUsage) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "file_usage::get", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce file_usage::get fail: %v", err)
		return false
	}
	return can
}

func (a FileUsageAuth) CanList(ctx context.Context, r *ent.FileUsage) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "file_usage::list", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce file_usage::list fail: %v", err)
		return false
	}
	return can
}

func (a FileUsageAuth) CanUpdate(ctx context.Context, r *ent.FileUsage) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "file_usage::update", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce file_usage::update fail: %v", err)
		return false
	}
	return can
}

func NewFileUsageAuth(auth *bauth.Authorizer, iFn func(ctx context.Context) string) IFileUsageAuth {
	return &FileUsageAuth{authSvc: auth, identityFn: iFn}
}
