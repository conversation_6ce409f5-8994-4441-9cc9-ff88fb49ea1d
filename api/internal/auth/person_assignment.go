package auth

import (
	"bcare/common/bauth"
	"bcare/ent"
	"context"
	"github.com/zeromicro/go-zero/core/logx"
)

type IPersonAssignmentAuth interface {
	CanAdd(ctx context.Context, r *ent.PersonAssignment) bool
	CanDelete(ctx context.Context, r *ent.PersonAssignment) bool
	CanUpdate(ctx context.Context, r *ent.PersonAssignment) bool
}

type PersonAssignmentAuth struct {
	authSvc    *bauth.Authorizer
	identityFn func(ctx context.Context) string
}

func (a PersonAssignmentAuth) CanAdd(ctx context.Context, r *ent.PersonAssignment) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "person_assignment::add", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce person_assignment::add fail: %v", err)
		return false
	}
	return can
}

func (a PersonAssignmentAuth) CanDelete(ctx context.Context, r *ent.PersonAssignment) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "person_assignment::delete", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce person_assignment::delete fail: %v", err)
		return false
	}
	return can
}

func (a PersonAssignmentAuth) CanUpdate(ctx context.Context, r *ent.PersonAssignment) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "person_assignment::update", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce person_assignment::update fail: %v", err)
		return false
	}
	return can
}

func NewPersonAssignmentAuth(auth *bauth.Authorizer, iFn func(ctx context.Context) string) IPersonAssignmentAuth {
	return &PersonAssignmentAuth{authSvc: auth, identityFn: iFn}
}
