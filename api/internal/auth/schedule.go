package auth

import (
	"bcare/common/bauth"
	"bcare/ent"
	"context"
	"github.com/zeromicro/go-zero/core/logx"
)

type IScheduleAuth interface {
	CanAdd(ctx context.Context, r *ent.Schedule) bool
	CanDelete(ctx context.Context, r *ent.Schedule) bool
	CanGetWorkSchedule(ctx context.Context, r *ent.Schedule) bool
	CanList(ctx context.Context, r *ent.Schedule) bool
	CanUpdate(ctx context.Context, r *ent.Schedule) bool
}

type ScheduleAuth struct {
	authSvc    *bauth.Authorizer
	identityFn func(ctx context.Context) string
}

func (a ScheduleAuth) CanAdd(ctx context.Context, r *ent.Schedule) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "schedule::add", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce schedule::add fail: %v", err)
		return false
	}
	return can
}

func (a ScheduleAuth) CanDelete(ctx context.Context, r *ent.Schedule) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "schedule::delete", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce schedule::delete fail: %v", err)
		return false
	}
	return can
}

func (a ScheduleAuth) CanGetWorkSchedule(ctx context.Context, r *ent.Schedule) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "schedule::get_work_schedule", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce schedule::get_work_schedule fail: %v", err)
		return false
	}
	return can
}

func (a ScheduleAuth) CanList(ctx context.Context, r *ent.Schedule) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "schedule::list", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce schedule::list fail: %v", err)
		return false
	}
	return can
}

func (a ScheduleAuth) CanUpdate(ctx context.Context, r *ent.Schedule) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "schedule::update", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce schedule::update fail: %v", err)
		return false
	}
	return can
}

func NewScheduleAuth(auth *bauth.Authorizer, iFn func(ctx context.Context) string) IScheduleAuth {
	return &ScheduleAuth{authSvc: auth, identityFn: iFn}
}
