package auth

import (
	"bcare/api/internal/dto"
	"bcare/common/bauth"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type IMaterialAuth interface {
	CanAdd(ctx context.Context) bool
	CanDelete(ctx context.Context, r *dto.MaterialOutput) bool
	CanGet(ctx context.Context, r *dto.MaterialOutput) bool
	CanList(ctx context.Context) bool
	CanUpdate(ctx context.Context, r *dto.MaterialOutput) bool
}

type MaterialAuth struct {
	authSvc    *bauth.Authorizer
	identityFn func(ctx context.Context) string
}

func (a MaterialAuth) CanAdd(ctx context.Context) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "material::add", "*")
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce material::add fail: %v", err)
		return false
	}
	return can
}

func (a MaterialAuth) CanDelete(ctx context.Context, r *dto.MaterialOutput) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "material::delete", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce material::delete fail: %v", err)
		return false
	}
	return can
}

func (a MaterialAuth) CanGet(ctx context.Context, r *dto.MaterialOutput) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "material::get", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce material::get fail: %v", err)
		return false
	}
	return can
}

func (a MaterialAuth) CanList(ctx context.Context) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "material::list", "*")
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce material::list fail: %v", err)
		return false
	}
	return can
}

func (a MaterialAuth) CanUpdate(ctx context.Context, r *dto.MaterialOutput) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "material::update", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce material::update fail: %v", err)
		return false
	}
	return can
}

func NewMaterialAuth(auth *bauth.Authorizer, iFn func(ctx context.Context) string) IMaterialAuth {
	return &MaterialAuth{authSvc: auth, identityFn: iFn}
}
