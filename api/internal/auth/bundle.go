package auth

import (
	"bcare/common/bauth"
	"bcare/ent"
	"context"
	"github.com/zeromicro/go-zero/core/logx"
)

type IBundleAuth interface {
	CanAdd(ctx context.Context, r *ent.Bundle) bool
	CanDelete(ctx context.Context, r *ent.Bundle) bool
	CanGet(ctx context.Context, r *ent.Bundle) bool
	CanList(ctx context.Context, r *ent.Bundle) bool
	CanUpdate(ctx context.Context, r *ent.Bundle) bool
}

type BundleAuth struct {
	authSvc    *bauth.Authorizer
	identityFn func(ctx context.Context) string
}

func (a BundleAuth) CanAdd(ctx context.Context, r *ent.Bundle) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "bundle::add", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce bundle::add fail: %v", err)
		return false
	}
	return can
}

func (a BundleAuth) CanDelete(ctx context.Context, r *ent.Bundle) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "bundle::delete", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce bundle::delete fail: %v", err)
		return false
	}
	return can
}

func (a BundleAuth) CanGet(ctx context.Context, r *ent.Bundle) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "bundle::get", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce bundle::get fail: %v", err)
		return false
	}
	return can
}

func (a BundleAuth) CanList(ctx context.Context, r *ent.Bundle) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "bundle::list", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce bundle::list fail: %v", err)
		return false
	}
	return can
}

func (a BundleAuth) CanUpdate(ctx context.Context, r *ent.Bundle) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "bundle::update", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce bundle::update fail: %v", err)
		return false
	}
	return can
}

func NewBundleAuth(auth *bauth.Authorizer, iFn func(ctx context.Context) string) IBundleAuth {
	return &BundleAuth{authSvc: auth, identityFn: iFn}
}
