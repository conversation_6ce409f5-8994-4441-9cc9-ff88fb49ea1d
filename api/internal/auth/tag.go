package auth

import (
	"bcare/api/internal/dto"
	"bcare/common/bauth"
	"context"
	"github.com/zeromicro/go-zero/core/logx"
)

type ITagAuth interface {
	CanAdd(ctx context.Context) bool
	CanDelete(ctx context.Context, r *dto.TagOutput) bool
	CanGet(ctx context.Context, r *dto.TagOutput) bool
	CanList(ctx context.Context) bool
	CanUpdate(ctx context.Context, r *dto.TagOutput) bool
}

type TagAuth struct {
	authSvc    *bauth.Authorizer
	identityFn func(ctx context.Context) string
}

func (a TagAuth) CanAdd(ctx context.Context) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "tag::add", "*")
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce tag::add fail: %v", err)
		return false
	}
	return can
}

func (a TagAuth) CanDelete(ctx context.Context, r *dto.TagOutput) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "tag::delete", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce tag::delete fail: %v", err)
		return false
	}
	return can
}

func (a TagAuth) CanGet(ctx context.Context, r *dto.TagOutput) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "tag::get", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce tag::get fail: %v", err)
		return false
	}
	return can
}

func (a TagAuth) CanList(ctx context.Context) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "tag::list", "*")
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce tag::list fail: %v", err)
		return false
	}
	return can
}

func (a TagAuth) CanUpdate(ctx context.Context, r *dto.TagOutput) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "tag::update", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce tag::update fail: %v", err)
		return false
	}
	return can
}

func NewTagAuth(auth *bauth.Authorizer, iFn func(ctx context.Context) string) ITagAuth {
	return &TagAuth{authSvc: auth, identityFn: iFn}
}
