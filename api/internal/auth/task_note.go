package auth

import (
	"bcare/api/internal/dto"
	"bcare/common/bauth"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type ITaskNoteAuth interface {
	CanAdd(ctx context.Context) bool
	CanDelete(ctx context.Context, r *dto.TaskNoteOutput) bool
	CanGet(ctx context.Context, r *dto.TaskNoteOutput) bool
	CanList(ctx context.Context) bool
	CanUpdate(ctx context.Context, r *dto.TaskNoteOutput) bool
}

type TaskNoteAuth struct {
	authSvc    *bauth.Authorizer
	identityFn func(ctx context.Context) string
}

func (a TaskNoteAuth) CanAdd(ctx context.Context) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "task::add_note", "*")
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce task_note::add fail: %v", err)
		return false
	}
	return can
}

func (a TaskNoteAuth) CanDelete(ctx context.Context, r *dto.TaskNoteOutput) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "task::delete_note", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce task_note::delete fail: %v", err)
		return false
	}
	return can
}

func (a TaskNoteAuth) CanGet(ctx context.Context, r *dto.TaskNoteOutput) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "task::get_note", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce task_note::get fail: %v", err)
		return false
	}
	return can
}

func (a TaskNoteAuth) CanList(ctx context.Context) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "task::list_note", "*")
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce task_note::list fail: %v", err)
		return false
	}
	return can
}

func (a TaskNoteAuth) CanUpdate(ctx context.Context, r *dto.TaskNoteOutput) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "task::update_note", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce task_note::update fail: %v", err)
		return false
	}
	return can
}

func NewTaskNoteAuth(auth *bauth.Authorizer, iFn func(ctx context.Context) string) ITaskNoteAuth {
	return &TaskNoteAuth{authSvc: auth, identityFn: iFn}
}
