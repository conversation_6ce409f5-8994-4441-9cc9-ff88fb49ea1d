package auth

import (
	"bcare/common/bauth"
	"bcare/ent"
	"context"
	"github.com/zeromicro/go-zero/core/logx"
)

type IInstallmentAuth interface {
	CanAdd(ctx context.Context, r *ent.Installment) bool
	CanDelete(ctx context.Context, r *ent.Installment) bool
	CanGet(ctx context.Context, r *ent.Installment) bool
	CanList(ctx context.Context, r *ent.Installment) bool
	CanUpdate(ctx context.Context, r *ent.Installment) bool
}

type InstallmentAuth struct {
	authSvc    *bauth.Authorizer
	identityFn func(ctx context.Context) string
}

func (a InstallmentAuth) CanAdd(ctx context.Context, r *ent.Installment) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "installment::add", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce installment::add fail: %v", err)
		return false
	}
	return can
}

func (a InstallmentAuth) CanDelete(ctx context.Context, r *ent.Installment) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "installment::delete", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce installment::delete fail: %v", err)
		return false
	}
	return can
}

func (a InstallmentAuth) CanGet(ctx context.Context, r *ent.Installment) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "installment::get", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce installment::get fail: %v", err)
		return false
	}
	return can
}

func (a InstallmentAuth) CanList(ctx context.Context, r *ent.Installment) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "installment::list", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce installment::list fail: %v", err)
		return false
	}
	return can
}

func (a InstallmentAuth) CanUpdate(ctx context.Context, r *ent.Installment) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "installment::update", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce installment::update fail: %v", err)
		return false
	}
	return can
}

func NewInstallmentAuth(auth *bauth.Authorizer, iFn func(ctx context.Context) string) IInstallmentAuth {
	return &InstallmentAuth{authSvc: auth, identityFn: iFn}
}
