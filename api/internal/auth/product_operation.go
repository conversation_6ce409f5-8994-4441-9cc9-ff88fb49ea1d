package auth

import (
	"bcare/common/bauth"
	"bcare/ent"
	"context"
	"github.com/zeromicro/go-zero/core/logx"
)

type IProductOperationAuth interface {
	CanAdd(ctx context.Context, r *ent.ProductOperation) bool
	CanDelete(ctx context.Context, r *ent.ProductOperation) bool
}

type ProductOperationAuth struct {
	authSvc    *bauth.Authorizer
	identityFn func(ctx context.Context) string
}

func (a ProductOperationAuth) CanAdd(ctx context.Context, r *ent.ProductOperation) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "product_operation::add", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce product_operation::add fail: %v", err)
		return false
	}
	return can
}

func (a ProductOperationAuth) CanDelete(ctx context.Context, r *ent.ProductOperation) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "product_operation::delete", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce product_operation::delete fail: %v", err)
		return false
	}
	return can
}

func NewProductOperationAuth(auth *bauth.Authorizer, iFn func(ctx context.Context) string) IProductOperationAuth {
	return &ProductOperationAuth{authSvc: auth, identityFn: iFn}
}
