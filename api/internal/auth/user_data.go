package auth

import (
	"bcare/common/bauth"
	"bcare/ent"
	"context"
	"github.com/zeromicro/go-zero/core/logx"
)

type IUserDataAuth interface {
	CanClearData(ctx context.Context, r *ent.UserData) bool
	CanGetData(ctx context.Context, r *ent.UserData) bool
	CanSetData(ctx context.Context, r *ent.UserData) bool
}

type UserDataAuth struct {
	authSvc    *bauth.Authorizer
	identityFn func(ctx context.Context) string
}

func (a UserDataAuth) CanClearData(ctx context.Context, r *ent.UserData) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "user_data::clear_data", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce user_data::clear_data fail: %v", err)
		return false
	}
	return can
}

func (a UserDataAuth) CanGetData(ctx context.Context, r *ent.UserData) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "user_data::get_data", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce user_data::get_data fail: %v", err)
		return false
	}
	return can
}

func (a UserDataAuth) CanSetData(ctx context.Context, r *ent.UserData) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "user_data::set_data", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce user_data::set_data fail: %v", err)
		return false
	}
	return can
}

func NewUserDataAuth(auth *bauth.Authorizer, iFn func(ctx context.Context) string) IUserDataAuth {
	return &UserDataAuth{authSvc: auth, identityFn: iFn}
}
