package auth

import (
	"bcare/common/bauth"
	"bcare/ent"
	"context"
	"github.com/zeromicro/go-zero/core/logx"
)

type IBillAuth interface {
	CanAdd(ctx context.Context, r *ent.Bill) bool
	CanDelete(ctx context.Context, r *ent.Bill) bool
	CanGet(ctx context.Context, r *ent.Bill) bool
	CanList(ctx context.Context, r *ent.Bill) bool
	CanQuery(ctx context.Context, r *ent.Bill) bool
	CanUpdate(ctx context.Context, r *ent.Bill) bool
}

type BillAuth struct {
	authSvc    *bauth.Authorizer
	identityFn func(ctx context.Context) string
}

func (a BillAuth) CanAdd(ctx context.Context, r *ent.Bill) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "bill::add", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce bill::add fail: %v", err)
		return false
	}
	return can
}

func (a BillAuth) CanDelete(ctx context.Context, r *ent.Bill) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "bill::delete", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce bill::delete fail: %v", err)
		return false
	}
	return can
}

func (a BillAuth) CanGet(ctx context.Context, r *ent.Bill) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "bill::get", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce bill::get fail: %v", err)
		return false
	}
	return can
}

func (a BillAuth) CanList(ctx context.Context, r *ent.Bill) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "bill::list", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce bill::list fail: %v", err)
		return false
	}
	return can
}

func (a BillAuth) CanQuery(ctx context.Context, r *ent.Bill) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "bill::query", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce bill::query fail: %v", err)
		return false
	}
	return can
}

func (a BillAuth) CanUpdate(ctx context.Context, r *ent.Bill) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "bill::update", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce bill::update fail: %v", err)
		return false
	}
	return can
}

func NewBillAuth(auth *bauth.Authorizer, iFn func(ctx context.Context) string) IBillAuth {
	return &BillAuth{authSvc: auth, identityFn: iFn}
}
