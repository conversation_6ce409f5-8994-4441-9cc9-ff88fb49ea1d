package auth

import (
	"bcare/api/internal/dto"
	"bcare/common/bauth"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type ITaskAssignmentAuth interface {
	CanAdd(ctx context.Context) bool
	CanDelete(ctx context.Context, r *dto.TaskAssignmentOutput) bool
	CanGet(ctx context.Context, r *dto.TaskAssignmentOutput) bool
	CanList(ctx context.Context) bool
	CanUpdate(ctx context.Context, r *dto.TaskAssignmentOutput) bool
}

type TaskAssignmentAuth struct {
	authSvc    *bauth.Authorizer
	identityFn func(ctx context.Context) string
}

func (a TaskAssignmentAuth) CanAdd(ctx context.Context) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "task_assignment::add", "*")
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce task_assignment::add fail: %v", err)
		return false
	}
	return can
}

func (a TaskAssignmentAuth) CanDelete(ctx context.Context, r *dto.TaskAssignmentOutput) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "task_assignment::delete", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce task_assignment::delete fail: %v", err)
		return false
	}
	return can
}

func (a TaskAssignmentAuth) CanGet(ctx context.Context, r *dto.TaskAssignmentOutput) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "task_assignment::get", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce task_assignment::get fail: %v", err)
		return false
	}
	return can
}

func (a TaskAssignmentAuth) CanList(ctx context.Context) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "task_assignment::list", "*")
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce task_assignment::list fail: %v", err)
		return false
	}
	return can
}

func (a TaskAssignmentAuth) CanUpdate(ctx context.Context, r *dto.TaskAssignmentOutput) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "task_assignment::update", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce task_assignment::update fail: %v", err)
		return false
	}
	return can
}

func NewTaskAssignmentAuth(auth *bauth.Authorizer, iFn func(ctx context.Context) string) ITaskAssignmentAuth {
	return &TaskAssignmentAuth{authSvc: auth, identityFn: iFn}
}
