package auth

import (
	"bcare/common/bauth"
	"bcare/ent"
	"context"
	"github.com/zeromicro/go-zero/core/logx"
)

type IReferralAuth interface {
	CanAdd(ctx context.Context, r *ent.Referral) bool
	CanDelete(ctx context.Context, r *ent.Referral) bool
	CanSearchReferrer(ctx context.Context, r *ent.Referral) bool
	CanUpdate(ctx context.Context, r *ent.Referral) bool
}

type ReferralAuth struct {
	authSvc    *bauth.Authorizer
	identityFn func(ctx context.Context) string
}

func (a ReferralAuth) CanAdd(ctx context.Context, r *ent.Referral) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "referral::add", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce referral::add fail: %v", err)
		return false
	}
	return can
}

func (a ReferralAuth) CanDelete(ctx context.Context, r *ent.Referral) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "referral::delete", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce referral::delete fail: %v", err)
		return false
	}
	return can
}

func (a ReferralAuth) CanSearchReferrer(ctx context.Context, r *ent.Referral) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "referral::search_referrer", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce referral::search_referrer fail: %v", err)
		return false
	}
	return can
}

func (a ReferralAuth) CanUpdate(ctx context.Context, r *ent.Referral) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "referral::update", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce referral::update fail: %v", err)
		return false
	}
	return can
}

func NewReferralAuth(auth *bauth.Authorizer, iFn func(ctx context.Context) string) IReferralAuth {
	return &ReferralAuth{authSvc: auth, identityFn: iFn}
}
