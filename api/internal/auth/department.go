package auth

import (
	"bcare/common/bauth"
	"bcare/ent"
	"context"
	"github.com/zeromicro/go-zero/core/logx"
)

type IDepartmentAuth interface {
	CanList(ctx context.Context, r *ent.Department) bool
}

type DepartmentAuth struct {
	authSvc    *bauth.Authorizer
	identityFn func(ctx context.Context) string
}

func (a DepartmentAuth) CanList(ctx context.Context, r *ent.Department) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "department::list", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce department::list fail: %v", err)
		return false
	}
	return can
}

func NewDepartmentAuth(auth *bauth.Authorizer, iFn func(ctx context.Context) string) IDepartmentAuth {
	return &DepartmentAuth{authSvc: auth, identityFn: iFn}
}
