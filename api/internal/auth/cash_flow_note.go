package auth

import (
	"bcare/api/internal/dto"
	"bcare/common/bauth"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type ICashFlowNoteAuth interface {
	CanAdd(ctx context.Context) bool
	CanDelete(ctx context.Context, r *dto.CashFlowNoteOutput) bool
	CanGet(ctx context.Context, r *dto.CashFlowNoteOutput) bool
	CanList(ctx context.Context) bool
	CanUpdate(ctx context.Context, r *dto.CashFlowNoteOutput) bool
}

type CashFlowNoteAuth struct {
	authSvc    *bauth.Authorizer
	identityFn func(ctx context.Context) string
}

func (a CashFlowNoteAuth) CanAdd(ctx context.Context) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "cashflow::add_note", "*")
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce cashflow_note::add fail: %v", err)
		return false
	}
	return can
}

func (a CashFlowNoteAuth) CanDelete(ctx context.Context, r *dto.CashFlowNoteOutput) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "cashflow::delete_note", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce cashflow_note::delete fail: %v", err)
		return false
	}
	return can
}

func (a CashFlowNoteAuth) CanGet(ctx context.Context, r *dto.CashFlowNoteOutput) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "cashflow::get_note", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce cashflow_note::get fail: %v", err)
		return false
	}
	return can
}

func (a CashFlowNoteAuth) CanList(ctx context.Context) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "cashflow::list_note", "*")
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce cashflow_note::list fail: %v", err)
		return false
	}
	return can
}

func (a CashFlowNoteAuth) CanUpdate(ctx context.Context, r *dto.CashFlowNoteOutput) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "cashflow::update_note", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce cashflow_note::update fail: %v", err)
		return false
	}
	return can
}

func NewCashFlowNoteAuth(auth *bauth.Authorizer, iFn func(ctx context.Context) string) ICashFlowNoteAuth {
	return &CashFlowNoteAuth{authSvc: auth, identityFn: iFn}
}
