package auth

import (
	"bcare/api/internal/dto"
	"bcare/common/bauth"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

// IOperationAuth defines the interface for operation authorization.
type IOperationAuth interface {
	CanAdd(ctx context.Context) bool
	CanDelete(ctx context.Context, r *dto.OperationOutput) bool
	CanGet(ctx context.Context, r *dto.OperationOutput) bool
	CanList(ctx context.Context) bool
	CanUpdate(ctx context.Context, r *dto.OperationOutput) bool
	CanAll(ctx context.Context) bool
}

// OperationAuth implements IOperationAuth.
type OperationAuth struct {
	authSvc    *bauth.Authorizer
	identityFn func(ctx context.Context) string
}

// NewOperationAuth creates a new OperationAuth instance.
func NewOperationAuth(auth *bauth.Authorizer, iFn func(ctx context.Context) string) IOperationAuth {
	return &OperationAuth{authSvc: auth, identityFn: iFn}
}

// CanAdd checks if the current identity can add an operation.
func (a *OperationAuth) CanAdd(ctx context.Context) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "operation::add", "*")
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce operation::add fail: %v", err)
		return false
	}
	return can
}

// CanDelete checks if the current identity can delete the given operation.
func (a *OperationAuth) CanDelete(ctx context.Context, r *dto.OperationOutput) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "operation::delete", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce operation::delete fail: %v", err)
		return false
	}
	return can
}

// CanGet checks if the current identity can get the given operation.
func (a *OperationAuth) CanGet(ctx context.Context, r *dto.OperationOutput) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "operation::get", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce operation::get fail: %v", err)
		return false
	}
	return can
}

// CanList checks if the current identity can list operations.
func (a *OperationAuth) CanList(ctx context.Context) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "operation::list", "*")
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce operation::list fail: %v", err)
		return false
	}
	return can
}

// CanUpdate checks if the current identity can update the given operation.
func (a *OperationAuth) CanUpdate(ctx context.Context, r *dto.OperationOutput) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "operation::update", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce operation::update fail: %v", err)
		return false
	}
	return can
}

// CanAll checks if the current identity can retrieve all operations.
// This might have different authorization logic or simply mirror CanList.
// For now, it checks "operation::all".
func (a *OperationAuth) CanAll(ctx context.Context) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "operation::all", "*")
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce operation::all fail: %v", err)
		return false
	}
	return can
}
