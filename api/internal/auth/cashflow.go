package auth

import (
	"bcare/api/internal/dto"
	"bcare/common/bauth"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type ICashFlowAuth interface {
	CanAdd(ctx context.Context) bool
	CanDelete(ctx context.Context, r *dto.CashFlowOutput) bool
	CanGet(ctx context.Context, r *dto.CashFlowOutput) bool
	CanList(ctx context.Context) bool
	CanUpdate(ctx context.Context, r *dto.CashFlowOutput) bool
	CanUpdateState(ctx context.Context, r *dto.CashFlowOutput) bool
	CanGetReport(ctx context.Context) bool
}

type CashFlowAuth struct {
	authSvc    *bauth.Authorizer
	identityFn func(ctx context.Context) string
}

func (a CashFlowAuth) CanAdd(ctx context.Context) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "cashflow::add", "*")
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce cashflow::add fail: %v", err)
		return false
	}
	return can
}

func (a CashFlowAuth) CanDelete(ctx context.Context, r *dto.CashFlowOutput) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "cashflow::delete", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce cashflow::delete fail: %v", err)
		return false
	}
	return can
}

func (a CashFlowAuth) CanGet(ctx context.Context, r *dto.CashFlowOutput) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "cashflow::get", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce cashflow::get fail: %v", err)
		return false
	}
	return can
}

func (a CashFlowAuth) CanList(ctx context.Context) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "cashflow::list", "*")
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce cashflow::list fail: %v", err)
		return false
	}
	return can
}

func (a CashFlowAuth) CanUpdate(ctx context.Context, r *dto.CashFlowOutput) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "cashflow::update", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce cashflow::update fail: %v", err)
		return false
	}
	return can
}

func (a CashFlowAuth) CanUpdateState(ctx context.Context, r *dto.CashFlowOutput) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "cashflow::update_state", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce cashflow::update_state fail: %v", err)
		return false
	}
	return can
}

func (a CashFlowAuth) CanGetReport(ctx context.Context) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "cashflow::get_report", "*")
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce cashflow::get_report fail: %v", err)
		return false
	}
	return can
}

func NewCashFlowAuth(auth *bauth.Authorizer, iFn func(ctx context.Context) string) ICashFlowAuth {
	return &CashFlowAuth{authSvc: auth, identityFn: iFn}
}
