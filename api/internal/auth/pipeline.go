package auth

import (
	"bcare/common/bauth"
	"bcare/ent"
	"context"
	"github.com/zeromicro/go-zero/core/logx"
)

type IPipelineAuth interface {
	CanAdd(ctx context.Context, r *ent.Pipeline) bool
	CanDelete(ctx context.Context, r *ent.Pipeline) bool
	CanGet(ctx context.Context, r *ent.Pipeline) bool
	CanList(ctx context.Context, r *ent.Pipeline) bool
	CanSwitchDeal(ctx context.Context, r *ent.Pipeline) bool
	CanUpdate(ctx context.Context, r *ent.Pipeline) bool
}

type PipelineAuth struct {
	authSvc    *bauth.Authorizer
	identityFn func(ctx context.Context) string
}

func (a PipelineAuth) CanAdd(ctx context.Context, r *ent.Pipeline) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "pipeline::add", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce pipeline::add fail: %v", err)
		return false
	}
	return can
}

func (a PipelineAuth) CanDelete(ctx context.Context, r *ent.Pipeline) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "pipeline::delete", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce pipeline::delete fail: %v", err)
		return false
	}
	return can
}

func (a PipelineAuth) CanGet(ctx context.Context, r *ent.Pipeline) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "pipeline::get", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce pipeline::get fail: %v", err)
		return false
	}
	return can
}

func (a PipelineAuth) CanList(ctx context.Context, r *ent.Pipeline) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "pipeline::list", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce pipeline::list fail: %v", err)
		return false
	}
	return can
}

func (a PipelineAuth) CanSwitchDeal(ctx context.Context, r *ent.Pipeline) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "pipeline::switch_deal", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce pipeline::switch_deal fail: %v", err)
		return false
	}
	return can
}

func (a PipelineAuth) CanUpdate(ctx context.Context, r *ent.Pipeline) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "pipeline::update", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce pipeline::update fail: %v", err)
		return false
	}
	return can
}

func NewPipelineAuth(auth *bauth.Authorizer, iFn func(ctx context.Context) string) IPipelineAuth {
	return &PipelineAuth{authSvc: auth, identityFn: iFn}
}
