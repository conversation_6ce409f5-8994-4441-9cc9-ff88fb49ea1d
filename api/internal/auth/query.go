package auth

import (
	"bcare/common/bauth"
	"bcare/ent"
	"context"
	"github.com/zeromicro/go-zero/core/logx"
)

type IQueryAuth interface {
	CanQuery(ctx context.Context, r *ent.Query) bool
}

type QueryAuth struct {
	authSvc    *bauth.Authorizer
	identityFn func(ctx context.Context) string
}

func (a QueryAuth) CanQuery(ctx context.Context, r *ent.Query) bool {
	can, err := a.authSvc.Enforcer.Enforce(a.identityFn(ctx), "query::query", r)
	if err != nil {
		logx.WithContext(ctx).Errorf("enforce query::query fail: %v", err)
		return false
	}
	return can
}

func NewQueryAuth(auth *bauth.Authorizer, iFn func(ctx context.Context) string) IQueryAuth {
	return &QueryAuth{authSvc: auth, identityFn: iFn}
}
