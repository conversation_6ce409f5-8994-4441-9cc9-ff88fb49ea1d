package types

import (
	"fmt"
	"strings"
)

// NotificationType represents generic notification categories
type NotificationType string

const (
	// Core notification categories - flexible and reusable
	NotificationTypeInfo       NotificationType = "info"       // General information
	NotificationTypeSuccess    NotificationType = "success"    // Positive outcomes
	NotificationTypeWarning    NotificationType = "warning"    // Attention needed
	NotificationTypeReminder   NotificationType = "reminder"   // Time-based alerts
	NotificationTypeAssignment NotificationType = "assignment" // Work assignments
	NotificationTypeUpdate     NotificationType = "update"     // Entity changes
	NotificationTypeAlert      NotificationType = "alert"      // Urgent notifications
)

// String returns the string representation of the notification type
func (nt NotificationType) String() string {
	return string(nt)
}

// DisplayName returns the human-readable name in Vietnamese
func (nt NotificationType) DisplayName() string {
	switch nt {
	case NotificationTypeInfo:
		return "Thông tin"
	case NotificationTypeSuccess:
		return "Thành công"
	case NotificationTypeWarning:
		return "Cảnh báo"
	case NotificationTypeReminder:
		return "Nhắc nhở"
	case NotificationTypeAssignment:
		return "Phân công"
	case NotificationTypeUpdate:
		return "Cập nhật"
	case NotificationTypeAlert:
		return "Cảnh báo khẩn"
	default:
		return "Không xác định"
	}
}

// IsValid checks if the notification type is valid
func (nt NotificationType) IsValid() bool {
	switch nt {
	case NotificationTypeInfo,
		NotificationTypeSuccess,
		NotificationTypeWarning,
		NotificationTypeReminder,
		NotificationTypeAssignment,
		NotificationTypeUpdate,
		NotificationTypeAlert:
		return true
	default:
		return false
	}
}

// NotificationTypeValues returns all notification types as strings for Ent enum
func NotificationTypeValues() []string {
	return []string{
		string(NotificationTypeInfo),
		string(NotificationTypeSuccess),
		string(NotificationTypeWarning),
		string(NotificationTypeReminder),
		string(NotificationTypeAssignment),
		string(NotificationTypeUpdate),
		string(NotificationTypeAlert),
	}
}

// NotificationEntityType represents entity types for notifications
type NotificationEntityType string

const (
	NotificationEntityTypeDeal        NotificationEntityType = "deal"
	NotificationEntityTypeTask        NotificationEntityType = "task"
	NotificationEntityTypePerson      NotificationEntityType = "person"
	NotificationEntityTypeAppointment NotificationEntityType = "appointment"
	NotificationEntityTypeBill        NotificationEntityType = "bill"
	NotificationEntityTypePayment     NotificationEntityType = "payment"
	NotificationEntityTypeAttachment  NotificationEntityType = "attachment"
)

// String returns the string representation of the entity type
func (net NotificationEntityType) String() string {
	return string(net)
}

// DisplayName returns the human-readable name in Vietnamese
func (net NotificationEntityType) DisplayName() string {
	switch net {
	case NotificationEntityTypeDeal:
		return "Deal"
	case NotificationEntityTypeTask:
		return "Task"
	case NotificationEntityTypePerson:
		return "Khách hàng"
	case NotificationEntityTypeAppointment:
		return "Lịch hẹn"
	case NotificationEntityTypeBill:
		return "Hóa đơn"
	case NotificationEntityTypePayment:
		return "Thanh toán"
	case NotificationEntityTypeAttachment:
		return "Phiếu điều trị"
	default:
		return "Không xác định"
	}
}

// IsValid checks if the entity type is valid
func (net NotificationEntityType) IsValid() bool {
	switch net {
	case NotificationEntityTypeDeal,
		NotificationEntityTypeTask,
		NotificationEntityTypePerson,
		NotificationEntityTypeAppointment,
		NotificationEntityTypeBill,
		NotificationEntityTypePayment,
		NotificationEntityTypeAttachment:
		return true
	default:
		return false
	}
}

// NotificationEntityTypeValues returns all entity types as strings for Ent enum
func NotificationEntityTypeValues() []string {
	return []string{
		string(NotificationEntityTypeDeal),
		string(NotificationEntityTypeTask),
		string(NotificationEntityTypePerson),
		string(NotificationEntityTypeAppointment),
		string(NotificationEntityTypeBill),
		string(NotificationEntityTypePayment),
		string(NotificationEntityTypeAttachment),
	}
}

// Helper functions for building semantic notification types

// BuildNotificationType creates semantic notification types using severity + domain_type pattern
// e.g. BuildNotificationType("update", "task_state_change") -> "update_task_state_change"
// e.g. BuildNotificationType("warning", "last_task") -> "warning_last_task"
func BuildNotificationType(severity, domainType string) string {
	return fmt.Sprintf("%s_%s", strings.ToLower(severity), strings.ToLower(domainType))
}

// ParseNotificationType converts a string to NotificationType with flexible validation
// Allows both predefined types and custom types for backwards compatibility
func ParseNotificationType(s string) (NotificationType, bool) {
	nt := NotificationType(strings.ToLower(s))
	// If it's a predefined type, validate it
	if nt.IsValid() {
		return nt, true
	}
	// Allow custom types for flexibility - return as-is
	return NotificationType(s), true
}

// ParseNotificationEntityType converts a string to NotificationEntityType
func ParseNotificationEntityType(s string) (NotificationEntityType, bool) {
	net := NotificationEntityType(strings.ToLower(s))
	return net, net.IsValid()
}

// SuggestNotificationCategory suggests an appropriate category based on semantic type
func SuggestNotificationCategory(semanticType string) NotificationType {
	lower := strings.ToLower(semanticType)

	switch {
	case strings.Contains(lower, "assign"):
		return NotificationTypeAssignment
	case strings.Contains(lower, "complete") || strings.Contains(lower, "success") || strings.Contains(lower, "won"):
		return NotificationTypeSuccess
	case strings.Contains(lower, "reminder") || strings.Contains(lower, "due"):
		return NotificationTypeReminder
	case strings.Contains(lower, "overdue") || strings.Contains(lower, "urgent"):
		return NotificationTypeAlert
	case strings.Contains(lower, "change") || strings.Contains(lower, "update"):
		return NotificationTypeUpdate
	case strings.Contains(lower, "warning") || strings.Contains(lower, "fail"):
		return NotificationTypeWarning
	default:
		return NotificationTypeInfo
	}
}
