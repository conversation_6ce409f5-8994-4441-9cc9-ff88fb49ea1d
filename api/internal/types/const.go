package types

const (
	//DealStatus
	DealStatusTemporary = 4
	//Appointment Type
	AppointmentType1 = 1
	AppointmentType2 = 2
	AppointmentType3 = 3
	AppointmentType4 = 4

	//Appointment Status
	AppointmentStatusDeleted    = -1
	AppointmentStatusDeActive   = 1
	AppointmentStatusActive     = 2
	AppointmentStatusOnTime     = 3
	AppointmentStatusLate       = 4
	AppointmentStatusReschedule = 5
	AppointmentStatusUnforeseen = 6

	//Appointment Reminder Status
	AppointmentReminderStatusUnsent = -1
	AppointmentReminderStatusSent   = 1

	//DealUser Role
	DealUserRoleTreatmentDoctor  = "treatment_doctor"
	DealUserRoleConsultantDoctor = "consultant_doctor"
	DealUserRoleAdvisor          = "advisor"
	DealUserRoleAssistant        = "assistant"
	DealUserRoleXRayTechnician   = "xray_technician"
	DealUserRoleDoctorAssistant  = "doctor_assistant"

	// Installment TransactionType
	InstallmentTypeDeposit = 1
	InstallmentTypePayment = 2
	InstallmentTypeRefund  = 3

	// Installment Status
	InstallmentStatusPaid = 3

	//Product Type
	ProductTypeService = "service"
	ProductTypeItem    = "item"
	ProductTypeGift    = "gift"

	//Deal Status
	DealStatusActive = 2
	DealStatusLost   = 3
	DealStatusWon    = 4

	//Note Type
	NoteTypeDefault    = 1
	NoteTypeFeeClosure = 2
	NoteTypePromotion  = 3
	NoteTypeComplaint  = 4

	//AttachmentType
	AttachmentTypeBill      = 1
	AttachmentTypeTreatment = 2
	AttachmentMainService   = 3
	AttachmentService       = 4

	//task
	TaskPriorityLow    = 1
	TaskPriorityMedium = 2
	TaskPriorityHigh   = 3

	TaskStateNewTask             = "new_task"
	TaskStateInProgress          = "in_progress"
	TaskStateAwaitingApproval    = "awaiting_approval"
	TaskStateCompleted           = "completed"
	TaskStateCancelled           = "cancelled"
	TaskStateOverdue             = "overdue"
	TaskStateCancelledInProgress = "cancelled_in_progress"
	TaskStateNewCompletedEarly   = "completed_early"

	TaskTypeCall               = "call"
	TaskTypeMessage            = "message"
	TaskTypeEmail              = "email"
	TaskTypeTheoreticalLecture = "theoretical_lecture"
	TaskTypeReport             = "report"

	TaskAssignmentStatePending   = "new_task"
	TaskAssignmentStateOngoing   = "ongoing"
	TaskAssignmentStateCompleted = "completed"
	TaskAssignmentStateOverdue   = "overdue"

	//Call
	CallStatusNew      = 2 // Mới tạo
	CallStatusReceived = 3 // Đã nhận cuộc gọi
	CallStatusEnded    = 4 // Kết thúc cuộc gọi
	CallStatusMissed   = 5 // Cuộc gọi nhỡ

	UserDepartmentPositionManager       = "manager"
	UserDepartmentPositionDeputyManager = "deputy_manager"
	UserDepartmentPositionTeamLeader    = "leader"
	UserDepartmentPositionDeputyLeader  = "deputy_leader"
	UserDepartmentPositionEmployee      = "employee"

	DiscountDistributionStrategyRatio = "ratio"   //phân bổ discount amount theo tỉ lệ value của attachment
	DiscountDistributionStrategyMax   = "largest" //phân bổ discount amount vào attachment có value lớn nhất
)

var (
	UserDepartmentPosition = map[string]string{
		UserDepartmentPositionManager:       "Trưởng phòng",
		UserDepartmentPositionDeputyManager: "Phó phòng",
		UserDepartmentPositionTeamLeader:    "Trưởng nhóm",
		UserDepartmentPositionDeputyLeader:  "Phó nhóm",
		UserDepartmentPositionEmployee:      "Nhân viên",
	}
	AppointmentTermTypes = map[string]int{
		"104": AppointmentType1,
		"105": AppointmentType2,
		"106": AppointmentType3,
		"107": AppointmentType4,
	}

	AppointmentTypes = map[int]string{
		AppointmentType1: "Chỉnh nha",
		AppointmentType2: "Tổng quát",
		AppointmentType3: "Implant",
		AppointmentType4: "Tiểu phẫu",
	}

	AppointmentStatus = map[int]string{
		AppointmentStatusDeActive:   "Huỷ hẹn",
		AppointmentStatusActive:     "Hẹn mới",
		AppointmentStatusOnTime:     "Đúng hẹn",
		AppointmentStatusLate:       "Trễ hẹn",
		AppointmentStatusReschedule: "Đổi hẹn",
		AppointmentStatusUnforeseen: "Phát sinh",
	}

	AppointmentReminderStatus = map[int]string{
		AppointmentReminderStatusUnsent: "Chưa gửi",
		AppointmentReminderStatusSent:   "Đã gửi",
	}

	ProductType = map[string]string{
		ProductTypeService: "Dịch vụ",
		ProductTypeItem:    "Sản phẩm",
		ProductTypeGift:    "Quà tặng",
	}

	DealUserRoles = map[string]string{
		DealUserRoleTreatmentDoctor:  "Bác sĩ điều trị",
		DealUserRoleConsultantDoctor: "Bác sĩ tư vấn",
		DealUserRoleAdvisor:          "Tư vấn viên",
		DealUserRoleAssistant:        "Phụ Tá",
		DealUserRoleXRayTechnician:   "Kỹ thuật viên(Xquang)",
		DealUserRoleDoctorAssistant:  "Trợ lý Bác sĩ",
	}

	DealStatus = map[int]string{
		DealStatusActive: "Chưa thanh toán",
		DealStatusLost:   "Khách bỏ về",
		DealStatusWon:    "Đã thanh toán",
	}

	NoteTypes = map[int]string{
		NoteTypeDefault:    "Khác",
		NoteTypeFeeClosure: "Chốt phí",
		NoteTypePromotion:  "Ưu đãi",
		NoteTypeComplaint:  "Phàn nàn",
	}
	ProductPosition = map[int]string{
		1: "hóa đơn",
		2: "dịch vụ chính",
		3: "dịch vụ đi kèm",
	}

	AttachmentType = map[int]string{
		AttachmentTypeBill:      "hóa đơn",
		AttachmentTypeTreatment: "điều trị",
		AttachmentMainService:   "Dịch vụ chính",
		AttachmentService:       "dịch vụ đi kèm",
	}

	TransactionTypes = map[int]string{
		InstallmentTypeDeposit: "Đặt cọc",
		InstallmentTypePayment: "Thu phí",
		InstallmentTypeRefund:  "Hoàn phí",
	}

	TaskPriorities = map[int]string{
		TaskPriorityLow:    "Thấp",
		TaskPriorityMedium: "Trung bình",
		TaskPriorityHigh:   "Cao",
	}

	TaskTypes = map[string]string{
		TaskTypeCall:               "Gọi điện",
		TaskTypeMessage:            "Nhắn tin",
		TaskTypeEmail:              "Email",
		TaskTypeTheoreticalLecture: "Hẹn lý thuyết",
		TaskTypeReport:             "Báo cáo",
	}

	TaskStates = map[string]string{
		TaskStateNewTask:             "Mới tạo",
		TaskStateInProgress:          "Đang thực hiện",
		TaskStateOverdue:             "Quá hạn",
		TaskStateAwaitingApproval:    "Đang chờ phê duyệt",
		TaskStateCompleted:           "Hoàn thành",
		TaskStateCancelled:           "Huỷ",
		TaskStateCancelledInProgress: "Huỷ khi đang thực hiện",
		TaskStateNewCompletedEarly:   "Hoàn thành sớm",
	}

	TaskAssignmentState = map[string]string{
		TaskAssignmentStatePending:   "Đang chờ",
		TaskAssignmentStateOngoing:   "Đang thực hiện",
		TaskAssignmentStateCompleted: "Đã hoàn thành",
		TaskAssignmentStateOverdue:   "Đã quá hạn",
	}

	Gender = map[string]string{
		"female": "Nữ",
		"male":   "Nam",
	}
)
