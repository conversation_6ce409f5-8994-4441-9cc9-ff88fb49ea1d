package types

// Example usage of flexible notification system

/*
// FLEXIBLE APPROACH - Use semantic types with suggested categories:

// Creating notifications with semantic types (recommended):
semanticType := types.BuildNotificationType("task", "assigned")  // "task_assigned"
category := types.SuggestNotificationCategory(semanticType)      // NotificationTypeAssignment

notification := &dto.CreateNotificationInput{
    UserID:     userID,
    Type:       semanticType,                                     // "task_assigned"
    Message:    "Task assigned to you",
    EntityType: string(types.NotificationEntityTypeTask),        // "task"
    Metadata: map[string]interface{}{
        "category": string(category),                             // "assignment"
        "action":   "assigned",
        "task_id":  taskID,
    },
}

// CATEGORY-BASED FILTERING:
switch category {
case types.NotificationTypeAssignment:
    // Handle all assignment types (task_assigned, deal_assigned, etc.)
case types.NotificationTypeSuccess:
    // Handle all success types (task_completed, deal_won, payment_received, etc.)
case types.NotificationTypeReminder:
    // Handle all reminder types (appointment_reminder, task_due_soon, etc.)
}

// SEMANTIC TYPE EXAMPLES:
semanticTypes := []string{
    types.BuildNotificationType("task", "assigned"),      // "task_assigned"
    types.BuildNotificationType("task", "state_change"),  // "task_state_change"
    types.BuildNotificationType("deal", "stage_changed"), // "deal_stage_changed"
    types.BuildNotificationType("bill", "created"),       // "bill_created"
    types.BuildNotificationType("payment", "received"),   // "payment_received"
}

// CURRENT USAGE COMPATIBILITY:
// Your existing code using "task_state_change" and "task_last_for_person"
// works without changes and gets auto-categorized:

existingType := "task_state_change"
suggestedCategory := types.SuggestNotificationCategory(existingType)
// Returns NotificationTypeUpdate

// MIGRATION STRATEGY:
// 1. Keep existing semantic types as-is
// 2. Use SuggestNotificationCategory() for UI filtering/grouping
// 3. Gradually adopt BuildNotificationType() for new notifications
// 4. Use metadata for additional context instead of creating new types

*/
