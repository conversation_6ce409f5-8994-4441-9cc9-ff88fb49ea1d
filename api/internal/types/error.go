package types

import "bcare/common/berr"

var (
	// Auth
	ErrWrongPassword = berr.NewErrCodeMsg(2002, "Wrong password")
	ErrWrongUserName = berr.NewErrCodeMsg(2002, "Wrong user name")
	ErrGenerateToken = berr.NewErrCodeMsg(2003, "Error when generate token")

	// User
	ErrUserAdd      = berr.NewErrCodeMsg(3001, "Error adding user: %s")
	ErrUserNotFound = berr.NewErrCodeMsg(3002, "User with ID %d not found: %s")
	ErrUserUpdate   = berr.NewErrCodeMsg(3003, "Cannot update user with ID: %s")
	ErrUserExist    = berr.NewErrCodeMsg(3004, "User with username '%s', email '%s' or phone '%s' already exists")
	ErrUserGetList  = berr.NewErrCodeMsg(3007, "Cannot retrieve users list: %s")
	ErrUserCopy     = berr.NewErrCodeMsg(3008, "Failed to copy user data: %s")
	ErrUserData     = berr.NewErrCodeMsg(3009, "Cannot set user data: %s")

	//Product
	ErrProductNotFound = berr.NewErrCodeMsg(4000, "Product with ID %d not found: %s")
	ErrProductAdd      = berr.NewErrCodeMsg(4001, "Error adding product: %s")
	ErrProductUpdate   = berr.NewErrCodeMsg(4002, "Error updating product : %s")
	ErrProductExist    = berr.NewErrCodeMsg(4003, "Product with name '%s' or code '%s' already exists")
	ErrProductGetList  = berr.NewErrCodeMsg(4004, "Cannot retrieve product list: %s")
	ErrProductDelete   = berr.NewErrCodeMsg(4005, "Cannot delete product: %s")
	ErrProductCopy     = berr.NewErrCodeMsg(4006, "Failed to copy product data: %s")

	//Mind
	ErrMind = berr.NewErrCodeMsg(41000, "Error while generating text")

	//Person
	ErrPersonAdd             = berr.NewErrCodeMsg(6001, "Cannot add new person: %s")
	ErrPersonExist           = berr.NewErrCodeMsg(6002, "Person already exists with phone %s")
	ErrPersonNotFound        = berr.NewErrCodeMsg(6003, "Person with ID %d not found: %s")
	ErrPersonUpdate          = berr.NewErrCodeMsg(6004, "Cannot update person : %s")
	ErrPersonDelete          = berr.NewErrCodeMsg(6005, "Cannot delete person: %s")
	ErrPersonGetList         = berr.NewErrCodeMsg(6006, "Cannot retrieve person list: %s")
	ErrPersonCopy            = berr.NewErrCodeMsg(6007, "Cannot copy person data: %s")
	ErrPersonCreate          = berr.NewErrCodeMsg(6008, "Cannot create person record: %s")
	ErrPersonGetExpectedTask = berr.NewErrCodeMsg(6009, "Cannot get person expected task: %s")
	ErrPersonReferralCreate  = berr.NewErrCodeMsg(6010, "Cannot create person referral: %s")
	ErrPersonReferralUpdate  = berr.NewErrCodeMsg(6011, "Cannot update person referral: %s")
	ErrPersonData            = berr.NewErrCodeMsg(6012, "Cannot set person data: %s")

	//Appointment
	ErrAppointmentAdd        = berr.NewErrCodeMsg(7001, "Failed to add appointment: %s")
	ErrDoctorHasAppointment  = berr.NewErrCodeMsg(7005, "Doctor with ID %d already has an appointment in the specified time range")
	ErrAppointmentNotFound   = berr.NewErrCodeMsg(7002, "Appointment with ID %d not found: %s")
	ErrAppointmentUpdate     = berr.NewErrCodeMsg(7003, "Failed to update appointment: %s")
	ErrAppointmentGetList    = berr.NewErrCodeMsg(7004, "Cannot retrieve appointments list: %s")
	ErrAppointmentCopyFailed = berr.NewErrCodeMsg(7006, "Failed to copy appointment data: %s")

	//Bundle
	ErrBundleNotFound     = berr.NewErrCodeMsg(8001, "Bundle not found")
	ErrBundleAdd          = berr.NewErrCodeMsg(8002, "Add bundle error")
	ErrBundleUpdate       = berr.NewErrCodeMsg(8003, "Cannot update bundle")
	ErrBundleMachineExist = berr.NewErrCodeMsg(8004, "Machine name exists")
	ErrBundleGetList      = berr.NewErrCodeMsg(8005, "Cannot get bundles list")

	//Field
	ErrFieldConfigNotFound = berr.NewErrCodeMsg(9001, "Field config not found")
	ErrFieldConfigAdd      = berr.NewErrCodeMsg(9002, "Field config add error")

	//Term
	ErrTermNotFound     = berr.NewErrCodeMsg(10001, "Term not found")
	ErrTermAdd          = berr.NewErrCodeMsg(10002, "Add term error")
	ErrTermUpdate       = berr.NewErrCodeMsg(10003, "Cannot update term")
	ErrTermMachineExist = berr.NewErrCodeMsg(10004, "Machine name exists")
	ErrTermGetList      = berr.NewErrCodeMsg(10005, "Cannot get terms list")
	ErrTermDelete       = berr.NewErrCodeMsg(10006, "Cannot delete term")

	//Schedule
	ErrScheduleNotFound = berr.NewErrCodeMsg(11001, "Schedule not found : %s")
	ErrScheduleUpdate   = berr.NewErrCodeMsg(11002, "Cannot update Schedule")
	ErrScheduleAdd      = berr.NewErrCodeMsg(11003, "Cannot Add Schedule")
	ErrScheduleDelete   = berr.NewErrCodeMsg(11004, "Cannot delete Schedule")

	//Deal
	ErrDealCreate   = berr.NewErrCodeMsg(12008, "Cannot create deal: %s")
	ErrDealUpdate   = berr.NewErrCodeMsg(12009, "Cannot update deal: %s")
	ErrDealDelete   = berr.NewErrCodeMsg(12010, "Cannot delete deal: %s")
	ErrDealList     = berr.NewErrCodeMsg(12011, "Cannot get deal list: %s")
	ErrDealCopy     = berr.NewErrCodeMsg(12012, "Cannot copy deal data: %s")
	ErrDealNotFound = berr.NewErrCodeMsg(12013, "Deal with ID %d not found: %s")
	ErrDealAdd      = berr.NewErrCodeMsg(12014, "Cannot add deal: %s")
	ErrDealQuery    = berr.NewErrCodeMsg(12015, "Error querying deal: %s")

	//Attachment
	ErrAttachmentNotFound    = berr.NewErrCodeMsg(13001, "Attachment with ID %d not found")
	ErrAttachmentAdd         = berr.NewErrCodeMsg(13002, "Failed to add attachment: %s")
	ErrAttachmentAddDiscount = berr.NewErrCodeMsg(13003, "Failed to add attachment discount: %s")
	ErrAttachmentUpdate      = berr.NewErrCodeMsg(13004, "Failed to update attachment: %s")
	ErrAttachmentGetList     = berr.NewErrCodeMsg(13005, "Cannot retrieve attachment list: %s")
	ErrAttachmentCopyFailed  = berr.NewErrCodeMsg(13007, "Failed to copy attachment data: %s")
	ErrAttachmentData        = berr.NewErrCodeMsg(13008, "Cannot set person data: %s")
	ErrAttachmentGetChildren = berr.NewErrCodeMsg(13009, "Cannot retrieve attachment children: %s")

	//Installment
	ErrInstallmentNotFound = berr.NewErrCodeMsg(14001, "Installment not found")
	ErrInstallmentAdd      = berr.NewErrCodeMsg(14002, "Add Installment error: %s")
	ErrInstallmentUpdate   = berr.NewErrCodeMsg(14003, "Cannot update Installment: %s")
	ErrInstallmentGetList  = berr.NewErrCodeMsg(14004, "Cannot get Installment list: %s")
	ErrInstallmentDelete   = berr.NewErrCodeMsg(14005, "Cannot delete Installment: %s")
	ErrInstallmentCreate   = berr.NewErrCodeMsg(140006, "Cannot create installment: %s")

	//DealUser
	ErrDealUserNotFound   = berr.NewErrCodeMsg(15001, "DealUser with ID %d not found")
	ErrDealUserAdd        = berr.NewErrCodeMsg(15002, "Error adding DealUser: %s")
	ErrDealUserUpdate     = berr.NewErrCodeMsg(15003, "Cannot update DealUser: %s")
	ErrDealUserGetList    = berr.NewErrCodeMsg(15004, "Cannot retrieve DealUser list: %s")
	ErrDealUserDelete     = berr.NewErrCodeMsg(15005, "Cannot delete DealUser: %s")
	ErrDealUserCopyFailed = berr.NewErrCodeMsg(15006, "Failed to copy DealUser data: %s")

	//Pipeline
	ErrPipelineNotFound = berr.NewErrCodeMsg(16001, "Pipeline not found")
	ErrPipelineAdd      = berr.NewErrCodeMsg(16002, "Add Pipeline error")
	ErrPipelineUpdate   = berr.NewErrCodeMsg(16003, "Cannot update Pipeline")
	ErrPipelineGetList  = berr.NewErrCodeMsg(16004, "Cannot get Pipeline list")

	//Stage
	ErrStageNotFound     = berr.NewErrCodeMsg(17001, "Stage not found")
	ErrStageAdd          = berr.NewErrCodeMsg(17002, "Add Stage error")
	ErrStageUpdate       = berr.NewErrCodeMsg(17003, "Cannot update Stage")
	ErrStageGetList      = berr.NewErrCodeMsg(17004, "Cannot get Stage list")
	ErrStageGetChildList = berr.NewErrCodeMsg(17004, "Cannot get child stage list")

	//Treatment
	ErrTreatmentLogNotFound = berr.NewErrCodeMsg(18001, "Treatment not found")
	ErrTreatmentLogAdd      = berr.NewErrCodeMsg(18002, "Add Treatment error")
	ErrTreatmentLogUpdate   = berr.NewErrCodeMsg(18003, "Cannot update Treatment")

	//Survey
	ErrSurveyNotFound = berr.NewErrCodeMsg(19001, "Survey not found")
	ErrSurveyAdd      = berr.NewErrCodeMsg(19002, "Add Survey error")
	ErrSurveyUpdate   = berr.NewErrCodeMsg(19003, "Cannot update Survey")
	ErrSurveyGetList  = berr.NewErrCodeMsg(19004, "Cannot get Survey list")

	//Question
	ErrQuestionNotFound = berr.NewErrCodeMsg(20001, "Question not found")
	ErrQuestionAdd      = berr.NewErrCodeMsg(20002, "Add Question error")
	ErrQuestionUpdate   = berr.NewErrCodeMsg(20003, "Cannot update Question")
	ErrQuestionGetList  = berr.NewErrCodeMsg(20004, "Cannot get Question list")

	//Response
	ErrResponseNotFound = berr.NewErrCodeMsg(21001, "Response not found")
	ErrResponseAdd      = berr.NewErrCodeMsg(21002, "Add Response error")
	ErrResponseUpdate   = berr.NewErrCodeMsg(21003, "Cannot update Response")
	ErrResponseGetList  = berr.NewErrCodeMsg(21004, "Cannot get Response list")

	//File
	ErrFileAdd      = berr.NewErrCodeMsg(22001, "File upload failed")
	ErrFileNotFound = berr.NewErrCodeMsg(22002, "File not found")

	//FileUsage
	ErrFileUsageAdd      = berr.NewErrCodeMsg(23001, "FileUsage cannot add")
	ErrFileUsageNotFound = berr.NewErrCodeMsg(23002, "FileUsage not found")
	ErrFileUsageDelete   = berr.NewErrCodeMsg(23003, "FileUsage cannot delete")
	ErrFileUsageGetList  = berr.NewErrCodeMsg(23004, "Cannot get FileUsage list")

	//Quote
	ErrQuoteAdd      = berr.NewErrCodeMsg(24001, "Quote cannot add")
	ErrQuoteNotFound = berr.NewErrCodeMsg(24002, "Quote not found")
	ErrQuoteDelete   = berr.NewErrCodeMsg(24003, "Quote cannot delete")
	ErrQuoteGetList  = berr.NewErrCodeMsg(24004, "Cannot get Quote list")
	ErrQuoteUpdate   = berr.NewErrCodeMsg(24005, "Quote cannot update")

	//QuoteItem
	ErrQuoteItemAdd      = berr.NewErrCodeMsg(25001, "QuoteItem cannot add")
	ErrQuoteItemNotFound = berr.NewErrCodeMsg(25002, "QuoteItem not found")
	ErrQuoteItemUpdate   = berr.NewErrCodeMsg(25003, "QuoteItem cannot update")
	ErrQuoteItemDelete   = berr.NewErrCodeMsg(25004, "QuoteItem cannot delete")
	ErrQuoteItemGetList  = berr.NewErrCodeMsg(25005, "Cannot get QuoteItem list")

	// Discount errors
	ErrDiscountAdd      = berr.NewErrCodeMsg(26001, "Discount cannot add")
	ErrDiscountUpdate   = berr.NewErrCodeMsg(26002, "Discount cannot update")
	ErrDiscountNotFound = berr.NewErrCodeMsg(26003, "Discount not found")
	ErrDiscountDelete   = berr.NewErrCodeMsg(26004, "Discount cannot delete")
	ErrDiscountGetList  = berr.NewErrCodeMsg(26005, "Cannot get Discount list")

	// Additional errors for EligibleDiscount logic
	ErrDiscountCalculate   = berr.NewErrCodeMsg(26007, "Discount calculate error")
	ErrProductsNotFound    = berr.NewErrCodeMsg(26008, "Products not found")
	ErrNoProductsFound     = berr.NewErrCodeMsg(26009, "No products found")
	ErrDiscountQueryFailed = berr.NewErrCodeMsg(26010, "Discount query failed")
	ErrDiscountCastFailed  = berr.NewErrCodeMsg(26011, "Discount cast failed")
	ErrDiscountCopyFailed  = berr.NewErrCodeMsg(26012, "Discount copy failed")

	//Discounts Usage
	ErrDiscountUsageAdd      = berr.NewErrCodeMsg(27001, "DiscountUsage cannot add")
	ErrDiscountUsageNotFound = berr.NewErrCodeMsg(27003, "DiscountUsage not found")
	ErrDiscountUsageDelete   = berr.NewErrCodeMsg(27004, "DiscountUsage cannot delete")
	ErrDiscountUsageGetList  = berr.NewErrCodeMsg(27005, "Cannot get DiscountUsage list")
	ErrDiscountUsageUpdate   = berr.NewErrCodeMsg(27002, "DiscountUsage cannot update") //Discounts Usage

	//Note
	ErrNoteAdd        = berr.NewErrCodeMsg(28001, "Cannot add note: %s")
	ErrNoteNotFound   = berr.NewErrCodeMsg(28003, "Note with ID %d not found: %s")
	ErrNoteDelete     = berr.NewErrCodeMsg(28004, "Cannot delete note: %s")
	ErrNoteGetList    = berr.NewErrCodeMsg(28005, "Cannot retrieve note list: %s")
	ErrNoteUpdate     = berr.NewErrCodeMsg(28002, "Cannot update note: %s")
	ErrNoteCopyFailed = berr.NewErrCodeMsg(28006, "Failed to copy note data: %s")

	//Bill
	ErrBillAdd      = berr.NewErrCodeMsg(29001, "Cannot add bill: %s")
	ErrBillNotFound = berr.NewErrCodeMsg(29003, "Bill not found: %s")
	ErrBillDelete   = berr.NewErrCodeMsg(29004, "Cannot delete bill: %s")
	ErrBillGetList  = berr.NewErrCodeMsg(29005, "Cannot get bill list: %s")
	ErrBillUpdate   = berr.NewErrCodeMsg(29002, "Cannot update bill: %s")
	ErrBillData     = berr.NewErrCodeMsg(29006, "Cannot set bill data: %s")

	//Bill item
	ErrBillItemAdd        = berr.NewErrCodeMsg(29006, "Cannot add bill item: %s")
	ErrBillItemUpdate     = berr.NewErrCodeMsg(29007, "Cannot update bill item: %s")
	ErrBillItemNotFound   = berr.NewErrCodeMsg(30003, "Bill item with ID %d not found: %s")
	ErrBillItemDelete     = berr.NewErrCodeMsg(30004, "Cannot delete bill item: %s")
	ErrBillItemGetList    = berr.NewErrCodeMsg(30005, "Cannot retrieve bill item list: %s")
	ErrBillItemInvalid    = berr.NewErrCodeMsg(30006, "Invalid bill item data: %s")
	ErrBillItemCopyFailed = berr.NewErrCodeMsg(30007, "Failed to copy bill item data: %s")

	//Relation
	ErrRelationAdd    = berr.NewErrCodeMsg(31001, "Relation cannot add")
	ErrRelationDelete = berr.NewErrCodeMsg(31002, "Relation cannot delete")
	ErrRelationUpdate = berr.NewErrCodeMsg(31003, "Relation cannot update")

	//Track
	ErrTrackAdd      = berr.NewErrCodeMsg(32001, "Track cannot add")
	ErrTrackDelete   = berr.NewErrCodeMsg(32002, "Track cannot delete")
	ErrTrackGetList  = berr.NewErrCodeMsg(32004, "Cannot get Track list")
	ErrTrackNotFound = berr.NewErrCodeMsg(32005, "Track not found")
	ErrTrackCreate   = berr.NewErrCodeMsg(12015, "Cannot create track for deal: %s")
	ErrTrackUpdate   = berr.NewErrCodeMsg(12016, "Cannot update track for deal: %s")
	//Referral
	ErrReferralAdd    = berr.NewErrCodeMsg(33001, "Referral cannot add")
	ErrReferralDelete = berr.NewErrCodeMsg(33002, "Referral cannot delete")
	ErrReferralUpdate = berr.NewErrCodeMsg(33003, "Referral cannot update")

	//Task
	ErrTaskAdd          = berr.NewErrCodeMsg(34001, "Cannot add task: %s")
	ErrTaskNotFound     = berr.NewErrCodeMsg(34003, "Task with ID %d not found: %s")
	ErrTaskDelete       = berr.NewErrCodeMsg(34004, "Cannot delete task: %s")
	ErrTaskGetList      = berr.NewErrCodeMsg(34005, "Cannot retrieve task list: %s")
	ErrTaskUpdate       = berr.NewErrCodeMsg(34002, "Cannot update task: %s")
	ErrTaskBulkUpdate   = berr.NewErrCodeMsg(34002, "Cannot update bulk task: %s")
	ErrTaskCopy         = berr.NewErrCodeMsg(34006, "Failed to copy task data: %s")
	ErrTaskAddRecurring = berr.NewErrCodeMsg(34007, "Cannot create recurring task: %s")
	//Setting
	ErrSettingAdd      = berr.NewErrCodeMsg(35001, "Setting cannot add")
	ErrSettingNotFound = berr.NewErrCodeMsg(35003, "Setting not found")
	ErrSettingDelete   = berr.NewErrCodeMsg(35004, "Setting cannot delete")
	ErrSettingGetList  = berr.NewErrCodeMsg(35005, "Cannot get Setting list")
	ErrSettingUpdate   = berr.NewErrCodeMsg(35002, "Setting cannot update")

	//Call
	ErrCallAdd            = berr.NewErrCodeMsg(36001, "Cannot add call: %s")
	ErrCallNotFound       = berr.NewErrCodeMsg(36003, "Call with ID %d not found: %s")
	ErrCallDelete         = berr.NewErrCodeMsg(36004, "Cannot delete call: %s")
	ErrCallGetList        = berr.NewErrCodeMsg(36005, "Cannot retrieve call list: %s")
	ErrCallUpdate         = berr.NewErrCodeMsg(36002, "Cannot update call: %s")
	ErrCallUpdateFeedback = berr.NewErrCodeMsg(36006, "Cannot update call feedback: %s")
	ErrCallCopyFailed     = berr.NewErrCodeMsg(36007, "Failed to copy call data: %s")

	//TaskNote
	ErrTaskNoteAdd      = berr.NewErrCodeMsg(37001, "TaskNote cannot add")
	ErrTaskNoteNotFound = berr.NewErrCodeMsg(37003, "TaskNote not found")
	ErrTaskNoteDelete   = berr.NewErrCodeMsg(37004, "TaskNote cannot delete")
	ErrTaskNoteUpdate   = berr.NewErrCodeMsg(37002, "TaskNote cannot update")

	// Định nghĩa các lỗi cho SwitchDeal
	ErrInvalidStageID    = berr.NewErrCodeMsg(38001, "Invalid Stage ID provided")
	ErrInvalidTrackID    = berr.NewErrCodeMsg(38002, "Invalid Track ID provided")
	ErrInvalidPipelineID = berr.NewErrCodeMsg(38003, "Invalid Pipeline ID provided")
	ErrSwitchDeal        = berr.NewErrCodeMsg(38004, "Failed to switch deal")

	//Installment Plan
	ErrPlanAdd        = berr.NewErrCodeMsg(39001, "Cannot add Plan: %s")
	ErrPlanNotFound   = berr.NewErrCodeMsg(39003, "Plan  not found: %s")
	ErrPlanDelete     = berr.NewErrCodeMsg(39004, "Cannot delete Plan: %s")
	ErrPlanGetList    = berr.NewErrCodeMsg(39005, "Cannot retrieve Plan list: %s")
	ErrPlanUpdate     = berr.NewErrCodeMsg(39002, "Cannot update Plan : %s")
	ErrPlanCopyFailed = berr.NewErrCodeMsg(39006, "Failed to copy Plan data: %s")

	//Operation
	ErrOperationAll = berr.NewErrCodeMsg(40001, "Operation cannot get : %s")

	//payment
	ErrPaymentAdd        = berr.NewErrCodeMsg(41001, "Cannot add payment: %s")
	ErrPaymentUpdate     = berr.NewErrCodeMsg(41002, "Cannot update payment: %s")
	ErrPaymentNotFound   = berr.NewErrCodeMsg(41003, "Payment with ID %d not found: %s")
	ErrPaymentDelete     = berr.NewErrCodeMsg(41004, "Cannot delete payment: %s")
	ErrPaymentGetList    = berr.NewErrCodeMsg(41005, "Cannot retrieve payment list: %s")
	ErrPaymentInvalid    = berr.NewErrCodeMsg(41006, "Invalid payment data: %s")
	ErrPaymentCopyFailed = berr.NewErrCodeMsg(41007, "Failed to copy payment data: %s")

	// PaymentAllocation
	ErrPaymentAllocationAdd        = berr.NewErrCodeMsg(42001, "Cannot add payment allocation: %s")
	ErrPaymentAllocationUpdate     = berr.NewErrCodeMsg(42002, "Cannot update payment allocation: %s")
	ErrPaymentAllocationNotFound   = berr.NewErrCodeMsg(42003, "Payment allocation with ID %d not found: %s")
	ErrPaymentAllocationDelete     = berr.NewErrCodeMsg(42004, "Cannot delete payment allocation: %s")
	ErrPaymentAllocationGetList    = berr.NewErrCodeMsg(42005, "Cannot retrieve payment allocation list: %s")
	ErrPaymentAllocationInvalid    = berr.NewErrCodeMsg(42006, "Invalid payment allocation data: %s")
	ErrPaymentAllocationCopyFailed = berr.NewErrCodeMsg(42007, "Failed to copy payment allocation data: %s")

	//Deposit
	ErrDepositAdd        = berr.NewErrCodeMsg(43001, "Cannot add deposit: %s")
	ErrDepositUpdate     = berr.NewErrCodeMsg(43002, "Cannot update deposit: %s")
	ErrDepositNotFound   = berr.NewErrCodeMsg(43003, "Deposit with ID %d not found: %s")
	ErrDepositDelete     = berr.NewErrCodeMsg(43004, "Cannot delete deposit: %s")
	ErrDepositGetList    = berr.NewErrCodeMsg(43005, "Cannot retrieve deposit list: %s")
	ErrDepositInvalid    = berr.NewErrCodeMsg(43006, "Invalid deposit data: %s")
	ErrDepositCopyFailed = berr.NewErrCodeMsg(43007, "Failed to copy deposit data: %s")

	// Additional deposit specific errors
	ErrInvalidAmount            = berr.NewErrCodeMsg(43008, "Invalid amount: amount must be greater than 0")
	ErrAllocationExceedsDeposit = berr.NewErrCodeMsg(43010, "Total allocation amount exceeds deposit amount")

	//history
	ErrHistoryGetList = berr.NewErrCodeMsg(44001, "Cannot retrieve history list: %s")
)
