package model

import (
	"bcare/api/internal/dto"
	"bcare/api/internal/types"
	"bcare/common/bconst"
	"bcare/common/berr"
	"bcare/common/bquery"
	"bcare/common/cast"
	"bcare/common/ctxdata"
	"bcare/ent"
	"bcare/ent/predicate"
	"bcare/ent/user"
	"bcare/ent/userdata"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
	"golang.org/x/crypto/bcrypt"
)

type UserModel struct {
	Logger   logx.Logger
	DbClient *ent.Client
}

func NewUserModel(dbClient *ent.Client) *UserModel {
	logger := logx.WithContext(context.Background()).WithFields(logx.Field("model", "UserModel"))
	return &UserModel{
		Logger:   logger,
		DbClient: dbClient,
	}
}

func (m *UserModel) checkDuplicate(ctx context.Context, username, email, phone *string, excludeID *int) error {
	op := "UserModel.checkDuplicate"
	var predicates []predicate.User

	if username != nil && *username != "" {
		predicates = append(predicates, user.UsernameEQ(*username))
	}
	//if email != nil && *email != "" {
	//	predicates = append(predicates, user.EmailEQ(*email))
	//}
	//if phone != nil && *phone != "" {
	//	predicates = append(predicates, user.PhoneEQ(*phone))
	//}

	if len(predicates) == 0 {
		return nil
	}

	query := m.DbClient.User.Query().Where(user.Or(predicates...))
	if excludeID != nil {
		query = query.Where(user.IDNEQ(*excludeID))
	}

	exists, err := query.Exist(ctx)
	if err != nil {
		return berr.ErrDB.Wrap(err).Op(op)
	}
	if exists {
		return berr.ErrDuplicateEntry.Op(op)
	}
	return nil
}

func (m *UserModel) CreateUser(ctx context.Context, input *dto.CreateUserInput) (*dto.UserOutput, error) {
	op := "UserModel.CreateUser"
	actorID := ctxdata.GetUidFromCtx(ctx)

	var emailPtr, phonePtr *string
	if input.Email != "" {
		emailPtr = &input.Email
	}
	if input.Phone != "" {
		phonePtr = &input.Phone
	}

	if err := m.checkDuplicate(ctx, &input.Username, emailPtr, phonePtr, nil); err != nil {
		return nil, err
	}

	userEnt := new(ent.User)
	err := cast.InputToEnt(userEnt, input)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	if input.Password == "" {
		return nil, berr.ErrValidationFailed.Op(op)
	}
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(input.Password), bcrypt.DefaultCost)
	if err != nil {
		return nil, berr.ErrOperationFailed.Wrap(err).Op(op)
	}
	userEnt.Password = string(hashedPassword)

	userEnt.Status = bconst.StatusNormal

	createdUser, err := m.DbClient.User.Create().
		SetUser(userEnt).
		Save(ctx)

	if err != nil {
		if ent.IsConstraintError(err) {
			return nil, berr.ErrDuplicateEntry.Wrap(err).Op(op)
		}
		return nil, berr.ErrCreateFailed.Wrap(err).Op(op)
	}

	m.Logger.WithContext(ctx).Infof("%s - User %d created by actor %d", op, createdUser.ID, actorID)

	output := new(dto.UserOutput)
	createdUserWithData, getErr := m.DbClient.User.Query().
		WithData(func(q *ent.UserDataQuery) { q.Where(userdata.KindNEQ("phone_view_history")) }).
		Where(user.ID(createdUser.ID)).
		Only(ctx)
	if getErr != nil {
		err = cast.ConvertViaJson(output, createdUser)
	} else {
		err = cast.ConvertViaJson(output, createdUserWithData)
	}

	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}
	return output, nil
}

func (m *UserModel) GetUser(ctx context.Context, id int) (*dto.UserOutput, error) {
	op := "UserModel.GetUser"

	userRecord, err := m.DbClient.User.Query().
		WithData().
		Where(user.IDEQ(id)).
		Only(ctx)
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, berr.ErrNotFound.Wrap(err).Op(op)
		}
		return nil, berr.ErrDB.Wrap(err).Op(op)
	}

	output := new(dto.UserOutput)
	err = cast.ConvertViaJson(output, userRecord)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}
	return output, nil
}

func (m *UserModel) ListUsers(ctx context.Context, input dto.ListUsersInput) (*dto.ListUsersOutput, error) {
	op := "UserModel.ListUsers"

	limit, offset := bquery.Paging(input.PageSize, input.Page)
	order := bquery.Ordering(input.OrderBy)

	query := m.DbClient.User.Query().
		WithData().
		Order(order)

	if len(input.Filter.Ids) > 0 {
		query = query.Where(user.IDIn(input.Filter.Ids...))
	}
	if input.Filter.Username != "" {
		query = query.Where(user.UsernameContainsFold(input.Filter.Username))
	}
	if input.Filter.Name != "" {
		query = query.Where(user.NameContainsFold(input.Filter.Name))
	}
	if input.Filter.Email != "" {
		query = query.Where(user.EmailContainsFold(input.Filter.Email))
	}
	if input.Filter.Gender != "" {
		query = query.Where(user.GenderEQ(input.Filter.Gender))
	}
	if input.Filter.Phone != "" {
		query = query.Where(user.PhoneContainsFold(input.Filter.Phone))
	}
	if input.Filter.Status != 0 {
		query = query.Where(user.StatusEQ(int8(input.Filter.Status)))
	}
	if input.Filter.State != "" {
		query = query.Where(user.StateEQ(user.State(input.Filter.State)))
	}
	if input.Filter.DepartmentID != 0 {
		query = query.Where(user.DepartmentIDEQ(input.Filter.DepartmentID))
	}

	if input.Search != "" {
		query = query.Where(
			user.Or(
				user.UsernameContainsFold(input.Search),
				user.NameContainsFold(input.Search),
				user.EmailContainsFold(input.Search),
				user.PhoneContainsFold(input.Search),
			),
		)
	}

	total, err := query.Clone().Count(ctx)
	if err != nil {
		return nil, berr.ErrListFailed.Wrap(err).Op(op)
	}

	output := &dto.ListUsersOutput{
		Users: []*dto.UserOutput{},
		Total: total,
	}

	if total == 0 {
		return output, nil
	}

	usersData, err := query.Limit(limit).Offset(offset).All(ctx)
	if err != nil {
		return nil, berr.ErrListFailed.Wrap(err).Op(op)
	}

	err = cast.ConvertViaJson(&output.Users, usersData)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	return output, nil
}

func (m *UserModel) UpdateUser(ctx context.Context, input *dto.UpdateUserInput) (*dto.UserOutput, error) {
	op := "UserModel.UpdateUser"
	actorID := ctxdata.GetUidFromCtx(ctx)

	if err := m.checkDuplicate(ctx, input.Username, input.Email, input.Phone, &input.ID); err != nil {
		return nil, err
	}

	currentUserRecord, err := m.DbClient.User.Get(ctx, input.ID)
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, berr.ErrNotFound.Wrap(err).Op(op)
		}
		return nil, berr.ErrDB.Wrap(err).Op(op)
	}

	updateDataEnt := new(ent.User)
	err = cast.InputToEnt(updateDataEnt, input)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	if input.Password != nil && *input.Password != "" {
		hashedPassword, err := bcrypt.GenerateFromPassword([]byte(*input.Password), bcrypt.DefaultCost)
		if err != nil {
			return nil, berr.ErrOperationFailed.Wrap(err).Op(op)
		}
		updateDataEnt.Password = string(hashedPassword)
	} else if input.Password != nil && *input.Password == "" {
		updateDataEnt.Password = ""
	}

	if input.Email != nil && *input.Email != currentUserRecord.Email {
		updateDataEnt.EmailConfirmed = false
	} else if input.Email != nil && *input.Email == currentUserRecord.Email {
		updateDataEnt.EmailConfirmed = currentUserRecord.EmailConfirmed
	} else if input.Email == nil {
		updateDataEnt.EmailConfirmed = currentUserRecord.EmailConfirmed
	}

	updatedUser, err := m.DbClient.User.UpdateOneID(input.ID).
		SetUser(updateDataEnt, input.Modified...).
		Save(ctx)
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, berr.ErrNotFound.Wrap(err).Op(op)
		}
		if ent.IsConstraintError(err) {
			return nil, berr.ErrDuplicateEntry.Wrap(err).Op(op)
		}
		return nil, berr.ErrUpdateFailed.Wrap(err).Op(op)
	}

	m.Logger.WithContext(ctx).Infof("%s - User %d updated by actor %d", op, updatedUser.ID, actorID)

	output := new(dto.UserOutput)
	updatedUserWithData, getErr := m.DbClient.User.Query().
		WithData(func(q *ent.UserDataQuery) { q.Where(userdata.KindNEQ("phone_view_history")) }).
		Where(user.ID(updatedUser.ID)).
		Only(ctx)
	if getErr != nil {
		err = cast.ConvertViaJson(output, updatedUser)
	} else {
		err = cast.ConvertViaJson(output, updatedUserWithData)
	}

	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}
	return output, nil
}

func (m *UserModel) DeleteUser(ctx context.Context, id int) error {
	op := "UserModel.DeleteUser"
	actorID := ctxdata.GetUidFromCtx(ctx)

	_, err := m.DbClient.User.Get(ctx, id)
	if err != nil {
		if ent.IsNotFound(err) {
			return berr.ErrNotFound.Wrap(err).Op(op)
		}
		return berr.ErrDB.Wrap(err).Op(op)
	}

	err = m.DbClient.User.DeleteOneID(id).Exec(ctx)
	if err != nil {
		return berr.ErrDeleteFailed.Wrap(err).Op(op)
	}

	m.Logger.WithContext(ctx).Infof("%s - User %d deleted by actor %d", op, id, actorID)
	return nil
}

func (m *UserModel) GetDepartmentLeaders(ctx context.Context, departmentID int) ([]*dto.UserOutput, error) {
	op := "UserModel.GetDepartmentLeaders"

	leaders, err := m.DbClient.User.Query().
		Where(
			user.DepartmentIDEQ(departmentID),
			user.DepartmentPositionIn(types.UserDepartmentPositionManager, types.UserDepartmentPositionDeputyManager),
			user.Status(bconst.StatusNormal),
		).
		All(ctx)

	if err != nil {
		return nil, berr.ErrDB.Wrap(err).Op(op)
	}

	var result []*dto.UserOutput
	for _, leader := range leaders {
		userOutput := new(dto.UserOutput)
		err = cast.ConvertViaJson(userOutput, leader)

		result = append(result, userOutput)
	}

	return result, nil
}

func (m *UserModel) GetDepartmentLeadersByUserID(ctx context.Context, userID int) ([]*dto.UserOutput, error) {
	op := "UserModel.GetDepartmentLeadersByUserID"

	targetUser, err := m.DbClient.User.Query().
		Where(user.IDEQ(userID)).
		Select(user.FieldDepartmentID).
		Only(ctx)
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, berr.ErrNotFound.Wrap(err).Op(op)
		}
		return nil, berr.ErrDB.Wrap(err).Op(op)
	}

	if targetUser.DepartmentID == 0 {
		return []*dto.UserOutput{}, nil
	}

	leaders, err := m.DbClient.User.Query().
		Where(
			user.DepartmentIDEQ(targetUser.DepartmentID),
			user.DepartmentPositionIn(types.UserDepartmentPositionManager, types.UserDepartmentPositionDeputyManager),
			user.Status(bconst.StatusNormal),
		).
		All(ctx)

	if err != nil {
		return nil, berr.ErrDB.Wrap(err).Op(op)
	}

	var result []*dto.UserOutput
	for _, leader := range leaders {
		userOutput := new(dto.UserOutput)
		err = cast.ConvertViaJson(userOutput, leader)
		if err != nil {
			continue
		}
		result = append(result, userOutput)
	}

	return result, nil
}
