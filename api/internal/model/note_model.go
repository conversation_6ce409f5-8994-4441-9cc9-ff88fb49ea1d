package model

import (
	"bcare/api/internal/dto"
	"context"

	"bcare/common/berr"
	"bcare/common/bquery"
	"bcare/common/cast"
	"bcare/common/ctxdata"
	"bcare/ent"
	"bcare/ent/note"
	"bcare/ent/user"

	"github.com/zeromicro/go-zero/core/logx"
)

type NoteModel struct {
	Logger   logx.Logger
	DbClient *ent.Client
}

func NewNoteModel(dbClient *ent.Client) *NoteModel {
	logger := logx.WithContext(context.Background()).WithFields(logx.Field("model", "NoteModel"))
	return &NoteModel{
		Logger:   logger,
		DbClient: dbClient,
	}
}

func (m *NoteModel) CreateNote(ctx context.Context, input *dto.CreateNoteInput) (*dto.NoteOutput, error) {
	op := "NoteModel.CreateNote"
	userID := ctxdata.GetUidFromCtx(ctx)

	noteRecord := new(ent.Note)
	err := cast.InputToEnt(noteRecord, input)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	noteRecord.UserID = userID

	createdNote, err := m.DbClient.Note.Create().
		SetNote(noteRecord).
		Save(ctx)

	if err != nil {
		return nil, berr.ErrCreateFailed.Wrap(err).Op(op)
	}

	m.Logger.WithContext(ctx).Infof("%s - Note %d created by user %d", op, createdNote.ID, userID)

	output := new(dto.NoteOutput)
	err = cast.ConvertViaJson(output, createdNote)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}
	return output, nil
}

func (m *NoteModel) GetNote(ctx context.Context, id int) (*dto.NoteOutput, error) {
	op := "NoteModel.GetNote"

	noteRecord, err := m.DbClient.Note.Query().Where(note.IDEQ(id)).Only(ctx)
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, berr.ErrNotFound.Wrap(err).Op(op)
		}
		return nil, berr.ErrDB.Wrap(err).Op(op)
	}

	output := new(dto.NoteOutput)
	err = cast.ConvertViaJson(output, noteRecord)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	return output, nil
}

func (m *NoteModel) ListNotes(ctx context.Context, input dto.ListNotesInput) (*dto.ListNotesOutput, error) {
	op := "NoteModel.ListNotes"

	limit, offset := bquery.Paging(input.PageSize, input.Page)
	order := bquery.Ordering(input.OrderBy)

	query := m.DbClient.Note.Query().
		WithCreator().
		WithPerson()

	if len(input.Filter.IDs) > 0 {
		query = query.Where(note.IDIn(input.Filter.IDs...))
	}

	if input.Search != "" {
		query = query.Where(note.BodyContainsFold(input.Search))
	} else if input.Filter.Body != "" {
		query = query.Where(note.BodyContainsFold(input.Filter.Body))
	}

	if input.Filter.Type != nil {
		query = query.Where(note.TypeEQ(*input.Filter.Type))
	}
	if input.Filter.PersonID != nil {
		query = query.Where(note.PersonIDEQ(*input.Filter.PersonID))
	}
	if input.Filter.DealID != nil {
		query = query.Where(note.DealIDEQ(*input.Filter.DealID))
	}
	if input.Filter.Status != nil {
		query = query.Where(note.StatusEQ(*input.Filter.Status))
	}
	if input.Filter.UserID != nil {
		query = query.Where(note.HasCreatorWith(user.IDEQ(*input.Filter.UserID)))
	}

	if input.DepartmentID != nil {
		query = query.Where(note.HasCreatorWith(user.DepartmentID(*input.DepartmentID)))
	}

	total, err := query.Clone().Count(ctx)
	if err != nil {
		return nil, berr.ErrListFailed.Wrap(err).Op(op)
	}

	output := &dto.ListNotesOutput{
		Notes: []*dto.NoteOutput{},
		Total: total,
	}

	if total == 0 {
		return output, nil
	}

	query = query.Order(order)
	entNotes, err := query.Limit(limit).Offset(offset).All(ctx)
	if err != nil {
		return nil, berr.ErrListFailed.Wrap(err).Op(op)
	}

	err = cast.ConvertViaJson(&output.Notes, entNotes)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	return output, nil
}

func (m *NoteModel) UpdateNote(ctx context.Context, input *dto.UpdateNoteInput) (*dto.NoteOutput, error) {
	op := "NoteModel.UpdateNote"
	userID := ctxdata.GetUidFromCtx(ctx)

	dbNoteRecord, err := m.DbClient.Note.Get(ctx, input.ID)
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, berr.ErrNotFound.Wrap(err).Op(op)
		}
		return nil, berr.ErrDB.Wrap(err).Op(op)
	}

	err = cast.InputToEnt(dbNoteRecord, input)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	updater := m.DbClient.Note.UpdateOneID(input.ID)
	updater.SetNote(dbNoteRecord, input.Modified...)

	savedNote, err := updater.Save(ctx)

	if err != nil {
		return nil, berr.ErrUpdateFailed.Wrap(err).Op(op)
	}

	m.Logger.WithContext(ctx).Infof("%s - Note %d updated by user %d", op, savedNote.ID, userID)

	output := new(dto.NoteOutput)
	err = cast.ConvertViaJson(output, savedNote)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}
	return output, nil
}

func (m *NoteModel) DeleteNote(ctx context.Context, id int) error {
	op := "NoteModel.DeleteNote"
	userID := ctxdata.GetUidFromCtx(ctx)

	_, err := m.DbClient.Note.Get(ctx, id)
	if err != nil {
		if ent.IsNotFound(err) {
			return berr.ErrNotFound.Wrap(err).Op(op)
		}
		return berr.ErrDB.Wrap(err).Op(op)
	}

	err = m.DbClient.Note.DeleteOneID(id).Exec(ctx)
	if err != nil {
		return berr.ErrDeleteFailed.Wrap(err).Op(op)
	}

	m.Logger.WithContext(ctx).Infof("%s - Note %d deleted by user %d", op, id, userID)
	return nil
}
