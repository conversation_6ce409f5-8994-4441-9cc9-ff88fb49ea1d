package model

import (
	"bcare/api/internal/dto"
	"bcare/api/internal/event"
	"bcare/common/berr"
	"bcare/common/bquery"
	"bcare/common/butils"
	"bcare/common/cast"
	"bcare/common/ctxdata"
	"bcare/common/eventbus"
	"bcare/ent"
	"bcare/ent/task"
	"bcare/ent/taskassignment"
	"context"
	"fmt"
	"time"

	"github.com/jinzhu/copier"

	"github.com/zeromicro/go-zero/core/logx"
)

type TaskModel struct {
	Logger              logx.Logger
	DbClient            *ent.Client
	ctx                 context.Context
	TaskAssignmentModel *TaskAssignmentModel
	TaskDepartmentModel *TaskDepartmentModel
	TaskRecurringModel  *TaskRecurringModel
	EB                  eventbus.Bus
}

func NewTaskModel(dbClient *ent.Client, assignmentModel *TaskAssignmentModel, departmentModel *TaskDepartmentModel, recurringModel *TaskRecurringModel, eb eventbus.Bus) *TaskModel {
	logger := logx.WithContext(context.Background()).WithFields(logx.Field("model", "TaskModel"))
	return &TaskModel{
		Logger:              logger,
		DbClient:            dbClient,
		ctx:                 context.Background(),
		TaskAssignmentModel: assignmentModel,
		TaskDepartmentModel: departmentModel,
		TaskRecurringModel:  recurringModel,
		EB:                  eb,
	}
}

func (m *TaskModel) Create(ctx context.Context, input *dto.CreateTaskInput) (*dto.TaskOutput, error) {
	op := "TaskModel.Create"
	userID := ctxdata.GetUidFromCtx(ctx)

	var taskRecord *ent.Task
	err := bquery.WithTx(ctx, m.DbClient, func(tx *ent.Tx) error {
		// Create main task
		taskRecord = new(ent.Task)
		err := cast.CopyInputToEnt(taskRecord, input)
		if err != nil {
			return berr.ErrCopyFailed.Wrap(err).Op(op)
		}

		taskRecord.CreatorID = userID

		createdTask, err := tx.Task.Create().
			SetTask(taskRecord).
			Save(ctx)

		if err != nil {
			return berr.ErrCreateFailed.Wrap(err).Op(op)
		}
		taskRecord = createdTask

		// Calculate dueAt
		dueAt := m.getDueAt(taskRecord, input)

		// Create recurring task if cron expression is provided
		if input.CronExpression != nil {
			recurringInput := &dto.CreateTaskRecurringInput{
				CronExpression: *input.CronExpression,
			}
			if _, err := m.TaskRecurringModel.CreateTaskRecurring(ctx, taskRecord, recurringInput); err != nil {
				return err
			}
		}

		// Create task assignments if any
		if len(input.Users) > 0 {
			if err := m.TaskAssignmentModel.CreateBulk(ctx, tx, taskRecord, input.Users, dueAt); err != nil {
				return err
			}
		}

		// Create department assignments if any
		if len(input.Departments) > 0 {
			if err := m.TaskDepartmentModel.CreateBulk(ctx, tx, taskRecord, input.Departments, dueAt); err != nil {
				return err
			}
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	m.Logger.WithContext(ctx).Infof("%s - Task %d created by user %d", op, taskRecord.ID, userID)

	output := new(dto.TaskOutput)
	err = cast.ConvertViaJson(output, taskRecord)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}
	return output, nil
}

func (m *TaskModel) Update(ctx context.Context, input *dto.UpdateTaskInput) (*dto.TaskOutput, error) {
	op := "TaskModel.Update"

	taskRecord := new(ent.Task)
	originTask := new(ent.Task)
	var err error
	err = bquery.WithTx(ctx, m.DbClient, func(tx *ent.Tx) error {
		// Get task with relations
		taskRecord, err = m.FetchTaskWithRelations(ctx, tx, input.ID)
		if err != nil {
			return err
		}

		// Update task fields

		_ = copier.Copy(&originTask, taskRecord)

		err = cast.InputToEnt(taskRecord, input)

		if err != nil {
			return berr.ErrCopyFailed.Wrap(err).Op(op)
		}

		// Validate dates
		if err := m.ValidateDates(taskRecord); err != nil {
			return berr.ErrValidationFailed.Wrap(err).Op(op)
		}

		// Calculate dueAt
		dueAt := m.getDueAt(taskRecord, input)

		// Update completion state if state has changed
		if originTask.State != taskRecord.State && input.State != nil && *input.State != "" {
			modified := m.UpdateCompletedState(originTask, taskRecord)
			if modified != "" {
				input.Modified = append(input.Modified, modified)
			}
			if err := m.TaskAssignmentModel.UpdateStateAndCompletedAt(ctx, tx, taskRecord); err != nil {
				return err
			}
			if err := m.TaskDepartmentModel.UpdateStateAndCompletedAt(ctx, tx, taskRecord); err != nil {
				return err
			}
		}

		// Update task
		taskRecord, err = tx.Task.UpdateOne(taskRecord).SetTask(taskRecord, input.Modified...).Save(ctx)
		if err != nil {
			return berr.ErrUpdateFailed.Wrap(err).Op(op)
		}

		// Update assignments if provided
		if len(input.Users) > 0 || butils.Contains(input.Modified, "users") {
			if err := m.TaskAssignmentModel.UpdateTaskAssignments(ctx, tx, taskRecord, input.Users, dueAt); err != nil {
				return err
			}
		}

		// Update department assignments if provided
		if len(input.Departments) > 0 || butils.Contains(input.Modified, "departments") {
			if err := m.TaskDepartmentModel.UpdateTaskDepartmentAssignments(ctx, tx, taskRecord, input.Departments, dueAt); err != nil {
				return err
			}
		}

		// Update due_at for all assignments if due date changed
		if taskRecord.DueDate != nil && !taskRecord.DueDate.Equal(*originTask.DueDate) {
			if err := m.TaskAssignmentModel.UpdateAllTaskAssignmentsDueAt(ctx, tx, taskRecord, dueAt); err != nil {
				return err
			}
			if err := m.TaskDepartmentModel.UpdateAllTaskAssignmentsDueAt(ctx, tx, taskRecord, dueAt); err != nil {
				return err
			}
		}

		// Update recurring task if applicable
		if taskRecord.Edges.Recurring != nil && taskRecord.CurrentSerial == 1 {

			if _, err := m.TaskRecurringModel.UpdateTaskRecurring(ctx, taskRecord.Edges.Recurring.ID, taskRecord, dueAt); err != nil {
				return err
			}
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	output := new(dto.TaskOutput)
	err = cast.ConvertViaJson(output, taskRecord)

	originOutput := new(dto.TaskOutput)
	_ = cast.ConvertViaJson(originOutput, originTask)
	dispatchCtx := context.Background()

	userID := ctxdata.GetUidFromCtx(ctx)
	eventToDispatch := event.TaskAfterUpdateEvent(originOutput, output, userID)

	if eventToDispatch != nil {
		m.EB.Dispatch(dispatchCtx, eventToDispatch)
	}

	m.Logger.WithContext(ctx).Infof("%s - Task %d updated by user %d", op, output.ID, userID)
	return output, nil
}

func (m *TaskModel) Get(ctx context.Context, id int) (*dto.TaskOutput, error) {
	op := "TaskModel.Get"

	taskRecord, err := m.DbClient.Task.Query().
		WithPerson().
		WithCreator().
		WithRecurring().
		WithAssignments(func(q *ent.TaskAssignmentQuery) {
			q.WithUser()
		}).
		WithDepartmentAssignments().
		Where(task.ID(id)).
		First(ctx)

	if err != nil {
		if ent.IsNotFound(err) {
			return nil, berr.ErrNotFound.Wrap(err).Op(op)
		}
		return nil, berr.ErrDB.Wrap(err).Op(op)
	}
	output := new(dto.TaskOutput)
	err = cast.ConvertViaJson(output, taskRecord)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	return output, nil
}

func (m *TaskModel) List(ctx context.Context, input dto.ListTaskInput) (*dto.ListTaskOutput, error) {
	op := "TaskModel.List"

	limit, offset := bquery.Paging(input.PageSize, input.Page)
	order := bquery.Ordering(input.OrderBy)

	query := m.DbClient.Task.Query().
		WithRecurring().
		WithAssignments().
		WithDepartmentAssignments().
		Order(order)

	// Apply filters
	if len(input.Filter.IDs) > 0 {
		query = query.Where(task.IDIn(input.Filter.IDs...))
	}
	if input.Filter.Title != nil {
		query = query.Where(task.TitleContains(*input.Filter.Title))
	}
	if input.Filter.Type != nil {
		query = query.Where(task.Type(*input.Filter.Type))
	}
	if input.Filter.Priority != nil {
		query = query.Where(task.Priority(*input.Filter.Priority))
	}
	if input.Filter.ParentID != nil {
		query = query.Where(task.ParentID(*input.Filter.ParentID))
	}
	if input.Filter.PersonID != nil {
		query = query.Where(task.PersonID(*input.Filter.PersonID))
	}
	if input.Filter.DealID != nil {
		query = query.Where(task.DealID(*input.Filter.DealID))
	}
	if input.Filter.AppointmentID != nil {
		query = query.Where(task.AppointmentID(*input.Filter.AppointmentID))
	}
	if input.Filter.DepartmentID != nil {
		query = query.Where(task.DepartmentID(*input.Filter.DepartmentID))
	}
	if input.Filter.CreatorID != nil {
		query = query.Where(task.CreatorID(*input.Filter.CreatorID))
	}
	if input.Filter.State != nil {
		query = query.Where(task.StateEQ(task.State(*input.Filter.State)))
	}

	// Get total count
	total, err := query.Clone().Count(ctx)
	if err != nil {
		return nil, berr.ErrListFailed.Wrap(err).Op(op)
	}

	output := &dto.ListTaskOutput{
		Tasks:     []dto.TaskOutput{},
		Total:     total,
		TotalPage: (total + input.PageSize - 1) / input.PageSize,
	}

	if total == 0 {
		return output, nil
	}

	tasks, err := query.Limit(limit).Offset(offset).All(ctx)
	if err != nil {
		return nil, berr.ErrListFailed.Wrap(err).Op(op)
	}

	err = cast.ConvertViaJson(&output.Tasks, tasks)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	return output, nil
}

func (m *TaskModel) Delete(ctx context.Context, id int) error {
	op := "TaskModel.Delete"

	_, err := m.DbClient.Task.Get(ctx, id)
	if err != nil {
		if ent.IsNotFound(err) {
			return berr.ErrNotFound.Wrap(err).Op(op)
		}
		return berr.ErrDB.Wrap(err).Op(op)
	}

	err = m.DbClient.Task.DeleteOneID(id).Exec(ctx)
	if err != nil {
		return berr.ErrDeleteFailed.Wrap(err).Op(op)
	}

	m.Logger.WithContext(ctx).Infof("%s - Task %d deleted", op, id)
	return nil
}

func (m *TaskModel) BulkDelete(ctx context.Context, ids []int) error {
	op := "TaskModel.BulkDelete"
	// Convert []int64 to []int
	intIds := make([]int, len(ids))
	for i, id := range ids {
		intIds[i] = int(id)
	}
	_, err := m.DbClient.Task.Delete().Where(task.IDIn(intIds...)).Exec(ctx)
	if err != nil {
		return berr.ErrDeleteFailed.Wrap(err).Op(op)
	}

	m.Logger.WithContext(ctx).Infof("%s - Tasks %v deleted", op, ids)
	return nil
}

func (m *TaskModel) BulkUpdate(ctx context.Context, ids []int, input *dto.UpdateTaskInput) (*dto.BulkUpdateResult, error) {
	op := "TaskModel.BulkUpdate"

	result := &dto.BulkUpdateResult{
		SuccessCount: 0,
		FailCount:    0,
		Errors:       make([]dto.TaskUpdateError, 0, len(ids)),
	}

	for _, id := range ids {
		updateInput := *input
		updateInput.ID = id

		_, err := m.Update(ctx, &updateInput)
		if err != nil {
			result.FailCount++
			result.Errors = append(result.Errors, dto.TaskUpdateError{
				TaskID: id,
				Error:  err.Error(),
			})
			continue
		}

		result.SuccessCount++
	}

	if result.SuccessCount == 0 {
		return nil, berr.ErrUpdateFailed.Wrap(nil).Op(op)
	}

	return result, nil
}

func (m *TaskModel) UpdateState(ctx context.Context, input *dto.UpdateTaskInput) error {
	op := "TaskModel.UpdateState"

	updateInput := *input
	updateInput.Modified = []string{"state", "completed_at"}

	_, err := m.Update(ctx, &updateInput)
	if err != nil {
		return berr.ErrUpdateFailed.Wrap(err).Op(op)
	}

	return nil
}

func (m *TaskModel) CountActiveTasksForPersonExcludingCurrent(ctx context.Context, personID int, currentTaskID int) (int, error) {
	op := "TaskModel.CountActiveTasksForPersonExcludingCurrent"

	count, err := m.DbClient.Task.Query().
		Where(
			task.PersonID(personID),
			task.IDNEQ(currentTaskID),
			task.StateNEQ(task.StateCompleted),
			task.StateNEQ(task.StateCompletedEarly),
			task.StateNEQ(task.StateAwaitingApproval),
		).
		Count(ctx)

	if err != nil {
		return 0, berr.ErrDB.Wrap(err).Op(op)
	}
	return count, nil
}

// Private Functions

func (m *TaskModel) getDueAt(taskRecord *ent.Task, input interface{}) time.Time {
	if taskRecord.Edges.Recurring != nil {
		if taskRecord.CurrentSerial == 1 {
			return butils.CalculateNextRunTime(taskRecord.Edges.Recurring.CronExpression, taskRecord.StartDate, nil)
		} else {
			return *taskRecord.Edges.Recurring.NextOccurrence
		}
	}
	if taskRecord.DueDate != nil {
		return *taskRecord.DueDate
	}
	return taskRecord.StartDate
}

func (m *TaskModel) updateAllTaskAssignmentsDueAt(ctx context.Context, tx *ent.Tx, taskID int, currentSerial int, dueAt time.Time) error {
	_, err := tx.TaskAssignment.Update().
		Where(taskassignment.TaskID(taskID)).
		SetDueAt(dueAt).
		Save(ctx)
	return err
}

func (m *TaskModel) updateRecurringInfo(ctx context.Context, tx *ent.Tx, taskRecord *ent.Task, dueAt time.Time) error {
	if taskRecord.Edges.Recurring == nil {
		return nil
	}

	_, err := tx.TaskRecurring.UpdateOneID(taskRecord.Edges.Recurring.ID).
		SetNextOccurrence(dueAt).
		Save(ctx)
	return err
}

func (m *TaskModel) UpdateCompletedState(originTask *ent.Task, taskRecord *ent.Task) string {
	modified := ""
	if originTask.State != taskRecord.State && IsCompletionState(taskRecord.State) {
		now := time.Now()
		taskRecord.CompletedAt = &now
		modified = "completed_at"
	}
	if !IsCompletionState(taskRecord.State) {
		taskRecord.CompletedAt = nil
		modified = "completed_at"
	}
	return modified
}

func (m *TaskModel) FetchTaskWithRelations(ctx context.Context, tx *ent.Tx, taskID int) (*ent.Task, error) {
	taskRecord, err := tx.Task.Query().
		WithAssignments().
		WithRecurring().
		Where(task.ID(taskID)).
		Only(ctx)

	if err != nil {
		if ent.IsNotFound(err) {
			return nil, berr.ErrNotFound.Wrap(err).Op("TaskModel.FetchTaskWithRelations")
		}
		return nil, berr.ErrDB.Wrap(err).Op("TaskModel.FetchTaskWithRelations")
	}
	return taskRecord, nil
}

// ValidateDates validate start_date, end_date, due_date of task
func (m *TaskModel) ValidateDates(task *ent.Task) error {
	// Validate start_date < end_date
	if task.EndDate != nil {
		if task.StartDate.After(*task.EndDate) {
			return fmt.Errorf("start_date must be before end_date")
		}
	}

	// Validate due_date is within range
	if task.DueDate != nil {
		if task.DueDate.Before(task.StartDate) {
			return fmt.Errorf("due_date must be after start_date")
		}
		if task.EndDate != nil && task.DueDate.After(*task.EndDate) {
			return fmt.Errorf("due_date must be before end_date")
		}
	}

	return nil
}

// Helper Functions

func IsCompletionState(state task.State) bool {
	return state == task.StateAwaitingApproval ||
		state == task.StateCompleted ||
		state == task.StateCompletedEarly ||
		state == task.StateCancelledInProgress
}
