package model

import (
	"bcare/api/internal/dto"
	"bcare/common/berr"
	"bcare/common/cast"
	"bcare/common/ctxdata"
	"bcare/ent"
	"bcare/ent/installmentplan"
	"context"
	"time"

	"github.com/zeromicro/go-zero/core/logx"
)

type InstallmentPlanModel struct {
	Logger   logx.Logger
	DbClient *ent.Client
}

func NewInstallmentPlanModel(dbClient *ent.Client) *InstallmentPlanModel {
	logger := logx.WithContext(context.Background()).WithFields(logx.Field("model", "InstallmentPlanModel"))
	return &InstallmentPlanModel{
		Logger:   logger,
		DbClient: dbClient,
	}
}

func (m *InstallmentPlanModel) Create(ctx context.Context, input *dto.CreateInstallmentPlanInput) (*dto.InstallmentPlanOutput, error) {
	op := "InstallmentPlanModel.Create"
	userID := ctxdata.GetUidFromCtx(ctx)

	planRecord := new(ent.InstallmentPlan)
	err := cast.InputToEnt(planRecord, input)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	planRecord.UserID = userID

	planRecord, err = m.DbClient.InstallmentPlan.Create().
		SetInstallmentPlan(planRecord).
		Save(ctx)
	if err != nil {
		return nil, berr.ErrCreateFailed.Wrap(err).Op(op)
	}

	output := new(dto.InstallmentPlanOutput)
	err = cast.ConvertViaJson(output, planRecord)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	return output, nil
}

func (m *InstallmentPlanModel) Get(ctx context.Context, id int) (*dto.InstallmentPlanOutput, error) {
	op := "InstallmentPlanModel.Get"

	planRecord, err := m.DbClient.InstallmentPlan.Query().
		WithInstallments().
		Where(installmentplan.ID(id)).
		Only(ctx)
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, berr.ErrNotFound.Wrap(err).Op(op)
		}
		return nil, berr.ErrDB.Wrap(err).Op(op)
	}

	output := new(dto.InstallmentPlanOutput)
	err = cast.ConvertViaJson(output, planRecord)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	return output, nil
}

func (m *InstallmentPlanModel) GetInstallmentPlansForDeal(ctx context.Context, tx *ent.Tx, dealID int) ([]*ent.InstallmentPlan, error) {
	op := "InstallmentPlanModel.GetInstallmentPlansForDeal"

	client := m.getClient(tx)
	installmentPlans, err := client.InstallmentPlan.Query().
		Where(
			installmentplan.DealID(dealID),
			installmentplan.DeletedAtIsNil(),
		).
		All(ctx)
	if err != nil {
		return nil, berr.ErrDB.Wrap(err).Op(op)
	}

	return installmentPlans, nil
}

func (m *InstallmentPlanModel) Update(ctx context.Context, input *dto.UpdateInstallmentPlanInput) (*dto.InstallmentPlanOutput, error) {
	op := "InstallmentPlanModel.Update"
	userID := ctxdata.GetUidFromCtx(ctx)

	planRecord, err := m.DbClient.InstallmentPlan.Get(ctx, input.ID)
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, berr.ErrNotFound.Wrap(err).Op(op)
		}
		return nil, berr.ErrDB.Wrap(err).Op(op)
	}

	err = cast.InputToEnt(planRecord, input)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	planRecord.UserID = userID

	planRecord, err = m.DbClient.InstallmentPlan.UpdateOneID(input.ID).
		SetInstallmentPlan(planRecord).
		Save(ctx)
	if err != nil {
		return nil, berr.ErrUpdateFailed.Wrap(err).Op(op)
	}

	output := new(dto.InstallmentPlanOutput)
	err = cast.ConvertViaJson(output, planRecord)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	return output, nil
}

func (m *InstallmentPlanModel) Delete(ctx context.Context, id int) error {
	op := "InstallmentPlanModel.Delete"
	userID := ctxdata.GetUidFromCtx(ctx)

	_, err := m.DbClient.InstallmentPlan.Get(ctx, id)
	if err != nil {
		if ent.IsNotFound(err) {
			return berr.ErrNotFound.Wrap(err).Op(op)
		}
		return berr.ErrDB.Wrap(err).Op(op)
	}

	_, err = m.DbClient.InstallmentPlan.UpdateOneID(id).
		SetUserID(userID).
		SetDeletedAt(time.Now()).
		Save(ctx)
	if err != nil {
		return berr.ErrDeleteFailed.Wrap(err).Op(op)
	}

	return nil
}

func (m *InstallmentPlanModel) getClient(tx *ent.Tx) *ent.Client {
	if tx != nil {
		return tx.Client()
	}
	return m.DbClient
}
