package model

import (
	"bcare/api/internal/dto"
	"bcare/common/berr"
	"bcare/common/bquery"
	"bcare/common/butils"
	"bcare/common/cast"
	"bcare/common/ctxdata"
	"bcare/ent"
	"bcare/ent/task"
	"bcare/ent/taskassignment"
	"context"
	"fmt"
	"time"

	"github.com/zeromicro/go-zero/core/logx"
)

type TaskAssignmentModel struct {
	Logger   logx.Logger
	DbClient *ent.Client
}

func NewTaskAssignmentModel(dbClient *ent.Client) *TaskAssignmentModel {
	logger := logx.WithContext(context.Background()).WithFields(logx.Field("model", "TaskAssignmentModel"))
	return &TaskAssignmentModel{
		Logger:   logger,
		DbClient: dbClient,
	}
}

func (m *TaskAssignmentModel) Create(ctx context.Context, input *dto.CreateTaskAssignmentInput) (*dto.TaskAssignmentOutput, error) {
	op := "TaskAssignmentModel.Create"
	userID := ctxdata.GetUidFromCtx(ctx)

	taskAssignmentRecord := new(ent.TaskAssignment)
	err := cast.CopyInputToEnt(taskAssignmentRecord, input)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	taskAssignmentRecord.UserID = userID

	createdTaskAssignment, err := m.DbClient.TaskAssignment.Create().
		SetTaskAssignment(taskAssignmentRecord).
		Save(ctx)

	if err != nil {
		return nil, berr.ErrCreateFailed.Wrap(err).Op(op)
	}

	m.Logger.WithContext(ctx).Infof("%s - TaskAssignment %d created by user %d", op, createdTaskAssignment.ID, userID)

	output := new(dto.TaskAssignmentOutput)
	err = cast.ConvertViaJson(output, createdTaskAssignment)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}
	return output, nil
}

func (m *TaskAssignmentModel) Get(ctx context.Context, id int) (*dto.TaskAssignmentOutput, error) {
	op := "TaskAssignmentModel.Get"

	taskAssignmentRecord, err := m.DbClient.TaskAssignment.Query().
		Where(taskassignment.ID(id)).
		First(ctx)

	if err != nil {
		if ent.IsNotFound(err) {
			return nil, berr.ErrNotFound.Wrap(err).Op(op)
		}
		return nil, berr.ErrDB.Wrap(err).Op(op)
	}

	output := new(dto.TaskAssignmentOutput)
	err = cast.ConvertViaJson(output, taskAssignmentRecord)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	return output, nil
}

func (m *TaskAssignmentModel) List(ctx context.Context, input dto.ListTaskAssignmentInput) (*dto.ListTaskAssignmentOutput, error) {
	op := "TaskAssignmentModel.List"

	limit, offset := bquery.Paging(input.PageSize, input.Page)
	order := bquery.Ordering(input.OrderBy)

	query := m.DbClient.TaskAssignment.Query().Order(order)

	if input.Filter.TaskID != 0 {
		query = query.Where(taskassignment.TaskID(input.Filter.TaskID))
	}
	if input.Filter.UserID != 0 {
		query = query.Where(taskassignment.UserID(input.Filter.UserID))
	}
	if input.Filter.Role != "" {
		query = query.Where(taskassignment.RoleEQ(taskassignment.Role(input.Filter.Role)))
	}

	total, err := query.Clone().Count(ctx)
	if err != nil {
		return nil, berr.ErrListFailed.Wrap(err).Op(op)
	}

	output := &dto.ListTaskAssignmentOutput{
		TaskAssignments: []dto.TaskAssignmentOutput{},
		Total:           total,
		TotalPage:       (total + input.PageSize - 1) / input.PageSize,
	}

	if total == 0 {
		return output, nil
	}

	taskAssignments, err := query.Limit(limit).Offset(offset).All(ctx)
	if err != nil {
		return nil, berr.ErrListFailed.Wrap(err).Op(op)
	}

	err = cast.ConvertViaJson(&output.TaskAssignments, taskAssignments)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	return output, nil
}

func (m *TaskAssignmentModel) Update(ctx context.Context, input *dto.UpdateTaskAssignmentInput) (*dto.TaskAssignmentOutput, error) {
	op := "TaskAssignmentModel.Update"
	userID := ctxdata.GetUidFromCtx(ctx)

	taskAssignmentRecord := new(ent.TaskAssignment)
	err := cast.CopyInputToEnt(taskAssignmentRecord, input)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	updatedTaskAssignment, err := m.DbClient.TaskAssignment.UpdateOneID(input.ID).
		SetTaskAssignment(taskAssignmentRecord).
		Save(ctx)

	if err != nil {
		if ent.IsNotFound(err) {
			return nil, berr.ErrNotFound.Wrap(err).Op(op)
		}
		return nil, berr.ErrUpdateFailed.Wrap(err).Op(op)
	}

	m.Logger.WithContext(ctx).Infof("%s - TaskAssignment %d updated by user %d", op, updatedTaskAssignment.ID, userID)

	output := new(dto.TaskAssignmentOutput)
	err = cast.ConvertViaJson(output, updatedTaskAssignment)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	return output, nil
}

func (m *TaskAssignmentModel) Delete(ctx context.Context, id int) (*dto.TaskAssignmentOutput, error) {
	op := "TaskAssignmentModel.Delete"
	userID := ctxdata.GetUidFromCtx(ctx)

	taskAssignmentRecord, err := m.DbClient.TaskAssignment.Get(ctx, id)
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, berr.ErrNotFound.Wrap(err).Op(op)
		}
		return nil, berr.ErrDB.Wrap(err).Op(op)
	}

	err = m.DbClient.TaskAssignment.DeleteOne(taskAssignmentRecord).Exec(ctx)
	if err != nil {
		return nil, berr.ErrDeleteFailed.Wrap(err).Op(op)
	}

	m.Logger.WithContext(ctx).Infof("%s - TaskAssignment %d deleted by user %d", op, taskAssignmentRecord.ID, userID)

	output := new(dto.TaskAssignmentOutput)
	err = cast.ConvertViaJson(output, taskAssignmentRecord)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	return output, nil
}

func (m *TaskAssignmentModel) AssignTasks(ctx context.Context, input *dto.AssignTasksInput) (*dto.AssignTasksOutput, error) {
	op := "TaskAssignmentModel.AssignTasks"
	userID := ctxdata.GetUidFromCtx(ctx)

	output := &dto.AssignTasksOutput{
		SuccessCount: 0,
		FailCount:    0,
		Errors:       make([]dto.TaskUpdateError, 0, len(input.IDList)),
	}

	// Process each task update in parallel using goroutines
	type updateResult struct {
		taskID int
		err    error
	}

	resultChan := make(chan updateResult, len(input.IDList))

	// Process tasks in batch to avoid overwhelming the system
	const batchSize = 100
	for i := 0; i < len(input.IDList); i += batchSize {
		end := i + batchSize
		if end > len(input.IDList) {
			end = len(input.IDList)
		}

		// Process batch of tasks
		for _, taskID := range input.IDList[i:end] {
			go func(id int) {
				err := m.processAssignment(ctx, id, input)
				resultChan <- updateResult{taskID: id, err: err}
			}(taskID)
		}
	}

	// Collect results
	for i := 0; i < len(input.IDList); i++ {
		res := <-resultChan
		if res.err != nil {
			output.Errors = append(output.Errors, dto.TaskUpdateError{
				TaskID: res.taskID,
				Error:  res.err.Error(),
			})
			output.FailCount++
		} else {
			output.SuccessCount++
		}
	}

	if output.SuccessCount == 0 {
		return nil, berr.ErrUpdateFailed.Wrap(nil).Op(op)
	}

	m.Logger.WithContext(ctx).Infof("%s - Assigned tasks by user %d: %d success, %d failed", op, userID, output.SuccessCount, output.FailCount)

	return output, nil
}

func (m *TaskAssignmentModel) processAssignment(ctx context.Context, taskID int, input *dto.AssignTasksInput) error {
	op := "TaskAssignmentModel.processAssignment"

	return bquery.WithTx(ctx, m.DbClient, func(tx *ent.Tx) error {
		// Fetch task with related data
		taskRecord, err := tx.Task.Query().WithRecurring().Where(task.ID(taskID)).First(ctx)
		if err != nil {
			return berr.ErrNotFound.Wrap(err).Op(op)
		}

		dueAt := time.Now()
		if taskRecord.Edges.Recurring != nil {
			recurring := taskRecord.Edges.Recurring
			if recurring.LastOccurrence != nil {
				dueAt = butils.CalculateNextRunTime(recurring.CronExpression, taskRecord.StartDate.Truncate(24*time.Hour), nil)
			} else {
				dueAt = *recurring.NextOccurrence
			}
		} else {
			dueAt = *taskRecord.DueDate
		}

		if len(input.Users) > 0 {
			// Separate users by role
			primaryUsers, contributorUsers, reviewerUsers := separateUsersByRole(input.Users)

			// Handle primary assignments if there are new primary users
			if len(primaryUsers) > 0 {
				if err := replaceRoleAssignments(ctx, tx, taskRecord, primaryUsers, "primary", dueAt); err != nil {
					return berr.ErrUpdateFailed.Wrap(err).Op(op)
				}
			}

			// Handle contributor assignments if there are new contributor users
			if len(contributorUsers) > 0 {
				if err := replaceRoleAssignments(ctx, tx, taskRecord, contributorUsers, "contributor", dueAt); err != nil {
					return berr.ErrUpdateFailed.Wrap(err).Op(op)
				}
			}

			// Handle reviewer assignments if there are new reviewer users
			if len(reviewerUsers) > 0 {
				if err := replaceRoleAssignments(ctx, tx, taskRecord, reviewerUsers, "reviewer", dueAt); err != nil {
					return berr.ErrUpdateFailed.Wrap(err).Op(op)
				}
			}
		}

		return nil
	})
}

func separateUsersByRole(users []dto.CreateTaskAssignmentInput) (primary, contributor, reviewer []dto.CreateTaskAssignmentInput) {
	for _, user := range users {
		switch user.Role {
		case "primary":
			primary = append(primary, user)
		case "contributor":
			contributor = append(contributor, user)
		case "reviewer":
			reviewer = append(reviewer, user)
		}
	}
	return primary, contributor, reviewer
}

func replaceRoleAssignments(ctx context.Context, tx *ent.Tx, taskRecord *ent.Task, newUsers []dto.CreateTaskAssignmentInput, role string, dueAt time.Time) error {
	// Delete all existing assignments for this role
	_, err := tx.TaskAssignment.Delete().
		Where(
			taskassignment.And(
				taskassignment.TaskID(taskRecord.ID),
				taskassignment.Serial(taskRecord.CurrentSerial),
				taskassignment.RoleEQ(taskassignment.Role(role)),
			),
		).Exec(ctx)
	if err != nil {
		return fmt.Errorf("failed to delete existing %s assignments: %w", role, err)
	}

	// Create new assignments in bulk
	bulkCreate := make([]*ent.TaskAssignmentCreate, len(newUsers))
	for i, newUser := range newUsers {
		taskAssignRecord := &ent.TaskAssignment{
			TaskID: taskRecord.ID,
			UserID: newUser.UserID,
			Role:   taskassignment.Role(role),
			DueAt:  dueAt,
			Serial: taskRecord.CurrentSerial,
		}

		bulkCreate[i] = tx.TaskAssignment.Create().
			SetTaskAssignment(taskAssignRecord)
	}

	// Execute bulk create
	if len(bulkCreate) > 0 {
		err = tx.TaskAssignment.CreateBulk(bulkCreate...).Exec(ctx)
		if err != nil {
			return fmt.Errorf("failed to create new %s assignments: %w", role, err)
		}
	}

	return nil
}

func (m *TaskAssignmentModel) BulkDelete(ctx context.Context, taskId int64) error {
	op := "TaskAssignmentModel.BulkDelete"
	_, err := m.DbClient.TaskAssignment.Delete().Where(taskassignment.TaskID(int(taskId))).Exec(ctx)
	if err != nil {
		return berr.ErrDeleteFailed.Wrap(err).Op(op)
	}

	m.Logger.WithContext(ctx).Infof("%s - TaskAssignments for task %d deleted", op, taskId)
	return nil
}

func (m *TaskAssignmentModel) CreateBulk(ctx context.Context, tx *ent.Tx, taskRecord *ent.Task, assignments []dto.CreateTaskAssignmentInput, dueAt time.Time) error {
	op := "TaskAssignmentModel.CreateBulk"
	bulkTaskAssigns := make([]*ent.TaskAssignmentCreate, len(assignments))
	for i, a := range assignments {
		taskAssignRecord := new(ent.TaskAssignment)
		if err := cast.CopyInputToEnt(taskAssignRecord, a); err != nil {
			return berr.ErrCopyFailed.Wrap(err).Op(op)
		}

		SetTaskAssignmentCommonFields(taskAssignRecord, taskRecord, dueAt)
		bulkTaskAssigns[i] = tx.TaskAssignment.Create().SetTaskAssignment(taskAssignRecord)
	}

	_, err := tx.TaskAssignment.CreateBulk(bulkTaskAssigns...).Save(ctx)
	if err != nil {
		return berr.ErrCreateFailed.Wrap(err).Op(op)
	}

	m.Logger.WithContext(ctx).Infof("%s - Created %d task assignments for task %d", op, len(assignments), taskRecord.ID)
	return nil
}

func (m *TaskAssignmentModel) UpdateBulk(ctx context.Context, taskRecord *ent.Task, assignments []dto.CreateTaskAssignmentInput, dueAt time.Time) error {
	op := "TaskAssignmentModel.UpdateBulk"

	return bquery.WithTx(ctx, m.DbClient, func(tx *ent.Tx) error {
		// Delete existing assignments
		_, err := tx.TaskAssignment.Delete().Where(taskassignment.TaskID(int(taskRecord.ID))).Exec(ctx)
		if err != nil {
			return berr.ErrDeleteFailed.Wrap(err).Op(op)
		}

		// Create new assignments
		return m.CreateBulk(ctx, tx, taskRecord, assignments, dueAt)
	})
}

func (m *TaskAssignmentModel) ListByTaskID(ctx context.Context, taskID int64) ([]*ent.TaskAssignment, error) {
	op := "TaskAssignmentModel.ListByTaskID"
	assignments, err := m.DbClient.TaskAssignment.Query().
		Where(taskassignment.TaskID(int(taskID))).
		All(ctx)

	if err != nil {
		return nil, berr.ErrDB.Wrap(err).Op(op)
	}

	return assignments, nil
}

func (m *TaskAssignmentModel) UpdateDueAt(ctx context.Context, taskID int64, currentSerial int, dueAt time.Time) error {
	op := "TaskAssignmentModel.UpdateDueAt"
	_, err := m.DbClient.TaskAssignment.Update().
		Where(taskassignment.TaskID(int(taskID)), taskassignment.Serial(currentSerial)).
		SetDueAt(dueAt).
		Save(ctx)

	if err != nil {
		return berr.ErrUpdateFailed.Wrap(err).Op(op)
	}

	m.Logger.WithContext(ctx).Infof("%s - Updated due_at for task %d assignments", op, taskID)
	return nil
}

func (m *TaskAssignmentModel) UpdateTaskAssignments(ctx context.Context, tx *ent.Tx, taskRecord *ent.Task, assignments []dto.CreateTaskAssignmentInput, dueAt time.Time) error {
	op := "TaskAssignmentModel.UpdateTaskAssignments"

	// Get existing assignments
	existingAssignments, err := tx.TaskAssignment.Query().
		Where(taskassignment.TaskID(taskRecord.ID)).
		All(ctx)
	if err != nil {
		return berr.ErrDB.Wrap(err).Op(op)
	}

	// Create a map of existing assignments with composite key (userId_role)
	existingMap := make(map[string]*ent.TaskAssignment)
	for _, assignment := range existingAssignments {
		key := fmt.Sprintf("%d_%s", assignment.UserID, assignment.Role)
		existingMap[key] = assignment
	}

	// Create a map of new assignments
	newAssignments := make(map[string]dto.CreateTaskAssignmentInput)
	for _, assignment := range assignments {
		key := fmt.Sprintf("%d_%s", assignment.UserID, assignment.Role)
		newAssignments[key] = assignment
	}

	// Update or create assignments
	bulkAssigns := make([]*ent.TaskAssignmentCreate, 0)
	for key, newAssignment := range newAssignments {
		if existingAssignment, exists := existingMap[key]; exists {
			// Update existing assignment
			_, err := tx.TaskAssignment.UpdateOne(existingAssignment).
				SetDueAt(dueAt).
				SetStatus(taskRecord.Status).
				Save(ctx)
			if err != nil {
				return berr.ErrUpdateFailed.Wrap(err).Op(op)
			}
			delete(existingMap, key)
		} else {
			// Create new assignment
			assignmentRecord := new(ent.TaskAssignment)
			assignmentRecord.TaskID = taskRecord.ID
			assignmentRecord.UserID = newAssignment.UserID
			assignmentRecord.Role = taskassignment.Role(newAssignment.Role)
			assignmentRecord.DueAt = dueAt
			assignmentRecord.Status = taskRecord.Status
			assignmentRecord.Serial = taskRecord.CurrentSerial
			assignmentRecord.State = taskassignment.State(taskRecord.State)
			if taskRecord.CompletedAt != nil {
				assignmentRecord.CompletedAt = taskRecord.CompletedAt
			}

			bulkAssigns = append(bulkAssigns, tx.TaskAssignment.Create().SetTaskAssignment(assignmentRecord))
		}
	}

	// Create new assignments in bulk
	if len(bulkAssigns) > 0 {
		_, err = tx.TaskAssignment.CreateBulk(bulkAssigns...).Save(ctx)
		if err != nil {
			return berr.ErrCreateFailed.Wrap(err).Op(op)
		}
	}

	// Delete assignments that are no longer needed
	for _, assignment := range existingMap {
		err := tx.TaskAssignment.DeleteOne(assignment).Exec(ctx)
		if err != nil {
			return berr.ErrDeleteFailed.Wrap(err).Op(op)
		}
	}

	m.Logger.WithContext(ctx).Infof("%s - Updated assignments for task %d", op, taskRecord.ID)
	return nil
}

func (m *TaskAssignmentModel) UpdateAllTaskAssignmentsDueAt(ctx context.Context, tx *ent.Tx, taskRecord *ent.Task, dueAt time.Time) error {
	op := "TaskAssignmentModel.UpdateAllTaskAssignmentsDueAt"

	// Update due_at for all assignments of the current serial
	_, err := tx.TaskAssignment.Update().
		Where(
			taskassignment.TaskID(taskRecord.ID),
			taskassignment.Serial(taskRecord.CurrentSerial),
		).
		SetDueAt(dueAt).
		Save(ctx)

	if err != nil {
		return berr.ErrUpdateFailed.Wrap(err).Op(op)
	}

	m.Logger.WithContext(ctx).Infof("%s - Updated due_at for all assignments of task %d", op, taskRecord.ID)
	return nil
}

func (m *TaskAssignmentModel) UpdateStateAndCompletedAt(ctx context.Context, tx *ent.Tx, taskRecord *ent.Task) error {
	op := "TaskAssignmentModel.UpdateStateAndCompletedAt"

	// Get all assignments for the current serial
	assignments, err := tx.TaskAssignment.Query().
		Where(
			taskassignment.TaskID(taskRecord.ID),
			taskassignment.Serial(taskRecord.CurrentSerial),
		).
		All(ctx)
	if err != nil {
		return berr.ErrDB.Wrap(err).Op(op)
	}

	// Update state and completed_at for each assignment
	for _, assignment := range assignments {
		update := tx.TaskAssignment.UpdateOne(assignment).
			SetState(taskassignment.State(taskRecord.State))

		if IsCompletionState(taskRecord.State) {
			now := time.Now()
			update.SetCompletedAt(now)
		} else {
			update.ClearCompletedAt()
		}

		_, err := update.Save(ctx)
		if err != nil {
			return berr.ErrUpdateFailed.Wrap(err).Op(op)
		}
	}

	m.Logger.WithContext(ctx).Infof("%s - Updated state and completed_at for task %d assignments", op, taskRecord.ID)
	return nil
}
