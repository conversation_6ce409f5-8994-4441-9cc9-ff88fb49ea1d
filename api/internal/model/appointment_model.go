package model

import (
	"bcare/api/internal/dto"
	"bcare/api/internal/types"
	"bcare/common/berr"
	"bcare/common/bquery"
	"bcare/common/cast"
	"bcare/common/ctxdata"
	"bcare/ent"
	"bcare/ent/appointment"
	"bcare/ent/schedule"
	"context"
	"time"

	"github.com/zeromicro/go-zero/core/logx"
)

type AppointmentModel struct {
	Logger   logx.Logger
	DbClient *ent.Client
}

func NewAppointmentModel(dbClient *ent.Client) *AppointmentModel {
	logger := logx.WithContext(context.Background()).WithFields(logx.Field("model", "AppointmentModel"))
	return &AppointmentModel{
		Logger:   logger,
		DbClient: dbClient,
	}
}

func (m *AppointmentModel) CreateAppointment(ctx context.Context, input *dto.CreateAppointmentInput) (*dto.AppointmentOutput, error) {
	op := "AppointmentModel.CreateAppointment"
	userID := ctxdata.GetUidFromCtx(ctx)

	appointmentRecord := new(ent.Appointment)
	err := bquery.WithTx(ctx, m.DbClient, func(tx *ent.Tx) error {
		if input.DoctorID != nil {
			_, err := tx.Schedule.
				Query().
				Where(
					schedule.And(
						schedule.UserID(*input.DoctorID),
						schedule.StartTimeLTE(input.StartTime),
						schedule.EndTimeGTE(input.EndTime),
					),
				).
				First(ctx)

			if err != nil {
				return berr.ErrNotFound.Wrap(err).Op(op)
			}

			err = m.checkAppointmentConflict(ctx, tx, *input.DoctorID, input.StartTime, input.EndTime, nil)
			if err != nil {
				return err
			}
		}

		err := cast.CopyInputToEnt(appointmentRecord, input)
		if err != nil {
			return berr.ErrCopyFailed.Wrap(err).Op(op)
		}

		appointmentRecord.CreatorID = &userID

		createdAppointment, err := tx.Appointment.Create().
			SetAppointment(appointmentRecord).
			Save(ctx)

		if err != nil {
			return berr.ErrCreateFailed.Wrap(err).Op(op)
		}

		appointmentRecord = createdAppointment
		return nil
	})

	if err != nil {
		return nil, err
	}

	output := new(dto.AppointmentOutput)
	err = cast.ConvertViaJson(output, appointmentRecord)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	m.Logger.WithContext(ctx).Infof("%s - Created appointment %d by user %d", op, appointmentRecord.ID, userID)
	return output, nil
}

func (m *AppointmentModel) GetAppointment(ctx context.Context, id int) (*dto.AppointmentOutput, error) {
	op := "AppointmentModel.GetAppointment"

	appointmentRecord, err := m.buildAppointmentQueryWithEdges().
		Where(appointment.ID(id)).
		Only(ctx)

	if err != nil {
		if ent.IsNotFound(err) {
			return nil, berr.ErrNotFound.Wrap(err, "appointment not found with ID: %d", id).Op(op)
		}
		return nil, berr.ErrServerCommon.Wrap(err, "failed to get appointment with ID: %d", id).Op(op)
	}

	output := new(dto.AppointmentOutput)
	err = cast.ConvertViaJson(output, appointmentRecord)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	return output, nil
}

func (m *AppointmentModel) ListAppointments(ctx context.Context, input dto.ListAppointmentInput) (*dto.ListAppointmentOutput, error) {
	op := "AppointmentModel.ListAppointments"

	limit, offset := bquery.Paging(input.PageSize, input.Page)
	order := bquery.Ordering(input.OrderBy)
	query := m.buildAppointmentQueryWithEdges().Order(order)

	if input.Filter.DoctorID != nil {
		query = query.Where(appointment.DoctorID(*input.Filter.DoctorID))
	}
	if input.Filter.PersonID != nil {
		query = query.Where(appointment.PersonID(*input.Filter.PersonID))
	}
	if input.Filter.Status != nil {
		query = query.Where(appointment.Status(int8(*input.Filter.Status)))
	}
	if input.Filter.Type != nil {
		query = query.Where(appointment.Type(int8(*input.Filter.Type)))
	}

	if input.FromDate != nil && input.ToDate != nil {
		if !input.FromDate.IsZero() && !input.ToDate.IsZero() {
			query = query.Where(
				appointment.StartTimeGTE(*input.FromDate),
				appointment.StartTimeLT(*input.ToDate),
			)
		}
	}

	if input.HasDoctor == "yes" {
		query = query.Where(appointment.DoctorIDNotNil())
	} else if input.HasDoctor == "no" {
		query = query.Where(appointment.DoctorIDIsNil())
	}

	total, err := query.Count(ctx)
	if err != nil {
		return nil, berr.ErrServerCommon.Wrap(err).Op(op)
	}

	appointments, err := query.
		Limit(limit).
		Offset(offset).
		All(ctx)

	if err != nil {
		return nil, berr.ErrListFailed.Wrap(err).Op(op)
	}

	output := &dto.ListAppointmentOutput{
		Appointments: make([]dto.AppointmentOutput, 0, len(appointments)),
		Total:        total,
	}

	for _, appointment := range appointments {
		appointmentOutput := new(dto.AppointmentOutput)
		err = cast.ConvertViaJson(appointmentOutput, appointment)
		if err != nil {
			return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
		}
		output.Appointments = append(output.Appointments, *appointmentOutput)
	}

	return output, nil
}

func (m *AppointmentModel) UpdateAppointment(ctx context.Context, input *dto.UpdateAppointmentInput) (*dto.AppointmentOutput, error) {
	op := "AppointmentModel.UpdateAppointment"

	appointmentRecord := new(ent.Appointment)
	err := bquery.WithTx(ctx, m.DbClient, func(tx *ent.Tx) error {
		existingAppointment, err := tx.Appointment.Query().
			WithDoctor().
			WithPerson().
			Where(appointment.ID(input.ID)).
			First(ctx)
		originAppointment := *existingAppointment

		if err != nil {
			if ent.IsNotFound(err) {
				return berr.ErrNotFound.Wrap(err).Op(op)
			}
			return berr.ErrServerCommon.Wrap(err).Op(op)
		}

		err = cast.CopyInputToEnt(appointmentRecord, input)
		if err != nil {
			return berr.ErrCopyFailed.Wrap(err).Op(op)
		}

		if appointmentRecord.DoctorID > 0 && (!originAppointment.StartTime.Equal(appointmentRecord.StartTime) || !originAppointment.EndTime.Equal(appointmentRecord.EndTime) || originAppointment.DoctorID != appointmentRecord.DoctorID) {
			err = m.checkAppointmentConflict(ctx, tx, appointmentRecord.DoctorID, appointmentRecord.StartTime, appointmentRecord.EndTime, &appointmentRecord.ID)
			if err != nil {
				return err
			}
		}

		updatedAppointment, err := tx.Appointment.UpdateOneID(input.ID).
			SetAppointment(appointmentRecord, input.Modified...).
			Save(ctx)

		if err != nil {
			return berr.ErrUpdateFailed.Wrap(err).Op(op)
		}

		// Load edges
		updatedAppointment.Edges.Doctor = existingAppointment.Edges.Doctor
		updatedAppointment.Edges.Person = existingAppointment.Edges.Person

		appointmentRecord = updatedAppointment
		return nil
	})

	if err != nil {
		return nil, err
	}

	output := new(dto.AppointmentOutput)
	err = cast.ConvertViaJson(output, appointmentRecord)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	m.Logger.WithContext(ctx).Infof("%s - Updated appointment %d", op, appointmentRecord.ID)
	return output, nil
}

func (m *AppointmentModel) DeleteAppointment(ctx context.Context, id int) error {
	op := "AppointmentModel.DeleteAppointment"

	err := bquery.WithTx(ctx, m.DbClient, func(tx *ent.Tx) error {
		appointmentRecord, err := tx.Appointment.Get(ctx, id)
		if err != nil {
			if ent.IsNotFound(err) {
				return berr.ErrNotFound.Wrap(err).Op(op)
			}
			return berr.ErrServerCommon.Wrap(err).Op(op)
		}

		err = tx.Appointment.DeleteOne(appointmentRecord).Exec(ctx)
		if err != nil {
			return berr.ErrDeleteFailed.Wrap(err).Op(op)
		}

		return nil
	})

	if err != nil {
		return err
	}

	m.Logger.WithContext(ctx).Infof("%s - Deleted appointment %d", op, id)
	return nil
}

func (m *AppointmentModel) GetLastedAppointment(ctx context.Context, personID int) (*dto.AppointmentOutput, error) {
	op := "AppointmentModel.GetLastedAppointment"

	appointmentRecord, err := m.DbClient.Appointment.Query().
		WithPerson().
		WithDoctor().
		Where(
			appointment.PersonID(personID),
			appointment.StartTimeGT(time.Now()),
			appointment.ArrivedAtIsNil(),
		).
		Order(appointment.ByStartTime()).
		First(ctx)

	if err != nil {
		if ent.IsNotFound(err) {
			return nil, nil
		}
		return nil, berr.ErrServerCommon.Wrap(err).Op(op)
	}

	output := new(dto.AppointmentOutput)
	err = cast.ConvertViaJson(output, appointmentRecord)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	return output, nil
}

// buildAppointmentQueryWithEdges builds query with common edges
func (m *AppointmentModel) buildAppointmentQueryWithEdges() *ent.AppointmentQuery {
	return m.DbClient.Appointment.Query().
		WithPerson().
		WithDoctor().
		WithCreator().
		WithTrack()
}

// checkAppointmentConflict kiểm tra xem có lịch hẹn nào đã tồn tại trong khoảng thời gian này không
func (m *AppointmentModel) checkAppointmentConflict(ctx context.Context, tx *ent.Tx, doctorID int, startTime, endTime time.Time, excludeID *int) error {
	op := "AppointmentModel.checkAppointmentConflict"

	query := tx.Appointment.
		Query().
		Where(
			appointment.And(
				appointment.StatusNEQ(types.AppointmentStatusDeActive),
				appointment.DoctorID(doctorID),
				appointment.And(
					appointment.StartTimeLT(endTime),
					appointment.EndTimeGT(startTime),
				),
			),
		)

	if excludeID != nil {
		query = query.Where(appointment.IDNEQ(*excludeID))
	}

	existingAppointments, err := query.First(ctx)

	if err != nil && !ent.IsNotFound(err) {
		return berr.ErrServerCommon.Wrap(err).Op(op)
	}

	if existingAppointments != nil {
		return berr.ErrDuplicateEntry.Wrap(nil).Op(op)
	}

	return nil
}
