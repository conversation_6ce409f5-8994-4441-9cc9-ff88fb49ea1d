package model

import (
	"bcare/api/internal/dto"
	"bcare/common/berr"
	"bcare/common/bquery"
	"bcare/ent"
	"bcare/ent/productoperation"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type ProductOperationModel struct {
	Logger   logx.Logger
	DbClient *ent.Client
}

func NewProductOperationModel(dbClient *ent.Client) *ProductOperationModel {
	logger := logx.WithContext(context.Background()).WithFields(logx.Field("model", "ProductOperationModel"))
	return &ProductOperationModel{
		Logger:   logger,
		DbClient: dbClient,
	}
}

func (m *ProductOperationModel) BulkSetProductOperations(ctx context.Context, input *dto.BulkSetProductOperationInput) error {
	op := "ProductOperationModel.BulkSetProductOperations"

	err := bquery.WithTx(ctx, m.DbClient, func(tx *ent.Tx) error {
		_, err := tx.ProductOperation.Delete().
			Where(productoperation.OperationIDEQ(input.OperationID)).
			Exec(ctx)
		if err != nil {
			return berr.ErrDeleteFailed.Wrap(err).Op(op)
		}

		if len(input.ProductIDs) > 0 {
			creators := make([]*ent.ProductOperationCreate, len(input.ProductIDs))
			for i, productID := range input.ProductIDs {
				creators[i] = tx.ProductOperation.Create().
					SetOperationID(input.OperationID).
					SetProductID(productID)
			}
			_, err = tx.ProductOperation.CreateBulk(creators...).Save(ctx)
			if err != nil {
				return berr.ErrCreateFailed.Wrap(err).Op(op)
			}
		}
		return nil
	})

	if err != nil {
		// Lỗi từ bquery.WithTx hoặc từ logic bên trong đã được wrap
		return err
	}

	m.Logger.WithContext(ctx).Infof("%s -OperationID %d (transaction committed)", op, input.OperationID)
	return nil
}
