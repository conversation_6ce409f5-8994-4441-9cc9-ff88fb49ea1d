package model

import (
	"bcare/api/internal/dto"
	"bcare/common/berr"
	"bcare/common/bquery"
	"bcare/common/cast"
	"bcare/common/ctxdata"
	"bcare/ent"
	"bcare/ent/discount"
	"bcare/ent/discountusage"
	"context"
	"encoding/json"

	"github.com/zeromicro/go-zero/core/logx"
)

type DiscountModel struct {
	Logger   logx.Logger
	DbClient *ent.Client
}

func NewDiscountModel(dbClient *ent.Client) *DiscountModel {
	logger := logx.WithContext(context.Background()).WithFields(logx.Field("model", "DiscountModel"))
	return &DiscountModel{
		Logger:   logger,
		DbClient: dbClient,
	}
}

// Helper function to get discounts by IDs
func (m *DiscountModel) getDiscountsByIDs(ctx context.Context, discountIDs []int, op string) ([]dto.DiscountOutput, error) {
	if len(discountIDs) == 0 {
		return nil, berr.ErrRequestParam.Wrap(nil).Op(op)
	}

	discountRecords, err := m.DbClient.Discount.Query().
		Where(discount.IDIn(discountIDs...)).
		All(ctx)
	if err != nil {
		return nil, berr.ErrDB.Wrap(err).Op(op)
	}

	var discountOutputs []dto.DiscountOutput
	err = cast.ConvertViaJson(&discountOutputs, discountRecords)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	return discountOutputs, nil
}

// Helper function to get all discounts and convert to outputs
func (m *DiscountModel) getAllDiscountsAsOutputs(ctx context.Context, op string) ([]dto.DiscountOutput, error) {
	discountRecords, err := m.DbClient.Discount.Query().All(ctx)
	if err != nil {
		return nil, berr.ErrDB.Wrap(err).Op(op)
	}

	var discountOutputs []dto.DiscountOutput
	for _, d := range discountRecords {
		var discountOutput dto.DiscountOutput
		err = cast.ConvertViaJson(&discountOutput, d)
		if err != nil {
			continue // Skip invalid records
		}
		discountOutputs = append(discountOutputs, discountOutput)
	}

	return discountOutputs, nil
}

func (m *DiscountModel) CreateDiscount(ctx context.Context, input *dto.CreateDiscountInput) (*dto.DiscountOutput, error) {
	op := "DiscountModel.CreateDiscount"
	userID := ctxdata.GetUidFromCtx(ctx)

	discountRecord := new(ent.Discount)

	err := cast.InputToEnt(discountRecord, input)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	if input.Meta != "" {
		var metaMap map[string]interface{}
		err = json.Unmarshal([]byte(input.Meta), &metaMap)
		if err != nil {
			return nil, berr.ErrRequestParam.Wrap(err).Op(op)
		}
		discountRecord.Meta = metaMap
	}

	createdDiscount, err := m.DbClient.Discount.Create().
		SetDiscount(discountRecord).
		Save(ctx)

	if err != nil {
		return nil, berr.ErrCreateFailed.Wrap(err).Op(op)
	}

	m.Logger.WithContext(ctx).Infof("%s - Discount %d created by user %d", op, createdDiscount.ID, userID)

	output := new(dto.DiscountOutput)
	err = cast.ConvertViaJson(output, createdDiscount)
	if err != nil {
		m.Logger.WithContext(ctx).Errorf("%s - Failed to convert created discount to output: %v", op, err)
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}
	return output, nil
}

func (m *DiscountModel) GetDiscount(ctx context.Context, id int) (*dto.DiscountOutput, error) {
	op := "DiscountModel.GetDiscount"

	discountRecord, err := m.DbClient.Discount.Get(ctx, id)
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, berr.ErrNotFound.Wrap(err).Op(op)
		}
		return nil, berr.ErrDB.Wrap(err).Op(op)
	}

	output := new(dto.DiscountOutput)
	err = cast.ConvertViaJson(output, discountRecord)
	if err != nil {
		m.Logger.WithContext(ctx).Errorf("%s - Failed to convert discount to output: %v", op, err)
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	return output, nil
}

func (m *DiscountModel) ListDiscounts(ctx context.Context, input *dto.ListDiscountInput) (*dto.ListDiscountOutput, error) {
	op := "DiscountModel.ListDiscounts"

	limit, offset := bquery.Paging(input.PageSize, input.Page)
	order := bquery.Ordering(input.OrderBy)

	query := m.DbClient.Discount.Query().Order(order)

	if input.Filter.Name != nil && *input.Filter.Name != "" {
		query = query.Where(discount.NameContains(*input.Filter.Name))
	}
	if input.Filter.Type != nil && *input.Filter.Type != "" {
		query = query.Where(discount.TypeEQ(*input.Filter.Type))
	}
	if input.Filter.Scope != nil && *input.Filter.Scope != "" {
		query = query.Where(discount.ScopeEQ(*input.Filter.Scope))
	}
	if input.Filter.UsageType != nil && *input.Filter.UsageType != "" {
		query = query.Where(discount.UsageTypeEQ(*input.Filter.UsageType))
	}
	if input.Filter.Status != nil {
		query = query.Where(discount.StatusEQ(int8(*input.Filter.Status)))
	}

	total, err := query.Clone().Count(ctx)
	if err != nil {
		return nil, berr.ErrListFailed.Wrap(err).Op(op)
	}

	totalPage := 1
	if input.PageSize > 0 {
		totalPage = (total + input.PageSize - 1) / input.PageSize
	}

	output := &dto.ListDiscountOutput{
		Discounts: []dto.DiscountOutput{},
		Total:     total,
		TotalPage: totalPage,
	}

	if total == 0 {
		return output, nil
	}

	discounts, err := query.Limit(limit).Offset(offset).All(ctx)
	if err != nil {
		return nil, berr.ErrListFailed.Wrap(err).Op(op)
	}

	err = cast.ConvertViaJson(&output.Discounts, discounts)
	if err != nil {
		m.Logger.WithContext(ctx).Errorf("%s - Failed to convert discounts to output: %v", op, err)
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	return output, nil
}

func (m *DiscountModel) UpdateDiscount(ctx context.Context, input *dto.UpdateDiscountInput) (*dto.DiscountOutput, error) {
	op := "DiscountModel.UpdateDiscount"

	discountRecord, err := m.DbClient.Discount.Get(ctx, input.ID)
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, berr.ErrNotFound.Wrap(err).Op(op)
		}
		return nil, berr.ErrDB.Wrap(err).Op(op)
	}

	err = cast.InputToEnt(discountRecord, input)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	if input.Meta != nil && *input.Meta != "" {
		var metaMap map[string]interface{}
		err = json.Unmarshal([]byte(*input.Meta), &metaMap)
		if err != nil {
			return nil, berr.ErrRequestParam.Wrap(err).Op(op)
		}
		discountRecord.Meta = metaMap
	}

	updatedDiscount, err := m.DbClient.Discount.UpdateOne(discountRecord).
		SetDiscount(discountRecord, input.Modified...).
		Save(ctx)

	if err != nil {
		return nil, berr.ErrUpdateFailed.Wrap(err).Op(op)
	}

	m.Logger.WithContext(ctx).Infof("%s - Discount %d updated", op, input.ID)

	output := new(dto.DiscountOutput)
	err = cast.ConvertViaJson(output, updatedDiscount)
	if err != nil {
		m.Logger.WithContext(ctx).Errorf("%s - Failed to convert updated discount to output: %v", op, err)
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	return output, nil
}

func (m *DiscountModel) DeleteDiscount(ctx context.Context, id int) error {
	op := "DiscountModel.DeleteDiscount"

	_, err := m.DbClient.Discount.Get(ctx, id)
	if err != nil {
		if ent.IsNotFound(err) {
			return berr.ErrNotFound.Wrap(err).Op(op)
		}
		return berr.ErrDB.Wrap(err).Op(op)
	}

	err = m.DbClient.Discount.DeleteOneID(id).Exec(ctx)
	if err != nil {
		return berr.ErrDeleteFailed.Wrap(err).Op(op)
	}

	m.Logger.WithContext(ctx).Infof("%s - Discount %d deleted", op, id)
	return nil
}

func (m *DiscountModel) CalculateDiscountForDeal(ctx context.Context, input *dto.CalculateDiscountInput, dealOutput *dto.DealOutput, personOutput *dto.PersonOutput) (*dto.CalculateDiscountOutput, error) {
	op := "DiscountModel.CalculateDiscountForDeal"

	if dealOutput == nil {
		return nil, berr.ErrRequestParam.Wrap(nil).Op(op)
	}

	discountOutputs, err := m.getDiscountsByIDs(ctx, input.DiscountIDs, op)
	if err != nil {
		return nil, err
	}

	result, err := CalculateDiscountsForDeal(discountOutputs, dealOutput, personOutput)
	if err != nil {
		return nil, berr.ErrServerCommon.Wrap(err).Op(op)
	}

	return result, nil
}

func (m *DiscountModel) CalculateDiscountForProduct(ctx context.Context, input *dto.CalculateDiscountAttachmentInput, productOutput *dto.ProductOutput) (*dto.CalculateDiscountOutput, error) {
	op := "DiscountModel.CalculateDiscountForProduct"

	if productOutput == nil {
		return nil, berr.ErrRequestParam.Wrap(nil).Op(op)
	}

	if input.Quantity <= 0 {
		return nil, berr.ErrRequestParam.Wrap(nil).Op(op)
	}

	discountOutputs, err := m.getDiscountsByIDs(ctx, input.DiscountIDs, op)
	if err != nil {
		return nil, err
	}

	result, err := CalculateDiscountsForProduct(discountOutputs, productOutput, nil, nil, input.Quantity)
	if err != nil {
		return nil, berr.ErrServerCommon.Wrap(err).Op(op)
	}

	return result, nil
}

func (m *DiscountModel) GetEligibleDiscounts(ctx context.Context, input *dto.EligibleDiscountInput, personOutput *dto.PersonOutput, dealOutput *dto.DealOutput, productOutputs []dto.ProductOutput) (*dto.EligibleDiscountsOutput, error) {
	op := "DiscountModel.GetEligibleDiscounts"

	if len(productOutputs) == 0 && dealOutput == nil {
		return nil, berr.ErrRequestParam.Wrap(nil).Op(op)
	}

	discountOutputs, err := m.getAllDiscountsAsOutputs(ctx, op)
	if err != nil {
		return nil, err
	}

	eligibleDiscounts := &dto.EligibleDiscountsOutput{
		EligibleDiscount: make([]dto.EligibleDiscountOutput, 0),
	}

	for _, discountOutput := range discountOutputs {
		switch discountOutput.Scope {
		case "deal":
			if dealOutput != nil {
				eligible := m.checkDiscountEligibilityForDealWithData(ctx, &discountOutput, dealOutput, personOutput)
				if eligible != nil {
					eligibleDiscounts.EligibleDiscount = append(eligibleDiscounts.EligibleDiscount, *eligible)
				}
			}
		case "product":
			for _, productOutput := range productOutputs {
				eligible := m.checkDiscountEligibilityForProductWithData(ctx, &discountOutput, &productOutput, dealOutput, personOutput)
				if eligible != nil {
					eligibleDiscounts.EligibleDiscount = append(eligibleDiscounts.EligibleDiscount, *eligible)
				}
			}
		}
	}

	return eligibleDiscounts, nil
}

func (m *DiscountModel) checkDiscountEligibilityForDealWithData(ctx context.Context, discount *dto.DiscountOutput, dealOutput *dto.DealOutput, personOutput *dto.PersonOutput) *dto.EligibleDiscountOutput {
	discountCalc := NewDiscountCalculation(*discount)
	shouldApply, discountAmount, err := discountCalc.ApplyToDeal(dealOutput, personOutput)
	if err != nil || !shouldApply {
		return nil
	}

	return &dto.EligibleDiscountOutput{
		Discount:       *discount,
		DiscountID:     discount.ID,
		DiscountAmount: discountAmount,
		EntityType:     "deal",
		EntityID:       dealOutput.ID,
	}
}

func (m *DiscountModel) checkDiscountEligibilityForProductWithData(ctx context.Context, discount *dto.DiscountOutput, productOutput *dto.ProductOutput, dealOutput *dto.DealOutput, personOutput *dto.PersonOutput) *dto.EligibleDiscountOutput {
	discountCalc := NewDiscountCalculation(*discount)
	shouldApply, discountAmount, err := discountCalc.ApplyToProduct(productOutput, dealOutput, personOutput, nil)
	if err != nil || !shouldApply {
		return nil
	}

	return &dto.EligibleDiscountOutput{
		Discount:       *discount,
		DiscountID:     discount.ID,
		DiscountAmount: discountAmount,
		EntityType:     "product",
		EntityID:       productOutput.ID,
	}
}

func (m *DiscountModel) GetDiscountUsagesByDiscount(ctx context.Context, discountID int) ([]*dto.DiscountUsageOutput, error) {
	op := "DiscountModel.GetDiscountUsagesByDiscount"

	usageRecords, err := m.DbClient.DiscountUsage.Query().
		Where(discountusage.DiscountID(discountID)).
		WithDiscount().
		All(ctx)
	if err != nil {
		return nil, berr.ErrDB.Wrap(err).Op(op)
	}

	var usageOutputs []*dto.DiscountUsageOutput
	for _, usage := range usageRecords {
		var usageOutput dto.DiscountUsageOutput
		err = cast.ConvertViaJson(&usageOutput, usage)
		if err != nil {
			continue
		}

		if usage.Edges.Discount != nil {
			err = cast.ConvertViaJson(&usageOutput.Discount, usage.Edges.Discount)
			if err != nil {
				continue
			}
		}

		usageOutputs = append(usageOutputs, &usageOutput)
	}

	return usageOutputs, nil
}
