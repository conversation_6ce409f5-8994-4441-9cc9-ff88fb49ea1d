package model

import (
	"bcare/api/internal/dto"
	"bcare/api/internal/types"
	"bcare/common/bconst"
	"bcare/common/berr"
	"bcare/common/bquery"
	"bcare/common/cast"
	"bcare/common/ctxdata"
	"bcare/ent"
	"bcare/ent/attachment"
	"bcare/ent/deal"
	"bcare/ent/installment"
	"bcare/ent/stage"
	"context"
	"math"

	"github.com/zeromicro/go-zero/core/logx"
)

type DealModel struct {
	Logger   logx.Logger
	DbClient *ent.Client
}

func NewDealModel(dbClient *ent.Client) *DealModel {
	logger := logx.WithContext(context.Background()).WithFields(logx.Field("model", "DealModel"))
	return &DealModel{
		Logger:   logger,
		DbClient: dbClient,
	}
}

func (m *DealModel) Get(ctx context.Context, input *dto.GetDealInput) (*dto.DealOutput, error) {
	op := "DealModel.Get"

	query := m.DbClient.Deal.Query()

	if input.IncludeRelation {
		query = query.WithPerson().WithAttachments()
	}

	dealRecord, err := query.Where(deal.ID(input.ID)).Only(ctx)
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, berr.ErrNotFound.Wrap(err).Op(op)
		}
		return nil, berr.ErrDB.Wrap(err).Op(op)
	}

	output := new(dto.DealOutput)
	if err := cast.ConvertViaJson(output, dealRecord); err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	return output, nil
}

func (m *DealModel) CreateDeal(ctx context.Context, input *dto.CreateDealInput) (*dto.DealOutput, error) {
	op := "DealModel.CreateDeal"
	userID := ctxdata.GetUidFromCtx(ctx)

	dealRecord := new(ent.Deal)
	err := cast.InputToEnt(dealRecord, input)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	createdDeal, err := m.DbClient.Deal.Create().
		SetDeal(dealRecord).
		Save(ctx)
	if err != nil {
		return nil, berr.ErrCreateFailed.Wrap(err).Op(op)
	}

	m.Logger.WithContext(ctx).Infof("%s - Deal %d created by user %d", op, createdDeal.ID, userID)

	output := new(dto.DealOutput)
	err = cast.ConvertViaJson(output, createdDeal)
	if err != nil {
		m.Logger.WithContext(ctx).Errorf("%s - Failed to convert deal output: %v", op, err)
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	return output, nil
}

func (m *DealModel) GetDealWithFullRelations(ctx context.Context, input *dto.GetDealInput) (*dto.DealOutput, error) {
	op := "DealModel.GetDealWithFullRelations"

	query := m.DbClient.Deal.Query()

	if input.IncludeRelation {
		query = query.
			WithPerson(func(pq *ent.PersonQuery) {
				pq.WithIssues()
			}).
			WithDealAssignment(func(daq *ent.DealUserQuery) {
				daq.WithUser().WithRatings()
			}).
			WithAttachments(func(aq *ent.AttachmentQuery) {
				aq.Where(attachment.KindEQ(attachment.KindProduct)).WithProduct()
			}).
			WithStage().
			WithPlans(func(pq *ent.InstallmentPlanQuery) {
				pq.WithInstallments()
			}).
			WithBills().
			WithTags()
	} else {
		query = query.WithStage()
	}

	dealRecord, err := query.Where(deal.ID(input.ID)).First(ctx)
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, berr.ErrNotFound.Wrap(err).Op(op)
		}
		return nil, berr.ErrDB.Wrap(err).Op(op)
	}

	output := new(dto.DealOutput)
	err = cast.ConvertViaJson(output, dealRecord)
	if err != nil {
		m.Logger.WithContext(ctx).Errorf("%s - Failed to convert deal output: %v", op, err)
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	return output, nil
}

func (m *DealModel) ListDeals(ctx context.Context, input dto.ListDealInput) (*dto.ListDealOutput, error) {
	op := "DealModel.ListDeals"

	limit, offset := bquery.Paging(input.PageSize, input.Page)
	order := bquery.Ordering(input.OrderBy)

	query := m.buildDealQuery(input)
	query = query.Limit(limit).Offset(offset).Order(order)

	output := &dto.ListDealOutput{
		Deals:     []dto.DealOutput{},
		Total:     0,
		TotalPage: 0,
	}

	if input.PageSize < 300 {
		total, err := query.Clone().Count(ctx)
		if err != nil {
			return nil, berr.ErrListFailed.Wrap(err).Op(op)
		}
		output.Total = total
		output.TotalPage = (total + limit - 1) / limit
	}

	deals, err := query.All(ctx)
	if err != nil {
		return nil, berr.ErrListFailed.Wrap(err).Op(op)
	}

	if len(deals) == 0 {
		return output, nil
	}

	err = cast.ConvertViaJson(&output.Deals, deals)
	if err != nil {
		m.Logger.WithContext(ctx).Errorf("%s - Failed to convert deals output: %v", op, err)
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	return output, nil
}

func (m *DealModel) UpdateDeal(ctx context.Context, input *dto.UpdateDealInput) (*dto.DealOutput, error) {
	op := "DealModel.UpdateDeal"
	userID := ctxdata.GetUidFromCtx(ctx)

	var dealRecord *ent.Deal
	var originDeal *ent.Deal

	err := bquery.WithTx(ctx, m.DbClient, func(tx *ent.Tx) error {
		var err error
		dealRecord, err = tx.Deal.Query().
			WithDealAssignment(func(q *ent.DealUserQuery) {
				q.WithUser()
			}).
			WithPerson(func(q *ent.PersonQuery) {
				q.WithIssues()
			}).
			WithStage().
			Where(deal.ID(input.ID)).
			Only(ctx)

		if err != nil {
			if ent.IsNotFound(err) {
				return berr.ErrNotFound.Wrap(err).Op(op)
			}
			return berr.ErrDB.Wrap(err).Op(op)
		}

		originDeal = new(ent.Deal)
		err = cast.ConvertViaJson(originDeal, dealRecord)
		if err != nil {
			return berr.ErrCopyFailed.Wrap(err).Op(op)
		}

		err = cast.InputToEnt(dealRecord, input)
		if err != nil {
			return berr.ErrCopyFailed.Wrap(err).Op(op)
		}

		dealRecord, err = tx.Deal.UpdateOneID(input.ID).
			SetDeal(dealRecord, input.Modified...).
			Save(ctx)
		if err != nil {
			return berr.ErrUpdateFailed.Wrap(err).Op(op)
		}

		dealRecord.Edges = originDeal.Edges
		return nil
	})

	if err != nil {
		return nil, err
	}

	m.Logger.WithContext(ctx).Infof("%s - Deal %d updated by user %d", op, input.ID, userID)

	output := new(dto.DealOutput)
	err = cast.ConvertViaJson(output, dealRecord)
	if err != nil {
		m.Logger.WithContext(ctx).Errorf("%s - Failed to convert deal output: %v", op, err)
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	return output, nil
}

func (m *DealModel) DeleteDeal(ctx context.Context, id int) error {
	op := "DealModel.DeleteDeal"
	userID := ctxdata.GetUidFromCtx(ctx)

	_, err := m.DbClient.Deal.Get(ctx, id)
	if err != nil {
		if ent.IsNotFound(err) {
			return berr.ErrNotFound.Wrap(err).Op(op)
		}
		return berr.ErrDB.Wrap(err).Op(op)
	}

	err = m.DbClient.Deal.DeleteOneID(id).Exec(ctx)
	if err != nil {
		return berr.ErrDeleteFailed.Wrap(err).Op(op)
	}

	m.Logger.WithContext(ctx).Infof("%s - Deal %d deleted by user %d", op, id, userID)
	return nil
}

func (m *DealModel) CheckoutDeal(ctx context.Context, id int) (*dto.DealOutput, error) {
	op := "DealModel.CheckoutDeal"
	userID := ctxdata.GetUidFromCtx(ctx)

	_, err := m.DbClient.Deal.Query().
		WithAttachments(func(q *ent.AttachmentQuery) {
			q.Where(attachment.StatusEQ(1))
		}).
		Where(deal.ID(id)).
		Only(ctx)
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, berr.ErrNotFound.Wrap(err).Op(op)
		}
		return nil, berr.ErrDB.Wrap(err).Op(op)
	}

	updatedDeal, err := m.DbClient.Deal.UpdateOneID(id).
		SetState(deal.StateActive).
		Save(ctx)
	if err != nil {
		return nil, berr.ErrUpdateFailed.Wrap(err).Op(op)
	}

	m.Logger.WithContext(ctx).Infof("%s - Deal %d checked out by user %d", op, id, userID)

	output := new(dto.DealOutput)
	err = cast.ConvertViaJson(output, updatedDeal)
	if err != nil {
		m.Logger.WithContext(ctx).Errorf("%s - Failed to convert deal output: %v", op, err)
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	return output, nil
}

func (m *DealModel) GetDeal(ctx context.Context, input *dto.GetDealInput) (*dto.DealOutput, error) {
	op := "DealModel.GetDeal"

	query := m.DbClient.Deal.Query()

	if input.IncludeRelation {
		query = query.
			WithPerson(func(pq *ent.PersonQuery) {
				pq.WithIssues()
			}).
			WithDealAssignment(func(daq *ent.DealUserQuery) {
				daq.WithUser().WithRatings()
			}).
			WithAttachments(func(aq *ent.AttachmentQuery) {
				aq.Where(attachment.KindEQ(attachment.KindProduct)).WithProduct()
			}).
			WithStage().
			WithPlans(func(pq *ent.InstallmentPlanQuery) {
				pq.WithInstallments()
			}).
			WithBills().
			WithTags()
	} else {
		query = query.WithStage()
	}

	dealRecord, err := query.Where(deal.ID(input.ID)).First(ctx)
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, berr.ErrNotFound.Wrap(err).Op(op)
		}
		return nil, berr.ErrDB.Wrap(err).Op(op)
	}

	output := new(dto.DealOutput)
	err = cast.ConvertViaJson(output, dealRecord)
	if err != nil {
		m.Logger.WithContext(ctx).Errorf("%s - Failed to convert deal output: %v", op, err)
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	return output, nil
}

func (m *DealModel) ResetAttachmentDiscounts(ctx context.Context, tx *ent.Tx, dealID int) error {
	op := "DealModel.ResetAttachmentDiscounts"

	_, err := tx.Attachment.Update().
		Where(
			attachment.DealID(dealID),
			attachment.DeletedAtIsNil(),
		).
		SetDiscount(0).
		Save(ctx)
	if err != nil {
		return berr.ErrUpdateFailed.Wrap(err).Op(op)
	}

	return nil
}

func (m *DealModel) GetAttachmentsForDeal(ctx context.Context, tx *ent.Tx, dealID int) ([]*ent.Attachment, error) {
	op := "DealModel.GetAttachmentsForDeal"

	attachments, err := tx.Attachment.Query().
		Where(
			attachment.DealID(dealID),
			attachment.DeletedAtIsNil(),
		).
		Order(ent.Desc(attachment.FieldPrice)).
		All(ctx)
	if err != nil {
		return nil, berr.ErrDB.Wrap(err).Op(op)
	}

	return attachments, nil
}

func (m *DealModel) ApplyDiscountDistribution(ctx context.Context, tx *ent.Tx, attachmentDiscounts map[int]float64) error {
	op := "DealModel.ApplyDiscountDistribution"

	for attachmentID, discountAmount := range attachmentDiscounts {
		_, err := tx.Attachment.UpdateOneID(attachmentID).
			SetDiscount(discountAmount).
			Save(ctx)
		if err != nil {
			return berr.ErrUpdateFailed.Wrap(err).Op(op)
		}
	}

	return nil
}

func (m *DealModel) DistributeDiscountToAttachments(attachments []*ent.Attachment, discountAmount float64, totalAttachmentAmount float64) map[int]float64 {
	distributionStrategy := types.DiscountDistributionStrategyRatio
	attachmentDiscounts := make(map[int]float64)

	if distributionStrategy == types.DiscountDistributionStrategyRatio {
		var totalAllocatedDiscount float64
		lastAttachment := attachments[len(attachments)-1]

		for i := 0; i < len(attachments)-1; i++ {
			att := attachments[i]
			if totalAttachmentAmount > 0 {
				ratio := att.Price / totalAttachmentAmount
				discountAmountForAttachment := math.Floor((discountAmount*ratio)/100) * 100
				attachmentDiscounts[att.ID] += discountAmountForAttachment
				totalAllocatedDiscount += discountAmountForAttachment
			}
		}

		remainingDiscount := discountAmount - totalAllocatedDiscount
		remainingDiscount = math.Floor(remainingDiscount/100) * 100
		attachmentDiscounts[lastAttachment.ID] += remainingDiscount
	} else {
		attachmentDiscounts[attachments[0].ID] += discountAmount
	}

	return attachmentDiscounts
}

func (m *DealModel) GetAttachmentsWithoutBillItems(ctx context.Context, tx *ent.Tx, dealID int) ([]*ent.Attachment, error) {
	op := "DealModel.GetAttachmentsWithoutBillItems"

	attachments, err := tx.Attachment.Query().
		Where(
			attachment.KindEQ(attachment.KindProduct),
			attachment.DealID(dealID),
			attachment.Not(
				attachment.HasBillItemWith(),
			),
		).All(ctx)
	if err != nil {
		return nil, berr.ErrDB.Wrap(err).Op(op)
	}

	return attachments, nil
}

func (m *DealModel) UpdateAttachmentsPlanID(ctx context.Context, tx *ent.Tx, attachments []*ent.Attachment, planID int) error {
	op := "DealModel.UpdateAttachmentsPlanID"

	client := m.getClient(tx)
	for _, attachment := range attachments {
		_, err := client.Attachment.UpdateOneID(attachment.ID).
			SetPlanID(planID).
			Save(ctx)
		if err != nil {
			return berr.ErrUpdateFailed.Wrap(err).Op(op)
		}
	}

	return nil
}

func (m *DealModel) getClient(tx *ent.Tx) *ent.Client {
	if tx != nil {
		return tx.Client()
	}
	return m.DbClient
}

func (m *DealModel) buildDealQuery(input dto.ListDealInput) *ent.DealQuery {
	query := m.DbClient.Deal.Query()

	if input.IncludeRelation {
		query = query.
			WithPerson(func(pq *ent.PersonQuery) {
				pq.WithIssues()
			}).
			WithDealAssignment(func(daq *ent.DealUserQuery) {
				daq.WithUser()
			}).
			WithAttachments(
				func(q *ent.AttachmentQuery) {
					q.Where(attachment.KindEQ(attachment.KindProduct), attachment.StatusNEQ(bconst.StatusTemp)).WithBillItem(func(q *ent.BillItemQuery) {
						q.WithAllocations()
					})
				},
			).
			WithStage().
			WithPlans(func(pq *ent.InstallmentPlanQuery) {
				pq.WithInstallments(func(iq *ent.InstallmentQuery) {
					iq.WithAllocations()
				})
			})
	} else {
		query = query.WithStage()
	}

	if input.Filter.PipelineID != nil {
		query = query.Where(deal.HasStageWith(stage.PipelineID(*input.Filter.PipelineID)))
	}

	if input.Filter.Status == nil || *input.Filter.Status == 0 {
		query = query.Where(deal.StatusGTE(bconst.StatusNormal))
	} else {
		query = query.Where(deal.StatusEQ(*input.Filter.Status))
	}

	if input.Filter.PersonID != nil {
		query = query.Where(deal.PersonID(*input.Filter.PersonID))
	}
	if input.Filter.StageID != nil {
		query = query.Where(deal.StageID(*input.Filter.StageID))
	}
	if input.Filter.State != nil {
		query = query.Where(deal.StateEQ(deal.State(*input.Filter.State)))
	}

	return query
}

func (m *DealModel) ExtractDealIDs(deals []*ent.Deal) (dealIDs []int, pipelineIDs []int) {
	dealIDs = make([]int, len(deals))
	pipelineIDs = make([]int, 0, len(deals))

	for i, d := range deals {
		dealIDs[i] = d.ID
		if d.Edges.Stage != nil {
			pipelineIDs = append(pipelineIDs, d.Edges.Stage.PipelineID)
		}
	}

	return
}

func (m *DealModel) MapDealsWithRelations(deals []dto.DealOutput, discountUsages []dto.DiscountUsageOutput) error {
	discountUsageMap := make(map[int][]dto.DiscountUsageOutput)
	for _, du := range discountUsages {
		if du.DealID != nil {
			discountUsageMap[*du.DealID] = append(discountUsageMap[*du.DealID], du)
		}
	}

	for i, dealRecord := range deals {
		if dus, exists := discountUsageMap[dealRecord.ID]; exists {
			err := cast.ConvertViaJson(&deals[i].DiscountUsages, dus)
			if err != nil {
				return err
			}

			for _, du := range dus {
				deals[i].DiscountAmount += du.Value * float64(du.UsageCount)
			}
		}

		if len(dealRecord.Plans) > 0 {
			for _, plan := range dealRecord.Plans {
				deals[i].DownPayment += plan.DownPayment
				deals[i].TotalPlanAmount += plan.TotalAmount
				for _, installmentRecord := range plan.Installments {
					paidAmount := 0.0
					for _, allocation := range installmentRecord.Allocations {
						paidAmount += allocation.Amount
					}
					switch installmentRecord.Kind {
					case string(installment.KindDownPayment):
						deals[i].DownPaymentReceivedAmount += paidAmount
					case string(installment.KindSequencePayment):
						if installmentRecord.PaidAt != nil {
							deals[i].PaidAmount += paidAmount
							deals[i].PaidInstallmentCount++
						}
					case string(installment.KindRefundPayment):
						deals[i].RefundAmount += installmentRecord.Amount
					}
				}
				deals[i].TotalAmount = deals[i].TotalAmount - deals[i].DiscountAmount
			}
			deals[i].TotalAmount = deals[i].TotalAmount - deals[i].DiscountAmount
		} else {
			discountAmount := 0.0
			totalAmount := 0.0
			for _, at := range dealRecord.Attachments {
				paidAmount := 0.0
				refundAmount := 0.0
				discountAmount += at.Discount
				totalAmount += at.Price*float64(at.Quantity) - at.Discount
				if at.BillItem != nil {
					if at.BillItem.PaymentAllocations != nil {
						for _, allocation := range at.BillItem.PaymentAllocations {
							if allocation.Amount > 0 {
								paidAmount += allocation.Amount
							} else {
								refundAmount += allocation.Amount
							}
						}
					}
				}
				deals[i].PaidAmount += paidAmount
				deals[i].RefundAmount += refundAmount
			}
			if dus, exists := discountUsageMap[dealRecord.ID]; !exists {
				deals[i].DiscountAmount += discountAmount
				err := cast.ConvertViaJson(&deals[i].DiscountUsages, dus)
				if err != nil {
					return err
				}
			}
			deals[i].TotalAmount = totalAmount
		}
	}

	return nil
}
