package model

import (
	"bcare/api/internal/dto"
	"bcare/common/berr"
	"bcare/common/bquery"
	"bcare/common/cast"
	"bcare/common/ctxdata"
	"bcare/ent"
	"bcare/ent/attachment"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type AttachmentModel struct {
	Logger   logx.Logger
	DbClient *ent.Client
}

func NewAttachmentModel(dbClient *ent.Client) *AttachmentModel {
	logger := logx.WithContext(context.Background()).WithFields(logx.Field("model", "AttachmentModel"))
	return &AttachmentModel{
		Logger:   logger,
		DbClient: dbClient,
	}
}

func (m *AttachmentModel) Create(ctx context.Context, input *dto.CreateAttachmentInput) (*dto.AttachmentOutput, error) {
	op := "AttachmentModel.Create"
	userID := ctxdata.GetUidFromCtx(ctx)

	attachmentRecord := new(ent.Attachment)
	err := cast.InputToEnt(attachmentRecord, input)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	createdAttachment, err := m.DbClient.Attachment.Create().
		SetAttachment(attachmentRecord).
		Save(ctx)
	if err != nil {
		return nil, berr.ErrCreateFailed.Wrap(err).Op(op)
	}

	m.Logger.WithContext(ctx).Infof("%s - Attachment %d created by user %d", op, createdAttachment.ID, userID)

	output := new(dto.AttachmentOutput)
	err = cast.ConvertViaJson(output, createdAttachment)
	if err != nil {
		m.Logger.WithContext(ctx).Errorf("%s - Failed to convert attachment output: %v", op, err)
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	return output, nil
}

func (m *AttachmentModel) Get(ctx context.Context, id int) (*dto.AttachmentOutput, error) {
	op := "AttachmentModel.Get"

	attachmentRecord, err := m.DbClient.Attachment.Query().
		WithProduct().
		WithBillItem().
		Where(attachment.ID(id)).
		Only(ctx)
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, berr.ErrNotFound.Wrap(err).Op(op)
		}
		return nil, berr.ErrDB.Wrap(err).Op(op)
	}

	output := new(dto.AttachmentOutput)
	err = cast.ConvertViaJson(output, attachmentRecord)
	if err != nil {
		m.Logger.WithContext(ctx).Errorf("%s - Failed to convert attachment output: %v", op, err)
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	return output, nil
}

func (m *AttachmentModel) List(ctx context.Context, input *dto.ListAttachmentInput) (*dto.ListAttachmentOutput, error) {
	op := "AttachmentModel.List"

	limit, offset := bquery.Paging(input.PageSize, input.Page)
	order := bquery.Ordering(input.OrderBy)

	query := m.DbClient.Attachment.Query().
		WithProduct().
		WithBillItem().
		Order(order)

	if input.Filter.DealID != 0 {
		query = query.Where(attachment.DealID(input.Filter.DealID))
	}
	if input.Filter.ProductID != 0 {
		query = query.Where(attachment.ProductID(input.Filter.ProductID))
	}
	if input.Filter.ParentID != 0 {
		query = query.Where(attachment.ParentID(input.Filter.ParentID))
	}
	if input.Filter.Kind != "" {
		query = query.Where(attachment.KindEQ(attachment.Kind(input.Filter.Kind)))
	}
	if input.Filter.Status != 0 {
		query = query.Where(attachment.StatusEQ(int8(input.Filter.Status)))
	}
	if input.Filter.PlanID != 0 {
		query = query.Where(attachment.PlanID(input.Filter.PlanID))
	}
	if input.Filter.PersonID != 0 {
		query = query.Where(attachment.PersonID(input.Filter.PersonID))
	}

	total, err := query.Clone().Count(ctx)
	if err != nil {
		return nil, berr.ErrListFailed.Wrap(err).Op(op)
	}

	output := &dto.ListAttachmentOutput{
		Attachments: []dto.AttachmentOutput{},
		Total:       total,
		TotalPage:   (total + limit - 1) / limit,
	}

	if total == 0 {
		return output, nil
	}

	attachments, err := query.Limit(limit).Offset(offset).All(ctx)
	if err != nil {
		return nil, berr.ErrListFailed.Wrap(err).Op(op)
	}

	err = cast.ConvertViaJson(&output.Attachments, attachments)
	if err != nil {
		m.Logger.WithContext(ctx).Errorf("%s - Failed to convert attachments output: %v", op, err)
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	return output, nil
}

func (m *AttachmentModel) ListByDealID(ctx context.Context, dealID int) ([]dto.AttachmentOutput, error) {
	op := "AttachmentModel.ListByDealID"

	attachments, err := m.DbClient.Attachment.Query().
		Where(
			attachment.DealID(dealID),
			attachment.DeletedAtIsNil(),
		).
		Order(ent.Desc(attachment.FieldPrice)).
		All(ctx)
	if err != nil {
		return nil, berr.ErrDB.Wrap(err).Op(op)
	}

	var outputs []dto.AttachmentOutput
	err = cast.ConvertViaJson(&outputs, attachments)
	if err != nil {
		m.Logger.WithContext(ctx).Errorf("%s - Failed to convert attachments output: %v", op, err)
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	return outputs, nil
}

func (m *AttachmentModel) Update(ctx context.Context, input *dto.UpdateAttachmentInput) (*dto.AttachmentOutput, error) {
	op := "AttachmentModel.Update"
	userID := ctxdata.GetUidFromCtx(ctx)

	attachmentRecord, err := m.DbClient.Attachment.Get(ctx, input.ID)
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, berr.ErrNotFound.Wrap(err).Op(op)
		}
		return nil, berr.ErrDB.Wrap(err).Op(op)
	}

	err = cast.InputToEnt(attachmentRecord, input)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	updatedAttachment, err := m.DbClient.Attachment.UpdateOne(attachmentRecord).
		SetAttachment(attachmentRecord, input.Modified...).
		Save(ctx)
	if err != nil {
		return nil, berr.ErrUpdateFailed.Wrap(err).Op(op)
	}

	m.Logger.WithContext(ctx).Infof("%s - Attachment %d updated by user %d", op, input.ID, userID)

	output := new(dto.AttachmentOutput)
	err = cast.ConvertViaJson(output, updatedAttachment)
	if err != nil {
		m.Logger.WithContext(ctx).Errorf("%s - Failed to convert attachment output: %v", op, err)
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	return output, nil
}

func (m *AttachmentModel) BulkUpdateDiscount(ctx context.Context, dealID int, discount float64) error {
	op := "AttachmentModel.BulkUpdateDiscount"

	_, err := m.DbClient.Attachment.Update().
		Where(
			attachment.DealID(dealID),
			attachment.DeletedAtIsNil(),
		).
		SetDiscount(discount).
		Save(ctx)
	if err != nil {
		return berr.ErrUpdateFailed.Wrap(err).Op(op)
	}

	return nil
}

func (m *AttachmentModel) UpdateDiscountByID(ctx context.Context, attachmentID int, discount float64) error {
	op := "AttachmentModel.UpdateDiscountByID"

	_, err := m.DbClient.Attachment.UpdateOneID(attachmentID).
		SetDiscount(discount).
		Save(ctx)
	if err != nil {
		return berr.ErrUpdateFailed.Wrap(err).Op(op)
	}

	return nil
}

func (m *AttachmentModel) Delete(ctx context.Context, id int) error {
	op := "AttachmentModel.Delete"
	userID := ctxdata.GetUidFromCtx(ctx)

	_, err := m.DbClient.Attachment.Get(ctx, id)
	if err != nil {
		if ent.IsNotFound(err) {
			return berr.ErrNotFound.Wrap(err).Op(op)
		}
		return berr.ErrDB.Wrap(err).Op(op)
	}

	err = m.DbClient.Attachment.DeleteOneID(id).Exec(ctx)
	if err != nil {
		return berr.ErrDeleteFailed.Wrap(err).Op(op)
	}

	m.Logger.WithContext(ctx).Infof("%s - Attachment %d deleted by user %d", op, id, userID)
	return nil
}
