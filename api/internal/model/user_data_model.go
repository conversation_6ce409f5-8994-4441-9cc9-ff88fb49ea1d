package model

import (
	"bcare/api/internal/dto"
	"bcare/common/berr"
	"bcare/common/bquery"
	"bcare/common/cast"
	"bcare/common/ctxdata"
	"bcare/ent"
	"bcare/ent/userdata"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type UserDataModel struct {
	Logger   logx.Logger
	DbClient *ent.Client
}

func NewUserDataModel(dbClient *ent.Client) *UserDataModel {
	logger := logx.WithContext(context.Background()).WithFields(logx.Field("model", "UserDataModel"))
	return &UserDataModel{
		Logger:   logger,
		DbClient: dbClient,
	}
}

func (m *UserDataModel) GetUserData(ctx context.Context, input *dto.GetUserDataInput) (*dto.UserDataDetailOutput, error) {
	op := "UserDataModel.GetUserData"

	userDataEntity, err := m.DbClient.UserData.Query().
		Where(userdata.UserID(input.UserID), userdata.KindEQ(input.Kind)).
		Only(ctx)

	if err != nil {
		if ent.IsNotFound(err) {
			return nil, berr.ErrNotFound.Wrap(err).Op(op)
		}
		return nil, berr.ErrDB.Wrap(err).Op(op)
	}

	output := new(dto.UserDataDetailOutput)
	err = cast.ConvertViaJson(output, userDataEntity)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	if input.Key != nil && *input.Key != "" {
		if dataMap, ok := output.Data[*input.Key]; ok {
			output.Data = map[string]interface{}{*input.Key: dataMap}
		} else {
			return nil, berr.ErrNotFound.Op(op)
		}
	}

	return output, nil
}

func (m *UserDataModel) SetUserData(ctx context.Context, input *dto.SetUserDataInput) (*dto.UserDataDetailOutput, error) {
	op := "UserDataModel.SetUserData"
	userID := ctxdata.GetUidFromCtx(ctx)

	ud, err := m.DbClient.UserData.Query().
		Where(userdata.UserID(input.UserID), userdata.KindEQ(input.Kind)).
		Only(ctx)

	if err != nil && !ent.IsNotFound(err) {
		return nil, berr.ErrDB.Wrap(err).Op(op)
	}

	var savedUserData *ent.UserData
	if ent.IsNotFound(err) {
		inputData := make(map[string]interface{})
		if len(input.Data) > 0 {
			inputData = input.Data
		} else {
			inputData = map[string]interface{}{input.Key: input.Value}
		}
		savedUserData, err = m.DbClient.UserData.Create().
			SetUserID(input.UserID).
			SetKind(input.Kind).
			SetData(inputData).
			Save(ctx)
		if err != nil {
			return nil, berr.ErrCreateFailed.Wrap(err).Op(op)
		}
		m.Logger.WithContext(ctx).Infof("%s - UserData created for user_id %d, kind %s by user %d", op, savedUserData.UserID, savedUserData.Kind, userID)
	} else {
		inputData := make(map[string]interface{})
		if len(input.Data) > 0 {
			inputData = input.Data
		} else {
			if ud.Data != nil {
				inputData = ud.Data
			}
			inputData[input.Key] = input.Value
		}
		savedUserData, err = ud.Update().
			SetData(inputData).
			Save(ctx)
		if err != nil {
			return nil, berr.ErrUpdateFailed.Wrap(err).Op(op)
		}
		m.Logger.WithContext(ctx).Infof("%s - UserData updated for user_id %d, kind %s by user %d", op, savedUserData.UserID, savedUserData.Kind, userID)
	}

	output := new(dto.UserDataDetailOutput)
	err = cast.ConvertViaJson(output, savedUserData)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	return output, nil
}

func (m *UserDataModel) UpdateUserData(ctx context.Context, input *dto.UpdateUserDataInput) (*dto.UserDataDetailOutput, error) {
	op := "UserDataModel.UpdateUserData"
	userID := ctxdata.GetUidFromCtx(ctx)

	ud, err := m.DbClient.UserData.Query().
		Where(userdata.UserID(input.UserID), userdata.KindEQ(input.Kind)).
		Only(ctx)

	if err != nil {
		if ent.IsNotFound(err) {
			return nil, berr.ErrNotFound.Wrap(err).Op(op)
		}
		return nil, berr.ErrDB.Wrap(err).Op(op)
	}

	updatedData := ud.Data
	if input.Data != nil {
		if updatedData == nil {
			updatedData = make(map[string]interface{})
		}
		for k, v := range *input.Data {
			updatedData[k] = v
		}
	}

	savedUserData, err := ud.Update().
		SetData(updatedData).
		Save(ctx)

	if err != nil {
		return nil, berr.ErrUpdateFailed.Wrap(err).Op(op)
	}

	m.Logger.WithContext(ctx).Infof("%s - UserData partially updated for user_id %d, kind %s by user %d", op, savedUserData.UserID, savedUserData.Kind, userID)

	output := new(dto.UserDataDetailOutput)
	err = cast.ConvertViaJson(output, savedUserData)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	return output, nil
}

func (m *UserDataModel) ClearUserData(ctx context.Context, input *dto.ClearUserDataInput) error {
	op := "UserDataModel.ClearUserData"
	userID := ctxdata.GetUidFromCtx(ctx)

	query := m.DbClient.UserData.Query().Where(userdata.UserID(input.UserID))
	if input.Kind != nil && *input.Kind != "" {
		query = query.Where(userdata.KindEQ(*input.Kind))
	}

	uds, err := query.All(ctx)
	if err != nil {
		return berr.ErrDB.Wrap(err).Op(op)
	}

	if len(uds) == 0 {
		return berr.ErrNotFound.Op(op)
	}

	for _, ud := range uds {
		if input.Key != nil && *input.Key != "" {
			if ud.Data != nil {
				delete(ud.Data, *input.Key)
				_, updateErr := ud.Update().SetData(ud.Data).Save(ctx)
				if updateErr != nil {
					return berr.ErrUpdateFailed.Wrap(updateErr).Op(op)
				}
				m.Logger.WithContext(ctx).Infof("%s - Key %s cleared for UserData user_id %d, kind %s by user %d", op, *input.Key, ud.UserID, ud.Kind, userID)
			}
		} else {
			deleteErr := m.DbClient.UserData.DeleteOne(ud).Exec(ctx)
			if deleteErr != nil {
				return berr.ErrDeleteFailed.Wrap(deleteErr).Op(op)
			}
			m.Logger.WithContext(ctx).Infof("%s - UserData deleted for user_id %d, kind %s by user %d", op, ud.UserID, ud.Kind, userID)
		}
	}
	return nil
}

func (m *UserDataModel) ListUserData(ctx context.Context, input *dto.ListUserDataInput) (*dto.ListUserDataOutput, error) {
	op := "UserDataModel.ListUserData"

	limit, offset := bquery.Paging(input.PageSize, input.Page)
	order := bquery.Ordering(input.OrderBy)

	query := m.DbClient.UserData.Query().Order(order)

	if input.Filter != nil {
		if input.Filter.UserID != nil {
			query = query.Where(userdata.UserID(*input.Filter.UserID))
		}
		if input.Filter.Kind != nil && *input.Filter.Kind != "" {
			query = query.Where(userdata.KindEQ(*input.Filter.Kind))
		}
	}

	total, err := query.Clone().Count(ctx)
	if err != nil {
		return nil, berr.ErrListFailed.Wrap(err).Op(op)
	}

	output := &dto.ListUserDataOutput{
		UserData: []*dto.UserDataDetailOutput{},
		Total:    total,
	}

	if total == 0 {
		return output, nil
	}

	entities, err := query.Limit(limit).Offset(offset).All(ctx)
	if err != nil {
		return nil, berr.ErrListFailed.Wrap(err).Op(op)
	}

	err = cast.ConvertViaJson(&output.UserData, entities)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	return output, nil
}
