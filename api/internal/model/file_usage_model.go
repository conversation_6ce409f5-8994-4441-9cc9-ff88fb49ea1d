package model

import (
	"bcare/api/internal/dto"
	"bcare/common/berr"
	"bcare/common/bquery"
	"bcare/common/cast"
	"bcare/ent"
	"bcare/ent/fileusage"
	"bcare/ent/track"
	"context"
	"entgo.io/ent/dialect/sql"
	"github.com/zeromicro/go-zero/core/logx"
)

type FileUsageModel struct {
	Logger   logx.Logger
	DbClient *ent.Client
}

func NewFileUsageModel(dbClient *ent.Client) *FileUsageModel {
	logger := logx.WithContext(context.Background()).WithFields(logx.Field("model", "FileUsageModel"))
	return &FileUsageModel{
		Logger:   logger,
		DbClient: dbClient,
	}
}

func (m *FileUsageModel) CreateFileUsage(ctx context.Context, input *dto.CreateFileUsageInput) (*dto.FileUsageOutput, error) {
	op := "FileUsageModel.CreateFileUsage"

	fileUsageRecord := new(ent.FileUsage)

	err := cast.InputToEnt(fileUsageRecord, input)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	if input.TrackID == 0 && input.EntityType == "person" {
		activeTrack, err := m.DbClient.Track.Query().
			Where(track.PersonID(input.EntityID)).
			Order(track.ByCreatedAt(sql.OrderDesc())).
			First(ctx)

		if err == nil && activeTrack != nil {
			fileUsageRecord.TrackID = activeTrack.ID
		}
	}

	createdFileUsage, err := m.DbClient.FileUsage.Create().
		SetFileUsage(fileUsageRecord).
		Save(ctx)

	if err != nil {
		return nil, berr.ErrCreateFailed.Wrap(err).Op(op)
	}

	m.Logger.WithContext(ctx).Infof("%s - FileUsage %d created", op, createdFileUsage.ID)

	result, err := m.DbClient.FileUsage.Query().
		Where(fileusage.ID(createdFileUsage.ID)).
		WithFile().
		First(ctx)

	if err != nil {
		return nil, berr.ErrDB.Wrap(err).Op(op)
	}

	output := new(dto.FileUsageOutput)
	_ = cast.ConvertViaJson(output, result)

	return output, nil
}

func (m *FileUsageModel) GetFileUsage(ctx context.Context, id int) (*dto.FileUsageOutput, error) {
	op := "FileUsageModel.GetFileUsage"

	fileUsageRecord, err := m.DbClient.FileUsage.Query().
		Where(fileusage.ID(id)).
		WithFile().
		First(ctx)

	if err != nil {
		if ent.IsNotFound(err) {
			return nil, berr.ErrNotFound.Wrap(err).Op(op)
		}
		return nil, berr.ErrDB.Wrap(err).Op(op)
	}

	output := new(dto.FileUsageOutput)
	_ = cast.ConvertViaJson(output, fileUsageRecord)

	return output, nil
}

func (m *FileUsageModel) ListFileUsages(ctx context.Context, input dto.ListFileUsagesInput) (*dto.ListFileUsagesOutput, error) {
	op := "FileUsageModel.ListFileUsages"

	limit, offset := bquery.Paging(input.PageSize, input.Page)
	order := bquery.Ordering(input.OrderBy)

	query := m.DbClient.FileUsage.Query().
		WithFile().
		WithTrack().
		Order(order)

	if input.Filter.FileID != 0 {
		query = query.Where(fileusage.FileID(input.Filter.FileID))
	}
	if input.Filter.EntityType != "" {
		query = query.Where(fileusage.EntityTypeEQ(input.Filter.EntityType))
	}
	if input.Filter.EntityID != 0 {
		query = query.Where(fileusage.EntityID(input.Filter.EntityID))
	}
	if input.Filter.UsageType != "" {
		query = query.Where(fileusage.UsageTypeEQ(input.Filter.UsageType))
	}
	if input.Filter.TrackID != 0 {
		query = query.Where(fileusage.TrackID(input.Filter.TrackID))
	}

	total, err := query.Clone().Count(ctx)
	if err != nil {
		return nil, berr.ErrListFailed.Wrap(err).Op(op)
	}

	output := &dto.ListFileUsagesOutput{
		FileUsages: []*dto.FileUsageOutput{},
		Total:      total,
	}

	if total == 0 {
		return output, nil
	}

	fileUsages, err := query.Limit(limit).Offset(offset).All(ctx)
	if err != nil {
		return nil, berr.ErrListFailed.Wrap(err).Op(op)
	}

	_ = cast.ConvertViaJson(&output.FileUsages, fileUsages)

	return output, nil
}

func (m *FileUsageModel) UpdateFileUsage(ctx context.Context, input *dto.UpdateFileUsageInput) (*dto.FileUsageOutput, error) {
	op := "FileUsageModel.UpdateFileUsage"

	fileUsageRecord, err := m.DbClient.FileUsage.Get(ctx, input.ID)
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, berr.ErrNotFound.Wrap(err).Op(op)
		}
		return nil, berr.ErrDB.Wrap(err).Op(op)
	}

	err = cast.InputToEnt(fileUsageRecord, input)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	updatedFileUsage, err := m.DbClient.FileUsage.UpdateOne(fileUsageRecord).
		SetFileUsage(fileUsageRecord, input.Modified...).
		Save(ctx)

	if err != nil {
		return nil, berr.ErrUpdateFailed.Wrap(err).Op(op)
	}

	m.Logger.WithContext(ctx).Infof("%s - FileUsage %d updated", op, input.ID)

	result, err := m.DbClient.FileUsage.Query().
		Where(fileusage.ID(updatedFileUsage.ID)).
		WithFile().
		First(ctx)

	if err != nil {
		return nil, berr.ErrDB.Wrap(err).Op(op)
	}

	output := new(dto.FileUsageOutput)
	_ = cast.ConvertViaJson(output, result)

	return output, nil
}

func (m *FileUsageModel) DeleteFileUsage(ctx context.Context, id int) error {
	op := "FileUsageModel.DeleteFileUsage"

	_, err := m.DbClient.FileUsage.Get(ctx, id)
	if err != nil {
		if ent.IsNotFound(err) {
			return berr.ErrNotFound.Wrap(err).Op(op)
		}
		return berr.ErrDB.Wrap(err).Op(op)
	}

	err = m.DbClient.FileUsage.DeleteOneID(id).Exec(ctx)
	if err != nil {
		return berr.ErrDeleteFailed.Wrap(err).Op(op)
	}

	m.Logger.WithContext(ctx).Infof("%s - FileUsage %d deleted", op, id)
	return nil
}
