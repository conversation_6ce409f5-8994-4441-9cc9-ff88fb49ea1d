package model

import (
	"context"
	"time"

	"bcare/api/internal/dto"
	"bcare/api/internal/event"
	"bcare/common/bconst"
	"bcare/common/berr"
	"bcare/common/bquery"
	"bcare/common/cast"
	"bcare/common/ctxdata"
	"bcare/common/eventbus"
	"bcare/ent"
	"bcare/ent/notification"
	"bcare/ent/user"

	"github.com/zeromicro/go-zero/core/logx"
)

// NotificationModel là model xử lý logic cho notifications
type NotificationModel struct {
	Logger   logx.Logger
	DbClient *ent.Client
	EB       eventbus.Bus
}

// NewNotificationModel tạo một instance mới của NotificationModel
func NewNotificationModel(dbClient *ent.Client, eb eventbus.Bus) *NotificationModel {
	logger := logx.WithContext(context.Background()).WithFields(logx.Field("model", "NotificationModel"))
	return &NotificationModel{
		Logger:   logger,
		DbClient: dbClient,
		EB:       eb,
	}
}

// Create tạo một notification mới
func (m *NotificationModel) Create(ctx context.Context, input *dto.CreateNotificationInput) (*dto.NotificationOutput, error) {
	op := "NotificationModel.Create"

	notificationRecord := new(ent.Notification)
	err := cast.InputToEnt(notificationRecord, input)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	createdNotification, err := m.DbClient.Notification.Create().
		SetNotification(notificationRecord).
		Save(ctx)
	if err != nil {
		return nil, berr.ErrCreateFailed.Wrap(err).Op(op)
	}

	output := new(dto.NotificationOutput)
	if err := cast.ConvertViaJson(output, createdNotification); err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	// Dispatch notification created event
	userID := ctxdata.GetUidFromCtx(ctx)
	dispatchCtx := context.Background()
	eventToDispatch := event.NotificationAfterCreateEvent(output, userID)

	if eventToDispatch != nil {
		m.EB.Dispatch(dispatchCtx, eventToDispatch)
	}

	m.Logger.WithContext(ctx).Infof("%s - Notification %d created for user %d", op, output.ID, output.UserID)

	return output, nil
}

// GetByID lấy thông tin notification theo ID
func (m *NotificationModel) GetByID(ctx context.Context, id int) (*dto.NotificationOutput, error) {
	op := "NotificationModel.GetByID"

	notification, err := m.DbClient.Notification.Get(ctx, id)
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, berr.ErrNotFound.Wrap(err).Op(op)
		}
		return nil, berr.ErrDB.Wrap(err).Op(op)
	}

	output := new(dto.NotificationOutput)
	if err := cast.ConvertViaJson(output, notification); err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	return output, nil
}

// List lấy danh sách notifications theo filter
func (m *NotificationModel) List(ctx context.Context, input *dto.ListNotificationsInput) (*dto.ListNotificationsOutput, error) {
	op := "NotificationModel.List"

	limit, offset := bquery.Paging(input.PageSize, input.Page)
	order := bquery.Ordering(input.OrderBy)

	query := m.DbClient.Notification.Query().Order(order)

	// Apply filters
	if len(input.Filter.Ids) > 0 {
		query = query.Where(notification.IDIn(input.Filter.Ids...))
	}
	if input.Filter.UserID != 0 {
		query = query.Where(notification.UserID(input.Filter.UserID))
	}
	if input.Filter.IsRead != nil {
		query = query.Where(notification.IsRead(*input.Filter.IsRead))
	}
	if input.Filter.Type != "" {
		query = query.Where(notification.Type(input.Filter.Type))
	}
	if input.Filter.Status != 0 {
		query = query.Where(notification.Status(input.Filter.Status))
	}

	// Get total count
	total, err := query.Clone().Count(ctx)
	if err != nil {
		return nil, berr.ErrListFailed.Wrap(err).Op(op)
	}

	output := &dto.ListNotificationsOutput{
		Notifications: []*dto.NotificationOutput{},
		Total:         total,
	}

	if total == 0 {
		return output, nil
	}

	// Get paginated results
	notifications, err := query.Limit(limit).Offset(offset).All(ctx)
	if err != nil {
		return nil, berr.ErrListFailed.Wrap(err).Op(op)
	}

	if err := cast.ConvertViaJson(&output.Notifications, notifications); err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	return output, nil
}

// GetUnreadCount lấy số lượng notifications chưa đọc của user
func (m *NotificationModel) GetUnreadCount(ctx context.Context, userID int) (*dto.UnreadCountOutput, error) {
	op := "NotificationModel.GetUnreadCount"

	count, err := m.DbClient.Notification.Query().
		Where(
			notification.UserID(userID),
			notification.IsRead(false),
		).
		Count(ctx)
	if err != nil {
		return nil, berr.ErrDB.Wrap(err).Op(op)
	}

	return &dto.UnreadCountOutput{
		UnreadCount: count,
	}, nil
}

// MarkAsRead đánh dấu một notification đã đọc
func (m *NotificationModel) MarkAsRead(ctx context.Context, ids []int) (int, error) {
	op := "NotificationModel.MarkAsRead"

	if len(ids) == 0 {
		return 0, nil // Không có gì để cập nhật
	}

	updatedCount, err := m.DbClient.Notification.Update().
		Where(notification.IDIn(ids...)).
		SetIsRead(true).
		SetReadAt(time.Now()).
		Save(ctx)

	if err != nil {
		return 0, berr.ErrUpdateFailed.Wrap(err).Op(op)
	}

	return updatedCount, nil
}

// MarkAllAsRead đánh dấu tất cả notifications của user đã đọc
func (m *NotificationModel) MarkAllAsRead(ctx context.Context, userID int) (*dto.MarkAllAsReadOutput, error) {
	op := "NotificationModel.MarkAllAsRead"

	result, err := m.DbClient.Notification.Update().
		Where(
			notification.UserID(userID),
			notification.IsRead(false),
		).
		SetIsRead(true).
		SetReadAt(time.Now()).
		Save(ctx)
	if err != nil {
		return nil, berr.ErrUpdateFailed.Wrap(err).Op(op)
	}

	return &dto.MarkAllAsReadOutput{
		Message:      "All notifications marked as read successfully",
		UpdatedCount: result,
	}, nil
}

// Update cập nhật thông tin notification
func (m *NotificationModel) Update(ctx context.Context, input *dto.UpdateNotificationInput) (*dto.NotificationOutput, error) {
	op := "NotificationModel.Update"

	notificationRecord, err := m.DbClient.Notification.Get(ctx, input.ID)
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, berr.ErrNotFound.Wrap(err).Op(op)
		}
		return nil, berr.ErrDB.Wrap(err).Op(op)
	}

	err = cast.InputToEnt(notificationRecord, input)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	updatedNotification, err := m.DbClient.Notification.UpdateOne(notificationRecord).
		SetNotification(notificationRecord, input.Modified...).
		Save(ctx)
	if err != nil {
		return nil, berr.ErrUpdateFailed.Wrap(err).Op(op)
	}

	m.Logger.WithContext(ctx).Infof("%s - Notification %d updated", op, input.ID)

	output := new(dto.NotificationOutput)
	if err := cast.ConvertViaJson(output, updatedNotification); err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	return output, nil
}

// Delete xóa một notification
func (m *NotificationModel) Delete(ctx context.Context, id int) error {
	op := "NotificationModel.Delete"

	_, err := m.DbClient.Notification.Get(ctx, id)
	if err != nil {
		if ent.IsNotFound(err) {
			return berr.ErrNotFound.Wrap(err).Op(op)
		}
		return berr.ErrDB.Wrap(err).Op(op)
	}

	err = m.DbClient.Notification.DeleteOneID(id).Exec(ctx)
	if err != nil {
		return berr.ErrDeleteFailed.Wrap(err).Op(op)
	}

	m.Logger.WithContext(ctx).Infof("%s - Notification %d deleted", op, id)
	return nil
}

// CreateBatch tạo notifications hàng loạt cho nhiều user
func (m *NotificationModel) CreateBatch(ctx context.Context, input *dto.CreateBatchNotificationInput) (*dto.CreateBatchNotificationOutput, error) {
	op := "NotificationModel.CreateBatch"

	if len(input.UserIDs) == 0 {
		return &dto.CreateBatchNotificationOutput{CreatedCount: 0, Message: "No users to send notifications to"}, nil
	}

	chunkSize := 1000
	totalCreated := 0

	for i := 0; i < len(input.UserIDs); i += chunkSize {
		end := i + chunkSize
		if end > len(input.UserIDs) {
			end = len(input.UserIDs)
		}

		chunk := input.UserIDs[i:end]
		bulk := make([]*ent.NotificationCreate, len(chunk))

		for j, userID := range chunk {
			bulk[j] = m.DbClient.Notification.Create().
				SetUserID(userID).
				SetType(input.Type).
				SetMessage(input.Message).
				SetIsRead(false)

			if input.EntityID != nil {
				bulk[j].SetEntityID(*input.EntityID)
			}
			if input.EntityType != nil {
				bulk[j].SetEntityType(*input.EntityType)
			}
			if input.SenderID != nil {
				bulk[j].SetSenderID(*input.SenderID)
			}
			if input.Metadata != nil {
				bulk[j].SetMetadata(input.Metadata)
			}
		}

		created, err := m.DbClient.Notification.CreateBulk(bulk...).Save(ctx)
		if err != nil {
			return nil, berr.ErrCreateFailed.Wrap(err).Op(op)
		}

		totalCreated += len(created)

		userID := ctxdata.GetUidFromCtx(ctx)
		dispatchCtx := context.Background()
		outputs := make([]*dto.NotificationOutput, len(created))
		for k, createdNotif := range created {
			output := new(dto.NotificationOutput)
			if err := cast.ConvertViaJson(output, createdNotif); err == nil {
				outputs[k] = output
			}
		}
		eventToDispatch := event.NotificationAfterCreateEvent(outputs[0], userID)
		if eventToDispatch != nil {
			m.EB.Dispatch(dispatchCtx, eventToDispatch)
		}
	}

	m.Logger.WithContext(ctx).Infof("%s - Created %d notifications in batch", op, totalCreated)

	return &dto.CreateBatchNotificationOutput{
		CreatedCount: totalCreated,
		Message:      "Batch notifications created successfully",
	}, nil
}

// CreateForAllUsers tạo notification cho tất cả user đang hoạt động
func (m *NotificationModel) CreateForAllUsers(ctx context.Context, input *dto.CreateBroadcastNotificationInput) (*dto.CreateBatchNotificationOutput, error) {
	op := "NotificationModel.CreateForAllUsers"

	userIDs, err := m.DbClient.User.Query().
		Where(user.Status(bconst.StatusNormal)).
		IDs(ctx)
	if err != nil {
		return nil, berr.ErrDB.Wrap(err).Op(op)
	}

	batchInput := &dto.CreateBatchNotificationInput{
		UserIDs:    userIDs,
		Type:       input.Type,
		Message:    input.Message,
		EntityID:   input.EntityID,
		EntityType: input.EntityType,
		SenderID:   input.SenderID,
		Metadata:   input.Metadata,
	}

	return m.CreateBatch(ctx, batchInput)
}

// CreateForUserGroup tạo notifications cho nhóm user theo department hoặc role
func (m *NotificationModel) CreateForUserGroup(ctx context.Context, input *dto.CreateGroupNotificationInput) (*dto.CreateBatchNotificationOutput, error) {
	op := "NotificationModel.CreateForUserGroup"

	query := m.DbClient.User.Query().Where(user.Status(bconst.StatusNormal))

	if len(input.DepartmentIDs) > 0 {
		query = query.Where(user.DepartmentIDIn(input.DepartmentIDs...))
	}

	userIDs, err := query.IDs(ctx)
	if err != nil {
		return nil, berr.ErrDB.Wrap(err).Op(op)
	}

	batchInput := &dto.CreateBatchNotificationInput{
		UserIDs:    userIDs,
		Type:       input.Type,
		Message:    input.Message,
		EntityID:   input.EntityID,
		EntityType: input.EntityType,
		SenderID:   input.SenderID,
		Metadata:   input.Metadata,
	}

	return m.CreateBatch(ctx, batchInput)
}
