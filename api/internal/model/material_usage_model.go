package model

import (
	"bcare/api/internal/dto"
	"bcare/api/internal/types"
	"bcare/bob/queries"
	"bcare/common/berr"
	"bcare/common/bquery"
	"bcare/common/cast"
	"bcare/common/ctxdata"
	"bcare/common/excelutil"
	"bcare/ent"
	"bcare/ent/materialusage"
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/stephenafamo/bob"
	"github.com/zeromicro/go-zero/core/logx"
)

type MaterialUsageModel struct {
	Logger   logx.Logger
	DbClient *ent.Client
	Bob      bob.DB
}

func NewMaterialUsageModel(dbClient *ent.Client, bobDB bob.DB) *MaterialUsageModel {
	logger := logx.WithContext(context.Background()).WithFields(logx.Field("model", "MaterialUsageModel"))
	return &MaterialUsageModel{
		Logger:   logger,
		DbClient: dbClient,
		Bob:      bobDB,
	}
}

func (m *MaterialUsageModel) CreateMaterialUsage(ctx context.Context, input *dto.CreateMaterialUsageInput) (*dto.MaterialUsage, error) {
	op := "MaterialUsageModel.CreateMaterialUsage"
	userID := ctxdata.GetUidFromCtx(ctx)

	materialUsageRecord := new(ent.MaterialUsage)

	err := cast.InputToEnt(materialUsageRecord, input)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	materialUsageRecord.UserID = &userID

	createdMaterialUsage, err := m.DbClient.MaterialUsage.Create().
		SetMaterialUsage(materialUsageRecord).
		Save(ctx)

	if err != nil {
		return nil, berr.ErrCreateFailed.Wrap(err).Op(op)
	}

	m.Logger.WithContext(ctx).Infof("%s - MaterialUsage %d created by user %d", op, createdMaterialUsage.ID, userID)

	output := new(dto.MaterialUsage)
	err = cast.ConvertViaJson(output, createdMaterialUsage)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}
	return output, nil
}

func (m *MaterialUsageModel) CreateBulkMaterialUsage(ctx context.Context, input *dto.MaterialUsageBulkInput) error {
	op := "MaterialUsageModel.CreateBulkMaterialUsage"
	userID := ctxdata.GetUidFromCtx(ctx)

	if len(input.MaterialUsageAddItem) == 0 {
		return nil
	}

	bulkCreators := make([]*ent.MaterialUsageCreate, len(input.MaterialUsageAddItem))
	for i, item := range input.MaterialUsageAddItem {
		materialUsageRecord := new(ent.MaterialUsage)

		err := cast.InputToEnt(materialUsageRecord, item)
		if err != nil {
			return berr.ErrCopyFailed.Wrap(err).Op(op)
		}

		materialUsageRecord.UserID = &userID

		bulkCreators[i] = m.DbClient.MaterialUsage.Create().SetMaterialUsage(materialUsageRecord)
	}

	_, err := m.DbClient.MaterialUsage.CreateBulk(bulkCreators...).Save(ctx)
	if err != nil {
		return berr.ErrCreateFailed.Wrap(err).Op(op)
	}

	m.Logger.WithContext(ctx).Infof("%s - %d MaterialUsages created by user %d", op, len(input.MaterialUsageAddItem), userID)

	return nil
}

func (m *MaterialUsageModel) GetMaterialUsage(ctx context.Context, id int) (*dto.MaterialUsage, error) {
	op := "MaterialUsageModel.GetMaterialUsage"

	materialUsageRecord, err := m.DbClient.MaterialUsage.Get(ctx, id)
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, berr.ErrNotFound.Wrap(err).Op(op)
		}
		return nil, berr.ErrDB.Wrap(err).Op(op)
	}

	output := new(dto.MaterialUsage)
	err = cast.ConvertViaJson(output, materialUsageRecord)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	return output, nil
}

func (m *MaterialUsageModel) ListMaterialUsages(ctx context.Context, input dto.ListMaterialUsageInput) (*dto.ListMaterialUsageOutput, error) {
	op := "MaterialUsageModel.ListMaterialUsages"

	limit, offset := bquery.Paging(input.PageSize, input.Page)
	order := bquery.Ordering(input.OrderBy)

	query := m.DbClient.MaterialUsage.Query().Order(order)

	if input.Filter.AttachmentID != 0 {
		query = query.Where(materialusage.AttachmentIDEQ(input.Filter.AttachmentID))
	}
	if input.Filter.MaterialID != 0 {
		query = query.Where(materialusage.MaterialIDEQ(input.Filter.MaterialID))
	}
	if input.Filter.OperationID != 0 {
		query = query.Where(materialusage.OperationIDEQ(input.Filter.OperationID))
	}
	if input.Filter.UserID != 0 {
		query = query.Where(materialusage.UserIDEQ(input.Filter.UserID))
	}
	if input.Filter.Kind != "" {
		query = query.Where(materialusage.KindEQ(materialusage.Kind(input.Filter.Kind)))
	}

	total, err := query.Clone().Count(ctx)
	if err != nil {
		return nil, berr.ErrListFailed.Wrap(err).Op(op)
	}

	output := &dto.ListMaterialUsageOutput{
		MaterialUsages: []dto.MaterialUsage{},
		Total:          total,
	}

	if total == 0 {
		return output, nil
	}

	materialUsages, err := query.Limit(limit).Offset(offset).All(ctx)
	if err != nil {
		return nil, berr.ErrListFailed.Wrap(err).Op(op)
	}

	err = cast.ConvertViaJson(&output.MaterialUsages, materialUsages)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	return output, nil
}

func (m *MaterialUsageModel) UpdateMaterialUsage(ctx context.Context, input *dto.UpdateMaterialUsageInput) (*dto.MaterialUsage, error) {
	op := "MaterialUsageModel.UpdateMaterialUsage"

	materialUsageRecord, err := m.DbClient.MaterialUsage.Get(ctx, input.ID)
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, berr.ErrNotFound.Wrap(err).Op(op)
		}
		return nil, berr.ErrDB.Wrap(err).Op(op)
	}

	err = cast.InputToEnt(materialUsageRecord, input)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	updatedMaterialUsage, err := m.DbClient.MaterialUsage.UpdateOne(materialUsageRecord).
		SetMaterialUsage(materialUsageRecord, input.Modified...).
		Save(ctx)

	if err != nil {
		return nil, berr.ErrUpdateFailed.Wrap(err).Op(op)
	}

	m.Logger.WithContext(ctx).Infof("%s - MaterialUsage %d updated", op, input.ID)

	output := new(dto.MaterialUsage)
	err = cast.ConvertViaJson(output, updatedMaterialUsage)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	return output, nil
}

func (m *MaterialUsageModel) DeleteMaterialUsage(ctx context.Context, id int) error {
	op := "MaterialUsageModel.DeleteMaterialUsage"

	_, err := m.DbClient.MaterialUsage.Get(ctx, id)
	if err != nil {
		if ent.IsNotFound(err) {
			return berr.ErrNotFound.Wrap(err).Op(op)
		}
		return berr.ErrDB.Wrap(err).Op(op)
	}

	err = m.DbClient.MaterialUsage.DeleteOneID(id).Exec(ctx)
	if err != nil {
		return berr.ErrDeleteFailed.Wrap(err).Op(op)
	}

	m.Logger.WithContext(ctx).Infof("%s - MaterialUsage %d deleted", op, id)
	return nil
}

func (m *MaterialUsageModel) GetMaterialUsageReport(ctx context.Context, input dto.ReportMaterialUsageInput) (*dto.ReportMaterialUsageOutput, error) {
	op := "MaterialUsageModel.GetMaterialUsageReport"

	tx, err := m.Bob.BeginTx(ctx, nil)
	if err != nil {
		return nil, berr.ErrDB.Wrap(err).Op(op)
	}
	defer func() {
		if err != nil {
			tx.Rollback(ctx)
		} else {
			tx.Commit(ctx)
		}
	}()

	limit, offset := bquery.Paging(input.PageSize, input.Page)
	query := queries.MaterialUsageReport(input.Filter.From, input.Filter.To, int64(input.Filter.OperationId), int64(input.Filter.MaterialId), input.Filter.Search, int32(limit), int32(offset))
	rows, err := query.All(ctx, tx)
	if err != nil {
		return nil, berr.ErrDB.Wrap(err).Op(op)
	}

	results := make([]dto.ReportMaterialUsage, 0)

	for _, row := range rows {
		var code string
		if row.Code.Valid {
			code = row.Code.V
		}

		results = append(results, dto.ReportMaterialUsage{
			Name:                row.Name,
			Id:                  int(row.ID),
			Code:                code,
			Unit:                row.Unit,
			TotalAttachment:     row.TotalAttachment,
			TotalQuotedQuantity: row.TotalQuotedQuantity,
			TotalUsedQuantity:   row.TotalUsedQuantity,
		})
	}

	countQuery := queries.MaterialUsageReportCount(input.Filter.From, input.Filter.To, int64(input.Filter.OperationId), int64(input.Filter.MaterialId), input.Filter.Search)
	total, err := countQuery.One(ctx, tx)
	if err != nil {
		return nil, berr.ErrDB.Wrap(err).Op(op)
	}

	output := &dto.ReportMaterialUsageOutput{
		MaterialUsageReport: results,
		Total:               int(total),
	}

	return output, nil
}

func (m *MaterialUsageModel) GetMaterialUsageDetailReport(ctx context.Context, input dto.ReportMaterialUsageInput) (*dto.DetailReportMaterialUsageOutput, error) {
	op := "MaterialUsageModel.GetMaterialUsageDetailReport"

	tx, err := m.Bob.BeginTx(ctx, nil)
	if err != nil {
		return nil, berr.ErrDB.Wrap(err).Op(op)
	}
	defer func() {
		if err != nil {
			tx.Rollback(ctx)
		} else {
			tx.Commit(ctx)
		}
	}()

	limit, offset := bquery.Paging(input.PageSize, input.Page)
	query := queries.MaterialUsageDetailReport(input.Filter.From, input.Filter.To, int64(input.Filter.PersonId), int64(input.Filter.OperationId), int64(input.Filter.MaterialId), input.Filter.Search, int32(limit), int32(offset))
	rows, err := query.All(ctx, tx)
	if err != nil {
		return nil, berr.ErrDB.Wrap(err).Op(op)
	}
	results := make([]dto.MaterialUsageDetail, 0)

	for i, row := range rows {

		var operationDetails []dto.OperationDetailReport

		if len(row.OperationDetails.Val) > 0 {
			err = json.Unmarshal(row.OperationDetails.Val, &operationDetails)
			if err != nil {
				return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
			}
		}

		results = append(results, dto.MaterialUsageDetail{
			No:               int64(i + 1),
			PersonName:       row.PersonName,
			OperationDetails: operationDetails,
		})
	}

	countQuery := queries.MaterialUsageDetailReportCount(input.Filter.From, input.Filter.To, int64(input.Filter.PersonId), int64(input.Filter.OperationId), int64(input.Filter.MaterialId), input.Filter.Search)
	total, err := countQuery.One(ctx, tx)
	if err != nil {
		return nil, berr.ErrDB.Wrap(err).Op(op)
	}

	output := &dto.DetailReportMaterialUsageOutput{
		Items: results,
		Total: int(total),
	}

	return output, nil
}

type MaterialUsageReportExporter struct {
	reports []types.MaterialUsageReport
}

func NewMaterialUsageReportExporter(reports []types.MaterialUsageReport) excelutil.ConfigurableExporter {
	return &MaterialUsageReportExporter{reports: reports}
}

func (e *MaterialUsageReportExporter) GetConfig() excelutil.ExportConfig {
	return excelutil.ExportConfig{
		FileName:  fmt.Sprintf("bao-cao-su-dung-vat-lieu-%s", time.Now().Format("2006-01-02")),
		SheetName: "Material Usage Report",
		Columns: []excelutil.ColumnConfig{
			{Header: "STT", Width: 8, Formatter: excelutil.IntFormatter},
			{Header: "Mã vật liệu", Width: 15, Formatter: excelutil.StringFormatter},
			{Header: "Tên vật liệu", Width: 25, Formatter: excelutil.StringFormatter},
			{Header: "Đơn vị tính", Width: 12, Formatter: excelutil.StringFormatter},
			{Header: "Tổng ca", Width: 10, Formatter: excelutil.IntFormatter},
			{Header: "Định mức", Width: 12, Formatter: excelutil.FloatFormatter(2)},
			{Header: "Thực tế sử dụng", Width: 15, Formatter: excelutil.FloatFormatter(2)},
			{
				Header:    "Chênh lệch",
				Width:     12,
				Formatter: excelutil.DifferenceFormatter,
				ConditionalStyle: &excelutil.ConditionalStyle{
					Condition: func(value interface{}) bool {
						if f, ok := value.(float64); ok {
							return f > 0
						}
						return false
					},
					Style: excelutil.PositiveDifferenceStyle,
				},
			},
		},
	}
}

func (e *MaterialUsageReportExporter) GetRows() ([]map[string]interface{}, error) {
	rows := make([]map[string]interface{}, len(e.reports))
	for i, report := range e.reports {
		difference := report.TotalUsedQuantity - report.TotalQuotedQuantity
		rows[i] = map[string]interface{}{
			"STT":             i + 1,
			"Mã vật liệu":     report.Code,
			"Tên vật liệu":    report.Name,
			"Đơn vị tính":     report.Unit,
			"Tổng ca":         report.TotalAttachment,
			"Định mức":        report.TotalQuotedQuantity,
			"Thực tế sử dụng": report.TotalUsedQuantity,
			"Chênh lệch":      difference,
		}
	}
	return rows, nil
}

type MaterialUsageDetailReportExporter struct {
	details []types.MaterialUsageDetailRow
}

func NewMaterialUsageDetailReportExporter(details []types.MaterialUsageDetailRow) excelutil.ConfigurableExporter {
	return &MaterialUsageDetailReportExporter{details: details}
}

func (e *MaterialUsageDetailReportExporter) GetConfig() excelutil.ExportConfig {
	return excelutil.ExportConfig{
		FileName:  fmt.Sprintf("bao-cao-chi-tiet-su-dung-vat-lieu-%s", time.Now().Format("2006-01-02")),
		SheetName: "Material Usage Detail",
		Columns: []excelutil.ColumnConfig{
			{Header: "STT", Width: 8, Formatter: excelutil.IntFormatter},
			{Header: "Tên khách hàng", Width: 20, Formatter: excelutil.StringFormatter},
			{Header: "Tên công việc", Width: 25, Formatter: excelutil.StringFormatter},
			{Header: "Tên vật liệu", Width: 25, Formatter: excelutil.StringFormatter},
			{Header: "Định mức", Width: 12, Formatter: excelutil.FloatFormatter(2)},
			{Header: "Thực tế sử dụng", Width: 15, Formatter: excelutil.FloatFormatter(2)},
			{
				Header:    "Chênh lệch",
				Width:     12,
				Formatter: excelutil.DifferenceFormatter,
				ConditionalStyle: &excelutil.ConditionalStyle{
					Condition: func(value interface{}) bool {
						if f, ok := value.(float64); ok {
							return f != 0
						}
						return false
					},
					Style: excelutil.PositiveDifferenceStyle,
				},
			},
			{Header: "Đơn vị tính", Width: 12, Formatter: excelutil.StringFormatter},
		},
	}
}

func (e *MaterialUsageDetailReportExporter) GetRows() ([]map[string]interface{}, error) {
	var rows []map[string]interface{}
	stt := 1

	for _, detail := range e.details {
		for _, operation := range detail.OperationDetails {
			for _, material := range operation.UsedMaterials {
				row := map[string]interface{}{
					"STT":             stt,
					"Tên khách hàng":  detail.PersonName,
					"Tên công việc":   operation.OperationName,
					"Tên vật liệu":    material.MaterialName,
					"Định mức":        material.QuotedQuantity,
					"Thực tế sử dụng": material.UsedQuantity,
					"Chênh lệch":      material.Difference,
					"Đơn vị tính":     material.Unit,
				}
				rows = append(rows, row)
				stt++
			}
		}
	}
	return rows, nil
}
