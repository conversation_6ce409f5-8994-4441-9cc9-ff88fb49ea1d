package model

import (
	"bcare/common/berr"
	"bcare/common/bquery"
	"bcare/common/cast"
	"bcare/ent"
	"bcare/ent/operation" // Assuming 'operation' is the correct ent package name
	"context"

	"bcare/api/internal/dto"

	"github.com/zeromicro/go-zero/core/logx"
)

type OperationModel struct {
	Logger   logx.Logger
	DbClient *ent.Client
}

func NewOperationModel(dbClient *ent.Client) *OperationModel {
	logger := logx.WithContext(context.Background()).WithFields(logx.Field("model", "OperationModel"))
	return &OperationModel{
		Logger:   logger,
		DbClient: dbClient,
	}
}

func (m *OperationModel) CreateOperation(ctx context.Context, input *dto.CreateOperationInput) (*dto.OperationOutput, error) {
	op := "OperationModel.CreateOperation"

	operationRecord := new(ent.Operation)
	err := cast.InputToEnt(operationRecord, input)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	createdOperation, err := m.DbClient.Operation.Create().
		SetOperation(operationRecord).
		Save(ctx)

	if err != nil {
		return nil, berr.ErrCreateFailed.Wrap(err).Op(op)
	}

	m.Logger.WithContext(ctx).Infof("%s - Operation %d created", op, createdOperation.ID)

	output := new(dto.OperationOutput)
	err = cast.ConvertViaJson(output, createdOperation)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}
	return output, nil
}

func (m *OperationModel) GetOperation(ctx context.Context, id int) (*dto.OperationOutput, error) {
	op := "OperationModel.GetOperation"

	operationRecord, err := m.DbClient.Operation.Query().
		WithProductOperation().
		WithOperationMaterials().
		Where(operation.ID(id)).
		Only(ctx)

	//operationRecord.Edges
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, berr.ErrNotFound.Wrap(err).Op(op)
		}
		return nil, berr.ErrDB.Wrap(err).Op(op)
	}

	output := new(dto.OperationOutput)
	err = cast.ConvertViaJson(output, operationRecord)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	return output, nil
}

func (m *OperationModel) ListOperations(ctx context.Context, input *dto.ListOperationsInput) (*dto.ListOperationsOutput, error) {
	op := "OperationModel.ListOperations"

	limit, offset := bquery.Paging(input.PageSize, input.Page)
	order := bquery.Ordering(input.OrderBy)

	query := m.DbClient.Operation.Query().Order(order)

	if input.Filter.Name != "" {
		query = query.Where(operation.NameContainsFold(input.Filter.Name))
	}

	if input.Search != "" {
		query = query.Where(operation.Or(
			operation.NameContainsFold(input.Search),
			operation.DescriptionContainsFold(input.Search),
		))
	}

	total, err := query.Clone().Count(ctx)
	if err != nil {
		return nil, berr.ErrListFailed.Wrap(err).Op(op)
	}

	output := &dto.ListOperationsOutput{
		Operations: []*dto.OperationOutput{},
		Total:      total,
	}

	if total == 0 {
		return output, nil
	}

	operations, err := query.Limit(limit).Offset(offset).All(ctx)
	if err != nil {
		return nil, berr.ErrListFailed.Wrap(err).Op(op)
	}

	err = cast.ConvertViaJson(&output.Operations, operations)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	return output, nil
}

func (m *OperationModel) UpdateOperation(ctx context.Context, input *dto.UpdateOperationInput) (*dto.OperationOutput, error) {
	op := "OperationModel.UpdateOperation"

	operationRecord, err := m.DbClient.Operation.Get(ctx, input.ID)
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, berr.ErrNotFound.Wrap(err).Op(op)
		}
		return nil, berr.ErrDB.Wrap(err).Op(op)
	}

	// Apply changes from DTO to the fetched record
	err = cast.InputToEnt(operationRecord, input)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	updatedOperation, err := m.DbClient.Operation.UpdateOneID(input.ID).
		SetOperation(operationRecord, input.Modified...). // Using SetOperation pattern
		Save(ctx)

	if err != nil {
		return nil, berr.ErrUpdateFailed.Wrap(err).Op(op)
	}

	m.Logger.WithContext(ctx).Infof("%s - Operation %d updated", op, input.ID)

	output := new(dto.OperationOutput)
	err = cast.ConvertViaJson(output, updatedOperation)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	return output, nil
}

func (m *OperationModel) DeleteOperation(ctx context.Context, id int) error {
	op := "OperationModel.DeleteOperation"

	_, err := m.DbClient.Operation.Get(ctx, id)
	if err != nil {
		if ent.IsNotFound(err) {
			return berr.ErrNotFound.Wrap(err).Op(op)
		}
		return berr.ErrDB.Wrap(err).Op(op)
	}

	err = m.DbClient.Operation.DeleteOneID(id).Exec(ctx)
	if err != nil {
		return berr.ErrDeleteFailed.Wrap(err).Op(op)
	}

	m.Logger.WithContext(ctx).Infof("%s - Operation %d deleted", op, id)
	return nil
}
