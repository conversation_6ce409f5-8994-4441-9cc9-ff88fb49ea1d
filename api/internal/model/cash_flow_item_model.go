package model

import (
	"bcare/api/internal/dto"
	"bcare/common/berr"
	"bcare/common/bquery"
	"bcare/common/cast"
	"bcare/common/ctxdata"
	"bcare/ent"
	"bcare/ent/cashflowitem"
	"bcare/ent/department"
	"bcare/ent/term"
	"context"

	"github.com/stephenafamo/bob"
	"github.com/zeromicro/go-zero/core/logx"
)

type CashFlowItemModel struct {
	Logger   logx.Logger
	DbClient *ent.Client
	Bob      bob.DB
}

func NewCashFlowItemModel(dbClient *ent.Client, bobDB bob.DB) *CashFlowItemModel {
	logger := logx.WithContext(context.Background()).WithFields(logx.Field("model", "CashFlowItemModel"))
	return &CashFlowItemModel{
		Logger:   logger,
		DbClient: dbClient,
		Bob:      bobDB,
	}
}

func (m *CashFlowItemModel) CreateCashFlowItem(ctx context.Context, input *dto.CreateCashFlowItemInput) (*dto.CashFlowItemWithRelationsOutput, error) {
	op := "CashFlowItemModel.CreateCashFlowItem"
	userID := ctxdata.GetUidFromCtx(ctx)

	cashFlowItemRecord := new(ent.CashFlowItem)

	err := cast.CopyInputToEnt(cashFlowItemRecord, input)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	createdCashFlowItem, err := m.DbClient.CashFlowItem.Create().
		SetCashFlowItem(cashFlowItemRecord).
		Save(ctx)

	if err != nil {
		return nil, berr.ErrCreateFailed.Wrap(err).Op(op)
	}

	m.Logger.WithContext(ctx).Infof("%s - CashFlowItem %d created by user %d", op, createdCashFlowItem.ID, userID)

	return m.getCashFlowItemWithRelations(ctx, createdCashFlowItem.ID)
}

func (m *CashFlowItemModel) GetCashFlowItem(ctx context.Context, input dto.GetCashFlowItemInput) (*dto.CashFlowItemWithRelationsOutput, error) {
	op := "CashFlowItemModel.GetCashFlowItem"

	if input.IncludeRelation {
		return m.getCashFlowItemWithRelations(ctx, input.ID)
	}

	cashFlowItemRecord, err := m.DbClient.CashFlowItem.Get(ctx, input.ID)
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, berr.ErrNotFound.Wrap(err).Op(op)
		}
		return nil, berr.ErrDB.Wrap(err).Op(op)
	}

	output := new(dto.CashFlowItemWithRelationsOutput)
	err = cast.ConvertViaJson(output, cashFlowItemRecord)
	if err != nil {
		m.Logger.WithContext(ctx).Errorf("%s - Convert output failed: %v", op, err)
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	return output, nil
}

func (m *CashFlowItemModel) getCashFlowItemWithRelations(ctx context.Context, id int) (*dto.CashFlowItemWithRelationsOutput, error) {
	op := "CashFlowItemModel.getCashFlowItemWithRelations"

	cashFlowItemRecord, err := m.DbClient.CashFlowItem.Query().
		Where(cashflowitem.ID(id)).
		WithCategory(func(q *ent.TermQuery) {
			q.Select(term.FieldID, term.FieldName, term.FieldBundle, term.FieldDescription)
		}).
		WithDepartment(func(q *ent.DepartmentQuery) {
			q.Select(department.FieldID, department.FieldName, department.FieldDescription)
		}).
		Only(ctx)

	if err != nil {
		if ent.IsNotFound(err) {
			return nil, berr.ErrNotFound.Wrap(err).Op(op)
		}
		return nil, berr.ErrDB.Wrap(err).Op(op)
	}

	output := new(dto.CashFlowItemWithRelationsOutput)
	err = cast.ConvertViaJson(output, cashFlowItemRecord)
	if err != nil {
		m.Logger.WithContext(ctx).Errorf("%s - Convert output failed: %v", op, err)
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	return output, nil
}

func (m *CashFlowItemModel) ListCashFlowItems(ctx context.Context, input dto.ListCashFlowItemsInput) (*dto.ListCashFlowItemsOutput, error) {
	op := "CashFlowItemModel.ListCashFlowItems"

	limit, offset := bquery.Paging(input.PageSize, input.Page)
	order := bquery.Ordering(input.OrderBy)

	query := m.DbClient.CashFlowItem.Query().Order(order)

	if input.CashFlowID != 0 {
		query = query.Where(cashflowitem.CashFlowID(input.CashFlowID))
	}
	if input.CategoryID != 0 {
		query = query.Where(cashflowitem.CategoryID(input.CategoryID))
	}
	if input.DepartmentID != 0 {
		query = query.Where(cashflowitem.DepartmentID(input.DepartmentID))
	}

	total, err := query.Clone().Count(ctx)
	if err != nil {
		return nil, berr.ErrListFailed.Wrap(err).Op(op)
	}

	totalPage := (total + limit - 1) / limit

	output := &dto.ListCashFlowItemsOutput{
		Items:     []*dto.CashFlowItemWithRelationsOutput{},
		Total:     total,
		TotalPage: totalPage,
	}

	if total == 0 {
		return output, nil
	}

	cashFlowItems, err := query.
		WithCategory(func(q *ent.TermQuery) {
			q.Select(term.FieldID, term.FieldName, term.FieldBundle, term.FieldDescription)
		}).
		WithDepartment(func(q *ent.DepartmentQuery) {
			q.Select(department.FieldID, department.FieldName, department.FieldDescription)
		}).
		Limit(limit).
		Offset(offset).
		All(ctx)

	if err != nil {
		return nil, berr.ErrListFailed.Wrap(err).Op(op)
	}

	err = cast.ConvertViaJson(&output.Items, cashFlowItems)
	if err != nil {
		m.Logger.WithContext(ctx).Errorf("%s - Convert output failed: %v", op, err)
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	return output, nil
}

func (m *CashFlowItemModel) UpdateCashFlowItem(ctx context.Context, input *dto.UpdateCashFlowItemInput) (*dto.CashFlowItemWithRelationsOutput, error) {
	op := "CashFlowItemModel.UpdateCashFlowItem"
	userID := ctxdata.GetUidFromCtx(ctx)

	cashFlowItemRecord, err := m.DbClient.CashFlowItem.Get(ctx, input.ID)
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, berr.ErrNotFound.Wrap(err).Op(op)
		}
		return nil, berr.ErrDB.Wrap(err).Op(op)
	}

	err = cast.CopyInputToEnt(cashFlowItemRecord, input)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	updatedCashFlowItem, err := m.DbClient.CashFlowItem.UpdateOne(cashFlowItemRecord).
		SetCashFlowItem(cashFlowItemRecord, input.Modified...).
		Save(ctx)

	if err != nil {
		return nil, berr.ErrUpdateFailed.Wrap(err).Op(op)
	}

	m.Logger.WithContext(ctx).Infof("%s - CashFlowItem %d updated by user %d", op, input.ID, userID)

	return m.getCashFlowItemWithRelations(ctx, updatedCashFlowItem.ID)
}

func (m *CashFlowItemModel) DeleteCashFlowItem(ctx context.Context, input dto.DeleteCashFlowItemInput) error {
	op := "CashFlowItemModel.DeleteCashFlowItem"
	userID := ctxdata.GetUidFromCtx(ctx)

	_, err := m.DbClient.CashFlowItem.Get(ctx, input.ID)
	if err != nil {
		if ent.IsNotFound(err) {
			return berr.ErrNotFound.Wrap(err).Op(op)
		}
		return berr.ErrDB.Wrap(err).Op(op)
	}

	err = m.DbClient.CashFlowItem.DeleteOneID(input.ID).Exec(ctx)
	if err != nil {
		return berr.ErrDeleteFailed.Wrap(err).Op(op)
	}

	m.Logger.WithContext(ctx).Infof("%s - CashFlowItem %d deleted by user %d", op, input.ID, userID)
	return nil
}

func (m *CashFlowItemModel) ListCashFlowItemsByCashFlowIDs(ctx context.Context, cashFlowIDs []int) ([]*dto.CashFlowItemWithRelationsOutput, error) {
	op := "CashFlowItemModel.ListCashFlowItemsByCashFlowIDs"

	if len(cashFlowIDs) == 0 {
		return []*dto.CashFlowItemWithRelationsOutput{}, nil
	}

	cashFlowItems, err := m.DbClient.CashFlowItem.Query().
		Where(cashflowitem.CashFlowIDIn(cashFlowIDs...)).
		WithCategory(func(q *ent.TermQuery) {
			q.Select(term.FieldID, term.FieldName, term.FieldBundle, term.FieldDescription)
		}).
		WithDepartment(func(q *ent.DepartmentQuery) {
			q.Select(department.FieldID, department.FieldName, department.FieldDescription)
		}).
		Order(ent.Asc(cashflowitem.FieldCashFlowID), ent.Asc(cashflowitem.FieldOrder)).
		All(ctx)

	if err != nil {
		return nil, berr.ErrListFailed.Wrap(err).Op(op)
	}

	var output []*dto.CashFlowItemWithRelationsOutput
	err = cast.ConvertViaJson(&output, cashFlowItems)
	if err != nil {
		m.Logger.WithContext(ctx).Errorf("%s - Convert output failed: %v", op, err)
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	return output, nil
}

func (m *CashFlowItemModel) CreateCashFlowItems(ctx context.Context, cashFlowID int, items []dto.CreateCashFlowItemInput) ([]*dto.CashFlowItemWithRelationsOutput, error) {
	op := "CashFlowItemModel.CreateCashFlowItems"
	userID := ctxdata.GetUidFromCtx(ctx)

	if len(items) == 0 {
		return []*dto.CashFlowItemWithRelationsOutput{}, nil
	}

	bulk := make([]*ent.CashFlowItemCreate, len(items))
	for i, item := range items {
		itemRecord := new(ent.CashFlowItem)
		itemRecord.CashFlowID = cashFlowID
		itemRecord.CategoryID = item.CategoryID
		itemRecord.DepartmentID = &item.DepartmentID
		itemRecord.Amount = item.Amount
		itemRecord.Note = item.Note
		itemRecord.Order = item.Order
		itemRecord.HasVat = item.HasVat

		bulk[i] = m.DbClient.CashFlowItem.Create().SetCashFlowItem(itemRecord)
	}

	createdItems, err := m.DbClient.CashFlowItem.CreateBulk(bulk...).Save(ctx)
	if err != nil {
		return nil, berr.ErrCreateFailed.Wrap(err).Op(op)
	}

	m.Logger.WithContext(ctx).Infof("%s - %d CashFlowItems created for CashFlow %d by user %d", op, len(createdItems), cashFlowID, userID)

	itemIDs := make([]int, len(createdItems))
	for i, item := range createdItems {
		itemIDs[i] = item.ID
	}

	return m.getCashFlowItemsByIDs(ctx, itemIDs)
}

func (m *CashFlowItemModel) getCashFlowItemsByIDs(ctx context.Context, ids []int) ([]*dto.CashFlowItemWithRelationsOutput, error) {
	op := "CashFlowItemModel.getCashFlowItemsByIDs"

	if len(ids) == 0 {
		return []*dto.CashFlowItemWithRelationsOutput{}, nil
	}

	cashFlowItems, err := m.DbClient.CashFlowItem.Query().
		Where(cashflowitem.IDIn(ids...)).
		WithCategory(func(q *ent.TermQuery) {
			q.Select(term.FieldID, term.FieldName, term.FieldBundle, term.FieldDescription)
		}).
		WithDepartment(func(q *ent.DepartmentQuery) {
			q.Select(department.FieldID, department.FieldName, department.FieldDescription)
		}).
		Order(ent.Asc(cashflowitem.FieldOrder)).
		All(ctx)

	if err != nil {
		return nil, berr.ErrListFailed.Wrap(err).Op(op)
	}

	var output []*dto.CashFlowItemWithRelationsOutput
	err = cast.ConvertViaJson(&output, cashFlowItems)
	if err != nil {
		m.Logger.WithContext(ctx).Errorf("%s - Convert output failed: %v", op, err)
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	return output, nil
}
