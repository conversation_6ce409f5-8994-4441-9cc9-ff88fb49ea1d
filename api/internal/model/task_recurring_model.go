package model

import (
	"bcare/api/internal/dto"
	"bcare/common/berr"
	"bcare/common/butils"
	"bcare/common/cast"
	"bcare/ent"
	"bcare/ent/taskrecurring"
	"context"
	"github.com/zeromicro/go-zero/core/logx"
	"time"
)

type TaskRecurringModel struct {
	Logger   logx.Logger
	DbClient *ent.Client
}

func NewTaskRecurringModel(dbClient *ent.Client) *TaskRecurringModel {
	logger := logx.WithContext(context.Background()).WithFields(logx.Field("model", "TaskRecurringModel"))
	return &TaskRecurringModel{
		Logger:   logger,
		DbClient: dbClient,
	}
}

func (m *TaskRecurringModel) CreateTaskRecurring(ctx context.Context, taskRecord *ent.Task, input *dto.CreateTaskRecurringInput) (*dto.TaskRecurringOutput, error) {
	op := "TaskRecurringModel.CreateTaskRecurring"
	record := new(ent.TaskRecurring)
	if err := cast.CopyInputToEnt(input, record); err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}
	dueAt := butils.CalculateNextRunTime(input.CronExpression, taskRecord.StartDate, nil)
	nextOccurrence := butils.CalculateNextRunTime(input.CronExpression, dueAt, taskRecord.EndDate)

	created, err := m.DbClient.TaskRecurring.Create().
		SetNextOccurrence(nextOccurrence).
		SetLastOccurrence(dueAt).
		SetTaskID(taskRecord.ID).
		SetCronExpression(input.CronExpression).
		Save(ctx)
	if err != nil {
		return nil, berr.ErrCreateFailed.Wrap(err).Op(op)
	}
	m.Logger.WithContext(ctx).Infof("%s - TaskRecurring %d created", op, created.ID)
	output := new(dto.TaskRecurringOutput)
	if err := cast.ConvertViaJson(output, created); err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}
	return output, nil
}

func (m *TaskRecurringModel) GetTaskRecurring(ctx context.Context, id int) (*dto.TaskRecurringOutput, error) {
	op := "TaskRecurringModel.GetTaskRecurring"
	record, err := m.DbClient.TaskRecurring.Query().Where(taskrecurring.ID(id)).First(ctx)
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, berr.ErrNotFound.Wrap(err).Op(op)
		}
		return nil, berr.ErrDB.Wrap(err).Op(op)
	}
	output := new(dto.TaskRecurringOutput)
	if err := cast.ConvertViaJson(output, record); err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}
	return output, nil
}

func (m *TaskRecurringModel) UpdateTaskRecurring(ctx context.Context, id int, taskRecord *ent.Task, dueAt time.Time) (*dto.TaskRecurringOutput, error) {
	op := "TaskRecurringModel.UpdateTaskRecurring"
	record, err := m.DbClient.TaskRecurring.Query().Where(taskrecurring.ID(id)).First(ctx)
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, berr.ErrNotFound.Wrap(err).Op(op)
		}
		return nil, berr.ErrDB.Wrap(err).Op(op)
	}

	nextOccurrence := butils.CalculateNextRunTime(taskRecord.Edges.Recurring.CronExpression, dueAt, taskRecord.EndDate)

	updated, err := m.DbClient.TaskRecurring.
		UpdateOne(record).
		SetNextOccurrence(nextOccurrence).
		SetLastOccurrence(dueAt).
		Save(ctx)
	if err != nil {
		return nil, berr.ErrUpdateFailed.Wrap(err).Op(op)
	}

	output := new(dto.TaskRecurringOutput)
	if err := cast.ConvertViaJson(output, updated); err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}
	return output, nil
}
