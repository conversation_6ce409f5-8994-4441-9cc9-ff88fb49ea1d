package model

import (
	"bcare/api/internal/dto"
	"bcare/common/berr"
	"bcare/common/bquery"
	"bcare/common/cast"
	"bcare/common/ctxdata"
	"bcare/ent"
	"bcare/ent/file"
	"context"
	"github.com/zeromicro/go-zero/core/logx"
)

type FileModel struct {
	Logger   logx.Logger
	DbClient *ent.Client
}

func NewFileModel(dbClient *ent.Client) *FileModel {
	logger := logx.WithContext(context.Background()).WithFields(logx.Field("model", "FileModel"))
	return &FileModel{
		Logger:   logger,
		DbClient: dbClient,
	}
}

func (m *FileModel) CreateFile(ctx context.Context, input *dto.CreateFileInput) (*dto.FileOutput, error) {
	op := "FileModel.CreateFile"
	userID := ctxdata.GetUidFromCtx(ctx)

	fileRecord := new(ent.File)

	err := cast.InputToEnt(fileRecord, input)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	fileRecord.UserID = userID

	createdFile, err := m.DbClient.File.Create().
		SetFile(fileRecord).
		Save(ctx)

	if err != nil {
		return nil, berr.ErrCreateFailed.Wrap(err).Op(op)
	}

	m.Logger.WithContext(ctx).Infof("%s - File %d created by user %d", op, createdFile.ID, userID)

	output := new(dto.FileOutput)
	err = cast.ConvertViaJson(output, createdFile)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}
	return output, nil
}

func (m *FileModel) GetFile(ctx context.Context, id int) (*dto.FileOutput, error) {
	op := "FileModel.GetFile"

	fileRecord, err := m.DbClient.File.Get(ctx, id)
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, berr.ErrNotFound.Wrap(err).Op(op)
		}
		return nil, berr.ErrDB.Wrap(err).Op(op)
	}

	output := new(dto.FileOutput)
	err = cast.ConvertViaJson(output, fileRecord)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	return output, nil
}

func (m *FileModel) ListFiles(ctx context.Context, input dto.ListFilesInput) (*dto.ListFilesOutput, error) {
	op := "FileModel.ListFiles"

	limit, offset := bquery.Paging(input.PageSize, input.Page)
	order := bquery.Ordering(input.OrderBy)

	query := m.DbClient.File.Query().Order(order)

	if input.Filter.Name != "" {
		query = query.Where(file.NameEQ(input.Filter.Name))
	}
	if input.Filter.Kind != "" {
		query = query.Where(file.KindEQ(input.Filter.Kind))
	}
	if input.Filter.Type != "" {
		query = query.Where(file.TypeEQ(input.Filter.Type))
	}
	if input.Filter.UserID != 0 {
		query = query.Where(file.UserID(input.Filter.UserID))
	}
	if input.Filter.Storage != "" {
		query = query.Where(file.StorageEQ(input.Filter.Storage))
	}

	total, err := query.Clone().Count(ctx)
	if err != nil {
		return nil, berr.ErrListFailed.Wrap(err).Op(op)
	}

	output := &dto.ListFilesOutput{
		Files: []*dto.FileOutput{},
		Total: total,
	}

	if total == 0 {
		return output, nil
	}

	files, err := query.Limit(limit).Offset(offset).All(ctx)
	if err != nil {
		return nil, berr.ErrListFailed.Wrap(err).Op(op)
	}

	err = cast.ConvertViaJson(&output.Files, files)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	return output, nil
}

func (m *FileModel) UpdateFile(ctx context.Context, input *dto.UpdateFileInput) (*dto.FileOutput, error) {
	op := "FileModel.UpdateFile"

	fileRecord, err := m.DbClient.File.Get(ctx, input.ID)
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, berr.ErrNotFound.Wrap(err).Op(op)
		}
		return nil, berr.ErrDB.Wrap(err).Op(op)
	}

	err = cast.InputToEnt(fileRecord, input)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	updatedFile, err := m.DbClient.File.UpdateOne(fileRecord).
		SetFile(fileRecord, input.Modified...).
		Save(ctx)

	if err != nil {
		return nil, berr.ErrUpdateFailed.Wrap(err).Op(op)
	}

	m.Logger.WithContext(ctx).Infof("%s - File %d updated", op, input.ID)

	output := new(dto.FileOutput)
	err = cast.ConvertViaJson(output, updatedFile)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	return output, nil
}

func (m *FileModel) DeleteFile(ctx context.Context, id int) error {
	op := "FileModel.DeleteFile"

	_, err := m.DbClient.File.Get(ctx, id)
	if err != nil {
		if ent.IsNotFound(err) {
			return berr.ErrNotFound.Wrap(err).Op(op)
		}
		return berr.ErrDB.Wrap(err).Op(op)
	}

	err = m.DbClient.File.DeleteOneID(id).Exec(ctx)
	if err != nil {
		return berr.ErrDeleteFailed.Wrap(err).Op(op)
	}

	m.Logger.WithContext(ctx).Infof("%s - File %d deleted", op, id)
	return nil
}
