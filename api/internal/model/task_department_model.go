package model

import (
	"bcare/api/internal/dto"
	"bcare/common/berr"
	"bcare/common/bquery"
	"bcare/common/cast"
	"bcare/common/ctxdata"
	"bcare/ent"
	"bcare/ent/taskdepartment"
	"context"
	"time"

	"github.com/zeromicro/go-zero/core/logx"
)

type TaskDepartmentModel struct {
	Logger   logx.Logger
	DbClient *ent.Client
}

func NewTaskDepartmentModel(dbClient *ent.Client) *TaskDepartmentModel {
	logger := logx.WithContext(context.Background()).WithFields(logx.Field("model", "TaskDepartmentModel"))
	return &TaskDepartmentModel{
		Logger:   logger,
		DbClient: dbClient,
	}
}

func (m *TaskDepartmentModel) CreateBulk(ctx context.Context, tx *ent.Tx, taskRecord *ent.Task, assignments []dto.CreateTaskDepartmentInput, dueAt time.Time) error {
	op := "TaskDepartmentModel.CreateBulk"
	bulkDepartmentAssigns := make([]*ent.TaskDepartmentCreate, len(assignments))
	for i, a := range assignments {
		departmentAssignRecord := new(ent.TaskDepartment)
		if err := cast.CopyInputToEnt(departmentAssignRecord, a); err != nil {
			return berr.ErrCopyFailed.Wrap(err).Op(op)
		}

		SetTaskDepartmentCommonFields(departmentAssignRecord, taskRecord, dueAt)
		bulkDepartmentAssigns[i] = tx.TaskDepartment.Create().SetTaskDepartment(departmentAssignRecord)
	}

	_, err := tx.TaskDepartment.CreateBulk(bulkDepartmentAssigns...).Save(ctx)
	if err != nil {
		return berr.ErrCreateFailed.Wrap(err).Op(op)
	}

	m.Logger.WithContext(ctx).Infof("%s - Created %d department assignments for task %d", op, len(assignments), taskRecord.ID)
	return nil
}

func (m *TaskDepartmentModel) ListByTaskID(ctx context.Context, taskID int64) ([]*ent.TaskDepartment, error) {
	op := "TaskDepartmentModel.ListByTaskID"
	departments, err := m.DbClient.TaskDepartment.Query().
		Where(taskdepartment.TaskID(int(taskID))).
		All(ctx)

	if err != nil {
		return nil, berr.ErrDB.Wrap(err).Op(op)
	}

	return departments, nil
}

func (m *TaskDepartmentModel) UpdateDueAt(ctx context.Context, taskID int64, currentSerial int, dueAt time.Time) error {
	op := "TaskDepartmentModel.UpdateDueAt"
	_, err := m.DbClient.TaskDepartment.Update().
		Where(taskdepartment.TaskID(int(taskID)), taskdepartment.Serial(currentSerial)).
		SetDueAt(dueAt).
		Save(ctx)

	if err != nil {
		return berr.ErrUpdateFailed.Wrap(err).Op(op)
	}

	m.Logger.WithContext(ctx).Infof("%s - Updated due_at for task %d department assignments", op, taskID)
	return nil
}

func (m *TaskDepartmentModel) Create(ctx context.Context, input *dto.CreateTaskDepartmentInput) (*dto.TaskDepartmentOutput, error) {
	op := "TaskDepartmentModel.Create"
	userID := ctxdata.GetUidFromCtx(ctx)

	taskDepartmentRecord := new(ent.TaskDepartment)
	err := cast.CopyInputToEnt(taskDepartmentRecord, input)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	createdTaskDepartment, err := m.DbClient.TaskDepartment.Create().
		SetTaskDepartment(taskDepartmentRecord).
		Save(ctx)

	if err != nil {
		return nil, berr.ErrCreateFailed.Wrap(err).Op(op)
	}

	m.Logger.WithContext(ctx).Infof("%s - TaskDepartment %d created by user %d", op, createdTaskDepartment.ID, userID)

	output := new(dto.TaskDepartmentOutput)
	err = cast.ConvertViaJson(output, createdTaskDepartment)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}
	return output, nil
}

func (m *TaskDepartmentModel) Get(ctx context.Context, id int) (*dto.TaskDepartmentOutput, error) {
	op := "TaskDepartmentModel.Get"

	taskDepartmentRecord, err := m.DbClient.TaskDepartment.Query().
		Where(taskdepartment.ID(id)).
		First(ctx)

	if err != nil {
		if ent.IsNotFound(err) {
			return nil, berr.ErrNotFound.Wrap(err).Op(op)
		}
		return nil, berr.ErrDB.Wrap(err).Op(op)
	}

	output := new(dto.TaskDepartmentOutput)
	err = cast.ConvertViaJson(output, taskDepartmentRecord)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	return output, nil
}

func (m *TaskDepartmentModel) List(ctx context.Context, input *dto.ListTaskDepartmentInput) (*dto.ListTaskDepartmentOutput, error) {
	op := "TaskDepartmentModel.List"

	limit, offset := bquery.Paging(input.PageSize, input.Page)
	order := bquery.Ordering(input.OrderBy)

	query := m.DbClient.TaskDepartment.Query().Order(order)

	if input.Filter.TaskID != 0 {
		query = query.Where(taskdepartment.TaskID(input.Filter.TaskID))
	}
	if input.Filter.DepartmentID != 0 {
		query = query.Where(taskdepartment.DepartmentID(input.Filter.DepartmentID))
	}

	total, err := query.Clone().Count(ctx)
	if err != nil {
		return nil, berr.ErrListFailed.Wrap(err).Op(op)
	}

	output := &dto.ListTaskDepartmentOutput{
		TaskDepartments: []dto.TaskDepartmentOutput{},
		Total:           total,
		TotalPage:       (total + input.PageSize - 1) / input.PageSize,
	}

	if total == 0 {
		return output, nil
	}

	taskDepartments, err := query.Limit(limit).Offset(offset).All(ctx)
	if err != nil {
		return nil, berr.ErrListFailed.Wrap(err).Op(op)
	}

	err = cast.ConvertViaJson(&output.TaskDepartments, taskDepartments)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	return output, nil
}

func (m *TaskDepartmentModel) Update(ctx context.Context, input *dto.UpdateTaskDepartmentInput) (*dto.TaskDepartmentOutput, error) {
	op := "TaskDepartmentModel.Update"
	userID := ctxdata.GetUidFromCtx(ctx)

	taskDepartmentRecord := new(ent.TaskDepartment)
	err := cast.CopyInputToEnt(taskDepartmentRecord, input)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	updatedTaskDepartment, err := m.DbClient.TaskDepartment.UpdateOneID(input.ID).
		SetTaskDepartment(taskDepartmentRecord).
		Save(ctx)

	if err != nil {
		if ent.IsNotFound(err) {
			return nil, berr.ErrNotFound.Wrap(err).Op(op)
		}
		return nil, berr.ErrUpdateFailed.Wrap(err).Op(op)
	}

	m.Logger.WithContext(ctx).Infof("%s - TaskDepartment %d updated by user %d", op, updatedTaskDepartment.ID, userID)

	output := new(dto.TaskDepartmentOutput)
	err = cast.ConvertViaJson(output, updatedTaskDepartment)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	return output, nil
}

func (m *TaskDepartmentModel) Delete(ctx context.Context, id int) (*dto.TaskDepartmentOutput, error) {
	op := "TaskDepartmentModel.Delete"
	userID := ctxdata.GetUidFromCtx(ctx)

	taskDepartmentRecord, err := m.DbClient.TaskDepartment.Get(ctx, id)
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, berr.ErrNotFound.Wrap(err).Op(op)
		}
		return nil, berr.ErrDB.Wrap(err).Op(op)
	}

	err = m.DbClient.TaskDepartment.DeleteOne(taskDepartmentRecord).Exec(ctx)
	if err != nil {
		return nil, berr.ErrDeleteFailed.Wrap(err).Op(op)
	}

	m.Logger.WithContext(ctx).Infof("%s - TaskDepartment %d deleted by user %d", op, taskDepartmentRecord.ID, userID)

	output := new(dto.TaskDepartmentOutput)
	err = cast.ConvertViaJson(output, taskDepartmentRecord)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	return output, nil
}

func (m *TaskDepartmentModel) BulkDelete(ctx context.Context, taskId int64) error {
	op := "TaskDepartmentModel.BulkDelete"
	_, err := m.DbClient.TaskDepartment.Delete().Where(taskdepartment.TaskID(int(taskId))).Exec(ctx)
	if err != nil {
		return berr.ErrDeleteFailed.Wrap(err).Op(op)
	}

	m.Logger.WithContext(ctx).Infof("%s - TaskDepartments for task %d deleted", op, taskId)
	return nil
}

func (m *TaskDepartmentModel) UpdateTaskDepartmentAssignments(ctx context.Context, tx *ent.Tx, taskRecord *ent.Task, assignments []dto.CreateTaskDepartmentInput, dueAt time.Time) error {
	op := "TaskDepartmentModel.UpdateTaskDepartmentAssignments"

	// Delete existing department assignments
	_, err := tx.TaskDepartment.Delete().
		Where(taskdepartment.TaskID(taskRecord.ID)).
		Exec(ctx)
	if err != nil {
		return berr.ErrDeleteFailed.Wrap(err).Op(op)
	}

	// Create new department assignments
	bulkDeptAssigns := make([]*ent.TaskDepartmentCreate, len(assignments))
	for i, assignment := range assignments {
		departmentAssignRecord := new(ent.TaskDepartment)
		departmentAssignRecord.TaskID = taskRecord.ID
		departmentAssignRecord.DepartmentID = assignment.DepartmentID
		departmentAssignRecord.Role = taskdepartment.Role(assignment.Role)
		departmentAssignRecord.DueAt = dueAt
		departmentAssignRecord.Status = taskRecord.Status
		departmentAssignRecord.Serial = taskRecord.CurrentSerial
		departmentAssignRecord.State = taskdepartment.StateNewTask

		bulkDeptAssigns[i] = tx.TaskDepartment.Create().SetTaskDepartment(departmentAssignRecord)
	}

	_, err = tx.TaskDepartment.CreateBulk(bulkDeptAssigns...).Save(ctx)
	if err != nil {
		return berr.ErrCreateFailed.Wrap(err).Op(op)
	}

	m.Logger.WithContext(ctx).Infof("%s - Updated department assignments for task %d", op, taskRecord.ID)
	return nil
}

func (m *TaskDepartmentModel) UpdateAllTaskAssignmentsDueAt(ctx context.Context, tx *ent.Tx, taskRecord *ent.Task, dueAt time.Time) error {
	op := "TaskDepartmentModel.UpdateAllTaskAssignmentsDueAt"

	// Update due_at for all department assignments of the current serial
	_, err := tx.TaskDepartment.Update().
		Where(
			taskdepartment.TaskID(taskRecord.ID),
			taskdepartment.Serial(taskRecord.CurrentSerial),
		).
		SetDueAt(dueAt).
		Save(ctx)

	if err != nil {
		return berr.ErrUpdateFailed.Wrap(err).Op(op)
	}

	m.Logger.WithContext(ctx).Infof("%s - Updated due_at for all department assignments of task %d", op, taskRecord.ID)
	return nil
}

func (m *TaskDepartmentModel) UpdateStateAndCompletedAt(ctx context.Context, tx *ent.Tx, taskRecord *ent.Task) error {
	op := "TaskDepartmentModel.UpdateStateAndCompletedAt"

	// Get all department assignments for the current serial
	assignments, err := tx.TaskDepartment.Query().
		Where(
			taskdepartment.TaskID(taskRecord.ID),
			taskdepartment.Serial(taskRecord.CurrentSerial),
		).
		All(ctx)
	if err != nil {
		return berr.ErrDB.Wrap(err).Op(op)
	}

	// Update state and completed_at for each assignment
	for _, assignment := range assignments {
		update := tx.TaskDepartment.UpdateOne(assignment).
			SetState(taskdepartment.State(taskRecord.State))

		if IsCompletionState(taskRecord.State) {
			now := time.Now()
			update.SetCompletedAt(now)
		} else {
			update.ClearCompletedAt()
		}

		_, err := update.Save(ctx)
		if err != nil {
			return berr.ErrUpdateFailed.Wrap(err).Op(op)
		}
	}

	m.Logger.WithContext(ctx).Infof("%s - Updated state and completed_at for task %d department assignments", op, taskRecord.ID)
	return nil
}
