package model

import (
	"bcare/api/internal/dto"
	"bcare/common/berr"
	"bcare/common/bquery"
	"bcare/common/cast"
	"bcare/common/ctxdata"
	"bcare/ent"
	"bcare/ent/tasknote"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type TaskNoteModel struct {
	Logger   logx.Logger
	DbClient *ent.Client
}

func NewTaskNoteModel(dbClient *ent.Client) *TaskNoteModel {
	logger := logx.WithContext(context.Background()).WithFields(logx.Field("model", "TaskNoteModel"))
	return &TaskNoteModel{
		Logger:   logger,
		DbClient: dbClient,
	}
}

func (m *TaskNoteModel) Create(ctx context.Context, input *dto.CreateTaskNoteInput) (*dto.TaskNoteOutput, error) {
	op := "TaskNoteModel.Create"
	userID := ctxdata.GetUidFromCtx(ctx)

	taskNoteRecord := new(ent.TaskNote)
	err := cast.InputToEnt(taskNoteRecord, input)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	taskNoteRecord.UserID = userID

	createdTaskNote, err := m.DbClient.TaskNote.Create().
		SetTaskNote(taskNoteRecord).
		Save(ctx)

	if err != nil {
		return nil, berr.ErrCreateFailed.Wrap(err).Op(op)
	}

	m.Logger.WithContext(ctx).Infof("%s - TaskNote %d created by user %d", op, createdTaskNote.ID, userID)

	output := new(dto.TaskNoteOutput)
	err = cast.ConvertViaJson(output, createdTaskNote)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}
	return output, nil
}

func (m *TaskNoteModel) Get(ctx context.Context, id int) (*dto.TaskNoteOutput, error) {
	op := "TaskNoteModel.Get"

	taskNoteRecord, err := m.DbClient.TaskNote.Query().
		Where(tasknote.ID(id)).
		First(ctx)

	if err != nil {
		if ent.IsNotFound(err) {
			return nil, berr.ErrNotFound.Wrap(err).Op(op)
		}
		return nil, berr.ErrDB.Wrap(err).Op(op)
	}

	output := new(dto.TaskNoteOutput)
	err = cast.ConvertViaJson(output, taskNoteRecord)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	return output, nil
}

func (m *TaskNoteModel) List(ctx context.Context, input dto.ListTaskNoteInput) (*dto.ListTaskNoteOutput, error) {
	op := "TaskNoteModel.List"

	limit, offset := bquery.Paging(input.PageSize, input.Page)
	order := bquery.Ordering(input.OrderBy)

	query := m.DbClient.TaskNote.Query().WithCreator().Order(order)

	if input.Filter.TaskID != 0 {
		query = query.Where(tasknote.TaskID(input.Filter.TaskID))
	}
	if input.Filter.UserID != 0 {
		query = query.Where(tasknote.UserID(input.Filter.UserID))
	}

	total, err := query.Clone().Count(ctx)
	if err != nil {
		return nil, berr.ErrListFailed.Wrap(err).Op(op)
	}

	output := &dto.ListTaskNoteOutput{
		TaskNotes: []dto.TaskNoteOutput{},
		Total:     total,
		TotalPage: (total + input.PageSize - 1) / input.PageSize,
	}

	if total == 0 {
		return output, nil
	}

	taskNotes, err := query.Limit(limit).Offset(offset).All(ctx)
	if err != nil {
		return nil, berr.ErrListFailed.Wrap(err).Op(op)
	}

	err = cast.ConvertViaJson(&output.TaskNotes, taskNotes)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	return output, nil
}

func (m *TaskNoteModel) Update(ctx context.Context, input *dto.UpdateTaskNoteInput) (*dto.TaskNoteOutput, error) {
	op := "TaskNoteModel.Update"

	taskNoteRecord, err := m.DbClient.TaskNote.Query().
		Where(tasknote.ID(input.ID)).
		First(ctx)

	if err != nil {
		if ent.IsNotFound(err) {
			return nil, berr.ErrNotFound.Wrap(err).Op(op)
		}
		return nil, berr.ErrDB.Wrap(err).Op(op)
	}

	err = cast.InputToEnt(taskNoteRecord, input)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	updatedTaskNote, err := m.DbClient.TaskNote.UpdateOneID(input.ID).
		SetTaskNote(taskNoteRecord, input.Modified...).
		Save(ctx)

	if err != nil {
		return nil, berr.ErrUpdateFailed.Wrap(err).Op(op)
	}

	m.Logger.WithContext(ctx).Infof("%s - TaskNote %d updated", op, input.ID)

	output := new(dto.TaskNoteOutput)
	err = cast.ConvertViaJson(output, updatedTaskNote)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	return output, nil
}

func (m *TaskNoteModel) Delete(ctx context.Context, id int) error {
	op := "TaskNoteModel.Delete"

	_, err := m.DbClient.TaskNote.Get(ctx, id)
	if err != nil {
		if ent.IsNotFound(err) {
			return berr.ErrNotFound.Wrap(err).Op(op)
		}
		return berr.ErrDB.Wrap(err).Op(op)
	}

	err = m.DbClient.TaskNote.DeleteOneID(id).Exec(ctx)
	if err != nil {
		return berr.ErrDeleteFailed.Wrap(err).Op(op)
	}

	m.Logger.WithContext(ctx).Infof("%s - TaskNote %d deleted", op, id)
	return nil
}

func (m *TaskNoteModel) BulkDelete(ctx context.Context, taskId int64) error {
	op := "TaskNoteModel.BulkDelete"
	_, err := m.DbClient.TaskNote.Delete().Where(tasknote.TaskID(int(taskId))).Exec(ctx)
	if err != nil {
		return berr.ErrDeleteFailed.Wrap(err).Op(op)
	}

	m.Logger.WithContext(ctx).Infof("%s - TaskNotes for task %d deleted", op, taskId)
	return nil
}
