package model

import (
	"bcare/api/internal/dto"
	"bcare/common/berr"
	"bcare/common/bquery"
	"bcare/common/cast"
	"bcare/common/ctxdata"
	"bcare/ent"
	"bcare/ent/track"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type TrackModel struct {
	Logger   logx.Logger
	DbClient *ent.Client
}

func NewTrackModel(dbClient *ent.Client) *TrackModel {
	logger := logx.WithContext(context.Background()).WithFields(logx.Field("model", "TrackModel"))
	return &TrackModel{
		Logger:   logger,
		DbClient: dbClient,
	}
}

func (m *TrackModel) Create(ctx context.Context, input *dto.CreateTrackInput) (*dto.TrackOutput, error) {
	op := "TrackModel.Create"
	userID := ctxdata.GetUidFromCtx(ctx)

	trackRecord := new(ent.Track)
	err := cast.InputToEnt(trackRecord, input)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	createdTrack, err := m.DbClient.Track.Create().
		SetTrack(trackRecord).
		Save(ctx)
	if err != nil {
		return nil, berr.ErrCreateFailed.Wrap(err).Op(op)
	}

	m.Logger.WithContext(ctx).Infof("%s - Track %d created by user %d", op, createdTrack.ID, userID)

	output := new(dto.TrackOutput)
	err = cast.ConvertViaJson(output, createdTrack)
	if err != nil {
		m.Logger.WithContext(ctx).Errorf("%s - Failed to convert track output: %v", op, err)
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	return output, nil
}

func (m *TrackModel) Get(ctx context.Context, id int) (*dto.TrackOutput, error) {
	op := "TrackModel.Get"

	trackRecord, err := m.DbClient.Track.Query().
		WithAppointments().
		WithPerson().
		WithDeal().
		Where(track.ID(id)).
		Only(ctx)
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, berr.ErrNotFound.Wrap(err).Op(op)
		}
		return nil, berr.ErrDB.Wrap(err).Op(op)
	}

	output := new(dto.TrackOutput)
	err = cast.ConvertViaJson(output, trackRecord)
	if err != nil {
		m.Logger.WithContext(ctx).Errorf("%s - Failed to convert track output: %v", op, err)
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	return output, nil
}

func (m *TrackModel) List(ctx context.Context, input *dto.ListTrackInput) (*dto.ListTrackOutput, error) {
	op := "TrackModel.List"

	limit, offset := bquery.Paging(input.PageSize, input.Page)
	order := bquery.Ordering(input.OrderBy)

	query := m.DbClient.Track.Query().Order(order)

	if input.IncludeRelation {
		query = query.WithAppointments().WithPerson().WithDeal()
	}

	if input.Filter.PipelineID != nil {
		query = query.Where(track.PipelineID(*input.Filter.PipelineID))
	}
	if input.Filter.DealID != nil {
		query = query.Where(track.DealID(*input.Filter.DealID))
	}
	if input.Filter.PersonID != nil {
		query = query.Where(track.PersonID(*input.Filter.PersonID))
	}
	if input.Filter.StageID != nil {
		query = query.Where(track.StageID(*input.Filter.StageID))
	}
	if input.Filter.Status != nil {
		query = query.Where(track.StatusEQ(int8(*input.Filter.Status)))
	}

	total, err := query.Clone().Count(ctx)
	if err != nil {
		return nil, berr.ErrListFailed.Wrap(err).Op(op)
	}

	output := &dto.ListTrackOutput{
		Tracks:    []dto.TrackOutput{},
		Total:     total,
		TotalPage: (total + limit - 1) / limit,
	}

	if total == 0 {
		return output, nil
	}

	tracks, err := query.Limit(limit).Offset(offset).All(ctx)
	if err != nil {
		return nil, berr.ErrListFailed.Wrap(err).Op(op)
	}

	err = cast.ConvertViaJson(&output.Tracks, tracks)
	if err != nil {
		m.Logger.WithContext(ctx).Errorf("%s - Failed to convert tracks output: %v", op, err)
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	return output, nil
}

func (m *TrackModel) ListByDealIDs(ctx context.Context, dealIDs []int, pipelineIDs []int) ([]dto.TrackOutput, error) {
	op := "TrackModel.ListByDealIDs"

	if len(dealIDs) == 0 {
		return []dto.TrackOutput{}, nil
	}

	query := m.DbClient.Track.Query().
		Where(track.DealIDIn(dealIDs...)).
		WithAppointments()

	if len(pipelineIDs) > 0 {
		query = query.Where(track.PipelineIDIn(pipelineIDs...))
	}

	tracks, err := query.All(ctx)
	if err != nil {
		return nil, berr.ErrDB.Wrap(err).Op(op)
	}

	var outputs []dto.TrackOutput
	err = cast.ConvertViaJson(&outputs, tracks)
	if err != nil {
		m.Logger.WithContext(ctx).Errorf("%s - Failed to convert tracks output: %v", op, err)
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	return outputs, nil
}

func (m *TrackModel) Update(ctx context.Context, input *dto.UpdateTrackInput) (*dto.TrackOutput, error) {
	op := "TrackModel.Update"
	userID := ctxdata.GetUidFromCtx(ctx)

	trackRecord, err := m.DbClient.Track.Get(ctx, input.ID)
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, berr.ErrNotFound.Wrap(err).Op(op)
		}
		return nil, berr.ErrDB.Wrap(err).Op(op)
	}

	err = cast.InputToEnt(trackRecord, input)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	updatedTrack, err := m.DbClient.Track.UpdateOne(trackRecord).
		SetTrack(trackRecord, input.Modified...).
		Save(ctx)
	if err != nil {
		return nil, berr.ErrUpdateFailed.Wrap(err).Op(op)
	}

	m.Logger.WithContext(ctx).Infof("%s - Track %d updated by user %d", op, input.ID, userID)

	output := new(dto.TrackOutput)
	err = cast.ConvertViaJson(output, updatedTrack)
	if err != nil {
		m.Logger.WithContext(ctx).Errorf("%s - Failed to convert track output: %v", op, err)
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	return output, nil
}

func (m *TrackModel) Delete(ctx context.Context, id int) error {
	op := "TrackModel.Delete"
	userID := ctxdata.GetUidFromCtx(ctx)

	_, err := m.DbClient.Track.Get(ctx, id)
	if err != nil {
		if ent.IsNotFound(err) {
			return berr.ErrNotFound.Wrap(err).Op(op)
		}
		return berr.ErrDB.Wrap(err).Op(op)
	}

	err = m.DbClient.Track.DeleteOneID(id).Exec(ctx)
	if err != nil {
		return berr.ErrDeleteFailed.Wrap(err).Op(op)
	}

	m.Logger.WithContext(ctx).Infof("%s - Track %d deleted by user %d", op, id, userID)
	return nil
}
