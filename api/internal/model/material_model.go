package model

import (
	"bcare/api/internal/dto"
	"bcare/common/berr"
	"bcare/common/bquery"
	"bcare/common/cast"
	"bcare/ent"
	"bcare/ent/material"
	"context"
	"github.com/stephenafamo/bob"

	"github.com/zeromicro/go-zero/core/logx"
)

type MaterialModel struct {
	Logger   logx.Logger
	DbClient *ent.Client
	Bob      bob.DB
}

func NewMaterialModel(dbClient *ent.Client, bobDB bob.DB) *MaterialModel {
	logger := logx.WithContext(context.Background()).WithFields(logx.Field("model", "MaterialModel"))
	return &MaterialModel{
		Logger:   logger,
		DbClient: dbClient,
		Bob:      bobDB,
	}
}

func (m *MaterialModel) CreateMaterial(ctx context.Context, input *dto.CreateMaterialInput) (*dto.MaterialOutput, error) {
	op := "MaterialModel.CreateMaterial"
	materialRecord := new(ent.Material)

	err := cast.InputToEnt(materialRecord, input)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	createdMaterial, err := m.DbClient.Material.Create().
		SetMaterial(materialRecord).
		Save(ctx)

	if err != nil {
		return nil, berr.ErrCreateFailed.Wrap(err).Op(op)
	}

	m.Logger.WithContext(ctx).Infof("%s - Material %d created", op, createdMaterial.ID)

	output := new(dto.MaterialOutput)
	err = cast.ConvertViaJson(output, createdMaterial)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}
	return output, nil
}

func (m *MaterialModel) GetMaterial(ctx context.Context, input *dto.GetMaterialInput) (*dto.MaterialOutput, error) {
	op := "MaterialModel.GetMaterial"

	var materialRecord *ent.Material
	var err error

	if input.ID != 0 {
		materialRecord, err = m.DbClient.Material.Get(ctx, input.ID)
	} else if input.Code != "" {
		materialRecord, err = m.DbClient.Material.Query().
			Where(material.CodeEQ(input.Code)).
			Only(ctx)
	} else if input.Name != "" {
		materialRecord, err = m.DbClient.Material.Query().
			Where(material.NameEQ(input.Name)).
			Only(ctx)
	}

	if err != nil {
		if ent.IsNotFound(err) {
			return nil, berr.ErrNotFound.Wrap(err).Op(op)
		}
		return nil, berr.ErrDB.Wrap(err).Op(op)
	}

	output := new(dto.MaterialOutput)
	err = cast.ConvertViaJson(output, materialRecord)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	return output, nil
}

func (m *MaterialModel) ListMaterials(ctx context.Context, input *dto.ListMaterialInput) (*dto.ListMaterialOutput, error) {
	op := "MaterialModel.ListMaterials"

	limit, offset := bquery.Paging(input.PageSize, input.Page)
	order := bquery.Ordering(input.OrderBy)

	query := m.DbClient.Material.Query().Order(order)

	if input.Filter.Code != "" {
		query = query.Where(material.CodeEQ(input.Filter.Code))
	}
	if input.Filter.Name != "" {
		query = query.Where(material.NameEQ(input.Filter.Name))
	}
	if input.Filter.Unit != "" {
		query = query.Where(material.UnitEQ(input.Filter.Unit))
	}
	if input.Filter.Status != 0 {
		query = query.Where(material.StatusEQ(int8(input.Filter.Status)))
	}

	total, err := query.Clone().Count(ctx)
	if err != nil {
		return nil, berr.ErrListFailed.Wrap(err).Op(op)
	}

	output := &dto.ListMaterialOutput{
		Materials: []dto.MaterialOutput{},
		Total:     total,
	}

	if total == 0 {
		return output, nil
	}

	materials, err := query.Limit(limit).Offset(offset).All(ctx)
	if err != nil {
		return nil, berr.ErrListFailed.Wrap(err).Op(op)
	}

	err = cast.ConvertViaJson(&output.Materials, materials)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	return output, nil
}

func (m *MaterialModel) UpdateMaterial(ctx context.Context, input *dto.UpdateMaterialInput) (*dto.MaterialOutput, error) {
	op := "MaterialModel.UpdateMaterial"

	materialRecord, err := m.DbClient.Material.Get(ctx, input.ID)
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, berr.ErrNotFound.Wrap(err).Op(op)
		}
		return nil, berr.ErrDB.Wrap(err).Op(op)
	}

	err = cast.InputToEnt(materialRecord, input)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	updatedMaterial, err := m.DbClient.Material.UpdateOne(materialRecord).
		SetMaterial(materialRecord, input.Modified...).
		Save(ctx)

	if err != nil {
		return nil, berr.ErrUpdateFailed.Wrap(err).Op(op)
	}

	m.Logger.WithContext(ctx).Infof("%s - Material %d updated", op, input.ID)

	output := new(dto.MaterialOutput)
	err = cast.ConvertViaJson(output, updatedMaterial)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	return output, nil
}

func (m *MaterialModel) DeleteMaterial(ctx context.Context, input *dto.DeleteMaterialInput) error {
	op := "MaterialModel.DeleteMaterial"

	materialRecord, err := m.DbClient.Material.Get(ctx, input.ID)
	if err != nil {
		if ent.IsNotFound(err) {
			return berr.ErrNotFound.Wrap(err).Op(op)
		}
		return berr.ErrDB.Wrap(err).Op(op)
	}

	if materialRecord.Name != input.Name {
		return berr.ErrNotFound.Wrap(err).Op(op)
	}

	err = m.DbClient.Material.DeleteOneID(input.ID).Exec(ctx)
	if err != nil {
		return berr.ErrDeleteFailed.Wrap(err).Op(op)
	}

	m.Logger.WithContext(ctx).Infof("%s - Material %d deleted", op, input.ID)
	return nil
}
