package model

import (
	"bcare/api/internal/dto"
	"fmt"
	"math"
	"time"

	"github.com/expr-lang/expr"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"
)

type CalculationDiscountType string

const (
	CalculationDiscountTypePercentage CalculationDiscountType = "percent"
	CalculationDiscountTypeFixed      CalculationDiscountType = "fixed"
	CalculationDiscountTypeTiered     CalculationDiscountType = "tiered"
)

type CalculationDiscountScope string

const (
	CalculationDiscountScopeDeal    CalculationDiscountScope = "deal"
	CalculationDiscountScopeProduct CalculationDiscountScope = "product"
)

type CalculationDiscountUsageType string

const (
	CalculationDiscountUsageMultiple CalculationDiscountUsageType = "multiple"
	CalculationDiscountUsageOneTime  CalculationDiscountUsageType = "one_time"
	CalculationDiscountUsageSpecial  CalculationDiscountUsageType = "special"
)

type (
	DiscountCalculationModel interface {
		ApplyDiscounts(deal *dto.DealOutput, person *dto.PersonOutput, products []*dto.ProductOutput) (totalDiscount float64, err error)
	}

	DiscountCalculation struct {
		dto.DiscountOutput
	}
)

type DiscountCalculator interface {
	Calculate(basePrice float64, discount *DiscountCalculation, env map[string]interface{}) (discountAmount float64, afterDiscount float64)
}

type PercentageDiscount struct{}

func (pd *PercentageDiscount) Calculate(basePrice float64, discount *DiscountCalculation, env map[string]interface{}) (discountAmount float64, afterDiscount float64) {
	discountAmount = basePrice * discount.Value
	afterDiscount = basePrice - discountAmount
	return
}

type FixedDiscount struct{}

func (fd *FixedDiscount) Calculate(basePrice float64, discount *DiscountCalculation, env map[string]interface{}) (discountAmount float64, afterDiscount float64) {
	discountAmount = discount.Value
	afterDiscount = math.Max(basePrice-discountAmount, 0)
	return
}

type TieredDiscount struct{}

func (td *TieredDiscount) Calculate(basePrice float64, discount *DiscountCalculation, env map[string]interface{}) (discountAmount float64, afterDiscount float64) {
	var discountFactor float64
	if basePrice > 200 {
		discountFactor = 0.2
	} else if basePrice > 100 {
		discountFactor = 0.1
	} else {
		discountFactor = 0
	}
	discountAmount = basePrice * discountFactor
	afterDiscount = basePrice - discountAmount
	return
}

type DiscountCalculationManager struct {
	calculators map[CalculationDiscountType]DiscountCalculator
}

func NewDiscountCalculationManager() *DiscountCalculationManager {
	return &DiscountCalculationManager{
		calculators: map[CalculationDiscountType]DiscountCalculator{
			CalculationDiscountTypePercentage: &PercentageDiscount{},
			CalculationDiscountTypeFixed:      &FixedDiscount{},
			CalculationDiscountTypeTiered:     &TieredDiscount{},
		},
	}
}

func (dcm *DiscountCalculationManager) CalculateDiscount(basePrice float64, discount *DiscountCalculation, env map[string]interface{}) (discountAmount float64, afterDiscount float64, err error) {
	calculator, ok := dcm.calculators[CalculationDiscountType(discount.Type)]
	if !ok {
		err = errors.New("Unsupported discount type")
		logx.Error(err)
		return 0, basePrice, err
	}

	discountAmount, afterDiscount = calculator.Calculate(basePrice, discount, env)

	return discountAmount, afterDiscount, nil
}

type DiscountPrioritizer struct {
	discounts []*DiscountCalculation
}

func (dp *DiscountPrioritizer) AddDiscount(d *DiscountCalculation) {
	dp.discounts = append(dp.discounts, d)
}

func (dp *DiscountPrioritizer) GetApplicableDiscounts(env map[string]interface{}) []*DiscountCalculation {
	var applicable []*DiscountCalculation

	for _, d := range dp.discounts {
		if d.Status != 1 {
			continue
		}

		// Parse start and end times from string
		startTime, err := time.Parse(time.RFC3339, d.Start)
		if err != nil {
			logx.Errorw("Failed to parse start time",
				logx.LogField{Key: "error", Value: err},
				logx.LogField{Key: "discount", Value: d.ID},
			)
			continue
		}

		endTime, err := time.Parse(time.RFC3339, d.End)
		if err != nil {
			logx.Errorw("Failed to parse end time",
				logx.LogField{Key: "error", Value: err},
				logx.LogField{Key: "discount", Value: d.ID},
			)
			continue
		}

		if startTime.After(time.Now()) || endTime.Before(time.Now()) {
			continue
		}

		compiledCond, err := expr.Compile(d.Condition, expr.Env(env))
		if err != nil {
			logx.Errorw("Failed to compile condition",
				logx.LogField{Key: "error", Value: err},
				logx.LogField{Key: "discount", Value: d.ID},
			)
			continue
		}

		output, err := expr.Run(compiledCond, env)
		if err != nil {
			logx.Errorw("Failed to run condition",
				logx.LogField{Key: "error", Value: err},
				logx.LogField{Key: "discount", Value: d.ID},
			)
			continue
		}

		if shouldApply, ok := output.(bool); ok && shouldApply {
			applicable = append(applicable, d)
		}
	}

	return applicable
}

func (d *DiscountCalculation) ApplyDiscount(basePrice float64, deal *dto.DealOutput, person *dto.PersonOutput, product *dto.ProductOutput) (shouldApply bool, discountAmount float64, afterDiscount float64, err error) {
	// Parse start and end times from string
	startTime, err := time.Parse(time.RFC3339, d.Start)
	if err != nil {
		logx.Errorw("Failed to parse start time", logx.LogField{Key: "error", Value: err})
		return false, 0, basePrice, err
	}

	endTime, err := time.Parse(time.RFC3339, d.End)
	if err != nil {
		logx.Errorw("Failed to parse end time", logx.LogField{Key: "error", Value: err})
		return false, 0, basePrice, err
	}

	now := time.Now()
	if !startTime.IsZero() && !endTime.IsZero() && (startTime.After(now) || endTime.Before(now)) {
		return false, 0, basePrice, nil
	}

	// Check discount scope
	switch CalculationDiscountScope(d.Scope) {
	case CalculationDiscountScopeDeal:
		if deal == nil {
			// Invalid: Deal discount requires deal information
			return false, 0, basePrice, nil
		}
		if product != nil {
			// Invalid: Deal discount should not have product information
			return false, 0, basePrice, nil
		}
	case CalculationDiscountScopeProduct:
		if product == nil {
			// Invalid: Product discount requires product information
			return false, 0, basePrice, nil
		}
	}

	env := prepareCalculationEnvironment(deal, person)
	if product != nil {
		env["product"] = product
	}
	env["contains"] = func(params ...any) (any, error) {
		if len(params) != 2 {
			return nil, fmt.Errorf("contains function requires 2 parameters")
		}

		arr, ok := params[0].([]string)
		if !ok {
			return false, fmt.Errorf("first parameter must be []string")
		}

		str, ok := params[1].(string)
		if !ok {
			return false, fmt.Errorf("second parameter must be string")
		}

		if arr == nil {
			return false, nil
		}

		for _, v := range arr {
			if v == str {
				return true, nil
			}
		}
		return false, nil
	}

	// Check if condition is empty
	if d.Condition == "" {
		// If condition is empty, we assume the discount should always apply
		shouldApply = true
	} else {
		compiledCond, err := expr.Compile(d.Condition, expr.Env(env))
		if err != nil {
			logx.Errorw("Failed to compile condition", logx.LogField{Key: "error", Value: err})
			return false, 0, basePrice, err
		}

		output, err := expr.Run(compiledCond, env)
		if err != nil {
			logx.Errorw("Failed to run condition", logx.LogField{Key: "error", Value: err})
			return false, 0, basePrice, err
		}

		shouldApply = output.(bool)
	}

	if !shouldApply {
		return false, 0, basePrice, nil
	}

	dcm := NewDiscountCalculationManager()
	discountAmount, afterDiscount, err = dcm.CalculateDiscount(basePrice, d, env)
	if err != nil {
		logx.Errorw("Failed to calculate discount", logx.LogField{Key: "error", Value: err})
	}
	return true, discountAmount, afterDiscount, err
}

func (d *DiscountCalculation) ApplyToDeal(deal *dto.DealOutput, person *dto.PersonOutput) (shouldApplyDiscount bool, discountValue float64, err error) {
	shouldApply, discountAmount, _, err := d.ApplyDiscount(deal.TotalAmount, deal, person, nil)
	if err != nil {
		logx.Errorw("Failed to apply discount to deal", logx.LogField{Key: "error", Value: err})
	}
	return shouldApply, discountAmount, err
}

func (d *DiscountCalculation) ApplyToProduct(product *dto.ProductOutput, deal *dto.DealOutput, person *dto.PersonOutput, discountUsage *dto.DiscountUsageOutput) (shouldApplyDiscount bool, discountValue float64, err error) {
	env := prepareCalculationEnvironment(deal, person)
	env["product"] = product
	if discountUsage != nil {
		env["usage_count"] = discountUsage.UsageCount
	}

	shouldApply, discountAmount, _, err := d.ApplyDiscount(float64(product.Price), deal, person, product)
	if err != nil {
		logx.Errorw("Failed to apply discount to product", logx.LogField{Key: "error", Value: err})
	}
	return shouldApply, discountAmount, err
}

func prepareCalculationEnvironment(deal *dto.DealOutput, person *dto.PersonOutput) map[string]interface{} {
	env := make(map[string]interface{})

	if deal != nil {
		env["deal"] = deal
	}

	if person != nil {
		env["person"] = person
	}

	env["fn"] = map[string]interface{}{
		"contains": func(arr []string, str string) bool {
			if arr == nil {
				return false
			}
			for _, v := range arr {
				if v == str {
					return true
				}
			}
			return false
		},
	}

	return env
}

// NewDiscountCalculation creates a new DiscountCalculation from DiscountOutput
func NewDiscountCalculation(discountOutput dto.DiscountOutput) *DiscountCalculation {
	return &DiscountCalculation{
		DiscountOutput: discountOutput,
	}
}

// ConvertToDiscountCalculations converts a slice of DiscountOutput to DiscountCalculation
func ConvertToDiscountCalculations(discounts []dto.DiscountOutput) []*DiscountCalculation {
	calculations := make([]*DiscountCalculation, len(discounts))
	for i, discount := range discounts {
		calculations[i] = NewDiscountCalculation(discount)
	}
	return calculations
}

// CalculateDiscountsForDeal calculates applicable discounts for a deal
func CalculateDiscountsForDeal(discounts []dto.DiscountOutput, deal *dto.DealOutput, person *dto.PersonOutput) (*dto.CalculateDiscountOutput, error) {
	if deal == nil {
		return nil, errors.New("deal is required")
	}

	calculations := ConvertToDiscountCalculations(discounts)
	totalDiscountAmount := 0.0
	results := make([]dto.DiscountResult, 0)
	discountAmounts := make(map[int]float64)

	for _, calc := range calculations {
		if CalculationDiscountScope(calc.Scope) != CalculationDiscountScopeDeal {
			continue
		}

		shouldApply, discountValue, err := calc.ApplyToDeal(deal, person)
		if err != nil {
			logx.Errorw("Failed to apply discount to deal",
				logx.LogField{Key: "error", Value: err},
				logx.LogField{Key: "discount_id", Value: calc.ID},
			)
			continue
		}

		if shouldApply {
			totalDiscountAmount += discountValue
			discountAmounts[calc.ID] = discountValue

			result := dto.DiscountResult{
				DiscountID:  calc.ID,
				TotalAmount: discountValue,
				Details: []dto.DiscountDetail{
					{
						Amount:    discountValue,
						AppliedTo: "deal",
					},
				},
			}
			results = append(results, result)
		}
	}

	return &dto.CalculateDiscountOutput{
		TotalDiscountAmount: totalDiscountAmount,
		Results:             results,
		DiscountAmounts:     discountAmounts,
	}, nil
}

// CalculateDiscountsForProduct calculates applicable discounts for a product
func CalculateDiscountsForProduct(discounts []dto.DiscountOutput, product *dto.ProductOutput, deal *dto.DealOutput, person *dto.PersonOutput, quantity int) (*dto.CalculateDiscountOutput, error) {
	if product == nil {
		return nil, errors.New("product is required")
	}

	calculations := ConvertToDiscountCalculations(discounts)
	totalDiscountAmount := 0.0
	results := make([]dto.DiscountResult, 0)
	discountAmounts := make(map[int]float64)

	for _, calc := range calculations {
		if CalculationDiscountScope(calc.Scope) != CalculationDiscountScopeProduct {
			continue
		}

		shouldApply, discountValue, err := calc.ApplyToProduct(product, deal, person, nil)
		if err != nil {
			logx.Errorw("Failed to apply discount to product",
				logx.LogField{Key: "error", Value: err},
				logx.LogField{Key: "discount_id", Value: calc.ID},
			)
			continue
		}

		if shouldApply {
			// Apply discount per quantity
			totalDiscountForProduct := discountValue * float64(quantity)
			totalDiscountAmount += totalDiscountForProduct
			discountAmounts[calc.ID] = totalDiscountForProduct

			result := dto.DiscountResult{
				DiscountID:  calc.ID,
				TotalAmount: totalDiscountForProduct,
				Details: []dto.DiscountDetail{
					{
						Amount:       totalDiscountForProduct,
						AppliedTo:    "product",
						AttachmentID: &product.ID,
					},
				},
			}
			results = append(results, result)
		}
	}

	return &dto.CalculateDiscountOutput{
		TotalDiscountAmount: totalDiscountAmount,
		Results:             results,
		DiscountAmounts:     discountAmounts,
	}, nil
}
