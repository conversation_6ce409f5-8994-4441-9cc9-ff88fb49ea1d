package model

import (
	"bcare/api/internal/dto"
	"bcare/common/berr"
	"bcare/common/bquery"
	"bcare/common/cast"
	"bcare/common/ctxdata"
	"bcare/ent"
	"bcare/ent/discountusage"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type DiscountUsageModel struct {
	Logger   logx.Logger
	DbClient *ent.Client
}

func NewDiscountUsageModel(dbClient *ent.Client) *DiscountUsageModel {
	logger := logx.WithContext(context.Background()).WithFields(logx.Field("model", "DiscountUsageModel"))
	return &DiscountUsageModel{
		Logger:   logger,
		DbClient: dbClient,
	}
}

func (m *DiscountUsageModel) CreateDiscountUsage(ctx context.Context, input *dto.CreateDiscountUsageInput) (*dto.DiscountUsageOutput, error) {
	op := "DiscountUsageModel.CreateDiscountUsage"
	userID := ctxdata.GetUidFromCtx(ctx)

	discountUsageRecord := new(ent.DiscountUsage)

	err := cast.InputToEnt(discountUsageRecord, input)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	createdDiscountUsage, err := m.DbClient.DiscountUsage.Create().
		SetDiscountUsage(discountUsageRecord).
		Save(ctx)

	if err != nil {
		return nil, berr.ErrCreateFailed.Wrap(err).Op(op)
	}

	m.Logger.WithContext(ctx).Infof("%s - DiscountUsage %d created by user %d", op, createdDiscountUsage.ID, userID)

	output := new(dto.DiscountUsageOutput)
	err = cast.ConvertViaJson(output, createdDiscountUsage)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}
	return output, nil
}

func (m *DiscountUsageModel) GetDiscountUsage(ctx context.Context, id int) (*dto.DiscountUsageOutput, error) {
	op := "DiscountUsageModel.GetDiscountUsage"

	discountUsageRecord, err := m.DbClient.DiscountUsage.Query().
		Where(discountusage.ID(id)).
		WithDiscount().
		First(ctx)
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, berr.ErrNotFound.Wrap(err).Op(op)
		}
		return nil, berr.ErrDB.Wrap(err).Op(op)
	}

	output := new(dto.DiscountUsageOutput)
	err = cast.ConvertViaJson(output, discountUsageRecord)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	if discountUsageRecord.Edges.Discount != nil {
		discountOutput := new(dto.DiscountOutput)
		err = cast.ConvertViaJson(discountOutput, discountUsageRecord.Edges.Discount)
		if err != nil {
			m.Logger.WithContext(ctx).Errorf("%s - Failed to convert discount: %v", op, err)
		} else {
			output.Discount = discountOutput
		}
	}

	return output, nil
}

func (m *DiscountUsageModel) ListDiscountUsages(ctx context.Context, input dto.ListDiscountUsageInput) (*dto.ListDiscountUsageOutput, error) {
	op := "DiscountUsageModel.ListDiscountUsages"

	limit, offset := bquery.Paging(input.PageSize, input.Page)
	order := bquery.Ordering(input.OrderBy)

	query := m.DbClient.DiscountUsage.Query().Order(order).WithDiscount()

	if input.Filter.DiscountID != nil {
		query = query.Where(discountusage.DiscountID(*input.Filter.DiscountID))
	}
	if input.Filter.UserID != nil {
		query = query.Where(discountusage.UserIDEQ(*input.Filter.UserID))
	}
	if input.Filter.PersonID != nil {
		query = query.Where(discountusage.PersonIDEQ(*input.Filter.PersonID))
	}
	if input.Filter.AttachmentID != nil {
		query = query.Where(discountusage.AttachmentIDEQ(*input.Filter.AttachmentID))
	}
	if input.Filter.DealID != nil {
		query = query.Where(discountusage.DealIDEQ(*input.Filter.DealID))
	}
	if input.Filter.Status != nil {
		query = query.Where(discountusage.StatusEQ(*input.Filter.Status))
	}

	total, err := query.Clone().Count(ctx)
	if err != nil {
		return nil, berr.ErrListFailed.Wrap(err).Op(op)
	}

	output := &dto.ListDiscountUsageOutput{
		DiscountUsages: []dto.DiscountUsageOutput{},
		Total:          total,
	}

	if total == 0 {
		return output, nil
	}

	discountUsages, err := query.Limit(limit).Offset(offset).All(ctx)
	if err != nil {
		return nil, berr.ErrListFailed.Wrap(err).Op(op)
	}

	err = cast.ConvertViaJson(&output.DiscountUsages, discountUsages)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	return output, nil
}

func (m *DiscountUsageModel) ListByDealIDs(ctx context.Context, dealIDs []int) ([]dto.DiscountUsageOutput, error) {
	op := "DiscountUsageModel.ListByDealIDs"

	if len(dealIDs) == 0 {
		return []dto.DiscountUsageOutput{}, nil
	}

	discountUsages, err := m.DbClient.DiscountUsage.Query().
		Where(discountusage.DealIDIn(dealIDs...)).
		WithDiscount().
		All(ctx)
	if err != nil {
		return nil, berr.ErrDB.Wrap(err).Op(op)
	}

	var outputs []dto.DiscountUsageOutput
	err = cast.ConvertViaJson(&outputs, discountUsages)
	if err != nil {
		m.Logger.WithContext(ctx).Errorf("%s - Failed to convert discount usages output: %v", op, err)
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	return outputs, nil
}

func (m *DiscountUsageModel) DeleteByDealID(ctx context.Context, dealID int) error {
	op := "DiscountUsageModel.DeleteByDealID"

	_, err := m.DbClient.DiscountUsage.Delete().Where(discountusage.DealID(dealID)).Exec(ctx)
	if err != nil {
		return berr.ErrDeleteFailed.Wrap(err).Op(op)
	}

	return nil
}

func (m *DiscountUsageModel) CreateForDeal(ctx context.Context, dealID int, personID int, discountIDs []int, discountMap map[int]float64) error {
	op := "DiscountUsageModel.CreateForDeal"
	userID := ctxdata.GetUidFromCtx(ctx)

	for _, discountID := range discountIDs {
		discountValue := discountMap[discountID]

		_, err := m.DbClient.DiscountUsage.Create().
			SetDiscountID(discountID).
			SetDealID(dealID).
			SetPersonID(personID).
			SetUsageCount(1).
			SetEntityType("deal").
			SetValue(discountValue).
			Save(ctx)
		if err != nil {
			return berr.ErrCreateFailed.Wrap(err).Op(op)
		}
	}

	m.Logger.WithContext(ctx).Infof("%s - Discount usages created for deal %d by user %d", op, dealID, userID)
	return nil
}

func (m *DiscountUsageModel) UpdateDiscountUsage(ctx context.Context, input *dto.UpdateDiscountUsageInput) (*dto.DiscountUsageOutput, error) {
	op := "DiscountUsageModel.UpdateDiscountUsage"

	discountUsageRecord, err := m.DbClient.DiscountUsage.Get(ctx, input.ID)
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, berr.ErrNotFound.Wrap(err).Op(op)
		}
		return nil, berr.ErrDB.Wrap(err).Op(op)
	}

	err = cast.InputToEnt(discountUsageRecord, input)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	updatedDiscountUsage, err := m.DbClient.DiscountUsage.UpdateOne(discountUsageRecord).
		SetDiscountUsage(discountUsageRecord, input.Modified...).
		Save(ctx)

	if err != nil {
		return nil, berr.ErrUpdateFailed.Wrap(err).Op(op)
	}

	m.Logger.WithContext(ctx).Infof("%s - DiscountUsage %d updated", op, input.ID)

	output := new(dto.DiscountUsageOutput)
	err = cast.ConvertViaJson(output, updatedDiscountUsage)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	return output, nil
}

func (m *DiscountUsageModel) DeleteDiscountUsage(ctx context.Context, id int) error {
	op := "DiscountUsageModel.DeleteDiscountUsage"

	_, err := m.DbClient.DiscountUsage.Get(ctx, id)
	if err != nil {
		if ent.IsNotFound(err) {
			return berr.ErrNotFound.Wrap(err).Op(op)
		}
		return berr.ErrDB.Wrap(err).Op(op)
	}

	err = m.DbClient.DiscountUsage.DeleteOneID(id).Exec(ctx)
	if err != nil {
		return berr.ErrDeleteFailed.Wrap(err).Op(op)
	}

	m.Logger.WithContext(ctx).Infof("%s - DiscountUsage %d deleted", op, id)
	return nil
}
