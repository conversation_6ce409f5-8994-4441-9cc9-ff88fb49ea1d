package model

import (
	"bcare/api/internal/dto"
	"bcare/common/berr"
	"bcare/common/cast"
	"bcare/ent"
	"bcare/ent/person"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type PersonModel struct {
	Logger   logx.Logger
	DbClient *ent.Client
}

func NewPersonModel(dbClient *ent.Client) *PersonModel {
	logger := logx.WithContext(context.Background()).WithFields(logx.Field("model", "PersonModel"))
	return &PersonModel{
		Logger:   logger,
		DbClient: dbClient,
	}
}

func (m *PersonModel) Get(ctx context.Context, input *dto.GetPersonInput) (*dto.PersonOutput, error) {
	op := "PersonModel.Get"

	query := m.DbClient.Person.Query()

	if input.IncludeRelation {
		query = query.WithJob().WithSource().WithCreator()
	}

	personRecord, err := query.Where(person.ID(input.ID)).Only(ctx)
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, berr.ErrNotFound.Wrap(err).Op(op)
		}
		return nil, berr.ErrDB.Wrap(err).Op(op)
	}

	output := new(dto.PersonOutput)
	if err := cast.ConvertViaJson(output, personRecord); err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	return output, nil
}
