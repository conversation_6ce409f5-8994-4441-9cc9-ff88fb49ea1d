package model

import (
	"bcare/api/internal/dto"
	"bcare/common/berr"
	"bcare/common/bquery"
	"bcare/common/cast"
	"bcare/common/ctxdata"
	"bcare/ent"
	"bcare/ent/tag"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type TagModel struct {
	Logger   logx.Logger
	DbClient *ent.Client
}

func NewTagModel(dbClient *ent.Client) *TagModel {
	logger := logx.WithContext(context.Background()).WithFields(logx.Field("model", "TagModel"))
	return &TagModel{
		Logger:   logger,
		DbClient: dbClient,
	}
}

func (m *TagModel) CreateTag(ctx context.Context, input *dto.CreateTagInput) (*dto.TagOutput, error) {
	op := "TagModel.CreateTag"
	userID := ctxdata.GetUidFromCtx(ctx)

	tagRecord := new(ent.Tag)

	err := cast.InputToEnt(tagRecord, input)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	tagRecord.UserID = userID

	createdTag, err := m.DbClient.Tag.Create().
		SetTag(tagRecord).
		Save(ctx)

	if err != nil {
		return nil, berr.ErrCreateFailed.Wrap(err).Op(op)
	}

	m.Logger.WithContext(ctx).Infof("%s - Tag %d created by user %d", op, createdTag.ID, userID)

	output := new(dto.TagOutput)
	err = cast.ConvertViaJson(output, createdTag)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}
	return output, nil
}

func (m *TagModel) GetTag(ctx context.Context, id int) (*dto.TagOutput, error) {
	op := "TagModel.GetTag"

	tagRecord, err := m.DbClient.Tag.Get(ctx, id)
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, berr.ErrNotFound.Wrap(err).Op(op)
		}
		return nil, berr.ErrDB.Wrap(err).Op(op)
	}

	output := new(dto.TagOutput)
	err = cast.ConvertViaJson(output, tagRecord)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	return output, nil
}

func (m *TagModel) ListTags(ctx context.Context, input dto.ListTagsInput) (*dto.ListTagsOutput, error) {
	op := "TagModel.ListTags"

	limit, offset := bquery.Paging(input.PageSize, input.Page)
	order := bquery.Ordering(input.OrderBy)

	query := m.DbClient.Tag.Query().Order(order)

	if len(input.Filter.Ids) > 0 {
		query = query.Where(tag.IDIn(input.Filter.Ids...))
	}
	if input.Filter.Name != "" {
		query = query.Where(tag.NameEQ(input.Filter.Name))
	}
	if input.Filter.Category != "" {
		query = query.Where(tag.CategoryEQ(input.Filter.Category))
	}
	if input.Filter.Status != 0 {
		query = query.Where(tag.StatusEQ(input.Filter.Status))
	}
	if input.Filter.UserId != 0 {
		query = query.Where(tag.UserID(input.Filter.UserId))
	}

	total, err := query.Clone().Count(ctx)
	if err != nil {
		return nil, berr.ErrListFailed.Wrap(err).Op(op)
	}

	output := &dto.ListTagsOutput{
		Tags:  []*dto.TagOutput{},
		Total: total,
	}

	if total == 0 {
		return output, nil
	}

	tags, err := query.Limit(limit).Offset(offset).All(ctx)
	if err != nil {
		return nil, berr.ErrListFailed.Wrap(err).Op(op)
	}

	err = cast.ConvertViaJson(&output.Tags, tags)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	return output, nil
}

func (m *TagModel) UpdateTag(ctx context.Context, input *dto.UpdateTagInput) (*dto.TagOutput, error) {
	op := "TagModel.UpdateTag"

	tagRecord, err := m.DbClient.Tag.Get(ctx, input.ID)
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, berr.ErrNotFound.Wrap(err).Op(op)
		}
		return nil, berr.ErrDB.Wrap(err).Op(op)
	}

	err = cast.InputToEnt(tagRecord, input)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	updatedTag, err := m.DbClient.Tag.UpdateOne(tagRecord).
		SetTag(tagRecord, input.Modified...).
		Save(ctx)

	if err != nil {
		return nil, berr.ErrUpdateFailed.Wrap(err).Op(op)
	}

	m.Logger.WithContext(ctx).Infof("%s - Tag %d updated", op, input.ID)

	output := new(dto.TagOutput)
	err = cast.ConvertViaJson(output, updatedTag)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	return output, nil
}

func (m *TagModel) DeleteTag(ctx context.Context, id int) error {
	op := "TagModel.DeleteTag"

	_, err := m.DbClient.Tag.Get(ctx, id)
	if err != nil {
		if ent.IsNotFound(err) {
			return berr.ErrNotFound.Wrap(err).Op(op)
		}
		return berr.ErrDB.Wrap(err).Op(op)
	}

	err = m.DbClient.Tag.DeleteOneID(id).Exec(ctx)
	if err != nil {
		return berr.ErrDeleteFailed.Wrap(err).Op(op)
	}

	m.Logger.WithContext(ctx).Infof("%s - Tag %d deleted", op, id)
	return nil
}
