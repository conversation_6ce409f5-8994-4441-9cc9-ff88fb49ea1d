package model

import (
	"bcare/api/internal/dto"
	"context"
	"fmt"
	"strings"

	"bcare/common/bconst"
	"bcare/common/berr"
	"bcare/common/bquery"
	"bcare/common/cast"
	"bcare/common/ctxdata"
	"bcare/ent"
	"bcare/ent/predicate"
	"bcare/ent/product"
	"bcare/ent/productoperation"

	"github.com/zeromicro/go-zero/core/logx"
)

type ProductModel struct {
	Logger   logx.Logger
	DbClient *ent.Client
}

func NewProductModel(dbClient *ent.Client) *ProductModel {
	logger := logx.WithContext(context.Background()).WithFields(logx.Field("model", "ProductModel"))
	return &ProductModel{
		Logger:   logger,
		DbClient: dbClient,
	}
}

func (m *ProductModel) checkDuplicateProduct(ctx context.Context, name *string, code *string, excludeID *int) error {
	op := "ProductModel.checkDuplicateProduct"
	var predicates []predicate.Product

	if name != nil && *name != "" {
		predicates = append(predicates, product.NameEQ(*name))
	}
	if code != nil && *code != "" {
		predicates = append(predicates, product.CodeEQ(*code))
	}

	if len(predicates) == 0 {
		return nil
	}

	query := m.DbClient.Product.Query().Where(product.Or(predicates...))
	if excludeID != nil && *excludeID != 0 {
		query = query.Where(product.IDNEQ(*excludeID))
	}

	exists, err := query.Exist(ctx)
	if err != nil {
		return berr.ErrDB.Wrap(err).Op(op)
	}
	if exists {
		return berr.ErrDuplicateEntry.Op(op)
	}
	return nil
}

func (m *ProductModel) Create(ctx context.Context, input *dto.CreateProductInput) (*dto.ProductOutput, error) {
	op := "ProductModel.Create"

	var nameToCheck *string
	if input.Name != "" {
		nameToCheck = &input.Name
	}
	var codeToCheck *string
	if input.Code != "" {
		codeToCheck = &input.Code
	}

	if nameToCheck != nil || codeToCheck != nil {
		if err := m.checkDuplicateProduct(ctx, nameToCheck, codeToCheck, nil); err != nil {
			return nil, err
		}
	}

	var newProd *ent.Product
	var err error

	err = bquery.WithTx(ctx, m.DbClient, func(tx *ent.Tx) error {
		entProduct := new(ent.Product)
		if errConv := cast.ConvertViaJson(entProduct, input); errConv != nil {
			return berr.ErrCopyFailed.Wrap(errConv).Op(op)
		}

		if entProduct.Code == "" && input.Code == "" && input.Name != "" {
			entProduct.Code = strings.ReplaceAll(strings.ToLower(input.Name), " ", "-")
		} else {
			entProduct.Code = input.Code
		}

		if input.Status == 0 {
			entProduct.Status = bconst.StatusNormal
		} else {
			entProduct.Status = int8(input.Status)
		}

		pc := tx.Product.Create().SetProduct(entProduct)

		newProd, err = pc.Save(ctx)
		if err != nil {
			if ent.IsConstraintError(err) {
				return berr.ErrDuplicateEntry.Wrap(err).Op(op)
			}
			return berr.ErrCreateFailed.Wrap(err).Op(op)
		}

		if input.Operations != nil {
			for _, opInput := range input.Operations {
				_, err = tx.ProductOperation.Create().
					SetProductID(newProd.ID).
					SetOperationID(opInput.OperationID).
					Save(ctx)
				if err != nil {
					return berr.ErrCreateFailed.Wrap(fmt.Errorf("failed to create product operation: %w", err)).Op(op)
				}
			}
		}
		return nil
	})

	if err != nil {
		return nil, err
	}

	m.Logger.WithContext(ctx).Infof("%s - Product %d created by user %d", op, newProd.ID, ctxdata.GetUidFromCtx(ctx))

	prodWithEdges, fetchErr := m.DbClient.Product.Query().
		Where(product.ID(newProd.ID)).
		WithCategory().
		WithOperations().
		Only(ctx)
	if fetchErr != nil {
		prodWithEdges = newProd
	}

	output := new(dto.ProductOutput)
	if err := cast.ConvertViaJson(output, prodWithEdges); err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	return output, nil
}

func (m *ProductModel) Get(ctx context.Context, input *dto.GetProductInput) (*dto.ProductOutput, error) {
	op := "ProductModel.Get"

	query := m.DbClient.Product.Query().WithCategory().WithOperations()
	if input.ID != 0 {
		query = query.Where(product.ID(input.ID))
	} else if input.Code != "" {
		query = query.Where(product.Code(input.Code))
	} else if input.Name != "" {
		query = query.Where(product.Name(input.Name))
	} else {
		return nil, berr.ErrRequestParam.Op(op)
	}

	prod, err := query.Only(ctx)
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, berr.ErrNotFound.Wrap(err).Op(op)
		}
		return nil, berr.ErrDB.Wrap(err).Op(op)
	}

	output := new(dto.ProductOutput)
	if err := cast.ConvertViaJson(output, prod); err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}
	return output, nil
}

// GetByIDs fetches multiple products by their IDs in a single query
func (m *ProductModel) GetByIDs(ctx context.Context, productIDs []int) ([]dto.ProductOutput, error) {
	op := "ProductModel.GetByIDs"

	if len(productIDs) == 0 {
		return []dto.ProductOutput{}, nil
	}

	products, err := m.DbClient.Product.Query().
		Where(product.IDIn(productIDs...)).
		WithCategory().
		WithOperations().
		All(ctx)
	if err != nil {
		return nil, berr.ErrDB.Wrap(err).Op(op)
	}

	var productOutputs []dto.ProductOutput
	for _, prod := range products {
		var output dto.ProductOutput
		if err := cast.ConvertViaJson(&output, prod); err != nil {
			m.Logger.WithContext(ctx).Errorf("%s - Failed to convert product %d to output: %v", op, prod.ID, err)
			continue // Skip invalid records
		}
		productOutputs = append(productOutputs, output)
	}

	return productOutputs, nil
}

func (m *ProductModel) Update(ctx context.Context, input *dto.UpdateProductInput) (*dto.ProductOutput, error) {
	op := "ProductModel.Update"

	var nameToCheckUpdate *string
	if input.Name != nil && *input.Name != "" {
		nameToCheckUpdate = input.Name
	}
	var codeToCheckUpdate *string
	if input.Code != nil && *input.Code != "" {
		codeToCheckUpdate = input.Code
	}

	if nameToCheckUpdate != nil || codeToCheckUpdate != nil {
		if err := m.checkDuplicateProduct(ctx, nameToCheckUpdate, codeToCheckUpdate, &input.ID); err != nil {
			return nil, err
		}
	}

	var updatedProd *ent.Product
	var err error

	err = bquery.WithTx(ctx, m.DbClient, func(tx *ent.Tx) error {
		entProduct, err := tx.Product.Get(ctx, input.ID)
		if err != nil {
			if ent.IsNotFound(err) {
				return berr.ErrNotFound.Wrap(err).Op(op)
			}
			return berr.ErrDB.Wrap(err).Op(op)
		}

		if err := cast.InputToEnt(entProduct, input); err != nil {
			return berr.ErrCopyFailed.Wrap(err).Op(op)
		}

		updatedProd, err = tx.Product.UpdateOne(entProduct).
			SetProduct(entProduct, input.Modified...).
			Save(ctx)
		if err != nil {
			if ent.IsConstraintError(err) {
				return berr.ErrDuplicateEntry.Wrap(err).Op(op)
			}
			return berr.ErrUpdateFailed.Wrap(err).Op(op)
		}

		if input.Operations != nil {
			_, err = tx.ProductOperation.Delete().
				Where(productoperation.ProductID(input.ID)).
				Exec(ctx)
			if err != nil {
				return berr.ErrUpdateFailed.Wrap(fmt.Errorf("failed to clear existing product operations: %w", err)).Op(op)
			}

			for _, opInput := range input.Operations {
				_, err = tx.ProductOperation.Create().
					SetProductID(input.ID).
					SetOperationID(opInput.OperationID).
					Save(ctx)
				if err != nil {
					return berr.ErrUpdateFailed.Wrap(fmt.Errorf("failed to create product operation: %w", err)).Op(op)
				}
			}
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	m.Logger.WithContext(ctx).Infof("%s - Product %d updated", op, input.ID)

	prodWithEdges, fetchErr := m.DbClient.Product.Query().
		Where(product.ID(updatedProd.ID)).
		WithCategory().
		WithOperations().
		Only(ctx)
	if fetchErr != nil {
		prodWithEdges = updatedProd
	}

	output := new(dto.ProductOutput)
	if err := cast.ConvertViaJson(output, prodWithEdges); err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	return output, nil
}

func (m *ProductModel) DeleteProduct(ctx context.Context, id int) (*dto.ProductOutput, error) {
	op := "ProductModel.DeleteProduct"

	prod, err := m.DbClient.Product.Query().
		Where(product.ID(id)).
		WithCategory().
		WithOperations().
		Only(ctx)
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, berr.ErrNotFound.Wrap(err).Op(op)
		}
		return nil, berr.ErrDB.Wrap(err).Op(op)
	}

	output := new(dto.ProductOutput)
	if err := cast.ConvertViaJson(output, prod); err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	err = bquery.WithTx(ctx, m.DbClient, func(tx *ent.Tx) error {
		_, err = tx.ProductOperation.Delete().
			Where(productoperation.ProductID(id)).
			Exec(ctx)
		if err != nil {
			return berr.ErrDeleteFailed.Wrap(fmt.Errorf("failed to delete product operations: %w", err)).Op(op)
		}

		err = tx.Product.DeleteOneID(id).Exec(ctx)
		if err != nil {
			return berr.ErrDeleteFailed.Wrap(err).Op(op)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	m.Logger.WithContext(ctx).Infof("%s - Product %d deleted", op, id)
	return output, nil
}

func (m *ProductModel) List(ctx context.Context, input *dto.ListProductInput) (*dto.ListProductOutput, error) {
	op := "ProductModel.List"

	limit, offset := bquery.Paging(input.PageSize, input.Page)
	order := bquery.Ordering(input.OrderBy)

	query := m.DbClient.Product.Query().
		WithCategory().
		WithOperations().
		Order(order)

	if input.Filter.Name != "" {
		query = query.Where(product.NameContainsFold(input.Filter.Name))
	}
	if input.Filter.Code != "" {
		query = query.Where(product.CodeContainsFold(input.Filter.Code))
	}
	if input.Filter.Type != "" {
		query = query.Where(product.TypeEQ(input.Filter.Type))
	}
	if input.Filter.Status != 0 {
		query = query.Where(product.StatusEQ(int8(input.Filter.Status)))
	}
	if input.Filter.SKU != "" {
		query = query.Where(product.SkuContainsFold(input.Filter.SKU))
	}
	if input.Filter.UnitID != 0 {
		query = query.Where(product.UnitID(input.Filter.UnitID))
	}
	if input.Filter.GroupID != 0 {
		query = query.Where(product.GroupID(input.Filter.GroupID))
	}
	if input.Filter.CategoryID != 0 {
		query = query.Where(product.CategoryID(input.Filter.CategoryID))
	}

	if input.Search != "" {
		query = query.Where(
			product.Or(
				product.NameContainsFold(input.Search),
				product.CodeContainsFold(input.Search),
				product.DescriptionContainsFold(input.Search),
				product.SkuContainsFold(input.Search),
			),
		)
	}

	total, err := query.Clone().Count(ctx)
	if err != nil {
		return nil, berr.ErrListFailed.Wrap(err).Op(op)
	}

	products, err := query.Limit(limit).Offset(offset).All(ctx)
	if err != nil {
		return nil, berr.ErrListFailed.Wrap(err).Op(op)
	}

	outputProducts := make([]dto.ProductOutput, 0, len(products))
	for _, p := range products {
		out := new(dto.ProductOutput)
		if err := cast.ConvertViaJson(out, p); err != nil {
			continue
		}
		outputProducts = append(outputProducts, *out)
	}

	totalPage := 0
	if input.PageSize > 0 && total > 0 {
		totalPage = (total + input.PageSize - 1) / input.PageSize
	}

	return &dto.ListProductOutput{
		Products:  outputProducts,
		Total:     total,
		TotalPage: totalPage,
	}, nil
}
