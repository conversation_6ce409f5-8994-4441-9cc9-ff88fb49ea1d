This file is a merged representation of the entire codebase, combined into a single document by Repomix.

<file_summary>
This section contains a summary of this file.

<purpose>
This file contains a packed representation of the entire repository's contents.
It is designed to be easily consumable by AI systems for analysis, code review,
or other automated processes.
</purpose>

<file_format>
The content is organized as follows:
1. This summary section
2. Repository information
3. Directory structure
4. Repository files, each consisting of:
  - File path as an attribute
  - Full contents of the file
</file_format>

<usage_guidelines>
- This file should be treated as read-only. Any changes should be made to the
  original repository files, not this packed version.
- When processing this file, use the file path to distinguish
  between different files in the repository.
- Be aware that this file may contain sensitive information. Handle it with
  the same level of security as you would the original repository.
</usage_guidelines>

<notes>
- Some files may have been excluded based on .gitignore rules and Repomix's configuration
- Binary files are not included in this packed representation. Please refer to the Repository Structure section for a complete list of file paths, including binary files
- Files matching patterns in .gitignore are excluded
- Files matching default ignore patterns are excluded
- Files are sorted by Git change count (files with more changes are at the bottom)
</notes>

<additional_info>

</additional_info>

</file_summary>

<directory_structure>
tag_dto.go
tag_model.go
</directory_structure>

<files>
This section contains the contents of the repository's files.

<file path="tag_dto.go">
package model

import "time"

type TagOutput struct {
	ID          int        `json:"id"`
	Name        string     `json:"name"`
	Category    string     `json:"category"`
	Description string     `json:"description"`
	Color       string     `json:"color"`
	UserID      int        `json:"user_id"`
	Status      int        `json:"status"`
	Version     int        `json:"version"`
	CreatedAt   time.Time  `json:"created_at"`
	UpdatedAt   time.Time  `json:"updated_at"`
	DeletedAt   string `json:"deleted_at,omitempty"`
}

type CreateTagInput struct {
	Name        string `json:"name"`
	Category    string `json:"category"`
	Description string `json:"description,omitempty"`
	Color       string `json:"color"`
}

type UpdateTagInput struct {
	ID          int      `json:"id"`
	Name        string   `json:"name,omitempty"`
	Category    string   `json:"category,omitempty"`
	Description string   `json:"description,omitempty"`
	Color       string   `json:"color,omitempty"`
	Status      *int8    `json:"status,omitempty"`
	Modified    []string `json:"modified,omitempty"`
}

type ListTagsInput struct {
	Filter   TagInputFilter `json:"filter,omitempty"`
	Page     int            `json:"page,omitempty"`
	PageSize int            `json:"page_size,omitempty"`
	OrderBy  string         `json:"order_by,omitempty"`
	Search   string         `json:"search,omitempty"`
}

type TagInputFilter struct {
	Ids      []int  `json:"ids,omitempty"`
	Name     string `json:"name,omitempty"`
	Category string `json:"category,omitempty"`
	Status   int8   `json:"status,range=[0:10],omitempty"`
	UserId   int    `json:"user_id,omitempty"`
}

type ListTagsOutput struct {
	Tags  []*TagOutput
	Total int
}
</file>

<file path="tag_model.go">
package model

import (
	"bcare/common/berr"
	"bcare/common/bquery"
	"bcare/common/cast"
	"bcare/common/ctxdata"
	"bcare/ent"
	"bcare/ent/tag"
	"context"
	"github.com/zeromicro/go-zero/core/logx"
)

type TagModel struct {
	Logger   logx.Logger
	DbClient *ent.Client
}

func NewTagModel(dbClient *ent.Client) *TagModel {
	logger := logx.WithContext(context.Background()).WithFields(logx.Field("model", "TagModel"))
	return &TagModel{
		Logger:   logger,
		DbClient: dbClient,
	}
}

func (m *TagModel) CreateTag(ctx context.Context, input *CreateTagInput) (*TagOutput, error) {
	op := "TagModel.CreateTag"
	userID := ctxdata.GetUidFromCtx(ctx)

	tagRecord := new(ent.Tag)

	err := cast.InputToEnt(tagRecord, input)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	tagRecord.UserID = userID

	createdTag, err := m.DbClient.Tag.Create().
		SetTag(tagRecord).
		Save(ctx)

	if err != nil {
		return nil, berr.ErrCreateFailed.Wrap(err).Op(op)
	}

	m.Logger.WithContext(ctx).Infof("%s - Tag %d created by user %d", op, createdTag.ID, userID)

	output := new(TagOutput)
	err = cast.ConvertViaJson(output, createdTag)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}
	return output, nil
}

func (m *TagModel) GetTag(ctx context.Context, id int) (*TagOutput, error) {
	op := "TagModel.GetTag"

	tagRecord, err := m.DbClient.Tag.Get(ctx, id)
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, berr.ErrNotFound.Wrap(err).Op(op)
		}
		return nil, berr.ErrDB.Wrap(err).Op(op)
	}

	output := new(TagOutput)
	err = cast.ConvertViaJson(output, tagRecord)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	return output, nil
}

func (m *TagModel) ListTags(ctx context.Context, input ListTagsInput) (*ListTagsOutput, error) {
	op := "TagModel.ListTags"

	limit, offset := bquery.Paging(input.PageSize, input.Page)
	order := bquery.Ordering(input.OrderBy)

	query := m.DbClient.Tag.Query().Order(order)

	if len(input.Filter.Ids) > 0 {
		query = query.Where(tag.IDIn(input.Filter.Ids...))
	}
	if input.Filter.Name != "" {
		query = query.Where(tag.NameEQ(input.Filter.Name))
	}
	if input.Filter.Category != "" {
		query = query.Where(tag.CategoryEQ(input.Filter.Category))
	}
	if input.Filter.Status != 0 {
		query = query.Where(tag.StatusEQ(input.Filter.Status))
	}
	if input.Filter.UserId != 0 {
		query = query.Where(tag.UserID(input.Filter.UserId))
	}

	total, err := query.Clone().Count(ctx)
	if err != nil {
		return nil, berr.ErrListFailed.Wrap(err).Op(op)
	}

	output := &ListTagsOutput{
		Tags:  []*TagOutput{},
		Total: total,
	}

	if total == 0 {
		return output, nil
	}

	tags, err := query.Limit(limit).Offset(offset).All(ctx)
	if err != nil {
		return nil, berr.ErrListFailed.Wrap(err).Op(op)
	}

	err = cast.ConvertViaJson(&output.Tags, tags)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	return output, nil
}

func (m *TagModel) UpdateTag(ctx context.Context, input *UpdateTagInput) (*TagOutput, error) {
	op := "TagModel.UpdateTag"

	tagRecord, err := m.DbClient.Tag.Get(ctx, input.ID)
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, berr.ErrNotFound.Wrap(err).Op(op)
		}
		return nil, berr.ErrDB.Wrap(err).Op(op)
	}

	err = cast.InputToEnt(tagRecord, input)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	updatedTag, err := m.DbClient.Tag.UpdateOne(tagRecord).
		SetTag(tagRecord, input.Modified...).
		Save(ctx)

	if err != nil {
		return nil, berr.ErrUpdateFailed.Wrap(err).Op(op)
	}

	m.Logger.WithContext(ctx).Infof("%s - Tag %d updated", op, input.ID)

	output := new(TagOutput)
	err = cast.ConvertViaJson(output, updatedTag)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	return output, nil
}

func (m *TagModel) DeleteTag(ctx context.Context, id int) error {
	op := "TagModel.DeleteTag"

	_, err := m.DbClient.Tag.Get(ctx, id)
	if err != nil {
		if ent.IsNotFound(err) {
			return berr.ErrNotFound.Wrap(err).Op(op)
		}
		return berr.ErrDB.Wrap(err).Op(op)
	}

	err = m.DbClient.Tag.DeleteOneID(id).Exec(ctx)
	if err != nil {
		return berr.ErrDeleteFailed.Wrap(err).Op(op)
	}

	m.Logger.WithContext(ctx).Infof("%s - Tag %d deleted", op, id)
	return nil
}
</file>

</files>
