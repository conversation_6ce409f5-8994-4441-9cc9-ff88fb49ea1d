package model

import (
	"bcare/api/internal/dto"
	"bcare/common/berr"
	"bcare/common/bquery"
	"bcare/common/cast"
	"bcare/ent"
	"bcare/ent/operationmaterial"
	"context"
	"github.com/zeromicro/go-zero/core/logx"
)

type OperationMaterialModel struct {
	Logger   logx.Logger
	DbClient *ent.Client
}

func NewOperationMaterialModel(dbClient *ent.Client) *OperationMaterialModel {
	logger := logx.WithContext(context.Background()).WithFields(logx.Field("model", "OperationMaterialModel"))
	return &OperationMaterialModel{
		Logger:   logger,
		DbClient: dbClient,
	}
}

func (m *OperationMaterialModel) CreateOperationMaterial(ctx context.Context, input *dto.CreateOperationMaterialInput) (*dto.OperationMaterialOutput, error) {
	op := "OperationMaterialModel.CreateOperationMaterial"

	operationMaterialRecord := new(ent.OperationMaterial)

	err := cast.InputToEnt(operationMaterialRecord, input)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	createdOperationMaterial, err := m.DbClient.OperationMaterial.Create().
		SetOperationMaterial(operationMaterialRecord).
		Save(ctx)

	if err != nil {
		return nil, berr.ErrCreateFailed.Wrap(err).Op(op)
	}

	m.Logger.WithContext(ctx).Infof("%s - OperationMaterial %d created", op, createdOperationMaterial.ID)

	output := new(dto.OperationMaterialOutput)
	err = cast.ConvertViaJson(output, createdOperationMaterial)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}
	return output, nil
}

func (m *OperationMaterialModel) GetOperationMaterial(ctx context.Context, input *dto.GetOperationMaterialInput) (*dto.OperationMaterialOutput, error) {
	op := "OperationMaterialModel.GetOperationMaterial"

	var operationMaterialRecord *ent.OperationMaterial
	var err error

	if input.ID != 0 {
		operationMaterialRecord, err = m.DbClient.OperationMaterial.Get(ctx, input.ID)
	} else if input.OperationID != 0 {
		operationMaterialRecord, err = m.DbClient.OperationMaterial.Query().
			Where(operationmaterial.OperationIDEQ(input.OperationID)).
			Only(ctx)
	} else if input.MaterialID != 0 {
		operationMaterialRecord, err = m.DbClient.OperationMaterial.Query().
			Where(operationmaterial.MaterialIDEQ(input.MaterialID)).
			Only(ctx)
	}

	if err != nil {
		if ent.IsNotFound(err) {
			return nil, berr.ErrNotFound.Wrap(err).Op(op)
		}
		return nil, berr.ErrDB.Wrap(err).Op(op)
	}

	output := new(dto.OperationMaterialOutput)
	err = cast.ConvertViaJson(output, operationMaterialRecord)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	return output, nil
}

func (m *OperationMaterialModel) ListOperationMaterials(ctx context.Context, input *dto.ListOperationMaterialInput) (*dto.ListOperationMaterialOutput, error) {
	op := "OperationMaterialModel.ListOperationMaterials"

	limit, offset := bquery.Paging(input.PageSize, input.Page)
	order := bquery.Ordering(input.OrderBy)

	query := m.DbClient.OperationMaterial.Query().Order(order)

	if input.Filter.OperationID != 0 {
		query = query.Where(operationmaterial.OperationIDEQ(input.Filter.OperationID))
	}
	if input.Filter.MaterialID != 0 {
		query = query.Where(operationmaterial.MaterialIDEQ(input.Filter.MaterialID))
	}

	total, err := query.Clone().Count(ctx)
	if err != nil {
		return nil, berr.ErrListFailed.Wrap(err).Op(op)
	}

	output := &dto.ListOperationMaterialOutput{
		OperationMaterials: []dto.OperationMaterialOutput{},
		Total:              total,
	}

	if total == 0 {
		return output, nil
	}

	operationMaterials, err := query.Limit(limit).Offset(offset).All(ctx)
	if err != nil {
		return nil, berr.ErrListFailed.Wrap(err).Op(op)
	}

	err = cast.ConvertViaJson(&output.OperationMaterials, operationMaterials)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	return output, nil
}

func (m *OperationMaterialModel) UpdateOperationMaterial(ctx context.Context, input *dto.UpdateOperationMaterialInput) (*dto.OperationMaterialOutput, error) {
	op := "OperationMaterialModel.UpdateOperationMaterial"

	operationMaterialRecord, err := m.DbClient.OperationMaterial.Get(ctx, input.ID)
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, berr.ErrNotFound.Wrap(err).Op(op)
		}
		return nil, berr.ErrDB.Wrap(err).Op(op)
	}

	err = cast.InputToEnt(operationMaterialRecord, input)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	updatedOperationMaterial, err := m.DbClient.OperationMaterial.UpdateOne(operationMaterialRecord).
		SetOperationMaterial(operationMaterialRecord, input.Modified...).
		Save(ctx)

	if err != nil {
		return nil, berr.ErrUpdateFailed.Wrap(err).Op(op)
	}

	m.Logger.WithContext(ctx).Infof("%s - OperationMaterial %d updated", op, input.ID)

	output := new(dto.OperationMaterialOutput)
	err = cast.ConvertViaJson(output, updatedOperationMaterial)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	return output, nil
}

func (m *OperationMaterialModel) DeleteOperationMaterial(ctx context.Context, input *dto.DeleteOperationMaterialInput) error {
	op := "OperationMaterialModel.DeleteOperationMaterial"

	operationMaterialRecord, err := m.DbClient.OperationMaterial.Get(ctx, input.ID)
	if err != nil {
		if ent.IsNotFound(err) {
			return berr.ErrNotFound.Wrap(err).Op(op)
		}
		return berr.ErrDB.Wrap(err).Op(op)
	}

	if operationMaterialRecord.OperationID != input.OperationID {
		return berr.ErrNotFound.Wrap(err).Op(op)
	}

	err = m.DbClient.OperationMaterial.DeleteOneID(input.ID).Exec(ctx)
	if err != nil {
		return berr.ErrDeleteFailed.Wrap(err).Op(op)
	}

	m.Logger.WithContext(ctx).Infof("%s - OperationMaterial %d deleted", op, input.ID)
	return nil
}

func (m *OperationMaterialModel) BulkSetOperationMaterials(ctx context.Context, input *dto.BulkSetInput) error {
	op := "OperationMaterialModel.BulkSetOperationMaterials"

	err := bquery.WithTx(ctx, m.DbClient, func(tx *ent.Tx) error {
		_, err := tx.OperationMaterial.Delete().
			Where(operationmaterial.OperationIDEQ(input.OperationId)).
			Exec(ctx)
		if err != nil {
			return berr.ErrDeleteFailed.Wrap(err).Op(op)
		}

		if len(input.Materials) > 0 {
			creators := make([]*ent.OperationMaterialCreate, len(input.Materials))
			for i, item := range input.Materials {
				creators[i] = tx.OperationMaterial.Create().
					SetOperationID(input.OperationId).
					SetMaterialID(item.MaterialId).
					SetQuantity(item.Quantity)
			}
			_, err = tx.OperationMaterial.CreateBulk(creators...).Save(ctx)
			if err != nil {
				return berr.ErrCreateFailed.Wrap(err).Op(op)
			}
		}
		return nil
	})

	if err != nil {
		return err
	}

	m.Logger.WithContext(ctx).Infof("%s - Đã cập nhật thành công vật tư cho OperationID %d (transaction committed)", op, input.OperationId)

	return nil
}
