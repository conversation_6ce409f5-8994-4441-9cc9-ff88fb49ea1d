package model

import (
	"bcare/api/internal/dto"
	"bcare/api/internal/types"
	"bcare/bob/queries"
	"bcare/common/berr"
	"bcare/common/bquery"
	"bcare/common/cast"
	"bcare/common/ctxdata"
	"bcare/common/excelutil"
	"bcare/ent"
	"bcare/ent/cashflow"
	"bcare/ent/user"
	"context"
	"fmt"
	"html"
	"regexp"
	"strings"
	"time"

	"github.com/stephenafamo/bob"
	"github.com/zeromicro/go-zero/core/logx"
)

type CashFlowModel struct {
	Logger   logx.Logger
	DbClient *ent.Client
	Bob      bob.DB
}

func NewCashFlowModel(dbClient *ent.Client, bobDB bob.DB) *CashFlowModel {
	logger := logx.WithContext(context.Background()).WithFields(logx.Field("model", "CashFlowModel"))
	return &CashFlowModel{
		Logger:   logger,
		DbClient: dbClient,
		Bob:      bobDB,
	}
}

func (m *CashFlowModel) CreateCashFlow(ctx context.Context, input *dto.CreateCashFlowInput) (*dto.CashFlowWithRelationsOutput, error) {
	op := "CashFlowModel.CreateCashFlow"
	userID := ctxdata.GetUidFromCtx(ctx)

	cashFlowRecord := new(ent.CashFlow)

	err := cast.CopyInputToEnt(cashFlowRecord, input)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	cashFlowRecord.CreatorID = userID

	// Set initial state based on type
	if input.Type == "income" {
		cashFlowRecord.State = cashflow.StatePAID
		now := time.Now()
		cashFlowRecord.PaidAt = &now
	} else {
		cashFlowRecord.State = cashflow.StatePENDING
	}

	createdCashFlow, err := m.DbClient.CashFlow.Create().
		SetCashFlow(cashFlowRecord).
		Save(ctx)

	if err != nil {
		return nil, berr.ErrCreateFailed.Wrap(err).Op(op)
	}

	m.Logger.WithContext(ctx).Infof("%s - CashFlow %d created by user %d", op, createdCashFlow.ID, userID)

	// Return with relations
	return m.getCashFlowWithRelations(ctx, createdCashFlow.ID)
}

func (m *CashFlowModel) GetCashFlow(ctx context.Context, input dto.GetCashFlowInput) (*dto.CashFlowWithRelationsOutput, error) {
	op := "CashFlowModel.GetCashFlow"

	if input.IncludeRelation {
		return m.getCashFlowWithRelations(ctx, input.ID)
	}

	cashFlowRecord, err := m.DbClient.CashFlow.Get(ctx, input.ID)
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, berr.ErrNotFound.Wrap(err).Op(op)
		}
		return nil, berr.ErrDB.Wrap(err).Op(op)
	}

	output := new(dto.CashFlowWithRelationsOutput)
	err = cast.ConvertViaJson(output, cashFlowRecord)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	return output, nil
}

func (m *CashFlowModel) getCashFlowWithRelations(ctx context.Context, id int) (*dto.CashFlowWithRelationsOutput, error) {
	op := "CashFlowModel.getCashFlowWithRelations"

	cashFlowRecord, err := m.DbClient.CashFlow.Query().
		Where(cashflow.ID(id)).
		WithCreator(func(q *ent.UserQuery) {
			q.Select(user.FieldID, user.FieldUsername, user.FieldName, user.FieldProfileImage, user.FieldDepartmentID)
		}).
		WithApprover(func(q *ent.UserQuery) {
			q.Select(user.FieldID, user.FieldUsername, user.FieldName, user.FieldProfileImage, user.FieldDepartmentID)
		}).
		WithCounterpart(func(q *ent.UserQuery) {
			q.Select(user.FieldID, user.FieldUsername, user.FieldName, user.FieldProfileImage, user.FieldDepartmentID)
		}).
		WithRecipient(func(q *ent.UserQuery) {
			q.Select(user.FieldID, user.FieldUsername, user.FieldName, user.FieldProfileImage, user.FieldDepartmentID)
		}).
		WithItems(func(q *ent.CashFlowItemQuery) {
			q.WithCategory()
		}).
		Only(ctx)

	if err != nil {
		if ent.IsNotFound(err) {
			return nil, berr.ErrNotFound.Wrap(err).Op(op)
		}
		return nil, berr.ErrDB.Wrap(err).Op(op)
	}

	output := new(dto.CashFlowWithRelationsOutput)
	err = cast.ConvertViaJson(output, cashFlowRecord)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	return output, nil
}

func (m *CashFlowModel) ListCashFlows(ctx context.Context, input dto.ListCashFlowsInput) (*dto.ListCashFlowsOutput, error) {
	op := "CashFlowModel.ListCashFlows"

	limit, offset := bquery.Paging(input.PageSize, input.Page)
	order := bquery.Ordering(input.OrderBy)

	query := m.DbClient.CashFlow.Query().Order(order)

	// Apply filters
	if input.Filter.Type != nil && *input.Filter.Type != "" {
		query = query.Where(cashflow.TypeEQ(cashflow.Type(*input.Filter.Type)))
	}
	if input.Filter.State != nil && *input.Filter.State != "" {
		query = query.Where(cashflow.StateEQ(cashflow.State(*input.Filter.State)))
	}
	if input.Filter.CreatorID != nil && *input.Filter.CreatorID != 0 {
		query = query.Where(cashflow.CreatorID(*input.Filter.CreatorID))
	}
	if input.Filter.CounterpartID != nil && *input.Filter.CounterpartID != 0 {
		query = query.Where(cashflow.CounterpartID(*input.Filter.CounterpartID))
	}
	if input.Filter.ApproverID != nil && *input.Filter.ApproverID != 0 {
		query = query.Where(cashflow.ApproverID(*input.Filter.ApproverID))
	}

	// Date filters
	if input.Filter.FromDate != nil {
		query = query.Where(cashflow.CreatedAtGTE(*input.Filter.FromDate))
	}
	if input.Filter.ToDate != nil {
		endDate := input.Filter.ToDate.Add(24*time.Hour - time.Second) // End of day
		query = query.Where(cashflow.CreatedAtLTE(endDate))
	}

	// Search functionality
	if input.Search != "" {
		query = query.Where(cashflow.DescriptionContains(input.Search))
	}

	total, err := query.Clone().Count(ctx)
	if err != nil {
		return nil, berr.ErrListFailed.Wrap(err).Op(op)
	}

	totalPage := (total + limit - 1) / limit

	output := &dto.ListCashFlowsOutput{
		CashFlows: []*dto.CashFlowWithRelationsOutput{},
		Total:     total,
		TotalPage: totalPage,
	}

	if total == 0 {
		return output, nil
	}

	cashFlows, err := query.
		WithNotes().
		WithCreator(func(q *ent.UserQuery) {
			q.Select(user.FieldID, user.FieldUsername, user.FieldName, user.FieldProfileImage, user.FieldDepartmentID)
		}).
		WithApprover(func(q *ent.UserQuery) {
			q.Select(user.FieldID, user.FieldUsername, user.FieldName, user.FieldProfileImage, user.FieldDepartmentID)
		}).
		WithCounterpart(func(q *ent.UserQuery) {
			q.Select(user.FieldID, user.FieldUsername, user.FieldName, user.FieldProfileImage, user.FieldDepartmentID)
		}).
		WithItems(func(q *ent.CashFlowItemQuery) {
			q.WithCategory()
		}).
		Limit(limit).
		Offset(offset).
		All(ctx)

	if err != nil {
		return nil, berr.ErrListFailed.Wrap(err).Op(op)
	}

	err = cast.ConvertViaJson(&output.CashFlows, cashFlows)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	return output, nil
}

func (m *CashFlowModel) UpdateCashFlow(ctx context.Context, input *dto.UpdateCashFlowInput) (*dto.CashFlowWithRelationsOutput, error) {
	op := "CashFlowModel.UpdateCashFlow"

	cashFlowRecord, err := m.DbClient.CashFlow.Get(ctx, input.ID)
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, berr.ErrNotFound.Wrap(err).Op(op)
		}
		return nil, berr.ErrDB.Wrap(err).Op(op)
	}

	err = cast.CopyInputToEnt(cashFlowRecord, input)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	updatedCashFlow, err := m.DbClient.CashFlow.UpdateOne(cashFlowRecord).
		SetCashFlow(cashFlowRecord, input.Modified...).
		Save(ctx)

	if err != nil {
		return nil, berr.ErrUpdateFailed.Wrap(err).Op(op)
	}

	m.Logger.WithContext(ctx).Infof("%s - CashFlow %d updated", op, input.ID)

	return m.getCashFlowWithRelations(ctx, updatedCashFlow.ID)
}

func (m *CashFlowModel) UpdateCashFlowState(ctx context.Context, input *dto.UpdateCashFlowStateInput) (*dto.CashFlowWithRelationsOutput, error) {
	op := "CashFlowModel.UpdateCashFlowState"
	userID := ctxdata.GetUidFromCtx(ctx)

	cashFlowRecord, err := m.DbClient.CashFlow.Get(ctx, input.ID)
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, berr.ErrNotFound.Wrap(err).Op(op)
		}
		return nil, berr.ErrDB.Wrap(err).Op(op)
	}

	// Validate state transitions
	if !m.isValidStateTransition(string(cashFlowRecord.State), input.State) {
		return nil, berr.ErrUpdateFailed.Wrap(fmt.Errorf("Invalid state transition from %s to %s", cashFlowRecord.State, input.State)).Op(op)
	}

	// Set approver and timestamps
	updateQuery := m.DbClient.CashFlow.UpdateOneID(input.ID).
		SetState(cashflow.State(input.State))

	if input.State == string(cashflow.StateAPPROVED) {
		updateQuery = updateQuery.SetApproverID(userID)
	}

	if input.State == string(cashflow.StatePAID) {
		now := time.Now()
		updateQuery = updateQuery.SetPayerID(userID).SetPaidAt(now)
	}

	updatedCashFlow, err := updateQuery.Save(ctx)
	if err != nil {
		return nil, berr.ErrUpdateFailed.Wrap(err).Op(op)
	}

	m.Logger.WithContext(ctx).Infof("%s - CashFlow %d state updated to %s by user %d", op, input.ID, input.State, userID)

	return m.getCashFlowWithRelations(ctx, updatedCashFlow.ID)
}

func (m *CashFlowModel) isValidStateTransition(currentState, newState string) bool {
	validTransitions := map[string][]string{
		"PENDING":  {"APPROVED", "REJECTED", "CANCELED"},
		"APPROVED": {"PAID", "CANCELED"},
		"REJECTED": {"CANCELED"},
		"PAID":     {},
		"CANCELED": {},
	}

	allowedStates, exists := validTransitions[currentState]
	if !exists {
		return false
	}

	for _, state := range allowedStates {
		if state == newState {
			return true
		}
	}
	return false
}

func (m *CashFlowModel) DeleteCashFlow(ctx context.Context, input dto.DeleteCashFlowInput) error {
	op := "CashFlowModel.DeleteCashFlow"

	cashFlowRecord, err := m.DbClient.CashFlow.Get(ctx, input.ID)
	if err != nil {
		if ent.IsNotFound(err) {
			return berr.ErrNotFound.Wrap(err).Op(op)
		}
		return berr.ErrDB.Wrap(err).Op(op)
	}

	// Only allow deletion if status is PENDING or CANCELED
	if cashFlowRecord.State != cashflow.StatePENDING && cashFlowRecord.State != cashflow.StateCANCELED {
		return berr.ErrDB.Wrap(fmt.Errorf("Can only delete pending or canceled cashflows")).Op(op)
	}

	err = m.DbClient.CashFlow.DeleteOneID(input.ID).Exec(ctx)
	if err != nil {
		return berr.ErrDeleteFailed.Wrap(err).Op(op)
	}

	m.Logger.WithContext(ctx).Infof("%s - CashFlow %d deleted", op, input.ID)
	return nil
}

func (m *CashFlowModel) GetCashFlowReport(ctx context.Context, input dto.CashFlowReportInput) (*dto.CashFlowReportOutput, error) {
	op := "CashFlowModel.GetCashFlowReport"

	tx, err := m.Bob.BeginTx(ctx, nil)
	if err != nil {
		return nil, berr.ErrDB.Wrap(err).Op(op)
	}
	defer func() {
		if err != nil {
			tx.Rollback(ctx)
		} else {
			tx.Commit(ctx)
		}
	}()

	// Execute main report query
	reportQuery := queries.CashFlowReport(input.FromDate, input.ToDate)
	reportRow, err := reportQuery.One(ctx, tx)
	if err != nil {
		return nil, berr.ErrDB.Wrap(err).Op(op)
	}

	// Execute income by category query
	incomeQuery := queries.CashFlowIncomeByCategory(input.FromDate, input.ToDate)
	incomeRows, err := incomeQuery.All(ctx, tx)
	if err != nil {
		return nil, berr.ErrDB.Wrap(err).Op(op)
	}

	// Execute expense by category query
	expenseQuery := queries.CashFlowExpenseByCategory(input.FromDate, input.ToDate)
	expenseRows, err := expenseQuery.All(ctx, tx)
	if err != nil {
		return nil, berr.ErrDB.Wrap(err).Op(op)
	}

	// Convert income categories
	incomeCategories := make([]*dto.CategorySummaryOutput, 0, len(incomeRows))
	for _, row := range incomeRows {
		incomeCategories = append(incomeCategories, &dto.CategorySummaryOutput{
			CategoryID:   int(row.CategoryID),
			CategoryName: row.CategoryName,
			Amount:       int(row.Amount),
			Count:        int(row.Count),
		})
	}

	// Convert expense categories
	expenseCategories := make([]*dto.CategorySummaryOutput, 0, len(expenseRows))
	for _, row := range expenseRows {
		expenseCategories = append(expenseCategories, &dto.CategorySummaryOutput{
			CategoryID:   int(row.CategoryID),
			CategoryName: row.CategoryName,
			Amount:       int(row.Amount),
			Count:        int(row.Count),
		})
	}

	// Generate period string
	period := "All time"
	if !input.FromDate.IsZero() && !input.ToDate.IsZero() {
		period = fmt.Sprintf("%s to %s", input.FromDate.Format("2006-01-02"), input.ToDate.Format("2006-01-02"))
	} else if !input.FromDate.IsZero() {
		period = fmt.Sprintf("From %s", input.FromDate.Format("2006-01-02"))
	} else if !input.ToDate.IsZero() {
		period = fmt.Sprintf("To %s", input.ToDate.Format("2006-01-02"))
	}

	// Build output
	output := &dto.CashFlowReportOutput{
		CashFlowSummaryReportOutput: dto.CashFlowSummaryReportOutput{
			TotalIncome:           int(reportRow.TotalIncome.IntPart()),
			TotalExpense:          int(reportRow.TotalExpense.IntPart()),
			NetAmount:             reportRow.NetAmount,
			PendingApprovalCount:  int(reportRow.PendingApprovalCount),
			PendingApprovalAmount: int(reportRow.PendingApprovalAmount.IntPart()),
			// Payment methods totals (paid transactions)
			TotalCashAll:       int(reportRow.TotalCashAll.IntPart()),
			TotalCreditCardAll: int(reportRow.TotalCreditCardAll.IntPart()),
			TotalMposAll:       int(reportRow.TotalMposAll.IntPart()),
			TotalBankAll:       int(reportRow.TotalBankAll.IntPart()),
			TotalMomoAll:       int(reportRow.TotalMomoAll.IntPart()),
			// Payment methods for pending transactions
			PendingCash:       int(reportRow.PendingCash.IntPart()),
			PendingCreditCard: int(reportRow.PendingCreditCard.IntPart()),
			PendingMpos:       int(reportRow.PendingMpos.IntPart()),
			PendingBank:       int(reportRow.PendingBank.IntPart()),
			PendingMomo:       int(reportRow.PendingMomo.IntPart()),
			IncomeByCategory:  incomeCategories,
			ExpenseByCategory: expenseCategories,
		},
		Period:      period,
		GeneratedAt: time.Now(),
	}

	return output, nil
}

// GetCashFlowPeriodReport generates period-based cash flow reports
func (m *CashFlowModel) GetCashFlowPeriodReport(ctx context.Context, input dto.CashFlowPeriodInput) (*dto.CashFlowPeriodOutput, error) {
	op := "CashFlowModel.GetCashFlowPeriodReport"

	periods := m.calculatePeriods(input.PeriodType, input.Date, input.PeriodCount)
	if len(periods) == 0 {
		return nil, berr.ErrDB.Wrap(fmt.Errorf("Invalid period type or date")).Op(op)
	}

	tx, err := m.Bob.BeginTx(ctx, nil)
	if err != nil {
		return nil, berr.ErrDB.Wrap(err).Op(op)
	}
	defer func() {
		if err != nil {
			tx.Rollback(ctx)
		} else {
			tx.Commit(ctx)
		}
	}()

	periodSummaries := make([]dto.PeriodSummaryOutput, len(periods))
	for i, period := range periods {
		summary, err := m.generatePeriodSummary(ctx, tx, period)
		if err != nil {
			return nil, berr.ErrDB.Wrap(err).Op(op)
		}
		periodSummaries[i] = *summary
	}

	output := &dto.CashFlowPeriodOutput{
		CurrentPeriod:   periodSummaries[0],
		PreviousPeriods: periodSummaries[1:],
		PeriodType:      input.PeriodType,
		GeneratedAt:     time.Now(),
	}

	return output, nil
}

func (m *CashFlowModel) calculatePeriods(periodType string, date time.Time, periodCount int) []dto.Period {
	var periods []dto.Period

	switch periodType {
	case "week":
		startOfWeek := date.AddDate(0, 0, -int(date.Weekday())+1)
		if date.Weekday() == time.Sunday {
			startOfWeek = startOfWeek.AddDate(0, 0, -7)
		}

		for i := 0; i < periodCount; i++ {
			weekStart := startOfWeek.AddDate(0, 0, -7*i)
			weekEnd := weekStart.AddDate(0, 0, 6)

			year, week := weekStart.ISOWeek()
			periodLabel := fmt.Sprintf("Tuần %d (%s)", week,
				fmt.Sprintf("%s-%s",
					weekStart.Format("02/01"),
					weekEnd.Format("02/01/2006")))

			periods = append(periods, dto.Period{
				Period:      fmt.Sprintf("%d-W%02d", year, week),
				PeriodLabel: periodLabel,
				StartDate:   weekStart,
				EndDate:     weekEnd.Add(23*time.Hour + 59*time.Minute + 59*time.Second),
			})
		}

	case "month":
		year, month, _ := date.Date()

		for i := 0; i < periodCount; i++ {
			targetMonth := month - time.Month(i)
			targetYear := year

			for targetMonth < 1 {
				targetMonth += 12
				targetYear--
			}

			monthStart := time.Date(targetYear, targetMonth, 1, 0, 0, 0, 0, date.Location())
			monthEnd := monthStart.AddDate(0, 1, -1).Add(23*time.Hour + 59*time.Minute + 59*time.Second)

			periodLabel := fmt.Sprintf("Tháng %d/%d", int(targetMonth), targetYear)

			periods = append(periods, dto.Period{
				Period:      fmt.Sprintf("%d-%02d", targetYear, int(targetMonth)),
				PeriodLabel: periodLabel,
				StartDate:   monthStart,
				EndDate:     monthEnd,
			})
		}

	case "year":
		year := date.Year()

		for i := 0; i < periodCount; i++ {
			targetYear := year - i
			yearStart := time.Date(targetYear, 1, 1, 0, 0, 0, 0, date.Location())
			yearEnd := time.Date(targetYear, 12, 31, 23, 59, 59, 0, date.Location())

			periodLabel := fmt.Sprintf("Năm %d", targetYear)

			periods = append(periods, dto.Period{
				Period:      fmt.Sprintf("%d", targetYear),
				PeriodLabel: periodLabel,
				StartDate:   yearStart,
				EndDate:     yearEnd,
			})
		}
	}

	return periods
}

func (m *CashFlowModel) generatePeriodSummary(ctx context.Context, tx bob.Transaction, period dto.Period) (*dto.PeriodSummaryOutput, error) {
	reportInput := dto.CashFlowReportInput{
		FromDate: period.StartDate,
		ToDate:   period.EndDate,
	}

	reportQuery := queries.CashFlowReport(reportInput.FromDate, reportInput.ToDate)
	reportRow, err := reportQuery.One(ctx, tx)
	if err != nil {
		return nil, err
	}

	// Department-first queries (department → category)
	incomeByDeptQuery := queries.CashFlowIncomeByDepartment(reportInput.FromDate, reportInput.ToDate)
	incomeByDeptRows, err := incomeByDeptQuery.All(ctx, tx)
	if err != nil {
		return nil, err
	}

	expenseByDeptQuery := queries.CashFlowExpenseByDepartment(reportInput.FromDate, reportInput.ToDate)
	expenseByDeptRows, err := expenseByDeptQuery.All(ctx, tx)
	if err != nil {
		return nil, err
	}

	// Category-first queries (category → department)
	incomeByCatQuery := queries.CashFlowIncomeByCategoryDepartment(reportInput.FromDate, reportInput.ToDate)
	incomeByCatRows, err := incomeByCatQuery.All(ctx, tx)
	if err != nil {
		return nil, err
	}

	expenseByCatQuery := queries.CashFlowExpenseByCategoryDepartment(reportInput.FromDate, reportInput.ToDate)
	expenseByCatRows, err := expenseByCatQuery.All(ctx, tx)
	if err != nil {
		return nil, err
	}

	// Group department-first data
	incomeDepartments := m.groupIncomeByDepartment(incomeByDeptRows)
	expenseDepartments := m.groupExpenseByDepartment(expenseByDeptRows)

	// Group category-first data
	incomeCategories := m.groupIncomeByCategoryDepartment(incomeByCatRows)
	expenseCategories := m.groupExpenseByCategoryDepartment(expenseByCatRows)

	transactionCount, err := m.DbClient.CashFlow.Query().
		Where(
			cashflow.CreatedAtGTE(period.StartDate),
			cashflow.CreatedAtLTE(period.EndDate),
		).
		Count(ctx)
	if err != nil {
		transactionCount = 0
	}

	summary := &dto.PeriodSummaryOutput{
		Period:           period.Period,
		PeriodLabel:      period.PeriodLabel,
		StartDate:        period.StartDate,
		EndDate:          period.EndDate,
		TotalIncome:      int(reportRow.TotalIncome.IntPart()),
		TotalExpense:     int(reportRow.TotalExpense.IntPart()),
		NetAmount:        reportRow.NetAmount,
		TransactionCount: transactionCount,
		TotalCash:        int(reportRow.TotalCashAll.IntPart()),
		TotalCreditCard:  int(reportRow.TotalCreditCardAll.IntPart()),
		TotalMpos:        int(reportRow.TotalMposAll.IntPart()),
		TotalBank:        int(reportRow.TotalBankAll.IntPart()),
		TotalMomo:        int(reportRow.TotalMomoAll.IntPart()),
		// Department-first grouping
		IncomeByDepartment:  incomeDepartments,
		ExpenseByDepartment: expenseDepartments,
		// Category-first grouping
		IncomeByCategory:  incomeCategories,
		ExpenseByCategory: expenseCategories,
	}

	return summary, nil
}

// groupIncomeByDepartment groups income data by department and categories
func (m *CashFlowModel) groupIncomeByDepartment(rows []queries.CashFlowIncomeByDepartmentRow) []*dto.DepartmentSummaryOutput {
	departmentMap := make(map[int64]*dto.DepartmentSummaryOutput)

	for _, row := range rows {
		departmentID := row.DepartmentID

		// Get or create department summary
		if _, exists := departmentMap[departmentID]; !exists {
			departmentMap[departmentID] = &dto.DepartmentSummaryOutput{
				DepartmentID:   int(departmentID),
				DepartmentName: row.DepartmentName,
				TotalAmount:    0,
				TotalCount:     0,
				Categories:     []*dto.DepartmentCategorySummaryOutput{},
			}
		}

		// Add category to department
		categoryData := &dto.DepartmentCategorySummaryOutput{
			CategoryID:   int(row.CategoryID),
			CategoryName: row.CategoryName,
			Amount:       row.Amount,
			Count:        int(row.Count),
		}

		departmentMap[departmentID].Categories = append(departmentMap[departmentID].Categories, categoryData)
		departmentMap[departmentID].TotalAmount += row.Amount
		departmentMap[departmentID].TotalCount += int(row.Count)
	}

	// Convert map to slice
	departments := make([]*dto.DepartmentSummaryOutput, 0, len(departmentMap))
	for _, dept := range departmentMap {
		departments = append(departments, dept)
	}

	return departments
}

// groupExpenseByDepartment groups expense data by department and categories
func (m *CashFlowModel) groupExpenseByDepartment(rows []queries.CashFlowExpenseByDepartmentRow) []*dto.DepartmentSummaryOutput {
	departmentMap := make(map[int64]*dto.DepartmentSummaryOutput)

	for _, row := range rows {
		departmentID := row.DepartmentID

		// Get or create department summary
		if _, exists := departmentMap[departmentID]; !exists {
			departmentMap[departmentID] = &dto.DepartmentSummaryOutput{
				DepartmentID:   int(departmentID),
				DepartmentName: row.DepartmentName,
				TotalAmount:    0,
				TotalCount:     0,
				Categories:     []*dto.DepartmentCategorySummaryOutput{},
			}
		}

		// Add category to department
		categoryData := &dto.DepartmentCategorySummaryOutput{
			CategoryID:   int(row.CategoryID),
			CategoryName: row.CategoryName,
			Amount:       row.Amount,
			Count:        int(row.Count),
		}

		departmentMap[departmentID].Categories = append(departmentMap[departmentID].Categories, categoryData)
		departmentMap[departmentID].TotalAmount += row.Amount
		departmentMap[departmentID].TotalCount += int(row.Count)
	}

	// Convert map to slice
	departments := make([]*dto.DepartmentSummaryOutput, 0, len(departmentMap))
	for _, dept := range departmentMap {
		departments = append(departments, dept)
	}

	return departments
}

// groupIncomeByCategoryDepartment groups income data by category and then by department
func (m *CashFlowModel) groupIncomeByCategoryDepartment(rows []queries.CashFlowIncomeByCategoryDepartmentRow) []*dto.CategorySummaryDepartmentOutput {
	categoryMap := make(map[int64]*dto.CategorySummaryDepartmentOutput)

	for _, row := range rows {
		categoryID := row.CategoryID

		// Get or create category summary
		if _, exists := categoryMap[categoryID]; !exists {
			categoryMap[categoryID] = &dto.CategorySummaryDepartmentOutput{
				CategoryID:   int(categoryID),
				CategoryName: row.CategoryName,
				TotalAmount:  0,
				TotalCount:   0,
				Departments:  []*dto.CategoryDepartmentSummaryOutput{},
			}
		}

		// Add department to category
		departmentData := &dto.CategoryDepartmentSummaryOutput{
			DepartmentID:   int(row.DepartmentID),
			DepartmentName: row.DepartmentName,
			Amount:         row.Amount,
			Count:          int(row.Count),
		}

		categoryMap[categoryID].Departments = append(categoryMap[categoryID].Departments, departmentData)
		categoryMap[categoryID].TotalAmount += row.Amount
		categoryMap[categoryID].TotalCount += int(row.Count)
	}

	// Convert map to slice
	categories := make([]*dto.CategorySummaryDepartmentOutput, 0, len(categoryMap))
	for _, cat := range categoryMap {
		categories = append(categories, cat)
	}

	return categories
}

// groupExpenseByCategoryDepartment groups expense data by category and then by department
func (m *CashFlowModel) groupExpenseByCategoryDepartment(rows []queries.CashFlowExpenseByCategoryDepartmentRow) []*dto.CategorySummaryDepartmentOutput {
	categoryMap := make(map[int64]*dto.CategorySummaryDepartmentOutput)

	for _, row := range rows {
		categoryID := row.CategoryID

		// Get or create category summary
		if _, exists := categoryMap[categoryID]; !exists {
			categoryMap[categoryID] = &dto.CategorySummaryDepartmentOutput{
				CategoryID:   int(categoryID),
				CategoryName: row.CategoryName,
				TotalAmount:  0,
				TotalCount:   0,
				Departments:  []*dto.CategoryDepartmentSummaryOutput{},
			}
		}

		// Add department to category
		departmentData := &dto.CategoryDepartmentSummaryOutput{
			DepartmentID:   int(row.DepartmentID),
			DepartmentName: row.DepartmentName,
			Amount:         row.Amount,
			Count:          int(row.Count),
		}

		categoryMap[categoryID].Departments = append(categoryMap[categoryID].Departments, departmentData)
		categoryMap[categoryID].TotalAmount += row.Amount
		categoryMap[categoryID].TotalCount += int(row.Count)
	}

	// Convert map to slice
	categories := make([]*dto.CategorySummaryDepartmentOutput, 0, len(categoryMap))
	for _, cat := range categoryMap {
		categories = append(categories, cat)
	}

	return categories
}

// CashFlowExporter implements ConfigurableExporter for CashFlow list export
type CashFlowExporter struct {
	cashFlows []types.CashFlowResponse
}

func NewCashFlowExporter(cashFlows []types.CashFlowResponse) excelutil.ConfigurableExporter {
	return &CashFlowExporter{cashFlows: cashFlows}
}

func (e *CashFlowExporter) GetConfig() excelutil.ExportConfig {
	return excelutil.ExportConfig{
		FileName:  fmt.Sprintf("danh-sach-thu-chi-%s", time.Now().Format("2006-01-02")),
		SheetName: "Cash Flow List",
		Columns: []excelutil.ColumnConfig{
			{Header: "STT", Width: 8, Formatter: excelutil.IntFormatter},
			{Header: "Loại", Width: 12, Formatter: excelutil.StringFormatter},
			{Header: "Số tiền", Width: 15, Formatter: excelutil.IntFormatter},
			{Header: "Mô tả", Width: 30, Formatter: excelutil.StringFormatter},
			{Header: "Trạng thái", Width: 12, Formatter: excelutil.StringFormatter},
			{Header: "Người tạo", Width: 20, Formatter: excelutil.StringFormatter},
			{Header: "Người chi/thu", Width: 20, Formatter: excelutil.StringFormatter},
			{Header: "Danh mục", Width: 20, Formatter: excelutil.StringFormatter},
			{Header: "Người duyệt", Width: 20, Formatter: excelutil.StringFormatter},
			{Header: "Tiền mặt", Width: 12, Formatter: excelutil.IntFormatter},
			{Header: "Thẻ tín dụng", Width: 15, Formatter: excelutil.IntFormatter},
			{Header: "MPOS", Width: 12, Formatter: excelutil.IntFormatter},
			{Header: "Chuyển khoản", Width: 12, Formatter: excelutil.IntFormatter},
			{Header: "MoMo", Width: 12, Formatter: excelutil.IntFormatter},
			{Header: "Ngày tạo", Width: 18, Formatter: excelutil.StringFormatter},
			{Header: "Ngày duyệt", Width: 18, Formatter: excelutil.StringFormatter},
		},
	}
}

func (e *CashFlowExporter) GetRows() ([]map[string]interface{}, error) {
	rows := make([]map[string]interface{}, len(e.cashFlows))
	for i, cf := range e.cashFlows {
		// Format type
		typeDisplay := "Thu"
		if cf.Type == "expense" {
			typeDisplay = "Chi"
		}

		// Format state
		stateDisplay := cf.State
		switch cf.State {
		case "PENDING":
			stateDisplay = "Chờ duyệt"
		case "APPROVED":
			stateDisplay = "Đã duyệt"
		case "REJECTED":
			stateDisplay = "Từ chối"
		case "PAID":
			stateDisplay = "Đã thanh toán"
		case "CANCELED":
			stateDisplay = "Đã hủy"
		}

		// Get creator name
		creatorName := ""
		if cf.Creator != nil {
			creatorName = cf.Creator.Name
		}

		// Get counterpart name
		counterpartName := ""
		if cf.Counterpart != nil {
			counterpartName = cf.Counterpart.Name
		}

		// Get approver name
		approverName := ""
		if cf.Approver != nil {
			approverName = cf.Approver.Name
		}

		// Format paid date
		paidAtDisplay := ""
		if cf.PaidAt != nil {
			paidAtDisplay = *cf.PaidAt
		}

		// Strip HTML tags from description
		description := stripHTMLTags(cf.Description)

		rows[i] = map[string]interface{}{
			"STT":           i + 1,
			"Loại":          typeDisplay,
			"Số tiền":       cf.Amount,
			"Mô tả":         description,
			"Trạng thái":    stateDisplay,
			"Người tạo":     creatorName,
			"Người chi/thu": counterpartName,
			"Danh mục":      "", // CashFlow doesn't have direct category relation
			"Người duyệt":   approverName,
			"Tiền mặt":      cf.Cash,
			"Thẻ tín dụng":  cf.CreditCard,
			"MPOS":          cf.Mpos,
			"Chuyển khoản":  cf.Bank,
			"MoMo":          cf.Momo,
			"Ngày tạo":      cf.CreatedAt,
			"Ngày duyệt":    paidAtDisplay,
		}
	}
	return rows, nil
}

// stripHTMLTags removes HTML tags and decodes HTML entities from text
func stripHTMLTags(htmlText string) string {
	// First decode HTML entities
	decoded := html.UnescapeString(htmlText)

	// Remove HTML tags using regex
	re := regexp.MustCompile(`<[^>]*>`)
	cleaned := re.ReplaceAllString(decoded, "")

	// Clean up extra whitespace
	cleaned = strings.TrimSpace(cleaned)
	cleaned = regexp.MustCompile(`\s+`).ReplaceAllString(cleaned, " ")

	return cleaned
}
