package model

import (
	"bcare/api/internal/dto"
	"bcare/common/berr"
	"bcare/common/bquery"
	"bcare/common/cast"
	"bcare/common/ctxdata"
	"bcare/ent"
	"bcare/ent/cashflownote"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type CashFlowNoteModel struct {
	Logger   logx.Logger
	DbClient *ent.Client
}

func NewCashFlowNoteModel(dbClient *ent.Client) *CashFlowNoteModel {
	logger := logx.WithContext(context.Background()).WithFields(logx.Field("model", "CashFlowNoteModel"))
	return &CashFlowNoteModel{
		Logger:   logger,
		DbClient: dbClient,
	}
}

func (m *CashFlowNoteModel) Create(ctx context.Context, input *dto.CreateCashFlowNoteInput) (*dto.CashFlowNoteOutput, error) {
	op := "CashFlowNoteModel.Create"
	userID := ctxdata.GetUidFromCtx(ctx)

	cashFlowNoteRecord := new(ent.CashFlowNote)
	err := cast.InputToEnt(cashFlowNoteRecord, input)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	cashFlowNoteRecord.UserID = userID

	createdCashFlowNote, err := m.DbClient.CashFlowNote.Create().
		SetCashFlowNote(cashFlowNoteRecord).
		Save(ctx)

	if err != nil {
		return nil, berr.ErrCreateFailed.Wrap(err).Op(op)
	}

	m.Logger.WithContext(ctx).Infof("%s - CashFlowNote %d created by user %d", op, createdCashFlowNote.ID, userID)

	output := new(dto.CashFlowNoteOutput)
	err = cast.ConvertViaJson(output, createdCashFlowNote)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}
	return output, nil
}

func (m *CashFlowNoteModel) Get(ctx context.Context, id int) (*dto.CashFlowNoteOutput, error) {
	op := "CashFlowNoteModel.Get"

	cashFlowNoteRecord, err := m.DbClient.CashFlowNote.Query().
		Where(cashflownote.ID(id)).
		First(ctx)

	if err != nil {
		if ent.IsNotFound(err) {
			return nil, berr.ErrNotFound.Wrap(err).Op(op)
		}
		return nil, berr.ErrDB.Wrap(err).Op(op)
	}

	output := new(dto.CashFlowNoteOutput)
	err = cast.ConvertViaJson(output, cashFlowNoteRecord)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	return output, nil
}

func (m *CashFlowNoteModel) List(ctx context.Context, input dto.ListCashFlowNoteInput) (*dto.ListCashFlowNoteOutput, error) {
	op := "CashFlowNoteModel.List"

	limit, offset := bquery.Paging(input.PageSize, input.Page)
	order := bquery.Ordering(input.OrderBy)

	query := m.DbClient.CashFlowNote.Query().WithCreator().Order(order)

	if input.Filter.CashFlowID != 0 {
		query = query.Where(cashflownote.CashFlowID(input.Filter.CashFlowID))
	}
	if input.Filter.UserID != 0 {
		query = query.Where(cashflownote.UserID(input.Filter.UserID))
	}

	total, err := query.Clone().Count(ctx)
	if err != nil {
		return nil, berr.ErrListFailed.Wrap(err).Op(op)
	}

	output := &dto.ListCashFlowNoteOutput{
		CashFlowNotes: []dto.CashFlowNoteOutput{},
		Total:         total,
		TotalPage:     (total + input.PageSize - 1) / input.PageSize,
	}

	if total == 0 {
		return output, nil
	}

	cashFlowNotes, err := query.Limit(limit).Offset(offset).All(ctx)
	if err != nil {
		return nil, berr.ErrListFailed.Wrap(err).Op(op)
	}

	err = cast.ConvertViaJson(&output.CashFlowNotes, cashFlowNotes)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	return output, nil
}

func (m *CashFlowNoteModel) Update(ctx context.Context, input *dto.UpdateCashFlowNoteInput) (*dto.CashFlowNoteOutput, error) {
	op := "CashFlowNoteModel.Update"

	cashFlowNoteRecord, err := m.DbClient.CashFlowNote.Query().
		Where(cashflownote.ID(input.ID)).
		First(ctx)

	if err != nil {
		if ent.IsNotFound(err) {
			return nil, berr.ErrNotFound.Wrap(err).Op(op)
		}
		return nil, berr.ErrDB.Wrap(err).Op(op)
	}

	err = cast.InputToEnt(cashFlowNoteRecord, input)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	updatedCashFlowNote, err := m.DbClient.CashFlowNote.UpdateOneID(input.ID).
		SetCashFlowNote(cashFlowNoteRecord, input.Modified...).
		Save(ctx)

	if err != nil {
		return nil, berr.ErrUpdateFailed.Wrap(err).Op(op)
	}

	m.Logger.WithContext(ctx).Infof("%s - CashFlowNote %d updated", op, input.ID)

	output := new(dto.CashFlowNoteOutput)
	err = cast.ConvertViaJson(output, updatedCashFlowNote)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	return output, nil
}

func (m *CashFlowNoteModel) Delete(ctx context.Context, id int) error {
	op := "CashFlowNoteModel.Delete"

	_, err := m.DbClient.CashFlowNote.Get(ctx, id)
	if err != nil {
		if ent.IsNotFound(err) {
			return berr.ErrNotFound.Wrap(err).Op(op)
		}
		return berr.ErrDB.Wrap(err).Op(op)
	}

	err = m.DbClient.CashFlowNote.DeleteOneID(id).Exec(ctx)
	if err != nil {
		return berr.ErrDeleteFailed.Wrap(err).Op(op)
	}

	m.Logger.WithContext(ctx).Infof("%s - CashFlowNote %d deleted", op, id)
	return nil
}

func (m *CashFlowNoteModel) BulkDelete(ctx context.Context, cashflowId int64) error {
	op := "CashFlowNoteModel.BulkDelete"
	_, err := m.DbClient.CashFlowNote.Delete().Where(cashflownote.CashFlowID(int(cashflowId))).Exec(ctx)
	if err != nil {
		return berr.ErrDeleteFailed.Wrap(err).Op(op)
	}

	m.Logger.WithContext(ctx).Infof("%s - CashFlowNotes for cashflow %d deleted", op, cashflowId)
	return nil
}
