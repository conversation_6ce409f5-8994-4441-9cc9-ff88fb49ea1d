package model

import (
	"bcare/ent"
	"bcare/ent/taskdepartment"
	"time"
)

// SetTaskAssignmentCommonFields sets common fields for task assignment
func SetTaskAssignmentCommonFields(assignment *ent.TaskAssignment, task *ent.Task, dueAt time.Time) {
	assignment.TaskID = task.ID
	assignment.DueAt = dueAt
	assignment.Status = task.Status
	assignment.Serial = task.CurrentSerial
}

// SetTaskDepartmentCommonFields sets common fields for task department
func SetTaskDepartmentCommonFields(department *ent.TaskDepartment, task *ent.Task, dueAt time.Time) {
	department.TaskID = task.ID
	department.Serial = task.CurrentSerial
	department.Status = task.Status
	department.State = taskdepartment.State(task.State)
	department.DueAt = dueAt
	if task.CompletedAt != nil {
		department.CompletedAt = task.CompletedAt
	}
}
