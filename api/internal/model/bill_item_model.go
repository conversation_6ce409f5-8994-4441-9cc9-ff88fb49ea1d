package model

import (
	"bcare/api/internal/dto"
	"bcare/common/bconst"
	"bcare/common/berr"
	"bcare/common/bquery"
	"bcare/common/cast"
	"bcare/common/ctxdata"
	"bcare/ent"
	"bcare/ent/billitem"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type BillItemModel struct {
	Logger   logx.Logger
	DbClient *ent.Client
}

func NewBillItemModel(dbClient *ent.Client) *BillItemModel {
	logger := logx.WithContext(context.Background()).WithFields(logx.Field("model", "BillItemModel"))
	return &BillItemModel{
		Logger:   logger,
		DbClient: dbClient,
	}
}

func (m *BillItemModel) Create(ctx context.Context, input *dto.CreateBillItemInput) (*dto.BillItemOutput, error) {
	op := "BillItemModel.Create"
	userID := ctxdata.GetUidFromCtx(ctx)

	billItemRecord := new(ent.BillItem)
	err := cast.InputToEnt(billItemRecord, input)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	createdBillItem, err := m.DbClient.BillItem.Create().
		SetBillItem(billItemRecord).
		Save(ctx)
	if err != nil {
		return nil, berr.ErrCreateFailed.Wrap(err).Op(op)
	}

	m.Logger.WithContext(ctx).Infof("%s - BillItem %d created by user %d", op, createdBillItem.ID, userID)

	output := new(dto.BillItemOutput)
	err = cast.ConvertViaJson(output, createdBillItem)
	if err != nil {
		m.Logger.WithContext(ctx).Errorf("%s - Failed to convert bill item output: %v", op, err)
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	return output, nil
}

func (m *BillItemModel) Get(ctx context.Context, id int) (*dto.BillItemOutput, error) {
	op := "BillItemModel.Get"

	billItemRecord, err := m.DbClient.BillItem.Query().
		WithAttachment().
		WithAllocations().
		Where(billitem.ID(id)).
		Only(ctx)
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, berr.ErrNotFound.Wrap(err).Op(op)
		}
		return nil, berr.ErrDB.Wrap(err).Op(op)
	}

	output := new(dto.BillItemOutput)
	err = cast.ConvertViaJson(output, billItemRecord)
	if err != nil {
		m.Logger.WithContext(ctx).Errorf("%s - Failed to convert bill item output: %v", op, err)
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	return output, nil
}

func (m *BillItemModel) List(ctx context.Context, input *dto.ListBillItemInput) (*dto.ListBillItemOutput, error) {
	op := "BillItemModel.List"

	limit, offset := bquery.Paging(input.PageSize, input.Page)
	order := bquery.Ordering(input.OrderBy)

	query := m.DbClient.BillItem.Query().
		WithAttachment().
		WithAllocations().
		Order(order)

	if input.Filter.BillID != 0 {
		query = query.Where(billitem.BillID(input.Filter.BillID))
	}
	if input.Filter.AttachmentID != 0 {
		query = query.Where(billitem.AttachmentID(input.Filter.AttachmentID))
	}

	total, err := query.Clone().Count(ctx)
	if err != nil {
		return nil, berr.ErrListFailed.Wrap(err).Op(op)
	}

	output := &dto.ListBillItemOutput{
		BillItems: []dto.BillItemOutput{},
		Total:     total,
		TotalPage: (total + limit - 1) / limit,
	}

	if total == 0 {
		return output, nil
	}

	billItems, err := query.Limit(limit).Offset(offset).All(ctx)
	if err != nil {
		return nil, berr.ErrListFailed.Wrap(err).Op(op)
	}

	err = cast.ConvertViaJson(&output.BillItems, billItems)
	if err != nil {
		m.Logger.WithContext(ctx).Errorf("%s - Failed to convert bill items output: %v", op, err)
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	return output, nil
}

func (m *BillItemModel) CreateForAttachments(ctx context.Context, dealID int, attachmentOutputs []dto.AttachmentOutput) error {
	op := "BillItemModel.CreateForAttachments"
	userID := ctxdata.GetUidFromCtx(ctx)

	for _, att := range attachmentOutputs {
		amount := att.Price*float64(att.Quantity) - att.Discount
		billItemRecord := &ent.BillItem{
			Status: bconst.StatusNormal,
			UserID: userID,
			Amount: amount,
		}

		_, err := m.DbClient.BillItem.Create().
			SetBillItem(billItemRecord).
			SetAttachmentID(att.ID).
			Save(ctx)
		if err != nil {
			return berr.ErrCreateFailed.Wrap(err).Op(op)
		}
	}

	m.Logger.WithContext(ctx).Infof("%s - Bill items created for deal %d by user %d", op, dealID, userID)
	return nil
}

func (m *BillItemModel) Update(ctx context.Context, input *dto.UpdateBillItemInput) (*dto.BillItemOutput, error) {
	op := "BillItemModel.Update"
	userID := ctxdata.GetUidFromCtx(ctx)

	billItemRecord, err := m.DbClient.BillItem.Get(ctx, input.ID)
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, berr.ErrNotFound.Wrap(err).Op(op)
		}
		return nil, berr.ErrDB.Wrap(err).Op(op)
	}

	err = cast.InputToEnt(billItemRecord, input)
	if err != nil {
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	updatedBillItem, err := m.DbClient.BillItem.UpdateOne(billItemRecord).
		SetBillItem(billItemRecord, input.Modified...).
		Save(ctx)
	if err != nil {
		return nil, berr.ErrUpdateFailed.Wrap(err).Op(op)
	}

	m.Logger.WithContext(ctx).Infof("%s - BillItem %d updated by user %d", op, input.ID, userID)

	output := new(dto.BillItemOutput)
	err = cast.ConvertViaJson(output, updatedBillItem)
	if err != nil {
		m.Logger.WithContext(ctx).Errorf("%s - Failed to convert bill item output: %v", op, err)
		return nil, berr.ErrCopyFailed.Wrap(err).Op(op)
	}

	return output, nil
}

func (m *BillItemModel) Delete(ctx context.Context, id int) error {
	op := "BillItemModel.Delete"
	userID := ctxdata.GetUidFromCtx(ctx)

	_, err := m.DbClient.BillItem.Get(ctx, id)
	if err != nil {
		if ent.IsNotFound(err) {
			return berr.ErrNotFound.Wrap(err).Op(op)
		}
		return berr.ErrDB.Wrap(err).Op(op)
	}

	err = m.DbClient.BillItem.DeleteOneID(id).Exec(ctx)
	if err != nil {
		return berr.ErrDeleteFailed.Wrap(err).Op(op)
	}

	m.Logger.WithContext(ctx).Infof("%s - BillItem %d deleted by user %d", op, id, userID)
	return nil
}
