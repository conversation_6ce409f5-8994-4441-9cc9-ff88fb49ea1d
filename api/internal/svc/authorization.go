package svc

import (
	"bcare/api/internal/auth"
	"bcare/common/bauth"
	"bcare/common/ctxdata"
	"context"
	"strconv"
)

type APIAuthorization struct {
	Appointment              auth.IAppointmentAuth
	Attachment               auth.IAttachmentAuth
	Bill                     auth.IBillAuth
	BillItem                 auth.IBillItemAuth
	Call                     auth.ICallAuth
	CashFlow                 auth.ICashFlowAuth
	CashFlowItem             auth.ICashFlowItemAuth
	CashFlowNote             auth.ICashFlowNoteAuth
	Tag                      auth.ITagAuth
	Deal                     auth.IDealAuth
	DealUser                 auth.IDealUserAuth
	Discount                 auth.IDiscountAuth
	DiscountUsage            auth.IDiscountUsageAuth
	EconomyDeposit           auth.IEconomyDepositAuth
	EconomyPayment           auth.IEconomyPaymentAuth
	EconomyPaymentAllocation auth.IEconomyPaymentAllocationAuth
	File                     auth.IFileAuth
	Installment              auth.IInstallmentAuth
	InstallmentPlan          auth.IInstallmentPlanAuth
	Note                     auth.INoteAuth
	Operation                auth.IOperationAuth
	Person                   auth.IPersonAuth
	PersonAssignment         auth.IPersonAssignmentAuth
	Product                  auth.IProductAuth
	Schedule                 auth.IScheduleAuth
	Setting                  auth.ISettingAuth
	Task                     auth.ITaskAuth
	TaskNote                 auth.ITaskNoteAuth
	TaskDepartment           auth.ITaskDepartmentAuth
	TaskAssignment           auth.ITaskAssignmentAuth
	Track                    auth.ITrackAuth
	User                     auth.IUserAuth
	Material                 auth.IMaterialAuth
	OperationMaterial        auth.IOperationMaterialAuth
	MaterialUsage            auth.IMaterialUsageAuth
	IdentityFn               func(ctx context.Context) string
}

func NewAPIAuthorization(authorizer *bauth.Authorizer) *APIAuthorization {
	var IdentityFn = func(ctx context.Context) string {
		return strconv.Itoa(ctxdata.GetUidFromCtx(ctx))
	}

	return &APIAuthorization{
		Appointment:              auth.NewAppointmentAuth(authorizer, IdentityFn),
		Attachment:               auth.NewAttachmentAuth(authorizer, IdentityFn),
		Bill:                     auth.NewBillAuth(authorizer, IdentityFn),
		BillItem:                 auth.NewBillItemAuth(authorizer, IdentityFn),
		Call:                     auth.NewCallAuth(authorizer, IdentityFn),
		CashFlow:                 auth.NewCashFlowAuth(authorizer, IdentityFn),
		CashFlowItem:             auth.NewCashFlowItemAuth(authorizer, IdentityFn),
		CashFlowNote:             auth.NewCashFlowNoteAuth(authorizer, IdentityFn),
		Tag:                      auth.NewTagAuth(authorizer, IdentityFn),
		Deal:                     auth.NewDealAuth(authorizer, IdentityFn),
		DealUser:                 auth.NewDealUserAuth(authorizer, IdentityFn),
		Discount:                 auth.NewDiscountAuth(authorizer, IdentityFn),
		DiscountUsage:            auth.NewDiscountUsageAuth(authorizer, IdentityFn),
		EconomyDeposit:           auth.NewEconomyDepositAuth(authorizer, IdentityFn),
		EconomyPayment:           auth.NewEconomyPaymentAuth(authorizer, IdentityFn),
		EconomyPaymentAllocation: auth.NewEconomyPaymentAllocationAuth(authorizer, IdentityFn),
		File:                     auth.NewFileAuth(authorizer, IdentityFn),
		Installment:              auth.NewInstallmentAuth(authorizer, IdentityFn),
		InstallmentPlan:          auth.NewInstallmentPlanAuth(authorizer, IdentityFn),
		Note:                     auth.NewNoteAuth(authorizer, IdentityFn),
		Operation:                auth.NewOperationAuth(authorizer, IdentityFn),
		Person:                   auth.NewPersonAuth(authorizer, IdentityFn),
		PersonAssignment:         auth.NewPersonAssignmentAuth(authorizer, IdentityFn),
		Product:                  auth.NewProductAuth(authorizer, IdentityFn),
		Schedule:                 auth.NewScheduleAuth(authorizer, IdentityFn),
		Setting:                  auth.NewSettingAuth(authorizer, IdentityFn),
		Task:                     auth.NewTaskAuth(authorizer, IdentityFn),
		TaskNote:                 auth.NewTaskNoteAuth(authorizer, IdentityFn),
		TaskDepartment:           auth.NewTaskDepartmentAuth(authorizer, IdentityFn),
		TaskAssignment:           auth.NewTaskAssignmentAuth(authorizer, IdentityFn),
		Track:                    auth.NewTrackAuth(authorizer, IdentityFn),
		User:                     auth.NewUserAuth(authorizer, IdentityFn),
		Material:                 auth.NewMaterialAuth(authorizer, IdentityFn),
		OperationMaterial:        auth.NewOperationMaterialAuth(authorizer, IdentityFn),
		MaterialUsage:            auth.NewMaterialUsageAuth(authorizer, IdentityFn),
		IdentityFn:               IdentityFn,
	}
}
