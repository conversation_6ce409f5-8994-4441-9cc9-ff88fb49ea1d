package svc

import (
	"bcare/api/internal/types"
	"bcare/common/bsms"
	"bcare/ent"
	"bcare/ent/setting"
	"context"
	"encoding/json"
	"github.com/zeromicro/go-zero/core/logx"
	"sync"
)

type SettingService struct {
	svcCtx         *ServiceContext
	ent            *ent.Client
	Call           *types.CallSetting
	PaymentMessage *types.PaymentMessageTemplates
	Sms            *bsms.SmsSetting
	Zns            *bsms.ZnsSetting
	Email          *bsms.EmailSetting
	Otp            *bsms.OtpSetting
	mu             sync.RWMutex
}

var (
	settingOnce     sync.Once
	settingInstance *SettingService
)

func NewSettingService(client *ent.Client, svcCtx *ServiceContext) *SettingService {
	settingOnce.Do(func() {
		settingInstance = &SettingService{
			ent:            client,
			svcCtx:         svcCtx, // Add service context
			Call:           &types.CallSetting{},
			Sms:            &bsms.SmsSetting{},
			Zns:            &bsms.ZnsSetting{},
			Email:          &bsms.EmailSetting{},
			Otp:            &bsms.OtpSetting{},
			PaymentMessage: &types.PaymentMessageTemplates{},
		}
	})

	settingInstance.ReloadAll()

	return settingInstance
}

func (ss *SettingService) ReloadAll() {
	settings, err := ss.ent.Setting.Query().Where(
		setting.And(
			setting.Or(
				setting.Category("call"),
				setting.Category("sms"),
				setting.Category("zns"),
				setting.Category("email"),
				setting.Category("otp"),
				setting.Category("sms_zns_payment"),
			),
			setting.Name("setting"),
		)).All(context.Background())

	if err != nil {
		logx.Errorf("load sms zns email call setting error %v", err)
		return
	}

	ss.mu.Lock()
	defer ss.mu.Unlock()

	for _, s := range settings {
		// Convert map[string]interface{} to JSON bytes
		jsonBytes, err := json.Marshal(s.Value)
		if err != nil {
			logx.Errorf("marshal %s setting error: %v", s.Category, err)
			continue
		}

		switch s.Category {
		case "sms":
			if err := json.Unmarshal(jsonBytes, ss.Sms); err != nil {
				logx.Errorf("unmarshal sms setting error: %v", err)
			}
		case "zns":
			if err := json.Unmarshal(jsonBytes, ss.Zns); err != nil {
				logx.Errorf("unmarshal zns setting error: %v", err)
			}

		case "call":
			if err := json.Unmarshal(jsonBytes, ss.Call); err != nil {
				logx.Errorf("unmarshal call setting error: %v", err)
			}
		case "email":
			if err := json.Unmarshal(jsonBytes, ss.Email); err != nil {
				logx.Errorf("unmarshal email setting error: %v", err)
			}
		case "otp":
			if err := json.Unmarshal(jsonBytes, ss.Otp); err != nil {
				logx.Errorf("unmarshal otp setting error: %v", err)
			}
		case "sms_zns_payment":
			if err := json.Unmarshal(jsonBytes, ss.PaymentMessage); err != nil {
				logx.Errorf("unmarshal sms_zns_payment setting error: %v", err)
			}
		}
	}
}

func (ss *SettingService) GetSetting(category string) (interface{}, bool) {
	ss.mu.RLock()
	defer ss.mu.RUnlock()

	switch category {
	case "sms":
		return ss.Sms, true
	case "zns":
		return ss.Zns, true
	case "call":
		return ss.Call, true
	case "email":
		return ss.Email, true
	case "sms_zns_payment":
		return ss.PaymentMessage, true
	case "otp":
		return ss.Otp, true
	default:
		return nil, false
	}

}

func (ss *SettingService) ReloadAllSvcSetting() {
	if ss.svcCtx == nil {
		logx.Error("ServiceContext is nil")
		return
	}

	if ss.svcCtx.Sms == nil {
		logx.Error("SMS service is nil in ServiceContext")
		return
	}

	if ss.svcCtx.Zns == nil {
		logx.Error("ZNS service is nil in ServiceContext")
		return
	}

	if ss.svcCtx.Email == nil {
		logx.Error("Email service is nil in ServiceContext")
		return
	}

	ss.svcCtx.Sms.SmsSetting = *ss.Sms
	ss.svcCtx.Zns.ZnsSetting = *ss.Zns
	ss.svcCtx.Email.EmailSetting = *ss.Email
}
