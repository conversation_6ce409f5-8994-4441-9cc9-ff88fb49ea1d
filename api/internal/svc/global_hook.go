package svc

import (
	"bcare/api/internal/event"
	"bcare/api/internal/types"
	"bcare/common/ctxdata"
	"bcare/ent"
	"context"
	"reflect"
	"time"

	"github.com/zeromicro/go-zero/core/logx"
)

// TrackingMode defines how an entity is tracked for history events
type TrackingMode string

const (
	// TrackingModeFull tracks both old and new values by querying the entity before mutation
	TrackingModeFull TrackingMode = "full"

	// TrackingModeHalf only tracks new values without querying old values
	TrackingModeHalf TrackingMode = "half"

	// TrackingModeIgnore doesn't track the entity at all
	TrackingModeIgnore TrackingMode = "ignore"
)

// trackedSchemas xác định những schema nào sẽ được Global Hook xử lý để ghi history.
var trackedSchemas = map[string]TrackingMode{
	"Deal":             TrackingModeFull,
	"Person":           TrackingModeFull,
	"Appointment":      TrackingModeFull,
	"Task":             TrackingModeFull,
	"User":             TrackingModeHalf,
	"Setting":          TrackingModeHalf,
	"Referral":         TrackingModeHalf,
	"TaskNote":         TrackingModeFull,
	"UserData":         TrackingModeHalf,
	"Track":            TrackingModeHalf,
	"Stage":            TrackingModeHalf,
	"TaskAssignment":   TrackingModeFull,
	"TaskDepartment":   TrackingModeFull,
	"Schedule":         TrackingModeHalf,
	"ProductOperation": TrackingModeHalf,
	"Product":          TrackingModeHalf,
	"Pipeline":         TrackingModeHalf,
	"PersonAssignment": TrackingModeFull,
	"PersonData":       TrackingModeHalf,
	"Note":             TrackingModeHalf,
	"Operation":        TrackingModeHalf,
	"FileUsage":        TrackingModeHalf,
	"Installment":      TrackingModeHalf,
	"InstallmentPlan":  TrackingModeHalf,
	"DiscountUsage":    TrackingModeHalf,
	"File":             TrackingModeHalf,
	"Discount":         TrackingModeHalf,
	"Department":       TrackingModeHalf,
	"Call":             TrackingModeHalf,
	"DealUser":         TrackingModeFull,
	"BillData":         TrackingModeHalf,
	"BillItem":         TrackingModeHalf,
	"Bundle":           TrackingModeHalf,
	"Attachment":       TrackingModeHalf,
	"AttachmentData":   TrackingModeHalf,
	"Bill":             TrackingModeHalf,
	"TagPerson":        TrackingModeFull,
	"Tag":              TrackingModeHalf,
	"TagDeal":          TrackingModeFull,
}

// genericGetter là kiểu hàm để lấy một entity dựa trên ID.
type genericGetter func(ctx context.Context, client *ent.Client, id int) (ent.Value, error)

// schemaGetters ánh xạ tên schema tới hàm getter tương ứng.
var schemaGetters = map[string]genericGetter{
	"Deal": func(ctx context.Context, client *ent.Client, id int) (ent.Value, error) {
		v, err := client.Deal.Get(ctx, id)
		return v, err
	},
	"Person": func(ctx context.Context, client *ent.Client, id int) (ent.Value, error) {
		v, err := client.Person.Get(ctx, id)
		return v, err
	},
	"Appointment": func(ctx context.Context, client *ent.Client, id int) (ent.Value, error) {
		v, err := client.Appointment.Get(ctx, id)
		return v, err
	},
	"Task": func(ctx context.Context, client *ent.Client, id int) (ent.Value, error) {
		v, err := client.Task.Get(ctx, id)
		return v, err
	},
	"User": func(ctx context.Context, client *ent.Client, id int) (ent.Value, error) {
		v, err := client.User.Get(ctx, id)
		return v, err
	},
	"Setting": func(ctx context.Context, client *ent.Client, id int) (ent.Value, error) {
		v, err := client.Setting.Get(ctx, id)
		return v, err
	},
	"Referral": func(ctx context.Context, client *ent.Client, id int) (ent.Value, error) {
		v, err := client.Referral.Get(ctx, id)
		return v, err
	},
	"TaskNote": func(ctx context.Context, client *ent.Client, id int) (ent.Value, error) {
		v, err := client.TaskNote.Get(ctx, id)
		return v, err
	},
	"UserData": func(ctx context.Context, client *ent.Client, id int) (ent.Value, error) {
		v, err := client.UserData.Get(ctx, id)
		return v, err
	},
	"Track": func(ctx context.Context, client *ent.Client, id int) (ent.Value, error) {
		v, err := client.Track.Get(ctx, id)
		return v, err
	},
	"Stage": func(ctx context.Context, client *ent.Client, id int) (ent.Value, error) {
		v, err := client.Stage.Get(ctx, id)
		return v, err
	},
	"TaskAssignment": func(ctx context.Context, client *ent.Client, id int) (ent.Value, error) {
		v, err := client.TaskAssignment.Get(ctx, id)
		return v, err
	},
	"TaskDepartment": func(ctx context.Context, client *ent.Client, id int) (ent.Value, error) {
		v, err := client.TaskDepartment.Get(ctx, id)
		return v, err
	},
	"Schedule": func(ctx context.Context, client *ent.Client, id int) (ent.Value, error) {
		v, err := client.Schedule.Get(ctx, id)
		return v, err
	},
	"ProductOperation": func(ctx context.Context, client *ent.Client, id int) (ent.Value, error) {
		v, err := client.ProductOperation.Get(ctx, id)
		return v, err
	},
	"Product": func(ctx context.Context, client *ent.Client, id int) (ent.Value, error) {
		v, err := client.Product.Get(ctx, id)
		return v, err
	},
	"Pipeline": func(ctx context.Context, client *ent.Client, id int) (ent.Value, error) {
		v, err := client.Pipeline.Get(ctx, id)
		return v, err
	},
	"PersonAssignment": func(ctx context.Context, client *ent.Client, id int) (ent.Value, error) {
		v, err := client.PersonAssignment.Get(ctx, id)
		return v, err
	},
	"PersonData": func(ctx context.Context, client *ent.Client, id int) (ent.Value, error) {
		v, err := client.PersonData.Get(ctx, id)
		return v, err
	},
	"Note": func(ctx context.Context, client *ent.Client, id int) (ent.Value, error) {
		v, err := client.Note.Get(ctx, id)
		return v, err
	},
	"Operation": func(ctx context.Context, client *ent.Client, id int) (ent.Value, error) {
		v, err := client.Operation.Get(ctx, id)
		return v, err
	},
	"FileUsage": func(ctx context.Context, client *ent.Client, id int) (ent.Value, error) {
		v, err := client.FileUsage.Get(ctx, id)
		return v, err
	},
	"Installment": func(ctx context.Context, client *ent.Client, id int) (ent.Value, error) {
		v, err := client.Installment.Get(ctx, id)
		return v, err
	},
	"InstallmentPlan": func(ctx context.Context, client *ent.Client, id int) (ent.Value, error) {
		v, err := client.InstallmentPlan.Get(ctx, id)
		return v, err
	},
	"DiscountUsage": func(ctx context.Context, client *ent.Client, id int) (ent.Value, error) {
		v, err := client.DiscountUsage.Get(ctx, id)
		return v, err
	},
	"File": func(ctx context.Context, client *ent.Client, id int) (ent.Value, error) {
		v, err := client.File.Get(ctx, id)
		return v, err
	},
	"Discount": func(ctx context.Context, client *ent.Client, id int) (ent.Value, error) {
		v, err := client.Discount.Get(ctx, id)
		return v, err
	},
	"Department": func(ctx context.Context, client *ent.Client, id int) (ent.Value, error) {
		v, err := client.Department.Get(ctx, id)
		return v, err
	},
	"Call": func(ctx context.Context, client *ent.Client, id int) (ent.Value, error) {
		v, err := client.Call.Get(ctx, id)
		return v, err
	},
	"DealUser": func(ctx context.Context, client *ent.Client, id int) (ent.Value, error) {
		v, err := client.DealUser.Get(ctx, id)
		return v, err
	},
	"BillData": func(ctx context.Context, client *ent.Client, id int) (ent.Value, error) {
		v, err := client.BillData.Get(ctx, id)
		return v, err
	},
	"BillItem": func(ctx context.Context, client *ent.Client, id int) (ent.Value, error) {
		v, err := client.BillItem.Get(ctx, id)
		return v, err
	},
	"Bundle": func(ctx context.Context, client *ent.Client, id int) (ent.Value, error) {
		v, err := client.Bundle.Get(ctx, id)
		return v, err
	},
	"Attachment": func(ctx context.Context, client *ent.Client, id int) (ent.Value, error) {
		v, err := client.Attachment.Get(ctx, id)
		return v, err
	},
	"AttachmentData": func(ctx context.Context, client *ent.Client, id int) (ent.Value, error) {
		v, err := client.AttachmentData.Get(ctx, id)
		return v, err
	},
	"Bill": func(ctx context.Context, client *ent.Client, id int) (ent.Value, error) {
		v, err := client.Bill.Get(ctx, id)
		return v, err
	},
	"TagDeal": func(ctx context.Context, client *ent.Client, id int) (ent.Value, error) {
		v, err := client.TagDeal.Get(ctx, id)
		return v, err
	},
	"Tag": func(ctx context.Context, client *ent.Client, id int) (ent.Value, error) {
		v, err := client.Tag.Get(ctx, id)
		return v, err
	},
	"TagPerson": func(ctx context.Context, client *ent.Client, id int) (ent.Value, error) {
		v, err := client.TagPerson.Get(ctx, id)
		return v, err
	},
}

func GlobalEventDispatchHook() ent.Hook {
	return func(next ent.Mutator) ent.Mutator {
		return ent.MutateFunc(func(ctx context.Context, m ent.Mutation) (ent.Value, error) {
			schemaType := m.Type()
			// Get tracking mode, default to ignore if not specified
			trackingMode, tracked := trackedSchemas[schemaType]
			if !tracked {
				trackingMode = TrackingModeIgnore
			}

			// Skip if tracking mode is ignore
			if trackingMode == TrackingModeIgnore {
				return next.Mutate(ctx, m)
			}

			var oldValue ent.Value
			var opType types.OperationType
			op := m.Op()
			userID := ctxdata.GetUidFromCtx(ctx)
			resourceID := 0
			var client *ent.Client

			if op.Is(ent.OpCreate) {
				opType = types.OpTypeCreate
			} else if op.Is(ent.OpUpdate | ent.OpUpdateOne) {
				opType = types.OpTypeUpdate
			} else if op.Is(ent.OpDelete | ent.OpDeleteOne) {
				opType = types.OpTypeDelete
			} else {
				return next.Mutate(ctx, m)
			}

			// Lấy Client và ID thông qua Type Assertion
			var idExists bool
			switch schemaType {
			case "Deal":
				if concreteMut, ok := m.(*ent.DealMutation); ok {
					resourceID, idExists = concreteMut.ID()
					client = concreteMut.Client()
				}
			case "Person":
				if concreteMut, ok := m.(*ent.PersonMutation); ok {
					resourceID, idExists = concreteMut.ID()
					client = concreteMut.Client()
				}
			case "Appointment":
				if concreteMut, ok := m.(*ent.AppointmentMutation); ok {
					resourceID, idExists = concreteMut.ID()
					client = concreteMut.Client()
				}
			case "Task":
				if concreteMut, ok := m.(*ent.TaskMutation); ok {
					resourceID, idExists = concreteMut.ID()
					client = concreteMut.Client()
				}
			case "User":
				if concreteMut, ok := m.(*ent.UserMutation); ok {
					resourceID, idExists = concreteMut.ID()
					client = concreteMut.Client()
				}
			case "Setting":
				if concreteMut, ok := m.(*ent.SettingMutation); ok {
					resourceID, idExists = concreteMut.ID()
					client = concreteMut.Client()
				}
			case "Referral":
				if concreteMut, ok := m.(*ent.ReferralMutation); ok {
					resourceID, idExists = concreteMut.ID()
					client = concreteMut.Client()
				}
			case "TaskNote":
				if concreteMut, ok := m.(*ent.TaskNoteMutation); ok {
					resourceID, idExists = concreteMut.ID()
					client = concreteMut.Client()
				}
			case "UserData":
				if concreteMut, ok := m.(*ent.UserDataMutation); ok {
					resourceID, idExists = concreteMut.ID()
					client = concreteMut.Client()
				}
			case "Track":
				if concreteMut, ok := m.(*ent.TrackMutation); ok {
					resourceID, idExists = concreteMut.ID()
					client = concreteMut.Client()
				}
			case "Stage":
				if concreteMut, ok := m.(*ent.StageMutation); ok {
					resourceID, idExists = concreteMut.ID()
					client = concreteMut.Client()
				}
			case "TaskAssignment":
				if concreteMut, ok := m.(*ent.TaskAssignmentMutation); ok {
					resourceID, idExists = concreteMut.ID()
					client = concreteMut.Client()
				}
			case "TaskDepartment":
				if concreteMut, ok := m.(*ent.TaskDepartmentMutation); ok {
					resourceID, idExists = concreteMut.ID()
					client = concreteMut.Client()
				}
			case "Schedule":
				if concreteMut, ok := m.(*ent.ScheduleMutation); ok {
					resourceID, idExists = concreteMut.ID()
					client = concreteMut.Client()
				}
			case "ProductOperation":
				if concreteMut, ok := m.(*ent.ProductOperationMutation); ok {
					resourceID, idExists = concreteMut.ID()
					client = concreteMut.Client()
				}
			case "Product":
				if concreteMut, ok := m.(*ent.ProductMutation); ok {
					resourceID, idExists = concreteMut.ID()
					client = concreteMut.Client()
				}
			case "Pipeline":
				if concreteMut, ok := m.(*ent.PipelineMutation); ok {
					resourceID, idExists = concreteMut.ID()
					client = concreteMut.Client()
				}
			case "PersonAssignment":
				if concreteMut, ok := m.(*ent.PersonAssignmentMutation); ok {
					resourceID, idExists = concreteMut.ID()
					client = concreteMut.Client()
				}
			case "PersonData":
				if concreteMut, ok := m.(*ent.PersonDataMutation); ok {
					resourceID, idExists = concreteMut.ID()
					client = concreteMut.Client()
				}
			case "Note":
				if concreteMut, ok := m.(*ent.NoteMutation); ok {
					resourceID, idExists = concreteMut.ID()
					client = concreteMut.Client()
				}
			case "Operation":
				if concreteMut, ok := m.(*ent.OperationMutation); ok {
					resourceID, idExists = concreteMut.ID()
					client = concreteMut.Client()
				}
			case "FileUsage":
				if concreteMut, ok := m.(*ent.FileUsageMutation); ok {
					resourceID, idExists = concreteMut.ID()
					client = concreteMut.Client()
				}
			case "Installment":
				if concreteMut, ok := m.(*ent.InstallmentMutation); ok {
					resourceID, idExists = concreteMut.ID()
					client = concreteMut.Client()
				}
			case "InstallmentPlan":
				if concreteMut, ok := m.(*ent.InstallmentPlanMutation); ok {
					resourceID, idExists = concreteMut.ID()
					client = concreteMut.Client()
				}
			case "DiscountUsage":
				if concreteMut, ok := m.(*ent.DiscountUsageMutation); ok {
					resourceID, idExists = concreteMut.ID()
					client = concreteMut.Client()
				}
			case "File":
				if concreteMut, ok := m.(*ent.FileMutation); ok {
					resourceID, idExists = concreteMut.ID()
					client = concreteMut.Client()
				}
			case "Discount":
				if concreteMut, ok := m.(*ent.DiscountMutation); ok {
					resourceID, idExists = concreteMut.ID()
					client = concreteMut.Client()
				}
			case "Department":
				if concreteMut, ok := m.(*ent.DepartmentMutation); ok {
					resourceID, idExists = concreteMut.ID()
					client = concreteMut.Client()
				}
			case "Call":
				if concreteMut, ok := m.(*ent.CallMutation); ok {
					resourceID, idExists = concreteMut.ID()
					client = concreteMut.Client()
				}
			case "DealUser":
				if concreteMut, ok := m.(*ent.DealUserMutation); ok {
					resourceID, idExists = concreteMut.ID()
					client = concreteMut.Client()
				}
			case "BillData":
				if concreteMut, ok := m.(*ent.BillDataMutation); ok {
					resourceID, idExists = concreteMut.ID()
					client = concreteMut.Client()
				}
			case "BillItem":
				if concreteMut, ok := m.(*ent.BillItemMutation); ok {
					resourceID, idExists = concreteMut.ID()
					client = concreteMut.Client()
				}
			case "Bundle":
				if concreteMut, ok := m.(*ent.BundleMutation); ok {
					resourceID, idExists = concreteMut.ID()
					client = concreteMut.Client()
				}
			case "Attachment":
				if concreteMut, ok := m.(*ent.AttachmentMutation); ok {
					resourceID, idExists = concreteMut.ID()
					client = concreteMut.Client()
				}
			case "AttachmentData":
				if concreteMut, ok := m.(*ent.AttachmentDataMutation); ok {
					resourceID, idExists = concreteMut.ID()
					client = concreteMut.Client()
				}
			case "Bill":
				if concreteMut, ok := m.(*ent.BillMutation); ok {
					resourceID, idExists = concreteMut.ID()
					client = concreteMut.Client()
				}
			case "Tag":
				if concreteMut, ok := m.(*ent.TagMutation); ok {
					resourceID, idExists = concreteMut.ID()
					client = concreteMut.Client()
				}
			case "TagDeal":
				if concreteMut, ok := m.(*ent.TagDealMutation); ok {
					resourceID, idExists = concreteMut.ID()
					client = concreteMut.Client()
				}
			case "TagPerson":
				if concreteMut, ok := m.(*ent.TagPersonMutation); ok {
					resourceID, idExists = concreteMut.ID()
					client = concreteMut.Client()
				}
			default:
				// Schema được theo dõi nhưng không có case trong switch?
				// Log lỗi nhưng không làm gián đoạn mutation
				logx.WithContext(ctx).Infof("HISTORY_HOOK: Configuration warning - Schema '%s' is tracked but no type assertion case found.", schemaType)
				return next.Mutate(ctx, m)
			}

			if client == nil {
				// Use the hook's context (ctx) for this error
				logx.WithContext(ctx).Errorf("HISTORY_HOOK: Failed to get client via type assertion for schema '%s'. Continuing without history tracking.", schemaType)
				return next.Mutate(ctx, m)
			}

			// Lấy OldValue cho Update/Delete chỉ khi ở TrackingModeFull
			if trackingMode == TrackingModeFull && (opType == types.OpTypeUpdate || opType == types.OpTypeDelete) {
				if idExists {
					if getter, found := schemaGetters[schemaType]; found {
						queriedValue, queryErr := getter(ctx, client, resourceID)
						if queryErr != nil && !ent.IsNotFound(queryErr) {
							// Use Errorf for query failures
							logx.WithContext(ctx).Errorf("HISTORY_HOOK: Failed to query %s %d before %s: %v. Continuing without old value.",
								schemaType, resourceID, opType, queryErr)
						} else if queryErr == nil {
							oldValue = queriedValue
						} else { // IsNotFound
							// Use Infof for expected Not Found cases
							logx.WithContext(ctx).Infof("HISTORY_HOOK: %s %d not found before %s operation. OldValue will be nil.",
								schemaType, resourceID, opType)
						}
					} else {
						// Use Infof for missing getter configuration
						logx.WithContext(ctx).Infof("HISTORY_HOOK: Schema '%s' is tracked but no getter found. Continuing without old value.", schemaType)
					}
				} else {
					// Use Infof when ID is missing for fetching old state
					logx.WithContext(ctx).Infof("HISTORY_HOOK: ID not found in mutation for %s operation on %s. Cannot fetch old state.",
						opType, schemaType)
				}
			}

			// Thực hiện Mutation Gốc
			newValue, err := next.Mutate(ctx, m)

			// Xử lý lỗi Mutation gốc
			if err != nil {
				// Use Errorf for mutation failures
				logx.WithContext(ctx).Errorf("HISTORY_HOOK: Mutation failed for %s (%s): %v. No event dispatched.", schemaType, opType, err)
				return newValue, err
			}

			// Logic Sau khi Mutate (Dispatch Event nếu thành công)
			if opType == types.OpTypeCreate {
				// Sử dụng reflection để lấy ID
				val := reflect.ValueOf(newValue)

				// Kiểm tra liệu newValue có phải là con trỏ hay không
				if val.Kind() == reflect.Ptr {
					val = val.Elem() // Dereference con trỏ để lấy giá trị thực
				}

				// Tìm field ID
				if val.Kind() == reflect.Struct {
					idField := val.FieldByName("ID")
					if idField.IsValid() && idField.CanInt() {
						resourceID = int(idField.Int())
						idExists = true
					}
				}

				if !idExists {
					// Use Infof if ID cannot be determined after Create
					logx.WithContext(ctx).Infof("HISTORY_HOOK: Failed to determine ResourceID after %s for %s. Skipping event dispatch.",
						opType, schemaType)
					return newValue, nil
				}
			} else if !idExists && (opType == types.OpTypeUpdate || opType == types.OpTypeDelete) {
				// Use Infof if ID is missing for Update/Delete event dispatch
				logx.WithContext(ctx).Infof("HISTORY_HOOK: Cannot dispatch event for %s on %s because no ID was found. Skipping event dispatch.",
					opType, schemaType)
				return newValue, nil
			}

			occurredAt := time.Now()
			if opType == types.OpTypeCreate {
				if ts, ok := newValue.(TimestamperCreated); ok {
					occurredAt = ts.GetCreatedAt()
				}
			} else if opType == types.OpTypeUpdate {
				if ts, ok := newValue.(TimestamperUpdated); ok {
					occurredAt = ts.GetUpdatedAt()
				}
			}

			// Tạo và Dispatch Event
			var eventToDispatch *event.EntityChangeEvent
			dispatchCtx := context.Background()

			switch opType {
			case types.OpTypeCreate:
				if idExists {
					eventToDispatch = event.NewEntityChangeEvent(schemaType, resourceID, opType, userID, occurredAt, nil, newValue)
					// Use Infof for dispatch logging
					logx.WithContext(ctx).Infof("HISTORY_HOOK: Dispatching AfterCreate event for %s ID: %d", schemaType, resourceID)
				}
			case types.OpTypeUpdate:
				if idExists {
					if trackingMode == TrackingModeFull && oldValue != nil {
						eventToDispatch = event.NewEntityChangeEvent(schemaType, resourceID, opType, userID, occurredAt, oldValue, newValue)
					} else {
						eventToDispatch = event.NewEntityChangeEvent(schemaType, resourceID, opType, userID, occurredAt, nil, newValue)
					}
					// Use Infof for dispatch logging
					logx.WithContext(ctx).Infof("HISTORY_HOOK: Dispatching AfterUpdate event for %s ID: %d with tracking mode: %s",
						schemaType, resourceID, trackingMode)
				}
			case types.OpTypeDelete:
				if idExists {
					if trackingMode == TrackingModeFull && oldValue != nil {
						eventToDispatch = event.NewEntityChangeEvent(schemaType, resourceID, opType, userID, occurredAt, oldValue, nil)
					} else {
						eventToDispatch = event.NewEntityChangeEvent(schemaType, resourceID, opType, userID, occurredAt, nil, nil)
					}
					// Use Infof for dispatch logging
					logx.WithContext(ctx).Infof("HISTORY_HOOK: Dispatching AfterDelete event for %s ID: %d with tracking mode: %s",
						schemaType, resourceID, trackingMode)
				}
			}

			if eventToDispatch != nil {
				EB.Dispatch(dispatchCtx, eventToDispatch)
			}

			return newValue, nil
		})
	}
}

// Helper Interfaces

type Identifier interface {
	ID() int
}

type TimestamperCreated interface {
	GetCreatedAt() time.Time
}

type TimestamperUpdated interface {
	GetUpdatedAt() time.Time
}
