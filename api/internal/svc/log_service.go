package svc

import (
	"bcare/api/internal/config"
	"bcare/common/bfile"
	"encoding/json"
	"errors"
	"io"
	"path/filepath"
	"strings"
	"sync"
)

var logsMap = map[string]LogHandle{
	"app": {
		Name: "app",
		Path: "app.log",
		Json: false, //console logs/print, not json
	},
	"access": {
		Name: "access",
		Path: "access.log",
		Json: true,
	},
	"error": {
		Name: "error",
		Path: "error.log",
		Json: true,
	},
	"slow": {
		Name: "slow",
		Path: "slow.log",
		Json: true,
	},
	"stat": {
		Name: "stat",
		Path: "stat.log",
		Json: true,
	},
	"severe": {
		Name: "severe",
		Path: "journal.error.log",
		Json: false,
	},
}

type LogService struct {
	config config.Config
}

var onceLog sync.Once
var logInstance *LogService

func NewLogService(c config.Config) *LogService {
	onceLog.Do(func() {
		logInstance = &LogService{
			config: c,
		}
	})

	return logInstance
}

func (ls *LogService) LogDirSize() (uint64, error) {
	return bfile.DirSize(ls.config.Log.Path)
}

func (ls *LogService) LogRead(id string, page, limit int) (any, error) {
	handle, ok := logsMap[id]
	if !ok {
		return nil, errors.New("log id not found")
	}
	return ls.logRead(filepath.Join(ls.config.Log.Path, handle.Path), page, limit, handle.Json)
}
func (ls *LogService) logRead(filePath string, page, limit int, isJson bool) (any, error) {
	var res []any
	list, err := bfile.ReadFilePagination(filePath, page, limit)
	if err != nil {
		errStr := err.Error()
		if strings.Contains(errStr, "cannot find") || strings.Contains(errStr, "no such file") {
			return nil, nil
		}
		if errors.Is(err, io.EOF) {
			if page > 1 {
				return nil, errors.New("empty")
			}
			return nil, errors.New("not found")
		}
		return nil, err
	}
	for _, line := range list {
		if isJson {
			var item map[string]any
			if err = json.Unmarshal([]byte(line), &item); err != nil {
				continue
			}
			res = append(res, item)
		} else {
			res = append(res, line)
		}
	}
	return res, nil
}

type LogHandle struct {
	Name string
	Path string
	Json bool
}
