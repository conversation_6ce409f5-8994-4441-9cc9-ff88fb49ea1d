package svc

import (
	"bcare/api/protobuf"
	"context"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
)

type GRPCClient struct {
	conn   *grpc.ClientConn
	client protobuf.AIServiceClient
}

func NewGRPCClient(address string) (*GRPCClient, error) {
	conn, err := grpc.Dial(address, grpc.WithTransportCredentials(insecure.NewCredentials()), grpc.WithBlock())
	if err != nil {
		return nil, err
	}

	client := protobuf.NewAIServiceClient(conn)

	return &GRPCClient{
		conn:   conn,
		client: client,
	}, nil
}

func (c *GRPCClient) Close() {
	c.conn.Close()
}

func (c *GRPCClient) GetCompletion(ctx context.Context, prompt string, maxTokens int32, model string, provider string, systemInstruction string) (int32, string, error) {
	req := &protobuf.CompletionRequest{
		Prompt:            prompt,
		MaxTokens:         maxTokens,
		Model:             model,
		Provider:          provider,
		SystemInstruction: systemInstruction,
	}

	resp, err := c.client.GetCompletion(ctx, req)
	if err != nil {
		return 0, "", err
	}

	return resp.Error, resp.Data, nil
}

func (c *GRPCClient) SpeechToText(ctx context.Context, audioFilePath string, model string, provider string) (string, error) {
	req := &protobuf.SpeechToTextRequest{
		AudioFilePath: audioFilePath,
		Model:         model,
		Provider:      provider,
	}

	resp, err := c.client.SpeechToText(ctx, req)
	if err != nil {
		return "", err
	}

	return resp.Text, nil
}
