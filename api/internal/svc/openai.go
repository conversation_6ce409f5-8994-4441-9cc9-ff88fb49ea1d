package svc

import (
	"context"
	"encoding/json"
	"errors"
	"github.com/invopop/jsonschema"
	"github.com/openai/openai-go"
	"github.com/openai/openai-go/option"
)

// OpenAIService encapsulates the OpenAI client and schema reflector
type OpenAIService struct {
	client *openai.Client
}

// OpenaiResponse represents the standard response structure
type OpenaiResponse struct {
	Error int    `json:"error" jsonschema_description:"0 for success, 1 for error"`
	Data  string `json:"data" jsonschema_description:"left empty for error"`
}

// CompletionParams contains all parameters for generating a completion
type CompletionParams struct {
	Messages []openai.ChatCompletionMessageParamUnion `json:"messages"`
	Model    string                                   `json:"model"`
	MaxToken int64                                    `json:"max_token"`
}

// NewOpenAIService creates a new OpenAIService instance
func NewOpenAIService(apiKey string) (*OpenAIService, error) {
	if apiKey == "" {
		return nil, errors.New("API key is required")
	}

	client := openai.NewClient(
		option.WithAPIKey(apiKey),
	)

	return &OpenAIService{
		client: client,
	}, nil
}

// GenerateSchema generates a JSON schema for the OpenaiResponse struct
func GenerateSchema() interface{} {
	reflector := jsonschema.Reflector{
		AllowAdditionalProperties: false,
		DoNotReference:            true,
	}
	schema := reflector.Reflect(OpenaiResponse{})
	return schema
}

// OpenaiResponseSchema is the generated JSON schema for OpenaiResponse
var OpenaiResponseSchema = GenerateSchema()

// GenerateCompletion generates a completion based on the provided parameters
func (s *OpenAIService) GenerateCompletion(ctx context.Context, params CompletionParams) (*OpenaiResponse, error) {
	if len(params.Messages) == 0 {
		return nil, errors.New("at least one message is required")
	}

	schemaParam := openai.ResponseFormatJSONSchemaJSONSchemaParam{
		Name:        openai.F("simple_response"),
		Description: openai.F(""),
		Schema:      openai.F(OpenaiResponseSchema),
		Strict:      openai.Bool(true),
	}

	chatParams := openai.ChatCompletionNewParams{
		MaxTokens: openai.F(params.MaxToken),
		Messages:  openai.F(params.Messages),
		Model:     openai.F(params.Model),
		ResponseFormat: openai.F[openai.ChatCompletionNewParamsResponseFormatUnion](
			openai.ResponseFormatJSONSchemaParam{
				Type:       openai.F(openai.ResponseFormatJSONSchemaTypeJSONSchema),
				JSONSchema: openai.F(schemaParam),
			},
		),
	}

	chatCompletion, err := s.client.Chat.Completions.New(ctx, chatParams)
	if err != nil {
		return nil, errors.New("failed to generate completion: " + err.Error())
	}

	if len(chatCompletion.Choices) == 0 {
		return nil, errors.New("no completion choices returned")
	}

	var response OpenaiResponse
	err = json.Unmarshal([]byte(chatCompletion.Choices[0].Message.Content), &response)
	if err != nil {
		return nil, errors.New("failed to unmarshal response: " + err.Error())
	}

	return &response, nil
}
