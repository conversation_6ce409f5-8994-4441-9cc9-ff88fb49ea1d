package svc

import (
	"bcare/api/internal/config"
	"bcare/api/internal/middleware"
	"bcare/common/bauth"
	"bcare/common/bauth/entadapter"
	"bcare/common/bquery"
	"bcare/common/bsms"
	"bcare/common/eventbus"
	"bcare/ent"
	"context"
	"entgo.io/ent/dialect"
	entsql "entgo.io/ent/dialect/sql"
	"github.com/casbin/casbin/v2"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/rest"
	"path/filepath"
)

var EB = eventbus.Service()

type ServiceContext struct {
	Config config.Config

	Setting *SettingService

	Enforcer *casbin.Enforcer

	Auth *APIAuthorization

	CheckUrl             rest.Middleware
	AuthorizerMiddleware rest.Middleware

	Sms   *bsms.SmsService
	Zns   *bsms.ZnsService
	Email *bsms.EmailService

	DB sqlx.SqlConn

	Ent *ent.Client

	// Bmind AI Service
	AI *AIService

	Openai *OpenAIService
}

func NewServiceContext(c config.Config) *ServiceContext {
	//MySQL
	sqlConn := sqlx.NewSqlConn("postgres", c.Database.DataSource)
	sqlx.DisableLog()

	rawDB, err := sqlConn.RawDB()
	if err != nil {
		panic(err)
	}
	// Create an ent.Driver from `db`.
	drv := entsql.OpenDB(dialect.Postgres, rawDB)
	entClient := ent.NewClient(ent.Driver(drv))

	err = entClient.Schema.Create(context.Background())
	if err != nil {
		panic(err)
	}

	// Chạy script SQL trong một goroutine
	go func() {
		scriptPath := filepath.Join("etc", "schema", "views.sql")
		if err := bquery.RunSQLScript(rawDB, scriptPath); err != nil {
			panic(err)
		}
	}()

	//Casbin authorizer
	authorizer := bauth.MustNewAuthorizer("etc/abac_with_sub_role.conf",
		bauth.WithDefaultPolicy("etc/abac_with_sub_role.csv"),
		bauth.WithAdapter(entadapter.MustNewAdapterWithClient(entClient)))
	APIAuth := NewAPIAuthorization(authorizer)

	openaiService, err := NewOpenAIService(c.ApiKey.OpenAI)

	// Khởi tạo ServiceContext trước
	svcCtx := &ServiceContext{
		Config: c,
		//Middlewares
		CheckUrl:             middleware.NewCheckUrlMiddleware().Handle,
		AuthorizerMiddleware: middleware.NewAuthorizerMiddleware(authorizer, middleware.WithUidField(c.Auth.IdentityKey)).Handle,
		//Casbin enforcer
		Enforcer: authorizer.Enforcer,
		//Auth
		Auth: APIAuth,
		//Raw DB Connection
		DB: sqlConn,
		//Ent
		Ent: entClient,
		//Openai
		Openai: openaiService,
	}

	// Khởi tạo SettingService với ServiceContext
	settingService := NewSettingService(entClient, svcCtx)

	// Khởi tạo Sms & Zns & Eail sau khi có SettingService
	Sms, Zns, Email := InitSmsZnsEmail(settingService)

	// Cập nhật các service vào ServiceContext
	svcCtx.Setting = settingService
	svcCtx.Sms = Sms
	svcCtx.Zns = Zns
	svcCtx.Email = Email

	return svcCtx
}
