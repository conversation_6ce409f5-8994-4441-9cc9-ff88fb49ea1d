package svc

import (
	"bcare/ent"
	"bcare/ent/setting"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/zeromicro/go-zero/core/logx"
	"sync"
)

type AIPrompt struct {
	ID                string `json:"id"`
	Name              string `json:"name"`
	Description       string `json:"description"`
	Content           string `json:"content"`
	MaxToken          int    `json:"max_token"`
	ModelDefault      string `json:"model_default"`
	SystemInstruction string `json:"system_instruction"`
}

type AIService struct {
	ent        *ent.Client
	grpcClient *GRPCClient
	prompts    map[string]*AIPrompt
	mu         sync.RWMutex
}

var (
	aiOnce     sync.Once
	aiInstance *AIService
)

func NewAIService(client *ent.Client, grpcAddress string) (*AIService, error) {
	var err error
	aiOnce.Do(func() {
		grpcClient, grpcErr := NewGRPCClient(grpcAddress)
		if grpcErr != nil {
			err = grpcErr
			return
		}

		aiInstance = &AIService{
			ent:        client,
			grpcClient: grpcClient,
			prompts:    make(map[string]*AIPrompt),
		}
		aiInstance.ReloadPrompts()
	})

	if err != nil {
		return nil, err
	}

	return aiInstance, nil
}

func (s *AIService) Close() {
	s.grpcClient.Close()
}

func (s *AIService) ReloadPrompts() {
	prompts, err := s.ent.Setting.Query().Where(
		setting.Category("ai_prompt"),
	).All(context.Background())

	if err != nil {
		logx.Errorf("load AI prompts error %v", err)
		return
	}

	s.mu.Lock()
	defer s.mu.Unlock()

	for _, p := range prompts {
		var prompt AIPrompt
		// Convert map[string]interface{} to AIPrompt struct
		jsonBytes, err := json.Marshal(p.Value)
		if err != nil {
			logx.Errorf("marshal AI prompt error %v", err)
			continue
		}

		err = json.Unmarshal(jsonBytes, &prompt)
		if err != nil {
			logx.Errorf("unmarshal AI prompt error %v", err)
			continue
		}
		s.prompts[prompt.ID] = &prompt
	}
}

func (s *AIService) GetPrompt(id string) (*AIPrompt, bool) {
	s.mu.RLock()
	prompt, exists := s.prompts[id]
	s.mu.RUnlock()

	if exists {
		return prompt, true
	}

	// Lazy loading if not found in cache
	prompt, err := s.getPromptFromDB(id)
	if err != nil {
		return nil, false
	}

	s.mu.Lock()
	s.prompts[id] = prompt
	s.mu.Unlock()

	return prompt, true
}

func (s *AIService) getPromptFromDB(id string) (*AIPrompt, error) {
	setting, err := s.ent.Setting.Query().
		Where(
			setting.Category("ai_prompt"),
			setting.Name(id),
		).Only(context.Background())

	if err != nil {
		return nil, err
	}

	var prompt AIPrompt
	jsonBytes, err := json.Marshal(setting.Value)
	if err != nil {
		return nil, fmt.Errorf("marshal setting value error: %w", err)
	}

	err = json.Unmarshal(jsonBytes, &prompt)
	if err != nil {
		return nil, err
	}

	return &prompt, nil
}

func (s *AIService) DeletePrompt(id string) error {
	_, err := s.ent.Setting.Delete().
		Where(
			setting.Category("ai_prompt"),
			setting.Name(id),
		).Exec(context.Background())

	if err != nil {
		return err
	}

	s.mu.Lock()
	delete(s.prompts, id)
	s.mu.Unlock()

	return nil
}

func (s *AIService) GetAllPrompts() map[string]*AIPrompt {
	s.mu.RLock()
	defer s.mu.RUnlock()

	prompts := make(map[string]*AIPrompt, len(s.prompts))
	for k, v := range s.prompts {
		prompts[k] = v
	}

	return prompts
}

func (s *AIService) UpdatePrompt(prompt *AIPrompt) error {
	if prompt == nil {
		return errors.New("prompt cannot be nil")
	}

	// Convert prompt to map[string]interface{}
	jsonBytes, err := json.Marshal(prompt)
	if err != nil {
		return fmt.Errorf("failed to marshal prompt: %w", err)
	}

	var value map[string]interface{}
	if err := json.Unmarshal(jsonBytes, &value); err != nil {
		return fmt.Errorf("failed to convert prompt to map: %w", err)
	}

	_, err = s.ent.Setting.Update().
		Where(
			setting.Category("ai_prompt"),
			setting.Name(prompt.ID),
		).
		SetValue(value).
		Save(context.Background())

	if err != nil {
		return fmt.Errorf("failed to update prompt in database: %w", err)
	}

	s.mu.Lock()
	s.prompts[prompt.ID] = prompt
	s.mu.Unlock()

	return nil
}

func (s *AIService) GetCompletion(ctx context.Context, promptID, userInput string, maxTokens int32) (int32, string, error) {
	prompt, exists := s.GetPrompt(promptID)
	if !exists {
		return 0, "", fmt.Errorf("prompt not found: %s", promptID)
	}

	if maxTokens <= 0 {
		maxTokens = int32(prompt.MaxToken)
	}

	errorCode, completion, err := s.grpcClient.GetCompletion(ctx, prompt.Content, maxTokens, prompt.ModelDefault, "", prompt.SystemInstruction)
	if err != nil {
		return 0, "", fmt.Errorf("failed to get completion: %w", err)
	}

	return errorCode, completion, nil
}

func (s *AIService) SpeechToText(ctx context.Context, audioFilePath string) (string, error) {
	text, err := s.grpcClient.SpeechToText(ctx, audioFilePath, "default_model", "default_provider")
	if err != nil {
		return "", fmt.Errorf("failed to convert speech to text: %w", err)
	}

	return text, nil
}
