package eventhandler

import (
	"bcare/api/internal/svc"
	"bcare/common/eventbus"
	"bcare/ent"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

// RegisterAllHooks khởi tạo và đăng ký tất cả các hook/listener của ứng dụng
// với event bus được cung cấp.
func RegisterAllHooks(bus eventbus.Bus, client *ent.Client, svcCtx *svc.ServiceContext) {
	// Use background context for registration logs as this likely runs at startup
	ctx := context.Background()
	logx.WithContext(ctx).Infof("Registering application event hooks...")

	// --- Khởi tạo và Đăng ký GenericHistoryHook ---
	genericHistoryHandler := NewGenericHistoryHandler(client)
	genericHistoryHandler.Register(bus)
	logx.WithContext(ctx).Infof(" - GenericHistoryHook registered.")

	// --- Khởi tạo và Đăng ký StageHistoryHook ---
	stageHistoryHandler := NewStageHistoryHandler(client)
	stageHistoryHandler.Register(bus)
	logx.WithContext(ctx).Infof(" - StageHistoryHook registered.")

	// --- Khởi tạo và Đăng ký TaskHandler ---
	taskHandler := NewTaskHandler(svcCtx)
	taskHandler.Register(bus)
	logx.WithContext(ctx).Infof(" - TaskHandler registered.")

	// --- Khởi tạo và Đăng ký NotificationHandler ---
	notificationHandler := NewNotificationHandler()
	notificationHandler.Register(bus)
	logx.WithContext(ctx).Infof(" - NotificationHandler registered.")

	logx.WithContext(ctx).Infof("All application event hooks registered successfully.")
}
