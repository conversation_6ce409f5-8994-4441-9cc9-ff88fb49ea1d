package eventhandler

import (
	"bcare/api/internal/event"
	"bcare/api/internal/realtime"
	"bcare/api/internal/types"
	"bcare/common/eventbus"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type NotificationHandler struct{}

func NewNotificationHandler() *NotificationHandler {
	return &NotificationHandler{}
}

func (h *NotificationHandler) Register(bus eventbus.Bus) {
	bgCtx := context.Background()
	logx.WithContext(bgCtx).Infof("Registering NotificationHandler for notification events...")

	_ = bus.Register(
		h.handleNotificationCreated,
		eventbus.For("Notification"),
		eventbus.On("afterCreate"),
	)

	logx.WithContext(bgCtx).Infof("NotificationHandler registration complete.")
}

func (h *NotificationHandler) handleNotificationCreated(ctx context.Context, ev eventbus.Event) error {
	createEvent, ok := ev.(*event.NotificationAfterCreate)
	if !ok {
		return nil
	}

	notification := createEvent.Notification
	if notification == nil {
		logx.WithContext(ctx).Infof("[NotificationHandler] Received Create event with nil Notification. Skipping websocket.")
		return nil
	}

	notificationType, _ := types.ParseNotificationType(notification.Type)

	var entityTypeStr string
	if notification.EntityType != nil {
		entityType, _ := types.ParseNotificationEntityType(*notification.EntityType)
		entityTypeStr = entityType.String()
	}

	err := realtime.SendToUser(notification.UserID, "notification", map[string]any{
		"id":          notification.ID,
		"type":        notificationType.String(),
		"message":     notification.Message,
		"entity_id":   notification.EntityID,
		"entity_type": entityTypeStr,
		"is_read":     notification.IsRead,
		"metadata":    notification.Metadata,
		"created_at":  notification.CreatedAt,
	})

	if err != nil {
		logx.WithContext(ctx).Errorf("[NotificationHandler] Failed to send notification websocket message to user %d for notification %d: %v",
			notification.UserID, notification.ID, err)
		return err
	}

	logx.WithContext(ctx).Infof("[NotificationHandler] Sent notification websocket message to user %d for notification %d",
		notification.UserID, notification.ID)

	return nil
}
