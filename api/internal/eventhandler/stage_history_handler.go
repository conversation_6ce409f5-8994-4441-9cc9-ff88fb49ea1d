package eventhandler

import (
	"bcare/common/eventbus"
	"bcare/ent"
	"context"
	"reflect"
	"strconv"
	"time"

	"github.com/zeromicro/go-zero/core/logx"
)

// StageHistoryHandler listens for Deal update events and logs Stage changes.
type StageHistoryHandler struct {
	client *ent.Client
}

// NewStageHistoryHandler initializes StageHistoryHandler.
func NewStageHistoryHandler(client *ent.Client) *StageHistoryHandler {
	return &StageHistoryHandler{client: client}
}

func (h *StageHistoryHandler) Register(bus eventbus.Bus) {
	bgCtx := context.Background()
	logx.WithContext(bgCtx).Infof("Registering StageHistoryHandler for deal:afterUpdate event...")
	// Only register for the "deal:afterUpdate" event
	_ = bus.Register(
		h.handleDealStageChange,
		eventbus.For("Deal"),
		eventbus.On("afterUpdate"),
	)
	logx.WithContext(bgCtx).Infof("StageHistoryHandler registration complete.")
}

func (h *StageHistoryHandler) handleDealStageChange(ctx context.Context, ev eventbus.Event) error {
	updateEvent, ok := ev.(HistorizableEvent)
	if !ok {
		return nil
	}

	oldRecordAny := updateEvent.GetOldRecord()
	newRecordAny := updateEvent.GetNewRecord()

	if oldRecordAny == nil || newRecordAny == nil {
		logx.WithContext(ctx).Infof("[StageHistoryHandler] Received Update event with nil OldRecord or NewRecord for Resource ID %d. Skipping stage check.", updateEvent.GetResourceID())
		return nil
	}

	oldDeal, okOld := oldRecordAny.(*ent.Deal)
	newDeal, okNew := newRecordAny.(*ent.Deal)

	if !okOld || !okNew {
		logx.WithContext(ctx).Errorf("[StageHistoryHandler] Failed to assert OldRecord/NewRecord to *ent.Deal for Resource ID %d. Types received: Old=%T, New=%T",
			updateEvent.GetResourceID(), oldRecordAny, newRecordAny)
		return nil
	}

	oldStageIDPtr := oldDeal.StageID
	newStageIDPtr := newDeal.StageID

	if reflect.DeepEqual(oldStageIDPtr, newStageIDPtr) {
		return nil
	}

	logx.WithContext(ctx).Infof("[StageHistoryHandler] Stage changed for Deal ID %d: From %v To %v", newDeal.ID, formatStageID(oldStageIDPtr), formatStageID(newStageIDPtr))

	dealID := newDeal.ID
	userID := updateEvent.GetUserID()
	changedAt := newDeal.UpdatedAt

	createCmd := h.client.DealStageHistory.Create().
		SetDealID(dealID).
		SetChangedAt(changedAt)

	if oldStageIDPtr != nil {
		createCmd.SetBefore(*oldStageIDPtr)
	} else {
		createCmd.SetNillableBefore(nil)
	}

	if newStageIDPtr != nil {
		createCmd.SetAfter(*newStageIDPtr)
	} else {
		createCmd.SetNillableAfter(nil)
	}

	if userID > 0 {
		createCmd.SetUserID(userID)
	} else {
		logx.WithContext(ctx).Infof("[StageHistoryHandler] Stage change log for Deal %d has invalid or missing UserID (%d)", dealID, userID)
	}

	dbCtx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	_, err := createCmd.Save(dbCtx)
	if err != nil {
		logx.WithContext(ctx).Errorf("[StageHistoryHandler] Error saving deal stage history for Deal %d: %v", dealID, err)
	} else {
		logx.WithContext(ctx).Infof("[StageHistoryHandler] Saved stage change history for Deal %d", dealID)
	}

	return nil
}

func formatStageID(stageID *int) string {
	if stageID == nil {
		return "<nil>"
	}
	return strconv.Itoa(*stageID)
}
