package eventhandler

import (
	"bcare/api/internal/types"
	"bcare/common/butils"
	"bcare/common/eventbus"
	"bcare/ent"
	"bcare/ent/appointmenthistory"
	"bcare/ent/dealhistory"
	"bcare/ent/entityhistory"
	"bcare/ent/personhistory"
	"bcare/ent/taskhistory"
	"context"
	"fmt"
	"github.com/zeromicro/go-zero/core/logx"
	"reflect"
	"strconv"
	"time"
)

// OperationType định nghĩa lo<PERSON>i thao tác
type OperationType string

// HistorizableEvent là interface cho các sự kiện có thể ghi lịch sử.
// Interface này chỉ dành cho các event cần tracking history (như EntityChangeEvent).
// Các event khác (Deal, Task, Notification) không cần implement interface này.
type HistorizableEvent interface {
	eventbus.Event // Nhúng: ResourceType(), EventType(), Match()
	GetResourceID() int
	GetOperationType() types.OperationType
	GetChanges() (map[string]interface{}, map[string]interface{}, error)
	GetUserID() int
	GetOccurredAt() time.Time
	GetNewRecord() any
	GetOldRecord() any
	GetEntityType() string // Trả về entity type không có prefix "ent_"
}

type SimplerRelationConfig struct {
	MainEntity      string
	ForeignKeyField string
}

var relationConfigs = map[string]SimplerRelationConfig{
	"DealUser":         {MainEntity: "Deal", ForeignKeyField: "deal_id"},
	"TagDeal":          {MainEntity: "Deal", ForeignKeyField: "deal_id"},
	"TagPerson":        {MainEntity: "Person", ForeignKeyField: "person_id"},
	"PersonAssignment": {MainEntity: "Person", ForeignKeyField: "person_id"},
	"TaskAssignment":   {MainEntity: "Task", ForeignKeyField: "task_id"},
	"TaskDepartment":   {MainEntity: "Task", ForeignKeyField: "task_id"},
	"TaskNote":         {MainEntity: "Task", ForeignKeyField: "task_id"},
}

type GenericHistoryHandler struct {
	client *ent.Client
}

func NewGenericHistoryHandler(client *ent.Client) *GenericHistoryHandler {
	return &GenericHistoryHandler{client: client}
}

func (h *GenericHistoryHandler) Register(bus eventbus.Bus) {
	bgCtx := context.Background()
	logx.WithContext(bgCtx).Infof("Registering GenericHistoryHandler...")
	// Register handler for all entity change events using pattern matching
	// This will only match events with resource types starting with "ent_"
	_ = bus.Register(
		h.handleEntityChangeEvent,
		eventbus.ForPattern("ent_*"),
	)

	logx.WithContext(bgCtx).Infof("GenericHistoryHandler registration complete.")
}

func (h *GenericHistoryHandler) handleEntityChangeEvent(ctx context.Context, ev eventbus.Event) error {
	// No need to check for "ent_" prefix here since pattern matching handles it
	changeEvent, ok := ev.(HistorizableEvent)
	if !ok {
		return nil
	}

	resourceType := changeEvent.ResourceType()
	resourceID := changeEvent.GetResourceID()
	opType := changeEvent.GetOperationType()
	userID := changeEvent.GetUserID()
	occurredAt := changeEvent.GetOccurredAt()
	newValue := changeEvent.GetNewRecord()
	oldValue := changeEvent.GetOldRecord()

	before, after, err := calculateDiff(ctx, opType, oldValue, newValue, resourceType, resourceID)
	if err != nil {
		logx.WithContext(ctx).Errorf("GENERIC_HISTORY: Error processing diff for %s:%d (%s): %v. Skipping history record.", resourceType, resourceID, opType, err)
		return nil
	}
	if before == nil && after == nil && opType != types.OpTypeDelete {
		logx.WithContext(ctx).Infof("GENERIC_HISTORY: No data changes detected or processed for %s:%d (%s). Skipping history record.", resourceType, resourceID, opType)
		return nil
	}
	if opType == types.OpTypeUpdate && len(before) == 0 && len(after) == 0 {
		logx.WithContext(ctx).Infof("GENERIC_HISTORY: No effective changes detected for update on %s:%d. Skipping history record.", resourceType, resourceID)
		return nil
	}

	dbCtx := ctx
	var saveErr error
	var mainEntityID int
	var idFound bool
	// Get entity type directly from the event
	entityType := changeEvent.GetEntityType()
	mainEntity := entityType

	if config, isRelation := relationConfigs[entityType]; isRelation {
		mainEntity = config.MainEntity

		if opType == types.OpTypeDelete && before != nil {
			mainEntityID, idFound = extractIntID(before, config.ForeignKeyField)
		} else if after != nil {
			mainEntityID, idFound = extractIntID(after, config.ForeignKeyField)
		} else if before != nil {
			mainEntityID, idFound = extractIntID(before, config.ForeignKeyField)
		}

		if !idFound {
			logx.WithContext(ctx).Infof("GENERIC_HISTORY: Could not find foreign key '%s' for relation %s:%d (%s). Falling back to EntityHistory.",
				config.ForeignKeyField, resourceType, resourceID, opType)
			mainEntity = "EntityHistory"
		}
	} else {
		mainEntityID = resourceID
		idFound = true
	}

	switch mainEntity {
	case "Deal":
		if !idFound {
			break
		}
		creator := h.client.DealHistory.Create().SetDealID(mainEntityID).
			SetOperation(dealhistory.Operation(opType)).
			SetChangedAt(occurredAt).
			SetUserID(userID).
			SetBefore(before).
			SetAfter(after)
		if entityType != "Deal" {
			creator.SetRelated(entityType)
			creator.SetRelatedID(resourceID)
		}
		_, saveErr = creator.Save(dbCtx)
	case "Person":
		if !idFound {
			break
		}
		creator := h.client.PersonHistory.Create().SetPersonID(mainEntityID).
			SetOperation(personhistory.Operation(opType)).
			SetChangedAt(occurredAt).
			SetUserID(userID).
			SetBefore(before).
			SetAfter(after)
		if entityType != "Person" {
			creator.SetRelated(entityType)
			creator.SetRelatedID(resourceID)
		}
		_, saveErr = creator.Save(dbCtx)
	case "Appointment":
		if !idFound {
			break
		}
		creator := h.client.AppointmentHistory.Create().SetAppointmentID(mainEntityID).
			SetOperation(appointmenthistory.Operation(opType)).
			SetBefore(before).
			SetAfter(after)
		if entityType != "Appointment" {
			creator.SetRelated(entityType)
			creator.SetRelatedID(resourceID)
		}
		_, saveErr = creator.Save(dbCtx)
	case "Task":
		if !idFound {
			break
		}
		creator := h.client.TaskHistory.Create().SetTaskID(mainEntityID).
			SetOperation(taskhistory.Operation(opType)).
			SetBefore(before).
			SetAfter(after)
		if entityType != "Task" {
			creator.SetRelated(entityType)
			creator.SetRelatedID(resourceID)
		}
		_, saveErr = creator.Save(dbCtx)
	default:
		logx.WithContext(ctx).Infof("GENERIC_HISTORY: Saving history for unhandled type '%s' or relation fallback to EntityHistory table (Original: %s:%d)", mainEntity, resourceType, resourceID)
		creator := h.client.EntityHistory.Create().
			SetEntityType(entityType).
			SetEntityID(resourceID).
			SetOperation(entityhistory.Operation(opType)).
			SetBefore(before).
			SetAfter(after).
			SetUserID(userID).
			SetChangedAt(occurredAt)

		_, saveErr = creator.Save(dbCtx)
		mainEntity = "EntityHistory"
	}

	if saveErr != nil {
		logx.WithContext(ctx).Errorf("GENERIC_HISTORY: Error saving history log to %s table for %s:%d related to %s:%d (%s): %v",
			mainEntity, resourceType, resourceID, mainEntity, mainEntityID, opType, saveErr)
		return nil
	}

	if mainEntity != "EntityHistory" {
		logx.WithContext(ctx).Infof("GENERIC_HISTORY: Successfully saved history log to %s table for change on %s:%d (MainID: %d, Op: %s)",
			mainEntity, resourceType, resourceID, mainEntityID, opType)
	}

	return nil
}

func calculateDiff(ctx context.Context, opType types.OperationType, oldValue, newValue interface{}, resourceType string, resourceID int) (map[string]interface{}, map[string]interface{}, error) {
	var before, after map[string]interface{}
	var err error

	// Helper function to check if a value is a struct or pointer to struct
	isStructLike := func(v interface{}) bool {
		if v == nil {
			return false
		}
		val := reflect.ValueOf(v)
		if val.Kind() == reflect.Ptr {
			if val.IsNil() {
				return false
			}
			val = val.Elem()
		}
		return val.Kind() == reflect.Struct || val.Kind() == reflect.Map
	}

	switch opType {
	case types.OpTypeCreate:
		if newValue == nil {
			return nil, nil, fmt.Errorf("NewValue is nil")
		}
		// Check if newValue is a struct-like value that can be converted
		if !isStructLike(newValue) {
			logx.WithContext(ctx).Infof("GENERIC_HISTORY: NewValue for create on %s:%d is not a struct (type: %T), skipping history record.", resourceType, resourceID, newValue)
			return nil, nil, nil
		}
		after, err = butils.StructToMap(newValue)
	case types.OpTypeUpdate:
		// Handle newValue
		if newValue != nil {
			if !isStructLike(newValue) {
				logx.WithContext(ctx).Infof("GENERIC_HISTORY: NewValue for update on %s:%d is not a struct (type: %T), likely a bulk operation. Skipping history record.", resourceType, resourceID, newValue)
				return nil, nil, nil
			}
			after, err = butils.StructToMap(newValue)
			if err != nil {
				return nil, nil, fmt.Errorf("converting NewValue: %w", err)
			}
		}

		// Handle oldValue
		if oldValue != nil {
			if !isStructLike(oldValue) {
				logx.WithContext(ctx).Infof("GENERIC_HISTORY: OldValue for update on %s:%d is not a struct (type: %T), skipping history record.", resourceType, resourceID, oldValue)
				return nil, nil, nil
			}
			before, err = butils.StructToMap(oldValue)
			if err != nil {
				return nil, nil, fmt.Errorf("converting OldValue: %w", err)
			}
		}

		if oldValue != nil && newValue != nil {
			before, after, err = butils.DiffStructs(oldValue, newValue)
		} else {
			err = nil
			if oldValue == nil && newValue != nil {
				logx.WithContext(ctx).Infof("GENERIC_HISTORY: OldValue is nil for update on %s:%d. Logging full NewValue.", resourceType, resourceID)
				after, err = butils.StructToMap(newValue)
			} else if oldValue != nil {
				logx.WithContext(ctx).Infof("GENERIC_HISTORY: NewValue is nil for update on %s:%d. Logging full OldValue.", resourceType, resourceID)
				before, err = butils.StructToMap(oldValue)
			} else {
				return nil, nil, fmt.Errorf("both OldValue and NewValue are nil for update on %s:%d", resourceType, resourceID)
			}
			if err != nil {
				return nil, nil, fmt.Errorf("converting non-nil value during partial update: %w", err)
			}
			err = nil
		}

	case types.OpTypeDelete:
		if oldValue == nil {
			return nil, nil, nil
		}
		if !isStructLike(oldValue) {
			logx.WithContext(ctx).Infof("GENERIC_HISTORY: OldValue for delete on %s:%d is not a struct (type: %T), skipping history record.", resourceType, resourceID, oldValue)
			return nil, nil, nil
		}
		before, err = butils.StructToMap(oldValue)
	default:
		return nil, nil, fmt.Errorf("unsupported operation type '%s'", opType)
	}

	if err != nil {
		return nil, nil, fmt.Errorf("processing %s: %w", opType, err)
	}

	return before, after, nil
}

// Helper function to safely extract an integer ID from the map
func extractIntID(data map[string]interface{}, key string) (int, bool) {
	if val, ok := data[key]; ok {
		switch v := val.(type) {
		case int:
			return v, true
		case int64:
			return int(v), true
		case float64: // Sometimes numbers get decoded as float64
			return int(v), true
		case string: // In case it's stored as string
			id, err := strconv.Atoi(v)
			if err == nil {
				return id, true
			}
		}
	}
	return 0, false
}
