package eventhandler

import (
	"bcare/api/internal/dto"
	"bcare/api/internal/event"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/eventbus"
	"bcare/ent/task"
	"context"
	"fmt"
	"time"

	"github.com/zeromicro/go-zero/core/logx"
)

type TaskHandler struct {
	svcCtx *svc.ServiceContext
}

func NewTaskHandler(svcCtx *svc.ServiceContext) *TaskHandler {
	return &TaskHandler{
		svcCtx: svcCtx,
	}
}

func (h *TaskHandler) Register(bus eventbus.Bus) {
	bgCtx := context.Background()
	logx.WithContext(bgCtx).Infof("Registering TaskHandler for task events...")

	_ = bus.Register(
		h.handleTaskUpdate,
		eventbus.For("Task"),
		eventbus.On("afterUpdate"),
	)

	logx.WithContext(bgCtx).Infof("TaskHandler registration complete.")
}

func (h *TaskHandler) handleTaskUpdate(ctx context.Context, ev eventbus.Event) error {
	updateEvent, ok := ev.(*event.TaskAfterUpdate)
	if !ok {
		return nil
	}

	oldTask := updateEvent.OldTask
	newTask := updateEvent.Task

	if oldTask == nil || newTask == nil {
		logx.WithContext(ctx).Infof("[TaskHandler] Received Update event with nil OldTask or Task. Skipping.")
		return nil
	}

	if oldTask.State == newTask.State {
		return nil
	}

	h.executeTaskStateChangeWorkflow(ctx, oldTask, newTask)

	h.executeLastTaskForPersonWorkflow(ctx, newTask, updateEvent.GetUserID())

	return nil
}

func (h *TaskHandler) executeTaskStateChangeWorkflow(ctx context.Context, oldTask, newTask *dto.TaskOutput) {
	completionStates := map[task.State]bool{
		task.StateAwaitingApproval:    true,
		task.StateCompleted:           true,
		task.StateCompletedEarly:      true,
		task.StateCancelledInProgress: true,
	}

	if !completionStates[task.State(newTask.State)] {
		return
	}

	if newTask.CreatorID == 0 {
		logx.WithContext(ctx).Infof("[TaskStateChangeWorkflow] Task %d has no creator ID. Skipping notification.", newTask.ID)
		return
	}

	entityType := types.NotificationEntityTypeTask.String()
	notificationType := types.BuildNotificationType("update", "task_state_change")
	notification := &dto.CreateNotificationInput{
		UserID:     newTask.CreatorID,
		Type:       notificationType,
		Message:    fmt.Sprintf("Task '%s' has been marked as %s", newTask.Title, newTask.State),
		EntityID:   &newTask.ID,
		EntityType: &entityType,
		IsRead:     false,
		Metadata: map[string]interface{}{
			"old_state":  oldTask.State,
			"new_state":  newTask.State,
			"task_id":    newTask.ID,
			"task_title": newTask.Title,
		},
	}

	dbCtx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	_, err := h.svcCtx.NotificationModel.Create(dbCtx, notification)
	if err != nil {
		logx.WithContext(ctx).Errorf("[TaskStateChangeWorkflow] Failed to create notification: %v", err)
		return
	}

	logx.WithContext(ctx).Infof("[TaskStateChangeWorkflow] Created notification for task %d state change from %s to %s",
		newTask.ID, oldTask.State, newTask.State)
}

func (h *TaskHandler) executeLastTaskForPersonWorkflow(ctx context.Context, newTask *dto.TaskOutput, userID int) {
	completionStates := map[task.State]bool{
		task.StateAwaitingApproval:    true,
		task.StateCompleted:           true,
		task.StateCompletedEarly:      true,
		task.StateCancelledInProgress: true,
	}

	if !completionStates[task.State(newTask.State)] {
		return
	}

	if newTask.PersonID == nil {
		return
	}

	if userID == 0 {
		logx.WithContext(ctx).Infof("[LastTaskWorkflow] No user ID for last task notification. Skipping.")
		return
	}

	dbCtx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	activeTaskCount, err := h.svcCtx.TaskModel.CountActiveTasksForPersonExcludingCurrent(dbCtx, *newTask.PersonID, newTask.ID)
	if err != nil {
		logx.WithContext(ctx).Errorf("[LastTaskWorkflow] Failed to count active tasks: %v", err)
		return
	}

	if activeTaskCount > 0 {
		return
	}

	logx.WithContext(ctx).Infof("[LastTaskWorkflow] Task %d is the last task for person %d, notifying leaders", newTask.ID, *newTask.PersonID)

	leaders, err := h.svcCtx.UserModel.GetDepartmentLeadersByUserID(dbCtx, userID)
	if err != nil {
		logx.WithContext(ctx).Errorf("[LastTaskWorkflow] Failed to get department leaders for user %d: %v", userID, err)
		return
	}

	if len(leaders) == 0 {
		logx.WithContext(ctx).Infof("[LastTaskWorkflow] No department leaders found for user %d", userID)
		return
	}

	entityType := types.NotificationEntityTypeTask.String()
	notificationType := types.BuildNotificationType("warning", "last_task")
	for _, leader := range leaders {
		notification := &dto.CreateNotificationInput{
			UserID:     leader.ID,
			Type:       notificationType,
			Message:    fmt.Sprintf("Task '%s' is the last task for person ID %d", newTask.Title, *newTask.PersonID),
			EntityID:   &newTask.ID,
			EntityType: &entityType,
			IsRead:     false,
			Metadata: map[string]interface{}{
				"task_id":     newTask.ID,
				"task_title":  newTask.Title,
				"person_id":   *newTask.PersonID,
				"caller_id":   userID,
				"leader_id":   leader.ID,
				"leader_name": leader.Name,
			},
		}

		_, err := h.svcCtx.NotificationModel.Create(dbCtx, notification)
		if err != nil {
			logx.WithContext(ctx).Errorf("[LastTaskWorkflow] Failed to create notification for leader %d: %v", leader.ID, err)
			continue
		}
	}

	logx.WithContext(ctx).Infof("[LastTaskWorkflow] Created %d last task notifications for task %d, person %d",
		len(leaders), newTask.ID, *newTask.PersonID)
}
