package auth

import (
	"net/http"

	"bcare/common/bresult"

	"bcare/api/internal/logic/auth"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"github.com/zeromicro/go-zero/rest/httpx"
)

func VerifyHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.VerifyRequest
		if err := httpx.Parse(r, &req); err != nil {
			bresult.ParamErrorResult(r, w, err)
			return
		}

		l := auth.NewVerifyLogic(r.Context(), svcCtx)
		resp, err := l.Verify(&req)
		bresult.HttpResult(r, w, resp, err)
	}
}
