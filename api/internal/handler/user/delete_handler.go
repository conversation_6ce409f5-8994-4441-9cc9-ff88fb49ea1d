package user

import (
	"net/http"

	"bcare/common/bresult"

	"bcare/api/internal/logic/user"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"github.com/zeromicro/go-zero/rest/httpx"
)

func DeleteHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.UserDeleteRequest
		if err := httpx.Parse(r, &req); err != nil {
			bresult.ParamErrorResult(r, w, err)
			return
		}

		l := user.NewDeleteLogic(r.Context(), svcCtx)
		resp, err := l.Delete(&req)
		bresult.HttpResult(r, w, resp, err)
	}
}
