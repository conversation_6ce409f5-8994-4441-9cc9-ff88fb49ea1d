package discount

import (
	"net/http"

	"bcare/common/bresult"

	"bcare/api/internal/logic/discount"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"github.com/zeromicro/go-zero/rest/httpx"
)

func DeleteHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.DiscountDeleteRequest
		if err := httpx.Parse(r, &req); err != nil {
			bresult.ParamErrorResult(r, w, err)
			return
		}

		l := discount.NewDeleteLogic(r.Context(), svcCtx)
		err := l.Delete(&req)
		bresult.HttpResult(r, w, nil, err)
	}
}
