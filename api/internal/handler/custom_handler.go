package handler

import (
	"bcare/api/internal/svc"
	"bufio"
	"fmt"
	"github.com/hibiken/asynq"
	"github.com/hibiken/asynqmon"
	"net"
	"net/http"
	"strings"
)

func CustomHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	asynqmonHandler := asynqmon.New(asynqmon.Options{
		RootPath:     "/monitoring",
		RedisConnOpt: asynq.RedisClientOpt{Addr: ":6379", Password: svcCtx.Config.Database.RedisPass},
	})

	fileServer := http.FileServer(http.Dir(svcCtx.Config.Storage.Local.Path))

	return func(w http.ResponseWriter, r *http.Request) {
		path := r.URL.Path

		switch {
		case strings.HasPrefix(path, "/monitoring"):
			asynqmonHandler.ServeHTTP(w, r)
			return

		case strings.HasPrefix(path, "/storage/") && r.Method == "GET":
			w.<PERSON><PERSON>().Set("Access-Control-Expose-Headers", "Content-Disposition, Content-Length, Content-Type")
			w.Header().Set("Cache-Control", "no-cache")
			http.StripPrefix("/storage/", fileServer).ServeHTTP(w, r)
			return

		case path == "/check":
			w.Header().Set("Content-Type", "text/plain")
			w.WriteHeader(http.StatusOK)
			_, _ = w.Write([]byte("hello blazy"))
			return

		default:
			http.NotFound(w, r)
			return
		}
	}
}

// Custom response writer để xử lý vấn đề WriteHeader
type graphqlResponseWriter struct {
	http.ResponseWriter
	wroteHeader bool
}

func (w *graphqlResponseWriter) WriteHeader(code int) {
	if !w.wroteHeader {
		w.wroteHeader = true
		w.ResponseWriter.WriteHeader(code)
	}
}

func (w *graphqlResponseWriter) Write(b []byte) (int, error) {
	// Đảm bảo WriteHeader được gọi nếu chưa
	if !w.wroteHeader {
		w.WriteHeader(http.StatusOK)
	}
	return w.ResponseWriter.Write(b)
}

func (w *graphqlResponseWriter) Flush() {
	if f, ok := w.ResponseWriter.(http.Flusher); ok {
		f.Flush()
	}
}

func (w *graphqlResponseWriter) Hijack() (net.Conn, *bufio.ReadWriter, error) {
	if h, ok := w.ResponseWriter.(http.Hijacker); ok {
		return h.Hijack()
	}
	return nil, nil, fmt.Errorf("underlying ResponseWriter does not support Hijack")
}
