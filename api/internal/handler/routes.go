// Code generated by goctl. DO NOT EDIT.
// goctl 1.7.1

package handler

import (
	"net/http"

	activity "bcare/api/internal/handler/activity"
	admin "bcare/api/internal/handler/admin"
	appointment "bcare/api/internal/handler/appointment"
	attachment "bcare/api/internal/handler/attachment"
	attachment_data "bcare/api/internal/handler/attachment_data"
	auth "bcare/api/internal/handler/auth"
	bill "bcare/api/internal/handler/bill"
	bill_data "bcare/api/internal/handler/bill_data"
	bill_item "bcare/api/internal/handler/bill_item"
	bundle "bcare/api/internal/handler/bundle"
	call "bcare/api/internal/handler/call"
	casbin "bcare/api/internal/handler/casbin"
	cash_flow "bcare/api/internal/handler/cash_flow"
	cash_flow_item "bcare/api/internal/handler/cash_flow_item"
	cash_flow_note "bcare/api/internal/handler/cash_flow_note"
	constant "bcare/api/internal/handler/constant"
	deal "bcare/api/internal/handler/deal"
	deal_stage_history "bcare/api/internal/handler/deal_stage_history"
	deal_user "bcare/api/internal/handler/deal_user"
	department "bcare/api/internal/handler/department"
	discount "bcare/api/internal/handler/discount"
	discount_usage "bcare/api/internal/handler/discount_usage"
	download "bcare/api/internal/handler/download"
	economy_deposit "bcare/api/internal/handler/economy_deposit"
	economy_payment "bcare/api/internal/handler/economy_payment"
	economy_payment_allocation "bcare/api/internal/handler/economy_payment_allocation"
	export "bcare/api/internal/handler/export"
	file "bcare/api/internal/handler/file"
	file_usage "bcare/api/internal/handler/file_usage"
	form_submission "bcare/api/internal/handler/form_submission"
	history "bcare/api/internal/handler/history"
	installment "bcare/api/internal/handler/installment"
	installment_plan "bcare/api/internal/handler/installment_plan"
	location "bcare/api/internal/handler/location"
	material "bcare/api/internal/handler/material"
	material_usage "bcare/api/internal/handler/material_usage"
	mind "bcare/api/internal/handler/mind"
	note "bcare/api/internal/handler/note"
	notification "bcare/api/internal/handler/notification"
	operation "bcare/api/internal/handler/operation"
	operation_material "bcare/api/internal/handler/operation_material"
	person "bcare/api/internal/handler/person"
	person_assignment "bcare/api/internal/handler/person_assignment"
	person_data "bcare/api/internal/handler/person_data"
	pipeline "bcare/api/internal/handler/pipeline"
	product "bcare/api/internal/handler/product"
	product_operation "bcare/api/internal/handler/product_operation"
	public "bcare/api/internal/handler/public"
	query "bcare/api/internal/handler/query"
	queryv2 "bcare/api/internal/handler/queryv2"
	referral "bcare/api/internal/handler/referral"
	schedule "bcare/api/internal/handler/schedule"
	setting "bcare/api/internal/handler/setting"
	stage "bcare/api/internal/handler/stage"
	sync "bcare/api/internal/handler/sync"
	tag "bcare/api/internal/handler/tag"
	tag_deal "bcare/api/internal/handler/tag_deal"
	tag_person "bcare/api/internal/handler/tag_person"
	task "bcare/api/internal/handler/task"
	task_assignment "bcare/api/internal/handler/task_assignment"
	task_department "bcare/api/internal/handler/task_department"
	taxonomy "bcare/api/internal/handler/taxonomy"
	track "bcare/api/internal/handler/track"
	user "bcare/api/internal/handler/user"
	user_data "bcare/api/internal/handler/user_data"
	user_stat "bcare/api/internal/handler/user_stat"
	"bcare/api/internal/svc"

	"github.com/zeromicro/go-zero/rest"
)

func RegisterHandlers(server *rest.Server, serverCtx *svc.ServiceContext) {
	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/list",
				Handler: activity.ListHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/query",
				Handler: activity.QueryHandler(serverCtx),
			},
		},
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/v1/activity"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/logs",
				Handler: admin.LogsHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/monitor",
				Handler: admin.MonitorHandler(serverCtx),
			},
		},
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/v1/admin"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/add",
				Handler: appointment.AddHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/delete",
				Handler: appointment.DeleteHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/get",
				Handler: appointment.GetHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/get/next",
				Handler: appointment.GetLastedHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/list",
				Handler: appointment.ListHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/query",
				Handler: appointment.QueryHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/update",
				Handler: appointment.UpdateHandler(serverCtx),
			},
		},
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/v1/appointment"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/add",
				Handler: attachment.AddHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/children",
				Handler: attachment.ChildrenHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/delete",
				Handler: attachment.DeleteHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/force-created-at",
				Handler: attachment.ForceUpdateCreatedAtHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/get",
				Handler: attachment.GetHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/list",
				Handler: attachment.ListHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/query",
				Handler: attachment.QueryHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/query-operation",
				Handler: attachment.QueryOperationHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/update",
				Handler: attachment.UpdateHandler(serverCtx),
			},
		},
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/v1/attachment"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/data/clear",
				Handler: attachment_data.ClearHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/data/get",
				Handler: attachment_data.GetHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/data/set",
				Handler: attachment_data.SetHandler(serverCtx),
			},
		},
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/v1/attachment"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/csrf-token",
				Handler: auth.GenerateCsrfHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/login",
				Handler: auth.LoginHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/refresh",
				Handler: auth.RefreshHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/verify",
				Handler: auth.VerifyHandler(serverCtx),
			},
		},
		rest.WithPrefix("/v1/auth"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/add",
				Handler: bill.AddHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/add-from-deal",
				Handler: bill.AddFromDealHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/delete",
				Handler: bill.DeleteHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/get",
				Handler: bill.GetHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/list",
				Handler: bill.ListHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/query",
				Handler: bill.QueryHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/update",
				Handler: bill.UpdateHandler(serverCtx),
			},
		},
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/v1/bill"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/data/clear",
				Handler: bill_data.ClearHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/data/get",
				Handler: bill_data.GetHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/data/set",
				Handler: bill_data.SetHandler(serverCtx),
			},
		},
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/v1/bill"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/add",
				Handler: bill_item.AddHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/add-from-attachment",
				Handler: bill_item.AddFromAttachmentHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/delete",
				Handler: bill_item.DeleteHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/get",
				Handler: bill_item.GetHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/list",
				Handler: bill_item.ListHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/paid",
				Handler: bill_item.GetPaidHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/partially-paid",
				Handler: bill_item.GetPartiallyPaidHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/query",
				Handler: bill_item.QueryHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/update",
				Handler: bill_item.UpdateHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/update-from-attachment",
				Handler: bill_item.UpdateFromAttachmentHandler(serverCtx),
			},
		},
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/v1/bill-item"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/add",
				Handler: bundle.AddHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/delete",
				Handler: bundle.DeleteHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/get",
				Handler: bundle.GetHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/list",
				Handler: bundle.ListHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/update",
				Handler: bundle.UpdateHandler(serverCtx),
			},
		},
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/v1/bundle"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/accept",
				Handler: call.AcceptHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/add",
				Handler: call.AddHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/delete",
				Handler: call.DeleteHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/end",
				Handler: call.EndHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/get",
				Handler: call.GetHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/list",
				Handler: call.ListHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/query",
				Handler: call.QueryHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/update/feedback",
				Handler: call.UpdateFeedbackHandler(serverCtx),
			},
		},
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/v1/call"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/role/add",
				Handler: casbin.RoleAddHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/role/delete",
				Handler: casbin.RoleDeleteHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/role/list",
				Handler: casbin.RoleListHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/sync",
				Handler: casbin.SyncHandler(serverCtx),
			},
		},
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/v1/casbin"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/add",
				Handler: cash_flow.AddHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/delete",
				Handler: cash_flow.DeleteHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/export",
				Handler: cash_flow.ExportHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/get",
				Handler: cash_flow.GetHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/list",
				Handler: cash_flow.ListHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/period-report",
				Handler: cash_flow.PeriodReportHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/summary",
				Handler: cash_flow.SummaryHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/update",
				Handler: cash_flow.UpdateHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/update-state",
				Handler: cash_flow.UpdateStateHandler(serverCtx),
			},
		},
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/v1/cash-flow"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/add",
				Handler: cash_flow_item.AddHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/delete",
				Handler: cash_flow_item.DeleteHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/get",
				Handler: cash_flow_item.GetHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/list",
				Handler: cash_flow_item.ListHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/update",
				Handler: cash_flow_item.UpdateHandler(serverCtx),
			},
		},
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/v1/cash-flow/item"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/add",
				Handler: cash_flow_note.AddHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/delete",
				Handler: cash_flow_note.DeleteHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/list",
				Handler: cash_flow_note.ListHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/update",
				Handler: cash_flow_note.UpdateHandler(serverCtx),
			},
		},
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/v1/cash-flow/note"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/",
				Handler: constant.GetHandler(serverCtx),
			},
		},
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/v1/constant"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/add",
				Handler: deal.AddHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/checkout",
				Handler: deal.CheckoutHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/delete",
				Handler: deal.DeleteHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/get",
				Handler: deal.GetHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/list",
				Handler: deal.ListHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/list-big",
				Handler: deal.ListBigHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/update",
				Handler: deal.UpdateHandler(serverCtx),
			},
		},
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/v1/deal"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/list",
				Handler: deal_stage_history.ListHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/query",
				Handler: deal_stage_history.QueryHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/update",
				Handler: deal_stage_history.UpdateHandler(serverCtx),
			},
		},
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/v1/deal/history_stage"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/add",
				Handler: deal_user.AddRatingHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/delete",
				Handler: deal_user.DeleteRatingHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/update",
				Handler: deal_user.UpdateRatingHandler(serverCtx),
			},
		},
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/v1/deal_user/rating"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/add",
				Handler: deal_user.AddHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/delete",
				Handler: deal_user.DeleteHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/list",
				Handler: deal_user.ListHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/update",
				Handler: deal_user.UpdateHandler(serverCtx),
			},
		},
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/v1/deal_user"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/list",
				Handler: department.ListHandler(serverCtx),
			},
		},
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/v1/department"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/add",
				Handler: discount.AddHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/calculate",
				Handler: discount.CalculateDiscountHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/calculate-attachment",
				Handler: discount.CalculateDiscountAttachmentHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/delete",
				Handler: discount.DeleteHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/eligible",
				Handler: discount.EligibleDiscountHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/get",
				Handler: discount.GetHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/list",
				Handler: discount.ListHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/update",
				Handler: discount.UpdateHandler(serverCtx),
			},
		},
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/v1/discount"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/add",
				Handler: discount_usage.AddHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/delete",
				Handler: discount_usage.DeleteHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/list",
				Handler: discount_usage.ListHandler(serverCtx),
			},
		},
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/v1/discount_usage"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodGet,
				Path:    "/file",
				Handler: download.DownloadFileHandler(serverCtx),
			},
		},
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/v1/download"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/add",
				Handler: economy_deposit.AddHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/allocation/add",
				Handler: economy_deposit.AllocationAddHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/allocation/delete",
				Handler: economy_deposit.AllocationDeleteHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/cancel-conversion",
				Handler: economy_deposit.CancelConversionHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/convert-to-payment",
				Handler: economy_deposit.ConvertToPaymentHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/delete",
				Handler: economy_deposit.DeleteHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/get",
				Handler: economy_deposit.GetHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/list",
				Handler: economy_deposit.ListHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/update",
				Handler: economy_deposit.UpdateHandler(serverCtx),
			},
		},
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/v1/economy/deposit"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/add",
				Handler: economy_payment.AddHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/delete",
				Handler: economy_payment.DeleteHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/get",
				Handler: economy_payment.GetHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/list",
				Handler: economy_payment.ListHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/query",
				Handler: economy_payment.QueryHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/query-detail",
				Handler: economy_payment.QueryDetailHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/update",
				Handler: economy_payment.UpdateHandler(serverCtx),
			},
		},
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/v1/economy/payment"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/add",
				Handler: economy_payment_allocation.AddHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/delete",
				Handler: economy_payment_allocation.DeleteHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/update",
				Handler: economy_payment_allocation.UpdateHandler(serverCtx),
			},
		},
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/v1/economy/payment-allocation"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/",
				Handler: export.ExportHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/download",
				Handler: export.DownloadHandler(serverCtx),
			},
		},
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/v1/export"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/add",
				Handler: file.AddHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/delete",
				Handler: file.DeleteHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/get",
				Handler: file.GetHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/list",
				Handler: file.ListHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/update",
				Handler: file.UpdateHandler(serverCtx),
			},
		},
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/v1/file"),
		rest.WithMaxBytes(20971520),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/add",
				Handler: file_usage.AddHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/delete",
				Handler: file_usage.DeleteHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/get",
				Handler: file_usage.GetHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/list",
				Handler: file_usage.ListHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/update",
				Handler: file_usage.UpdateHandler(serverCtx),
			},
		},
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/v1/file_usage"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/convert-to-person",
				Handler: form_submission.ConvertToPersonHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/delete",
				Handler: form_submission.DeleteFormSubmissionHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/list",
				Handler: form_submission.ListFormSubmissionsHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/query",
				Handler: form_submission.QueryFormSubmissionsHandler(serverCtx),
			},
		},
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/v1/formsubmission"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/history",
				Handler: history.GetHistoryListHandler(serverCtx),
			},
		},
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/api/v1"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/add",
				Handler: installment.AddHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/delete",
				Handler: installment.DeleteHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/get",
				Handler: installment.GetHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/list",
				Handler: installment.ListHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/partially-paid",
				Handler: installment.GetPartiallyPaidHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/refund",
				Handler: installment.GetRefundHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/update",
				Handler: installment.UpdateHandler(serverCtx),
			},
		},
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/v1/installment"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/add",
				Handler: installment_plan.AddHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/delete",
				Handler: installment_plan.DeleteHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/get",
				Handler: installment_plan.GetHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/list",
				Handler: installment_plan.ListHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/list-refundable",
				Handler: installment_plan.ListRefundableHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/update",
				Handler: installment_plan.UpdateHandler(serverCtx),
			},
		},
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/v1/installment-plan"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/",
				Handler: location.GetHandler(serverCtx),
			},
		},
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/v1/location"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/add",
				Handler: material.AddHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/delete",
				Handler: material.DeleteHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/get",
				Handler: material.GetHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/list",
				Handler: material.ListHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/update",
				Handler: material.UpdateHandler(serverCtx),
			},
		},
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/v1/material"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/add",
				Handler: material_usage.AddHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/bulk-add",
				Handler: material_usage.BulkAddHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/delete",
				Handler: material_usage.DeleteHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/export-detail-report",
				Handler: material_usage.ExportDetailReportHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/export-report",
				Handler: material_usage.ExportReportHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/get",
				Handler: material_usage.GetHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/list",
				Handler: material_usage.ListHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/report",
				Handler: material_usage.ReportHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/report-detail",
				Handler: material_usage.DetailReportHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/update",
				Handler: material_usage.UpdateHandler(serverCtx),
			},
		},
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/v1/material/usage"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/complete",
				Handler: mind.CompleteHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/content/command",
				Handler: mind.ContentCommandHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/content/continue",
				Handler: mind.ContentContinueHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/content/improve",
				Handler: mind.ContentImproveHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/content/longer",
				Handler: mind.ContentLongerHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/content/shorter",
				Handler: mind.ContentShorterHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/getcron",
				Handler: mind.GetCronHandler(serverCtx),
			},
		},
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/v1/mind"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/add",
				Handler: note.AddHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/delete",
				Handler: note.DeleteHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/list",
				Handler: note.ListHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/update",
				Handler: note.UpdateHandler(serverCtx),
			},
		},
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/v1/note"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/add",
				Handler: notification.AddHandler(serverCtx),
			},
			{
				// Tạo thông báo hàng loạt cho nhiều người dùng
				Method:  http.MethodPost,
				Path:    "/add-batch",
				Handler: notification.AddBatchHandler(serverCtx),
			},
			{
				// Tạo thông báo cho nhóm người dùng theo phòng ban/vai trò
				Method:  http.MethodPost,
				Path:    "/add-group",
				Handler: notification.AddGroupHandler(serverCtx),
			},
			{
				// Phát sóng thông báo cho tất cả người dùng
				Method:  http.MethodPost,
				Path:    "/broadcast",
				Handler: notification.BroadcastHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/delete",
				Handler: notification.DeleteHandler(serverCtx),
			},
			{
				// Lấy danh sách thông báo cho người dùng đã đăng nhập
				Method:  http.MethodPost,
				Path:    "/list",
				Handler: notification.ListHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/read",
				Handler: notification.MarkAsReadHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/read-all",
				Handler: notification.MarkAllAsReadHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/unread-count",
				Handler: notification.UnreadHandler(serverCtx),
			},
		},
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/v1/notification"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/add",
				Handler: operation.AddHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/all",
				Handler: operation.AllHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/delete",
				Handler: operation.DeleteHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/get",
				Handler: operation.GetHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/list",
				Handler: operation.ListHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/update",
				Handler: operation.UpdateHandler(serverCtx),
			},
		},
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/v1/operation"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/add",
				Handler: operation_material.AddHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/bulk-set",
				Handler: operation_material.BulkSetHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/delete",
				Handler: operation_material.DeleteHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/get",
				Handler: operation_material.GetHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/list",
				Handler: operation_material.ListHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/update",
				Handler: operation_material.UpdateHandler(serverCtx),
			},
		},
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/v1/operation/material"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/history",
				Handler: person.HistoryMessageHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/send",
				Handler: person.SendMessageHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/template",
				Handler: person.MessageTemplateHandler(serverCtx),
			},
		},
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/v1/person/message"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/add",
				Handler: person.AddHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/delete",
				Handler: person.DeleteHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/expect-task",
				Handler: person.GetExpectedTasksHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/get",
				Handler: person.GetHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/is-person-in",
				Handler: person.IsPersonInHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/list",
				Handler: person.ListHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/query",
				Handler: person.QueryHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/timeline",
				Handler: person.TimelineHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/update",
				Handler: person.UpdateHandler(serverCtx),
			},
		},
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/v1/person"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/add",
				Handler: person_assignment.AddHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/delete",
				Handler: person_assignment.DeleteHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/update",
				Handler: person_assignment.UpdateHandler(serverCtx),
			},
		},
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/v1/person_assignment"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/data/clear",
				Handler: person_data.ClearDataHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/data/get",
				Handler: person_data.GetDataHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/data/set",
				Handler: person_data.SetDataHandler(serverCtx),
			},
		},
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/v1/person"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/add",
				Handler: pipeline.AddHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/delete",
				Handler: pipeline.DeleteHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/get",
				Handler: pipeline.GetHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/list",
				Handler: pipeline.ListHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/switch-deal",
				Handler: pipeline.SwitchDealHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/update",
				Handler: pipeline.UpdateHandler(serverCtx),
			},
		},
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/v1/pipeline"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/add",
				Handler: product.AddHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/delete",
				Handler: product.DeleteHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/get",
				Handler: product.GetHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/list",
				Handler: product.ListHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/update",
				Handler: product.UpdateHandler(serverCtx),
			},
		},
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/v1/product"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/add",
				Handler: product_operation.AddHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/bulk-set",
				Handler: product_operation.BulkSetHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/delete",
				Handler: product_operation.DeleteHandler(serverCtx),
			},
		},
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/v1/product_operation"),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.IpLimitMiddleware, serverCtx.CSRFMiddleware, serverCtx.RateLimitMiddleware},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/form/add",
					Handler: public.FormAddHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/v1/public"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/query",
				Handler: query.QueryHandler(serverCtx),
			},
		},
		rest.WithPrefix("/v1/query"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				// Raw Query
				Method:  http.MethodPost,
				Path:    "/raw",
				Handler: query.RawQueryHandler(serverCtx),
			},
		},
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/v1/query"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/query",
				Handler: queryv2.QueryV2Handler(serverCtx),
			},
		},
		rest.WithPrefix("/v1/query-v2"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/add",
				Handler: referral.AddHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/delete",
				Handler: referral.DeleteHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/search-referrer",
				Handler: referral.SearchReferrerHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/update",
				Handler: referral.UpdateHandler(serverCtx),
			},
		},
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/v1/referral"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/add",
				Handler: schedule.AddHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/delete",
				Handler: schedule.DeleteHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/get-work-schedule",
				Handler: schedule.GetWorkScheduleHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/list",
				Handler: schedule.ListHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/update",
				Handler: schedule.UpdateHandler(serverCtx),
			},
		},
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/v1/schedule"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/add",
				Handler: setting.AddHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/delete",
				Handler: setting.DeleteHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/list",
				Handler: setting.ListHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/sync",
				Handler: setting.SyncHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/update",
				Handler: setting.UpdateHandler(serverCtx),
			},
		},
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/v1/setting"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/add",
				Handler: stage.AddHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/delete",
				Handler: stage.DeleteHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/get",
				Handler: stage.GetHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/list",
				Handler: stage.ListHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/update",
				Handler: stage.UpdateHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/update-stage",
				Handler: stage.UpdateStageHandler(serverCtx),
			},
		},
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/v1/stage"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/data-getfly",
				Handler: sync.SyncDataGetFlyHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/data-up",
				Handler: sync.SyncDataUpHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/task-getfly",
				Handler: sync.SyncTaskGetFlyHandler(serverCtx),
			},
		},
		rest.WithPrefix("/v1/sync"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/add",
				Handler: tag.AddHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/delete",
				Handler: tag.DeleteHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/get",
				Handler: tag.GetHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/list",
				Handler: tag.ListHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/update",
				Handler: tag.UpdateHandler(serverCtx),
			},
		},
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/v1/tag"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/add",
				Handler: tag_deal.AddHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/delete",
				Handler: tag_deal.DeleteHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/update",
				Handler: tag_deal.UpdateHandler(serverCtx),
			},
		},
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/v1/tag/deal"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/add",
				Handler: tag_person.AddHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/delete",
				Handler: tag_person.DeleteHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/update",
				Handler: tag_person.UpdateHandler(serverCtx),
			},
		},
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/v1/tag/person"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/add",
				Handler: task.AddHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/bulk-delete",
				Handler: task.BulkDeleteHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/bulk-update",
				Handler: task.BulkUpdateHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/delete",
				Handler: task.DeleteHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/get",
				Handler: task.GetHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/is-last",
				Handler: task.TaskIsLastHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/list",
				Handler: task.ListHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/query",
				Handler: task.QueryHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/update",
				Handler: task.UpdateHandler(serverCtx),
			},
		},
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/v1/task"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/add",
				Handler: task.AddNoteHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/delete",
				Handler: task.DeleteNoteHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/list",
				Handler: task.ListNoteHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/update",
				Handler: task.UpdateNoteHandler(serverCtx),
			},
		},
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/v1/task/note"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/add",
				Handler: task_assignment.AddHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/assign-tasks",
				Handler: task_assignment.AssignTasksHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/delete",
				Handler: task_assignment.DeleteHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/query",
				Handler: task_assignment.QueryHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/update",
				Handler: task_assignment.UpdateHandler(serverCtx),
			},
		},
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/v1/task_assignment"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/add",
				Handler: task_department.AddHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/delete",
				Handler: task_department.DeleteHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/query",
				Handler: task_department.QueryHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/update",
				Handler: task_department.UpdateHandler(serverCtx),
			},
		},
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/v1/task_department"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/term-add",
				Handler: taxonomy.TermAddHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/term-delete",
				Handler: taxonomy.TermDeleteHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/term-get",
				Handler: taxonomy.TermGetHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/term-list",
				Handler: taxonomy.TermListHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/term-update",
				Handler: taxonomy.TermUpdateHandler(serverCtx),
			},
		},
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/v1/taxonomy"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/active",
				Handler: track.ActiveTrackHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/add",
				Handler: track.AddHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/checkout",
				Handler: track.CheckoutHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/delete",
				Handler: track.DeleteHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/list",
				Handler: track.ListHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/query",
				Handler: track.QueryHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/update",
				Handler: track.UpdateHandler(serverCtx),
			},
		},
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/v1/track"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/add",
				Handler: user.AddHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/delete",
				Handler: user.DeleteHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/get",
				Handler: user.GetHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/list",
				Handler: user.ListHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/update",
				Handler: user.UpdateHandler(serverCtx),
			},
		},
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/v1/user"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/data/clear",
				Handler: user_data.ClearDataHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/data/get",
				Handler: user_data.GetDataHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/data/set",
				Handler: user_data.SetDataHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/data/set-array",
				Handler: user_data.SetArrayDataHandler(serverCtx),
			},
		},
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/v1/user"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/performance",
				Handler: user_stat.PerformanceHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/person",
				Handler: user_stat.PersonStatHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/schedule",
				Handler: user_stat.ScheduleHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/task",
				Handler: user_stat.TaskStatHandler(serverCtx),
			},
		},
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/v1/user/stat"),
	)
}
