package worker

import (
	"bcare/api/internal/svc"
	"bcare/ent/setting"
	"context"
	"fmt"
	"github.com/hibiken/asynq"
)

type ZnsGetTokenHandler struct {
	svcCtx *svc.ServiceContext
}

func NewZnsGetTokenHandler(ctx *svc.ServiceContext) *ZnsGetTokenHandler {
	return &ZnsGetTokenHandler{
		svcCtx: ctx,
	}
}

func (handler *ZnsGetTokenHandler) ProcessTask(ctx context.Context, t *asynq.Task) error {
	dataSetting := handler.svcCtx.Zns.RefreshAccessToken()
	// Convert struct to map[string]interface{}
	valueMap := map[string]interface{}{
		"secret_key":        dataSetting.SecretKey,
		"app_id":            dataSetting.AppID,
		"url_template":      dataSetting.UrlTemplate,
		"url_refresh_token": dataSetting.UrlRefreshToken,
		"access_token":      dataSetting.AccessToken,
		"refresh_token":     dataSetting.RefreshToken,
	}

	settingRecord, err := handler.svcCtx.Ent.Setting.Query().Where(
		setting.And(
			setting.Category("zns"),
			setting.Name("setting"),
		)).First(ctx)
	if err != nil {
		return nil
	}
	// Update the setting record with the new valueMap
	_, err = handler.svcCtx.Ent.Setting.UpdateOneID(settingRecord.ID).
		SetValue(valueMap).
		Save(ctx)
	if err != nil {
		return fmt.Errorf("failed to update setting: %w", err)
	}

	return nil
}
