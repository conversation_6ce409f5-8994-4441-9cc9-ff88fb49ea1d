package worker

import (
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/ent/appointment"
	"bcare/ent/setting"
	"bcare/ent/track"
	"context"
	"github.com/hibiken/asynq"
	"github.com/zeromicro/go-zero/core/logx"
	"time"
)

type DailyHandler struct {
	svcCtx *svc.ServiceContext
}

func NewDailyHandler(ctx *svc.ServiceContext) *DailyHandler {
	return &DailyHandler{
		svcCtx: ctx,
	}
}

func (handler *DailyHandler) ProcessTask(ctx context.Context, t *asynq.Task) error {
	//update các track có trong phòng khám
	err := handler.svcCtx.Ent.Track.Update().Where(track.StageIDNotNil()).ClearPipelineID().ClearStageID().SetEnd(time.Now()).Exec(ctx)
	if err != nil {
		logx.WithContext(ctx).Errorf(err.<PERSON>rror())
		return nil
	}

	// update cuộc hẹn chưa tới trong ngày
	err = handler.svcCtx.Ent.Appointment.Update().Where(appointment.StatusNEQ(types.AppointmentStatusOnTime), appointment.StatusNEQ(types.AppointmentStatusLate), appointment.StatusNEQ(types.AppointmentStatusDeleted), appointment.StartTimeLTE(time.Now()), appointment.ArrivedAtIsNil()).SetStatus(types.AppointmentStatusLate).Exec(ctx)
	if err != nil {
		logx.WithContext(ctx).Errorf(err.Error())
		return nil
	}

	settingOtp := handler.svcCtx.Setting.Otp

	settingOtp.DynamicIpAllowlist = []string{}

	valueMapOtp := map[string]interface{}{
		"enabled":              settingOtp.Enabled,
		"expire_minutes":       settingOtp.ExpireMinutes,
		"length":               settingOtp.Length,
		"receivers":            settingOtp.Receivers,
		"whitelist_ip":         settingOtp.WhitelistIp,
		"dynamic_ip_allowlist": settingOtp.DynamicIpAllowlist,
	}

	settingRecordSms, _ := handler.svcCtx.Ent.Setting.Query().Where(
		setting.And(
			setting.Category("otp"),
			setting.Name("setting"),
		)).First(ctx)
	// Update the setting record with the new valueMap
	if settingRecordSms != nil {
		_, _ = handler.svcCtx.Ent.Setting.UpdateOneID(settingRecordSms.ID).
			SetValue(valueMapOtp).
			Save(ctx)
	}
	return nil

}
