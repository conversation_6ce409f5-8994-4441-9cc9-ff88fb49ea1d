package worker

import (
	"bcare/api/internal/svc"
	"bcare/api/internal/task/tasks"
	"bcare/ent"
	"bcare/ent/messagehistory"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"github.com/hibiken/asynq"
	"github.com/zeromicro/go-zero/core/logx"
)

type ZnsUpdateHandler struct {
	svcCtx *svc.ServiceContext
}

func NewZnsUpdateHandler(ctx *svc.ServiceContext) *ZnsUpdateHandler {
	return &ZnsUpdateHandler{
		svcCtx: ctx,
	}
}

func (handler *ZnsUpdateHandler) ProcessTask(ctx context.Context, t *asynq.Task) error {
	var p tasks.ZnsUpdatePayload
	if err := json.Unmarshal(t.Payload(), &p); err != nil {
		return fmt.Errorf("json.Unmarshal failed: %v: %w", err, asynq.SkipRetry)
	}
	logx.WithContext(ctx).Infof("Processing ZNS update task for MessageID: %s, Phone: %s", p.MessageID, p.Phone)

	resp, err := handler.ZnsInfoRetrieval(ctx, p.MessageID, p.Phone)
	if err != nil {
		return fmt.Errorf("ZnsInfoRetrieval failed: %v", err)
	}

	affected, err := handler.ZnsRecordUpdate(ctx, resp, p.MessageID)
	if err != nil {
		return fmt.Errorf("ZnsRecordUpdate failed: %v", err)
	}

	logx.WithContext(ctx).Infof("MessageID %s updated: %v", p.MessageID, affected)

	return nil
}

func (handler *ZnsUpdateHandler) ZnsInfoRetrieval(ctx context.Context, messageId, phone string) (response Result, err error) {
	//todo : lấy url fetch trạng thái từ setting
	req, err := http.NewRequest("GET", fmt.Sprintf("https://business.openapi.zalo.me/message/status?message_id=%s&phone=%s", messageId, phone), nil)
	if err != nil {
		return response, err
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("access_token", handler.svcCtx.Setting.Zns.AccessToken)

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return response, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return response, err
	}

	// Giải mã phản hồi JSON thành một struct
	err = json.Unmarshal(body, &response)
	if err != nil {
		return response, err
	}

	if response.Error != 0 {
		err = fmt.Errorf("zns info is not ready yet: %v", messageId)
		return response, err
	}
	return
}

func (handler *ZnsUpdateHandler) ZnsRecordUpdate(ctx context.Context, znsInfo Result, messageID string) (affected int, err error) {

	logx.WithContext(ctx).Infof("Entering ZnsRecordUpdate with messageID: %v", messageID)
	defer logx.WithContext(ctx).Infof("Exiting ZnsRecordUpdate with messageID: %v", messageID)
	//
	znsRecord := &ent.MessageHistory{}
	//
	data := znsInfo.Data
	loc, _ := time.LoadLocation("Asia/Ho_Chi_Minh")
	t, err := time.ParseInLocation("15:04 02/01/2006", data.DeliveryTime, loc)
	if err == nil {
		znsRecord.DeliveredAt = &t
	}
	return handler.svcCtx.Ent.MessageHistory.Update().Where(messagehistory.MessageID(messageID)).SetMessageHistory(znsRecord).Save(ctx)
}

type Result struct {
	Error   int
	Message string
	Data    struct {
		DeliveryTime string `json:"delivery_time"`
		Message      string `json:"message"`
		Status       int    `json:"status"`
	}
}
