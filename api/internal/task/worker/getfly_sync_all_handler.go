package worker

import (
	"bcare/api/internal/svc"
	"bcare/api/internal/task/tasks"
	"bcare/api/model"
	"context"
	"encoding/json"
	"fmt"
	"github.com/hibiken/asynq"
	"github.com/zeromicro/go-zero/core/logx"
)

type GetFlySyncAllHandler struct {
	svcCtx *svc.ServiceContext
}

func NewGetFlySyncAllHandler(svcCtx *svc.ServiceContext) *GetFlySyncAllHandler {
	return &GetFlySyncAllHandler{
		svcCtx: svcCtx,
	}
}

func (handler *GetFlySyncAllHandler) ProcessTask(ctx context.Context, t *asynq.Task) error {
	var p tasks.GetFlySyncAllPayload
	if err := json.Unmarshal(t.Payload(), &p); err != nil {
		logx.WithContext(ctx).Errorf("json.Unmarshal failed: %v: %w", err, asynq.SkipRetry)
		return fmt.Errorf("json.Unmarshal failed: %v: %w", err, asynq.SkipRetry)
	}

	err := model.Account2PersonSync(p.Account, ctx, handler.svcCtx.Ent)
	if err != nil {
		logx.WithContext(ctx).Errorf("model.Account2PersonSync err: %v", err)
		return err
	}
	return nil
}
