package worker

import (
	"bcare/api/internal/svc"
	"bcare/api/internal/task/tasks"
	"bcare/common/bconst"
	"bcare/ent"
	"bcare/ent/call"
	"bcare/ent/person"
	"bcare/ent/predicate"
	"bytes"
	"context"
	"encoding/json"
	"entgo.io/ent/dialect/sql"
	"fmt"
	"github.com/hibiken/asynq"
	"github.com/zeromicro/go-zero/core/logx"
	"io"
	"net/http"
	"strconv"
	"strings"
	"time"
)

type CallSyncHandler struct {
	svcCtx *svc.ServiceContext
}

type CallApiListResponse struct {
	Response []CdrWrapper `json:"response"`
}

type CdrWrapper struct {
	Cdr CdrData `json:"Cdr"`
}

type CdrData struct {
	Version       string  `json:"@version"`
	Timestamp     string  `json:"@timestamp"`
	Pdd           string  `json:"pdd"`
	StartEpoch    int64   `json:"start_epoch"`
	EndEpoch      int64   `json:"end_epoch"`
	Mos           float64 `json:"mos"`
	DomainUUID    string  `json:"domain_uuid"`
	Destination   string  `json:"destination"`
	Recording     string  `json:"recording"`
	CidName       string  `json:"cid_name"`
	Domain        string  `json:"domain"`
	Start         string  `json:"start"`
	CallID        string  `json:"call_id"`
	Status        string  `json:"status"`
	Source        string  `json:"source"`
	UUID          string  `json:"uuid"`
	Direction     string  `json:"direction"`
	Tta           string  `json:"tta"`
	Duration      string  `json:"duration"`
	RecordingFile string  `json:"recording_file"`
}

func NewCallSyncHandler(svcCtx *svc.ServiceContext) *CallSyncHandler {
	return &CallSyncHandler{
		svcCtx: svcCtx,
	}
}

func (handler *CallSyncHandler) ProcessTask(ctx context.Context, t *asynq.Task) error {
	var p tasks.CallSyncPayload
	p.From = time.Now().Add(-30 * time.Minute)
	p.To = time.Now().Add(-10 * time.Minute)

	resp, err := handler.CallInfoRetrieval(ctx, p.From, p.To)
	if err != nil {
		logx.WithContext(ctx).Errorf("CallInfoRetrieval failed: %v", err)
		return fmt.Errorf("CallInfoRetrieval failed: %v", err)
	}

	affected, err := handler.CallRecordUpdate(ctx, resp, p.From, p.To)
	if err != nil {
		logx.WithContext(ctx).Errorf("CallRecordUpdate failed: %v", err)
		return fmt.Errorf("CallRecordUpdate failed: %v", err)
	}

	logx.WithContext(ctx).Infof("Call sync completed for period %v to %v: %d records affected",
		p.From.Format("2006-01-02 15:04:05"),
		p.To.Format("2006-01-02 15:04:05"),
		affected)
	return nil
}

func (handler *CallSyncHandler) CallInfoRetrieval(ctx context.Context, from, to time.Time) (response CallApiListResponse, err error) {

	type Request struct {
		Submission struct {
			ApiKey    string `json:"api_key"`
			ApiSecret string `json:"api_secret"`
			DateRange struct {
				From string `json:"from"`
				To   string `json:"to"`
			} `json:"date_range"`
		} `json:"submission"`
	}

	var rq Request
	rq.Submission.DateRange.From = from.Format("2006-01-02 15:04:05")
	rq.Submission.DateRange.To = to.Format("2006-01-02 15:04")
	rq.Submission.ApiKey = handler.svcCtx.Setting.Call.ApiKey
	rq.Submission.ApiSecret = handler.svcCtx.Setting.Call.ApiSecret

	requestJSON, _ := json.Marshal(rq)
	req, err := http.NewRequest("POST", handler.svcCtx.Setting.Call.ApiUrl, bytes.NewBuffer(requestJSON))
	if err != nil {
		logx.WithContext(ctx).Errorf("Error creating HTTP request: %v", err)
		return response, err
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	// Thực hiện yêu cầu
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		logx.WithContext(ctx).Errorf("Error sending HTTP request: %v", err)
		return response, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		logx.WithContext(ctx).Errorf("Error reading response body: %v", err)
		return response, err
	}

	// Giải mã phản hồi JSON thành một struct
	err = json.Unmarshal(body, &response)
	if err != nil {
		logx.WithContext(ctx).Errorf("Error unmarshaling response: %v", err)
		return response, err
	}
	if len(response.Response) == 0 {
		err = fmt.Errorf("call info is not ready yet %s - %s", from, to)
		logx.WithContext(ctx).Errorf(err.Error())
		return response, err
	}

	return
}

func (handler *CallSyncHandler) CallRecordUpdate(ctx context.Context, callInfo CallApiListResponse, from, to time.Time) (affected int, err error) {
	// Prepare slices for bulk operations
	var newCalls []*ent.CallCreate
	var updatedCount int

	// Step 1: Filter and prepare records
	for _, resp := range callInfo.Response {
		cdr := resp.Cdr

		// Check if UUID exists
		existByUUID, err := handler.svcCtx.Ent.Call.Query().
			Where(call.UUID(cdr.UUID)).
			Exist(ctx)

		if err != nil {
			logx.WithContext(ctx).Errorf("Error checking UUID existence: %v", err)
			return affected, err
		}

		// Skip if UUID already exists
		if existByUUID {
			continue
		}

		// Check if CallID exists
		existingCall, err := handler.svcCtx.Ent.Call.Query().
			Where(
				call.Or(
					call.CallID(cdr.CallID),          // Thử exact match trước
					call.CallIDHasPrefix(cdr.CallID), // Nếu không match, thử prefix match
				),
			).
			First(ctx)

		if err != nil && !ent.IsNotFound(err) {
			logx.WithContext(ctx).Errorf("Error checking CallID existence: %v", err)
			return affected, err
		}

		// Prepare common data
		t, _ := time.Parse("15:04:05", cdr.Duration)
		seconds := t.Hour()*3600 + t.Minute()*60 + t.Second()
		startTime, _ := time.Parse(time.RFC3339, cdr.Timestamp)

		pdd, _ := strconv.Atoi(cdr.Pdd)
		tta, _ := strconv.Atoi(cdr.Tta)

		phone := ""
		destination := cdr.Destination
		source := cdr.Source
		if cdr.Direction == "outbound" {
			phone = cdr.Destination
			if len(phone) == 9 || !strings.HasPrefix(phone, "0") {
				if !strings.HasPrefix(phone, "0") {
					phone = "0" + phone
				}
			}
			destination = phone
		} else {
			phone = cdr.Source
			if len(phone) != 10 || !strings.HasPrefix(phone, "0") {
				if !strings.HasPrefix(phone, "0") {
					phone = "0" + phone
				}
			}
			source = phone
		}

		// Find related person
		conditions := []predicate.Person{
			person.Phone(phone),
		}
		conditions = append(conditions, func(s *sql.Selector) {
			s.Where(sql.P(func(b *sql.Builder) {
				b.WriteString("person_field->>'secondary_phone' = ")
				b.WriteString(fmt.Sprintf("'%s'", phone))
			}))
		})

		personRecord, _ := handler.svcCtx.Ent.Person.Query().
			Where(person.Or(conditions...)).First(ctx)

		// If existing call found, update it
		if existingCall != nil {
			updateQuery := existingCall.Update().
				SetCallID(cdr.CallID).
				SetDirection(cdr.Direction).
				SetSource(source).
				SetDestination(destination).
				SetUUID(cdr.UUID).
				SetPdd(pdd).
				SetTta(tta).
				SetCallStatus(cdr.Status).
				SetScore(float32(cdr.Mos)).
				SetStartTime(startTime).
				SetDuration(seconds)

			// Only update recording file if it exists and is valid
			if cdr.RecordingFile != "" && handler.CheckRecordingFileExists(cdr.RecordingFile) {
				updateQuery.SetRecordingFile(cdr.RecordingFile)
			}

			// Set person ID if found
			if personRecord != nil {
				updateQuery.SetPersonID(personRecord.ID)
			}

			// Execute update
			_, err = updateQuery.Save(ctx)
			if err != nil {
				logx.WithContext(ctx).Errorf("Error updating existing call: %v", err)
				return affected, err
			}
			updatedCount++
			continue
		}

		// Create new record if no existing call found
		newCallCreate := handler.svcCtx.Ent.Call.Create().
			SetCallID(cdr.CallID).
			SetDirection(cdr.Direction).
			SetSource(source).
			SetDestination(destination).
			SetUUID(cdr.UUID).
			SetPdd(pdd).
			SetTta(tta).
			SetCallStatus(cdr.Status).
			SetScore(float32(cdr.Mos)).
			SetStartTime(startTime).
			SetDuration(seconds).
			SetStatus(bconst.StatusNormal)

		// Only set recording file if it exists and is valid
		if cdr.RecordingFile != "" && handler.CheckRecordingFileExists(cdr.RecordingFile) {
			newCallCreate.SetRecordingFile(cdr.RecordingFile)
		}

		// Set person ID if found
		if personRecord != nil {
			newCallCreate.SetPersonID(personRecord.ID)
		}

		newCalls = append(newCalls, newCallCreate)
	}

	// Bulk create new records if any
	if len(newCalls) > 0 {
		created, err := handler.svcCtx.Ent.Call.CreateBulk(newCalls...).Save(ctx)
		if err != nil {
			logx.WithContext(ctx).Errorf("Error bulk creating call records: %v", err)
			return affected, err
		}
		affected += len(created)
	}

	// Step 2: Delete records without UUID in the time range
	deletedCount, err := handler.svcCtx.Ent.Call.Delete().
		Where(
			call.And(
				call.StartTimeLTE(to),
				call.StartTimeGTE(from),
				call.UUIDIsNil(),
				//call.UserIDIsNil(),
			),
		).
		Exec(ctx)

	if err != nil {
		logx.WithContext(ctx).Errorf("Error deleting records without UUID: %v", err)
		return affected, err
	}

	affected += deletedCount + updatedCount

	logx.WithContext(ctx).Infof("CallRecordUpdate completed: %d records affected (%d created, %d updated, %d deleted)",
		affected, len(newCalls), updatedCount, deletedCount)
	return affected, nil
}

func (handler *CallSyncHandler) CheckRecordingFileExists(recordingURL string) bool {
	if recordingURL == "" {
		return false
	}

	resp, err := http.Get(recordingURL)
	if err != nil {
		return false
	}
	defer resp.Body.Close()

	// Read response body
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return false
	}

	// Check if response is error message
	var response struct {
		Status int    `json:"status"`
		Msg    string `json:"msg"`
	}

	if err := json.Unmarshal(body, &response); err == nil {
		if response.Status == 404 && response.Msg == "File not found" {
			return false
		}
	}

	return resp.StatusCode == 200
}
