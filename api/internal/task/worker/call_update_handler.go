package worker

import (
	"bcare/api/internal/svc"
	"bcare/api/internal/task/tasks"
	"bcare/common/bconst"
	"bcare/ent/call"
	"bcare/ent/schema"
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/hibiken/asynq"
	"github.com/zeromicro/go-zero/core/logx"
)

type CallUpdateHandler struct {
	svcCtx *svc.ServiceContext
}

func NewCallUpdateHandler(ctx *svc.ServiceContext) *CallUpdateHandler {
	return &CallUpdateHandler{
		svcCtx: ctx,
	}
}

func (handler *CallUpdateHandler) ProcessTask(ctx context.Context, t *asynq.Task) error {
	var p tasks.CallUpdatePayload
	if err := json.Unmarshal(t.Payload(), &p); err != nil {
		return fmt.Errorf("json.Unmarshal failed: %v: %w", err, asynq.SkipRetry)
	}
	callRecord, _ := handler.svcCtx.Ent.Call.Query().Where(call.CallID(p.CallID)).First(ctx)
	if callRecord == nil {
		return asynq.SkipRetry
	}

	count, _ := asynq.GetRetryCount(ctx)
	if count > 5 {
		affected, err := handler.CallRecordDelete(ctx, p.CallID)
		if err != nil {
			return fmt.Errorf("CallRecordDelete failed: %v", err)
		}

		logx.WithContext(ctx).Infof("CallID %d delete: %v", p.CallID, affected)
	}

	resp, err := handler.CallInfoRetrieval(ctx, p.CallID)
	if err != nil {
		return nil
	}

	affected, err := handler.CallRecordUpdate(ctx, resp)
	if err != nil {
		return fmt.Errorf("CallRecordUpdate failed: %v", err)
	}

	logx.WithContext(ctx).Infof("CallID %d updated: %v", p.CallID, affected)

	return nil
}

func (handler *CallUpdateHandler) CallInfoRetrieval(ctx context.Context, callId string) (response CallApiResponse, err error) {
	logx.WithContext(ctx).Infof("Entering CallInfoRetrieval with callId: %s", callId)
	defer logx.WithContext(ctx).Infof("Exiting CallInfoRetrieval with callId: %s", callId)

	type Request struct {
		Submission struct {
			ApiKey    string `json:"api_key"`
			ApiSecret string `json:"api_secret"`
			CallId    string `json:"call_id"`
		} `json:"submission"`
	}
	var rq Request
	if len(callId) >= 37 {
		rq.Submission.CallId = callId[:37]
	} else {
		rq.Submission.CallId = callId
	}
	rq.Submission.ApiKey = handler.svcCtx.Setting.Call.ApiKey
	rq.Submission.ApiSecret = handler.svcCtx.Setting.Call.ApiSecret

	requestJSON, _ := json.Marshal(rq)

	req, err := http.NewRequest("POST", handler.svcCtx.Setting.Call.ApiUrl, bytes.NewBuffer(requestJSON))
	if err != nil {
		return response, err
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	// Thực hiện yêu cầu
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return response, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return response, err
	}

	// Giải mã phản hồi JSON thành một struct
	err = json.Unmarshal(body, &response)
	if err != nil {
		return response, err
	}

	if len(response.Response) == 0 {
		return response, nil
	}

	return
}

func (handler *CallUpdateHandler) CallRecordDelete(ctx context.Context, callID string) (affected int, err error) {
	return handler.svcCtx.Ent.Call.Delete().Where(call.CallID(callID)).Exec(ctx)
}

func (handler *CallUpdateHandler) CallRecordUpdate(ctx context.Context, callInfo CallApiResponse) (affected int, err error) {
	if len(callInfo.Response) != 1 {
		err = fmt.Errorf("parameter is not valid, maybe call info is not ready yet")
		return 0, err
	}

	cdr := callInfo.Response[0].Cdr

	logx.WithContext(ctx).Infof("Entering CallRecordUpdate with callID: %v", cdr.CallID)
	defer logx.WithContext(ctx).Infof("Exiting CallRecordUpdate with callID: %v", cdr.CallID)

	skipSoftDelete := schema.SkipSoftDelete(ctx)

	callRecord, _ := handler.svcCtx.Ent.Call.Query().Where(
		call.Or(
			call.CallID(cdr.CallID),          // Thử exact match trước
			call.CallIDHasPrefix(cdr.CallID), // Nếu không match, thử prefix match
		),
	).First(skipSoftDelete)

	if callRecord == nil {
		return 0, nil
	}
	phone := ""
	destination := cdr.Destination
	source := cdr.Source
	if cdr.Direction == "outbound" {
		phone = cdr.Destination
		// Check if phone not has 10 digits or not start with 0
		if len(phone) == 9 || !strings.HasPrefix(phone, "0") {
			// If phone not start with 0, add 0 prefix
			if !strings.HasPrefix(phone, "0") {
				phone = "0" + phone
			}
		}
		destination = phone
	} else {
		phone = cdr.Source
		// Check if phone not has 10 digits or not start with 0
		if len(phone) != 10 || !strings.HasPrefix(phone, "0") {
			// If phone not start with 0, add 0 prefix
			if !strings.HasPrefix(phone, "0") {
				phone = "0" + phone
			}
		}
		source = phone
	}

	callRecord.CallID = cdr.CallID
	callRecord.Direction = cdr.Direction
	callRecord.Source = source
	callRecord.Destination = destination
	callRecord.UUID = cdr.UUID
	callRecord.RecordingFile = cdr.RecordingFile
	callRecord.Pdd, _ = strconv.Atoi(cdr.Pdd)
	callRecord.Tta, _ = strconv.Atoi(cdr.Tta)
	callRecord.CallStatus = cdr.Status
	callRecord.Score = float32(cdr.Mos)
	callRecord.StartTime, _ = time.Parse(time.RFC3339, cdr.Timestamp)
	callRecord.Status = bconst.StatusNormal
	t, _ := time.Parse("15:04:05", cdr.Duration)
	seconds := t.Hour()*3600 + t.Minute()*60 + t.Second()
	callRecord.Duration = seconds

	return handler.svcCtx.Ent.Call.Update().Where(call.CallID(cdr.CallID)).SetCall(callRecord).ClearDeletedAt().Save(skipSoftDelete)
}

type CallApiResponse struct {
	Response []struct {
		Cdr struct {
			Version       string  `json:"@version"`
			Pdd           string  `json:"pdd"`
			StartEpoch    int     `json:"start_epoch"`
			EndEpoch      int     `json:"end_epoch"`
			Mos           float64 `json:"mos"`
			UUID          string  `json:"uuid"`
			Direction     string  `json:"direction"`
			CidName       string  `json:"cid_name"`
			Destination   string  `json:"destination"`
			Source        string  `json:"source"`
			CallID        string  `json:"call_id"`
			DomainUUID    string  `json:"domain_uuid"`
			Duration      string  `json:"duration"`
			Recording     string  `json:"recording"`
			Tta           string  `json:"tta"`
			Timestamp     string  `json:"@timestamp"`
			Domain        string  `json:"domain"`
			Start         string  `json:"start"`
			Status        string  `json:"status"`
			RecordingFile string  `json:"recording_file"`
		} `json:"Cdr"`
	} `json:"response"`
}
