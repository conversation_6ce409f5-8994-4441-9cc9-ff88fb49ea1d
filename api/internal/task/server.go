package task

import (
	"bcare/api/internal/svc"
	"bcare/api/internal/task/tasks"
	"bcare/api/internal/task/worker"
	"bcare/common/bconst"
	"bcare/common/berr"
	"log"
	"time"

	"github.com/hibiken/asynq"
	"github.com/zeromicro/go-zero/core/logx"
)

var server *asynq.Server
var serverReady = make(chan bool, 1)

func InitializeTaskServer(redisAddr string, redisPass string, queueName string) {
	server = asynq.NewServer(
		asynq.RedisClientOpt{
			Addr:     redisAddr,
			Password: redisPass,
		},
		asynq.Config{
			LogLevel:    asynq.WarnLevel,
			Concurrency: 50,
			Queues: map[string]int{
				queueName: 10, // Only process tasks from service-specific queue
			},
		},
	)
}

func RunServer(svcCtx *svc.ServiceContext) {
	defer berr.Recover()
	svcName := svcCtx.Config.Name
	logx.Infof("Task server starting with service name: %s", svcName)

	// mux maps a type to a handler
	mux := asynq.NewServeMux()

	// Register handlers without service name prefix since we use separate queues
	logx.Infof("Registering handler: %s", tasks.TypeCallUpdate)
	mux.Handle(tasks.TypeCallUpdate, worker.NewCallUpdateHandler(svcCtx))

	logx.Infof("Registering handler: %s", tasks.TypeCallSync)
	mux.Handle(tasks.TypeCallSync, worker.NewCallSyncHandler(svcCtx))

	logx.Infof("Registering handler: %s", tasks.TypeZnsUpdate)
	mux.Handle(tasks.TypeZnsUpdate, worker.NewZnsUpdateHandler(svcCtx))

	logx.Infof("Registering handler: %s", tasks.TypeSyncDataUp)
	mux.Handle(tasks.TypeSyncDataUp, worker.NewSyncDataUpHandler(svcCtx))

	logx.Infof("Registering handler: %s", tasks.TypeDaily)
	mux.Handle(tasks.TypeDaily, worker.NewDailyHandler(svcCtx))

	logx.Infof("Registering handler: %s", tasks.TypeBackupDb)
	mux.Handle(tasks.TypeBackupDb, worker.NewBackupDbHandler(svcCtx))

	logx.Infof("Registering handler: %s", tasks.TypeGetSyncAllFly)
	mux.Handle(tasks.TypeGetSyncAllFly, worker.NewGetFlySyncAllHandler(svcCtx))

	logx.Infof("Registering handler: %s", tasks.TypeRecurringTasks)
	mux.Handle(tasks.TypeRecurringTasks, worker.NewRecurringTaskHandler(svcCtx))

	logx.Infof("Registering handler: %s", tasks.TypeZnsGetToken)
	mux.Handle(tasks.TypeZnsGetToken, worker.NewZnsGetTokenHandler(svcCtx))

	logx.Infof("Registering handler: %s", tasks.TypeExcelExport)
	mux.Handle(tasks.TypeExcelExport, worker.NewExportExcelHandler(svcCtx))

	logx.Infof("Task server handlers registered, starting server...")

	// Signal that server is ready
	select {
	case serverReady <- true:
	default:
	}

	if err := server.Run(mux); err != nil {
		log.Fatalf("could not run server: %v", err)
	}
}

func WaitForServerReady() {
	<-serverReady
}

func RunSchedule(svcCtx *svc.ServiceContext) {
	defer berr.Recover()

	// Use manual scheduling with ticker to control queue destination
	go func() {
		ticker := time.NewTicker(1 * time.Minute) // Check every minute
		defer ticker.Stop()

		lastDailyRun := time.Now().Add(-25 * time.Hour)      // Ensure first run
		lastHourlyRun := time.Now().Add(-2 * time.Hour)      // Ensure first run
		lastCallSyncRun := time.Now().Add(-11 * time.Minute) // Ensure first run
		lastBackupRun := time.Now().Add(-2 * time.Hour)      // Ensure first run
		lastTokenRun := time.Now().Add(-25 * time.Hour)      // Ensure first run

		logx.Infof("Starting custom scheduler for service: %s, queue: %s", svcCtx.Config.Name, QueueName)

		for range ticker.C {
			now := time.Now()

			// Daily task - run at 0h (midnight) every day
			if now.Hour() == 0 && now.Minute() == 0 && now.Sub(lastDailyRun) > 23*time.Hour {
				dailyTask, err := tasks.NewDailyTask()
				if err == nil {
					_, err = EnqueueTaskWithQueue(dailyTask)
					if err == nil {
						logx.Infof("Enqueued daily task to queue: %s", QueueName)
						lastDailyRun = now
					} else {
						logx.Errorf("Failed to enqueue daily task: %v", err)
					}
				}
			}

			// Recurring task - run every hour (at minute 0)
			if now.Minute() == 0 && now.Sub(lastHourlyRun) > 59*time.Minute {
				recurringTask, err := tasks.NewRecurringTask()
				if err == nil {
					_, err = EnqueueTaskWithQueue(recurringTask)
					if err == nil {
						logx.Infof("Enqueued recurring task to queue: %s", QueueName)
						lastHourlyRun = now
					} else {
						logx.Errorf("Failed to enqueue recurring task: %v", err)
					}
				}
			}

			if svcCtx.Config.Env == bconst.EnvProduction {
				// Call sync task - run every 10 minutes
				if now.Minute()%10 == 0 && now.Sub(lastCallSyncRun) > 9*time.Minute {
					callSyncTask, err := tasks.NewCallSyncTask()
					if err == nil {
						_, err = EnqueueTaskWithQueue(callSyncTask)
						if err == nil {
							logx.Infof("Enqueued call sync task to queue: %s", QueueName)
							lastCallSyncRun = now
						} else {
							logx.Errorf("Failed to enqueue call sync task: %v", err)
						}
					}
				}

				// Backup task - run every hour (at minute 0) from 8-22
				if now.Hour() >= 8 && now.Hour() <= 22 && now.Minute() == 0 && now.Sub(lastBackupRun) > 59*time.Minute {
					backupDbTask, err := tasks.NewBackupDbTask()
					if err == nil {
						_, err = EnqueueTaskWithQueue(backupDbTask)
						if err == nil {
							logx.Infof("Enqueued backup db task to queue: %s", QueueName)
							lastBackupRun = now
						} else {
							logx.Errorf("Failed to enqueue backup db task: %v", err)
						}
					}
				}

				// ZNS token task - run daily at 13:00
				if now.Hour() == 13 && now.Minute() == 0 && now.Sub(lastTokenRun) > 23*time.Hour {
					getTokenTask, err := tasks.NewZnsGetTokenTask()
					if err == nil {
						_, err = EnqueueTaskWithQueue(getTokenTask)
						if err == nil {
							logx.Infof("Enqueued ZNS token task to queue: %s", QueueName)
							lastTokenRun = now
						} else {
							logx.Errorf("Failed to enqueue ZNS token task: %v", err)
						}
					}
				}
			}

		}
	}()
}
