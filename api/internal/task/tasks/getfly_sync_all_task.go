package tasks

import (
	"bcare/api/model"
	"encoding/json"
	"github.com/hibiken/asynq"
	"time"
)

type GetFlySyncAllPayload struct {
	Account model.AccountSync
}

func NewGetFlySyncAllTask(account model.AccountSync) (*asynq.Task, error) {
	payload, err := json.Marshal(GetFlySyncAllPayload{Account: account})
	if err != nil {
		return nil, err
	}
	return asynq.NewTask(TypeGetSyncAllFly, payload, asynq.MaxRetry(5), asynq.Timeout(20*time.Minute), asynq.Retention(24*time.Hour)), nil
}
