package tasks

import (
	"encoding/json"
	"time"

	"github.com/hibiken/asynq"
)

type ZnsUpdatePayload struct {
	MessageID string `json:"message_id"`
	Phone     string `json:"phone"`
}

func NewZnsUpdateTask(messageId, phone string) (*asynq.Task, error) {
	payload, err := json.Marshal(ZnsUpdatePayload{
		MessageID: messageId,
		Phone:     phone,
	})
	if err != nil {
		return nil, err
	}
	return asynq.NewTask(TypeZnsUpdate, payload, asynq.MaxRetry(5), asynq.Timeout(20*time.Minute), asynq.Retention(24*time.Hour)), nil
}
