package tasks

import (
	"encoding/json"
	"time"

	"github.com/hibiken/asynq"
)

type CallUpdatePayload struct {
	CallID string
}

func NewCallUpdateTask(callID string) (*asynq.Task, error) {
	payload, err := json.Marshal(CallUpdatePayload{CallID: callID})
	if err != nil {
		return nil, err
	}
	// task options can be passed to NewTask, which can be overridden at enqueue time.
	return asynq.NewTask(TypeCallUpdate, payload, asynq.MaxRetry(5), asynq.Timeout(20*time.Minute), asynq.Retention(24*time.Hour)), nil
}
