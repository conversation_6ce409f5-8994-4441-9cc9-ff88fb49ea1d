package tasks

import (
	"encoding/json"
	"time"

	"github.com/hibiken/asynq"
)

type SyncDataPayload struct {
	PersonId int  `json:"person_id"`
	IsSan    bool `json:"is_san"`
}

func NewSyncDataUpTask(personId int) (*asynq.Task, error) {
	payload, err := json.Marshal(SyncDataPayload{PersonId: personId})
	if err != nil {
		return nil, err
	}
	// task options can be passed to NewTask, which can be overridden at enqueue time.
	return asynq.NewTask(TypeSyncDataUp, payload, asynq.MaxRetry(5), asynq.Timeout(20*time.Minute)), nil
}
