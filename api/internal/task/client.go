package task

import (
	"github.com/hibiken/asynq"
	"time"
)

var (
	Client    *asynq.Client
	Scheduler *asynq.Scheduler
	loc, _    = time.LoadLocation("Asia/Ho_Chi_Minh")
)

func InitializeTaskClients(redisAddr string, redisPass string) {
	Client = asynq.NewClient(asynq.RedisClientOpt{
		Addr:     redisAddr,
		Password: redisPass,
	})

	Scheduler = asynq.NewScheduler(
		asynq.RedisClientOpt{
			Addr:     redisAddr,
			Password: redisPass,
		},
		&asynq.SchedulerOpts{
			Location: loc,
			LogLevel: asynq.WarnLevel,
		},
	)
}
