package realtime

import (
	"bcare/api/internal/svc"
	"bcare/common/websocket"
	"encoding/json"
	"fmt"
	"github.com/golang-jwt/jwt/v4"
	"github.com/lxzan/gws"
	"github.com/zeromicro/go-zero/core/logx"
	"time"
)

func AuthHandler(hub websocket.WsHub, svcCtx *svc.ServiceContext) func(client *gws.Conn, msg *WsMessage) {
	return func(client *gws.Conn, msg *WsMessage) {
		var userID int
		var err error
		//Event that client want to handle
		accessToken, ok := msg.Data.(string)
		if !ok {
			err = fmt.Errorf("[WebSocket] invalid token format: expected string, got %T", msg.Data)
			client.WriteClose(1002, []byte("ws: invalid token format"))
			return
		}
		if accessToken == "" {
			err = fmt.Errorf("[WebSocket] access token is not valid")
			client.WriteClose(1008, []byte("ws: access token is not valid"))
			return
		}

		// Parse và xác thực access token
		token, err := jwt.Parse(accessToken, func(token *jwt.Token) (interface{}, error) {
			// Cung cấp secret key để xác thực token
			return []byte(svcCtx.Config.Auth.AccessSecret), nil
		})

		if err != nil || !token.Valid {
			err = fmt.Errorf("[WebSocket] invalid access token")
			client.WriteClose(1008, []byte("ws: invalid access token"))
			return
		}
		// Lấy thông tin user ID từ claims của token
		claims, ok := token.Claims.(jwt.MapClaims)
		if !ok {
			err = fmt.Errorf("[WebSocket] invalid token claims")
			client.WriteClose(1008, []byte("ws: invalid token claims"))
			return
		}

		expFloat, ok := claims["exp"].(float64)
		if !ok {
			err = fmt.Errorf("[WebSocket] invalid expiration time in token")
			client.WriteClose(1008, []byte("ws: invalid expiration time in token"))
			return
		}
		expTime := time.Unix(int64(expFloat), 0)
		if time.Now().After(expTime) {
			// Token đã hết hạn, xử lý lỗi hoặc yêu cầu người dùng đăng nhập lại
			client.WriteClose(1008, []byte("ws: token expired"))
			return
		}

		if userIDFloat, ok := claims[svcCtx.Config.Auth.IdentityKey].(float64); ok {
			userID = int(userIDFloat)
		} else {
			err = fmt.Errorf("[WebSocket] invalid user ID in token")
			client.WriteClose(1008, []byte("ws: invalid user ID in token"))
			return
		}
		client.Session().Store("user_id", userID)

		//Subscribe connection vào topic <user_id>
		Subscribe(NewSubscriber(client), GetUserIDString(client))
		userConnCount := CountSubscriberByTopic(GetUserIDString(client))

		logx.Infof("[WebSocket] UserID %d đã mở %d kết nối\n", userID, userConnCount)

		resp := &WsMessage{
			Event: "auth",
			Data:  GetKey(client),
		}
		if err != nil {
			logx.Errorf("ws %v", err)
		}

		payload, _ := json.Marshal(resp)
		hub.SendToUser(client, payload)
	}
}
