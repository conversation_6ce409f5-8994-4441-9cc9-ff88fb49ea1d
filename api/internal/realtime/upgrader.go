package realtime

import (
	"bcare/api/internal/svc"
	"bcare/common/ctxdata"
	"github.com/lxzan/gws"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/rest"
	"net/http"
	"strconv"
)

func NewDefaultUpgrader(eventHandler gws.Event) *gws.Upgrader {
	return gws.NewUpgrader(eventHandler, &gws.ServerOption{
		ParallelEnabled: true,         // Parallel message processing
		Recovery:        gws.Recovery, // Exception recovery
		PermessageDeflate: gws.PermessageDeflate{
			Enabled:               true,
			ServerContextTakeover: false,
			ClientContextTakeover: false,
		},

		Authorize: SimpleAuthorizer,
	})
}

// JWTAuthorizer Extract UserID từ JWT Claims để authenticate
var JWTAuthorizer = func(r *http.Request, session gws.SessionStorage) bool {
	var userID = strconv.Itoa(ctxdata.GetUidFromCtx(r.Context()))

	if userID == "" {
		return false
	}

	session.Store("user_id", userID)
	session.Store("key", r.Header.Get("Sec-WebSocket-Key"))

	return true
}

// SimpleAuthorizer Pass in the userID in querystring
// Use Sec-WebSocket-Key as the connection key
// When refreshing the page, the OnClose/OnError event of the previous connection will be triggered. At this time, you need to compare the key and delete the connection stored in the map.
var SimpleAuthorizer = func(r *http.Request, session gws.SessionStorage) bool {
	//userID, _ := strconv.ParseInt(r.URL.Query().Get("id"), 10, 64)
	//if userID == 0 {
	//	return false
	//}
	//session.Store("user_id", int(userID))
	session.Store("key", r.Header.Get("Sec-WebSocket-Key"))
	return true
}

func WebsocketBootstrap(ctx *svc.ServiceContext, server *rest.Server) {
	// Khởi tạo global hub
	hub := InitGlobalHub(ctx)
	upgrader := NewDefaultUpgrader(hub)

	server.AddRoute(rest.Route{
		Method: http.MethodGet,
		Path:   "/ws",
		Handler: func(w http.ResponseWriter, r *http.Request) {
			socket, err := upgrader.Upgrade(w, r)
			var channel = make(chan []byte, 8)
			var closer = make(chan struct{})
			socket.Session().Store("channel", channel)
			socket.Session().Store("closer", closer)

			if err != nil {
				logx.Errorf("WS Accept: " + err.Error())
				return
			}
			go func() {
				for {
					select {
					case p := <-channel:
						_ = socket.WriteMessage(gws.OpcodeText, p)
					case <-closer:
						return
					}
				}
			}()
			go func() {
				socket.ReadLoop()
			}()
		},
	},
	//rest.WithJwt(ctx.Config.Auth.AccessSecret),
	)
}
