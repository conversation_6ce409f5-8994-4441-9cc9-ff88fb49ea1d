package realtime

import (
	"bcare/api/internal/svc"
	"encoding/json"
	"github.com/lxzan/gws"
	"github.com/zeromicro/go-zero/core/logx"
	"strconv"
	"sync"
	"sync/atomic"
	"time"
)

const (
	PingInterval               = 10 * time.Second // Client heartbeat interval
	HeartbeatInterval          = 20 * time.Second // Heartbeat wait timeout
	HeartbeatWaitTimeout       = 50 * time.Second // Heartbeat wait timeout
	NotificationTopic          = "notification"
	DefaultBroadcastBufferSize = 1000 // Default broadcast channel buffer size
)

var (
	mutexMap sync.Map
	// Thêm biến globalHub và mutex để bảo vệ việc khởi tạo
	globalHub     *Hub
	globalHubOnce sync.Once
	globalHubMu   sync.RWMutex
)

// GetGlobalHub trả về instance của global hub
func GetGlobalHub() *Hub {
	globalHubMu.RLock()
	defer globalHubMu.RUnlock()
	return globalHub
}

// InitGlobalHub khởi tạo global hub với context
func InitGlobalHub(ctx *svc.ServiceContext) *Hub {
	globalHubOnce.Do(func() {
		globalHubMu.Lock()
		defer globalHubMu.Unlock()
		globalHub = NewHub(ctx)
		go globalHub.Run()
	})
	return globalHub
}

func getMutex(userID int) *sync.Mutex {
	if mu, ok := mutexMap.Load(userID); ok {
		return mu.(*sync.Mutex)
	}
	mu := &sync.Mutex{}
	mutexMap.Store(userID, mu)
	return mu
}

type Hub struct {
	serverCtx           *svc.ServiceContext
	clients             *gws.ConcurrentMap[string, *gws.Conn]
	broadcast           chan []byte
	handlers            map[string]func(*gws.Conn, *WsMessage)
	clientCount         atomic.Int32
	broadcastBufferSize int
}

func NewHub(ctx *svc.ServiceContext) *Hub {
	return NewHubWithBufferSize(ctx, DefaultBroadcastBufferSize)
}

func NewHubWithBufferSize(ctx *svc.ServiceContext, bufferSize int) *Hub {
	hub := &Hub{
		serverCtx:           ctx,
		broadcast:           make(chan []byte, bufferSize),
		clients:             gws.NewConcurrentMap[string, *gws.Conn](16),
		handlers:            make(map[string]func(*gws.Conn, *WsMessage)), // Khởi tạo handlers
		broadcastBufferSize: bufferSize,
	}

	//hub.handlers["deal.stage_update"] = deal.UpdateStageHandler(hub, ctx)
	//hub.handlers["simple"] = deal.SimpleHandler(hub, ctx)
	hub.handlers["auth"] = AuthHandler(hub, ctx)
	hub.handlers["join"] = JoinRoomHandler(hub, ctx)
	hub.handlers["leave"] = LeaveRoomHandler(hub, ctx)
	hub.handlers["on"] = OnHandler(hub, ctx)
	hub.handlers["off"] = OffHandler(hub, ctx)

	return hub
}

func (h *Hub) Clients() *gws.ConcurrentMap[string, *gws.Conn] {
	return h.clients
}

func (h *Hub) BroadcastBufferSize() int {
	return h.broadcastBufferSize
}

func (h *Hub) OnOpen(socket *gws.Conn) {
	//Lấy UserID của socket, UserID này đã được store vào session storage ở bước upgrade từ http
	key := GetKey(socket)

	err := socket.SetDeadline(time.Now().Add(PingInterval + HeartbeatWaitTimeout))
	if err != nil {
		logx.Errorf("[WebSocket] Cant reset deadline %s", err.Error())
	}

	h.clients.Store(key, socket)
}

func (h *Hub) OnClose(socket *gws.Conn, err error) {
	key := GetKey(socket)
	userID := strconv.Itoa(GetUserID(socket))
	_, exist := h.clients.Load(key)
	if exist {
		h.clients.Delete(key)
	}

	UnsubscribeAll(NewSubscriber(socket))
	userConnCount := CountSubscriberByTopic(userID)

	var closer, _ = socket.Session().Load("closer")
	closer.(chan struct{}) <- struct{}{}

	logx.Infof("[WebSocket] User %d còn %d kết nối, nguyên nhân: %v", userID, userConnCount, err)
}

func (h *Hub) OnPing(socket *gws.Conn, payload []byte) {
	err := socket.SetDeadline(time.Now().Add(PingInterval + HeartbeatWaitTimeout))
	if err != nil {
		logx.Errorf("[WebSocket] Cant reset deadline %s", err.Error())
	}
	_ = socket.WriteString("pong")
}

func (h *Hub) OnPong(socket *gws.Conn, payload []byte) {
	err := socket.SetDeadline(time.Now().Add(PingInterval + HeartbeatWaitTimeout))
	if err != nil {
		logx.Errorf("[WebSocket] Cant reset deadline %s", err.Error())
	}
}

func (h *Hub) OnMessage(socket *gws.Conn, message *gws.Message) {
	defer message.Close()

	// Chrome websocket does not support the ping method, so simulate ping in the text frame
	if b := message.Data.Bytes(); len(b) == 4 && string(b) == "ping" {
		h.OnPing(socket, nil)
		return
	} else if b := message.Data.Bytes(); len(b) == 4 && string(b) == "pong" {
		h.OnPong(socket, nil)
		return
	}

	var msg = &WsMessage{}
	_ = json.Unmarshal(message.Data.Bytes(), msg)

	logx.Info("[WebSocket] Message: %s", msg)

	handler, ok := h.handlers[msg.Event]
	if ok {
		handler(socket, msg)
	} else {
		//Ở đây ý tưởng là các message nếu ko phải các system event như join leave broadcast hay on thì sẽ là các
		//custom event của client và sẽ được process bởi EmitHandler (handler này forward message đến các client khác
		//tuỳ thuộc vào room và chỉ định broadcast của message.
		handler = EmitHandler(h, h.serverCtx)
		handler(socket, msg)
		//logx.Errorf("WebSocket handler not found: %s", msg.Event)
	}
}

func (h *Hub) Run() {
	ticker := time.NewTicker(HeartbeatInterval)
	defer ticker.Stop()

	// Create a worker pool for parallel broadcast processing
	const numWorkers = 4
	messageQueue := make(chan []byte, h.broadcastBufferSize)

	// Start worker goroutines for parallel message processing
	for i := 0; i < numWorkers; i++ {
		go func() {
			for message := range messageQueue {
				Broadcast(h.clients, gws.OpcodeText, message)
			}
		}()
	}

	for {
		select {
		case message := <-h.broadcast:
			// Non-blocking send to worker queue
			select {
			case messageQueue <- message:
			default:
				// Queue is full, log and drop message to prevent blocking
				logx.Errorf("[WebSocket] Message queue full, dropping message")
			}
		case <-ticker.C:
			// Heartbeat messages are processed directly to ensure they're not dropped
			Broadcast(h.clients, gws.OpcodeText, []byte("ping"))
		}
	}
}

func (h *Hub) Send(socket *gws.Conn, payload []byte) {
	var channel, _ = socket.Session().Load("channel")
	select {
	case channel.(chan []byte) <- payload:
	default:
		return
	}
}

func (h *Hub) SendToUser(client *gws.Conn, payload []byte) {
	_ = client.WriteMessage(gws.OpcodeText, payload)
}

func (h *Hub) Broadcast(opcode gws.Opcode, payload []byte, event string, filter func(string) bool) {
	var b = NewEventBroadcaster(event, payload)
	defer func() {
		b.Broadcaster.Close()
		b = nil
	}()

	h.clients.Range(func(key string, socket *gws.Conn) bool {
		sub := NewSubscriber(socket)
		if sub.HandledEvent(event) {
			if filter == nil || filter(sub.GetSubscriberID()) {
				_, loaded := b.Sent.LoadOrStore(sub.GetSubscriberID(), struct{}{})
				if !loaded {
					_ = b.Broadcaster.Broadcast(socket)
				}
			}
		}
		return true
	})
}

func Broadcast(clients *gws.ConcurrentMap[string, *gws.Conn], opcode gws.Opcode, payload []byte) {
	var b = gws.NewBroadcaster(opcode, payload)
	defer b.Close()

	clients.Range(func(key string, socket *gws.Conn) bool {
		_ = b.Broadcast(socket)
		return true
	})
}
