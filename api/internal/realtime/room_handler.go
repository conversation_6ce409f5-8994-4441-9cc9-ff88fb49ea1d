package realtime

import (
	"bcare/api/internal/svc"
	"bcare/common/websocket"
	"encoding/json"
	"fmt"
	"github.com/lxzan/gws"
)

func JoinRoomHandler(hub websocket.WsHub, svcCtx *svc.ServiceContext) func(client *gws.Conn, msg *WsMessage) {
	return func(client *gws.Conn, msg *WsMessage) {

		var err error
		room, ok := msg.Data.(string)
		if !ok {
			err = fmt.Errorf("invalid room format: expected string, got %T", msg.Data)
		} else if room == "" {
			err = fmt.Errorf("room name is not valid")
		} else {
			RoomsMap.Store(room, struct{}{})
			Subscribe(NewSubscriber(client), room)
		}

		// Kiểm tra sự tồn tại của phần tử
		//_, exists := RoomsMap.Load(room)
		//if exists {
		//	fmt.Println(room + " exists in RoomsMap")
		//}

		resp := &WsResponseData{
			Code:    0,
			Message: "join room successfully",
			Data:    room,
		}
		if err != nil {
			resp.Message = err.Error()
			resp.Code = 1001
		}

		msg.Data = resp

		payload, _ := json.Marshal(msg)
		hub.SendToUser(client, payload)
	}
}

func LeaveRoomHandler(hub websocket.WsHub, svcCtx *svc.ServiceContext) func(client *gws.Conn, msg *WsMessage) {
	return func(client *gws.Conn, msg *WsMessage) {
		var err error
		room, ok := msg.Data.(string)
		if !ok {
			err = fmt.Errorf("invalid room format: expected string, got %T", msg.Data)
		} else if room == "" {
			err = fmt.Errorf("room name is not valid")
		} else {
			// Unsubscribe the client from the room
			Unsubscribe(NewSubscriber(client), room)

			// Only delete room from tracking map if no subscribers remain
			if CountSubscriberByTopic(room) == 0 {
				RoomsMap.Delete(room)
			}
		}

		resp := &WsResponseData{
			Code:    0,
			Message: "leave room successfully",
			Data:    room,
		}
		if err != nil {
			resp.Message = err.Error()
			resp.Code = 1002
		}

		msg.Data = resp

		payload, _ := json.Marshal(msg)
		hub.SendToUser(client, payload)
	}
}
