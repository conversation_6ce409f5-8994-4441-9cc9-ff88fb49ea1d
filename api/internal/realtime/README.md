# BCare Realtime Package Documentation

The `api/internal/realtime` package provides a comprehensive WebSocket-based real-time communication system for the BCare application. It implements a hub-and-spoke architecture with room-based messaging, authentication, and publish-subscribe patterns.

## Quick Start

### Server Integration

```go
// Bootstrap in main.go
ctx := svc.NewServiceContext(cfg)
realtime.WebsocketBootstrap(ctx, server) // Adds /ws endpoint
```

### Send Messages from Business Logic

```go
// Send to specific user
realtime.SendToUser(123, "notification", map[string]any{
    "message": "New appointment scheduled",
    "type": "info",
})

// Broadcast to room/topic
realtime.BroadcastToTopic("room_456", "deal_updated", dealData)

// Broadcast to all clients
realtime.BroadcastToAll("system_announcement", data)
```

### Client-Side Usage

```javascript
const ws = new WebSocket('ws://localhost:8888/ws');

// Authenticate
ws.onopen = () => {
    ws.send(JSON.stringify({
        event: 'auth',
        data: 'your-jwt-token'
    }));
};

// Join room
ws.send(JSON.stringify({
    event: 'join',
    data: 'room_123'
}));

// Subscribe to events
ws.send(JSON.stringify({
    event: 'on',
    data: 'deal_updated'
}));

// Handle messages
ws.onmessage = (event) => {
    const message = JSON.parse(event.data);
    console.log(message.event, message.data);
};
```

## Core Components

### Hub (hub.go)
Central communication hub managing all WebSocket connections.

**Key Features:**
- Global singleton pattern via `GetGlobalHub()`
- Connection management with unique keys
- Message routing to handlers
- Broadcast system with filtering
- Heartbeat monitoring
- 4-worker parallel processing

**Main Methods:**
- `Broadcast(opcode, payload, event, filter)` - Filtered broadcasting
- `SendToUser(client, payload)` - Direct user messaging

### Authentication (auth_handler.go)
JWT-based authentication system.

**Flow:**
1. Client sends JWT via `auth` event
2. Server validates token and extracts user ID
3. Auto-subscribes to user's personal topic
4. Returns confirmation with connection key

### Room Management (room_handler.go)
Dynamic room creation and subscription management.

**Events:**
- `join` - Subscribe to room topic
- `leave` - Unsubscribe from room topic
- Automatic cleanup when empty

### Message Protocol (packet.go)

```go
type WsMessage struct {
    Event    string         `json:"event"`
    Data     any            `json:"data"`
    Metadata map[string]any `json:"metadata"`
    Id       string         `json:"id"`
}
```

## Helper Functions

### Broadcasting Functions
- `SendToUser(userID, event, data)` - Send to specific user
- `SendToUsers(userIDs, event, data)` - Send to multiple users  
- `BroadcastToTopic(topic, event, data)` - Broadcast to topic
- `BroadcastToAll(event, data)` - Broadcast to all clients
- `BroadcastToAllExcept(event, data, excludeUserIDs)` - Broadcast with exclusions

### Connection Utilities
- `GetUserID(socket)` - Extract user ID from session
- `GetKey(socket)` - Get connection unique key
- `GetUserIDString(socket)` - Get user ID as string

## Message Types & Events

### System Events
- `auth` - Client authentication
- `join` - Join room/topic
- `leave` - Leave room/topic
- `on` - Subscribe to event type
- `off` - Unsubscribe from event type
- `ping`/`pong` - Heartbeat messages

### Custom Events
- `notification` - User notifications
- `deal_updated` - Deal changes
- `task_assigned` - Task assignments
- Any custom event types defined by application

## Advanced Usage

### Business Logic Integration

```go
// Notify deal updates to relevant users
func NotifyDealUpdate(dealID int, userIDs []int) {
    dealTopic := fmt.Sprintf("deal_%d", dealID)
    realtime.BroadcastToTopic(dealTopic, "deal_updated", map[string]any{
        "deal_id": dealID,
        "timestamp": time.Now(),
    })
}

// Send task notifications
func NotifyTaskAssignment(userID int, taskData any) {
    realtime.SendToUser(userID, "task_assigned", taskData)
}
```

### Room-Based Communication

```javascript
// Join specific deal room
ws.send(JSON.stringify({
    event: 'join',
    data: 'deal_123'
}));

// Send message to room members
ws.send(JSON.stringify({
    event: 'chat_message',
    data: { message: 'Hello team!' },
    metadata: {
        rooms: ['deal_123'],
        broadcast: true
    }
}));
```

## Architecture Features

### Performance
- Parallel message processing (4 workers)
- Connection pooling with concurrent maps
- Message deduplication
- Non-blocking message queues
- Configurable buffer sizes

### Security
- JWT authentication with token validation
- Session management
- Authorization hooks
- Connection limits

### Reliability
- Heartbeat monitoring (10s ping, 20s heartbeat, 50s timeout)
- Graceful connection cleanup
- Error recovery mechanisms
- Automatic resource management

## Configuration

```go
const (
    PingInterval               = 10 * time.Second
    HeartbeatInterval          = 20 * time.Second  
    HeartbeatWaitTimeout       = 50 * time.Second
    NotificationTopic          = "notification"
    DefaultBroadcastBufferSize = 1000
)
```

## File Structure

- `hub.go` - Main hub implementation
- `auth_handler.go` - Authentication logic
- `room_handler.go` - Room management
- `emit_handler.go` - Message broadcasting
- `on_handler.go` - Event subscriptions
- `leave_handler.go` - Leave operations
- `packet.go` - Message protocols
- `pubsub.go` - Publish-subscribe system
- `upgrader.go` - WebSocket upgrade handling
- `helper.go` - Utility functions
- `utils.go` - Additional utilities

## Integration Points

The realtime package integrates with:
- `svc.ServiceContext` for dependency injection
- JWT authentication system
- Business logic layers for event broadcasting
- HTTP server for WebSocket endpoint exposure

This package provides a production-ready solution for real-time features like live notifications, collaborative editing, chat systems, and real-time data updates.