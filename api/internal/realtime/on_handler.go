package realtime

import (
	"bcare/api/internal/svc"
	"bcare/common/websocket"
	"encoding/json"
	"fmt"
	"github.com/lxzan/gws"
)

func OnHandler(hub websocket.WsHub, svcCtx *svc.ServiceContext) func(client *gws.Conn, msg *WsMessage) {
	return func(client *gws.Conn, msg *WsMessage) {
		var err error
		//Event that client want to handle
		var event = msg.Data.(string)
		if event == "" {
			err = fmt.Errorf("event name is not valid")
		} else {
			Subscribe(NewSubscriber(client), event)
		}

		resp := &WsResponseData{
			Code:    0,
			Message: "event handler registered successfully",
			Data:    event,
		}
		if err != nil {
			resp.Message = err.Error()
			resp.Code = 1001
		}

		msg.Data = resp

		payload, _ := json.Marshal(msg)
		hub.SendToUser(client, payload)
	}
}

func OffHandler(hub websocket.WsHub, svcCtx *svc.ServiceContext) func(client *gws.Conn, msg *WsMessage) {
	return func(client *gws.Conn, msg *WsMessage) {
		var err error
		//Event that client want to handle
		var event = msg.Data.(string)
		if event == "" {
			err = fmt.Errorf("event name is not valid")
		} else {
			Unsubscribe(NewSubscriber(client), event)
		}

		resp := &WsResponseData{
			Code:    0,
			Message: "event handler unregistered successfully",
			Data:    event,
		}
		if err != nil {
			resp.Message = err.Error()
			resp.Code = 1001
		}

		msg.Data = resp

		payload, _ := json.Marshal(msg)
		hub.SendToUser(client, payload)
	}
}
