package realtime

import (
	"github.com/lxzan/gws"
	"strconv"
	"sync"
)

var RoomsMap sync.Map

func GetUserIDString(socket *gws.Conn) string {
	return strconv.Itoa(GetUserID(socket))
}

func GetUserID(socket *gws.Conn) int {
	userID, ok := socket.Session().Load("user_id")
	if !ok {
		return -1
	}
	return userID.(int)
}

func GetKey(socket *gws.Conn) string {
	key, ok := socket.Session().Load("key")
	if !ok {
		return ""
	}
	return key.(string)
}
