package realtime

import (
	"bcare/api/internal/svc"
	"bcare/common/cast"
	"bcare/common/websocket"
	"encoding/json"
	"github.com/lxzan/gws"
	"github.com/zeromicro/go-zero/core/logx"
)

func EmitHandler(hub websocket.WsHub, svcCtx *svc.ServiceContext) func(client *gws.Conn, msg *WsMessage) {
	return func(client *gws.Conn, msg *WsMessage) {
		event := msg.Event
		data := msg.Data
		hasMetadata := len(msg.Metadata) > 0
		sub := NewSubscriber(client)

		msgToSend, err := prepareMessage(client, event, data, msg.Metadata, hasMetadata)
		if err != nil {
			logx.Errorf("marshal error: %v", err)
			return
		}

		isBroadcast := false
		if hasMetadata {
			if b, ok := msg.Metadata["broadcast"].(bool); ok {
				isBroadcast = b
			}

			if msg.Metadata["rooms"] != nil {
				rooms := cast.Anyss(msg.Metadata["rooms"])
				if len(rooms) > 0 {
					PublishMany(rooms, msgToSend, event, isBroadcast, sub.GetSubscriberID())
					return
				}
			}

			if msg.Metadata["except"] != nil {
				rooms := cast.Anyss(msg.Metadata["except"])
				if len(rooms) > 0 {
					publishExcept(rooms, msgToSend, event, isBroadcast, sub.GetSubscriberID())
					return
				}
			}
		}

		if isBroadcast {
			// Broadcast to all clients except the sender
			hub.Broadcast(gws.OpcodeText, msgToSend, event, func(subscriberID string) bool {
				return subscriberID != sub.GetSubscriberID()
			})
		} else {
			// Broadcast to all clients including the sender
			hub.Broadcast(gws.OpcodeText, msgToSend, event, nil)
		}
	}
}

func prepareMessage(client *gws.Conn, event string, data any, metadata map[string]any, hasMetadata bool) ([]byte, error) {
	isBroadcast := false
	if hasMetadata {
		if b, ok := metadata["broadcast"]; ok {
			if boolVal, ok := b.(bool); ok {
				isBroadcast = boolVal
			}
		}
	}

	return json.Marshal(WsMessage{
		Event: event,
		Data:  data,
		Metadata: map[string]any{
			"from":      GetUserIDString(client),
			"key":       GetKey(client),
			"broadcast": isBroadcast,
		},
	})
}

func publishExcept(except []string, msgToSend []byte, event string, excludeSender bool, senderID string) {
	RoomsMap.Range(func(key any, val any) bool {
		room, ok := key.(string)
		if !ok {
			logx.Errorf("Invalid room key type: expected string, got %T", key)
			return true
		}
		if !isExcept(room, except) {
			Publish(room, msgToSend, event, excludeSender, senderID)
		}
		return true
	})
}

func isExcept(room string, except []string) bool {
	for _, ex := range except {
		if ex == room {
			return true
		}
	}
	return false
}
