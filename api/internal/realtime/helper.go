package realtime

import (
	"encoding/json"
	"github.com/lxzan/gws"
	"strconv"
)

// Gửi message đến user cụ thể
func SendToUser(userID int, event string, data any) error {
	msg := WsMessage{Event: event, Data: data}
	payload, _ := json.Marshal(msg)
	topic := strconv.Itoa(userID)
	Publish(topic, payload, event, false, "")
	return nil
}

// Broadcast message đến một topic
func BroadcastToTopic(topic string, event string, data any) error {
	msg := WsMessage{Event: event, Data: data}
	payload, _ := json.Marshal(msg)
	Publish(topic, payload, event, false, "")
	return nil
}

// Gửi message đến nhiều users
func SendToUsers(userIDs []int, event string, data any) error {
	msg := WsMessage{Event: event, Data: data}
	payload, _ := json.Marshal(msg)
	for _, userID := range userIDs {
		topic := strconv.Itoa(userID)
		Publish(topic, payload, event, false, "")
	}
	return nil
}

// BroadcastToAll gửi message tới toàn bộ client đang kết nối
func BroadcastToAll(event string, data any) error {
	hub := GetGlobalHub()
	if hub == nil {
		return nil
	}

	msg := WsMessage{Event: event, Data: data}
	payload, _ := json.Marshal(msg)

	// Sử dụng hub để broadcast tới tất cả client
	hub.Broadcast(gws.OpcodeText, payload, event, nil)
	return nil
}

// BroadcastToAllExcept gửi message tới toàn bộ client ngoại trừ các userID được chỉ định
func BroadcastToAllExcept(event string, data any, excludeUserIDs []int) error {
	hub := GetGlobalHub()
	if hub == nil {
		return nil
	}

	msg := WsMessage{Event: event, Data: data}
	payload, _ := json.Marshal(msg)

	// Tạo map chứa các userID bị loại trừ để kiểm tra nhanh hơn
	excludeMap := make(map[string]struct{})
	for _, id := range excludeUserIDs {
		excludeMap[strconv.Itoa(id)] = struct{}{}
	}

	// Filter function để loại trừ các userID
	filter := func(subscriberID string) bool {
		_, excluded := excludeMap[subscriberID]
		return !excluded
	}

	// Broadcast với filter
	hub.Broadcast(gws.OpcodeText, payload, event, filter)
	return nil
}
