package realtime

import (
	"bcare/common/butils"
	"fmt"
	"github.com/lxzan/event_emitter"
	"github.com/lxzan/gws"
	"sync"
)

var EM = event_emitter.New[string, *Subscriber](&event_emitter.Config{
	BucketNum:  16,
	BucketSize: 128,
})

//TODO refactor lai 1 chut su dung 1 topic all cho broadcast?

type Subscriber gws.Conn

func NewSubscriber(conn *gws.Conn) *Subscriber { return (*Subscriber)(conn) }

func (c *Subscriber) GetSubscriberID() string {
	socketKey, _ := c.GetMetadata().Load("key")
	return socketKey.(string)
}

func (c *Subscriber) GetMetadata() event_emitter.Metadata { return c.Conn().Session() }

func (c *Subscriber) Conn() *gws.Conn { return (*gws.Conn)(c) }

func (c *Subscriber) HandledEvent(event string) bool {
	for _, topic := range EM.GetTopicsBySubscriber(c) {
		if topic == event {
			return true
		}
	}
	return false
}

func Unsubscribe(s *Subscriber, topic string) {
	EM.UnSubscribe(s, topic)
}

func UnsubscribeAll(s *Subscriber) {
	EM.UnSubscribeAll(s)
}

func Subscribe(s *Subscriber, topic string) {
	EM.Subscribe(s, topic, func(broadcaster any) {
		switch b := broadcaster.(type) {
		case *EventBroadcaster:
			//Kiểm tra handle event để tăng performance
			if s.HandledEvent(b.Event) {
				//Dedup
				_, loaded := b.Sent.LoadOrStore(s.GetSubscriberID(), struct{}{})
				if !loaded {
					if b.ExcludeSender && b.SenderID == s.GetSubscriberID() {
						// Không gửi lại cho người gửi nếu ExcludeSender là true
						return
					}
					_ = b.Broadcaster.Broadcast(s.Conn())
				}
			}
		case *gws.Broadcaster:
			_ = b.Broadcast(s.Conn())
		}
	})

	// Broadcast join room event
	joinMsg := NewEventBroadcaster("user_joined_room", []byte(fmt.Sprintf(`{"user_id": "%s", "room": "%s"}`, s.GetSubscriberID(), topic)))
	joinMsg.ExcludeSender = true
	joinMsg.SenderID = s.GetSubscriberID()
	EM.Publish(topic, joinMsg)
}

type EventBroadcaster struct {
	uid           string
	Event         string
	Broadcaster   *gws.Broadcaster
	Sent          sync.Map
	ExcludeSender bool
	SenderID      string
}

func NewEventBroadcaster(event string, msg []byte) *EventBroadcaster {
	return &EventBroadcaster{
		uid:           butils.NewID(),
		Event:         event,
		Broadcaster:   gws.NewBroadcaster(gws.OpcodeText, msg),
		ExcludeSender: false,
		SenderID:      "",
	}
}

// Publish Them event vao de tang performance, chi broadcast cho client handle event do
func Publish(topic string, msg []byte, event string, excludeSender bool, senderID string) {
	var broadcaster = NewEventBroadcaster(event, msg)
	broadcaster.ExcludeSender = excludeSender
	broadcaster.SenderID = senderID
	defer func() {
		broadcaster.Broadcaster.Close()
		broadcaster = nil
	}()

	EM.Publish(topic, broadcaster)
}

func PublishMany(topics []string, msg []byte, event string, excludeSender bool, senderID string) {
	var broadcaster = NewEventBroadcaster(event, msg)
	broadcaster.ExcludeSender = excludeSender
	broadcaster.SenderID = senderID
	defer func() {
		broadcaster.Broadcaster.Close()
		broadcaster = nil
	}()

	for _, topic := range topics {
		EM.Publish(topic, broadcaster)
	}
}

func CountSubscriberByTopic(topic string) int {
	return EM.CountSubscriberByTopic(topic)
}

func HasTopic(topic string) bool {
	//TODO test lai ham nay

	return EM.CountSubscriberByTopic(topic) > 0
}
