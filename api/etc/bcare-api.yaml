Name: bcare-api
Host: localhost
Port: ${APP_PORT}
Env: dev
Timeout: 30000
MaxBytes: 20971520 # 8MB
Middlewares:
  Log: true

Auth:
  IdentityKey: id
  AccessSecret: ad879037-c7a4-4063-9236-6bfc35d54b7d
  AccessExpire: 259200
  RefreshExpire: 2592000
  OTP:
    Enabled: false        # Bật/tắt tính năng OTP
    Length: 6           # Độ dài mã OTP
    ExpireMinutes: 30   # Thời gian hết hạn của OTP (phút)
    Receivers:
      Type: "multiple"     # "single" hoặc "multiple"
      SingleEmail: "<EMAIL>"  # Dùng khi Type = "single"
      EmailList: # Dùng khi Type = "multiple"
        - "<EMAIL>"
Database:
  RedisPass: ${DB_PASS}
  DataSource: postgres://${DB_USER}:${DB_PASS}@127.0.0.1:5432/${DB_NAME}?sslmode=disable&TimeZone=Asia/Ho_Chi_Minh&search_path=core
  DbUser: ${DB_USER}
  DbName: ${DB_NAME}
  DbPass: ${DB_PASS}
General:
  DateFormat: "02/01/2006"
  DateTimeFormat: "15:04 02/01/2006"
  FullDateTimeFormat: "15:04:23 02/01/2006"
  SqlDateFormat: "%d-%m-%Y"
  CORS:
    - http://localhost:7777
    - https://updental.blazy.vn
    - https://crm.sandental.vn
App:
  DiscountDistributionStrategy: "largest" #phân bổ discount amount cho attachment có value lớn nhất trước
Log:
  ServiceName: bcare-api
  Mode: console
  Encoding: plain
  TimeFormat: "02-01-2006 15:04:05"
  Level: error
  Stat: true
  StackCooldownMillis: 100
Storage:
  Local:
    Path: ./storage
    Prefix: /storage
SyncGetFly: false
Debug: false
PhoneTest: "0708276861"
ServicesAddr:
  Bmind: http://localhost:50051
ApiKey:
  OpenAI: ${OPENAI_API_KEY}
  Anthropic: ${ANTHROPIC_API_KEY}