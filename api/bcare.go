//go:build !no_k8s

package main

import (
	"bcare/api/internal/config"
	"bcare/api/internal/eventhandler"
	"bcare/api/internal/handler"
	"bcare/api/internal/realtime"
	"bcare/api/internal/svc"
	"bcare/api/internal/task"
	"bcare/common/berr"
	_ "bcare/ent/runtime"
	"flag"
	"fmt"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/rest"
	"golang.org/x/sync/errgroup"
)

var configFile = flag.String("f", "etc/bcare-api.yaml", "the config file")
var rawQueryConfigFile = flag.String("raw_query", "etc/raw_query_policy.yaml", "the raw query policy file")

func main() {
	flag.Parse()

	// Load config
	config.Init(*configFile)
	//config.LoadAdditional(*rawQueryConfigFile)
	cfg := config.Get()

	logx.MustSetup(cfg.Log)
	//logx.CollectSysLog()
	//berr.CollectConsole(cfg.Log.Path)

	// Initialize Redis clients cho task
	redisAddr := "127.0.0.1:6379"
	queueName := cfg.Name // Use service name as queue name
	task.InitializeTaskClients(redisAddr, cfg.Database.RedisPass, queueName)
	task.InitializeTaskServer(redisAddr, cfg.Database.RedisPass, queueName)

	ctx := svc.NewServiceContext(cfg)

	// *** Đăng ký TẤT CẢ Hook một lần duy nhất ***
	eventhandler.RegisterAllHooks(svc.EB, ctx.Ent, ctx) // Đăng ký với cùng Event Bus

	server := rest.MustNewServer(
		cfg.RestConf,
		rest.WithCors(cfg.General.CORS...),
		rest.WithNotFoundHandler(handler.CustomHandler(ctx)),
	)

	defer server.Stop()

	// WebSocket
	realtime.WebsocketBootstrap(ctx, server)

	// API Handlers
	handler.RegisterHandlers(server, ctx)

	// Initialize errgroup
	var g errgroup.Group

	// Start task server first and wait for it to be ready
	g.Go(berr.SafeGo(func() { task.RunServer(ctx) }))
	g.Go(berr.SafeGo(func() { task.RunSchedule(ctx) }))

	// Wait for task server to be ready before starting API server
	task.WaitForServerReady()

	// Handle errors
	go func() {
		if err := g.Wait(); err != nil {
			logx.Errorf("background tasks error: %v", err)
		}
	}()

	fmt.Printf("Starting server at %s:%d...\n", cfg.Host, cfg.Port)

	server.Start()
}
