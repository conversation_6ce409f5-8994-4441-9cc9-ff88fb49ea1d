{"output": {"filePath": "repomix-output.md", "style": "markdown", "parsableStyle": false, "fileSummary": true, "directoryStructure": true, "removeComments": false, "removeEmptyLines": false, "compress": false, "topFilesLength": 5, "showLineNumbers": false, "copyToClipboard": false, "git": {"sortByChanges": true, "sortByChangesMaxCommits": 100}}, "include": ["api/internal/model", "api/internal/logic/tag", "common/bresult", "common/berr", "common/cast", "common/ctxdata", "api/internal/logic/task", "api/internal/logic/bill", "api/internal/logic/person", "api/internal/logic/product", "api/internal/logic/deal", "api/internal/logic/attachment", "api/internal/model"], "ignore": {"useGitignore": true, "useDefaultPatterns": true, "customPatterns": []}, "security": {"enableSecurityCheck": true}, "tokenCount": {"encoding": "o200k_base"}}