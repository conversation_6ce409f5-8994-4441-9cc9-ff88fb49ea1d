This file is a merged representation of the entire codebase, combined into a single document by Repomix.

<file_summary>
This section contains a summary of this file.

<purpose>
This file contains a packed representation of the entire repository's contents.
It is designed to be easily consumable by AI systems for analysis, code review,
or other automated processes.
</purpose>

<file_format>
The content is organized as follows:
1. This summary section
2. Repository information
3. Directory structure
4. Repository files, each consisting of:
  - File path as an attribute
  - Full contents of the file
</file_format>

<usage_guidelines>
- This file should be treated as read-only. Any changes should be made to the
  original repository files, not this packed version.
- When processing this file, use the file path to distinguish
  between different files in the repository.
- Be aware that this file may contain sensitive information. Handle it with
  the same level of security as you would the original repository.
</usage_guidelines>

<notes>
- Some files may have been excluded based on .gitignore rules and Repomix's configuration
- Binary files are not included in this packed representation. Please refer to the Repository Structure section for a complete list of file paths, including binary files
- Files matching patterns in .gitignore are excluded
- Files matching default ignore patterns are excluded
- Files are sorted by Git change count (files with more changes are at the bottom)
</notes>

<additional_info>

</additional_info>

</file_summary>

<directory_structure>
add_logic.go
checkout_logic.go
delete_logic.go
get_logic.go
list_big_logic.go
list_logic.go
update_logic.go
</directory_structure>

<files>
This section contains the contents of the repository's files.

<file path="add_logic.go">
package deal

import (
	"bcare/api/internal/dto"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/berr"
	"bcare/common/cast"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type AddLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAddLogic(ctx context.Context, svcCtx *svc.ServiceContext) AddLogic {
	return AddLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AddLogic) Add(req *types.DealAddRequest) (resp *types.DealResponse, err error) {
	if !l.svcCtx.Auth.Deal.CanAdd(l.ctx, nil) {
		return nil, berr.ErrPermissionDenied.Wrap(nil)
	}

	input := new(dto.CreateDealInput)
	err = cast.ReqToModelInput(input, req)
	if err != nil {
		return nil, berr.ErrRequestParam.Wrap(err)
	}

	dealOutput, err := l.svcCtx.DealModel.CreateDeal(l.ctx, input)
	if err != nil {
		return nil, err
	}

	resp = new(types.DealResponse)
	err = cast.ConvertViaJson(resp, dealOutput)
	if err != nil {
		return nil, berr.ErrServerCommon.Wrap(err)
	}

	return resp, nil
}
</file>

<file path="checkout_logic.go">
package deal

import (
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/berr"
	"bcare/common/cast"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type CheckoutLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewCheckoutLogic(ctx context.Context, svcCtx *svc.ServiceContext) CheckoutLogic {
	return CheckoutLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *CheckoutLogic) Checkout(req *types.DealCheckoutRequest) (resp *types.DealResponse, err error) {
	if !l.svcCtx.Auth.Deal.CanCheckout(l.ctx, nil) {
		return nil, berr.ErrPermissionDenied.Wrap(nil)
	}

	dealOutput, err := l.svcCtx.DealModel.CheckoutDeal(l.ctx, req.Id)
	if err != nil {
		return nil, err
	}

	resp = new(types.DealResponse)
	err = cast.ConvertViaJson(resp, dealOutput)
	if err != nil {
		return nil, berr.ErrServerCommon.Wrap(err)
	}

	return resp, nil
}
</file>

<file path="delete_logic.go">
package deal

import (
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/berr"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewDeleteLogic(ctx context.Context, svcCtx *svc.ServiceContext) DeleteLogic {
	return DeleteLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DeleteLogic) Delete(req *types.DealDeleteRequest) (resp *types.DealResponse, err error) {
	if !l.svcCtx.Auth.Deal.CanDelete(l.ctx, nil) {
		return nil, berr.ErrPermissionDenied.Wrap(nil)
	}

	err = l.svcCtx.DealModel.DeleteDeal(l.ctx, req.Id)
	if err != nil {
		return nil, err
	}

	return resp, nil
}
</file>

<file path="get_logic.go">
package deal

import (
	"bcare/api/internal/dto"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/berr"
	"bcare/common/cast"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetLogic(ctx context.Context, svcCtx *svc.ServiceContext) GetLogic {
	return GetLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// Get retrieves a single deal with optional related data.
func (l *GetLogic) Get(req *types.DealGetRequest) (*types.DealResponse, error) {
	input := &dto.GetDealInput{
		ID:              req.Id,
		IncludeRelation: req.IncludeRelation,
	}

	dealOutput, err := l.svcCtx.DealModel.GetDealWithFullRelations(l.ctx, input)
	if err != nil {
		return nil, err
	}

	if req.IncludeRelation {
		err = l.enrichDealWithRelations(dealOutput)
		if err != nil {
			return nil, err
		}
	}

	resp := new(types.DealResponse)
	err = cast.ConvertViaJson(resp, dealOutput)
	if err != nil {
		return nil, berr.ErrServerCommon.Wrap(err)
	}

	return resp, nil
}

func (l *GetLogic) enrichDealWithRelations(dealOutput *dto.DealOutput) error {
	dealIDs := []int{dealOutput.ID}

	discountUsages, discountUsagesErr := l.svcCtx.DiscountUsageModel.ListByDealIDs(l.ctx, dealIDs)

	if discountUsagesErr != nil {
		return discountUsagesErr
	}

	err := cast.ConvertViaJson(&dealOutput.DiscountUsages, discountUsages)
	if err != nil {
		return berr.ErrCopyFailed.Wrap(err)
	}

	for _, du := range discountUsages {
		dealOutput.DiscountAmount += du.Value * float64(du.UsageCount)
	}

	return nil
}
</file>

<file path="list_big_logic.go">
package deal

import (
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"context"
	"github.com/zeromicro/go-zero/core/logx"
)

type ListBigLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewListBigLogic(ctx context.Context, svcCtx *svc.ServiceContext) ListBigLogic {
	return ListBigLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ListBigLogic) ListBig(req *types.DealListRequest) (resp *types.DealListResponse, err error) {

	return resp, err
}
</file>

<file path="list_logic.go">
package deal

import (
	"bcare/api/internal/dto"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/berr"
	"bcare/common/cast"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type ListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewListLogic(ctx context.Context, svcCtx *svc.ServiceContext) ListLogic {
	return ListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ListLogic) List(req *types.DealListRequest) (*types.DealListResponse, error) {
	if !l.svcCtx.Auth.Deal.CanList(l.ctx, nil) {
		return nil, berr.ErrPermissionDenied.Wrap(nil)
	}

	input := dto.ListDealInput{
		PageSize:        req.PageSize,
		Page:            req.Page,
		OrderBy:         req.OrderBy,
		IncludeRelation: req.IncludeRelation,
	}

	// Handle filter conversion
	if req.Filter.PersonId != 0 {
		input.Filter.PersonID = &req.Filter.PersonId
	}
	if req.Filter.StageId != 0 {
		input.Filter.StageID = &req.Filter.StageId
	}
	if req.PipelineId != 0 {
		input.Filter.PipelineID = &req.PipelineId
	}
	if req.Filter.Status != 0 {
		input.Filter.Status = &req.Filter.Status
	}
	if req.Filter.State != "" {
		input.Filter.State = &req.Filter.State
	}

	// Get deals from DealModel
	listOutput, err := l.svcCtx.DealModel.ListDeals(l.ctx, input)
	if err != nil {
		return nil, err
	}

	// If no relations needed or no deals, return early
	if !req.IncludeRelation || len(listOutput.Deals) == 0 {
		resp := &types.DealListResponse{
			Deals: make([]types.DealResponse, 0, len(listOutput.Deals)),
			Total: listOutput.Total,
		}

		err = cast.ConvertViaJson(&resp.Deals, listOutput.Deals)
		if err != nil {
			return nil, berr.ErrServerCommon.Wrap(err)
		}

		return resp, nil
	}

	// Orchestrate related data fetching for enhanced deals
	enhancedDeals, err := l.enrichDealsWithRelations(listOutput.Deals)
	if err != nil {
		return nil, err
	}

	resp := &types.DealListResponse{
		Deals: make([]types.DealResponse, 0, len(enhancedDeals)),
		Total: listOutput.Total,
	}

	err = cast.ConvertViaJson(&resp.Deals, enhancedDeals)
	if err != nil {
		return nil, berr.ErrServerCommon.Wrap(err)
	}

	return resp, nil
}

// enrichDealsWithRelations orchestrates fetching of tracks and discount usages for deals
func (l *ListLogic) enrichDealsWithRelations(deals []dto.DealOutput) ([]dto.DealOutput, error) {
	if len(deals) == 0 {
		return deals, nil
	}

	dealIDs := make([]int, len(deals))
	for i, deal := range deals {
		dealIDs[i] = deal.ID
	}

	discountUsages, discountErr := l.svcCtx.DiscountUsageModel.ListByDealIDs(l.ctx, dealIDs)

	if discountErr != nil {
		return nil, berr.ErrServerCommon.Wrap(discountErr)
	}

	// Reuse existing MapDealsWithRelations for financial calculations
	err := l.svcCtx.DealModel.MapDealsWithRelations(deals, discountUsages)
	if err != nil {
		return nil, berr.ErrServerCommon.Wrap(err)
	}

	return deals, nil
}
</file>

<file path="update_logic.go">
package deal

import (
	"bcare/api/internal/dto"
	"bcare/api/internal/svc"
	"bcare/api/internal/types"
	"bcare/common/berr"
	"bcare/common/bquery"
	"bcare/common/cast"
	"bcare/ent"
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateLogic {
	return &UpdateLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateLogic) Update(req *types.DealUpdateRequest) (resp *types.DealResponse, err error) {
	if !l.svcCtx.Auth.Deal.CanUpdate(l.ctx, nil) {
		return nil, berr.ErrPermissionDenied.Wrap(nil)
	}

	input := new(dto.UpdateDealInput)
	err = cast.ReqToModelInput(input, req)
	if err != nil {
		return nil, berr.ErrRequestParam.Wrap(err)
	}

	err = bquery.WithTx(l.ctx, l.svcCtx.Ent, func(tx *ent.Tx) error {
		dealOutput, err := l.svcCtx.DealModel.UpdateDeal(l.ctx, input)
		if err != nil {
			return err
		}

		// Handle discounts only if provided
		if input.Discounts != nil {
			err = l.applyDiscountsToAttachments(tx, input.ID, input.Discounts)
			if err != nil {
				return err
			}
		}

		// Check if deal state is won or active and create bill items for attachments if needed
		if dealOutput.State == "won" || dealOutput.State == "paying" {
			err = l.createBillItemsForDeal(tx, input.ID)
			if err != nil {
				return err
			}
		}

		// Update the final response
		resp = new(types.DealResponse)
		err = cast.ConvertViaJson(resp, dealOutput)
		if err != nil {
			return berr.ErrServerCommon.Wrap(err)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return resp, nil
}

// applyDiscountsToAttachments orchestrates discount application to deal attachments
func (l *UpdateLogic) applyDiscountsToAttachments(tx *ent.Tx, dealID int, discountIDs []int) error {
	if len(discountIDs) == 0 {
		return nil
	}

	// Delete old discount usages for the deal
	err := l.svcCtx.DiscountUsageModel.DeleteByDealID(l.ctx, dealID)
	if err != nil {
		return err
	}

	// Reset all attachment discounts to 0
	err = l.svcCtx.DealModel.ResetAttachmentDiscounts(l.ctx, tx, dealID)
	if err != nil {
		return err
	}

	// Get all attachments for the deal to distribute discounts
	attachments, err := l.svcCtx.DealModel.GetAttachmentsForDeal(l.ctx, tx, dealID)
	if err != nil {
		return err
	}

	if len(attachments) == 0 {
		return nil
	}

	// Calculate total attachment amount for discount distribution
	var totalAttachmentAmount float64
	for _, att := range attachments {
		totalAttachmentAmount += att.Price * float64(att.Quantity)
	}

	// Process each discount
	attachmentDiscounts := make(map[int]float64)

	for _, discountID := range discountIDs {
		// Get discount details
		discountOutput, err := l.svcCtx.DiscountModel.GetDiscount(l.ctx, discountID)
		if err != nil {
			continue // Skip invalid discounts
		}

		// Calculate discount amount based on discount scope and type
		discountAmount := l.calculateDiscountAmount(discountOutput, totalAttachmentAmount)

		// Create discount usage for the deal
		_, err = l.svcCtx.DiscountUsageModel.CreateDiscountUsage(l.ctx, &dto.CreateDiscountUsageInput{
			DiscountID: discountID,
			DealID:     &dealID,
			PersonID:   &attachments[0].PersonID, // All attachments in a deal have same PersonID
			Value:      discountAmount,
		})
		if err != nil {
			continue // Skip if can't create usage
		}

		// Distribute discount amount to attachments
		distributedDiscounts := l.svcCtx.DealModel.DistributeDiscountToAttachments(attachments, discountAmount, totalAttachmentAmount)

		// Accumulate discounts for each attachment
		for attachmentID, amount := range distributedDiscounts {
			attachmentDiscounts[attachmentID] += amount
		}
	}

	// Apply accumulated discounts to attachments
	err = l.svcCtx.DealModel.ApplyDiscountDistribution(l.ctx, tx, attachmentDiscounts)
	if err != nil {
		return err
	}

	return nil
}

// createBillItemsForDeal orchestrates bill item creation for deal attachments
func (l *UpdateLogic) createBillItemsForDeal(tx *ent.Tx, dealID int) error {
	// Get attachments that don't have bill items yet
	attachments, err := l.svcCtx.DealModel.GetAttachmentsWithoutBillItems(l.ctx, tx, dealID)
	if err != nil {
		return err
	}

	if len(attachments) == 0 {
		return nil // No attachments to create bill items for
	}

	// Convert ent.Attachment to dto.AttachmentOutput for BillItemModel
	var attachmentOutputs []dto.AttachmentOutput
	err = cast.ConvertViaJson(&attachmentOutputs, attachments)
	if err != nil {
		return berr.ErrCopyFailed.Wrap(err)
	}

	// Create bill items for all attachments
	err = l.svcCtx.BillItemModel.CreateForAttachments(l.ctx, dealID, attachmentOutputs)
	if err != nil {
		return err
	}

	return nil
}

// calculateDiscountAmount calculates discount amount based on discount configuration
func (l *UpdateLogic) calculateDiscountAmount(discount *dto.DiscountOutput, totalAmount float64) float64 {
	switch discount.Type {
	case "percentage":
		return totalAmount * (discount.Value / 100.0)
	case "fixed":
		return discount.Value
	default:
		return 0.0
	}
}
</file>

</files>
