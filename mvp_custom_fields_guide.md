# Custom Fields MVP Implementation Guide

## Tổng quan

MVP này implement hệ thống Custom Fields cho entity `Person` sử dụng phương pháp Dynamic Schema Migration như đã định nghĩa trong tech doc.

## Các Component đã được implement

### Component A: Metadata Storage
- **Entity**: `CustomFieldDefinition` (ent/schema/custom_field_definition.go)
- **Bảng**: `custom_field_definitions`
- **Chức năng**: Lưu trữ định nghĩa các custom field

### Component B: Migration Trigger & Executor  
- **Logic**: `api/internal/logic/custom_field/migrate_logic.go`
- **Chức năng**: Thực thi ALTER TABLE để thêm cột custom vào bảng `person`

### Component C: Dynamic Data Access Layer
- **Model**: `api/internal/model/person_custom_field_model.go`
- **Chức năng**: Đ<PERSON><PERSON>/ghi dữ liệu với cả cột cố định và cột custom

## Cách Test MVP

### 1. Setup Database

Trước tiên, generate và chạy migration:

```bash
# Generate Ent entities
go generate ./ent

# Run application để tạo tables
go run api/bcare.go
```

### 2. Test Case 1: Tạo Custom Field Definition

**Step 1:** Tạo definition cho field `loyalty_points` (number)

```sql
-- Thêm vào bảng custom_field_definitions
INSERT INTO custom_field_definitions (
    tenant_id, entity_name, column_name, field_type, 
    display_name, description, is_migrated, created_at, updated_at
) VALUES (
    'tenant_A', 'persons', 'loyalty_points', 'number',
    'Loyalty Points', 'Customer loyalty points', false, NOW(), NOW()
);
```

### 3. Test Case 2: Execute Migration

**Step 2:** Gọi API migration (cần tạo handler)

```bash
# Giả sử có API endpoint
curl -X POST http://localhost:8888/internal/migrations/execute \
  -H "Content-Type: application/json" \
  -d '{
    "tenant_id": "tenant_A",
    "field_definition_id": 1
  }'
```

**Kết quả mong đợi:**
- Cột `loyalty_points INTEGER` được thêm vào bảng `person`
- Field definition được mark `is_migrated = true`

### 4. Test Case 3: Update Person với Custom Field

Sử dụng `PersonCustomFieldModel.UpdatePersonWithCustomFields()`:

```go
// Trong code hoặc test
req := &types.UpdatePersonWithCustomFieldsRequest{
    PersonID: 1,
    TenantID: "tenant_A",
    StandardData: map[string]interface{}{
        "full_name": "Nguyen Van A Updated",
    },
    CustomData: map[string]interface{}{
        "loyalty_points": 100,
    },
}

// Call update logic
updateLogic := custom_field.NewUpdatePersonLogic(ctx, svcCtx)
err := updateLogic.UpdatePersonWithCustomFields(req)
```

### 5. Test Case 4: Read Person với Custom Fields

Sử dụng `PersonCustomFieldModel.GetPersonByID()`:

```go
// Trong code hoặc test
getLogic := custom_field.NewGetPersonLogic(ctx, svcCtx)
person, err := getLogic.GetPersonWithCustomFields("tenant_A", 1)

// Kết quả mong đợi:
// person.CustomFields["loyalty_points"] = 100
```

### 6. Test Case 5: List Persons với Custom Fields

```go
persons, err := getLogic.GetPersonsWithCustomFields("tenant_A")

// Mỗi person trong list sẽ có CustomFields populated
```

### 7. Test Case 6: Multi-tenant Isolation

**Step 1:** Tạo field cho tenant_B

```sql
INSERT INTO custom_field_definitions (
    tenant_id, entity_name, column_name, field_type, 
    display_name, description, is_migrated, created_at, updated_at
) VALUES (
    'tenant_B', 'persons', 'referral_source', 'text',
    'Referral Source', 'How customer found us', false, NOW(), NOW()
);
```

**Step 2:** Execute migration cho tenant_B

**Step 3:** Verify isolation:
- tenant_A chỉ thấy `loyalty_points`
- tenant_B chỉ thấy `referral_source`

## Acceptance Criteria Checklist

✅ **Setup**: Ứng dụng chạy được
✅ **Define**: Có thể thêm field definition vào database
✅ **Migrate**: Component B có thể execute ALTER TABLE
✅ **Write**: Component C có thể update person với custom fields
✅ **Read**: Component C có thể retrieve person với custom fields  
✅ **Isolation**: Multi-tenant hoạt động đúng

## Cấu trúc Files được tạo

```
ent/schema/
├── custom_field_definition.go          # Component A

api/internal/logic/custom_field/
├── migrate_logic.go                     # Component B
├── create_definition_logic.go           # Helper logic
├── get_person_logic.go                  # Component C (Read)
└── update_person_logic.go               # Component C (Write)

api/internal/model/
└── person_custom_field_model.go         # Component C (Core)

api/internal/types/
└── custom_field.go                      # API Types
```

## Limitations và Next Steps

### MVP Limitations:
1. Chỉ hỗ trợ entity `Person`
2. Chỉ hỗ trợ ADD column (chưa có ALTER/DROP)
3. Chỉ hỗ trợ 3 data types: text, number, boolean
4. Migration phải trigger manually
5. Chưa có validation, required fields
6. Chưa có API handlers (cần tạo thêm)

### Next Steps sau MVP:
1. Tạo REST API endpoints
2. Thêm validation và constraints
3. Support thêm data types
4. Auto migration triggers  
5. Support ALTER và DROP operations
6. Extend sang entities khác
7. UI để manage custom fields

## Database Schema

### Bảng `custom_field_definitions`
```sql
CREATE TABLE custom_field_definitions (
    id SERIAL PRIMARY KEY,
    tenant_id VARCHAR NOT NULL,
    entity_name VARCHAR NOT NULL DEFAULT 'persons',
    column_name VARCHAR NOT NULL,
    field_type VARCHAR NOT NULL CHECK (field_type IN ('text', 'number', 'boolean')),
    display_name VARCHAR,
    description TEXT,
    is_migrated BOOLEAN DEFAULT FALSE,
    migrated_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(tenant_id, entity_name, column_name)
);
```

### Bảng `person` sau migration
```sql
-- Original columns
ALTER TABLE person ADD COLUMN loyalty_points INTEGER;      -- tenant_A
ALTER TABLE person ADD COLUMN referral_source VARCHAR(255); -- tenant_B
-- etc.
```

## Security Considerations

1. **SQL Injection Prevention**: Column names được validate qua `isValidColumnName()`
2. **Tenant Isolation**: Queries luôn filter theo `tenant_id`
3. **Column Whitelist**: Chỉ cho phép update columns đã được define và migrated
4. **Type Safety**: Field types được validate trước khi migration

## Performance Notes

1. **Dynamic SELECT**: Query performance tốt nhờ dùng real columns
2. **Type Safety**: Database level type checking
3. **Index Strategy**: Có thể thêm index cho custom columns sau
4. **Caching**: Metadata được cache trong memory (có thể improve)

Đây là MVP hoàn chỉnh để test concept Dynamic Schema Custom Fields! 