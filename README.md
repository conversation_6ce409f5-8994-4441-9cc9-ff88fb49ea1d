# bcare

### Goctl
```shell
go install github.com/blazy-vn/goctl@1.2.2
```
### Goctl swagger
```shell
go install github.com/blazy-vn/goctl-swagger@1.0.0
```
### API
```shell
goctl api go -api ./api/doc/bcare.api --dir ./api --style=go_zero --remote https://github.com/blazy-vn/go-zero-template
```
### API Typescript
```shell
goctl api ts -api ./api/doc/bcare.api --dir ./ts
```
### API Swagger
```shell
goctl api swagger -api ./api/doc/bcare.api --dir ./ts
```

### Model
```shell
goctl model pg datasource --url="postgres://blazy:matkhaudedoan@127.0.0.1:15432/bcare_updental_test?sslmode=disable" --schema="core" --table="*" --dir "./api/internal/model"
```
### Swagger
```shell
goctl api plugin -plugin goctl-swagger="swagger -filename bcare.json -host localhost:8888 -basepath /" -api ./api/doc/bcare.api -dir ./swagger
```
### Access token
```html
eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE2OTY2NzE2MzYsImlhdCI6MTY5NjQxMjQzNiwiaWQiOjF9.4FfD9Jj1gUWQco_gbHPRZ-xHDjiF6NP83cmdFnlFhZs
```

### Build with Docker
```shell
DOCKER_BUILDKIT=1 docker build . -t bcare
```

### Entgo
```shell
go generate ./ent
```

### Proto
```shell
protoc --proto_path=./api/proto --go_out=./api/protobuf --go_opt=paths=source_relative --go-grpc_out=./api/protobuf --go-grpc_opt=paths=source_relative ./api/proto/*.proto
```
