# Define multiple authors and excluded directory
$authors = @("vuhoanglam", "vhoanglam")
$since = "2 days ago"
$excludeDir = "ent"  # Directory to exclude

# Initialize counters
$totalAdded = 0
$totalRemoved = 0

# Loop through each author to get their git stats
foreach ($author in $authors) {
    # Get git log excluding specific directory
    $output = git log --author="$author" --since="$since" --numstat --pretty="%H" ':(exclude)$excludeDir'

    $output | ForEach-Object {
        if ($_ -match "^(\d+)\s+(\d+)\s+") {
            $totalAdded += [int]$Matches[1]
            $totalRemoved += [int]$Matches[2]
        }
    }
}

# Calculate total changes
$totalChanges = $totalAdded + $totalRemoved

# Display results
Write-Host "Total changes for authors: $($authors -join ', ')"
Write-Host "Time period: since $since"
Write-Host "Excluded directory: $excludeDir"
Write-Host "Added: $totalAdded, Removed: $totalRemoved, Total changes: $totalChanges"