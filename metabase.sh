#!/bin/bash
read -p "Metabase DB Name: " MB_DB_DBNAME
read -p "Metabase DB User: " MB_DB_USER
read -p "Metabase DB pass: " MB_DB_PASS
read -p "MySQL root pass: " ROOT_PASSWORD
read -p "Nginx domain name: " DOMAIN_NAME
sudo groupadd -r metabase
sudo useradd -r -s /bin/false -g metabase metabase
sudo chown -R metabase:metabase /opt/metabase
sudo touch /var/log/metabase.log
sudo chown syslog:adm /var/log/metabase.log
sudo touch /etc/default/metabase
sudo chmod 640 /etc/default/metabase
sudo touch /etc/systemd/system/metabase.service
sudo cat << EOF > /etc/systemd/system/metabase.service
[Unit]
Description=Metabase server
After=syslog.target
After=network.target
[Service]
WorkingDirectory=/opt/metabase
ExecStart=java -jar /opt/metabase/metabase.jar
EnvironmentFile=/etc/default/metabase
User=metabase
Type=simple
StandardOutput=syslog
StandardError=syslog
SyslogIdentifier=metabase
SuccessExitStatus=143
TimeoutStopSec=120
Restart=always
[Install]
WantedBy=multi-user.target
EOF
sudo cat << EOF > /etc/default/metabase
MB_PASSWORD_COMPLEXITY=normal
MB_PASSWORD_LENGTH=10
MB_JETTY_HOST=0.0.0.0
MB_JETTY_PORT=3000
MB_DB_TYPE=mysql
MB_DB_DBNAME=$MB_DB_DBNAME
MB_DB_PORT=3306
MB_DB_USER=$MB_DB_USER
MB_DB_PASS=$MB_DB_PASS
MB_DB_HOST=127.0.0.1
MB_EMOJI_IN_LOGS=true
EOF
sudo touch /etc/rsyslog.d/metabase.conf
sudo cat << EOF > /etc/rsyslog.d/metabase.conf
if \$programname == 'metabase' then /var/log/metabase.log
& stop
EOF
sudo systemctl restart rsyslog.service
sudo mysql -uroot -p$ROOT_PASSWORD -e "CREATE USER '$MB_DB_USER'@'%' IDENTIFIED BY '$MB_DB_PASS';"
sudo mysql -uroot -p$ROOT_PASSWORD -e "CREATE DATABASE $MB_DB_DBNAME;"
sudo mysql -uroot -p$ROOT_PASSWORD -e "GRANT ALL PRIVILEGES ON $MB_DB_DBNAME.* TO '$MB_DB_USER'@'%';"
sudo mysql -uroot -p$ROOT_PASSWORD -e "FLUSH PRIVILEGES;"
sudo touch /etc/nginx/sites-available/$DOMAIN_NAME.conf
sudo cat << EOF > /etc/nginx/sites-available/$DOMAIN_NAME.conf
server {
  listen 80;
  listen [::]:80;
  server_name $DOMAIN_NAME;
  location / {
    proxy_pass http://127.0.0.1:3000;
  }
}
EOF
sudo ln -s /etc/nginx/sites-available/$DOMAIN_NAME.conf /etc/nginx/sites-enabled/
sudo systemctl reload nginx
sudo systemctl daemon-reload
sudo systemctl start metabase.service
sudo systemctl status metabase.service
sudo systemctl enable metabase.service