// Code generated by ent, DO NOT EDIT.

package ent

import (
	"bcare/ent/appointment"
	"bcare/ent/call"
	"bcare/ent/deal"
	"bcare/ent/formsubmission"
	"bcare/ent/installmentplan"
	"bcare/ent/issue"
	"bcare/ent/messagehistory"
	"bcare/ent/person"
	"bcare/ent/personassignment"
	"bcare/ent/personreferral"
	"bcare/ent/predicate"
	"bcare/ent/tag"
	"bcare/ent/tagperson"
	"bcare/ent/task"
	"bcare/ent/term"
	"bcare/ent/track"
	"bcare/ent/types"
	"bcare/ent/user"
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// PersonUpdate is the builder for updating Person entities.
type PersonUpdate struct {
	config
	hooks     []Hook
	mutation  *PersonMutation
	modifiers []func(*sql.UpdateBuilder)
}

// Where appends a list predicates to the PersonUpdate builder.
func (pu *PersonUpdate) Where(ps ...predicate.Person) *PersonUpdate {
	pu.mutation.Where(ps...)
	return pu
}

// SetDeletedAt sets the "deleted_at" field.
func (pu *PersonUpdate) SetDeletedAt(t time.Time) *PersonUpdate {
	pu.mutation.SetDeletedAt(t)
	return pu
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (pu *PersonUpdate) SetNillableDeletedAt(t *time.Time) *PersonUpdate {
	if t != nil {
		pu.SetDeletedAt(*t)
	}
	return pu
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (pu *PersonUpdate) ClearDeletedAt() *PersonUpdate {
	pu.mutation.ClearDeletedAt()
	return pu
}

// SetStatus sets the "status" field.
func (pu *PersonUpdate) SetStatus(i int8) *PersonUpdate {
	pu.mutation.ResetStatus()
	pu.mutation.SetStatus(i)
	return pu
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (pu *PersonUpdate) SetNillableStatus(i *int8) *PersonUpdate {
	if i != nil {
		pu.SetStatus(*i)
	}
	return pu
}

// AddStatus adds i to the "status" field.
func (pu *PersonUpdate) AddStatus(i int8) *PersonUpdate {
	pu.mutation.AddStatus(i)
	return pu
}

// SetVersion sets the "version" field.
func (pu *PersonUpdate) SetVersion(i int) *PersonUpdate {
	pu.mutation.ResetVersion()
	pu.mutation.SetVersion(i)
	return pu
}

// SetNillableVersion sets the "version" field if the given value is not nil.
func (pu *PersonUpdate) SetNillableVersion(i *int) *PersonUpdate {
	if i != nil {
		pu.SetVersion(*i)
	}
	return pu
}

// AddVersion adds i to the "version" field.
func (pu *PersonUpdate) AddVersion(i int) *PersonUpdate {
	pu.mutation.AddVersion(i)
	return pu
}

// SetUpdatedAt sets the "updated_at" field.
func (pu *PersonUpdate) SetUpdatedAt(t time.Time) *PersonUpdate {
	pu.mutation.SetUpdatedAt(t)
	return pu
}

// SetFullName sets the "full_name" field.
func (pu *PersonUpdate) SetFullName(s string) *PersonUpdate {
	pu.mutation.SetFullName(s)
	return pu
}

// SetNillableFullName sets the "full_name" field if the given value is not nil.
func (pu *PersonUpdate) SetNillableFullName(s *string) *PersonUpdate {
	if s != nil {
		pu.SetFullName(*s)
	}
	return pu
}

// SetDateOfBirth sets the "date_of_birth" field.
func (pu *PersonUpdate) SetDateOfBirth(t time.Time) *PersonUpdate {
	pu.mutation.SetDateOfBirth(t)
	return pu
}

// SetNillableDateOfBirth sets the "date_of_birth" field if the given value is not nil.
func (pu *PersonUpdate) SetNillableDateOfBirth(t *time.Time) *PersonUpdate {
	if t != nil {
		pu.SetDateOfBirth(*t)
	}
	return pu
}

// ClearDateOfBirth clears the value of the "date_of_birth" field.
func (pu *PersonUpdate) ClearDateOfBirth() *PersonUpdate {
	pu.mutation.ClearDateOfBirth()
	return pu
}

// SetGender sets the "gender" field.
func (pu *PersonUpdate) SetGender(s string) *PersonUpdate {
	pu.mutation.SetGender(s)
	return pu
}

// SetNillableGender sets the "gender" field if the given value is not nil.
func (pu *PersonUpdate) SetNillableGender(s *string) *PersonUpdate {
	if s != nil {
		pu.SetGender(*s)
	}
	return pu
}

// ClearGender clears the value of the "gender" field.
func (pu *PersonUpdate) ClearGender() *PersonUpdate {
	pu.mutation.ClearGender()
	return pu
}

// SetProvinceID sets the "province_id" field.
func (pu *PersonUpdate) SetProvinceID(i int) *PersonUpdate {
	pu.mutation.ResetProvinceID()
	pu.mutation.SetProvinceID(i)
	return pu
}

// SetNillableProvinceID sets the "province_id" field if the given value is not nil.
func (pu *PersonUpdate) SetNillableProvinceID(i *int) *PersonUpdate {
	if i != nil {
		pu.SetProvinceID(*i)
	}
	return pu
}

// AddProvinceID adds i to the "province_id" field.
func (pu *PersonUpdate) AddProvinceID(i int) *PersonUpdate {
	pu.mutation.AddProvinceID(i)
	return pu
}

// ClearProvinceID clears the value of the "province_id" field.
func (pu *PersonUpdate) ClearProvinceID() *PersonUpdate {
	pu.mutation.ClearProvinceID()
	return pu
}

// SetDistrictID sets the "district_id" field.
func (pu *PersonUpdate) SetDistrictID(i int) *PersonUpdate {
	pu.mutation.ResetDistrictID()
	pu.mutation.SetDistrictID(i)
	return pu
}

// SetNillableDistrictID sets the "district_id" field if the given value is not nil.
func (pu *PersonUpdate) SetNillableDistrictID(i *int) *PersonUpdate {
	if i != nil {
		pu.SetDistrictID(*i)
	}
	return pu
}

// AddDistrictID adds i to the "district_id" field.
func (pu *PersonUpdate) AddDistrictID(i int) *PersonUpdate {
	pu.mutation.AddDistrictID(i)
	return pu
}

// ClearDistrictID clears the value of the "district_id" field.
func (pu *PersonUpdate) ClearDistrictID() *PersonUpdate {
	pu.mutation.ClearDistrictID()
	return pu
}

// SetWardID sets the "ward_id" field.
func (pu *PersonUpdate) SetWardID(i int) *PersonUpdate {
	pu.mutation.ResetWardID()
	pu.mutation.SetWardID(i)
	return pu
}

// SetNillableWardID sets the "ward_id" field if the given value is not nil.
func (pu *PersonUpdate) SetNillableWardID(i *int) *PersonUpdate {
	if i != nil {
		pu.SetWardID(*i)
	}
	return pu
}

// AddWardID adds i to the "ward_id" field.
func (pu *PersonUpdate) AddWardID(i int) *PersonUpdate {
	pu.mutation.AddWardID(i)
	return pu
}

// ClearWardID clears the value of the "ward_id" field.
func (pu *PersonUpdate) ClearWardID() *PersonUpdate {
	pu.mutation.ClearWardID()
	return pu
}

// SetAddressNumber sets the "address_number" field.
func (pu *PersonUpdate) SetAddressNumber(s string) *PersonUpdate {
	pu.mutation.SetAddressNumber(s)
	return pu
}

// SetNillableAddressNumber sets the "address_number" field if the given value is not nil.
func (pu *PersonUpdate) SetNillableAddressNumber(s *string) *PersonUpdate {
	if s != nil {
		pu.SetAddressNumber(*s)
	}
	return pu
}

// ClearAddressNumber clears the value of the "address_number" field.
func (pu *PersonUpdate) ClearAddressNumber() *PersonUpdate {
	pu.mutation.ClearAddressNumber()
	return pu
}

// SetPhone sets the "phone" field.
func (pu *PersonUpdate) SetPhone(s string) *PersonUpdate {
	pu.mutation.SetPhone(s)
	return pu
}

// SetNillablePhone sets the "phone" field if the given value is not nil.
func (pu *PersonUpdate) SetNillablePhone(s *string) *PersonUpdate {
	if s != nil {
		pu.SetPhone(*s)
	}
	return pu
}

// SetEmail sets the "email" field.
func (pu *PersonUpdate) SetEmail(s string) *PersonUpdate {
	pu.mutation.SetEmail(s)
	return pu
}

// SetNillableEmail sets the "email" field if the given value is not nil.
func (pu *PersonUpdate) SetNillableEmail(s *string) *PersonUpdate {
	if s != nil {
		pu.SetEmail(*s)
	}
	return pu
}

// ClearEmail clears the value of the "email" field.
func (pu *PersonUpdate) ClearEmail() *PersonUpdate {
	pu.mutation.ClearEmail()
	return pu
}

// SetJobID sets the "job_id" field.
func (pu *PersonUpdate) SetJobID(i int) *PersonUpdate {
	pu.mutation.SetJobID(i)
	return pu
}

// SetNillableJobID sets the "job_id" field if the given value is not nil.
func (pu *PersonUpdate) SetNillableJobID(i *int) *PersonUpdate {
	if i != nil {
		pu.SetJobID(*i)
	}
	return pu
}

// ClearJobID clears the value of the "job_id" field.
func (pu *PersonUpdate) ClearJobID() *PersonUpdate {
	pu.mutation.ClearJobID()
	return pu
}

// SetUserID sets the "user_id" field.
func (pu *PersonUpdate) SetUserID(i int) *PersonUpdate {
	pu.mutation.SetUserID(i)
	return pu
}

// SetNillableUserID sets the "user_id" field if the given value is not nil.
func (pu *PersonUpdate) SetNillableUserID(i *int) *PersonUpdate {
	if i != nil {
		pu.SetUserID(*i)
	}
	return pu
}

// ClearUserID clears the value of the "user_id" field.
func (pu *PersonUpdate) ClearUserID() *PersonUpdate {
	pu.mutation.ClearUserID()
	return pu
}

// SetSourceID sets the "source_id" field.
func (pu *PersonUpdate) SetSourceID(i int) *PersonUpdate {
	pu.mutation.SetSourceID(i)
	return pu
}

// SetNillableSourceID sets the "source_id" field if the given value is not nil.
func (pu *PersonUpdate) SetNillableSourceID(i *int) *PersonUpdate {
	if i != nil {
		pu.SetSourceID(*i)
	}
	return pu
}

// ClearSourceID clears the value of the "source_id" field.
func (pu *PersonUpdate) ClearSourceID() *PersonUpdate {
	pu.mutation.ClearSourceID()
	return pu
}

// SetPhoneConfirm sets the "phone_confirm" field.
func (pu *PersonUpdate) SetPhoneConfirm(b bool) *PersonUpdate {
	pu.mutation.SetPhoneConfirm(b)
	return pu
}

// SetNillablePhoneConfirm sets the "phone_confirm" field if the given value is not nil.
func (pu *PersonUpdate) SetNillablePhoneConfirm(b *bool) *PersonUpdate {
	if b != nil {
		pu.SetPhoneConfirm(*b)
	}
	return pu
}

// ClearPhoneConfirm clears the value of the "phone_confirm" field.
func (pu *PersonUpdate) ClearPhoneConfirm() *PersonUpdate {
	pu.mutation.ClearPhoneConfirm()
	return pu
}

// SetMailConfirm sets the "mail_confirm" field.
func (pu *PersonUpdate) SetMailConfirm(b bool) *PersonUpdate {
	pu.mutation.SetMailConfirm(b)
	return pu
}

// SetNillableMailConfirm sets the "mail_confirm" field if the given value is not nil.
func (pu *PersonUpdate) SetNillableMailConfirm(b *bool) *PersonUpdate {
	if b != nil {
		pu.SetMailConfirm(*b)
	}
	return pu
}

// ClearMailConfirm clears the value of the "mail_confirm" field.
func (pu *PersonUpdate) ClearMailConfirm() *PersonUpdate {
	pu.mutation.ClearMailConfirm()
	return pu
}

// SetPersonField sets the "person_field" field.
func (pu *PersonUpdate) SetPersonField(tm *types.PersonMeta) *PersonUpdate {
	pu.mutation.SetPersonField(tm)
	return pu
}

// ClearPersonField clears the value of the "person_field" field.
func (pu *PersonUpdate) ClearPersonField() *PersonUpdate {
	pu.mutation.ClearPersonField()
	return pu
}

// AddDealIDs adds the "deals" edge to the Deal entity by IDs.
func (pu *PersonUpdate) AddDealIDs(ids ...int) *PersonUpdate {
	pu.mutation.AddDealIDs(ids...)
	return pu
}

// AddDeals adds the "deals" edges to the Deal entity.
func (pu *PersonUpdate) AddDeals(d ...*Deal) *PersonUpdate {
	ids := make([]int, len(d))
	for i := range d {
		ids[i] = d[i].ID
	}
	return pu.AddDealIDs(ids...)
}

// AddTagIDs adds the "tags" edge to the Tag entity by IDs.
func (pu *PersonUpdate) AddTagIDs(ids ...int) *PersonUpdate {
	pu.mutation.AddTagIDs(ids...)
	return pu
}

// AddTags adds the "tags" edges to the Tag entity.
func (pu *PersonUpdate) AddTags(t ...*Tag) *PersonUpdate {
	ids := make([]int, len(t))
	for i := range t {
		ids[i] = t[i].ID
	}
	return pu.AddTagIDs(ids...)
}

// AddFormSubmissionIDs adds the "form_submissions" edge to the FormSubmission entity by IDs.
func (pu *PersonUpdate) AddFormSubmissionIDs(ids ...int) *PersonUpdate {
	pu.mutation.AddFormSubmissionIDs(ids...)
	return pu
}

// AddFormSubmissions adds the "form_submissions" edges to the FormSubmission entity.
func (pu *PersonUpdate) AddFormSubmissions(f ...*FormSubmission) *PersonUpdate {
	ids := make([]int, len(f))
	for i := range f {
		ids[i] = f[i].ID
	}
	return pu.AddFormSubmissionIDs(ids...)
}

// AddPlanIDs adds the "plans" edge to the InstallmentPlan entity by IDs.
func (pu *PersonUpdate) AddPlanIDs(ids ...int) *PersonUpdate {
	pu.mutation.AddPlanIDs(ids...)
	return pu
}

// AddPlans adds the "plans" edges to the InstallmentPlan entity.
func (pu *PersonUpdate) AddPlans(i ...*InstallmentPlan) *PersonUpdate {
	ids := make([]int, len(i))
	for j := range i {
		ids[j] = i[j].ID
	}
	return pu.AddPlanIDs(ids...)
}

// AddCallIDs adds the "calls" edge to the Call entity by IDs.
func (pu *PersonUpdate) AddCallIDs(ids ...int) *PersonUpdate {
	pu.mutation.AddCallIDs(ids...)
	return pu
}

// AddCalls adds the "calls" edges to the Call entity.
func (pu *PersonUpdate) AddCalls(c ...*Call) *PersonUpdate {
	ids := make([]int, len(c))
	for i := range c {
		ids[i] = c[i].ID
	}
	return pu.AddCallIDs(ids...)
}

// AddTaskIDs adds the "tasks" edge to the Task entity by IDs.
func (pu *PersonUpdate) AddTaskIDs(ids ...int) *PersonUpdate {
	pu.mutation.AddTaskIDs(ids...)
	return pu
}

// AddTasks adds the "tasks" edges to the Task entity.
func (pu *PersonUpdate) AddTasks(t ...*Task) *PersonUpdate {
	ids := make([]int, len(t))
	for i := range t {
		ids[i] = t[i].ID
	}
	return pu.AddTaskIDs(ids...)
}

// AddIssueIDs adds the "issues" edge to the Issue entity by IDs.
func (pu *PersonUpdate) AddIssueIDs(ids ...int) *PersonUpdate {
	pu.mutation.AddIssueIDs(ids...)
	return pu
}

// AddIssues adds the "issues" edges to the Issue entity.
func (pu *PersonUpdate) AddIssues(i ...*Issue) *PersonUpdate {
	ids := make([]int, len(i))
	for j := range i {
		ids[j] = i[j].ID
	}
	return pu.AddIssueIDs(ids...)
}

// AddAppointmentIDs adds the "appointments" edge to the Appointment entity by IDs.
func (pu *PersonUpdate) AddAppointmentIDs(ids ...int) *PersonUpdate {
	pu.mutation.AddAppointmentIDs(ids...)
	return pu
}

// AddAppointments adds the "appointments" edges to the Appointment entity.
func (pu *PersonUpdate) AddAppointments(a ...*Appointment) *PersonUpdate {
	ids := make([]int, len(a))
	for i := range a {
		ids[i] = a[i].ID
	}
	return pu.AddAppointmentIDs(ids...)
}

// AddTrackIDs adds the "tracks" edge to the Track entity by IDs.
func (pu *PersonUpdate) AddTrackIDs(ids ...int) *PersonUpdate {
	pu.mutation.AddTrackIDs(ids...)
	return pu
}

// AddTracks adds the "tracks" edges to the Track entity.
func (pu *PersonUpdate) AddTracks(t ...*Track) *PersonUpdate {
	ids := make([]int, len(t))
	for i := range t {
		ids[i] = t[i].ID
	}
	return pu.AddTrackIDs(ids...)
}

// AddMessageHistoryIDs adds the "message_histories" edge to the MessageHistory entity by IDs.
func (pu *PersonUpdate) AddMessageHistoryIDs(ids ...int) *PersonUpdate {
	pu.mutation.AddMessageHistoryIDs(ids...)
	return pu
}

// AddMessageHistories adds the "message_histories" edges to the MessageHistory entity.
func (pu *PersonUpdate) AddMessageHistories(m ...*MessageHistory) *PersonUpdate {
	ids := make([]int, len(m))
	for i := range m {
		ids[i] = m[i].ID
	}
	return pu.AddMessageHistoryIDs(ids...)
}

// AddAssigneeIDs adds the "assignees" edge to the User entity by IDs.
func (pu *PersonUpdate) AddAssigneeIDs(ids ...int) *PersonUpdate {
	pu.mutation.AddAssigneeIDs(ids...)
	return pu
}

// AddAssignees adds the "assignees" edges to the User entity.
func (pu *PersonUpdate) AddAssignees(u ...*User) *PersonUpdate {
	ids := make([]int, len(u))
	for i := range u {
		ids[i] = u[i].ID
	}
	return pu.AddAssigneeIDs(ids...)
}

// SetJob sets the "job" edge to the Term entity.
func (pu *PersonUpdate) SetJob(t *Term) *PersonUpdate {
	return pu.SetJobID(t.ID)
}

// SetSource sets the "source" edge to the Term entity.
func (pu *PersonUpdate) SetSource(t *Term) *PersonUpdate {
	return pu.SetSourceID(t.ID)
}

// SetCreatorID sets the "creator" edge to the User entity by ID.
func (pu *PersonUpdate) SetCreatorID(id int) *PersonUpdate {
	pu.mutation.SetCreatorID(id)
	return pu
}

// SetNillableCreatorID sets the "creator" edge to the User entity by ID if the given value is not nil.
func (pu *PersonUpdate) SetNillableCreatorID(id *int) *PersonUpdate {
	if id != nil {
		pu = pu.SetCreatorID(*id)
	}
	return pu
}

// SetCreator sets the "creator" edge to the User entity.
func (pu *PersonUpdate) SetCreator(u *User) *PersonUpdate {
	return pu.SetCreatorID(u.ID)
}

// SetReferredByID sets the "referred_by" edge to the PersonReferral entity by ID.
func (pu *PersonUpdate) SetReferredByID(id int) *PersonUpdate {
	pu.mutation.SetReferredByID(id)
	return pu
}

// SetNillableReferredByID sets the "referred_by" edge to the PersonReferral entity by ID if the given value is not nil.
func (pu *PersonUpdate) SetNillableReferredByID(id *int) *PersonUpdate {
	if id != nil {
		pu = pu.SetReferredByID(*id)
	}
	return pu
}

// SetReferredBy sets the "referred_by" edge to the PersonReferral entity.
func (pu *PersonUpdate) SetReferredBy(p *PersonReferral) *PersonUpdate {
	return pu.SetReferredByID(p.ID)
}

// AddTagPersonIDs adds the "tag_person" edge to the TagPerson entity by IDs.
func (pu *PersonUpdate) AddTagPersonIDs(ids ...int) *PersonUpdate {
	pu.mutation.AddTagPersonIDs(ids...)
	return pu
}

// AddTagPerson adds the "tag_person" edges to the TagPerson entity.
func (pu *PersonUpdate) AddTagPerson(t ...*TagPerson) *PersonUpdate {
	ids := make([]int, len(t))
	for i := range t {
		ids[i] = t[i].ID
	}
	return pu.AddTagPersonIDs(ids...)
}

// AddAssignmentIDs adds the "assignment" edge to the PersonAssignment entity by IDs.
func (pu *PersonUpdate) AddAssignmentIDs(ids ...int) *PersonUpdate {
	pu.mutation.AddAssignmentIDs(ids...)
	return pu
}

// AddAssignment adds the "assignment" edges to the PersonAssignment entity.
func (pu *PersonUpdate) AddAssignment(p ...*PersonAssignment) *PersonUpdate {
	ids := make([]int, len(p))
	for i := range p {
		ids[i] = p[i].ID
	}
	return pu.AddAssignmentIDs(ids...)
}

// Mutation returns the PersonMutation object of the builder.
func (pu *PersonUpdate) Mutation() *PersonMutation {
	return pu.mutation
}

// ClearDeals clears all "deals" edges to the Deal entity.
func (pu *PersonUpdate) ClearDeals() *PersonUpdate {
	pu.mutation.ClearDeals()
	return pu
}

// RemoveDealIDs removes the "deals" edge to Deal entities by IDs.
func (pu *PersonUpdate) RemoveDealIDs(ids ...int) *PersonUpdate {
	pu.mutation.RemoveDealIDs(ids...)
	return pu
}

// RemoveDeals removes "deals" edges to Deal entities.
func (pu *PersonUpdate) RemoveDeals(d ...*Deal) *PersonUpdate {
	ids := make([]int, len(d))
	for i := range d {
		ids[i] = d[i].ID
	}
	return pu.RemoveDealIDs(ids...)
}

// ClearTags clears all "tags" edges to the Tag entity.
func (pu *PersonUpdate) ClearTags() *PersonUpdate {
	pu.mutation.ClearTags()
	return pu
}

// RemoveTagIDs removes the "tags" edge to Tag entities by IDs.
func (pu *PersonUpdate) RemoveTagIDs(ids ...int) *PersonUpdate {
	pu.mutation.RemoveTagIDs(ids...)
	return pu
}

// RemoveTags removes "tags" edges to Tag entities.
func (pu *PersonUpdate) RemoveTags(t ...*Tag) *PersonUpdate {
	ids := make([]int, len(t))
	for i := range t {
		ids[i] = t[i].ID
	}
	return pu.RemoveTagIDs(ids...)
}

// ClearFormSubmissions clears all "form_submissions" edges to the FormSubmission entity.
func (pu *PersonUpdate) ClearFormSubmissions() *PersonUpdate {
	pu.mutation.ClearFormSubmissions()
	return pu
}

// RemoveFormSubmissionIDs removes the "form_submissions" edge to FormSubmission entities by IDs.
func (pu *PersonUpdate) RemoveFormSubmissionIDs(ids ...int) *PersonUpdate {
	pu.mutation.RemoveFormSubmissionIDs(ids...)
	return pu
}

// RemoveFormSubmissions removes "form_submissions" edges to FormSubmission entities.
func (pu *PersonUpdate) RemoveFormSubmissions(f ...*FormSubmission) *PersonUpdate {
	ids := make([]int, len(f))
	for i := range f {
		ids[i] = f[i].ID
	}
	return pu.RemoveFormSubmissionIDs(ids...)
}

// ClearPlans clears all "plans" edges to the InstallmentPlan entity.
func (pu *PersonUpdate) ClearPlans() *PersonUpdate {
	pu.mutation.ClearPlans()
	return pu
}

// RemovePlanIDs removes the "plans" edge to InstallmentPlan entities by IDs.
func (pu *PersonUpdate) RemovePlanIDs(ids ...int) *PersonUpdate {
	pu.mutation.RemovePlanIDs(ids...)
	return pu
}

// RemovePlans removes "plans" edges to InstallmentPlan entities.
func (pu *PersonUpdate) RemovePlans(i ...*InstallmentPlan) *PersonUpdate {
	ids := make([]int, len(i))
	for j := range i {
		ids[j] = i[j].ID
	}
	return pu.RemovePlanIDs(ids...)
}

// ClearCalls clears all "calls" edges to the Call entity.
func (pu *PersonUpdate) ClearCalls() *PersonUpdate {
	pu.mutation.ClearCalls()
	return pu
}

// RemoveCallIDs removes the "calls" edge to Call entities by IDs.
func (pu *PersonUpdate) RemoveCallIDs(ids ...int) *PersonUpdate {
	pu.mutation.RemoveCallIDs(ids...)
	return pu
}

// RemoveCalls removes "calls" edges to Call entities.
func (pu *PersonUpdate) RemoveCalls(c ...*Call) *PersonUpdate {
	ids := make([]int, len(c))
	for i := range c {
		ids[i] = c[i].ID
	}
	return pu.RemoveCallIDs(ids...)
}

// ClearTasks clears all "tasks" edges to the Task entity.
func (pu *PersonUpdate) ClearTasks() *PersonUpdate {
	pu.mutation.ClearTasks()
	return pu
}

// RemoveTaskIDs removes the "tasks" edge to Task entities by IDs.
func (pu *PersonUpdate) RemoveTaskIDs(ids ...int) *PersonUpdate {
	pu.mutation.RemoveTaskIDs(ids...)
	return pu
}

// RemoveTasks removes "tasks" edges to Task entities.
func (pu *PersonUpdate) RemoveTasks(t ...*Task) *PersonUpdate {
	ids := make([]int, len(t))
	for i := range t {
		ids[i] = t[i].ID
	}
	return pu.RemoveTaskIDs(ids...)
}

// ClearIssues clears all "issues" edges to the Issue entity.
func (pu *PersonUpdate) ClearIssues() *PersonUpdate {
	pu.mutation.ClearIssues()
	return pu
}

// RemoveIssueIDs removes the "issues" edge to Issue entities by IDs.
func (pu *PersonUpdate) RemoveIssueIDs(ids ...int) *PersonUpdate {
	pu.mutation.RemoveIssueIDs(ids...)
	return pu
}

// RemoveIssues removes "issues" edges to Issue entities.
func (pu *PersonUpdate) RemoveIssues(i ...*Issue) *PersonUpdate {
	ids := make([]int, len(i))
	for j := range i {
		ids[j] = i[j].ID
	}
	return pu.RemoveIssueIDs(ids...)
}

// ClearAppointments clears all "appointments" edges to the Appointment entity.
func (pu *PersonUpdate) ClearAppointments() *PersonUpdate {
	pu.mutation.ClearAppointments()
	return pu
}

// RemoveAppointmentIDs removes the "appointments" edge to Appointment entities by IDs.
func (pu *PersonUpdate) RemoveAppointmentIDs(ids ...int) *PersonUpdate {
	pu.mutation.RemoveAppointmentIDs(ids...)
	return pu
}

// RemoveAppointments removes "appointments" edges to Appointment entities.
func (pu *PersonUpdate) RemoveAppointments(a ...*Appointment) *PersonUpdate {
	ids := make([]int, len(a))
	for i := range a {
		ids[i] = a[i].ID
	}
	return pu.RemoveAppointmentIDs(ids...)
}

// ClearTracks clears all "tracks" edges to the Track entity.
func (pu *PersonUpdate) ClearTracks() *PersonUpdate {
	pu.mutation.ClearTracks()
	return pu
}

// RemoveTrackIDs removes the "tracks" edge to Track entities by IDs.
func (pu *PersonUpdate) RemoveTrackIDs(ids ...int) *PersonUpdate {
	pu.mutation.RemoveTrackIDs(ids...)
	return pu
}

// RemoveTracks removes "tracks" edges to Track entities.
func (pu *PersonUpdate) RemoveTracks(t ...*Track) *PersonUpdate {
	ids := make([]int, len(t))
	for i := range t {
		ids[i] = t[i].ID
	}
	return pu.RemoveTrackIDs(ids...)
}

// ClearMessageHistories clears all "message_histories" edges to the MessageHistory entity.
func (pu *PersonUpdate) ClearMessageHistories() *PersonUpdate {
	pu.mutation.ClearMessageHistories()
	return pu
}

// RemoveMessageHistoryIDs removes the "message_histories" edge to MessageHistory entities by IDs.
func (pu *PersonUpdate) RemoveMessageHistoryIDs(ids ...int) *PersonUpdate {
	pu.mutation.RemoveMessageHistoryIDs(ids...)
	return pu
}

// RemoveMessageHistories removes "message_histories" edges to MessageHistory entities.
func (pu *PersonUpdate) RemoveMessageHistories(m ...*MessageHistory) *PersonUpdate {
	ids := make([]int, len(m))
	for i := range m {
		ids[i] = m[i].ID
	}
	return pu.RemoveMessageHistoryIDs(ids...)
}

// ClearAssignees clears all "assignees" edges to the User entity.
func (pu *PersonUpdate) ClearAssignees() *PersonUpdate {
	pu.mutation.ClearAssignees()
	return pu
}

// RemoveAssigneeIDs removes the "assignees" edge to User entities by IDs.
func (pu *PersonUpdate) RemoveAssigneeIDs(ids ...int) *PersonUpdate {
	pu.mutation.RemoveAssigneeIDs(ids...)
	return pu
}

// RemoveAssignees removes "assignees" edges to User entities.
func (pu *PersonUpdate) RemoveAssignees(u ...*User) *PersonUpdate {
	ids := make([]int, len(u))
	for i := range u {
		ids[i] = u[i].ID
	}
	return pu.RemoveAssigneeIDs(ids...)
}

// ClearJob clears the "job" edge to the Term entity.
func (pu *PersonUpdate) ClearJob() *PersonUpdate {
	pu.mutation.ClearJob()
	return pu
}

// ClearSource clears the "source" edge to the Term entity.
func (pu *PersonUpdate) ClearSource() *PersonUpdate {
	pu.mutation.ClearSource()
	return pu
}

// ClearCreator clears the "creator" edge to the User entity.
func (pu *PersonUpdate) ClearCreator() *PersonUpdate {
	pu.mutation.ClearCreator()
	return pu
}

// ClearReferredBy clears the "referred_by" edge to the PersonReferral entity.
func (pu *PersonUpdate) ClearReferredBy() *PersonUpdate {
	pu.mutation.ClearReferredBy()
	return pu
}

// ClearTagPerson clears all "tag_person" edges to the TagPerson entity.
func (pu *PersonUpdate) ClearTagPerson() *PersonUpdate {
	pu.mutation.ClearTagPerson()
	return pu
}

// RemoveTagPersonIDs removes the "tag_person" edge to TagPerson entities by IDs.
func (pu *PersonUpdate) RemoveTagPersonIDs(ids ...int) *PersonUpdate {
	pu.mutation.RemoveTagPersonIDs(ids...)
	return pu
}

// RemoveTagPerson removes "tag_person" edges to TagPerson entities.
func (pu *PersonUpdate) RemoveTagPerson(t ...*TagPerson) *PersonUpdate {
	ids := make([]int, len(t))
	for i := range t {
		ids[i] = t[i].ID
	}
	return pu.RemoveTagPersonIDs(ids...)
}

// ClearAssignment clears all "assignment" edges to the PersonAssignment entity.
func (pu *PersonUpdate) ClearAssignment() *PersonUpdate {
	pu.mutation.ClearAssignment()
	return pu
}

// RemoveAssignmentIDs removes the "assignment" edge to PersonAssignment entities by IDs.
func (pu *PersonUpdate) RemoveAssignmentIDs(ids ...int) *PersonUpdate {
	pu.mutation.RemoveAssignmentIDs(ids...)
	return pu
}

// RemoveAssignment removes "assignment" edges to PersonAssignment entities.
func (pu *PersonUpdate) RemoveAssignment(p ...*PersonAssignment) *PersonUpdate {
	ids := make([]int, len(p))
	for i := range p {
		ids[i] = p[i].ID
	}
	return pu.RemoveAssignmentIDs(ids...)
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (pu *PersonUpdate) Save(ctx context.Context) (int, error) {
	if err := pu.defaults(); err != nil {
		return 0, err
	}
	return withHooks(ctx, pu.sqlSave, pu.mutation, pu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (pu *PersonUpdate) SaveX(ctx context.Context) int {
	affected, err := pu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (pu *PersonUpdate) Exec(ctx context.Context) error {
	_, err := pu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (pu *PersonUpdate) ExecX(ctx context.Context) {
	if err := pu.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (pu *PersonUpdate) defaults() error {
	if _, ok := pu.mutation.UpdatedAt(); !ok {
		if person.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized person.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := person.UpdateDefaultUpdatedAt()
		pu.mutation.SetUpdatedAt(v)
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (pu *PersonUpdate) Modify(modifiers ...func(u *sql.UpdateBuilder)) *PersonUpdate {
	pu.modifiers = append(pu.modifiers, modifiers...)
	return pu
}

func (pu *PersonUpdate) sqlSave(ctx context.Context) (n int, err error) {
	_spec := sqlgraph.NewUpdateSpec(person.Table, person.Columns, sqlgraph.NewFieldSpec(person.FieldID, field.TypeInt))
	if ps := pu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := pu.mutation.DeletedAt(); ok {
		_spec.SetField(person.FieldDeletedAt, field.TypeTime, value)
	}
	if pu.mutation.DeletedAtCleared() {
		_spec.ClearField(person.FieldDeletedAt, field.TypeTime)
	}
	if value, ok := pu.mutation.Status(); ok {
		_spec.SetField(person.FieldStatus, field.TypeInt8, value)
	}
	if value, ok := pu.mutation.AddedStatus(); ok {
		_spec.AddField(person.FieldStatus, field.TypeInt8, value)
	}
	if value, ok := pu.mutation.Version(); ok {
		_spec.SetField(person.FieldVersion, field.TypeInt, value)
	}
	if value, ok := pu.mutation.AddedVersion(); ok {
		_spec.AddField(person.FieldVersion, field.TypeInt, value)
	}
	if value, ok := pu.mutation.UpdatedAt(); ok {
		_spec.SetField(person.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := pu.mutation.FullName(); ok {
		_spec.SetField(person.FieldFullName, field.TypeString, value)
	}
	if value, ok := pu.mutation.DateOfBirth(); ok {
		_spec.SetField(person.FieldDateOfBirth, field.TypeTime, value)
	}
	if pu.mutation.DateOfBirthCleared() {
		_spec.ClearField(person.FieldDateOfBirth, field.TypeTime)
	}
	if value, ok := pu.mutation.Gender(); ok {
		_spec.SetField(person.FieldGender, field.TypeString, value)
	}
	if pu.mutation.GenderCleared() {
		_spec.ClearField(person.FieldGender, field.TypeString)
	}
	if value, ok := pu.mutation.ProvinceID(); ok {
		_spec.SetField(person.FieldProvinceID, field.TypeInt, value)
	}
	if value, ok := pu.mutation.AddedProvinceID(); ok {
		_spec.AddField(person.FieldProvinceID, field.TypeInt, value)
	}
	if pu.mutation.ProvinceIDCleared() {
		_spec.ClearField(person.FieldProvinceID, field.TypeInt)
	}
	if value, ok := pu.mutation.DistrictID(); ok {
		_spec.SetField(person.FieldDistrictID, field.TypeInt, value)
	}
	if value, ok := pu.mutation.AddedDistrictID(); ok {
		_spec.AddField(person.FieldDistrictID, field.TypeInt, value)
	}
	if pu.mutation.DistrictIDCleared() {
		_spec.ClearField(person.FieldDistrictID, field.TypeInt)
	}
	if value, ok := pu.mutation.WardID(); ok {
		_spec.SetField(person.FieldWardID, field.TypeInt, value)
	}
	if value, ok := pu.mutation.AddedWardID(); ok {
		_spec.AddField(person.FieldWardID, field.TypeInt, value)
	}
	if pu.mutation.WardIDCleared() {
		_spec.ClearField(person.FieldWardID, field.TypeInt)
	}
	if value, ok := pu.mutation.AddressNumber(); ok {
		_spec.SetField(person.FieldAddressNumber, field.TypeString, value)
	}
	if pu.mutation.AddressNumberCleared() {
		_spec.ClearField(person.FieldAddressNumber, field.TypeString)
	}
	if value, ok := pu.mutation.Phone(); ok {
		_spec.SetField(person.FieldPhone, field.TypeString, value)
	}
	if value, ok := pu.mutation.Email(); ok {
		_spec.SetField(person.FieldEmail, field.TypeString, value)
	}
	if pu.mutation.EmailCleared() {
		_spec.ClearField(person.FieldEmail, field.TypeString)
	}
	if value, ok := pu.mutation.PhoneConfirm(); ok {
		_spec.SetField(person.FieldPhoneConfirm, field.TypeBool, value)
	}
	if pu.mutation.PhoneConfirmCleared() {
		_spec.ClearField(person.FieldPhoneConfirm, field.TypeBool)
	}
	if value, ok := pu.mutation.MailConfirm(); ok {
		_spec.SetField(person.FieldMailConfirm, field.TypeBool, value)
	}
	if pu.mutation.MailConfirmCleared() {
		_spec.ClearField(person.FieldMailConfirm, field.TypeBool)
	}
	if value, ok := pu.mutation.PersonField(); ok {
		_spec.SetField(person.FieldPersonField, field.TypeJSON, value)
	}
	if pu.mutation.PersonFieldCleared() {
		_spec.ClearField(person.FieldPersonField, field.TypeJSON)
	}
	if pu.mutation.DealsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   person.DealsTable,
			Columns: []string{person.DealsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(deal.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := pu.mutation.RemovedDealsIDs(); len(nodes) > 0 && !pu.mutation.DealsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   person.DealsTable,
			Columns: []string{person.DealsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(deal.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := pu.mutation.DealsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   person.DealsTable,
			Columns: []string{person.DealsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(deal.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if pu.mutation.TagsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: false,
			Table:   person.TagsTable,
			Columns: person.TagsPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tag.FieldID, field.TypeInt),
			},
		}
		createE := &TagPersonCreate{config: pu.config, mutation: newTagPersonMutation(pu.config, OpCreate)}
		createE.defaults()
		_, specE := createE.createSpec()
		edge.Target.Fields = specE.Fields
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := pu.mutation.RemovedTagsIDs(); len(nodes) > 0 && !pu.mutation.TagsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: false,
			Table:   person.TagsTable,
			Columns: person.TagsPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tag.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		createE := &TagPersonCreate{config: pu.config, mutation: newTagPersonMutation(pu.config, OpCreate)}
		createE.defaults()
		_, specE := createE.createSpec()
		edge.Target.Fields = specE.Fields
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := pu.mutation.TagsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: false,
			Table:   person.TagsTable,
			Columns: person.TagsPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tag.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		createE := &TagPersonCreate{config: pu.config, mutation: newTagPersonMutation(pu.config, OpCreate)}
		createE.defaults()
		_, specE := createE.createSpec()
		edge.Target.Fields = specE.Fields
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if pu.mutation.FormSubmissionsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   person.FormSubmissionsTable,
			Columns: []string{person.FormSubmissionsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(formsubmission.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := pu.mutation.RemovedFormSubmissionsIDs(); len(nodes) > 0 && !pu.mutation.FormSubmissionsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   person.FormSubmissionsTable,
			Columns: []string{person.FormSubmissionsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(formsubmission.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := pu.mutation.FormSubmissionsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   person.FormSubmissionsTable,
			Columns: []string{person.FormSubmissionsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(formsubmission.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if pu.mutation.PlansCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   person.PlansTable,
			Columns: []string{person.PlansColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(installmentplan.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := pu.mutation.RemovedPlansIDs(); len(nodes) > 0 && !pu.mutation.PlansCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   person.PlansTable,
			Columns: []string{person.PlansColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(installmentplan.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := pu.mutation.PlansIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   person.PlansTable,
			Columns: []string{person.PlansColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(installmentplan.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if pu.mutation.CallsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   person.CallsTable,
			Columns: []string{person.CallsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(call.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := pu.mutation.RemovedCallsIDs(); len(nodes) > 0 && !pu.mutation.CallsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   person.CallsTable,
			Columns: []string{person.CallsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(call.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := pu.mutation.CallsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   person.CallsTable,
			Columns: []string{person.CallsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(call.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if pu.mutation.TasksCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   person.TasksTable,
			Columns: []string{person.TasksColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(task.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := pu.mutation.RemovedTasksIDs(); len(nodes) > 0 && !pu.mutation.TasksCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   person.TasksTable,
			Columns: []string{person.TasksColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(task.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := pu.mutation.TasksIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   person.TasksTable,
			Columns: []string{person.TasksColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(task.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if pu.mutation.IssuesCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   person.IssuesTable,
			Columns: []string{person.IssuesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(issue.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := pu.mutation.RemovedIssuesIDs(); len(nodes) > 0 && !pu.mutation.IssuesCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   person.IssuesTable,
			Columns: []string{person.IssuesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(issue.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := pu.mutation.IssuesIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   person.IssuesTable,
			Columns: []string{person.IssuesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(issue.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if pu.mutation.AppointmentsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   person.AppointmentsTable,
			Columns: []string{person.AppointmentsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(appointment.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := pu.mutation.RemovedAppointmentsIDs(); len(nodes) > 0 && !pu.mutation.AppointmentsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   person.AppointmentsTable,
			Columns: []string{person.AppointmentsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(appointment.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := pu.mutation.AppointmentsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   person.AppointmentsTable,
			Columns: []string{person.AppointmentsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(appointment.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if pu.mutation.TracksCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   person.TracksTable,
			Columns: []string{person.TracksColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(track.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := pu.mutation.RemovedTracksIDs(); len(nodes) > 0 && !pu.mutation.TracksCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   person.TracksTable,
			Columns: []string{person.TracksColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(track.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := pu.mutation.TracksIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   person.TracksTable,
			Columns: []string{person.TracksColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(track.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if pu.mutation.MessageHistoriesCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   person.MessageHistoriesTable,
			Columns: []string{person.MessageHistoriesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(messagehistory.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := pu.mutation.RemovedMessageHistoriesIDs(); len(nodes) > 0 && !pu.mutation.MessageHistoriesCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   person.MessageHistoriesTable,
			Columns: []string{person.MessageHistoriesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(messagehistory.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := pu.mutation.MessageHistoriesIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   person.MessageHistoriesTable,
			Columns: []string{person.MessageHistoriesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(messagehistory.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if pu.mutation.AssigneesCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: false,
			Table:   person.AssigneesTable,
			Columns: person.AssigneesPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeInt),
			},
		}
		createE := &PersonAssignmentCreate{config: pu.config, mutation: newPersonAssignmentMutation(pu.config, OpCreate)}
		createE.defaults()
		_, specE := createE.createSpec()
		edge.Target.Fields = specE.Fields
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := pu.mutation.RemovedAssigneesIDs(); len(nodes) > 0 && !pu.mutation.AssigneesCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: false,
			Table:   person.AssigneesTable,
			Columns: person.AssigneesPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		createE := &PersonAssignmentCreate{config: pu.config, mutation: newPersonAssignmentMutation(pu.config, OpCreate)}
		createE.defaults()
		_, specE := createE.createSpec()
		edge.Target.Fields = specE.Fields
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := pu.mutation.AssigneesIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: false,
			Table:   person.AssigneesTable,
			Columns: person.AssigneesPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		createE := &PersonAssignmentCreate{config: pu.config, mutation: newPersonAssignmentMutation(pu.config, OpCreate)}
		createE.defaults()
		_, specE := createE.createSpec()
		edge.Target.Fields = specE.Fields
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if pu.mutation.JobCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   person.JobTable,
			Columns: []string{person.JobColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(term.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := pu.mutation.JobIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   person.JobTable,
			Columns: []string{person.JobColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(term.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if pu.mutation.SourceCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   person.SourceTable,
			Columns: []string{person.SourceColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(term.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := pu.mutation.SourceIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   person.SourceTable,
			Columns: []string{person.SourceColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(term.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if pu.mutation.CreatorCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   person.CreatorTable,
			Columns: []string{person.CreatorColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := pu.mutation.CreatorIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   person.CreatorTable,
			Columns: []string{person.CreatorColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if pu.mutation.ReferredByCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2O,
			Inverse: false,
			Table:   person.ReferredByTable,
			Columns: []string{person.ReferredByColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(personreferral.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := pu.mutation.ReferredByIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2O,
			Inverse: false,
			Table:   person.ReferredByTable,
			Columns: []string{person.ReferredByColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(personreferral.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if pu.mutation.TagPersonCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   person.TagPersonTable,
			Columns: []string{person.TagPersonColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tagperson.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := pu.mutation.RemovedTagPersonIDs(); len(nodes) > 0 && !pu.mutation.TagPersonCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   person.TagPersonTable,
			Columns: []string{person.TagPersonColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tagperson.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := pu.mutation.TagPersonIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   person.TagPersonTable,
			Columns: []string{person.TagPersonColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tagperson.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if pu.mutation.AssignmentCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   person.AssignmentTable,
			Columns: []string{person.AssignmentColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(personassignment.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := pu.mutation.RemovedAssignmentIDs(); len(nodes) > 0 && !pu.mutation.AssignmentCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   person.AssignmentTable,
			Columns: []string{person.AssignmentColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(personassignment.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := pu.mutation.AssignmentIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   person.AssignmentTable,
			Columns: []string{person.AssignmentColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(personassignment.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_spec.AddModifiers(pu.modifiers...)
	if n, err = sqlgraph.UpdateNodes(ctx, pu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{person.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	pu.mutation.done = true
	return n, nil
}

// PersonUpdateOne is the builder for updating a single Person entity.
type PersonUpdateOne struct {
	config
	fields    []string
	hooks     []Hook
	mutation  *PersonMutation
	modifiers []func(*sql.UpdateBuilder)
}

// SetDeletedAt sets the "deleted_at" field.
func (puo *PersonUpdateOne) SetDeletedAt(t time.Time) *PersonUpdateOne {
	puo.mutation.SetDeletedAt(t)
	return puo
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (puo *PersonUpdateOne) SetNillableDeletedAt(t *time.Time) *PersonUpdateOne {
	if t != nil {
		puo.SetDeletedAt(*t)
	}
	return puo
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (puo *PersonUpdateOne) ClearDeletedAt() *PersonUpdateOne {
	puo.mutation.ClearDeletedAt()
	return puo
}

// SetStatus sets the "status" field.
func (puo *PersonUpdateOne) SetStatus(i int8) *PersonUpdateOne {
	puo.mutation.ResetStatus()
	puo.mutation.SetStatus(i)
	return puo
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (puo *PersonUpdateOne) SetNillableStatus(i *int8) *PersonUpdateOne {
	if i != nil {
		puo.SetStatus(*i)
	}
	return puo
}

// AddStatus adds i to the "status" field.
func (puo *PersonUpdateOne) AddStatus(i int8) *PersonUpdateOne {
	puo.mutation.AddStatus(i)
	return puo
}

// SetVersion sets the "version" field.
func (puo *PersonUpdateOne) SetVersion(i int) *PersonUpdateOne {
	puo.mutation.ResetVersion()
	puo.mutation.SetVersion(i)
	return puo
}

// SetNillableVersion sets the "version" field if the given value is not nil.
func (puo *PersonUpdateOne) SetNillableVersion(i *int) *PersonUpdateOne {
	if i != nil {
		puo.SetVersion(*i)
	}
	return puo
}

// AddVersion adds i to the "version" field.
func (puo *PersonUpdateOne) AddVersion(i int) *PersonUpdateOne {
	puo.mutation.AddVersion(i)
	return puo
}

// SetUpdatedAt sets the "updated_at" field.
func (puo *PersonUpdateOne) SetUpdatedAt(t time.Time) *PersonUpdateOne {
	puo.mutation.SetUpdatedAt(t)
	return puo
}

// SetFullName sets the "full_name" field.
func (puo *PersonUpdateOne) SetFullName(s string) *PersonUpdateOne {
	puo.mutation.SetFullName(s)
	return puo
}

// SetNillableFullName sets the "full_name" field if the given value is not nil.
func (puo *PersonUpdateOne) SetNillableFullName(s *string) *PersonUpdateOne {
	if s != nil {
		puo.SetFullName(*s)
	}
	return puo
}

// SetDateOfBirth sets the "date_of_birth" field.
func (puo *PersonUpdateOne) SetDateOfBirth(t time.Time) *PersonUpdateOne {
	puo.mutation.SetDateOfBirth(t)
	return puo
}

// SetNillableDateOfBirth sets the "date_of_birth" field if the given value is not nil.
func (puo *PersonUpdateOne) SetNillableDateOfBirth(t *time.Time) *PersonUpdateOne {
	if t != nil {
		puo.SetDateOfBirth(*t)
	}
	return puo
}

// ClearDateOfBirth clears the value of the "date_of_birth" field.
func (puo *PersonUpdateOne) ClearDateOfBirth() *PersonUpdateOne {
	puo.mutation.ClearDateOfBirth()
	return puo
}

// SetGender sets the "gender" field.
func (puo *PersonUpdateOne) SetGender(s string) *PersonUpdateOne {
	puo.mutation.SetGender(s)
	return puo
}

// SetNillableGender sets the "gender" field if the given value is not nil.
func (puo *PersonUpdateOne) SetNillableGender(s *string) *PersonUpdateOne {
	if s != nil {
		puo.SetGender(*s)
	}
	return puo
}

// ClearGender clears the value of the "gender" field.
func (puo *PersonUpdateOne) ClearGender() *PersonUpdateOne {
	puo.mutation.ClearGender()
	return puo
}

// SetProvinceID sets the "province_id" field.
func (puo *PersonUpdateOne) SetProvinceID(i int) *PersonUpdateOne {
	puo.mutation.ResetProvinceID()
	puo.mutation.SetProvinceID(i)
	return puo
}

// SetNillableProvinceID sets the "province_id" field if the given value is not nil.
func (puo *PersonUpdateOne) SetNillableProvinceID(i *int) *PersonUpdateOne {
	if i != nil {
		puo.SetProvinceID(*i)
	}
	return puo
}

// AddProvinceID adds i to the "province_id" field.
func (puo *PersonUpdateOne) AddProvinceID(i int) *PersonUpdateOne {
	puo.mutation.AddProvinceID(i)
	return puo
}

// ClearProvinceID clears the value of the "province_id" field.
func (puo *PersonUpdateOne) ClearProvinceID() *PersonUpdateOne {
	puo.mutation.ClearProvinceID()
	return puo
}

// SetDistrictID sets the "district_id" field.
func (puo *PersonUpdateOne) SetDistrictID(i int) *PersonUpdateOne {
	puo.mutation.ResetDistrictID()
	puo.mutation.SetDistrictID(i)
	return puo
}

// SetNillableDistrictID sets the "district_id" field if the given value is not nil.
func (puo *PersonUpdateOne) SetNillableDistrictID(i *int) *PersonUpdateOne {
	if i != nil {
		puo.SetDistrictID(*i)
	}
	return puo
}

// AddDistrictID adds i to the "district_id" field.
func (puo *PersonUpdateOne) AddDistrictID(i int) *PersonUpdateOne {
	puo.mutation.AddDistrictID(i)
	return puo
}

// ClearDistrictID clears the value of the "district_id" field.
func (puo *PersonUpdateOne) ClearDistrictID() *PersonUpdateOne {
	puo.mutation.ClearDistrictID()
	return puo
}

// SetWardID sets the "ward_id" field.
func (puo *PersonUpdateOne) SetWardID(i int) *PersonUpdateOne {
	puo.mutation.ResetWardID()
	puo.mutation.SetWardID(i)
	return puo
}

// SetNillableWardID sets the "ward_id" field if the given value is not nil.
func (puo *PersonUpdateOne) SetNillableWardID(i *int) *PersonUpdateOne {
	if i != nil {
		puo.SetWardID(*i)
	}
	return puo
}

// AddWardID adds i to the "ward_id" field.
func (puo *PersonUpdateOne) AddWardID(i int) *PersonUpdateOne {
	puo.mutation.AddWardID(i)
	return puo
}

// ClearWardID clears the value of the "ward_id" field.
func (puo *PersonUpdateOne) ClearWardID() *PersonUpdateOne {
	puo.mutation.ClearWardID()
	return puo
}

// SetAddressNumber sets the "address_number" field.
func (puo *PersonUpdateOne) SetAddressNumber(s string) *PersonUpdateOne {
	puo.mutation.SetAddressNumber(s)
	return puo
}

// SetNillableAddressNumber sets the "address_number" field if the given value is not nil.
func (puo *PersonUpdateOne) SetNillableAddressNumber(s *string) *PersonUpdateOne {
	if s != nil {
		puo.SetAddressNumber(*s)
	}
	return puo
}

// ClearAddressNumber clears the value of the "address_number" field.
func (puo *PersonUpdateOne) ClearAddressNumber() *PersonUpdateOne {
	puo.mutation.ClearAddressNumber()
	return puo
}

// SetPhone sets the "phone" field.
func (puo *PersonUpdateOne) SetPhone(s string) *PersonUpdateOne {
	puo.mutation.SetPhone(s)
	return puo
}

// SetNillablePhone sets the "phone" field if the given value is not nil.
func (puo *PersonUpdateOne) SetNillablePhone(s *string) *PersonUpdateOne {
	if s != nil {
		puo.SetPhone(*s)
	}
	return puo
}

// SetEmail sets the "email" field.
func (puo *PersonUpdateOne) SetEmail(s string) *PersonUpdateOne {
	puo.mutation.SetEmail(s)
	return puo
}

// SetNillableEmail sets the "email" field if the given value is not nil.
func (puo *PersonUpdateOne) SetNillableEmail(s *string) *PersonUpdateOne {
	if s != nil {
		puo.SetEmail(*s)
	}
	return puo
}

// ClearEmail clears the value of the "email" field.
func (puo *PersonUpdateOne) ClearEmail() *PersonUpdateOne {
	puo.mutation.ClearEmail()
	return puo
}

// SetJobID sets the "job_id" field.
func (puo *PersonUpdateOne) SetJobID(i int) *PersonUpdateOne {
	puo.mutation.SetJobID(i)
	return puo
}

// SetNillableJobID sets the "job_id" field if the given value is not nil.
func (puo *PersonUpdateOne) SetNillableJobID(i *int) *PersonUpdateOne {
	if i != nil {
		puo.SetJobID(*i)
	}
	return puo
}

// ClearJobID clears the value of the "job_id" field.
func (puo *PersonUpdateOne) ClearJobID() *PersonUpdateOne {
	puo.mutation.ClearJobID()
	return puo
}

// SetUserID sets the "user_id" field.
func (puo *PersonUpdateOne) SetUserID(i int) *PersonUpdateOne {
	puo.mutation.SetUserID(i)
	return puo
}

// SetNillableUserID sets the "user_id" field if the given value is not nil.
func (puo *PersonUpdateOne) SetNillableUserID(i *int) *PersonUpdateOne {
	if i != nil {
		puo.SetUserID(*i)
	}
	return puo
}

// ClearUserID clears the value of the "user_id" field.
func (puo *PersonUpdateOne) ClearUserID() *PersonUpdateOne {
	puo.mutation.ClearUserID()
	return puo
}

// SetSourceID sets the "source_id" field.
func (puo *PersonUpdateOne) SetSourceID(i int) *PersonUpdateOne {
	puo.mutation.SetSourceID(i)
	return puo
}

// SetNillableSourceID sets the "source_id" field if the given value is not nil.
func (puo *PersonUpdateOne) SetNillableSourceID(i *int) *PersonUpdateOne {
	if i != nil {
		puo.SetSourceID(*i)
	}
	return puo
}

// ClearSourceID clears the value of the "source_id" field.
func (puo *PersonUpdateOne) ClearSourceID() *PersonUpdateOne {
	puo.mutation.ClearSourceID()
	return puo
}

// SetPhoneConfirm sets the "phone_confirm" field.
func (puo *PersonUpdateOne) SetPhoneConfirm(b bool) *PersonUpdateOne {
	puo.mutation.SetPhoneConfirm(b)
	return puo
}

// SetNillablePhoneConfirm sets the "phone_confirm" field if the given value is not nil.
func (puo *PersonUpdateOne) SetNillablePhoneConfirm(b *bool) *PersonUpdateOne {
	if b != nil {
		puo.SetPhoneConfirm(*b)
	}
	return puo
}

// ClearPhoneConfirm clears the value of the "phone_confirm" field.
func (puo *PersonUpdateOne) ClearPhoneConfirm() *PersonUpdateOne {
	puo.mutation.ClearPhoneConfirm()
	return puo
}

// SetMailConfirm sets the "mail_confirm" field.
func (puo *PersonUpdateOne) SetMailConfirm(b bool) *PersonUpdateOne {
	puo.mutation.SetMailConfirm(b)
	return puo
}

// SetNillableMailConfirm sets the "mail_confirm" field if the given value is not nil.
func (puo *PersonUpdateOne) SetNillableMailConfirm(b *bool) *PersonUpdateOne {
	if b != nil {
		puo.SetMailConfirm(*b)
	}
	return puo
}

// ClearMailConfirm clears the value of the "mail_confirm" field.
func (puo *PersonUpdateOne) ClearMailConfirm() *PersonUpdateOne {
	puo.mutation.ClearMailConfirm()
	return puo
}

// SetPersonField sets the "person_field" field.
func (puo *PersonUpdateOne) SetPersonField(tm *types.PersonMeta) *PersonUpdateOne {
	puo.mutation.SetPersonField(tm)
	return puo
}

// ClearPersonField clears the value of the "person_field" field.
func (puo *PersonUpdateOne) ClearPersonField() *PersonUpdateOne {
	puo.mutation.ClearPersonField()
	return puo
}

// AddDealIDs adds the "deals" edge to the Deal entity by IDs.
func (puo *PersonUpdateOne) AddDealIDs(ids ...int) *PersonUpdateOne {
	puo.mutation.AddDealIDs(ids...)
	return puo
}

// AddDeals adds the "deals" edges to the Deal entity.
func (puo *PersonUpdateOne) AddDeals(d ...*Deal) *PersonUpdateOne {
	ids := make([]int, len(d))
	for i := range d {
		ids[i] = d[i].ID
	}
	return puo.AddDealIDs(ids...)
}

// AddTagIDs adds the "tags" edge to the Tag entity by IDs.
func (puo *PersonUpdateOne) AddTagIDs(ids ...int) *PersonUpdateOne {
	puo.mutation.AddTagIDs(ids...)
	return puo
}

// AddTags adds the "tags" edges to the Tag entity.
func (puo *PersonUpdateOne) AddTags(t ...*Tag) *PersonUpdateOne {
	ids := make([]int, len(t))
	for i := range t {
		ids[i] = t[i].ID
	}
	return puo.AddTagIDs(ids...)
}

// AddFormSubmissionIDs adds the "form_submissions" edge to the FormSubmission entity by IDs.
func (puo *PersonUpdateOne) AddFormSubmissionIDs(ids ...int) *PersonUpdateOne {
	puo.mutation.AddFormSubmissionIDs(ids...)
	return puo
}

// AddFormSubmissions adds the "form_submissions" edges to the FormSubmission entity.
func (puo *PersonUpdateOne) AddFormSubmissions(f ...*FormSubmission) *PersonUpdateOne {
	ids := make([]int, len(f))
	for i := range f {
		ids[i] = f[i].ID
	}
	return puo.AddFormSubmissionIDs(ids...)
}

// AddPlanIDs adds the "plans" edge to the InstallmentPlan entity by IDs.
func (puo *PersonUpdateOne) AddPlanIDs(ids ...int) *PersonUpdateOne {
	puo.mutation.AddPlanIDs(ids...)
	return puo
}

// AddPlans adds the "plans" edges to the InstallmentPlan entity.
func (puo *PersonUpdateOne) AddPlans(i ...*InstallmentPlan) *PersonUpdateOne {
	ids := make([]int, len(i))
	for j := range i {
		ids[j] = i[j].ID
	}
	return puo.AddPlanIDs(ids...)
}

// AddCallIDs adds the "calls" edge to the Call entity by IDs.
func (puo *PersonUpdateOne) AddCallIDs(ids ...int) *PersonUpdateOne {
	puo.mutation.AddCallIDs(ids...)
	return puo
}

// AddCalls adds the "calls" edges to the Call entity.
func (puo *PersonUpdateOne) AddCalls(c ...*Call) *PersonUpdateOne {
	ids := make([]int, len(c))
	for i := range c {
		ids[i] = c[i].ID
	}
	return puo.AddCallIDs(ids...)
}

// AddTaskIDs adds the "tasks" edge to the Task entity by IDs.
func (puo *PersonUpdateOne) AddTaskIDs(ids ...int) *PersonUpdateOne {
	puo.mutation.AddTaskIDs(ids...)
	return puo
}

// AddTasks adds the "tasks" edges to the Task entity.
func (puo *PersonUpdateOne) AddTasks(t ...*Task) *PersonUpdateOne {
	ids := make([]int, len(t))
	for i := range t {
		ids[i] = t[i].ID
	}
	return puo.AddTaskIDs(ids...)
}

// AddIssueIDs adds the "issues" edge to the Issue entity by IDs.
func (puo *PersonUpdateOne) AddIssueIDs(ids ...int) *PersonUpdateOne {
	puo.mutation.AddIssueIDs(ids...)
	return puo
}

// AddIssues adds the "issues" edges to the Issue entity.
func (puo *PersonUpdateOne) AddIssues(i ...*Issue) *PersonUpdateOne {
	ids := make([]int, len(i))
	for j := range i {
		ids[j] = i[j].ID
	}
	return puo.AddIssueIDs(ids...)
}

// AddAppointmentIDs adds the "appointments" edge to the Appointment entity by IDs.
func (puo *PersonUpdateOne) AddAppointmentIDs(ids ...int) *PersonUpdateOne {
	puo.mutation.AddAppointmentIDs(ids...)
	return puo
}

// AddAppointments adds the "appointments" edges to the Appointment entity.
func (puo *PersonUpdateOne) AddAppointments(a ...*Appointment) *PersonUpdateOne {
	ids := make([]int, len(a))
	for i := range a {
		ids[i] = a[i].ID
	}
	return puo.AddAppointmentIDs(ids...)
}

// AddTrackIDs adds the "tracks" edge to the Track entity by IDs.
func (puo *PersonUpdateOne) AddTrackIDs(ids ...int) *PersonUpdateOne {
	puo.mutation.AddTrackIDs(ids...)
	return puo
}

// AddTracks adds the "tracks" edges to the Track entity.
func (puo *PersonUpdateOne) AddTracks(t ...*Track) *PersonUpdateOne {
	ids := make([]int, len(t))
	for i := range t {
		ids[i] = t[i].ID
	}
	return puo.AddTrackIDs(ids...)
}

// AddMessageHistoryIDs adds the "message_histories" edge to the MessageHistory entity by IDs.
func (puo *PersonUpdateOne) AddMessageHistoryIDs(ids ...int) *PersonUpdateOne {
	puo.mutation.AddMessageHistoryIDs(ids...)
	return puo
}

// AddMessageHistories adds the "message_histories" edges to the MessageHistory entity.
func (puo *PersonUpdateOne) AddMessageHistories(m ...*MessageHistory) *PersonUpdateOne {
	ids := make([]int, len(m))
	for i := range m {
		ids[i] = m[i].ID
	}
	return puo.AddMessageHistoryIDs(ids...)
}

// AddAssigneeIDs adds the "assignees" edge to the User entity by IDs.
func (puo *PersonUpdateOne) AddAssigneeIDs(ids ...int) *PersonUpdateOne {
	puo.mutation.AddAssigneeIDs(ids...)
	return puo
}

// AddAssignees adds the "assignees" edges to the User entity.
func (puo *PersonUpdateOne) AddAssignees(u ...*User) *PersonUpdateOne {
	ids := make([]int, len(u))
	for i := range u {
		ids[i] = u[i].ID
	}
	return puo.AddAssigneeIDs(ids...)
}

// SetJob sets the "job" edge to the Term entity.
func (puo *PersonUpdateOne) SetJob(t *Term) *PersonUpdateOne {
	return puo.SetJobID(t.ID)
}

// SetSource sets the "source" edge to the Term entity.
func (puo *PersonUpdateOne) SetSource(t *Term) *PersonUpdateOne {
	return puo.SetSourceID(t.ID)
}

// SetCreatorID sets the "creator" edge to the User entity by ID.
func (puo *PersonUpdateOne) SetCreatorID(id int) *PersonUpdateOne {
	puo.mutation.SetCreatorID(id)
	return puo
}

// SetNillableCreatorID sets the "creator" edge to the User entity by ID if the given value is not nil.
func (puo *PersonUpdateOne) SetNillableCreatorID(id *int) *PersonUpdateOne {
	if id != nil {
		puo = puo.SetCreatorID(*id)
	}
	return puo
}

// SetCreator sets the "creator" edge to the User entity.
func (puo *PersonUpdateOne) SetCreator(u *User) *PersonUpdateOne {
	return puo.SetCreatorID(u.ID)
}

// SetReferredByID sets the "referred_by" edge to the PersonReferral entity by ID.
func (puo *PersonUpdateOne) SetReferredByID(id int) *PersonUpdateOne {
	puo.mutation.SetReferredByID(id)
	return puo
}

// SetNillableReferredByID sets the "referred_by" edge to the PersonReferral entity by ID if the given value is not nil.
func (puo *PersonUpdateOne) SetNillableReferredByID(id *int) *PersonUpdateOne {
	if id != nil {
		puo = puo.SetReferredByID(*id)
	}
	return puo
}

// SetReferredBy sets the "referred_by" edge to the PersonReferral entity.
func (puo *PersonUpdateOne) SetReferredBy(p *PersonReferral) *PersonUpdateOne {
	return puo.SetReferredByID(p.ID)
}

// AddTagPersonIDs adds the "tag_person" edge to the TagPerson entity by IDs.
func (puo *PersonUpdateOne) AddTagPersonIDs(ids ...int) *PersonUpdateOne {
	puo.mutation.AddTagPersonIDs(ids...)
	return puo
}

// AddTagPerson adds the "tag_person" edges to the TagPerson entity.
func (puo *PersonUpdateOne) AddTagPerson(t ...*TagPerson) *PersonUpdateOne {
	ids := make([]int, len(t))
	for i := range t {
		ids[i] = t[i].ID
	}
	return puo.AddTagPersonIDs(ids...)
}

// AddAssignmentIDs adds the "assignment" edge to the PersonAssignment entity by IDs.
func (puo *PersonUpdateOne) AddAssignmentIDs(ids ...int) *PersonUpdateOne {
	puo.mutation.AddAssignmentIDs(ids...)
	return puo
}

// AddAssignment adds the "assignment" edges to the PersonAssignment entity.
func (puo *PersonUpdateOne) AddAssignment(p ...*PersonAssignment) *PersonUpdateOne {
	ids := make([]int, len(p))
	for i := range p {
		ids[i] = p[i].ID
	}
	return puo.AddAssignmentIDs(ids...)
}

// Mutation returns the PersonMutation object of the builder.
func (puo *PersonUpdateOne) Mutation() *PersonMutation {
	return puo.mutation
}

// ClearDeals clears all "deals" edges to the Deal entity.
func (puo *PersonUpdateOne) ClearDeals() *PersonUpdateOne {
	puo.mutation.ClearDeals()
	return puo
}

// RemoveDealIDs removes the "deals" edge to Deal entities by IDs.
func (puo *PersonUpdateOne) RemoveDealIDs(ids ...int) *PersonUpdateOne {
	puo.mutation.RemoveDealIDs(ids...)
	return puo
}

// RemoveDeals removes "deals" edges to Deal entities.
func (puo *PersonUpdateOne) RemoveDeals(d ...*Deal) *PersonUpdateOne {
	ids := make([]int, len(d))
	for i := range d {
		ids[i] = d[i].ID
	}
	return puo.RemoveDealIDs(ids...)
}

// ClearTags clears all "tags" edges to the Tag entity.
func (puo *PersonUpdateOne) ClearTags() *PersonUpdateOne {
	puo.mutation.ClearTags()
	return puo
}

// RemoveTagIDs removes the "tags" edge to Tag entities by IDs.
func (puo *PersonUpdateOne) RemoveTagIDs(ids ...int) *PersonUpdateOne {
	puo.mutation.RemoveTagIDs(ids...)
	return puo
}

// RemoveTags removes "tags" edges to Tag entities.
func (puo *PersonUpdateOne) RemoveTags(t ...*Tag) *PersonUpdateOne {
	ids := make([]int, len(t))
	for i := range t {
		ids[i] = t[i].ID
	}
	return puo.RemoveTagIDs(ids...)
}

// ClearFormSubmissions clears all "form_submissions" edges to the FormSubmission entity.
func (puo *PersonUpdateOne) ClearFormSubmissions() *PersonUpdateOne {
	puo.mutation.ClearFormSubmissions()
	return puo
}

// RemoveFormSubmissionIDs removes the "form_submissions" edge to FormSubmission entities by IDs.
func (puo *PersonUpdateOne) RemoveFormSubmissionIDs(ids ...int) *PersonUpdateOne {
	puo.mutation.RemoveFormSubmissionIDs(ids...)
	return puo
}

// RemoveFormSubmissions removes "form_submissions" edges to FormSubmission entities.
func (puo *PersonUpdateOne) RemoveFormSubmissions(f ...*FormSubmission) *PersonUpdateOne {
	ids := make([]int, len(f))
	for i := range f {
		ids[i] = f[i].ID
	}
	return puo.RemoveFormSubmissionIDs(ids...)
}

// ClearPlans clears all "plans" edges to the InstallmentPlan entity.
func (puo *PersonUpdateOne) ClearPlans() *PersonUpdateOne {
	puo.mutation.ClearPlans()
	return puo
}

// RemovePlanIDs removes the "plans" edge to InstallmentPlan entities by IDs.
func (puo *PersonUpdateOne) RemovePlanIDs(ids ...int) *PersonUpdateOne {
	puo.mutation.RemovePlanIDs(ids...)
	return puo
}

// RemovePlans removes "plans" edges to InstallmentPlan entities.
func (puo *PersonUpdateOne) RemovePlans(i ...*InstallmentPlan) *PersonUpdateOne {
	ids := make([]int, len(i))
	for j := range i {
		ids[j] = i[j].ID
	}
	return puo.RemovePlanIDs(ids...)
}

// ClearCalls clears all "calls" edges to the Call entity.
func (puo *PersonUpdateOne) ClearCalls() *PersonUpdateOne {
	puo.mutation.ClearCalls()
	return puo
}

// RemoveCallIDs removes the "calls" edge to Call entities by IDs.
func (puo *PersonUpdateOne) RemoveCallIDs(ids ...int) *PersonUpdateOne {
	puo.mutation.RemoveCallIDs(ids...)
	return puo
}

// RemoveCalls removes "calls" edges to Call entities.
func (puo *PersonUpdateOne) RemoveCalls(c ...*Call) *PersonUpdateOne {
	ids := make([]int, len(c))
	for i := range c {
		ids[i] = c[i].ID
	}
	return puo.RemoveCallIDs(ids...)
}

// ClearTasks clears all "tasks" edges to the Task entity.
func (puo *PersonUpdateOne) ClearTasks() *PersonUpdateOne {
	puo.mutation.ClearTasks()
	return puo
}

// RemoveTaskIDs removes the "tasks" edge to Task entities by IDs.
func (puo *PersonUpdateOne) RemoveTaskIDs(ids ...int) *PersonUpdateOne {
	puo.mutation.RemoveTaskIDs(ids...)
	return puo
}

// RemoveTasks removes "tasks" edges to Task entities.
func (puo *PersonUpdateOne) RemoveTasks(t ...*Task) *PersonUpdateOne {
	ids := make([]int, len(t))
	for i := range t {
		ids[i] = t[i].ID
	}
	return puo.RemoveTaskIDs(ids...)
}

// ClearIssues clears all "issues" edges to the Issue entity.
func (puo *PersonUpdateOne) ClearIssues() *PersonUpdateOne {
	puo.mutation.ClearIssues()
	return puo
}

// RemoveIssueIDs removes the "issues" edge to Issue entities by IDs.
func (puo *PersonUpdateOne) RemoveIssueIDs(ids ...int) *PersonUpdateOne {
	puo.mutation.RemoveIssueIDs(ids...)
	return puo
}

// RemoveIssues removes "issues" edges to Issue entities.
func (puo *PersonUpdateOne) RemoveIssues(i ...*Issue) *PersonUpdateOne {
	ids := make([]int, len(i))
	for j := range i {
		ids[j] = i[j].ID
	}
	return puo.RemoveIssueIDs(ids...)
}

// ClearAppointments clears all "appointments" edges to the Appointment entity.
func (puo *PersonUpdateOne) ClearAppointments() *PersonUpdateOne {
	puo.mutation.ClearAppointments()
	return puo
}

// RemoveAppointmentIDs removes the "appointments" edge to Appointment entities by IDs.
func (puo *PersonUpdateOne) RemoveAppointmentIDs(ids ...int) *PersonUpdateOne {
	puo.mutation.RemoveAppointmentIDs(ids...)
	return puo
}

// RemoveAppointments removes "appointments" edges to Appointment entities.
func (puo *PersonUpdateOne) RemoveAppointments(a ...*Appointment) *PersonUpdateOne {
	ids := make([]int, len(a))
	for i := range a {
		ids[i] = a[i].ID
	}
	return puo.RemoveAppointmentIDs(ids...)
}

// ClearTracks clears all "tracks" edges to the Track entity.
func (puo *PersonUpdateOne) ClearTracks() *PersonUpdateOne {
	puo.mutation.ClearTracks()
	return puo
}

// RemoveTrackIDs removes the "tracks" edge to Track entities by IDs.
func (puo *PersonUpdateOne) RemoveTrackIDs(ids ...int) *PersonUpdateOne {
	puo.mutation.RemoveTrackIDs(ids...)
	return puo
}

// RemoveTracks removes "tracks" edges to Track entities.
func (puo *PersonUpdateOne) RemoveTracks(t ...*Track) *PersonUpdateOne {
	ids := make([]int, len(t))
	for i := range t {
		ids[i] = t[i].ID
	}
	return puo.RemoveTrackIDs(ids...)
}

// ClearMessageHistories clears all "message_histories" edges to the MessageHistory entity.
func (puo *PersonUpdateOne) ClearMessageHistories() *PersonUpdateOne {
	puo.mutation.ClearMessageHistories()
	return puo
}

// RemoveMessageHistoryIDs removes the "message_histories" edge to MessageHistory entities by IDs.
func (puo *PersonUpdateOne) RemoveMessageHistoryIDs(ids ...int) *PersonUpdateOne {
	puo.mutation.RemoveMessageHistoryIDs(ids...)
	return puo
}

// RemoveMessageHistories removes "message_histories" edges to MessageHistory entities.
func (puo *PersonUpdateOne) RemoveMessageHistories(m ...*MessageHistory) *PersonUpdateOne {
	ids := make([]int, len(m))
	for i := range m {
		ids[i] = m[i].ID
	}
	return puo.RemoveMessageHistoryIDs(ids...)
}

// ClearAssignees clears all "assignees" edges to the User entity.
func (puo *PersonUpdateOne) ClearAssignees() *PersonUpdateOne {
	puo.mutation.ClearAssignees()
	return puo
}

// RemoveAssigneeIDs removes the "assignees" edge to User entities by IDs.
func (puo *PersonUpdateOne) RemoveAssigneeIDs(ids ...int) *PersonUpdateOne {
	puo.mutation.RemoveAssigneeIDs(ids...)
	return puo
}

// RemoveAssignees removes "assignees" edges to User entities.
func (puo *PersonUpdateOne) RemoveAssignees(u ...*User) *PersonUpdateOne {
	ids := make([]int, len(u))
	for i := range u {
		ids[i] = u[i].ID
	}
	return puo.RemoveAssigneeIDs(ids...)
}

// ClearJob clears the "job" edge to the Term entity.
func (puo *PersonUpdateOne) ClearJob() *PersonUpdateOne {
	puo.mutation.ClearJob()
	return puo
}

// ClearSource clears the "source" edge to the Term entity.
func (puo *PersonUpdateOne) ClearSource() *PersonUpdateOne {
	puo.mutation.ClearSource()
	return puo
}

// ClearCreator clears the "creator" edge to the User entity.
func (puo *PersonUpdateOne) ClearCreator() *PersonUpdateOne {
	puo.mutation.ClearCreator()
	return puo
}

// ClearReferredBy clears the "referred_by" edge to the PersonReferral entity.
func (puo *PersonUpdateOne) ClearReferredBy() *PersonUpdateOne {
	puo.mutation.ClearReferredBy()
	return puo
}

// ClearTagPerson clears all "tag_person" edges to the TagPerson entity.
func (puo *PersonUpdateOne) ClearTagPerson() *PersonUpdateOne {
	puo.mutation.ClearTagPerson()
	return puo
}

// RemoveTagPersonIDs removes the "tag_person" edge to TagPerson entities by IDs.
func (puo *PersonUpdateOne) RemoveTagPersonIDs(ids ...int) *PersonUpdateOne {
	puo.mutation.RemoveTagPersonIDs(ids...)
	return puo
}

// RemoveTagPerson removes "tag_person" edges to TagPerson entities.
func (puo *PersonUpdateOne) RemoveTagPerson(t ...*TagPerson) *PersonUpdateOne {
	ids := make([]int, len(t))
	for i := range t {
		ids[i] = t[i].ID
	}
	return puo.RemoveTagPersonIDs(ids...)
}

// ClearAssignment clears all "assignment" edges to the PersonAssignment entity.
func (puo *PersonUpdateOne) ClearAssignment() *PersonUpdateOne {
	puo.mutation.ClearAssignment()
	return puo
}

// RemoveAssignmentIDs removes the "assignment" edge to PersonAssignment entities by IDs.
func (puo *PersonUpdateOne) RemoveAssignmentIDs(ids ...int) *PersonUpdateOne {
	puo.mutation.RemoveAssignmentIDs(ids...)
	return puo
}

// RemoveAssignment removes "assignment" edges to PersonAssignment entities.
func (puo *PersonUpdateOne) RemoveAssignment(p ...*PersonAssignment) *PersonUpdateOne {
	ids := make([]int, len(p))
	for i := range p {
		ids[i] = p[i].ID
	}
	return puo.RemoveAssignmentIDs(ids...)
}

// Where appends a list predicates to the PersonUpdate builder.
func (puo *PersonUpdateOne) Where(ps ...predicate.Person) *PersonUpdateOne {
	puo.mutation.Where(ps...)
	return puo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (puo *PersonUpdateOne) Select(field string, fields ...string) *PersonUpdateOne {
	puo.fields = append([]string{field}, fields...)
	return puo
}

// Save executes the query and returns the updated Person entity.
func (puo *PersonUpdateOne) Save(ctx context.Context) (*Person, error) {
	if err := puo.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, puo.sqlSave, puo.mutation, puo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (puo *PersonUpdateOne) SaveX(ctx context.Context) *Person {
	node, err := puo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (puo *PersonUpdateOne) Exec(ctx context.Context) error {
	_, err := puo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (puo *PersonUpdateOne) ExecX(ctx context.Context) {
	if err := puo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (puo *PersonUpdateOne) defaults() error {
	if _, ok := puo.mutation.UpdatedAt(); !ok {
		if person.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized person.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := person.UpdateDefaultUpdatedAt()
		puo.mutation.SetUpdatedAt(v)
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (puo *PersonUpdateOne) Modify(modifiers ...func(u *sql.UpdateBuilder)) *PersonUpdateOne {
	puo.modifiers = append(puo.modifiers, modifiers...)
	return puo
}

func (puo *PersonUpdateOne) sqlSave(ctx context.Context) (_node *Person, err error) {
	_spec := sqlgraph.NewUpdateSpec(person.Table, person.Columns, sqlgraph.NewFieldSpec(person.FieldID, field.TypeInt))
	id, ok := puo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "Person.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := puo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, person.FieldID)
		for _, f := range fields {
			if !person.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != person.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := puo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := puo.mutation.DeletedAt(); ok {
		_spec.SetField(person.FieldDeletedAt, field.TypeTime, value)
	}
	if puo.mutation.DeletedAtCleared() {
		_spec.ClearField(person.FieldDeletedAt, field.TypeTime)
	}
	if value, ok := puo.mutation.Status(); ok {
		_spec.SetField(person.FieldStatus, field.TypeInt8, value)
	}
	if value, ok := puo.mutation.AddedStatus(); ok {
		_spec.AddField(person.FieldStatus, field.TypeInt8, value)
	}
	if value, ok := puo.mutation.Version(); ok {
		_spec.SetField(person.FieldVersion, field.TypeInt, value)
	}
	if value, ok := puo.mutation.AddedVersion(); ok {
		_spec.AddField(person.FieldVersion, field.TypeInt, value)
	}
	if value, ok := puo.mutation.UpdatedAt(); ok {
		_spec.SetField(person.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := puo.mutation.FullName(); ok {
		_spec.SetField(person.FieldFullName, field.TypeString, value)
	}
	if value, ok := puo.mutation.DateOfBirth(); ok {
		_spec.SetField(person.FieldDateOfBirth, field.TypeTime, value)
	}
	if puo.mutation.DateOfBirthCleared() {
		_spec.ClearField(person.FieldDateOfBirth, field.TypeTime)
	}
	if value, ok := puo.mutation.Gender(); ok {
		_spec.SetField(person.FieldGender, field.TypeString, value)
	}
	if puo.mutation.GenderCleared() {
		_spec.ClearField(person.FieldGender, field.TypeString)
	}
	if value, ok := puo.mutation.ProvinceID(); ok {
		_spec.SetField(person.FieldProvinceID, field.TypeInt, value)
	}
	if value, ok := puo.mutation.AddedProvinceID(); ok {
		_spec.AddField(person.FieldProvinceID, field.TypeInt, value)
	}
	if puo.mutation.ProvinceIDCleared() {
		_spec.ClearField(person.FieldProvinceID, field.TypeInt)
	}
	if value, ok := puo.mutation.DistrictID(); ok {
		_spec.SetField(person.FieldDistrictID, field.TypeInt, value)
	}
	if value, ok := puo.mutation.AddedDistrictID(); ok {
		_spec.AddField(person.FieldDistrictID, field.TypeInt, value)
	}
	if puo.mutation.DistrictIDCleared() {
		_spec.ClearField(person.FieldDistrictID, field.TypeInt)
	}
	if value, ok := puo.mutation.WardID(); ok {
		_spec.SetField(person.FieldWardID, field.TypeInt, value)
	}
	if value, ok := puo.mutation.AddedWardID(); ok {
		_spec.AddField(person.FieldWardID, field.TypeInt, value)
	}
	if puo.mutation.WardIDCleared() {
		_spec.ClearField(person.FieldWardID, field.TypeInt)
	}
	if value, ok := puo.mutation.AddressNumber(); ok {
		_spec.SetField(person.FieldAddressNumber, field.TypeString, value)
	}
	if puo.mutation.AddressNumberCleared() {
		_spec.ClearField(person.FieldAddressNumber, field.TypeString)
	}
	if value, ok := puo.mutation.Phone(); ok {
		_spec.SetField(person.FieldPhone, field.TypeString, value)
	}
	if value, ok := puo.mutation.Email(); ok {
		_spec.SetField(person.FieldEmail, field.TypeString, value)
	}
	if puo.mutation.EmailCleared() {
		_spec.ClearField(person.FieldEmail, field.TypeString)
	}
	if value, ok := puo.mutation.PhoneConfirm(); ok {
		_spec.SetField(person.FieldPhoneConfirm, field.TypeBool, value)
	}
	if puo.mutation.PhoneConfirmCleared() {
		_spec.ClearField(person.FieldPhoneConfirm, field.TypeBool)
	}
	if value, ok := puo.mutation.MailConfirm(); ok {
		_spec.SetField(person.FieldMailConfirm, field.TypeBool, value)
	}
	if puo.mutation.MailConfirmCleared() {
		_spec.ClearField(person.FieldMailConfirm, field.TypeBool)
	}
	if value, ok := puo.mutation.PersonField(); ok {
		_spec.SetField(person.FieldPersonField, field.TypeJSON, value)
	}
	if puo.mutation.PersonFieldCleared() {
		_spec.ClearField(person.FieldPersonField, field.TypeJSON)
	}
	if puo.mutation.DealsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   person.DealsTable,
			Columns: []string{person.DealsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(deal.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := puo.mutation.RemovedDealsIDs(); len(nodes) > 0 && !puo.mutation.DealsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   person.DealsTable,
			Columns: []string{person.DealsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(deal.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := puo.mutation.DealsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   person.DealsTable,
			Columns: []string{person.DealsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(deal.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if puo.mutation.TagsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: false,
			Table:   person.TagsTable,
			Columns: person.TagsPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tag.FieldID, field.TypeInt),
			},
		}
		createE := &TagPersonCreate{config: puo.config, mutation: newTagPersonMutation(puo.config, OpCreate)}
		createE.defaults()
		_, specE := createE.createSpec()
		edge.Target.Fields = specE.Fields
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := puo.mutation.RemovedTagsIDs(); len(nodes) > 0 && !puo.mutation.TagsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: false,
			Table:   person.TagsTable,
			Columns: person.TagsPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tag.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		createE := &TagPersonCreate{config: puo.config, mutation: newTagPersonMutation(puo.config, OpCreate)}
		createE.defaults()
		_, specE := createE.createSpec()
		edge.Target.Fields = specE.Fields
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := puo.mutation.TagsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: false,
			Table:   person.TagsTable,
			Columns: person.TagsPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tag.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		createE := &TagPersonCreate{config: puo.config, mutation: newTagPersonMutation(puo.config, OpCreate)}
		createE.defaults()
		_, specE := createE.createSpec()
		edge.Target.Fields = specE.Fields
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if puo.mutation.FormSubmissionsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   person.FormSubmissionsTable,
			Columns: []string{person.FormSubmissionsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(formsubmission.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := puo.mutation.RemovedFormSubmissionsIDs(); len(nodes) > 0 && !puo.mutation.FormSubmissionsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   person.FormSubmissionsTable,
			Columns: []string{person.FormSubmissionsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(formsubmission.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := puo.mutation.FormSubmissionsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   person.FormSubmissionsTable,
			Columns: []string{person.FormSubmissionsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(formsubmission.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if puo.mutation.PlansCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   person.PlansTable,
			Columns: []string{person.PlansColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(installmentplan.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := puo.mutation.RemovedPlansIDs(); len(nodes) > 0 && !puo.mutation.PlansCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   person.PlansTable,
			Columns: []string{person.PlansColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(installmentplan.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := puo.mutation.PlansIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   person.PlansTable,
			Columns: []string{person.PlansColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(installmentplan.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if puo.mutation.CallsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   person.CallsTable,
			Columns: []string{person.CallsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(call.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := puo.mutation.RemovedCallsIDs(); len(nodes) > 0 && !puo.mutation.CallsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   person.CallsTable,
			Columns: []string{person.CallsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(call.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := puo.mutation.CallsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   person.CallsTable,
			Columns: []string{person.CallsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(call.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if puo.mutation.TasksCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   person.TasksTable,
			Columns: []string{person.TasksColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(task.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := puo.mutation.RemovedTasksIDs(); len(nodes) > 0 && !puo.mutation.TasksCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   person.TasksTable,
			Columns: []string{person.TasksColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(task.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := puo.mutation.TasksIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   person.TasksTable,
			Columns: []string{person.TasksColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(task.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if puo.mutation.IssuesCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   person.IssuesTable,
			Columns: []string{person.IssuesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(issue.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := puo.mutation.RemovedIssuesIDs(); len(nodes) > 0 && !puo.mutation.IssuesCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   person.IssuesTable,
			Columns: []string{person.IssuesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(issue.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := puo.mutation.IssuesIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   person.IssuesTable,
			Columns: []string{person.IssuesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(issue.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if puo.mutation.AppointmentsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   person.AppointmentsTable,
			Columns: []string{person.AppointmentsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(appointment.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := puo.mutation.RemovedAppointmentsIDs(); len(nodes) > 0 && !puo.mutation.AppointmentsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   person.AppointmentsTable,
			Columns: []string{person.AppointmentsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(appointment.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := puo.mutation.AppointmentsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   person.AppointmentsTable,
			Columns: []string{person.AppointmentsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(appointment.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if puo.mutation.TracksCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   person.TracksTable,
			Columns: []string{person.TracksColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(track.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := puo.mutation.RemovedTracksIDs(); len(nodes) > 0 && !puo.mutation.TracksCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   person.TracksTable,
			Columns: []string{person.TracksColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(track.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := puo.mutation.TracksIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   person.TracksTable,
			Columns: []string{person.TracksColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(track.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if puo.mutation.MessageHistoriesCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   person.MessageHistoriesTable,
			Columns: []string{person.MessageHistoriesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(messagehistory.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := puo.mutation.RemovedMessageHistoriesIDs(); len(nodes) > 0 && !puo.mutation.MessageHistoriesCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   person.MessageHistoriesTable,
			Columns: []string{person.MessageHistoriesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(messagehistory.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := puo.mutation.MessageHistoriesIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   person.MessageHistoriesTable,
			Columns: []string{person.MessageHistoriesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(messagehistory.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if puo.mutation.AssigneesCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: false,
			Table:   person.AssigneesTable,
			Columns: person.AssigneesPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeInt),
			},
		}
		createE := &PersonAssignmentCreate{config: puo.config, mutation: newPersonAssignmentMutation(puo.config, OpCreate)}
		createE.defaults()
		_, specE := createE.createSpec()
		edge.Target.Fields = specE.Fields
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := puo.mutation.RemovedAssigneesIDs(); len(nodes) > 0 && !puo.mutation.AssigneesCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: false,
			Table:   person.AssigneesTable,
			Columns: person.AssigneesPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		createE := &PersonAssignmentCreate{config: puo.config, mutation: newPersonAssignmentMutation(puo.config, OpCreate)}
		createE.defaults()
		_, specE := createE.createSpec()
		edge.Target.Fields = specE.Fields
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := puo.mutation.AssigneesIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: false,
			Table:   person.AssigneesTable,
			Columns: person.AssigneesPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		createE := &PersonAssignmentCreate{config: puo.config, mutation: newPersonAssignmentMutation(puo.config, OpCreate)}
		createE.defaults()
		_, specE := createE.createSpec()
		edge.Target.Fields = specE.Fields
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if puo.mutation.JobCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   person.JobTable,
			Columns: []string{person.JobColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(term.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := puo.mutation.JobIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   person.JobTable,
			Columns: []string{person.JobColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(term.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if puo.mutation.SourceCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   person.SourceTable,
			Columns: []string{person.SourceColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(term.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := puo.mutation.SourceIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   person.SourceTable,
			Columns: []string{person.SourceColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(term.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if puo.mutation.CreatorCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   person.CreatorTable,
			Columns: []string{person.CreatorColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := puo.mutation.CreatorIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   person.CreatorTable,
			Columns: []string{person.CreatorColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if puo.mutation.ReferredByCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2O,
			Inverse: false,
			Table:   person.ReferredByTable,
			Columns: []string{person.ReferredByColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(personreferral.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := puo.mutation.ReferredByIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2O,
			Inverse: false,
			Table:   person.ReferredByTable,
			Columns: []string{person.ReferredByColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(personreferral.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if puo.mutation.TagPersonCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   person.TagPersonTable,
			Columns: []string{person.TagPersonColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tagperson.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := puo.mutation.RemovedTagPersonIDs(); len(nodes) > 0 && !puo.mutation.TagPersonCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   person.TagPersonTable,
			Columns: []string{person.TagPersonColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tagperson.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := puo.mutation.TagPersonIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   person.TagPersonTable,
			Columns: []string{person.TagPersonColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tagperson.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if puo.mutation.AssignmentCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   person.AssignmentTable,
			Columns: []string{person.AssignmentColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(personassignment.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := puo.mutation.RemovedAssignmentIDs(); len(nodes) > 0 && !puo.mutation.AssignmentCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   person.AssignmentTable,
			Columns: []string{person.AssignmentColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(personassignment.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := puo.mutation.AssignmentIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   person.AssignmentTable,
			Columns: []string{person.AssignmentColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(personassignment.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_spec.AddModifiers(puo.modifiers...)
	_node = &Person{config: puo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, puo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{person.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	puo.mutation.done = true
	return _node, nil
}
