// Code generated by ent, DO NOT EDIT.

package ent

import (
	"bcare/ent/attachment"
	"bcare/ent/bill"
	"bcare/ent/deal"
	"bcare/ent/dealuser"
	"bcare/ent/deposit"
	"bcare/ent/discountusage"
	"bcare/ent/installmentplan"
	"bcare/ent/person"
	"bcare/ent/predicate"
	"bcare/ent/stage"
	"bcare/ent/tag"
	"bcare/ent/tagdeal"
	"bcare/ent/task"
	"bcare/ent/track"
	"bcare/ent/types"
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/dialect/sql/sqljson"
	"entgo.io/ent/schema/field"
)

// DealUpdate is the builder for updating Deal entities.
type DealUpdate struct {
	config
	hooks     []Hook
	mutation  *DealMutation
	modifiers []func(*sql.UpdateBuilder)
}

// Where appends a list predicates to the DealUpdate builder.
func (du *DealUpdate) Where(ps ...predicate.Deal) *DealUpdate {
	du.mutation.Where(ps...)
	return du
}

// SetDeletedAt sets the "deleted_at" field.
func (du *DealUpdate) SetDeletedAt(t time.Time) *DealUpdate {
	du.mutation.SetDeletedAt(t)
	return du
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (du *DealUpdate) SetNillableDeletedAt(t *time.Time) *DealUpdate {
	if t != nil {
		du.SetDeletedAt(*t)
	}
	return du
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (du *DealUpdate) ClearDeletedAt() *DealUpdate {
	du.mutation.ClearDeletedAt()
	return du
}

// SetStatus sets the "status" field.
func (du *DealUpdate) SetStatus(i int8) *DealUpdate {
	du.mutation.ResetStatus()
	du.mutation.SetStatus(i)
	return du
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (du *DealUpdate) SetNillableStatus(i *int8) *DealUpdate {
	if i != nil {
		du.SetStatus(*i)
	}
	return du
}

// AddStatus adds i to the "status" field.
func (du *DealUpdate) AddStatus(i int8) *DealUpdate {
	du.mutation.AddStatus(i)
	return du
}

// SetVersion sets the "version" field.
func (du *DealUpdate) SetVersion(i int) *DealUpdate {
	du.mutation.ResetVersion()
	du.mutation.SetVersion(i)
	return du
}

// SetNillableVersion sets the "version" field if the given value is not nil.
func (du *DealUpdate) SetNillableVersion(i *int) *DealUpdate {
	if i != nil {
		du.SetVersion(*i)
	}
	return du
}

// AddVersion adds i to the "version" field.
func (du *DealUpdate) AddVersion(i int) *DealUpdate {
	du.mutation.AddVersion(i)
	return du
}

// SetUpdatedAt sets the "updated_at" field.
func (du *DealUpdate) SetUpdatedAt(t time.Time) *DealUpdate {
	du.mutation.SetUpdatedAt(t)
	return du
}

// SetPersonID sets the "person_id" field.
func (du *DealUpdate) SetPersonID(i int) *DealUpdate {
	du.mutation.SetPersonID(i)
	return du
}

// SetNillablePersonID sets the "person_id" field if the given value is not nil.
func (du *DealUpdate) SetNillablePersonID(i *int) *DealUpdate {
	if i != nil {
		du.SetPersonID(*i)
	}
	return du
}

// ClearPersonID clears the value of the "person_id" field.
func (du *DealUpdate) ClearPersonID() *DealUpdate {
	du.mutation.ClearPersonID()
	return du
}

// SetParentDealID sets the "parent_deal_id" field.
func (du *DealUpdate) SetParentDealID(i int) *DealUpdate {
	du.mutation.ResetParentDealID()
	du.mutation.SetParentDealID(i)
	return du
}

// SetNillableParentDealID sets the "parent_deal_id" field if the given value is not nil.
func (du *DealUpdate) SetNillableParentDealID(i *int) *DealUpdate {
	if i != nil {
		du.SetParentDealID(*i)
	}
	return du
}

// AddParentDealID adds i to the "parent_deal_id" field.
func (du *DealUpdate) AddParentDealID(i int) *DealUpdate {
	du.mutation.AddParentDealID(i)
	return du
}

// ClearParentDealID clears the value of the "parent_deal_id" field.
func (du *DealUpdate) ClearParentDealID() *DealUpdate {
	du.mutation.ClearParentDealID()
	return du
}

// SetTotalAmount sets the "total_amount" field.
func (du *DealUpdate) SetTotalAmount(f float64) *DealUpdate {
	du.mutation.ResetTotalAmount()
	du.mutation.SetTotalAmount(f)
	return du
}

// SetNillableTotalAmount sets the "total_amount" field if the given value is not nil.
func (du *DealUpdate) SetNillableTotalAmount(f *float64) *DealUpdate {
	if f != nil {
		du.SetTotalAmount(*f)
	}
	return du
}

// AddTotalAmount adds f to the "total_amount" field.
func (du *DealUpdate) AddTotalAmount(f float64) *DealUpdate {
	du.mutation.AddTotalAmount(f)
	return du
}

// SetStageID sets the "stage_id" field.
func (du *DealUpdate) SetStageID(i int) *DealUpdate {
	du.mutation.SetStageID(i)
	return du
}

// SetNillableStageID sets the "stage_id" field if the given value is not nil.
func (du *DealUpdate) SetNillableStageID(i *int) *DealUpdate {
	if i != nil {
		du.SetStageID(*i)
	}
	return du
}

// ClearStageID clears the value of the "stage_id" field.
func (du *DealUpdate) ClearStageID() *DealUpdate {
	du.mutation.ClearStageID()
	return du
}

// SetStageHistory sets the "stage_history" field.
func (du *DealUpdate) SetStageHistory(the []types.StageHistoryEntry) *DealUpdate {
	du.mutation.SetStageHistory(the)
	return du
}

// AppendStageHistory appends the to the "stage_history" field.
func (du *DealUpdate) AppendStageHistory(the []types.StageHistoryEntry) *DealUpdate {
	du.mutation.AppendStageHistory(the)
	return du
}

// ClearStageHistory clears the value of the "stage_history" field.
func (du *DealUpdate) ClearStageHistory() *DealUpdate {
	du.mutation.ClearStageHistory()
	return du
}

// SetName sets the "name" field.
func (du *DealUpdate) SetName(s string) *DealUpdate {
	du.mutation.SetName(s)
	return du
}

// SetNillableName sets the "name" field if the given value is not nil.
func (du *DealUpdate) SetNillableName(s *string) *DealUpdate {
	if s != nil {
		du.SetName(*s)
	}
	return du
}

// ClearName clears the value of the "name" field.
func (du *DealUpdate) ClearName() *DealUpdate {
	du.mutation.ClearName()
	return du
}

// SetState sets the "state" field.
func (du *DealUpdate) SetState(d deal.State) *DealUpdate {
	du.mutation.SetState(d)
	return du
}

// SetNillableState sets the "state" field if the given value is not nil.
func (du *DealUpdate) SetNillableState(d *deal.State) *DealUpdate {
	if d != nil {
		du.SetState(*d)
	}
	return du
}

// SetPerson sets the "person" edge to the Person entity.
func (du *DealUpdate) SetPerson(p *Person) *DealUpdate {
	return du.SetPersonID(p.ID)
}

// SetStage sets the "stage" edge to the Stage entity.
func (du *DealUpdate) SetStage(s *Stage) *DealUpdate {
	return du.SetStageID(s.ID)
}

// AddAttachmentIDs adds the "attachments" edge to the Attachment entity by IDs.
func (du *DealUpdate) AddAttachmentIDs(ids ...int) *DealUpdate {
	du.mutation.AddAttachmentIDs(ids...)
	return du
}

// AddAttachments adds the "attachments" edges to the Attachment entity.
func (du *DealUpdate) AddAttachments(a ...*Attachment) *DealUpdate {
	ids := make([]int, len(a))
	for i := range a {
		ids[i] = a[i].ID
	}
	return du.AddAttachmentIDs(ids...)
}

// AddPlanIDs adds the "plans" edge to the InstallmentPlan entity by IDs.
func (du *DealUpdate) AddPlanIDs(ids ...int) *DealUpdate {
	du.mutation.AddPlanIDs(ids...)
	return du
}

// AddPlans adds the "plans" edges to the InstallmentPlan entity.
func (du *DealUpdate) AddPlans(i ...*InstallmentPlan) *DealUpdate {
	ids := make([]int, len(i))
	for j := range i {
		ids[j] = i[j].ID
	}
	return du.AddPlanIDs(ids...)
}

// AddBillIDs adds the "bills" edge to the Bill entity by IDs.
func (du *DealUpdate) AddBillIDs(ids ...int) *DealUpdate {
	du.mutation.AddBillIDs(ids...)
	return du
}

// AddBills adds the "bills" edges to the Bill entity.
func (du *DealUpdate) AddBills(b ...*Bill) *DealUpdate {
	ids := make([]int, len(b))
	for i := range b {
		ids[i] = b[i].ID
	}
	return du.AddBillIDs(ids...)
}

// AddTrackIDs adds the "tracks" edge to the Track entity by IDs.
func (du *DealUpdate) AddTrackIDs(ids ...int) *DealUpdate {
	du.mutation.AddTrackIDs(ids...)
	return du
}

// AddTracks adds the "tracks" edges to the Track entity.
func (du *DealUpdate) AddTracks(t ...*Track) *DealUpdate {
	ids := make([]int, len(t))
	for i := range t {
		ids[i] = t[i].ID
	}
	return du.AddTrackIDs(ids...)
}

// AddTaskIDs adds the "tasks" edge to the Task entity by IDs.
func (du *DealUpdate) AddTaskIDs(ids ...int) *DealUpdate {
	du.mutation.AddTaskIDs(ids...)
	return du
}

// AddTasks adds the "tasks" edges to the Task entity.
func (du *DealUpdate) AddTasks(t ...*Task) *DealUpdate {
	ids := make([]int, len(t))
	for i := range t {
		ids[i] = t[i].ID
	}
	return du.AddTaskIDs(ids...)
}

// AddDealAssignmentIDs adds the "deal_assignment" edge to the DealUser entity by IDs.
func (du *DealUpdate) AddDealAssignmentIDs(ids ...int) *DealUpdate {
	du.mutation.AddDealAssignmentIDs(ids...)
	return du
}

// AddDealAssignment adds the "deal_assignment" edges to the DealUser entity.
func (du *DealUpdate) AddDealAssignment(d ...*DealUser) *DealUpdate {
	ids := make([]int, len(d))
	for i := range d {
		ids[i] = d[i].ID
	}
	return du.AddDealAssignmentIDs(ids...)
}

// AddTagIDs adds the "tags" edge to the Tag entity by IDs.
func (du *DealUpdate) AddTagIDs(ids ...int) *DealUpdate {
	du.mutation.AddTagIDs(ids...)
	return du
}

// AddTags adds the "tags" edges to the Tag entity.
func (du *DealUpdate) AddTags(t ...*Tag) *DealUpdate {
	ids := make([]int, len(t))
	for i := range t {
		ids[i] = t[i].ID
	}
	return du.AddTagIDs(ids...)
}

// AddDepositIDs adds the "deposits" edge to the Deposit entity by IDs.
func (du *DealUpdate) AddDepositIDs(ids ...int) *DealUpdate {
	du.mutation.AddDepositIDs(ids...)
	return du
}

// AddDeposits adds the "deposits" edges to the Deposit entity.
func (du *DealUpdate) AddDeposits(d ...*Deposit) *DealUpdate {
	ids := make([]int, len(d))
	for i := range d {
		ids[i] = d[i].ID
	}
	return du.AddDepositIDs(ids...)
}

// AddDiscountUsageIDs adds the "discount_usages" edge to the DiscountUsage entity by IDs.
func (du *DealUpdate) AddDiscountUsageIDs(ids ...int) *DealUpdate {
	du.mutation.AddDiscountUsageIDs(ids...)
	return du
}

// AddDiscountUsages adds the "discount_usages" edges to the DiscountUsage entity.
func (du *DealUpdate) AddDiscountUsages(d ...*DiscountUsage) *DealUpdate {
	ids := make([]int, len(d))
	for i := range d {
		ids[i] = d[i].ID
	}
	return du.AddDiscountUsageIDs(ids...)
}

// AddTagDealIDs adds the "tag_deal" edge to the TagDeal entity by IDs.
func (du *DealUpdate) AddTagDealIDs(ids ...int) *DealUpdate {
	du.mutation.AddTagDealIDs(ids...)
	return du
}

// AddTagDeal adds the "tag_deal" edges to the TagDeal entity.
func (du *DealUpdate) AddTagDeal(t ...*TagDeal) *DealUpdate {
	ids := make([]int, len(t))
	for i := range t {
		ids[i] = t[i].ID
	}
	return du.AddTagDealIDs(ids...)
}

// Mutation returns the DealMutation object of the builder.
func (du *DealUpdate) Mutation() *DealMutation {
	return du.mutation
}

// ClearPerson clears the "person" edge to the Person entity.
func (du *DealUpdate) ClearPerson() *DealUpdate {
	du.mutation.ClearPerson()
	return du
}

// ClearStage clears the "stage" edge to the Stage entity.
func (du *DealUpdate) ClearStage() *DealUpdate {
	du.mutation.ClearStage()
	return du
}

// ClearAttachments clears all "attachments" edges to the Attachment entity.
func (du *DealUpdate) ClearAttachments() *DealUpdate {
	du.mutation.ClearAttachments()
	return du
}

// RemoveAttachmentIDs removes the "attachments" edge to Attachment entities by IDs.
func (du *DealUpdate) RemoveAttachmentIDs(ids ...int) *DealUpdate {
	du.mutation.RemoveAttachmentIDs(ids...)
	return du
}

// RemoveAttachments removes "attachments" edges to Attachment entities.
func (du *DealUpdate) RemoveAttachments(a ...*Attachment) *DealUpdate {
	ids := make([]int, len(a))
	for i := range a {
		ids[i] = a[i].ID
	}
	return du.RemoveAttachmentIDs(ids...)
}

// ClearPlans clears all "plans" edges to the InstallmentPlan entity.
func (du *DealUpdate) ClearPlans() *DealUpdate {
	du.mutation.ClearPlans()
	return du
}

// RemovePlanIDs removes the "plans" edge to InstallmentPlan entities by IDs.
func (du *DealUpdate) RemovePlanIDs(ids ...int) *DealUpdate {
	du.mutation.RemovePlanIDs(ids...)
	return du
}

// RemovePlans removes "plans" edges to InstallmentPlan entities.
func (du *DealUpdate) RemovePlans(i ...*InstallmentPlan) *DealUpdate {
	ids := make([]int, len(i))
	for j := range i {
		ids[j] = i[j].ID
	}
	return du.RemovePlanIDs(ids...)
}

// ClearBills clears all "bills" edges to the Bill entity.
func (du *DealUpdate) ClearBills() *DealUpdate {
	du.mutation.ClearBills()
	return du
}

// RemoveBillIDs removes the "bills" edge to Bill entities by IDs.
func (du *DealUpdate) RemoveBillIDs(ids ...int) *DealUpdate {
	du.mutation.RemoveBillIDs(ids...)
	return du
}

// RemoveBills removes "bills" edges to Bill entities.
func (du *DealUpdate) RemoveBills(b ...*Bill) *DealUpdate {
	ids := make([]int, len(b))
	for i := range b {
		ids[i] = b[i].ID
	}
	return du.RemoveBillIDs(ids...)
}

// ClearTracks clears all "tracks" edges to the Track entity.
func (du *DealUpdate) ClearTracks() *DealUpdate {
	du.mutation.ClearTracks()
	return du
}

// RemoveTrackIDs removes the "tracks" edge to Track entities by IDs.
func (du *DealUpdate) RemoveTrackIDs(ids ...int) *DealUpdate {
	du.mutation.RemoveTrackIDs(ids...)
	return du
}

// RemoveTracks removes "tracks" edges to Track entities.
func (du *DealUpdate) RemoveTracks(t ...*Track) *DealUpdate {
	ids := make([]int, len(t))
	for i := range t {
		ids[i] = t[i].ID
	}
	return du.RemoveTrackIDs(ids...)
}

// ClearTasks clears all "tasks" edges to the Task entity.
func (du *DealUpdate) ClearTasks() *DealUpdate {
	du.mutation.ClearTasks()
	return du
}

// RemoveTaskIDs removes the "tasks" edge to Task entities by IDs.
func (du *DealUpdate) RemoveTaskIDs(ids ...int) *DealUpdate {
	du.mutation.RemoveTaskIDs(ids...)
	return du
}

// RemoveTasks removes "tasks" edges to Task entities.
func (du *DealUpdate) RemoveTasks(t ...*Task) *DealUpdate {
	ids := make([]int, len(t))
	for i := range t {
		ids[i] = t[i].ID
	}
	return du.RemoveTaskIDs(ids...)
}

// ClearDealAssignment clears all "deal_assignment" edges to the DealUser entity.
func (du *DealUpdate) ClearDealAssignment() *DealUpdate {
	du.mutation.ClearDealAssignment()
	return du
}

// RemoveDealAssignmentIDs removes the "deal_assignment" edge to DealUser entities by IDs.
func (du *DealUpdate) RemoveDealAssignmentIDs(ids ...int) *DealUpdate {
	du.mutation.RemoveDealAssignmentIDs(ids...)
	return du
}

// RemoveDealAssignment removes "deal_assignment" edges to DealUser entities.
func (du *DealUpdate) RemoveDealAssignment(d ...*DealUser) *DealUpdate {
	ids := make([]int, len(d))
	for i := range d {
		ids[i] = d[i].ID
	}
	return du.RemoveDealAssignmentIDs(ids...)
}

// ClearTags clears all "tags" edges to the Tag entity.
func (du *DealUpdate) ClearTags() *DealUpdate {
	du.mutation.ClearTags()
	return du
}

// RemoveTagIDs removes the "tags" edge to Tag entities by IDs.
func (du *DealUpdate) RemoveTagIDs(ids ...int) *DealUpdate {
	du.mutation.RemoveTagIDs(ids...)
	return du
}

// RemoveTags removes "tags" edges to Tag entities.
func (du *DealUpdate) RemoveTags(t ...*Tag) *DealUpdate {
	ids := make([]int, len(t))
	for i := range t {
		ids[i] = t[i].ID
	}
	return du.RemoveTagIDs(ids...)
}

// ClearDeposits clears all "deposits" edges to the Deposit entity.
func (du *DealUpdate) ClearDeposits() *DealUpdate {
	du.mutation.ClearDeposits()
	return du
}

// RemoveDepositIDs removes the "deposits" edge to Deposit entities by IDs.
func (du *DealUpdate) RemoveDepositIDs(ids ...int) *DealUpdate {
	du.mutation.RemoveDepositIDs(ids...)
	return du
}

// RemoveDeposits removes "deposits" edges to Deposit entities.
func (du *DealUpdate) RemoveDeposits(d ...*Deposit) *DealUpdate {
	ids := make([]int, len(d))
	for i := range d {
		ids[i] = d[i].ID
	}
	return du.RemoveDepositIDs(ids...)
}

// ClearDiscountUsages clears all "discount_usages" edges to the DiscountUsage entity.
func (du *DealUpdate) ClearDiscountUsages() *DealUpdate {
	du.mutation.ClearDiscountUsages()
	return du
}

// RemoveDiscountUsageIDs removes the "discount_usages" edge to DiscountUsage entities by IDs.
func (du *DealUpdate) RemoveDiscountUsageIDs(ids ...int) *DealUpdate {
	du.mutation.RemoveDiscountUsageIDs(ids...)
	return du
}

// RemoveDiscountUsages removes "discount_usages" edges to DiscountUsage entities.
func (du *DealUpdate) RemoveDiscountUsages(d ...*DiscountUsage) *DealUpdate {
	ids := make([]int, len(d))
	for i := range d {
		ids[i] = d[i].ID
	}
	return du.RemoveDiscountUsageIDs(ids...)
}

// ClearTagDeal clears all "tag_deal" edges to the TagDeal entity.
func (du *DealUpdate) ClearTagDeal() *DealUpdate {
	du.mutation.ClearTagDeal()
	return du
}

// RemoveTagDealIDs removes the "tag_deal" edge to TagDeal entities by IDs.
func (du *DealUpdate) RemoveTagDealIDs(ids ...int) *DealUpdate {
	du.mutation.RemoveTagDealIDs(ids...)
	return du
}

// RemoveTagDeal removes "tag_deal" edges to TagDeal entities.
func (du *DealUpdate) RemoveTagDeal(t ...*TagDeal) *DealUpdate {
	ids := make([]int, len(t))
	for i := range t {
		ids[i] = t[i].ID
	}
	return du.RemoveTagDealIDs(ids...)
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (du *DealUpdate) Save(ctx context.Context) (int, error) {
	if err := du.defaults(); err != nil {
		return 0, err
	}
	return withHooks(ctx, du.sqlSave, du.mutation, du.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (du *DealUpdate) SaveX(ctx context.Context) int {
	affected, err := du.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (du *DealUpdate) Exec(ctx context.Context) error {
	_, err := du.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (du *DealUpdate) ExecX(ctx context.Context) {
	if err := du.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (du *DealUpdate) defaults() error {
	if _, ok := du.mutation.UpdatedAt(); !ok {
		if deal.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized deal.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := deal.UpdateDefaultUpdatedAt()
		du.mutation.SetUpdatedAt(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (du *DealUpdate) check() error {
	if v, ok := du.mutation.State(); ok {
		if err := deal.StateValidator(v); err != nil {
			return &ValidationError{Name: "state", err: fmt.Errorf(`ent: validator failed for field "Deal.state": %w`, err)}
		}
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (du *DealUpdate) Modify(modifiers ...func(u *sql.UpdateBuilder)) *DealUpdate {
	du.modifiers = append(du.modifiers, modifiers...)
	return du
}

func (du *DealUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := du.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(deal.Table, deal.Columns, sqlgraph.NewFieldSpec(deal.FieldID, field.TypeInt))
	if ps := du.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := du.mutation.DeletedAt(); ok {
		_spec.SetField(deal.FieldDeletedAt, field.TypeTime, value)
	}
	if du.mutation.DeletedAtCleared() {
		_spec.ClearField(deal.FieldDeletedAt, field.TypeTime)
	}
	if value, ok := du.mutation.Status(); ok {
		_spec.SetField(deal.FieldStatus, field.TypeInt8, value)
	}
	if value, ok := du.mutation.AddedStatus(); ok {
		_spec.AddField(deal.FieldStatus, field.TypeInt8, value)
	}
	if value, ok := du.mutation.Version(); ok {
		_spec.SetField(deal.FieldVersion, field.TypeInt, value)
	}
	if value, ok := du.mutation.AddedVersion(); ok {
		_spec.AddField(deal.FieldVersion, field.TypeInt, value)
	}
	if value, ok := du.mutation.UpdatedAt(); ok {
		_spec.SetField(deal.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := du.mutation.ParentDealID(); ok {
		_spec.SetField(deal.FieldParentDealID, field.TypeInt, value)
	}
	if value, ok := du.mutation.AddedParentDealID(); ok {
		_spec.AddField(deal.FieldParentDealID, field.TypeInt, value)
	}
	if du.mutation.ParentDealIDCleared() {
		_spec.ClearField(deal.FieldParentDealID, field.TypeInt)
	}
	if value, ok := du.mutation.TotalAmount(); ok {
		_spec.SetField(deal.FieldTotalAmount, field.TypeFloat64, value)
	}
	if value, ok := du.mutation.AddedTotalAmount(); ok {
		_spec.AddField(deal.FieldTotalAmount, field.TypeFloat64, value)
	}
	if value, ok := du.mutation.StageHistory(); ok {
		_spec.SetField(deal.FieldStageHistory, field.TypeJSON, value)
	}
	if value, ok := du.mutation.AppendedStageHistory(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, deal.FieldStageHistory, value)
		})
	}
	if du.mutation.StageHistoryCleared() {
		_spec.ClearField(deal.FieldStageHistory, field.TypeJSON)
	}
	if value, ok := du.mutation.Name(); ok {
		_spec.SetField(deal.FieldName, field.TypeString, value)
	}
	if du.mutation.NameCleared() {
		_spec.ClearField(deal.FieldName, field.TypeString)
	}
	if value, ok := du.mutation.State(); ok {
		_spec.SetField(deal.FieldState, field.TypeEnum, value)
	}
	if du.mutation.PersonCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   deal.PersonTable,
			Columns: []string{deal.PersonColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(person.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := du.mutation.PersonIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   deal.PersonTable,
			Columns: []string{deal.PersonColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(person.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if du.mutation.StageCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   deal.StageTable,
			Columns: []string{deal.StageColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(stage.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := du.mutation.StageIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   deal.StageTable,
			Columns: []string{deal.StageColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(stage.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if du.mutation.AttachmentsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   deal.AttachmentsTable,
			Columns: []string{deal.AttachmentsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(attachment.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := du.mutation.RemovedAttachmentsIDs(); len(nodes) > 0 && !du.mutation.AttachmentsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   deal.AttachmentsTable,
			Columns: []string{deal.AttachmentsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(attachment.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := du.mutation.AttachmentsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   deal.AttachmentsTable,
			Columns: []string{deal.AttachmentsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(attachment.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if du.mutation.PlansCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   deal.PlansTable,
			Columns: []string{deal.PlansColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(installmentplan.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := du.mutation.RemovedPlansIDs(); len(nodes) > 0 && !du.mutation.PlansCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   deal.PlansTable,
			Columns: []string{deal.PlansColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(installmentplan.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := du.mutation.PlansIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   deal.PlansTable,
			Columns: []string{deal.PlansColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(installmentplan.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if du.mutation.BillsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   deal.BillsTable,
			Columns: []string{deal.BillsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(bill.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := du.mutation.RemovedBillsIDs(); len(nodes) > 0 && !du.mutation.BillsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   deal.BillsTable,
			Columns: []string{deal.BillsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(bill.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := du.mutation.BillsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   deal.BillsTable,
			Columns: []string{deal.BillsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(bill.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if du.mutation.TracksCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   deal.TracksTable,
			Columns: []string{deal.TracksColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(track.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := du.mutation.RemovedTracksIDs(); len(nodes) > 0 && !du.mutation.TracksCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   deal.TracksTable,
			Columns: []string{deal.TracksColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(track.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := du.mutation.TracksIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   deal.TracksTable,
			Columns: []string{deal.TracksColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(track.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if du.mutation.TasksCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   deal.TasksTable,
			Columns: []string{deal.TasksColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(task.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := du.mutation.RemovedTasksIDs(); len(nodes) > 0 && !du.mutation.TasksCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   deal.TasksTable,
			Columns: []string{deal.TasksColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(task.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := du.mutation.TasksIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   deal.TasksTable,
			Columns: []string{deal.TasksColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(task.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if du.mutation.DealAssignmentCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   deal.DealAssignmentTable,
			Columns: []string{deal.DealAssignmentColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(dealuser.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := du.mutation.RemovedDealAssignmentIDs(); len(nodes) > 0 && !du.mutation.DealAssignmentCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   deal.DealAssignmentTable,
			Columns: []string{deal.DealAssignmentColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(dealuser.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := du.mutation.DealAssignmentIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   deal.DealAssignmentTable,
			Columns: []string{deal.DealAssignmentColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(dealuser.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if du.mutation.TagsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: false,
			Table:   deal.TagsTable,
			Columns: deal.TagsPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tag.FieldID, field.TypeInt),
			},
		}
		createE := &TagDealCreate{config: du.config, mutation: newTagDealMutation(du.config, OpCreate)}
		createE.defaults()
		_, specE := createE.createSpec()
		edge.Target.Fields = specE.Fields
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := du.mutation.RemovedTagsIDs(); len(nodes) > 0 && !du.mutation.TagsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: false,
			Table:   deal.TagsTable,
			Columns: deal.TagsPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tag.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		createE := &TagDealCreate{config: du.config, mutation: newTagDealMutation(du.config, OpCreate)}
		createE.defaults()
		_, specE := createE.createSpec()
		edge.Target.Fields = specE.Fields
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := du.mutation.TagsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: false,
			Table:   deal.TagsTable,
			Columns: deal.TagsPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tag.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		createE := &TagDealCreate{config: du.config, mutation: newTagDealMutation(du.config, OpCreate)}
		createE.defaults()
		_, specE := createE.createSpec()
		edge.Target.Fields = specE.Fields
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if du.mutation.DepositsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   deal.DepositsTable,
			Columns: []string{deal.DepositsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(deposit.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := du.mutation.RemovedDepositsIDs(); len(nodes) > 0 && !du.mutation.DepositsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   deal.DepositsTable,
			Columns: []string{deal.DepositsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(deposit.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := du.mutation.DepositsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   deal.DepositsTable,
			Columns: []string{deal.DepositsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(deposit.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if du.mutation.DiscountUsagesCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   deal.DiscountUsagesTable,
			Columns: []string{deal.DiscountUsagesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(discountusage.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := du.mutation.RemovedDiscountUsagesIDs(); len(nodes) > 0 && !du.mutation.DiscountUsagesCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   deal.DiscountUsagesTable,
			Columns: []string{deal.DiscountUsagesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(discountusage.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := du.mutation.DiscountUsagesIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   deal.DiscountUsagesTable,
			Columns: []string{deal.DiscountUsagesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(discountusage.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if du.mutation.TagDealCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   deal.TagDealTable,
			Columns: []string{deal.TagDealColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tagdeal.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := du.mutation.RemovedTagDealIDs(); len(nodes) > 0 && !du.mutation.TagDealCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   deal.TagDealTable,
			Columns: []string{deal.TagDealColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tagdeal.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := du.mutation.TagDealIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   deal.TagDealTable,
			Columns: []string{deal.TagDealColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tagdeal.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_spec.AddModifiers(du.modifiers...)
	if n, err = sqlgraph.UpdateNodes(ctx, du.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{deal.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	du.mutation.done = true
	return n, nil
}

// DealUpdateOne is the builder for updating a single Deal entity.
type DealUpdateOne struct {
	config
	fields    []string
	hooks     []Hook
	mutation  *DealMutation
	modifiers []func(*sql.UpdateBuilder)
}

// SetDeletedAt sets the "deleted_at" field.
func (duo *DealUpdateOne) SetDeletedAt(t time.Time) *DealUpdateOne {
	duo.mutation.SetDeletedAt(t)
	return duo
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (duo *DealUpdateOne) SetNillableDeletedAt(t *time.Time) *DealUpdateOne {
	if t != nil {
		duo.SetDeletedAt(*t)
	}
	return duo
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (duo *DealUpdateOne) ClearDeletedAt() *DealUpdateOne {
	duo.mutation.ClearDeletedAt()
	return duo
}

// SetStatus sets the "status" field.
func (duo *DealUpdateOne) SetStatus(i int8) *DealUpdateOne {
	duo.mutation.ResetStatus()
	duo.mutation.SetStatus(i)
	return duo
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (duo *DealUpdateOne) SetNillableStatus(i *int8) *DealUpdateOne {
	if i != nil {
		duo.SetStatus(*i)
	}
	return duo
}

// AddStatus adds i to the "status" field.
func (duo *DealUpdateOne) AddStatus(i int8) *DealUpdateOne {
	duo.mutation.AddStatus(i)
	return duo
}

// SetVersion sets the "version" field.
func (duo *DealUpdateOne) SetVersion(i int) *DealUpdateOne {
	duo.mutation.ResetVersion()
	duo.mutation.SetVersion(i)
	return duo
}

// SetNillableVersion sets the "version" field if the given value is not nil.
func (duo *DealUpdateOne) SetNillableVersion(i *int) *DealUpdateOne {
	if i != nil {
		duo.SetVersion(*i)
	}
	return duo
}

// AddVersion adds i to the "version" field.
func (duo *DealUpdateOne) AddVersion(i int) *DealUpdateOne {
	duo.mutation.AddVersion(i)
	return duo
}

// SetUpdatedAt sets the "updated_at" field.
func (duo *DealUpdateOne) SetUpdatedAt(t time.Time) *DealUpdateOne {
	duo.mutation.SetUpdatedAt(t)
	return duo
}

// SetPersonID sets the "person_id" field.
func (duo *DealUpdateOne) SetPersonID(i int) *DealUpdateOne {
	duo.mutation.SetPersonID(i)
	return duo
}

// SetNillablePersonID sets the "person_id" field if the given value is not nil.
func (duo *DealUpdateOne) SetNillablePersonID(i *int) *DealUpdateOne {
	if i != nil {
		duo.SetPersonID(*i)
	}
	return duo
}

// ClearPersonID clears the value of the "person_id" field.
func (duo *DealUpdateOne) ClearPersonID() *DealUpdateOne {
	duo.mutation.ClearPersonID()
	return duo
}

// SetParentDealID sets the "parent_deal_id" field.
func (duo *DealUpdateOne) SetParentDealID(i int) *DealUpdateOne {
	duo.mutation.ResetParentDealID()
	duo.mutation.SetParentDealID(i)
	return duo
}

// SetNillableParentDealID sets the "parent_deal_id" field if the given value is not nil.
func (duo *DealUpdateOne) SetNillableParentDealID(i *int) *DealUpdateOne {
	if i != nil {
		duo.SetParentDealID(*i)
	}
	return duo
}

// AddParentDealID adds i to the "parent_deal_id" field.
func (duo *DealUpdateOne) AddParentDealID(i int) *DealUpdateOne {
	duo.mutation.AddParentDealID(i)
	return duo
}

// ClearParentDealID clears the value of the "parent_deal_id" field.
func (duo *DealUpdateOne) ClearParentDealID() *DealUpdateOne {
	duo.mutation.ClearParentDealID()
	return duo
}

// SetTotalAmount sets the "total_amount" field.
func (duo *DealUpdateOne) SetTotalAmount(f float64) *DealUpdateOne {
	duo.mutation.ResetTotalAmount()
	duo.mutation.SetTotalAmount(f)
	return duo
}

// SetNillableTotalAmount sets the "total_amount" field if the given value is not nil.
func (duo *DealUpdateOne) SetNillableTotalAmount(f *float64) *DealUpdateOne {
	if f != nil {
		duo.SetTotalAmount(*f)
	}
	return duo
}

// AddTotalAmount adds f to the "total_amount" field.
func (duo *DealUpdateOne) AddTotalAmount(f float64) *DealUpdateOne {
	duo.mutation.AddTotalAmount(f)
	return duo
}

// SetStageID sets the "stage_id" field.
func (duo *DealUpdateOne) SetStageID(i int) *DealUpdateOne {
	duo.mutation.SetStageID(i)
	return duo
}

// SetNillableStageID sets the "stage_id" field if the given value is not nil.
func (duo *DealUpdateOne) SetNillableStageID(i *int) *DealUpdateOne {
	if i != nil {
		duo.SetStageID(*i)
	}
	return duo
}

// ClearStageID clears the value of the "stage_id" field.
func (duo *DealUpdateOne) ClearStageID() *DealUpdateOne {
	duo.mutation.ClearStageID()
	return duo
}

// SetStageHistory sets the "stage_history" field.
func (duo *DealUpdateOne) SetStageHistory(the []types.StageHistoryEntry) *DealUpdateOne {
	duo.mutation.SetStageHistory(the)
	return duo
}

// AppendStageHistory appends the to the "stage_history" field.
func (duo *DealUpdateOne) AppendStageHistory(the []types.StageHistoryEntry) *DealUpdateOne {
	duo.mutation.AppendStageHistory(the)
	return duo
}

// ClearStageHistory clears the value of the "stage_history" field.
func (duo *DealUpdateOne) ClearStageHistory() *DealUpdateOne {
	duo.mutation.ClearStageHistory()
	return duo
}

// SetName sets the "name" field.
func (duo *DealUpdateOne) SetName(s string) *DealUpdateOne {
	duo.mutation.SetName(s)
	return duo
}

// SetNillableName sets the "name" field if the given value is not nil.
func (duo *DealUpdateOne) SetNillableName(s *string) *DealUpdateOne {
	if s != nil {
		duo.SetName(*s)
	}
	return duo
}

// ClearName clears the value of the "name" field.
func (duo *DealUpdateOne) ClearName() *DealUpdateOne {
	duo.mutation.ClearName()
	return duo
}

// SetState sets the "state" field.
func (duo *DealUpdateOne) SetState(d deal.State) *DealUpdateOne {
	duo.mutation.SetState(d)
	return duo
}

// SetNillableState sets the "state" field if the given value is not nil.
func (duo *DealUpdateOne) SetNillableState(d *deal.State) *DealUpdateOne {
	if d != nil {
		duo.SetState(*d)
	}
	return duo
}

// SetPerson sets the "person" edge to the Person entity.
func (duo *DealUpdateOne) SetPerson(p *Person) *DealUpdateOne {
	return duo.SetPersonID(p.ID)
}

// SetStage sets the "stage" edge to the Stage entity.
func (duo *DealUpdateOne) SetStage(s *Stage) *DealUpdateOne {
	return duo.SetStageID(s.ID)
}

// AddAttachmentIDs adds the "attachments" edge to the Attachment entity by IDs.
func (duo *DealUpdateOne) AddAttachmentIDs(ids ...int) *DealUpdateOne {
	duo.mutation.AddAttachmentIDs(ids...)
	return duo
}

// AddAttachments adds the "attachments" edges to the Attachment entity.
func (duo *DealUpdateOne) AddAttachments(a ...*Attachment) *DealUpdateOne {
	ids := make([]int, len(a))
	for i := range a {
		ids[i] = a[i].ID
	}
	return duo.AddAttachmentIDs(ids...)
}

// AddPlanIDs adds the "plans" edge to the InstallmentPlan entity by IDs.
func (duo *DealUpdateOne) AddPlanIDs(ids ...int) *DealUpdateOne {
	duo.mutation.AddPlanIDs(ids...)
	return duo
}

// AddPlans adds the "plans" edges to the InstallmentPlan entity.
func (duo *DealUpdateOne) AddPlans(i ...*InstallmentPlan) *DealUpdateOne {
	ids := make([]int, len(i))
	for j := range i {
		ids[j] = i[j].ID
	}
	return duo.AddPlanIDs(ids...)
}

// AddBillIDs adds the "bills" edge to the Bill entity by IDs.
func (duo *DealUpdateOne) AddBillIDs(ids ...int) *DealUpdateOne {
	duo.mutation.AddBillIDs(ids...)
	return duo
}

// AddBills adds the "bills" edges to the Bill entity.
func (duo *DealUpdateOne) AddBills(b ...*Bill) *DealUpdateOne {
	ids := make([]int, len(b))
	for i := range b {
		ids[i] = b[i].ID
	}
	return duo.AddBillIDs(ids...)
}

// AddTrackIDs adds the "tracks" edge to the Track entity by IDs.
func (duo *DealUpdateOne) AddTrackIDs(ids ...int) *DealUpdateOne {
	duo.mutation.AddTrackIDs(ids...)
	return duo
}

// AddTracks adds the "tracks" edges to the Track entity.
func (duo *DealUpdateOne) AddTracks(t ...*Track) *DealUpdateOne {
	ids := make([]int, len(t))
	for i := range t {
		ids[i] = t[i].ID
	}
	return duo.AddTrackIDs(ids...)
}

// AddTaskIDs adds the "tasks" edge to the Task entity by IDs.
func (duo *DealUpdateOne) AddTaskIDs(ids ...int) *DealUpdateOne {
	duo.mutation.AddTaskIDs(ids...)
	return duo
}

// AddTasks adds the "tasks" edges to the Task entity.
func (duo *DealUpdateOne) AddTasks(t ...*Task) *DealUpdateOne {
	ids := make([]int, len(t))
	for i := range t {
		ids[i] = t[i].ID
	}
	return duo.AddTaskIDs(ids...)
}

// AddDealAssignmentIDs adds the "deal_assignment" edge to the DealUser entity by IDs.
func (duo *DealUpdateOne) AddDealAssignmentIDs(ids ...int) *DealUpdateOne {
	duo.mutation.AddDealAssignmentIDs(ids...)
	return duo
}

// AddDealAssignment adds the "deal_assignment" edges to the DealUser entity.
func (duo *DealUpdateOne) AddDealAssignment(d ...*DealUser) *DealUpdateOne {
	ids := make([]int, len(d))
	for i := range d {
		ids[i] = d[i].ID
	}
	return duo.AddDealAssignmentIDs(ids...)
}

// AddTagIDs adds the "tags" edge to the Tag entity by IDs.
func (duo *DealUpdateOne) AddTagIDs(ids ...int) *DealUpdateOne {
	duo.mutation.AddTagIDs(ids...)
	return duo
}

// AddTags adds the "tags" edges to the Tag entity.
func (duo *DealUpdateOne) AddTags(t ...*Tag) *DealUpdateOne {
	ids := make([]int, len(t))
	for i := range t {
		ids[i] = t[i].ID
	}
	return duo.AddTagIDs(ids...)
}

// AddDepositIDs adds the "deposits" edge to the Deposit entity by IDs.
func (duo *DealUpdateOne) AddDepositIDs(ids ...int) *DealUpdateOne {
	duo.mutation.AddDepositIDs(ids...)
	return duo
}

// AddDeposits adds the "deposits" edges to the Deposit entity.
func (duo *DealUpdateOne) AddDeposits(d ...*Deposit) *DealUpdateOne {
	ids := make([]int, len(d))
	for i := range d {
		ids[i] = d[i].ID
	}
	return duo.AddDepositIDs(ids...)
}

// AddDiscountUsageIDs adds the "discount_usages" edge to the DiscountUsage entity by IDs.
func (duo *DealUpdateOne) AddDiscountUsageIDs(ids ...int) *DealUpdateOne {
	duo.mutation.AddDiscountUsageIDs(ids...)
	return duo
}

// AddDiscountUsages adds the "discount_usages" edges to the DiscountUsage entity.
func (duo *DealUpdateOne) AddDiscountUsages(d ...*DiscountUsage) *DealUpdateOne {
	ids := make([]int, len(d))
	for i := range d {
		ids[i] = d[i].ID
	}
	return duo.AddDiscountUsageIDs(ids...)
}

// AddTagDealIDs adds the "tag_deal" edge to the TagDeal entity by IDs.
func (duo *DealUpdateOne) AddTagDealIDs(ids ...int) *DealUpdateOne {
	duo.mutation.AddTagDealIDs(ids...)
	return duo
}

// AddTagDeal adds the "tag_deal" edges to the TagDeal entity.
func (duo *DealUpdateOne) AddTagDeal(t ...*TagDeal) *DealUpdateOne {
	ids := make([]int, len(t))
	for i := range t {
		ids[i] = t[i].ID
	}
	return duo.AddTagDealIDs(ids...)
}

// Mutation returns the DealMutation object of the builder.
func (duo *DealUpdateOne) Mutation() *DealMutation {
	return duo.mutation
}

// ClearPerson clears the "person" edge to the Person entity.
func (duo *DealUpdateOne) ClearPerson() *DealUpdateOne {
	duo.mutation.ClearPerson()
	return duo
}

// ClearStage clears the "stage" edge to the Stage entity.
func (duo *DealUpdateOne) ClearStage() *DealUpdateOne {
	duo.mutation.ClearStage()
	return duo
}

// ClearAttachments clears all "attachments" edges to the Attachment entity.
func (duo *DealUpdateOne) ClearAttachments() *DealUpdateOne {
	duo.mutation.ClearAttachments()
	return duo
}

// RemoveAttachmentIDs removes the "attachments" edge to Attachment entities by IDs.
func (duo *DealUpdateOne) RemoveAttachmentIDs(ids ...int) *DealUpdateOne {
	duo.mutation.RemoveAttachmentIDs(ids...)
	return duo
}

// RemoveAttachments removes "attachments" edges to Attachment entities.
func (duo *DealUpdateOne) RemoveAttachments(a ...*Attachment) *DealUpdateOne {
	ids := make([]int, len(a))
	for i := range a {
		ids[i] = a[i].ID
	}
	return duo.RemoveAttachmentIDs(ids...)
}

// ClearPlans clears all "plans" edges to the InstallmentPlan entity.
func (duo *DealUpdateOne) ClearPlans() *DealUpdateOne {
	duo.mutation.ClearPlans()
	return duo
}

// RemovePlanIDs removes the "plans" edge to InstallmentPlan entities by IDs.
func (duo *DealUpdateOne) RemovePlanIDs(ids ...int) *DealUpdateOne {
	duo.mutation.RemovePlanIDs(ids...)
	return duo
}

// RemovePlans removes "plans" edges to InstallmentPlan entities.
func (duo *DealUpdateOne) RemovePlans(i ...*InstallmentPlan) *DealUpdateOne {
	ids := make([]int, len(i))
	for j := range i {
		ids[j] = i[j].ID
	}
	return duo.RemovePlanIDs(ids...)
}

// ClearBills clears all "bills" edges to the Bill entity.
func (duo *DealUpdateOne) ClearBills() *DealUpdateOne {
	duo.mutation.ClearBills()
	return duo
}

// RemoveBillIDs removes the "bills" edge to Bill entities by IDs.
func (duo *DealUpdateOne) RemoveBillIDs(ids ...int) *DealUpdateOne {
	duo.mutation.RemoveBillIDs(ids...)
	return duo
}

// RemoveBills removes "bills" edges to Bill entities.
func (duo *DealUpdateOne) RemoveBills(b ...*Bill) *DealUpdateOne {
	ids := make([]int, len(b))
	for i := range b {
		ids[i] = b[i].ID
	}
	return duo.RemoveBillIDs(ids...)
}

// ClearTracks clears all "tracks" edges to the Track entity.
func (duo *DealUpdateOne) ClearTracks() *DealUpdateOne {
	duo.mutation.ClearTracks()
	return duo
}

// RemoveTrackIDs removes the "tracks" edge to Track entities by IDs.
func (duo *DealUpdateOne) RemoveTrackIDs(ids ...int) *DealUpdateOne {
	duo.mutation.RemoveTrackIDs(ids...)
	return duo
}

// RemoveTracks removes "tracks" edges to Track entities.
func (duo *DealUpdateOne) RemoveTracks(t ...*Track) *DealUpdateOne {
	ids := make([]int, len(t))
	for i := range t {
		ids[i] = t[i].ID
	}
	return duo.RemoveTrackIDs(ids...)
}

// ClearTasks clears all "tasks" edges to the Task entity.
func (duo *DealUpdateOne) ClearTasks() *DealUpdateOne {
	duo.mutation.ClearTasks()
	return duo
}

// RemoveTaskIDs removes the "tasks" edge to Task entities by IDs.
func (duo *DealUpdateOne) RemoveTaskIDs(ids ...int) *DealUpdateOne {
	duo.mutation.RemoveTaskIDs(ids...)
	return duo
}

// RemoveTasks removes "tasks" edges to Task entities.
func (duo *DealUpdateOne) RemoveTasks(t ...*Task) *DealUpdateOne {
	ids := make([]int, len(t))
	for i := range t {
		ids[i] = t[i].ID
	}
	return duo.RemoveTaskIDs(ids...)
}

// ClearDealAssignment clears all "deal_assignment" edges to the DealUser entity.
func (duo *DealUpdateOne) ClearDealAssignment() *DealUpdateOne {
	duo.mutation.ClearDealAssignment()
	return duo
}

// RemoveDealAssignmentIDs removes the "deal_assignment" edge to DealUser entities by IDs.
func (duo *DealUpdateOne) RemoveDealAssignmentIDs(ids ...int) *DealUpdateOne {
	duo.mutation.RemoveDealAssignmentIDs(ids...)
	return duo
}

// RemoveDealAssignment removes "deal_assignment" edges to DealUser entities.
func (duo *DealUpdateOne) RemoveDealAssignment(d ...*DealUser) *DealUpdateOne {
	ids := make([]int, len(d))
	for i := range d {
		ids[i] = d[i].ID
	}
	return duo.RemoveDealAssignmentIDs(ids...)
}

// ClearTags clears all "tags" edges to the Tag entity.
func (duo *DealUpdateOne) ClearTags() *DealUpdateOne {
	duo.mutation.ClearTags()
	return duo
}

// RemoveTagIDs removes the "tags" edge to Tag entities by IDs.
func (duo *DealUpdateOne) RemoveTagIDs(ids ...int) *DealUpdateOne {
	duo.mutation.RemoveTagIDs(ids...)
	return duo
}

// RemoveTags removes "tags" edges to Tag entities.
func (duo *DealUpdateOne) RemoveTags(t ...*Tag) *DealUpdateOne {
	ids := make([]int, len(t))
	for i := range t {
		ids[i] = t[i].ID
	}
	return duo.RemoveTagIDs(ids...)
}

// ClearDeposits clears all "deposits" edges to the Deposit entity.
func (duo *DealUpdateOne) ClearDeposits() *DealUpdateOne {
	duo.mutation.ClearDeposits()
	return duo
}

// RemoveDepositIDs removes the "deposits" edge to Deposit entities by IDs.
func (duo *DealUpdateOne) RemoveDepositIDs(ids ...int) *DealUpdateOne {
	duo.mutation.RemoveDepositIDs(ids...)
	return duo
}

// RemoveDeposits removes "deposits" edges to Deposit entities.
func (duo *DealUpdateOne) RemoveDeposits(d ...*Deposit) *DealUpdateOne {
	ids := make([]int, len(d))
	for i := range d {
		ids[i] = d[i].ID
	}
	return duo.RemoveDepositIDs(ids...)
}

// ClearDiscountUsages clears all "discount_usages" edges to the DiscountUsage entity.
func (duo *DealUpdateOne) ClearDiscountUsages() *DealUpdateOne {
	duo.mutation.ClearDiscountUsages()
	return duo
}

// RemoveDiscountUsageIDs removes the "discount_usages" edge to DiscountUsage entities by IDs.
func (duo *DealUpdateOne) RemoveDiscountUsageIDs(ids ...int) *DealUpdateOne {
	duo.mutation.RemoveDiscountUsageIDs(ids...)
	return duo
}

// RemoveDiscountUsages removes "discount_usages" edges to DiscountUsage entities.
func (duo *DealUpdateOne) RemoveDiscountUsages(d ...*DiscountUsage) *DealUpdateOne {
	ids := make([]int, len(d))
	for i := range d {
		ids[i] = d[i].ID
	}
	return duo.RemoveDiscountUsageIDs(ids...)
}

// ClearTagDeal clears all "tag_deal" edges to the TagDeal entity.
func (duo *DealUpdateOne) ClearTagDeal() *DealUpdateOne {
	duo.mutation.ClearTagDeal()
	return duo
}

// RemoveTagDealIDs removes the "tag_deal" edge to TagDeal entities by IDs.
func (duo *DealUpdateOne) RemoveTagDealIDs(ids ...int) *DealUpdateOne {
	duo.mutation.RemoveTagDealIDs(ids...)
	return duo
}

// RemoveTagDeal removes "tag_deal" edges to TagDeal entities.
func (duo *DealUpdateOne) RemoveTagDeal(t ...*TagDeal) *DealUpdateOne {
	ids := make([]int, len(t))
	for i := range t {
		ids[i] = t[i].ID
	}
	return duo.RemoveTagDealIDs(ids...)
}

// Where appends a list predicates to the DealUpdate builder.
func (duo *DealUpdateOne) Where(ps ...predicate.Deal) *DealUpdateOne {
	duo.mutation.Where(ps...)
	return duo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (duo *DealUpdateOne) Select(field string, fields ...string) *DealUpdateOne {
	duo.fields = append([]string{field}, fields...)
	return duo
}

// Save executes the query and returns the updated Deal entity.
func (duo *DealUpdateOne) Save(ctx context.Context) (*Deal, error) {
	if err := duo.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, duo.sqlSave, duo.mutation, duo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (duo *DealUpdateOne) SaveX(ctx context.Context) *Deal {
	node, err := duo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (duo *DealUpdateOne) Exec(ctx context.Context) error {
	_, err := duo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (duo *DealUpdateOne) ExecX(ctx context.Context) {
	if err := duo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (duo *DealUpdateOne) defaults() error {
	if _, ok := duo.mutation.UpdatedAt(); !ok {
		if deal.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized deal.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := deal.UpdateDefaultUpdatedAt()
		duo.mutation.SetUpdatedAt(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (duo *DealUpdateOne) check() error {
	if v, ok := duo.mutation.State(); ok {
		if err := deal.StateValidator(v); err != nil {
			return &ValidationError{Name: "state", err: fmt.Errorf(`ent: validator failed for field "Deal.state": %w`, err)}
		}
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (duo *DealUpdateOne) Modify(modifiers ...func(u *sql.UpdateBuilder)) *DealUpdateOne {
	duo.modifiers = append(duo.modifiers, modifiers...)
	return duo
}

func (duo *DealUpdateOne) sqlSave(ctx context.Context) (_node *Deal, err error) {
	if err := duo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(deal.Table, deal.Columns, sqlgraph.NewFieldSpec(deal.FieldID, field.TypeInt))
	id, ok := duo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "Deal.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := duo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, deal.FieldID)
		for _, f := range fields {
			if !deal.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != deal.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := duo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := duo.mutation.DeletedAt(); ok {
		_spec.SetField(deal.FieldDeletedAt, field.TypeTime, value)
	}
	if duo.mutation.DeletedAtCleared() {
		_spec.ClearField(deal.FieldDeletedAt, field.TypeTime)
	}
	if value, ok := duo.mutation.Status(); ok {
		_spec.SetField(deal.FieldStatus, field.TypeInt8, value)
	}
	if value, ok := duo.mutation.AddedStatus(); ok {
		_spec.AddField(deal.FieldStatus, field.TypeInt8, value)
	}
	if value, ok := duo.mutation.Version(); ok {
		_spec.SetField(deal.FieldVersion, field.TypeInt, value)
	}
	if value, ok := duo.mutation.AddedVersion(); ok {
		_spec.AddField(deal.FieldVersion, field.TypeInt, value)
	}
	if value, ok := duo.mutation.UpdatedAt(); ok {
		_spec.SetField(deal.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := duo.mutation.ParentDealID(); ok {
		_spec.SetField(deal.FieldParentDealID, field.TypeInt, value)
	}
	if value, ok := duo.mutation.AddedParentDealID(); ok {
		_spec.AddField(deal.FieldParentDealID, field.TypeInt, value)
	}
	if duo.mutation.ParentDealIDCleared() {
		_spec.ClearField(deal.FieldParentDealID, field.TypeInt)
	}
	if value, ok := duo.mutation.TotalAmount(); ok {
		_spec.SetField(deal.FieldTotalAmount, field.TypeFloat64, value)
	}
	if value, ok := duo.mutation.AddedTotalAmount(); ok {
		_spec.AddField(deal.FieldTotalAmount, field.TypeFloat64, value)
	}
	if value, ok := duo.mutation.StageHistory(); ok {
		_spec.SetField(deal.FieldStageHistory, field.TypeJSON, value)
	}
	if value, ok := duo.mutation.AppendedStageHistory(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, deal.FieldStageHistory, value)
		})
	}
	if duo.mutation.StageHistoryCleared() {
		_spec.ClearField(deal.FieldStageHistory, field.TypeJSON)
	}
	if value, ok := duo.mutation.Name(); ok {
		_spec.SetField(deal.FieldName, field.TypeString, value)
	}
	if duo.mutation.NameCleared() {
		_spec.ClearField(deal.FieldName, field.TypeString)
	}
	if value, ok := duo.mutation.State(); ok {
		_spec.SetField(deal.FieldState, field.TypeEnum, value)
	}
	if duo.mutation.PersonCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   deal.PersonTable,
			Columns: []string{deal.PersonColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(person.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := duo.mutation.PersonIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   deal.PersonTable,
			Columns: []string{deal.PersonColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(person.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if duo.mutation.StageCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   deal.StageTable,
			Columns: []string{deal.StageColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(stage.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := duo.mutation.StageIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   deal.StageTable,
			Columns: []string{deal.StageColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(stage.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if duo.mutation.AttachmentsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   deal.AttachmentsTable,
			Columns: []string{deal.AttachmentsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(attachment.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := duo.mutation.RemovedAttachmentsIDs(); len(nodes) > 0 && !duo.mutation.AttachmentsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   deal.AttachmentsTable,
			Columns: []string{deal.AttachmentsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(attachment.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := duo.mutation.AttachmentsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   deal.AttachmentsTable,
			Columns: []string{deal.AttachmentsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(attachment.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if duo.mutation.PlansCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   deal.PlansTable,
			Columns: []string{deal.PlansColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(installmentplan.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := duo.mutation.RemovedPlansIDs(); len(nodes) > 0 && !duo.mutation.PlansCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   deal.PlansTable,
			Columns: []string{deal.PlansColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(installmentplan.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := duo.mutation.PlansIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   deal.PlansTable,
			Columns: []string{deal.PlansColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(installmentplan.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if duo.mutation.BillsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   deal.BillsTable,
			Columns: []string{deal.BillsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(bill.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := duo.mutation.RemovedBillsIDs(); len(nodes) > 0 && !duo.mutation.BillsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   deal.BillsTable,
			Columns: []string{deal.BillsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(bill.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := duo.mutation.BillsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   deal.BillsTable,
			Columns: []string{deal.BillsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(bill.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if duo.mutation.TracksCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   deal.TracksTable,
			Columns: []string{deal.TracksColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(track.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := duo.mutation.RemovedTracksIDs(); len(nodes) > 0 && !duo.mutation.TracksCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   deal.TracksTable,
			Columns: []string{deal.TracksColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(track.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := duo.mutation.TracksIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   deal.TracksTable,
			Columns: []string{deal.TracksColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(track.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if duo.mutation.TasksCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   deal.TasksTable,
			Columns: []string{deal.TasksColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(task.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := duo.mutation.RemovedTasksIDs(); len(nodes) > 0 && !duo.mutation.TasksCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   deal.TasksTable,
			Columns: []string{deal.TasksColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(task.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := duo.mutation.TasksIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   deal.TasksTable,
			Columns: []string{deal.TasksColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(task.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if duo.mutation.DealAssignmentCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   deal.DealAssignmentTable,
			Columns: []string{deal.DealAssignmentColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(dealuser.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := duo.mutation.RemovedDealAssignmentIDs(); len(nodes) > 0 && !duo.mutation.DealAssignmentCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   deal.DealAssignmentTable,
			Columns: []string{deal.DealAssignmentColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(dealuser.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := duo.mutation.DealAssignmentIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   deal.DealAssignmentTable,
			Columns: []string{deal.DealAssignmentColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(dealuser.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if duo.mutation.TagsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: false,
			Table:   deal.TagsTable,
			Columns: deal.TagsPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tag.FieldID, field.TypeInt),
			},
		}
		createE := &TagDealCreate{config: duo.config, mutation: newTagDealMutation(duo.config, OpCreate)}
		createE.defaults()
		_, specE := createE.createSpec()
		edge.Target.Fields = specE.Fields
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := duo.mutation.RemovedTagsIDs(); len(nodes) > 0 && !duo.mutation.TagsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: false,
			Table:   deal.TagsTable,
			Columns: deal.TagsPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tag.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		createE := &TagDealCreate{config: duo.config, mutation: newTagDealMutation(duo.config, OpCreate)}
		createE.defaults()
		_, specE := createE.createSpec()
		edge.Target.Fields = specE.Fields
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := duo.mutation.TagsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: false,
			Table:   deal.TagsTable,
			Columns: deal.TagsPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tag.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		createE := &TagDealCreate{config: duo.config, mutation: newTagDealMutation(duo.config, OpCreate)}
		createE.defaults()
		_, specE := createE.createSpec()
		edge.Target.Fields = specE.Fields
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if duo.mutation.DepositsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   deal.DepositsTable,
			Columns: []string{deal.DepositsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(deposit.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := duo.mutation.RemovedDepositsIDs(); len(nodes) > 0 && !duo.mutation.DepositsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   deal.DepositsTable,
			Columns: []string{deal.DepositsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(deposit.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := duo.mutation.DepositsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   deal.DepositsTable,
			Columns: []string{deal.DepositsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(deposit.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if duo.mutation.DiscountUsagesCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   deal.DiscountUsagesTable,
			Columns: []string{deal.DiscountUsagesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(discountusage.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := duo.mutation.RemovedDiscountUsagesIDs(); len(nodes) > 0 && !duo.mutation.DiscountUsagesCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   deal.DiscountUsagesTable,
			Columns: []string{deal.DiscountUsagesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(discountusage.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := duo.mutation.DiscountUsagesIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   deal.DiscountUsagesTable,
			Columns: []string{deal.DiscountUsagesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(discountusage.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if duo.mutation.TagDealCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   deal.TagDealTable,
			Columns: []string{deal.TagDealColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tagdeal.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := duo.mutation.RemovedTagDealIDs(); len(nodes) > 0 && !duo.mutation.TagDealCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   deal.TagDealTable,
			Columns: []string{deal.TagDealColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tagdeal.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := duo.mutation.TagDealIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   deal.TagDealTable,
			Columns: []string{deal.TagDealColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tagdeal.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_spec.AddModifiers(duo.modifiers...)
	_node = &Deal{config: duo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, duo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{deal.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	duo.mutation.done = true
	return _node, nil
}
