// Code generated by ent, DO NOT EDIT.

package ent

import (
	"bcare/ent/deal"
	"bcare/ent/person"
	"bcare/ent/pipeline"
	"bcare/ent/stage"
	"bcare/ent/track"
	"bcare/ent/types"
	"bcare/ent/user"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

// Track is the model entity for the Track schema.
type Track struct {
	config `json:"-"`
	// ID of the ent.
	ID int `json:"id,omitempty"`
	// DeletedAt holds the value of the "deleted_at" field.
	DeletedAt time.Time `json:"deleted_at,omitempty"`
	// Status holds the value of the "status" field.
	Status int8 `json:"status,omitempty"`
	// Version holds the value of the "version" field.
	Version int `json:"version,omitempty"`
	// CreatedAt holds the value of the "created_at" field.
	CreatedAt time.Time `json:"created_at,omitempty"`
	// UpdatedAt holds the value of the "updated_at" field.
	UpdatedAt time.Time `json:"updated_at,omitempty"`
	// <PERSON>gin holds the value of the "begin" field.
	Begin time.Time `json:"begin,omitempty"`
	// End holds the value of the "end" field.
	End time.Time `json:"end,omitempty"`
	// DealID holds the value of the "deal_id" field.
	DealID *int `json:"deal_id,omitempty"`
	// PipelineID holds the value of the "pipeline_id" field.
	PipelineID *int `json:"pipeline_id,omitempty"`
	// StageID holds the value of the "stage_id" field.
	StageID *int `json:"stage_id,omitempty"`
	// UserID holds the value of the "user_id" field.
	UserID int `json:"user_id,omitempty"`
	// PersonID holds the value of the "person_id" field.
	PersonID int `json:"person_id,omitempty"`
	// Weight holds the value of the "weight" field.
	Weight float64 `json:"weight,omitempty"`
	// Meta holds the value of the "meta" field.
	Meta struct{} `json:"meta,omitempty"`
	// State holds the value of the "state" field.
	State track.State `json:"state,omitempty"`
	// DealStageID holds the value of the "deal_stage_id" field.
	DealStageID int `json:"deal_stage_id,omitempty"`
	// StageHistory holds the value of the "stage_history" field.
	StageHistory []types.StageHistoryEntry `json:"stage_history,omitempty"`
	// Edges holds the relations/edges for other nodes in the graph.
	// The values are being populated by the TrackQuery when eager-loading is set.
	Edges        TrackEdges `json:"-"`
	selectValues sql.SelectValues
}

// TrackEdges holds the relations/edges for other nodes in the graph.
type TrackEdges struct {
	// Deal holds the value of the deal edge.
	Deal *Deal `json:"deal,omitempty"`
	// User holds the value of the user edge.
	User *User `json:"user,omitempty"`
	// Person holds the value of the person edge.
	Person *Person `json:"person,omitempty"`
	// Pipeline holds the value of the pipeline edge.
	Pipeline *Pipeline `json:"pipeline,omitempty"`
	// Stage holds the value of the stage edge.
	Stage *Stage `json:"stage,omitempty"`
	// Appointments holds the value of the appointments edge.
	Appointments []*Appointment `json:"appointments,omitempty"`
	// FileUsages holds the value of the file_usages edge.
	FileUsages []*FileUsage `json:"file_usages,omitempty"`
	// Attachments holds the value of the attachments edge.
	Attachments []*Attachment `json:"attachments,omitempty"`
	// BillItems holds the value of the bill_items edge.
	BillItems []*BillItem `json:"bill_items,omitempty"`
	// loadedTypes holds the information for reporting if a
	// type was loaded (or requested) in eager-loading or not.
	loadedTypes [9]bool
}

// DealOrErr returns the Deal value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e TrackEdges) DealOrErr() (*Deal, error) {
	if e.Deal != nil {
		return e.Deal, nil
	} else if e.loadedTypes[0] {
		return nil, &NotFoundError{label: deal.Label}
	}
	return nil, &NotLoadedError{edge: "deal"}
}

// UserOrErr returns the User value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e TrackEdges) UserOrErr() (*User, error) {
	if e.User != nil {
		return e.User, nil
	} else if e.loadedTypes[1] {
		return nil, &NotFoundError{label: user.Label}
	}
	return nil, &NotLoadedError{edge: "user"}
}

// PersonOrErr returns the Person value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e TrackEdges) PersonOrErr() (*Person, error) {
	if e.Person != nil {
		return e.Person, nil
	} else if e.loadedTypes[2] {
		return nil, &NotFoundError{label: person.Label}
	}
	return nil, &NotLoadedError{edge: "person"}
}

// PipelineOrErr returns the Pipeline value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e TrackEdges) PipelineOrErr() (*Pipeline, error) {
	if e.Pipeline != nil {
		return e.Pipeline, nil
	} else if e.loadedTypes[3] {
		return nil, &NotFoundError{label: pipeline.Label}
	}
	return nil, &NotLoadedError{edge: "pipeline"}
}

// StageOrErr returns the Stage value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e TrackEdges) StageOrErr() (*Stage, error) {
	if e.Stage != nil {
		return e.Stage, nil
	} else if e.loadedTypes[4] {
		return nil, &NotFoundError{label: stage.Label}
	}
	return nil, &NotLoadedError{edge: "stage"}
}

// AppointmentsOrErr returns the Appointments value or an error if the edge
// was not loaded in eager-loading.
func (e TrackEdges) AppointmentsOrErr() ([]*Appointment, error) {
	if e.loadedTypes[5] {
		return e.Appointments, nil
	}
	return nil, &NotLoadedError{edge: "appointments"}
}

// FileUsagesOrErr returns the FileUsages value or an error if the edge
// was not loaded in eager-loading.
func (e TrackEdges) FileUsagesOrErr() ([]*FileUsage, error) {
	if e.loadedTypes[6] {
		return e.FileUsages, nil
	}
	return nil, &NotLoadedError{edge: "file_usages"}
}

// AttachmentsOrErr returns the Attachments value or an error if the edge
// was not loaded in eager-loading.
func (e TrackEdges) AttachmentsOrErr() ([]*Attachment, error) {
	if e.loadedTypes[7] {
		return e.Attachments, nil
	}
	return nil, &NotLoadedError{edge: "attachments"}
}

// BillItemsOrErr returns the BillItems value or an error if the edge
// was not loaded in eager-loading.
func (e TrackEdges) BillItemsOrErr() ([]*BillItem, error) {
	if e.loadedTypes[8] {
		return e.BillItems, nil
	}
	return nil, &NotLoadedError{edge: "bill_items"}
}

// scanValues returns the types for scanning values from sql.Rows.
func (*Track) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case track.FieldMeta, track.FieldStageHistory:
			values[i] = new([]byte)
		case track.FieldWeight:
			values[i] = new(sql.NullFloat64)
		case track.FieldID, track.FieldStatus, track.FieldVersion, track.FieldDealID, track.FieldPipelineID, track.FieldStageID, track.FieldUserID, track.FieldPersonID, track.FieldDealStageID:
			values[i] = new(sql.NullInt64)
		case track.FieldState:
			values[i] = new(sql.NullString)
		case track.FieldDeletedAt, track.FieldCreatedAt, track.FieldUpdatedAt, track.FieldBegin, track.FieldEnd:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the Track fields.
func (t *Track) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case track.FieldID:
			value, ok := values[i].(*sql.NullInt64)
			if !ok {
				return fmt.Errorf("unexpected type %T for field id", value)
			}
			t.ID = int(value.Int64)
		case track.FieldDeletedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field deleted_at", values[i])
			} else if value.Valid {
				t.DeletedAt = value.Time
			}
		case track.FieldStatus:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field status", values[i])
			} else if value.Valid {
				t.Status = int8(value.Int64)
			}
		case track.FieldVersion:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field version", values[i])
			} else if value.Valid {
				t.Version = int(value.Int64)
			}
		case track.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				t.CreatedAt = value.Time
			}
		case track.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				t.UpdatedAt = value.Time
			}
		case track.FieldBegin:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field begin", values[i])
			} else if value.Valid {
				t.Begin = value.Time
			}
		case track.FieldEnd:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field end", values[i])
			} else if value.Valid {
				t.End = value.Time
			}
		case track.FieldDealID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field deal_id", values[i])
			} else if value.Valid {
				t.DealID = new(int)
				*t.DealID = int(value.Int64)
			}
		case track.FieldPipelineID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field pipeline_id", values[i])
			} else if value.Valid {
				t.PipelineID = new(int)
				*t.PipelineID = int(value.Int64)
			}
		case track.FieldStageID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field stage_id", values[i])
			} else if value.Valid {
				t.StageID = new(int)
				*t.StageID = int(value.Int64)
			}
		case track.FieldUserID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field user_id", values[i])
			} else if value.Valid {
				t.UserID = int(value.Int64)
			}
		case track.FieldPersonID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field person_id", values[i])
			} else if value.Valid {
				t.PersonID = int(value.Int64)
			}
		case track.FieldWeight:
			if value, ok := values[i].(*sql.NullFloat64); !ok {
				return fmt.Errorf("unexpected type %T for field weight", values[i])
			} else if value.Valid {
				t.Weight = value.Float64
			}
		case track.FieldMeta:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field meta", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &t.Meta); err != nil {
					return fmt.Errorf("unmarshal field meta: %w", err)
				}
			}
		case track.FieldState:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field state", values[i])
			} else if value.Valid {
				t.State = track.State(value.String)
			}
		case track.FieldDealStageID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field deal_stage_id", values[i])
			} else if value.Valid {
				t.DealStageID = int(value.Int64)
			}
		case track.FieldStageHistory:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field stage_history", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &t.StageHistory); err != nil {
					return fmt.Errorf("unmarshal field stage_history: %w", err)
				}
			}
		default:
			t.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the Track.
// This includes values selected through modifiers, order, etc.
func (t *Track) Value(name string) (ent.Value, error) {
	return t.selectValues.Get(name)
}

// QueryDeal queries the "deal" edge of the Track entity.
func (t *Track) QueryDeal() *DealQuery {
	return NewTrackClient(t.config).QueryDeal(t)
}

// QueryUser queries the "user" edge of the Track entity.
func (t *Track) QueryUser() *UserQuery {
	return NewTrackClient(t.config).QueryUser(t)
}

// QueryPerson queries the "person" edge of the Track entity.
func (t *Track) QueryPerson() *PersonQuery {
	return NewTrackClient(t.config).QueryPerson(t)
}

// QueryPipeline queries the "pipeline" edge of the Track entity.
func (t *Track) QueryPipeline() *PipelineQuery {
	return NewTrackClient(t.config).QueryPipeline(t)
}

// QueryStage queries the "stage" edge of the Track entity.
func (t *Track) QueryStage() *StageQuery {
	return NewTrackClient(t.config).QueryStage(t)
}

// QueryAppointments queries the "appointments" edge of the Track entity.
func (t *Track) QueryAppointments() *AppointmentQuery {
	return NewTrackClient(t.config).QueryAppointments(t)
}

// QueryFileUsages queries the "file_usages" edge of the Track entity.
func (t *Track) QueryFileUsages() *FileUsageQuery {
	return NewTrackClient(t.config).QueryFileUsages(t)
}

// QueryAttachments queries the "attachments" edge of the Track entity.
func (t *Track) QueryAttachments() *AttachmentQuery {
	return NewTrackClient(t.config).QueryAttachments(t)
}

// QueryBillItems queries the "bill_items" edge of the Track entity.
func (t *Track) QueryBillItems() *BillItemQuery {
	return NewTrackClient(t.config).QueryBillItems(t)
}

// Update returns a builder for updating this Track.
// Note that you need to call Track.Unwrap() before calling this method if this Track
// was returned from a transaction, and the transaction was committed or rolled back.
func (t *Track) Update() *TrackUpdateOne {
	return NewTrackClient(t.config).UpdateOne(t)
}

// Unwrap unwraps the Track entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (t *Track) Unwrap() *Track {
	_tx, ok := t.config.driver.(*txDriver)
	if !ok {
		panic("ent: Track is not a transactional entity")
	}
	t.config.driver = _tx.drv
	return t
}

// String implements the fmt.Stringer.
func (t *Track) String() string {
	var builder strings.Builder
	builder.WriteString("Track(")
	builder.WriteString(fmt.Sprintf("id=%v, ", t.ID))
	builder.WriteString("deleted_at=")
	builder.WriteString(t.DeletedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("status=")
	builder.WriteString(fmt.Sprintf("%v", t.Status))
	builder.WriteString(", ")
	builder.WriteString("version=")
	builder.WriteString(fmt.Sprintf("%v", t.Version))
	builder.WriteString(", ")
	builder.WriteString("created_at=")
	builder.WriteString(t.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(t.UpdatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("begin=")
	builder.WriteString(t.Begin.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("end=")
	builder.WriteString(t.End.Format(time.ANSIC))
	builder.WriteString(", ")
	if v := t.DealID; v != nil {
		builder.WriteString("deal_id=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteString(", ")
	if v := t.PipelineID; v != nil {
		builder.WriteString("pipeline_id=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteString(", ")
	if v := t.StageID; v != nil {
		builder.WriteString("stage_id=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteString(", ")
	builder.WriteString("user_id=")
	builder.WriteString(fmt.Sprintf("%v", t.UserID))
	builder.WriteString(", ")
	builder.WriteString("person_id=")
	builder.WriteString(fmt.Sprintf("%v", t.PersonID))
	builder.WriteString(", ")
	builder.WriteString("weight=")
	builder.WriteString(fmt.Sprintf("%v", t.Weight))
	builder.WriteString(", ")
	builder.WriteString("meta=")
	builder.WriteString(fmt.Sprintf("%v", t.Meta))
	builder.WriteString(", ")
	builder.WriteString("state=")
	builder.WriteString(fmt.Sprintf("%v", t.State))
	builder.WriteString(", ")
	builder.WriteString("deal_stage_id=")
	builder.WriteString(fmt.Sprintf("%v", t.DealStageID))
	builder.WriteString(", ")
	builder.WriteString("stage_history=")
	builder.WriteString(fmt.Sprintf("%v", t.StageHistory))
	builder.WriteByte(')')
	return builder.String()
}

// MarshalJSON implements the json.Marshaler interface.
func (t *Track) MarshalJSON() ([]byte, error) {
	type Alias Track
	return json.Marshal(&struct {
		*Alias
		TrackEdges
	}{
		Alias:      (*Alias)(t),
		TrackEdges: t.Edges,
	})
}

// MarshalSimpleTime
func (t *Track) MarshalSimpleTime() ([]byte, error) {
	type Alias Track
	return json.Marshal(&struct {
		*Alias
		TrackEdges
		DeletedAt string `json:"deleted_at,omitempty"`
		CreatedAt string `json:"created_at,omitempty"`
		UpdatedAt string `json:"updated_at,omitempty"`
		Begin     string `json:"begin,omitempty"`
		End       string `json:"end,omitempty"`
	}{
		Alias:      (*Alias)(t),
		TrackEdges: t.Edges,
		DeletedAt:  t.DeletedAt.Format("15:04 02/01/2006"),
		CreatedAt:  t.CreatedAt.Format("15:04 02/01/2006"),
		UpdatedAt:  t.UpdatedAt.Format("15:04 02/01/2006"),
		Begin:      t.Begin.Format("15:04 02/01/2006"),
		End:        t.End.Format("15:04 02/01/2006"),
	})
}

// Tracks is a parsable slice of Track.
type Tracks []*Track
