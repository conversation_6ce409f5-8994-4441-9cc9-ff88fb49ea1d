// Code generated by ent, DO NOT EDIT.

package ent

import (
	"bcare/ent/discount"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

// Discount is the model entity for the Discount schema.
type Discount struct {
	config `json:"-"`
	// ID of the ent.
	ID int `json:"id,omitempty"`
	// DeletedAt holds the value of the "deleted_at" field.
	DeletedAt time.Time `json:"deleted_at,omitempty"`
	// Status holds the value of the "status" field.
	Status int8 `json:"status,omitempty"`
	// Version holds the value of the "version" field.
	Version int `json:"version,omitempty"`
	// CreatedAt holds the value of the "created_at" field.
	CreatedAt time.Time `json:"created_at,omitempty"`
	// UpdatedAt holds the value of the "updated_at" field.
	UpdatedAt time.Time `json:"updated_at,omitempty"`
	// Name holds the value of the "name" field.
	Name string `json:"name,omitempty"`
	// Type holds the value of the "type" field.
	Type string `json:"type,omitempty"`
	// Value holds the value of the "value" field.
	Value float64 `json:"value,omitempty"`
	// Scope holds the value of the "scope" field.
	Scope string `json:"scope,omitempty"`
	// Condition holds the value of the "condition" field.
	Condition string `json:"condition,omitempty"`
	// UsageType holds the value of the "usage_type" field.
	UsageType string `json:"usage_type,omitempty"`
	// Description holds the value of the "description" field.
	Description string `json:"description,omitempty"`
	// Meta holds the value of the "meta" field.
	Meta map[string]interface{} `json:"meta,omitempty"`
	// Start holds the value of the "start" field.
	Start time.Time `json:"start,omitempty"`
	// End holds the value of the "end" field.
	End time.Time `json:"end,omitempty"`
	// Edges holds the relations/edges for other nodes in the graph.
	// The values are being populated by the DiscountQuery when eager-loading is set.
	Edges        DiscountEdges `json:"-"`
	selectValues sql.SelectValues
}

// DiscountEdges holds the relations/edges for other nodes in the graph.
type DiscountEdges struct {
	// DiscountUsages holds the value of the discount_usages edge.
	DiscountUsages []*DiscountUsage `json:"discount_usages,omitempty"`
	// loadedTypes holds the information for reporting if a
	// type was loaded (or requested) in eager-loading or not.
	loadedTypes [1]bool
}

// DiscountUsagesOrErr returns the DiscountUsages value or an error if the edge
// was not loaded in eager-loading.
func (e DiscountEdges) DiscountUsagesOrErr() ([]*DiscountUsage, error) {
	if e.loadedTypes[0] {
		return e.DiscountUsages, nil
	}
	return nil, &NotLoadedError{edge: "discount_usages"}
}

// scanValues returns the types for scanning values from sql.Rows.
func (*Discount) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case discount.FieldMeta:
			values[i] = new([]byte)
		case discount.FieldValue:
			values[i] = new(sql.NullFloat64)
		case discount.FieldID, discount.FieldStatus, discount.FieldVersion:
			values[i] = new(sql.NullInt64)
		case discount.FieldName, discount.FieldType, discount.FieldScope, discount.FieldCondition, discount.FieldUsageType, discount.FieldDescription:
			values[i] = new(sql.NullString)
		case discount.FieldDeletedAt, discount.FieldCreatedAt, discount.FieldUpdatedAt, discount.FieldStart, discount.FieldEnd:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the Discount fields.
func (d *Discount) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case discount.FieldID:
			value, ok := values[i].(*sql.NullInt64)
			if !ok {
				return fmt.Errorf("unexpected type %T for field id", value)
			}
			d.ID = int(value.Int64)
		case discount.FieldDeletedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field deleted_at", values[i])
			} else if value.Valid {
				d.DeletedAt = value.Time
			}
		case discount.FieldStatus:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field status", values[i])
			} else if value.Valid {
				d.Status = int8(value.Int64)
			}
		case discount.FieldVersion:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field version", values[i])
			} else if value.Valid {
				d.Version = int(value.Int64)
			}
		case discount.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				d.CreatedAt = value.Time
			}
		case discount.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				d.UpdatedAt = value.Time
			}
		case discount.FieldName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field name", values[i])
			} else if value.Valid {
				d.Name = value.String
			}
		case discount.FieldType:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field type", values[i])
			} else if value.Valid {
				d.Type = value.String
			}
		case discount.FieldValue:
			if value, ok := values[i].(*sql.NullFloat64); !ok {
				return fmt.Errorf("unexpected type %T for field value", values[i])
			} else if value.Valid {
				d.Value = value.Float64
			}
		case discount.FieldScope:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field scope", values[i])
			} else if value.Valid {
				d.Scope = value.String
			}
		case discount.FieldCondition:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field condition", values[i])
			} else if value.Valid {
				d.Condition = value.String
			}
		case discount.FieldUsageType:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field usage_type", values[i])
			} else if value.Valid {
				d.UsageType = value.String
			}
		case discount.FieldDescription:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field description", values[i])
			} else if value.Valid {
				d.Description = value.String
			}
		case discount.FieldMeta:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field meta", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &d.Meta); err != nil {
					return fmt.Errorf("unmarshal field meta: %w", err)
				}
			}
		case discount.FieldStart:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field start", values[i])
			} else if value.Valid {
				d.Start = value.Time
			}
		case discount.FieldEnd:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field end", values[i])
			} else if value.Valid {
				d.End = value.Time
			}
		default:
			d.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// GetValue returns the ent.Value that was dynamically selected and assigned to the Discount.
// This includes values selected through modifiers, order, etc.
func (d *Discount) GetValue(name string) (ent.Value, error) {
	return d.selectValues.Get(name)
}

// QueryDiscountUsages queries the "discount_usages" edge of the Discount entity.
func (d *Discount) QueryDiscountUsages() *DiscountUsageQuery {
	return NewDiscountClient(d.config).QueryDiscountUsages(d)
}

// Update returns a builder for updating this Discount.
// Note that you need to call Discount.Unwrap() before calling this method if this Discount
// was returned from a transaction, and the transaction was committed or rolled back.
func (d *Discount) Update() *DiscountUpdateOne {
	return NewDiscountClient(d.config).UpdateOne(d)
}

// Unwrap unwraps the Discount entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (d *Discount) Unwrap() *Discount {
	_tx, ok := d.config.driver.(*txDriver)
	if !ok {
		panic("ent: Discount is not a transactional entity")
	}
	d.config.driver = _tx.drv
	return d
}

// String implements the fmt.Stringer.
func (d *Discount) String() string {
	var builder strings.Builder
	builder.WriteString("Discount(")
	builder.WriteString(fmt.Sprintf("id=%v, ", d.ID))
	builder.WriteString("deleted_at=")
	builder.WriteString(d.DeletedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("status=")
	builder.WriteString(fmt.Sprintf("%v", d.Status))
	builder.WriteString(", ")
	builder.WriteString("version=")
	builder.WriteString(fmt.Sprintf("%v", d.Version))
	builder.WriteString(", ")
	builder.WriteString("created_at=")
	builder.WriteString(d.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(d.UpdatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("name=")
	builder.WriteString(d.Name)
	builder.WriteString(", ")
	builder.WriteString("type=")
	builder.WriteString(d.Type)
	builder.WriteString(", ")
	builder.WriteString("value=")
	builder.WriteString(fmt.Sprintf("%v", d.Value))
	builder.WriteString(", ")
	builder.WriteString("scope=")
	builder.WriteString(d.Scope)
	builder.WriteString(", ")
	builder.WriteString("condition=")
	builder.WriteString(d.Condition)
	builder.WriteString(", ")
	builder.WriteString("usage_type=")
	builder.WriteString(d.UsageType)
	builder.WriteString(", ")
	builder.WriteString("description=")
	builder.WriteString(d.Description)
	builder.WriteString(", ")
	builder.WriteString("meta=")
	builder.WriteString(fmt.Sprintf("%v", d.Meta))
	builder.WriteString(", ")
	builder.WriteString("start=")
	builder.WriteString(d.Start.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("end=")
	builder.WriteString(d.End.Format(time.ANSIC))
	builder.WriteByte(')')
	return builder.String()
}

// MarshalJSON implements the json.Marshaler interface.
func (d *Discount) MarshalJSON() ([]byte, error) {
	type Alias Discount
	return json.Marshal(&struct {
		*Alias
		DiscountEdges
	}{
		Alias:         (*Alias)(d),
		DiscountEdges: d.Edges,
	})
}

// MarshalSimpleTime
func (d *Discount) MarshalSimpleTime() ([]byte, error) {
	type Alias Discount
	return json.Marshal(&struct {
		*Alias
		DiscountEdges
		DeletedAt string `json:"deleted_at,omitempty"`
		CreatedAt string `json:"created_at,omitempty"`
		UpdatedAt string `json:"updated_at,omitempty"`
		Start     string `json:"start,omitempty"`
		End       string `json:"end,omitempty"`
	}{
		Alias:         (*Alias)(d),
		DiscountEdges: d.Edges,
		DeletedAt:     d.DeletedAt.Format("15:04 02/01/2006"),
		CreatedAt:     d.CreatedAt.Format("15:04 02/01/2006"),
		UpdatedAt:     d.UpdatedAt.Format("15:04 02/01/2006"),
		Start:         d.Start.Format("15:04 02/01/2006"),
		End:           d.End.Format("15:04 02/01/2006"),
	})
}

// Discounts is a parsable slice of Discount.
type Discounts []*Discount
