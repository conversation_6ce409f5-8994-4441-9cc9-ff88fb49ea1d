package types

import "time"

type PersonMeta struct {
	TreatmentID       *int    `json:"treatment_id"`
	TreatmentStatusID *int    `json:"treatment_status_id"`
	Description       *string `json:"description"`
	PancakeLink       *string `json:"pancake_link"`
	HasZalo           *string `json:"has_zalo,options=yes|no|unknown"`
	SecondaryPhone    *string `json:"secondary_phone"`
	Code              *string `json:"code"`
	MedicalCondition  *string `json:"medical_condition"`
	SpecialNote       *string `json:"special_note"`
	AccountId         *string `json:"account_id"`
}

type AttachmentMeta struct {
	CoordinatorId     *int    `json:"coordinator_id"`
	TechnicianId      *int    `json:"technician_id"`
	AssistantId       *int    `json:"assistant_id"`
	Description       *string `json:"description"`
	ToothPosition     *string `json:"tooth_position"`
	ToothPositionDesc *string `json:"tooth_position_desc"`
	Instruction       *string `json:"instruction"`
}

type StageHistoryEntry struct {
	StageID   *int       `json:"stage_id"`
	EnteredAt time.Time  `json:"entered_at"`
	ExitedAt  *time.Time `json:"exited_at,omitempty"`
	UserId    int        `json:"user_id,omitempty"`
	Reason    string     `json:"reason,omitempty"`
}

type TaskFieldChange struct {
	Field    string      `json:"field"`
	OldValue interface{} `json:"old_value"`
	NewValue interface{} `json:"new_value"`
}

type TaskHistoryEntry struct {
	UserID    int               `json:"user_id"`
	Changes   []TaskFieldChange `json:"changes"`
	Timestamp time.Time         `json:"timestamp"`
}

// PaymentAllocationDetail represents the structure of each payment allocation entry
type PaymentAllocation struct {
	ProductName string    `json:"product_name"`
	Code        string    `json:"code"`
	Category    string    `json:"category"`
	Quantity    int       `json:"quantity"`
	Price       float64   `json:"price"` // Unit price
	Discount    float64   `json:"discount"`
	Amount      float64   `json:"amount"` // Total amount for this allocation
	Creator     *UserInfo `json:"creator,omitempty"`
	Payer       *UserInfo `json:"payer,omitempty"`
	PaidAmount  float64   `json:"paid_amount"`
}

// UserInfo represents user information in the payment allocation
type UserInfo struct {
	ID       int    `json:"id"`
	Username string `json:"username"`
	Name     string `json:"name"`
}

var (
	AppointmentStatus = map[int8]string{
		-1: "Đã xoá",
		1:  "Huỷ hẹn",
		2:  "Hẹn mới",
		3:  "Đúng hẹn",
		4:  "Trễ hẹn",
		5:  "Đổi hẹn",
		6:  "Phát sinh",
	}
)
