// Code generated by ent, DO NOT EDIT.

package ent

import (
	"bcare/ent/discount"
	"bcare/ent/discountusage"
	"bcare/ent/predicate"
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// DiscountUpdate is the builder for updating Discount entities.
type DiscountUpdate struct {
	config
	hooks     []Hook
	mutation  *DiscountMutation
	modifiers []func(*sql.UpdateBuilder)
}

// Where appends a list predicates to the DiscountUpdate builder.
func (du *DiscountUpdate) Where(ps ...predicate.Discount) *DiscountUpdate {
	du.mutation.Where(ps...)
	return du
}

// SetDeletedAt sets the "deleted_at" field.
func (du *DiscountUpdate) SetDeletedAt(t time.Time) *DiscountUpdate {
	du.mutation.SetDeletedAt(t)
	return du
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (du *DiscountUpdate) SetNillableDeletedAt(t *time.Time) *DiscountUpdate {
	if t != nil {
		du.SetDeletedAt(*t)
	}
	return du
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (du *DiscountUpdate) ClearDeletedAt() *DiscountUpdate {
	du.mutation.ClearDeletedAt()
	return du
}

// SetStatus sets the "status" field.
func (du *DiscountUpdate) SetStatus(i int8) *DiscountUpdate {
	du.mutation.ResetStatus()
	du.mutation.SetStatus(i)
	return du
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (du *DiscountUpdate) SetNillableStatus(i *int8) *DiscountUpdate {
	if i != nil {
		du.SetStatus(*i)
	}
	return du
}

// AddStatus adds i to the "status" field.
func (du *DiscountUpdate) AddStatus(i int8) *DiscountUpdate {
	du.mutation.AddStatus(i)
	return du
}

// SetVersion sets the "version" field.
func (du *DiscountUpdate) SetVersion(i int) *DiscountUpdate {
	du.mutation.ResetVersion()
	du.mutation.SetVersion(i)
	return du
}

// SetNillableVersion sets the "version" field if the given value is not nil.
func (du *DiscountUpdate) SetNillableVersion(i *int) *DiscountUpdate {
	if i != nil {
		du.SetVersion(*i)
	}
	return du
}

// AddVersion adds i to the "version" field.
func (du *DiscountUpdate) AddVersion(i int) *DiscountUpdate {
	du.mutation.AddVersion(i)
	return du
}

// SetUpdatedAt sets the "updated_at" field.
func (du *DiscountUpdate) SetUpdatedAt(t time.Time) *DiscountUpdate {
	du.mutation.SetUpdatedAt(t)
	return du
}

// SetName sets the "name" field.
func (du *DiscountUpdate) SetName(s string) *DiscountUpdate {
	du.mutation.SetName(s)
	return du
}

// SetNillableName sets the "name" field if the given value is not nil.
func (du *DiscountUpdate) SetNillableName(s *string) *DiscountUpdate {
	if s != nil {
		du.SetName(*s)
	}
	return du
}

// SetType sets the "type" field.
func (du *DiscountUpdate) SetType(s string) *DiscountUpdate {
	du.mutation.SetType(s)
	return du
}

// SetNillableType sets the "type" field if the given value is not nil.
func (du *DiscountUpdate) SetNillableType(s *string) *DiscountUpdate {
	if s != nil {
		du.SetType(*s)
	}
	return du
}

// SetValue sets the "value" field.
func (du *DiscountUpdate) SetValue(f float64) *DiscountUpdate {
	du.mutation.ResetValue()
	du.mutation.SetValue(f)
	return du
}

// SetNillableValue sets the "value" field if the given value is not nil.
func (du *DiscountUpdate) SetNillableValue(f *float64) *DiscountUpdate {
	if f != nil {
		du.SetValue(*f)
	}
	return du
}

// AddValue adds f to the "value" field.
func (du *DiscountUpdate) AddValue(f float64) *DiscountUpdate {
	du.mutation.AddValue(f)
	return du
}

// SetScope sets the "scope" field.
func (du *DiscountUpdate) SetScope(s string) *DiscountUpdate {
	du.mutation.SetScope(s)
	return du
}

// SetNillableScope sets the "scope" field if the given value is not nil.
func (du *DiscountUpdate) SetNillableScope(s *string) *DiscountUpdate {
	if s != nil {
		du.SetScope(*s)
	}
	return du
}

// SetCondition sets the "condition" field.
func (du *DiscountUpdate) SetCondition(s string) *DiscountUpdate {
	du.mutation.SetCondition(s)
	return du
}

// SetNillableCondition sets the "condition" field if the given value is not nil.
func (du *DiscountUpdate) SetNillableCondition(s *string) *DiscountUpdate {
	if s != nil {
		du.SetCondition(*s)
	}
	return du
}

// ClearCondition clears the value of the "condition" field.
func (du *DiscountUpdate) ClearCondition() *DiscountUpdate {
	du.mutation.ClearCondition()
	return du
}

// SetUsageType sets the "usage_type" field.
func (du *DiscountUpdate) SetUsageType(s string) *DiscountUpdate {
	du.mutation.SetUsageType(s)
	return du
}

// SetNillableUsageType sets the "usage_type" field if the given value is not nil.
func (du *DiscountUpdate) SetNillableUsageType(s *string) *DiscountUpdate {
	if s != nil {
		du.SetUsageType(*s)
	}
	return du
}

// SetDescription sets the "description" field.
func (du *DiscountUpdate) SetDescription(s string) *DiscountUpdate {
	du.mutation.SetDescription(s)
	return du
}

// SetNillableDescription sets the "description" field if the given value is not nil.
func (du *DiscountUpdate) SetNillableDescription(s *string) *DiscountUpdate {
	if s != nil {
		du.SetDescription(*s)
	}
	return du
}

// ClearDescription clears the value of the "description" field.
func (du *DiscountUpdate) ClearDescription() *DiscountUpdate {
	du.mutation.ClearDescription()
	return du
}

// SetMeta sets the "meta" field.
func (du *DiscountUpdate) SetMeta(m map[string]interface{}) *DiscountUpdate {
	du.mutation.SetMeta(m)
	return du
}

// ClearMeta clears the value of the "meta" field.
func (du *DiscountUpdate) ClearMeta() *DiscountUpdate {
	du.mutation.ClearMeta()
	return du
}

// SetStart sets the "start" field.
func (du *DiscountUpdate) SetStart(t time.Time) *DiscountUpdate {
	du.mutation.SetStart(t)
	return du
}

// SetNillableStart sets the "start" field if the given value is not nil.
func (du *DiscountUpdate) SetNillableStart(t *time.Time) *DiscountUpdate {
	if t != nil {
		du.SetStart(*t)
	}
	return du
}

// ClearStart clears the value of the "start" field.
func (du *DiscountUpdate) ClearStart() *DiscountUpdate {
	du.mutation.ClearStart()
	return du
}

// SetEnd sets the "end" field.
func (du *DiscountUpdate) SetEnd(t time.Time) *DiscountUpdate {
	du.mutation.SetEnd(t)
	return du
}

// SetNillableEnd sets the "end" field if the given value is not nil.
func (du *DiscountUpdate) SetNillableEnd(t *time.Time) *DiscountUpdate {
	if t != nil {
		du.SetEnd(*t)
	}
	return du
}

// ClearEnd clears the value of the "end" field.
func (du *DiscountUpdate) ClearEnd() *DiscountUpdate {
	du.mutation.ClearEnd()
	return du
}

// AddDiscountUsageIDs adds the "discount_usages" edge to the DiscountUsage entity by IDs.
func (du *DiscountUpdate) AddDiscountUsageIDs(ids ...int) *DiscountUpdate {
	du.mutation.AddDiscountUsageIDs(ids...)
	return du
}

// AddDiscountUsages adds the "discount_usages" edges to the DiscountUsage entity.
func (du *DiscountUpdate) AddDiscountUsages(d ...*DiscountUsage) *DiscountUpdate {
	ids := make([]int, len(d))
	for i := range d {
		ids[i] = d[i].ID
	}
	return du.AddDiscountUsageIDs(ids...)
}

// Mutation returns the DiscountMutation object of the builder.
func (du *DiscountUpdate) Mutation() *DiscountMutation {
	return du.mutation
}

// ClearDiscountUsages clears all "discount_usages" edges to the DiscountUsage entity.
func (du *DiscountUpdate) ClearDiscountUsages() *DiscountUpdate {
	du.mutation.ClearDiscountUsages()
	return du
}

// RemoveDiscountUsageIDs removes the "discount_usages" edge to DiscountUsage entities by IDs.
func (du *DiscountUpdate) RemoveDiscountUsageIDs(ids ...int) *DiscountUpdate {
	du.mutation.RemoveDiscountUsageIDs(ids...)
	return du
}

// RemoveDiscountUsages removes "discount_usages" edges to DiscountUsage entities.
func (du *DiscountUpdate) RemoveDiscountUsages(d ...*DiscountUsage) *DiscountUpdate {
	ids := make([]int, len(d))
	for i := range d {
		ids[i] = d[i].ID
	}
	return du.RemoveDiscountUsageIDs(ids...)
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (du *DiscountUpdate) Save(ctx context.Context) (int, error) {
	if err := du.defaults(); err != nil {
		return 0, err
	}
	return withHooks(ctx, du.sqlSave, du.mutation, du.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (du *DiscountUpdate) SaveX(ctx context.Context) int {
	affected, err := du.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (du *DiscountUpdate) Exec(ctx context.Context) error {
	_, err := du.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (du *DiscountUpdate) ExecX(ctx context.Context) {
	if err := du.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (du *DiscountUpdate) defaults() error {
	if _, ok := du.mutation.UpdatedAt(); !ok {
		if discount.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized discount.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := discount.UpdateDefaultUpdatedAt()
		du.mutation.SetUpdatedAt(v)
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (du *DiscountUpdate) Modify(modifiers ...func(u *sql.UpdateBuilder)) *DiscountUpdate {
	du.modifiers = append(du.modifiers, modifiers...)
	return du
}

func (du *DiscountUpdate) sqlSave(ctx context.Context) (n int, err error) {
	_spec := sqlgraph.NewUpdateSpec(discount.Table, discount.Columns, sqlgraph.NewFieldSpec(discount.FieldID, field.TypeInt))
	if ps := du.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := du.mutation.DeletedAt(); ok {
		_spec.SetField(discount.FieldDeletedAt, field.TypeTime, value)
	}
	if du.mutation.DeletedAtCleared() {
		_spec.ClearField(discount.FieldDeletedAt, field.TypeTime)
	}
	if value, ok := du.mutation.Status(); ok {
		_spec.SetField(discount.FieldStatus, field.TypeInt8, value)
	}
	if value, ok := du.mutation.AddedStatus(); ok {
		_spec.AddField(discount.FieldStatus, field.TypeInt8, value)
	}
	if value, ok := du.mutation.Version(); ok {
		_spec.SetField(discount.FieldVersion, field.TypeInt, value)
	}
	if value, ok := du.mutation.AddedVersion(); ok {
		_spec.AddField(discount.FieldVersion, field.TypeInt, value)
	}
	if value, ok := du.mutation.UpdatedAt(); ok {
		_spec.SetField(discount.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := du.mutation.Name(); ok {
		_spec.SetField(discount.FieldName, field.TypeString, value)
	}
	if value, ok := du.mutation.GetType(); ok {
		_spec.SetField(discount.FieldType, field.TypeString, value)
	}
	if value, ok := du.mutation.Value(); ok {
		_spec.SetField(discount.FieldValue, field.TypeFloat64, value)
	}
	if value, ok := du.mutation.AddedValue(); ok {
		_spec.AddField(discount.FieldValue, field.TypeFloat64, value)
	}
	if value, ok := du.mutation.Scope(); ok {
		_spec.SetField(discount.FieldScope, field.TypeString, value)
	}
	if value, ok := du.mutation.Condition(); ok {
		_spec.SetField(discount.FieldCondition, field.TypeString, value)
	}
	if du.mutation.ConditionCleared() {
		_spec.ClearField(discount.FieldCondition, field.TypeString)
	}
	if value, ok := du.mutation.UsageType(); ok {
		_spec.SetField(discount.FieldUsageType, field.TypeString, value)
	}
	if value, ok := du.mutation.Description(); ok {
		_spec.SetField(discount.FieldDescription, field.TypeString, value)
	}
	if du.mutation.DescriptionCleared() {
		_spec.ClearField(discount.FieldDescription, field.TypeString)
	}
	if value, ok := du.mutation.Meta(); ok {
		_spec.SetField(discount.FieldMeta, field.TypeJSON, value)
	}
	if du.mutation.MetaCleared() {
		_spec.ClearField(discount.FieldMeta, field.TypeJSON)
	}
	if value, ok := du.mutation.Start(); ok {
		_spec.SetField(discount.FieldStart, field.TypeTime, value)
	}
	if du.mutation.StartCleared() {
		_spec.ClearField(discount.FieldStart, field.TypeTime)
	}
	if value, ok := du.mutation.End(); ok {
		_spec.SetField(discount.FieldEnd, field.TypeTime, value)
	}
	if du.mutation.EndCleared() {
		_spec.ClearField(discount.FieldEnd, field.TypeTime)
	}
	if du.mutation.DiscountUsagesCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   discount.DiscountUsagesTable,
			Columns: []string{discount.DiscountUsagesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(discountusage.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := du.mutation.RemovedDiscountUsagesIDs(); len(nodes) > 0 && !du.mutation.DiscountUsagesCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   discount.DiscountUsagesTable,
			Columns: []string{discount.DiscountUsagesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(discountusage.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := du.mutation.DiscountUsagesIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   discount.DiscountUsagesTable,
			Columns: []string{discount.DiscountUsagesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(discountusage.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_spec.AddModifiers(du.modifiers...)
	if n, err = sqlgraph.UpdateNodes(ctx, du.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{discount.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	du.mutation.done = true
	return n, nil
}

// DiscountUpdateOne is the builder for updating a single Discount entity.
type DiscountUpdateOne struct {
	config
	fields    []string
	hooks     []Hook
	mutation  *DiscountMutation
	modifiers []func(*sql.UpdateBuilder)
}

// SetDeletedAt sets the "deleted_at" field.
func (duo *DiscountUpdateOne) SetDeletedAt(t time.Time) *DiscountUpdateOne {
	duo.mutation.SetDeletedAt(t)
	return duo
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (duo *DiscountUpdateOne) SetNillableDeletedAt(t *time.Time) *DiscountUpdateOne {
	if t != nil {
		duo.SetDeletedAt(*t)
	}
	return duo
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (duo *DiscountUpdateOne) ClearDeletedAt() *DiscountUpdateOne {
	duo.mutation.ClearDeletedAt()
	return duo
}

// SetStatus sets the "status" field.
func (duo *DiscountUpdateOne) SetStatus(i int8) *DiscountUpdateOne {
	duo.mutation.ResetStatus()
	duo.mutation.SetStatus(i)
	return duo
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (duo *DiscountUpdateOne) SetNillableStatus(i *int8) *DiscountUpdateOne {
	if i != nil {
		duo.SetStatus(*i)
	}
	return duo
}

// AddStatus adds i to the "status" field.
func (duo *DiscountUpdateOne) AddStatus(i int8) *DiscountUpdateOne {
	duo.mutation.AddStatus(i)
	return duo
}

// SetVersion sets the "version" field.
func (duo *DiscountUpdateOne) SetVersion(i int) *DiscountUpdateOne {
	duo.mutation.ResetVersion()
	duo.mutation.SetVersion(i)
	return duo
}

// SetNillableVersion sets the "version" field if the given value is not nil.
func (duo *DiscountUpdateOne) SetNillableVersion(i *int) *DiscountUpdateOne {
	if i != nil {
		duo.SetVersion(*i)
	}
	return duo
}

// AddVersion adds i to the "version" field.
func (duo *DiscountUpdateOne) AddVersion(i int) *DiscountUpdateOne {
	duo.mutation.AddVersion(i)
	return duo
}

// SetUpdatedAt sets the "updated_at" field.
func (duo *DiscountUpdateOne) SetUpdatedAt(t time.Time) *DiscountUpdateOne {
	duo.mutation.SetUpdatedAt(t)
	return duo
}

// SetName sets the "name" field.
func (duo *DiscountUpdateOne) SetName(s string) *DiscountUpdateOne {
	duo.mutation.SetName(s)
	return duo
}

// SetNillableName sets the "name" field if the given value is not nil.
func (duo *DiscountUpdateOne) SetNillableName(s *string) *DiscountUpdateOne {
	if s != nil {
		duo.SetName(*s)
	}
	return duo
}

// SetType sets the "type" field.
func (duo *DiscountUpdateOne) SetType(s string) *DiscountUpdateOne {
	duo.mutation.SetType(s)
	return duo
}

// SetNillableType sets the "type" field if the given value is not nil.
func (duo *DiscountUpdateOne) SetNillableType(s *string) *DiscountUpdateOne {
	if s != nil {
		duo.SetType(*s)
	}
	return duo
}

// SetValue sets the "value" field.
func (duo *DiscountUpdateOne) SetValue(f float64) *DiscountUpdateOne {
	duo.mutation.ResetValue()
	duo.mutation.SetValue(f)
	return duo
}

// SetNillableValue sets the "value" field if the given value is not nil.
func (duo *DiscountUpdateOne) SetNillableValue(f *float64) *DiscountUpdateOne {
	if f != nil {
		duo.SetValue(*f)
	}
	return duo
}

// AddValue adds f to the "value" field.
func (duo *DiscountUpdateOne) AddValue(f float64) *DiscountUpdateOne {
	duo.mutation.AddValue(f)
	return duo
}

// SetScope sets the "scope" field.
func (duo *DiscountUpdateOne) SetScope(s string) *DiscountUpdateOne {
	duo.mutation.SetScope(s)
	return duo
}

// SetNillableScope sets the "scope" field if the given value is not nil.
func (duo *DiscountUpdateOne) SetNillableScope(s *string) *DiscountUpdateOne {
	if s != nil {
		duo.SetScope(*s)
	}
	return duo
}

// SetCondition sets the "condition" field.
func (duo *DiscountUpdateOne) SetCondition(s string) *DiscountUpdateOne {
	duo.mutation.SetCondition(s)
	return duo
}

// SetNillableCondition sets the "condition" field if the given value is not nil.
func (duo *DiscountUpdateOne) SetNillableCondition(s *string) *DiscountUpdateOne {
	if s != nil {
		duo.SetCondition(*s)
	}
	return duo
}

// ClearCondition clears the value of the "condition" field.
func (duo *DiscountUpdateOne) ClearCondition() *DiscountUpdateOne {
	duo.mutation.ClearCondition()
	return duo
}

// SetUsageType sets the "usage_type" field.
func (duo *DiscountUpdateOne) SetUsageType(s string) *DiscountUpdateOne {
	duo.mutation.SetUsageType(s)
	return duo
}

// SetNillableUsageType sets the "usage_type" field if the given value is not nil.
func (duo *DiscountUpdateOne) SetNillableUsageType(s *string) *DiscountUpdateOne {
	if s != nil {
		duo.SetUsageType(*s)
	}
	return duo
}

// SetDescription sets the "description" field.
func (duo *DiscountUpdateOne) SetDescription(s string) *DiscountUpdateOne {
	duo.mutation.SetDescription(s)
	return duo
}

// SetNillableDescription sets the "description" field if the given value is not nil.
func (duo *DiscountUpdateOne) SetNillableDescription(s *string) *DiscountUpdateOne {
	if s != nil {
		duo.SetDescription(*s)
	}
	return duo
}

// ClearDescription clears the value of the "description" field.
func (duo *DiscountUpdateOne) ClearDescription() *DiscountUpdateOne {
	duo.mutation.ClearDescription()
	return duo
}

// SetMeta sets the "meta" field.
func (duo *DiscountUpdateOne) SetMeta(m map[string]interface{}) *DiscountUpdateOne {
	duo.mutation.SetMeta(m)
	return duo
}

// ClearMeta clears the value of the "meta" field.
func (duo *DiscountUpdateOne) ClearMeta() *DiscountUpdateOne {
	duo.mutation.ClearMeta()
	return duo
}

// SetStart sets the "start" field.
func (duo *DiscountUpdateOne) SetStart(t time.Time) *DiscountUpdateOne {
	duo.mutation.SetStart(t)
	return duo
}

// SetNillableStart sets the "start" field if the given value is not nil.
func (duo *DiscountUpdateOne) SetNillableStart(t *time.Time) *DiscountUpdateOne {
	if t != nil {
		duo.SetStart(*t)
	}
	return duo
}

// ClearStart clears the value of the "start" field.
func (duo *DiscountUpdateOne) ClearStart() *DiscountUpdateOne {
	duo.mutation.ClearStart()
	return duo
}

// SetEnd sets the "end" field.
func (duo *DiscountUpdateOne) SetEnd(t time.Time) *DiscountUpdateOne {
	duo.mutation.SetEnd(t)
	return duo
}

// SetNillableEnd sets the "end" field if the given value is not nil.
func (duo *DiscountUpdateOne) SetNillableEnd(t *time.Time) *DiscountUpdateOne {
	if t != nil {
		duo.SetEnd(*t)
	}
	return duo
}

// ClearEnd clears the value of the "end" field.
func (duo *DiscountUpdateOne) ClearEnd() *DiscountUpdateOne {
	duo.mutation.ClearEnd()
	return duo
}

// AddDiscountUsageIDs adds the "discount_usages" edge to the DiscountUsage entity by IDs.
func (duo *DiscountUpdateOne) AddDiscountUsageIDs(ids ...int) *DiscountUpdateOne {
	duo.mutation.AddDiscountUsageIDs(ids...)
	return duo
}

// AddDiscountUsages adds the "discount_usages" edges to the DiscountUsage entity.
func (duo *DiscountUpdateOne) AddDiscountUsages(d ...*DiscountUsage) *DiscountUpdateOne {
	ids := make([]int, len(d))
	for i := range d {
		ids[i] = d[i].ID
	}
	return duo.AddDiscountUsageIDs(ids...)
}

// Mutation returns the DiscountMutation object of the builder.
func (duo *DiscountUpdateOne) Mutation() *DiscountMutation {
	return duo.mutation
}

// ClearDiscountUsages clears all "discount_usages" edges to the DiscountUsage entity.
func (duo *DiscountUpdateOne) ClearDiscountUsages() *DiscountUpdateOne {
	duo.mutation.ClearDiscountUsages()
	return duo
}

// RemoveDiscountUsageIDs removes the "discount_usages" edge to DiscountUsage entities by IDs.
func (duo *DiscountUpdateOne) RemoveDiscountUsageIDs(ids ...int) *DiscountUpdateOne {
	duo.mutation.RemoveDiscountUsageIDs(ids...)
	return duo
}

// RemoveDiscountUsages removes "discount_usages" edges to DiscountUsage entities.
func (duo *DiscountUpdateOne) RemoveDiscountUsages(d ...*DiscountUsage) *DiscountUpdateOne {
	ids := make([]int, len(d))
	for i := range d {
		ids[i] = d[i].ID
	}
	return duo.RemoveDiscountUsageIDs(ids...)
}

// Where appends a list predicates to the DiscountUpdate builder.
func (duo *DiscountUpdateOne) Where(ps ...predicate.Discount) *DiscountUpdateOne {
	duo.mutation.Where(ps...)
	return duo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (duo *DiscountUpdateOne) Select(field string, fields ...string) *DiscountUpdateOne {
	duo.fields = append([]string{field}, fields...)
	return duo
}

// Save executes the query and returns the updated Discount entity.
func (duo *DiscountUpdateOne) Save(ctx context.Context) (*Discount, error) {
	if err := duo.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, duo.sqlSave, duo.mutation, duo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (duo *DiscountUpdateOne) SaveX(ctx context.Context) *Discount {
	node, err := duo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (duo *DiscountUpdateOne) Exec(ctx context.Context) error {
	_, err := duo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (duo *DiscountUpdateOne) ExecX(ctx context.Context) {
	if err := duo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (duo *DiscountUpdateOne) defaults() error {
	if _, ok := duo.mutation.UpdatedAt(); !ok {
		if discount.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized discount.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := discount.UpdateDefaultUpdatedAt()
		duo.mutation.SetUpdatedAt(v)
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (duo *DiscountUpdateOne) Modify(modifiers ...func(u *sql.UpdateBuilder)) *DiscountUpdateOne {
	duo.modifiers = append(duo.modifiers, modifiers...)
	return duo
}

func (duo *DiscountUpdateOne) sqlSave(ctx context.Context) (_node *Discount, err error) {
	_spec := sqlgraph.NewUpdateSpec(discount.Table, discount.Columns, sqlgraph.NewFieldSpec(discount.FieldID, field.TypeInt))
	id, ok := duo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "Discount.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := duo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, discount.FieldID)
		for _, f := range fields {
			if !discount.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != discount.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := duo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := duo.mutation.DeletedAt(); ok {
		_spec.SetField(discount.FieldDeletedAt, field.TypeTime, value)
	}
	if duo.mutation.DeletedAtCleared() {
		_spec.ClearField(discount.FieldDeletedAt, field.TypeTime)
	}
	if value, ok := duo.mutation.Status(); ok {
		_spec.SetField(discount.FieldStatus, field.TypeInt8, value)
	}
	if value, ok := duo.mutation.AddedStatus(); ok {
		_spec.AddField(discount.FieldStatus, field.TypeInt8, value)
	}
	if value, ok := duo.mutation.Version(); ok {
		_spec.SetField(discount.FieldVersion, field.TypeInt, value)
	}
	if value, ok := duo.mutation.AddedVersion(); ok {
		_spec.AddField(discount.FieldVersion, field.TypeInt, value)
	}
	if value, ok := duo.mutation.UpdatedAt(); ok {
		_spec.SetField(discount.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := duo.mutation.Name(); ok {
		_spec.SetField(discount.FieldName, field.TypeString, value)
	}
	if value, ok := duo.mutation.GetType(); ok {
		_spec.SetField(discount.FieldType, field.TypeString, value)
	}
	if value, ok := duo.mutation.Value(); ok {
		_spec.SetField(discount.FieldValue, field.TypeFloat64, value)
	}
	if value, ok := duo.mutation.AddedValue(); ok {
		_spec.AddField(discount.FieldValue, field.TypeFloat64, value)
	}
	if value, ok := duo.mutation.Scope(); ok {
		_spec.SetField(discount.FieldScope, field.TypeString, value)
	}
	if value, ok := duo.mutation.Condition(); ok {
		_spec.SetField(discount.FieldCondition, field.TypeString, value)
	}
	if duo.mutation.ConditionCleared() {
		_spec.ClearField(discount.FieldCondition, field.TypeString)
	}
	if value, ok := duo.mutation.UsageType(); ok {
		_spec.SetField(discount.FieldUsageType, field.TypeString, value)
	}
	if value, ok := duo.mutation.Description(); ok {
		_spec.SetField(discount.FieldDescription, field.TypeString, value)
	}
	if duo.mutation.DescriptionCleared() {
		_spec.ClearField(discount.FieldDescription, field.TypeString)
	}
	if value, ok := duo.mutation.Meta(); ok {
		_spec.SetField(discount.FieldMeta, field.TypeJSON, value)
	}
	if duo.mutation.MetaCleared() {
		_spec.ClearField(discount.FieldMeta, field.TypeJSON)
	}
	if value, ok := duo.mutation.Start(); ok {
		_spec.SetField(discount.FieldStart, field.TypeTime, value)
	}
	if duo.mutation.StartCleared() {
		_spec.ClearField(discount.FieldStart, field.TypeTime)
	}
	if value, ok := duo.mutation.End(); ok {
		_spec.SetField(discount.FieldEnd, field.TypeTime, value)
	}
	if duo.mutation.EndCleared() {
		_spec.ClearField(discount.FieldEnd, field.TypeTime)
	}
	if duo.mutation.DiscountUsagesCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   discount.DiscountUsagesTable,
			Columns: []string{discount.DiscountUsagesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(discountusage.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := duo.mutation.RemovedDiscountUsagesIDs(); len(nodes) > 0 && !duo.mutation.DiscountUsagesCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   discount.DiscountUsagesTable,
			Columns: []string{discount.DiscountUsagesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(discountusage.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := duo.mutation.DiscountUsagesIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   discount.DiscountUsagesTable,
			Columns: []string{discount.DiscountUsagesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(discountusage.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_spec.AddModifiers(duo.modifiers...)
	_node = &Discount{config: duo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, duo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{discount.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	duo.mutation.done = true
	return _node, nil
}
