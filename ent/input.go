// Code generated by ent, DO NOT EDIT.

package ent

import (
	"reflect"
	"time"
)

func isZero(v interface{}) bool {
	if v == nil {
		return true
	}

	switch v := v.(type) {
	case string:
		return v == ""
	case bool:
		return !v
	case int:
		return v == 0
	case int8:
		return v == 0
	case int16:
		return v == 0
	case int32:
		return v == 0
	case int64:
		return v == 0
	case uint:
		return v == 0
	case uint8:
		return v == 0
	case uint16:
		return v == 0
	case uint32:
		return v == 0
	case uint64:
		return v == 0
	case float32:
		return v == 0
	case float64:
		return v == 0
	case time.Time:
		return v.IsZero()
	case *string:
		return v == nil || *v == ""
	case *bool:
		return v == nil || !*v
	case *int:
		return v == nil || *v == 0
	case *int8:
		return v == nil || *v == 0
	case *int16:
		return v == nil || *v == 0
	case *int32:
		return v == nil || *v == 0
	case *int64:
		return v == nil || *v == 0
	case *uint:
		return v == nil || *v == 0
	case *uint8:
		return v == nil || *v == 0
	case *uint16:
		return v == nil || *v == 0
	case *uint32:
		return v == nil || *v == 0
	case *uint64:
		return v == nil || *v == 0
	case *float32:
		return v == nil || *v == 0
	case *float64:
		return v == nil || *v == 0
	case *time.Time:
		return v == nil || v.IsZero()
	default:
		rv := reflect.ValueOf(v)
		if rv.Kind() == reflect.Ptr {
			return rv.IsNil() || isZero(rv.Elem().Interface())
		}
		return rv.IsZero()
	}
}

func (ac *AppointmentCreate) SetAppointment(input *Appointment) *AppointmentCreate {
	if !isZero(input.Status) {
		ac.SetStatus(input.Status)
	}
	if !isZero(input.Title) {
		ac.SetTitle(input.Title)
	}
	if !isZero(input.StartTime) {
		ac.SetStartTime(input.StartTime)
	}
	if !isZero(input.EndTime) {
		ac.SetEndTime(input.EndTime)
	}
	if input.ArrivedAt != nil && !isZero(*input.ArrivedAt) {
		ac.SetArrivedAt(*input.ArrivedAt)
	}
	if !isZero(input.Notes) {
		ac.SetNotes(input.Notes)
	}
	if !isZero(input.Type) {
		ac.SetType(input.Type)
	}
	if !isZero(input.PersonID) {
		ac.SetPersonID(input.PersonID)
	}
	if !isZero(input.DoctorID) {
		ac.SetDoctorID(input.DoctorID)
	}
	if input.CreatorID != nil && !isZero(*input.CreatorID) {
		ac.SetCreatorID(*input.CreatorID)
	}
	if !isZero(input.TrackID) {
		ac.SetTrackID(input.TrackID)
	}
	if !isZero(input.ReminderStatus) {
		ac.SetReminderStatus(input.ReminderStatus)
	}
	if input.ExtraNotes != nil && !isZero(*input.ExtraNotes) {
		ac.SetExtraNotes(*input.ExtraNotes)
	}
	if !isZero(input.History) {
		ac.SetHistory(input.History)
	}
	return ac
}

func (auo *AppointmentUpdateOne) SetAppointment(input *Appointment, forceUpdates ...string) *AppointmentUpdateOne {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Status) {
		auo.SetStatus(input.Status)
	} else if forceUpdateMap["status"] {
		auo.SetStatus(input.Status)
	}
	if !isZero(input.Title) {
		auo.SetTitle(input.Title)
	} else if forceUpdateMap["title"] {
		auo.ClearTitle()
	}
	if !isZero(input.StartTime) {
		auo.SetStartTime(input.StartTime)
	} else if forceUpdateMap["start_time"] {
		auo.ClearStartTime()
	}
	if !isZero(input.EndTime) {
		auo.SetEndTime(input.EndTime)
	} else if forceUpdateMap["end_time"] {
		auo.ClearEndTime()
	}
	if input.ArrivedAt != nil && !isZero(*input.ArrivedAt) {
		auo.SetArrivedAt(*input.ArrivedAt)
	} else if forceUpdateMap["arrived_at"] {
		auo.ClearArrivedAt()
	}
	if !isZero(input.Notes) {
		auo.SetNotes(input.Notes)
	} else if forceUpdateMap["notes"] {
		auo.ClearNotes()
	}
	if !isZero(input.Type) {
		auo.SetType(input.Type)
	} else if forceUpdateMap["type"] {
		auo.ClearType()
	}
	if !isZero(input.PersonID) {
		auo.SetPersonID(input.PersonID)
	} else if forceUpdateMap["person_id"] {
		auo.ClearPersonID()
	}
	if !isZero(input.DoctorID) {
		auo.SetDoctorID(input.DoctorID)
	} else if forceUpdateMap["doctor_id"] {
		auo.ClearDoctorID()
	}
	if input.CreatorID != nil && !isZero(*input.CreatorID) {
		auo.SetCreatorID(*input.CreatorID)
	} else if forceUpdateMap["creator_id"] {
		auo.ClearCreatorID()
	}
	if !isZero(input.TrackID) {
		auo.SetTrackID(input.TrackID)
	} else if forceUpdateMap["track_id"] {
		auo.ClearTrackID()
	}
	if !isZero(input.ReminderStatus) {
		auo.SetReminderStatus(input.ReminderStatus)
	} else if forceUpdateMap["reminder_status"] {
		auo.ClearReminderStatus()
	}
	if input.ExtraNotes != nil && !isZero(*input.ExtraNotes) {
		auo.SetExtraNotes(*input.ExtraNotes)
	} else if forceUpdateMap["extra_notes"] {
		auo.ClearExtraNotes()
	}
	if !isZero(input.History) {
		auo.SetHistory(input.History)
	} else if forceUpdateMap["history"] {
		auo.ClearHistory()
	}
	return auo
}

func (au *AppointmentUpdate) SetAppointment(input *Appointment, forceUpdates ...string) *AppointmentUpdate {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Status) {
		au.SetStatus(input.Status)
	} else if forceUpdateMap["status"] {
		au.SetStatus(input.Status)
	}
	if !isZero(input.Title) {
		au.SetTitle(input.Title)
	} else if forceUpdateMap["title"] {
		au.ClearTitle()
	}
	if !isZero(input.StartTime) {
		au.SetStartTime(input.StartTime)
	} else if forceUpdateMap["start_time"] {
		au.ClearStartTime()
	}
	if !isZero(input.EndTime) {
		au.SetEndTime(input.EndTime)
	} else if forceUpdateMap["end_time"] {
		au.ClearEndTime()
	}
	if input.ArrivedAt != nil && !isZero(*input.ArrivedAt) {
		au.SetArrivedAt(*input.ArrivedAt)
	} else if forceUpdateMap["arrived_at"] {
		au.ClearArrivedAt()
	}
	if !isZero(input.Notes) {
		au.SetNotes(input.Notes)
	} else if forceUpdateMap["notes"] {
		au.ClearNotes()
	}
	if !isZero(input.Type) {
		au.SetType(input.Type)
	} else if forceUpdateMap["type"] {
		au.ClearType()
	}
	if !isZero(input.PersonID) {
		au.SetPersonID(input.PersonID)
	} else if forceUpdateMap["person_id"] {
		au.ClearPersonID()
	}
	if !isZero(input.DoctorID) {
		au.SetDoctorID(input.DoctorID)
	} else if forceUpdateMap["doctor_id"] {
		au.ClearDoctorID()
	}
	if input.CreatorID != nil && !isZero(*input.CreatorID) {
		au.SetCreatorID(*input.CreatorID)
	} else if forceUpdateMap["creator_id"] {
		au.ClearCreatorID()
	}
	if !isZero(input.TrackID) {
		au.SetTrackID(input.TrackID)
	} else if forceUpdateMap["track_id"] {
		au.ClearTrackID()
	}
	if !isZero(input.ReminderStatus) {
		au.SetReminderStatus(input.ReminderStatus)
	} else if forceUpdateMap["reminder_status"] {
		au.ClearReminderStatus()
	}
	if input.ExtraNotes != nil && !isZero(*input.ExtraNotes) {
		au.SetExtraNotes(*input.ExtraNotes)
	} else if forceUpdateMap["extra_notes"] {
		au.ClearExtraNotes()
	}
	if !isZero(input.History) {
		au.SetHistory(input.History)
	} else if forceUpdateMap["history"] {
		au.ClearHistory()
	}
	return au
}

func (ahc *AppointmentHistoryCreate) SetAppointmentHistory(input *AppointmentHistory) *AppointmentHistoryCreate {
	if !isZero(input.AppointmentID) {
		ahc.SetAppointmentID(input.AppointmentID)
	}
	if !isZero(input.Before) {
		ahc.SetBefore(input.Before)
	}
	if !isZero(input.After) {
		ahc.SetAfter(input.After)
	}
	if !isZero(input.Operation) {
		ahc.SetOperation(input.Operation)
	}
	if !isZero(input.Related) {
		ahc.SetRelated(input.Related)
	}
	if !isZero(input.RelatedID) {
		ahc.SetRelatedID(input.RelatedID)
	}
	if !isZero(input.ChangedAt) {
		ahc.SetChangedAt(input.ChangedAt)
	}
	if !isZero(input.UserID) {
		ahc.SetUserID(input.UserID)
	}
	return ahc
}

func (ahuo *AppointmentHistoryUpdateOne) SetAppointmentHistory(input *AppointmentHistory, forceUpdates ...string) *AppointmentHistoryUpdateOne {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.AppointmentID) {
		ahuo.SetAppointmentID(input.AppointmentID)
	} else if forceUpdateMap["appointment_id"] {
		ahuo.ClearAppointmentID()
	}
	if !isZero(input.Before) {
		ahuo.SetBefore(input.Before)
	} else if forceUpdateMap["before"] {
		ahuo.ClearBefore()
	}
	if !isZero(input.After) {
		ahuo.SetAfter(input.After)
	} else if forceUpdateMap["after"] {
		ahuo.ClearAfter()
	}
	if !isZero(input.Operation) {
		ahuo.SetOperation(input.Operation)
	} else if forceUpdateMap["operation"] {
		ahuo.SetOperation(input.Operation)
	}
	if !isZero(input.Related) {
		ahuo.SetRelated(input.Related)
	} else if forceUpdateMap["related"] {
		ahuo.ClearRelated()
	}
	if !isZero(input.RelatedID) {
		ahuo.SetRelatedID(input.RelatedID)
	} else if forceUpdateMap["related_id"] {
		ahuo.ClearRelatedID()
	}
	if !isZero(input.UserID) {
		ahuo.SetUserID(input.UserID)
	} else if forceUpdateMap["user_id"] {
		ahuo.ClearUserID()
	}
	return ahuo
}

func (ahu *AppointmentHistoryUpdate) SetAppointmentHistory(input *AppointmentHistory, forceUpdates ...string) *AppointmentHistoryUpdate {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.AppointmentID) {
		ahu.SetAppointmentID(input.AppointmentID)
	} else if forceUpdateMap["appointment_id"] {
		ahu.ClearAppointmentID()
	}
	if !isZero(input.Before) {
		ahu.SetBefore(input.Before)
	} else if forceUpdateMap["before"] {
		ahu.ClearBefore()
	}
	if !isZero(input.After) {
		ahu.SetAfter(input.After)
	} else if forceUpdateMap["after"] {
		ahu.ClearAfter()
	}
	if !isZero(input.Operation) {
		ahu.SetOperation(input.Operation)
	} else if forceUpdateMap["operation"] {
		ahu.SetOperation(input.Operation)
	}
	if !isZero(input.Related) {
		ahu.SetRelated(input.Related)
	} else if forceUpdateMap["related"] {
		ahu.ClearRelated()
	}
	if !isZero(input.RelatedID) {
		ahu.SetRelatedID(input.RelatedID)
	} else if forceUpdateMap["related_id"] {
		ahu.ClearRelatedID()
	}
	if !isZero(input.UserID) {
		ahu.SetUserID(input.UserID)
	} else if forceUpdateMap["user_id"] {
		ahu.ClearUserID()
	}
	return ahu
}

func (ac *AttachmentCreate) SetAttachment(input *Attachment) *AttachmentCreate {
	if !isZero(input.Status) {
		ac.SetStatus(input.Status)
	}
	if input.DealID != nil && !isZero(*input.DealID) {
		ac.SetDealID(*input.DealID)
	}
	if !isZero(input.PersonID) {
		ac.SetPersonID(input.PersonID)
	}
	if input.ProductID != nil && !isZero(*input.ProductID) {
		ac.SetProductID(*input.ProductID)
	}
	if !isZero(input.BrandID) {
		ac.SetBrandID(input.BrandID)
	}
	if !isZero(input.Quantity) {
		ac.SetQuantity(input.Quantity)
	}
	if !isZero(input.Price) {
		ac.SetPrice(input.Price)
	}
	if !isZero(input.Discount) {
		ac.SetDiscount(input.Discount)
	}
	if !isZero(input.Note) {
		ac.SetNote(input.Note)
	}
	if !isZero(input.Title) {
		ac.SetTitle(input.Title)
	}
	if !isZero(input.UserID) {
		ac.SetUserID(input.UserID)
	}
	if input.ParentID != nil && !isZero(*input.ParentID) {
		ac.SetParentID(*input.ParentID)
	}
	if input.PlanID != nil && !isZero(*input.PlanID) {
		ac.SetPlanID(*input.PlanID)
	}
	if !isZero(input.Kind) {
		ac.SetKind(input.Kind)
	}
	if !isZero(input.TrackID) {
		ac.SetTrackID(input.TrackID)
	}
	return ac
}

func (auo *AttachmentUpdateOne) SetAttachment(input *Attachment, forceUpdates ...string) *AttachmentUpdateOne {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Status) {
		auo.SetStatus(input.Status)
	} else if forceUpdateMap["status"] {
		auo.SetStatus(input.Status)
	}
	if input.DealID != nil && !isZero(*input.DealID) {
		auo.SetDealID(*input.DealID)
	} else if forceUpdateMap["deal_id"] {
		auo.ClearDealID()
	}
	if !isZero(input.PersonID) {
		auo.SetPersonID(input.PersonID)
	} else if forceUpdateMap["person_id"] {
		auo.ClearPersonID()
	}
	if input.ProductID != nil && !isZero(*input.ProductID) {
		auo.SetProductID(*input.ProductID)
	} else if forceUpdateMap["product_id"] {
		auo.ClearProductID()
	}
	if !isZero(input.BrandID) {
		auo.SetBrandID(input.BrandID)
	} else if forceUpdateMap["brand_id"] {
		auo.SetBrandID(input.BrandID)
	}
	if !isZero(input.Quantity) {
		auo.SetQuantity(input.Quantity)
	} else if forceUpdateMap["quantity"] {
		auo.SetQuantity(input.Quantity)
	}
	if !isZero(input.Price) {
		auo.SetPrice(input.Price)
	} else if forceUpdateMap["price"] {
		auo.SetPrice(input.Price)
	}
	if !isZero(input.Discount) {
		auo.SetDiscount(input.Discount)
	} else if forceUpdateMap["discount"] {
		auo.SetDiscount(input.Discount)
	}
	if !isZero(input.Note) {
		auo.SetNote(input.Note)
	} else if forceUpdateMap["note"] {
		auo.ClearNote()
	}
	if !isZero(input.Title) {
		auo.SetTitle(input.Title)
	} else if forceUpdateMap["title"] {
		auo.ClearTitle()
	}
	if !isZero(input.UserID) {
		auo.SetUserID(input.UserID)
	} else if forceUpdateMap["user_id"] {
		auo.ClearUserID()
	}
	if input.ParentID != nil && !isZero(*input.ParentID) {
		auo.SetParentID(*input.ParentID)
	} else if forceUpdateMap["parent_id"] {
		auo.ClearParentID()
	}
	if input.PlanID != nil && !isZero(*input.PlanID) {
		auo.SetPlanID(*input.PlanID)
	} else if forceUpdateMap["plan_id"] {
		auo.ClearPlanID()
	}
	if !isZero(input.Kind) {
		auo.SetKind(input.Kind)
	} else if forceUpdateMap["kind"] {
		auo.SetKind(input.Kind)
	}
	if !isZero(input.TrackID) {
		auo.SetTrackID(input.TrackID)
	} else if forceUpdateMap["track_id"] {
		auo.ClearTrackID()
	}
	return auo
}

func (au *AttachmentUpdate) SetAttachment(input *Attachment, forceUpdates ...string) *AttachmentUpdate {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Status) {
		au.SetStatus(input.Status)
	} else if forceUpdateMap["status"] {
		au.SetStatus(input.Status)
	}
	if input.DealID != nil && !isZero(*input.DealID) {
		au.SetDealID(*input.DealID)
	} else if forceUpdateMap["deal_id"] {
		au.ClearDealID()
	}
	if !isZero(input.PersonID) {
		au.SetPersonID(input.PersonID)
	} else if forceUpdateMap["person_id"] {
		au.ClearPersonID()
	}
	if input.ProductID != nil && !isZero(*input.ProductID) {
		au.SetProductID(*input.ProductID)
	} else if forceUpdateMap["product_id"] {
		au.ClearProductID()
	}
	if !isZero(input.BrandID) {
		au.SetBrandID(input.BrandID)
	} else if forceUpdateMap["brand_id"] {
		au.SetBrandID(input.BrandID)
	}
	if !isZero(input.Quantity) {
		au.SetQuantity(input.Quantity)
	} else if forceUpdateMap["quantity"] {
		au.SetQuantity(input.Quantity)
	}
	if !isZero(input.Price) {
		au.SetPrice(input.Price)
	} else if forceUpdateMap["price"] {
		au.SetPrice(input.Price)
	}
	if !isZero(input.Discount) {
		au.SetDiscount(input.Discount)
	} else if forceUpdateMap["discount"] {
		au.SetDiscount(input.Discount)
	}
	if !isZero(input.Note) {
		au.SetNote(input.Note)
	} else if forceUpdateMap["note"] {
		au.ClearNote()
	}
	if !isZero(input.Title) {
		au.SetTitle(input.Title)
	} else if forceUpdateMap["title"] {
		au.ClearTitle()
	}
	if !isZero(input.UserID) {
		au.SetUserID(input.UserID)
	} else if forceUpdateMap["user_id"] {
		au.ClearUserID()
	}
	if input.ParentID != nil && !isZero(*input.ParentID) {
		au.SetParentID(*input.ParentID)
	} else if forceUpdateMap["parent_id"] {
		au.ClearParentID()
	}
	if input.PlanID != nil && !isZero(*input.PlanID) {
		au.SetPlanID(*input.PlanID)
	} else if forceUpdateMap["plan_id"] {
		au.ClearPlanID()
	}
	if !isZero(input.Kind) {
		au.SetKind(input.Kind)
	} else if forceUpdateMap["kind"] {
		au.SetKind(input.Kind)
	}
	if !isZero(input.TrackID) {
		au.SetTrackID(input.TrackID)
	} else if forceUpdateMap["track_id"] {
		au.ClearTrackID()
	}
	return au
}

func (adc *AttachmentDataCreate) SetAttachmentData(input *AttachmentData) *AttachmentDataCreate {
	if !isZero(input.AttachmentID) {
		adc.SetAttachmentID(input.AttachmentID)
	}
	if !isZero(input.Kind) {
		adc.SetKind(input.Kind)
	}
	if !isZero(input.UserID) {
		adc.SetUserID(input.UserID)
	}
	if input.ParticipantID != nil && !isZero(*input.ParticipantID) {
		adc.SetParticipantID(*input.ParticipantID)
	}
	if !isZero(input.Data) {
		adc.SetData(input.Data)
	}
	return adc
}

func (aduo *AttachmentDataUpdateOne) SetAttachmentData(input *AttachmentData, forceUpdates ...string) *AttachmentDataUpdateOne {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.AttachmentID) {
		aduo.SetAttachmentID(input.AttachmentID)
	} else if forceUpdateMap["attachment_id"] {
		aduo.SetAttachmentID(input.AttachmentID)
	}
	if !isZero(input.Kind) {
		aduo.SetKind(input.Kind)
	} else if forceUpdateMap["kind"] {
		aduo.SetKind(input.Kind)
	}
	if !isZero(input.UserID) {
		aduo.SetUserID(input.UserID)
	} else if forceUpdateMap["user_id"] {
		aduo.SetUserID(input.UserID)
	}
	if input.ParticipantID != nil && !isZero(*input.ParticipantID) {
		aduo.SetParticipantID(*input.ParticipantID)
	} else if forceUpdateMap["participant_id"] {
		aduo.ClearParticipantID()
	}
	if !isZero(input.Data) {
		aduo.SetData(input.Data)
	} else if forceUpdateMap["data"] {
		aduo.ClearData()
	}
	return aduo
}

func (adu *AttachmentDataUpdate) SetAttachmentData(input *AttachmentData, forceUpdates ...string) *AttachmentDataUpdate {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.AttachmentID) {
		adu.SetAttachmentID(input.AttachmentID)
	} else if forceUpdateMap["attachment_id"] {
		adu.SetAttachmentID(input.AttachmentID)
	}
	if !isZero(input.Kind) {
		adu.SetKind(input.Kind)
	} else if forceUpdateMap["kind"] {
		adu.SetKind(input.Kind)
	}
	if !isZero(input.UserID) {
		adu.SetUserID(input.UserID)
	} else if forceUpdateMap["user_id"] {
		adu.SetUserID(input.UserID)
	}
	if input.ParticipantID != nil && !isZero(*input.ParticipantID) {
		adu.SetParticipantID(*input.ParticipantID)
	} else if forceUpdateMap["participant_id"] {
		adu.ClearParticipantID()
	}
	if !isZero(input.Data) {
		adu.SetData(input.Data)
	} else if forceUpdateMap["data"] {
		adu.ClearData()
	}
	return adu
}

func (bc *BillCreate) SetBill(input *Bill) *BillCreate {
	if !isZero(input.Status) {
		bc.SetStatus(input.Status)
	}
	if input.DealID != nil && !isZero(*input.DealID) {
		bc.SetDealID(*input.DealID)
	}
	if !isZero(input.PersonID) {
		bc.SetPersonID(input.PersonID)
	}
	if !isZero(input.UserID) {
		bc.SetUserID(input.UserID)
	}
	if !isZero(input.BrandID) {
		bc.SetBrandID(input.BrandID)
	}
	if !isZero(input.DebtRemaining) {
		bc.SetDebtRemaining(input.DebtRemaining)
	}
	if !isZero(input.DebtPayment) {
		bc.SetDebtPayment(input.DebtPayment)
	}
	if !isZero(input.Discount) {
		bc.SetDiscount(input.Discount)
	}
	if !isZero(input.Refund) {
		bc.SetRefund(input.Refund)
	}
	if !isZero(input.TrackID) {
		bc.SetTrackID(input.TrackID)
	}
	if !isZero(input.State) {
		bc.SetState(input.State)
	}
	return bc
}

func (buo *BillUpdateOne) SetBill(input *Bill, forceUpdates ...string) *BillUpdateOne {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Status) {
		buo.SetStatus(input.Status)
	} else if forceUpdateMap["status"] {
		buo.SetStatus(input.Status)
	}
	if input.DealID != nil && !isZero(*input.DealID) {
		buo.SetDealID(*input.DealID)
	} else if forceUpdateMap["deal_id"] {
		buo.ClearDealID()
	}
	if !isZero(input.PersonID) {
		buo.SetPersonID(input.PersonID)
	} else if forceUpdateMap["person_id"] {
		buo.ClearPersonID()
	}
	if !isZero(input.UserID) {
		buo.SetUserID(input.UserID)
	} else if forceUpdateMap["user_id"] {
		buo.ClearUserID()
	}
	if !isZero(input.BrandID) {
		buo.SetBrandID(input.BrandID)
	} else if forceUpdateMap["brand_id"] {
		buo.SetBrandID(input.BrandID)
	}
	if !isZero(input.DebtRemaining) {
		buo.SetDebtRemaining(input.DebtRemaining)
	} else if forceUpdateMap["debt_remaining"] {
		buo.SetDebtRemaining(input.DebtRemaining)
	}
	if !isZero(input.DebtPayment) {
		buo.SetDebtPayment(input.DebtPayment)
	} else if forceUpdateMap["debt_payment"] {
		buo.SetDebtPayment(input.DebtPayment)
	}
	if !isZero(input.Discount) {
		buo.SetDiscount(input.Discount)
	} else if forceUpdateMap["discount"] {
		buo.SetDiscount(input.Discount)
	}
	if !isZero(input.Refund) {
		buo.SetRefund(input.Refund)
	} else if forceUpdateMap["refund"] {
		buo.SetRefund(input.Refund)
	}
	if !isZero(input.TrackID) {
		buo.SetTrackID(input.TrackID)
	} else if forceUpdateMap["track_id"] {
		buo.ClearTrackID()
	}
	if !isZero(input.State) {
		buo.SetState(input.State)
	} else if forceUpdateMap["state"] {
		buo.SetState(input.State)
	}
	return buo
}

func (bu *BillUpdate) SetBill(input *Bill, forceUpdates ...string) *BillUpdate {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Status) {
		bu.SetStatus(input.Status)
	} else if forceUpdateMap["status"] {
		bu.SetStatus(input.Status)
	}
	if input.DealID != nil && !isZero(*input.DealID) {
		bu.SetDealID(*input.DealID)
	} else if forceUpdateMap["deal_id"] {
		bu.ClearDealID()
	}
	if !isZero(input.PersonID) {
		bu.SetPersonID(input.PersonID)
	} else if forceUpdateMap["person_id"] {
		bu.ClearPersonID()
	}
	if !isZero(input.UserID) {
		bu.SetUserID(input.UserID)
	} else if forceUpdateMap["user_id"] {
		bu.ClearUserID()
	}
	if !isZero(input.BrandID) {
		bu.SetBrandID(input.BrandID)
	} else if forceUpdateMap["brand_id"] {
		bu.SetBrandID(input.BrandID)
	}
	if !isZero(input.DebtRemaining) {
		bu.SetDebtRemaining(input.DebtRemaining)
	} else if forceUpdateMap["debt_remaining"] {
		bu.SetDebtRemaining(input.DebtRemaining)
	}
	if !isZero(input.DebtPayment) {
		bu.SetDebtPayment(input.DebtPayment)
	} else if forceUpdateMap["debt_payment"] {
		bu.SetDebtPayment(input.DebtPayment)
	}
	if !isZero(input.Discount) {
		bu.SetDiscount(input.Discount)
	} else if forceUpdateMap["discount"] {
		bu.SetDiscount(input.Discount)
	}
	if !isZero(input.Refund) {
		bu.SetRefund(input.Refund)
	} else if forceUpdateMap["refund"] {
		bu.SetRefund(input.Refund)
	}
	if !isZero(input.TrackID) {
		bu.SetTrackID(input.TrackID)
	} else if forceUpdateMap["track_id"] {
		bu.ClearTrackID()
	}
	if !isZero(input.State) {
		bu.SetState(input.State)
	} else if forceUpdateMap["state"] {
		bu.SetState(input.State)
	}
	return bu
}

func (bdc *BillDataCreate) SetBillData(input *BillData) *BillDataCreate {
	if !isZero(input.BillID) {
		bdc.SetBillID(input.BillID)
	}
	if !isZero(input.Kind) {
		bdc.SetKind(input.Kind)
	}
	if !isZero(input.UserID) {
		bdc.SetUserID(input.UserID)
	}
	if !isZero(input.Data) {
		bdc.SetData(input.Data)
	}
	return bdc
}

func (bduo *BillDataUpdateOne) SetBillData(input *BillData, forceUpdates ...string) *BillDataUpdateOne {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.BillID) {
		bduo.SetBillID(input.BillID)
	} else if forceUpdateMap["bill_id"] {
		bduo.SetBillID(input.BillID)
	}
	if !isZero(input.Kind) {
		bduo.SetKind(input.Kind)
	} else if forceUpdateMap["kind"] {
		bduo.SetKind(input.Kind)
	}
	if !isZero(input.UserID) {
		bduo.SetUserID(input.UserID)
	} else if forceUpdateMap["user_id"] {
		bduo.SetUserID(input.UserID)
	}
	if !isZero(input.Data) {
		bduo.SetData(input.Data)
	} else if forceUpdateMap["data"] {
		bduo.ClearData()
	}
	return bduo
}

func (bdu *BillDataUpdate) SetBillData(input *BillData, forceUpdates ...string) *BillDataUpdate {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.BillID) {
		bdu.SetBillID(input.BillID)
	} else if forceUpdateMap["bill_id"] {
		bdu.SetBillID(input.BillID)
	}
	if !isZero(input.Kind) {
		bdu.SetKind(input.Kind)
	} else if forceUpdateMap["kind"] {
		bdu.SetKind(input.Kind)
	}
	if !isZero(input.UserID) {
		bdu.SetUserID(input.UserID)
	} else if forceUpdateMap["user_id"] {
		bdu.SetUserID(input.UserID)
	}
	if !isZero(input.Data) {
		bdu.SetData(input.Data)
	} else if forceUpdateMap["data"] {
		bdu.ClearData()
	}
	return bdu
}

func (bic *BillItemCreate) SetBillItem(input *BillItem) *BillItemCreate {
	if !isZero(input.Status) {
		bic.SetStatus(input.Status)
	}
	if input.BillID != nil && !isZero(*input.BillID) {
		bic.SetBillID(*input.BillID)
	}
	if input.AttachmentID != nil && !isZero(*input.AttachmentID) {
		bic.SetAttachmentID(*input.AttachmentID)
	}
	if input.InstallmentID != nil && !isZero(*input.InstallmentID) {
		bic.SetInstallmentID(*input.InstallmentID)
	}
	if !isZero(input.UserID) {
		bic.SetUserID(input.UserID)
	}
	if !isZero(input.Amount) {
		bic.SetAmount(input.Amount)
	}
	if !isZero(input.Note) {
		bic.SetNote(input.Note)
	}
	if !isZero(input.TrackID) {
		bic.SetTrackID(input.TrackID)
	}
	return bic
}

func (biuo *BillItemUpdateOne) SetBillItem(input *BillItem, forceUpdates ...string) *BillItemUpdateOne {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Status) {
		biuo.SetStatus(input.Status)
	} else if forceUpdateMap["status"] {
		biuo.SetStatus(input.Status)
	}
	if input.BillID != nil && !isZero(*input.BillID) {
		biuo.SetBillID(*input.BillID)
	} else if forceUpdateMap["bill_id"] {
		biuo.ClearBillID()
	}
	if input.AttachmentID != nil && !isZero(*input.AttachmentID) {
		biuo.SetAttachmentID(*input.AttachmentID)
	} else if forceUpdateMap["attachment_id"] {
		biuo.ClearAttachmentID()
	}
	if input.InstallmentID != nil && !isZero(*input.InstallmentID) {
		biuo.SetInstallmentID(*input.InstallmentID)
	} else if forceUpdateMap["installment_id"] {
		biuo.ClearInstallmentID()
	}
	if !isZero(input.UserID) {
		biuo.SetUserID(input.UserID)
	} else if forceUpdateMap["user_id"] {
		biuo.ClearUserID()
	}
	if !isZero(input.Amount) {
		biuo.SetAmount(input.Amount)
	} else if forceUpdateMap["amount"] {
		biuo.SetAmount(input.Amount)
	}
	if !isZero(input.Note) {
		biuo.SetNote(input.Note)
	} else if forceUpdateMap["note"] {
		biuo.ClearNote()
	}
	if !isZero(input.TrackID) {
		biuo.SetTrackID(input.TrackID)
	} else if forceUpdateMap["track_id"] {
		biuo.ClearTrackID()
	}
	return biuo
}

func (biu *BillItemUpdate) SetBillItem(input *BillItem, forceUpdates ...string) *BillItemUpdate {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Status) {
		biu.SetStatus(input.Status)
	} else if forceUpdateMap["status"] {
		biu.SetStatus(input.Status)
	}
	if input.BillID != nil && !isZero(*input.BillID) {
		biu.SetBillID(*input.BillID)
	} else if forceUpdateMap["bill_id"] {
		biu.ClearBillID()
	}
	if input.AttachmentID != nil && !isZero(*input.AttachmentID) {
		biu.SetAttachmentID(*input.AttachmentID)
	} else if forceUpdateMap["attachment_id"] {
		biu.ClearAttachmentID()
	}
	if input.InstallmentID != nil && !isZero(*input.InstallmentID) {
		biu.SetInstallmentID(*input.InstallmentID)
	} else if forceUpdateMap["installment_id"] {
		biu.ClearInstallmentID()
	}
	if !isZero(input.UserID) {
		biu.SetUserID(input.UserID)
	} else if forceUpdateMap["user_id"] {
		biu.ClearUserID()
	}
	if !isZero(input.Amount) {
		biu.SetAmount(input.Amount)
	} else if forceUpdateMap["amount"] {
		biu.SetAmount(input.Amount)
	}
	if !isZero(input.Note) {
		biu.SetNote(input.Note)
	} else if forceUpdateMap["note"] {
		biu.ClearNote()
	}
	if !isZero(input.TrackID) {
		biu.SetTrackID(input.TrackID)
	} else if forceUpdateMap["track_id"] {
		biu.ClearTrackID()
	}
	return biu
}

func (bc *BundleCreate) SetBundle(input *Bundle) *BundleCreate {
	if !isZero(input.Status) {
		bc.SetStatus(input.Status)
	}
	if !isZero(input.MachineName) {
		bc.SetMachineName(input.MachineName)
	}
	if !isZero(input.Name) {
		bc.SetName(input.Name)
	}
	if !isZero(input.Type) {
		bc.SetType(input.Type)
	}
	if !isZero(input.Description) {
		bc.SetDescription(input.Description)
	}
	return bc
}

func (buo *BundleUpdateOne) SetBundle(input *Bundle, forceUpdates ...string) *BundleUpdateOne {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Status) {
		buo.SetStatus(input.Status)
	} else if forceUpdateMap["status"] {
		buo.SetStatus(input.Status)
	}
	if !isZero(input.MachineName) {
		buo.SetMachineName(input.MachineName)
	} else if forceUpdateMap["machine_name"] {
		buo.SetMachineName(input.MachineName)
	}
	if !isZero(input.Name) {
		buo.SetName(input.Name)
	} else if forceUpdateMap["name"] {
		buo.SetName(input.Name)
	}
	if !isZero(input.Type) {
		buo.SetType(input.Type)
	} else if forceUpdateMap["type"] {
		buo.SetType(input.Type)
	}
	if !isZero(input.Description) {
		buo.SetDescription(input.Description)
	} else if forceUpdateMap["description"] {
		buo.SetDescription(input.Description)
	}
	return buo
}

func (bu *BundleUpdate) SetBundle(input *Bundle, forceUpdates ...string) *BundleUpdate {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Status) {
		bu.SetStatus(input.Status)
	} else if forceUpdateMap["status"] {
		bu.SetStatus(input.Status)
	}
	if !isZero(input.MachineName) {
		bu.SetMachineName(input.MachineName)
	} else if forceUpdateMap["machine_name"] {
		bu.SetMachineName(input.MachineName)
	}
	if !isZero(input.Name) {
		bu.SetName(input.Name)
	} else if forceUpdateMap["name"] {
		bu.SetName(input.Name)
	}
	if !isZero(input.Type) {
		bu.SetType(input.Type)
	} else if forceUpdateMap["type"] {
		bu.SetType(input.Type)
	}
	if !isZero(input.Description) {
		bu.SetDescription(input.Description)
	} else if forceUpdateMap["description"] {
		bu.SetDescription(input.Description)
	}
	return bu
}

func (cc *CallCreate) SetCall(input *Call) *CallCreate {
	if !isZero(input.Status) {
		cc.SetStatus(input.Status)
	}
	if !isZero(input.CallID) {
		cc.SetCallID(input.CallID)
	}
	if !isZero(input.StartTime) {
		cc.SetStartTime(input.StartTime)
	}
	if !isZero(input.Duration) {
		cc.SetDuration(input.Duration)
	}
	if !isZero(input.WattingTime) {
		cc.SetWattingTime(input.WattingTime)
	}
	if !isZero(input.Direction) {
		cc.SetDirection(input.Direction)
	}
	if !isZero(input.Source) {
		cc.SetSource(input.Source)
	}
	if !isZero(input.Destination) {
		cc.SetDestination(input.Destination)
	}
	if !isZero(input.RecordingFile) {
		cc.SetRecordingFile(input.RecordingFile)
	}
	if !isZero(input.UUID) {
		cc.SetUUID(input.UUID)
	}
	if !isZero(input.Kind) {
		cc.SetKind(input.Kind)
	}
	if !isZero(input.Score) {
		cc.SetScore(input.Score)
	}
	if !isZero(input.Pdd) {
		cc.SetPdd(input.Pdd)
	}
	if !isZero(input.Tta) {
		cc.SetTta(input.Tta)
	}
	if !isZero(input.Rating) {
		cc.SetRating(input.Rating)
	}
	if !isZero(input.Feedback) {
		cc.SetFeedback(input.Feedback)
	}
	if !isZero(input.CallStatus) {
		cc.SetCallStatus(input.CallStatus)
	}
	if !isZero(input.PersonID) {
		cc.SetPersonID(input.PersonID)
	}
	if !isZero(input.UserID) {
		cc.SetUserID(input.UserID)
	}
	return cc
}

func (cuo *CallUpdateOne) SetCall(input *Call, forceUpdates ...string) *CallUpdateOne {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Status) {
		cuo.SetStatus(input.Status)
	} else if forceUpdateMap["status"] {
		cuo.SetStatus(input.Status)
	}
	if !isZero(input.CallID) {
		cuo.SetCallID(input.CallID)
	} else if forceUpdateMap["call_id"] {
		cuo.SetCallID(input.CallID)
	}
	if !isZero(input.StartTime) {
		cuo.SetStartTime(input.StartTime)
	} else if forceUpdateMap["start_time"] {
		cuo.SetStartTime(input.StartTime)
	}
	if !isZero(input.Duration) {
		cuo.SetDuration(input.Duration)
	} else if forceUpdateMap["duration"] {
		cuo.SetDuration(input.Duration)
	}
	if !isZero(input.WattingTime) {
		cuo.SetWattingTime(input.WattingTime)
	} else if forceUpdateMap["watting_time"] {
		cuo.SetWattingTime(input.WattingTime)
	}
	if !isZero(input.Direction) {
		cuo.SetDirection(input.Direction)
	} else if forceUpdateMap["direction"] {
		cuo.ClearDirection()
	}
	if !isZero(input.Source) {
		cuo.SetSource(input.Source)
	} else if forceUpdateMap["source"] {
		cuo.ClearSource()
	}
	if !isZero(input.Destination) {
		cuo.SetDestination(input.Destination)
	} else if forceUpdateMap["destination"] {
		cuo.ClearDestination()
	}
	if !isZero(input.RecordingFile) {
		cuo.SetRecordingFile(input.RecordingFile)
	} else if forceUpdateMap["recording_file"] {
		cuo.ClearRecordingFile()
	}
	if !isZero(input.UUID) {
		cuo.SetUUID(input.UUID)
	} else if forceUpdateMap["uuid"] {
		cuo.ClearUUID()
	}
	if !isZero(input.Kind) {
		cuo.SetKind(input.Kind)
	} else if forceUpdateMap["kind"] {
		cuo.ClearKind()
	}
	if !isZero(input.Score) {
		cuo.SetScore(input.Score)
	} else if forceUpdateMap["score"] {
		cuo.ClearScore()
	}
	if !isZero(input.Pdd) {
		cuo.SetPdd(input.Pdd)
	} else if forceUpdateMap["pdd"] {
		cuo.SetPdd(input.Pdd)
	}
	if !isZero(input.Tta) {
		cuo.SetTta(input.Tta)
	} else if forceUpdateMap["tta"] {
		cuo.SetTta(input.Tta)
	}
	if !isZero(input.Rating) {
		cuo.SetRating(input.Rating)
	} else if forceUpdateMap["rating"] {
		cuo.SetRating(input.Rating)
	}
	if !isZero(input.Feedback) {
		cuo.SetFeedback(input.Feedback)
	} else if forceUpdateMap["feedback"] {
		cuo.ClearFeedback()
	}
	if !isZero(input.CallStatus) {
		cuo.SetCallStatus(input.CallStatus)
	} else if forceUpdateMap["call_status"] {
		cuo.ClearCallStatus()
	}
	if !isZero(input.PersonID) {
		cuo.SetPersonID(input.PersonID)
	} else if forceUpdateMap["person_id"] {
		cuo.ClearPersonID()
	}
	if !isZero(input.UserID) {
		cuo.SetUserID(input.UserID)
	} else if forceUpdateMap["user_id"] {
		cuo.ClearUserID()
	}
	return cuo
}

func (cu *CallUpdate) SetCall(input *Call, forceUpdates ...string) *CallUpdate {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Status) {
		cu.SetStatus(input.Status)
	} else if forceUpdateMap["status"] {
		cu.SetStatus(input.Status)
	}
	if !isZero(input.CallID) {
		cu.SetCallID(input.CallID)
	} else if forceUpdateMap["call_id"] {
		cu.SetCallID(input.CallID)
	}
	if !isZero(input.StartTime) {
		cu.SetStartTime(input.StartTime)
	} else if forceUpdateMap["start_time"] {
		cu.SetStartTime(input.StartTime)
	}
	if !isZero(input.Duration) {
		cu.SetDuration(input.Duration)
	} else if forceUpdateMap["duration"] {
		cu.SetDuration(input.Duration)
	}
	if !isZero(input.WattingTime) {
		cu.SetWattingTime(input.WattingTime)
	} else if forceUpdateMap["watting_time"] {
		cu.SetWattingTime(input.WattingTime)
	}
	if !isZero(input.Direction) {
		cu.SetDirection(input.Direction)
	} else if forceUpdateMap["direction"] {
		cu.ClearDirection()
	}
	if !isZero(input.Source) {
		cu.SetSource(input.Source)
	} else if forceUpdateMap["source"] {
		cu.ClearSource()
	}
	if !isZero(input.Destination) {
		cu.SetDestination(input.Destination)
	} else if forceUpdateMap["destination"] {
		cu.ClearDestination()
	}
	if !isZero(input.RecordingFile) {
		cu.SetRecordingFile(input.RecordingFile)
	} else if forceUpdateMap["recording_file"] {
		cu.ClearRecordingFile()
	}
	if !isZero(input.UUID) {
		cu.SetUUID(input.UUID)
	} else if forceUpdateMap["uuid"] {
		cu.ClearUUID()
	}
	if !isZero(input.Kind) {
		cu.SetKind(input.Kind)
	} else if forceUpdateMap["kind"] {
		cu.ClearKind()
	}
	if !isZero(input.Score) {
		cu.SetScore(input.Score)
	} else if forceUpdateMap["score"] {
		cu.ClearScore()
	}
	if !isZero(input.Pdd) {
		cu.SetPdd(input.Pdd)
	} else if forceUpdateMap["pdd"] {
		cu.SetPdd(input.Pdd)
	}
	if !isZero(input.Tta) {
		cu.SetTta(input.Tta)
	} else if forceUpdateMap["tta"] {
		cu.SetTta(input.Tta)
	}
	if !isZero(input.Rating) {
		cu.SetRating(input.Rating)
	} else if forceUpdateMap["rating"] {
		cu.SetRating(input.Rating)
	}
	if !isZero(input.Feedback) {
		cu.SetFeedback(input.Feedback)
	} else if forceUpdateMap["feedback"] {
		cu.ClearFeedback()
	}
	if !isZero(input.CallStatus) {
		cu.SetCallStatus(input.CallStatus)
	} else if forceUpdateMap["call_status"] {
		cu.ClearCallStatus()
	}
	if !isZero(input.PersonID) {
		cu.SetPersonID(input.PersonID)
	} else if forceUpdateMap["person_id"] {
		cu.ClearPersonID()
	}
	if !isZero(input.UserID) {
		cu.SetUserID(input.UserID)
	} else if forceUpdateMap["user_id"] {
		cu.ClearUserID()
	}
	return cu
}

func (crc *CasbinRuleCreate) SetCasbinRule(input *CasbinRule) *CasbinRuleCreate {
	if !isZero(input.Ptype) {
		crc.SetPtype(input.Ptype)
	}
	if !isZero(input.V0) {
		crc.SetV0(input.V0)
	}
	if !isZero(input.V1) {
		crc.SetV1(input.V1)
	}
	if !isZero(input.V2) {
		crc.SetV2(input.V2)
	}
	if !isZero(input.V3) {
		crc.SetV3(input.V3)
	}
	if !isZero(input.V4) {
		crc.SetV4(input.V4)
	}
	if !isZero(input.V5) {
		crc.SetV5(input.V5)
	}
	return crc
}

func (cruo *CasbinRuleUpdateOne) SetCasbinRule(input *CasbinRule, forceUpdates ...string) *CasbinRuleUpdateOne {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Ptype) {
		cruo.SetPtype(input.Ptype)
	} else if forceUpdateMap["Ptype"] {
		cruo.SetPtype(input.Ptype)
	}
	if !isZero(input.V0) {
		cruo.SetV0(input.V0)
	} else if forceUpdateMap["V0"] {
		cruo.SetV0(input.V0)
	}
	if !isZero(input.V1) {
		cruo.SetV1(input.V1)
	} else if forceUpdateMap["V1"] {
		cruo.SetV1(input.V1)
	}
	if !isZero(input.V2) {
		cruo.SetV2(input.V2)
	} else if forceUpdateMap["V2"] {
		cruo.SetV2(input.V2)
	}
	if !isZero(input.V3) {
		cruo.SetV3(input.V3)
	} else if forceUpdateMap["V3"] {
		cruo.SetV3(input.V3)
	}
	if !isZero(input.V4) {
		cruo.SetV4(input.V4)
	} else if forceUpdateMap["V4"] {
		cruo.SetV4(input.V4)
	}
	if !isZero(input.V5) {
		cruo.SetV5(input.V5)
	} else if forceUpdateMap["V5"] {
		cruo.SetV5(input.V5)
	}
	return cruo
}

func (cru *CasbinRuleUpdate) SetCasbinRule(input *CasbinRule, forceUpdates ...string) *CasbinRuleUpdate {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Ptype) {
		cru.SetPtype(input.Ptype)
	} else if forceUpdateMap["Ptype"] {
		cru.SetPtype(input.Ptype)
	}
	if !isZero(input.V0) {
		cru.SetV0(input.V0)
	} else if forceUpdateMap["V0"] {
		cru.SetV0(input.V0)
	}
	if !isZero(input.V1) {
		cru.SetV1(input.V1)
	} else if forceUpdateMap["V1"] {
		cru.SetV1(input.V1)
	}
	if !isZero(input.V2) {
		cru.SetV2(input.V2)
	} else if forceUpdateMap["V2"] {
		cru.SetV2(input.V2)
	}
	if !isZero(input.V3) {
		cru.SetV3(input.V3)
	} else if forceUpdateMap["V3"] {
		cru.SetV3(input.V3)
	}
	if !isZero(input.V4) {
		cru.SetV4(input.V4)
	} else if forceUpdateMap["V4"] {
		cru.SetV4(input.V4)
	}
	if !isZero(input.V5) {
		cru.SetV5(input.V5)
	} else if forceUpdateMap["V5"] {
		cru.SetV5(input.V5)
	}
	return cru
}

func (cfc *CashFlowCreate) SetCashFlow(input *CashFlow) *CashFlowCreate {
	if !isZero(input.Status) {
		cfc.SetStatus(input.Status)
	}
	if !isZero(input.Type) {
		cfc.SetType(input.Type)
	}
	if !isZero(input.Amount) {
		cfc.SetAmount(input.Amount)
	}
	if input.Cash != nil && !isZero(*input.Cash) {
		cfc.SetCash(*input.Cash)
	}
	if input.CreditCard != nil && !isZero(*input.CreditCard) {
		cfc.SetCreditCard(*input.CreditCard)
	}
	if input.Mpos != nil && !isZero(*input.Mpos) {
		cfc.SetMpos(*input.Mpos)
	}
	if input.Bank != nil && !isZero(*input.Bank) {
		cfc.SetBank(*input.Bank)
	}
	if input.Momo != nil && !isZero(*input.Momo) {
		cfc.SetMomo(*input.Momo)
	}
	if !isZero(input.Description) {
		cfc.SetDescription(input.Description)
	}
	if !isZero(input.CreatorID) {
		cfc.SetCreatorID(input.CreatorID)
	}
	if input.CounterpartID != nil && !isZero(*input.CounterpartID) {
		cfc.SetCounterpartID(*input.CounterpartID)
	}
	if input.RecipientID != nil && !isZero(*input.RecipientID) {
		cfc.SetRecipientID(*input.RecipientID)
	}
	if input.PayerID != nil && !isZero(*input.PayerID) {
		cfc.SetPayerID(*input.PayerID)
	}
	if !isZero(input.State) {
		cfc.SetState(input.State)
	}
	if input.ApproverID != nil && !isZero(*input.ApproverID) {
		cfc.SetApproverID(*input.ApproverID)
	}
	if input.PaidAt != nil && !isZero(*input.PaidAt) {
		cfc.SetPaidAt(*input.PaidAt)
	}
	return cfc
}

func (cfuo *CashFlowUpdateOne) SetCashFlow(input *CashFlow, forceUpdates ...string) *CashFlowUpdateOne {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Status) {
		cfuo.SetStatus(input.Status)
	} else if forceUpdateMap["status"] {
		cfuo.SetStatus(input.Status)
	}
	if !isZero(input.Amount) {
		cfuo.SetAmount(input.Amount)
	} else if forceUpdateMap["amount"] {
		cfuo.SetAmount(input.Amount)
	}
	if input.Cash != nil && !isZero(*input.Cash) {
		cfuo.SetCash(*input.Cash)
	} else if forceUpdateMap["cash"] {
		cfuo.ClearCash()
	}
	if input.CreditCard != nil && !isZero(*input.CreditCard) {
		cfuo.SetCreditCard(*input.CreditCard)
	} else if forceUpdateMap["credit_card"] {
		cfuo.ClearCreditCard()
	}
	if input.Mpos != nil && !isZero(*input.Mpos) {
		cfuo.SetMpos(*input.Mpos)
	} else if forceUpdateMap["mpos"] {
		cfuo.ClearMpos()
	}
	if input.Bank != nil && !isZero(*input.Bank) {
		cfuo.SetBank(*input.Bank)
	} else if forceUpdateMap["bank"] {
		cfuo.ClearBank()
	}
	if input.Momo != nil && !isZero(*input.Momo) {
		cfuo.SetMomo(*input.Momo)
	} else if forceUpdateMap["momo"] {
		cfuo.ClearMomo()
	}
	if !isZero(input.Description) {
		cfuo.SetDescription(input.Description)
	} else if forceUpdateMap["description"] {
		cfuo.ClearDescription()
	}
	if !isZero(input.CreatorID) {
		cfuo.SetCreatorID(input.CreatorID)
	} else if forceUpdateMap["creator_id"] {
		cfuo.SetCreatorID(input.CreatorID)
	}
	if input.CounterpartID != nil && !isZero(*input.CounterpartID) {
		cfuo.SetCounterpartID(*input.CounterpartID)
	} else if forceUpdateMap["counterpart_id"] {
		cfuo.ClearCounterpartID()
	}
	if input.RecipientID != nil && !isZero(*input.RecipientID) {
		cfuo.SetRecipientID(*input.RecipientID)
	} else if forceUpdateMap["recipient_id"] {
		cfuo.ClearRecipientID()
	}
	if input.PayerID != nil && !isZero(*input.PayerID) {
		cfuo.SetPayerID(*input.PayerID)
	} else if forceUpdateMap["payer_id"] {
		cfuo.ClearPayerID()
	}
	if !isZero(input.State) {
		cfuo.SetState(input.State)
	} else if forceUpdateMap["state"] {
		cfuo.SetState(input.State)
	}
	if input.ApproverID != nil && !isZero(*input.ApproverID) {
		cfuo.SetApproverID(*input.ApproverID)
	} else if forceUpdateMap["approver_id"] {
		cfuo.ClearApproverID()
	}
	if input.PaidAt != nil && !isZero(*input.PaidAt) {
		cfuo.SetPaidAt(*input.PaidAt)
	} else if forceUpdateMap["paid_at"] {
		cfuo.ClearPaidAt()
	}
	return cfuo
}

func (cfu *CashFlowUpdate) SetCashFlow(input *CashFlow, forceUpdates ...string) *CashFlowUpdate {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Status) {
		cfu.SetStatus(input.Status)
	} else if forceUpdateMap["status"] {
		cfu.SetStatus(input.Status)
	}
	if !isZero(input.Amount) {
		cfu.SetAmount(input.Amount)
	} else if forceUpdateMap["amount"] {
		cfu.SetAmount(input.Amount)
	}
	if input.Cash != nil && !isZero(*input.Cash) {
		cfu.SetCash(*input.Cash)
	} else if forceUpdateMap["cash"] {
		cfu.ClearCash()
	}
	if input.CreditCard != nil && !isZero(*input.CreditCard) {
		cfu.SetCreditCard(*input.CreditCard)
	} else if forceUpdateMap["credit_card"] {
		cfu.ClearCreditCard()
	}
	if input.Mpos != nil && !isZero(*input.Mpos) {
		cfu.SetMpos(*input.Mpos)
	} else if forceUpdateMap["mpos"] {
		cfu.ClearMpos()
	}
	if input.Bank != nil && !isZero(*input.Bank) {
		cfu.SetBank(*input.Bank)
	} else if forceUpdateMap["bank"] {
		cfu.ClearBank()
	}
	if input.Momo != nil && !isZero(*input.Momo) {
		cfu.SetMomo(*input.Momo)
	} else if forceUpdateMap["momo"] {
		cfu.ClearMomo()
	}
	if !isZero(input.Description) {
		cfu.SetDescription(input.Description)
	} else if forceUpdateMap["description"] {
		cfu.ClearDescription()
	}
	if !isZero(input.CreatorID) {
		cfu.SetCreatorID(input.CreatorID)
	} else if forceUpdateMap["creator_id"] {
		cfu.SetCreatorID(input.CreatorID)
	}
	if input.CounterpartID != nil && !isZero(*input.CounterpartID) {
		cfu.SetCounterpartID(*input.CounterpartID)
	} else if forceUpdateMap["counterpart_id"] {
		cfu.ClearCounterpartID()
	}
	if input.RecipientID != nil && !isZero(*input.RecipientID) {
		cfu.SetRecipientID(*input.RecipientID)
	} else if forceUpdateMap["recipient_id"] {
		cfu.ClearRecipientID()
	}
	if input.PayerID != nil && !isZero(*input.PayerID) {
		cfu.SetPayerID(*input.PayerID)
	} else if forceUpdateMap["payer_id"] {
		cfu.ClearPayerID()
	}
	if !isZero(input.State) {
		cfu.SetState(input.State)
	} else if forceUpdateMap["state"] {
		cfu.SetState(input.State)
	}
	if input.ApproverID != nil && !isZero(*input.ApproverID) {
		cfu.SetApproverID(*input.ApproverID)
	} else if forceUpdateMap["approver_id"] {
		cfu.ClearApproverID()
	}
	if input.PaidAt != nil && !isZero(*input.PaidAt) {
		cfu.SetPaidAt(*input.PaidAt)
	} else if forceUpdateMap["paid_at"] {
		cfu.ClearPaidAt()
	}
	return cfu
}

func (cfic *CashFlowItemCreate) SetCashFlowItem(input *CashFlowItem) *CashFlowItemCreate {
	if !isZero(input.CashFlowID) {
		cfic.SetCashFlowID(input.CashFlowID)
	}
	if !isZero(input.CategoryID) {
		cfic.SetCategoryID(input.CategoryID)
	}
	if input.DepartmentID != nil && !isZero(*input.DepartmentID) {
		cfic.SetDepartmentID(*input.DepartmentID)
	}
	if !isZero(input.Amount) {
		cfic.SetAmount(input.Amount)
	}
	if !isZero(input.Note) {
		cfic.SetNote(input.Note)
	}
	if !isZero(input.Order) {
		cfic.SetOrder(input.Order)
	}
	if !isZero(input.HasVat) {
		cfic.SetHasVat(input.HasVat)
	}
	return cfic
}

func (cfiuo *CashFlowItemUpdateOne) SetCashFlowItem(input *CashFlowItem, forceUpdates ...string) *CashFlowItemUpdateOne {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.CashFlowID) {
		cfiuo.SetCashFlowID(input.CashFlowID)
	} else if forceUpdateMap["cash_flow_id"] {
		cfiuo.SetCashFlowID(input.CashFlowID)
	}
	if !isZero(input.CategoryID) {
		cfiuo.SetCategoryID(input.CategoryID)
	} else if forceUpdateMap["category_id"] {
		cfiuo.SetCategoryID(input.CategoryID)
	}
	if input.DepartmentID != nil && !isZero(*input.DepartmentID) {
		cfiuo.SetDepartmentID(*input.DepartmentID)
	} else if forceUpdateMap["department_id"] {
		cfiuo.ClearDepartmentID()
	}
	if !isZero(input.Amount) {
		cfiuo.SetAmount(input.Amount)
	} else if forceUpdateMap["amount"] {
		cfiuo.SetAmount(input.Amount)
	}
	if !isZero(input.Note) {
		cfiuo.SetNote(input.Note)
	} else if forceUpdateMap["note"] {
		cfiuo.ClearNote()
	}
	if !isZero(input.Order) {
		cfiuo.SetOrder(input.Order)
	} else if forceUpdateMap["order"] {
		cfiuo.SetOrder(input.Order)
	}
	if !isZero(input.HasVat) {
		cfiuo.SetHasVat(input.HasVat)
	} else if forceUpdateMap["has_vat"] {
		cfiuo.ClearHasVat()
	}
	return cfiuo
}

func (cfiu *CashFlowItemUpdate) SetCashFlowItem(input *CashFlowItem, forceUpdates ...string) *CashFlowItemUpdate {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.CashFlowID) {
		cfiu.SetCashFlowID(input.CashFlowID)
	} else if forceUpdateMap["cash_flow_id"] {
		cfiu.SetCashFlowID(input.CashFlowID)
	}
	if !isZero(input.CategoryID) {
		cfiu.SetCategoryID(input.CategoryID)
	} else if forceUpdateMap["category_id"] {
		cfiu.SetCategoryID(input.CategoryID)
	}
	if input.DepartmentID != nil && !isZero(*input.DepartmentID) {
		cfiu.SetDepartmentID(*input.DepartmentID)
	} else if forceUpdateMap["department_id"] {
		cfiu.ClearDepartmentID()
	}
	if !isZero(input.Amount) {
		cfiu.SetAmount(input.Amount)
	} else if forceUpdateMap["amount"] {
		cfiu.SetAmount(input.Amount)
	}
	if !isZero(input.Note) {
		cfiu.SetNote(input.Note)
	} else if forceUpdateMap["note"] {
		cfiu.ClearNote()
	}
	if !isZero(input.Order) {
		cfiu.SetOrder(input.Order)
	} else if forceUpdateMap["order"] {
		cfiu.SetOrder(input.Order)
	}
	if !isZero(input.HasVat) {
		cfiu.SetHasVat(input.HasVat)
	} else if forceUpdateMap["has_vat"] {
		cfiu.ClearHasVat()
	}
	return cfiu
}

func (cfnc *CashFlowNoteCreate) SetCashFlowNote(input *CashFlowNote) *CashFlowNoteCreate {
	if !isZero(input.Status) {
		cfnc.SetStatus(input.Status)
	}
	if !isZero(input.CashFlowID) {
		cfnc.SetCashFlowID(input.CashFlowID)
	}
	if !isZero(input.UserID) {
		cfnc.SetUserID(input.UserID)
	}
	if !isZero(input.Body) {
		cfnc.SetBody(input.Body)
	}
	return cfnc
}

func (cfnuo *CashFlowNoteUpdateOne) SetCashFlowNote(input *CashFlowNote, forceUpdates ...string) *CashFlowNoteUpdateOne {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Status) {
		cfnuo.SetStatus(input.Status)
	} else if forceUpdateMap["status"] {
		cfnuo.SetStatus(input.Status)
	}
	if !isZero(input.CashFlowID) {
		cfnuo.SetCashFlowID(input.CashFlowID)
	} else if forceUpdateMap["cash_flow_id"] {
		cfnuo.ClearCashFlowID()
	}
	if !isZero(input.UserID) {
		cfnuo.SetUserID(input.UserID)
	} else if forceUpdateMap["user_id"] {
		cfnuo.ClearUserID()
	}
	if !isZero(input.Body) {
		cfnuo.SetBody(input.Body)
	} else if forceUpdateMap["body"] {
		cfnuo.ClearBody()
	}
	return cfnuo
}

func (cfnu *CashFlowNoteUpdate) SetCashFlowNote(input *CashFlowNote, forceUpdates ...string) *CashFlowNoteUpdate {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Status) {
		cfnu.SetStatus(input.Status)
	} else if forceUpdateMap["status"] {
		cfnu.SetStatus(input.Status)
	}
	if !isZero(input.CashFlowID) {
		cfnu.SetCashFlowID(input.CashFlowID)
	} else if forceUpdateMap["cash_flow_id"] {
		cfnu.ClearCashFlowID()
	}
	if !isZero(input.UserID) {
		cfnu.SetUserID(input.UserID)
	} else if forceUpdateMap["user_id"] {
		cfnu.ClearUserID()
	}
	if !isZero(input.Body) {
		cfnu.SetBody(input.Body)
	} else if forceUpdateMap["body"] {
		cfnu.ClearBody()
	}
	return cfnu
}

func (ddatc *DbsandentistDboAppointmentTimeCreate) SetDbsandentistDboAppointmentTime(input *DbsandentistDboAppointmentTime) *DbsandentistDboAppointmentTimeCreate {
	if !isZero(input.Customercode) {
		ddatc.SetCustomercode(input.Customercode)
	}
	if !isZero(input.Title) {
		ddatc.SetTitle(input.Title)
	}
	if !isZero(input.Content) {
		ddatc.SetContent(input.Content)
	}
	if !isZero(input.Doctor) {
		ddatc.SetDoctor(input.Doctor)
	}
	if !isZero(input.Appointmentdatetime) {
		ddatc.SetAppointmentdatetime(input.Appointmentdatetime)
	}
	if !isZero(input.Source) {
		ddatc.SetSource(input.Source)
	}
	if !isZero(input.Status) {
		ddatc.SetStatus(input.Status)
	}
	if !isZero(input.Reason) {
		ddatc.SetReason(input.Reason)
	}
	if !isZero(input.Origid) {
		ddatc.SetOrigid(input.Origid)
	}
	if !isZero(input.Assignedstaff) {
		ddatc.SetAssignedstaff(input.Assignedstaff)
	}
	if !isZero(input.Companycode) {
		ddatc.SetCompanycode(input.Companycode)
	}
	if !isZero(input.Storeto) {
		ddatc.SetStoreto(input.Storeto)
	}
	if !isZero(input.Createddate) {
		ddatc.SetCreateddate(input.Createddate)
	}
	if !isZero(input.Createduser) {
		ddatc.SetCreateduser(input.Createduser)
	}
	if !isZero(input.Lastupdatedate) {
		ddatc.SetLastupdatedate(input.Lastupdatedate)
	}
	if !isZero(input.Lastupdateuser) {
		ddatc.SetLastupdateuser(input.Lastupdateuser)
	}
	if !isZero(input.Userconfirm) {
		ddatc.SetUserconfirm(input.Userconfirm)
	}
	if !isZero(input.Dateconfirm) {
		ddatc.SetDateconfirm(input.Dateconfirm)
	}
	if !isZero(input.Reasoncode) {
		ddatc.SetReasoncode(input.Reasoncode)
	}
	if !isZero(input.Reasonname) {
		ddatc.SetReasonname(input.Reasonname)
	}
	if !isZero(input.Typeappointment) {
		ddatc.SetTypeappointment(input.Typeappointment)
	}
	return ddatc
}

func (ddatuo *DbsandentistDboAppointmentTimeUpdateOne) SetDbsandentistDboAppointmentTime(input *DbsandentistDboAppointmentTime, forceUpdates ...string) *DbsandentistDboAppointmentTimeUpdateOne {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Customercode) {
		ddatuo.SetCustomercode(input.Customercode)
	} else if forceUpdateMap["customercode"] {
		ddatuo.ClearCustomercode()
	}
	if !isZero(input.Title) {
		ddatuo.SetTitle(input.Title)
	} else if forceUpdateMap["title"] {
		ddatuo.ClearTitle()
	}
	if !isZero(input.Content) {
		ddatuo.SetContent(input.Content)
	} else if forceUpdateMap["content"] {
		ddatuo.ClearContent()
	}
	if !isZero(input.Doctor) {
		ddatuo.SetDoctor(input.Doctor)
	} else if forceUpdateMap["doctor"] {
		ddatuo.ClearDoctor()
	}
	if !isZero(input.Appointmentdatetime) {
		ddatuo.SetAppointmentdatetime(input.Appointmentdatetime)
	} else if forceUpdateMap["appointmentdatetime"] {
		ddatuo.ClearAppointmentdatetime()
	}
	if !isZero(input.Source) {
		ddatuo.SetSource(input.Source)
	} else if forceUpdateMap["source"] {
		ddatuo.ClearSource()
	}
	if !isZero(input.Status) {
		ddatuo.SetStatus(input.Status)
	} else if forceUpdateMap["status"] {
		ddatuo.ClearStatus()
	}
	if !isZero(input.Reason) {
		ddatuo.SetReason(input.Reason)
	} else if forceUpdateMap["reason"] {
		ddatuo.ClearReason()
	}
	if !isZero(input.Origid) {
		ddatuo.SetOrigid(input.Origid)
	} else if forceUpdateMap["origid"] {
		ddatuo.ClearOrigid()
	}
	if !isZero(input.Assignedstaff) {
		ddatuo.SetAssignedstaff(input.Assignedstaff)
	} else if forceUpdateMap["assignedstaff"] {
		ddatuo.ClearAssignedstaff()
	}
	if !isZero(input.Companycode) {
		ddatuo.SetCompanycode(input.Companycode)
	} else if forceUpdateMap["companycode"] {
		ddatuo.ClearCompanycode()
	}
	if !isZero(input.Storeto) {
		ddatuo.SetStoreto(input.Storeto)
	} else if forceUpdateMap["storeto"] {
		ddatuo.ClearStoreto()
	}
	if !isZero(input.Createddate) {
		ddatuo.SetCreateddate(input.Createddate)
	} else if forceUpdateMap["createddate"] {
		ddatuo.ClearCreateddate()
	}
	if !isZero(input.Createduser) {
		ddatuo.SetCreateduser(input.Createduser)
	} else if forceUpdateMap["createduser"] {
		ddatuo.ClearCreateduser()
	}
	if !isZero(input.Lastupdatedate) {
		ddatuo.SetLastupdatedate(input.Lastupdatedate)
	} else if forceUpdateMap["lastupdatedate"] {
		ddatuo.ClearLastupdatedate()
	}
	if !isZero(input.Lastupdateuser) {
		ddatuo.SetLastupdateuser(input.Lastupdateuser)
	} else if forceUpdateMap["lastupdateuser"] {
		ddatuo.ClearLastupdateuser()
	}
	if !isZero(input.Userconfirm) {
		ddatuo.SetUserconfirm(input.Userconfirm)
	} else if forceUpdateMap["userconfirm"] {
		ddatuo.ClearUserconfirm()
	}
	if !isZero(input.Dateconfirm) {
		ddatuo.SetDateconfirm(input.Dateconfirm)
	} else if forceUpdateMap["dateconfirm"] {
		ddatuo.ClearDateconfirm()
	}
	if !isZero(input.Reasoncode) {
		ddatuo.SetReasoncode(input.Reasoncode)
	} else if forceUpdateMap["reasoncode"] {
		ddatuo.ClearReasoncode()
	}
	if !isZero(input.Reasonname) {
		ddatuo.SetReasonname(input.Reasonname)
	} else if forceUpdateMap["reasonname"] {
		ddatuo.ClearReasonname()
	}
	if !isZero(input.Typeappointment) {
		ddatuo.SetTypeappointment(input.Typeappointment)
	} else if forceUpdateMap["typeappointment"] {
		ddatuo.ClearTypeappointment()
	}
	return ddatuo
}

func (ddatu *DbsandentistDboAppointmentTimeUpdate) SetDbsandentistDboAppointmentTime(input *DbsandentistDboAppointmentTime, forceUpdates ...string) *DbsandentistDboAppointmentTimeUpdate {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Customercode) {
		ddatu.SetCustomercode(input.Customercode)
	} else if forceUpdateMap["customercode"] {
		ddatu.ClearCustomercode()
	}
	if !isZero(input.Title) {
		ddatu.SetTitle(input.Title)
	} else if forceUpdateMap["title"] {
		ddatu.ClearTitle()
	}
	if !isZero(input.Content) {
		ddatu.SetContent(input.Content)
	} else if forceUpdateMap["content"] {
		ddatu.ClearContent()
	}
	if !isZero(input.Doctor) {
		ddatu.SetDoctor(input.Doctor)
	} else if forceUpdateMap["doctor"] {
		ddatu.ClearDoctor()
	}
	if !isZero(input.Appointmentdatetime) {
		ddatu.SetAppointmentdatetime(input.Appointmentdatetime)
	} else if forceUpdateMap["appointmentdatetime"] {
		ddatu.ClearAppointmentdatetime()
	}
	if !isZero(input.Source) {
		ddatu.SetSource(input.Source)
	} else if forceUpdateMap["source"] {
		ddatu.ClearSource()
	}
	if !isZero(input.Status) {
		ddatu.SetStatus(input.Status)
	} else if forceUpdateMap["status"] {
		ddatu.ClearStatus()
	}
	if !isZero(input.Reason) {
		ddatu.SetReason(input.Reason)
	} else if forceUpdateMap["reason"] {
		ddatu.ClearReason()
	}
	if !isZero(input.Origid) {
		ddatu.SetOrigid(input.Origid)
	} else if forceUpdateMap["origid"] {
		ddatu.ClearOrigid()
	}
	if !isZero(input.Assignedstaff) {
		ddatu.SetAssignedstaff(input.Assignedstaff)
	} else if forceUpdateMap["assignedstaff"] {
		ddatu.ClearAssignedstaff()
	}
	if !isZero(input.Companycode) {
		ddatu.SetCompanycode(input.Companycode)
	} else if forceUpdateMap["companycode"] {
		ddatu.ClearCompanycode()
	}
	if !isZero(input.Storeto) {
		ddatu.SetStoreto(input.Storeto)
	} else if forceUpdateMap["storeto"] {
		ddatu.ClearStoreto()
	}
	if !isZero(input.Createddate) {
		ddatu.SetCreateddate(input.Createddate)
	} else if forceUpdateMap["createddate"] {
		ddatu.ClearCreateddate()
	}
	if !isZero(input.Createduser) {
		ddatu.SetCreateduser(input.Createduser)
	} else if forceUpdateMap["createduser"] {
		ddatu.ClearCreateduser()
	}
	if !isZero(input.Lastupdatedate) {
		ddatu.SetLastupdatedate(input.Lastupdatedate)
	} else if forceUpdateMap["lastupdatedate"] {
		ddatu.ClearLastupdatedate()
	}
	if !isZero(input.Lastupdateuser) {
		ddatu.SetLastupdateuser(input.Lastupdateuser)
	} else if forceUpdateMap["lastupdateuser"] {
		ddatu.ClearLastupdateuser()
	}
	if !isZero(input.Userconfirm) {
		ddatu.SetUserconfirm(input.Userconfirm)
	} else if forceUpdateMap["userconfirm"] {
		ddatu.ClearUserconfirm()
	}
	if !isZero(input.Dateconfirm) {
		ddatu.SetDateconfirm(input.Dateconfirm)
	} else if forceUpdateMap["dateconfirm"] {
		ddatu.ClearDateconfirm()
	}
	if !isZero(input.Reasoncode) {
		ddatu.SetReasoncode(input.Reasoncode)
	} else if forceUpdateMap["reasoncode"] {
		ddatu.ClearReasoncode()
	}
	if !isZero(input.Reasonname) {
		ddatu.SetReasonname(input.Reasonname)
	} else if forceUpdateMap["reasonname"] {
		ddatu.ClearReasonname()
	}
	if !isZero(input.Typeappointment) {
		ddatu.SetTypeappointment(input.Typeappointment)
	} else if forceUpdateMap["typeappointment"] {
		ddatu.ClearTypeappointment()
	}
	return ddatu
}

func (ddbc *DbsandentistDboBillCreate) SetDbsandentistDboBill(input *DbsandentistDboBill) *DbsandentistDboBillCreate {
	if !isZero(input.Companycode) {
		ddbc.SetCompanycode(input.Companycode)
	}
	if !isZero(input.Storecode) {
		ddbc.SetStorecode(input.Storecode)
	}
	if !isZero(input.Code) {
		ddbc.SetCode(input.Code)
	}
	if !isZero(input.Customercode) {
		ddbc.SetCustomercode(input.Customercode)
	}
	if !isZero(input.Status) {
		ddbc.SetStatus(input.Status)
	}
	if !isZero(input.Createddate) {
		ddbc.SetCreateddate(input.Createddate)
	}
	if !isZero(input.Createduser) {
		ddbc.SetCreateduser(input.Createduser)
	}
	if !isZero(input.Lastupdatedate) {
		ddbc.SetLastupdatedate(input.Lastupdatedate)
	}
	if !isZero(input.Lastupdateuser) {
		ddbc.SetLastupdateuser(input.Lastupdateuser)
	}
	if !isZero(input.Datemedical) {
		ddbc.SetDatemedical(input.Datemedical)
	}
	return ddbc
}

func (ddbuo *DbsandentistDboBillUpdateOne) SetDbsandentistDboBill(input *DbsandentistDboBill, forceUpdates ...string) *DbsandentistDboBillUpdateOne {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Companycode) {
		ddbuo.SetCompanycode(input.Companycode)
	} else if forceUpdateMap["companycode"] {
		ddbuo.ClearCompanycode()
	}
	if !isZero(input.Storecode) {
		ddbuo.SetStorecode(input.Storecode)
	} else if forceUpdateMap["storecode"] {
		ddbuo.ClearStorecode()
	}
	if !isZero(input.Code) {
		ddbuo.SetCode(input.Code)
	} else if forceUpdateMap["code"] {
		ddbuo.ClearCode()
	}
	if !isZero(input.Customercode) {
		ddbuo.SetCustomercode(input.Customercode)
	} else if forceUpdateMap["customercode"] {
		ddbuo.ClearCustomercode()
	}
	if !isZero(input.Status) {
		ddbuo.SetStatus(input.Status)
	} else if forceUpdateMap["status"] {
		ddbuo.ClearStatus()
	}
	if !isZero(input.Createddate) {
		ddbuo.SetCreateddate(input.Createddate)
	} else if forceUpdateMap["createddate"] {
		ddbuo.ClearCreateddate()
	}
	if !isZero(input.Createduser) {
		ddbuo.SetCreateduser(input.Createduser)
	} else if forceUpdateMap["createduser"] {
		ddbuo.ClearCreateduser()
	}
	if !isZero(input.Lastupdatedate) {
		ddbuo.SetLastupdatedate(input.Lastupdatedate)
	} else if forceUpdateMap["lastupdatedate"] {
		ddbuo.ClearLastupdatedate()
	}
	if !isZero(input.Lastupdateuser) {
		ddbuo.SetLastupdateuser(input.Lastupdateuser)
	} else if forceUpdateMap["lastupdateuser"] {
		ddbuo.ClearLastupdateuser()
	}
	if !isZero(input.Datemedical) {
		ddbuo.SetDatemedical(input.Datemedical)
	} else if forceUpdateMap["datemedical"] {
		ddbuo.ClearDatemedical()
	}
	return ddbuo
}

func (ddbu *DbsandentistDboBillUpdate) SetDbsandentistDboBill(input *DbsandentistDboBill, forceUpdates ...string) *DbsandentistDboBillUpdate {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Companycode) {
		ddbu.SetCompanycode(input.Companycode)
	} else if forceUpdateMap["companycode"] {
		ddbu.ClearCompanycode()
	}
	if !isZero(input.Storecode) {
		ddbu.SetStorecode(input.Storecode)
	} else if forceUpdateMap["storecode"] {
		ddbu.ClearStorecode()
	}
	if !isZero(input.Code) {
		ddbu.SetCode(input.Code)
	} else if forceUpdateMap["code"] {
		ddbu.ClearCode()
	}
	if !isZero(input.Customercode) {
		ddbu.SetCustomercode(input.Customercode)
	} else if forceUpdateMap["customercode"] {
		ddbu.ClearCustomercode()
	}
	if !isZero(input.Status) {
		ddbu.SetStatus(input.Status)
	} else if forceUpdateMap["status"] {
		ddbu.ClearStatus()
	}
	if !isZero(input.Createddate) {
		ddbu.SetCreateddate(input.Createddate)
	} else if forceUpdateMap["createddate"] {
		ddbu.ClearCreateddate()
	}
	if !isZero(input.Createduser) {
		ddbu.SetCreateduser(input.Createduser)
	} else if forceUpdateMap["createduser"] {
		ddbu.ClearCreateduser()
	}
	if !isZero(input.Lastupdatedate) {
		ddbu.SetLastupdatedate(input.Lastupdatedate)
	} else if forceUpdateMap["lastupdatedate"] {
		ddbu.ClearLastupdatedate()
	}
	if !isZero(input.Lastupdateuser) {
		ddbu.SetLastupdateuser(input.Lastupdateuser)
	} else if forceUpdateMap["lastupdateuser"] {
		ddbu.ClearLastupdateuser()
	}
	if !isZero(input.Datemedical) {
		ddbu.SetDatemedical(input.Datemedical)
	} else if forceUpdateMap["datemedical"] {
		ddbu.ClearDatemedical()
	}
	return ddbu
}

func (ddbic *DbsandentistDboBillItemCreate) SetDbsandentistDboBillItem(input *DbsandentistDboBillItem) *DbsandentistDboBillItemCreate {
	if !isZero(input.Medicalcode) {
		ddbic.SetMedicalcode(input.Medicalcode)
	}
	if !isZero(input.Invoicecode) {
		ddbic.SetInvoicecode(input.Invoicecode)
	}
	if !isZero(input.Itemtype) {
		ddbic.SetItemtype(input.Itemtype)
	}
	if !isZero(input.Itemcode) {
		ddbic.SetItemcode(input.Itemcode)
	}
	if !isZero(input.Itemname) {
		ddbic.SetItemname(input.Itemname)
	}
	if !isZero(input.Uomcode) {
		ddbic.SetUomcode(input.Uomcode)
	}
	if !isZero(input.Datetime) {
		ddbic.SetDatetime(input.Datetime)
	}
	if !isZero(input.Quantity) {
		ddbic.SetQuantity(input.Quantity)
	}
	if !isZero(input.Price) {
		ddbic.SetPrice(input.Price)
	}
	if !isZero(input.Discountcoupon) {
		ddbic.SetDiscountcoupon(input.Discountcoupon)
	}
	if !isZero(input.Discountamount) {
		ddbic.SetDiscountamount(input.Discountamount)
	}
	if !isZero(input.Totaldiscount) {
		ddbic.SetTotaldiscount(input.Totaldiscount)
	}
	if !isZero(input.Totalamount) {
		ddbic.SetTotalamount(input.Totalamount)
	}
	if !isZero(input.Couponcode) {
		ddbic.SetCouponcode(input.Couponcode)
	}
	if !isZero(input.Discountcode) {
		ddbic.SetDiscountcode(input.Discountcode)
	}
	if !isZero(input.Createddate) {
		ddbic.SetCreateddate(input.Createddate)
	}
	if !isZero(input.Createduser) {
		ddbic.SetCreateduser(input.Createduser)
	}
	if !isZero(input.Lastupdatedate) {
		ddbic.SetLastupdatedate(input.Lastupdatedate)
	}
	if !isZero(input.Lastupdateuser) {
		ddbic.SetLastupdateuser(input.Lastupdateuser)
	}
	return ddbic
}

func (ddbiuo *DbsandentistDboBillItemUpdateOne) SetDbsandentistDboBillItem(input *DbsandentistDboBillItem, forceUpdates ...string) *DbsandentistDboBillItemUpdateOne {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Medicalcode) {
		ddbiuo.SetMedicalcode(input.Medicalcode)
	} else if forceUpdateMap["medicalcode"] {
		ddbiuo.ClearMedicalcode()
	}
	if !isZero(input.Invoicecode) {
		ddbiuo.SetInvoicecode(input.Invoicecode)
	} else if forceUpdateMap["invoicecode"] {
		ddbiuo.ClearInvoicecode()
	}
	if !isZero(input.Itemtype) {
		ddbiuo.SetItemtype(input.Itemtype)
	} else if forceUpdateMap["itemtype"] {
		ddbiuo.ClearItemtype()
	}
	if !isZero(input.Itemcode) {
		ddbiuo.SetItemcode(input.Itemcode)
	} else if forceUpdateMap["itemcode"] {
		ddbiuo.ClearItemcode()
	}
	if !isZero(input.Itemname) {
		ddbiuo.SetItemname(input.Itemname)
	} else if forceUpdateMap["itemname"] {
		ddbiuo.ClearItemname()
	}
	if !isZero(input.Uomcode) {
		ddbiuo.SetUomcode(input.Uomcode)
	} else if forceUpdateMap["uomcode"] {
		ddbiuo.ClearUomcode()
	}
	if !isZero(input.Datetime) {
		ddbiuo.SetDatetime(input.Datetime)
	} else if forceUpdateMap["datetime"] {
		ddbiuo.ClearDatetime()
	}
	if !isZero(input.Quantity) {
		ddbiuo.SetQuantity(input.Quantity)
	} else if forceUpdateMap["quantity"] {
		ddbiuo.ClearQuantity()
	}
	if !isZero(input.Price) {
		ddbiuo.SetPrice(input.Price)
	} else if forceUpdateMap["price"] {
		ddbiuo.ClearPrice()
	}
	if !isZero(input.Discountcoupon) {
		ddbiuo.SetDiscountcoupon(input.Discountcoupon)
	} else if forceUpdateMap["discountcoupon"] {
		ddbiuo.ClearDiscountcoupon()
	}
	if !isZero(input.Discountamount) {
		ddbiuo.SetDiscountamount(input.Discountamount)
	} else if forceUpdateMap["discountamount"] {
		ddbiuo.ClearDiscountamount()
	}
	if !isZero(input.Totaldiscount) {
		ddbiuo.SetTotaldiscount(input.Totaldiscount)
	} else if forceUpdateMap["totaldiscount"] {
		ddbiuo.ClearTotaldiscount()
	}
	if !isZero(input.Totalamount) {
		ddbiuo.SetTotalamount(input.Totalamount)
	} else if forceUpdateMap["totalamount"] {
		ddbiuo.ClearTotalamount()
	}
	if !isZero(input.Couponcode) {
		ddbiuo.SetCouponcode(input.Couponcode)
	} else if forceUpdateMap["couponcode"] {
		ddbiuo.ClearCouponcode()
	}
	if !isZero(input.Discountcode) {
		ddbiuo.SetDiscountcode(input.Discountcode)
	} else if forceUpdateMap["discountcode"] {
		ddbiuo.ClearDiscountcode()
	}
	if !isZero(input.Createddate) {
		ddbiuo.SetCreateddate(input.Createddate)
	} else if forceUpdateMap["createddate"] {
		ddbiuo.ClearCreateddate()
	}
	if !isZero(input.Createduser) {
		ddbiuo.SetCreateduser(input.Createduser)
	} else if forceUpdateMap["createduser"] {
		ddbiuo.ClearCreateduser()
	}
	if !isZero(input.Lastupdatedate) {
		ddbiuo.SetLastupdatedate(input.Lastupdatedate)
	} else if forceUpdateMap["lastupdatedate"] {
		ddbiuo.ClearLastupdatedate()
	}
	if !isZero(input.Lastupdateuser) {
		ddbiuo.SetLastupdateuser(input.Lastupdateuser)
	} else if forceUpdateMap["lastupdateuser"] {
		ddbiuo.ClearLastupdateuser()
	}
	return ddbiuo
}

func (ddbiu *DbsandentistDboBillItemUpdate) SetDbsandentistDboBillItem(input *DbsandentistDboBillItem, forceUpdates ...string) *DbsandentistDboBillItemUpdate {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Medicalcode) {
		ddbiu.SetMedicalcode(input.Medicalcode)
	} else if forceUpdateMap["medicalcode"] {
		ddbiu.ClearMedicalcode()
	}
	if !isZero(input.Invoicecode) {
		ddbiu.SetInvoicecode(input.Invoicecode)
	} else if forceUpdateMap["invoicecode"] {
		ddbiu.ClearInvoicecode()
	}
	if !isZero(input.Itemtype) {
		ddbiu.SetItemtype(input.Itemtype)
	} else if forceUpdateMap["itemtype"] {
		ddbiu.ClearItemtype()
	}
	if !isZero(input.Itemcode) {
		ddbiu.SetItemcode(input.Itemcode)
	} else if forceUpdateMap["itemcode"] {
		ddbiu.ClearItemcode()
	}
	if !isZero(input.Itemname) {
		ddbiu.SetItemname(input.Itemname)
	} else if forceUpdateMap["itemname"] {
		ddbiu.ClearItemname()
	}
	if !isZero(input.Uomcode) {
		ddbiu.SetUomcode(input.Uomcode)
	} else if forceUpdateMap["uomcode"] {
		ddbiu.ClearUomcode()
	}
	if !isZero(input.Datetime) {
		ddbiu.SetDatetime(input.Datetime)
	} else if forceUpdateMap["datetime"] {
		ddbiu.ClearDatetime()
	}
	if !isZero(input.Quantity) {
		ddbiu.SetQuantity(input.Quantity)
	} else if forceUpdateMap["quantity"] {
		ddbiu.ClearQuantity()
	}
	if !isZero(input.Price) {
		ddbiu.SetPrice(input.Price)
	} else if forceUpdateMap["price"] {
		ddbiu.ClearPrice()
	}
	if !isZero(input.Discountcoupon) {
		ddbiu.SetDiscountcoupon(input.Discountcoupon)
	} else if forceUpdateMap["discountcoupon"] {
		ddbiu.ClearDiscountcoupon()
	}
	if !isZero(input.Discountamount) {
		ddbiu.SetDiscountamount(input.Discountamount)
	} else if forceUpdateMap["discountamount"] {
		ddbiu.ClearDiscountamount()
	}
	if !isZero(input.Totaldiscount) {
		ddbiu.SetTotaldiscount(input.Totaldiscount)
	} else if forceUpdateMap["totaldiscount"] {
		ddbiu.ClearTotaldiscount()
	}
	if !isZero(input.Totalamount) {
		ddbiu.SetTotalamount(input.Totalamount)
	} else if forceUpdateMap["totalamount"] {
		ddbiu.ClearTotalamount()
	}
	if !isZero(input.Couponcode) {
		ddbiu.SetCouponcode(input.Couponcode)
	} else if forceUpdateMap["couponcode"] {
		ddbiu.ClearCouponcode()
	}
	if !isZero(input.Discountcode) {
		ddbiu.SetDiscountcode(input.Discountcode)
	} else if forceUpdateMap["discountcode"] {
		ddbiu.ClearDiscountcode()
	}
	if !isZero(input.Createddate) {
		ddbiu.SetCreateddate(input.Createddate)
	} else if forceUpdateMap["createddate"] {
		ddbiu.ClearCreateddate()
	}
	if !isZero(input.Createduser) {
		ddbiu.SetCreateduser(input.Createduser)
	} else if forceUpdateMap["createduser"] {
		ddbiu.ClearCreateduser()
	}
	if !isZero(input.Lastupdatedate) {
		ddbiu.SetLastupdatedate(input.Lastupdatedate)
	} else if forceUpdateMap["lastupdatedate"] {
		ddbiu.ClearLastupdatedate()
	}
	if !isZero(input.Lastupdateuser) {
		ddbiu.SetLastupdateuser(input.Lastupdateuser)
	} else if forceUpdateMap["lastupdateuser"] {
		ddbiu.ClearLastupdateuser()
	}
	return ddbiu
}

func (ddbidc *DbsandentistDboBillItemDetailCreate) SetDbsandentistDboBillItemDetail(input *DbsandentistDboBillItemDetail) *DbsandentistDboBillItemDetailCreate {
	if !isZero(input.Medicalcode) {
		ddbidc.SetMedicalcode(input.Medicalcode)
	}
	if !isZero(input.Itemcode) {
		ddbidc.SetItemcode(input.Itemcode)
	}
	if !isZero(input.Itemname) {
		ddbidc.SetItemname(input.Itemname)
	}
	if !isZero(input.Numbermedical) {
		ddbidc.SetNumbermedical(input.Numbermedical)
	}
	if !isZero(input.Note) {
		ddbidc.SetNote(input.Note)
	}
	if !isZero(input.Dateroad) {
		ddbidc.SetDateroad(input.Dateroad)
	}
	if !isZero(input.Createddate) {
		ddbidc.SetCreateddate(input.Createddate)
	}
	if !isZero(input.Createduser) {
		ddbidc.SetCreateduser(input.Createduser)
	}
	if !isZero(input.Lastupdatedate) {
		ddbidc.SetLastupdatedate(input.Lastupdatedate)
	}
	if !isZero(input.Lastupdateuser) {
		ddbidc.SetLastupdateuser(input.Lastupdateuser)
	}
	if !isZero(input.Doctorcode) {
		ddbidc.SetDoctorcode(input.Doctorcode)
	}
	if !isZero(input.Assistantcode) {
		ddbidc.SetAssistantcode(input.Assistantcode)
	}
	if !isZero(input.Signature) {
		ddbidc.SetSignature(input.Signature)
	}
	if !isZero(input.Signdate) {
		ddbidc.SetSigndate(input.Signdate)
	}
	return ddbidc
}

func (ddbiduo *DbsandentistDboBillItemDetailUpdateOne) SetDbsandentistDboBillItemDetail(input *DbsandentistDboBillItemDetail, forceUpdates ...string) *DbsandentistDboBillItemDetailUpdateOne {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Medicalcode) {
		ddbiduo.SetMedicalcode(input.Medicalcode)
	} else if forceUpdateMap["medicalcode"] {
		ddbiduo.ClearMedicalcode()
	}
	if !isZero(input.Itemcode) {
		ddbiduo.SetItemcode(input.Itemcode)
	} else if forceUpdateMap["itemcode"] {
		ddbiduo.ClearItemcode()
	}
	if !isZero(input.Itemname) {
		ddbiduo.SetItemname(input.Itemname)
	} else if forceUpdateMap["itemname"] {
		ddbiduo.ClearItemname()
	}
	if !isZero(input.Numbermedical) {
		ddbiduo.SetNumbermedical(input.Numbermedical)
	} else if forceUpdateMap["numbermedical"] {
		ddbiduo.ClearNumbermedical()
	}
	if !isZero(input.Note) {
		ddbiduo.SetNote(input.Note)
	} else if forceUpdateMap["note"] {
		ddbiduo.ClearNote()
	}
	if !isZero(input.Dateroad) {
		ddbiduo.SetDateroad(input.Dateroad)
	} else if forceUpdateMap["dateroad"] {
		ddbiduo.ClearDateroad()
	}
	if !isZero(input.Createddate) {
		ddbiduo.SetCreateddate(input.Createddate)
	} else if forceUpdateMap["createddate"] {
		ddbiduo.ClearCreateddate()
	}
	if !isZero(input.Createduser) {
		ddbiduo.SetCreateduser(input.Createduser)
	} else if forceUpdateMap["createduser"] {
		ddbiduo.ClearCreateduser()
	}
	if !isZero(input.Lastupdatedate) {
		ddbiduo.SetLastupdatedate(input.Lastupdatedate)
	} else if forceUpdateMap["lastupdatedate"] {
		ddbiduo.ClearLastupdatedate()
	}
	if !isZero(input.Lastupdateuser) {
		ddbiduo.SetLastupdateuser(input.Lastupdateuser)
	} else if forceUpdateMap["lastupdateuser"] {
		ddbiduo.ClearLastupdateuser()
	}
	if !isZero(input.Doctorcode) {
		ddbiduo.SetDoctorcode(input.Doctorcode)
	} else if forceUpdateMap["doctorcode"] {
		ddbiduo.ClearDoctorcode()
	}
	if !isZero(input.Assistantcode) {
		ddbiduo.SetAssistantcode(input.Assistantcode)
	} else if forceUpdateMap["assistantcode"] {
		ddbiduo.ClearAssistantcode()
	}
	if !isZero(input.Signature) {
		ddbiduo.SetSignature(input.Signature)
	} else if forceUpdateMap["signature"] {
		ddbiduo.ClearSignature()
	}
	if !isZero(input.Signdate) {
		ddbiduo.SetSigndate(input.Signdate)
	} else if forceUpdateMap["signdate"] {
		ddbiduo.ClearSigndate()
	}
	return ddbiduo
}

func (ddbidu *DbsandentistDboBillItemDetailUpdate) SetDbsandentistDboBillItemDetail(input *DbsandentistDboBillItemDetail, forceUpdates ...string) *DbsandentistDboBillItemDetailUpdate {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Medicalcode) {
		ddbidu.SetMedicalcode(input.Medicalcode)
	} else if forceUpdateMap["medicalcode"] {
		ddbidu.ClearMedicalcode()
	}
	if !isZero(input.Itemcode) {
		ddbidu.SetItemcode(input.Itemcode)
	} else if forceUpdateMap["itemcode"] {
		ddbidu.ClearItemcode()
	}
	if !isZero(input.Itemname) {
		ddbidu.SetItemname(input.Itemname)
	} else if forceUpdateMap["itemname"] {
		ddbidu.ClearItemname()
	}
	if !isZero(input.Numbermedical) {
		ddbidu.SetNumbermedical(input.Numbermedical)
	} else if forceUpdateMap["numbermedical"] {
		ddbidu.ClearNumbermedical()
	}
	if !isZero(input.Note) {
		ddbidu.SetNote(input.Note)
	} else if forceUpdateMap["note"] {
		ddbidu.ClearNote()
	}
	if !isZero(input.Dateroad) {
		ddbidu.SetDateroad(input.Dateroad)
	} else if forceUpdateMap["dateroad"] {
		ddbidu.ClearDateroad()
	}
	if !isZero(input.Createddate) {
		ddbidu.SetCreateddate(input.Createddate)
	} else if forceUpdateMap["createddate"] {
		ddbidu.ClearCreateddate()
	}
	if !isZero(input.Createduser) {
		ddbidu.SetCreateduser(input.Createduser)
	} else if forceUpdateMap["createduser"] {
		ddbidu.ClearCreateduser()
	}
	if !isZero(input.Lastupdatedate) {
		ddbidu.SetLastupdatedate(input.Lastupdatedate)
	} else if forceUpdateMap["lastupdatedate"] {
		ddbidu.ClearLastupdatedate()
	}
	if !isZero(input.Lastupdateuser) {
		ddbidu.SetLastupdateuser(input.Lastupdateuser)
	} else if forceUpdateMap["lastupdateuser"] {
		ddbidu.ClearLastupdateuser()
	}
	if !isZero(input.Doctorcode) {
		ddbidu.SetDoctorcode(input.Doctorcode)
	} else if forceUpdateMap["doctorcode"] {
		ddbidu.ClearDoctorcode()
	}
	if !isZero(input.Assistantcode) {
		ddbidu.SetAssistantcode(input.Assistantcode)
	} else if forceUpdateMap["assistantcode"] {
		ddbidu.ClearAssistantcode()
	}
	if !isZero(input.Signature) {
		ddbidu.SetSignature(input.Signature)
	} else if forceUpdateMap["signature"] {
		ddbidu.ClearSignature()
	}
	if !isZero(input.Signdate) {
		ddbidu.SetSigndate(input.Signdate)
	} else if forceUpdateMap["signdate"] {
		ddbidu.ClearSigndate()
	}
	return ddbidu
}

func (ddbpc *DbsandentistDboBillPaymentCreate) SetDbsandentistDboBillPayment(input *DbsandentistDboBillPayment) *DbsandentistDboBillPaymentCreate {
	if !isZero(input.Medicalcode) {
		ddbpc.SetMedicalcode(input.Medicalcode)
	}
	if !isZero(input.Invoicecode) {
		ddbpc.SetInvoicecode(input.Invoicecode)
	}
	if !isZero(input.Paymentdate) {
		ddbpc.SetPaymentdate(input.Paymentdate)
	}
	if !isZero(input.Paymentmethod) {
		ddbpc.SetPaymentmethod(input.Paymentmethod)
	}
	if !isZero(input.Paymentamount) {
		ddbpc.SetPaymentamount(input.Paymentamount)
	}
	if !isZero(input.Bank) {
		ddbpc.SetBank(input.Bank)
	}
	if !isZero(input.Sohd) {
		ddbpc.SetSohd(input.Sohd)
	}
	if !isZero(input.Refcode) {
		ddbpc.SetRefcode(input.Refcode)
	}
	if !isZero(input.Createddate) {
		ddbpc.SetCreateddate(input.Createddate)
	}
	if !isZero(input.Createduser) {
		ddbpc.SetCreateduser(input.Createduser)
	}
	if !isZero(input.Lastupdatedate) {
		ddbpc.SetLastupdatedate(input.Lastupdatedate)
	}
	if !isZero(input.Lastupdateuser) {
		ddbpc.SetLastupdateuser(input.Lastupdateuser)
	}
	if !isZero(input.Approvecode) {
		ddbpc.SetApprovecode(input.Approvecode)
	}
	if !isZero(input.Reasoncode) {
		ddbpc.SetReasoncode(input.Reasoncode)
	}
	if !isZero(input.Reasonname) {
		ddbpc.SetReasonname(input.Reasonname)
	}
	if !isZero(input.Note) {
		ddbpc.SetNote(input.Note)
	}
	return ddbpc
}

func (ddbpuo *DbsandentistDboBillPaymentUpdateOne) SetDbsandentistDboBillPayment(input *DbsandentistDboBillPayment, forceUpdates ...string) *DbsandentistDboBillPaymentUpdateOne {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Medicalcode) {
		ddbpuo.SetMedicalcode(input.Medicalcode)
	} else if forceUpdateMap["medicalcode"] {
		ddbpuo.ClearMedicalcode()
	}
	if !isZero(input.Invoicecode) {
		ddbpuo.SetInvoicecode(input.Invoicecode)
	} else if forceUpdateMap["invoicecode"] {
		ddbpuo.ClearInvoicecode()
	}
	if !isZero(input.Paymentdate) {
		ddbpuo.SetPaymentdate(input.Paymentdate)
	} else if forceUpdateMap["paymentdate"] {
		ddbpuo.ClearPaymentdate()
	}
	if !isZero(input.Paymentmethod) {
		ddbpuo.SetPaymentmethod(input.Paymentmethod)
	} else if forceUpdateMap["paymentmethod"] {
		ddbpuo.ClearPaymentmethod()
	}
	if !isZero(input.Paymentamount) {
		ddbpuo.SetPaymentamount(input.Paymentamount)
	} else if forceUpdateMap["paymentamount"] {
		ddbpuo.ClearPaymentamount()
	}
	if !isZero(input.Bank) {
		ddbpuo.SetBank(input.Bank)
	} else if forceUpdateMap["bank"] {
		ddbpuo.ClearBank()
	}
	if !isZero(input.Sohd) {
		ddbpuo.SetSohd(input.Sohd)
	} else if forceUpdateMap["sohd"] {
		ddbpuo.ClearSohd()
	}
	if !isZero(input.Refcode) {
		ddbpuo.SetRefcode(input.Refcode)
	} else if forceUpdateMap["refcode"] {
		ddbpuo.ClearRefcode()
	}
	if !isZero(input.Createddate) {
		ddbpuo.SetCreateddate(input.Createddate)
	} else if forceUpdateMap["createddate"] {
		ddbpuo.ClearCreateddate()
	}
	if !isZero(input.Createduser) {
		ddbpuo.SetCreateduser(input.Createduser)
	} else if forceUpdateMap["createduser"] {
		ddbpuo.ClearCreateduser()
	}
	if !isZero(input.Lastupdatedate) {
		ddbpuo.SetLastupdatedate(input.Lastupdatedate)
	} else if forceUpdateMap["lastupdatedate"] {
		ddbpuo.ClearLastupdatedate()
	}
	if !isZero(input.Lastupdateuser) {
		ddbpuo.SetLastupdateuser(input.Lastupdateuser)
	} else if forceUpdateMap["lastupdateuser"] {
		ddbpuo.ClearLastupdateuser()
	}
	if !isZero(input.Approvecode) {
		ddbpuo.SetApprovecode(input.Approvecode)
	} else if forceUpdateMap["approvecode"] {
		ddbpuo.ClearApprovecode()
	}
	if !isZero(input.Reasoncode) {
		ddbpuo.SetReasoncode(input.Reasoncode)
	} else if forceUpdateMap["reasoncode"] {
		ddbpuo.ClearReasoncode()
	}
	if !isZero(input.Reasonname) {
		ddbpuo.SetReasonname(input.Reasonname)
	} else if forceUpdateMap["reasonname"] {
		ddbpuo.ClearReasonname()
	}
	if !isZero(input.Note) {
		ddbpuo.SetNote(input.Note)
	} else if forceUpdateMap["note"] {
		ddbpuo.ClearNote()
	}
	return ddbpuo
}

func (ddbpu *DbsandentistDboBillPaymentUpdate) SetDbsandentistDboBillPayment(input *DbsandentistDboBillPayment, forceUpdates ...string) *DbsandentistDboBillPaymentUpdate {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Medicalcode) {
		ddbpu.SetMedicalcode(input.Medicalcode)
	} else if forceUpdateMap["medicalcode"] {
		ddbpu.ClearMedicalcode()
	}
	if !isZero(input.Invoicecode) {
		ddbpu.SetInvoicecode(input.Invoicecode)
	} else if forceUpdateMap["invoicecode"] {
		ddbpu.ClearInvoicecode()
	}
	if !isZero(input.Paymentdate) {
		ddbpu.SetPaymentdate(input.Paymentdate)
	} else if forceUpdateMap["paymentdate"] {
		ddbpu.ClearPaymentdate()
	}
	if !isZero(input.Paymentmethod) {
		ddbpu.SetPaymentmethod(input.Paymentmethod)
	} else if forceUpdateMap["paymentmethod"] {
		ddbpu.ClearPaymentmethod()
	}
	if !isZero(input.Paymentamount) {
		ddbpu.SetPaymentamount(input.Paymentamount)
	} else if forceUpdateMap["paymentamount"] {
		ddbpu.ClearPaymentamount()
	}
	if !isZero(input.Bank) {
		ddbpu.SetBank(input.Bank)
	} else if forceUpdateMap["bank"] {
		ddbpu.ClearBank()
	}
	if !isZero(input.Sohd) {
		ddbpu.SetSohd(input.Sohd)
	} else if forceUpdateMap["sohd"] {
		ddbpu.ClearSohd()
	}
	if !isZero(input.Refcode) {
		ddbpu.SetRefcode(input.Refcode)
	} else if forceUpdateMap["refcode"] {
		ddbpu.ClearRefcode()
	}
	if !isZero(input.Createddate) {
		ddbpu.SetCreateddate(input.Createddate)
	} else if forceUpdateMap["createddate"] {
		ddbpu.ClearCreateddate()
	}
	if !isZero(input.Createduser) {
		ddbpu.SetCreateduser(input.Createduser)
	} else if forceUpdateMap["createduser"] {
		ddbpu.ClearCreateduser()
	}
	if !isZero(input.Lastupdatedate) {
		ddbpu.SetLastupdatedate(input.Lastupdatedate)
	} else if forceUpdateMap["lastupdatedate"] {
		ddbpu.ClearLastupdatedate()
	}
	if !isZero(input.Lastupdateuser) {
		ddbpu.SetLastupdateuser(input.Lastupdateuser)
	} else if forceUpdateMap["lastupdateuser"] {
		ddbpu.ClearLastupdateuser()
	}
	if !isZero(input.Approvecode) {
		ddbpu.SetApprovecode(input.Approvecode)
	} else if forceUpdateMap["approvecode"] {
		ddbpu.ClearApprovecode()
	}
	if !isZero(input.Reasoncode) {
		ddbpu.SetReasoncode(input.Reasoncode)
	} else if forceUpdateMap["reasoncode"] {
		ddbpu.ClearReasoncode()
	}
	if !isZero(input.Reasonname) {
		ddbpu.SetReasonname(input.Reasonname)
	} else if forceUpdateMap["reasonname"] {
		ddbpu.ClearReasonname()
	}
	if !isZero(input.Note) {
		ddbpu.SetNote(input.Note)
	} else if forceUpdateMap["note"] {
		ddbpu.ClearNote()
	}
	return ddbpu
}

func (ddcc *DbsandentistDboCustomerCreate) SetDbsandentistDboCustomer(input *DbsandentistDboCustomer) *DbsandentistDboCustomerCreate {
	if !isZero(input.Customercode) {
		ddcc.SetCustomercode(input.Customercode)
	}
	if !isZero(input.Customername) {
		ddcc.SetCustomername(input.Customername)
	}
	if !isZero(input.Birthday) {
		ddcc.SetBirthday(input.Birthday)
	}
	if !isZero(input.Gender) {
		ddcc.SetGender(input.Gender)
	}
	if !isZero(input.Phonenumber) {
		ddcc.SetPhonenumber(input.Phonenumber)
	}
	if !isZero(input.Email) {
		ddcc.SetEmail(input.Email)
	}
	if !isZero(input.Housestreet) {
		ddcc.SetHousestreet(input.Housestreet)
	}
	if !isZero(input.Source) {
		ddcc.SetSource(input.Source)
	}
	if !isZero(input.Createddate) {
		ddcc.SetCreateddate(input.Createddate)
	}
	if !isZero(input.Ghichu) {
		ddcc.SetGhichu(input.Ghichu)
	}
	return ddcc
}

func (ddcuo *DbsandentistDboCustomerUpdateOne) SetDbsandentistDboCustomer(input *DbsandentistDboCustomer, forceUpdates ...string) *DbsandentistDboCustomerUpdateOne {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Customercode) {
		ddcuo.SetCustomercode(input.Customercode)
	} else if forceUpdateMap["customercode"] {
		ddcuo.ClearCustomercode()
	}
	if !isZero(input.Customername) {
		ddcuo.SetCustomername(input.Customername)
	} else if forceUpdateMap["customername"] {
		ddcuo.ClearCustomername()
	}
	if !isZero(input.Birthday) {
		ddcuo.SetBirthday(input.Birthday)
	} else if forceUpdateMap["birthday"] {
		ddcuo.ClearBirthday()
	}
	if !isZero(input.Gender) {
		ddcuo.SetGender(input.Gender)
	} else if forceUpdateMap["gender"] {
		ddcuo.ClearGender()
	}
	if !isZero(input.Phonenumber) {
		ddcuo.SetPhonenumber(input.Phonenumber)
	} else if forceUpdateMap["phonenumber"] {
		ddcuo.ClearPhonenumber()
	}
	if !isZero(input.Email) {
		ddcuo.SetEmail(input.Email)
	} else if forceUpdateMap["email"] {
		ddcuo.ClearEmail()
	}
	if !isZero(input.Housestreet) {
		ddcuo.SetHousestreet(input.Housestreet)
	} else if forceUpdateMap["housestreet"] {
		ddcuo.ClearHousestreet()
	}
	if !isZero(input.Source) {
		ddcuo.SetSource(input.Source)
	} else if forceUpdateMap["source"] {
		ddcuo.ClearSource()
	}
	if !isZero(input.Createddate) {
		ddcuo.SetCreateddate(input.Createddate)
	} else if forceUpdateMap["createddate"] {
		ddcuo.ClearCreateddate()
	}
	if !isZero(input.Ghichu) {
		ddcuo.SetGhichu(input.Ghichu)
	} else if forceUpdateMap["ghichu"] {
		ddcuo.ClearGhichu()
	}
	return ddcuo
}

func (ddcu *DbsandentistDboCustomerUpdate) SetDbsandentistDboCustomer(input *DbsandentistDboCustomer, forceUpdates ...string) *DbsandentistDboCustomerUpdate {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Customercode) {
		ddcu.SetCustomercode(input.Customercode)
	} else if forceUpdateMap["customercode"] {
		ddcu.ClearCustomercode()
	}
	if !isZero(input.Customername) {
		ddcu.SetCustomername(input.Customername)
	} else if forceUpdateMap["customername"] {
		ddcu.ClearCustomername()
	}
	if !isZero(input.Birthday) {
		ddcu.SetBirthday(input.Birthday)
	} else if forceUpdateMap["birthday"] {
		ddcu.ClearBirthday()
	}
	if !isZero(input.Gender) {
		ddcu.SetGender(input.Gender)
	} else if forceUpdateMap["gender"] {
		ddcu.ClearGender()
	}
	if !isZero(input.Phonenumber) {
		ddcu.SetPhonenumber(input.Phonenumber)
	} else if forceUpdateMap["phonenumber"] {
		ddcu.ClearPhonenumber()
	}
	if !isZero(input.Email) {
		ddcu.SetEmail(input.Email)
	} else if forceUpdateMap["email"] {
		ddcu.ClearEmail()
	}
	if !isZero(input.Housestreet) {
		ddcu.SetHousestreet(input.Housestreet)
	} else if forceUpdateMap["housestreet"] {
		ddcu.ClearHousestreet()
	}
	if !isZero(input.Source) {
		ddcu.SetSource(input.Source)
	} else if forceUpdateMap["source"] {
		ddcu.ClearSource()
	}
	if !isZero(input.Createddate) {
		ddcu.SetCreateddate(input.Createddate)
	} else if forceUpdateMap["createddate"] {
		ddcu.ClearCreateddate()
	}
	if !isZero(input.Ghichu) {
		ddcu.SetGhichu(input.Ghichu)
	} else if forceUpdateMap["ghichu"] {
		ddcu.ClearGhichu()
	}
	return ddcu
}

func (dc *DealCreate) SetDeal(input *Deal) *DealCreate {
	if !isZero(input.Status) {
		dc.SetStatus(input.Status)
	}
	if !isZero(input.PersonID) {
		dc.SetPersonID(input.PersonID)
	}
	if input.ParentDealID != nil && !isZero(*input.ParentDealID) {
		dc.SetParentDealID(*input.ParentDealID)
	}
	if !isZero(input.TotalAmount) {
		dc.SetTotalAmount(input.TotalAmount)
	}
	if input.StageID != nil && !isZero(*input.StageID) {
		dc.SetStageID(*input.StageID)
	}
	if !isZero(input.StageHistory) {
		dc.SetStageHistory(input.StageHistory)
	}
	if !isZero(input.Name) {
		dc.SetName(input.Name)
	}
	if !isZero(input.State) {
		dc.SetState(input.State)
	}
	return dc
}

func (duo *DealUpdateOne) SetDeal(input *Deal, forceUpdates ...string) *DealUpdateOne {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Status) {
		duo.SetStatus(input.Status)
	} else if forceUpdateMap["status"] {
		duo.SetStatus(input.Status)
	}
	if !isZero(input.PersonID) {
		duo.SetPersonID(input.PersonID)
	} else if forceUpdateMap["person_id"] {
		duo.ClearPersonID()
	}
	if input.ParentDealID != nil && !isZero(*input.ParentDealID) {
		duo.SetParentDealID(*input.ParentDealID)
	} else if forceUpdateMap["parent_deal_id"] {
		duo.ClearParentDealID()
	}
	if !isZero(input.TotalAmount) {
		duo.SetTotalAmount(input.TotalAmount)
	} else if forceUpdateMap["total_amount"] {
		duo.SetTotalAmount(input.TotalAmount)
	}
	if input.StageID != nil && !isZero(*input.StageID) {
		duo.SetStageID(*input.StageID)
	} else if forceUpdateMap["stage_id"] {
		duo.ClearStageID()
	}
	if !isZero(input.StageHistory) {
		duo.SetStageHistory(input.StageHistory)
	} else if forceUpdateMap["stage_history"] {
		duo.ClearStageHistory()
	}
	if !isZero(input.Name) {
		duo.SetName(input.Name)
	} else if forceUpdateMap["name"] {
		duo.ClearName()
	}
	if !isZero(input.State) {
		duo.SetState(input.State)
	} else if forceUpdateMap["state"] {
		duo.SetState(input.State)
	}
	return duo
}

func (du *DealUpdate) SetDeal(input *Deal, forceUpdates ...string) *DealUpdate {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Status) {
		du.SetStatus(input.Status)
	} else if forceUpdateMap["status"] {
		du.SetStatus(input.Status)
	}
	if !isZero(input.PersonID) {
		du.SetPersonID(input.PersonID)
	} else if forceUpdateMap["person_id"] {
		du.ClearPersonID()
	}
	if input.ParentDealID != nil && !isZero(*input.ParentDealID) {
		du.SetParentDealID(*input.ParentDealID)
	} else if forceUpdateMap["parent_deal_id"] {
		du.ClearParentDealID()
	}
	if !isZero(input.TotalAmount) {
		du.SetTotalAmount(input.TotalAmount)
	} else if forceUpdateMap["total_amount"] {
		du.SetTotalAmount(input.TotalAmount)
	}
	if input.StageID != nil && !isZero(*input.StageID) {
		du.SetStageID(*input.StageID)
	} else if forceUpdateMap["stage_id"] {
		du.ClearStageID()
	}
	if !isZero(input.StageHistory) {
		du.SetStageHistory(input.StageHistory)
	} else if forceUpdateMap["stage_history"] {
		du.ClearStageHistory()
	}
	if !isZero(input.Name) {
		du.SetName(input.Name)
	} else if forceUpdateMap["name"] {
		du.ClearName()
	}
	if !isZero(input.State) {
		du.SetState(input.State)
	} else if forceUpdateMap["state"] {
		du.SetState(input.State)
	}
	return du
}

func (dhc *DealHistoryCreate) SetDealHistory(input *DealHistory) *DealHistoryCreate {
	if !isZero(input.DealID) {
		dhc.SetDealID(input.DealID)
	}
	if !isZero(input.Before) {
		dhc.SetBefore(input.Before)
	}
	if !isZero(input.After) {
		dhc.SetAfter(input.After)
	}
	if !isZero(input.Operation) {
		dhc.SetOperation(input.Operation)
	}
	if !isZero(input.ChangedAt) {
		dhc.SetChangedAt(input.ChangedAt)
	}
	if !isZero(input.UserID) {
		dhc.SetUserID(input.UserID)
	}
	if !isZero(input.Related) {
		dhc.SetRelated(input.Related)
	}
	if !isZero(input.RelatedID) {
		dhc.SetRelatedID(input.RelatedID)
	}
	return dhc
}

func (dhuo *DealHistoryUpdateOne) SetDealHistory(input *DealHistory, forceUpdates ...string) *DealHistoryUpdateOne {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.DealID) {
		dhuo.SetDealID(input.DealID)
	} else if forceUpdateMap["deal_id"] {
		dhuo.ClearDealID()
	}
	if !isZero(input.Before) {
		dhuo.SetBefore(input.Before)
	} else if forceUpdateMap["before"] {
		dhuo.ClearBefore()
	}
	if !isZero(input.After) {
		dhuo.SetAfter(input.After)
	} else if forceUpdateMap["after"] {
		dhuo.ClearAfter()
	}
	if !isZero(input.Operation) {
		dhuo.SetOperation(input.Operation)
	} else if forceUpdateMap["operation"] {
		dhuo.SetOperation(input.Operation)
	}
	if !isZero(input.UserID) {
		dhuo.SetUserID(input.UserID)
	} else if forceUpdateMap["user_id"] {
		dhuo.ClearUserID()
	}
	if !isZero(input.Related) {
		dhuo.SetRelated(input.Related)
	} else if forceUpdateMap["related"] {
		dhuo.ClearRelated()
	}
	if !isZero(input.RelatedID) {
		dhuo.SetRelatedID(input.RelatedID)
	} else if forceUpdateMap["related_id"] {
		dhuo.ClearRelatedID()
	}
	return dhuo
}

func (dhu *DealHistoryUpdate) SetDealHistory(input *DealHistory, forceUpdates ...string) *DealHistoryUpdate {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.DealID) {
		dhu.SetDealID(input.DealID)
	} else if forceUpdateMap["deal_id"] {
		dhu.ClearDealID()
	}
	if !isZero(input.Before) {
		dhu.SetBefore(input.Before)
	} else if forceUpdateMap["before"] {
		dhu.ClearBefore()
	}
	if !isZero(input.After) {
		dhu.SetAfter(input.After)
	} else if forceUpdateMap["after"] {
		dhu.ClearAfter()
	}
	if !isZero(input.Operation) {
		dhu.SetOperation(input.Operation)
	} else if forceUpdateMap["operation"] {
		dhu.SetOperation(input.Operation)
	}
	if !isZero(input.UserID) {
		dhu.SetUserID(input.UserID)
	} else if forceUpdateMap["user_id"] {
		dhu.ClearUserID()
	}
	if !isZero(input.Related) {
		dhu.SetRelated(input.Related)
	} else if forceUpdateMap["related"] {
		dhu.ClearRelated()
	}
	if !isZero(input.RelatedID) {
		dhu.SetRelatedID(input.RelatedID)
	} else if forceUpdateMap["related_id"] {
		dhu.ClearRelatedID()
	}
	return dhu
}

func (dshc *DealStageHistoryCreate) SetDealStageHistory(input *DealStageHistory) *DealStageHistoryCreate {
	if !isZero(input.DealID) {
		dshc.SetDealID(input.DealID)
	}
	if input.Before != nil && !isZero(*input.Before) {
		dshc.SetBefore(*input.Before)
	}
	if input.After != nil && !isZero(*input.After) {
		dshc.SetAfter(*input.After)
	}
	if !isZero(input.UserID) {
		dshc.SetUserID(input.UserID)
	}
	if !isZero(input.ChangedAt) {
		dshc.SetChangedAt(input.ChangedAt)
	}
	return dshc
}

func (dshuo *DealStageHistoryUpdateOne) SetDealStageHistory(input *DealStageHistory, forceUpdates ...string) *DealStageHistoryUpdateOne {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.DealID) {
		dshuo.SetDealID(input.DealID)
	} else if forceUpdateMap["deal_id"] {
		dshuo.SetDealID(input.DealID)
	}
	if input.Before != nil && !isZero(*input.Before) {
		dshuo.SetBefore(*input.Before)
	} else if forceUpdateMap["before"] {
		dshuo.ClearBefore()
	}
	if input.After != nil && !isZero(*input.After) {
		dshuo.SetAfter(*input.After)
	} else if forceUpdateMap["after"] {
		dshuo.ClearAfter()
	}
	if !isZero(input.UserID) {
		dshuo.SetUserID(input.UserID)
	} else if forceUpdateMap["user_id"] {
		dshuo.ClearUserID()
	}
	return dshuo
}

func (dshu *DealStageHistoryUpdate) SetDealStageHistory(input *DealStageHistory, forceUpdates ...string) *DealStageHistoryUpdate {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.DealID) {
		dshu.SetDealID(input.DealID)
	} else if forceUpdateMap["deal_id"] {
		dshu.SetDealID(input.DealID)
	}
	if input.Before != nil && !isZero(*input.Before) {
		dshu.SetBefore(*input.Before)
	} else if forceUpdateMap["before"] {
		dshu.ClearBefore()
	}
	if input.After != nil && !isZero(*input.After) {
		dshu.SetAfter(*input.After)
	} else if forceUpdateMap["after"] {
		dshu.ClearAfter()
	}
	if !isZero(input.UserID) {
		dshu.SetUserID(input.UserID)
	} else if forceUpdateMap["user_id"] {
		dshu.ClearUserID()
	}
	return dshu
}

func (duc *DealUserCreate) SetDealUser(input *DealUser) *DealUserCreate {
	if !isZero(input.DealID) {
		duc.SetDealID(input.DealID)
	}
	if !isZero(input.UserID) {
		duc.SetUserID(input.UserID)
	}
	if !isZero(input.Role) {
		duc.SetRole(input.Role)
	}
	if !isZero(input.Point) {
		duc.SetPoint(input.Point)
	}
	return duc
}

func (duuo *DealUserUpdateOne) SetDealUser(input *DealUser, forceUpdates ...string) *DealUserUpdateOne {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.DealID) {
		duuo.SetDealID(input.DealID)
	} else if forceUpdateMap["deal_id"] {
		duuo.ClearDealID()
	}
	if !isZero(input.UserID) {
		duuo.SetUserID(input.UserID)
	} else if forceUpdateMap["user_id"] {
		duuo.ClearUserID()
	}
	if !isZero(input.Role) {
		duuo.SetRole(input.Role)
	} else if forceUpdateMap["role"] {
		duuo.ClearRole()
	}
	if !isZero(input.Point) {
		duuo.SetPoint(input.Point)
	} else if forceUpdateMap["point"] {
		duuo.ClearPoint()
	}
	return duuo
}

func (duu *DealUserUpdate) SetDealUser(input *DealUser, forceUpdates ...string) *DealUserUpdate {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.DealID) {
		duu.SetDealID(input.DealID)
	} else if forceUpdateMap["deal_id"] {
		duu.ClearDealID()
	}
	if !isZero(input.UserID) {
		duu.SetUserID(input.UserID)
	} else if forceUpdateMap["user_id"] {
		duu.ClearUserID()
	}
	if !isZero(input.Role) {
		duu.SetRole(input.Role)
	} else if forceUpdateMap["role"] {
		duu.ClearRole()
	}
	if !isZero(input.Point) {
		duu.SetPoint(input.Point)
	} else if forceUpdateMap["point"] {
		duu.ClearPoint()
	}
	return duu
}

func (durc *DealUserRatingCreate) SetDealUserRating(input *DealUserRating) *DealUserRatingCreate {
	if !isZero(input.DealUserID) {
		durc.SetDealUserID(input.DealUserID)
	}
	if !isZero(input.Category) {
		durc.SetCategory(input.Category)
	}
	if !isZero(input.Rating) {
		durc.SetRating(input.Rating)
	}
	return durc
}

func (duruo *DealUserRatingUpdateOne) SetDealUserRating(input *DealUserRating, forceUpdates ...string) *DealUserRatingUpdateOne {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.DealUserID) {
		duruo.SetDealUserID(input.DealUserID)
	} else if forceUpdateMap["deal_user_id"] {
		duruo.ClearDealUserID()
	}
	if !isZero(input.Category) {
		duruo.SetCategory(input.Category)
	} else if forceUpdateMap["category"] {
		duruo.ClearCategory()
	}
	if !isZero(input.Rating) {
		duruo.SetRating(input.Rating)
	} else if forceUpdateMap["rating"] {
		duruo.ClearRating()
	}
	return duruo
}

func (duru *DealUserRatingUpdate) SetDealUserRating(input *DealUserRating, forceUpdates ...string) *DealUserRatingUpdate {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.DealUserID) {
		duru.SetDealUserID(input.DealUserID)
	} else if forceUpdateMap["deal_user_id"] {
		duru.ClearDealUserID()
	}
	if !isZero(input.Category) {
		duru.SetCategory(input.Category)
	} else if forceUpdateMap["category"] {
		duru.ClearCategory()
	}
	if !isZero(input.Rating) {
		duru.SetRating(input.Rating)
	} else if forceUpdateMap["rating"] {
		duru.ClearRating()
	}
	return duru
}

func (dc *DepartmentCreate) SetDepartment(input *Department) *DepartmentCreate {
	if !isZero(input.Name) {
		dc.SetName(input.Name)
	}
	if !isZero(input.Description) {
		dc.SetDescription(input.Description)
	}
	return dc
}

func (duo *DepartmentUpdateOne) SetDepartment(input *Department, forceUpdates ...string) *DepartmentUpdateOne {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Name) {
		duo.SetName(input.Name)
	} else if forceUpdateMap["name"] {
		duo.SetName(input.Name)
	}
	if !isZero(input.Description) {
		duo.SetDescription(input.Description)
	} else if forceUpdateMap["description"] {
		duo.ClearDescription()
	}
	return duo
}

func (du *DepartmentUpdate) SetDepartment(input *Department, forceUpdates ...string) *DepartmentUpdate {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Name) {
		du.SetName(input.Name)
	} else if forceUpdateMap["name"] {
		du.SetName(input.Name)
	}
	if !isZero(input.Description) {
		du.SetDescription(input.Description)
	} else if forceUpdateMap["description"] {
		du.ClearDescription()
	}
	return du
}

func (dc *DepositCreate) SetDeposit(input *Deposit) *DepositCreate {
	if !isZero(input.Status) {
		dc.SetStatus(input.Status)
	}
	if !isZero(input.DealID) {
		dc.SetDealID(input.DealID)
	}
	if !isZero(input.TotalAmount) {
		dc.SetTotalAmount(input.TotalAmount)
	}
	if !isZero(input.PaidAmount) {
		dc.SetPaidAmount(input.PaidAmount)
	}
	if !isZero(input.RemainingAmount) {
		dc.SetRemainingAmount(input.RemainingAmount)
	}
	if !isZero(input.State) {
		dc.SetState(input.State)
	}
	if !isZero(input.Description) {
		dc.SetDescription(input.Description)
	}
	if !isZero(input.CreatedBy) {
		dc.SetCreatedBy(input.CreatedBy)
	}
	if !isZero(input.UpdatedBy) {
		dc.SetUpdatedBy(input.UpdatedBy)
	}
	return dc
}

func (duo *DepositUpdateOne) SetDeposit(input *Deposit, forceUpdates ...string) *DepositUpdateOne {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Status) {
		duo.SetStatus(input.Status)
	} else if forceUpdateMap["status"] {
		duo.SetStatus(input.Status)
	}
	if !isZero(input.DealID) {
		duo.SetDealID(input.DealID)
	} else if forceUpdateMap["deal_id"] {
		duo.ClearDealID()
	}
	if !isZero(input.TotalAmount) {
		duo.SetTotalAmount(input.TotalAmount)
	} else if forceUpdateMap["total_amount"] {
		duo.SetTotalAmount(input.TotalAmount)
	}
	if !isZero(input.PaidAmount) {
		duo.SetPaidAmount(input.PaidAmount)
	} else if forceUpdateMap["paid_amount"] {
		duo.SetPaidAmount(input.PaidAmount)
	}
	if !isZero(input.RemainingAmount) {
		duo.SetRemainingAmount(input.RemainingAmount)
	} else if forceUpdateMap["remaining_amount"] {
		duo.SetRemainingAmount(input.RemainingAmount)
	}
	if !isZero(input.State) {
		duo.SetState(input.State)
	} else if forceUpdateMap["state"] {
		duo.SetState(input.State)
	}
	if !isZero(input.Description) {
		duo.SetDescription(input.Description)
	} else if forceUpdateMap["description"] {
		duo.ClearDescription()
	}
	if !isZero(input.CreatedBy) {
		duo.SetCreatedBy(input.CreatedBy)
	} else if forceUpdateMap["created_by"] {
		duo.ClearCreatedBy()
	}
	if !isZero(input.UpdatedBy) {
		duo.SetUpdatedBy(input.UpdatedBy)
	} else if forceUpdateMap["updated_by"] {
		duo.ClearUpdatedBy()
	}
	return duo
}

func (du *DepositUpdate) SetDeposit(input *Deposit, forceUpdates ...string) *DepositUpdate {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Status) {
		du.SetStatus(input.Status)
	} else if forceUpdateMap["status"] {
		du.SetStatus(input.Status)
	}
	if !isZero(input.DealID) {
		du.SetDealID(input.DealID)
	} else if forceUpdateMap["deal_id"] {
		du.ClearDealID()
	}
	if !isZero(input.TotalAmount) {
		du.SetTotalAmount(input.TotalAmount)
	} else if forceUpdateMap["total_amount"] {
		du.SetTotalAmount(input.TotalAmount)
	}
	if !isZero(input.PaidAmount) {
		du.SetPaidAmount(input.PaidAmount)
	} else if forceUpdateMap["paid_amount"] {
		du.SetPaidAmount(input.PaidAmount)
	}
	if !isZero(input.RemainingAmount) {
		du.SetRemainingAmount(input.RemainingAmount)
	} else if forceUpdateMap["remaining_amount"] {
		du.SetRemainingAmount(input.RemainingAmount)
	}
	if !isZero(input.State) {
		du.SetState(input.State)
	} else if forceUpdateMap["state"] {
		du.SetState(input.State)
	}
	if !isZero(input.Description) {
		du.SetDescription(input.Description)
	} else if forceUpdateMap["description"] {
		du.ClearDescription()
	}
	if !isZero(input.CreatedBy) {
		du.SetCreatedBy(input.CreatedBy)
	} else if forceUpdateMap["created_by"] {
		du.ClearCreatedBy()
	}
	if !isZero(input.UpdatedBy) {
		du.SetUpdatedBy(input.UpdatedBy)
	} else if forceUpdateMap["updated_by"] {
		du.ClearUpdatedBy()
	}
	return du
}

func (dac *DepositAllocationCreate) SetDepositAllocation(input *DepositAllocation) *DepositAllocationCreate {
	if !isZero(input.Status) {
		dac.SetStatus(input.Status)
	}
	if !isZero(input.DepositID) {
		dac.SetDepositID(input.DepositID)
	}
	if !isZero(input.AttachmentID) {
		dac.SetAttachmentID(input.AttachmentID)
	}
	if !isZero(input.DealID) {
		dac.SetDealID(input.DealID)
	}
	if !isZero(input.Amount) {
		dac.SetAmount(input.Amount)
	}
	if !isZero(input.State) {
		dac.SetState(input.State)
	}
	if !isZero(input.CreatedBy) {
		dac.SetCreatedBy(input.CreatedBy)
	}
	if !isZero(input.UpdatedBy) {
		dac.SetUpdatedBy(input.UpdatedBy)
	}
	return dac
}

func (dauo *DepositAllocationUpdateOne) SetDepositAllocation(input *DepositAllocation, forceUpdates ...string) *DepositAllocationUpdateOne {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Status) {
		dauo.SetStatus(input.Status)
	} else if forceUpdateMap["status"] {
		dauo.SetStatus(input.Status)
	}
	if !isZero(input.DepositID) {
		dauo.SetDepositID(input.DepositID)
	} else if forceUpdateMap["deposit_id"] {
		dauo.ClearDepositID()
	}
	if !isZero(input.AttachmentID) {
		dauo.SetAttachmentID(input.AttachmentID)
	} else if forceUpdateMap["attachment_id"] {
		dauo.ClearAttachmentID()
	}
	if !isZero(input.DealID) {
		dauo.SetDealID(input.DealID)
	} else if forceUpdateMap["deal_id"] {
		dauo.ClearDealID()
	}
	if !isZero(input.Amount) {
		dauo.SetAmount(input.Amount)
	} else if forceUpdateMap["amount"] {
		dauo.SetAmount(input.Amount)
	}
	if !isZero(input.State) {
		dauo.SetState(input.State)
	} else if forceUpdateMap["state"] {
		dauo.SetState(input.State)
	}
	if !isZero(input.CreatedBy) {
		dauo.SetCreatedBy(input.CreatedBy)
	} else if forceUpdateMap["created_by"] {
		dauo.ClearCreatedBy()
	}
	if !isZero(input.UpdatedBy) {
		dauo.SetUpdatedBy(input.UpdatedBy)
	} else if forceUpdateMap["updated_by"] {
		dauo.ClearUpdatedBy()
	}
	return dauo
}

func (dau *DepositAllocationUpdate) SetDepositAllocation(input *DepositAllocation, forceUpdates ...string) *DepositAllocationUpdate {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Status) {
		dau.SetStatus(input.Status)
	} else if forceUpdateMap["status"] {
		dau.SetStatus(input.Status)
	}
	if !isZero(input.DepositID) {
		dau.SetDepositID(input.DepositID)
	} else if forceUpdateMap["deposit_id"] {
		dau.ClearDepositID()
	}
	if !isZero(input.AttachmentID) {
		dau.SetAttachmentID(input.AttachmentID)
	} else if forceUpdateMap["attachment_id"] {
		dau.ClearAttachmentID()
	}
	if !isZero(input.DealID) {
		dau.SetDealID(input.DealID)
	} else if forceUpdateMap["deal_id"] {
		dau.ClearDealID()
	}
	if !isZero(input.Amount) {
		dau.SetAmount(input.Amount)
	} else if forceUpdateMap["amount"] {
		dau.SetAmount(input.Amount)
	}
	if !isZero(input.State) {
		dau.SetState(input.State)
	} else if forceUpdateMap["state"] {
		dau.SetState(input.State)
	}
	if !isZero(input.CreatedBy) {
		dau.SetCreatedBy(input.CreatedBy)
	} else if forceUpdateMap["created_by"] {
		dau.ClearCreatedBy()
	}
	if !isZero(input.UpdatedBy) {
		dau.SetUpdatedBy(input.UpdatedBy)
	} else if forceUpdateMap["updated_by"] {
		dau.ClearUpdatedBy()
	}
	return dau
}

func (dpc *DepositPaymentCreate) SetDepositPayment(input *DepositPayment) *DepositPaymentCreate {
	if !isZero(input.Status) {
		dpc.SetStatus(input.Status)
	}
	if !isZero(input.DepositID) {
		dpc.SetDepositID(input.DepositID)
	}
	if !isZero(input.PaymentID) {
		dpc.SetPaymentID(input.PaymentID)
	}
	if !isZero(input.Amount) {
		dpc.SetAmount(input.Amount)
	}
	if !isZero(input.ConversionDate) {
		dpc.SetConversionDate(input.ConversionDate)
	}
	if !isZero(input.State) {
		dpc.SetState(input.State)
	}
	if !isZero(input.Note) {
		dpc.SetNote(input.Note)
	}
	if !isZero(input.CreatedBy) {
		dpc.SetCreatedBy(input.CreatedBy)
	}
	if !isZero(input.UpdatedBy) {
		dpc.SetUpdatedBy(input.UpdatedBy)
	}
	return dpc
}

func (dpuo *DepositPaymentUpdateOne) SetDepositPayment(input *DepositPayment, forceUpdates ...string) *DepositPaymentUpdateOne {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Status) {
		dpuo.SetStatus(input.Status)
	} else if forceUpdateMap["status"] {
		dpuo.SetStatus(input.Status)
	}
	if !isZero(input.DepositID) {
		dpuo.SetDepositID(input.DepositID)
	} else if forceUpdateMap["deposit_id"] {
		dpuo.ClearDepositID()
	}
	if !isZero(input.PaymentID) {
		dpuo.SetPaymentID(input.PaymentID)
	} else if forceUpdateMap["payment_id"] {
		dpuo.ClearPaymentID()
	}
	if !isZero(input.Amount) {
		dpuo.SetAmount(input.Amount)
	} else if forceUpdateMap["amount"] {
		dpuo.SetAmount(input.Amount)
	}
	if !isZero(input.ConversionDate) {
		dpuo.SetConversionDate(input.ConversionDate)
	} else if forceUpdateMap["conversion_date"] {
		dpuo.SetConversionDate(input.ConversionDate)
	}
	if !isZero(input.State) {
		dpuo.SetState(input.State)
	} else if forceUpdateMap["state"] {
		dpuo.SetState(input.State)
	}
	if !isZero(input.Note) {
		dpuo.SetNote(input.Note)
	} else if forceUpdateMap["note"] {
		dpuo.ClearNote()
	}
	if !isZero(input.CreatedBy) {
		dpuo.SetCreatedBy(input.CreatedBy)
	} else if forceUpdateMap["created_by"] {
		dpuo.ClearCreatedBy()
	}
	if !isZero(input.UpdatedBy) {
		dpuo.SetUpdatedBy(input.UpdatedBy)
	} else if forceUpdateMap["updated_by"] {
		dpuo.ClearUpdatedBy()
	}
	return dpuo
}

func (dpu *DepositPaymentUpdate) SetDepositPayment(input *DepositPayment, forceUpdates ...string) *DepositPaymentUpdate {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Status) {
		dpu.SetStatus(input.Status)
	} else if forceUpdateMap["status"] {
		dpu.SetStatus(input.Status)
	}
	if !isZero(input.DepositID) {
		dpu.SetDepositID(input.DepositID)
	} else if forceUpdateMap["deposit_id"] {
		dpu.ClearDepositID()
	}
	if !isZero(input.PaymentID) {
		dpu.SetPaymentID(input.PaymentID)
	} else if forceUpdateMap["payment_id"] {
		dpu.ClearPaymentID()
	}
	if !isZero(input.Amount) {
		dpu.SetAmount(input.Amount)
	} else if forceUpdateMap["amount"] {
		dpu.SetAmount(input.Amount)
	}
	if !isZero(input.ConversionDate) {
		dpu.SetConversionDate(input.ConversionDate)
	} else if forceUpdateMap["conversion_date"] {
		dpu.SetConversionDate(input.ConversionDate)
	}
	if !isZero(input.State) {
		dpu.SetState(input.State)
	} else if forceUpdateMap["state"] {
		dpu.SetState(input.State)
	}
	if !isZero(input.Note) {
		dpu.SetNote(input.Note)
	} else if forceUpdateMap["note"] {
		dpu.ClearNote()
	}
	if !isZero(input.CreatedBy) {
		dpu.SetCreatedBy(input.CreatedBy)
	} else if forceUpdateMap["created_by"] {
		dpu.ClearCreatedBy()
	}
	if !isZero(input.UpdatedBy) {
		dpu.SetUpdatedBy(input.UpdatedBy)
	} else if forceUpdateMap["updated_by"] {
		dpu.ClearUpdatedBy()
	}
	return dpu
}

func (dc *DiscountCreate) SetDiscount(input *Discount) *DiscountCreate {
	if !isZero(input.Status) {
		dc.SetStatus(input.Status)
	}
	if !isZero(input.Name) {
		dc.SetName(input.Name)
	}
	if !isZero(input.Type) {
		dc.SetType(input.Type)
	}
	if !isZero(input.Value) {
		dc.SetValue(input.Value)
	}
	if !isZero(input.Scope) {
		dc.SetScope(input.Scope)
	}
	if !isZero(input.Condition) {
		dc.SetCondition(input.Condition)
	}
	if !isZero(input.UsageType) {
		dc.SetUsageType(input.UsageType)
	}
	if !isZero(input.Description) {
		dc.SetDescription(input.Description)
	}
	if !isZero(input.Meta) {
		dc.SetMeta(input.Meta)
	}
	if !isZero(input.Start) {
		dc.SetStart(input.Start)
	}
	if !isZero(input.End) {
		dc.SetEnd(input.End)
	}
	return dc
}

func (duo *DiscountUpdateOne) SetDiscount(input *Discount, forceUpdates ...string) *DiscountUpdateOne {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Status) {
		duo.SetStatus(input.Status)
	} else if forceUpdateMap["status"] {
		duo.SetStatus(input.Status)
	}
	if !isZero(input.Name) {
		duo.SetName(input.Name)
	} else if forceUpdateMap["name"] {
		duo.SetName(input.Name)
	}
	if !isZero(input.Type) {
		duo.SetType(input.Type)
	} else if forceUpdateMap["type"] {
		duo.SetType(input.Type)
	}
	if !isZero(input.Value) {
		duo.SetValue(input.Value)
	} else if forceUpdateMap["value"] {
		duo.SetValue(input.Value)
	}
	if !isZero(input.Scope) {
		duo.SetScope(input.Scope)
	} else if forceUpdateMap["scope"] {
		duo.SetScope(input.Scope)
	}
	if !isZero(input.Condition) {
		duo.SetCondition(input.Condition)
	} else if forceUpdateMap["condition"] {
		duo.ClearCondition()
	}
	if !isZero(input.UsageType) {
		duo.SetUsageType(input.UsageType)
	} else if forceUpdateMap["usage_type"] {
		duo.SetUsageType(input.UsageType)
	}
	if !isZero(input.Description) {
		duo.SetDescription(input.Description)
	} else if forceUpdateMap["description"] {
		duo.ClearDescription()
	}
	if !isZero(input.Meta) {
		duo.SetMeta(input.Meta)
	} else if forceUpdateMap["meta"] {
		duo.ClearMeta()
	}
	if !isZero(input.Start) {
		duo.SetStart(input.Start)
	} else if forceUpdateMap["start"] {
		duo.ClearStart()
	}
	if !isZero(input.End) {
		duo.SetEnd(input.End)
	} else if forceUpdateMap["end"] {
		duo.ClearEnd()
	}
	return duo
}

func (du *DiscountUpdate) SetDiscount(input *Discount, forceUpdates ...string) *DiscountUpdate {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Status) {
		du.SetStatus(input.Status)
	} else if forceUpdateMap["status"] {
		du.SetStatus(input.Status)
	}
	if !isZero(input.Name) {
		du.SetName(input.Name)
	} else if forceUpdateMap["name"] {
		du.SetName(input.Name)
	}
	if !isZero(input.Type) {
		du.SetType(input.Type)
	} else if forceUpdateMap["type"] {
		du.SetType(input.Type)
	}
	if !isZero(input.Value) {
		du.SetValue(input.Value)
	} else if forceUpdateMap["value"] {
		du.SetValue(input.Value)
	}
	if !isZero(input.Scope) {
		du.SetScope(input.Scope)
	} else if forceUpdateMap["scope"] {
		du.SetScope(input.Scope)
	}
	if !isZero(input.Condition) {
		du.SetCondition(input.Condition)
	} else if forceUpdateMap["condition"] {
		du.ClearCondition()
	}
	if !isZero(input.UsageType) {
		du.SetUsageType(input.UsageType)
	} else if forceUpdateMap["usage_type"] {
		du.SetUsageType(input.UsageType)
	}
	if !isZero(input.Description) {
		du.SetDescription(input.Description)
	} else if forceUpdateMap["description"] {
		du.ClearDescription()
	}
	if !isZero(input.Meta) {
		du.SetMeta(input.Meta)
	} else if forceUpdateMap["meta"] {
		du.ClearMeta()
	}
	if !isZero(input.Start) {
		du.SetStart(input.Start)
	} else if forceUpdateMap["start"] {
		du.ClearStart()
	}
	if !isZero(input.End) {
		du.SetEnd(input.End)
	} else if forceUpdateMap["end"] {
		du.ClearEnd()
	}
	return du
}

func (duc *DiscountUsageCreate) SetDiscountUsage(input *DiscountUsage) *DiscountUsageCreate {
	if !isZero(input.Status) {
		duc.SetStatus(input.Status)
	}
	if !isZero(input.DiscountID) {
		duc.SetDiscountID(input.DiscountID)
	}
	if input.UserID != nil && !isZero(*input.UserID) {
		duc.SetUserID(*input.UserID)
	}
	if input.PersonID != nil && !isZero(*input.PersonID) {
		duc.SetPersonID(*input.PersonID)
	}
	if input.AttachmentID != nil && !isZero(*input.AttachmentID) {
		duc.SetAttachmentID(*input.AttachmentID)
	}
	if !isZero(input.EntityType) {
		duc.SetEntityType(input.EntityType)
	}
	if input.DealID != nil && !isZero(*input.DealID) {
		duc.SetDealID(*input.DealID)
	}
	if !isZero(input.UsageCount) {
		duc.SetUsageCount(input.UsageCount)
	}
	if !isZero(input.Value) {
		duc.SetValue(input.Value)
	}
	return duc
}

func (duuo *DiscountUsageUpdateOne) SetDiscountUsage(input *DiscountUsage, forceUpdates ...string) *DiscountUsageUpdateOne {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Status) {
		duuo.SetStatus(input.Status)
	} else if forceUpdateMap["status"] {
		duuo.SetStatus(input.Status)
	}
	if !isZero(input.DiscountID) {
		duuo.SetDiscountID(input.DiscountID)
	} else if forceUpdateMap["discount_id"] {
		duuo.SetDiscountID(input.DiscountID)
	}
	if input.UserID != nil && !isZero(*input.UserID) {
		duuo.SetUserID(*input.UserID)
	} else if forceUpdateMap["user_id"] {
		duuo.ClearUserID()
	}
	if input.PersonID != nil && !isZero(*input.PersonID) {
		duuo.SetPersonID(*input.PersonID)
	} else if forceUpdateMap["person_id"] {
		duuo.ClearPersonID()
	}
	if input.AttachmentID != nil && !isZero(*input.AttachmentID) {
		duuo.SetAttachmentID(*input.AttachmentID)
	} else if forceUpdateMap["attachment_id"] {
		duuo.ClearAttachmentID()
	}
	if !isZero(input.EntityType) {
		duuo.SetEntityType(input.EntityType)
	} else if forceUpdateMap["entity_type"] {
		duuo.ClearEntityType()
	}
	if input.DealID != nil && !isZero(*input.DealID) {
		duuo.SetDealID(*input.DealID)
	} else if forceUpdateMap["deal_id"] {
		duuo.ClearDealID()
	}
	if !isZero(input.UsageCount) {
		duuo.SetUsageCount(input.UsageCount)
	} else if forceUpdateMap["usage_count"] {
		duuo.SetUsageCount(input.UsageCount)
	}
	if !isZero(input.Value) {
		duuo.SetValue(input.Value)
	} else if forceUpdateMap["value"] {
		duuo.SetValue(input.Value)
	}
	return duuo
}

func (duu *DiscountUsageUpdate) SetDiscountUsage(input *DiscountUsage, forceUpdates ...string) *DiscountUsageUpdate {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Status) {
		duu.SetStatus(input.Status)
	} else if forceUpdateMap["status"] {
		duu.SetStatus(input.Status)
	}
	if !isZero(input.DiscountID) {
		duu.SetDiscountID(input.DiscountID)
	} else if forceUpdateMap["discount_id"] {
		duu.SetDiscountID(input.DiscountID)
	}
	if input.UserID != nil && !isZero(*input.UserID) {
		duu.SetUserID(*input.UserID)
	} else if forceUpdateMap["user_id"] {
		duu.ClearUserID()
	}
	if input.PersonID != nil && !isZero(*input.PersonID) {
		duu.SetPersonID(*input.PersonID)
	} else if forceUpdateMap["person_id"] {
		duu.ClearPersonID()
	}
	if input.AttachmentID != nil && !isZero(*input.AttachmentID) {
		duu.SetAttachmentID(*input.AttachmentID)
	} else if forceUpdateMap["attachment_id"] {
		duu.ClearAttachmentID()
	}
	if !isZero(input.EntityType) {
		duu.SetEntityType(input.EntityType)
	} else if forceUpdateMap["entity_type"] {
		duu.ClearEntityType()
	}
	if input.DealID != nil && !isZero(*input.DealID) {
		duu.SetDealID(*input.DealID)
	} else if forceUpdateMap["deal_id"] {
		duu.ClearDealID()
	}
	if !isZero(input.UsageCount) {
		duu.SetUsageCount(input.UsageCount)
	} else if forceUpdateMap["usage_count"] {
		duu.SetUsageCount(input.UsageCount)
	}
	if !isZero(input.Value) {
		duu.SetValue(input.Value)
	} else if forceUpdateMap["value"] {
		duu.SetValue(input.Value)
	}
	return duu
}

func (ehc *EntityHistoryCreate) SetEntityHistory(input *EntityHistory) *EntityHistoryCreate {
	if !isZero(input.EntityType) {
		ehc.SetEntityType(input.EntityType)
	}
	if !isZero(input.EntityID) {
		ehc.SetEntityID(input.EntityID)
	}
	if !isZero(input.Before) {
		ehc.SetBefore(input.Before)
	}
	if !isZero(input.After) {
		ehc.SetAfter(input.After)
	}
	if !isZero(input.Operation) {
		ehc.SetOperation(input.Operation)
	}
	if !isZero(input.ChangedAt) {
		ehc.SetChangedAt(input.ChangedAt)
	}
	if !isZero(input.UserID) {
		ehc.SetUserID(input.UserID)
	}
	return ehc
}

func (ehuo *EntityHistoryUpdateOne) SetEntityHistory(input *EntityHistory, forceUpdates ...string) *EntityHistoryUpdateOne {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.EntityType) {
		ehuo.SetEntityType(input.EntityType)
	} else if forceUpdateMap["entity_type"] {
		ehuo.SetEntityType(input.EntityType)
	}
	if !isZero(input.EntityID) {
		ehuo.SetEntityID(input.EntityID)
	} else if forceUpdateMap["entity_id"] {
		ehuo.SetEntityID(input.EntityID)
	}
	if !isZero(input.Before) {
		ehuo.SetBefore(input.Before)
	} else if forceUpdateMap["before"] {
		ehuo.ClearBefore()
	}
	if !isZero(input.After) {
		ehuo.SetAfter(input.After)
	} else if forceUpdateMap["after"] {
		ehuo.ClearAfter()
	}
	if !isZero(input.Operation) {
		ehuo.SetOperation(input.Operation)
	} else if forceUpdateMap["operation"] {
		ehuo.SetOperation(input.Operation)
	}
	if !isZero(input.UserID) {
		ehuo.SetUserID(input.UserID)
	} else if forceUpdateMap["user_id"] {
		ehuo.ClearUserID()
	}
	return ehuo
}

func (ehu *EntityHistoryUpdate) SetEntityHistory(input *EntityHistory, forceUpdates ...string) *EntityHistoryUpdate {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.EntityType) {
		ehu.SetEntityType(input.EntityType)
	} else if forceUpdateMap["entity_type"] {
		ehu.SetEntityType(input.EntityType)
	}
	if !isZero(input.EntityID) {
		ehu.SetEntityID(input.EntityID)
	} else if forceUpdateMap["entity_id"] {
		ehu.SetEntityID(input.EntityID)
	}
	if !isZero(input.Before) {
		ehu.SetBefore(input.Before)
	} else if forceUpdateMap["before"] {
		ehu.ClearBefore()
	}
	if !isZero(input.After) {
		ehu.SetAfter(input.After)
	} else if forceUpdateMap["after"] {
		ehu.ClearAfter()
	}
	if !isZero(input.Operation) {
		ehu.SetOperation(input.Operation)
	} else if forceUpdateMap["operation"] {
		ehu.SetOperation(input.Operation)
	}
	if !isZero(input.UserID) {
		ehu.SetUserID(input.UserID)
	} else if forceUpdateMap["user_id"] {
		ehu.ClearUserID()
	}
	return ehu
}

func (ejc *ExportJobCreate) SetExportJob(input *ExportJob) *ExportJobCreate {
	if !isZero(input.Status) {
		ejc.SetStatus(input.Status)
	}
	if !isZero(input.UserID) {
		ejc.SetUserID(input.UserID)
	}
	if !isZero(input.State) {
		ejc.SetState(input.State)
	}
	if !isZero(input.Progress) {
		ejc.SetProgress(input.Progress)
	}
	if input.FilePath != nil && !isZero(*input.FilePath) {
		ejc.SetFilePath(*input.FilePath)
	}
	if input.StartedAt != nil && !isZero(*input.StartedAt) {
		ejc.SetStartedAt(*input.StartedAt)
	}
	if input.CompletedAt != nil && !isZero(*input.CompletedAt) {
		ejc.SetCompletedAt(*input.CompletedAt)
	}
	if !isZero(input.RequestPayload) {
		ejc.SetRequestPayload(input.RequestPayload)
	}
	if input.ErrorMessage != nil && !isZero(*input.ErrorMessage) {
		ejc.SetErrorMessage(*input.ErrorMessage)
	}
	if input.EstimatedRecords != nil && !isZero(*input.EstimatedRecords) {
		ejc.SetEstimatedRecords(*input.EstimatedRecords)
	}
	if input.ActualRecords != nil && !isZero(*input.ActualRecords) {
		ejc.SetActualRecords(*input.ActualRecords)
	}
	if input.ExpiresAt != nil && !isZero(*input.ExpiresAt) {
		ejc.SetExpiresAt(*input.ExpiresAt)
	}
	return ejc
}

func (ejuo *ExportJobUpdateOne) SetExportJob(input *ExportJob, forceUpdates ...string) *ExportJobUpdateOne {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Status) {
		ejuo.SetStatus(input.Status)
	} else if forceUpdateMap["status"] {
		ejuo.SetStatus(input.Status)
	}
	if !isZero(input.UserID) {
		ejuo.SetUserID(input.UserID)
	} else if forceUpdateMap["user_id"] {
		ejuo.ClearUserID()
	}
	if !isZero(input.State) {
		ejuo.SetState(input.State)
	} else if forceUpdateMap["state"] {
		ejuo.SetState(input.State)
	}
	if !isZero(input.Progress) {
		ejuo.SetProgress(input.Progress)
	} else if forceUpdateMap["progress"] {
		ejuo.SetProgress(input.Progress)
	}
	if input.FilePath != nil && !isZero(*input.FilePath) {
		ejuo.SetFilePath(*input.FilePath)
	} else if forceUpdateMap["file_path"] {
		ejuo.ClearFilePath()
	}
	if input.StartedAt != nil && !isZero(*input.StartedAt) {
		ejuo.SetStartedAt(*input.StartedAt)
	} else if forceUpdateMap["started_at"] {
		ejuo.ClearStartedAt()
	}
	if input.CompletedAt != nil && !isZero(*input.CompletedAt) {
		ejuo.SetCompletedAt(*input.CompletedAt)
	} else if forceUpdateMap["completed_at"] {
		ejuo.ClearCompletedAt()
	}
	if !isZero(input.RequestPayload) {
		ejuo.SetRequestPayload(input.RequestPayload)
	} else if forceUpdateMap["request_payload"] {
		ejuo.ClearRequestPayload()
	}
	if input.ErrorMessage != nil && !isZero(*input.ErrorMessage) {
		ejuo.SetErrorMessage(*input.ErrorMessage)
	} else if forceUpdateMap["error_message"] {
		ejuo.ClearErrorMessage()
	}
	if input.EstimatedRecords != nil && !isZero(*input.EstimatedRecords) {
		ejuo.SetEstimatedRecords(*input.EstimatedRecords)
	} else if forceUpdateMap["estimated_records"] {
		ejuo.ClearEstimatedRecords()
	}
	if input.ActualRecords != nil && !isZero(*input.ActualRecords) {
		ejuo.SetActualRecords(*input.ActualRecords)
	} else if forceUpdateMap["actual_records"] {
		ejuo.ClearActualRecords()
	}
	if input.ExpiresAt != nil && !isZero(*input.ExpiresAt) {
		ejuo.SetExpiresAt(*input.ExpiresAt)
	} else if forceUpdateMap["expires_at"] {
		ejuo.ClearExpiresAt()
	}
	return ejuo
}

func (eju *ExportJobUpdate) SetExportJob(input *ExportJob, forceUpdates ...string) *ExportJobUpdate {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Status) {
		eju.SetStatus(input.Status)
	} else if forceUpdateMap["status"] {
		eju.SetStatus(input.Status)
	}
	if !isZero(input.UserID) {
		eju.SetUserID(input.UserID)
	} else if forceUpdateMap["user_id"] {
		eju.ClearUserID()
	}
	if !isZero(input.State) {
		eju.SetState(input.State)
	} else if forceUpdateMap["state"] {
		eju.SetState(input.State)
	}
	if !isZero(input.Progress) {
		eju.SetProgress(input.Progress)
	} else if forceUpdateMap["progress"] {
		eju.SetProgress(input.Progress)
	}
	if input.FilePath != nil && !isZero(*input.FilePath) {
		eju.SetFilePath(*input.FilePath)
	} else if forceUpdateMap["file_path"] {
		eju.ClearFilePath()
	}
	if input.StartedAt != nil && !isZero(*input.StartedAt) {
		eju.SetStartedAt(*input.StartedAt)
	} else if forceUpdateMap["started_at"] {
		eju.ClearStartedAt()
	}
	if input.CompletedAt != nil && !isZero(*input.CompletedAt) {
		eju.SetCompletedAt(*input.CompletedAt)
	} else if forceUpdateMap["completed_at"] {
		eju.ClearCompletedAt()
	}
	if !isZero(input.RequestPayload) {
		eju.SetRequestPayload(input.RequestPayload)
	} else if forceUpdateMap["request_payload"] {
		eju.ClearRequestPayload()
	}
	if input.ErrorMessage != nil && !isZero(*input.ErrorMessage) {
		eju.SetErrorMessage(*input.ErrorMessage)
	} else if forceUpdateMap["error_message"] {
		eju.ClearErrorMessage()
	}
	if input.EstimatedRecords != nil && !isZero(*input.EstimatedRecords) {
		eju.SetEstimatedRecords(*input.EstimatedRecords)
	} else if forceUpdateMap["estimated_records"] {
		eju.ClearEstimatedRecords()
	}
	if input.ActualRecords != nil && !isZero(*input.ActualRecords) {
		eju.SetActualRecords(*input.ActualRecords)
	} else if forceUpdateMap["actual_records"] {
		eju.ClearActualRecords()
	}
	if input.ExpiresAt != nil && !isZero(*input.ExpiresAt) {
		eju.SetExpiresAt(*input.ExpiresAt)
	} else if forceUpdateMap["expires_at"] {
		eju.ClearExpiresAt()
	}
	return eju
}

func (fc *FileCreate) SetFile(input *File) *FileCreate {
	if !isZero(input.Status) {
		fc.SetStatus(input.Status)
	}
	if !isZero(input.Name) {
		fc.SetName(input.Name)
	}
	if !isZero(input.Kind) {
		fc.SetKind(input.Kind)
	}
	if !isZero(input.Type) {
		fc.SetType(input.Type)
	}
	if !isZero(input.Size) {
		fc.SetSize(input.Size)
	}
	if !isZero(input.Path) {
		fc.SetPath(input.Path)
	}
	if !isZero(input.UserID) {
		fc.SetUserID(input.UserID)
	}
	if !isZero(input.Storage) {
		fc.SetStorage(input.Storage)
	}
	if !isZero(input.Meta) {
		fc.SetMeta(input.Meta)
	}
	return fc
}

func (fuo *FileUpdateOne) SetFile(input *File, forceUpdates ...string) *FileUpdateOne {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Status) {
		fuo.SetStatus(input.Status)
	} else if forceUpdateMap["status"] {
		fuo.SetStatus(input.Status)
	}
	if !isZero(input.Name) {
		fuo.SetName(input.Name)
	} else if forceUpdateMap["name"] {
		fuo.ClearName()
	}
	if !isZero(input.Kind) {
		fuo.SetKind(input.Kind)
	} else if forceUpdateMap["kind"] {
		fuo.ClearKind()
	}
	if !isZero(input.Type) {
		fuo.SetType(input.Type)
	} else if forceUpdateMap["type"] {
		fuo.ClearType()
	}
	if !isZero(input.Size) {
		fuo.SetSize(input.Size)
	} else if forceUpdateMap["size"] {
		fuo.SetSize(input.Size)
	}
	if !isZero(input.Path) {
		fuo.SetPath(input.Path)
	} else if forceUpdateMap["path"] {
		fuo.SetPath(input.Path)
	}
	if !isZero(input.UserID) {
		fuo.SetUserID(input.UserID)
	} else if forceUpdateMap["user_id"] {
		fuo.ClearUserID()
	}
	if !isZero(input.Storage) {
		fuo.SetStorage(input.Storage)
	} else if forceUpdateMap["storage"] {
		fuo.SetStorage(input.Storage)
	}
	if !isZero(input.Meta) {
		fuo.SetMeta(input.Meta)
	} else if forceUpdateMap["meta"] {
		fuo.ClearMeta()
	}
	return fuo
}

func (fu *FileUpdate) SetFile(input *File, forceUpdates ...string) *FileUpdate {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Status) {
		fu.SetStatus(input.Status)
	} else if forceUpdateMap["status"] {
		fu.SetStatus(input.Status)
	}
	if !isZero(input.Name) {
		fu.SetName(input.Name)
	} else if forceUpdateMap["name"] {
		fu.ClearName()
	}
	if !isZero(input.Kind) {
		fu.SetKind(input.Kind)
	} else if forceUpdateMap["kind"] {
		fu.ClearKind()
	}
	if !isZero(input.Type) {
		fu.SetType(input.Type)
	} else if forceUpdateMap["type"] {
		fu.ClearType()
	}
	if !isZero(input.Size) {
		fu.SetSize(input.Size)
	} else if forceUpdateMap["size"] {
		fu.SetSize(input.Size)
	}
	if !isZero(input.Path) {
		fu.SetPath(input.Path)
	} else if forceUpdateMap["path"] {
		fu.SetPath(input.Path)
	}
	if !isZero(input.UserID) {
		fu.SetUserID(input.UserID)
	} else if forceUpdateMap["user_id"] {
		fu.ClearUserID()
	}
	if !isZero(input.Storage) {
		fu.SetStorage(input.Storage)
	} else if forceUpdateMap["storage"] {
		fu.SetStorage(input.Storage)
	}
	if !isZero(input.Meta) {
		fu.SetMeta(input.Meta)
	} else if forceUpdateMap["meta"] {
		fu.ClearMeta()
	}
	return fu
}

func (fuc *FileUsageCreate) SetFileUsage(input *FileUsage) *FileUsageCreate {
	if !isZero(input.Status) {
		fuc.SetStatus(input.Status)
	}
	if !isZero(input.FileID) {
		fuc.SetFileID(input.FileID)
	}
	if !isZero(input.EntityType) {
		fuc.SetEntityType(input.EntityType)
	}
	if !isZero(input.EntityID) {
		fuc.SetEntityID(input.EntityID)
	}
	if !isZero(input.UsageType) {
		fuc.SetUsageType(input.UsageType)
	}
	if !isZero(input.UsageMeta) {
		fuc.SetUsageMeta(input.UsageMeta)
	}
	if !isZero(input.TrackID) {
		fuc.SetTrackID(input.TrackID)
	}
	return fuc
}

func (fuuo *FileUsageUpdateOne) SetFileUsage(input *FileUsage, forceUpdates ...string) *FileUsageUpdateOne {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Status) {
		fuuo.SetStatus(input.Status)
	} else if forceUpdateMap["status"] {
		fuuo.SetStatus(input.Status)
	}
	if !isZero(input.FileID) {
		fuuo.SetFileID(input.FileID)
	} else if forceUpdateMap["file_id"] {
		fuuo.SetFileID(input.FileID)
	}
	if !isZero(input.EntityType) {
		fuuo.SetEntityType(input.EntityType)
	} else if forceUpdateMap["entity_type"] {
		fuuo.SetEntityType(input.EntityType)
	}
	if !isZero(input.EntityID) {
		fuuo.SetEntityID(input.EntityID)
	} else if forceUpdateMap["entity_id"] {
		fuuo.SetEntityID(input.EntityID)
	}
	if !isZero(input.UsageType) {
		fuuo.SetUsageType(input.UsageType)
	} else if forceUpdateMap["usage_type"] {
		fuuo.ClearUsageType()
	}
	if !isZero(input.UsageMeta) {
		fuuo.SetUsageMeta(input.UsageMeta)
	} else if forceUpdateMap["usage_meta"] {
		fuuo.ClearUsageMeta()
	}
	if !isZero(input.TrackID) {
		fuuo.SetTrackID(input.TrackID)
	} else if forceUpdateMap["track_id"] {
		fuuo.ClearTrackID()
	}
	return fuuo
}

func (fuu *FileUsageUpdate) SetFileUsage(input *FileUsage, forceUpdates ...string) *FileUsageUpdate {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Status) {
		fuu.SetStatus(input.Status)
	} else if forceUpdateMap["status"] {
		fuu.SetStatus(input.Status)
	}
	if !isZero(input.FileID) {
		fuu.SetFileID(input.FileID)
	} else if forceUpdateMap["file_id"] {
		fuu.SetFileID(input.FileID)
	}
	if !isZero(input.EntityType) {
		fuu.SetEntityType(input.EntityType)
	} else if forceUpdateMap["entity_type"] {
		fuu.SetEntityType(input.EntityType)
	}
	if !isZero(input.EntityID) {
		fuu.SetEntityID(input.EntityID)
	} else if forceUpdateMap["entity_id"] {
		fuu.SetEntityID(input.EntityID)
	}
	if !isZero(input.UsageType) {
		fuu.SetUsageType(input.UsageType)
	} else if forceUpdateMap["usage_type"] {
		fuu.ClearUsageType()
	}
	if !isZero(input.UsageMeta) {
		fuu.SetUsageMeta(input.UsageMeta)
	} else if forceUpdateMap["usage_meta"] {
		fuu.ClearUsageMeta()
	}
	if !isZero(input.TrackID) {
		fuu.SetTrackID(input.TrackID)
	} else if forceUpdateMap["track_id"] {
		fuu.ClearTrackID()
	}
	return fuu
}

func (fsc *FormSubmissionCreate) SetFormSubmission(input *FormSubmission) *FormSubmissionCreate {
	if !isZero(input.Status) {
		fsc.SetStatus(input.Status)
	}
	if !isZero(input.FullName) {
		fsc.SetFullName(input.FullName)
	}
	if !isZero(input.Phone) {
		fsc.SetPhone(input.Phone)
	}
	if !isZero(input.Email) {
		fsc.SetEmail(input.Email)
	}
	if !isZero(input.Data) {
		fsc.SetData(input.Data)
	}
	if !isZero(input.SourceURL) {
		fsc.SetSourceURL(input.SourceURL)
	}
	if !isZero(input.ReferrerURL) {
		fsc.SetReferrerURL(input.ReferrerURL)
	}
	if !isZero(input.SourceID) {
		fsc.SetSourceID(input.SourceID)
	}
	if !isZero(input.FormName) {
		fsc.SetFormName(input.FormName)
	}
	if !isZero(input.State) {
		fsc.SetState(input.State)
	}
	if input.ProcessedAt != nil && !isZero(*input.ProcessedAt) {
		fsc.SetProcessedAt(*input.ProcessedAt)
	}
	if !isZero(input.History) {
		fsc.SetHistory(input.History)
	}
	if input.PersonID != nil && !isZero(*input.PersonID) {
		fsc.SetPersonID(*input.PersonID)
	}
	return fsc
}

func (fsuo *FormSubmissionUpdateOne) SetFormSubmission(input *FormSubmission, forceUpdates ...string) *FormSubmissionUpdateOne {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Status) {
		fsuo.SetStatus(input.Status)
	} else if forceUpdateMap["status"] {
		fsuo.SetStatus(input.Status)
	}
	if !isZero(input.FullName) {
		fsuo.SetFullName(input.FullName)
	} else if forceUpdateMap["full_name"] {
		fsuo.SetFullName(input.FullName)
	}
	if !isZero(input.Phone) {
		fsuo.SetPhone(input.Phone)
	} else if forceUpdateMap["phone"] {
		fsuo.SetPhone(input.Phone)
	}
	if !isZero(input.Email) {
		fsuo.SetEmail(input.Email)
	} else if forceUpdateMap["email"] {
		fsuo.ClearEmail()
	}
	if !isZero(input.Data) {
		fsuo.SetData(input.Data)
	} else if forceUpdateMap["data"] {
		fsuo.SetData(input.Data)
	}
	if !isZero(input.SourceURL) {
		fsuo.SetSourceURL(input.SourceURL)
	} else if forceUpdateMap["source_url"] {
		fsuo.SetSourceURL(input.SourceURL)
	}
	if !isZero(input.ReferrerURL) {
		fsuo.SetReferrerURL(input.ReferrerURL)
	} else if forceUpdateMap["referrer_url"] {
		fsuo.ClearReferrerURL()
	}
	if !isZero(input.SourceID) {
		fsuo.SetSourceID(input.SourceID)
	} else if forceUpdateMap["source_id"] {
		fsuo.SetSourceID(input.SourceID)
	}
	if !isZero(input.FormName) {
		fsuo.SetFormName(input.FormName)
	} else if forceUpdateMap["form_name"] {
		fsuo.SetFormName(input.FormName)
	}
	if !isZero(input.State) {
		fsuo.SetState(input.State)
	} else if forceUpdateMap["state"] {
		fsuo.SetState(input.State)
	}
	if input.ProcessedAt != nil && !isZero(*input.ProcessedAt) {
		fsuo.SetProcessedAt(*input.ProcessedAt)
	} else if forceUpdateMap["processed_at"] {
		fsuo.ClearProcessedAt()
	}
	if !isZero(input.History) {
		fsuo.SetHistory(input.History)
	} else if forceUpdateMap["history"] {
		fsuo.ClearHistory()
	}
	if input.PersonID != nil && !isZero(*input.PersonID) {
		fsuo.SetPersonID(*input.PersonID)
	} else if forceUpdateMap["person_id"] {
		fsuo.ClearPersonID()
	}
	return fsuo
}

func (fsu *FormSubmissionUpdate) SetFormSubmission(input *FormSubmission, forceUpdates ...string) *FormSubmissionUpdate {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Status) {
		fsu.SetStatus(input.Status)
	} else if forceUpdateMap["status"] {
		fsu.SetStatus(input.Status)
	}
	if !isZero(input.FullName) {
		fsu.SetFullName(input.FullName)
	} else if forceUpdateMap["full_name"] {
		fsu.SetFullName(input.FullName)
	}
	if !isZero(input.Phone) {
		fsu.SetPhone(input.Phone)
	} else if forceUpdateMap["phone"] {
		fsu.SetPhone(input.Phone)
	}
	if !isZero(input.Email) {
		fsu.SetEmail(input.Email)
	} else if forceUpdateMap["email"] {
		fsu.ClearEmail()
	}
	if !isZero(input.Data) {
		fsu.SetData(input.Data)
	} else if forceUpdateMap["data"] {
		fsu.SetData(input.Data)
	}
	if !isZero(input.SourceURL) {
		fsu.SetSourceURL(input.SourceURL)
	} else if forceUpdateMap["source_url"] {
		fsu.SetSourceURL(input.SourceURL)
	}
	if !isZero(input.ReferrerURL) {
		fsu.SetReferrerURL(input.ReferrerURL)
	} else if forceUpdateMap["referrer_url"] {
		fsu.ClearReferrerURL()
	}
	if !isZero(input.SourceID) {
		fsu.SetSourceID(input.SourceID)
	} else if forceUpdateMap["source_id"] {
		fsu.SetSourceID(input.SourceID)
	}
	if !isZero(input.FormName) {
		fsu.SetFormName(input.FormName)
	} else if forceUpdateMap["form_name"] {
		fsu.SetFormName(input.FormName)
	}
	if !isZero(input.State) {
		fsu.SetState(input.State)
	} else if forceUpdateMap["state"] {
		fsu.SetState(input.State)
	}
	if input.ProcessedAt != nil && !isZero(*input.ProcessedAt) {
		fsu.SetProcessedAt(*input.ProcessedAt)
	} else if forceUpdateMap["processed_at"] {
		fsu.ClearProcessedAt()
	}
	if !isZero(input.History) {
		fsu.SetHistory(input.History)
	} else if forceUpdateMap["history"] {
		fsu.ClearHistory()
	}
	if input.PersonID != nil && !isZero(*input.PersonID) {
		fsu.SetPersonID(*input.PersonID)
	} else if forceUpdateMap["person_id"] {
		fsu.ClearPersonID()
	}
	return fsu
}

func (ic *InstallmentCreate) SetInstallment(input *Installment) *InstallmentCreate {
	if !isZero(input.Status) {
		ic.SetStatus(input.Status)
	}
	if !isZero(input.PlanID) {
		ic.SetPlanID(input.PlanID)
	}
	if !isZero(input.InstallmentNumber) {
		ic.SetInstallmentNumber(input.InstallmentNumber)
	}
	if !isZero(input.Amount) {
		ic.SetAmount(input.Amount)
	}
	if !isZero(input.Note) {
		ic.SetNote(input.Note)
	}
	if !isZero(input.Name) {
		ic.SetName(input.Name)
	}
	if input.PersonID != nil && !isZero(*input.PersonID) {
		ic.SetPersonID(*input.PersonID)
	}
	if !isZero(input.UserID) {
		ic.SetUserID(input.UserID)
	}
	if !isZero(input.TransactionType) {
		ic.SetTransactionType(input.TransactionType)
	}
	if !isZero(input.Kind) {
		ic.SetKind(input.Kind)
	}
	if input.PaidAt != nil && !isZero(*input.PaidAt) {
		ic.SetPaidAt(*input.PaidAt)
	}
	return ic
}

func (iuo *InstallmentUpdateOne) SetInstallment(input *Installment, forceUpdates ...string) *InstallmentUpdateOne {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Status) {
		iuo.SetStatus(input.Status)
	} else if forceUpdateMap["status"] {
		iuo.SetStatus(input.Status)
	}
	if !isZero(input.PlanID) {
		iuo.SetPlanID(input.PlanID)
	} else if forceUpdateMap["plan_id"] {
		iuo.ClearPlanID()
	}
	if !isZero(input.InstallmentNumber) {
		iuo.SetInstallmentNumber(input.InstallmentNumber)
	} else if forceUpdateMap["installment_number"] {
		iuo.SetInstallmentNumber(input.InstallmentNumber)
	}
	if !isZero(input.Amount) {
		iuo.SetAmount(input.Amount)
	} else if forceUpdateMap["amount"] {
		iuo.SetAmount(input.Amount)
	}
	if !isZero(input.Note) {
		iuo.SetNote(input.Note)
	} else if forceUpdateMap["note"] {
		iuo.ClearNote()
	}
	if !isZero(input.Name) {
		iuo.SetName(input.Name)
	} else if forceUpdateMap["name"] {
		iuo.ClearName()
	}
	if input.PersonID != nil && !isZero(*input.PersonID) {
		iuo.SetPersonID(*input.PersonID)
	} else if forceUpdateMap["person_id"] {
		iuo.ClearPersonID()
	}
	if !isZero(input.UserID) {
		iuo.SetUserID(input.UserID)
	} else if forceUpdateMap["user_id"] {
		iuo.ClearUserID()
	}
	if !isZero(input.TransactionType) {
		iuo.SetTransactionType(input.TransactionType)
	} else if forceUpdateMap["transaction_type"] {
		iuo.SetTransactionType(input.TransactionType)
	}
	if !isZero(input.Kind) {
		iuo.SetKind(input.Kind)
	} else if forceUpdateMap["kind"] {
		iuo.ClearKind()
	}
	if input.PaidAt != nil && !isZero(*input.PaidAt) {
		iuo.SetPaidAt(*input.PaidAt)
	} else if forceUpdateMap["paid_at"] {
		iuo.ClearPaidAt()
	}
	return iuo
}

func (iu *InstallmentUpdate) SetInstallment(input *Installment, forceUpdates ...string) *InstallmentUpdate {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Status) {
		iu.SetStatus(input.Status)
	} else if forceUpdateMap["status"] {
		iu.SetStatus(input.Status)
	}
	if !isZero(input.PlanID) {
		iu.SetPlanID(input.PlanID)
	} else if forceUpdateMap["plan_id"] {
		iu.ClearPlanID()
	}
	if !isZero(input.InstallmentNumber) {
		iu.SetInstallmentNumber(input.InstallmentNumber)
	} else if forceUpdateMap["installment_number"] {
		iu.SetInstallmentNumber(input.InstallmentNumber)
	}
	if !isZero(input.Amount) {
		iu.SetAmount(input.Amount)
	} else if forceUpdateMap["amount"] {
		iu.SetAmount(input.Amount)
	}
	if !isZero(input.Note) {
		iu.SetNote(input.Note)
	} else if forceUpdateMap["note"] {
		iu.ClearNote()
	}
	if !isZero(input.Name) {
		iu.SetName(input.Name)
	} else if forceUpdateMap["name"] {
		iu.ClearName()
	}
	if input.PersonID != nil && !isZero(*input.PersonID) {
		iu.SetPersonID(*input.PersonID)
	} else if forceUpdateMap["person_id"] {
		iu.ClearPersonID()
	}
	if !isZero(input.UserID) {
		iu.SetUserID(input.UserID)
	} else if forceUpdateMap["user_id"] {
		iu.ClearUserID()
	}
	if !isZero(input.TransactionType) {
		iu.SetTransactionType(input.TransactionType)
	} else if forceUpdateMap["transaction_type"] {
		iu.SetTransactionType(input.TransactionType)
	}
	if !isZero(input.Kind) {
		iu.SetKind(input.Kind)
	} else if forceUpdateMap["kind"] {
		iu.ClearKind()
	}
	if input.PaidAt != nil && !isZero(*input.PaidAt) {
		iu.SetPaidAt(*input.PaidAt)
	} else if forceUpdateMap["paid_at"] {
		iu.ClearPaidAt()
	}
	return iu
}

func (ipc *InstallmentPlanCreate) SetInstallmentPlan(input *InstallmentPlan) *InstallmentPlanCreate {
	if !isZero(input.Status) {
		ipc.SetStatus(input.Status)
	}
	if !isZero(input.PersonID) {
		ipc.SetPersonID(input.PersonID)
	}
	if input.DealID != nil && !isZero(*input.DealID) {
		ipc.SetDealID(*input.DealID)
	}
	if input.BillID != nil && !isZero(*input.BillID) {
		ipc.SetBillID(*input.BillID)
	}
	if !isZero(input.UserID) {
		ipc.SetUserID(input.UserID)
	}
	if !isZero(input.Name) {
		ipc.SetName(input.Name)
	}
	if !isZero(input.TotalAmount) {
		ipc.SetTotalAmount(input.TotalAmount)
	}
	if !isZero(input.DownPayment) {
		ipc.SetDownPayment(input.DownPayment)
	}
	if !isZero(input.TotalInstallments) {
		ipc.SetTotalInstallments(input.TotalInstallments)
	}
	if !isZero(input.State) {
		ipc.SetState(input.State)
	}
	return ipc
}

func (ipuo *InstallmentPlanUpdateOne) SetInstallmentPlan(input *InstallmentPlan, forceUpdates ...string) *InstallmentPlanUpdateOne {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Status) {
		ipuo.SetStatus(input.Status)
	} else if forceUpdateMap["status"] {
		ipuo.SetStatus(input.Status)
	}
	if !isZero(input.PersonID) {
		ipuo.SetPersonID(input.PersonID)
	} else if forceUpdateMap["person_id"] {
		ipuo.ClearPersonID()
	}
	if input.DealID != nil && !isZero(*input.DealID) {
		ipuo.SetDealID(*input.DealID)
	} else if forceUpdateMap["deal_id"] {
		ipuo.ClearDealID()
	}
	if input.BillID != nil && !isZero(*input.BillID) {
		ipuo.SetBillID(*input.BillID)
	} else if forceUpdateMap["bill_id"] {
		ipuo.ClearBillID()
	}
	if !isZero(input.UserID) {
		ipuo.SetUserID(input.UserID)
	} else if forceUpdateMap["user_id"] {
		ipuo.ClearUserID()
	}
	if !isZero(input.Name) {
		ipuo.SetName(input.Name)
	} else if forceUpdateMap["name"] {
		ipuo.ClearName()
	}
	if !isZero(input.TotalAmount) {
		ipuo.SetTotalAmount(input.TotalAmount)
	} else if forceUpdateMap["total_amount"] {
		ipuo.SetTotalAmount(input.TotalAmount)
	}
	if !isZero(input.DownPayment) {
		ipuo.SetDownPayment(input.DownPayment)
	} else if forceUpdateMap["down_payment"] {
		ipuo.SetDownPayment(input.DownPayment)
	}
	if !isZero(input.TotalInstallments) {
		ipuo.SetTotalInstallments(input.TotalInstallments)
	} else if forceUpdateMap["total_installments"] {
		ipuo.SetTotalInstallments(input.TotalInstallments)
	}
	if !isZero(input.State) {
		ipuo.SetState(input.State)
	} else if forceUpdateMap["state"] {
		ipuo.SetState(input.State)
	}
	return ipuo
}

func (ipu *InstallmentPlanUpdate) SetInstallmentPlan(input *InstallmentPlan, forceUpdates ...string) *InstallmentPlanUpdate {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Status) {
		ipu.SetStatus(input.Status)
	} else if forceUpdateMap["status"] {
		ipu.SetStatus(input.Status)
	}
	if !isZero(input.PersonID) {
		ipu.SetPersonID(input.PersonID)
	} else if forceUpdateMap["person_id"] {
		ipu.ClearPersonID()
	}
	if input.DealID != nil && !isZero(*input.DealID) {
		ipu.SetDealID(*input.DealID)
	} else if forceUpdateMap["deal_id"] {
		ipu.ClearDealID()
	}
	if input.BillID != nil && !isZero(*input.BillID) {
		ipu.SetBillID(*input.BillID)
	} else if forceUpdateMap["bill_id"] {
		ipu.ClearBillID()
	}
	if !isZero(input.UserID) {
		ipu.SetUserID(input.UserID)
	} else if forceUpdateMap["user_id"] {
		ipu.ClearUserID()
	}
	if !isZero(input.Name) {
		ipu.SetName(input.Name)
	} else if forceUpdateMap["name"] {
		ipu.ClearName()
	}
	if !isZero(input.TotalAmount) {
		ipu.SetTotalAmount(input.TotalAmount)
	} else if forceUpdateMap["total_amount"] {
		ipu.SetTotalAmount(input.TotalAmount)
	}
	if !isZero(input.DownPayment) {
		ipu.SetDownPayment(input.DownPayment)
	} else if forceUpdateMap["down_payment"] {
		ipu.SetDownPayment(input.DownPayment)
	}
	if !isZero(input.TotalInstallments) {
		ipu.SetTotalInstallments(input.TotalInstallments)
	} else if forceUpdateMap["total_installments"] {
		ipu.SetTotalInstallments(input.TotalInstallments)
	}
	if !isZero(input.State) {
		ipu.SetState(input.State)
	} else if forceUpdateMap["state"] {
		ipu.SetState(input.State)
	}
	return ipu
}

func (ic *IssueCreate) SetIssue(input *Issue) *IssueCreate {
	if !isZero(input.Status) {
		ic.SetStatus(input.Status)
	}
	if !isZero(input.Title) {
		ic.SetTitle(input.Title)
	}
	if !isZero(input.Description) {
		ic.SetDescription(input.Description)
	}
	if !isZero(input.Type) {
		ic.SetType(input.Type)
	}
	if !isZero(input.Progress) {
		ic.SetProgress(input.Progress)
	}
	if !isZero(input.Priority) {
		ic.SetPriority(input.Priority)
	}
	if !isZero(input.PersonID) {
		ic.SetPersonID(input.PersonID)
	}
	return ic
}

func (iuo *IssueUpdateOne) SetIssue(input *Issue, forceUpdates ...string) *IssueUpdateOne {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Status) {
		iuo.SetStatus(input.Status)
	} else if forceUpdateMap["status"] {
		iuo.SetStatus(input.Status)
	}
	if !isZero(input.Title) {
		iuo.SetTitle(input.Title)
	} else if forceUpdateMap["title"] {
		iuo.SetTitle(input.Title)
	}
	if !isZero(input.Description) {
		iuo.SetDescription(input.Description)
	} else if forceUpdateMap["description"] {
		iuo.ClearDescription()
	}
	if !isZero(input.Type) {
		iuo.SetType(input.Type)
	} else if forceUpdateMap["type"] {
		iuo.SetType(input.Type)
	}
	if !isZero(input.Progress) {
		iuo.SetProgress(input.Progress)
	} else if forceUpdateMap["progress"] {
		iuo.SetProgress(input.Progress)
	}
	if !isZero(input.Priority) {
		iuo.SetPriority(input.Priority)
	} else if forceUpdateMap["priority"] {
		iuo.SetPriority(input.Priority)
	}
	if !isZero(input.PersonID) {
		iuo.SetPersonID(input.PersonID)
	} else if forceUpdateMap["person_id"] {
		iuo.ClearPersonID()
	}
	return iuo
}

func (iu *IssueUpdate) SetIssue(input *Issue, forceUpdates ...string) *IssueUpdate {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Status) {
		iu.SetStatus(input.Status)
	} else if forceUpdateMap["status"] {
		iu.SetStatus(input.Status)
	}
	if !isZero(input.Title) {
		iu.SetTitle(input.Title)
	} else if forceUpdateMap["title"] {
		iu.SetTitle(input.Title)
	}
	if !isZero(input.Description) {
		iu.SetDescription(input.Description)
	} else if forceUpdateMap["description"] {
		iu.ClearDescription()
	}
	if !isZero(input.Type) {
		iu.SetType(input.Type)
	} else if forceUpdateMap["type"] {
		iu.SetType(input.Type)
	}
	if !isZero(input.Progress) {
		iu.SetProgress(input.Progress)
	} else if forceUpdateMap["progress"] {
		iu.SetProgress(input.Progress)
	}
	if !isZero(input.Priority) {
		iu.SetPriority(input.Priority)
	} else if forceUpdateMap["priority"] {
		iu.SetPriority(input.Priority)
	}
	if !isZero(input.PersonID) {
		iu.SetPersonID(input.PersonID)
	} else if forceUpdateMap["person_id"] {
		iu.ClearPersonID()
	}
	return iu
}

func (icc *IssueCommentCreate) SetIssueComment(input *IssueComment) *IssueCommentCreate {
	if !isZero(input.Status) {
		icc.SetStatus(input.Status)
	}
	if !isZero(input.IssueID) {
		icc.SetIssueID(input.IssueID)
	}
	if !isZero(input.UserID) {
		icc.SetUserID(input.UserID)
	}
	if !isZero(input.Comment) {
		icc.SetComment(input.Comment)
	}
	return icc
}

func (icuo *IssueCommentUpdateOne) SetIssueComment(input *IssueComment, forceUpdates ...string) *IssueCommentUpdateOne {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Status) {
		icuo.SetStatus(input.Status)
	} else if forceUpdateMap["status"] {
		icuo.SetStatus(input.Status)
	}
	if !isZero(input.IssueID) {
		icuo.SetIssueID(input.IssueID)
	} else if forceUpdateMap["issue_id"] {
		icuo.ClearIssueID()
	}
	if !isZero(input.UserID) {
		icuo.SetUserID(input.UserID)
	} else if forceUpdateMap["user_id"] {
		icuo.ClearUserID()
	}
	if !isZero(input.Comment) {
		icuo.SetComment(input.Comment)
	} else if forceUpdateMap["comment"] {
		icuo.SetComment(input.Comment)
	}
	return icuo
}

func (icu *IssueCommentUpdate) SetIssueComment(input *IssueComment, forceUpdates ...string) *IssueCommentUpdate {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Status) {
		icu.SetStatus(input.Status)
	} else if forceUpdateMap["status"] {
		icu.SetStatus(input.Status)
	}
	if !isZero(input.IssueID) {
		icu.SetIssueID(input.IssueID)
	} else if forceUpdateMap["issue_id"] {
		icu.ClearIssueID()
	}
	if !isZero(input.UserID) {
		icu.SetUserID(input.UserID)
	} else if forceUpdateMap["user_id"] {
		icu.ClearUserID()
	}
	if !isZero(input.Comment) {
		icu.SetComment(input.Comment)
	} else if forceUpdateMap["comment"] {
		icu.SetComment(input.Comment)
	}
	return icu
}

func (ldc *LocalDistrictCreate) SetLocalDistrict(input *LocalDistrict) *LocalDistrictCreate {
	if !isZero(input.Name) {
		ldc.SetName(input.Name)
	}
	if !isZero(input.Prefix) {
		ldc.SetPrefix(input.Prefix)
	}
	if !isZero(input.ProvinceID) {
		ldc.SetProvinceID(input.ProvinceID)
	}
	return ldc
}

func (lduo *LocalDistrictUpdateOne) SetLocalDistrict(input *LocalDistrict, forceUpdates ...string) *LocalDistrictUpdateOne {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Name) {
		lduo.SetName(input.Name)
	} else if forceUpdateMap["name"] {
		lduo.ClearName()
	}
	if !isZero(input.Prefix) {
		lduo.SetPrefix(input.Prefix)
	} else if forceUpdateMap["prefix"] {
		lduo.ClearPrefix()
	}
	if !isZero(input.ProvinceID) {
		lduo.SetProvinceID(input.ProvinceID)
	} else if forceUpdateMap["province_id"] {
		lduo.ClearProvinceID()
	}
	return lduo
}

func (ldu *LocalDistrictUpdate) SetLocalDistrict(input *LocalDistrict, forceUpdates ...string) *LocalDistrictUpdate {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Name) {
		ldu.SetName(input.Name)
	} else if forceUpdateMap["name"] {
		ldu.ClearName()
	}
	if !isZero(input.Prefix) {
		ldu.SetPrefix(input.Prefix)
	} else if forceUpdateMap["prefix"] {
		ldu.ClearPrefix()
	}
	if !isZero(input.ProvinceID) {
		ldu.SetProvinceID(input.ProvinceID)
	} else if forceUpdateMap["province_id"] {
		ldu.ClearProvinceID()
	}
	return ldu
}

func (lpc *LocalProvinceCreate) SetLocalProvince(input *LocalProvince) *LocalProvinceCreate {
	if !isZero(input.Name) {
		lpc.SetName(input.Name)
	}
	if !isZero(input.Code) {
		lpc.SetCode(input.Code)
	}
	return lpc
}

func (lpuo *LocalProvinceUpdateOne) SetLocalProvince(input *LocalProvince, forceUpdates ...string) *LocalProvinceUpdateOne {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Name) {
		lpuo.SetName(input.Name)
	} else if forceUpdateMap["name"] {
		lpuo.ClearName()
	}
	if !isZero(input.Code) {
		lpuo.SetCode(input.Code)
	} else if forceUpdateMap["code"] {
		lpuo.ClearCode()
	}
	return lpuo
}

func (lpu *LocalProvinceUpdate) SetLocalProvince(input *LocalProvince, forceUpdates ...string) *LocalProvinceUpdate {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Name) {
		lpu.SetName(input.Name)
	} else if forceUpdateMap["name"] {
		lpu.ClearName()
	}
	if !isZero(input.Code) {
		lpu.SetCode(input.Code)
	} else if forceUpdateMap["code"] {
		lpu.ClearCode()
	}
	return lpu
}

func (lwc *LocalWardCreate) SetLocalWard(input *LocalWard) *LocalWardCreate {
	if !isZero(input.Name) {
		lwc.SetName(input.Name)
	}
	if !isZero(input.Prefix) {
		lwc.SetPrefix(input.Prefix)
	}
	if !isZero(input.ProvinceID) {
		lwc.SetProvinceID(input.ProvinceID)
	}
	if !isZero(input.DistrictID) {
		lwc.SetDistrictID(input.DistrictID)
	}
	return lwc
}

func (lwuo *LocalWardUpdateOne) SetLocalWard(input *LocalWard, forceUpdates ...string) *LocalWardUpdateOne {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Name) {
		lwuo.SetName(input.Name)
	} else if forceUpdateMap["name"] {
		lwuo.SetName(input.Name)
	}
	if !isZero(input.Prefix) {
		lwuo.SetPrefix(input.Prefix)
	} else if forceUpdateMap["prefix"] {
		lwuo.ClearPrefix()
	}
	if !isZero(input.ProvinceID) {
		lwuo.SetProvinceID(input.ProvinceID)
	} else if forceUpdateMap["province_id"] {
		lwuo.ClearProvinceID()
	}
	if !isZero(input.DistrictID) {
		lwuo.SetDistrictID(input.DistrictID)
	} else if forceUpdateMap["district_id"] {
		lwuo.ClearDistrictID()
	}
	return lwuo
}

func (lwu *LocalWardUpdate) SetLocalWard(input *LocalWard, forceUpdates ...string) *LocalWardUpdate {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Name) {
		lwu.SetName(input.Name)
	} else if forceUpdateMap["name"] {
		lwu.SetName(input.Name)
	}
	if !isZero(input.Prefix) {
		lwu.SetPrefix(input.Prefix)
	} else if forceUpdateMap["prefix"] {
		lwu.ClearPrefix()
	}
	if !isZero(input.ProvinceID) {
		lwu.SetProvinceID(input.ProvinceID)
	} else if forceUpdateMap["province_id"] {
		lwu.ClearProvinceID()
	}
	if !isZero(input.DistrictID) {
		lwu.SetDistrictID(input.DistrictID)
	} else if forceUpdateMap["district_id"] {
		lwu.ClearDistrictID()
	}
	return lwu
}

func (mc *MaterialCreate) SetMaterial(input *Material) *MaterialCreate {
	if !isZero(input.Status) {
		mc.SetStatus(input.Status)
	}
	if !isZero(input.Code) {
		mc.SetCode(input.Code)
	}
	if !isZero(input.Name) {
		mc.SetName(input.Name)
	}
	if !isZero(input.Unit) {
		mc.SetUnit(input.Unit)
	}
	if !isZero(input.PackagingSpecification) {
		mc.SetPackagingSpecification(input.PackagingSpecification)
	}
	if !isZero(input.Description) {
		mc.SetDescription(input.Description)
	}
	if !isZero(input.CostPrice) {
		mc.SetCostPrice(input.CostPrice)
	}
	if !isZero(input.Kind) {
		mc.SetKind(input.Kind)
	}
	return mc
}

func (muo *MaterialUpdateOne) SetMaterial(input *Material, forceUpdates ...string) *MaterialUpdateOne {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Status) {
		muo.SetStatus(input.Status)
	} else if forceUpdateMap["status"] {
		muo.SetStatus(input.Status)
	}
	if !isZero(input.Code) {
		muo.SetCode(input.Code)
	} else if forceUpdateMap["code"] {
		muo.ClearCode()
	}
	if !isZero(input.Name) {
		muo.SetName(input.Name)
	} else if forceUpdateMap["name"] {
		muo.SetName(input.Name)
	}
	if !isZero(input.Unit) {
		muo.SetUnit(input.Unit)
	} else if forceUpdateMap["unit"] {
		muo.SetUnit(input.Unit)
	}
	if !isZero(input.PackagingSpecification) {
		muo.SetPackagingSpecification(input.PackagingSpecification)
	} else if forceUpdateMap["packaging_specification"] {
		muo.ClearPackagingSpecification()
	}
	if !isZero(input.Description) {
		muo.SetDescription(input.Description)
	} else if forceUpdateMap["description"] {
		muo.ClearDescription()
	}
	if !isZero(input.CostPrice) {
		muo.SetCostPrice(input.CostPrice)
	} else if forceUpdateMap["cost_price"] {
		muo.ClearCostPrice()
	}
	if !isZero(input.Kind) {
		muo.SetKind(input.Kind)
	} else if forceUpdateMap["kind"] {
		muo.SetKind(input.Kind)
	}
	return muo
}

func (mu *MaterialUpdate) SetMaterial(input *Material, forceUpdates ...string) *MaterialUpdate {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Status) {
		mu.SetStatus(input.Status)
	} else if forceUpdateMap["status"] {
		mu.SetStatus(input.Status)
	}
	if !isZero(input.Code) {
		mu.SetCode(input.Code)
	} else if forceUpdateMap["code"] {
		mu.ClearCode()
	}
	if !isZero(input.Name) {
		mu.SetName(input.Name)
	} else if forceUpdateMap["name"] {
		mu.SetName(input.Name)
	}
	if !isZero(input.Unit) {
		mu.SetUnit(input.Unit)
	} else if forceUpdateMap["unit"] {
		mu.SetUnit(input.Unit)
	}
	if !isZero(input.PackagingSpecification) {
		mu.SetPackagingSpecification(input.PackagingSpecification)
	} else if forceUpdateMap["packaging_specification"] {
		mu.ClearPackagingSpecification()
	}
	if !isZero(input.Description) {
		mu.SetDescription(input.Description)
	} else if forceUpdateMap["description"] {
		mu.ClearDescription()
	}
	if !isZero(input.CostPrice) {
		mu.SetCostPrice(input.CostPrice)
	} else if forceUpdateMap["cost_price"] {
		mu.ClearCostPrice()
	}
	if !isZero(input.Kind) {
		mu.SetKind(input.Kind)
	} else if forceUpdateMap["kind"] {
		mu.SetKind(input.Kind)
	}
	return mu
}

func (muc *MaterialUsageCreate) SetMaterialUsage(input *MaterialUsage) *MaterialUsageCreate {
	if !isZero(input.Status) {
		muc.SetStatus(input.Status)
	}
	if !isZero(input.AttachmentID) {
		muc.SetAttachmentID(input.AttachmentID)
	}
	if !isZero(input.MaterialID) {
		muc.SetMaterialID(input.MaterialID)
	}
	if input.OperationID != nil && !isZero(*input.OperationID) {
		muc.SetOperationID(*input.OperationID)
	}
	if !isZero(input.OperationKey) {
		muc.SetOperationKey(input.OperationKey)
	}
	if !isZero(input.Kind) {
		muc.SetKind(input.Kind)
	}
	if !isZero(input.QuotedQuantity) {
		muc.SetQuotedQuantity(input.QuotedQuantity)
	}
	if !isZero(input.UsedQuantity) {
		muc.SetUsedQuantity(input.UsedQuantity)
	}
	if input.UserID != nil && !isZero(*input.UserID) {
		muc.SetUserID(*input.UserID)
	}
	if !isZero(input.Note) {
		muc.SetNote(input.Note)
	}
	return muc
}

func (muuo *MaterialUsageUpdateOne) SetMaterialUsage(input *MaterialUsage, forceUpdates ...string) *MaterialUsageUpdateOne {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Status) {
		muuo.SetStatus(input.Status)
	} else if forceUpdateMap["status"] {
		muuo.SetStatus(input.Status)
	}
	if !isZero(input.AttachmentID) {
		muuo.SetAttachmentID(input.AttachmentID)
	} else if forceUpdateMap["attachment_id"] {
		muuo.SetAttachmentID(input.AttachmentID)
	}
	if !isZero(input.MaterialID) {
		muuo.SetMaterialID(input.MaterialID)
	} else if forceUpdateMap["material_id"] {
		muuo.SetMaterialID(input.MaterialID)
	}
	if input.OperationID != nil && !isZero(*input.OperationID) {
		muuo.SetOperationID(*input.OperationID)
	} else if forceUpdateMap["operation_id"] {
		muuo.ClearOperationID()
	}
	if !isZero(input.OperationKey) {
		muuo.SetOperationKey(input.OperationKey)
	} else if forceUpdateMap["operation_key"] {
		muuo.ClearOperationKey()
	}
	if !isZero(input.Kind) {
		muuo.SetKind(input.Kind)
	} else if forceUpdateMap["kind"] {
		muuo.SetKind(input.Kind)
	}
	if !isZero(input.QuotedQuantity) {
		muuo.SetQuotedQuantity(input.QuotedQuantity)
	} else if forceUpdateMap["quoted_quantity"] {
		muuo.SetQuotedQuantity(input.QuotedQuantity)
	}
	if !isZero(input.UsedQuantity) {
		muuo.SetUsedQuantity(input.UsedQuantity)
	} else if forceUpdateMap["used_quantity"] {
		muuo.SetUsedQuantity(input.UsedQuantity)
	}
	if input.UserID != nil && !isZero(*input.UserID) {
		muuo.SetUserID(*input.UserID)
	} else if forceUpdateMap["user_id"] {
		muuo.ClearUserID()
	}
	if !isZero(input.Note) {
		muuo.SetNote(input.Note)
	} else if forceUpdateMap["note"] {
		muuo.ClearNote()
	}
	return muuo
}

func (muu *MaterialUsageUpdate) SetMaterialUsage(input *MaterialUsage, forceUpdates ...string) *MaterialUsageUpdate {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Status) {
		muu.SetStatus(input.Status)
	} else if forceUpdateMap["status"] {
		muu.SetStatus(input.Status)
	}
	if !isZero(input.AttachmentID) {
		muu.SetAttachmentID(input.AttachmentID)
	} else if forceUpdateMap["attachment_id"] {
		muu.SetAttachmentID(input.AttachmentID)
	}
	if !isZero(input.MaterialID) {
		muu.SetMaterialID(input.MaterialID)
	} else if forceUpdateMap["material_id"] {
		muu.SetMaterialID(input.MaterialID)
	}
	if input.OperationID != nil && !isZero(*input.OperationID) {
		muu.SetOperationID(*input.OperationID)
	} else if forceUpdateMap["operation_id"] {
		muu.ClearOperationID()
	}
	if !isZero(input.OperationKey) {
		muu.SetOperationKey(input.OperationKey)
	} else if forceUpdateMap["operation_key"] {
		muu.ClearOperationKey()
	}
	if !isZero(input.Kind) {
		muu.SetKind(input.Kind)
	} else if forceUpdateMap["kind"] {
		muu.SetKind(input.Kind)
	}
	if !isZero(input.QuotedQuantity) {
		muu.SetQuotedQuantity(input.QuotedQuantity)
	} else if forceUpdateMap["quoted_quantity"] {
		muu.SetQuotedQuantity(input.QuotedQuantity)
	}
	if !isZero(input.UsedQuantity) {
		muu.SetUsedQuantity(input.UsedQuantity)
	} else if forceUpdateMap["used_quantity"] {
		muu.SetUsedQuantity(input.UsedQuantity)
	}
	if input.UserID != nil && !isZero(*input.UserID) {
		muu.SetUserID(*input.UserID)
	} else if forceUpdateMap["user_id"] {
		muu.ClearUserID()
	}
	if !isZero(input.Note) {
		muu.SetNote(input.Note)
	} else if forceUpdateMap["note"] {
		muu.ClearNote()
	}
	return muu
}

func (mhc *MessageHistoryCreate) SetMessageHistory(input *MessageHistory) *MessageHistoryCreate {
	if !isZero(input.Status) {
		mhc.SetStatus(input.Status)
	}
	if !isZero(input.PersonID) {
		mhc.SetPersonID(input.PersonID)
	}
	if !isZero(input.UserID) {
		mhc.SetUserID(input.UserID)
	}
	if !isZero(input.MessageID) {
		mhc.SetMessageID(input.MessageID)
	}
	if !isZero(input.Phone) {
		mhc.SetPhone(input.Phone)
	}
	if !isZero(input.Type) {
		mhc.SetType(input.Type)
	}
	if !isZero(input.Content) {
		mhc.SetContent(input.Content)
	}
	if !isZero(input.ZnsData) {
		mhc.SetZnsData(input.ZnsData)
	}
	if !isZero(input.ErrorCode) {
		mhc.SetErrorCode(input.ErrorCode)
	}
	if !isZero(input.MessageStatus) {
		mhc.SetMessageStatus(input.MessageStatus)
	}
	if input.DeliveredAt != nil && !isZero(*input.DeliveredAt) {
		mhc.SetDeliveredAt(*input.DeliveredAt)
	}
	return mhc
}

func (mhuo *MessageHistoryUpdateOne) SetMessageHistory(input *MessageHistory, forceUpdates ...string) *MessageHistoryUpdateOne {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Status) {
		mhuo.SetStatus(input.Status)
	} else if forceUpdateMap["status"] {
		mhuo.SetStatus(input.Status)
	}
	if !isZero(input.PersonID) {
		mhuo.SetPersonID(input.PersonID)
	} else if forceUpdateMap["person_id"] {
		mhuo.ClearPersonID()
	}
	if !isZero(input.UserID) {
		mhuo.SetUserID(input.UserID)
	} else if forceUpdateMap["user_id"] {
		mhuo.ClearUserID()
	}
	if !isZero(input.MessageID) {
		mhuo.SetMessageID(input.MessageID)
	} else if forceUpdateMap["message_id"] {
		mhuo.ClearMessageID()
	}
	if !isZero(input.Phone) {
		mhuo.SetPhone(input.Phone)
	} else if forceUpdateMap["phone"] {
		mhuo.ClearPhone()
	}
	if !isZero(input.Type) {
		mhuo.SetType(input.Type)
	} else if forceUpdateMap["type"] {
		mhuo.ClearType()
	}
	if !isZero(input.Content) {
		mhuo.SetContent(input.Content)
	} else if forceUpdateMap["content"] {
		mhuo.ClearContent()
	}
	if !isZero(input.ZnsData) {
		mhuo.SetZnsData(input.ZnsData)
	} else if forceUpdateMap["zns_data"] {
		mhuo.ClearZnsData()
	}
	if !isZero(input.ErrorCode) {
		mhuo.SetErrorCode(input.ErrorCode)
	} else if forceUpdateMap["error_code"] {
		mhuo.ClearErrorCode()
	}
	if !isZero(input.MessageStatus) {
		mhuo.SetMessageStatus(input.MessageStatus)
	} else if forceUpdateMap["message_status"] {
		mhuo.ClearMessageStatus()
	}
	if input.DeliveredAt != nil && !isZero(*input.DeliveredAt) {
		mhuo.SetDeliveredAt(*input.DeliveredAt)
	} else if forceUpdateMap["delivered_at"] {
		mhuo.ClearDeliveredAt()
	}
	return mhuo
}

func (mhu *MessageHistoryUpdate) SetMessageHistory(input *MessageHistory, forceUpdates ...string) *MessageHistoryUpdate {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Status) {
		mhu.SetStatus(input.Status)
	} else if forceUpdateMap["status"] {
		mhu.SetStatus(input.Status)
	}
	if !isZero(input.PersonID) {
		mhu.SetPersonID(input.PersonID)
	} else if forceUpdateMap["person_id"] {
		mhu.ClearPersonID()
	}
	if !isZero(input.UserID) {
		mhu.SetUserID(input.UserID)
	} else if forceUpdateMap["user_id"] {
		mhu.ClearUserID()
	}
	if !isZero(input.MessageID) {
		mhu.SetMessageID(input.MessageID)
	} else if forceUpdateMap["message_id"] {
		mhu.ClearMessageID()
	}
	if !isZero(input.Phone) {
		mhu.SetPhone(input.Phone)
	} else if forceUpdateMap["phone"] {
		mhu.ClearPhone()
	}
	if !isZero(input.Type) {
		mhu.SetType(input.Type)
	} else if forceUpdateMap["type"] {
		mhu.ClearType()
	}
	if !isZero(input.Content) {
		mhu.SetContent(input.Content)
	} else if forceUpdateMap["content"] {
		mhu.ClearContent()
	}
	if !isZero(input.ZnsData) {
		mhu.SetZnsData(input.ZnsData)
	} else if forceUpdateMap["zns_data"] {
		mhu.ClearZnsData()
	}
	if !isZero(input.ErrorCode) {
		mhu.SetErrorCode(input.ErrorCode)
	} else if forceUpdateMap["error_code"] {
		mhu.ClearErrorCode()
	}
	if !isZero(input.MessageStatus) {
		mhu.SetMessageStatus(input.MessageStatus)
	} else if forceUpdateMap["message_status"] {
		mhu.ClearMessageStatus()
	}
	if input.DeliveredAt != nil && !isZero(*input.DeliveredAt) {
		mhu.SetDeliveredAt(*input.DeliveredAt)
	} else if forceUpdateMap["delivered_at"] {
		mhu.ClearDeliveredAt()
	}
	return mhu
}

func (nc *NoteCreate) SetNote(input *Note) *NoteCreate {
	if !isZero(input.Status) {
		nc.SetStatus(input.Status)
	}
	if !isZero(input.Body) {
		nc.SetBody(input.Body)
	}
	if !isZero(input.Type) {
		nc.SetType(input.Type)
	}
	if !isZero(input.PersonID) {
		nc.SetPersonID(input.PersonID)
	}
	if input.DealID != nil && !isZero(*input.DealID) {
		nc.SetDealID(*input.DealID)
	}
	if !isZero(input.UserID) {
		nc.SetUserID(input.UserID)
	}
	if !isZero(input.History) {
		nc.SetHistory(input.History)
	}
	return nc
}

func (nuo *NoteUpdateOne) SetNote(input *Note, forceUpdates ...string) *NoteUpdateOne {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Status) {
		nuo.SetStatus(input.Status)
	} else if forceUpdateMap["status"] {
		nuo.SetStatus(input.Status)
	}
	if !isZero(input.Body) {
		nuo.SetBody(input.Body)
	} else if forceUpdateMap["body"] {
		nuo.ClearBody()
	}
	if !isZero(input.Type) {
		nuo.SetType(input.Type)
	} else if forceUpdateMap["type"] {
		nuo.SetType(input.Type)
	}
	if !isZero(input.PersonID) {
		nuo.SetPersonID(input.PersonID)
	} else if forceUpdateMap["person_id"] {
		nuo.ClearPersonID()
	}
	if input.DealID != nil && !isZero(*input.DealID) {
		nuo.SetDealID(*input.DealID)
	} else if forceUpdateMap["deal_id"] {
		nuo.ClearDealID()
	}
	if !isZero(input.UserID) {
		nuo.SetUserID(input.UserID)
	} else if forceUpdateMap["user_id"] {
		nuo.ClearUserID()
	}
	if !isZero(input.History) {
		nuo.SetHistory(input.History)
	} else if forceUpdateMap["history"] {
		nuo.ClearHistory()
	}
	return nuo
}

func (nu *NoteUpdate) SetNote(input *Note, forceUpdates ...string) *NoteUpdate {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Status) {
		nu.SetStatus(input.Status)
	} else if forceUpdateMap["status"] {
		nu.SetStatus(input.Status)
	}
	if !isZero(input.Body) {
		nu.SetBody(input.Body)
	} else if forceUpdateMap["body"] {
		nu.ClearBody()
	}
	if !isZero(input.Type) {
		nu.SetType(input.Type)
	} else if forceUpdateMap["type"] {
		nu.SetType(input.Type)
	}
	if !isZero(input.PersonID) {
		nu.SetPersonID(input.PersonID)
	} else if forceUpdateMap["person_id"] {
		nu.ClearPersonID()
	}
	if input.DealID != nil && !isZero(*input.DealID) {
		nu.SetDealID(*input.DealID)
	} else if forceUpdateMap["deal_id"] {
		nu.ClearDealID()
	}
	if !isZero(input.UserID) {
		nu.SetUserID(input.UserID)
	} else if forceUpdateMap["user_id"] {
		nu.ClearUserID()
	}
	if !isZero(input.History) {
		nu.SetHistory(input.History)
	} else if forceUpdateMap["history"] {
		nu.ClearHistory()
	}
	return nu
}

func (nc *NotificationCreate) SetNotification(input *Notification) *NotificationCreate {
	if !isZero(input.Status) {
		nc.SetStatus(input.Status)
	}
	if !isZero(input.UserID) {
		nc.SetUserID(input.UserID)
	}
	if !isZero(input.Type) {
		nc.SetType(input.Type)
	}
	if !isZero(input.Message) {
		nc.SetMessage(input.Message)
	}
	if !isZero(input.EntityID) {
		nc.SetEntityID(input.EntityID)
	}
	if !isZero(input.EntityType) {
		nc.SetEntityType(input.EntityType)
	}
	if !isZero(input.IsRead) {
		nc.SetIsRead(input.IsRead)
	}
	if !isZero(input.ReadAt) {
		nc.SetReadAt(input.ReadAt)
	}
	if !isZero(input.SenderID) {
		nc.SetSenderID(input.SenderID)
	}
	if !isZero(input.Metadata) {
		nc.SetMetadata(input.Metadata)
	}
	return nc
}

func (nuo *NotificationUpdateOne) SetNotification(input *Notification, forceUpdates ...string) *NotificationUpdateOne {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Status) {
		nuo.SetStatus(input.Status)
	} else if forceUpdateMap["status"] {
		nuo.SetStatus(input.Status)
	}
	if !isZero(input.UserID) {
		nuo.SetUserID(input.UserID)
	} else if forceUpdateMap["user_id"] {
		nuo.SetUserID(input.UserID)
	}
	if !isZero(input.Type) {
		nuo.SetType(input.Type)
	} else if forceUpdateMap["type"] {
		nuo.SetType(input.Type)
	}
	if !isZero(input.Message) {
		nuo.SetMessage(input.Message)
	} else if forceUpdateMap["message"] {
		nuo.SetMessage(input.Message)
	}
	if !isZero(input.EntityID) {
		nuo.SetEntityID(input.EntityID)
	} else if forceUpdateMap["entity_id"] {
		nuo.ClearEntityID()
	}
	if !isZero(input.EntityType) {
		nuo.SetEntityType(input.EntityType)
	} else if forceUpdateMap["entity_type"] {
		nuo.ClearEntityType()
	}
	if !isZero(input.IsRead) {
		nuo.SetIsRead(input.IsRead)
	} else if forceUpdateMap["is_read"] {
		nuo.SetIsRead(input.IsRead)
	}
	if !isZero(input.ReadAt) {
		nuo.SetReadAt(input.ReadAt)
	} else if forceUpdateMap["read_at"] {
		nuo.ClearReadAt()
	}
	if !isZero(input.SenderID) {
		nuo.SetSenderID(input.SenderID)
	} else if forceUpdateMap["sender_id"] {
		nuo.ClearSenderID()
	}
	if !isZero(input.Metadata) {
		nuo.SetMetadata(input.Metadata)
	} else if forceUpdateMap["metadata"] {
		nuo.ClearMetadata()
	}
	return nuo
}

func (nu *NotificationUpdate) SetNotification(input *Notification, forceUpdates ...string) *NotificationUpdate {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Status) {
		nu.SetStatus(input.Status)
	} else if forceUpdateMap["status"] {
		nu.SetStatus(input.Status)
	}
	if !isZero(input.UserID) {
		nu.SetUserID(input.UserID)
	} else if forceUpdateMap["user_id"] {
		nu.SetUserID(input.UserID)
	}
	if !isZero(input.Type) {
		nu.SetType(input.Type)
	} else if forceUpdateMap["type"] {
		nu.SetType(input.Type)
	}
	if !isZero(input.Message) {
		nu.SetMessage(input.Message)
	} else if forceUpdateMap["message"] {
		nu.SetMessage(input.Message)
	}
	if !isZero(input.EntityID) {
		nu.SetEntityID(input.EntityID)
	} else if forceUpdateMap["entity_id"] {
		nu.ClearEntityID()
	}
	if !isZero(input.EntityType) {
		nu.SetEntityType(input.EntityType)
	} else if forceUpdateMap["entity_type"] {
		nu.ClearEntityType()
	}
	if !isZero(input.IsRead) {
		nu.SetIsRead(input.IsRead)
	} else if forceUpdateMap["is_read"] {
		nu.SetIsRead(input.IsRead)
	}
	if !isZero(input.ReadAt) {
		nu.SetReadAt(input.ReadAt)
	} else if forceUpdateMap["read_at"] {
		nu.ClearReadAt()
	}
	if !isZero(input.SenderID) {
		nu.SetSenderID(input.SenderID)
	} else if forceUpdateMap["sender_id"] {
		nu.ClearSenderID()
	}
	if !isZero(input.Metadata) {
		nu.SetMetadata(input.Metadata)
	} else if forceUpdateMap["metadata"] {
		nu.ClearMetadata()
	}
	return nu
}

func (oc *OTPCreate) SetOTP(input *OTP) *OTPCreate {
	if !isZero(input.UserID) {
		oc.SetUserID(input.UserID)
	}
	if !isZero(input.Code) {
		oc.SetCode(input.Code)
	}
	if !isZero(input.Purpose) {
		oc.SetPurpose(input.Purpose)
	}
	if !isZero(input.ExpiresAt) {
		oc.SetExpiresAt(input.ExpiresAt)
	}
	if !isZero(input.IsUsed) {
		oc.SetIsUsed(input.IsUsed)
	}
	if !isZero(input.SentTo) {
		oc.SetSentTo(input.SentTo)
	}
	return oc
}

func (ouo *OTPUpdateOne) SetOTP(input *OTP, forceUpdates ...string) *OTPUpdateOne {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.UserID) {
		ouo.SetUserID(input.UserID)
	} else if forceUpdateMap["user_id"] {
		ouo.SetUserID(input.UserID)
	}
	if !isZero(input.Code) {
		ouo.SetCode(input.Code)
	} else if forceUpdateMap["code"] {
		ouo.SetCode(input.Code)
	}
	if !isZero(input.Purpose) {
		ouo.SetPurpose(input.Purpose)
	} else if forceUpdateMap["purpose"] {
		ouo.SetPurpose(input.Purpose)
	}
	if !isZero(input.ExpiresAt) {
		ouo.SetExpiresAt(input.ExpiresAt)
	} else if forceUpdateMap["expires_at"] {
		ouo.SetExpiresAt(input.ExpiresAt)
	}
	if !isZero(input.IsUsed) {
		ouo.SetIsUsed(input.IsUsed)
	} else if forceUpdateMap["is_used"] {
		ouo.SetIsUsed(input.IsUsed)
	}
	if !isZero(input.SentTo) {
		ouo.SetSentTo(input.SentTo)
	} else if forceUpdateMap["sent_to"] {
		ouo.SetSentTo(input.SentTo)
	}
	return ouo
}

func (ou *OTPUpdate) SetOTP(input *OTP, forceUpdates ...string) *OTPUpdate {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.UserID) {
		ou.SetUserID(input.UserID)
	} else if forceUpdateMap["user_id"] {
		ou.SetUserID(input.UserID)
	}
	if !isZero(input.Code) {
		ou.SetCode(input.Code)
	} else if forceUpdateMap["code"] {
		ou.SetCode(input.Code)
	}
	if !isZero(input.Purpose) {
		ou.SetPurpose(input.Purpose)
	} else if forceUpdateMap["purpose"] {
		ou.SetPurpose(input.Purpose)
	}
	if !isZero(input.ExpiresAt) {
		ou.SetExpiresAt(input.ExpiresAt)
	} else if forceUpdateMap["expires_at"] {
		ou.SetExpiresAt(input.ExpiresAt)
	}
	if !isZero(input.IsUsed) {
		ou.SetIsUsed(input.IsUsed)
	} else if forceUpdateMap["is_used"] {
		ou.SetIsUsed(input.IsUsed)
	}
	if !isZero(input.SentTo) {
		ou.SetSentTo(input.SentTo)
	} else if forceUpdateMap["sent_to"] {
		ou.SetSentTo(input.SentTo)
	}
	return ou
}

func (oc *OperationCreate) SetOperation(input *Operation) *OperationCreate {
	if !isZero(input.Status) {
		oc.SetStatus(input.Status)
	}
	if !isZero(input.Name) {
		oc.SetName(input.Name)
	}
	if !isZero(input.Description) {
		oc.SetDescription(input.Description)
	}
	if !isZero(input.Duration) {
		oc.SetDuration(input.Duration)
	}
	if !isZero(input.Group) {
		oc.SetGroup(input.Group)
	}
	return oc
}

func (ouo *OperationUpdateOne) SetOperation(input *Operation, forceUpdates ...string) *OperationUpdateOne {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Status) {
		ouo.SetStatus(input.Status)
	} else if forceUpdateMap["status"] {
		ouo.SetStatus(input.Status)
	}
	if !isZero(input.Name) {
		ouo.SetName(input.Name)
	} else if forceUpdateMap["name"] {
		ouo.SetName(input.Name)
	}
	if !isZero(input.Description) {
		ouo.SetDescription(input.Description)
	} else if forceUpdateMap["description"] {
		ouo.ClearDescription()
	}
	if !isZero(input.Duration) {
		ouo.SetDuration(input.Duration)
	} else if forceUpdateMap["duration"] {
		ouo.ClearDuration()
	}
	if !isZero(input.Group) {
		ouo.SetGroup(input.Group)
	} else if forceUpdateMap["group"] {
		ouo.ClearGroup()
	}
	return ouo
}

func (ou *OperationUpdate) SetOperation(input *Operation, forceUpdates ...string) *OperationUpdate {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Status) {
		ou.SetStatus(input.Status)
	} else if forceUpdateMap["status"] {
		ou.SetStatus(input.Status)
	}
	if !isZero(input.Name) {
		ou.SetName(input.Name)
	} else if forceUpdateMap["name"] {
		ou.SetName(input.Name)
	}
	if !isZero(input.Description) {
		ou.SetDescription(input.Description)
	} else if forceUpdateMap["description"] {
		ou.ClearDescription()
	}
	if !isZero(input.Duration) {
		ou.SetDuration(input.Duration)
	} else if forceUpdateMap["duration"] {
		ou.ClearDuration()
	}
	if !isZero(input.Group) {
		ou.SetGroup(input.Group)
	} else if forceUpdateMap["group"] {
		ou.ClearGroup()
	}
	return ou
}

func (omc *OperationMaterialCreate) SetOperationMaterial(input *OperationMaterial) *OperationMaterialCreate {
	if !isZero(input.OperationID) {
		omc.SetOperationID(input.OperationID)
	}
	if !isZero(input.MaterialID) {
		omc.SetMaterialID(input.MaterialID)
	}
	if !isZero(input.Quantity) {
		omc.SetQuantity(input.Quantity)
	}
	return omc
}

func (omuo *OperationMaterialUpdateOne) SetOperationMaterial(input *OperationMaterial, forceUpdates ...string) *OperationMaterialUpdateOne {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.OperationID) {
		omuo.SetOperationID(input.OperationID)
	} else if forceUpdateMap["operation_id"] {
		omuo.ClearOperationID()
	}
	if !isZero(input.MaterialID) {
		omuo.SetMaterialID(input.MaterialID)
	} else if forceUpdateMap["material_id"] {
		omuo.ClearMaterialID()
	}
	if !isZero(input.Quantity) {
		omuo.SetQuantity(input.Quantity)
	} else if forceUpdateMap["quantity"] {
		omuo.SetQuantity(input.Quantity)
	}
	return omuo
}

func (omu *OperationMaterialUpdate) SetOperationMaterial(input *OperationMaterial, forceUpdates ...string) *OperationMaterialUpdate {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.OperationID) {
		omu.SetOperationID(input.OperationID)
	} else if forceUpdateMap["operation_id"] {
		omu.ClearOperationID()
	}
	if !isZero(input.MaterialID) {
		omu.SetMaterialID(input.MaterialID)
	} else if forceUpdateMap["material_id"] {
		omu.ClearMaterialID()
	}
	if !isZero(input.Quantity) {
		omu.SetQuantity(input.Quantity)
	} else if forceUpdateMap["quantity"] {
		omu.SetQuantity(input.Quantity)
	}
	return omu
}

func (oc *OrganizationCreate) SetOrganization(input *Organization) *OrganizationCreate {
	if !isZero(input.Status) {
		oc.SetStatus(input.Status)
	}
	if !isZero(input.FullName) {
		oc.SetFullName(input.FullName)
	}
	if !isZero(input.ProvinceID) {
		oc.SetProvinceID(input.ProvinceID)
	}
	if !isZero(input.DistrictID) {
		oc.SetDistrictID(input.DistrictID)
	}
	if !isZero(input.WardID) {
		oc.SetWardID(input.WardID)
	}
	if !isZero(input.AddressNumber) {
		oc.SetAddressNumber(input.AddressNumber)
	}
	if !isZero(input.Phone) {
		oc.SetPhone(input.Phone)
	}
	if !isZero(input.Email) {
		oc.SetEmail(input.Email)
	}
	return oc
}

func (ouo *OrganizationUpdateOne) SetOrganization(input *Organization, forceUpdates ...string) *OrganizationUpdateOne {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Status) {
		ouo.SetStatus(input.Status)
	} else if forceUpdateMap["status"] {
		ouo.SetStatus(input.Status)
	}
	if !isZero(input.FullName) {
		ouo.SetFullName(input.FullName)
	} else if forceUpdateMap["full_name"] {
		ouo.SetFullName(input.FullName)
	}
	if !isZero(input.ProvinceID) {
		ouo.SetProvinceID(input.ProvinceID)
	} else if forceUpdateMap["province_id"] {
		ouo.ClearProvinceID()
	}
	if !isZero(input.DistrictID) {
		ouo.SetDistrictID(input.DistrictID)
	} else if forceUpdateMap["district_id"] {
		ouo.ClearDistrictID()
	}
	if !isZero(input.WardID) {
		ouo.SetWardID(input.WardID)
	} else if forceUpdateMap["ward_id"] {
		ouo.ClearWardID()
	}
	if !isZero(input.AddressNumber) {
		ouo.SetAddressNumber(input.AddressNumber)
	} else if forceUpdateMap["address_number"] {
		ouo.ClearAddressNumber()
	}
	if !isZero(input.Phone) {
		ouo.SetPhone(input.Phone)
	} else if forceUpdateMap["phone"] {
		ouo.ClearPhone()
	}
	if !isZero(input.Email) {
		ouo.SetEmail(input.Email)
	} else if forceUpdateMap["email"] {
		ouo.ClearEmail()
	}
	return ouo
}

func (ou *OrganizationUpdate) SetOrganization(input *Organization, forceUpdates ...string) *OrganizationUpdate {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Status) {
		ou.SetStatus(input.Status)
	} else if forceUpdateMap["status"] {
		ou.SetStatus(input.Status)
	}
	if !isZero(input.FullName) {
		ou.SetFullName(input.FullName)
	} else if forceUpdateMap["full_name"] {
		ou.SetFullName(input.FullName)
	}
	if !isZero(input.ProvinceID) {
		ou.SetProvinceID(input.ProvinceID)
	} else if forceUpdateMap["province_id"] {
		ou.ClearProvinceID()
	}
	if !isZero(input.DistrictID) {
		ou.SetDistrictID(input.DistrictID)
	} else if forceUpdateMap["district_id"] {
		ou.ClearDistrictID()
	}
	if !isZero(input.WardID) {
		ou.SetWardID(input.WardID)
	} else if forceUpdateMap["ward_id"] {
		ou.ClearWardID()
	}
	if !isZero(input.AddressNumber) {
		ou.SetAddressNumber(input.AddressNumber)
	} else if forceUpdateMap["address_number"] {
		ou.ClearAddressNumber()
	}
	if !isZero(input.Phone) {
		ou.SetPhone(input.Phone)
	} else if forceUpdateMap["phone"] {
		ou.ClearPhone()
	}
	if !isZero(input.Email) {
		ou.SetEmail(input.Email)
	} else if forceUpdateMap["email"] {
		ou.ClearEmail()
	}
	return ou
}

func (pc *PaymentCreate) SetPayment(input *Payment) *PaymentCreate {
	if !isZero(input.Status) {
		pc.SetStatus(input.Status)
	}
	if !isZero(input.TotalAmount) {
		pc.SetTotalAmount(input.TotalAmount)
	}
	if !isZero(input.Cash) {
		pc.SetCash(input.Cash)
	}
	if !isZero(input.CreditCard) {
		pc.SetCreditCard(input.CreditCard)
	}
	if !isZero(input.Mpos) {
		pc.SetMpos(input.Mpos)
	}
	if !isZero(input.Bank) {
		pc.SetBank(input.Bank)
	}
	if !isZero(input.Momo) {
		pc.SetMomo(input.Momo)
	}
	if !isZero(input.State) {
		pc.SetState(input.State)
	}
	if !isZero(input.PaymentDate) {
		pc.SetPaymentDate(input.PaymentDate)
	}
	if !isZero(input.BillID) {
		pc.SetBillID(input.BillID)
	}
	if !isZero(input.PersonID) {
		pc.SetPersonID(input.PersonID)
	}
	if !isZero(input.UserID) {
		pc.SetUserID(input.UserID)
	}
	if input.DealID != nil && !isZero(*input.DealID) {
		pc.SetDealID(*input.DealID)
	}
	return pc
}

func (puo *PaymentUpdateOne) SetPayment(input *Payment, forceUpdates ...string) *PaymentUpdateOne {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Status) {
		puo.SetStatus(input.Status)
	} else if forceUpdateMap["status"] {
		puo.SetStatus(input.Status)
	}
	if !isZero(input.TotalAmount) {
		puo.SetTotalAmount(input.TotalAmount)
	} else if forceUpdateMap["total_amount"] {
		puo.SetTotalAmount(input.TotalAmount)
	}
	if !isZero(input.Cash) {
		puo.SetCash(input.Cash)
	} else if forceUpdateMap["cash"] {
		puo.SetCash(input.Cash)
	}
	if !isZero(input.CreditCard) {
		puo.SetCreditCard(input.CreditCard)
	} else if forceUpdateMap["credit_card"] {
		puo.SetCreditCard(input.CreditCard)
	}
	if !isZero(input.Mpos) {
		puo.SetMpos(input.Mpos)
	} else if forceUpdateMap["mpos"] {
		puo.SetMpos(input.Mpos)
	}
	if !isZero(input.Bank) {
		puo.SetBank(input.Bank)
	} else if forceUpdateMap["bank"] {
		puo.SetBank(input.Bank)
	}
	if !isZero(input.Momo) {
		puo.SetMomo(input.Momo)
	} else if forceUpdateMap["momo"] {
		puo.SetMomo(input.Momo)
	}
	if !isZero(input.State) {
		puo.SetState(input.State)
	} else if forceUpdateMap["state"] {
		puo.SetState(input.State)
	}
	if !isZero(input.PaymentDate) {
		puo.SetPaymentDate(input.PaymentDate)
	} else if forceUpdateMap["payment_date"] {
		puo.SetPaymentDate(input.PaymentDate)
	}
	if !isZero(input.BillID) {
		puo.SetBillID(input.BillID)
	} else if forceUpdateMap["bill_id"] {
		puo.ClearBillID()
	}
	if !isZero(input.PersonID) {
		puo.SetPersonID(input.PersonID)
	} else if forceUpdateMap["person_id"] {
		puo.ClearPersonID()
	}
	if !isZero(input.UserID) {
		puo.SetUserID(input.UserID)
	} else if forceUpdateMap["user_id"] {
		puo.ClearUserID()
	}
	if input.DealID != nil && !isZero(*input.DealID) {
		puo.SetDealID(*input.DealID)
	} else if forceUpdateMap["deal_id"] {
		puo.ClearDealID()
	}
	return puo
}

func (pu *PaymentUpdate) SetPayment(input *Payment, forceUpdates ...string) *PaymentUpdate {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Status) {
		pu.SetStatus(input.Status)
	} else if forceUpdateMap["status"] {
		pu.SetStatus(input.Status)
	}
	if !isZero(input.TotalAmount) {
		pu.SetTotalAmount(input.TotalAmount)
	} else if forceUpdateMap["total_amount"] {
		pu.SetTotalAmount(input.TotalAmount)
	}
	if !isZero(input.Cash) {
		pu.SetCash(input.Cash)
	} else if forceUpdateMap["cash"] {
		pu.SetCash(input.Cash)
	}
	if !isZero(input.CreditCard) {
		pu.SetCreditCard(input.CreditCard)
	} else if forceUpdateMap["credit_card"] {
		pu.SetCreditCard(input.CreditCard)
	}
	if !isZero(input.Mpos) {
		pu.SetMpos(input.Mpos)
	} else if forceUpdateMap["mpos"] {
		pu.SetMpos(input.Mpos)
	}
	if !isZero(input.Bank) {
		pu.SetBank(input.Bank)
	} else if forceUpdateMap["bank"] {
		pu.SetBank(input.Bank)
	}
	if !isZero(input.Momo) {
		pu.SetMomo(input.Momo)
	} else if forceUpdateMap["momo"] {
		pu.SetMomo(input.Momo)
	}
	if !isZero(input.State) {
		pu.SetState(input.State)
	} else if forceUpdateMap["state"] {
		pu.SetState(input.State)
	}
	if !isZero(input.PaymentDate) {
		pu.SetPaymentDate(input.PaymentDate)
	} else if forceUpdateMap["payment_date"] {
		pu.SetPaymentDate(input.PaymentDate)
	}
	if !isZero(input.BillID) {
		pu.SetBillID(input.BillID)
	} else if forceUpdateMap["bill_id"] {
		pu.ClearBillID()
	}
	if !isZero(input.PersonID) {
		pu.SetPersonID(input.PersonID)
	} else if forceUpdateMap["person_id"] {
		pu.ClearPersonID()
	}
	if !isZero(input.UserID) {
		pu.SetUserID(input.UserID)
	} else if forceUpdateMap["user_id"] {
		pu.ClearUserID()
	}
	if input.DealID != nil && !isZero(*input.DealID) {
		pu.SetDealID(*input.DealID)
	} else if forceUpdateMap["deal_id"] {
		pu.ClearDealID()
	}
	return pu
}

func (pac *PaymentAllocationCreate) SetPaymentAllocation(input *PaymentAllocation) *PaymentAllocationCreate {
	if !isZero(input.Status) {
		pac.SetStatus(input.Status)
	}
	if !isZero(input.PaymentID) {
		pac.SetPaymentID(input.PaymentID)
	}
	if !isZero(input.BillID) {
		pac.SetBillID(input.BillID)
	}
	if !isZero(input.UserID) {
		pac.SetUserID(input.UserID)
	}
	if input.BillItemID != nil && !isZero(*input.BillItemID) {
		pac.SetBillItemID(*input.BillItemID)
	}
	if input.InstallmentID != nil && !isZero(*input.InstallmentID) {
		pac.SetInstallmentID(*input.InstallmentID)
	}
	if !isZero(input.Note) {
		pac.SetNote(input.Note)
	}
	if !isZero(input.State) {
		pac.SetState(input.State)
	}
	if !isZero(input.Amount) {
		pac.SetAmount(input.Amount)
	}
	return pac
}

func (pauo *PaymentAllocationUpdateOne) SetPaymentAllocation(input *PaymentAllocation, forceUpdates ...string) *PaymentAllocationUpdateOne {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Status) {
		pauo.SetStatus(input.Status)
	} else if forceUpdateMap["status"] {
		pauo.SetStatus(input.Status)
	}
	if !isZero(input.PaymentID) {
		pauo.SetPaymentID(input.PaymentID)
	} else if forceUpdateMap["payment_id"] {
		pauo.SetPaymentID(input.PaymentID)
	}
	if !isZero(input.BillID) {
		pauo.SetBillID(input.BillID)
	} else if forceUpdateMap["bill_id"] {
		pauo.ClearBillID()
	}
	if !isZero(input.UserID) {
		pauo.SetUserID(input.UserID)
	} else if forceUpdateMap["user_id"] {
		pauo.ClearUserID()
	}
	if input.BillItemID != nil && !isZero(*input.BillItemID) {
		pauo.SetBillItemID(*input.BillItemID)
	} else if forceUpdateMap["bill_item_id"] {
		pauo.ClearBillItemID()
	}
	if input.InstallmentID != nil && !isZero(*input.InstallmentID) {
		pauo.SetInstallmentID(*input.InstallmentID)
	} else if forceUpdateMap["installment_id"] {
		pauo.ClearInstallmentID()
	}
	if !isZero(input.Note) {
		pauo.SetNote(input.Note)
	} else if forceUpdateMap["note"] {
		pauo.ClearNote()
	}
	if !isZero(input.State) {
		pauo.SetState(input.State)
	} else if forceUpdateMap["state"] {
		pauo.ClearState()
	}
	if !isZero(input.Amount) {
		pauo.SetAmount(input.Amount)
	} else if forceUpdateMap["amount"] {
		pauo.SetAmount(input.Amount)
	}
	return pauo
}

func (pau *PaymentAllocationUpdate) SetPaymentAllocation(input *PaymentAllocation, forceUpdates ...string) *PaymentAllocationUpdate {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Status) {
		pau.SetStatus(input.Status)
	} else if forceUpdateMap["status"] {
		pau.SetStatus(input.Status)
	}
	if !isZero(input.PaymentID) {
		pau.SetPaymentID(input.PaymentID)
	} else if forceUpdateMap["payment_id"] {
		pau.SetPaymentID(input.PaymentID)
	}
	if !isZero(input.BillID) {
		pau.SetBillID(input.BillID)
	} else if forceUpdateMap["bill_id"] {
		pau.ClearBillID()
	}
	if !isZero(input.UserID) {
		pau.SetUserID(input.UserID)
	} else if forceUpdateMap["user_id"] {
		pau.ClearUserID()
	}
	if input.BillItemID != nil && !isZero(*input.BillItemID) {
		pau.SetBillItemID(*input.BillItemID)
	} else if forceUpdateMap["bill_item_id"] {
		pau.ClearBillItemID()
	}
	if input.InstallmentID != nil && !isZero(*input.InstallmentID) {
		pau.SetInstallmentID(*input.InstallmentID)
	} else if forceUpdateMap["installment_id"] {
		pau.ClearInstallmentID()
	}
	if !isZero(input.Note) {
		pau.SetNote(input.Note)
	} else if forceUpdateMap["note"] {
		pau.ClearNote()
	}
	if !isZero(input.State) {
		pau.SetState(input.State)
	} else if forceUpdateMap["state"] {
		pau.ClearState()
	}
	if !isZero(input.Amount) {
		pau.SetAmount(input.Amount)
	} else if forceUpdateMap["amount"] {
		pau.SetAmount(input.Amount)
	}
	return pau
}

func (pc *PersonCreate) SetPerson(input *Person) *PersonCreate {
	if !isZero(input.Status) {
		pc.SetStatus(input.Status)
	}
	if !isZero(input.FullName) {
		pc.SetFullName(input.FullName)
	}
	if input.DateOfBirth != nil && !isZero(*input.DateOfBirth) {
		pc.SetDateOfBirth(*input.DateOfBirth)
	}
	if !isZero(input.Gender) {
		pc.SetGender(input.Gender)
	}
	if !isZero(input.ProvinceID) {
		pc.SetProvinceID(input.ProvinceID)
	}
	if !isZero(input.DistrictID) {
		pc.SetDistrictID(input.DistrictID)
	}
	if !isZero(input.WardID) {
		pc.SetWardID(input.WardID)
	}
	if !isZero(input.AddressNumber) {
		pc.SetAddressNumber(input.AddressNumber)
	}
	if !isZero(input.Phone) {
		pc.SetPhone(input.Phone)
	}
	if !isZero(input.Email) {
		pc.SetEmail(input.Email)
	}
	if input.JobID != nil && !isZero(*input.JobID) {
		pc.SetJobID(*input.JobID)
	}
	if !isZero(input.UserID) {
		pc.SetUserID(input.UserID)
	}
	if input.SourceID != nil && !isZero(*input.SourceID) {
		pc.SetSourceID(*input.SourceID)
	}
	if !isZero(input.PhoneConfirm) {
		pc.SetPhoneConfirm(input.PhoneConfirm)
	}
	if !isZero(input.MailConfirm) {
		pc.SetMailConfirm(input.MailConfirm)
	}
	if !isZero(input.PersonField) {
		pc.SetPersonField(input.PersonField)
	}
	return pc
}

func (puo *PersonUpdateOne) SetPerson(input *Person, forceUpdates ...string) *PersonUpdateOne {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Status) {
		puo.SetStatus(input.Status)
	} else if forceUpdateMap["status"] {
		puo.SetStatus(input.Status)
	}
	if !isZero(input.FullName) {
		puo.SetFullName(input.FullName)
	} else if forceUpdateMap["full_name"] {
		puo.SetFullName(input.FullName)
	}
	if input.DateOfBirth != nil && !isZero(*input.DateOfBirth) {
		puo.SetDateOfBirth(*input.DateOfBirth)
	} else if forceUpdateMap["date_of_birth"] {
		puo.ClearDateOfBirth()
	}
	if !isZero(input.Gender) {
		puo.SetGender(input.Gender)
	} else if forceUpdateMap["gender"] {
		puo.ClearGender()
	}
	if !isZero(input.ProvinceID) {
		puo.SetProvinceID(input.ProvinceID)
	} else if forceUpdateMap["province_id"] {
		puo.ClearProvinceID()
	}
	if !isZero(input.DistrictID) {
		puo.SetDistrictID(input.DistrictID)
	} else if forceUpdateMap["district_id"] {
		puo.ClearDistrictID()
	}
	if !isZero(input.WardID) {
		puo.SetWardID(input.WardID)
	} else if forceUpdateMap["ward_id"] {
		puo.ClearWardID()
	}
	if !isZero(input.AddressNumber) {
		puo.SetAddressNumber(input.AddressNumber)
	} else if forceUpdateMap["address_number"] {
		puo.ClearAddressNumber()
	}
	if !isZero(input.Phone) {
		puo.SetPhone(input.Phone)
	} else if forceUpdateMap["phone"] {
		puo.SetPhone(input.Phone)
	}
	if !isZero(input.Email) {
		puo.SetEmail(input.Email)
	} else if forceUpdateMap["email"] {
		puo.ClearEmail()
	}
	if input.JobID != nil && !isZero(*input.JobID) {
		puo.SetJobID(*input.JobID)
	} else if forceUpdateMap["job_id"] {
		puo.ClearJobID()
	}
	if !isZero(input.UserID) {
		puo.SetUserID(input.UserID)
	} else if forceUpdateMap["user_id"] {
		puo.ClearUserID()
	}
	if input.SourceID != nil && !isZero(*input.SourceID) {
		puo.SetSourceID(*input.SourceID)
	} else if forceUpdateMap["source_id"] {
		puo.ClearSourceID()
	}
	if !isZero(input.PhoneConfirm) {
		puo.SetPhoneConfirm(input.PhoneConfirm)
	} else if forceUpdateMap["phone_confirm"] {
		puo.ClearPhoneConfirm()
	}
	if !isZero(input.MailConfirm) {
		puo.SetMailConfirm(input.MailConfirm)
	} else if forceUpdateMap["mail_confirm"] {
		puo.ClearMailConfirm()
	}
	if !isZero(input.PersonField) {
		puo.SetPersonField(input.PersonField)
	} else if forceUpdateMap["person_field"] {
		puo.ClearPersonField()
	}
	return puo
}

func (pu *PersonUpdate) SetPerson(input *Person, forceUpdates ...string) *PersonUpdate {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Status) {
		pu.SetStatus(input.Status)
	} else if forceUpdateMap["status"] {
		pu.SetStatus(input.Status)
	}
	if !isZero(input.FullName) {
		pu.SetFullName(input.FullName)
	} else if forceUpdateMap["full_name"] {
		pu.SetFullName(input.FullName)
	}
	if input.DateOfBirth != nil && !isZero(*input.DateOfBirth) {
		pu.SetDateOfBirth(*input.DateOfBirth)
	} else if forceUpdateMap["date_of_birth"] {
		pu.ClearDateOfBirth()
	}
	if !isZero(input.Gender) {
		pu.SetGender(input.Gender)
	} else if forceUpdateMap["gender"] {
		pu.ClearGender()
	}
	if !isZero(input.ProvinceID) {
		pu.SetProvinceID(input.ProvinceID)
	} else if forceUpdateMap["province_id"] {
		pu.ClearProvinceID()
	}
	if !isZero(input.DistrictID) {
		pu.SetDistrictID(input.DistrictID)
	} else if forceUpdateMap["district_id"] {
		pu.ClearDistrictID()
	}
	if !isZero(input.WardID) {
		pu.SetWardID(input.WardID)
	} else if forceUpdateMap["ward_id"] {
		pu.ClearWardID()
	}
	if !isZero(input.AddressNumber) {
		pu.SetAddressNumber(input.AddressNumber)
	} else if forceUpdateMap["address_number"] {
		pu.ClearAddressNumber()
	}
	if !isZero(input.Phone) {
		pu.SetPhone(input.Phone)
	} else if forceUpdateMap["phone"] {
		pu.SetPhone(input.Phone)
	}
	if !isZero(input.Email) {
		pu.SetEmail(input.Email)
	} else if forceUpdateMap["email"] {
		pu.ClearEmail()
	}
	if input.JobID != nil && !isZero(*input.JobID) {
		pu.SetJobID(*input.JobID)
	} else if forceUpdateMap["job_id"] {
		pu.ClearJobID()
	}
	if !isZero(input.UserID) {
		pu.SetUserID(input.UserID)
	} else if forceUpdateMap["user_id"] {
		pu.ClearUserID()
	}
	if input.SourceID != nil && !isZero(*input.SourceID) {
		pu.SetSourceID(*input.SourceID)
	} else if forceUpdateMap["source_id"] {
		pu.ClearSourceID()
	}
	if !isZero(input.PhoneConfirm) {
		pu.SetPhoneConfirm(input.PhoneConfirm)
	} else if forceUpdateMap["phone_confirm"] {
		pu.ClearPhoneConfirm()
	}
	if !isZero(input.MailConfirm) {
		pu.SetMailConfirm(input.MailConfirm)
	} else if forceUpdateMap["mail_confirm"] {
		pu.ClearMailConfirm()
	}
	if !isZero(input.PersonField) {
		pu.SetPersonField(input.PersonField)
	} else if forceUpdateMap["person_field"] {
		pu.ClearPersonField()
	}
	return pu
}

func (pac *PersonAssignmentCreate) SetPersonAssignment(input *PersonAssignment) *PersonAssignmentCreate {
	if !isZero(input.PersonID) {
		pac.SetPersonID(input.PersonID)
	}
	if !isZero(input.UserID) {
		pac.SetUserID(input.UserID)
	}
	if !isZero(input.Role) {
		pac.SetRole(input.Role)
	}
	return pac
}

func (pauo *PersonAssignmentUpdateOne) SetPersonAssignment(input *PersonAssignment, forceUpdates ...string) *PersonAssignmentUpdateOne {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.PersonID) {
		pauo.SetPersonID(input.PersonID)
	} else if forceUpdateMap["person_id"] {
		pauo.SetPersonID(input.PersonID)
	}
	if !isZero(input.UserID) {
		pauo.SetUserID(input.UserID)
	} else if forceUpdateMap["user_id"] {
		pauo.SetUserID(input.UserID)
	}
	if !isZero(input.Role) {
		pauo.SetRole(input.Role)
	} else if forceUpdateMap["role"] {
		pauo.ClearRole()
	}
	return pauo
}

func (pau *PersonAssignmentUpdate) SetPersonAssignment(input *PersonAssignment, forceUpdates ...string) *PersonAssignmentUpdate {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.PersonID) {
		pau.SetPersonID(input.PersonID)
	} else if forceUpdateMap["person_id"] {
		pau.SetPersonID(input.PersonID)
	}
	if !isZero(input.UserID) {
		pau.SetUserID(input.UserID)
	} else if forceUpdateMap["user_id"] {
		pau.SetUserID(input.UserID)
	}
	if !isZero(input.Role) {
		pau.SetRole(input.Role)
	} else if forceUpdateMap["role"] {
		pau.ClearRole()
	}
	return pau
}

func (pdc *PersonDataCreate) SetPersonData(input *PersonData) *PersonDataCreate {
	if !isZero(input.PersonID) {
		pdc.SetPersonID(input.PersonID)
	}
	if !isZero(input.Kind) {
		pdc.SetKind(input.Kind)
	}
	if !isZero(input.Data) {
		pdc.SetData(input.Data)
	}
	return pdc
}

func (pduo *PersonDataUpdateOne) SetPersonData(input *PersonData, forceUpdates ...string) *PersonDataUpdateOne {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.PersonID) {
		pduo.SetPersonID(input.PersonID)
	} else if forceUpdateMap["person_id"] {
		pduo.SetPersonID(input.PersonID)
	}
	if !isZero(input.Kind) {
		pduo.SetKind(input.Kind)
	} else if forceUpdateMap["kind"] {
		pduo.SetKind(input.Kind)
	}
	if !isZero(input.Data) {
		pduo.SetData(input.Data)
	} else if forceUpdateMap["data"] {
		pduo.ClearData()
	}
	return pduo
}

func (pdu *PersonDataUpdate) SetPersonData(input *PersonData, forceUpdates ...string) *PersonDataUpdate {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.PersonID) {
		pdu.SetPersonID(input.PersonID)
	} else if forceUpdateMap["person_id"] {
		pdu.SetPersonID(input.PersonID)
	}
	if !isZero(input.Kind) {
		pdu.SetKind(input.Kind)
	} else if forceUpdateMap["kind"] {
		pdu.SetKind(input.Kind)
	}
	if !isZero(input.Data) {
		pdu.SetData(input.Data)
	} else if forceUpdateMap["data"] {
		pdu.ClearData()
	}
	return pdu
}

func (phc *PersonHistoryCreate) SetPersonHistory(input *PersonHistory) *PersonHistoryCreate {
	if !isZero(input.PersonID) {
		phc.SetPersonID(input.PersonID)
	}
	if !isZero(input.Before) {
		phc.SetBefore(input.Before)
	}
	if !isZero(input.After) {
		phc.SetAfter(input.After)
	}
	if !isZero(input.Operation) {
		phc.SetOperation(input.Operation)
	}
	if !isZero(input.Related) {
		phc.SetRelated(input.Related)
	}
	if !isZero(input.RelatedID) {
		phc.SetRelatedID(input.RelatedID)
	}
	if !isZero(input.ChangedAt) {
		phc.SetChangedAt(input.ChangedAt)
	}
	if !isZero(input.UserID) {
		phc.SetUserID(input.UserID)
	}
	return phc
}

func (phuo *PersonHistoryUpdateOne) SetPersonHistory(input *PersonHistory, forceUpdates ...string) *PersonHistoryUpdateOne {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.PersonID) {
		phuo.SetPersonID(input.PersonID)
	} else if forceUpdateMap["person_id"] {
		phuo.ClearPersonID()
	}
	if !isZero(input.Before) {
		phuo.SetBefore(input.Before)
	} else if forceUpdateMap["before"] {
		phuo.ClearBefore()
	}
	if !isZero(input.After) {
		phuo.SetAfter(input.After)
	} else if forceUpdateMap["after"] {
		phuo.ClearAfter()
	}
	if !isZero(input.Operation) {
		phuo.SetOperation(input.Operation)
	} else if forceUpdateMap["operation"] {
		phuo.SetOperation(input.Operation)
	}
	if !isZero(input.Related) {
		phuo.SetRelated(input.Related)
	} else if forceUpdateMap["related"] {
		phuo.ClearRelated()
	}
	if !isZero(input.RelatedID) {
		phuo.SetRelatedID(input.RelatedID)
	} else if forceUpdateMap["related_id"] {
		phuo.ClearRelatedID()
	}
	if !isZero(input.UserID) {
		phuo.SetUserID(input.UserID)
	} else if forceUpdateMap["user_id"] {
		phuo.ClearUserID()
	}
	return phuo
}

func (phu *PersonHistoryUpdate) SetPersonHistory(input *PersonHistory, forceUpdates ...string) *PersonHistoryUpdate {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.PersonID) {
		phu.SetPersonID(input.PersonID)
	} else if forceUpdateMap["person_id"] {
		phu.ClearPersonID()
	}
	if !isZero(input.Before) {
		phu.SetBefore(input.Before)
	} else if forceUpdateMap["before"] {
		phu.ClearBefore()
	}
	if !isZero(input.After) {
		phu.SetAfter(input.After)
	} else if forceUpdateMap["after"] {
		phu.ClearAfter()
	}
	if !isZero(input.Operation) {
		phu.SetOperation(input.Operation)
	} else if forceUpdateMap["operation"] {
		phu.SetOperation(input.Operation)
	}
	if !isZero(input.Related) {
		phu.SetRelated(input.Related)
	} else if forceUpdateMap["related"] {
		phu.ClearRelated()
	}
	if !isZero(input.RelatedID) {
		phu.SetRelatedID(input.RelatedID)
	} else if forceUpdateMap["related_id"] {
		phu.ClearRelatedID()
	}
	if !isZero(input.UserID) {
		phu.SetUserID(input.UserID)
	} else if forceUpdateMap["user_id"] {
		phu.ClearUserID()
	}
	return phu
}

func (prc *PersonReferralCreate) SetPersonReferral(input *PersonReferral) *PersonReferralCreate {
	if !isZero(input.Note) {
		prc.SetNote(input.Note)
	}
	if !isZero(input.Relationship) {
		prc.SetRelationship(input.Relationship)
	}
	if !isZero(input.ReferrerID) {
		prc.SetReferrerID(input.ReferrerID)
	}
	if !isZero(input.ReferredID) {
		prc.SetReferredID(input.ReferredID)
	}
	return prc
}

func (pruo *PersonReferralUpdateOne) SetPersonReferral(input *PersonReferral, forceUpdates ...string) *PersonReferralUpdateOne {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Note) {
		pruo.SetNote(input.Note)
	} else if forceUpdateMap["note"] {
		pruo.ClearNote()
	}
	if !isZero(input.Relationship) {
		pruo.SetRelationship(input.Relationship)
	} else if forceUpdateMap["relationship"] {
		pruo.ClearRelationship()
	}
	if !isZero(input.ReferrerID) {
		pruo.SetReferrerID(input.ReferrerID)
	} else if forceUpdateMap["referrer_id"] {
		pruo.SetReferrerID(input.ReferrerID)
	}
	if !isZero(input.ReferredID) {
		pruo.SetReferredID(input.ReferredID)
	} else if forceUpdateMap["referred_id"] {
		pruo.SetReferredID(input.ReferredID)
	}
	return pruo
}

func (pru *PersonReferralUpdate) SetPersonReferral(input *PersonReferral, forceUpdates ...string) *PersonReferralUpdate {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Note) {
		pru.SetNote(input.Note)
	} else if forceUpdateMap["note"] {
		pru.ClearNote()
	}
	if !isZero(input.Relationship) {
		pru.SetRelationship(input.Relationship)
	} else if forceUpdateMap["relationship"] {
		pru.ClearRelationship()
	}
	if !isZero(input.ReferrerID) {
		pru.SetReferrerID(input.ReferrerID)
	} else if forceUpdateMap["referrer_id"] {
		pru.SetReferrerID(input.ReferrerID)
	}
	if !isZero(input.ReferredID) {
		pru.SetReferredID(input.ReferredID)
	} else if forceUpdateMap["referred_id"] {
		pru.SetReferredID(input.ReferredID)
	}
	return pru
}

func (pc *PipelineCreate) SetPipeline(input *Pipeline) *PipelineCreate {
	if !isZero(input.Status) {
		pc.SetStatus(input.Status)
	}
	if !isZero(input.Name) {
		pc.SetName(input.Name)
	}
	if !isZero(input.Description) {
		pc.SetDescription(input.Description)
	}
	if !isZero(input.UserID) {
		pc.SetUserID(input.UserID)
	}
	return pc
}

func (puo *PipelineUpdateOne) SetPipeline(input *Pipeline, forceUpdates ...string) *PipelineUpdateOne {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Status) {
		puo.SetStatus(input.Status)
	} else if forceUpdateMap["status"] {
		puo.SetStatus(input.Status)
	}
	if !isZero(input.Name) {
		puo.SetName(input.Name)
	} else if forceUpdateMap["name"] {
		puo.SetName(input.Name)
	}
	if !isZero(input.Description) {
		puo.SetDescription(input.Description)
	} else if forceUpdateMap["description"] {
		puo.ClearDescription()
	}
	if !isZero(input.UserID) {
		puo.SetUserID(input.UserID)
	} else if forceUpdateMap["user_id"] {
		puo.ClearUserID()
	}
	return puo
}

func (pu *PipelineUpdate) SetPipeline(input *Pipeline, forceUpdates ...string) *PipelineUpdate {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Status) {
		pu.SetStatus(input.Status)
	} else if forceUpdateMap["status"] {
		pu.SetStatus(input.Status)
	}
	if !isZero(input.Name) {
		pu.SetName(input.Name)
	} else if forceUpdateMap["name"] {
		pu.SetName(input.Name)
	}
	if !isZero(input.Description) {
		pu.SetDescription(input.Description)
	} else if forceUpdateMap["description"] {
		pu.ClearDescription()
	}
	if !isZero(input.UserID) {
		pu.SetUserID(input.UserID)
	} else if forceUpdateMap["user_id"] {
		pu.ClearUserID()
	}
	return pu
}

func (pc *ProductCreate) SetProduct(input *Product) *ProductCreate {
	if !isZero(input.Status) {
		pc.SetStatus(input.Status)
	}
	if !isZero(input.Name) {
		pc.SetName(input.Name)
	}
	if !isZero(input.Code) {
		pc.SetCode(input.Code)
	}
	if !isZero(input.Description) {
		pc.SetDescription(input.Description)
	}
	if !isZero(input.Price) {
		pc.SetPrice(input.Price)
	}
	if !isZero(input.Type) {
		pc.SetType(input.Type)
	}
	if !isZero(input.Quantity) {
		pc.SetQuantity(input.Quantity)
	}
	if !isZero(input.Sku) {
		pc.SetSku(input.Sku)
	}
	if !isZero(input.Attributes) {
		pc.SetAttributes(input.Attributes)
	}
	if !isZero(input.Categories) {
		pc.SetCategories(input.Categories)
	}
	if !isZero(input.Image) {
		pc.SetImage(input.Image)
	}
	if !isZero(input.Revision) {
		pc.SetRevision(input.Revision)
	}
	if input.UnitID != nil && !isZero(*input.UnitID) {
		pc.SetUnitID(*input.UnitID)
	}
	if input.GroupID != nil && !isZero(*input.GroupID) {
		pc.SetGroupID(*input.GroupID)
	}
	if input.CategoryID != nil && !isZero(*input.CategoryID) {
		pc.SetCategoryID(*input.CategoryID)
	}
	if !isZero(input.Collection) {
		pc.SetCollection(input.Collection)
	}
	return pc
}

func (puo *ProductUpdateOne) SetProduct(input *Product, forceUpdates ...string) *ProductUpdateOne {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Status) {
		puo.SetStatus(input.Status)
	} else if forceUpdateMap["status"] {
		puo.SetStatus(input.Status)
	}
	if !isZero(input.Name) {
		puo.SetName(input.Name)
	} else if forceUpdateMap["name"] {
		puo.SetName(input.Name)
	}
	if !isZero(input.Code) {
		puo.SetCode(input.Code)
	} else if forceUpdateMap["code"] {
		puo.ClearCode()
	}
	if !isZero(input.Description) {
		puo.SetDescription(input.Description)
	} else if forceUpdateMap["description"] {
		puo.ClearDescription()
	}
	if !isZero(input.Price) {
		puo.SetPrice(input.Price)
	} else if forceUpdateMap["price"] {
		puo.SetPrice(input.Price)
	}
	if !isZero(input.Type) {
		puo.SetType(input.Type)
	} else if forceUpdateMap["type"] {
		puo.ClearType()
	}
	if !isZero(input.Quantity) {
		puo.SetQuantity(input.Quantity)
	} else if forceUpdateMap["quantity"] {
		puo.SetQuantity(input.Quantity)
	}
	if !isZero(input.Sku) {
		puo.SetSku(input.Sku)
	} else if forceUpdateMap["sku"] {
		puo.ClearSku()
	}
	if !isZero(input.Attributes) {
		puo.SetAttributes(input.Attributes)
	} else if forceUpdateMap["attributes"] {
		puo.ClearAttributes()
	}
	if !isZero(input.Categories) {
		puo.SetCategories(input.Categories)
	} else if forceUpdateMap["categories"] {
		puo.ClearCategories()
	}
	if !isZero(input.Image) {
		puo.SetImage(input.Image)
	} else if forceUpdateMap["image"] {
		puo.ClearImage()
	}
	if !isZero(input.Revision) {
		puo.SetRevision(input.Revision)
	} else if forceUpdateMap["revision"] {
		puo.ClearRevision()
	}
	if input.UnitID != nil && !isZero(*input.UnitID) {
		puo.SetUnitID(*input.UnitID)
	} else if forceUpdateMap["unit_id"] {
		puo.ClearUnitID()
	}
	if input.GroupID != nil && !isZero(*input.GroupID) {
		puo.SetGroupID(*input.GroupID)
	} else if forceUpdateMap["group_id"] {
		puo.ClearGroupID()
	}
	if input.CategoryID != nil && !isZero(*input.CategoryID) {
		puo.SetCategoryID(*input.CategoryID)
	} else if forceUpdateMap["category_id"] {
		puo.ClearCategoryID()
	}
	if !isZero(input.Collection) {
		puo.SetCollection(input.Collection)
	} else if forceUpdateMap["collection"] {
		puo.ClearCollection()
	}
	return puo
}

func (pu *ProductUpdate) SetProduct(input *Product, forceUpdates ...string) *ProductUpdate {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Status) {
		pu.SetStatus(input.Status)
	} else if forceUpdateMap["status"] {
		pu.SetStatus(input.Status)
	}
	if !isZero(input.Name) {
		pu.SetName(input.Name)
	} else if forceUpdateMap["name"] {
		pu.SetName(input.Name)
	}
	if !isZero(input.Code) {
		pu.SetCode(input.Code)
	} else if forceUpdateMap["code"] {
		pu.ClearCode()
	}
	if !isZero(input.Description) {
		pu.SetDescription(input.Description)
	} else if forceUpdateMap["description"] {
		pu.ClearDescription()
	}
	if !isZero(input.Price) {
		pu.SetPrice(input.Price)
	} else if forceUpdateMap["price"] {
		pu.SetPrice(input.Price)
	}
	if !isZero(input.Type) {
		pu.SetType(input.Type)
	} else if forceUpdateMap["type"] {
		pu.ClearType()
	}
	if !isZero(input.Quantity) {
		pu.SetQuantity(input.Quantity)
	} else if forceUpdateMap["quantity"] {
		pu.SetQuantity(input.Quantity)
	}
	if !isZero(input.Sku) {
		pu.SetSku(input.Sku)
	} else if forceUpdateMap["sku"] {
		pu.ClearSku()
	}
	if !isZero(input.Attributes) {
		pu.SetAttributes(input.Attributes)
	} else if forceUpdateMap["attributes"] {
		pu.ClearAttributes()
	}
	if !isZero(input.Categories) {
		pu.SetCategories(input.Categories)
	} else if forceUpdateMap["categories"] {
		pu.ClearCategories()
	}
	if !isZero(input.Image) {
		pu.SetImage(input.Image)
	} else if forceUpdateMap["image"] {
		pu.ClearImage()
	}
	if !isZero(input.Revision) {
		pu.SetRevision(input.Revision)
	} else if forceUpdateMap["revision"] {
		pu.ClearRevision()
	}
	if input.UnitID != nil && !isZero(*input.UnitID) {
		pu.SetUnitID(*input.UnitID)
	} else if forceUpdateMap["unit_id"] {
		pu.ClearUnitID()
	}
	if input.GroupID != nil && !isZero(*input.GroupID) {
		pu.SetGroupID(*input.GroupID)
	} else if forceUpdateMap["group_id"] {
		pu.ClearGroupID()
	}
	if input.CategoryID != nil && !isZero(*input.CategoryID) {
		pu.SetCategoryID(*input.CategoryID)
	} else if forceUpdateMap["category_id"] {
		pu.ClearCategoryID()
	}
	if !isZero(input.Collection) {
		pu.SetCollection(input.Collection)
	} else if forceUpdateMap["collection"] {
		pu.ClearCollection()
	}
	return pu
}

func (poc *ProductOperationCreate) SetProductOperation(input *ProductOperation) *ProductOperationCreate {
	if !isZero(input.OrderSequence) {
		poc.SetOrderSequence(input.OrderSequence)
	}
	if !isZero(input.ProductID) {
		poc.SetProductID(input.ProductID)
	}
	if !isZero(input.OperationID) {
		poc.SetOperationID(input.OperationID)
	}
	return poc
}

func (pouo *ProductOperationUpdateOne) SetProductOperation(input *ProductOperation, forceUpdates ...string) *ProductOperationUpdateOne {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.OrderSequence) {
		pouo.SetOrderSequence(input.OrderSequence)
	} else if forceUpdateMap["order_sequence"] {
		pouo.ClearOrderSequence()
	}
	if !isZero(input.ProductID) {
		pouo.SetProductID(input.ProductID)
	} else if forceUpdateMap["product_id"] {
		pouo.SetProductID(input.ProductID)
	}
	if !isZero(input.OperationID) {
		pouo.SetOperationID(input.OperationID)
	} else if forceUpdateMap["operation_id"] {
		pouo.SetOperationID(input.OperationID)
	}
	return pouo
}

func (pou *ProductOperationUpdate) SetProductOperation(input *ProductOperation, forceUpdates ...string) *ProductOperationUpdate {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.OrderSequence) {
		pou.SetOrderSequence(input.OrderSequence)
	} else if forceUpdateMap["order_sequence"] {
		pou.ClearOrderSequence()
	}
	if !isZero(input.ProductID) {
		pou.SetProductID(input.ProductID)
	} else if forceUpdateMap["product_id"] {
		pou.SetProductID(input.ProductID)
	}
	if !isZero(input.OperationID) {
		pou.SetOperationID(input.OperationID)
	} else if forceUpdateMap["operation_id"] {
		pou.SetOperationID(input.OperationID)
	}
	return pou
}

func (rc *ReferralCreate) SetReferral(input *Referral) *ReferralCreate {
	if !isZero(input.Status) {
		rc.SetStatus(input.Status)
	}
	if !isZero(input.ReferredPersonID) {
		rc.SetReferredPersonID(input.ReferredPersonID)
	}
	if !isZero(input.Notes) {
		rc.SetNotes(input.Notes)
	}
	if !isZero(input.EntityType) {
		rc.SetEntityType(input.EntityType)
	}
	if !isZero(input.EntityID) {
		rc.SetEntityID(input.EntityID)
	}
	if !isZero(input.ReferrerRelationship) {
		rc.SetReferrerRelationship(input.ReferrerRelationship)
	}
	return rc
}

func (ruo *ReferralUpdateOne) SetReferral(input *Referral, forceUpdates ...string) *ReferralUpdateOne {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Status) {
		ruo.SetStatus(input.Status)
	} else if forceUpdateMap["status"] {
		ruo.SetStatus(input.Status)
	}
	if !isZero(input.ReferredPersonID) {
		ruo.SetReferredPersonID(input.ReferredPersonID)
	} else if forceUpdateMap["referred_person_id"] {
		ruo.SetReferredPersonID(input.ReferredPersonID)
	}
	if !isZero(input.Notes) {
		ruo.SetNotes(input.Notes)
	} else if forceUpdateMap["notes"] {
		ruo.ClearNotes()
	}
	if !isZero(input.EntityType) {
		ruo.SetEntityType(input.EntityType)
	} else if forceUpdateMap["entity_type"] {
		ruo.ClearEntityType()
	}
	if !isZero(input.EntityID) {
		ruo.SetEntityID(input.EntityID)
	} else if forceUpdateMap["entity_id"] {
		ruo.ClearEntityID()
	}
	if !isZero(input.ReferrerRelationship) {
		ruo.SetReferrerRelationship(input.ReferrerRelationship)
	} else if forceUpdateMap["referrer_relationship"] {
		ruo.ClearReferrerRelationship()
	}
	return ruo
}

func (ru *ReferralUpdate) SetReferral(input *Referral, forceUpdates ...string) *ReferralUpdate {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Status) {
		ru.SetStatus(input.Status)
	} else if forceUpdateMap["status"] {
		ru.SetStatus(input.Status)
	}
	if !isZero(input.ReferredPersonID) {
		ru.SetReferredPersonID(input.ReferredPersonID)
	} else if forceUpdateMap["referred_person_id"] {
		ru.SetReferredPersonID(input.ReferredPersonID)
	}
	if !isZero(input.Notes) {
		ru.SetNotes(input.Notes)
	} else if forceUpdateMap["notes"] {
		ru.ClearNotes()
	}
	if !isZero(input.EntityType) {
		ru.SetEntityType(input.EntityType)
	} else if forceUpdateMap["entity_type"] {
		ru.ClearEntityType()
	}
	if !isZero(input.EntityID) {
		ru.SetEntityID(input.EntityID)
	} else if forceUpdateMap["entity_id"] {
		ru.ClearEntityID()
	}
	if !isZero(input.ReferrerRelationship) {
		ru.SetReferrerRelationship(input.ReferrerRelationship)
	} else if forceUpdateMap["referrer_relationship"] {
		ru.ClearReferrerRelationship()
	}
	return ru
}

func (sc *ScheduleCreate) SetSchedule(input *Schedule) *ScheduleCreate {
	if !isZero(input.Status) {
		sc.SetStatus(input.Status)
	}
	if !isZero(input.UserID) {
		sc.SetUserID(input.UserID)
	}
	if !isZero(input.StartTime) {
		sc.SetStartTime(input.StartTime)
	}
	if !isZero(input.EndTime) {
		sc.SetEndTime(input.EndTime)
	}
	if !isZero(input.Stage) {
		sc.SetStage(input.Stage)
	}
	return sc
}

func (suo *ScheduleUpdateOne) SetSchedule(input *Schedule, forceUpdates ...string) *ScheduleUpdateOne {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Status) {
		suo.SetStatus(input.Status)
	} else if forceUpdateMap["status"] {
		suo.SetStatus(input.Status)
	}
	if !isZero(input.UserID) {
		suo.SetUserID(input.UserID)
	} else if forceUpdateMap["user_id"] {
		suo.ClearUserID()
	}
	if !isZero(input.StartTime) {
		suo.SetStartTime(input.StartTime)
	} else if forceUpdateMap["start_time"] {
		suo.ClearStartTime()
	}
	if !isZero(input.EndTime) {
		suo.SetEndTime(input.EndTime)
	} else if forceUpdateMap["end_time"] {
		suo.ClearEndTime()
	}
	if !isZero(input.Stage) {
		suo.SetStage(input.Stage)
	} else if forceUpdateMap["stage"] {
		suo.SetStage(input.Stage)
	}
	return suo
}

func (su *ScheduleUpdate) SetSchedule(input *Schedule, forceUpdates ...string) *ScheduleUpdate {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Status) {
		su.SetStatus(input.Status)
	} else if forceUpdateMap["status"] {
		su.SetStatus(input.Status)
	}
	if !isZero(input.UserID) {
		su.SetUserID(input.UserID)
	} else if forceUpdateMap["user_id"] {
		su.ClearUserID()
	}
	if !isZero(input.StartTime) {
		su.SetStartTime(input.StartTime)
	} else if forceUpdateMap["start_time"] {
		su.ClearStartTime()
	}
	if !isZero(input.EndTime) {
		su.SetEndTime(input.EndTime)
	} else if forceUpdateMap["end_time"] {
		su.ClearEndTime()
	}
	if !isZero(input.Stage) {
		su.SetStage(input.Stage)
	} else if forceUpdateMap["stage"] {
		su.SetStage(input.Stage)
	}
	return su
}

func (sc *SettingCreate) SetSetting(input *Setting) *SettingCreate {
	if !isZero(input.Status) {
		sc.SetStatus(input.Status)
	}
	if !isZero(input.Category) {
		sc.SetCategory(input.Category)
	}
	if !isZero(input.Name) {
		sc.SetName(input.Name)
	}
	if !isZero(input.Value) {
		sc.SetValue(input.Value)
	}
	if !isZero(input.Description) {
		sc.SetDescription(input.Description)
	}
	return sc
}

func (suo *SettingUpdateOne) SetSetting(input *Setting, forceUpdates ...string) *SettingUpdateOne {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Status) {
		suo.SetStatus(input.Status)
	} else if forceUpdateMap["status"] {
		suo.SetStatus(input.Status)
	}
	if !isZero(input.Category) {
		suo.SetCategory(input.Category)
	} else if forceUpdateMap["category"] {
		suo.SetCategory(input.Category)
	}
	if !isZero(input.Name) {
		suo.SetName(input.Name)
	} else if forceUpdateMap["name"] {
		suo.SetName(input.Name)
	}
	if !isZero(input.Value) {
		suo.SetValue(input.Value)
	} else if forceUpdateMap["value"] {
		suo.ClearValue()
	}
	if !isZero(input.Description) {
		suo.SetDescription(input.Description)
	} else if forceUpdateMap["description"] {
		suo.ClearDescription()
	}
	return suo
}

func (su *SettingUpdate) SetSetting(input *Setting, forceUpdates ...string) *SettingUpdate {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Status) {
		su.SetStatus(input.Status)
	} else if forceUpdateMap["status"] {
		su.SetStatus(input.Status)
	}
	if !isZero(input.Category) {
		su.SetCategory(input.Category)
	} else if forceUpdateMap["category"] {
		su.SetCategory(input.Category)
	}
	if !isZero(input.Name) {
		su.SetName(input.Name)
	} else if forceUpdateMap["name"] {
		su.SetName(input.Name)
	}
	if !isZero(input.Value) {
		su.SetValue(input.Value)
	} else if forceUpdateMap["value"] {
		su.ClearValue()
	}
	if !isZero(input.Description) {
		su.SetDescription(input.Description)
	} else if forceUpdateMap["description"] {
		su.ClearDescription()
	}
	return su
}

func (sc *StageCreate) SetStage(input *Stage) *StageCreate {
	if !isZero(input.Status) {
		sc.SetStatus(input.Status)
	}
	if !isZero(input.Name) {
		sc.SetName(input.Name)
	}
	if !isZero(input.PipelineID) {
		sc.SetPipelineID(input.PipelineID)
	}
	if !isZero(input.OrderNumber) {
		sc.SetOrderNumber(input.OrderNumber)
	}
	if input.ParentStageID != nil && !isZero(*input.ParentStageID) {
		sc.SetParentStageID(*input.ParentStageID)
	}
	if !isZero(input.Meta) {
		sc.SetMeta(input.Meta)
	}
	return sc
}

func (suo *StageUpdateOne) SetStage(input *Stage, forceUpdates ...string) *StageUpdateOne {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Status) {
		suo.SetStatus(input.Status)
	} else if forceUpdateMap["status"] {
		suo.SetStatus(input.Status)
	}
	if !isZero(input.Name) {
		suo.SetName(input.Name)
	} else if forceUpdateMap["name"] {
		suo.SetName(input.Name)
	}
	if !isZero(input.PipelineID) {
		suo.SetPipelineID(input.PipelineID)
	} else if forceUpdateMap["pipeline_id"] {
		suo.ClearPipelineID()
	}
	if !isZero(input.OrderNumber) {
		suo.SetOrderNumber(input.OrderNumber)
	} else if forceUpdateMap["order_number"] {
		suo.ClearOrderNumber()
	}
	if input.ParentStageID != nil && !isZero(*input.ParentStageID) {
		suo.SetParentStageID(*input.ParentStageID)
	} else if forceUpdateMap["parent_stage_id"] {
		suo.ClearParentStageID()
	}
	if !isZero(input.Meta) {
		suo.SetMeta(input.Meta)
	} else if forceUpdateMap["meta"] {
		suo.ClearMeta()
	}
	return suo
}

func (su *StageUpdate) SetStage(input *Stage, forceUpdates ...string) *StageUpdate {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Status) {
		su.SetStatus(input.Status)
	} else if forceUpdateMap["status"] {
		su.SetStatus(input.Status)
	}
	if !isZero(input.Name) {
		su.SetName(input.Name)
	} else if forceUpdateMap["name"] {
		su.SetName(input.Name)
	}
	if !isZero(input.PipelineID) {
		su.SetPipelineID(input.PipelineID)
	} else if forceUpdateMap["pipeline_id"] {
		su.ClearPipelineID()
	}
	if !isZero(input.OrderNumber) {
		su.SetOrderNumber(input.OrderNumber)
	} else if forceUpdateMap["order_number"] {
		su.ClearOrderNumber()
	}
	if input.ParentStageID != nil && !isZero(*input.ParentStageID) {
		su.SetParentStageID(*input.ParentStageID)
	} else if forceUpdateMap["parent_stage_id"] {
		su.ClearParentStageID()
	}
	if !isZero(input.Meta) {
		su.SetMeta(input.Meta)
	} else if forceUpdateMap["meta"] {
		su.ClearMeta()
	}
	return su
}

func (tc *TagCreate) SetTag(input *Tag) *TagCreate {
	if !isZero(input.Status) {
		tc.SetStatus(input.Status)
	}
	if !isZero(input.Name) {
		tc.SetName(input.Name)
	}
	if !isZero(input.Category) {
		tc.SetCategory(input.Category)
	}
	if !isZero(input.Description) {
		tc.SetDescription(input.Description)
	}
	if !isZero(input.UserID) {
		tc.SetUserID(input.UserID)
	}
	return tc
}

func (tuo *TagUpdateOne) SetTag(input *Tag, forceUpdates ...string) *TagUpdateOne {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Status) {
		tuo.SetStatus(input.Status)
	} else if forceUpdateMap["status"] {
		tuo.SetStatus(input.Status)
	}
	if !isZero(input.Name) {
		tuo.SetName(input.Name)
	} else if forceUpdateMap["name"] {
		tuo.SetName(input.Name)
	}
	if !isZero(input.Category) {
		tuo.SetCategory(input.Category)
	} else if forceUpdateMap["category"] {
		tuo.SetCategory(input.Category)
	}
	if !isZero(input.Description) {
		tuo.SetDescription(input.Description)
	} else if forceUpdateMap["description"] {
		tuo.ClearDescription()
	}
	if !isZero(input.UserID) {
		tuo.SetUserID(input.UserID)
	} else if forceUpdateMap["user_id"] {
		tuo.ClearUserID()
	}
	return tuo
}

func (tu *TagUpdate) SetTag(input *Tag, forceUpdates ...string) *TagUpdate {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Status) {
		tu.SetStatus(input.Status)
	} else if forceUpdateMap["status"] {
		tu.SetStatus(input.Status)
	}
	if !isZero(input.Name) {
		tu.SetName(input.Name)
	} else if forceUpdateMap["name"] {
		tu.SetName(input.Name)
	}
	if !isZero(input.Category) {
		tu.SetCategory(input.Category)
	} else if forceUpdateMap["category"] {
		tu.SetCategory(input.Category)
	}
	if !isZero(input.Description) {
		tu.SetDescription(input.Description)
	} else if forceUpdateMap["description"] {
		tu.ClearDescription()
	}
	if !isZero(input.UserID) {
		tu.SetUserID(input.UserID)
	} else if forceUpdateMap["user_id"] {
		tu.ClearUserID()
	}
	return tu
}

func (tdc *TagDealCreate) SetTagDeal(input *TagDeal) *TagDealCreate {
	if !isZero(input.DealID) {
		tdc.SetDealID(input.DealID)
	}
	if !isZero(input.TagID) {
		tdc.SetTagID(input.TagID)
	}
	if !isZero(input.AssignedBy) {
		tdc.SetAssignedBy(input.AssignedBy)
	}
	if !isZero(input.RemovedBy) {
		tdc.SetRemovedBy(input.RemovedBy)
	}
	return tdc
}

func (tduo *TagDealUpdateOne) SetTagDeal(input *TagDeal, forceUpdates ...string) *TagDealUpdateOne {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.DealID) {
		tduo.SetDealID(input.DealID)
	} else if forceUpdateMap["deal_id"] {
		tduo.SetDealID(input.DealID)
	}
	if !isZero(input.TagID) {
		tduo.SetTagID(input.TagID)
	} else if forceUpdateMap["tag_id"] {
		tduo.SetTagID(input.TagID)
	}
	if !isZero(input.AssignedBy) {
		tduo.SetAssignedBy(input.AssignedBy)
	} else if forceUpdateMap["assigned_by"] {
		tduo.ClearAssignedBy()
	}
	if !isZero(input.RemovedBy) {
		tduo.SetRemovedBy(input.RemovedBy)
	} else if forceUpdateMap["removed_by"] {
		tduo.ClearRemovedBy()
	}
	return tduo
}

func (tdu *TagDealUpdate) SetTagDeal(input *TagDeal, forceUpdates ...string) *TagDealUpdate {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.DealID) {
		tdu.SetDealID(input.DealID)
	} else if forceUpdateMap["deal_id"] {
		tdu.SetDealID(input.DealID)
	}
	if !isZero(input.TagID) {
		tdu.SetTagID(input.TagID)
	} else if forceUpdateMap["tag_id"] {
		tdu.SetTagID(input.TagID)
	}
	if !isZero(input.AssignedBy) {
		tdu.SetAssignedBy(input.AssignedBy)
	} else if forceUpdateMap["assigned_by"] {
		tdu.ClearAssignedBy()
	}
	if !isZero(input.RemovedBy) {
		tdu.SetRemovedBy(input.RemovedBy)
	} else if forceUpdateMap["removed_by"] {
		tdu.ClearRemovedBy()
	}
	return tdu
}

func (tpc *TagPersonCreate) SetTagPerson(input *TagPerson) *TagPersonCreate {
	if !isZero(input.PersonID) {
		tpc.SetPersonID(input.PersonID)
	}
	if !isZero(input.TagID) {
		tpc.SetTagID(input.TagID)
	}
	if !isZero(input.AssignedBy) {
		tpc.SetAssignedBy(input.AssignedBy)
	}
	if !isZero(input.RemovedBy) {
		tpc.SetRemovedBy(input.RemovedBy)
	}
	return tpc
}

func (tpuo *TagPersonUpdateOne) SetTagPerson(input *TagPerson, forceUpdates ...string) *TagPersonUpdateOne {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.PersonID) {
		tpuo.SetPersonID(input.PersonID)
	} else if forceUpdateMap["person_id"] {
		tpuo.SetPersonID(input.PersonID)
	}
	if !isZero(input.TagID) {
		tpuo.SetTagID(input.TagID)
	} else if forceUpdateMap["tag_id"] {
		tpuo.SetTagID(input.TagID)
	}
	if !isZero(input.AssignedBy) {
		tpuo.SetAssignedBy(input.AssignedBy)
	} else if forceUpdateMap["assigned_by"] {
		tpuo.ClearAssignedBy()
	}
	if !isZero(input.RemovedBy) {
		tpuo.SetRemovedBy(input.RemovedBy)
	} else if forceUpdateMap["removed_by"] {
		tpuo.ClearRemovedBy()
	}
	return tpuo
}

func (tpu *TagPersonUpdate) SetTagPerson(input *TagPerson, forceUpdates ...string) *TagPersonUpdate {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.PersonID) {
		tpu.SetPersonID(input.PersonID)
	} else if forceUpdateMap["person_id"] {
		tpu.SetPersonID(input.PersonID)
	}
	if !isZero(input.TagID) {
		tpu.SetTagID(input.TagID)
	} else if forceUpdateMap["tag_id"] {
		tpu.SetTagID(input.TagID)
	}
	if !isZero(input.AssignedBy) {
		tpu.SetAssignedBy(input.AssignedBy)
	} else if forceUpdateMap["assigned_by"] {
		tpu.ClearAssignedBy()
	}
	if !isZero(input.RemovedBy) {
		tpu.SetRemovedBy(input.RemovedBy)
	} else if forceUpdateMap["removed_by"] {
		tpu.ClearRemovedBy()
	}
	return tpu
}

func (tc *TaskCreate) SetTask(input *Task) *TaskCreate {
	if !isZero(input.Status) {
		tc.SetStatus(input.Status)
	}
	if !isZero(input.Title) {
		tc.SetTitle(input.Title)
	}
	if !isZero(input.Note) {
		tc.SetNote(input.Note)
	}
	if !isZero(input.StartDate) {
		tc.SetStartDate(input.StartDate)
	}
	if input.DueDate != nil && !isZero(*input.DueDate) {
		tc.SetDueDate(*input.DueDate)
	}
	if input.EndDate != nil && !isZero(*input.EndDate) {
		tc.SetEndDate(*input.EndDate)
	}
	if !isZero(input.Type) {
		tc.SetType(input.Type)
	}
	if !isZero(input.Priority) {
		tc.SetPriority(input.Priority)
	}
	if !isZero(input.CurrentSerial) {
		tc.SetCurrentSerial(input.CurrentSerial)
	}
	if input.ParentID != nil && !isZero(*input.ParentID) {
		tc.SetParentID(*input.ParentID)
	}
	if input.PersonID != nil && !isZero(*input.PersonID) {
		tc.SetPersonID(*input.PersonID)
	}
	if input.DealID != nil && !isZero(*input.DealID) {
		tc.SetDealID(*input.DealID)
	}
	if input.AppointmentID != nil && !isZero(*input.AppointmentID) {
		tc.SetAppointmentID(*input.AppointmentID)
	}
	if input.DepartmentID != nil && !isZero(*input.DepartmentID) {
		tc.SetDepartmentID(*input.DepartmentID)
	}
	if !isZero(input.CreatorID) {
		tc.SetCreatorID(input.CreatorID)
	}
	if !isZero(input.State) {
		tc.SetState(input.State)
	}
	if input.CompletedAt != nil && !isZero(*input.CompletedAt) {
		tc.SetCompletedAt(*input.CompletedAt)
	}
	if !isZero(input.History) {
		tc.SetHistory(input.History)
	}
	return tc
}

func (tuo *TaskUpdateOne) SetTask(input *Task, forceUpdates ...string) *TaskUpdateOne {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Status) {
		tuo.SetStatus(input.Status)
	} else if forceUpdateMap["status"] {
		tuo.SetStatus(input.Status)
	}
	if !isZero(input.Title) {
		tuo.SetTitle(input.Title)
	} else if forceUpdateMap["title"] {
		tuo.SetTitle(input.Title)
	}
	if !isZero(input.Note) {
		tuo.SetNote(input.Note)
	} else if forceUpdateMap["note"] {
		tuo.ClearNote()
	}
	if !isZero(input.StartDate) {
		tuo.SetStartDate(input.StartDate)
	} else if forceUpdateMap["start_date"] {
		tuo.ClearStartDate()
	}
	if input.DueDate != nil && !isZero(*input.DueDate) {
		tuo.SetDueDate(*input.DueDate)
	} else if forceUpdateMap["due_date"] {
		tuo.ClearDueDate()
	}
	if input.EndDate != nil && !isZero(*input.EndDate) {
		tuo.SetEndDate(*input.EndDate)
	} else if forceUpdateMap["end_date"] {
		tuo.ClearEndDate()
	}
	if !isZero(input.Type) {
		tuo.SetType(input.Type)
	} else if forceUpdateMap["type"] {
		tuo.ClearType()
	}
	if !isZero(input.Priority) {
		tuo.SetPriority(input.Priority)
	} else if forceUpdateMap["priority"] {
		tuo.ClearPriority()
	}
	if !isZero(input.CurrentSerial) {
		tuo.SetCurrentSerial(input.CurrentSerial)
	} else if forceUpdateMap["current_serial"] {
		tuo.SetCurrentSerial(input.CurrentSerial)
	}
	if input.ParentID != nil && !isZero(*input.ParentID) {
		tuo.SetParentID(*input.ParentID)
	} else if forceUpdateMap["parent_id"] {
		tuo.ClearParentID()
	}
	if input.PersonID != nil && !isZero(*input.PersonID) {
		tuo.SetPersonID(*input.PersonID)
	} else if forceUpdateMap["person_id"] {
		tuo.ClearPersonID()
	}
	if input.DealID != nil && !isZero(*input.DealID) {
		tuo.SetDealID(*input.DealID)
	} else if forceUpdateMap["deal_id"] {
		tuo.ClearDealID()
	}
	if input.AppointmentID != nil && !isZero(*input.AppointmentID) {
		tuo.SetAppointmentID(*input.AppointmentID)
	} else if forceUpdateMap["appointment_id"] {
		tuo.ClearAppointmentID()
	}
	if input.DepartmentID != nil && !isZero(*input.DepartmentID) {
		tuo.SetDepartmentID(*input.DepartmentID)
	} else if forceUpdateMap["department_id"] {
		tuo.ClearDepartmentID()
	}
	if !isZero(input.CreatorID) {
		tuo.SetCreatorID(input.CreatorID)
	} else if forceUpdateMap["creator_id"] {
		tuo.ClearCreatorID()
	}
	if !isZero(input.State) {
		tuo.SetState(input.State)
	} else if forceUpdateMap["state"] {
		tuo.ClearState()
	}
	if input.CompletedAt != nil && !isZero(*input.CompletedAt) {
		tuo.SetCompletedAt(*input.CompletedAt)
	} else if forceUpdateMap["completed_at"] {
		tuo.ClearCompletedAt()
	}
	if !isZero(input.History) {
		tuo.SetHistory(input.History)
	} else if forceUpdateMap["history"] {
		tuo.ClearHistory()
	}
	return tuo
}

func (tu *TaskUpdate) SetTask(input *Task, forceUpdates ...string) *TaskUpdate {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Status) {
		tu.SetStatus(input.Status)
	} else if forceUpdateMap["status"] {
		tu.SetStatus(input.Status)
	}
	if !isZero(input.Title) {
		tu.SetTitle(input.Title)
	} else if forceUpdateMap["title"] {
		tu.SetTitle(input.Title)
	}
	if !isZero(input.Note) {
		tu.SetNote(input.Note)
	} else if forceUpdateMap["note"] {
		tu.ClearNote()
	}
	if !isZero(input.StartDate) {
		tu.SetStartDate(input.StartDate)
	} else if forceUpdateMap["start_date"] {
		tu.ClearStartDate()
	}
	if input.DueDate != nil && !isZero(*input.DueDate) {
		tu.SetDueDate(*input.DueDate)
	} else if forceUpdateMap["due_date"] {
		tu.ClearDueDate()
	}
	if input.EndDate != nil && !isZero(*input.EndDate) {
		tu.SetEndDate(*input.EndDate)
	} else if forceUpdateMap["end_date"] {
		tu.ClearEndDate()
	}
	if !isZero(input.Type) {
		tu.SetType(input.Type)
	} else if forceUpdateMap["type"] {
		tu.ClearType()
	}
	if !isZero(input.Priority) {
		tu.SetPriority(input.Priority)
	} else if forceUpdateMap["priority"] {
		tu.ClearPriority()
	}
	if !isZero(input.CurrentSerial) {
		tu.SetCurrentSerial(input.CurrentSerial)
	} else if forceUpdateMap["current_serial"] {
		tu.SetCurrentSerial(input.CurrentSerial)
	}
	if input.ParentID != nil && !isZero(*input.ParentID) {
		tu.SetParentID(*input.ParentID)
	} else if forceUpdateMap["parent_id"] {
		tu.ClearParentID()
	}
	if input.PersonID != nil && !isZero(*input.PersonID) {
		tu.SetPersonID(*input.PersonID)
	} else if forceUpdateMap["person_id"] {
		tu.ClearPersonID()
	}
	if input.DealID != nil && !isZero(*input.DealID) {
		tu.SetDealID(*input.DealID)
	} else if forceUpdateMap["deal_id"] {
		tu.ClearDealID()
	}
	if input.AppointmentID != nil && !isZero(*input.AppointmentID) {
		tu.SetAppointmentID(*input.AppointmentID)
	} else if forceUpdateMap["appointment_id"] {
		tu.ClearAppointmentID()
	}
	if input.DepartmentID != nil && !isZero(*input.DepartmentID) {
		tu.SetDepartmentID(*input.DepartmentID)
	} else if forceUpdateMap["department_id"] {
		tu.ClearDepartmentID()
	}
	if !isZero(input.CreatorID) {
		tu.SetCreatorID(input.CreatorID)
	} else if forceUpdateMap["creator_id"] {
		tu.ClearCreatorID()
	}
	if !isZero(input.State) {
		tu.SetState(input.State)
	} else if forceUpdateMap["state"] {
		tu.ClearState()
	}
	if input.CompletedAt != nil && !isZero(*input.CompletedAt) {
		tu.SetCompletedAt(*input.CompletedAt)
	} else if forceUpdateMap["completed_at"] {
		tu.ClearCompletedAt()
	}
	if !isZero(input.History) {
		tu.SetHistory(input.History)
	} else if forceUpdateMap["history"] {
		tu.ClearHistory()
	}
	return tu
}

func (tac *TaskAssignmentCreate) SetTaskAssignment(input *TaskAssignment) *TaskAssignmentCreate {
	if !isZero(input.Status) {
		tac.SetStatus(input.Status)
	}
	if !isZero(input.TaskID) {
		tac.SetTaskID(input.TaskID)
	}
	if !isZero(input.Serial) {
		tac.SetSerial(input.Serial)
	}
	if !isZero(input.UserID) {
		tac.SetUserID(input.UserID)
	}
	if !isZero(input.DueAt) {
		tac.SetDueAt(input.DueAt)
	}
	if !isZero(input.StartedAt) {
		tac.SetStartedAt(input.StartedAt)
	}
	if input.CompletedAt != nil && !isZero(*input.CompletedAt) {
		tac.SetCompletedAt(*input.CompletedAt)
	}
	if !isZero(input.State) {
		tac.SetState(input.State)
	}
	if !isZero(input.Role) {
		tac.SetRole(input.Role)
	}
	return tac
}

func (tauo *TaskAssignmentUpdateOne) SetTaskAssignment(input *TaskAssignment, forceUpdates ...string) *TaskAssignmentUpdateOne {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Status) {
		tauo.SetStatus(input.Status)
	} else if forceUpdateMap["status"] {
		tauo.SetStatus(input.Status)
	}
	if !isZero(input.TaskID) {
		tauo.SetTaskID(input.TaskID)
	} else if forceUpdateMap["task_id"] {
		tauo.ClearTaskID()
	}
	if !isZero(input.UserID) {
		tauo.SetUserID(input.UserID)
	} else if forceUpdateMap["user_id"] {
		tauo.ClearUserID()
	}
	if !isZero(input.DueAt) {
		tauo.SetDueAt(input.DueAt)
	} else if forceUpdateMap["due_at"] {
		tauo.SetDueAt(input.DueAt)
	}
	if !isZero(input.StartedAt) {
		tauo.SetStartedAt(input.StartedAt)
	} else if forceUpdateMap["started_at"] {
		tauo.ClearStartedAt()
	}
	if input.CompletedAt != nil && !isZero(*input.CompletedAt) {
		tauo.SetCompletedAt(*input.CompletedAt)
	} else if forceUpdateMap["completed_at"] {
		tauo.ClearCompletedAt()
	}
	if !isZero(input.State) {
		tauo.SetState(input.State)
	} else if forceUpdateMap["state"] {
		tauo.SetState(input.State)
	}
	if !isZero(input.Role) {
		tauo.SetRole(input.Role)
	} else if forceUpdateMap["role"] {
		tauo.ClearRole()
	}
	return tauo
}

func (tau *TaskAssignmentUpdate) SetTaskAssignment(input *TaskAssignment, forceUpdates ...string) *TaskAssignmentUpdate {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Status) {
		tau.SetStatus(input.Status)
	} else if forceUpdateMap["status"] {
		tau.SetStatus(input.Status)
	}
	if !isZero(input.TaskID) {
		tau.SetTaskID(input.TaskID)
	} else if forceUpdateMap["task_id"] {
		tau.ClearTaskID()
	}
	if !isZero(input.UserID) {
		tau.SetUserID(input.UserID)
	} else if forceUpdateMap["user_id"] {
		tau.ClearUserID()
	}
	if !isZero(input.DueAt) {
		tau.SetDueAt(input.DueAt)
	} else if forceUpdateMap["due_at"] {
		tau.SetDueAt(input.DueAt)
	}
	if !isZero(input.StartedAt) {
		tau.SetStartedAt(input.StartedAt)
	} else if forceUpdateMap["started_at"] {
		tau.ClearStartedAt()
	}
	if input.CompletedAt != nil && !isZero(*input.CompletedAt) {
		tau.SetCompletedAt(*input.CompletedAt)
	} else if forceUpdateMap["completed_at"] {
		tau.ClearCompletedAt()
	}
	if !isZero(input.State) {
		tau.SetState(input.State)
	} else if forceUpdateMap["state"] {
		tau.SetState(input.State)
	}
	if !isZero(input.Role) {
		tau.SetRole(input.Role)
	} else if forceUpdateMap["role"] {
		tau.ClearRole()
	}
	return tau
}

func (tdc *TaskDepartmentCreate) SetTaskDepartment(input *TaskDepartment) *TaskDepartmentCreate {
	if !isZero(input.Status) {
		tdc.SetStatus(input.Status)
	}
	if !isZero(input.TaskID) {
		tdc.SetTaskID(input.TaskID)
	}
	if !isZero(input.Serial) {
		tdc.SetSerial(input.Serial)
	}
	if !isZero(input.DepartmentID) {
		tdc.SetDepartmentID(input.DepartmentID)
	}
	if input.CompleteBy != nil && !isZero(*input.CompleteBy) {
		tdc.SetCompleteBy(*input.CompleteBy)
	}
	if !isZero(input.DueAt) {
		tdc.SetDueAt(input.DueAt)
	}
	if !isZero(input.StartedAt) {
		tdc.SetStartedAt(input.StartedAt)
	}
	if input.CompletedAt != nil && !isZero(*input.CompletedAt) {
		tdc.SetCompletedAt(*input.CompletedAt)
	}
	if !isZero(input.State) {
		tdc.SetState(input.State)
	}
	if !isZero(input.Role) {
		tdc.SetRole(input.Role)
	}
	return tdc
}

func (tduo *TaskDepartmentUpdateOne) SetTaskDepartment(input *TaskDepartment, forceUpdates ...string) *TaskDepartmentUpdateOne {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Status) {
		tduo.SetStatus(input.Status)
	} else if forceUpdateMap["status"] {
		tduo.SetStatus(input.Status)
	}
	if !isZero(input.TaskID) {
		tduo.SetTaskID(input.TaskID)
	} else if forceUpdateMap["task_id"] {
		tduo.ClearTaskID()
	}
	if !isZero(input.DepartmentID) {
		tduo.SetDepartmentID(input.DepartmentID)
	} else if forceUpdateMap["department_id"] {
		tduo.ClearDepartmentID()
	}
	if input.CompleteBy != nil && !isZero(*input.CompleteBy) {
		tduo.SetCompleteBy(*input.CompleteBy)
	} else if forceUpdateMap["complete_by"] {
		tduo.ClearCompleteBy()
	}
	if !isZero(input.DueAt) {
		tduo.SetDueAt(input.DueAt)
	} else if forceUpdateMap["due_at"] {
		tduo.SetDueAt(input.DueAt)
	}
	if !isZero(input.StartedAt) {
		tduo.SetStartedAt(input.StartedAt)
	} else if forceUpdateMap["started_at"] {
		tduo.ClearStartedAt()
	}
	if input.CompletedAt != nil && !isZero(*input.CompletedAt) {
		tduo.SetCompletedAt(*input.CompletedAt)
	} else if forceUpdateMap["completed_at"] {
		tduo.ClearCompletedAt()
	}
	if !isZero(input.State) {
		tduo.SetState(input.State)
	} else if forceUpdateMap["state"] {
		tduo.SetState(input.State)
	}
	if !isZero(input.Role) {
		tduo.SetRole(input.Role)
	} else if forceUpdateMap["role"] {
		tduo.ClearRole()
	}
	return tduo
}

func (tdu *TaskDepartmentUpdate) SetTaskDepartment(input *TaskDepartment, forceUpdates ...string) *TaskDepartmentUpdate {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Status) {
		tdu.SetStatus(input.Status)
	} else if forceUpdateMap["status"] {
		tdu.SetStatus(input.Status)
	}
	if !isZero(input.TaskID) {
		tdu.SetTaskID(input.TaskID)
	} else if forceUpdateMap["task_id"] {
		tdu.ClearTaskID()
	}
	if !isZero(input.DepartmentID) {
		tdu.SetDepartmentID(input.DepartmentID)
	} else if forceUpdateMap["department_id"] {
		tdu.ClearDepartmentID()
	}
	if input.CompleteBy != nil && !isZero(*input.CompleteBy) {
		tdu.SetCompleteBy(*input.CompleteBy)
	} else if forceUpdateMap["complete_by"] {
		tdu.ClearCompleteBy()
	}
	if !isZero(input.DueAt) {
		tdu.SetDueAt(input.DueAt)
	} else if forceUpdateMap["due_at"] {
		tdu.SetDueAt(input.DueAt)
	}
	if !isZero(input.StartedAt) {
		tdu.SetStartedAt(input.StartedAt)
	} else if forceUpdateMap["started_at"] {
		tdu.ClearStartedAt()
	}
	if input.CompletedAt != nil && !isZero(*input.CompletedAt) {
		tdu.SetCompletedAt(*input.CompletedAt)
	} else if forceUpdateMap["completed_at"] {
		tdu.ClearCompletedAt()
	}
	if !isZero(input.State) {
		tdu.SetState(input.State)
	} else if forceUpdateMap["state"] {
		tdu.SetState(input.State)
	}
	if !isZero(input.Role) {
		tdu.SetRole(input.Role)
	} else if forceUpdateMap["role"] {
		tdu.ClearRole()
	}
	return tdu
}

func (thc *TaskHistoryCreate) SetTaskHistory(input *TaskHistory) *TaskHistoryCreate {
	if !isZero(input.TaskID) {
		thc.SetTaskID(input.TaskID)
	}
	if !isZero(input.Before) {
		thc.SetBefore(input.Before)
	}
	if !isZero(input.After) {
		thc.SetAfter(input.After)
	}
	if !isZero(input.Operation) {
		thc.SetOperation(input.Operation)
	}
	if !isZero(input.Related) {
		thc.SetRelated(input.Related)
	}
	if !isZero(input.RelatedID) {
		thc.SetRelatedID(input.RelatedID)
	}
	if !isZero(input.ChangedAt) {
		thc.SetChangedAt(input.ChangedAt)
	}
	if !isZero(input.UserID) {
		thc.SetUserID(input.UserID)
	}
	return thc
}

func (thuo *TaskHistoryUpdateOne) SetTaskHistory(input *TaskHistory, forceUpdates ...string) *TaskHistoryUpdateOne {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.TaskID) {
		thuo.SetTaskID(input.TaskID)
	} else if forceUpdateMap["task_id"] {
		thuo.ClearTaskID()
	}
	if !isZero(input.Before) {
		thuo.SetBefore(input.Before)
	} else if forceUpdateMap["before"] {
		thuo.ClearBefore()
	}
	if !isZero(input.After) {
		thuo.SetAfter(input.After)
	} else if forceUpdateMap["after"] {
		thuo.ClearAfter()
	}
	if !isZero(input.Operation) {
		thuo.SetOperation(input.Operation)
	} else if forceUpdateMap["operation"] {
		thuo.SetOperation(input.Operation)
	}
	if !isZero(input.Related) {
		thuo.SetRelated(input.Related)
	} else if forceUpdateMap["related"] {
		thuo.ClearRelated()
	}
	if !isZero(input.RelatedID) {
		thuo.SetRelatedID(input.RelatedID)
	} else if forceUpdateMap["related_id"] {
		thuo.ClearRelatedID()
	}
	if !isZero(input.UserID) {
		thuo.SetUserID(input.UserID)
	} else if forceUpdateMap["user_id"] {
		thuo.ClearUserID()
	}
	return thuo
}

func (thu *TaskHistoryUpdate) SetTaskHistory(input *TaskHistory, forceUpdates ...string) *TaskHistoryUpdate {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.TaskID) {
		thu.SetTaskID(input.TaskID)
	} else if forceUpdateMap["task_id"] {
		thu.ClearTaskID()
	}
	if !isZero(input.Before) {
		thu.SetBefore(input.Before)
	} else if forceUpdateMap["before"] {
		thu.ClearBefore()
	}
	if !isZero(input.After) {
		thu.SetAfter(input.After)
	} else if forceUpdateMap["after"] {
		thu.ClearAfter()
	}
	if !isZero(input.Operation) {
		thu.SetOperation(input.Operation)
	} else if forceUpdateMap["operation"] {
		thu.SetOperation(input.Operation)
	}
	if !isZero(input.Related) {
		thu.SetRelated(input.Related)
	} else if forceUpdateMap["related"] {
		thu.ClearRelated()
	}
	if !isZero(input.RelatedID) {
		thu.SetRelatedID(input.RelatedID)
	} else if forceUpdateMap["related_id"] {
		thu.ClearRelatedID()
	}
	if !isZero(input.UserID) {
		thu.SetUserID(input.UserID)
	} else if forceUpdateMap["user_id"] {
		thu.ClearUserID()
	}
	return thu
}

func (tnc *TaskNoteCreate) SetTaskNote(input *TaskNote) *TaskNoteCreate {
	if !isZero(input.Status) {
		tnc.SetStatus(input.Status)
	}
	if !isZero(input.TaskID) {
		tnc.SetTaskID(input.TaskID)
	}
	if !isZero(input.UserID) {
		tnc.SetUserID(input.UserID)
	}
	if !isZero(input.Body) {
		tnc.SetBody(input.Body)
	}
	return tnc
}

func (tnuo *TaskNoteUpdateOne) SetTaskNote(input *TaskNote, forceUpdates ...string) *TaskNoteUpdateOne {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Status) {
		tnuo.SetStatus(input.Status)
	} else if forceUpdateMap["status"] {
		tnuo.SetStatus(input.Status)
	}
	if !isZero(input.TaskID) {
		tnuo.SetTaskID(input.TaskID)
	} else if forceUpdateMap["task_id"] {
		tnuo.ClearTaskID()
	}
	if !isZero(input.UserID) {
		tnuo.SetUserID(input.UserID)
	} else if forceUpdateMap["user_id"] {
		tnuo.ClearUserID()
	}
	if !isZero(input.Body) {
		tnuo.SetBody(input.Body)
	} else if forceUpdateMap["body"] {
		tnuo.ClearBody()
	}
	return tnuo
}

func (tnu *TaskNoteUpdate) SetTaskNote(input *TaskNote, forceUpdates ...string) *TaskNoteUpdate {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Status) {
		tnu.SetStatus(input.Status)
	} else if forceUpdateMap["status"] {
		tnu.SetStatus(input.Status)
	}
	if !isZero(input.TaskID) {
		tnu.SetTaskID(input.TaskID)
	} else if forceUpdateMap["task_id"] {
		tnu.ClearTaskID()
	}
	if !isZero(input.UserID) {
		tnu.SetUserID(input.UserID)
	} else if forceUpdateMap["user_id"] {
		tnu.ClearUserID()
	}
	if !isZero(input.Body) {
		tnu.SetBody(input.Body)
	} else if forceUpdateMap["body"] {
		tnu.ClearBody()
	}
	return tnu
}

func (trc *TaskRecurringCreate) SetTaskRecurring(input *TaskRecurring) *TaskRecurringCreate {
	if !isZero(input.TaskID) {
		trc.SetTaskID(input.TaskID)
	}
	if !isZero(input.CronExpression) {
		trc.SetCronExpression(input.CronExpression)
	}
	if input.NextOccurrence != nil && !isZero(*input.NextOccurrence) {
		trc.SetNextOccurrence(*input.NextOccurrence)
	}
	if input.LastOccurrence != nil && !isZero(*input.LastOccurrence) {
		trc.SetLastOccurrence(*input.LastOccurrence)
	}
	return trc
}

func (truo *TaskRecurringUpdateOne) SetTaskRecurring(input *TaskRecurring, forceUpdates ...string) *TaskRecurringUpdateOne {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.TaskID) {
		truo.SetTaskID(input.TaskID)
	} else if forceUpdateMap["task_id"] {
		truo.SetTaskID(input.TaskID)
	}
	if !isZero(input.CronExpression) {
		truo.SetCronExpression(input.CronExpression)
	} else if forceUpdateMap["cron_expression"] {
		truo.SetCronExpression(input.CronExpression)
	}
	if input.NextOccurrence != nil && !isZero(*input.NextOccurrence) {
		truo.SetNextOccurrence(*input.NextOccurrence)
	} else if forceUpdateMap["next_occurrence"] {
		truo.ClearNextOccurrence()
	}
	if input.LastOccurrence != nil && !isZero(*input.LastOccurrence) {
		truo.SetLastOccurrence(*input.LastOccurrence)
	} else if forceUpdateMap["last_occurrence"] {
		truo.ClearLastOccurrence()
	}
	return truo
}

func (tru *TaskRecurringUpdate) SetTaskRecurring(input *TaskRecurring, forceUpdates ...string) *TaskRecurringUpdate {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.TaskID) {
		tru.SetTaskID(input.TaskID)
	} else if forceUpdateMap["task_id"] {
		tru.SetTaskID(input.TaskID)
	}
	if !isZero(input.CronExpression) {
		tru.SetCronExpression(input.CronExpression)
	} else if forceUpdateMap["cron_expression"] {
		tru.SetCronExpression(input.CronExpression)
	}
	if input.NextOccurrence != nil && !isZero(*input.NextOccurrence) {
		tru.SetNextOccurrence(*input.NextOccurrence)
	} else if forceUpdateMap["next_occurrence"] {
		tru.ClearNextOccurrence()
	}
	if input.LastOccurrence != nil && !isZero(*input.LastOccurrence) {
		tru.SetLastOccurrence(*input.LastOccurrence)
	} else if forceUpdateMap["last_occurrence"] {
		tru.ClearLastOccurrence()
	}
	return tru
}

func (tc *TermCreate) SetTerm(input *Term) *TermCreate {
	if !isZero(input.Status) {
		tc.SetStatus(input.Status)
	}
	if !isZero(input.Name) {
		tc.SetName(input.Name)
	}
	if !isZero(input.Bundle) {
		tc.SetBundle(input.Bundle)
	}
	if !isZero(input.Description) {
		tc.SetDescription(input.Description)
	}
	if !isZero(input.Body) {
		tc.SetBody(input.Body)
	}
	if !isZero(input.Weight) {
		tc.SetWeight(input.Weight)
	}
	return tc
}

func (tuo *TermUpdateOne) SetTerm(input *Term, forceUpdates ...string) *TermUpdateOne {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Status) {
		tuo.SetStatus(input.Status)
	} else if forceUpdateMap["status"] {
		tuo.SetStatus(input.Status)
	}
	if !isZero(input.Name) {
		tuo.SetName(input.Name)
	} else if forceUpdateMap["name"] {
		tuo.SetName(input.Name)
	}
	if !isZero(input.Bundle) {
		tuo.SetBundle(input.Bundle)
	} else if forceUpdateMap["bundle"] {
		tuo.SetBundle(input.Bundle)
	}
	if !isZero(input.Description) {
		tuo.SetDescription(input.Description)
	} else if forceUpdateMap["description"] {
		tuo.ClearDescription()
	}
	if !isZero(input.Body) {
		tuo.SetBody(input.Body)
	} else if forceUpdateMap["body"] {
		tuo.ClearBody()
	}
	if !isZero(input.Weight) {
		tuo.SetWeight(input.Weight)
	} else if forceUpdateMap["weight"] {
		tuo.SetWeight(input.Weight)
	}
	return tuo
}

func (tu *TermUpdate) SetTerm(input *Term, forceUpdates ...string) *TermUpdate {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Status) {
		tu.SetStatus(input.Status)
	} else if forceUpdateMap["status"] {
		tu.SetStatus(input.Status)
	}
	if !isZero(input.Name) {
		tu.SetName(input.Name)
	} else if forceUpdateMap["name"] {
		tu.SetName(input.Name)
	}
	if !isZero(input.Bundle) {
		tu.SetBundle(input.Bundle)
	} else if forceUpdateMap["bundle"] {
		tu.SetBundle(input.Bundle)
	}
	if !isZero(input.Description) {
		tu.SetDescription(input.Description)
	} else if forceUpdateMap["description"] {
		tu.ClearDescription()
	}
	if !isZero(input.Body) {
		tu.SetBody(input.Body)
	} else if forceUpdateMap["body"] {
		tu.ClearBody()
	}
	if !isZero(input.Weight) {
		tu.SetWeight(input.Weight)
	} else if forceUpdateMap["weight"] {
		tu.SetWeight(input.Weight)
	}
	return tu
}

func (tc *TrackCreate) SetTrack(input *Track) *TrackCreate {
	if !isZero(input.Status) {
		tc.SetStatus(input.Status)
	}
	if !isZero(input.Begin) {
		tc.SetBegin(input.Begin)
	}
	if !isZero(input.End) {
		tc.SetEnd(input.End)
	}
	if input.DealID != nil && !isZero(*input.DealID) {
		tc.SetDealID(*input.DealID)
	}
	if input.PipelineID != nil && !isZero(*input.PipelineID) {
		tc.SetPipelineID(*input.PipelineID)
	}
	if input.StageID != nil && !isZero(*input.StageID) {
		tc.SetStageID(*input.StageID)
	}
	if !isZero(input.UserID) {
		tc.SetUserID(input.UserID)
	}
	if !isZero(input.PersonID) {
		tc.SetPersonID(input.PersonID)
	}
	if !isZero(input.Weight) {
		tc.SetWeight(input.Weight)
	}
	if !isZero(input.Meta) {
		tc.SetMeta(input.Meta)
	}
	if !isZero(input.State) {
		tc.SetState(input.State)
	}
	if !isZero(input.DealStageID) {
		tc.SetDealStageID(input.DealStageID)
	}
	if !isZero(input.StageHistory) {
		tc.SetStageHistory(input.StageHistory)
	}
	return tc
}

func (tuo *TrackUpdateOne) SetTrack(input *Track, forceUpdates ...string) *TrackUpdateOne {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Status) {
		tuo.SetStatus(input.Status)
	} else if forceUpdateMap["status"] {
		tuo.SetStatus(input.Status)
	}
	if !isZero(input.Begin) {
		tuo.SetBegin(input.Begin)
	} else if forceUpdateMap["begin"] {
		tuo.ClearBegin()
	}
	if !isZero(input.End) {
		tuo.SetEnd(input.End)
	} else if forceUpdateMap["end"] {
		tuo.ClearEnd()
	}
	if input.DealID != nil && !isZero(*input.DealID) {
		tuo.SetDealID(*input.DealID)
	} else if forceUpdateMap["deal_id"] {
		tuo.ClearDealID()
	}
	if input.PipelineID != nil && !isZero(*input.PipelineID) {
		tuo.SetPipelineID(*input.PipelineID)
	} else if forceUpdateMap["pipeline_id"] {
		tuo.ClearPipelineID()
	}
	if input.StageID != nil && !isZero(*input.StageID) {
		tuo.SetStageID(*input.StageID)
	} else if forceUpdateMap["stage_id"] {
		tuo.ClearStageID()
	}
	if !isZero(input.UserID) {
		tuo.SetUserID(input.UserID)
	} else if forceUpdateMap["user_id"] {
		tuo.ClearUserID()
	}
	if !isZero(input.PersonID) {
		tuo.SetPersonID(input.PersonID)
	} else if forceUpdateMap["person_id"] {
		tuo.ClearPersonID()
	}
	if !isZero(input.Weight) {
		tuo.SetWeight(input.Weight)
	} else if forceUpdateMap["weight"] {
		tuo.ClearWeight()
	}
	if !isZero(input.Meta) {
		tuo.SetMeta(input.Meta)
	} else if forceUpdateMap["meta"] {
		tuo.ClearMeta()
	}
	if !isZero(input.State) {
		tuo.SetState(input.State)
	} else if forceUpdateMap["state"] {
		tuo.ClearState()
	}
	if !isZero(input.DealStageID) {
		tuo.SetDealStageID(input.DealStageID)
	} else if forceUpdateMap["deal_stage_id"] {
		tuo.ClearDealStageID()
	}
	if !isZero(input.StageHistory) {
		tuo.SetStageHistory(input.StageHistory)
	} else if forceUpdateMap["stage_history"] {
		tuo.ClearStageHistory()
	}
	return tuo
}

func (tu *TrackUpdate) SetTrack(input *Track, forceUpdates ...string) *TrackUpdate {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Status) {
		tu.SetStatus(input.Status)
	} else if forceUpdateMap["status"] {
		tu.SetStatus(input.Status)
	}
	if !isZero(input.Begin) {
		tu.SetBegin(input.Begin)
	} else if forceUpdateMap["begin"] {
		tu.ClearBegin()
	}
	if !isZero(input.End) {
		tu.SetEnd(input.End)
	} else if forceUpdateMap["end"] {
		tu.ClearEnd()
	}
	if input.DealID != nil && !isZero(*input.DealID) {
		tu.SetDealID(*input.DealID)
	} else if forceUpdateMap["deal_id"] {
		tu.ClearDealID()
	}
	if input.PipelineID != nil && !isZero(*input.PipelineID) {
		tu.SetPipelineID(*input.PipelineID)
	} else if forceUpdateMap["pipeline_id"] {
		tu.ClearPipelineID()
	}
	if input.StageID != nil && !isZero(*input.StageID) {
		tu.SetStageID(*input.StageID)
	} else if forceUpdateMap["stage_id"] {
		tu.ClearStageID()
	}
	if !isZero(input.UserID) {
		tu.SetUserID(input.UserID)
	} else if forceUpdateMap["user_id"] {
		tu.ClearUserID()
	}
	if !isZero(input.PersonID) {
		tu.SetPersonID(input.PersonID)
	} else if forceUpdateMap["person_id"] {
		tu.ClearPersonID()
	}
	if !isZero(input.Weight) {
		tu.SetWeight(input.Weight)
	} else if forceUpdateMap["weight"] {
		tu.ClearWeight()
	}
	if !isZero(input.Meta) {
		tu.SetMeta(input.Meta)
	} else if forceUpdateMap["meta"] {
		tu.ClearMeta()
	}
	if !isZero(input.State) {
		tu.SetState(input.State)
	} else if forceUpdateMap["state"] {
		tu.ClearState()
	}
	if !isZero(input.DealStageID) {
		tu.SetDealStageID(input.DealStageID)
	} else if forceUpdateMap["deal_stage_id"] {
		tu.ClearDealStageID()
	}
	if !isZero(input.StageHistory) {
		tu.SetStageHistory(input.StageHistory)
	} else if forceUpdateMap["stage_history"] {
		tu.ClearStageHistory()
	}
	return tu
}

func (uc *UserCreate) SetUser(input *User) *UserCreate {
	if !isZero(input.Status) {
		uc.SetStatus(input.Status)
	}
	if !isZero(input.Username) {
		uc.SetUsername(input.Username)
	}
	if !isZero(input.Password) {
		uc.SetPassword(input.Password)
	}
	if !isZero(input.Phone) {
		uc.SetPhone(input.Phone)
	}
	if !isZero(input.Email) {
		uc.SetEmail(input.Email)
	}
	if !isZero(input.EmailConfirmed) {
		uc.SetEmailConfirmed(input.EmailConfirmed)
	}
	if !isZero(input.Name) {
		uc.SetName(input.Name)
	}
	if !isZero(input.Gender) {
		uc.SetGender(input.Gender)
	}
	if !isZero(input.DepartmentID) {
		uc.SetDepartmentID(input.DepartmentID)
	}
	if !isZero(input.DepartmentPosition) {
		uc.SetDepartmentPosition(input.DepartmentPosition)
	}
	if !isZero(input.ProfileImage) {
		uc.SetProfileImage(input.ProfileImage)
	}
	if !isZero(input.SuspendedAt) {
		uc.SetSuspendedAt(input.SuspendedAt)
	}
	if !isZero(input.State) {
		uc.SetState(input.State)
	}
	return uc
}

func (uuo *UserUpdateOne) SetUser(input *User, forceUpdates ...string) *UserUpdateOne {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Status) {
		uuo.SetStatus(input.Status)
	} else if forceUpdateMap["status"] {
		uuo.SetStatus(input.Status)
	}
	if !isZero(input.Username) {
		uuo.SetUsername(input.Username)
	} else if forceUpdateMap["username"] {
		uuo.SetUsername(input.Username)
	}
	if !isZero(input.Password) {
		uuo.SetPassword(input.Password)
	} else if forceUpdateMap["password"] {
		uuo.SetPassword(input.Password)
	}
	if !isZero(input.Phone) {
		uuo.SetPhone(input.Phone)
	} else if forceUpdateMap["phone"] {
		uuo.ClearPhone()
	}
	if !isZero(input.Email) {
		uuo.SetEmail(input.Email)
	} else if forceUpdateMap["email"] {
		uuo.ClearEmail()
	}
	if !isZero(input.EmailConfirmed) {
		uuo.SetEmailConfirmed(input.EmailConfirmed)
	} else if forceUpdateMap["email_confirmed"] {
		uuo.ClearEmailConfirmed()
	}
	if !isZero(input.Name) {
		uuo.SetName(input.Name)
	} else if forceUpdateMap["name"] {
		uuo.ClearName()
	}
	if !isZero(input.Gender) {
		uuo.SetGender(input.Gender)
	} else if forceUpdateMap["gender"] {
		uuo.SetGender(input.Gender)
	}
	if !isZero(input.DepartmentID) {
		uuo.SetDepartmentID(input.DepartmentID)
	} else if forceUpdateMap["department_id"] {
		uuo.ClearDepartmentID()
	}
	if !isZero(input.DepartmentPosition) {
		uuo.SetDepartmentPosition(input.DepartmentPosition)
	} else if forceUpdateMap["department_position"] {
		uuo.ClearDepartmentPosition()
	}
	if !isZero(input.ProfileImage) {
		uuo.SetProfileImage(input.ProfileImage)
	} else if forceUpdateMap["profile_image"] {
		uuo.ClearProfileImage()
	}
	if !isZero(input.SuspendedAt) {
		uuo.SetSuspendedAt(input.SuspendedAt)
	} else if forceUpdateMap["suspended_at"] {
		uuo.ClearSuspendedAt()
	}
	if !isZero(input.State) {
		uuo.SetState(input.State)
	} else if forceUpdateMap["state"] {
		uuo.SetState(input.State)
	}
	return uuo
}

func (uu *UserUpdate) SetUser(input *User, forceUpdates ...string) *UserUpdate {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.Status) {
		uu.SetStatus(input.Status)
	} else if forceUpdateMap["status"] {
		uu.SetStatus(input.Status)
	}
	if !isZero(input.Username) {
		uu.SetUsername(input.Username)
	} else if forceUpdateMap["username"] {
		uu.SetUsername(input.Username)
	}
	if !isZero(input.Password) {
		uu.SetPassword(input.Password)
	} else if forceUpdateMap["password"] {
		uu.SetPassword(input.Password)
	}
	if !isZero(input.Phone) {
		uu.SetPhone(input.Phone)
	} else if forceUpdateMap["phone"] {
		uu.ClearPhone()
	}
	if !isZero(input.Email) {
		uu.SetEmail(input.Email)
	} else if forceUpdateMap["email"] {
		uu.ClearEmail()
	}
	if !isZero(input.EmailConfirmed) {
		uu.SetEmailConfirmed(input.EmailConfirmed)
	} else if forceUpdateMap["email_confirmed"] {
		uu.ClearEmailConfirmed()
	}
	if !isZero(input.Name) {
		uu.SetName(input.Name)
	} else if forceUpdateMap["name"] {
		uu.ClearName()
	}
	if !isZero(input.Gender) {
		uu.SetGender(input.Gender)
	} else if forceUpdateMap["gender"] {
		uu.SetGender(input.Gender)
	}
	if !isZero(input.DepartmentID) {
		uu.SetDepartmentID(input.DepartmentID)
	} else if forceUpdateMap["department_id"] {
		uu.ClearDepartmentID()
	}
	if !isZero(input.DepartmentPosition) {
		uu.SetDepartmentPosition(input.DepartmentPosition)
	} else if forceUpdateMap["department_position"] {
		uu.ClearDepartmentPosition()
	}
	if !isZero(input.ProfileImage) {
		uu.SetProfileImage(input.ProfileImage)
	} else if forceUpdateMap["profile_image"] {
		uu.ClearProfileImage()
	}
	if !isZero(input.SuspendedAt) {
		uu.SetSuspendedAt(input.SuspendedAt)
	} else if forceUpdateMap["suspended_at"] {
		uu.ClearSuspendedAt()
	}
	if !isZero(input.State) {
		uu.SetState(input.State)
	} else if forceUpdateMap["state"] {
		uu.SetState(input.State)
	}
	return uu
}

func (udc *UserDataCreate) SetUserData(input *UserData) *UserDataCreate {
	if !isZero(input.UserID) {
		udc.SetUserID(input.UserID)
	}
	if !isZero(input.Kind) {
		udc.SetKind(input.Kind)
	}
	if !isZero(input.Data) {
		udc.SetData(input.Data)
	}
	return udc
}

func (uduo *UserDataUpdateOne) SetUserData(input *UserData, forceUpdates ...string) *UserDataUpdateOne {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.UserID) {
		uduo.SetUserID(input.UserID)
	} else if forceUpdateMap["user_id"] {
		uduo.SetUserID(input.UserID)
	}
	if !isZero(input.Kind) {
		uduo.SetKind(input.Kind)
	} else if forceUpdateMap["kind"] {
		uduo.SetKind(input.Kind)
	}
	if !isZero(input.Data) {
		uduo.SetData(input.Data)
	} else if forceUpdateMap["data"] {
		uduo.ClearData()
	}
	return uduo
}

func (udu *UserDataUpdate) SetUserData(input *UserData, forceUpdates ...string) *UserDataUpdate {
	forceUpdateMap := make(map[string]bool)
	for _, field := range forceUpdates {
		forceUpdateMap[field] = true
	}
	if !isZero(input.UserID) {
		udu.SetUserID(input.UserID)
	} else if forceUpdateMap["user_id"] {
		udu.SetUserID(input.UserID)
	}
	if !isZero(input.Kind) {
		udu.SetKind(input.Kind)
	} else if forceUpdateMap["kind"] {
		udu.SetKind(input.Kind)
	}
	if !isZero(input.Data) {
		udu.SetData(input.Data)
	} else if forceUpdateMap["data"] {
		udu.ClearData()
	}
	return udu
}
