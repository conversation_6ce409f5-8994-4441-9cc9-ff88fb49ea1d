// Code generated by ent, DO NOT EDIT.

package paymentallocation

import (
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

const (
	// Label holds the string label denoting the paymentallocation type in the database.
	Label = "payment_allocation"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldDeletedAt holds the string denoting the deleted_at field in the database.
	FieldDeletedAt = "deleted_at"
	// FieldStatus holds the string denoting the status field in the database.
	FieldStatus = "status"
	// FieldVersion holds the string denoting the version field in the database.
	FieldVersion = "version"
	// FieldCreatedAt holds the string denoting the created_at field in the database.
	FieldCreatedAt = "created_at"
	// FieldUpdatedAt holds the string denoting the updated_at field in the database.
	FieldUpdatedAt = "updated_at"
	// FieldPaymentID holds the string denoting the payment_id field in the database.
	FieldPaymentID = "payment_id"
	// FieldBillID holds the string denoting the bill_id field in the database.
	FieldBillID = "bill_id"
	// FieldUserID holds the string denoting the user_id field in the database.
	FieldUserID = "user_id"
	// FieldBillItemID holds the string denoting the bill_item_id field in the database.
	FieldBillItemID = "bill_item_id"
	// FieldInstallmentID holds the string denoting the installment_id field in the database.
	FieldInstallmentID = "installment_id"
	// FieldNote holds the string denoting the note field in the database.
	FieldNote = "note"
	// FieldState holds the string denoting the state field in the database.
	FieldState = "state"
	// FieldAmount holds the string denoting the amount field in the database.
	FieldAmount = "amount"
	// EdgePayment holds the string denoting the payment edge name in mutations.
	EdgePayment = "payment"
	// EdgeBillItem holds the string denoting the bill_item edge name in mutations.
	EdgeBillItem = "bill_item"
	// EdgeInstallment holds the string denoting the installment edge name in mutations.
	EdgeInstallment = "installment"
	// Table holds the table name of the paymentallocation in the database.
	Table = "payment_allocation"
	// PaymentTable is the table that holds the payment relation/edge.
	PaymentTable = "payment_allocation"
	// PaymentInverseTable is the table name for the Payment entity.
	// It exists in this package in order to avoid circular dependency with the "payment" package.
	PaymentInverseTable = "payment"
	// PaymentColumn is the table column denoting the payment relation/edge.
	PaymentColumn = "payment_id"
	// BillItemTable is the table that holds the bill_item relation/edge.
	BillItemTable = "payment_allocation"
	// BillItemInverseTable is the table name for the BillItem entity.
	// It exists in this package in order to avoid circular dependency with the "billitem" package.
	BillItemInverseTable = "bill_item"
	// BillItemColumn is the table column denoting the bill_item relation/edge.
	BillItemColumn = "bill_item_id"
	// InstallmentTable is the table that holds the installment relation/edge.
	InstallmentTable = "payment_allocation"
	// InstallmentInverseTable is the table name for the Installment entity.
	// It exists in this package in order to avoid circular dependency with the "installment" package.
	InstallmentInverseTable = "installment"
	// InstallmentColumn is the table column denoting the installment relation/edge.
	InstallmentColumn = "installment_id"
)

// Columns holds all SQL columns for paymentallocation fields.
var Columns = []string{
	FieldID,
	FieldDeletedAt,
	FieldStatus,
	FieldVersion,
	FieldCreatedAt,
	FieldUpdatedAt,
	FieldPaymentID,
	FieldBillID,
	FieldUserID,
	FieldBillItemID,
	FieldInstallmentID,
	FieldNote,
	FieldState,
	FieldAmount,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

// Note that the variables below are initialized by the runtime
// package on the initialization of the application. Therefore,
// it should be imported in the main as follows:
//
//	import _ "bcare/ent/runtime"
var (
	Hooks        [2]ent.Hook
	Interceptors [1]ent.Interceptor
	// DefaultStatus holds the default value on creation for the "status" field.
	DefaultStatus int8
	// DefaultVersion holds the default value on creation for the "version" field.
	DefaultVersion int
	// DefaultCreatedAt holds the default value on creation for the "created_at" field.
	DefaultCreatedAt func() time.Time
	// DefaultUpdatedAt holds the default value on creation for the "updated_at" field.
	DefaultUpdatedAt func() time.Time
	// UpdateDefaultUpdatedAt holds the default value on update for the "updated_at" field.
	UpdateDefaultUpdatedAt func() time.Time
	// DefaultAmount holds the default value on creation for the "amount" field.
	DefaultAmount float64
)

// OrderOption defines the ordering options for the PaymentAllocation queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByDeletedAt orders the results by the deleted_at field.
func ByDeletedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDeletedAt, opts...).ToFunc()
}

// ByStatus orders the results by the status field.
func ByStatus(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldStatus, opts...).ToFunc()
}

// ByVersion orders the results by the version field.
func ByVersion(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldVersion, opts...).ToFunc()
}

// ByCreatedAt orders the results by the created_at field.
func ByCreatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedAt, opts...).ToFunc()
}

// ByUpdatedAt orders the results by the updated_at field.
func ByUpdatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdatedAt, opts...).ToFunc()
}

// ByPaymentID orders the results by the payment_id field.
func ByPaymentID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPaymentID, opts...).ToFunc()
}

// ByBillID orders the results by the bill_id field.
func ByBillID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldBillID, opts...).ToFunc()
}

// ByUserID orders the results by the user_id field.
func ByUserID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUserID, opts...).ToFunc()
}

// ByBillItemID orders the results by the bill_item_id field.
func ByBillItemID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldBillItemID, opts...).ToFunc()
}

// ByInstallmentID orders the results by the installment_id field.
func ByInstallmentID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldInstallmentID, opts...).ToFunc()
}

// ByNote orders the results by the note field.
func ByNote(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldNote, opts...).ToFunc()
}

// ByState orders the results by the state field.
func ByState(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldState, opts...).ToFunc()
}

// ByAmount orders the results by the amount field.
func ByAmount(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldAmount, opts...).ToFunc()
}

// ByPaymentField orders the results by payment field.
func ByPaymentField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newPaymentStep(), sql.OrderByField(field, opts...))
	}
}

// ByBillItemField orders the results by bill_item field.
func ByBillItemField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newBillItemStep(), sql.OrderByField(field, opts...))
	}
}

// ByInstallmentField orders the results by installment field.
func ByInstallmentField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newInstallmentStep(), sql.OrderByField(field, opts...))
	}
}
func newPaymentStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(PaymentInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2O, false, PaymentTable, PaymentColumn),
	)
}
func newBillItemStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(BillItemInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2O, false, BillItemTable, BillItemColumn),
	)
}
func newInstallmentStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(InstallmentInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2O, false, InstallmentTable, InstallmentColumn),
	)
}
