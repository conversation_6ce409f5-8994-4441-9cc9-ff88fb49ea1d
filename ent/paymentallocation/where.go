// Code generated by ent, DO NOT EDIT.

package paymentallocation

import (
	"bcare/ent/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

// ID filters vertices based on their ID field.
func ID(id int) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id int) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id int) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...int) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...int) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id int) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id int) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id int) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id int) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldLTE(FieldID, id))
}

// DeletedAt applies equality check predicate on the "deleted_at" field. It's identical to DeletedAtEQ.
func DeletedAt(v time.Time) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldEQ(FieldDeletedAt, v))
}

// Status applies equality check predicate on the "status" field. It's identical to StatusEQ.
func Status(v int8) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldEQ(FieldStatus, v))
}

// Version applies equality check predicate on the "version" field. It's identical to VersionEQ.
func Version(v int) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldEQ(FieldVersion, v))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldEQ(FieldUpdatedAt, v))
}

// PaymentID applies equality check predicate on the "payment_id" field. It's identical to PaymentIDEQ.
func PaymentID(v int) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldEQ(FieldPaymentID, v))
}

// BillID applies equality check predicate on the "bill_id" field. It's identical to BillIDEQ.
func BillID(v int) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldEQ(FieldBillID, v))
}

// UserID applies equality check predicate on the "user_id" field. It's identical to UserIDEQ.
func UserID(v int) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldEQ(FieldUserID, v))
}

// BillItemID applies equality check predicate on the "bill_item_id" field. It's identical to BillItemIDEQ.
func BillItemID(v int) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldEQ(FieldBillItemID, v))
}

// InstallmentID applies equality check predicate on the "installment_id" field. It's identical to InstallmentIDEQ.
func InstallmentID(v int) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldEQ(FieldInstallmentID, v))
}

// Note applies equality check predicate on the "note" field. It's identical to NoteEQ.
func Note(v string) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldEQ(FieldNote, v))
}

// State applies equality check predicate on the "state" field. It's identical to StateEQ.
func State(v string) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldEQ(FieldState, v))
}

// Amount applies equality check predicate on the "amount" field. It's identical to AmountEQ.
func Amount(v float64) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldEQ(FieldAmount, v))
}

// DeletedAtEQ applies the EQ predicate on the "deleted_at" field.
func DeletedAtEQ(v time.Time) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldEQ(FieldDeletedAt, v))
}

// DeletedAtNEQ applies the NEQ predicate on the "deleted_at" field.
func DeletedAtNEQ(v time.Time) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldNEQ(FieldDeletedAt, v))
}

// DeletedAtIn applies the In predicate on the "deleted_at" field.
func DeletedAtIn(vs ...time.Time) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldIn(FieldDeletedAt, vs...))
}

// DeletedAtNotIn applies the NotIn predicate on the "deleted_at" field.
func DeletedAtNotIn(vs ...time.Time) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldNotIn(FieldDeletedAt, vs...))
}

// DeletedAtGT applies the GT predicate on the "deleted_at" field.
func DeletedAtGT(v time.Time) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldGT(FieldDeletedAt, v))
}

// DeletedAtGTE applies the GTE predicate on the "deleted_at" field.
func DeletedAtGTE(v time.Time) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldGTE(FieldDeletedAt, v))
}

// DeletedAtLT applies the LT predicate on the "deleted_at" field.
func DeletedAtLT(v time.Time) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldLT(FieldDeletedAt, v))
}

// DeletedAtLTE applies the LTE predicate on the "deleted_at" field.
func DeletedAtLTE(v time.Time) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldLTE(FieldDeletedAt, v))
}

// DeletedAtIsNil applies the IsNil predicate on the "deleted_at" field.
func DeletedAtIsNil() predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldIsNull(FieldDeletedAt))
}

// DeletedAtNotNil applies the NotNil predicate on the "deleted_at" field.
func DeletedAtNotNil() predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldNotNull(FieldDeletedAt))
}

// StatusEQ applies the EQ predicate on the "status" field.
func StatusEQ(v int8) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldEQ(FieldStatus, v))
}

// StatusNEQ applies the NEQ predicate on the "status" field.
func StatusNEQ(v int8) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldNEQ(FieldStatus, v))
}

// StatusIn applies the In predicate on the "status" field.
func StatusIn(vs ...int8) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldIn(FieldStatus, vs...))
}

// StatusNotIn applies the NotIn predicate on the "status" field.
func StatusNotIn(vs ...int8) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldNotIn(FieldStatus, vs...))
}

// StatusGT applies the GT predicate on the "status" field.
func StatusGT(v int8) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldGT(FieldStatus, v))
}

// StatusGTE applies the GTE predicate on the "status" field.
func StatusGTE(v int8) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldGTE(FieldStatus, v))
}

// StatusLT applies the LT predicate on the "status" field.
func StatusLT(v int8) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldLT(FieldStatus, v))
}

// StatusLTE applies the LTE predicate on the "status" field.
func StatusLTE(v int8) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldLTE(FieldStatus, v))
}

// VersionEQ applies the EQ predicate on the "version" field.
func VersionEQ(v int) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldEQ(FieldVersion, v))
}

// VersionNEQ applies the NEQ predicate on the "version" field.
func VersionNEQ(v int) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldNEQ(FieldVersion, v))
}

// VersionIn applies the In predicate on the "version" field.
func VersionIn(vs ...int) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldIn(FieldVersion, vs...))
}

// VersionNotIn applies the NotIn predicate on the "version" field.
func VersionNotIn(vs ...int) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldNotIn(FieldVersion, vs...))
}

// VersionGT applies the GT predicate on the "version" field.
func VersionGT(v int) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldGT(FieldVersion, v))
}

// VersionGTE applies the GTE predicate on the "version" field.
func VersionGTE(v int) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldGTE(FieldVersion, v))
}

// VersionLT applies the LT predicate on the "version" field.
func VersionLT(v int) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldLT(FieldVersion, v))
}

// VersionLTE applies the LTE predicate on the "version" field.
func VersionLTE(v int) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldLTE(FieldVersion, v))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldLTE(FieldUpdatedAt, v))
}

// PaymentIDEQ applies the EQ predicate on the "payment_id" field.
func PaymentIDEQ(v int) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldEQ(FieldPaymentID, v))
}

// PaymentIDNEQ applies the NEQ predicate on the "payment_id" field.
func PaymentIDNEQ(v int) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldNEQ(FieldPaymentID, v))
}

// PaymentIDIn applies the In predicate on the "payment_id" field.
func PaymentIDIn(vs ...int) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldIn(FieldPaymentID, vs...))
}

// PaymentIDNotIn applies the NotIn predicate on the "payment_id" field.
func PaymentIDNotIn(vs ...int) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldNotIn(FieldPaymentID, vs...))
}

// BillIDEQ applies the EQ predicate on the "bill_id" field.
func BillIDEQ(v int) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldEQ(FieldBillID, v))
}

// BillIDNEQ applies the NEQ predicate on the "bill_id" field.
func BillIDNEQ(v int) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldNEQ(FieldBillID, v))
}

// BillIDIn applies the In predicate on the "bill_id" field.
func BillIDIn(vs ...int) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldIn(FieldBillID, vs...))
}

// BillIDNotIn applies the NotIn predicate on the "bill_id" field.
func BillIDNotIn(vs ...int) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldNotIn(FieldBillID, vs...))
}

// BillIDGT applies the GT predicate on the "bill_id" field.
func BillIDGT(v int) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldGT(FieldBillID, v))
}

// BillIDGTE applies the GTE predicate on the "bill_id" field.
func BillIDGTE(v int) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldGTE(FieldBillID, v))
}

// BillIDLT applies the LT predicate on the "bill_id" field.
func BillIDLT(v int) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldLT(FieldBillID, v))
}

// BillIDLTE applies the LTE predicate on the "bill_id" field.
func BillIDLTE(v int) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldLTE(FieldBillID, v))
}

// BillIDIsNil applies the IsNil predicate on the "bill_id" field.
func BillIDIsNil() predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldIsNull(FieldBillID))
}

// BillIDNotNil applies the NotNil predicate on the "bill_id" field.
func BillIDNotNil() predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldNotNull(FieldBillID))
}

// UserIDEQ applies the EQ predicate on the "user_id" field.
func UserIDEQ(v int) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldEQ(FieldUserID, v))
}

// UserIDNEQ applies the NEQ predicate on the "user_id" field.
func UserIDNEQ(v int) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldNEQ(FieldUserID, v))
}

// UserIDIn applies the In predicate on the "user_id" field.
func UserIDIn(vs ...int) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldIn(FieldUserID, vs...))
}

// UserIDNotIn applies the NotIn predicate on the "user_id" field.
func UserIDNotIn(vs ...int) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldNotIn(FieldUserID, vs...))
}

// UserIDGT applies the GT predicate on the "user_id" field.
func UserIDGT(v int) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldGT(FieldUserID, v))
}

// UserIDGTE applies the GTE predicate on the "user_id" field.
func UserIDGTE(v int) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldGTE(FieldUserID, v))
}

// UserIDLT applies the LT predicate on the "user_id" field.
func UserIDLT(v int) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldLT(FieldUserID, v))
}

// UserIDLTE applies the LTE predicate on the "user_id" field.
func UserIDLTE(v int) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldLTE(FieldUserID, v))
}

// UserIDIsNil applies the IsNil predicate on the "user_id" field.
func UserIDIsNil() predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldIsNull(FieldUserID))
}

// UserIDNotNil applies the NotNil predicate on the "user_id" field.
func UserIDNotNil() predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldNotNull(FieldUserID))
}

// BillItemIDEQ applies the EQ predicate on the "bill_item_id" field.
func BillItemIDEQ(v int) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldEQ(FieldBillItemID, v))
}

// BillItemIDNEQ applies the NEQ predicate on the "bill_item_id" field.
func BillItemIDNEQ(v int) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldNEQ(FieldBillItemID, v))
}

// BillItemIDIn applies the In predicate on the "bill_item_id" field.
func BillItemIDIn(vs ...int) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldIn(FieldBillItemID, vs...))
}

// BillItemIDNotIn applies the NotIn predicate on the "bill_item_id" field.
func BillItemIDNotIn(vs ...int) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldNotIn(FieldBillItemID, vs...))
}

// BillItemIDIsNil applies the IsNil predicate on the "bill_item_id" field.
func BillItemIDIsNil() predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldIsNull(FieldBillItemID))
}

// BillItemIDNotNil applies the NotNil predicate on the "bill_item_id" field.
func BillItemIDNotNil() predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldNotNull(FieldBillItemID))
}

// InstallmentIDEQ applies the EQ predicate on the "installment_id" field.
func InstallmentIDEQ(v int) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldEQ(FieldInstallmentID, v))
}

// InstallmentIDNEQ applies the NEQ predicate on the "installment_id" field.
func InstallmentIDNEQ(v int) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldNEQ(FieldInstallmentID, v))
}

// InstallmentIDIn applies the In predicate on the "installment_id" field.
func InstallmentIDIn(vs ...int) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldIn(FieldInstallmentID, vs...))
}

// InstallmentIDNotIn applies the NotIn predicate on the "installment_id" field.
func InstallmentIDNotIn(vs ...int) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldNotIn(FieldInstallmentID, vs...))
}

// InstallmentIDIsNil applies the IsNil predicate on the "installment_id" field.
func InstallmentIDIsNil() predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldIsNull(FieldInstallmentID))
}

// InstallmentIDNotNil applies the NotNil predicate on the "installment_id" field.
func InstallmentIDNotNil() predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldNotNull(FieldInstallmentID))
}

// NoteEQ applies the EQ predicate on the "note" field.
func NoteEQ(v string) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldEQ(FieldNote, v))
}

// NoteNEQ applies the NEQ predicate on the "note" field.
func NoteNEQ(v string) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldNEQ(FieldNote, v))
}

// NoteIn applies the In predicate on the "note" field.
func NoteIn(vs ...string) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldIn(FieldNote, vs...))
}

// NoteNotIn applies the NotIn predicate on the "note" field.
func NoteNotIn(vs ...string) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldNotIn(FieldNote, vs...))
}

// NoteGT applies the GT predicate on the "note" field.
func NoteGT(v string) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldGT(FieldNote, v))
}

// NoteGTE applies the GTE predicate on the "note" field.
func NoteGTE(v string) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldGTE(FieldNote, v))
}

// NoteLT applies the LT predicate on the "note" field.
func NoteLT(v string) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldLT(FieldNote, v))
}

// NoteLTE applies the LTE predicate on the "note" field.
func NoteLTE(v string) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldLTE(FieldNote, v))
}

// NoteContains applies the Contains predicate on the "note" field.
func NoteContains(v string) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldContains(FieldNote, v))
}

// NoteHasPrefix applies the HasPrefix predicate on the "note" field.
func NoteHasPrefix(v string) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldHasPrefix(FieldNote, v))
}

// NoteHasSuffix applies the HasSuffix predicate on the "note" field.
func NoteHasSuffix(v string) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldHasSuffix(FieldNote, v))
}

// NoteIsNil applies the IsNil predicate on the "note" field.
func NoteIsNil() predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldIsNull(FieldNote))
}

// NoteNotNil applies the NotNil predicate on the "note" field.
func NoteNotNil() predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldNotNull(FieldNote))
}

// NoteEqualFold applies the EqualFold predicate on the "note" field.
func NoteEqualFold(v string) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldEqualFold(FieldNote, v))
}

// NoteContainsFold applies the ContainsFold predicate on the "note" field.
func NoteContainsFold(v string) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldContainsFold(FieldNote, v))
}

// StateEQ applies the EQ predicate on the "state" field.
func StateEQ(v string) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldEQ(FieldState, v))
}

// StateNEQ applies the NEQ predicate on the "state" field.
func StateNEQ(v string) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldNEQ(FieldState, v))
}

// StateIn applies the In predicate on the "state" field.
func StateIn(vs ...string) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldIn(FieldState, vs...))
}

// StateNotIn applies the NotIn predicate on the "state" field.
func StateNotIn(vs ...string) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldNotIn(FieldState, vs...))
}

// StateGT applies the GT predicate on the "state" field.
func StateGT(v string) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldGT(FieldState, v))
}

// StateGTE applies the GTE predicate on the "state" field.
func StateGTE(v string) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldGTE(FieldState, v))
}

// StateLT applies the LT predicate on the "state" field.
func StateLT(v string) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldLT(FieldState, v))
}

// StateLTE applies the LTE predicate on the "state" field.
func StateLTE(v string) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldLTE(FieldState, v))
}

// StateContains applies the Contains predicate on the "state" field.
func StateContains(v string) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldContains(FieldState, v))
}

// StateHasPrefix applies the HasPrefix predicate on the "state" field.
func StateHasPrefix(v string) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldHasPrefix(FieldState, v))
}

// StateHasSuffix applies the HasSuffix predicate on the "state" field.
func StateHasSuffix(v string) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldHasSuffix(FieldState, v))
}

// StateIsNil applies the IsNil predicate on the "state" field.
func StateIsNil() predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldIsNull(FieldState))
}

// StateNotNil applies the NotNil predicate on the "state" field.
func StateNotNil() predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldNotNull(FieldState))
}

// StateEqualFold applies the EqualFold predicate on the "state" field.
func StateEqualFold(v string) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldEqualFold(FieldState, v))
}

// StateContainsFold applies the ContainsFold predicate on the "state" field.
func StateContainsFold(v string) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldContainsFold(FieldState, v))
}

// AmountEQ applies the EQ predicate on the "amount" field.
func AmountEQ(v float64) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldEQ(FieldAmount, v))
}

// AmountNEQ applies the NEQ predicate on the "amount" field.
func AmountNEQ(v float64) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldNEQ(FieldAmount, v))
}

// AmountIn applies the In predicate on the "amount" field.
func AmountIn(vs ...float64) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldIn(FieldAmount, vs...))
}

// AmountNotIn applies the NotIn predicate on the "amount" field.
func AmountNotIn(vs ...float64) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldNotIn(FieldAmount, vs...))
}

// AmountGT applies the GT predicate on the "amount" field.
func AmountGT(v float64) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldGT(FieldAmount, v))
}

// AmountGTE applies the GTE predicate on the "amount" field.
func AmountGTE(v float64) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldGTE(FieldAmount, v))
}

// AmountLT applies the LT predicate on the "amount" field.
func AmountLT(v float64) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldLT(FieldAmount, v))
}

// AmountLTE applies the LTE predicate on the "amount" field.
func AmountLTE(v float64) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.FieldLTE(FieldAmount, v))
}

// HasPayment applies the HasEdge predicate on the "payment" edge.
func HasPayment() predicate.PaymentAllocation {
	return predicate.PaymentAllocation(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, PaymentTable, PaymentColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasPaymentWith applies the HasEdge predicate on the "payment" edge with a given conditions (other predicates).
func HasPaymentWith(preds ...predicate.Payment) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(func(s *sql.Selector) {
		step := newPaymentStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasBillItem applies the HasEdge predicate on the "bill_item" edge.
func HasBillItem() predicate.PaymentAllocation {
	return predicate.PaymentAllocation(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, BillItemTable, BillItemColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasBillItemWith applies the HasEdge predicate on the "bill_item" edge with a given conditions (other predicates).
func HasBillItemWith(preds ...predicate.BillItem) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(func(s *sql.Selector) {
		step := newBillItemStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasInstallment applies the HasEdge predicate on the "installment" edge.
func HasInstallment() predicate.PaymentAllocation {
	return predicate.PaymentAllocation(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, InstallmentTable, InstallmentColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasInstallmentWith applies the HasEdge predicate on the "installment" edge with a given conditions (other predicates).
func HasInstallmentWith(preds ...predicate.Installment) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(func(s *sql.Selector) {
		step := newInstallmentStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.PaymentAllocation) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.PaymentAllocation) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.PaymentAllocation) predicate.PaymentAllocation {
	return predicate.PaymentAllocation(sql.NotPredicates(p))
}
