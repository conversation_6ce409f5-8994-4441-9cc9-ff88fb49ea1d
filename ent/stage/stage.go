// Code generated by ent, DO NOT EDIT.

package stage

import (
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

const (
	// Label holds the string label denoting the stage type in the database.
	Label = "stage"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldDeletedAt holds the string denoting the deleted_at field in the database.
	FieldDeletedAt = "deleted_at"
	// FieldStatus holds the string denoting the status field in the database.
	FieldStatus = "status"
	// FieldVersion holds the string denoting the version field in the database.
	FieldVersion = "version"
	// FieldCreatedAt holds the string denoting the created_at field in the database.
	FieldCreatedAt = "created_at"
	// FieldUpdatedAt holds the string denoting the updated_at field in the database.
	FieldUpdatedAt = "updated_at"
	// FieldName holds the string denoting the name field in the database.
	FieldName = "name"
	// FieldPipelineID holds the string denoting the pipeline_id field in the database.
	FieldPipelineID = "pipeline_id"
	// FieldOrderNumber holds the string denoting the order_number field in the database.
	FieldOrderNumber = "order_number"
	// FieldParentStageID holds the string denoting the parent_stage_id field in the database.
	FieldParentStageID = "parent_stage_id"
	// FieldMeta holds the string denoting the meta field in the database.
	FieldMeta = "meta"
	// EdgeParent holds the string denoting the parent edge name in mutations.
	EdgeParent = "parent"
	// EdgeChildren holds the string denoting the children edge name in mutations.
	EdgeChildren = "children"
	// EdgeDeals holds the string denoting the deals edge name in mutations.
	EdgeDeals = "deals"
	// EdgePersonStages holds the string denoting the person_stages edge name in mutations.
	EdgePersonStages = "person_stages"
	// EdgePipeline holds the string denoting the pipeline edge name in mutations.
	EdgePipeline = "pipeline"
	// Table holds the table name of the stage in the database.
	Table = "stage"
	// ParentTable is the table that holds the parent relation/edge.
	ParentTable = "stage"
	// ParentColumn is the table column denoting the parent relation/edge.
	ParentColumn = "parent_stage_id"
	// ChildrenTable is the table that holds the children relation/edge.
	ChildrenTable = "stage"
	// ChildrenColumn is the table column denoting the children relation/edge.
	ChildrenColumn = "parent_stage_id"
	// DealsTable is the table that holds the deals relation/edge.
	DealsTable = "deal"
	// DealsInverseTable is the table name for the Deal entity.
	// It exists in this package in order to avoid circular dependency with the "deal" package.
	DealsInverseTable = "deal"
	// DealsColumn is the table column denoting the deals relation/edge.
	DealsColumn = "stage_id"
	// PersonStagesTable is the table that holds the person_stages relation/edge.
	PersonStagesTable = "person_stage"
	// PersonStagesInverseTable is the table name for the PersonStage entity.
	// It exists in this package in order to avoid circular dependency with the "personstage" package.
	PersonStagesInverseTable = "person_stage"
	// PersonStagesColumn is the table column denoting the person_stages relation/edge.
	PersonStagesColumn = "stage_id"
	// PipelineTable is the table that holds the pipeline relation/edge.
	PipelineTable = "stage"
	// PipelineInverseTable is the table name for the Pipeline entity.
	// It exists in this package in order to avoid circular dependency with the "pipeline" package.
	PipelineInverseTable = "pipeline"
	// PipelineColumn is the table column denoting the pipeline relation/edge.
	PipelineColumn = "pipeline_id"
)

// Columns holds all SQL columns for stage fields.
var Columns = []string{
	FieldID,
	FieldDeletedAt,
	FieldStatus,
	FieldVersion,
	FieldCreatedAt,
	FieldUpdatedAt,
	FieldName,
	FieldPipelineID,
	FieldOrderNumber,
	FieldParentStageID,
	FieldMeta,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

// Note that the variables below are initialized by the runtime
// package on the initialization of the application. Therefore,
// it should be imported in the main as follows:
//
//	import _ "bcare/ent/runtime"
var (
	Hooks        [2]ent.Hook
	Interceptors [1]ent.Interceptor
	// DefaultStatus holds the default value on creation for the "status" field.
	DefaultStatus int8
	// DefaultVersion holds the default value on creation for the "version" field.
	DefaultVersion int
	// DefaultCreatedAt holds the default value on creation for the "created_at" field.
	DefaultCreatedAt func() time.Time
	// DefaultUpdatedAt holds the default value on creation for the "updated_at" field.
	DefaultUpdatedAt func() time.Time
	// UpdateDefaultUpdatedAt holds the default value on update for the "updated_at" field.
	UpdateDefaultUpdatedAt func() time.Time
)

// OrderOption defines the ordering options for the Stage queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByDeletedAt orders the results by the deleted_at field.
func ByDeletedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDeletedAt, opts...).ToFunc()
}

// ByStatus orders the results by the status field.
func ByStatus(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldStatus, opts...).ToFunc()
}

// ByVersion orders the results by the version field.
func ByVersion(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldVersion, opts...).ToFunc()
}

// ByCreatedAt orders the results by the created_at field.
func ByCreatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedAt, opts...).ToFunc()
}

// ByUpdatedAt orders the results by the updated_at field.
func ByUpdatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdatedAt, opts...).ToFunc()
}

// ByName orders the results by the name field.
func ByName(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldName, opts...).ToFunc()
}

// ByPipelineID orders the results by the pipeline_id field.
func ByPipelineID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPipelineID, opts...).ToFunc()
}

// ByOrderNumber orders the results by the order_number field.
func ByOrderNumber(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldOrderNumber, opts...).ToFunc()
}

// ByParentStageID orders the results by the parent_stage_id field.
func ByParentStageID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldParentStageID, opts...).ToFunc()
}

// ByMeta orders the results by the meta field.
func ByMeta(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldMeta, opts...).ToFunc()
}

// ByParentField orders the results by parent field.
func ByParentField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newParentStep(), sql.OrderByField(field, opts...))
	}
}

// ByChildrenCount orders the results by children count.
func ByChildrenCount(opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborsCount(s, newChildrenStep(), opts...)
	}
}

// ByChildren orders the results by children terms.
func ByChildren(term sql.OrderTerm, terms ...sql.OrderTerm) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newChildrenStep(), append([]sql.OrderTerm{term}, terms...)...)
	}
}

// ByDealsCount orders the results by deals count.
func ByDealsCount(opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborsCount(s, newDealsStep(), opts...)
	}
}

// ByDeals orders the results by deals terms.
func ByDeals(term sql.OrderTerm, terms ...sql.OrderTerm) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newDealsStep(), append([]sql.OrderTerm{term}, terms...)...)
	}
}

// ByPersonStagesCount orders the results by person_stages count.
func ByPersonStagesCount(opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborsCount(s, newPersonStagesStep(), opts...)
	}
}

// ByPersonStages orders the results by person_stages terms.
func ByPersonStages(term sql.OrderTerm, terms ...sql.OrderTerm) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newPersonStagesStep(), append([]sql.OrderTerm{term}, terms...)...)
	}
}

// ByPipelineField orders the results by pipeline field.
func ByPipelineField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newPipelineStep(), sql.OrderByField(field, opts...))
	}
}
func newParentStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(Table, FieldID),
		sqlgraph.Edge(sqlgraph.M2O, true, ParentTable, ParentColumn),
	)
}
func newChildrenStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(Table, FieldID),
		sqlgraph.Edge(sqlgraph.O2M, false, ChildrenTable, ChildrenColumn),
	)
}
func newDealsStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(DealsInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.O2M, true, DealsTable, DealsColumn),
	)
}
func newPersonStagesStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(PersonStagesInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.O2M, true, PersonStagesTable, PersonStagesColumn),
	)
}
func newPipelineStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(PipelineInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2O, false, PipelineTable, PipelineColumn),
	)
}
