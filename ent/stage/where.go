// Code generated by ent, DO NOT EDIT.

package stage

import (
	"bcare/ent/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

// ID filters vertices based on their ID field.
func ID(id int) predicate.Stage {
	return predicate.Stage(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id int) predicate.Stage {
	return predicate.Stage(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id int) predicate.Stage {
	return predicate.Stage(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...int) predicate.Stage {
	return predicate.Stage(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...int) predicate.Stage {
	return predicate.Stage(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id int) predicate.Stage {
	return predicate.Stage(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id int) predicate.Stage {
	return predicate.Stage(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id int) predicate.Stage {
	return predicate.Stage(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id int) predicate.Stage {
	return predicate.Stage(sql.FieldLTE(FieldID, id))
}

// DeletedAt applies equality check predicate on the "deleted_at" field. It's identical to DeletedAtEQ.
func DeletedAt(v time.Time) predicate.Stage {
	return predicate.Stage(sql.FieldEQ(FieldDeletedAt, v))
}

// Status applies equality check predicate on the "status" field. It's identical to StatusEQ.
func Status(v int8) predicate.Stage {
	return predicate.Stage(sql.FieldEQ(FieldStatus, v))
}

// Version applies equality check predicate on the "version" field. It's identical to VersionEQ.
func Version(v int) predicate.Stage {
	return predicate.Stage(sql.FieldEQ(FieldVersion, v))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.Stage {
	return predicate.Stage(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.Stage {
	return predicate.Stage(sql.FieldEQ(FieldUpdatedAt, v))
}

// Name applies equality check predicate on the "name" field. It's identical to NameEQ.
func Name(v string) predicate.Stage {
	return predicate.Stage(sql.FieldEQ(FieldName, v))
}

// PipelineID applies equality check predicate on the "pipeline_id" field. It's identical to PipelineIDEQ.
func PipelineID(v int) predicate.Stage {
	return predicate.Stage(sql.FieldEQ(FieldPipelineID, v))
}

// OrderNumber applies equality check predicate on the "order_number" field. It's identical to OrderNumberEQ.
func OrderNumber(v int) predicate.Stage {
	return predicate.Stage(sql.FieldEQ(FieldOrderNumber, v))
}

// ParentStageID applies equality check predicate on the "parent_stage_id" field. It's identical to ParentStageIDEQ.
func ParentStageID(v int) predicate.Stage {
	return predicate.Stage(sql.FieldEQ(FieldParentStageID, v))
}

// Meta applies equality check predicate on the "meta" field. It's identical to MetaEQ.
func Meta(v string) predicate.Stage {
	return predicate.Stage(sql.FieldEQ(FieldMeta, v))
}

// DeletedAtEQ applies the EQ predicate on the "deleted_at" field.
func DeletedAtEQ(v time.Time) predicate.Stage {
	return predicate.Stage(sql.FieldEQ(FieldDeletedAt, v))
}

// DeletedAtNEQ applies the NEQ predicate on the "deleted_at" field.
func DeletedAtNEQ(v time.Time) predicate.Stage {
	return predicate.Stage(sql.FieldNEQ(FieldDeletedAt, v))
}

// DeletedAtIn applies the In predicate on the "deleted_at" field.
func DeletedAtIn(vs ...time.Time) predicate.Stage {
	return predicate.Stage(sql.FieldIn(FieldDeletedAt, vs...))
}

// DeletedAtNotIn applies the NotIn predicate on the "deleted_at" field.
func DeletedAtNotIn(vs ...time.Time) predicate.Stage {
	return predicate.Stage(sql.FieldNotIn(FieldDeletedAt, vs...))
}

// DeletedAtGT applies the GT predicate on the "deleted_at" field.
func DeletedAtGT(v time.Time) predicate.Stage {
	return predicate.Stage(sql.FieldGT(FieldDeletedAt, v))
}

// DeletedAtGTE applies the GTE predicate on the "deleted_at" field.
func DeletedAtGTE(v time.Time) predicate.Stage {
	return predicate.Stage(sql.FieldGTE(FieldDeletedAt, v))
}

// DeletedAtLT applies the LT predicate on the "deleted_at" field.
func DeletedAtLT(v time.Time) predicate.Stage {
	return predicate.Stage(sql.FieldLT(FieldDeletedAt, v))
}

// DeletedAtLTE applies the LTE predicate on the "deleted_at" field.
func DeletedAtLTE(v time.Time) predicate.Stage {
	return predicate.Stage(sql.FieldLTE(FieldDeletedAt, v))
}

// DeletedAtIsNil applies the IsNil predicate on the "deleted_at" field.
func DeletedAtIsNil() predicate.Stage {
	return predicate.Stage(sql.FieldIsNull(FieldDeletedAt))
}

// DeletedAtNotNil applies the NotNil predicate on the "deleted_at" field.
func DeletedAtNotNil() predicate.Stage {
	return predicate.Stage(sql.FieldNotNull(FieldDeletedAt))
}

// StatusEQ applies the EQ predicate on the "status" field.
func StatusEQ(v int8) predicate.Stage {
	return predicate.Stage(sql.FieldEQ(FieldStatus, v))
}

// StatusNEQ applies the NEQ predicate on the "status" field.
func StatusNEQ(v int8) predicate.Stage {
	return predicate.Stage(sql.FieldNEQ(FieldStatus, v))
}

// StatusIn applies the In predicate on the "status" field.
func StatusIn(vs ...int8) predicate.Stage {
	return predicate.Stage(sql.FieldIn(FieldStatus, vs...))
}

// StatusNotIn applies the NotIn predicate on the "status" field.
func StatusNotIn(vs ...int8) predicate.Stage {
	return predicate.Stage(sql.FieldNotIn(FieldStatus, vs...))
}

// StatusGT applies the GT predicate on the "status" field.
func StatusGT(v int8) predicate.Stage {
	return predicate.Stage(sql.FieldGT(FieldStatus, v))
}

// StatusGTE applies the GTE predicate on the "status" field.
func StatusGTE(v int8) predicate.Stage {
	return predicate.Stage(sql.FieldGTE(FieldStatus, v))
}

// StatusLT applies the LT predicate on the "status" field.
func StatusLT(v int8) predicate.Stage {
	return predicate.Stage(sql.FieldLT(FieldStatus, v))
}

// StatusLTE applies the LTE predicate on the "status" field.
func StatusLTE(v int8) predicate.Stage {
	return predicate.Stage(sql.FieldLTE(FieldStatus, v))
}

// VersionEQ applies the EQ predicate on the "version" field.
func VersionEQ(v int) predicate.Stage {
	return predicate.Stage(sql.FieldEQ(FieldVersion, v))
}

// VersionNEQ applies the NEQ predicate on the "version" field.
func VersionNEQ(v int) predicate.Stage {
	return predicate.Stage(sql.FieldNEQ(FieldVersion, v))
}

// VersionIn applies the In predicate on the "version" field.
func VersionIn(vs ...int) predicate.Stage {
	return predicate.Stage(sql.FieldIn(FieldVersion, vs...))
}

// VersionNotIn applies the NotIn predicate on the "version" field.
func VersionNotIn(vs ...int) predicate.Stage {
	return predicate.Stage(sql.FieldNotIn(FieldVersion, vs...))
}

// VersionGT applies the GT predicate on the "version" field.
func VersionGT(v int) predicate.Stage {
	return predicate.Stage(sql.FieldGT(FieldVersion, v))
}

// VersionGTE applies the GTE predicate on the "version" field.
func VersionGTE(v int) predicate.Stage {
	return predicate.Stage(sql.FieldGTE(FieldVersion, v))
}

// VersionLT applies the LT predicate on the "version" field.
func VersionLT(v int) predicate.Stage {
	return predicate.Stage(sql.FieldLT(FieldVersion, v))
}

// VersionLTE applies the LTE predicate on the "version" field.
func VersionLTE(v int) predicate.Stage {
	return predicate.Stage(sql.FieldLTE(FieldVersion, v))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.Stage {
	return predicate.Stage(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.Stage {
	return predicate.Stage(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.Stage {
	return predicate.Stage(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.Stage {
	return predicate.Stage(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.Stage {
	return predicate.Stage(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.Stage {
	return predicate.Stage(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.Stage {
	return predicate.Stage(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.Stage {
	return predicate.Stage(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.Stage {
	return predicate.Stage(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.Stage {
	return predicate.Stage(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.Stage {
	return predicate.Stage(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.Stage {
	return predicate.Stage(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.Stage {
	return predicate.Stage(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.Stage {
	return predicate.Stage(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.Stage {
	return predicate.Stage(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.Stage {
	return predicate.Stage(sql.FieldLTE(FieldUpdatedAt, v))
}

// NameEQ applies the EQ predicate on the "name" field.
func NameEQ(v string) predicate.Stage {
	return predicate.Stage(sql.FieldEQ(FieldName, v))
}

// NameNEQ applies the NEQ predicate on the "name" field.
func NameNEQ(v string) predicate.Stage {
	return predicate.Stage(sql.FieldNEQ(FieldName, v))
}

// NameIn applies the In predicate on the "name" field.
func NameIn(vs ...string) predicate.Stage {
	return predicate.Stage(sql.FieldIn(FieldName, vs...))
}

// NameNotIn applies the NotIn predicate on the "name" field.
func NameNotIn(vs ...string) predicate.Stage {
	return predicate.Stage(sql.FieldNotIn(FieldName, vs...))
}

// NameGT applies the GT predicate on the "name" field.
func NameGT(v string) predicate.Stage {
	return predicate.Stage(sql.FieldGT(FieldName, v))
}

// NameGTE applies the GTE predicate on the "name" field.
func NameGTE(v string) predicate.Stage {
	return predicate.Stage(sql.FieldGTE(FieldName, v))
}

// NameLT applies the LT predicate on the "name" field.
func NameLT(v string) predicate.Stage {
	return predicate.Stage(sql.FieldLT(FieldName, v))
}

// NameLTE applies the LTE predicate on the "name" field.
func NameLTE(v string) predicate.Stage {
	return predicate.Stage(sql.FieldLTE(FieldName, v))
}

// NameContains applies the Contains predicate on the "name" field.
func NameContains(v string) predicate.Stage {
	return predicate.Stage(sql.FieldContains(FieldName, v))
}

// NameHasPrefix applies the HasPrefix predicate on the "name" field.
func NameHasPrefix(v string) predicate.Stage {
	return predicate.Stage(sql.FieldHasPrefix(FieldName, v))
}

// NameHasSuffix applies the HasSuffix predicate on the "name" field.
func NameHasSuffix(v string) predicate.Stage {
	return predicate.Stage(sql.FieldHasSuffix(FieldName, v))
}

// NameEqualFold applies the EqualFold predicate on the "name" field.
func NameEqualFold(v string) predicate.Stage {
	return predicate.Stage(sql.FieldEqualFold(FieldName, v))
}

// NameContainsFold applies the ContainsFold predicate on the "name" field.
func NameContainsFold(v string) predicate.Stage {
	return predicate.Stage(sql.FieldContainsFold(FieldName, v))
}

// PipelineIDEQ applies the EQ predicate on the "pipeline_id" field.
func PipelineIDEQ(v int) predicate.Stage {
	return predicate.Stage(sql.FieldEQ(FieldPipelineID, v))
}

// PipelineIDNEQ applies the NEQ predicate on the "pipeline_id" field.
func PipelineIDNEQ(v int) predicate.Stage {
	return predicate.Stage(sql.FieldNEQ(FieldPipelineID, v))
}

// PipelineIDIn applies the In predicate on the "pipeline_id" field.
func PipelineIDIn(vs ...int) predicate.Stage {
	return predicate.Stage(sql.FieldIn(FieldPipelineID, vs...))
}

// PipelineIDNotIn applies the NotIn predicate on the "pipeline_id" field.
func PipelineIDNotIn(vs ...int) predicate.Stage {
	return predicate.Stage(sql.FieldNotIn(FieldPipelineID, vs...))
}

// PipelineIDIsNil applies the IsNil predicate on the "pipeline_id" field.
func PipelineIDIsNil() predicate.Stage {
	return predicate.Stage(sql.FieldIsNull(FieldPipelineID))
}

// PipelineIDNotNil applies the NotNil predicate on the "pipeline_id" field.
func PipelineIDNotNil() predicate.Stage {
	return predicate.Stage(sql.FieldNotNull(FieldPipelineID))
}

// OrderNumberEQ applies the EQ predicate on the "order_number" field.
func OrderNumberEQ(v int) predicate.Stage {
	return predicate.Stage(sql.FieldEQ(FieldOrderNumber, v))
}

// OrderNumberNEQ applies the NEQ predicate on the "order_number" field.
func OrderNumberNEQ(v int) predicate.Stage {
	return predicate.Stage(sql.FieldNEQ(FieldOrderNumber, v))
}

// OrderNumberIn applies the In predicate on the "order_number" field.
func OrderNumberIn(vs ...int) predicate.Stage {
	return predicate.Stage(sql.FieldIn(FieldOrderNumber, vs...))
}

// OrderNumberNotIn applies the NotIn predicate on the "order_number" field.
func OrderNumberNotIn(vs ...int) predicate.Stage {
	return predicate.Stage(sql.FieldNotIn(FieldOrderNumber, vs...))
}

// OrderNumberGT applies the GT predicate on the "order_number" field.
func OrderNumberGT(v int) predicate.Stage {
	return predicate.Stage(sql.FieldGT(FieldOrderNumber, v))
}

// OrderNumberGTE applies the GTE predicate on the "order_number" field.
func OrderNumberGTE(v int) predicate.Stage {
	return predicate.Stage(sql.FieldGTE(FieldOrderNumber, v))
}

// OrderNumberLT applies the LT predicate on the "order_number" field.
func OrderNumberLT(v int) predicate.Stage {
	return predicate.Stage(sql.FieldLT(FieldOrderNumber, v))
}

// OrderNumberLTE applies the LTE predicate on the "order_number" field.
func OrderNumberLTE(v int) predicate.Stage {
	return predicate.Stage(sql.FieldLTE(FieldOrderNumber, v))
}

// OrderNumberIsNil applies the IsNil predicate on the "order_number" field.
func OrderNumberIsNil() predicate.Stage {
	return predicate.Stage(sql.FieldIsNull(FieldOrderNumber))
}

// OrderNumberNotNil applies the NotNil predicate on the "order_number" field.
func OrderNumberNotNil() predicate.Stage {
	return predicate.Stage(sql.FieldNotNull(FieldOrderNumber))
}

// ParentStageIDEQ applies the EQ predicate on the "parent_stage_id" field.
func ParentStageIDEQ(v int) predicate.Stage {
	return predicate.Stage(sql.FieldEQ(FieldParentStageID, v))
}

// ParentStageIDNEQ applies the NEQ predicate on the "parent_stage_id" field.
func ParentStageIDNEQ(v int) predicate.Stage {
	return predicate.Stage(sql.FieldNEQ(FieldParentStageID, v))
}

// ParentStageIDIn applies the In predicate on the "parent_stage_id" field.
func ParentStageIDIn(vs ...int) predicate.Stage {
	return predicate.Stage(sql.FieldIn(FieldParentStageID, vs...))
}

// ParentStageIDNotIn applies the NotIn predicate on the "parent_stage_id" field.
func ParentStageIDNotIn(vs ...int) predicate.Stage {
	return predicate.Stage(sql.FieldNotIn(FieldParentStageID, vs...))
}

// ParentStageIDIsNil applies the IsNil predicate on the "parent_stage_id" field.
func ParentStageIDIsNil() predicate.Stage {
	return predicate.Stage(sql.FieldIsNull(FieldParentStageID))
}

// ParentStageIDNotNil applies the NotNil predicate on the "parent_stage_id" field.
func ParentStageIDNotNil() predicate.Stage {
	return predicate.Stage(sql.FieldNotNull(FieldParentStageID))
}

// MetaEQ applies the EQ predicate on the "meta" field.
func MetaEQ(v string) predicate.Stage {
	return predicate.Stage(sql.FieldEQ(FieldMeta, v))
}

// MetaNEQ applies the NEQ predicate on the "meta" field.
func MetaNEQ(v string) predicate.Stage {
	return predicate.Stage(sql.FieldNEQ(FieldMeta, v))
}

// MetaIn applies the In predicate on the "meta" field.
func MetaIn(vs ...string) predicate.Stage {
	return predicate.Stage(sql.FieldIn(FieldMeta, vs...))
}

// MetaNotIn applies the NotIn predicate on the "meta" field.
func MetaNotIn(vs ...string) predicate.Stage {
	return predicate.Stage(sql.FieldNotIn(FieldMeta, vs...))
}

// MetaGT applies the GT predicate on the "meta" field.
func MetaGT(v string) predicate.Stage {
	return predicate.Stage(sql.FieldGT(FieldMeta, v))
}

// MetaGTE applies the GTE predicate on the "meta" field.
func MetaGTE(v string) predicate.Stage {
	return predicate.Stage(sql.FieldGTE(FieldMeta, v))
}

// MetaLT applies the LT predicate on the "meta" field.
func MetaLT(v string) predicate.Stage {
	return predicate.Stage(sql.FieldLT(FieldMeta, v))
}

// MetaLTE applies the LTE predicate on the "meta" field.
func MetaLTE(v string) predicate.Stage {
	return predicate.Stage(sql.FieldLTE(FieldMeta, v))
}

// MetaContains applies the Contains predicate on the "meta" field.
func MetaContains(v string) predicate.Stage {
	return predicate.Stage(sql.FieldContains(FieldMeta, v))
}

// MetaHasPrefix applies the HasPrefix predicate on the "meta" field.
func MetaHasPrefix(v string) predicate.Stage {
	return predicate.Stage(sql.FieldHasPrefix(FieldMeta, v))
}

// MetaHasSuffix applies the HasSuffix predicate on the "meta" field.
func MetaHasSuffix(v string) predicate.Stage {
	return predicate.Stage(sql.FieldHasSuffix(FieldMeta, v))
}

// MetaIsNil applies the IsNil predicate on the "meta" field.
func MetaIsNil() predicate.Stage {
	return predicate.Stage(sql.FieldIsNull(FieldMeta))
}

// MetaNotNil applies the NotNil predicate on the "meta" field.
func MetaNotNil() predicate.Stage {
	return predicate.Stage(sql.FieldNotNull(FieldMeta))
}

// MetaEqualFold applies the EqualFold predicate on the "meta" field.
func MetaEqualFold(v string) predicate.Stage {
	return predicate.Stage(sql.FieldEqualFold(FieldMeta, v))
}

// MetaContainsFold applies the ContainsFold predicate on the "meta" field.
func MetaContainsFold(v string) predicate.Stage {
	return predicate.Stage(sql.FieldContainsFold(FieldMeta, v))
}

// HasParent applies the HasEdge predicate on the "parent" edge.
func HasParent() predicate.Stage {
	return predicate.Stage(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, ParentTable, ParentColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasParentWith applies the HasEdge predicate on the "parent" edge with a given conditions (other predicates).
func HasParentWith(preds ...predicate.Stage) predicate.Stage {
	return predicate.Stage(func(s *sql.Selector) {
		step := newParentStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasChildren applies the HasEdge predicate on the "children" edge.
func HasChildren() predicate.Stage {
	return predicate.Stage(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, ChildrenTable, ChildrenColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasChildrenWith applies the HasEdge predicate on the "children" edge with a given conditions (other predicates).
func HasChildrenWith(preds ...predicate.Stage) predicate.Stage {
	return predicate.Stage(func(s *sql.Selector) {
		step := newChildrenStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasDeals applies the HasEdge predicate on the "deals" edge.
func HasDeals() predicate.Stage {
	return predicate.Stage(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, DealsTable, DealsColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasDealsWith applies the HasEdge predicate on the "deals" edge with a given conditions (other predicates).
func HasDealsWith(preds ...predicate.Deal) predicate.Stage {
	return predicate.Stage(func(s *sql.Selector) {
		step := newDealsStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasPersonStages applies the HasEdge predicate on the "person_stages" edge.
func HasPersonStages() predicate.Stage {
	return predicate.Stage(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, PersonStagesTable, PersonStagesColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasPersonStagesWith applies the HasEdge predicate on the "person_stages" edge with a given conditions (other predicates).
func HasPersonStagesWith(preds ...predicate.PersonStage) predicate.Stage {
	return predicate.Stage(func(s *sql.Selector) {
		step := newPersonStagesStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasPipeline applies the HasEdge predicate on the "pipeline" edge.
func HasPipeline() predicate.Stage {
	return predicate.Stage(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, PipelineTable, PipelineColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasPipelineWith applies the HasEdge predicate on the "pipeline" edge with a given conditions (other predicates).
func HasPipelineWith(preds ...predicate.Pipeline) predicate.Stage {
	return predicate.Stage(func(s *sql.Selector) {
		step := newPipelineStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.Stage) predicate.Stage {
	return predicate.Stage(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.Stage) predicate.Stage {
	return predicate.Stage(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.Stage) predicate.Stage {
	return predicate.Stage(sql.NotPredicates(p))
}
