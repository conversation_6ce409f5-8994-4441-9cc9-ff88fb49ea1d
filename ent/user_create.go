// Code generated by ent, DO NOT EDIT.

package ent

import (
	"bcare/ent/appointment"
	"bcare/ent/call"
	"bcare/ent/exportjob"
	"bcare/ent/messagehistory"
	"bcare/ent/note"
	"bcare/ent/notification"
	"bcare/ent/person"
	"bcare/ent/personassignment"
	"bcare/ent/schedule"
	"bcare/ent/taskassignment"
	"bcare/ent/tasknote"
	"bcare/ent/user"
	"bcare/ent/userdata"
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// UserCreate is the builder for creating a User entity.
type UserCreate struct {
	config
	mutation *UserMutation
	hooks    []Hook
}

// SetDeletedAt sets the "deleted_at" field.
func (uc *UserCreate) SetDeletedAt(t time.Time) *UserCreate {
	uc.mutation.SetDeletedAt(t)
	return uc
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (uc *UserCreate) SetNillableDeletedAt(t *time.Time) *UserCreate {
	if t != nil {
		uc.SetDeletedAt(*t)
	}
	return uc
}

// SetStatus sets the "status" field.
func (uc *UserCreate) SetStatus(i int8) *UserCreate {
	uc.mutation.SetStatus(i)
	return uc
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (uc *UserCreate) SetNillableStatus(i *int8) *UserCreate {
	if i != nil {
		uc.SetStatus(*i)
	}
	return uc
}

// SetVersion sets the "version" field.
func (uc *UserCreate) SetVersion(i int) *UserCreate {
	uc.mutation.SetVersion(i)
	return uc
}

// SetNillableVersion sets the "version" field if the given value is not nil.
func (uc *UserCreate) SetNillableVersion(i *int) *UserCreate {
	if i != nil {
		uc.SetVersion(*i)
	}
	return uc
}

// SetCreatedAt sets the "created_at" field.
func (uc *UserCreate) SetCreatedAt(t time.Time) *UserCreate {
	uc.mutation.SetCreatedAt(t)
	return uc
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (uc *UserCreate) SetNillableCreatedAt(t *time.Time) *UserCreate {
	if t != nil {
		uc.SetCreatedAt(*t)
	}
	return uc
}

// SetUpdatedAt sets the "updated_at" field.
func (uc *UserCreate) SetUpdatedAt(t time.Time) *UserCreate {
	uc.mutation.SetUpdatedAt(t)
	return uc
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (uc *UserCreate) SetNillableUpdatedAt(t *time.Time) *UserCreate {
	if t != nil {
		uc.SetUpdatedAt(*t)
	}
	return uc
}

// SetUsername sets the "username" field.
func (uc *UserCreate) SetUsername(s string) *UserCreate {
	uc.mutation.SetUsername(s)
	return uc
}

// SetPassword sets the "password" field.
func (uc *UserCreate) SetPassword(s string) *UserCreate {
	uc.mutation.SetPassword(s)
	return uc
}

// SetPhone sets the "phone" field.
func (uc *UserCreate) SetPhone(s string) *UserCreate {
	uc.mutation.SetPhone(s)
	return uc
}

// SetNillablePhone sets the "phone" field if the given value is not nil.
func (uc *UserCreate) SetNillablePhone(s *string) *UserCreate {
	if s != nil {
		uc.SetPhone(*s)
	}
	return uc
}

// SetEmail sets the "email" field.
func (uc *UserCreate) SetEmail(s string) *UserCreate {
	uc.mutation.SetEmail(s)
	return uc
}

// SetNillableEmail sets the "email" field if the given value is not nil.
func (uc *UserCreate) SetNillableEmail(s *string) *UserCreate {
	if s != nil {
		uc.SetEmail(*s)
	}
	return uc
}

// SetEmailConfirmed sets the "email_confirmed" field.
func (uc *UserCreate) SetEmailConfirmed(b bool) *UserCreate {
	uc.mutation.SetEmailConfirmed(b)
	return uc
}

// SetNillableEmailConfirmed sets the "email_confirmed" field if the given value is not nil.
func (uc *UserCreate) SetNillableEmailConfirmed(b *bool) *UserCreate {
	if b != nil {
		uc.SetEmailConfirmed(*b)
	}
	return uc
}

// SetName sets the "name" field.
func (uc *UserCreate) SetName(s string) *UserCreate {
	uc.mutation.SetName(s)
	return uc
}

// SetNillableName sets the "name" field if the given value is not nil.
func (uc *UserCreate) SetNillableName(s *string) *UserCreate {
	if s != nil {
		uc.SetName(*s)
	}
	return uc
}

// SetGender sets the "gender" field.
func (uc *UserCreate) SetGender(s string) *UserCreate {
	uc.mutation.SetGender(s)
	return uc
}

// SetDepartmentID sets the "department_id" field.
func (uc *UserCreate) SetDepartmentID(i int) *UserCreate {
	uc.mutation.SetDepartmentID(i)
	return uc
}

// SetNillableDepartmentID sets the "department_id" field if the given value is not nil.
func (uc *UserCreate) SetNillableDepartmentID(i *int) *UserCreate {
	if i != nil {
		uc.SetDepartmentID(*i)
	}
	return uc
}

// SetDepartmentPosition sets the "department_position" field.
func (uc *UserCreate) SetDepartmentPosition(s string) *UserCreate {
	uc.mutation.SetDepartmentPosition(s)
	return uc
}

// SetNillableDepartmentPosition sets the "department_position" field if the given value is not nil.
func (uc *UserCreate) SetNillableDepartmentPosition(s *string) *UserCreate {
	if s != nil {
		uc.SetDepartmentPosition(*s)
	}
	return uc
}

// SetProfileImage sets the "profile_image" field.
func (uc *UserCreate) SetProfileImage(s string) *UserCreate {
	uc.mutation.SetProfileImage(s)
	return uc
}

// SetNillableProfileImage sets the "profile_image" field if the given value is not nil.
func (uc *UserCreate) SetNillableProfileImage(s *string) *UserCreate {
	if s != nil {
		uc.SetProfileImage(*s)
	}
	return uc
}

// SetSuspendedAt sets the "suspended_at" field.
func (uc *UserCreate) SetSuspendedAt(t time.Time) *UserCreate {
	uc.mutation.SetSuspendedAt(t)
	return uc
}

// SetNillableSuspendedAt sets the "suspended_at" field if the given value is not nil.
func (uc *UserCreate) SetNillableSuspendedAt(t *time.Time) *UserCreate {
	if t != nil {
		uc.SetSuspendedAt(*t)
	}
	return uc
}

// SetState sets the "state" field.
func (uc *UserCreate) SetState(u user.State) *UserCreate {
	uc.mutation.SetState(u)
	return uc
}

// SetNillableState sets the "state" field if the given value is not nil.
func (uc *UserCreate) SetNillableState(u *user.State) *UserCreate {
	if u != nil {
		uc.SetState(*u)
	}
	return uc
}

// AddAssignmentIDs adds the "assignment" edge to the TaskAssignment entity by IDs.
func (uc *UserCreate) AddAssignmentIDs(ids ...int) *UserCreate {
	uc.mutation.AddAssignmentIDs(ids...)
	return uc
}

// AddAssignment adds the "assignment" edges to the TaskAssignment entity.
func (uc *UserCreate) AddAssignment(t ...*TaskAssignment) *UserCreate {
	ids := make([]int, len(t))
	for i := range t {
		ids[i] = t[i].ID
	}
	return uc.AddAssignmentIDs(ids...)
}

// AddAssignedPersonIDs adds the "assigned_person" edge to the Person entity by IDs.
func (uc *UserCreate) AddAssignedPersonIDs(ids ...int) *UserCreate {
	uc.mutation.AddAssignedPersonIDs(ids...)
	return uc
}

// AddAssignedPerson adds the "assigned_person" edges to the Person entity.
func (uc *UserCreate) AddAssignedPerson(p ...*Person) *UserCreate {
	ids := make([]int, len(p))
	for i := range p {
		ids[i] = p[i].ID
	}
	return uc.AddAssignedPersonIDs(ids...)
}

// AddNoteIDs adds the "notes" edge to the Note entity by IDs.
func (uc *UserCreate) AddNoteIDs(ids ...int) *UserCreate {
	uc.mutation.AddNoteIDs(ids...)
	return uc
}

// AddNotes adds the "notes" edges to the Note entity.
func (uc *UserCreate) AddNotes(n ...*Note) *UserCreate {
	ids := make([]int, len(n))
	for i := range n {
		ids[i] = n[i].ID
	}
	return uc.AddNoteIDs(ids...)
}

// AddTaskNoteIDs adds the "task_notes" edge to the TaskNote entity by IDs.
func (uc *UserCreate) AddTaskNoteIDs(ids ...int) *UserCreate {
	uc.mutation.AddTaskNoteIDs(ids...)
	return uc
}

// AddTaskNotes adds the "task_notes" edges to the TaskNote entity.
func (uc *UserCreate) AddTaskNotes(t ...*TaskNote) *UserCreate {
	ids := make([]int, len(t))
	for i := range t {
		ids[i] = t[i].ID
	}
	return uc.AddTaskNoteIDs(ids...)
}

// AddAppointmentIDs adds the "appointments" edge to the Appointment entity by IDs.
func (uc *UserCreate) AddAppointmentIDs(ids ...int) *UserCreate {
	uc.mutation.AddAppointmentIDs(ids...)
	return uc
}

// AddAppointments adds the "appointments" edges to the Appointment entity.
func (uc *UserCreate) AddAppointments(a ...*Appointment) *UserCreate {
	ids := make([]int, len(a))
	for i := range a {
		ids[i] = a[i].ID
	}
	return uc.AddAppointmentIDs(ids...)
}

// AddCallIDs adds the "calls" edge to the Call entity by IDs.
func (uc *UserCreate) AddCallIDs(ids ...int) *UserCreate {
	uc.mutation.AddCallIDs(ids...)
	return uc
}

// AddCalls adds the "calls" edges to the Call entity.
func (uc *UserCreate) AddCalls(c ...*Call) *UserCreate {
	ids := make([]int, len(c))
	for i := range c {
		ids[i] = c[i].ID
	}
	return uc.AddCallIDs(ids...)
}

// AddScheduleIDs adds the "schedules" edge to the Schedule entity by IDs.
func (uc *UserCreate) AddScheduleIDs(ids ...int) *UserCreate {
	uc.mutation.AddScheduleIDs(ids...)
	return uc
}

// AddSchedules adds the "schedules" edges to the Schedule entity.
func (uc *UserCreate) AddSchedules(s ...*Schedule) *UserCreate {
	ids := make([]int, len(s))
	for i := range s {
		ids[i] = s[i].ID
	}
	return uc.AddScheduleIDs(ids...)
}

// AddMessageHistoryIDs adds the "message_histories" edge to the MessageHistory entity by IDs.
func (uc *UserCreate) AddMessageHistoryIDs(ids ...int) *UserCreate {
	uc.mutation.AddMessageHistoryIDs(ids...)
	return uc
}

// AddMessageHistories adds the "message_histories" edges to the MessageHistory entity.
func (uc *UserCreate) AddMessageHistories(m ...*MessageHistory) *UserCreate {
	ids := make([]int, len(m))
	for i := range m {
		ids[i] = m[i].ID
	}
	return uc.AddMessageHistoryIDs(ids...)
}

// AddDatumIDs adds the "data" edge to the UserData entity by IDs.
func (uc *UserCreate) AddDatumIDs(ids ...int) *UserCreate {
	uc.mutation.AddDatumIDs(ids...)
	return uc
}

// AddData adds the "data" edges to the UserData entity.
func (uc *UserCreate) AddData(u ...*UserData) *UserCreate {
	ids := make([]int, len(u))
	for i := range u {
		ids[i] = u[i].ID
	}
	return uc.AddDatumIDs(ids...)
}

// AddExportJobIDs adds the "export_jobs" edge to the ExportJob entity by IDs.
func (uc *UserCreate) AddExportJobIDs(ids ...int) *UserCreate {
	uc.mutation.AddExportJobIDs(ids...)
	return uc
}

// AddExportJobs adds the "export_jobs" edges to the ExportJob entity.
func (uc *UserCreate) AddExportJobs(e ...*ExportJob) *UserCreate {
	ids := make([]int, len(e))
	for i := range e {
		ids[i] = e[i].ID
	}
	return uc.AddExportJobIDs(ids...)
}

// AddNotificationIDs adds the "notifications" edge to the Notification entity by IDs.
func (uc *UserCreate) AddNotificationIDs(ids ...int) *UserCreate {
	uc.mutation.AddNotificationIDs(ids...)
	return uc
}

// AddNotifications adds the "notifications" edges to the Notification entity.
func (uc *UserCreate) AddNotifications(n ...*Notification) *UserCreate {
	ids := make([]int, len(n))
	for i := range n {
		ids[i] = n[i].ID
	}
	return uc.AddNotificationIDs(ids...)
}

// AddPersonAssignmentIDs adds the "person_assignment" edge to the PersonAssignment entity by IDs.
func (uc *UserCreate) AddPersonAssignmentIDs(ids ...int) *UserCreate {
	uc.mutation.AddPersonAssignmentIDs(ids...)
	return uc
}

// AddPersonAssignment adds the "person_assignment" edges to the PersonAssignment entity.
func (uc *UserCreate) AddPersonAssignment(p ...*PersonAssignment) *UserCreate {
	ids := make([]int, len(p))
	for i := range p {
		ids[i] = p[i].ID
	}
	return uc.AddPersonAssignmentIDs(ids...)
}

// Mutation returns the UserMutation object of the builder.
func (uc *UserCreate) Mutation() *UserMutation {
	return uc.mutation
}

// Save creates the User in the database.
func (uc *UserCreate) Save(ctx context.Context) (*User, error) {
	if err := uc.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, uc.sqlSave, uc.mutation, uc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (uc *UserCreate) SaveX(ctx context.Context) *User {
	v, err := uc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (uc *UserCreate) Exec(ctx context.Context) error {
	_, err := uc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (uc *UserCreate) ExecX(ctx context.Context) {
	if err := uc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (uc *UserCreate) defaults() error {
	if _, ok := uc.mutation.Status(); !ok {
		v := user.DefaultStatus
		uc.mutation.SetStatus(v)
	}
	if _, ok := uc.mutation.Version(); !ok {
		v := user.DefaultVersion
		uc.mutation.SetVersion(v)
	}
	if _, ok := uc.mutation.CreatedAt(); !ok {
		if user.DefaultCreatedAt == nil {
			return fmt.Errorf("ent: uninitialized user.DefaultCreatedAt (forgotten import ent/runtime?)")
		}
		v := user.DefaultCreatedAt()
		uc.mutation.SetCreatedAt(v)
	}
	if _, ok := uc.mutation.UpdatedAt(); !ok {
		if user.DefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized user.DefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := user.DefaultUpdatedAt()
		uc.mutation.SetUpdatedAt(v)
	}
	if _, ok := uc.mutation.State(); !ok {
		v := user.DefaultState
		uc.mutation.SetState(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (uc *UserCreate) check() error {
	if _, ok := uc.mutation.Status(); !ok {
		return &ValidationError{Name: "status", err: errors.New(`ent: missing required field "User.status"`)}
	}
	if _, ok := uc.mutation.Version(); !ok {
		return &ValidationError{Name: "version", err: errors.New(`ent: missing required field "User.version"`)}
	}
	if _, ok := uc.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "User.created_at"`)}
	}
	if _, ok := uc.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "User.updated_at"`)}
	}
	if _, ok := uc.mutation.Username(); !ok {
		return &ValidationError{Name: "username", err: errors.New(`ent: missing required field "User.username"`)}
	}
	if _, ok := uc.mutation.Password(); !ok {
		return &ValidationError{Name: "password", err: errors.New(`ent: missing required field "User.password"`)}
	}
	if _, ok := uc.mutation.Gender(); !ok {
		return &ValidationError{Name: "gender", err: errors.New(`ent: missing required field "User.gender"`)}
	}
	if _, ok := uc.mutation.State(); !ok {
		return &ValidationError{Name: "state", err: errors.New(`ent: missing required field "User.state"`)}
	}
	if v, ok := uc.mutation.State(); ok {
		if err := user.StateValidator(v); err != nil {
			return &ValidationError{Name: "state", err: fmt.Errorf(`ent: validator failed for field "User.state": %w`, err)}
		}
	}
	return nil
}

func (uc *UserCreate) sqlSave(ctx context.Context) (*User, error) {
	if err := uc.check(); err != nil {
		return nil, err
	}
	_node, _spec := uc.createSpec()
	if err := sqlgraph.CreateNode(ctx, uc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	id := _spec.ID.Value.(int64)
	_node.ID = int(id)
	uc.mutation.id = &_node.ID
	uc.mutation.done = true
	return _node, nil
}

func (uc *UserCreate) createSpec() (*User, *sqlgraph.CreateSpec) {
	var (
		_node = &User{config: uc.config}
		_spec = sqlgraph.NewCreateSpec(user.Table, sqlgraph.NewFieldSpec(user.FieldID, field.TypeInt))
	)
	if value, ok := uc.mutation.DeletedAt(); ok {
		_spec.SetField(user.FieldDeletedAt, field.TypeTime, value)
		_node.DeletedAt = value
	}
	if value, ok := uc.mutation.Status(); ok {
		_spec.SetField(user.FieldStatus, field.TypeInt8, value)
		_node.Status = value
	}
	if value, ok := uc.mutation.Version(); ok {
		_spec.SetField(user.FieldVersion, field.TypeInt, value)
		_node.Version = value
	}
	if value, ok := uc.mutation.CreatedAt(); ok {
		_spec.SetField(user.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := uc.mutation.UpdatedAt(); ok {
		_spec.SetField(user.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	if value, ok := uc.mutation.Username(); ok {
		_spec.SetField(user.FieldUsername, field.TypeString, value)
		_node.Username = value
	}
	if value, ok := uc.mutation.Password(); ok {
		_spec.SetField(user.FieldPassword, field.TypeString, value)
		_node.Password = value
	}
	if value, ok := uc.mutation.Phone(); ok {
		_spec.SetField(user.FieldPhone, field.TypeString, value)
		_node.Phone = value
	}
	if value, ok := uc.mutation.Email(); ok {
		_spec.SetField(user.FieldEmail, field.TypeString, value)
		_node.Email = value
	}
	if value, ok := uc.mutation.EmailConfirmed(); ok {
		_spec.SetField(user.FieldEmailConfirmed, field.TypeBool, value)
		_node.EmailConfirmed = value
	}
	if value, ok := uc.mutation.Name(); ok {
		_spec.SetField(user.FieldName, field.TypeString, value)
		_node.Name = value
	}
	if value, ok := uc.mutation.Gender(); ok {
		_spec.SetField(user.FieldGender, field.TypeString, value)
		_node.Gender = value
	}
	if value, ok := uc.mutation.DepartmentID(); ok {
		_spec.SetField(user.FieldDepartmentID, field.TypeInt, value)
		_node.DepartmentID = value
	}
	if value, ok := uc.mutation.DepartmentPosition(); ok {
		_spec.SetField(user.FieldDepartmentPosition, field.TypeString, value)
		_node.DepartmentPosition = value
	}
	if value, ok := uc.mutation.ProfileImage(); ok {
		_spec.SetField(user.FieldProfileImage, field.TypeString, value)
		_node.ProfileImage = value
	}
	if value, ok := uc.mutation.SuspendedAt(); ok {
		_spec.SetField(user.FieldSuspendedAt, field.TypeTime, value)
		_node.SuspendedAt = value
	}
	if value, ok := uc.mutation.State(); ok {
		_spec.SetField(user.FieldState, field.TypeEnum, value)
		_node.State = value
	}
	if nodes := uc.mutation.AssignmentIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   user.AssignmentTable,
			Columns: []string{user.AssignmentColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(taskassignment.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := uc.mutation.AssignedPersonIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: true,
			Table:   user.AssignedPersonTable,
			Columns: user.AssignedPersonPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(person.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		createE := &PersonAssignmentCreate{config: uc.config, mutation: newPersonAssignmentMutation(uc.config, OpCreate)}
		createE.defaults()
		_, specE := createE.createSpec()
		edge.Target.Fields = specE.Fields
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := uc.mutation.NotesIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   user.NotesTable,
			Columns: []string{user.NotesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(note.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := uc.mutation.TaskNotesIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   user.TaskNotesTable,
			Columns: []string{user.TaskNotesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tasknote.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := uc.mutation.AppointmentsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   user.AppointmentsTable,
			Columns: []string{user.AppointmentsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(appointment.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := uc.mutation.CallsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   user.CallsTable,
			Columns: []string{user.CallsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(call.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := uc.mutation.SchedulesIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   user.SchedulesTable,
			Columns: []string{user.SchedulesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(schedule.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := uc.mutation.MessageHistoriesIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   user.MessageHistoriesTable,
			Columns: []string{user.MessageHistoriesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(messagehistory.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := uc.mutation.DataIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   user.DataTable,
			Columns: []string{user.DataColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(userdata.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := uc.mutation.ExportJobsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   user.ExportJobsTable,
			Columns: []string{user.ExportJobsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(exportjob.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := uc.mutation.NotificationsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   user.NotificationsTable,
			Columns: []string{user.NotificationsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(notification.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := uc.mutation.PersonAssignmentIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   user.PersonAssignmentTable,
			Columns: []string{user.PersonAssignmentColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(personassignment.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	return _node, _spec
}

// UserCreateBulk is the builder for creating many User entities in bulk.
type UserCreateBulk struct {
	config
	err      error
	builders []*UserCreate
}

// Save creates the User entities in the database.
func (ucb *UserCreateBulk) Save(ctx context.Context) ([]*User, error) {
	if ucb.err != nil {
		return nil, ucb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(ucb.builders))
	nodes := make([]*User, len(ucb.builders))
	mutators := make([]Mutator, len(ucb.builders))
	for i := range ucb.builders {
		func(i int, root context.Context) {
			builder := ucb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*UserMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, ucb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, ucb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = int(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, ucb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (ucb *UserCreateBulk) SaveX(ctx context.Context) []*User {
	v, err := ucb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (ucb *UserCreateBulk) Exec(ctx context.Context) error {
	_, err := ucb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (ucb *UserCreateBulk) ExecX(ctx context.Context) {
	if err := ucb.Exec(ctx); err != nil {
		panic(err)
	}
}
