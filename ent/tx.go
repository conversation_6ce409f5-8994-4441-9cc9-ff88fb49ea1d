// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	stdsql "database/sql"
	"fmt"
	"sync"

	"entgo.io/ent/dialect"
)

// Tx is a transactional client that is created by calling Client.Tx().
type Tx struct {
	config
	// ActivityView is the client for interacting with the ActivityView builders.
	ActivityView *ActivityViewClient
	// Appointment is the client for interacting with the Appointment builders.
	Appointment *AppointmentClient
	// AppointmentHistory is the client for interacting with the AppointmentHistory builders.
	AppointmentHistory *AppointmentHistoryClient
	// Attachment is the client for interacting with the Attachment builders.
	Attachment *AttachmentClient
	// AttachmentData is the client for interacting with the AttachmentData builders.
	AttachmentData *AttachmentDataClient
	// AttachmentOperationReportView is the client for interacting with the AttachmentOperationReportView builders.
	AttachmentOperationReportView *AttachmentOperationReportViewClient
	// Bill is the client for interacting with the Bill builders.
	Bill *BillClient
	// BillData is the client for interacting with the BillData builders.
	BillData *BillDataClient
	// BillItem is the client for interacting with the BillItem builders.
	BillItem *BillItemClient
	// Bundle is the client for interacting with the Bundle builders.
	Bundle *BundleClient
	// Call is the client for interacting with the Call builders.
	Call *CallClient
	// CasbinRule is the client for interacting with the CasbinRule builders.
	CasbinRule *CasbinRuleClient
	// CashFlow is the client for interacting with the CashFlow builders.
	CashFlow *CashFlowClient
	// CashFlowItem is the client for interacting with the CashFlowItem builders.
	CashFlowItem *CashFlowItemClient
	// CashFlowNote is the client for interacting with the CashFlowNote builders.
	CashFlowNote *CashFlowNoteClient
	// DbsandentistDboAppointmentTime is the client for interacting with the DbsandentistDboAppointmentTime builders.
	DbsandentistDboAppointmentTime *DbsandentistDboAppointmentTimeClient
	// DbsandentistDboBill is the client for interacting with the DbsandentistDboBill builders.
	DbsandentistDboBill *DbsandentistDboBillClient
	// DbsandentistDboBillItem is the client for interacting with the DbsandentistDboBillItem builders.
	DbsandentistDboBillItem *DbsandentistDboBillItemClient
	// DbsandentistDboBillItemDetail is the client for interacting with the DbsandentistDboBillItemDetail builders.
	DbsandentistDboBillItemDetail *DbsandentistDboBillItemDetailClient
	// DbsandentistDboBillPayment is the client for interacting with the DbsandentistDboBillPayment builders.
	DbsandentistDboBillPayment *DbsandentistDboBillPaymentClient
	// DbsandentistDboCustomer is the client for interacting with the DbsandentistDboCustomer builders.
	DbsandentistDboCustomer *DbsandentistDboCustomerClient
	// Deal is the client for interacting with the Deal builders.
	Deal *DealClient
	// DealHistory is the client for interacting with the DealHistory builders.
	DealHistory *DealHistoryClient
	// DealStageHistory is the client for interacting with the DealStageHistory builders.
	DealStageHistory *DealStageHistoryClient
	// DealStageHistoryView is the client for interacting with the DealStageHistoryView builders.
	DealStageHistoryView *DealStageHistoryViewClient
	// DealUser is the client for interacting with the DealUser builders.
	DealUser *DealUserClient
	// DealUserRating is the client for interacting with the DealUserRating builders.
	DealUserRating *DealUserRatingClient
	// Department is the client for interacting with the Department builders.
	Department *DepartmentClient
	// Deposit is the client for interacting with the Deposit builders.
	Deposit *DepositClient
	// DepositAllocation is the client for interacting with the DepositAllocation builders.
	DepositAllocation *DepositAllocationClient
	// DepositPayment is the client for interacting with the DepositPayment builders.
	DepositPayment *DepositPaymentClient
	// Discount is the client for interacting with the Discount builders.
	Discount *DiscountClient
	// DiscountUsage is the client for interacting with the DiscountUsage builders.
	DiscountUsage *DiscountUsageClient
	// EntityHistory is the client for interacting with the EntityHistory builders.
	EntityHistory *EntityHistoryClient
	// ExportJob is the client for interacting with the ExportJob builders.
	ExportJob *ExportJobClient
	// File is the client for interacting with the File builders.
	File *FileClient
	// FileUsage is the client for interacting with the FileUsage builders.
	FileUsage *FileUsageClient
	// FormSubmission is the client for interacting with the FormSubmission builders.
	FormSubmission *FormSubmissionClient
	// Installment is the client for interacting with the Installment builders.
	Installment *InstallmentClient
	// InstallmentPlan is the client for interacting with the InstallmentPlan builders.
	InstallmentPlan *InstallmentPlanClient
	// Issue is the client for interacting with the Issue builders.
	Issue *IssueClient
	// IssueComment is the client for interacting with the IssueComment builders.
	IssueComment *IssueCommentClient
	// LocalDistrict is the client for interacting with the LocalDistrict builders.
	LocalDistrict *LocalDistrictClient
	// LocalProvince is the client for interacting with the LocalProvince builders.
	LocalProvince *LocalProvinceClient
	// LocalWard is the client for interacting with the LocalWard builders.
	LocalWard *LocalWardClient
	// Material is the client for interacting with the Material builders.
	Material *MaterialClient
	// MaterialUsage is the client for interacting with the MaterialUsage builders.
	MaterialUsage *MaterialUsageClient
	// MessageHistory is the client for interacting with the MessageHistory builders.
	MessageHistory *MessageHistoryClient
	// NewTracksReportView is the client for interacting with the NewTracksReportView builders.
	NewTracksReportView *NewTracksReportViewClient
	// Note is the client for interacting with the Note builders.
	Note *NoteClient
	// Notification is the client for interacting with the Notification builders.
	Notification *NotificationClient
	// OTP is the client for interacting with the OTP builders.
	OTP *OTPClient
	// Operation is the client for interacting with the Operation builders.
	Operation *OperationClient
	// OperationMaterial is the client for interacting with the OperationMaterial builders.
	OperationMaterial *OperationMaterialClient
	// Organization is the client for interacting with the Organization builders.
	Organization *OrganizationClient
	// Payment is the client for interacting with the Payment builders.
	Payment *PaymentClient
	// PaymentAllocation is the client for interacting with the PaymentAllocation builders.
	PaymentAllocation *PaymentAllocationClient
	// PaymentReportDetailView is the client for interacting with the PaymentReportDetailView builders.
	PaymentReportDetailView *PaymentReportDetailViewClient
	// PaymentReportView is the client for interacting with the PaymentReportView builders.
	PaymentReportView *PaymentReportViewClient
	// Person is the client for interacting with the Person builders.
	Person *PersonClient
	// PersonAssignment is the client for interacting with the PersonAssignment builders.
	PersonAssignment *PersonAssignmentClient
	// PersonData is the client for interacting with the PersonData builders.
	PersonData *PersonDataClient
	// PersonHistory is the client for interacting with the PersonHistory builders.
	PersonHistory *PersonHistoryClient
	// PersonQueryView is the client for interacting with the PersonQueryView builders.
	PersonQueryView *PersonQueryViewClient
	// PersonReferral is the client for interacting with the PersonReferral builders.
	PersonReferral *PersonReferralClient
	// PersonTimelineView is the client for interacting with the PersonTimelineView builders.
	PersonTimelineView *PersonTimelineViewClient
	// PhoneViewHistory is the client for interacting with the PhoneViewHistory builders.
	PhoneViewHistory *PhoneViewHistoryClient
	// Pipeline is the client for interacting with the Pipeline builders.
	Pipeline *PipelineClient
	// Product is the client for interacting with the Product builders.
	Product *ProductClient
	// ProductOperation is the client for interacting with the ProductOperation builders.
	ProductOperation *ProductOperationClient
	// Referral is the client for interacting with the Referral builders.
	Referral *ReferralClient
	// Schedule is the client for interacting with the Schedule builders.
	Schedule *ScheduleClient
	// Setting is the client for interacting with the Setting builders.
	Setting *SettingClient
	// Stage is the client for interacting with the Stage builders.
	Stage *StageClient
	// Tag is the client for interacting with the Tag builders.
	Tag *TagClient
	// TagDeal is the client for interacting with the TagDeal builders.
	TagDeal *TagDealClient
	// TagPerson is the client for interacting with the TagPerson builders.
	TagPerson *TagPersonClient
	// Task is the client for interacting with the Task builders.
	Task *TaskClient
	// TaskAssignment is the client for interacting with the TaskAssignment builders.
	TaskAssignment *TaskAssignmentClient
	// TaskAssignmentView is the client for interacting with the TaskAssignmentView builders.
	TaskAssignmentView *TaskAssignmentViewClient
	// TaskDepartment is the client for interacting with the TaskDepartment builders.
	TaskDepartment *TaskDepartmentClient
	// TaskHistory is the client for interacting with the TaskHistory builders.
	TaskHistory *TaskHistoryClient
	// TaskNote is the client for interacting with the TaskNote builders.
	TaskNote *TaskNoteClient
	// TaskRecurring is the client for interacting with the TaskRecurring builders.
	TaskRecurring *TaskRecurringClient
	// TaskSerialView is the client for interacting with the TaskSerialView builders.
	TaskSerialView *TaskSerialViewClient
	// Term is the client for interacting with the Term builders.
	Term *TermClient
	// Track is the client for interacting with the Track builders.
	Track *TrackClient
	// UnifiedHistoryView is the client for interacting with the UnifiedHistoryView builders.
	UnifiedHistoryView *UnifiedHistoryViewClient
	// User is the client for interacting with the User builders.
	User *UserClient
	// UserData is the client for interacting with the UserData builders.
	UserData *UserDataClient

	// lazily loaded.
	client     *Client
	clientOnce sync.Once
	// ctx lives for the life of the transaction. It is
	// the same context used by the underlying connection.
	ctx context.Context
}

type (
	// Committer is the interface that wraps the Commit method.
	Committer interface {
		Commit(context.Context, *Tx) error
	}

	// The CommitFunc type is an adapter to allow the use of ordinary
	// function as a Committer. If f is a function with the appropriate
	// signature, CommitFunc(f) is a Committer that calls f.
	CommitFunc func(context.Context, *Tx) error

	// CommitHook defines the "commit middleware". A function that gets a Committer
	// and returns a Committer. For example:
	//
	//	hook := func(next ent.Committer) ent.Committer {
	//		return ent.CommitFunc(func(ctx context.Context, tx *ent.Tx) error {
	//			// Do some stuff before.
	//			if err := next.Commit(ctx, tx); err != nil {
	//				return err
	//			}
	//			// Do some stuff after.
	//			return nil
	//		})
	//	}
	//
	CommitHook func(Committer) Committer
)

// Commit calls f(ctx, m).
func (f CommitFunc) Commit(ctx context.Context, tx *Tx) error {
	return f(ctx, tx)
}

// Commit commits the transaction.
func (tx *Tx) Commit() error {
	txDriver := tx.config.driver.(*txDriver)
	var fn Committer = CommitFunc(func(context.Context, *Tx) error {
		return txDriver.tx.Commit()
	})
	txDriver.mu.Lock()
	hooks := append([]CommitHook(nil), txDriver.onCommit...)
	txDriver.mu.Unlock()
	for i := len(hooks) - 1; i >= 0; i-- {
		fn = hooks[i](fn)
	}
	return fn.Commit(tx.ctx, tx)
}

// OnCommit adds a hook to call on commit.
func (tx *Tx) OnCommit(f CommitHook) {
	txDriver := tx.config.driver.(*txDriver)
	txDriver.mu.Lock()
	txDriver.onCommit = append(txDriver.onCommit, f)
	txDriver.mu.Unlock()
}

type (
	// Rollbacker is the interface that wraps the Rollback method.
	Rollbacker interface {
		Rollback(context.Context, *Tx) error
	}

	// The RollbackFunc type is an adapter to allow the use of ordinary
	// function as a Rollbacker. If f is a function with the appropriate
	// signature, RollbackFunc(f) is a Rollbacker that calls f.
	RollbackFunc func(context.Context, *Tx) error

	// RollbackHook defines the "rollback middleware". A function that gets a Rollbacker
	// and returns a Rollbacker. For example:
	//
	//	hook := func(next ent.Rollbacker) ent.Rollbacker {
	//		return ent.RollbackFunc(func(ctx context.Context, tx *ent.Tx) error {
	//			// Do some stuff before.
	//			if err := next.Rollback(ctx, tx); err != nil {
	//				return err
	//			}
	//			// Do some stuff after.
	//			return nil
	//		})
	//	}
	//
	RollbackHook func(Rollbacker) Rollbacker
)

// Rollback calls f(ctx, m).
func (f RollbackFunc) Rollback(ctx context.Context, tx *Tx) error {
	return f(ctx, tx)
}

// Rollback rollbacks the transaction.
func (tx *Tx) Rollback() error {
	txDriver := tx.config.driver.(*txDriver)
	var fn Rollbacker = RollbackFunc(func(context.Context, *Tx) error {
		return txDriver.tx.Rollback()
	})
	txDriver.mu.Lock()
	hooks := append([]RollbackHook(nil), txDriver.onRollback...)
	txDriver.mu.Unlock()
	for i := len(hooks) - 1; i >= 0; i-- {
		fn = hooks[i](fn)
	}
	return fn.Rollback(tx.ctx, tx)
}

// OnRollback adds a hook to call on rollback.
func (tx *Tx) OnRollback(f RollbackHook) {
	txDriver := tx.config.driver.(*txDriver)
	txDriver.mu.Lock()
	txDriver.onRollback = append(txDriver.onRollback, f)
	txDriver.mu.Unlock()
}

// Client returns a Client that binds to current transaction.
func (tx *Tx) Client() *Client {
	tx.clientOnce.Do(func() {
		tx.client = &Client{config: tx.config}
		tx.client.init()
	})
	return tx.client
}

func (tx *Tx) init() {
	tx.ActivityView = NewActivityViewClient(tx.config)
	tx.Appointment = NewAppointmentClient(tx.config)
	tx.AppointmentHistory = NewAppointmentHistoryClient(tx.config)
	tx.Attachment = NewAttachmentClient(tx.config)
	tx.AttachmentData = NewAttachmentDataClient(tx.config)
	tx.AttachmentOperationReportView = NewAttachmentOperationReportViewClient(tx.config)
	tx.Bill = NewBillClient(tx.config)
	tx.BillData = NewBillDataClient(tx.config)
	tx.BillItem = NewBillItemClient(tx.config)
	tx.Bundle = NewBundleClient(tx.config)
	tx.Call = NewCallClient(tx.config)
	tx.CasbinRule = NewCasbinRuleClient(tx.config)
	tx.CashFlow = NewCashFlowClient(tx.config)
	tx.CashFlowItem = NewCashFlowItemClient(tx.config)
	tx.CashFlowNote = NewCashFlowNoteClient(tx.config)
	tx.DbsandentistDboAppointmentTime = NewDbsandentistDboAppointmentTimeClient(tx.config)
	tx.DbsandentistDboBill = NewDbsandentistDboBillClient(tx.config)
	tx.DbsandentistDboBillItem = NewDbsandentistDboBillItemClient(tx.config)
	tx.DbsandentistDboBillItemDetail = NewDbsandentistDboBillItemDetailClient(tx.config)
	tx.DbsandentistDboBillPayment = NewDbsandentistDboBillPaymentClient(tx.config)
	tx.DbsandentistDboCustomer = NewDbsandentistDboCustomerClient(tx.config)
	tx.Deal = NewDealClient(tx.config)
	tx.DealHistory = NewDealHistoryClient(tx.config)
	tx.DealStageHistory = NewDealStageHistoryClient(tx.config)
	tx.DealStageHistoryView = NewDealStageHistoryViewClient(tx.config)
	tx.DealUser = NewDealUserClient(tx.config)
	tx.DealUserRating = NewDealUserRatingClient(tx.config)
	tx.Department = NewDepartmentClient(tx.config)
	tx.Deposit = NewDepositClient(tx.config)
	tx.DepositAllocation = NewDepositAllocationClient(tx.config)
	tx.DepositPayment = NewDepositPaymentClient(tx.config)
	tx.Discount = NewDiscountClient(tx.config)
	tx.DiscountUsage = NewDiscountUsageClient(tx.config)
	tx.EntityHistory = NewEntityHistoryClient(tx.config)
	tx.ExportJob = NewExportJobClient(tx.config)
	tx.File = NewFileClient(tx.config)
	tx.FileUsage = NewFileUsageClient(tx.config)
	tx.FormSubmission = NewFormSubmissionClient(tx.config)
	tx.Installment = NewInstallmentClient(tx.config)
	tx.InstallmentPlan = NewInstallmentPlanClient(tx.config)
	tx.Issue = NewIssueClient(tx.config)
	tx.IssueComment = NewIssueCommentClient(tx.config)
	tx.LocalDistrict = NewLocalDistrictClient(tx.config)
	tx.LocalProvince = NewLocalProvinceClient(tx.config)
	tx.LocalWard = NewLocalWardClient(tx.config)
	tx.Material = NewMaterialClient(tx.config)
	tx.MaterialUsage = NewMaterialUsageClient(tx.config)
	tx.MessageHistory = NewMessageHistoryClient(tx.config)
	tx.NewTracksReportView = NewNewTracksReportViewClient(tx.config)
	tx.Note = NewNoteClient(tx.config)
	tx.Notification = NewNotificationClient(tx.config)
	tx.OTP = NewOTPClient(tx.config)
	tx.Operation = NewOperationClient(tx.config)
	tx.OperationMaterial = NewOperationMaterialClient(tx.config)
	tx.Organization = NewOrganizationClient(tx.config)
	tx.Payment = NewPaymentClient(tx.config)
	tx.PaymentAllocation = NewPaymentAllocationClient(tx.config)
	tx.PaymentReportDetailView = NewPaymentReportDetailViewClient(tx.config)
	tx.PaymentReportView = NewPaymentReportViewClient(tx.config)
	tx.Person = NewPersonClient(tx.config)
	tx.PersonAssignment = NewPersonAssignmentClient(tx.config)
	tx.PersonData = NewPersonDataClient(tx.config)
	tx.PersonHistory = NewPersonHistoryClient(tx.config)
	tx.PersonQueryView = NewPersonQueryViewClient(tx.config)
	tx.PersonReferral = NewPersonReferralClient(tx.config)
	tx.PersonTimelineView = NewPersonTimelineViewClient(tx.config)
	tx.PhoneViewHistory = NewPhoneViewHistoryClient(tx.config)
	tx.Pipeline = NewPipelineClient(tx.config)
	tx.Product = NewProductClient(tx.config)
	tx.ProductOperation = NewProductOperationClient(tx.config)
	tx.Referral = NewReferralClient(tx.config)
	tx.Schedule = NewScheduleClient(tx.config)
	tx.Setting = NewSettingClient(tx.config)
	tx.Stage = NewStageClient(tx.config)
	tx.Tag = NewTagClient(tx.config)
	tx.TagDeal = NewTagDealClient(tx.config)
	tx.TagPerson = NewTagPersonClient(tx.config)
	tx.Task = NewTaskClient(tx.config)
	tx.TaskAssignment = NewTaskAssignmentClient(tx.config)
	tx.TaskAssignmentView = NewTaskAssignmentViewClient(tx.config)
	tx.TaskDepartment = NewTaskDepartmentClient(tx.config)
	tx.TaskHistory = NewTaskHistoryClient(tx.config)
	tx.TaskNote = NewTaskNoteClient(tx.config)
	tx.TaskRecurring = NewTaskRecurringClient(tx.config)
	tx.TaskSerialView = NewTaskSerialViewClient(tx.config)
	tx.Term = NewTermClient(tx.config)
	tx.Track = NewTrackClient(tx.config)
	tx.UnifiedHistoryView = NewUnifiedHistoryViewClient(tx.config)
	tx.User = NewUserClient(tx.config)
	tx.UserData = NewUserDataClient(tx.config)
}

// txDriver wraps the given dialect.Tx with a nop dialect.Driver implementation.
// The idea is to support transactions without adding any extra code to the builders.
// When a builder calls to driver.Tx(), it gets the same dialect.Tx instance.
// Commit and Rollback are nop for the internal builders and the user must call one
// of them in order to commit or rollback the transaction.
//
// If a closed transaction is embedded in one of the generated entities, and the entity
// applies a query, for example: ActivityView.QueryXXX(), the query will be executed
// through the driver which created this transaction.
//
// Note that txDriver is not goroutine safe.
type txDriver struct {
	// the driver we started the transaction from.
	drv dialect.Driver
	// tx is the underlying transaction.
	tx dialect.Tx
	// completion hooks.
	mu         sync.Mutex
	onCommit   []CommitHook
	onRollback []RollbackHook
}

// newTx creates a new transactional driver.
func newTx(ctx context.Context, drv dialect.Driver) (*txDriver, error) {
	tx, err := drv.Tx(ctx)
	if err != nil {
		return nil, err
	}
	return &txDriver{tx: tx, drv: drv}, nil
}

// Tx returns the transaction wrapper (txDriver) to avoid Commit or Rollback calls
// from the internal builders. Should be called only by the internal builders.
func (tx *txDriver) Tx(context.Context) (dialect.Tx, error) { return tx, nil }

// Dialect returns the dialect of the driver we started the transaction from.
func (tx *txDriver) Dialect() string { return tx.drv.Dialect() }

// Close is a nop close.
func (*txDriver) Close() error { return nil }

// Commit is a nop commit for the internal builders.
// User must call `Tx.Commit` in order to commit the transaction.
func (*txDriver) Commit() error { return nil }

// Rollback is a nop rollback for the internal builders.
// User must call `Tx.Rollback` in order to rollback the transaction.
func (*txDriver) Rollback() error { return nil }

// Exec calls tx.Exec.
func (tx *txDriver) Exec(ctx context.Context, query string, args, v any) error {
	return tx.tx.Exec(ctx, query, args, v)
}

// Query calls tx.Query.
func (tx *txDriver) Query(ctx context.Context, query string, args, v any) error {
	return tx.tx.Query(ctx, query, args, v)
}

var _ dialect.Driver = (*txDriver)(nil)

// ExecContext allows calling the underlying ExecContext method of the transaction if it is supported by it.
// See, database/sql#Tx.ExecContext for more information.
func (tx *txDriver) ExecContext(ctx context.Context, query string, args ...any) (stdsql.Result, error) {
	ex, ok := tx.tx.(interface {
		ExecContext(context.Context, string, ...any) (stdsql.Result, error)
	})
	if !ok {
		return nil, fmt.Errorf("Tx.ExecContext is not supported")
	}
	return ex.ExecContext(ctx, query, args...)
}

// QueryContext allows calling the underlying QueryContext method of the transaction if it is supported by it.
// See, database/sql#Tx.QueryContext for more information.
func (tx *txDriver) QueryContext(ctx context.Context, query string, args ...any) (*stdsql.Rows, error) {
	q, ok := tx.tx.(interface {
		QueryContext(context.Context, string, ...any) (*stdsql.Rows, error)
	})
	if !ok {
		return nil, fmt.Errorf("Tx.QueryContext is not supported")
	}
	return q.QueryContext(ctx, query, args...)
}
