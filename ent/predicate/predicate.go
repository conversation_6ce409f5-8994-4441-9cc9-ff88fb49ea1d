// Code generated by ent, DO NOT EDIT.

package predicate

import (
	"entgo.io/ent/dialect/sql"
)

// ActivityView is the predicate function for activityview builders.
type ActivityView func(*sql.Selector)

// Appointment is the predicate function for appointment builders.
type Appointment func(*sql.Selector)

// Attachment is the predicate function for attachment builders.
type Attachment func(*sql.Selector)

// AttachmentData is the predicate function for attachmentdata builders.
type AttachmentData func(*sql.Selector)

// AttachmentOperationReportView is the predicate function for attachmentoperationreportview builders.
type AttachmentOperationReportView func(*sql.Selector)

// Bill is the predicate function for bill builders.
type Bill func(*sql.Selector)

// BillData is the predicate function for billdata builders.
type BillData func(*sql.Selector)

// BillItem is the predicate function for billitem builders.
type BillItem func(*sql.Selector)

// Bundle is the predicate function for bundle builders.
type Bundle func(*sql.Selector)

// Call is the predicate function for call builders.
type Call func(*sql.Selector)

// CasbinRule is the predicate function for casbinrule builders.
type CasbinRule func(*sql.Selector)

// Deal is the predicate function for deal builders.
type Deal func(*sql.Selector)

// DealUser is the predicate function for dealuser builders.
type DealUser func(*sql.Selector)

// Department is the predicate function for department builders.
type Department func(*sql.Selector)

// Deposit is the predicate function for deposit builders.
type Deposit func(*sql.Selector)

// DepositAllocation is the predicate function for depositallocation builders.
type DepositAllocation func(*sql.Selector)

// DepositPayment is the predicate function for depositpayment builders.
type DepositPayment func(*sql.Selector)

// Discount is the predicate function for discount builders.
type Discount func(*sql.Selector)

// DiscountUsage is the predicate function for discountusage builders.
type DiscountUsage func(*sql.Selector)

// File is the predicate function for file builders.
type File func(*sql.Selector)

// FileUsage is the predicate function for fileusage builders.
type FileUsage func(*sql.Selector)

// Installment is the predicate function for installment builders.
type Installment func(*sql.Selector)

// InstallmentPlan is the predicate function for installmentplan builders.
type InstallmentPlan func(*sql.Selector)

// Issue is the predicate function for issue builders.
type Issue func(*sql.Selector)

// IssueComment is the predicate function for issuecomment builders.
type IssueComment func(*sql.Selector)

// LocalDistrict is the predicate function for localdistrict builders.
type LocalDistrict func(*sql.Selector)

// LocalProvince is the predicate function for localprovince builders.
type LocalProvince func(*sql.Selector)

// LocalWard is the predicate function for localward builders.
type LocalWard func(*sql.Selector)

// MessageHistory is the predicate function for messagehistory builders.
type MessageHistory func(*sql.Selector)

// Note is the predicate function for note builders.
type Note func(*sql.Selector)

// OTP is the predicate function for otp builders.
type OTP func(*sql.Selector)

// Operation is the predicate function for operation builders.
type Operation func(*sql.Selector)

// Organization is the predicate function for organization builders.
type Organization func(*sql.Selector)

// Payment is the predicate function for payment builders.
type Payment func(*sql.Selector)

// PaymentAllocation is the predicate function for paymentallocation builders.
type PaymentAllocation func(*sql.Selector)

// PaymentReportDetailView is the predicate function for paymentreportdetailview builders.
type PaymentReportDetailView func(*sql.Selector)

// PaymentReportView is the predicate function for paymentreportview builders.
type PaymentReportView func(*sql.Selector)

// Person is the predicate function for person builders.
type Person func(*sql.Selector)

// PersonAssignment is the predicate function for personassignment builders.
type PersonAssignment func(*sql.Selector)

// PersonData is the predicate function for persondata builders.
type PersonData func(*sql.Selector)

// PersonQueryView is the predicate function for personqueryview builders.
type PersonQueryView func(*sql.Selector)

// PersonReferral is the predicate function for personreferral builders.
type PersonReferral func(*sql.Selector)

// PersonStage is the predicate function for personstage builders.
type PersonStage func(*sql.Selector)

// PersonTimelineView is the predicate function for persontimelineview builders.
type PersonTimelineView func(*sql.Selector)

// Pipeline is the predicate function for pipeline builders.
type Pipeline func(*sql.Selector)

// Product is the predicate function for product builders.
type Product func(*sql.Selector)

// ProductOperation is the predicate function for productoperation builders.
type ProductOperation func(*sql.Selector)

// Referral is the predicate function for referral builders.
type Referral func(*sql.Selector)

// Schedule is the predicate function for schedule builders.
type Schedule func(*sql.Selector)

// Setting is the predicate function for setting builders.
type Setting func(*sql.Selector)

// Stage is the predicate function for stage builders.
type Stage func(*sql.Selector)

// Task is the predicate function for task builders.
type Task func(*sql.Selector)

// TaskAssignment is the predicate function for taskassignment builders.
type TaskAssignment func(*sql.Selector)

// TaskAssignmentView is the predicate function for taskassignmentview builders.
type TaskAssignmentView func(*sql.Selector)

// TaskDepartment is the predicate function for taskdepartment builders.
type TaskDepartment func(*sql.Selector)

// TaskNote is the predicate function for tasknote builders.
type TaskNote func(*sql.Selector)

// TaskRecurring is the predicate function for taskrecurring builders.
type TaskRecurring func(*sql.Selector)

// TaskSerialView is the predicate function for taskserialview builders.
type TaskSerialView func(*sql.Selector)

// Term is the predicate function for term builders.
type Term func(*sql.Selector)

// Track is the predicate function for track builders.
type Track func(*sql.Selector)

// User is the predicate function for user builders.
type User func(*sql.Selector)

// UserData is the predicate function for userdata builders.
type UserData func(*sql.Selector)
