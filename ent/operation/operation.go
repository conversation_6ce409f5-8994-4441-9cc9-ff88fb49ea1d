// Code generated by ent, DO NOT EDIT.

package operation

import (
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

const (
	// Label holds the string label denoting the operation type in the database.
	Label = "operation"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldDeletedAt holds the string denoting the deleted_at field in the database.
	FieldDeletedAt = "deleted_at"
	// FieldStatus holds the string denoting the status field in the database.
	FieldStatus = "status"
	// FieldVersion holds the string denoting the version field in the database.
	FieldVersion = "version"
	// FieldCreatedAt holds the string denoting the created_at field in the database.
	FieldCreatedAt = "created_at"
	// FieldUpdatedAt holds the string denoting the updated_at field in the database.
	FieldUpdatedAt = "updated_at"
	// FieldName holds the string denoting the name field in the database.
	FieldName = "name"
	// FieldDescription holds the string denoting the description field in the database.
	FieldDescription = "description"
	// FieldDuration holds the string denoting the duration field in the database.
	FieldDuration = "duration"
	// FieldGroup holds the string denoting the group field in the database.
	FieldGroup = "group"
	// EdgeAssignedProduct holds the string denoting the assigned_product edge name in mutations.
	EdgeAssignedProduct = "assigned_product"
	// EdgeProductOperation holds the string denoting the product_operation edge name in mutations.
	EdgeProductOperation = "product_operation"
	// Table holds the table name of the operation in the database.
	Table = "operation"
	// AssignedProductTable is the table that holds the assigned_product relation/edge. The primary key declared below.
	AssignedProductTable = "product_operation"
	// AssignedProductInverseTable is the table name for the Product entity.
	// It exists in this package in order to avoid circular dependency with the "product" package.
	AssignedProductInverseTable = "product"
	// ProductOperationTable is the table that holds the product_operation relation/edge.
	ProductOperationTable = "product_operation"
	// ProductOperationInverseTable is the table name for the ProductOperation entity.
	// It exists in this package in order to avoid circular dependency with the "productoperation" package.
	ProductOperationInverseTable = "product_operation"
	// ProductOperationColumn is the table column denoting the product_operation relation/edge.
	ProductOperationColumn = "operation_id"
)

// Columns holds all SQL columns for operation fields.
var Columns = []string{
	FieldID,
	FieldDeletedAt,
	FieldStatus,
	FieldVersion,
	FieldCreatedAt,
	FieldUpdatedAt,
	FieldName,
	FieldDescription,
	FieldDuration,
	FieldGroup,
}

var (
	// AssignedProductPrimaryKey and AssignedProductColumn2 are the table columns denoting the
	// primary key for the assigned_product relation (M2M).
	AssignedProductPrimaryKey = []string{"product_id", "operation_id"}
)

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

// Note that the variables below are initialized by the runtime
// package on the initialization of the application. Therefore,
// it should be imported in the main as follows:
//
//	import _ "bcare/ent/runtime"
var (
	Hooks        [2]ent.Hook
	Interceptors [1]ent.Interceptor
	// DefaultStatus holds the default value on creation for the "status" field.
	DefaultStatus int8
	// DefaultVersion holds the default value on creation for the "version" field.
	DefaultVersion int
	// DefaultCreatedAt holds the default value on creation for the "created_at" field.
	DefaultCreatedAt func() time.Time
	// DefaultUpdatedAt holds the default value on creation for the "updated_at" field.
	DefaultUpdatedAt func() time.Time
	// UpdateDefaultUpdatedAt holds the default value on update for the "updated_at" field.
	UpdateDefaultUpdatedAt func() time.Time
)

// OrderOption defines the ordering options for the Operation queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByDeletedAt orders the results by the deleted_at field.
func ByDeletedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDeletedAt, opts...).ToFunc()
}

// ByStatus orders the results by the status field.
func ByStatus(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldStatus, opts...).ToFunc()
}

// ByVersion orders the results by the version field.
func ByVersion(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldVersion, opts...).ToFunc()
}

// ByCreatedAt orders the results by the created_at field.
func ByCreatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedAt, opts...).ToFunc()
}

// ByUpdatedAt orders the results by the updated_at field.
func ByUpdatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdatedAt, opts...).ToFunc()
}

// ByName orders the results by the name field.
func ByName(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldName, opts...).ToFunc()
}

// ByDescription orders the results by the description field.
func ByDescription(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDescription, opts...).ToFunc()
}

// ByDuration orders the results by the duration field.
func ByDuration(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDuration, opts...).ToFunc()
}

// ByAssignedProductCount orders the results by assigned_product count.
func ByAssignedProductCount(opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborsCount(s, newAssignedProductStep(), opts...)
	}
}

// ByAssignedProduct orders the results by assigned_product terms.
func ByAssignedProduct(term sql.OrderTerm, terms ...sql.OrderTerm) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newAssignedProductStep(), append([]sql.OrderTerm{term}, terms...)...)
	}
}

// ByProductOperationCount orders the results by product_operation count.
func ByProductOperationCount(opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborsCount(s, newProductOperationStep(), opts...)
	}
}

// ByProductOperation orders the results by product_operation terms.
func ByProductOperation(term sql.OrderTerm, terms ...sql.OrderTerm) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newProductOperationStep(), append([]sql.OrderTerm{term}, terms...)...)
	}
}
func newAssignedProductStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(AssignedProductInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2M, true, AssignedProductTable, AssignedProductPrimaryKey...),
	)
}
func newProductOperationStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(ProductOperationInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.O2M, true, ProductOperationTable, ProductOperationColumn),
	)
}
