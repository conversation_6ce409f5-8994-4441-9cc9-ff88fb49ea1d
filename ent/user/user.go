// Code generated by ent, DO NOT EDIT.

package user

import (
	"fmt"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

const (
	// Label holds the string label denoting the user type in the database.
	Label = "user"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldDeletedAt holds the string denoting the deleted_at field in the database.
	FieldDeletedAt = "deleted_at"
	// FieldStatus holds the string denoting the status field in the database.
	FieldStatus = "status"
	// FieldVersion holds the string denoting the version field in the database.
	FieldVersion = "version"
	// FieldCreatedAt holds the string denoting the created_at field in the database.
	FieldCreatedAt = "created_at"
	// FieldUpdatedAt holds the string denoting the updated_at field in the database.
	FieldUpdatedAt = "updated_at"
	// FieldUsername holds the string denoting the username field in the database.
	FieldUsername = "username"
	// FieldPassword holds the string denoting the password field in the database.
	FieldPassword = "password"
	// FieldPhone holds the string denoting the phone field in the database.
	FieldPhone = "phone"
	// FieldEmail holds the string denoting the email field in the database.
	FieldEmail = "email"
	// FieldEmailConfirmed holds the string denoting the email_confirmed field in the database.
	FieldEmailConfirmed = "email_confirmed"
	// FieldName holds the string denoting the name field in the database.
	FieldName = "name"
	// FieldGender holds the string denoting the gender field in the database.
	FieldGender = "gender"
	// FieldDepartmentID holds the string denoting the department_id field in the database.
	FieldDepartmentID = "department_id"
	// FieldDepartmentPosition holds the string denoting the department_position field in the database.
	FieldDepartmentPosition = "department_position"
	// FieldProfileImage holds the string denoting the profile_image field in the database.
	FieldProfileImage = "profile_image"
	// FieldSuspendedAt holds the string denoting the suspended_at field in the database.
	FieldSuspendedAt = "suspended_at"
	// FieldState holds the string denoting the state field in the database.
	FieldState = "state"
	// EdgeAssignment holds the string denoting the assignment edge name in mutations.
	EdgeAssignment = "assignment"
	// EdgeAssignedPerson holds the string denoting the assigned_person edge name in mutations.
	EdgeAssignedPerson = "assigned_person"
	// EdgeAssignedDeal holds the string denoting the assigned_deal edge name in mutations.
	EdgeAssignedDeal = "assigned_deal"
	// EdgeNotes holds the string denoting the notes edge name in mutations.
	EdgeNotes = "notes"
	// EdgeTaskNotes holds the string denoting the task_notes edge name in mutations.
	EdgeTaskNotes = "task_notes"
	// EdgeAppointments holds the string denoting the appointments edge name in mutations.
	EdgeAppointments = "appointments"
	// EdgeCalls holds the string denoting the calls edge name in mutations.
	EdgeCalls = "calls"
	// EdgeSchedules holds the string denoting the schedules edge name in mutations.
	EdgeSchedules = "schedules"
	// EdgeMessageHistories holds the string denoting the message_histories edge name in mutations.
	EdgeMessageHistories = "message_histories"
	// EdgeData holds the string denoting the data edge name in mutations.
	EdgeData = "data"
	// EdgePersonAssignment holds the string denoting the person_assignment edge name in mutations.
	EdgePersonAssignment = "person_assignment"
	// EdgeDealAssignment holds the string denoting the deal_assignment edge name in mutations.
	EdgeDealAssignment = "deal_assignment"
	// Table holds the table name of the user in the database.
	Table = "user"
	// AssignmentTable is the table that holds the assignment relation/edge.
	AssignmentTable = "task_assignment"
	// AssignmentInverseTable is the table name for the TaskAssignment entity.
	// It exists in this package in order to avoid circular dependency with the "taskassignment" package.
	AssignmentInverseTable = "task_assignment"
	// AssignmentColumn is the table column denoting the assignment relation/edge.
	AssignmentColumn = "user_id"
	// AssignedPersonTable is the table that holds the assigned_person relation/edge. The primary key declared below.
	AssignedPersonTable = "person_assignment"
	// AssignedPersonInverseTable is the table name for the Person entity.
	// It exists in this package in order to avoid circular dependency with the "person" package.
	AssignedPersonInverseTable = "person"
	// AssignedDealTable is the table that holds the assigned_deal relation/edge. The primary key declared below.
	AssignedDealTable = "deal_user"
	// AssignedDealInverseTable is the table name for the Deal entity.
	// It exists in this package in order to avoid circular dependency with the "deal" package.
	AssignedDealInverseTable = "deal"
	// NotesTable is the table that holds the notes relation/edge.
	NotesTable = "note"
	// NotesInverseTable is the table name for the Note entity.
	// It exists in this package in order to avoid circular dependency with the "note" package.
	NotesInverseTable = "note"
	// NotesColumn is the table column denoting the notes relation/edge.
	NotesColumn = "user_id"
	// TaskNotesTable is the table that holds the task_notes relation/edge.
	TaskNotesTable = "task_note"
	// TaskNotesInverseTable is the table name for the TaskNote entity.
	// It exists in this package in order to avoid circular dependency with the "tasknote" package.
	TaskNotesInverseTable = "task_note"
	// TaskNotesColumn is the table column denoting the task_notes relation/edge.
	TaskNotesColumn = "user_id"
	// AppointmentsTable is the table that holds the appointments relation/edge.
	AppointmentsTable = "appointment"
	// AppointmentsInverseTable is the table name for the Appointment entity.
	// It exists in this package in order to avoid circular dependency with the "appointment" package.
	AppointmentsInverseTable = "appointment"
	// AppointmentsColumn is the table column denoting the appointments relation/edge.
	AppointmentsColumn = "doctor_id"
	// CallsTable is the table that holds the calls relation/edge.
	CallsTable = "call"
	// CallsInverseTable is the table name for the Call entity.
	// It exists in this package in order to avoid circular dependency with the "call" package.
	CallsInverseTable = "call"
	// CallsColumn is the table column denoting the calls relation/edge.
	CallsColumn = "user_id"
	// SchedulesTable is the table that holds the schedules relation/edge.
	SchedulesTable = "schedule"
	// SchedulesInverseTable is the table name for the Schedule entity.
	// It exists in this package in order to avoid circular dependency with the "schedule" package.
	SchedulesInverseTable = "schedule"
	// SchedulesColumn is the table column denoting the schedules relation/edge.
	SchedulesColumn = "user_id"
	// MessageHistoriesTable is the table that holds the message_histories relation/edge.
	MessageHistoriesTable = "message_history"
	// MessageHistoriesInverseTable is the table name for the MessageHistory entity.
	// It exists in this package in order to avoid circular dependency with the "messagehistory" package.
	MessageHistoriesInverseTable = "message_history"
	// MessageHistoriesColumn is the table column denoting the message_histories relation/edge.
	MessageHistoriesColumn = "user_id"
	// DataTable is the table that holds the data relation/edge.
	DataTable = "user_data"
	// DataInverseTable is the table name for the UserData entity.
	// It exists in this package in order to avoid circular dependency with the "userdata" package.
	DataInverseTable = "user_data"
	// DataColumn is the table column denoting the data relation/edge.
	DataColumn = "user_id"
	// PersonAssignmentTable is the table that holds the person_assignment relation/edge.
	PersonAssignmentTable = "person_assignment"
	// PersonAssignmentInverseTable is the table name for the PersonAssignment entity.
	// It exists in this package in order to avoid circular dependency with the "personassignment" package.
	PersonAssignmentInverseTable = "person_assignment"
	// PersonAssignmentColumn is the table column denoting the person_assignment relation/edge.
	PersonAssignmentColumn = "user_id"
	// DealAssignmentTable is the table that holds the deal_assignment relation/edge.
	DealAssignmentTable = "deal_user"
	// DealAssignmentInverseTable is the table name for the DealUser entity.
	// It exists in this package in order to avoid circular dependency with the "dealuser" package.
	DealAssignmentInverseTable = "deal_user"
	// DealAssignmentColumn is the table column denoting the deal_assignment relation/edge.
	DealAssignmentColumn = "user_id"
)

// Columns holds all SQL columns for user fields.
var Columns = []string{
	FieldID,
	FieldDeletedAt,
	FieldStatus,
	FieldVersion,
	FieldCreatedAt,
	FieldUpdatedAt,
	FieldUsername,
	FieldPassword,
	FieldPhone,
	FieldEmail,
	FieldEmailConfirmed,
	FieldName,
	FieldGender,
	FieldDepartmentID,
	FieldDepartmentPosition,
	FieldProfileImage,
	FieldSuspendedAt,
	FieldState,
}

var (
	// AssignedPersonPrimaryKey and AssignedPersonColumn2 are the table columns denoting the
	// primary key for the assigned_person relation (M2M).
	AssignedPersonPrimaryKey = []string{"person_id", "user_id"}
	// AssignedDealPrimaryKey and AssignedDealColumn2 are the table columns denoting the
	// primary key for the assigned_deal relation (M2M).
	AssignedDealPrimaryKey = []string{"deal_id", "user_id"}
)

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

// Note that the variables below are initialized by the runtime
// package on the initialization of the application. Therefore,
// it should be imported in the main as follows:
//
//	import _ "bcare/ent/runtime"
var (
	Hooks        [2]ent.Hook
	Interceptors [1]ent.Interceptor
	// DefaultStatus holds the default value on creation for the "status" field.
	DefaultStatus int8
	// DefaultVersion holds the default value on creation for the "version" field.
	DefaultVersion int
	// DefaultCreatedAt holds the default value on creation for the "created_at" field.
	DefaultCreatedAt func() time.Time
	// DefaultUpdatedAt holds the default value on creation for the "updated_at" field.
	DefaultUpdatedAt func() time.Time
	// UpdateDefaultUpdatedAt holds the default value on update for the "updated_at" field.
	UpdateDefaultUpdatedAt func() time.Time
)

// State defines the type for the "state" enum field.
type State string

// StateOffline is the default value of the State enum.
const DefaultState = StateOffline

// State values.
const (
	StateOnline   State = "online"
	StateOffline  State = "offline"
	StateInactive State = "inactive"
	StateBusy     State = "busy"
)

func (s State) String() string {
	return string(s)
}

// StateValidator is a validator for the "state" field enum values. It is called by the builders before save.
func StateValidator(s State) error {
	switch s {
	case StateOnline, StateOffline, StateInactive, StateBusy:
		return nil
	default:
		return fmt.Errorf("user: invalid enum value for state field: %q", s)
	}
}

// OrderOption defines the ordering options for the User queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByDeletedAt orders the results by the deleted_at field.
func ByDeletedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDeletedAt, opts...).ToFunc()
}

// ByStatus orders the results by the status field.
func ByStatus(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldStatus, opts...).ToFunc()
}

// ByVersion orders the results by the version field.
func ByVersion(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldVersion, opts...).ToFunc()
}

// ByCreatedAt orders the results by the created_at field.
func ByCreatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedAt, opts...).ToFunc()
}

// ByUpdatedAt orders the results by the updated_at field.
func ByUpdatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdatedAt, opts...).ToFunc()
}

// ByUsername orders the results by the username field.
func ByUsername(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUsername, opts...).ToFunc()
}

// ByPassword orders the results by the password field.
func ByPassword(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPassword, opts...).ToFunc()
}

// ByPhone orders the results by the phone field.
func ByPhone(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPhone, opts...).ToFunc()
}

// ByEmail orders the results by the email field.
func ByEmail(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldEmail, opts...).ToFunc()
}

// ByEmailConfirmed orders the results by the email_confirmed field.
func ByEmailConfirmed(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldEmailConfirmed, opts...).ToFunc()
}

// ByName orders the results by the name field.
func ByName(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldName, opts...).ToFunc()
}

// ByGender orders the results by the gender field.
func ByGender(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldGender, opts...).ToFunc()
}

// ByDepartmentID orders the results by the department_id field.
func ByDepartmentID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDepartmentID, opts...).ToFunc()
}

// ByDepartmentPosition orders the results by the department_position field.
func ByDepartmentPosition(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDepartmentPosition, opts...).ToFunc()
}

// ByProfileImage orders the results by the profile_image field.
func ByProfileImage(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldProfileImage, opts...).ToFunc()
}

// BySuspendedAt orders the results by the suspended_at field.
func BySuspendedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldSuspendedAt, opts...).ToFunc()
}

// ByState orders the results by the state field.
func ByState(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldState, opts...).ToFunc()
}

// ByAssignmentCount orders the results by assignment count.
func ByAssignmentCount(opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborsCount(s, newAssignmentStep(), opts...)
	}
}

// ByAssignment orders the results by assignment terms.
func ByAssignment(term sql.OrderTerm, terms ...sql.OrderTerm) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newAssignmentStep(), append([]sql.OrderTerm{term}, terms...)...)
	}
}

// ByAssignedPersonCount orders the results by assigned_person count.
func ByAssignedPersonCount(opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborsCount(s, newAssignedPersonStep(), opts...)
	}
}

// ByAssignedPerson orders the results by assigned_person terms.
func ByAssignedPerson(term sql.OrderTerm, terms ...sql.OrderTerm) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newAssignedPersonStep(), append([]sql.OrderTerm{term}, terms...)...)
	}
}

// ByAssignedDealCount orders the results by assigned_deal count.
func ByAssignedDealCount(opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborsCount(s, newAssignedDealStep(), opts...)
	}
}

// ByAssignedDeal orders the results by assigned_deal terms.
func ByAssignedDeal(term sql.OrderTerm, terms ...sql.OrderTerm) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newAssignedDealStep(), append([]sql.OrderTerm{term}, terms...)...)
	}
}

// ByNotesCount orders the results by notes count.
func ByNotesCount(opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborsCount(s, newNotesStep(), opts...)
	}
}

// ByNotes orders the results by notes terms.
func ByNotes(term sql.OrderTerm, terms ...sql.OrderTerm) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newNotesStep(), append([]sql.OrderTerm{term}, terms...)...)
	}
}

// ByTaskNotesCount orders the results by task_notes count.
func ByTaskNotesCount(opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborsCount(s, newTaskNotesStep(), opts...)
	}
}

// ByTaskNotes orders the results by task_notes terms.
func ByTaskNotes(term sql.OrderTerm, terms ...sql.OrderTerm) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newTaskNotesStep(), append([]sql.OrderTerm{term}, terms...)...)
	}
}

// ByAppointmentsCount orders the results by appointments count.
func ByAppointmentsCount(opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborsCount(s, newAppointmentsStep(), opts...)
	}
}

// ByAppointments orders the results by appointments terms.
func ByAppointments(term sql.OrderTerm, terms ...sql.OrderTerm) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newAppointmentsStep(), append([]sql.OrderTerm{term}, terms...)...)
	}
}

// ByCallsCount orders the results by calls count.
func ByCallsCount(opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborsCount(s, newCallsStep(), opts...)
	}
}

// ByCalls orders the results by calls terms.
func ByCalls(term sql.OrderTerm, terms ...sql.OrderTerm) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newCallsStep(), append([]sql.OrderTerm{term}, terms...)...)
	}
}

// BySchedulesCount orders the results by schedules count.
func BySchedulesCount(opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborsCount(s, newSchedulesStep(), opts...)
	}
}

// BySchedules orders the results by schedules terms.
func BySchedules(term sql.OrderTerm, terms ...sql.OrderTerm) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newSchedulesStep(), append([]sql.OrderTerm{term}, terms...)...)
	}
}

// ByMessageHistoriesCount orders the results by message_histories count.
func ByMessageHistoriesCount(opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborsCount(s, newMessageHistoriesStep(), opts...)
	}
}

// ByMessageHistories orders the results by message_histories terms.
func ByMessageHistories(term sql.OrderTerm, terms ...sql.OrderTerm) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newMessageHistoriesStep(), append([]sql.OrderTerm{term}, terms...)...)
	}
}

// ByDataCount orders the results by data count.
func ByDataCount(opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborsCount(s, newDataStep(), opts...)
	}
}

// ByData orders the results by data terms.
func ByData(term sql.OrderTerm, terms ...sql.OrderTerm) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newDataStep(), append([]sql.OrderTerm{term}, terms...)...)
	}
}

// ByPersonAssignmentCount orders the results by person_assignment count.
func ByPersonAssignmentCount(opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborsCount(s, newPersonAssignmentStep(), opts...)
	}
}

// ByPersonAssignment orders the results by person_assignment terms.
func ByPersonAssignment(term sql.OrderTerm, terms ...sql.OrderTerm) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newPersonAssignmentStep(), append([]sql.OrderTerm{term}, terms...)...)
	}
}

// ByDealAssignmentCount orders the results by deal_assignment count.
func ByDealAssignmentCount(opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborsCount(s, newDealAssignmentStep(), opts...)
	}
}

// ByDealAssignment orders the results by deal_assignment terms.
func ByDealAssignment(term sql.OrderTerm, terms ...sql.OrderTerm) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newDealAssignmentStep(), append([]sql.OrderTerm{term}, terms...)...)
	}
}
func newAssignmentStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(AssignmentInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.O2M, true, AssignmentTable, AssignmentColumn),
	)
}
func newAssignedPersonStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(AssignedPersonInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2M, true, AssignedPersonTable, AssignedPersonPrimaryKey...),
	)
}
func newAssignedDealStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(AssignedDealInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2M, true, AssignedDealTable, AssignedDealPrimaryKey...),
	)
}
func newNotesStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(NotesInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.O2M, true, NotesTable, NotesColumn),
	)
}
func newTaskNotesStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(TaskNotesInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.O2M, true, TaskNotesTable, TaskNotesColumn),
	)
}
func newAppointmentsStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(AppointmentsInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.O2M, true, AppointmentsTable, AppointmentsColumn),
	)
}
func newCallsStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(CallsInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.O2M, true, CallsTable, CallsColumn),
	)
}
func newSchedulesStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(SchedulesInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.O2M, true, SchedulesTable, SchedulesColumn),
	)
}
func newMessageHistoriesStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(MessageHistoriesInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.O2M, true, MessageHistoriesTable, MessageHistoriesColumn),
	)
}
func newDataStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(DataInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.O2M, true, DataTable, DataColumn),
	)
}
func newPersonAssignmentStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(PersonAssignmentInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.O2M, true, PersonAssignmentTable, PersonAssignmentColumn),
	)
}
func newDealAssignmentStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(DealAssignmentInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.O2M, true, DealAssignmentTable, DealAssignmentColumn),
	)
}
