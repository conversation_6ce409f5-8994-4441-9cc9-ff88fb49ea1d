// Code generated by ent, DO NOT EDIT.

package user

import (
	"bcare/ent/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

// ID filters vertices based on their ID field.
func ID(id int) predicate.User {
	return predicate.User(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id int) predicate.User {
	return predicate.User(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id int) predicate.User {
	return predicate.User(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...int) predicate.User {
	return predicate.User(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...int) predicate.User {
	return predicate.User(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id int) predicate.User {
	return predicate.User(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id int) predicate.User {
	return predicate.User(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id int) predicate.User {
	return predicate.User(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id int) predicate.User {
	return predicate.User(sql.FieldLTE(FieldID, id))
}

// DeletedAt applies equality check predicate on the "deleted_at" field. It's identical to DeletedAtEQ.
func DeletedAt(v time.Time) predicate.User {
	return predicate.User(sql.FieldEQ(FieldDeletedAt, v))
}

// Status applies equality check predicate on the "status" field. It's identical to StatusEQ.
func Status(v int8) predicate.User {
	return predicate.User(sql.FieldEQ(FieldStatus, v))
}

// Version applies equality check predicate on the "version" field. It's identical to VersionEQ.
func Version(v int) predicate.User {
	return predicate.User(sql.FieldEQ(FieldVersion, v))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.User {
	return predicate.User(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.User {
	return predicate.User(sql.FieldEQ(FieldUpdatedAt, v))
}

// Username applies equality check predicate on the "username" field. It's identical to UsernameEQ.
func Username(v string) predicate.User {
	return predicate.User(sql.FieldEQ(FieldUsername, v))
}

// Password applies equality check predicate on the "password" field. It's identical to PasswordEQ.
func Password(v string) predicate.User {
	return predicate.User(sql.FieldEQ(FieldPassword, v))
}

// Phone applies equality check predicate on the "phone" field. It's identical to PhoneEQ.
func Phone(v string) predicate.User {
	return predicate.User(sql.FieldEQ(FieldPhone, v))
}

// Email applies equality check predicate on the "email" field. It's identical to EmailEQ.
func Email(v string) predicate.User {
	return predicate.User(sql.FieldEQ(FieldEmail, v))
}

// EmailConfirmed applies equality check predicate on the "email_confirmed" field. It's identical to EmailConfirmedEQ.
func EmailConfirmed(v bool) predicate.User {
	return predicate.User(sql.FieldEQ(FieldEmailConfirmed, v))
}

// Name applies equality check predicate on the "name" field. It's identical to NameEQ.
func Name(v string) predicate.User {
	return predicate.User(sql.FieldEQ(FieldName, v))
}

// Gender applies equality check predicate on the "gender" field. It's identical to GenderEQ.
func Gender(v string) predicate.User {
	return predicate.User(sql.FieldEQ(FieldGender, v))
}

// DepartmentID applies equality check predicate on the "department_id" field. It's identical to DepartmentIDEQ.
func DepartmentID(v int) predicate.User {
	return predicate.User(sql.FieldEQ(FieldDepartmentID, v))
}

// DepartmentPosition applies equality check predicate on the "department_position" field. It's identical to DepartmentPositionEQ.
func DepartmentPosition(v string) predicate.User {
	return predicate.User(sql.FieldEQ(FieldDepartmentPosition, v))
}

// ProfileImage applies equality check predicate on the "profile_image" field. It's identical to ProfileImageEQ.
func ProfileImage(v string) predicate.User {
	return predicate.User(sql.FieldEQ(FieldProfileImage, v))
}

// SuspendedAt applies equality check predicate on the "suspended_at" field. It's identical to SuspendedAtEQ.
func SuspendedAt(v time.Time) predicate.User {
	return predicate.User(sql.FieldEQ(FieldSuspendedAt, v))
}

// DeletedAtEQ applies the EQ predicate on the "deleted_at" field.
func DeletedAtEQ(v time.Time) predicate.User {
	return predicate.User(sql.FieldEQ(FieldDeletedAt, v))
}

// DeletedAtNEQ applies the NEQ predicate on the "deleted_at" field.
func DeletedAtNEQ(v time.Time) predicate.User {
	return predicate.User(sql.FieldNEQ(FieldDeletedAt, v))
}

// DeletedAtIn applies the In predicate on the "deleted_at" field.
func DeletedAtIn(vs ...time.Time) predicate.User {
	return predicate.User(sql.FieldIn(FieldDeletedAt, vs...))
}

// DeletedAtNotIn applies the NotIn predicate on the "deleted_at" field.
func DeletedAtNotIn(vs ...time.Time) predicate.User {
	return predicate.User(sql.FieldNotIn(FieldDeletedAt, vs...))
}

// DeletedAtGT applies the GT predicate on the "deleted_at" field.
func DeletedAtGT(v time.Time) predicate.User {
	return predicate.User(sql.FieldGT(FieldDeletedAt, v))
}

// DeletedAtGTE applies the GTE predicate on the "deleted_at" field.
func DeletedAtGTE(v time.Time) predicate.User {
	return predicate.User(sql.FieldGTE(FieldDeletedAt, v))
}

// DeletedAtLT applies the LT predicate on the "deleted_at" field.
func DeletedAtLT(v time.Time) predicate.User {
	return predicate.User(sql.FieldLT(FieldDeletedAt, v))
}

// DeletedAtLTE applies the LTE predicate on the "deleted_at" field.
func DeletedAtLTE(v time.Time) predicate.User {
	return predicate.User(sql.FieldLTE(FieldDeletedAt, v))
}

// DeletedAtIsNil applies the IsNil predicate on the "deleted_at" field.
func DeletedAtIsNil() predicate.User {
	return predicate.User(sql.FieldIsNull(FieldDeletedAt))
}

// DeletedAtNotNil applies the NotNil predicate on the "deleted_at" field.
func DeletedAtNotNil() predicate.User {
	return predicate.User(sql.FieldNotNull(FieldDeletedAt))
}

// StatusEQ applies the EQ predicate on the "status" field.
func StatusEQ(v int8) predicate.User {
	return predicate.User(sql.FieldEQ(FieldStatus, v))
}

// StatusNEQ applies the NEQ predicate on the "status" field.
func StatusNEQ(v int8) predicate.User {
	return predicate.User(sql.FieldNEQ(FieldStatus, v))
}

// StatusIn applies the In predicate on the "status" field.
func StatusIn(vs ...int8) predicate.User {
	return predicate.User(sql.FieldIn(FieldStatus, vs...))
}

// StatusNotIn applies the NotIn predicate on the "status" field.
func StatusNotIn(vs ...int8) predicate.User {
	return predicate.User(sql.FieldNotIn(FieldStatus, vs...))
}

// StatusGT applies the GT predicate on the "status" field.
func StatusGT(v int8) predicate.User {
	return predicate.User(sql.FieldGT(FieldStatus, v))
}

// StatusGTE applies the GTE predicate on the "status" field.
func StatusGTE(v int8) predicate.User {
	return predicate.User(sql.FieldGTE(FieldStatus, v))
}

// StatusLT applies the LT predicate on the "status" field.
func StatusLT(v int8) predicate.User {
	return predicate.User(sql.FieldLT(FieldStatus, v))
}

// StatusLTE applies the LTE predicate on the "status" field.
func StatusLTE(v int8) predicate.User {
	return predicate.User(sql.FieldLTE(FieldStatus, v))
}

// VersionEQ applies the EQ predicate on the "version" field.
func VersionEQ(v int) predicate.User {
	return predicate.User(sql.FieldEQ(FieldVersion, v))
}

// VersionNEQ applies the NEQ predicate on the "version" field.
func VersionNEQ(v int) predicate.User {
	return predicate.User(sql.FieldNEQ(FieldVersion, v))
}

// VersionIn applies the In predicate on the "version" field.
func VersionIn(vs ...int) predicate.User {
	return predicate.User(sql.FieldIn(FieldVersion, vs...))
}

// VersionNotIn applies the NotIn predicate on the "version" field.
func VersionNotIn(vs ...int) predicate.User {
	return predicate.User(sql.FieldNotIn(FieldVersion, vs...))
}

// VersionGT applies the GT predicate on the "version" field.
func VersionGT(v int) predicate.User {
	return predicate.User(sql.FieldGT(FieldVersion, v))
}

// VersionGTE applies the GTE predicate on the "version" field.
func VersionGTE(v int) predicate.User {
	return predicate.User(sql.FieldGTE(FieldVersion, v))
}

// VersionLT applies the LT predicate on the "version" field.
func VersionLT(v int) predicate.User {
	return predicate.User(sql.FieldLT(FieldVersion, v))
}

// VersionLTE applies the LTE predicate on the "version" field.
func VersionLTE(v int) predicate.User {
	return predicate.User(sql.FieldLTE(FieldVersion, v))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.User {
	return predicate.User(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.User {
	return predicate.User(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.User {
	return predicate.User(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.User {
	return predicate.User(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.User {
	return predicate.User(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.User {
	return predicate.User(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.User {
	return predicate.User(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.User {
	return predicate.User(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.User {
	return predicate.User(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.User {
	return predicate.User(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.User {
	return predicate.User(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.User {
	return predicate.User(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.User {
	return predicate.User(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.User {
	return predicate.User(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.User {
	return predicate.User(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.User {
	return predicate.User(sql.FieldLTE(FieldUpdatedAt, v))
}

// UsernameEQ applies the EQ predicate on the "username" field.
func UsernameEQ(v string) predicate.User {
	return predicate.User(sql.FieldEQ(FieldUsername, v))
}

// UsernameNEQ applies the NEQ predicate on the "username" field.
func UsernameNEQ(v string) predicate.User {
	return predicate.User(sql.FieldNEQ(FieldUsername, v))
}

// UsernameIn applies the In predicate on the "username" field.
func UsernameIn(vs ...string) predicate.User {
	return predicate.User(sql.FieldIn(FieldUsername, vs...))
}

// UsernameNotIn applies the NotIn predicate on the "username" field.
func UsernameNotIn(vs ...string) predicate.User {
	return predicate.User(sql.FieldNotIn(FieldUsername, vs...))
}

// UsernameGT applies the GT predicate on the "username" field.
func UsernameGT(v string) predicate.User {
	return predicate.User(sql.FieldGT(FieldUsername, v))
}

// UsernameGTE applies the GTE predicate on the "username" field.
func UsernameGTE(v string) predicate.User {
	return predicate.User(sql.FieldGTE(FieldUsername, v))
}

// UsernameLT applies the LT predicate on the "username" field.
func UsernameLT(v string) predicate.User {
	return predicate.User(sql.FieldLT(FieldUsername, v))
}

// UsernameLTE applies the LTE predicate on the "username" field.
func UsernameLTE(v string) predicate.User {
	return predicate.User(sql.FieldLTE(FieldUsername, v))
}

// UsernameContains applies the Contains predicate on the "username" field.
func UsernameContains(v string) predicate.User {
	return predicate.User(sql.FieldContains(FieldUsername, v))
}

// UsernameHasPrefix applies the HasPrefix predicate on the "username" field.
func UsernameHasPrefix(v string) predicate.User {
	return predicate.User(sql.FieldHasPrefix(FieldUsername, v))
}

// UsernameHasSuffix applies the HasSuffix predicate on the "username" field.
func UsernameHasSuffix(v string) predicate.User {
	return predicate.User(sql.FieldHasSuffix(FieldUsername, v))
}

// UsernameEqualFold applies the EqualFold predicate on the "username" field.
func UsernameEqualFold(v string) predicate.User {
	return predicate.User(sql.FieldEqualFold(FieldUsername, v))
}

// UsernameContainsFold applies the ContainsFold predicate on the "username" field.
func UsernameContainsFold(v string) predicate.User {
	return predicate.User(sql.FieldContainsFold(FieldUsername, v))
}

// PasswordEQ applies the EQ predicate on the "password" field.
func PasswordEQ(v string) predicate.User {
	return predicate.User(sql.FieldEQ(FieldPassword, v))
}

// PasswordNEQ applies the NEQ predicate on the "password" field.
func PasswordNEQ(v string) predicate.User {
	return predicate.User(sql.FieldNEQ(FieldPassword, v))
}

// PasswordIn applies the In predicate on the "password" field.
func PasswordIn(vs ...string) predicate.User {
	return predicate.User(sql.FieldIn(FieldPassword, vs...))
}

// PasswordNotIn applies the NotIn predicate on the "password" field.
func PasswordNotIn(vs ...string) predicate.User {
	return predicate.User(sql.FieldNotIn(FieldPassword, vs...))
}

// PasswordGT applies the GT predicate on the "password" field.
func PasswordGT(v string) predicate.User {
	return predicate.User(sql.FieldGT(FieldPassword, v))
}

// PasswordGTE applies the GTE predicate on the "password" field.
func PasswordGTE(v string) predicate.User {
	return predicate.User(sql.FieldGTE(FieldPassword, v))
}

// PasswordLT applies the LT predicate on the "password" field.
func PasswordLT(v string) predicate.User {
	return predicate.User(sql.FieldLT(FieldPassword, v))
}

// PasswordLTE applies the LTE predicate on the "password" field.
func PasswordLTE(v string) predicate.User {
	return predicate.User(sql.FieldLTE(FieldPassword, v))
}

// PasswordContains applies the Contains predicate on the "password" field.
func PasswordContains(v string) predicate.User {
	return predicate.User(sql.FieldContains(FieldPassword, v))
}

// PasswordHasPrefix applies the HasPrefix predicate on the "password" field.
func PasswordHasPrefix(v string) predicate.User {
	return predicate.User(sql.FieldHasPrefix(FieldPassword, v))
}

// PasswordHasSuffix applies the HasSuffix predicate on the "password" field.
func PasswordHasSuffix(v string) predicate.User {
	return predicate.User(sql.FieldHasSuffix(FieldPassword, v))
}

// PasswordEqualFold applies the EqualFold predicate on the "password" field.
func PasswordEqualFold(v string) predicate.User {
	return predicate.User(sql.FieldEqualFold(FieldPassword, v))
}

// PasswordContainsFold applies the ContainsFold predicate on the "password" field.
func PasswordContainsFold(v string) predicate.User {
	return predicate.User(sql.FieldContainsFold(FieldPassword, v))
}

// PhoneEQ applies the EQ predicate on the "phone" field.
func PhoneEQ(v string) predicate.User {
	return predicate.User(sql.FieldEQ(FieldPhone, v))
}

// PhoneNEQ applies the NEQ predicate on the "phone" field.
func PhoneNEQ(v string) predicate.User {
	return predicate.User(sql.FieldNEQ(FieldPhone, v))
}

// PhoneIn applies the In predicate on the "phone" field.
func PhoneIn(vs ...string) predicate.User {
	return predicate.User(sql.FieldIn(FieldPhone, vs...))
}

// PhoneNotIn applies the NotIn predicate on the "phone" field.
func PhoneNotIn(vs ...string) predicate.User {
	return predicate.User(sql.FieldNotIn(FieldPhone, vs...))
}

// PhoneGT applies the GT predicate on the "phone" field.
func PhoneGT(v string) predicate.User {
	return predicate.User(sql.FieldGT(FieldPhone, v))
}

// PhoneGTE applies the GTE predicate on the "phone" field.
func PhoneGTE(v string) predicate.User {
	return predicate.User(sql.FieldGTE(FieldPhone, v))
}

// PhoneLT applies the LT predicate on the "phone" field.
func PhoneLT(v string) predicate.User {
	return predicate.User(sql.FieldLT(FieldPhone, v))
}

// PhoneLTE applies the LTE predicate on the "phone" field.
func PhoneLTE(v string) predicate.User {
	return predicate.User(sql.FieldLTE(FieldPhone, v))
}

// PhoneContains applies the Contains predicate on the "phone" field.
func PhoneContains(v string) predicate.User {
	return predicate.User(sql.FieldContains(FieldPhone, v))
}

// PhoneHasPrefix applies the HasPrefix predicate on the "phone" field.
func PhoneHasPrefix(v string) predicate.User {
	return predicate.User(sql.FieldHasPrefix(FieldPhone, v))
}

// PhoneHasSuffix applies the HasSuffix predicate on the "phone" field.
func PhoneHasSuffix(v string) predicate.User {
	return predicate.User(sql.FieldHasSuffix(FieldPhone, v))
}

// PhoneIsNil applies the IsNil predicate on the "phone" field.
func PhoneIsNil() predicate.User {
	return predicate.User(sql.FieldIsNull(FieldPhone))
}

// PhoneNotNil applies the NotNil predicate on the "phone" field.
func PhoneNotNil() predicate.User {
	return predicate.User(sql.FieldNotNull(FieldPhone))
}

// PhoneEqualFold applies the EqualFold predicate on the "phone" field.
func PhoneEqualFold(v string) predicate.User {
	return predicate.User(sql.FieldEqualFold(FieldPhone, v))
}

// PhoneContainsFold applies the ContainsFold predicate on the "phone" field.
func PhoneContainsFold(v string) predicate.User {
	return predicate.User(sql.FieldContainsFold(FieldPhone, v))
}

// EmailEQ applies the EQ predicate on the "email" field.
func EmailEQ(v string) predicate.User {
	return predicate.User(sql.FieldEQ(FieldEmail, v))
}

// EmailNEQ applies the NEQ predicate on the "email" field.
func EmailNEQ(v string) predicate.User {
	return predicate.User(sql.FieldNEQ(FieldEmail, v))
}

// EmailIn applies the In predicate on the "email" field.
func EmailIn(vs ...string) predicate.User {
	return predicate.User(sql.FieldIn(FieldEmail, vs...))
}

// EmailNotIn applies the NotIn predicate on the "email" field.
func EmailNotIn(vs ...string) predicate.User {
	return predicate.User(sql.FieldNotIn(FieldEmail, vs...))
}

// EmailGT applies the GT predicate on the "email" field.
func EmailGT(v string) predicate.User {
	return predicate.User(sql.FieldGT(FieldEmail, v))
}

// EmailGTE applies the GTE predicate on the "email" field.
func EmailGTE(v string) predicate.User {
	return predicate.User(sql.FieldGTE(FieldEmail, v))
}

// EmailLT applies the LT predicate on the "email" field.
func EmailLT(v string) predicate.User {
	return predicate.User(sql.FieldLT(FieldEmail, v))
}

// EmailLTE applies the LTE predicate on the "email" field.
func EmailLTE(v string) predicate.User {
	return predicate.User(sql.FieldLTE(FieldEmail, v))
}

// EmailContains applies the Contains predicate on the "email" field.
func EmailContains(v string) predicate.User {
	return predicate.User(sql.FieldContains(FieldEmail, v))
}

// EmailHasPrefix applies the HasPrefix predicate on the "email" field.
func EmailHasPrefix(v string) predicate.User {
	return predicate.User(sql.FieldHasPrefix(FieldEmail, v))
}

// EmailHasSuffix applies the HasSuffix predicate on the "email" field.
func EmailHasSuffix(v string) predicate.User {
	return predicate.User(sql.FieldHasSuffix(FieldEmail, v))
}

// EmailIsNil applies the IsNil predicate on the "email" field.
func EmailIsNil() predicate.User {
	return predicate.User(sql.FieldIsNull(FieldEmail))
}

// EmailNotNil applies the NotNil predicate on the "email" field.
func EmailNotNil() predicate.User {
	return predicate.User(sql.FieldNotNull(FieldEmail))
}

// EmailEqualFold applies the EqualFold predicate on the "email" field.
func EmailEqualFold(v string) predicate.User {
	return predicate.User(sql.FieldEqualFold(FieldEmail, v))
}

// EmailContainsFold applies the ContainsFold predicate on the "email" field.
func EmailContainsFold(v string) predicate.User {
	return predicate.User(sql.FieldContainsFold(FieldEmail, v))
}

// EmailConfirmedEQ applies the EQ predicate on the "email_confirmed" field.
func EmailConfirmedEQ(v bool) predicate.User {
	return predicate.User(sql.FieldEQ(FieldEmailConfirmed, v))
}

// EmailConfirmedNEQ applies the NEQ predicate on the "email_confirmed" field.
func EmailConfirmedNEQ(v bool) predicate.User {
	return predicate.User(sql.FieldNEQ(FieldEmailConfirmed, v))
}

// EmailConfirmedIsNil applies the IsNil predicate on the "email_confirmed" field.
func EmailConfirmedIsNil() predicate.User {
	return predicate.User(sql.FieldIsNull(FieldEmailConfirmed))
}

// EmailConfirmedNotNil applies the NotNil predicate on the "email_confirmed" field.
func EmailConfirmedNotNil() predicate.User {
	return predicate.User(sql.FieldNotNull(FieldEmailConfirmed))
}

// NameEQ applies the EQ predicate on the "name" field.
func NameEQ(v string) predicate.User {
	return predicate.User(sql.FieldEQ(FieldName, v))
}

// NameNEQ applies the NEQ predicate on the "name" field.
func NameNEQ(v string) predicate.User {
	return predicate.User(sql.FieldNEQ(FieldName, v))
}

// NameIn applies the In predicate on the "name" field.
func NameIn(vs ...string) predicate.User {
	return predicate.User(sql.FieldIn(FieldName, vs...))
}

// NameNotIn applies the NotIn predicate on the "name" field.
func NameNotIn(vs ...string) predicate.User {
	return predicate.User(sql.FieldNotIn(FieldName, vs...))
}

// NameGT applies the GT predicate on the "name" field.
func NameGT(v string) predicate.User {
	return predicate.User(sql.FieldGT(FieldName, v))
}

// NameGTE applies the GTE predicate on the "name" field.
func NameGTE(v string) predicate.User {
	return predicate.User(sql.FieldGTE(FieldName, v))
}

// NameLT applies the LT predicate on the "name" field.
func NameLT(v string) predicate.User {
	return predicate.User(sql.FieldLT(FieldName, v))
}

// NameLTE applies the LTE predicate on the "name" field.
func NameLTE(v string) predicate.User {
	return predicate.User(sql.FieldLTE(FieldName, v))
}

// NameContains applies the Contains predicate on the "name" field.
func NameContains(v string) predicate.User {
	return predicate.User(sql.FieldContains(FieldName, v))
}

// NameHasPrefix applies the HasPrefix predicate on the "name" field.
func NameHasPrefix(v string) predicate.User {
	return predicate.User(sql.FieldHasPrefix(FieldName, v))
}

// NameHasSuffix applies the HasSuffix predicate on the "name" field.
func NameHasSuffix(v string) predicate.User {
	return predicate.User(sql.FieldHasSuffix(FieldName, v))
}

// NameIsNil applies the IsNil predicate on the "name" field.
func NameIsNil() predicate.User {
	return predicate.User(sql.FieldIsNull(FieldName))
}

// NameNotNil applies the NotNil predicate on the "name" field.
func NameNotNil() predicate.User {
	return predicate.User(sql.FieldNotNull(FieldName))
}

// NameEqualFold applies the EqualFold predicate on the "name" field.
func NameEqualFold(v string) predicate.User {
	return predicate.User(sql.FieldEqualFold(FieldName, v))
}

// NameContainsFold applies the ContainsFold predicate on the "name" field.
func NameContainsFold(v string) predicate.User {
	return predicate.User(sql.FieldContainsFold(FieldName, v))
}

// GenderEQ applies the EQ predicate on the "gender" field.
func GenderEQ(v string) predicate.User {
	return predicate.User(sql.FieldEQ(FieldGender, v))
}

// GenderNEQ applies the NEQ predicate on the "gender" field.
func GenderNEQ(v string) predicate.User {
	return predicate.User(sql.FieldNEQ(FieldGender, v))
}

// GenderIn applies the In predicate on the "gender" field.
func GenderIn(vs ...string) predicate.User {
	return predicate.User(sql.FieldIn(FieldGender, vs...))
}

// GenderNotIn applies the NotIn predicate on the "gender" field.
func GenderNotIn(vs ...string) predicate.User {
	return predicate.User(sql.FieldNotIn(FieldGender, vs...))
}

// GenderGT applies the GT predicate on the "gender" field.
func GenderGT(v string) predicate.User {
	return predicate.User(sql.FieldGT(FieldGender, v))
}

// GenderGTE applies the GTE predicate on the "gender" field.
func GenderGTE(v string) predicate.User {
	return predicate.User(sql.FieldGTE(FieldGender, v))
}

// GenderLT applies the LT predicate on the "gender" field.
func GenderLT(v string) predicate.User {
	return predicate.User(sql.FieldLT(FieldGender, v))
}

// GenderLTE applies the LTE predicate on the "gender" field.
func GenderLTE(v string) predicate.User {
	return predicate.User(sql.FieldLTE(FieldGender, v))
}

// GenderContains applies the Contains predicate on the "gender" field.
func GenderContains(v string) predicate.User {
	return predicate.User(sql.FieldContains(FieldGender, v))
}

// GenderHasPrefix applies the HasPrefix predicate on the "gender" field.
func GenderHasPrefix(v string) predicate.User {
	return predicate.User(sql.FieldHasPrefix(FieldGender, v))
}

// GenderHasSuffix applies the HasSuffix predicate on the "gender" field.
func GenderHasSuffix(v string) predicate.User {
	return predicate.User(sql.FieldHasSuffix(FieldGender, v))
}

// GenderEqualFold applies the EqualFold predicate on the "gender" field.
func GenderEqualFold(v string) predicate.User {
	return predicate.User(sql.FieldEqualFold(FieldGender, v))
}

// GenderContainsFold applies the ContainsFold predicate on the "gender" field.
func GenderContainsFold(v string) predicate.User {
	return predicate.User(sql.FieldContainsFold(FieldGender, v))
}

// DepartmentIDEQ applies the EQ predicate on the "department_id" field.
func DepartmentIDEQ(v int) predicate.User {
	return predicate.User(sql.FieldEQ(FieldDepartmentID, v))
}

// DepartmentIDNEQ applies the NEQ predicate on the "department_id" field.
func DepartmentIDNEQ(v int) predicate.User {
	return predicate.User(sql.FieldNEQ(FieldDepartmentID, v))
}

// DepartmentIDIn applies the In predicate on the "department_id" field.
func DepartmentIDIn(vs ...int) predicate.User {
	return predicate.User(sql.FieldIn(FieldDepartmentID, vs...))
}

// DepartmentIDNotIn applies the NotIn predicate on the "department_id" field.
func DepartmentIDNotIn(vs ...int) predicate.User {
	return predicate.User(sql.FieldNotIn(FieldDepartmentID, vs...))
}

// DepartmentIDGT applies the GT predicate on the "department_id" field.
func DepartmentIDGT(v int) predicate.User {
	return predicate.User(sql.FieldGT(FieldDepartmentID, v))
}

// DepartmentIDGTE applies the GTE predicate on the "department_id" field.
func DepartmentIDGTE(v int) predicate.User {
	return predicate.User(sql.FieldGTE(FieldDepartmentID, v))
}

// DepartmentIDLT applies the LT predicate on the "department_id" field.
func DepartmentIDLT(v int) predicate.User {
	return predicate.User(sql.FieldLT(FieldDepartmentID, v))
}

// DepartmentIDLTE applies the LTE predicate on the "department_id" field.
func DepartmentIDLTE(v int) predicate.User {
	return predicate.User(sql.FieldLTE(FieldDepartmentID, v))
}

// DepartmentIDIsNil applies the IsNil predicate on the "department_id" field.
func DepartmentIDIsNil() predicate.User {
	return predicate.User(sql.FieldIsNull(FieldDepartmentID))
}

// DepartmentIDNotNil applies the NotNil predicate on the "department_id" field.
func DepartmentIDNotNil() predicate.User {
	return predicate.User(sql.FieldNotNull(FieldDepartmentID))
}

// DepartmentPositionEQ applies the EQ predicate on the "department_position" field.
func DepartmentPositionEQ(v string) predicate.User {
	return predicate.User(sql.FieldEQ(FieldDepartmentPosition, v))
}

// DepartmentPositionNEQ applies the NEQ predicate on the "department_position" field.
func DepartmentPositionNEQ(v string) predicate.User {
	return predicate.User(sql.FieldNEQ(FieldDepartmentPosition, v))
}

// DepartmentPositionIn applies the In predicate on the "department_position" field.
func DepartmentPositionIn(vs ...string) predicate.User {
	return predicate.User(sql.FieldIn(FieldDepartmentPosition, vs...))
}

// DepartmentPositionNotIn applies the NotIn predicate on the "department_position" field.
func DepartmentPositionNotIn(vs ...string) predicate.User {
	return predicate.User(sql.FieldNotIn(FieldDepartmentPosition, vs...))
}

// DepartmentPositionGT applies the GT predicate on the "department_position" field.
func DepartmentPositionGT(v string) predicate.User {
	return predicate.User(sql.FieldGT(FieldDepartmentPosition, v))
}

// DepartmentPositionGTE applies the GTE predicate on the "department_position" field.
func DepartmentPositionGTE(v string) predicate.User {
	return predicate.User(sql.FieldGTE(FieldDepartmentPosition, v))
}

// DepartmentPositionLT applies the LT predicate on the "department_position" field.
func DepartmentPositionLT(v string) predicate.User {
	return predicate.User(sql.FieldLT(FieldDepartmentPosition, v))
}

// DepartmentPositionLTE applies the LTE predicate on the "department_position" field.
func DepartmentPositionLTE(v string) predicate.User {
	return predicate.User(sql.FieldLTE(FieldDepartmentPosition, v))
}

// DepartmentPositionContains applies the Contains predicate on the "department_position" field.
func DepartmentPositionContains(v string) predicate.User {
	return predicate.User(sql.FieldContains(FieldDepartmentPosition, v))
}

// DepartmentPositionHasPrefix applies the HasPrefix predicate on the "department_position" field.
func DepartmentPositionHasPrefix(v string) predicate.User {
	return predicate.User(sql.FieldHasPrefix(FieldDepartmentPosition, v))
}

// DepartmentPositionHasSuffix applies the HasSuffix predicate on the "department_position" field.
func DepartmentPositionHasSuffix(v string) predicate.User {
	return predicate.User(sql.FieldHasSuffix(FieldDepartmentPosition, v))
}

// DepartmentPositionIsNil applies the IsNil predicate on the "department_position" field.
func DepartmentPositionIsNil() predicate.User {
	return predicate.User(sql.FieldIsNull(FieldDepartmentPosition))
}

// DepartmentPositionNotNil applies the NotNil predicate on the "department_position" field.
func DepartmentPositionNotNil() predicate.User {
	return predicate.User(sql.FieldNotNull(FieldDepartmentPosition))
}

// DepartmentPositionEqualFold applies the EqualFold predicate on the "department_position" field.
func DepartmentPositionEqualFold(v string) predicate.User {
	return predicate.User(sql.FieldEqualFold(FieldDepartmentPosition, v))
}

// DepartmentPositionContainsFold applies the ContainsFold predicate on the "department_position" field.
func DepartmentPositionContainsFold(v string) predicate.User {
	return predicate.User(sql.FieldContainsFold(FieldDepartmentPosition, v))
}

// ProfileImageEQ applies the EQ predicate on the "profile_image" field.
func ProfileImageEQ(v string) predicate.User {
	return predicate.User(sql.FieldEQ(FieldProfileImage, v))
}

// ProfileImageNEQ applies the NEQ predicate on the "profile_image" field.
func ProfileImageNEQ(v string) predicate.User {
	return predicate.User(sql.FieldNEQ(FieldProfileImage, v))
}

// ProfileImageIn applies the In predicate on the "profile_image" field.
func ProfileImageIn(vs ...string) predicate.User {
	return predicate.User(sql.FieldIn(FieldProfileImage, vs...))
}

// ProfileImageNotIn applies the NotIn predicate on the "profile_image" field.
func ProfileImageNotIn(vs ...string) predicate.User {
	return predicate.User(sql.FieldNotIn(FieldProfileImage, vs...))
}

// ProfileImageGT applies the GT predicate on the "profile_image" field.
func ProfileImageGT(v string) predicate.User {
	return predicate.User(sql.FieldGT(FieldProfileImage, v))
}

// ProfileImageGTE applies the GTE predicate on the "profile_image" field.
func ProfileImageGTE(v string) predicate.User {
	return predicate.User(sql.FieldGTE(FieldProfileImage, v))
}

// ProfileImageLT applies the LT predicate on the "profile_image" field.
func ProfileImageLT(v string) predicate.User {
	return predicate.User(sql.FieldLT(FieldProfileImage, v))
}

// ProfileImageLTE applies the LTE predicate on the "profile_image" field.
func ProfileImageLTE(v string) predicate.User {
	return predicate.User(sql.FieldLTE(FieldProfileImage, v))
}

// ProfileImageContains applies the Contains predicate on the "profile_image" field.
func ProfileImageContains(v string) predicate.User {
	return predicate.User(sql.FieldContains(FieldProfileImage, v))
}

// ProfileImageHasPrefix applies the HasPrefix predicate on the "profile_image" field.
func ProfileImageHasPrefix(v string) predicate.User {
	return predicate.User(sql.FieldHasPrefix(FieldProfileImage, v))
}

// ProfileImageHasSuffix applies the HasSuffix predicate on the "profile_image" field.
func ProfileImageHasSuffix(v string) predicate.User {
	return predicate.User(sql.FieldHasSuffix(FieldProfileImage, v))
}

// ProfileImageIsNil applies the IsNil predicate on the "profile_image" field.
func ProfileImageIsNil() predicate.User {
	return predicate.User(sql.FieldIsNull(FieldProfileImage))
}

// ProfileImageNotNil applies the NotNil predicate on the "profile_image" field.
func ProfileImageNotNil() predicate.User {
	return predicate.User(sql.FieldNotNull(FieldProfileImage))
}

// ProfileImageEqualFold applies the EqualFold predicate on the "profile_image" field.
func ProfileImageEqualFold(v string) predicate.User {
	return predicate.User(sql.FieldEqualFold(FieldProfileImage, v))
}

// ProfileImageContainsFold applies the ContainsFold predicate on the "profile_image" field.
func ProfileImageContainsFold(v string) predicate.User {
	return predicate.User(sql.FieldContainsFold(FieldProfileImage, v))
}

// SuspendedAtEQ applies the EQ predicate on the "suspended_at" field.
func SuspendedAtEQ(v time.Time) predicate.User {
	return predicate.User(sql.FieldEQ(FieldSuspendedAt, v))
}

// SuspendedAtNEQ applies the NEQ predicate on the "suspended_at" field.
func SuspendedAtNEQ(v time.Time) predicate.User {
	return predicate.User(sql.FieldNEQ(FieldSuspendedAt, v))
}

// SuspendedAtIn applies the In predicate on the "suspended_at" field.
func SuspendedAtIn(vs ...time.Time) predicate.User {
	return predicate.User(sql.FieldIn(FieldSuspendedAt, vs...))
}

// SuspendedAtNotIn applies the NotIn predicate on the "suspended_at" field.
func SuspendedAtNotIn(vs ...time.Time) predicate.User {
	return predicate.User(sql.FieldNotIn(FieldSuspendedAt, vs...))
}

// SuspendedAtGT applies the GT predicate on the "suspended_at" field.
func SuspendedAtGT(v time.Time) predicate.User {
	return predicate.User(sql.FieldGT(FieldSuspendedAt, v))
}

// SuspendedAtGTE applies the GTE predicate on the "suspended_at" field.
func SuspendedAtGTE(v time.Time) predicate.User {
	return predicate.User(sql.FieldGTE(FieldSuspendedAt, v))
}

// SuspendedAtLT applies the LT predicate on the "suspended_at" field.
func SuspendedAtLT(v time.Time) predicate.User {
	return predicate.User(sql.FieldLT(FieldSuspendedAt, v))
}

// SuspendedAtLTE applies the LTE predicate on the "suspended_at" field.
func SuspendedAtLTE(v time.Time) predicate.User {
	return predicate.User(sql.FieldLTE(FieldSuspendedAt, v))
}

// SuspendedAtIsNil applies the IsNil predicate on the "suspended_at" field.
func SuspendedAtIsNil() predicate.User {
	return predicate.User(sql.FieldIsNull(FieldSuspendedAt))
}

// SuspendedAtNotNil applies the NotNil predicate on the "suspended_at" field.
func SuspendedAtNotNil() predicate.User {
	return predicate.User(sql.FieldNotNull(FieldSuspendedAt))
}

// StateEQ applies the EQ predicate on the "state" field.
func StateEQ(v State) predicate.User {
	return predicate.User(sql.FieldEQ(FieldState, v))
}

// StateNEQ applies the NEQ predicate on the "state" field.
func StateNEQ(v State) predicate.User {
	return predicate.User(sql.FieldNEQ(FieldState, v))
}

// StateIn applies the In predicate on the "state" field.
func StateIn(vs ...State) predicate.User {
	return predicate.User(sql.FieldIn(FieldState, vs...))
}

// StateNotIn applies the NotIn predicate on the "state" field.
func StateNotIn(vs ...State) predicate.User {
	return predicate.User(sql.FieldNotIn(FieldState, vs...))
}

// HasAssignment applies the HasEdge predicate on the "assignment" edge.
func HasAssignment() predicate.User {
	return predicate.User(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, AssignmentTable, AssignmentColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasAssignmentWith applies the HasEdge predicate on the "assignment" edge with a given conditions (other predicates).
func HasAssignmentWith(preds ...predicate.TaskAssignment) predicate.User {
	return predicate.User(func(s *sql.Selector) {
		step := newAssignmentStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasAssignedPerson applies the HasEdge predicate on the "assigned_person" edge.
func HasAssignedPerson() predicate.User {
	return predicate.User(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2M, true, AssignedPersonTable, AssignedPersonPrimaryKey...),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasAssignedPersonWith applies the HasEdge predicate on the "assigned_person" edge with a given conditions (other predicates).
func HasAssignedPersonWith(preds ...predicate.Person) predicate.User {
	return predicate.User(func(s *sql.Selector) {
		step := newAssignedPersonStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasAssignedDeal applies the HasEdge predicate on the "assigned_deal" edge.
func HasAssignedDeal() predicate.User {
	return predicate.User(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2M, true, AssignedDealTable, AssignedDealPrimaryKey...),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasAssignedDealWith applies the HasEdge predicate on the "assigned_deal" edge with a given conditions (other predicates).
func HasAssignedDealWith(preds ...predicate.Deal) predicate.User {
	return predicate.User(func(s *sql.Selector) {
		step := newAssignedDealStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasNotes applies the HasEdge predicate on the "notes" edge.
func HasNotes() predicate.User {
	return predicate.User(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, NotesTable, NotesColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasNotesWith applies the HasEdge predicate on the "notes" edge with a given conditions (other predicates).
func HasNotesWith(preds ...predicate.Note) predicate.User {
	return predicate.User(func(s *sql.Selector) {
		step := newNotesStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasTaskNotes applies the HasEdge predicate on the "task_notes" edge.
func HasTaskNotes() predicate.User {
	return predicate.User(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, TaskNotesTable, TaskNotesColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasTaskNotesWith applies the HasEdge predicate on the "task_notes" edge with a given conditions (other predicates).
func HasTaskNotesWith(preds ...predicate.TaskNote) predicate.User {
	return predicate.User(func(s *sql.Selector) {
		step := newTaskNotesStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasAppointments applies the HasEdge predicate on the "appointments" edge.
func HasAppointments() predicate.User {
	return predicate.User(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, AppointmentsTable, AppointmentsColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasAppointmentsWith applies the HasEdge predicate on the "appointments" edge with a given conditions (other predicates).
func HasAppointmentsWith(preds ...predicate.Appointment) predicate.User {
	return predicate.User(func(s *sql.Selector) {
		step := newAppointmentsStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasCalls applies the HasEdge predicate on the "calls" edge.
func HasCalls() predicate.User {
	return predicate.User(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, CallsTable, CallsColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasCallsWith applies the HasEdge predicate on the "calls" edge with a given conditions (other predicates).
func HasCallsWith(preds ...predicate.Call) predicate.User {
	return predicate.User(func(s *sql.Selector) {
		step := newCallsStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasSchedules applies the HasEdge predicate on the "schedules" edge.
func HasSchedules() predicate.User {
	return predicate.User(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, SchedulesTable, SchedulesColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasSchedulesWith applies the HasEdge predicate on the "schedules" edge with a given conditions (other predicates).
func HasSchedulesWith(preds ...predicate.Schedule) predicate.User {
	return predicate.User(func(s *sql.Selector) {
		step := newSchedulesStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasMessageHistories applies the HasEdge predicate on the "message_histories" edge.
func HasMessageHistories() predicate.User {
	return predicate.User(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, MessageHistoriesTable, MessageHistoriesColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasMessageHistoriesWith applies the HasEdge predicate on the "message_histories" edge with a given conditions (other predicates).
func HasMessageHistoriesWith(preds ...predicate.MessageHistory) predicate.User {
	return predicate.User(func(s *sql.Selector) {
		step := newMessageHistoriesStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasData applies the HasEdge predicate on the "data" edge.
func HasData() predicate.User {
	return predicate.User(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, DataTable, DataColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasDataWith applies the HasEdge predicate on the "data" edge with a given conditions (other predicates).
func HasDataWith(preds ...predicate.UserData) predicate.User {
	return predicate.User(func(s *sql.Selector) {
		step := newDataStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasPersonAssignment applies the HasEdge predicate on the "person_assignment" edge.
func HasPersonAssignment() predicate.User {
	return predicate.User(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, PersonAssignmentTable, PersonAssignmentColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasPersonAssignmentWith applies the HasEdge predicate on the "person_assignment" edge with a given conditions (other predicates).
func HasPersonAssignmentWith(preds ...predicate.PersonAssignment) predicate.User {
	return predicate.User(func(s *sql.Selector) {
		step := newPersonAssignmentStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasDealAssignment applies the HasEdge predicate on the "deal_assignment" edge.
func HasDealAssignment() predicate.User {
	return predicate.User(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, DealAssignmentTable, DealAssignmentColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasDealAssignmentWith applies the HasEdge predicate on the "deal_assignment" edge with a given conditions (other predicates).
func HasDealAssignmentWith(preds ...predicate.DealUser) predicate.User {
	return predicate.User(func(s *sql.Selector) {
		step := newDealAssignmentStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.User) predicate.User {
	return predicate.User(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.User) predicate.User {
	return predicate.User(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.User) predicate.User {
	return predicate.User(sql.NotPredicates(p))
}
