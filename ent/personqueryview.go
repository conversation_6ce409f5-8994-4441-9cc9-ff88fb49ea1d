// Code generated by ent, DO NOT EDIT.

package ent

import (
	"bcare/ent/personqueryview"
	"bcare/ent/types"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

// PersonQueryView is the model entity for the PersonQueryView schema.
type PersonQueryView struct {
	config `json:"-"`
	// ID holds the value of the "id" field.
	ID int `json:"id,omitempty"`
	// PersonCode holds the value of the "person_code" field.
	PersonCode string `json:"person_code,omitempty"`
	// FullName holds the value of the "full_name" field.
	FullName string `json:"full_name,omitempty"`
	// DateOfBirth holds the value of the "date_of_birth" field.
	DateOfBirth *time.Time `json:"date_of_birth,omitempty"`
	// CreatedAt holds the value of the "created_at" field.
	CreatedAt time.Time `json:"created_at,omitempty"`
	// AppointmentTime holds the value of the "appointment_time" field.
	AppointmentTime time.Time `json:"appointment_time,omitempty"`
	// DeletedAt holds the value of the "deleted_at" field.
	DeletedAt time.Time `json:"deleted_at,omitempty"`
	// Gender holds the value of the "gender" field.
	Gender string `json:"gender,omitempty"`
	// ProvinceID holds the value of the "province_id" field.
	ProvinceID int `json:"province_id,omitempty"`
	// DistrictID holds the value of the "district_id" field.
	DistrictID int `json:"district_id,omitempty"`
	// WardID holds the value of the "ward_id" field.
	WardID int `json:"ward_id,omitempty"`
	// AddressNumber holds the value of the "address_number" field.
	AddressNumber string `json:"address_number,omitempty"`
	// Phone holds the value of the "phone" field.
	Phone string `json:"phone,omitempty"`
	// CreatorName holds the value of the "creator_name" field.
	CreatorName string `json:"creator_name,omitempty"`
	// PersonSource holds the value of the "person_source" field.
	PersonSource string `json:"person_source,omitempty"`
	// DoctorName holds the value of the "doctor_name" field.
	DoctorName string `json:"doctor_name,omitempty"`
	// TreatmentName holds the value of the "treatment_name" field.
	TreatmentName string `json:"treatment_name,omitempty"`
	// Email holds the value of the "email" field.
	Email string `json:"email,omitempty"`
	// JobID holds the value of the "job_id" field.
	JobID *int `json:"job_id,omitempty"`
	// UserID holds the value of the "user_id" field.
	UserID int `json:"user_id,omitempty"`
	// SourceID holds the value of the "source_id" field.
	SourceID *int `json:"source_id,omitempty"`
	// PhoneConfirm holds the value of the "phone_confirm" field.
	PhoneConfirm bool `json:"phone_confirm,omitempty"`
	// MailConfirm holds the value of the "mail_confirm" field.
	MailConfirm bool `json:"mail_confirm,omitempty"`
	// PersonField holds the value of the "person_field" field.
	PersonField *types.PersonMeta `json:"person_field,omitempty"`
	// StageName holds the value of the "stage_name" field.
	StageName string `json:"stage_name,omitempty"`
	// StageID holds the value of the "stage_id" field.
	StageID int `json:"stage_id,omitempty"`
	// StageParentID holds the value of the "stage_parent_id" field.
	StageParentID int `json:"stage_parent_id,omitempty"`
	// SaleID holds the value of the "sale_id" field.
	SaleID int `json:"sale_id,omitempty"`
	// Sale holds the value of the "sale" field.
	Sale         map[string]interface{} `json:"sale,omitempty"`
	selectValues sql.SelectValues
}

// scanValues returns the types for scanning values from sql.Rows.
func (*PersonQueryView) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case personqueryview.FieldPersonField, personqueryview.FieldSale:
			values[i] = new([]byte)
		case personqueryview.FieldPhoneConfirm, personqueryview.FieldMailConfirm:
			values[i] = new(sql.NullBool)
		case personqueryview.FieldID, personqueryview.FieldProvinceID, personqueryview.FieldDistrictID, personqueryview.FieldWardID, personqueryview.FieldJobID, personqueryview.FieldUserID, personqueryview.FieldSourceID, personqueryview.FieldStageID, personqueryview.FieldStageParentID, personqueryview.FieldSaleID:
			values[i] = new(sql.NullInt64)
		case personqueryview.FieldPersonCode, personqueryview.FieldFullName, personqueryview.FieldGender, personqueryview.FieldAddressNumber, personqueryview.FieldPhone, personqueryview.FieldCreatorName, personqueryview.FieldPersonSource, personqueryview.FieldDoctorName, personqueryview.FieldTreatmentName, personqueryview.FieldEmail, personqueryview.FieldStageName:
			values[i] = new(sql.NullString)
		case personqueryview.FieldDateOfBirth, personqueryview.FieldCreatedAt, personqueryview.FieldAppointmentTime, personqueryview.FieldDeletedAt:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the PersonQueryView fields.
func (pqv *PersonQueryView) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case personqueryview.FieldID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field id", values[i])
			} else if value.Valid {
				pqv.ID = int(value.Int64)
			}
		case personqueryview.FieldPersonCode:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field person_code", values[i])
			} else if value.Valid {
				pqv.PersonCode = value.String
			}
		case personqueryview.FieldFullName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field full_name", values[i])
			} else if value.Valid {
				pqv.FullName = value.String
			}
		case personqueryview.FieldDateOfBirth:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field date_of_birth", values[i])
			} else if value.Valid {
				pqv.DateOfBirth = new(time.Time)
				*pqv.DateOfBirth = value.Time
			}
		case personqueryview.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				pqv.CreatedAt = value.Time
			}
		case personqueryview.FieldAppointmentTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field appointment_time", values[i])
			} else if value.Valid {
				pqv.AppointmentTime = value.Time
			}
		case personqueryview.FieldDeletedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field deleted_at", values[i])
			} else if value.Valid {
				pqv.DeletedAt = value.Time
			}
		case personqueryview.FieldGender:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field gender", values[i])
			} else if value.Valid {
				pqv.Gender = value.String
			}
		case personqueryview.FieldProvinceID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field province_id", values[i])
			} else if value.Valid {
				pqv.ProvinceID = int(value.Int64)
			}
		case personqueryview.FieldDistrictID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field district_id", values[i])
			} else if value.Valid {
				pqv.DistrictID = int(value.Int64)
			}
		case personqueryview.FieldWardID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field ward_id", values[i])
			} else if value.Valid {
				pqv.WardID = int(value.Int64)
			}
		case personqueryview.FieldAddressNumber:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field address_number", values[i])
			} else if value.Valid {
				pqv.AddressNumber = value.String
			}
		case personqueryview.FieldPhone:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field phone", values[i])
			} else if value.Valid {
				pqv.Phone = value.String
			}
		case personqueryview.FieldCreatorName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field creator_name", values[i])
			} else if value.Valid {
				pqv.CreatorName = value.String
			}
		case personqueryview.FieldPersonSource:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field person_source", values[i])
			} else if value.Valid {
				pqv.PersonSource = value.String
			}
		case personqueryview.FieldDoctorName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field doctor_name", values[i])
			} else if value.Valid {
				pqv.DoctorName = value.String
			}
		case personqueryview.FieldTreatmentName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field treatment_name", values[i])
			} else if value.Valid {
				pqv.TreatmentName = value.String
			}
		case personqueryview.FieldEmail:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field email", values[i])
			} else if value.Valid {
				pqv.Email = value.String
			}
		case personqueryview.FieldJobID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field job_id", values[i])
			} else if value.Valid {
				pqv.JobID = new(int)
				*pqv.JobID = int(value.Int64)
			}
		case personqueryview.FieldUserID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field user_id", values[i])
			} else if value.Valid {
				pqv.UserID = int(value.Int64)
			}
		case personqueryview.FieldSourceID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field source_id", values[i])
			} else if value.Valid {
				pqv.SourceID = new(int)
				*pqv.SourceID = int(value.Int64)
			}
		case personqueryview.FieldPhoneConfirm:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field phone_confirm", values[i])
			} else if value.Valid {
				pqv.PhoneConfirm = value.Bool
			}
		case personqueryview.FieldMailConfirm:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field mail_confirm", values[i])
			} else if value.Valid {
				pqv.MailConfirm = value.Bool
			}
		case personqueryview.FieldPersonField:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field person_field", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &pqv.PersonField); err != nil {
					return fmt.Errorf("unmarshal field person_field: %w", err)
				}
			}
		case personqueryview.FieldStageName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field stage_name", values[i])
			} else if value.Valid {
				pqv.StageName = value.String
			}
		case personqueryview.FieldStageID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field stage_id", values[i])
			} else if value.Valid {
				pqv.StageID = int(value.Int64)
			}
		case personqueryview.FieldStageParentID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field stage_parent_id", values[i])
			} else if value.Valid {
				pqv.StageParentID = int(value.Int64)
			}
		case personqueryview.FieldSaleID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field sale_id", values[i])
			} else if value.Valid {
				pqv.SaleID = int(value.Int64)
			}
		case personqueryview.FieldSale:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field sale", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &pqv.Sale); err != nil {
					return fmt.Errorf("unmarshal field sale: %w", err)
				}
			}
		default:
			pqv.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the PersonQueryView.
// This includes values selected through modifiers, order, etc.
func (pqv *PersonQueryView) Value(name string) (ent.Value, error) {
	return pqv.selectValues.Get(name)
}

// Unwrap unwraps the PersonQueryView entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (pqv *PersonQueryView) Unwrap() *PersonQueryView {
	_tx, ok := pqv.config.driver.(*txDriver)
	if !ok {
		panic("ent: PersonQueryView is not a transactional entity")
	}
	pqv.config.driver = _tx.drv
	return pqv
}

// String implements the fmt.Stringer.
func (pqv *PersonQueryView) String() string {
	var builder strings.Builder
	builder.WriteString("PersonQueryView(")
	builder.WriteString("id=")
	builder.WriteString(fmt.Sprintf("%v", pqv.ID))
	builder.WriteString(", ")
	builder.WriteString("person_code=")
	builder.WriteString(pqv.PersonCode)
	builder.WriteString(", ")
	builder.WriteString("full_name=")
	builder.WriteString(pqv.FullName)
	builder.WriteString(", ")
	if v := pqv.DateOfBirth; v != nil {
		builder.WriteString("date_of_birth=")
		builder.WriteString(v.Format(time.ANSIC))
	}
	builder.WriteString(", ")
	builder.WriteString("created_at=")
	builder.WriteString(pqv.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("appointment_time=")
	builder.WriteString(pqv.AppointmentTime.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("deleted_at=")
	builder.WriteString(pqv.DeletedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("gender=")
	builder.WriteString(pqv.Gender)
	builder.WriteString(", ")
	builder.WriteString("province_id=")
	builder.WriteString(fmt.Sprintf("%v", pqv.ProvinceID))
	builder.WriteString(", ")
	builder.WriteString("district_id=")
	builder.WriteString(fmt.Sprintf("%v", pqv.DistrictID))
	builder.WriteString(", ")
	builder.WriteString("ward_id=")
	builder.WriteString(fmt.Sprintf("%v", pqv.WardID))
	builder.WriteString(", ")
	builder.WriteString("address_number=")
	builder.WriteString(pqv.AddressNumber)
	builder.WriteString(", ")
	builder.WriteString("phone=")
	builder.WriteString(pqv.Phone)
	builder.WriteString(", ")
	builder.WriteString("creator_name=")
	builder.WriteString(pqv.CreatorName)
	builder.WriteString(", ")
	builder.WriteString("person_source=")
	builder.WriteString(pqv.PersonSource)
	builder.WriteString(", ")
	builder.WriteString("doctor_name=")
	builder.WriteString(pqv.DoctorName)
	builder.WriteString(", ")
	builder.WriteString("treatment_name=")
	builder.WriteString(pqv.TreatmentName)
	builder.WriteString(", ")
	builder.WriteString("email=")
	builder.WriteString(pqv.Email)
	builder.WriteString(", ")
	if v := pqv.JobID; v != nil {
		builder.WriteString("job_id=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteString(", ")
	builder.WriteString("user_id=")
	builder.WriteString(fmt.Sprintf("%v", pqv.UserID))
	builder.WriteString(", ")
	if v := pqv.SourceID; v != nil {
		builder.WriteString("source_id=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteString(", ")
	builder.WriteString("phone_confirm=")
	builder.WriteString(fmt.Sprintf("%v", pqv.PhoneConfirm))
	builder.WriteString(", ")
	builder.WriteString("mail_confirm=")
	builder.WriteString(fmt.Sprintf("%v", pqv.MailConfirm))
	builder.WriteString(", ")
	builder.WriteString("person_field=")
	builder.WriteString(fmt.Sprintf("%v", pqv.PersonField))
	builder.WriteString(", ")
	builder.WriteString("stage_name=")
	builder.WriteString(pqv.StageName)
	builder.WriteString(", ")
	builder.WriteString("stage_id=")
	builder.WriteString(fmt.Sprintf("%v", pqv.StageID))
	builder.WriteString(", ")
	builder.WriteString("stage_parent_id=")
	builder.WriteString(fmt.Sprintf("%v", pqv.StageParentID))
	builder.WriteString(", ")
	builder.WriteString("sale_id=")
	builder.WriteString(fmt.Sprintf("%v", pqv.SaleID))
	builder.WriteString(", ")
	builder.WriteString("sale=")
	builder.WriteString(fmt.Sprintf("%v", pqv.Sale))
	builder.WriteByte(')')
	return builder.String()
}

// PersonQueryViews is a parsable slice of PersonQueryView.
type PersonQueryViews []*PersonQueryView
