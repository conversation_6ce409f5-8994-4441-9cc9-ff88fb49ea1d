// Code generated by ent, DO NOT EDIT.

package ent

import (
	"bcare/ent/operation"
	"bcare/ent/predicate"
	"bcare/ent/product"
	"bcare/ent/productoperation"
	"context"
	"database/sql/driver"
	"fmt"
	"math"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// OperationQuery is the builder for querying Operation entities.
type OperationQuery struct {
	config
	ctx                  *QueryContext
	order                []operation.OrderOption
	inters               []Interceptor
	predicates           []predicate.Operation
	withAssignedProduct  *ProductQuery
	withProductOperation *ProductOperationQuery
	modifiers            []func(*sql.Selector)
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the OperationQuery builder.
func (oq *OperationQuery) Where(ps ...predicate.Operation) *OperationQuery {
	oq.predicates = append(oq.predicates, ps...)
	return oq
}

// Limit the number of records to be returned by this query.
func (oq *OperationQuery) Limit(limit int) *OperationQuery {
	oq.ctx.Limit = &limit
	return oq
}

// Offset to start from.
func (oq *OperationQuery) Offset(offset int) *OperationQuery {
	oq.ctx.Offset = &offset
	return oq
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (oq *OperationQuery) Unique(unique bool) *OperationQuery {
	oq.ctx.Unique = &unique
	return oq
}

// Order specifies how the records should be ordered.
func (oq *OperationQuery) Order(o ...operation.OrderOption) *OperationQuery {
	oq.order = append(oq.order, o...)
	return oq
}

// QueryAssignedProduct chains the current query on the "assigned_product" edge.
func (oq *OperationQuery) QueryAssignedProduct() *ProductQuery {
	query := (&ProductClient{config: oq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := oq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := oq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(operation.Table, operation.FieldID, selector),
			sqlgraph.To(product.Table, product.FieldID),
			sqlgraph.Edge(sqlgraph.M2M, true, operation.AssignedProductTable, operation.AssignedProductPrimaryKey...),
		)
		fromU = sqlgraph.SetNeighbors(oq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// QueryProductOperation chains the current query on the "product_operation" edge.
func (oq *OperationQuery) QueryProductOperation() *ProductOperationQuery {
	query := (&ProductOperationClient{config: oq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := oq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := oq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(operation.Table, operation.FieldID, selector),
			sqlgraph.To(productoperation.Table, productoperation.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, operation.ProductOperationTable, operation.ProductOperationColumn),
		)
		fromU = sqlgraph.SetNeighbors(oq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// First returns the first Operation entity from the query.
// Returns a *NotFoundError when no Operation was found.
func (oq *OperationQuery) First(ctx context.Context) (*Operation, error) {
	nodes, err := oq.Limit(1).All(setContextOp(ctx, oq.ctx, ent.OpQueryFirst))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{operation.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (oq *OperationQuery) FirstX(ctx context.Context) *Operation {
	node, err := oq.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first Operation ID from the query.
// Returns a *NotFoundError when no Operation ID was found.
func (oq *OperationQuery) FirstID(ctx context.Context) (id int, err error) {
	var ids []int
	if ids, err = oq.Limit(1).IDs(setContextOp(ctx, oq.ctx, ent.OpQueryFirstID)); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{operation.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (oq *OperationQuery) FirstIDX(ctx context.Context) int {
	id, err := oq.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single Operation entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one Operation entity is found.
// Returns a *NotFoundError when no Operation entities are found.
func (oq *OperationQuery) Only(ctx context.Context) (*Operation, error) {
	nodes, err := oq.Limit(2).All(setContextOp(ctx, oq.ctx, ent.OpQueryOnly))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{operation.Label}
	default:
		return nil, &NotSingularError{operation.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (oq *OperationQuery) OnlyX(ctx context.Context) *Operation {
	node, err := oq.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only Operation ID in the query.
// Returns a *NotSingularError when more than one Operation ID is found.
// Returns a *NotFoundError when no entities are found.
func (oq *OperationQuery) OnlyID(ctx context.Context) (id int, err error) {
	var ids []int
	if ids, err = oq.Limit(2).IDs(setContextOp(ctx, oq.ctx, ent.OpQueryOnlyID)); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{operation.Label}
	default:
		err = &NotSingularError{operation.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (oq *OperationQuery) OnlyIDX(ctx context.Context) int {
	id, err := oq.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of Operations.
func (oq *OperationQuery) All(ctx context.Context) ([]*Operation, error) {
	ctx = setContextOp(ctx, oq.ctx, ent.OpQueryAll)
	if err := oq.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*Operation, *OperationQuery]()
	return withInterceptors[[]*Operation](ctx, oq, qr, oq.inters)
}

// AllX is like All, but panics if an error occurs.
func (oq *OperationQuery) AllX(ctx context.Context) []*Operation {
	nodes, err := oq.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of Operation IDs.
func (oq *OperationQuery) IDs(ctx context.Context) (ids []int, err error) {
	if oq.ctx.Unique == nil && oq.path != nil {
		oq.Unique(true)
	}
	ctx = setContextOp(ctx, oq.ctx, ent.OpQueryIDs)
	if err = oq.Select(operation.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (oq *OperationQuery) IDsX(ctx context.Context) []int {
	ids, err := oq.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (oq *OperationQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, oq.ctx, ent.OpQueryCount)
	if err := oq.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, oq, querierCount[*OperationQuery](), oq.inters)
}

// CountX is like Count, but panics if an error occurs.
func (oq *OperationQuery) CountX(ctx context.Context) int {
	count, err := oq.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (oq *OperationQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, oq.ctx, ent.OpQueryExist)
	switch _, err := oq.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("ent: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (oq *OperationQuery) ExistX(ctx context.Context) bool {
	exist, err := oq.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the OperationQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (oq *OperationQuery) Clone() *OperationQuery {
	if oq == nil {
		return nil
	}
	return &OperationQuery{
		config:               oq.config,
		ctx:                  oq.ctx.Clone(),
		order:                append([]operation.OrderOption{}, oq.order...),
		inters:               append([]Interceptor{}, oq.inters...),
		predicates:           append([]predicate.Operation{}, oq.predicates...),
		withAssignedProduct:  oq.withAssignedProduct.Clone(),
		withProductOperation: oq.withProductOperation.Clone(),
		// clone intermediate query.
		sql:       oq.sql.Clone(),
		path:      oq.path,
		modifiers: append([]func(*sql.Selector){}, oq.modifiers...),
	}
}

// WithAssignedProduct tells the query-builder to eager-load the nodes that are connected to
// the "assigned_product" edge. The optional arguments are used to configure the query builder of the edge.
func (oq *OperationQuery) WithAssignedProduct(opts ...func(*ProductQuery)) *OperationQuery {
	query := (&ProductClient{config: oq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	oq.withAssignedProduct = query
	return oq
}

// WithProductOperation tells the query-builder to eager-load the nodes that are connected to
// the "product_operation" edge. The optional arguments are used to configure the query builder of the edge.
func (oq *OperationQuery) WithProductOperation(opts ...func(*ProductOperationQuery)) *OperationQuery {
	query := (&ProductOperationClient{config: oq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	oq.withProductOperation = query
	return oq
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		DeletedAt time.Time `json:"deleted_at,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.Operation.Query().
//		GroupBy(operation.FieldDeletedAt).
//		Aggregate(ent.Count()).
//		Scan(ctx, &v)
func (oq *OperationQuery) GroupBy(field string, fields ...string) *OperationGroupBy {
	oq.ctx.Fields = append([]string{field}, fields...)
	grbuild := &OperationGroupBy{build: oq}
	grbuild.flds = &oq.ctx.Fields
	grbuild.label = operation.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		DeletedAt time.Time `json:"deleted_at,omitempty"`
//	}
//
//	client.Operation.Query().
//		Select(operation.FieldDeletedAt).
//		Scan(ctx, &v)
func (oq *OperationQuery) Select(fields ...string) *OperationSelect {
	oq.ctx.Fields = append(oq.ctx.Fields, fields...)
	sbuild := &OperationSelect{OperationQuery: oq}
	sbuild.label = operation.Label
	sbuild.flds, sbuild.scan = &oq.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a OperationSelect configured with the given aggregations.
func (oq *OperationQuery) Aggregate(fns ...AggregateFunc) *OperationSelect {
	return oq.Select().Aggregate(fns...)
}

func (oq *OperationQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range oq.inters {
		if inter == nil {
			return fmt.Errorf("ent: uninitialized interceptor (forgotten import ent/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, oq); err != nil {
				return err
			}
		}
	}
	for _, f := range oq.ctx.Fields {
		if !operation.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
		}
	}
	if oq.path != nil {
		prev, err := oq.path(ctx)
		if err != nil {
			return err
		}
		oq.sql = prev
	}
	return nil
}

func (oq *OperationQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*Operation, error) {
	var (
		nodes       = []*Operation{}
		_spec       = oq.querySpec()
		loadedTypes = [2]bool{
			oq.withAssignedProduct != nil,
			oq.withProductOperation != nil,
		}
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*Operation).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &Operation{config: oq.config}
		nodes = append(nodes, node)
		node.Edges.loadedTypes = loadedTypes
		return node.assignValues(columns, values)
	}
	if len(oq.modifiers) > 0 {
		_spec.Modifiers = oq.modifiers
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, oq.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	if query := oq.withAssignedProduct; query != nil {
		if err := oq.loadAssignedProduct(ctx, query, nodes,
			func(n *Operation) { n.Edges.AssignedProduct = []*Product{} },
			func(n *Operation, e *Product) { n.Edges.AssignedProduct = append(n.Edges.AssignedProduct, e) }); err != nil {
			return nil, err
		}
	}
	if query := oq.withProductOperation; query != nil {
		if err := oq.loadProductOperation(ctx, query, nodes,
			func(n *Operation) { n.Edges.ProductOperation = []*ProductOperation{} },
			func(n *Operation, e *ProductOperation) {
				n.Edges.ProductOperation = append(n.Edges.ProductOperation, e)
			}); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

func (oq *OperationQuery) loadAssignedProduct(ctx context.Context, query *ProductQuery, nodes []*Operation, init func(*Operation), assign func(*Operation, *Product)) error {
	edgeIDs := make([]driver.Value, len(nodes))
	byID := make(map[int]*Operation)
	nids := make(map[int]map[*Operation]struct{})
	for i, node := range nodes {
		edgeIDs[i] = node.ID
		byID[node.ID] = node
		if init != nil {
			init(node)
		}
	}
	query.Where(func(s *sql.Selector) {
		joinT := sql.Table(operation.AssignedProductTable)
		s.Join(joinT).On(s.C(product.FieldID), joinT.C(operation.AssignedProductPrimaryKey[0]))
		s.Where(sql.InValues(joinT.C(operation.AssignedProductPrimaryKey[1]), edgeIDs...))
		columns := s.SelectedColumns()
		s.Select(joinT.C(operation.AssignedProductPrimaryKey[1]))
		s.AppendSelect(columns...)
		s.SetDistinct(false)
	})
	if err := query.prepareQuery(ctx); err != nil {
		return err
	}
	qr := QuerierFunc(func(ctx context.Context, q Query) (Value, error) {
		return query.sqlAll(ctx, func(_ context.Context, spec *sqlgraph.QuerySpec) {
			assign := spec.Assign
			values := spec.ScanValues
			spec.ScanValues = func(columns []string) ([]any, error) {
				values, err := values(columns[1:])
				if err != nil {
					return nil, err
				}
				return append([]any{new(sql.NullInt64)}, values...), nil
			}
			spec.Assign = func(columns []string, values []any) error {
				outValue := int(values[0].(*sql.NullInt64).Int64)
				inValue := int(values[1].(*sql.NullInt64).Int64)
				if nids[inValue] == nil {
					nids[inValue] = map[*Operation]struct{}{byID[outValue]: {}}
					return assign(columns[1:], values[1:])
				}
				nids[inValue][byID[outValue]] = struct{}{}
				return nil
			}
		})
	})
	neighbors, err := withInterceptors[[]*Product](ctx, query, qr, query.inters)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		nodes, ok := nids[n.ID]
		if !ok {
			return fmt.Errorf(`unexpected "assigned_product" node returned %v`, n.ID)
		}
		for kn := range nodes {
			assign(kn, n)
		}
	}
	return nil
}
func (oq *OperationQuery) loadProductOperation(ctx context.Context, query *ProductOperationQuery, nodes []*Operation, init func(*Operation), assign func(*Operation, *ProductOperation)) error {
	fks := make([]driver.Value, 0, len(nodes))
	nodeids := make(map[int]*Operation)
	for i := range nodes {
		fks = append(fks, nodes[i].ID)
		nodeids[nodes[i].ID] = nodes[i]
		if init != nil {
			init(nodes[i])
		}
	}
	if len(query.ctx.Fields) > 0 {
		query.ctx.AppendFieldOnce(productoperation.FieldOperationID)
	}
	query.Where(predicate.ProductOperation(func(s *sql.Selector) {
		s.Where(sql.InValues(s.C(operation.ProductOperationColumn), fks...))
	}))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		fk := n.OperationID
		node, ok := nodeids[fk]
		if !ok {
			return fmt.Errorf(`unexpected referenced foreign-key "operation_id" returned %v for node %v`, fk, n.ID)
		}
		assign(node, n)
	}
	return nil
}

func (oq *OperationQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := oq.querySpec()
	if len(oq.modifiers) > 0 {
		_spec.Modifiers = oq.modifiers
	}
	_spec.Node.Columns = oq.ctx.Fields
	if len(oq.ctx.Fields) > 0 {
		_spec.Unique = oq.ctx.Unique != nil && *oq.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, oq.driver, _spec)
}

func (oq *OperationQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(operation.Table, operation.Columns, sqlgraph.NewFieldSpec(operation.FieldID, field.TypeInt))
	_spec.From = oq.sql
	if unique := oq.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if oq.path != nil {
		_spec.Unique = true
	}
	if fields := oq.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, operation.FieldID)
		for i := range fields {
			if fields[i] != operation.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
	}
	if ps := oq.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := oq.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := oq.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := oq.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (oq *OperationQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(oq.driver.Dialect())
	t1 := builder.Table(operation.Table)
	columns := oq.ctx.Fields
	if len(columns) == 0 {
		columns = operation.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if oq.sql != nil {
		selector = oq.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if oq.ctx.Unique != nil && *oq.ctx.Unique {
		selector.Distinct()
	}
	for _, m := range oq.modifiers {
		m(selector)
	}
	for _, p := range oq.predicates {
		p(selector)
	}
	for _, p := range oq.order {
		p(selector)
	}
	if offset := oq.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := oq.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// Modify adds a query modifier for attaching custom logic to queries.
func (oq *OperationQuery) Modify(modifiers ...func(s *sql.Selector)) *OperationSelect {
	oq.modifiers = append(oq.modifiers, modifiers...)
	return oq.Select()
}

// OperationGroupBy is the group-by builder for Operation entities.
type OperationGroupBy struct {
	selector
	build *OperationQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (ogb *OperationGroupBy) Aggregate(fns ...AggregateFunc) *OperationGroupBy {
	ogb.fns = append(ogb.fns, fns...)
	return ogb
}

// Scan applies the selector query and scans the result into the given value.
func (ogb *OperationGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, ogb.build.ctx, ent.OpQueryGroupBy)
	if err := ogb.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*OperationQuery, *OperationGroupBy](ctx, ogb.build, ogb, ogb.build.inters, v)
}

func (ogb *OperationGroupBy) sqlScan(ctx context.Context, root *OperationQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(ogb.fns))
	for _, fn := range ogb.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*ogb.flds)+len(ogb.fns))
		for _, f := range *ogb.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*ogb.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := ogb.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// OperationSelect is the builder for selecting fields of Operation entities.
type OperationSelect struct {
	*OperationQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (os *OperationSelect) Aggregate(fns ...AggregateFunc) *OperationSelect {
	os.fns = append(os.fns, fns...)
	return os
}

// Scan applies the selector query and scans the result into the given value.
func (os *OperationSelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, os.ctx, ent.OpQuerySelect)
	if err := os.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*OperationQuery, *OperationSelect](ctx, os.OperationQuery, os, os.inters, v)
}

func (os *OperationSelect) sqlScan(ctx context.Context, root *OperationQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(os.fns))
	for _, fn := range os.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*os.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := os.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// Modify adds a query modifier for attaching custom logic to queries.
func (os *OperationSelect) Modify(modifiers ...func(s *sql.Selector)) *OperationSelect {
	os.modifiers = append(os.modifiers, modifiers...)
	return os
}
