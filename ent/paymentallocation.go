// Code generated by ent, DO NOT EDIT.

package ent

import (
	"bcare/ent/billitem"
	"bcare/ent/installment"
	"bcare/ent/payment"
	"bcare/ent/paymentallocation"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

// PaymentAllocation is the model entity for the PaymentAllocation schema.
type PaymentAllocation struct {
	config `json:"-"`
	// ID of the ent.
	ID int `json:"id,omitempty"`
	// DeletedAt holds the value of the "deleted_at" field.
	DeletedAt time.Time `json:"deleted_at,omitempty"`
	// Status holds the value of the "status" field.
	Status int8 `json:"status,omitempty"`
	// Version holds the value of the "version" field.
	Version int `json:"version,omitempty"`
	// CreatedAt holds the value of the "created_at" field.
	CreatedAt time.Time `json:"created_at,omitempty"`
	// UpdatedAt holds the value of the "updated_at" field.
	UpdatedAt time.Time `json:"updated_at,omitempty"`
	// PaymentID holds the value of the "payment_id" field.
	PaymentID int `json:"payment_id,omitempty"`
	// BillID holds the value of the "bill_id" field.
	BillID int `json:"bill_id,omitempty"`
	// UserID holds the value of the "user_id" field.
	UserID int `json:"user_id,omitempty"`
	// BillItemID holds the value of the "bill_item_id" field.
	BillItemID *int `json:"bill_item_id,omitempty"`
	// InstallmentID holds the value of the "installment_id" field.
	InstallmentID *int `json:"installment_id,omitempty"`
	// Note holds the value of the "note" field.
	Note string `json:"note,omitempty"`
	// State holds the value of the "state" field.
	State string `json:"state,omitempty"`
	// Amount holds the value of the "amount" field.
	Amount float64 `json:"amount,omitempty"`
	// Edges holds the relations/edges for other nodes in the graph.
	// The values are being populated by the PaymentAllocationQuery when eager-loading is set.
	Edges        PaymentAllocationEdges `json:"-"`
	selectValues sql.SelectValues
}

// PaymentAllocationEdges holds the relations/edges for other nodes in the graph.
type PaymentAllocationEdges struct {
	// Payment holds the value of the payment edge.
	Payment *Payment `json:"payment,omitempty"`
	// BillItem holds the value of the bill_item edge.
	BillItem *BillItem `json:"bill_item,omitempty"`
	// Installment holds the value of the installment edge.
	Installment *Installment `json:"installment,omitempty"`
	// loadedTypes holds the information for reporting if a
	// type was loaded (or requested) in eager-loading or not.
	loadedTypes [3]bool
}

// PaymentOrErr returns the Payment value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e PaymentAllocationEdges) PaymentOrErr() (*Payment, error) {
	if e.Payment != nil {
		return e.Payment, nil
	} else if e.loadedTypes[0] {
		return nil, &NotFoundError{label: payment.Label}
	}
	return nil, &NotLoadedError{edge: "payment"}
}

// BillItemOrErr returns the BillItem value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e PaymentAllocationEdges) BillItemOrErr() (*BillItem, error) {
	if e.BillItem != nil {
		return e.BillItem, nil
	} else if e.loadedTypes[1] {
		return nil, &NotFoundError{label: billitem.Label}
	}
	return nil, &NotLoadedError{edge: "bill_item"}
}

// InstallmentOrErr returns the Installment value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e PaymentAllocationEdges) InstallmentOrErr() (*Installment, error) {
	if e.Installment != nil {
		return e.Installment, nil
	} else if e.loadedTypes[2] {
		return nil, &NotFoundError{label: installment.Label}
	}
	return nil, &NotLoadedError{edge: "installment"}
}

// scanValues returns the types for scanning values from sql.Rows.
func (*PaymentAllocation) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case paymentallocation.FieldAmount:
			values[i] = new(sql.NullFloat64)
		case paymentallocation.FieldID, paymentallocation.FieldStatus, paymentallocation.FieldVersion, paymentallocation.FieldPaymentID, paymentallocation.FieldBillID, paymentallocation.FieldUserID, paymentallocation.FieldBillItemID, paymentallocation.FieldInstallmentID:
			values[i] = new(sql.NullInt64)
		case paymentallocation.FieldNote, paymentallocation.FieldState:
			values[i] = new(sql.NullString)
		case paymentallocation.FieldDeletedAt, paymentallocation.FieldCreatedAt, paymentallocation.FieldUpdatedAt:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the PaymentAllocation fields.
func (pa *PaymentAllocation) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case paymentallocation.FieldID:
			value, ok := values[i].(*sql.NullInt64)
			if !ok {
				return fmt.Errorf("unexpected type %T for field id", value)
			}
			pa.ID = int(value.Int64)
		case paymentallocation.FieldDeletedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field deleted_at", values[i])
			} else if value.Valid {
				pa.DeletedAt = value.Time
			}
		case paymentallocation.FieldStatus:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field status", values[i])
			} else if value.Valid {
				pa.Status = int8(value.Int64)
			}
		case paymentallocation.FieldVersion:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field version", values[i])
			} else if value.Valid {
				pa.Version = int(value.Int64)
			}
		case paymentallocation.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				pa.CreatedAt = value.Time
			}
		case paymentallocation.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				pa.UpdatedAt = value.Time
			}
		case paymentallocation.FieldPaymentID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field payment_id", values[i])
			} else if value.Valid {
				pa.PaymentID = int(value.Int64)
			}
		case paymentallocation.FieldBillID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field bill_id", values[i])
			} else if value.Valid {
				pa.BillID = int(value.Int64)
			}
		case paymentallocation.FieldUserID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field user_id", values[i])
			} else if value.Valid {
				pa.UserID = int(value.Int64)
			}
		case paymentallocation.FieldBillItemID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field bill_item_id", values[i])
			} else if value.Valid {
				pa.BillItemID = new(int)
				*pa.BillItemID = int(value.Int64)
			}
		case paymentallocation.FieldInstallmentID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field installment_id", values[i])
			} else if value.Valid {
				pa.InstallmentID = new(int)
				*pa.InstallmentID = int(value.Int64)
			}
		case paymentallocation.FieldNote:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field note", values[i])
			} else if value.Valid {
				pa.Note = value.String
			}
		case paymentallocation.FieldState:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field state", values[i])
			} else if value.Valid {
				pa.State = value.String
			}
		case paymentallocation.FieldAmount:
			if value, ok := values[i].(*sql.NullFloat64); !ok {
				return fmt.Errorf("unexpected type %T for field amount", values[i])
			} else if value.Valid {
				pa.Amount = value.Float64
			}
		default:
			pa.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the PaymentAllocation.
// This includes values selected through modifiers, order, etc.
func (pa *PaymentAllocation) Value(name string) (ent.Value, error) {
	return pa.selectValues.Get(name)
}

// QueryPayment queries the "payment" edge of the PaymentAllocation entity.
func (pa *PaymentAllocation) QueryPayment() *PaymentQuery {
	return NewPaymentAllocationClient(pa.config).QueryPayment(pa)
}

// QueryBillItem queries the "bill_item" edge of the PaymentAllocation entity.
func (pa *PaymentAllocation) QueryBillItem() *BillItemQuery {
	return NewPaymentAllocationClient(pa.config).QueryBillItem(pa)
}

// QueryInstallment queries the "installment" edge of the PaymentAllocation entity.
func (pa *PaymentAllocation) QueryInstallment() *InstallmentQuery {
	return NewPaymentAllocationClient(pa.config).QueryInstallment(pa)
}

// Update returns a builder for updating this PaymentAllocation.
// Note that you need to call PaymentAllocation.Unwrap() before calling this method if this PaymentAllocation
// was returned from a transaction, and the transaction was committed or rolled back.
func (pa *PaymentAllocation) Update() *PaymentAllocationUpdateOne {
	return NewPaymentAllocationClient(pa.config).UpdateOne(pa)
}

// Unwrap unwraps the PaymentAllocation entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (pa *PaymentAllocation) Unwrap() *PaymentAllocation {
	_tx, ok := pa.config.driver.(*txDriver)
	if !ok {
		panic("ent: PaymentAllocation is not a transactional entity")
	}
	pa.config.driver = _tx.drv
	return pa
}

// String implements the fmt.Stringer.
func (pa *PaymentAllocation) String() string {
	var builder strings.Builder
	builder.WriteString("PaymentAllocation(")
	builder.WriteString(fmt.Sprintf("id=%v, ", pa.ID))
	builder.WriteString("deleted_at=")
	builder.WriteString(pa.DeletedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("status=")
	builder.WriteString(fmt.Sprintf("%v", pa.Status))
	builder.WriteString(", ")
	builder.WriteString("version=")
	builder.WriteString(fmt.Sprintf("%v", pa.Version))
	builder.WriteString(", ")
	builder.WriteString("created_at=")
	builder.WriteString(pa.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(pa.UpdatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("payment_id=")
	builder.WriteString(fmt.Sprintf("%v", pa.PaymentID))
	builder.WriteString(", ")
	builder.WriteString("bill_id=")
	builder.WriteString(fmt.Sprintf("%v", pa.BillID))
	builder.WriteString(", ")
	builder.WriteString("user_id=")
	builder.WriteString(fmt.Sprintf("%v", pa.UserID))
	builder.WriteString(", ")
	if v := pa.BillItemID; v != nil {
		builder.WriteString("bill_item_id=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteString(", ")
	if v := pa.InstallmentID; v != nil {
		builder.WriteString("installment_id=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteString(", ")
	builder.WriteString("note=")
	builder.WriteString(pa.Note)
	builder.WriteString(", ")
	builder.WriteString("state=")
	builder.WriteString(pa.State)
	builder.WriteString(", ")
	builder.WriteString("amount=")
	builder.WriteString(fmt.Sprintf("%v", pa.Amount))
	builder.WriteByte(')')
	return builder.String()
}

// MarshalJSON implements the json.Marshaler interface.
func (pa *PaymentAllocation) MarshalJSON() ([]byte, error) {
	type Alias PaymentAllocation
	return json.Marshal(&struct {
		*Alias
		PaymentAllocationEdges
	}{
		Alias:                  (*Alias)(pa),
		PaymentAllocationEdges: pa.Edges,
	})
}

// MarshalSimpleTime
func (pa *PaymentAllocation) MarshalSimpleTime() ([]byte, error) {
	type Alias PaymentAllocation
	return json.Marshal(&struct {
		*Alias
		PaymentAllocationEdges
		DeletedAt string `json:"deleted_at,omitempty"`
		CreatedAt string `json:"created_at,omitempty"`
		UpdatedAt string `json:"updated_at,omitempty"`
	}{
		Alias:                  (*Alias)(pa),
		PaymentAllocationEdges: pa.Edges,
		DeletedAt:              pa.DeletedAt.Format("15:04 02/01/2006"),
		CreatedAt:              pa.CreatedAt.Format("15:04 02/01/2006"),
		UpdatedAt:              pa.UpdatedAt.Format("15:04 02/01/2006"),
	})
}

// PaymentAllocations is a parsable slice of PaymentAllocation.
type PaymentAllocations []*PaymentAllocation
