// Code generated by ent, DO NOT EDIT.

package ent

import (
	"bcare/ent/deal"
	"bcare/ent/dealuser"
	"bcare/ent/user"
	"encoding/json"
	"fmt"
	"strings"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

// DealUser is the model entity for the DealUser schema.
type DealUser struct {
	config `json:"-"`
	// ID of the ent.
	ID int `json:"id,omitempty"`
	// DealID holds the value of the "deal_id" field.
	DealID int `json:"deal_id,omitempty"`
	// UserID holds the value of the "user_id" field.
	UserID int `json:"user_id,omitempty"`
	// Role holds the value of the "role" field.
	Role dealuser.Role `json:"role,omitempty"`
	// Point holds the value of the "point" field.
	Point map[string]interface{} `json:"point,omitempty"`
	// Edges holds the relations/edges for other nodes in the graph.
	// The values are being populated by the DealUserQuery when eager-loading is set.
	Edges        DealUserEdges `json:"-"`
	selectValues sql.SelectValues
}

// DealUserEdges holds the relations/edges for other nodes in the graph.
type DealUserEdges struct {
	// Deal holds the value of the deal edge.
	Deal *Deal `json:"deal,omitempty"`
	// User holds the value of the user edge.
	User *User `json:"user,omitempty"`
	// Ratings holds the value of the ratings edge.
	Ratings []*DealUserRating `json:"ratings,omitempty"`
	// loadedTypes holds the information for reporting if a
	// type was loaded (or requested) in eager-loading or not.
	loadedTypes [3]bool
}

// DealOrErr returns the Deal value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e DealUserEdges) DealOrErr() (*Deal, error) {
	if e.Deal != nil {
		return e.Deal, nil
	} else if e.loadedTypes[0] {
		return nil, &NotFoundError{label: deal.Label}
	}
	return nil, &NotLoadedError{edge: "deal"}
}

// UserOrErr returns the User value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e DealUserEdges) UserOrErr() (*User, error) {
	if e.User != nil {
		return e.User, nil
	} else if e.loadedTypes[1] {
		return nil, &NotFoundError{label: user.Label}
	}
	return nil, &NotLoadedError{edge: "user"}
}

// RatingsOrErr returns the Ratings value or an error if the edge
// was not loaded in eager-loading.
func (e DealUserEdges) RatingsOrErr() ([]*DealUserRating, error) {
	if e.loadedTypes[2] {
		return e.Ratings, nil
	}
	return nil, &NotLoadedError{edge: "ratings"}
}

// scanValues returns the types for scanning values from sql.Rows.
func (*DealUser) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case dealuser.FieldPoint:
			values[i] = new([]byte)
		case dealuser.FieldID, dealuser.FieldDealID, dealuser.FieldUserID:
			values[i] = new(sql.NullInt64)
		case dealuser.FieldRole:
			values[i] = new(sql.NullString)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the DealUser fields.
func (du *DealUser) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case dealuser.FieldID:
			value, ok := values[i].(*sql.NullInt64)
			if !ok {
				return fmt.Errorf("unexpected type %T for field id", value)
			}
			du.ID = int(value.Int64)
		case dealuser.FieldDealID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field deal_id", values[i])
			} else if value.Valid {
				du.DealID = int(value.Int64)
			}
		case dealuser.FieldUserID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field user_id", values[i])
			} else if value.Valid {
				du.UserID = int(value.Int64)
			}
		case dealuser.FieldRole:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field role", values[i])
			} else if value.Valid {
				du.Role = dealuser.Role(value.String)
			}
		case dealuser.FieldPoint:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field point", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &du.Point); err != nil {
					return fmt.Errorf("unmarshal field point: %w", err)
				}
			}
		default:
			du.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the DealUser.
// This includes values selected through modifiers, order, etc.
func (du *DealUser) Value(name string) (ent.Value, error) {
	return du.selectValues.Get(name)
}

// QueryDeal queries the "deal" edge of the DealUser entity.
func (du *DealUser) QueryDeal() *DealQuery {
	return NewDealUserClient(du.config).QueryDeal(du)
}

// QueryUser queries the "user" edge of the DealUser entity.
func (du *DealUser) QueryUser() *UserQuery {
	return NewDealUserClient(du.config).QueryUser(du)
}

// QueryRatings queries the "ratings" edge of the DealUser entity.
func (du *DealUser) QueryRatings() *DealUserRatingQuery {
	return NewDealUserClient(du.config).QueryRatings(du)
}

// Update returns a builder for updating this DealUser.
// Note that you need to call DealUser.Unwrap() before calling this method if this DealUser
// was returned from a transaction, and the transaction was committed or rolled back.
func (du *DealUser) Update() *DealUserUpdateOne {
	return NewDealUserClient(du.config).UpdateOne(du)
}

// Unwrap unwraps the DealUser entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (du *DealUser) Unwrap() *DealUser {
	_tx, ok := du.config.driver.(*txDriver)
	if !ok {
		panic("ent: DealUser is not a transactional entity")
	}
	du.config.driver = _tx.drv
	return du
}

// String implements the fmt.Stringer.
func (du *DealUser) String() string {
	var builder strings.Builder
	builder.WriteString("DealUser(")
	builder.WriteString(fmt.Sprintf("id=%v, ", du.ID))
	builder.WriteString("deal_id=")
	builder.WriteString(fmt.Sprintf("%v", du.DealID))
	builder.WriteString(", ")
	builder.WriteString("user_id=")
	builder.WriteString(fmt.Sprintf("%v", du.UserID))
	builder.WriteString(", ")
	builder.WriteString("role=")
	builder.WriteString(fmt.Sprintf("%v", du.Role))
	builder.WriteString(", ")
	builder.WriteString("point=")
	builder.WriteString(fmt.Sprintf("%v", du.Point))
	builder.WriteByte(')')
	return builder.String()
}

// MarshalJSON implements the json.Marshaler interface.
func (du *DealUser) MarshalJSON() ([]byte, error) {
	type Alias DealUser
	return json.Marshal(&struct {
		*Alias
		DealUserEdges
	}{
		Alias:         (*Alias)(du),
		DealUserEdges: du.Edges,
	})
}

// MarshalSimpleTime
func (du *DealUser) MarshalSimpleTime() ([]byte, error) {
	type Alias DealUser
	return json.Marshal(&struct {
		*Alias
		DealUserEdges
	}{
		Alias:         (*Alias)(du),
		DealUserEdges: du.Edges,
	})
}

// DealUsers is a parsable slice of DealUser.
type DealUsers []*DealUser
