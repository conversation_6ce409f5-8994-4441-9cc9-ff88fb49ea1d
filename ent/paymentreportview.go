// Code generated by ent, DO NOT EDIT.

package ent

import (
	"bcare/ent/paymentreportview"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

// PaymentReportView is the model entity for the PaymentReportView schema.
type PaymentReportView struct {
	config `json:"-"`
	// CreatedAt holds the value of the "created_at" field.
	CreatedAt time.Time `json:"created_at,omitempty"`
	// ID holds the value of the "id" field.
	ID int `json:"id,omitempty"`
	// PersonCode holds the value of the "person_code" field.
	PersonCode string `json:"person_code,omitempty"`
	// Province holds the value of the "province" field.
	Province string `json:"province,omitempty"`
	// District holds the value of the "district" field.
	District string `json:"district,omitempty"`
	// Ward holds the value of the "ward" field.
	Ward string `json:"ward,omitempty"`
	// AddressNumber holds the value of the "address_number" field.
	AddressNumber string `json:"address_number,omitempty"`
	// FullName holds the value of the "full_name" field.
	FullName string `json:"full_name,omitempty"`
	// Person holds the value of the "person" field.
	Person map[string]interface{} `json:"person,omitempty"`
	// Phone holds the value of the "phone" field.
	Phone string `json:"phone,omitempty"`
	// ProductNames holds the value of the "product_names" field.
	ProductNames string `json:"product_names,omitempty"`
	// GroupNames holds the value of the "group_names" field.
	GroupNames string `json:"group_names,omitempty"`
	// Income holds the value of the "income" field.
	Income float64 `json:"income,omitempty"`
	// Expense holds the value of the "expense" field.
	Expense float64 `json:"expense,omitempty"`
	// TotalAmount holds the value of the "total_amount" field.
	TotalAmount float64 `json:"total_amount,omitempty"`
	// Cash holds the value of the "cash" field.
	Cash float64 `json:"cash,omitempty"`
	// Bank holds the value of the "bank" field.
	Bank float64 `json:"bank,omitempty"`
	// Mpos holds the value of the "mpos" field.
	Mpos float64 `json:"mpos,omitempty"`
	// CreditCard holds the value of the "credit_card" field.
	CreditCard float64 `json:"credit_card,omitempty"`
	// Momo holds the value of the "momo" field.
	Momo float64 `json:"momo,omitempty"`
	// Total amount of products (category 32)
	ProductAmount float64 `json:"product_amount,omitempty"`
	// Total amount of general services (category 27)
	GeneralServiceAmount float64 `json:"general_service_amount,omitempty"`
	// Total amount of general services (category 27)
	GxtnAmount float64 `json:"gxtn_amount,omitempty"`
	// Total amount of general services (category 27)
	VeneerAmount float64 `json:"veneer_amount,omitempty"`
	// Total amount of orthodontic services (category 28 or has deal_plan)
	OrthodonticAmount float64 `json:"orthodontic_amount,omitempty"`
	// Total amount of implant services (category 29 or no deal_plan)
	ImplantAmount float64 `json:"implant_amount,omitempty"`
	// Total amount of other categories
	OtherAmount float64 `json:"other_amount,omitempty"`
	// DoctorName holds the value of the "doctor_name" field.
	DoctorName *string `json:"doctor_name,omitempty"`
	// CreatorName holds the value of the "creator_name" field.
	CreatorName *string `json:"creator_name,omitempty"`
	// Doctor holds the value of the "doctor" field.
	Doctor       map[string]interface{} `json:"doctor,omitempty"`
	selectValues sql.SelectValues
}

// scanValues returns the types for scanning values from sql.Rows.
func (*PaymentReportView) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case paymentreportview.FieldPerson, paymentreportview.FieldDoctor:
			values[i] = new([]byte)
		case paymentreportview.FieldIncome, paymentreportview.FieldExpense, paymentreportview.FieldTotalAmount, paymentreportview.FieldCash, paymentreportview.FieldBank, paymentreportview.FieldMpos, paymentreportview.FieldCreditCard, paymentreportview.FieldMomo, paymentreportview.FieldProductAmount, paymentreportview.FieldGeneralServiceAmount, paymentreportview.FieldGxtnAmount, paymentreportview.FieldVeneerAmount, paymentreportview.FieldOrthodonticAmount, paymentreportview.FieldImplantAmount, paymentreportview.FieldOtherAmount:
			values[i] = new(sql.NullFloat64)
		case paymentreportview.FieldID:
			values[i] = new(sql.NullInt64)
		case paymentreportview.FieldPersonCode, paymentreportview.FieldProvince, paymentreportview.FieldDistrict, paymentreportview.FieldWard, paymentreportview.FieldAddressNumber, paymentreportview.FieldFullName, paymentreportview.FieldPhone, paymentreportview.FieldProductNames, paymentreportview.FieldGroupNames, paymentreportview.FieldDoctorName, paymentreportview.FieldCreatorName:
			values[i] = new(sql.NullString)
		case paymentreportview.FieldCreatedAt:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the PaymentReportView fields.
func (prv *PaymentReportView) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case paymentreportview.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				prv.CreatedAt = value.Time
			}
		case paymentreportview.FieldID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field id", values[i])
			} else if value.Valid {
				prv.ID = int(value.Int64)
			}
		case paymentreportview.FieldPersonCode:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field person_code", values[i])
			} else if value.Valid {
				prv.PersonCode = value.String
			}
		case paymentreportview.FieldProvince:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field province", values[i])
			} else if value.Valid {
				prv.Province = value.String
			}
		case paymentreportview.FieldDistrict:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field district", values[i])
			} else if value.Valid {
				prv.District = value.String
			}
		case paymentreportview.FieldWard:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field ward", values[i])
			} else if value.Valid {
				prv.Ward = value.String
			}
		case paymentreportview.FieldAddressNumber:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field address_number", values[i])
			} else if value.Valid {
				prv.AddressNumber = value.String
			}
		case paymentreportview.FieldFullName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field full_name", values[i])
			} else if value.Valid {
				prv.FullName = value.String
			}
		case paymentreportview.FieldPerson:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field person", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &prv.Person); err != nil {
					return fmt.Errorf("unmarshal field person: %w", err)
				}
			}
		case paymentreportview.FieldPhone:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field phone", values[i])
			} else if value.Valid {
				prv.Phone = value.String
			}
		case paymentreportview.FieldProductNames:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field product_names", values[i])
			} else if value.Valid {
				prv.ProductNames = value.String
			}
		case paymentreportview.FieldGroupNames:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field group_names", values[i])
			} else if value.Valid {
				prv.GroupNames = value.String
			}
		case paymentreportview.FieldIncome:
			if value, ok := values[i].(*sql.NullFloat64); !ok {
				return fmt.Errorf("unexpected type %T for field income", values[i])
			} else if value.Valid {
				prv.Income = value.Float64
			}
		case paymentreportview.FieldExpense:
			if value, ok := values[i].(*sql.NullFloat64); !ok {
				return fmt.Errorf("unexpected type %T for field expense", values[i])
			} else if value.Valid {
				prv.Expense = value.Float64
			}
		case paymentreportview.FieldTotalAmount:
			if value, ok := values[i].(*sql.NullFloat64); !ok {
				return fmt.Errorf("unexpected type %T for field total_amount", values[i])
			} else if value.Valid {
				prv.TotalAmount = value.Float64
			}
		case paymentreportview.FieldCash:
			if value, ok := values[i].(*sql.NullFloat64); !ok {
				return fmt.Errorf("unexpected type %T for field cash", values[i])
			} else if value.Valid {
				prv.Cash = value.Float64
			}
		case paymentreportview.FieldBank:
			if value, ok := values[i].(*sql.NullFloat64); !ok {
				return fmt.Errorf("unexpected type %T for field bank", values[i])
			} else if value.Valid {
				prv.Bank = value.Float64
			}
		case paymentreportview.FieldMpos:
			if value, ok := values[i].(*sql.NullFloat64); !ok {
				return fmt.Errorf("unexpected type %T for field mpos", values[i])
			} else if value.Valid {
				prv.Mpos = value.Float64
			}
		case paymentreportview.FieldCreditCard:
			if value, ok := values[i].(*sql.NullFloat64); !ok {
				return fmt.Errorf("unexpected type %T for field credit_card", values[i])
			} else if value.Valid {
				prv.CreditCard = value.Float64
			}
		case paymentreportview.FieldMomo:
			if value, ok := values[i].(*sql.NullFloat64); !ok {
				return fmt.Errorf("unexpected type %T for field momo", values[i])
			} else if value.Valid {
				prv.Momo = value.Float64
			}
		case paymentreportview.FieldProductAmount:
			if value, ok := values[i].(*sql.NullFloat64); !ok {
				return fmt.Errorf("unexpected type %T for field product_amount", values[i])
			} else if value.Valid {
				prv.ProductAmount = value.Float64
			}
		case paymentreportview.FieldGeneralServiceAmount:
			if value, ok := values[i].(*sql.NullFloat64); !ok {
				return fmt.Errorf("unexpected type %T for field general_service_amount", values[i])
			} else if value.Valid {
				prv.GeneralServiceAmount = value.Float64
			}
		case paymentreportview.FieldGxtnAmount:
			if value, ok := values[i].(*sql.NullFloat64); !ok {
				return fmt.Errorf("unexpected type %T for field gxtn_amount", values[i])
			} else if value.Valid {
				prv.GxtnAmount = value.Float64
			}
		case paymentreportview.FieldVeneerAmount:
			if value, ok := values[i].(*sql.NullFloat64); !ok {
				return fmt.Errorf("unexpected type %T for field veneer_amount", values[i])
			} else if value.Valid {
				prv.VeneerAmount = value.Float64
			}
		case paymentreportview.FieldOrthodonticAmount:
			if value, ok := values[i].(*sql.NullFloat64); !ok {
				return fmt.Errorf("unexpected type %T for field orthodontic_amount", values[i])
			} else if value.Valid {
				prv.OrthodonticAmount = value.Float64
			}
		case paymentreportview.FieldImplantAmount:
			if value, ok := values[i].(*sql.NullFloat64); !ok {
				return fmt.Errorf("unexpected type %T for field implant_amount", values[i])
			} else if value.Valid {
				prv.ImplantAmount = value.Float64
			}
		case paymentreportview.FieldOtherAmount:
			if value, ok := values[i].(*sql.NullFloat64); !ok {
				return fmt.Errorf("unexpected type %T for field other_amount", values[i])
			} else if value.Valid {
				prv.OtherAmount = value.Float64
			}
		case paymentreportview.FieldDoctorName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field doctor_name", values[i])
			} else if value.Valid {
				prv.DoctorName = new(string)
				*prv.DoctorName = value.String
			}
		case paymentreportview.FieldCreatorName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field creator_name", values[i])
			} else if value.Valid {
				prv.CreatorName = new(string)
				*prv.CreatorName = value.String
			}
		case paymentreportview.FieldDoctor:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field doctor", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &prv.Doctor); err != nil {
					return fmt.Errorf("unmarshal field doctor: %w", err)
				}
			}
		default:
			prv.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the PaymentReportView.
// This includes values selected through modifiers, order, etc.
func (prv *PaymentReportView) Value(name string) (ent.Value, error) {
	return prv.selectValues.Get(name)
}

// Unwrap unwraps the PaymentReportView entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (prv *PaymentReportView) Unwrap() *PaymentReportView {
	_tx, ok := prv.config.driver.(*txDriver)
	if !ok {
		panic("ent: PaymentReportView is not a transactional entity")
	}
	prv.config.driver = _tx.drv
	return prv
}

// String implements the fmt.Stringer.
func (prv *PaymentReportView) String() string {
	var builder strings.Builder
	builder.WriteString("PaymentReportView(")
	builder.WriteString("created_at=")
	builder.WriteString(prv.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("id=")
	builder.WriteString(fmt.Sprintf("%v", prv.ID))
	builder.WriteString(", ")
	builder.WriteString("person_code=")
	builder.WriteString(prv.PersonCode)
	builder.WriteString(", ")
	builder.WriteString("province=")
	builder.WriteString(prv.Province)
	builder.WriteString(", ")
	builder.WriteString("district=")
	builder.WriteString(prv.District)
	builder.WriteString(", ")
	builder.WriteString("ward=")
	builder.WriteString(prv.Ward)
	builder.WriteString(", ")
	builder.WriteString("address_number=")
	builder.WriteString(prv.AddressNumber)
	builder.WriteString(", ")
	builder.WriteString("full_name=")
	builder.WriteString(prv.FullName)
	builder.WriteString(", ")
	builder.WriteString("person=")
	builder.WriteString(fmt.Sprintf("%v", prv.Person))
	builder.WriteString(", ")
	builder.WriteString("phone=")
	builder.WriteString(prv.Phone)
	builder.WriteString(", ")
	builder.WriteString("product_names=")
	builder.WriteString(prv.ProductNames)
	builder.WriteString(", ")
	builder.WriteString("group_names=")
	builder.WriteString(prv.GroupNames)
	builder.WriteString(", ")
	builder.WriteString("income=")
	builder.WriteString(fmt.Sprintf("%v", prv.Income))
	builder.WriteString(", ")
	builder.WriteString("expense=")
	builder.WriteString(fmt.Sprintf("%v", prv.Expense))
	builder.WriteString(", ")
	builder.WriteString("total_amount=")
	builder.WriteString(fmt.Sprintf("%v", prv.TotalAmount))
	builder.WriteString(", ")
	builder.WriteString("cash=")
	builder.WriteString(fmt.Sprintf("%v", prv.Cash))
	builder.WriteString(", ")
	builder.WriteString("bank=")
	builder.WriteString(fmt.Sprintf("%v", prv.Bank))
	builder.WriteString(", ")
	builder.WriteString("mpos=")
	builder.WriteString(fmt.Sprintf("%v", prv.Mpos))
	builder.WriteString(", ")
	builder.WriteString("credit_card=")
	builder.WriteString(fmt.Sprintf("%v", prv.CreditCard))
	builder.WriteString(", ")
	builder.WriteString("momo=")
	builder.WriteString(fmt.Sprintf("%v", prv.Momo))
	builder.WriteString(", ")
	builder.WriteString("product_amount=")
	builder.WriteString(fmt.Sprintf("%v", prv.ProductAmount))
	builder.WriteString(", ")
	builder.WriteString("general_service_amount=")
	builder.WriteString(fmt.Sprintf("%v", prv.GeneralServiceAmount))
	builder.WriteString(", ")
	builder.WriteString("gxtn_amount=")
	builder.WriteString(fmt.Sprintf("%v", prv.GxtnAmount))
	builder.WriteString(", ")
	builder.WriteString("veneer_amount=")
	builder.WriteString(fmt.Sprintf("%v", prv.VeneerAmount))
	builder.WriteString(", ")
	builder.WriteString("orthodontic_amount=")
	builder.WriteString(fmt.Sprintf("%v", prv.OrthodonticAmount))
	builder.WriteString(", ")
	builder.WriteString("implant_amount=")
	builder.WriteString(fmt.Sprintf("%v", prv.ImplantAmount))
	builder.WriteString(", ")
	builder.WriteString("other_amount=")
	builder.WriteString(fmt.Sprintf("%v", prv.OtherAmount))
	builder.WriteString(", ")
	if v := prv.DoctorName; v != nil {
		builder.WriteString("doctor_name=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := prv.CreatorName; v != nil {
		builder.WriteString("creator_name=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	builder.WriteString("doctor=")
	builder.WriteString(fmt.Sprintf("%v", prv.Doctor))
	builder.WriteByte(')')
	return builder.String()
}

// PaymentReportViews is a parsable slice of PaymentReportView.
type PaymentReportViews []*PaymentReportView
