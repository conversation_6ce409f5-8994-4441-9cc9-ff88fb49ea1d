// Code generated by ent, DO NOT EDIT.

package deal

import (
	"fmt"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

const (
	// Label holds the string label denoting the deal type in the database.
	Label = "deal"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldDeletedAt holds the string denoting the deleted_at field in the database.
	FieldDeletedAt = "deleted_at"
	// FieldStatus holds the string denoting the status field in the database.
	FieldStatus = "status"
	// FieldVersion holds the string denoting the version field in the database.
	FieldVersion = "version"
	// FieldCreatedAt holds the string denoting the created_at field in the database.
	FieldCreatedAt = "created_at"
	// FieldUpdatedAt holds the string denoting the updated_at field in the database.
	FieldUpdatedAt = "updated_at"
	// FieldPersonID holds the string denoting the person_id field in the database.
	FieldPersonID = "person_id"
	// FieldParentDealID holds the string denoting the parent_deal_id field in the database.
	FieldParentDealID = "parent_deal_id"
	// FieldTotalAmount holds the string denoting the total_amount field in the database.
	FieldTotalAmount = "total_amount"
	// FieldStageID holds the string denoting the stage_id field in the database.
	FieldStageID = "stage_id"
	// FieldStageHistory holds the string denoting the stage_history field in the database.
	FieldStageHistory = "stage_history"
	// FieldName holds the string denoting the name field in the database.
	FieldName = "name"
	// FieldState holds the string denoting the state field in the database.
	FieldState = "state"
	// EdgePerson holds the string denoting the person edge name in mutations.
	EdgePerson = "person"
	// EdgeStage holds the string denoting the stage edge name in mutations.
	EdgeStage = "stage"
	// EdgeAttachments holds the string denoting the attachments edge name in mutations.
	EdgeAttachments = "attachments"
	// EdgePlans holds the string denoting the plans edge name in mutations.
	EdgePlans = "plans"
	// EdgeBills holds the string denoting the bills edge name in mutations.
	EdgeBills = "bills"
	// EdgeTracks holds the string denoting the tracks edge name in mutations.
	EdgeTracks = "tracks"
	// EdgeTasks holds the string denoting the tasks edge name in mutations.
	EdgeTasks = "tasks"
	// EdgeAssignees holds the string denoting the assignees edge name in mutations.
	EdgeAssignees = "assignees"
	// EdgeDeposits holds the string denoting the deposits edge name in mutations.
	EdgeDeposits = "deposits"
	// EdgeDiscountUsages holds the string denoting the discount_usages edge name in mutations.
	EdgeDiscountUsages = "discount_usages"
	// EdgeDealAssignment holds the string denoting the deal_assignment edge name in mutations.
	EdgeDealAssignment = "deal_assignment"
	// Table holds the table name of the deal in the database.
	Table = "deal"
	// PersonTable is the table that holds the person relation/edge.
	PersonTable = "deal"
	// PersonInverseTable is the table name for the Person entity.
	// It exists in this package in order to avoid circular dependency with the "person" package.
	PersonInverseTable = "person"
	// PersonColumn is the table column denoting the person relation/edge.
	PersonColumn = "person_id"
	// StageTable is the table that holds the stage relation/edge.
	StageTable = "deal"
	// StageInverseTable is the table name for the Stage entity.
	// It exists in this package in order to avoid circular dependency with the "stage" package.
	StageInverseTable = "stage"
	// StageColumn is the table column denoting the stage relation/edge.
	StageColumn = "stage_id"
	// AttachmentsTable is the table that holds the attachments relation/edge.
	AttachmentsTable = "attachment"
	// AttachmentsInverseTable is the table name for the Attachment entity.
	// It exists in this package in order to avoid circular dependency with the "attachment" package.
	AttachmentsInverseTable = "attachment"
	// AttachmentsColumn is the table column denoting the attachments relation/edge.
	AttachmentsColumn = "deal_id"
	// PlansTable is the table that holds the plans relation/edge.
	PlansTable = "installment_plan"
	// PlansInverseTable is the table name for the InstallmentPlan entity.
	// It exists in this package in order to avoid circular dependency with the "installmentplan" package.
	PlansInverseTable = "installment_plan"
	// PlansColumn is the table column denoting the plans relation/edge.
	PlansColumn = "deal_id"
	// BillsTable is the table that holds the bills relation/edge.
	BillsTable = "bill"
	// BillsInverseTable is the table name for the Bill entity.
	// It exists in this package in order to avoid circular dependency with the "bill" package.
	BillsInverseTable = "bill"
	// BillsColumn is the table column denoting the bills relation/edge.
	BillsColumn = "deal_id"
	// TracksTable is the table that holds the tracks relation/edge.
	TracksTable = "track"
	// TracksInverseTable is the table name for the Track entity.
	// It exists in this package in order to avoid circular dependency with the "track" package.
	TracksInverseTable = "track"
	// TracksColumn is the table column denoting the tracks relation/edge.
	TracksColumn = "deal_id"
	// TasksTable is the table that holds the tasks relation/edge.
	TasksTable = "task"
	// TasksInverseTable is the table name for the Task entity.
	// It exists in this package in order to avoid circular dependency with the "task" package.
	TasksInverseTable = "task"
	// TasksColumn is the table column denoting the tasks relation/edge.
	TasksColumn = "deal_id"
	// AssigneesTable is the table that holds the assignees relation/edge. The primary key declared below.
	AssigneesTable = "deal_user"
	// AssigneesInverseTable is the table name for the User entity.
	// It exists in this package in order to avoid circular dependency with the "user" package.
	AssigneesInverseTable = "user"
	// DepositsTable is the table that holds the deposits relation/edge.
	DepositsTable = "deposits"
	// DepositsInverseTable is the table name for the Deposit entity.
	// It exists in this package in order to avoid circular dependency with the "deposit" package.
	DepositsInverseTable = "deposits"
	// DepositsColumn is the table column denoting the deposits relation/edge.
	DepositsColumn = "deal_id"
	// DiscountUsagesTable is the table that holds the discount_usages relation/edge.
	DiscountUsagesTable = "discount_usage"
	// DiscountUsagesInverseTable is the table name for the DiscountUsage entity.
	// It exists in this package in order to avoid circular dependency with the "discountusage" package.
	DiscountUsagesInverseTable = "discount_usage"
	// DiscountUsagesColumn is the table column denoting the discount_usages relation/edge.
	DiscountUsagesColumn = "deal_id"
	// DealAssignmentTable is the table that holds the deal_assignment relation/edge.
	DealAssignmentTable = "deal_user"
	// DealAssignmentInverseTable is the table name for the DealUser entity.
	// It exists in this package in order to avoid circular dependency with the "dealuser" package.
	DealAssignmentInverseTable = "deal_user"
	// DealAssignmentColumn is the table column denoting the deal_assignment relation/edge.
	DealAssignmentColumn = "deal_id"
)

// Columns holds all SQL columns for deal fields.
var Columns = []string{
	FieldID,
	FieldDeletedAt,
	FieldStatus,
	FieldVersion,
	FieldCreatedAt,
	FieldUpdatedAt,
	FieldPersonID,
	FieldParentDealID,
	FieldTotalAmount,
	FieldStageID,
	FieldStageHistory,
	FieldName,
	FieldState,
}

var (
	// AssigneesPrimaryKey and AssigneesColumn2 are the table columns denoting the
	// primary key for the assignees relation (M2M).
	AssigneesPrimaryKey = []string{"deal_id", "user_id"}
)

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

// Note that the variables below are initialized by the runtime
// package on the initialization of the application. Therefore,
// it should be imported in the main as follows:
//
//	import _ "bcare/ent/runtime"
var (
	Hooks        [3]ent.Hook
	Interceptors [1]ent.Interceptor
	// DefaultStatus holds the default value on creation for the "status" field.
	DefaultStatus int8
	// DefaultVersion holds the default value on creation for the "version" field.
	DefaultVersion int
	// DefaultCreatedAt holds the default value on creation for the "created_at" field.
	DefaultCreatedAt func() time.Time
	// DefaultUpdatedAt holds the default value on creation for the "updated_at" field.
	DefaultUpdatedAt func() time.Time
	// UpdateDefaultUpdatedAt holds the default value on update for the "updated_at" field.
	UpdateDefaultUpdatedAt func() time.Time
	// DefaultTotalAmount holds the default value on creation for the "total_amount" field.
	DefaultTotalAmount float64
)

// State defines the type for the "state" enum field.
type State string

// StateDraft is the default value of the State enum.
const DefaultState = StateDraft

// State values.
const (
	StateDraft     State = "draft"
	StateActive    State = "active"
	StatePaying    State = "paying"
	StateWon       State = "won"
	StateLost      State = "lost"
	StateCancelled State = "cancelled"
)

func (s State) String() string {
	return string(s)
}

// StateValidator is a validator for the "state" field enum values. It is called by the builders before save.
func StateValidator(s State) error {
	switch s {
	case StateDraft, StateActive, StatePaying, StateWon, StateLost, StateCancelled:
		return nil
	default:
		return fmt.Errorf("deal: invalid enum value for state field: %q", s)
	}
}

// OrderOption defines the ordering options for the Deal queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByDeletedAt orders the results by the deleted_at field.
func ByDeletedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDeletedAt, opts...).ToFunc()
}

// ByStatus orders the results by the status field.
func ByStatus(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldStatus, opts...).ToFunc()
}

// ByVersion orders the results by the version field.
func ByVersion(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldVersion, opts...).ToFunc()
}

// ByCreatedAt orders the results by the created_at field.
func ByCreatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedAt, opts...).ToFunc()
}

// ByUpdatedAt orders the results by the updated_at field.
func ByUpdatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdatedAt, opts...).ToFunc()
}

// ByPersonID orders the results by the person_id field.
func ByPersonID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPersonID, opts...).ToFunc()
}

// ByParentDealID orders the results by the parent_deal_id field.
func ByParentDealID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldParentDealID, opts...).ToFunc()
}

// ByTotalAmount orders the results by the total_amount field.
func ByTotalAmount(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldTotalAmount, opts...).ToFunc()
}

// ByStageID orders the results by the stage_id field.
func ByStageID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldStageID, opts...).ToFunc()
}

// ByName orders the results by the name field.
func ByName(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldName, opts...).ToFunc()
}

// ByState orders the results by the state field.
func ByState(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldState, opts...).ToFunc()
}

// ByPersonField orders the results by person field.
func ByPersonField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newPersonStep(), sql.OrderByField(field, opts...))
	}
}

// ByStageField orders the results by stage field.
func ByStageField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newStageStep(), sql.OrderByField(field, opts...))
	}
}

// ByAttachmentsCount orders the results by attachments count.
func ByAttachmentsCount(opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborsCount(s, newAttachmentsStep(), opts...)
	}
}

// ByAttachments orders the results by attachments terms.
func ByAttachments(term sql.OrderTerm, terms ...sql.OrderTerm) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newAttachmentsStep(), append([]sql.OrderTerm{term}, terms...)...)
	}
}

// ByPlansCount orders the results by plans count.
func ByPlansCount(opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborsCount(s, newPlansStep(), opts...)
	}
}

// ByPlans orders the results by plans terms.
func ByPlans(term sql.OrderTerm, terms ...sql.OrderTerm) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newPlansStep(), append([]sql.OrderTerm{term}, terms...)...)
	}
}

// ByBillsCount orders the results by bills count.
func ByBillsCount(opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborsCount(s, newBillsStep(), opts...)
	}
}

// ByBills orders the results by bills terms.
func ByBills(term sql.OrderTerm, terms ...sql.OrderTerm) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newBillsStep(), append([]sql.OrderTerm{term}, terms...)...)
	}
}

// ByTracksCount orders the results by tracks count.
func ByTracksCount(opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborsCount(s, newTracksStep(), opts...)
	}
}

// ByTracks orders the results by tracks terms.
func ByTracks(term sql.OrderTerm, terms ...sql.OrderTerm) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newTracksStep(), append([]sql.OrderTerm{term}, terms...)...)
	}
}

// ByTasksCount orders the results by tasks count.
func ByTasksCount(opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborsCount(s, newTasksStep(), opts...)
	}
}

// ByTasks orders the results by tasks terms.
func ByTasks(term sql.OrderTerm, terms ...sql.OrderTerm) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newTasksStep(), append([]sql.OrderTerm{term}, terms...)...)
	}
}

// ByAssigneesCount orders the results by assignees count.
func ByAssigneesCount(opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborsCount(s, newAssigneesStep(), opts...)
	}
}

// ByAssignees orders the results by assignees terms.
func ByAssignees(term sql.OrderTerm, terms ...sql.OrderTerm) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newAssigneesStep(), append([]sql.OrderTerm{term}, terms...)...)
	}
}

// ByDepositsCount orders the results by deposits count.
func ByDepositsCount(opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborsCount(s, newDepositsStep(), opts...)
	}
}

// ByDeposits orders the results by deposits terms.
func ByDeposits(term sql.OrderTerm, terms ...sql.OrderTerm) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newDepositsStep(), append([]sql.OrderTerm{term}, terms...)...)
	}
}

// ByDiscountUsagesCount orders the results by discount_usages count.
func ByDiscountUsagesCount(opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborsCount(s, newDiscountUsagesStep(), opts...)
	}
}

// ByDiscountUsages orders the results by discount_usages terms.
func ByDiscountUsages(term sql.OrderTerm, terms ...sql.OrderTerm) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newDiscountUsagesStep(), append([]sql.OrderTerm{term}, terms...)...)
	}
}

// ByDealAssignmentCount orders the results by deal_assignment count.
func ByDealAssignmentCount(opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborsCount(s, newDealAssignmentStep(), opts...)
	}
}

// ByDealAssignment orders the results by deal_assignment terms.
func ByDealAssignment(term sql.OrderTerm, terms ...sql.OrderTerm) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newDealAssignmentStep(), append([]sql.OrderTerm{term}, terms...)...)
	}
}
func newPersonStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(PersonInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2O, false, PersonTable, PersonColumn),
	)
}
func newStageStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(StageInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2O, false, StageTable, StageColumn),
	)
}
func newAttachmentsStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(AttachmentsInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.O2M, true, AttachmentsTable, AttachmentsColumn),
	)
}
func newPlansStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(PlansInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.O2M, true, PlansTable, PlansColumn),
	)
}
func newBillsStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(BillsInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.O2M, true, BillsTable, BillsColumn),
	)
}
func newTracksStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(TracksInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.O2M, true, TracksTable, TracksColumn),
	)
}
func newTasksStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(TasksInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.O2M, true, TasksTable, TasksColumn),
	)
}
func newAssigneesStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(AssigneesInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2M, false, AssigneesTable, AssigneesPrimaryKey...),
	)
}
func newDepositsStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(DepositsInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.O2M, true, DepositsTable, DepositsColumn),
	)
}
func newDiscountUsagesStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(DiscountUsagesInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.O2M, true, DiscountUsagesTable, DiscountUsagesColumn),
	)
}
func newDealAssignmentStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(DealAssignmentInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.O2M, true, DealAssignmentTable, DealAssignmentColumn),
	)
}
