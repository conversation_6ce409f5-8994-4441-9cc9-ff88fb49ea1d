// Code generated by ent, DO NOT EDIT.

package deal

import (
	"bcare/ent/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

// ID filters vertices based on their ID field.
func ID(id int) predicate.Deal {
	return predicate.Deal(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id int) predicate.Deal {
	return predicate.Deal(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id int) predicate.Deal {
	return predicate.Deal(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...int) predicate.Deal {
	return predicate.Deal(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...int) predicate.Deal {
	return predicate.Deal(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id int) predicate.Deal {
	return predicate.Deal(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id int) predicate.Deal {
	return predicate.Deal(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id int) predicate.Deal {
	return predicate.Deal(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id int) predicate.Deal {
	return predicate.Deal(sql.FieldLTE(FieldID, id))
}

// DeletedAt applies equality check predicate on the "deleted_at" field. It's identical to DeletedAtEQ.
func DeletedAt(v time.Time) predicate.Deal {
	return predicate.Deal(sql.FieldEQ(FieldDeletedAt, v))
}

// Status applies equality check predicate on the "status" field. It's identical to StatusEQ.
func Status(v int8) predicate.Deal {
	return predicate.Deal(sql.FieldEQ(FieldStatus, v))
}

// Version applies equality check predicate on the "version" field. It's identical to VersionEQ.
func Version(v int) predicate.Deal {
	return predicate.Deal(sql.FieldEQ(FieldVersion, v))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.Deal {
	return predicate.Deal(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.Deal {
	return predicate.Deal(sql.FieldEQ(FieldUpdatedAt, v))
}

// PersonID applies equality check predicate on the "person_id" field. It's identical to PersonIDEQ.
func PersonID(v int) predicate.Deal {
	return predicate.Deal(sql.FieldEQ(FieldPersonID, v))
}

// ParentDealID applies equality check predicate on the "parent_deal_id" field. It's identical to ParentDealIDEQ.
func ParentDealID(v int) predicate.Deal {
	return predicate.Deal(sql.FieldEQ(FieldParentDealID, v))
}

// TotalAmount applies equality check predicate on the "total_amount" field. It's identical to TotalAmountEQ.
func TotalAmount(v float64) predicate.Deal {
	return predicate.Deal(sql.FieldEQ(FieldTotalAmount, v))
}

// StageID applies equality check predicate on the "stage_id" field. It's identical to StageIDEQ.
func StageID(v int) predicate.Deal {
	return predicate.Deal(sql.FieldEQ(FieldStageID, v))
}

// Name applies equality check predicate on the "name" field. It's identical to NameEQ.
func Name(v string) predicate.Deal {
	return predicate.Deal(sql.FieldEQ(FieldName, v))
}

// DeletedAtEQ applies the EQ predicate on the "deleted_at" field.
func DeletedAtEQ(v time.Time) predicate.Deal {
	return predicate.Deal(sql.FieldEQ(FieldDeletedAt, v))
}

// DeletedAtNEQ applies the NEQ predicate on the "deleted_at" field.
func DeletedAtNEQ(v time.Time) predicate.Deal {
	return predicate.Deal(sql.FieldNEQ(FieldDeletedAt, v))
}

// DeletedAtIn applies the In predicate on the "deleted_at" field.
func DeletedAtIn(vs ...time.Time) predicate.Deal {
	return predicate.Deal(sql.FieldIn(FieldDeletedAt, vs...))
}

// DeletedAtNotIn applies the NotIn predicate on the "deleted_at" field.
func DeletedAtNotIn(vs ...time.Time) predicate.Deal {
	return predicate.Deal(sql.FieldNotIn(FieldDeletedAt, vs...))
}

// DeletedAtGT applies the GT predicate on the "deleted_at" field.
func DeletedAtGT(v time.Time) predicate.Deal {
	return predicate.Deal(sql.FieldGT(FieldDeletedAt, v))
}

// DeletedAtGTE applies the GTE predicate on the "deleted_at" field.
func DeletedAtGTE(v time.Time) predicate.Deal {
	return predicate.Deal(sql.FieldGTE(FieldDeletedAt, v))
}

// DeletedAtLT applies the LT predicate on the "deleted_at" field.
func DeletedAtLT(v time.Time) predicate.Deal {
	return predicate.Deal(sql.FieldLT(FieldDeletedAt, v))
}

// DeletedAtLTE applies the LTE predicate on the "deleted_at" field.
func DeletedAtLTE(v time.Time) predicate.Deal {
	return predicate.Deal(sql.FieldLTE(FieldDeletedAt, v))
}

// DeletedAtIsNil applies the IsNil predicate on the "deleted_at" field.
func DeletedAtIsNil() predicate.Deal {
	return predicate.Deal(sql.FieldIsNull(FieldDeletedAt))
}

// DeletedAtNotNil applies the NotNil predicate on the "deleted_at" field.
func DeletedAtNotNil() predicate.Deal {
	return predicate.Deal(sql.FieldNotNull(FieldDeletedAt))
}

// StatusEQ applies the EQ predicate on the "status" field.
func StatusEQ(v int8) predicate.Deal {
	return predicate.Deal(sql.FieldEQ(FieldStatus, v))
}

// StatusNEQ applies the NEQ predicate on the "status" field.
func StatusNEQ(v int8) predicate.Deal {
	return predicate.Deal(sql.FieldNEQ(FieldStatus, v))
}

// StatusIn applies the In predicate on the "status" field.
func StatusIn(vs ...int8) predicate.Deal {
	return predicate.Deal(sql.FieldIn(FieldStatus, vs...))
}

// StatusNotIn applies the NotIn predicate on the "status" field.
func StatusNotIn(vs ...int8) predicate.Deal {
	return predicate.Deal(sql.FieldNotIn(FieldStatus, vs...))
}

// StatusGT applies the GT predicate on the "status" field.
func StatusGT(v int8) predicate.Deal {
	return predicate.Deal(sql.FieldGT(FieldStatus, v))
}

// StatusGTE applies the GTE predicate on the "status" field.
func StatusGTE(v int8) predicate.Deal {
	return predicate.Deal(sql.FieldGTE(FieldStatus, v))
}

// StatusLT applies the LT predicate on the "status" field.
func StatusLT(v int8) predicate.Deal {
	return predicate.Deal(sql.FieldLT(FieldStatus, v))
}

// StatusLTE applies the LTE predicate on the "status" field.
func StatusLTE(v int8) predicate.Deal {
	return predicate.Deal(sql.FieldLTE(FieldStatus, v))
}

// VersionEQ applies the EQ predicate on the "version" field.
func VersionEQ(v int) predicate.Deal {
	return predicate.Deal(sql.FieldEQ(FieldVersion, v))
}

// VersionNEQ applies the NEQ predicate on the "version" field.
func VersionNEQ(v int) predicate.Deal {
	return predicate.Deal(sql.FieldNEQ(FieldVersion, v))
}

// VersionIn applies the In predicate on the "version" field.
func VersionIn(vs ...int) predicate.Deal {
	return predicate.Deal(sql.FieldIn(FieldVersion, vs...))
}

// VersionNotIn applies the NotIn predicate on the "version" field.
func VersionNotIn(vs ...int) predicate.Deal {
	return predicate.Deal(sql.FieldNotIn(FieldVersion, vs...))
}

// VersionGT applies the GT predicate on the "version" field.
func VersionGT(v int) predicate.Deal {
	return predicate.Deal(sql.FieldGT(FieldVersion, v))
}

// VersionGTE applies the GTE predicate on the "version" field.
func VersionGTE(v int) predicate.Deal {
	return predicate.Deal(sql.FieldGTE(FieldVersion, v))
}

// VersionLT applies the LT predicate on the "version" field.
func VersionLT(v int) predicate.Deal {
	return predicate.Deal(sql.FieldLT(FieldVersion, v))
}

// VersionLTE applies the LTE predicate on the "version" field.
func VersionLTE(v int) predicate.Deal {
	return predicate.Deal(sql.FieldLTE(FieldVersion, v))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.Deal {
	return predicate.Deal(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.Deal {
	return predicate.Deal(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.Deal {
	return predicate.Deal(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.Deal {
	return predicate.Deal(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.Deal {
	return predicate.Deal(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.Deal {
	return predicate.Deal(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.Deal {
	return predicate.Deal(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.Deal {
	return predicate.Deal(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.Deal {
	return predicate.Deal(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.Deal {
	return predicate.Deal(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.Deal {
	return predicate.Deal(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.Deal {
	return predicate.Deal(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.Deal {
	return predicate.Deal(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.Deal {
	return predicate.Deal(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.Deal {
	return predicate.Deal(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.Deal {
	return predicate.Deal(sql.FieldLTE(FieldUpdatedAt, v))
}

// PersonIDEQ applies the EQ predicate on the "person_id" field.
func PersonIDEQ(v int) predicate.Deal {
	return predicate.Deal(sql.FieldEQ(FieldPersonID, v))
}

// PersonIDNEQ applies the NEQ predicate on the "person_id" field.
func PersonIDNEQ(v int) predicate.Deal {
	return predicate.Deal(sql.FieldNEQ(FieldPersonID, v))
}

// PersonIDIn applies the In predicate on the "person_id" field.
func PersonIDIn(vs ...int) predicate.Deal {
	return predicate.Deal(sql.FieldIn(FieldPersonID, vs...))
}

// PersonIDNotIn applies the NotIn predicate on the "person_id" field.
func PersonIDNotIn(vs ...int) predicate.Deal {
	return predicate.Deal(sql.FieldNotIn(FieldPersonID, vs...))
}

// PersonIDIsNil applies the IsNil predicate on the "person_id" field.
func PersonIDIsNil() predicate.Deal {
	return predicate.Deal(sql.FieldIsNull(FieldPersonID))
}

// PersonIDNotNil applies the NotNil predicate on the "person_id" field.
func PersonIDNotNil() predicate.Deal {
	return predicate.Deal(sql.FieldNotNull(FieldPersonID))
}

// ParentDealIDEQ applies the EQ predicate on the "parent_deal_id" field.
func ParentDealIDEQ(v int) predicate.Deal {
	return predicate.Deal(sql.FieldEQ(FieldParentDealID, v))
}

// ParentDealIDNEQ applies the NEQ predicate on the "parent_deal_id" field.
func ParentDealIDNEQ(v int) predicate.Deal {
	return predicate.Deal(sql.FieldNEQ(FieldParentDealID, v))
}

// ParentDealIDIn applies the In predicate on the "parent_deal_id" field.
func ParentDealIDIn(vs ...int) predicate.Deal {
	return predicate.Deal(sql.FieldIn(FieldParentDealID, vs...))
}

// ParentDealIDNotIn applies the NotIn predicate on the "parent_deal_id" field.
func ParentDealIDNotIn(vs ...int) predicate.Deal {
	return predicate.Deal(sql.FieldNotIn(FieldParentDealID, vs...))
}

// ParentDealIDGT applies the GT predicate on the "parent_deal_id" field.
func ParentDealIDGT(v int) predicate.Deal {
	return predicate.Deal(sql.FieldGT(FieldParentDealID, v))
}

// ParentDealIDGTE applies the GTE predicate on the "parent_deal_id" field.
func ParentDealIDGTE(v int) predicate.Deal {
	return predicate.Deal(sql.FieldGTE(FieldParentDealID, v))
}

// ParentDealIDLT applies the LT predicate on the "parent_deal_id" field.
func ParentDealIDLT(v int) predicate.Deal {
	return predicate.Deal(sql.FieldLT(FieldParentDealID, v))
}

// ParentDealIDLTE applies the LTE predicate on the "parent_deal_id" field.
func ParentDealIDLTE(v int) predicate.Deal {
	return predicate.Deal(sql.FieldLTE(FieldParentDealID, v))
}

// ParentDealIDIsNil applies the IsNil predicate on the "parent_deal_id" field.
func ParentDealIDIsNil() predicate.Deal {
	return predicate.Deal(sql.FieldIsNull(FieldParentDealID))
}

// ParentDealIDNotNil applies the NotNil predicate on the "parent_deal_id" field.
func ParentDealIDNotNil() predicate.Deal {
	return predicate.Deal(sql.FieldNotNull(FieldParentDealID))
}

// TotalAmountEQ applies the EQ predicate on the "total_amount" field.
func TotalAmountEQ(v float64) predicate.Deal {
	return predicate.Deal(sql.FieldEQ(FieldTotalAmount, v))
}

// TotalAmountNEQ applies the NEQ predicate on the "total_amount" field.
func TotalAmountNEQ(v float64) predicate.Deal {
	return predicate.Deal(sql.FieldNEQ(FieldTotalAmount, v))
}

// TotalAmountIn applies the In predicate on the "total_amount" field.
func TotalAmountIn(vs ...float64) predicate.Deal {
	return predicate.Deal(sql.FieldIn(FieldTotalAmount, vs...))
}

// TotalAmountNotIn applies the NotIn predicate on the "total_amount" field.
func TotalAmountNotIn(vs ...float64) predicate.Deal {
	return predicate.Deal(sql.FieldNotIn(FieldTotalAmount, vs...))
}

// TotalAmountGT applies the GT predicate on the "total_amount" field.
func TotalAmountGT(v float64) predicate.Deal {
	return predicate.Deal(sql.FieldGT(FieldTotalAmount, v))
}

// TotalAmountGTE applies the GTE predicate on the "total_amount" field.
func TotalAmountGTE(v float64) predicate.Deal {
	return predicate.Deal(sql.FieldGTE(FieldTotalAmount, v))
}

// TotalAmountLT applies the LT predicate on the "total_amount" field.
func TotalAmountLT(v float64) predicate.Deal {
	return predicate.Deal(sql.FieldLT(FieldTotalAmount, v))
}

// TotalAmountLTE applies the LTE predicate on the "total_amount" field.
func TotalAmountLTE(v float64) predicate.Deal {
	return predicate.Deal(sql.FieldLTE(FieldTotalAmount, v))
}

// StageIDEQ applies the EQ predicate on the "stage_id" field.
func StageIDEQ(v int) predicate.Deal {
	return predicate.Deal(sql.FieldEQ(FieldStageID, v))
}

// StageIDNEQ applies the NEQ predicate on the "stage_id" field.
func StageIDNEQ(v int) predicate.Deal {
	return predicate.Deal(sql.FieldNEQ(FieldStageID, v))
}

// StageIDIn applies the In predicate on the "stage_id" field.
func StageIDIn(vs ...int) predicate.Deal {
	return predicate.Deal(sql.FieldIn(FieldStageID, vs...))
}

// StageIDNotIn applies the NotIn predicate on the "stage_id" field.
func StageIDNotIn(vs ...int) predicate.Deal {
	return predicate.Deal(sql.FieldNotIn(FieldStageID, vs...))
}

// StageIDIsNil applies the IsNil predicate on the "stage_id" field.
func StageIDIsNil() predicate.Deal {
	return predicate.Deal(sql.FieldIsNull(FieldStageID))
}

// StageIDNotNil applies the NotNil predicate on the "stage_id" field.
func StageIDNotNil() predicate.Deal {
	return predicate.Deal(sql.FieldNotNull(FieldStageID))
}

// StageHistoryIsNil applies the IsNil predicate on the "stage_history" field.
func StageHistoryIsNil() predicate.Deal {
	return predicate.Deal(sql.FieldIsNull(FieldStageHistory))
}

// StageHistoryNotNil applies the NotNil predicate on the "stage_history" field.
func StageHistoryNotNil() predicate.Deal {
	return predicate.Deal(sql.FieldNotNull(FieldStageHistory))
}

// NameEQ applies the EQ predicate on the "name" field.
func NameEQ(v string) predicate.Deal {
	return predicate.Deal(sql.FieldEQ(FieldName, v))
}

// NameNEQ applies the NEQ predicate on the "name" field.
func NameNEQ(v string) predicate.Deal {
	return predicate.Deal(sql.FieldNEQ(FieldName, v))
}

// NameIn applies the In predicate on the "name" field.
func NameIn(vs ...string) predicate.Deal {
	return predicate.Deal(sql.FieldIn(FieldName, vs...))
}

// NameNotIn applies the NotIn predicate on the "name" field.
func NameNotIn(vs ...string) predicate.Deal {
	return predicate.Deal(sql.FieldNotIn(FieldName, vs...))
}

// NameGT applies the GT predicate on the "name" field.
func NameGT(v string) predicate.Deal {
	return predicate.Deal(sql.FieldGT(FieldName, v))
}

// NameGTE applies the GTE predicate on the "name" field.
func NameGTE(v string) predicate.Deal {
	return predicate.Deal(sql.FieldGTE(FieldName, v))
}

// NameLT applies the LT predicate on the "name" field.
func NameLT(v string) predicate.Deal {
	return predicate.Deal(sql.FieldLT(FieldName, v))
}

// NameLTE applies the LTE predicate on the "name" field.
func NameLTE(v string) predicate.Deal {
	return predicate.Deal(sql.FieldLTE(FieldName, v))
}

// NameContains applies the Contains predicate on the "name" field.
func NameContains(v string) predicate.Deal {
	return predicate.Deal(sql.FieldContains(FieldName, v))
}

// NameHasPrefix applies the HasPrefix predicate on the "name" field.
func NameHasPrefix(v string) predicate.Deal {
	return predicate.Deal(sql.FieldHasPrefix(FieldName, v))
}

// NameHasSuffix applies the HasSuffix predicate on the "name" field.
func NameHasSuffix(v string) predicate.Deal {
	return predicate.Deal(sql.FieldHasSuffix(FieldName, v))
}

// NameIsNil applies the IsNil predicate on the "name" field.
func NameIsNil() predicate.Deal {
	return predicate.Deal(sql.FieldIsNull(FieldName))
}

// NameNotNil applies the NotNil predicate on the "name" field.
func NameNotNil() predicate.Deal {
	return predicate.Deal(sql.FieldNotNull(FieldName))
}

// NameEqualFold applies the EqualFold predicate on the "name" field.
func NameEqualFold(v string) predicate.Deal {
	return predicate.Deal(sql.FieldEqualFold(FieldName, v))
}

// NameContainsFold applies the ContainsFold predicate on the "name" field.
func NameContainsFold(v string) predicate.Deal {
	return predicate.Deal(sql.FieldContainsFold(FieldName, v))
}

// StateEQ applies the EQ predicate on the "state" field.
func StateEQ(v State) predicate.Deal {
	return predicate.Deal(sql.FieldEQ(FieldState, v))
}

// StateNEQ applies the NEQ predicate on the "state" field.
func StateNEQ(v State) predicate.Deal {
	return predicate.Deal(sql.FieldNEQ(FieldState, v))
}

// StateIn applies the In predicate on the "state" field.
func StateIn(vs ...State) predicate.Deal {
	return predicate.Deal(sql.FieldIn(FieldState, vs...))
}

// StateNotIn applies the NotIn predicate on the "state" field.
func StateNotIn(vs ...State) predicate.Deal {
	return predicate.Deal(sql.FieldNotIn(FieldState, vs...))
}

// HasPerson applies the HasEdge predicate on the "person" edge.
func HasPerson() predicate.Deal {
	return predicate.Deal(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, PersonTable, PersonColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasPersonWith applies the HasEdge predicate on the "person" edge with a given conditions (other predicates).
func HasPersonWith(preds ...predicate.Person) predicate.Deal {
	return predicate.Deal(func(s *sql.Selector) {
		step := newPersonStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasStage applies the HasEdge predicate on the "stage" edge.
func HasStage() predicate.Deal {
	return predicate.Deal(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, StageTable, StageColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasStageWith applies the HasEdge predicate on the "stage" edge with a given conditions (other predicates).
func HasStageWith(preds ...predicate.Stage) predicate.Deal {
	return predicate.Deal(func(s *sql.Selector) {
		step := newStageStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasAttachments applies the HasEdge predicate on the "attachments" edge.
func HasAttachments() predicate.Deal {
	return predicate.Deal(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, AttachmentsTable, AttachmentsColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasAttachmentsWith applies the HasEdge predicate on the "attachments" edge with a given conditions (other predicates).
func HasAttachmentsWith(preds ...predicate.Attachment) predicate.Deal {
	return predicate.Deal(func(s *sql.Selector) {
		step := newAttachmentsStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasPlans applies the HasEdge predicate on the "plans" edge.
func HasPlans() predicate.Deal {
	return predicate.Deal(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, PlansTable, PlansColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasPlansWith applies the HasEdge predicate on the "plans" edge with a given conditions (other predicates).
func HasPlansWith(preds ...predicate.InstallmentPlan) predicate.Deal {
	return predicate.Deal(func(s *sql.Selector) {
		step := newPlansStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasBills applies the HasEdge predicate on the "bills" edge.
func HasBills() predicate.Deal {
	return predicate.Deal(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, BillsTable, BillsColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasBillsWith applies the HasEdge predicate on the "bills" edge with a given conditions (other predicates).
func HasBillsWith(preds ...predicate.Bill) predicate.Deal {
	return predicate.Deal(func(s *sql.Selector) {
		step := newBillsStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasTracks applies the HasEdge predicate on the "tracks" edge.
func HasTracks() predicate.Deal {
	return predicate.Deal(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, TracksTable, TracksColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasTracksWith applies the HasEdge predicate on the "tracks" edge with a given conditions (other predicates).
func HasTracksWith(preds ...predicate.Track) predicate.Deal {
	return predicate.Deal(func(s *sql.Selector) {
		step := newTracksStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasTasks applies the HasEdge predicate on the "tasks" edge.
func HasTasks() predicate.Deal {
	return predicate.Deal(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, TasksTable, TasksColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasTasksWith applies the HasEdge predicate on the "tasks" edge with a given conditions (other predicates).
func HasTasksWith(preds ...predicate.Task) predicate.Deal {
	return predicate.Deal(func(s *sql.Selector) {
		step := newTasksStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasAssignees applies the HasEdge predicate on the "assignees" edge.
func HasAssignees() predicate.Deal {
	return predicate.Deal(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2M, false, AssigneesTable, AssigneesPrimaryKey...),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasAssigneesWith applies the HasEdge predicate on the "assignees" edge with a given conditions (other predicates).
func HasAssigneesWith(preds ...predicate.User) predicate.Deal {
	return predicate.Deal(func(s *sql.Selector) {
		step := newAssigneesStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasDeposits applies the HasEdge predicate on the "deposits" edge.
func HasDeposits() predicate.Deal {
	return predicate.Deal(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, DepositsTable, DepositsColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasDepositsWith applies the HasEdge predicate on the "deposits" edge with a given conditions (other predicates).
func HasDepositsWith(preds ...predicate.Deposit) predicate.Deal {
	return predicate.Deal(func(s *sql.Selector) {
		step := newDepositsStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasDiscountUsages applies the HasEdge predicate on the "discount_usages" edge.
func HasDiscountUsages() predicate.Deal {
	return predicate.Deal(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, DiscountUsagesTable, DiscountUsagesColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasDiscountUsagesWith applies the HasEdge predicate on the "discount_usages" edge with a given conditions (other predicates).
func HasDiscountUsagesWith(preds ...predicate.DiscountUsage) predicate.Deal {
	return predicate.Deal(func(s *sql.Selector) {
		step := newDiscountUsagesStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasDealAssignment applies the HasEdge predicate on the "deal_assignment" edge.
func HasDealAssignment() predicate.Deal {
	return predicate.Deal(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, DealAssignmentTable, DealAssignmentColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasDealAssignmentWith applies the HasEdge predicate on the "deal_assignment" edge with a given conditions (other predicates).
func HasDealAssignmentWith(preds ...predicate.DealUser) predicate.Deal {
	return predicate.Deal(func(s *sql.Selector) {
		step := newDealAssignmentStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.Deal) predicate.Deal {
	return predicate.Deal(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.Deal) predicate.Deal {
	return predicate.Deal(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.Deal) predicate.Deal {
	return predicate.Deal(sql.NotPredicates(p))
}
