// Code generated by ent, DO NOT EDIT.

package ent

import (
	"bcare/ent/operation"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

// Operation is the model entity for the Operation schema.
type Operation struct {
	config `json:"-"`
	// ID of the ent.
	ID int `json:"id,omitempty"`
	// DeletedAt holds the value of the "deleted_at" field.
	DeletedAt time.Time `json:"deleted_at,omitempty"`
	// Status holds the value of the "status" field.
	Status int8 `json:"status,omitempty"`
	// Version holds the value of the "version" field.
	Version int `json:"version,omitempty"`
	// CreatedAt holds the value of the "created_at" field.
	CreatedAt time.Time `json:"created_at,omitempty"`
	// UpdatedAt holds the value of the "updated_at" field.
	UpdatedAt time.Time `json:"updated_at,omitempty"`
	// Operation name
	Name string `json:"name,omitempty"`
	// Operation description
	Description string `json:"description,omitempty"`
	// Operation duration
	Duration int `json:"duration,omitempty"`
	// Group holds the value of the "group" field.
	Group []string `json:"group,omitempty"`
	// Edges holds the relations/edges for other nodes in the graph.
	// The values are being populated by the OperationQuery when eager-loading is set.
	Edges        OperationEdges `json:"-"`
	selectValues sql.SelectValues
}

// OperationEdges holds the relations/edges for other nodes in the graph.
type OperationEdges struct {
	// AssignedProduct holds the value of the assigned_product edge.
	AssignedProduct []*Product `json:"assigned_product,omitempty"`
	// ProductOperation holds the value of the product_operation edge.
	ProductOperation []*ProductOperation `json:"product_operation,omitempty"`
	// loadedTypes holds the information for reporting if a
	// type was loaded (or requested) in eager-loading or not.
	loadedTypes [2]bool
}

// AssignedProductOrErr returns the AssignedProduct value or an error if the edge
// was not loaded in eager-loading.
func (e OperationEdges) AssignedProductOrErr() ([]*Product, error) {
	if e.loadedTypes[0] {
		return e.AssignedProduct, nil
	}
	return nil, &NotLoadedError{edge: "assigned_product"}
}

// ProductOperationOrErr returns the ProductOperation value or an error if the edge
// was not loaded in eager-loading.
func (e OperationEdges) ProductOperationOrErr() ([]*ProductOperation, error) {
	if e.loadedTypes[1] {
		return e.ProductOperation, nil
	}
	return nil, &NotLoadedError{edge: "product_operation"}
}

// scanValues returns the types for scanning values from sql.Rows.
func (*Operation) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case operation.FieldGroup:
			values[i] = new([]byte)
		case operation.FieldID, operation.FieldStatus, operation.FieldVersion, operation.FieldDuration:
			values[i] = new(sql.NullInt64)
		case operation.FieldName, operation.FieldDescription:
			values[i] = new(sql.NullString)
		case operation.FieldDeletedAt, operation.FieldCreatedAt, operation.FieldUpdatedAt:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the Operation fields.
func (o *Operation) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case operation.FieldID:
			value, ok := values[i].(*sql.NullInt64)
			if !ok {
				return fmt.Errorf("unexpected type %T for field id", value)
			}
			o.ID = int(value.Int64)
		case operation.FieldDeletedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field deleted_at", values[i])
			} else if value.Valid {
				o.DeletedAt = value.Time
			}
		case operation.FieldStatus:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field status", values[i])
			} else if value.Valid {
				o.Status = int8(value.Int64)
			}
		case operation.FieldVersion:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field version", values[i])
			} else if value.Valid {
				o.Version = int(value.Int64)
			}
		case operation.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				o.CreatedAt = value.Time
			}
		case operation.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				o.UpdatedAt = value.Time
			}
		case operation.FieldName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field name", values[i])
			} else if value.Valid {
				o.Name = value.String
			}
		case operation.FieldDescription:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field description", values[i])
			} else if value.Valid {
				o.Description = value.String
			}
		case operation.FieldDuration:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field duration", values[i])
			} else if value.Valid {
				o.Duration = int(value.Int64)
			}
		case operation.FieldGroup:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field group", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &o.Group); err != nil {
					return fmt.Errorf("unmarshal field group: %w", err)
				}
			}
		default:
			o.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the Operation.
// This includes values selected through modifiers, order, etc.
func (o *Operation) Value(name string) (ent.Value, error) {
	return o.selectValues.Get(name)
}

// QueryAssignedProduct queries the "assigned_product" edge of the Operation entity.
func (o *Operation) QueryAssignedProduct() *ProductQuery {
	return NewOperationClient(o.config).QueryAssignedProduct(o)
}

// QueryProductOperation queries the "product_operation" edge of the Operation entity.
func (o *Operation) QueryProductOperation() *ProductOperationQuery {
	return NewOperationClient(o.config).QueryProductOperation(o)
}

// Update returns a builder for updating this Operation.
// Note that you need to call Operation.Unwrap() before calling this method if this Operation
// was returned from a transaction, and the transaction was committed or rolled back.
func (o *Operation) Update() *OperationUpdateOne {
	return NewOperationClient(o.config).UpdateOne(o)
}

// Unwrap unwraps the Operation entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (o *Operation) Unwrap() *Operation {
	_tx, ok := o.config.driver.(*txDriver)
	if !ok {
		panic("ent: Operation is not a transactional entity")
	}
	o.config.driver = _tx.drv
	return o
}

// String implements the fmt.Stringer.
func (o *Operation) String() string {
	var builder strings.Builder
	builder.WriteString("Operation(")
	builder.WriteString(fmt.Sprintf("id=%v, ", o.ID))
	builder.WriteString("deleted_at=")
	builder.WriteString(o.DeletedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("status=")
	builder.WriteString(fmt.Sprintf("%v", o.Status))
	builder.WriteString(", ")
	builder.WriteString("version=")
	builder.WriteString(fmt.Sprintf("%v", o.Version))
	builder.WriteString(", ")
	builder.WriteString("created_at=")
	builder.WriteString(o.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(o.UpdatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("name=")
	builder.WriteString(o.Name)
	builder.WriteString(", ")
	builder.WriteString("description=")
	builder.WriteString(o.Description)
	builder.WriteString(", ")
	builder.WriteString("duration=")
	builder.WriteString(fmt.Sprintf("%v", o.Duration))
	builder.WriteString(", ")
	builder.WriteString("group=")
	builder.WriteString(fmt.Sprintf("%v", o.Group))
	builder.WriteByte(')')
	return builder.String()
}

// MarshalJSON implements the json.Marshaler interface.
func (o *Operation) MarshalJSON() ([]byte, error) {
	type Alias Operation
	return json.Marshal(&struct {
		*Alias
		OperationEdges
	}{
		Alias:          (*Alias)(o),
		OperationEdges: o.Edges,
	})
}

// MarshalSimpleTime
func (o *Operation) MarshalSimpleTime() ([]byte, error) {
	type Alias Operation
	return json.Marshal(&struct {
		*Alias
		OperationEdges
		DeletedAt string `json:"deleted_at,omitempty"`
		CreatedAt string `json:"created_at,omitempty"`
		UpdatedAt string `json:"updated_at,omitempty"`
	}{
		Alias:          (*Alias)(o),
		OperationEdges: o.Edges,
		DeletedAt:      o.DeletedAt.Format("15:04 02/01/2006"),
		CreatedAt:      o.CreatedAt.Format("15:04 02/01/2006"),
		UpdatedAt:      o.UpdatedAt.Format("15:04 02/01/2006"),
	})
}

// Operations is a parsable slice of Operation.
type Operations []*Operation
