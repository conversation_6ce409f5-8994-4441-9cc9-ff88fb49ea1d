// Code generated by ent, DO NOT EDIT.

package ent

import (
	"bcare/ent/deal"
	"bcare/ent/dealuser"
	"bcare/ent/dealuserrating"
	"bcare/ent/predicate"
	"bcare/ent/user"
	"context"
	"errors"
	"fmt"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// DealUserUpdate is the builder for updating DealUser entities.
type DealUserUpdate struct {
	config
	hooks     []Hook
	mutation  *DealUserMutation
	modifiers []func(*sql.UpdateBuilder)
}

// Where appends a list predicates to the DealUserUpdate builder.
func (duu *DealUserUpdate) Where(ps ...predicate.DealUser) *DealUserUpdate {
	duu.mutation.Where(ps...)
	return duu
}

// SetDealID sets the "deal_id" field.
func (duu *DealUserUpdate) SetDealID(i int) *DealUserUpdate {
	duu.mutation.SetDealID(i)
	return duu
}

// SetNillableDealID sets the "deal_id" field if the given value is not nil.
func (duu *DealUserUpdate) SetNillableDealID(i *int) *DealUserUpdate {
	if i != nil {
		duu.SetDealID(*i)
	}
	return duu
}

// ClearDealID clears the value of the "deal_id" field.
func (duu *DealUserUpdate) ClearDealID() *DealUserUpdate {
	duu.mutation.ClearDealID()
	return duu
}

// SetUserID sets the "user_id" field.
func (duu *DealUserUpdate) SetUserID(i int) *DealUserUpdate {
	duu.mutation.SetUserID(i)
	return duu
}

// SetNillableUserID sets the "user_id" field if the given value is not nil.
func (duu *DealUserUpdate) SetNillableUserID(i *int) *DealUserUpdate {
	if i != nil {
		duu.SetUserID(*i)
	}
	return duu
}

// ClearUserID clears the value of the "user_id" field.
func (duu *DealUserUpdate) ClearUserID() *DealUserUpdate {
	duu.mutation.ClearUserID()
	return duu
}

// SetRole sets the "role" field.
func (duu *DealUserUpdate) SetRole(d dealuser.Role) *DealUserUpdate {
	duu.mutation.SetRole(d)
	return duu
}

// SetNillableRole sets the "role" field if the given value is not nil.
func (duu *DealUserUpdate) SetNillableRole(d *dealuser.Role) *DealUserUpdate {
	if d != nil {
		duu.SetRole(*d)
	}
	return duu
}

// ClearRole clears the value of the "role" field.
func (duu *DealUserUpdate) ClearRole() *DealUserUpdate {
	duu.mutation.ClearRole()
	return duu
}

// SetPoint sets the "point" field.
func (duu *DealUserUpdate) SetPoint(m map[string]interface{}) *DealUserUpdate {
	duu.mutation.SetPoint(m)
	return duu
}

// ClearPoint clears the value of the "point" field.
func (duu *DealUserUpdate) ClearPoint() *DealUserUpdate {
	duu.mutation.ClearPoint()
	return duu
}

// SetDeal sets the "deal" edge to the Deal entity.
func (duu *DealUserUpdate) SetDeal(d *Deal) *DealUserUpdate {
	return duu.SetDealID(d.ID)
}

// SetUser sets the "user" edge to the User entity.
func (duu *DealUserUpdate) SetUser(u *User) *DealUserUpdate {
	return duu.SetUserID(u.ID)
}

// AddRatingIDs adds the "ratings" edge to the DealUserRating entity by IDs.
func (duu *DealUserUpdate) AddRatingIDs(ids ...int) *DealUserUpdate {
	duu.mutation.AddRatingIDs(ids...)
	return duu
}

// AddRatings adds the "ratings" edges to the DealUserRating entity.
func (duu *DealUserUpdate) AddRatings(d ...*DealUserRating) *DealUserUpdate {
	ids := make([]int, len(d))
	for i := range d {
		ids[i] = d[i].ID
	}
	return duu.AddRatingIDs(ids...)
}

// Mutation returns the DealUserMutation object of the builder.
func (duu *DealUserUpdate) Mutation() *DealUserMutation {
	return duu.mutation
}

// ClearDeal clears the "deal" edge to the Deal entity.
func (duu *DealUserUpdate) ClearDeal() *DealUserUpdate {
	duu.mutation.ClearDeal()
	return duu
}

// ClearUser clears the "user" edge to the User entity.
func (duu *DealUserUpdate) ClearUser() *DealUserUpdate {
	duu.mutation.ClearUser()
	return duu
}

// ClearRatings clears all "ratings" edges to the DealUserRating entity.
func (duu *DealUserUpdate) ClearRatings() *DealUserUpdate {
	duu.mutation.ClearRatings()
	return duu
}

// RemoveRatingIDs removes the "ratings" edge to DealUserRating entities by IDs.
func (duu *DealUserUpdate) RemoveRatingIDs(ids ...int) *DealUserUpdate {
	duu.mutation.RemoveRatingIDs(ids...)
	return duu
}

// RemoveRatings removes "ratings" edges to DealUserRating entities.
func (duu *DealUserUpdate) RemoveRatings(d ...*DealUserRating) *DealUserUpdate {
	ids := make([]int, len(d))
	for i := range d {
		ids[i] = d[i].ID
	}
	return duu.RemoveRatingIDs(ids...)
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (duu *DealUserUpdate) Save(ctx context.Context) (int, error) {
	return withHooks(ctx, duu.sqlSave, duu.mutation, duu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (duu *DealUserUpdate) SaveX(ctx context.Context) int {
	affected, err := duu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (duu *DealUserUpdate) Exec(ctx context.Context) error {
	_, err := duu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (duu *DealUserUpdate) ExecX(ctx context.Context) {
	if err := duu.Exec(ctx); err != nil {
		panic(err)
	}
}

// check runs all checks and user-defined validators on the builder.
func (duu *DealUserUpdate) check() error {
	if v, ok := duu.mutation.Role(); ok {
		if err := dealuser.RoleValidator(v); err != nil {
			return &ValidationError{Name: "role", err: fmt.Errorf(`ent: validator failed for field "DealUser.role": %w`, err)}
		}
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (duu *DealUserUpdate) Modify(modifiers ...func(u *sql.UpdateBuilder)) *DealUserUpdate {
	duu.modifiers = append(duu.modifiers, modifiers...)
	return duu
}

func (duu *DealUserUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := duu.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(dealuser.Table, dealuser.Columns, sqlgraph.NewFieldSpec(dealuser.FieldID, field.TypeInt))
	if ps := duu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := duu.mutation.Role(); ok {
		_spec.SetField(dealuser.FieldRole, field.TypeEnum, value)
	}
	if duu.mutation.RoleCleared() {
		_spec.ClearField(dealuser.FieldRole, field.TypeEnum)
	}
	if value, ok := duu.mutation.Point(); ok {
		_spec.SetField(dealuser.FieldPoint, field.TypeJSON, value)
	}
	if duu.mutation.PointCleared() {
		_spec.ClearField(dealuser.FieldPoint, field.TypeJSON)
	}
	if duu.mutation.DealCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   dealuser.DealTable,
			Columns: []string{dealuser.DealColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(deal.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := duu.mutation.DealIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   dealuser.DealTable,
			Columns: []string{dealuser.DealColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(deal.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if duu.mutation.UserCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   dealuser.UserTable,
			Columns: []string{dealuser.UserColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := duu.mutation.UserIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   dealuser.UserTable,
			Columns: []string{dealuser.UserColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if duu.mutation.RatingsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   dealuser.RatingsTable,
			Columns: []string{dealuser.RatingsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(dealuserrating.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := duu.mutation.RemovedRatingsIDs(); len(nodes) > 0 && !duu.mutation.RatingsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   dealuser.RatingsTable,
			Columns: []string{dealuser.RatingsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(dealuserrating.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := duu.mutation.RatingsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   dealuser.RatingsTable,
			Columns: []string{dealuser.RatingsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(dealuserrating.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_spec.AddModifiers(duu.modifiers...)
	if n, err = sqlgraph.UpdateNodes(ctx, duu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{dealuser.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	duu.mutation.done = true
	return n, nil
}

// DealUserUpdateOne is the builder for updating a single DealUser entity.
type DealUserUpdateOne struct {
	config
	fields    []string
	hooks     []Hook
	mutation  *DealUserMutation
	modifiers []func(*sql.UpdateBuilder)
}

// SetDealID sets the "deal_id" field.
func (duuo *DealUserUpdateOne) SetDealID(i int) *DealUserUpdateOne {
	duuo.mutation.SetDealID(i)
	return duuo
}

// SetNillableDealID sets the "deal_id" field if the given value is not nil.
func (duuo *DealUserUpdateOne) SetNillableDealID(i *int) *DealUserUpdateOne {
	if i != nil {
		duuo.SetDealID(*i)
	}
	return duuo
}

// ClearDealID clears the value of the "deal_id" field.
func (duuo *DealUserUpdateOne) ClearDealID() *DealUserUpdateOne {
	duuo.mutation.ClearDealID()
	return duuo
}

// SetUserID sets the "user_id" field.
func (duuo *DealUserUpdateOne) SetUserID(i int) *DealUserUpdateOne {
	duuo.mutation.SetUserID(i)
	return duuo
}

// SetNillableUserID sets the "user_id" field if the given value is not nil.
func (duuo *DealUserUpdateOne) SetNillableUserID(i *int) *DealUserUpdateOne {
	if i != nil {
		duuo.SetUserID(*i)
	}
	return duuo
}

// ClearUserID clears the value of the "user_id" field.
func (duuo *DealUserUpdateOne) ClearUserID() *DealUserUpdateOne {
	duuo.mutation.ClearUserID()
	return duuo
}

// SetRole sets the "role" field.
func (duuo *DealUserUpdateOne) SetRole(d dealuser.Role) *DealUserUpdateOne {
	duuo.mutation.SetRole(d)
	return duuo
}

// SetNillableRole sets the "role" field if the given value is not nil.
func (duuo *DealUserUpdateOne) SetNillableRole(d *dealuser.Role) *DealUserUpdateOne {
	if d != nil {
		duuo.SetRole(*d)
	}
	return duuo
}

// ClearRole clears the value of the "role" field.
func (duuo *DealUserUpdateOne) ClearRole() *DealUserUpdateOne {
	duuo.mutation.ClearRole()
	return duuo
}

// SetPoint sets the "point" field.
func (duuo *DealUserUpdateOne) SetPoint(m map[string]interface{}) *DealUserUpdateOne {
	duuo.mutation.SetPoint(m)
	return duuo
}

// ClearPoint clears the value of the "point" field.
func (duuo *DealUserUpdateOne) ClearPoint() *DealUserUpdateOne {
	duuo.mutation.ClearPoint()
	return duuo
}

// SetDeal sets the "deal" edge to the Deal entity.
func (duuo *DealUserUpdateOne) SetDeal(d *Deal) *DealUserUpdateOne {
	return duuo.SetDealID(d.ID)
}

// SetUser sets the "user" edge to the User entity.
func (duuo *DealUserUpdateOne) SetUser(u *User) *DealUserUpdateOne {
	return duuo.SetUserID(u.ID)
}

// AddRatingIDs adds the "ratings" edge to the DealUserRating entity by IDs.
func (duuo *DealUserUpdateOne) AddRatingIDs(ids ...int) *DealUserUpdateOne {
	duuo.mutation.AddRatingIDs(ids...)
	return duuo
}

// AddRatings adds the "ratings" edges to the DealUserRating entity.
func (duuo *DealUserUpdateOne) AddRatings(d ...*DealUserRating) *DealUserUpdateOne {
	ids := make([]int, len(d))
	for i := range d {
		ids[i] = d[i].ID
	}
	return duuo.AddRatingIDs(ids...)
}

// Mutation returns the DealUserMutation object of the builder.
func (duuo *DealUserUpdateOne) Mutation() *DealUserMutation {
	return duuo.mutation
}

// ClearDeal clears the "deal" edge to the Deal entity.
func (duuo *DealUserUpdateOne) ClearDeal() *DealUserUpdateOne {
	duuo.mutation.ClearDeal()
	return duuo
}

// ClearUser clears the "user" edge to the User entity.
func (duuo *DealUserUpdateOne) ClearUser() *DealUserUpdateOne {
	duuo.mutation.ClearUser()
	return duuo
}

// ClearRatings clears all "ratings" edges to the DealUserRating entity.
func (duuo *DealUserUpdateOne) ClearRatings() *DealUserUpdateOne {
	duuo.mutation.ClearRatings()
	return duuo
}

// RemoveRatingIDs removes the "ratings" edge to DealUserRating entities by IDs.
func (duuo *DealUserUpdateOne) RemoveRatingIDs(ids ...int) *DealUserUpdateOne {
	duuo.mutation.RemoveRatingIDs(ids...)
	return duuo
}

// RemoveRatings removes "ratings" edges to DealUserRating entities.
func (duuo *DealUserUpdateOne) RemoveRatings(d ...*DealUserRating) *DealUserUpdateOne {
	ids := make([]int, len(d))
	for i := range d {
		ids[i] = d[i].ID
	}
	return duuo.RemoveRatingIDs(ids...)
}

// Where appends a list predicates to the DealUserUpdate builder.
func (duuo *DealUserUpdateOne) Where(ps ...predicate.DealUser) *DealUserUpdateOne {
	duuo.mutation.Where(ps...)
	return duuo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (duuo *DealUserUpdateOne) Select(field string, fields ...string) *DealUserUpdateOne {
	duuo.fields = append([]string{field}, fields...)
	return duuo
}

// Save executes the query and returns the updated DealUser entity.
func (duuo *DealUserUpdateOne) Save(ctx context.Context) (*DealUser, error) {
	return withHooks(ctx, duuo.sqlSave, duuo.mutation, duuo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (duuo *DealUserUpdateOne) SaveX(ctx context.Context) *DealUser {
	node, err := duuo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (duuo *DealUserUpdateOne) Exec(ctx context.Context) error {
	_, err := duuo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (duuo *DealUserUpdateOne) ExecX(ctx context.Context) {
	if err := duuo.Exec(ctx); err != nil {
		panic(err)
	}
}

// check runs all checks and user-defined validators on the builder.
func (duuo *DealUserUpdateOne) check() error {
	if v, ok := duuo.mutation.Role(); ok {
		if err := dealuser.RoleValidator(v); err != nil {
			return &ValidationError{Name: "role", err: fmt.Errorf(`ent: validator failed for field "DealUser.role": %w`, err)}
		}
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (duuo *DealUserUpdateOne) Modify(modifiers ...func(u *sql.UpdateBuilder)) *DealUserUpdateOne {
	duuo.modifiers = append(duuo.modifiers, modifiers...)
	return duuo
}

func (duuo *DealUserUpdateOne) sqlSave(ctx context.Context) (_node *DealUser, err error) {
	if err := duuo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(dealuser.Table, dealuser.Columns, sqlgraph.NewFieldSpec(dealuser.FieldID, field.TypeInt))
	id, ok := duuo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "DealUser.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := duuo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, dealuser.FieldID)
		for _, f := range fields {
			if !dealuser.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != dealuser.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := duuo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := duuo.mutation.Role(); ok {
		_spec.SetField(dealuser.FieldRole, field.TypeEnum, value)
	}
	if duuo.mutation.RoleCleared() {
		_spec.ClearField(dealuser.FieldRole, field.TypeEnum)
	}
	if value, ok := duuo.mutation.Point(); ok {
		_spec.SetField(dealuser.FieldPoint, field.TypeJSON, value)
	}
	if duuo.mutation.PointCleared() {
		_spec.ClearField(dealuser.FieldPoint, field.TypeJSON)
	}
	if duuo.mutation.DealCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   dealuser.DealTable,
			Columns: []string{dealuser.DealColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(deal.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := duuo.mutation.DealIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   dealuser.DealTable,
			Columns: []string{dealuser.DealColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(deal.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if duuo.mutation.UserCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   dealuser.UserTable,
			Columns: []string{dealuser.UserColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := duuo.mutation.UserIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   dealuser.UserTable,
			Columns: []string{dealuser.UserColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if duuo.mutation.RatingsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   dealuser.RatingsTable,
			Columns: []string{dealuser.RatingsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(dealuserrating.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := duuo.mutation.RemovedRatingsIDs(); len(nodes) > 0 && !duuo.mutation.RatingsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   dealuser.RatingsTable,
			Columns: []string{dealuser.RatingsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(dealuserrating.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := duuo.mutation.RatingsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   dealuser.RatingsTable,
			Columns: []string{dealuser.RatingsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(dealuserrating.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_spec.AddModifiers(duuo.modifiers...)
	_node = &DealUser{config: duuo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, duuo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{dealuser.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	duuo.mutation.done = true
	return _node, nil
}
