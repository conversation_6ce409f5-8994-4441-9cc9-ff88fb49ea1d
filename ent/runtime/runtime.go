// Code generated by ent, DO NOT EDIT.

package runtime

import (
	"bcare/ent/appointment"
	"bcare/ent/appointmenthistory"
	"bcare/ent/attachment"
	"bcare/ent/attachmentdata"
	"bcare/ent/bill"
	"bcare/ent/billdata"
	"bcare/ent/billitem"
	"bcare/ent/bundle"
	"bcare/ent/call"
	"bcare/ent/casbinrule"
	"bcare/ent/cashflow"
	"bcare/ent/cashflowitem"
	"bcare/ent/cashflownote"
	"bcare/ent/deal"
	"bcare/ent/dealhistory"
	"bcare/ent/dealstagehistory"
	"bcare/ent/dealuserrating"
	"bcare/ent/department"
	"bcare/ent/deposit"
	"bcare/ent/depositallocation"
	"bcare/ent/depositpayment"
	"bcare/ent/discount"
	"bcare/ent/discountusage"
	"bcare/ent/entityhistory"
	"bcare/ent/exportjob"
	"bcare/ent/file"
	"bcare/ent/fileusage"
	"bcare/ent/formsubmission"
	"bcare/ent/installment"
	"bcare/ent/installmentplan"
	"bcare/ent/issue"
	"bcare/ent/issuecomment"
	"bcare/ent/material"
	"bcare/ent/materialusage"
	"bcare/ent/messagehistory"
	"bcare/ent/note"
	"bcare/ent/notification"
	"bcare/ent/operation"
	"bcare/ent/operationmaterial"
	"bcare/ent/organization"
	"bcare/ent/otp"
	"bcare/ent/payment"
	"bcare/ent/paymentallocation"
	"bcare/ent/person"
	"bcare/ent/personassignment"
	"bcare/ent/persondata"
	"bcare/ent/personhistory"
	"bcare/ent/personreferral"
	"bcare/ent/pipeline"
	"bcare/ent/product"
	"bcare/ent/productoperation"
	"bcare/ent/referral"
	"bcare/ent/schedule"
	"bcare/ent/schema"
	"bcare/ent/setting"
	"bcare/ent/stage"
	"bcare/ent/tag"
	"bcare/ent/tagdeal"
	"bcare/ent/tagperson"
	"bcare/ent/task"
	"bcare/ent/taskassignment"
	"bcare/ent/taskdepartment"
	"bcare/ent/taskhistory"
	"bcare/ent/tasknote"
	"bcare/ent/taskrecurring"
	"bcare/ent/term"
	"bcare/ent/track"
	"bcare/ent/types"
	"bcare/ent/user"
	"bcare/ent/userdata"
	"time"
)

// The init function reads all schema descriptors with runtime code
// (default values, validators, hooks and policies) and stitches it
// to their package variables.
func init() {
	appointmentMixin := schema.Appointment{}.Mixin()
	appointmentMixinHooks0 := appointmentMixin[0].Hooks()
	appointmentMixinHooks1 := appointmentMixin[1].Hooks()
	appointmentHooks := schema.Appointment{}.Hooks()
	appointment.Hooks[0] = appointmentMixinHooks0[0]
	appointment.Hooks[1] = appointmentMixinHooks1[0]
	appointment.Hooks[2] = appointmentHooks[0]
	appointmentMixinInters0 := appointmentMixin[0].Interceptors()
	appointment.Interceptors[0] = appointmentMixinInters0[0]
	appointmentMixinFields0 := appointmentMixin[0].Fields()
	_ = appointmentMixinFields0
	appointmentMixinFields1 := appointmentMixin[1].Fields()
	_ = appointmentMixinFields1
	appointmentMixinFields2 := appointmentMixin[2].Fields()
	_ = appointmentMixinFields2
	appointmentFields := schema.Appointment{}.Fields()
	_ = appointmentFields
	// appointmentDescStatus is the schema descriptor for status field.
	appointmentDescStatus := appointmentMixinFields0[1].Descriptor()
	// appointment.DefaultStatus holds the default value on creation for the status field.
	appointment.DefaultStatus = appointmentDescStatus.Default.(int8)
	// appointmentDescVersion is the schema descriptor for version field.
	appointmentDescVersion := appointmentMixinFields1[0].Descriptor()
	// appointment.DefaultVersion holds the default value on creation for the version field.
	appointment.DefaultVersion = appointmentDescVersion.Default.(int)
	// appointmentDescCreatedAt is the schema descriptor for created_at field.
	appointmentDescCreatedAt := appointmentMixinFields2[0].Descriptor()
	// appointment.DefaultCreatedAt holds the default value on creation for the created_at field.
	appointment.DefaultCreatedAt = appointmentDescCreatedAt.Default.(func() time.Time)
	// appointmentDescUpdatedAt is the schema descriptor for updated_at field.
	appointmentDescUpdatedAt := appointmentMixinFields2[1].Descriptor()
	// appointment.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	appointment.DefaultUpdatedAt = appointmentDescUpdatedAt.Default.(func() time.Time)
	// appointment.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	appointment.UpdateDefaultUpdatedAt = appointmentDescUpdatedAt.UpdateDefault.(func() time.Time)
	// appointmentDescReminderStatus is the schema descriptor for reminder_status field.
	appointmentDescReminderStatus := appointmentFields[10].Descriptor()
	// appointment.DefaultReminderStatus holds the default value on creation for the reminder_status field.
	appointment.DefaultReminderStatus = appointmentDescReminderStatus.Default.(int8)
	// appointmentDescHistory is the schema descriptor for history field.
	appointmentDescHistory := appointmentFields[12].Descriptor()
	// appointment.DefaultHistory holds the default value on creation for the history field.
	appointment.DefaultHistory = appointmentDescHistory.Default.([]types.TaskHistoryEntry)
	appointmenthistoryMixin := schema.AppointmentHistory{}.Mixin()
	appointmenthistoryMixinFields0 := appointmenthistoryMixin[0].Fields()
	_ = appointmenthistoryMixinFields0
	appointmenthistoryFields := schema.AppointmentHistory{}.Fields()
	_ = appointmenthistoryFields
	// appointmenthistoryDescCreatedAt is the schema descriptor for created_at field.
	appointmenthistoryDescCreatedAt := appointmenthistoryMixinFields0[0].Descriptor()
	// appointmenthistory.DefaultCreatedAt holds the default value on creation for the created_at field.
	appointmenthistory.DefaultCreatedAt = appointmenthistoryDescCreatedAt.Default.(func() time.Time)
	// appointmenthistoryDescUpdatedAt is the schema descriptor for updated_at field.
	appointmenthistoryDescUpdatedAt := appointmenthistoryMixinFields0[1].Descriptor()
	// appointmenthistory.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	appointmenthistory.DefaultUpdatedAt = appointmenthistoryDescUpdatedAt.Default.(func() time.Time)
	// appointmenthistory.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	appointmenthistory.UpdateDefaultUpdatedAt = appointmenthistoryDescUpdatedAt.UpdateDefault.(func() time.Time)
	// appointmenthistoryDescChangedAt is the schema descriptor for changed_at field.
	appointmenthistoryDescChangedAt := appointmenthistoryFields[6].Descriptor()
	// appointmenthistory.DefaultChangedAt holds the default value on creation for the changed_at field.
	appointmenthistory.DefaultChangedAt = appointmenthistoryDescChangedAt.Default.(func() time.Time)
	attachmentMixin := schema.Attachment{}.Mixin()
	attachmentMixinHooks0 := attachmentMixin[0].Hooks()
	attachmentMixinHooks1 := attachmentMixin[1].Hooks()
	attachment.Hooks[0] = attachmentMixinHooks0[0]
	attachment.Hooks[1] = attachmentMixinHooks1[0]
	attachmentMixinInters0 := attachmentMixin[0].Interceptors()
	attachment.Interceptors[0] = attachmentMixinInters0[0]
	attachmentMixinFields0 := attachmentMixin[0].Fields()
	_ = attachmentMixinFields0
	attachmentMixinFields1 := attachmentMixin[1].Fields()
	_ = attachmentMixinFields1
	attachmentMixinFields2 := attachmentMixin[2].Fields()
	_ = attachmentMixinFields2
	attachmentFields := schema.Attachment{}.Fields()
	_ = attachmentFields
	// attachmentDescStatus is the schema descriptor for status field.
	attachmentDescStatus := attachmentMixinFields0[1].Descriptor()
	// attachment.DefaultStatus holds the default value on creation for the status field.
	attachment.DefaultStatus = attachmentDescStatus.Default.(int8)
	// attachmentDescVersion is the schema descriptor for version field.
	attachmentDescVersion := attachmentMixinFields1[0].Descriptor()
	// attachment.DefaultVersion holds the default value on creation for the version field.
	attachment.DefaultVersion = attachmentDescVersion.Default.(int)
	// attachmentDescCreatedAt is the schema descriptor for created_at field.
	attachmentDescCreatedAt := attachmentMixinFields2[0].Descriptor()
	// attachment.DefaultCreatedAt holds the default value on creation for the created_at field.
	attachment.DefaultCreatedAt = attachmentDescCreatedAt.Default.(func() time.Time)
	// attachmentDescUpdatedAt is the schema descriptor for updated_at field.
	attachmentDescUpdatedAt := attachmentMixinFields2[1].Descriptor()
	// attachment.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	attachment.DefaultUpdatedAt = attachmentDescUpdatedAt.Default.(func() time.Time)
	// attachment.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	attachment.UpdateDefaultUpdatedAt = attachmentDescUpdatedAt.UpdateDefault.(func() time.Time)
	// attachmentDescBrandID is the schema descriptor for brand_id field.
	attachmentDescBrandID := attachmentFields[3].Descriptor()
	// attachment.DefaultBrandID holds the default value on creation for the brand_id field.
	attachment.DefaultBrandID = attachmentDescBrandID.Default.(int)
	// attachmentDescQuantity is the schema descriptor for quantity field.
	attachmentDescQuantity := attachmentFields[4].Descriptor()
	// attachment.DefaultQuantity holds the default value on creation for the quantity field.
	attachment.DefaultQuantity = attachmentDescQuantity.Default.(int)
	// attachmentDescPrice is the schema descriptor for price field.
	attachmentDescPrice := attachmentFields[5].Descriptor()
	// attachment.DefaultPrice holds the default value on creation for the price field.
	attachment.DefaultPrice = attachmentDescPrice.Default.(float64)
	// attachmentDescDiscount is the schema descriptor for discount field.
	attachmentDescDiscount := attachmentFields[6].Descriptor()
	// attachment.DefaultDiscount holds the default value on creation for the discount field.
	attachment.DefaultDiscount = attachmentDescDiscount.Default.(float64)
	attachmentdataMixin := schema.AttachmentData{}.Mixin()
	attachmentdataMixinFields0 := attachmentdataMixin[0].Fields()
	_ = attachmentdataMixinFields0
	attachmentdataFields := schema.AttachmentData{}.Fields()
	_ = attachmentdataFields
	// attachmentdataDescCreatedAt is the schema descriptor for created_at field.
	attachmentdataDescCreatedAt := attachmentdataMixinFields0[0].Descriptor()
	// attachmentdata.DefaultCreatedAt holds the default value on creation for the created_at field.
	attachmentdata.DefaultCreatedAt = attachmentdataDescCreatedAt.Default.(func() time.Time)
	// attachmentdataDescUpdatedAt is the schema descriptor for updated_at field.
	attachmentdataDescUpdatedAt := attachmentdataMixinFields0[1].Descriptor()
	// attachmentdata.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	attachmentdata.DefaultUpdatedAt = attachmentdataDescUpdatedAt.Default.(func() time.Time)
	// attachmentdata.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	attachmentdata.UpdateDefaultUpdatedAt = attachmentdataDescUpdatedAt.UpdateDefault.(func() time.Time)
	billMixin := schema.Bill{}.Mixin()
	billMixinHooks0 := billMixin[0].Hooks()
	billMixinHooks1 := billMixin[1].Hooks()
	bill.Hooks[0] = billMixinHooks0[0]
	bill.Hooks[1] = billMixinHooks1[0]
	billMixinInters0 := billMixin[0].Interceptors()
	bill.Interceptors[0] = billMixinInters0[0]
	billMixinFields0 := billMixin[0].Fields()
	_ = billMixinFields0
	billMixinFields1 := billMixin[1].Fields()
	_ = billMixinFields1
	billMixinFields2 := billMixin[2].Fields()
	_ = billMixinFields2
	billFields := schema.Bill{}.Fields()
	_ = billFields
	// billDescStatus is the schema descriptor for status field.
	billDescStatus := billMixinFields0[1].Descriptor()
	// bill.DefaultStatus holds the default value on creation for the status field.
	bill.DefaultStatus = billDescStatus.Default.(int8)
	// billDescVersion is the schema descriptor for version field.
	billDescVersion := billMixinFields1[0].Descriptor()
	// bill.DefaultVersion holds the default value on creation for the version field.
	bill.DefaultVersion = billDescVersion.Default.(int)
	// billDescCreatedAt is the schema descriptor for created_at field.
	billDescCreatedAt := billMixinFields2[0].Descriptor()
	// bill.DefaultCreatedAt holds the default value on creation for the created_at field.
	bill.DefaultCreatedAt = billDescCreatedAt.Default.(func() time.Time)
	// billDescUpdatedAt is the schema descriptor for updated_at field.
	billDescUpdatedAt := billMixinFields2[1].Descriptor()
	// bill.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	bill.DefaultUpdatedAt = billDescUpdatedAt.Default.(func() time.Time)
	// bill.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	bill.UpdateDefaultUpdatedAt = billDescUpdatedAt.UpdateDefault.(func() time.Time)
	// billDescBrandID is the schema descriptor for brand_id field.
	billDescBrandID := billFields[3].Descriptor()
	// bill.DefaultBrandID holds the default value on creation for the brand_id field.
	bill.DefaultBrandID = billDescBrandID.Default.(int)
	// billDescDebtRemaining is the schema descriptor for debt_remaining field.
	billDescDebtRemaining := billFields[4].Descriptor()
	// bill.DefaultDebtRemaining holds the default value on creation for the debt_remaining field.
	bill.DefaultDebtRemaining = billDescDebtRemaining.Default.(float64)
	// billDescDebtPayment is the schema descriptor for debt_payment field.
	billDescDebtPayment := billFields[5].Descriptor()
	// bill.DefaultDebtPayment holds the default value on creation for the debt_payment field.
	bill.DefaultDebtPayment = billDescDebtPayment.Default.(float64)
	// billDescDiscount is the schema descriptor for discount field.
	billDescDiscount := billFields[6].Descriptor()
	// bill.DefaultDiscount holds the default value on creation for the discount field.
	bill.DefaultDiscount = billDescDiscount.Default.(float64)
	// billDescRefund is the schema descriptor for refund field.
	billDescRefund := billFields[7].Descriptor()
	// bill.DefaultRefund holds the default value on creation for the refund field.
	bill.DefaultRefund = billDescRefund.Default.(float64)
	billdataMixin := schema.BillData{}.Mixin()
	billdataMixinFields0 := billdataMixin[0].Fields()
	_ = billdataMixinFields0
	billdataFields := schema.BillData{}.Fields()
	_ = billdataFields
	// billdataDescCreatedAt is the schema descriptor for created_at field.
	billdataDescCreatedAt := billdataMixinFields0[0].Descriptor()
	// billdata.DefaultCreatedAt holds the default value on creation for the created_at field.
	billdata.DefaultCreatedAt = billdataDescCreatedAt.Default.(func() time.Time)
	// billdataDescUpdatedAt is the schema descriptor for updated_at field.
	billdataDescUpdatedAt := billdataMixinFields0[1].Descriptor()
	// billdata.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	billdata.DefaultUpdatedAt = billdataDescUpdatedAt.Default.(func() time.Time)
	// billdata.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	billdata.UpdateDefaultUpdatedAt = billdataDescUpdatedAt.UpdateDefault.(func() time.Time)
	billitemMixin := schema.BillItem{}.Mixin()
	billitemMixinHooks0 := billitemMixin[0].Hooks()
	billitemMixinHooks1 := billitemMixin[1].Hooks()
	billitem.Hooks[0] = billitemMixinHooks0[0]
	billitem.Hooks[1] = billitemMixinHooks1[0]
	billitemMixinInters0 := billitemMixin[0].Interceptors()
	billitem.Interceptors[0] = billitemMixinInters0[0]
	billitemMixinFields0 := billitemMixin[0].Fields()
	_ = billitemMixinFields0
	billitemMixinFields1 := billitemMixin[1].Fields()
	_ = billitemMixinFields1
	billitemMixinFields2 := billitemMixin[2].Fields()
	_ = billitemMixinFields2
	billitemFields := schema.BillItem{}.Fields()
	_ = billitemFields
	// billitemDescStatus is the schema descriptor for status field.
	billitemDescStatus := billitemMixinFields0[1].Descriptor()
	// billitem.DefaultStatus holds the default value on creation for the status field.
	billitem.DefaultStatus = billitemDescStatus.Default.(int8)
	// billitemDescVersion is the schema descriptor for version field.
	billitemDescVersion := billitemMixinFields1[0].Descriptor()
	// billitem.DefaultVersion holds the default value on creation for the version field.
	billitem.DefaultVersion = billitemDescVersion.Default.(int)
	// billitemDescCreatedAt is the schema descriptor for created_at field.
	billitemDescCreatedAt := billitemMixinFields2[0].Descriptor()
	// billitem.DefaultCreatedAt holds the default value on creation for the created_at field.
	billitem.DefaultCreatedAt = billitemDescCreatedAt.Default.(func() time.Time)
	// billitemDescUpdatedAt is the schema descriptor for updated_at field.
	billitemDescUpdatedAt := billitemMixinFields2[1].Descriptor()
	// billitem.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	billitem.DefaultUpdatedAt = billitemDescUpdatedAt.Default.(func() time.Time)
	// billitem.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	billitem.UpdateDefaultUpdatedAt = billitemDescUpdatedAt.UpdateDefault.(func() time.Time)
	// billitemDescAmount is the schema descriptor for amount field.
	billitemDescAmount := billitemFields[4].Descriptor()
	// billitem.DefaultAmount holds the default value on creation for the amount field.
	billitem.DefaultAmount = billitemDescAmount.Default.(float64)
	bundleMixin := schema.Bundle{}.Mixin()
	bundleMixinHooks0 := bundleMixin[0].Hooks()
	bundleMixinHooks1 := bundleMixin[1].Hooks()
	bundle.Hooks[0] = bundleMixinHooks0[0]
	bundle.Hooks[1] = bundleMixinHooks1[0]
	bundleMixinInters0 := bundleMixin[0].Interceptors()
	bundle.Interceptors[0] = bundleMixinInters0[0]
	bundleMixinFields0 := bundleMixin[0].Fields()
	_ = bundleMixinFields0
	bundleMixinFields1 := bundleMixin[1].Fields()
	_ = bundleMixinFields1
	bundleMixinFields2 := bundleMixin[2].Fields()
	_ = bundleMixinFields2
	bundleFields := schema.Bundle{}.Fields()
	_ = bundleFields
	// bundleDescStatus is the schema descriptor for status field.
	bundleDescStatus := bundleMixinFields0[1].Descriptor()
	// bundle.DefaultStatus holds the default value on creation for the status field.
	bundle.DefaultStatus = bundleDescStatus.Default.(int8)
	// bundleDescVersion is the schema descriptor for version field.
	bundleDescVersion := bundleMixinFields1[0].Descriptor()
	// bundle.DefaultVersion holds the default value on creation for the version field.
	bundle.DefaultVersion = bundleDescVersion.Default.(int)
	// bundleDescCreatedAt is the schema descriptor for created_at field.
	bundleDescCreatedAt := bundleMixinFields2[0].Descriptor()
	// bundle.DefaultCreatedAt holds the default value on creation for the created_at field.
	bundle.DefaultCreatedAt = bundleDescCreatedAt.Default.(func() time.Time)
	// bundleDescUpdatedAt is the schema descriptor for updated_at field.
	bundleDescUpdatedAt := bundleMixinFields2[1].Descriptor()
	// bundle.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	bundle.DefaultUpdatedAt = bundleDescUpdatedAt.Default.(func() time.Time)
	// bundle.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	bundle.UpdateDefaultUpdatedAt = bundleDescUpdatedAt.UpdateDefault.(func() time.Time)
	callMixin := schema.Call{}.Mixin()
	callMixinHooks0 := callMixin[0].Hooks()
	callMixinHooks1 := callMixin[1].Hooks()
	call.Hooks[0] = callMixinHooks0[0]
	call.Hooks[1] = callMixinHooks1[0]
	callMixinInters0 := callMixin[0].Interceptors()
	call.Interceptors[0] = callMixinInters0[0]
	callMixinFields0 := callMixin[0].Fields()
	_ = callMixinFields0
	callMixinFields1 := callMixin[1].Fields()
	_ = callMixinFields1
	callMixinFields2 := callMixin[2].Fields()
	_ = callMixinFields2
	callFields := schema.Call{}.Fields()
	_ = callFields
	// callDescStatus is the schema descriptor for status field.
	callDescStatus := callMixinFields0[1].Descriptor()
	// call.DefaultStatus holds the default value on creation for the status field.
	call.DefaultStatus = callDescStatus.Default.(int8)
	// callDescVersion is the schema descriptor for version field.
	callDescVersion := callMixinFields1[0].Descriptor()
	// call.DefaultVersion holds the default value on creation for the version field.
	call.DefaultVersion = callDescVersion.Default.(int)
	// callDescCreatedAt is the schema descriptor for created_at field.
	callDescCreatedAt := callMixinFields2[0].Descriptor()
	// call.DefaultCreatedAt holds the default value on creation for the created_at field.
	call.DefaultCreatedAt = callDescCreatedAt.Default.(func() time.Time)
	// callDescUpdatedAt is the schema descriptor for updated_at field.
	callDescUpdatedAt := callMixinFields2[1].Descriptor()
	// call.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	call.DefaultUpdatedAt = callDescUpdatedAt.Default.(func() time.Time)
	// call.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	call.UpdateDefaultUpdatedAt = callDescUpdatedAt.UpdateDefault.(func() time.Time)
	// callDescDuration is the schema descriptor for duration field.
	callDescDuration := callFields[2].Descriptor()
	// call.DefaultDuration holds the default value on creation for the duration field.
	call.DefaultDuration = callDescDuration.Default.(int)
	// callDescWattingTime is the schema descriptor for watting_time field.
	callDescWattingTime := callFields[3].Descriptor()
	// call.DefaultWattingTime holds the default value on creation for the watting_time field.
	call.DefaultWattingTime = callDescWattingTime.Default.(int)
	// callDescPdd is the schema descriptor for pdd field.
	callDescPdd := callFields[11].Descriptor()
	// call.DefaultPdd holds the default value on creation for the pdd field.
	call.DefaultPdd = callDescPdd.Default.(int)
	// callDescTta is the schema descriptor for tta field.
	callDescTta := callFields[12].Descriptor()
	// call.DefaultTta holds the default value on creation for the tta field.
	call.DefaultTta = callDescTta.Default.(int)
	// callDescRating is the schema descriptor for rating field.
	callDescRating := callFields[13].Descriptor()
	// call.DefaultRating holds the default value on creation for the rating field.
	call.DefaultRating = callDescRating.Default.(int)
	casbinruleFields := schema.CasbinRule{}.Fields()
	_ = casbinruleFields
	// casbinruleDescPtype is the schema descriptor for Ptype field.
	casbinruleDescPtype := casbinruleFields[0].Descriptor()
	// casbinrule.DefaultPtype holds the default value on creation for the Ptype field.
	casbinrule.DefaultPtype = casbinruleDescPtype.Default.(string)
	// casbinruleDescV0 is the schema descriptor for V0 field.
	casbinruleDescV0 := casbinruleFields[1].Descriptor()
	// casbinrule.DefaultV0 holds the default value on creation for the V0 field.
	casbinrule.DefaultV0 = casbinruleDescV0.Default.(string)
	// casbinruleDescV1 is the schema descriptor for V1 field.
	casbinruleDescV1 := casbinruleFields[2].Descriptor()
	// casbinrule.DefaultV1 holds the default value on creation for the V1 field.
	casbinrule.DefaultV1 = casbinruleDescV1.Default.(string)
	// casbinruleDescV2 is the schema descriptor for V2 field.
	casbinruleDescV2 := casbinruleFields[3].Descriptor()
	// casbinrule.DefaultV2 holds the default value on creation for the V2 field.
	casbinrule.DefaultV2 = casbinruleDescV2.Default.(string)
	// casbinruleDescV3 is the schema descriptor for V3 field.
	casbinruleDescV3 := casbinruleFields[4].Descriptor()
	// casbinrule.DefaultV3 holds the default value on creation for the V3 field.
	casbinrule.DefaultV3 = casbinruleDescV3.Default.(string)
	// casbinruleDescV4 is the schema descriptor for V4 field.
	casbinruleDescV4 := casbinruleFields[5].Descriptor()
	// casbinrule.DefaultV4 holds the default value on creation for the V4 field.
	casbinrule.DefaultV4 = casbinruleDescV4.Default.(string)
	// casbinruleDescV5 is the schema descriptor for V5 field.
	casbinruleDescV5 := casbinruleFields[6].Descriptor()
	// casbinrule.DefaultV5 holds the default value on creation for the V5 field.
	casbinrule.DefaultV5 = casbinruleDescV5.Default.(string)
	cashflowMixin := schema.CashFlow{}.Mixin()
	cashflowMixinHooks0 := cashflowMixin[0].Hooks()
	cashflowMixinHooks1 := cashflowMixin[1].Hooks()
	cashflow.Hooks[0] = cashflowMixinHooks0[0]
	cashflow.Hooks[1] = cashflowMixinHooks1[0]
	cashflowMixinInters0 := cashflowMixin[0].Interceptors()
	cashflow.Interceptors[0] = cashflowMixinInters0[0]
	cashflowMixinFields0 := cashflowMixin[0].Fields()
	_ = cashflowMixinFields0
	cashflowMixinFields1 := cashflowMixin[1].Fields()
	_ = cashflowMixinFields1
	cashflowMixinFields2 := cashflowMixin[2].Fields()
	_ = cashflowMixinFields2
	cashflowFields := schema.CashFlow{}.Fields()
	_ = cashflowFields
	// cashflowDescStatus is the schema descriptor for status field.
	cashflowDescStatus := cashflowMixinFields0[1].Descriptor()
	// cashflow.DefaultStatus holds the default value on creation for the status field.
	cashflow.DefaultStatus = cashflowDescStatus.Default.(int8)
	// cashflowDescVersion is the schema descriptor for version field.
	cashflowDescVersion := cashflowMixinFields1[0].Descriptor()
	// cashflow.DefaultVersion holds the default value on creation for the version field.
	cashflow.DefaultVersion = cashflowDescVersion.Default.(int)
	// cashflowDescCreatedAt is the schema descriptor for created_at field.
	cashflowDescCreatedAt := cashflowMixinFields2[0].Descriptor()
	// cashflow.DefaultCreatedAt holds the default value on creation for the created_at field.
	cashflow.DefaultCreatedAt = cashflowDescCreatedAt.Default.(func() time.Time)
	// cashflowDescUpdatedAt is the schema descriptor for updated_at field.
	cashflowDescUpdatedAt := cashflowMixinFields2[1].Descriptor()
	// cashflow.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	cashflow.DefaultUpdatedAt = cashflowDescUpdatedAt.Default.(func() time.Time)
	// cashflow.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	cashflow.UpdateDefaultUpdatedAt = cashflowDescUpdatedAt.UpdateDefault.(func() time.Time)
	// cashflowDescAmount is the schema descriptor for amount field.
	cashflowDescAmount := cashflowFields[1].Descriptor()
	// cashflow.AmountValidator is a validator for the "amount" field. It is called by the builders before save.
	cashflow.AmountValidator = cashflowDescAmount.Validators[0].(func(int) error)
	// cashflowDescCash is the schema descriptor for cash field.
	cashflowDescCash := cashflowFields[2].Descriptor()
	// cashflow.DefaultCash holds the default value on creation for the cash field.
	cashflow.DefaultCash = cashflowDescCash.Default.(int)
	// cashflowDescCreditCard is the schema descriptor for credit_card field.
	cashflowDescCreditCard := cashflowFields[3].Descriptor()
	// cashflow.DefaultCreditCard holds the default value on creation for the credit_card field.
	cashflow.DefaultCreditCard = cashflowDescCreditCard.Default.(int)
	// cashflowDescMpos is the schema descriptor for mpos field.
	cashflowDescMpos := cashflowFields[4].Descriptor()
	// cashflow.DefaultMpos holds the default value on creation for the mpos field.
	cashflow.DefaultMpos = cashflowDescMpos.Default.(int)
	// cashflowDescBank is the schema descriptor for bank field.
	cashflowDescBank := cashflowFields[5].Descriptor()
	// cashflow.DefaultBank holds the default value on creation for the bank field.
	cashflow.DefaultBank = cashflowDescBank.Default.(int)
	// cashflowDescMomo is the schema descriptor for momo field.
	cashflowDescMomo := cashflowFields[6].Descriptor()
	// cashflow.DefaultMomo holds the default value on creation for the momo field.
	cashflow.DefaultMomo = cashflowDescMomo.Default.(int)
	cashflowitemMixin := schema.CashFlowItem{}.Mixin()
	cashflowitemMixinFields0 := cashflowitemMixin[0].Fields()
	_ = cashflowitemMixinFields0
	cashflowitemFields := schema.CashFlowItem{}.Fields()
	_ = cashflowitemFields
	// cashflowitemDescCreatedAt is the schema descriptor for created_at field.
	cashflowitemDescCreatedAt := cashflowitemMixinFields0[0].Descriptor()
	// cashflowitem.DefaultCreatedAt holds the default value on creation for the created_at field.
	cashflowitem.DefaultCreatedAt = cashflowitemDescCreatedAt.Default.(func() time.Time)
	// cashflowitemDescUpdatedAt is the schema descriptor for updated_at field.
	cashflowitemDescUpdatedAt := cashflowitemMixinFields0[1].Descriptor()
	// cashflowitem.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	cashflowitem.DefaultUpdatedAt = cashflowitemDescUpdatedAt.Default.(func() time.Time)
	// cashflowitem.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	cashflowitem.UpdateDefaultUpdatedAt = cashflowitemDescUpdatedAt.UpdateDefault.(func() time.Time)
	// cashflowitemDescAmount is the schema descriptor for amount field.
	cashflowitemDescAmount := cashflowitemFields[3].Descriptor()
	// cashflowitem.AmountValidator is a validator for the "amount" field. It is called by the builders before save.
	cashflowitem.AmountValidator = cashflowitemDescAmount.Validators[0].(func(int) error)
	// cashflowitemDescOrder is the schema descriptor for order field.
	cashflowitemDescOrder := cashflowitemFields[5].Descriptor()
	// cashflowitem.DefaultOrder holds the default value on creation for the order field.
	cashflowitem.DefaultOrder = cashflowitemDescOrder.Default.(int)
	// cashflowitemDescHasVat is the schema descriptor for has_vat field.
	cashflowitemDescHasVat := cashflowitemFields[6].Descriptor()
	// cashflowitem.DefaultHasVat holds the default value on creation for the has_vat field.
	cashflowitem.DefaultHasVat = cashflowitemDescHasVat.Default.(bool)
	cashflownoteMixin := schema.CashFlowNote{}.Mixin()
	cashflownoteMixinHooks0 := cashflownoteMixin[0].Hooks()
	cashflownoteMixinHooks1 := cashflownoteMixin[1].Hooks()
	cashflownote.Hooks[0] = cashflownoteMixinHooks0[0]
	cashflownote.Hooks[1] = cashflownoteMixinHooks1[0]
	cashflownoteMixinInters0 := cashflownoteMixin[0].Interceptors()
	cashflownote.Interceptors[0] = cashflownoteMixinInters0[0]
	cashflownoteMixinFields0 := cashflownoteMixin[0].Fields()
	_ = cashflownoteMixinFields0
	cashflownoteMixinFields1 := cashflownoteMixin[1].Fields()
	_ = cashflownoteMixinFields1
	cashflownoteMixinFields2 := cashflownoteMixin[2].Fields()
	_ = cashflownoteMixinFields2
	cashflownoteFields := schema.CashFlowNote{}.Fields()
	_ = cashflownoteFields
	// cashflownoteDescStatus is the schema descriptor for status field.
	cashflownoteDescStatus := cashflownoteMixinFields0[1].Descriptor()
	// cashflownote.DefaultStatus holds the default value on creation for the status field.
	cashflownote.DefaultStatus = cashflownoteDescStatus.Default.(int8)
	// cashflownoteDescVersion is the schema descriptor for version field.
	cashflownoteDescVersion := cashflownoteMixinFields1[0].Descriptor()
	// cashflownote.DefaultVersion holds the default value on creation for the version field.
	cashflownote.DefaultVersion = cashflownoteDescVersion.Default.(int)
	// cashflownoteDescCreatedAt is the schema descriptor for created_at field.
	cashflownoteDescCreatedAt := cashflownoteMixinFields2[0].Descriptor()
	// cashflownote.DefaultCreatedAt holds the default value on creation for the created_at field.
	cashflownote.DefaultCreatedAt = cashflownoteDescCreatedAt.Default.(func() time.Time)
	// cashflownoteDescUpdatedAt is the schema descriptor for updated_at field.
	cashflownoteDescUpdatedAt := cashflownoteMixinFields2[1].Descriptor()
	// cashflownote.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	cashflownote.DefaultUpdatedAt = cashflownoteDescUpdatedAt.Default.(func() time.Time)
	// cashflownote.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	cashflownote.UpdateDefaultUpdatedAt = cashflownoteDescUpdatedAt.UpdateDefault.(func() time.Time)
	dealMixin := schema.Deal{}.Mixin()
	dealMixinHooks0 := dealMixin[0].Hooks()
	dealMixinHooks1 := dealMixin[1].Hooks()
	deal.Hooks[0] = dealMixinHooks0[0]
	deal.Hooks[1] = dealMixinHooks1[0]
	dealMixinInters0 := dealMixin[0].Interceptors()
	deal.Interceptors[0] = dealMixinInters0[0]
	dealMixinFields0 := dealMixin[0].Fields()
	_ = dealMixinFields0
	dealMixinFields1 := dealMixin[1].Fields()
	_ = dealMixinFields1
	dealMixinFields2 := dealMixin[2].Fields()
	_ = dealMixinFields2
	dealFields := schema.Deal{}.Fields()
	_ = dealFields
	// dealDescStatus is the schema descriptor for status field.
	dealDescStatus := dealMixinFields0[1].Descriptor()
	// deal.DefaultStatus holds the default value on creation for the status field.
	deal.DefaultStatus = dealDescStatus.Default.(int8)
	// dealDescVersion is the schema descriptor for version field.
	dealDescVersion := dealMixinFields1[0].Descriptor()
	// deal.DefaultVersion holds the default value on creation for the version field.
	deal.DefaultVersion = dealDescVersion.Default.(int)
	// dealDescCreatedAt is the schema descriptor for created_at field.
	dealDescCreatedAt := dealMixinFields2[0].Descriptor()
	// deal.DefaultCreatedAt holds the default value on creation for the created_at field.
	deal.DefaultCreatedAt = dealDescCreatedAt.Default.(func() time.Time)
	// dealDescUpdatedAt is the schema descriptor for updated_at field.
	dealDescUpdatedAt := dealMixinFields2[1].Descriptor()
	// deal.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	deal.DefaultUpdatedAt = dealDescUpdatedAt.Default.(func() time.Time)
	// deal.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	deal.UpdateDefaultUpdatedAt = dealDescUpdatedAt.UpdateDefault.(func() time.Time)
	// dealDescTotalAmount is the schema descriptor for total_amount field.
	dealDescTotalAmount := dealFields[2].Descriptor()
	// deal.DefaultTotalAmount holds the default value on creation for the total_amount field.
	deal.DefaultTotalAmount = dealDescTotalAmount.Default.(float64)
	dealhistoryMixin := schema.DealHistory{}.Mixin()
	dealhistoryMixinFields0 := dealhistoryMixin[0].Fields()
	_ = dealhistoryMixinFields0
	dealhistoryFields := schema.DealHistory{}.Fields()
	_ = dealhistoryFields
	// dealhistoryDescCreatedAt is the schema descriptor for created_at field.
	dealhistoryDescCreatedAt := dealhistoryMixinFields0[0].Descriptor()
	// dealhistory.DefaultCreatedAt holds the default value on creation for the created_at field.
	dealhistory.DefaultCreatedAt = dealhistoryDescCreatedAt.Default.(func() time.Time)
	// dealhistoryDescUpdatedAt is the schema descriptor for updated_at field.
	dealhistoryDescUpdatedAt := dealhistoryMixinFields0[1].Descriptor()
	// dealhistory.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	dealhistory.DefaultUpdatedAt = dealhistoryDescUpdatedAt.Default.(func() time.Time)
	// dealhistory.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	dealhistory.UpdateDefaultUpdatedAt = dealhistoryDescUpdatedAt.UpdateDefault.(func() time.Time)
	// dealhistoryDescChangedAt is the schema descriptor for changed_at field.
	dealhistoryDescChangedAt := dealhistoryFields[4].Descriptor()
	// dealhistory.DefaultChangedAt holds the default value on creation for the changed_at field.
	dealhistory.DefaultChangedAt = dealhistoryDescChangedAt.Default.(func() time.Time)
	dealstagehistoryMixin := schema.DealStageHistory{}.Mixin()
	dealstagehistoryMixinFields0 := dealstagehistoryMixin[0].Fields()
	_ = dealstagehistoryMixinFields0
	dealstagehistoryFields := schema.DealStageHistory{}.Fields()
	_ = dealstagehistoryFields
	// dealstagehistoryDescCreatedAt is the schema descriptor for created_at field.
	dealstagehistoryDescCreatedAt := dealstagehistoryMixinFields0[0].Descriptor()
	// dealstagehistory.DefaultCreatedAt holds the default value on creation for the created_at field.
	dealstagehistory.DefaultCreatedAt = dealstagehistoryDescCreatedAt.Default.(func() time.Time)
	// dealstagehistoryDescUpdatedAt is the schema descriptor for updated_at field.
	dealstagehistoryDescUpdatedAt := dealstagehistoryMixinFields0[1].Descriptor()
	// dealstagehistory.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	dealstagehistory.DefaultUpdatedAt = dealstagehistoryDescUpdatedAt.Default.(func() time.Time)
	// dealstagehistory.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	dealstagehistory.UpdateDefaultUpdatedAt = dealstagehistoryDescUpdatedAt.UpdateDefault.(func() time.Time)
	// dealstagehistoryDescChangedAt is the schema descriptor for changed_at field.
	dealstagehistoryDescChangedAt := dealstagehistoryFields[4].Descriptor()
	// dealstagehistory.DefaultChangedAt holds the default value on creation for the changed_at field.
	dealstagehistory.DefaultChangedAt = dealstagehistoryDescChangedAt.Default.(func() time.Time)
	dealuserratingMixin := schema.DealUserRating{}.Mixin()
	dealuserratingMixinFields0 := dealuserratingMixin[0].Fields()
	_ = dealuserratingMixinFields0
	dealuserratingFields := schema.DealUserRating{}.Fields()
	_ = dealuserratingFields
	// dealuserratingDescCreatedAt is the schema descriptor for created_at field.
	dealuserratingDescCreatedAt := dealuserratingMixinFields0[0].Descriptor()
	// dealuserrating.DefaultCreatedAt holds the default value on creation for the created_at field.
	dealuserrating.DefaultCreatedAt = dealuserratingDescCreatedAt.Default.(func() time.Time)
	// dealuserratingDescUpdatedAt is the schema descriptor for updated_at field.
	dealuserratingDescUpdatedAt := dealuserratingMixinFields0[1].Descriptor()
	// dealuserrating.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	dealuserrating.DefaultUpdatedAt = dealuserratingDescUpdatedAt.Default.(func() time.Time)
	// dealuserrating.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	dealuserrating.UpdateDefaultUpdatedAt = dealuserratingDescUpdatedAt.UpdateDefault.(func() time.Time)
	departmentMixin := schema.Department{}.Mixin()
	departmentMixinFields0 := departmentMixin[0].Fields()
	_ = departmentMixinFields0
	departmentFields := schema.Department{}.Fields()
	_ = departmentFields
	// departmentDescCreatedAt is the schema descriptor for created_at field.
	departmentDescCreatedAt := departmentMixinFields0[0].Descriptor()
	// department.DefaultCreatedAt holds the default value on creation for the created_at field.
	department.DefaultCreatedAt = departmentDescCreatedAt.Default.(func() time.Time)
	// departmentDescUpdatedAt is the schema descriptor for updated_at field.
	departmentDescUpdatedAt := departmentMixinFields0[1].Descriptor()
	// department.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	department.DefaultUpdatedAt = departmentDescUpdatedAt.Default.(func() time.Time)
	// department.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	department.UpdateDefaultUpdatedAt = departmentDescUpdatedAt.UpdateDefault.(func() time.Time)
	depositMixin := schema.Deposit{}.Mixin()
	depositMixinHooks0 := depositMixin[0].Hooks()
	depositMixinHooks1 := depositMixin[1].Hooks()
	deposit.Hooks[0] = depositMixinHooks0[0]
	deposit.Hooks[1] = depositMixinHooks1[0]
	depositMixinInters0 := depositMixin[0].Interceptors()
	deposit.Interceptors[0] = depositMixinInters0[0]
	depositMixinFields0 := depositMixin[0].Fields()
	_ = depositMixinFields0
	depositMixinFields1 := depositMixin[1].Fields()
	_ = depositMixinFields1
	depositMixinFields2 := depositMixin[2].Fields()
	_ = depositMixinFields2
	depositFields := schema.Deposit{}.Fields()
	_ = depositFields
	// depositDescStatus is the schema descriptor for status field.
	depositDescStatus := depositMixinFields0[1].Descriptor()
	// deposit.DefaultStatus holds the default value on creation for the status field.
	deposit.DefaultStatus = depositDescStatus.Default.(int8)
	// depositDescVersion is the schema descriptor for version field.
	depositDescVersion := depositMixinFields1[0].Descriptor()
	// deposit.DefaultVersion holds the default value on creation for the version field.
	deposit.DefaultVersion = depositDescVersion.Default.(int)
	// depositDescCreatedAt is the schema descriptor for created_at field.
	depositDescCreatedAt := depositMixinFields2[0].Descriptor()
	// deposit.DefaultCreatedAt holds the default value on creation for the created_at field.
	deposit.DefaultCreatedAt = depositDescCreatedAt.Default.(func() time.Time)
	// depositDescUpdatedAt is the schema descriptor for updated_at field.
	depositDescUpdatedAt := depositMixinFields2[1].Descriptor()
	// deposit.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	deposit.DefaultUpdatedAt = depositDescUpdatedAt.Default.(func() time.Time)
	// deposit.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	deposit.UpdateDefaultUpdatedAt = depositDescUpdatedAt.UpdateDefault.(func() time.Time)
	// depositDescPaidAmount is the schema descriptor for paid_amount field.
	depositDescPaidAmount := depositFields[2].Descriptor()
	// deposit.DefaultPaidAmount holds the default value on creation for the paid_amount field.
	deposit.DefaultPaidAmount = depositDescPaidAmount.Default.(float64)
	// depositDescDescription is the schema descriptor for description field.
	depositDescDescription := depositFields[5].Descriptor()
	// deposit.DescriptionValidator is a validator for the "description" field. It is called by the builders before save.
	deposit.DescriptionValidator = depositDescDescription.Validators[0].(func(string) error)
	depositallocationMixin := schema.DepositAllocation{}.Mixin()
	depositallocationMixinHooks0 := depositallocationMixin[0].Hooks()
	depositallocationMixinHooks1 := depositallocationMixin[1].Hooks()
	depositallocation.Hooks[0] = depositallocationMixinHooks0[0]
	depositallocation.Hooks[1] = depositallocationMixinHooks1[0]
	depositallocationMixinInters0 := depositallocationMixin[0].Interceptors()
	depositallocation.Interceptors[0] = depositallocationMixinInters0[0]
	depositallocationMixinFields0 := depositallocationMixin[0].Fields()
	_ = depositallocationMixinFields0
	depositallocationMixinFields1 := depositallocationMixin[1].Fields()
	_ = depositallocationMixinFields1
	depositallocationMixinFields2 := depositallocationMixin[2].Fields()
	_ = depositallocationMixinFields2
	depositallocationFields := schema.DepositAllocation{}.Fields()
	_ = depositallocationFields
	// depositallocationDescStatus is the schema descriptor for status field.
	depositallocationDescStatus := depositallocationMixinFields0[1].Descriptor()
	// depositallocation.DefaultStatus holds the default value on creation for the status field.
	depositallocation.DefaultStatus = depositallocationDescStatus.Default.(int8)
	// depositallocationDescVersion is the schema descriptor for version field.
	depositallocationDescVersion := depositallocationMixinFields1[0].Descriptor()
	// depositallocation.DefaultVersion holds the default value on creation for the version field.
	depositallocation.DefaultVersion = depositallocationDescVersion.Default.(int)
	// depositallocationDescCreatedAt is the schema descriptor for created_at field.
	depositallocationDescCreatedAt := depositallocationMixinFields2[0].Descriptor()
	// depositallocation.DefaultCreatedAt holds the default value on creation for the created_at field.
	depositallocation.DefaultCreatedAt = depositallocationDescCreatedAt.Default.(func() time.Time)
	// depositallocationDescUpdatedAt is the schema descriptor for updated_at field.
	depositallocationDescUpdatedAt := depositallocationMixinFields2[1].Descriptor()
	// depositallocation.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	depositallocation.DefaultUpdatedAt = depositallocationDescUpdatedAt.Default.(func() time.Time)
	// depositallocation.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	depositallocation.UpdateDefaultUpdatedAt = depositallocationDescUpdatedAt.UpdateDefault.(func() time.Time)
	depositpaymentMixin := schema.DepositPayment{}.Mixin()
	depositpaymentMixinHooks0 := depositpaymentMixin[0].Hooks()
	depositpaymentMixinHooks1 := depositpaymentMixin[1].Hooks()
	depositpayment.Hooks[0] = depositpaymentMixinHooks0[0]
	depositpayment.Hooks[1] = depositpaymentMixinHooks1[0]
	depositpaymentMixinInters0 := depositpaymentMixin[0].Interceptors()
	depositpayment.Interceptors[0] = depositpaymentMixinInters0[0]
	depositpaymentMixinFields0 := depositpaymentMixin[0].Fields()
	_ = depositpaymentMixinFields0
	depositpaymentMixinFields1 := depositpaymentMixin[1].Fields()
	_ = depositpaymentMixinFields1
	depositpaymentMixinFields2 := depositpaymentMixin[2].Fields()
	_ = depositpaymentMixinFields2
	depositpaymentFields := schema.DepositPayment{}.Fields()
	_ = depositpaymentFields
	// depositpaymentDescStatus is the schema descriptor for status field.
	depositpaymentDescStatus := depositpaymentMixinFields0[1].Descriptor()
	// depositpayment.DefaultStatus holds the default value on creation for the status field.
	depositpayment.DefaultStatus = depositpaymentDescStatus.Default.(int8)
	// depositpaymentDescVersion is the schema descriptor for version field.
	depositpaymentDescVersion := depositpaymentMixinFields1[0].Descriptor()
	// depositpayment.DefaultVersion holds the default value on creation for the version field.
	depositpayment.DefaultVersion = depositpaymentDescVersion.Default.(int)
	// depositpaymentDescCreatedAt is the schema descriptor for created_at field.
	depositpaymentDescCreatedAt := depositpaymentMixinFields2[0].Descriptor()
	// depositpayment.DefaultCreatedAt holds the default value on creation for the created_at field.
	depositpayment.DefaultCreatedAt = depositpaymentDescCreatedAt.Default.(func() time.Time)
	// depositpaymentDescUpdatedAt is the schema descriptor for updated_at field.
	depositpaymentDescUpdatedAt := depositpaymentMixinFields2[1].Descriptor()
	// depositpayment.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	depositpayment.DefaultUpdatedAt = depositpaymentDescUpdatedAt.Default.(func() time.Time)
	// depositpayment.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	depositpayment.UpdateDefaultUpdatedAt = depositpaymentDescUpdatedAt.UpdateDefault.(func() time.Time)
	// depositpaymentDescNote is the schema descriptor for note field.
	depositpaymentDescNote := depositpaymentFields[5].Descriptor()
	// depositpayment.NoteValidator is a validator for the "note" field. It is called by the builders before save.
	depositpayment.NoteValidator = depositpaymentDescNote.Validators[0].(func(string) error)
	discountMixin := schema.Discount{}.Mixin()
	discountMixinHooks0 := discountMixin[0].Hooks()
	discountMixinHooks1 := discountMixin[1].Hooks()
	discount.Hooks[0] = discountMixinHooks0[0]
	discount.Hooks[1] = discountMixinHooks1[0]
	discountMixinInters0 := discountMixin[0].Interceptors()
	discount.Interceptors[0] = discountMixinInters0[0]
	discountMixinFields0 := discountMixin[0].Fields()
	_ = discountMixinFields0
	discountMixinFields1 := discountMixin[1].Fields()
	_ = discountMixinFields1
	discountMixinFields2 := discountMixin[2].Fields()
	_ = discountMixinFields2
	discountFields := schema.Discount{}.Fields()
	_ = discountFields
	// discountDescStatus is the schema descriptor for status field.
	discountDescStatus := discountMixinFields0[1].Descriptor()
	// discount.DefaultStatus holds the default value on creation for the status field.
	discount.DefaultStatus = discountDescStatus.Default.(int8)
	// discountDescVersion is the schema descriptor for version field.
	discountDescVersion := discountMixinFields1[0].Descriptor()
	// discount.DefaultVersion holds the default value on creation for the version field.
	discount.DefaultVersion = discountDescVersion.Default.(int)
	// discountDescCreatedAt is the schema descriptor for created_at field.
	discountDescCreatedAt := discountMixinFields2[0].Descriptor()
	// discount.DefaultCreatedAt holds the default value on creation for the created_at field.
	discount.DefaultCreatedAt = discountDescCreatedAt.Default.(func() time.Time)
	// discountDescUpdatedAt is the schema descriptor for updated_at field.
	discountDescUpdatedAt := discountMixinFields2[1].Descriptor()
	// discount.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	discount.DefaultUpdatedAt = discountDescUpdatedAt.Default.(func() time.Time)
	// discount.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	discount.UpdateDefaultUpdatedAt = discountDescUpdatedAt.UpdateDefault.(func() time.Time)
	discountusageMixin := schema.DiscountUsage{}.Mixin()
	discountusageMixinHooks0 := discountusageMixin[0].Hooks()
	discountusageMixinHooks1 := discountusageMixin[1].Hooks()
	discountusage.Hooks[0] = discountusageMixinHooks0[0]
	discountusage.Hooks[1] = discountusageMixinHooks1[0]
	discountusageMixinInters0 := discountusageMixin[0].Interceptors()
	discountusage.Interceptors[0] = discountusageMixinInters0[0]
	discountusageMixinFields0 := discountusageMixin[0].Fields()
	_ = discountusageMixinFields0
	discountusageMixinFields1 := discountusageMixin[1].Fields()
	_ = discountusageMixinFields1
	discountusageMixinFields2 := discountusageMixin[2].Fields()
	_ = discountusageMixinFields2
	discountusageFields := schema.DiscountUsage{}.Fields()
	_ = discountusageFields
	// discountusageDescStatus is the schema descriptor for status field.
	discountusageDescStatus := discountusageMixinFields0[1].Descriptor()
	// discountusage.DefaultStatus holds the default value on creation for the status field.
	discountusage.DefaultStatus = discountusageDescStatus.Default.(int8)
	// discountusageDescVersion is the schema descriptor for version field.
	discountusageDescVersion := discountusageMixinFields1[0].Descriptor()
	// discountusage.DefaultVersion holds the default value on creation for the version field.
	discountusage.DefaultVersion = discountusageDescVersion.Default.(int)
	// discountusageDescCreatedAt is the schema descriptor for created_at field.
	discountusageDescCreatedAt := discountusageMixinFields2[0].Descriptor()
	// discountusage.DefaultCreatedAt holds the default value on creation for the created_at field.
	discountusage.DefaultCreatedAt = discountusageDescCreatedAt.Default.(func() time.Time)
	// discountusageDescUpdatedAt is the schema descriptor for updated_at field.
	discountusageDescUpdatedAt := discountusageMixinFields2[1].Descriptor()
	// discountusage.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	discountusage.DefaultUpdatedAt = discountusageDescUpdatedAt.Default.(func() time.Time)
	// discountusage.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	discountusage.UpdateDefaultUpdatedAt = discountusageDescUpdatedAt.UpdateDefault.(func() time.Time)
	// discountusageDescUsageCount is the schema descriptor for usage_count field.
	discountusageDescUsageCount := discountusageFields[6].Descriptor()
	// discountusage.DefaultUsageCount holds the default value on creation for the usage_count field.
	discountusage.DefaultUsageCount = discountusageDescUsageCount.Default.(int)
	// discountusageDescValue is the schema descriptor for value field.
	discountusageDescValue := discountusageFields[7].Descriptor()
	// discountusage.DefaultValue holds the default value on creation for the value field.
	discountusage.DefaultValue = discountusageDescValue.Default.(float64)
	entityhistoryMixin := schema.EntityHistory{}.Mixin()
	entityhistoryMixinFields0 := entityhistoryMixin[0].Fields()
	_ = entityhistoryMixinFields0
	entityhistoryFields := schema.EntityHistory{}.Fields()
	_ = entityhistoryFields
	// entityhistoryDescCreatedAt is the schema descriptor for created_at field.
	entityhistoryDescCreatedAt := entityhistoryMixinFields0[0].Descriptor()
	// entityhistory.DefaultCreatedAt holds the default value on creation for the created_at field.
	entityhistory.DefaultCreatedAt = entityhistoryDescCreatedAt.Default.(func() time.Time)
	// entityhistoryDescUpdatedAt is the schema descriptor for updated_at field.
	entityhistoryDescUpdatedAt := entityhistoryMixinFields0[1].Descriptor()
	// entityhistory.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	entityhistory.DefaultUpdatedAt = entityhistoryDescUpdatedAt.Default.(func() time.Time)
	// entityhistory.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	entityhistory.UpdateDefaultUpdatedAt = entityhistoryDescUpdatedAt.UpdateDefault.(func() time.Time)
	// entityhistoryDescEntityType is the schema descriptor for entity_type field.
	entityhistoryDescEntityType := entityhistoryFields[0].Descriptor()
	// entityhistory.EntityTypeValidator is a validator for the "entity_type" field. It is called by the builders before save.
	entityhistory.EntityTypeValidator = entityhistoryDescEntityType.Validators[0].(func(string) error)
	// entityhistoryDescChangedAt is the schema descriptor for changed_at field.
	entityhistoryDescChangedAt := entityhistoryFields[5].Descriptor()
	// entityhistory.DefaultChangedAt holds the default value on creation for the changed_at field.
	entityhistory.DefaultChangedAt = entityhistoryDescChangedAt.Default.(func() time.Time)
	exportjobMixin := schema.ExportJob{}.Mixin()
	exportjobMixinHooks0 := exportjobMixin[0].Hooks()
	exportjobMixinHooks1 := exportjobMixin[1].Hooks()
	exportjob.Hooks[0] = exportjobMixinHooks0[0]
	exportjob.Hooks[1] = exportjobMixinHooks1[0]
	exportjobMixinInters0 := exportjobMixin[0].Interceptors()
	exportjob.Interceptors[0] = exportjobMixinInters0[0]
	exportjobMixinFields0 := exportjobMixin[0].Fields()
	_ = exportjobMixinFields0
	exportjobMixinFields1 := exportjobMixin[1].Fields()
	_ = exportjobMixinFields1
	exportjobMixinFields2 := exportjobMixin[2].Fields()
	_ = exportjobMixinFields2
	exportjobFields := schema.ExportJob{}.Fields()
	_ = exportjobFields
	// exportjobDescStatus is the schema descriptor for status field.
	exportjobDescStatus := exportjobMixinFields0[1].Descriptor()
	// exportjob.DefaultStatus holds the default value on creation for the status field.
	exportjob.DefaultStatus = exportjobDescStatus.Default.(int8)
	// exportjobDescVersion is the schema descriptor for version field.
	exportjobDescVersion := exportjobMixinFields1[0].Descriptor()
	// exportjob.DefaultVersion holds the default value on creation for the version field.
	exportjob.DefaultVersion = exportjobDescVersion.Default.(int)
	// exportjobDescCreatedAt is the schema descriptor for created_at field.
	exportjobDescCreatedAt := exportjobMixinFields2[0].Descriptor()
	// exportjob.DefaultCreatedAt holds the default value on creation for the created_at field.
	exportjob.DefaultCreatedAt = exportjobDescCreatedAt.Default.(func() time.Time)
	// exportjobDescUpdatedAt is the schema descriptor for updated_at field.
	exportjobDescUpdatedAt := exportjobMixinFields2[1].Descriptor()
	// exportjob.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	exportjob.DefaultUpdatedAt = exportjobDescUpdatedAt.Default.(func() time.Time)
	// exportjob.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	exportjob.UpdateDefaultUpdatedAt = exportjobDescUpdatedAt.UpdateDefault.(func() time.Time)
	// exportjobDescProgress is the schema descriptor for progress field.
	exportjobDescProgress := exportjobFields[2].Descriptor()
	// exportjob.DefaultProgress holds the default value on creation for the progress field.
	exportjob.DefaultProgress = exportjobDescProgress.Default.(float64)
	// exportjobDescFilePath is the schema descriptor for file_path field.
	exportjobDescFilePath := exportjobFields[3].Descriptor()
	// exportjob.FilePathValidator is a validator for the "file_path" field. It is called by the builders before save.
	exportjob.FilePathValidator = exportjobDescFilePath.Validators[0].(func(string) error)
	// exportjobDescEstimatedRecords is the schema descriptor for estimated_records field.
	exportjobDescEstimatedRecords := exportjobFields[8].Descriptor()
	// exportjob.EstimatedRecordsValidator is a validator for the "estimated_records" field. It is called by the builders before save.
	exportjob.EstimatedRecordsValidator = exportjobDescEstimatedRecords.Validators[0].(func(int) error)
	// exportjobDescActualRecords is the schema descriptor for actual_records field.
	exportjobDescActualRecords := exportjobFields[9].Descriptor()
	// exportjob.ActualRecordsValidator is a validator for the "actual_records" field. It is called by the builders before save.
	exportjob.ActualRecordsValidator = exportjobDescActualRecords.Validators[0].(func(int) error)
	fileMixin := schema.File{}.Mixin()
	fileMixinHooks0 := fileMixin[0].Hooks()
	fileMixinHooks1 := fileMixin[1].Hooks()
	file.Hooks[0] = fileMixinHooks0[0]
	file.Hooks[1] = fileMixinHooks1[0]
	fileMixinInters0 := fileMixin[0].Interceptors()
	file.Interceptors[0] = fileMixinInters0[0]
	fileMixinFields0 := fileMixin[0].Fields()
	_ = fileMixinFields0
	fileMixinFields1 := fileMixin[1].Fields()
	_ = fileMixinFields1
	fileMixinFields2 := fileMixin[2].Fields()
	_ = fileMixinFields2
	fileFields := schema.File{}.Fields()
	_ = fileFields
	// fileDescStatus is the schema descriptor for status field.
	fileDescStatus := fileMixinFields0[1].Descriptor()
	// file.DefaultStatus holds the default value on creation for the status field.
	file.DefaultStatus = fileDescStatus.Default.(int8)
	// fileDescVersion is the schema descriptor for version field.
	fileDescVersion := fileMixinFields1[0].Descriptor()
	// file.DefaultVersion holds the default value on creation for the version field.
	file.DefaultVersion = fileDescVersion.Default.(int)
	// fileDescCreatedAt is the schema descriptor for created_at field.
	fileDescCreatedAt := fileMixinFields2[0].Descriptor()
	// file.DefaultCreatedAt holds the default value on creation for the created_at field.
	file.DefaultCreatedAt = fileDescCreatedAt.Default.(func() time.Time)
	// fileDescUpdatedAt is the schema descriptor for updated_at field.
	fileDescUpdatedAt := fileMixinFields2[1].Descriptor()
	// file.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	file.DefaultUpdatedAt = fileDescUpdatedAt.Default.(func() time.Time)
	// file.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	file.UpdateDefaultUpdatedAt = fileDescUpdatedAt.UpdateDefault.(func() time.Time)
	// fileDescSize is the schema descriptor for size field.
	fileDescSize := fileFields[3].Descriptor()
	// file.DefaultSize holds the default value on creation for the size field.
	file.DefaultSize = fileDescSize.Default.(int)
	fileusageMixin := schema.FileUsage{}.Mixin()
	fileusageMixinHooks0 := fileusageMixin[0].Hooks()
	fileusageMixinHooks1 := fileusageMixin[1].Hooks()
	fileusage.Hooks[0] = fileusageMixinHooks0[0]
	fileusage.Hooks[1] = fileusageMixinHooks1[0]
	fileusageMixinInters0 := fileusageMixin[0].Interceptors()
	fileusage.Interceptors[0] = fileusageMixinInters0[0]
	fileusageMixinFields0 := fileusageMixin[0].Fields()
	_ = fileusageMixinFields0
	fileusageMixinFields1 := fileusageMixin[1].Fields()
	_ = fileusageMixinFields1
	fileusageMixinFields2 := fileusageMixin[2].Fields()
	_ = fileusageMixinFields2
	fileusageFields := schema.FileUsage{}.Fields()
	_ = fileusageFields
	// fileusageDescStatus is the schema descriptor for status field.
	fileusageDescStatus := fileusageMixinFields0[1].Descriptor()
	// fileusage.DefaultStatus holds the default value on creation for the status field.
	fileusage.DefaultStatus = fileusageDescStatus.Default.(int8)
	// fileusageDescVersion is the schema descriptor for version field.
	fileusageDescVersion := fileusageMixinFields1[0].Descriptor()
	// fileusage.DefaultVersion holds the default value on creation for the version field.
	fileusage.DefaultVersion = fileusageDescVersion.Default.(int)
	// fileusageDescCreatedAt is the schema descriptor for created_at field.
	fileusageDescCreatedAt := fileusageMixinFields2[0].Descriptor()
	// fileusage.DefaultCreatedAt holds the default value on creation for the created_at field.
	fileusage.DefaultCreatedAt = fileusageDescCreatedAt.Default.(func() time.Time)
	// fileusageDescUpdatedAt is the schema descriptor for updated_at field.
	fileusageDescUpdatedAt := fileusageMixinFields2[1].Descriptor()
	// fileusage.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	fileusage.DefaultUpdatedAt = fileusageDescUpdatedAt.Default.(func() time.Time)
	// fileusage.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	fileusage.UpdateDefaultUpdatedAt = fileusageDescUpdatedAt.UpdateDefault.(func() time.Time)
	formsubmissionMixin := schema.FormSubmission{}.Mixin()
	formsubmissionMixinHooks0 := formsubmissionMixin[0].Hooks()
	formsubmissionMixinHooks1 := formsubmissionMixin[1].Hooks()
	formsubmissionHooks := schema.FormSubmission{}.Hooks()
	formsubmission.Hooks[0] = formsubmissionMixinHooks0[0]
	formsubmission.Hooks[1] = formsubmissionMixinHooks1[0]
	formsubmission.Hooks[2] = formsubmissionHooks[0]
	formsubmissionMixinInters0 := formsubmissionMixin[0].Interceptors()
	formsubmission.Interceptors[0] = formsubmissionMixinInters0[0]
	formsubmissionMixinFields0 := formsubmissionMixin[0].Fields()
	_ = formsubmissionMixinFields0
	formsubmissionMixinFields1 := formsubmissionMixin[1].Fields()
	_ = formsubmissionMixinFields1
	formsubmissionMixinFields2 := formsubmissionMixin[2].Fields()
	_ = formsubmissionMixinFields2
	formsubmissionFields := schema.FormSubmission{}.Fields()
	_ = formsubmissionFields
	// formsubmissionDescStatus is the schema descriptor for status field.
	formsubmissionDescStatus := formsubmissionMixinFields0[1].Descriptor()
	// formsubmission.DefaultStatus holds the default value on creation for the status field.
	formsubmission.DefaultStatus = formsubmissionDescStatus.Default.(int8)
	// formsubmissionDescVersion is the schema descriptor for version field.
	formsubmissionDescVersion := formsubmissionMixinFields1[0].Descriptor()
	// formsubmission.DefaultVersion holds the default value on creation for the version field.
	formsubmission.DefaultVersion = formsubmissionDescVersion.Default.(int)
	// formsubmissionDescCreatedAt is the schema descriptor for created_at field.
	formsubmissionDescCreatedAt := formsubmissionMixinFields2[0].Descriptor()
	// formsubmission.DefaultCreatedAt holds the default value on creation for the created_at field.
	formsubmission.DefaultCreatedAt = formsubmissionDescCreatedAt.Default.(func() time.Time)
	// formsubmissionDescUpdatedAt is the schema descriptor for updated_at field.
	formsubmissionDescUpdatedAt := formsubmissionMixinFields2[1].Descriptor()
	// formsubmission.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	formsubmission.DefaultUpdatedAt = formsubmissionDescUpdatedAt.Default.(func() time.Time)
	// formsubmission.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	formsubmission.UpdateDefaultUpdatedAt = formsubmissionDescUpdatedAt.UpdateDefault.(func() time.Time)
	// formsubmissionDescHistory is the schema descriptor for history field.
	formsubmissionDescHistory := formsubmissionFields[10].Descriptor()
	// formsubmission.DefaultHistory holds the default value on creation for the history field.
	formsubmission.DefaultHistory = formsubmissionDescHistory.Default.([]types.RecordHistoryEntry)
	installmentMixin := schema.Installment{}.Mixin()
	installmentMixinHooks0 := installmentMixin[0].Hooks()
	installmentMixinHooks1 := installmentMixin[1].Hooks()
	installment.Hooks[0] = installmentMixinHooks0[0]
	installment.Hooks[1] = installmentMixinHooks1[0]
	installmentMixinInters0 := installmentMixin[0].Interceptors()
	installment.Interceptors[0] = installmentMixinInters0[0]
	installmentMixinFields0 := installmentMixin[0].Fields()
	_ = installmentMixinFields0
	installmentMixinFields1 := installmentMixin[1].Fields()
	_ = installmentMixinFields1
	installmentMixinFields2 := installmentMixin[2].Fields()
	_ = installmentMixinFields2
	installmentFields := schema.Installment{}.Fields()
	_ = installmentFields
	// installmentDescStatus is the schema descriptor for status field.
	installmentDescStatus := installmentMixinFields0[1].Descriptor()
	// installment.DefaultStatus holds the default value on creation for the status field.
	installment.DefaultStatus = installmentDescStatus.Default.(int8)
	// installmentDescVersion is the schema descriptor for version field.
	installmentDescVersion := installmentMixinFields1[0].Descriptor()
	// installment.DefaultVersion holds the default value on creation for the version field.
	installment.DefaultVersion = installmentDescVersion.Default.(int)
	// installmentDescCreatedAt is the schema descriptor for created_at field.
	installmentDescCreatedAt := installmentMixinFields2[0].Descriptor()
	// installment.DefaultCreatedAt holds the default value on creation for the created_at field.
	installment.DefaultCreatedAt = installmentDescCreatedAt.Default.(func() time.Time)
	// installmentDescUpdatedAt is the schema descriptor for updated_at field.
	installmentDescUpdatedAt := installmentMixinFields2[1].Descriptor()
	// installment.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	installment.DefaultUpdatedAt = installmentDescUpdatedAt.Default.(func() time.Time)
	// installment.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	installment.UpdateDefaultUpdatedAt = installmentDescUpdatedAt.UpdateDefault.(func() time.Time)
	// installmentDescInstallmentNumber is the schema descriptor for installment_number field.
	installmentDescInstallmentNumber := installmentFields[1].Descriptor()
	// installment.DefaultInstallmentNumber holds the default value on creation for the installment_number field.
	installment.DefaultInstallmentNumber = installmentDescInstallmentNumber.Default.(int)
	// installmentDescAmount is the schema descriptor for amount field.
	installmentDescAmount := installmentFields[2].Descriptor()
	// installment.DefaultAmount holds the default value on creation for the amount field.
	installment.DefaultAmount = installmentDescAmount.Default.(float64)
	// installmentDescTransactionType is the schema descriptor for transaction_type field.
	installmentDescTransactionType := installmentFields[7].Descriptor()
	// installment.DefaultTransactionType holds the default value on creation for the transaction_type field.
	installment.DefaultTransactionType = installmentDescTransactionType.Default.(int)
	installmentplanMixin := schema.InstallmentPlan{}.Mixin()
	installmentplanMixinHooks0 := installmentplanMixin[0].Hooks()
	installmentplanMixinHooks1 := installmentplanMixin[1].Hooks()
	installmentplan.Hooks[0] = installmentplanMixinHooks0[0]
	installmentplan.Hooks[1] = installmentplanMixinHooks1[0]
	installmentplanMixinInters0 := installmentplanMixin[0].Interceptors()
	installmentplan.Interceptors[0] = installmentplanMixinInters0[0]
	installmentplanMixinFields0 := installmentplanMixin[0].Fields()
	_ = installmentplanMixinFields0
	installmentplanMixinFields1 := installmentplanMixin[1].Fields()
	_ = installmentplanMixinFields1
	installmentplanMixinFields2 := installmentplanMixin[2].Fields()
	_ = installmentplanMixinFields2
	installmentplanFields := schema.InstallmentPlan{}.Fields()
	_ = installmentplanFields
	// installmentplanDescStatus is the schema descriptor for status field.
	installmentplanDescStatus := installmentplanMixinFields0[1].Descriptor()
	// installmentplan.DefaultStatus holds the default value on creation for the status field.
	installmentplan.DefaultStatus = installmentplanDescStatus.Default.(int8)
	// installmentplanDescVersion is the schema descriptor for version field.
	installmentplanDescVersion := installmentplanMixinFields1[0].Descriptor()
	// installmentplan.DefaultVersion holds the default value on creation for the version field.
	installmentplan.DefaultVersion = installmentplanDescVersion.Default.(int)
	// installmentplanDescCreatedAt is the schema descriptor for created_at field.
	installmentplanDescCreatedAt := installmentplanMixinFields2[0].Descriptor()
	// installmentplan.DefaultCreatedAt holds the default value on creation for the created_at field.
	installmentplan.DefaultCreatedAt = installmentplanDescCreatedAt.Default.(func() time.Time)
	// installmentplanDescUpdatedAt is the schema descriptor for updated_at field.
	installmentplanDescUpdatedAt := installmentplanMixinFields2[1].Descriptor()
	// installmentplan.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	installmentplan.DefaultUpdatedAt = installmentplanDescUpdatedAt.Default.(func() time.Time)
	// installmentplan.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	installmentplan.UpdateDefaultUpdatedAt = installmentplanDescUpdatedAt.UpdateDefault.(func() time.Time)
	// installmentplanDescName is the schema descriptor for name field.
	installmentplanDescName := installmentplanFields[4].Descriptor()
	// installmentplan.DefaultName holds the default value on creation for the name field.
	installmentplan.DefaultName = installmentplanDescName.Default.(string)
	// installmentplanDescTotalAmount is the schema descriptor for total_amount field.
	installmentplanDescTotalAmount := installmentplanFields[5].Descriptor()
	// installmentplan.DefaultTotalAmount holds the default value on creation for the total_amount field.
	installmentplan.DefaultTotalAmount = installmentplanDescTotalAmount.Default.(float64)
	// installmentplanDescDownPayment is the schema descriptor for down_payment field.
	installmentplanDescDownPayment := installmentplanFields[6].Descriptor()
	// installmentplan.DefaultDownPayment holds the default value on creation for the down_payment field.
	installmentplan.DefaultDownPayment = installmentplanDescDownPayment.Default.(float64)
	// installmentplanDescTotalInstallments is the schema descriptor for total_installments field.
	installmentplanDescTotalInstallments := installmentplanFields[7].Descriptor()
	// installmentplan.DefaultTotalInstallments holds the default value on creation for the total_installments field.
	installmentplan.DefaultTotalInstallments = installmentplanDescTotalInstallments.Default.(int)
	issueMixin := schema.Issue{}.Mixin()
	issueMixinHooks0 := issueMixin[0].Hooks()
	issueMixinHooks1 := issueMixin[1].Hooks()
	issue.Hooks[0] = issueMixinHooks0[0]
	issue.Hooks[1] = issueMixinHooks1[0]
	issueMixinInters0 := issueMixin[0].Interceptors()
	issue.Interceptors[0] = issueMixinInters0[0]
	issueMixinFields0 := issueMixin[0].Fields()
	_ = issueMixinFields0
	issueMixinFields1 := issueMixin[1].Fields()
	_ = issueMixinFields1
	issueMixinFields2 := issueMixin[2].Fields()
	_ = issueMixinFields2
	issueFields := schema.Issue{}.Fields()
	_ = issueFields
	// issueDescStatus is the schema descriptor for status field.
	issueDescStatus := issueMixinFields0[1].Descriptor()
	// issue.DefaultStatus holds the default value on creation for the status field.
	issue.DefaultStatus = issueDescStatus.Default.(int8)
	// issueDescVersion is the schema descriptor for version field.
	issueDescVersion := issueMixinFields1[0].Descriptor()
	// issue.DefaultVersion holds the default value on creation for the version field.
	issue.DefaultVersion = issueDescVersion.Default.(int)
	// issueDescCreatedAt is the schema descriptor for created_at field.
	issueDescCreatedAt := issueMixinFields2[0].Descriptor()
	// issue.DefaultCreatedAt holds the default value on creation for the created_at field.
	issue.DefaultCreatedAt = issueDescCreatedAt.Default.(func() time.Time)
	// issueDescUpdatedAt is the schema descriptor for updated_at field.
	issueDescUpdatedAt := issueMixinFields2[1].Descriptor()
	// issue.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	issue.DefaultUpdatedAt = issueDescUpdatedAt.Default.(func() time.Time)
	// issue.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	issue.UpdateDefaultUpdatedAt = issueDescUpdatedAt.UpdateDefault.(func() time.Time)
	issuecommentMixin := schema.IssueComment{}.Mixin()
	issuecommentMixinHooks0 := issuecommentMixin[0].Hooks()
	issuecommentMixinHooks1 := issuecommentMixin[1].Hooks()
	issuecomment.Hooks[0] = issuecommentMixinHooks0[0]
	issuecomment.Hooks[1] = issuecommentMixinHooks1[0]
	issuecommentMixinInters0 := issuecommentMixin[0].Interceptors()
	issuecomment.Interceptors[0] = issuecommentMixinInters0[0]
	issuecommentMixinFields0 := issuecommentMixin[0].Fields()
	_ = issuecommentMixinFields0
	issuecommentMixinFields1 := issuecommentMixin[1].Fields()
	_ = issuecommentMixinFields1
	issuecommentMixinFields2 := issuecommentMixin[2].Fields()
	_ = issuecommentMixinFields2
	issuecommentFields := schema.IssueComment{}.Fields()
	_ = issuecommentFields
	// issuecommentDescStatus is the schema descriptor for status field.
	issuecommentDescStatus := issuecommentMixinFields0[1].Descriptor()
	// issuecomment.DefaultStatus holds the default value on creation for the status field.
	issuecomment.DefaultStatus = issuecommentDescStatus.Default.(int8)
	// issuecommentDescVersion is the schema descriptor for version field.
	issuecommentDescVersion := issuecommentMixinFields1[0].Descriptor()
	// issuecomment.DefaultVersion holds the default value on creation for the version field.
	issuecomment.DefaultVersion = issuecommentDescVersion.Default.(int)
	// issuecommentDescCreatedAt is the schema descriptor for created_at field.
	issuecommentDescCreatedAt := issuecommentMixinFields2[0].Descriptor()
	// issuecomment.DefaultCreatedAt holds the default value on creation for the created_at field.
	issuecomment.DefaultCreatedAt = issuecommentDescCreatedAt.Default.(func() time.Time)
	// issuecommentDescUpdatedAt is the schema descriptor for updated_at field.
	issuecommentDescUpdatedAt := issuecommentMixinFields2[1].Descriptor()
	// issuecomment.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	issuecomment.DefaultUpdatedAt = issuecommentDescUpdatedAt.Default.(func() time.Time)
	// issuecomment.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	issuecomment.UpdateDefaultUpdatedAt = issuecommentDescUpdatedAt.UpdateDefault.(func() time.Time)
	materialMixin := schema.Material{}.Mixin()
	materialMixinHooks0 := materialMixin[0].Hooks()
	materialMixinHooks1 := materialMixin[1].Hooks()
	material.Hooks[0] = materialMixinHooks0[0]
	material.Hooks[1] = materialMixinHooks1[0]
	materialMixinInters0 := materialMixin[0].Interceptors()
	material.Interceptors[0] = materialMixinInters0[0]
	materialMixinFields0 := materialMixin[0].Fields()
	_ = materialMixinFields0
	materialMixinFields1 := materialMixin[1].Fields()
	_ = materialMixinFields1
	materialMixinFields2 := materialMixin[2].Fields()
	_ = materialMixinFields2
	materialFields := schema.Material{}.Fields()
	_ = materialFields
	// materialDescStatus is the schema descriptor for status field.
	materialDescStatus := materialMixinFields0[1].Descriptor()
	// material.DefaultStatus holds the default value on creation for the status field.
	material.DefaultStatus = materialDescStatus.Default.(int8)
	// materialDescVersion is the schema descriptor for version field.
	materialDescVersion := materialMixinFields1[0].Descriptor()
	// material.DefaultVersion holds the default value on creation for the version field.
	material.DefaultVersion = materialDescVersion.Default.(int)
	// materialDescCreatedAt is the schema descriptor for created_at field.
	materialDescCreatedAt := materialMixinFields2[0].Descriptor()
	// material.DefaultCreatedAt holds the default value on creation for the created_at field.
	material.DefaultCreatedAt = materialDescCreatedAt.Default.(func() time.Time)
	// materialDescUpdatedAt is the schema descriptor for updated_at field.
	materialDescUpdatedAt := materialMixinFields2[1].Descriptor()
	// material.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	material.DefaultUpdatedAt = materialDescUpdatedAt.Default.(func() time.Time)
	// material.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	material.UpdateDefaultUpdatedAt = materialDescUpdatedAt.UpdateDefault.(func() time.Time)
	materialusageMixin := schema.MaterialUsage{}.Mixin()
	materialusageMixinHooks0 := materialusageMixin[0].Hooks()
	materialusageMixinHooks1 := materialusageMixin[1].Hooks()
	materialusage.Hooks[0] = materialusageMixinHooks0[0]
	materialusage.Hooks[1] = materialusageMixinHooks1[0]
	materialusageMixinInters0 := materialusageMixin[0].Interceptors()
	materialusage.Interceptors[0] = materialusageMixinInters0[0]
	materialusageMixinFields0 := materialusageMixin[0].Fields()
	_ = materialusageMixinFields0
	materialusageMixinFields1 := materialusageMixin[1].Fields()
	_ = materialusageMixinFields1
	materialusageMixinFields2 := materialusageMixin[2].Fields()
	_ = materialusageMixinFields2
	materialusageFields := schema.MaterialUsage{}.Fields()
	_ = materialusageFields
	// materialusageDescStatus is the schema descriptor for status field.
	materialusageDescStatus := materialusageMixinFields0[1].Descriptor()
	// materialusage.DefaultStatus holds the default value on creation for the status field.
	materialusage.DefaultStatus = materialusageDescStatus.Default.(int8)
	// materialusageDescVersion is the schema descriptor for version field.
	materialusageDescVersion := materialusageMixinFields1[0].Descriptor()
	// materialusage.DefaultVersion holds the default value on creation for the version field.
	materialusage.DefaultVersion = materialusageDescVersion.Default.(int)
	// materialusageDescCreatedAt is the schema descriptor for created_at field.
	materialusageDescCreatedAt := materialusageMixinFields2[0].Descriptor()
	// materialusage.DefaultCreatedAt holds the default value on creation for the created_at field.
	materialusage.DefaultCreatedAt = materialusageDescCreatedAt.Default.(func() time.Time)
	// materialusageDescUpdatedAt is the schema descriptor for updated_at field.
	materialusageDescUpdatedAt := materialusageMixinFields2[1].Descriptor()
	// materialusage.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	materialusage.DefaultUpdatedAt = materialusageDescUpdatedAt.Default.(func() time.Time)
	// materialusage.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	materialusage.UpdateDefaultUpdatedAt = materialusageDescUpdatedAt.UpdateDefault.(func() time.Time)
	// materialusageDescQuotedQuantity is the schema descriptor for quoted_quantity field.
	materialusageDescQuotedQuantity := materialusageFields[5].Descriptor()
	// materialusage.DefaultQuotedQuantity holds the default value on creation for the quoted_quantity field.
	materialusage.DefaultQuotedQuantity = float64(materialusageDescQuotedQuantity.Default.(float64))
	messagehistoryMixin := schema.MessageHistory{}.Mixin()
	messagehistoryMixinHooks0 := messagehistoryMixin[0].Hooks()
	messagehistoryMixinHooks1 := messagehistoryMixin[1].Hooks()
	messagehistory.Hooks[0] = messagehistoryMixinHooks0[0]
	messagehistory.Hooks[1] = messagehistoryMixinHooks1[0]
	messagehistoryMixinInters0 := messagehistoryMixin[0].Interceptors()
	messagehistory.Interceptors[0] = messagehistoryMixinInters0[0]
	messagehistoryMixinFields0 := messagehistoryMixin[0].Fields()
	_ = messagehistoryMixinFields0
	messagehistoryMixinFields1 := messagehistoryMixin[1].Fields()
	_ = messagehistoryMixinFields1
	messagehistoryMixinFields2 := messagehistoryMixin[2].Fields()
	_ = messagehistoryMixinFields2
	messagehistoryFields := schema.MessageHistory{}.Fields()
	_ = messagehistoryFields
	// messagehistoryDescStatus is the schema descriptor for status field.
	messagehistoryDescStatus := messagehistoryMixinFields0[1].Descriptor()
	// messagehistory.DefaultStatus holds the default value on creation for the status field.
	messagehistory.DefaultStatus = messagehistoryDescStatus.Default.(int8)
	// messagehistoryDescVersion is the schema descriptor for version field.
	messagehistoryDescVersion := messagehistoryMixinFields1[0].Descriptor()
	// messagehistory.DefaultVersion holds the default value on creation for the version field.
	messagehistory.DefaultVersion = messagehistoryDescVersion.Default.(int)
	// messagehistoryDescCreatedAt is the schema descriptor for created_at field.
	messagehistoryDescCreatedAt := messagehistoryMixinFields2[0].Descriptor()
	// messagehistory.DefaultCreatedAt holds the default value on creation for the created_at field.
	messagehistory.DefaultCreatedAt = messagehistoryDescCreatedAt.Default.(func() time.Time)
	// messagehistoryDescUpdatedAt is the schema descriptor for updated_at field.
	messagehistoryDescUpdatedAt := messagehistoryMixinFields2[1].Descriptor()
	// messagehistory.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	messagehistory.DefaultUpdatedAt = messagehistoryDescUpdatedAt.Default.(func() time.Time)
	// messagehistory.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	messagehistory.UpdateDefaultUpdatedAt = messagehistoryDescUpdatedAt.UpdateDefault.(func() time.Time)
	noteMixin := schema.Note{}.Mixin()
	noteMixinHooks0 := noteMixin[0].Hooks()
	noteMixinHooks1 := noteMixin[1].Hooks()
	noteHooks := schema.Note{}.Hooks()
	note.Hooks[0] = noteMixinHooks0[0]
	note.Hooks[1] = noteMixinHooks1[0]
	note.Hooks[2] = noteHooks[0]
	noteMixinInters0 := noteMixin[0].Interceptors()
	note.Interceptors[0] = noteMixinInters0[0]
	noteMixinFields0 := noteMixin[0].Fields()
	_ = noteMixinFields0
	noteMixinFields1 := noteMixin[1].Fields()
	_ = noteMixinFields1
	noteMixinFields2 := noteMixin[2].Fields()
	_ = noteMixinFields2
	noteFields := schema.Note{}.Fields()
	_ = noteFields
	// noteDescStatus is the schema descriptor for status field.
	noteDescStatus := noteMixinFields0[1].Descriptor()
	// note.DefaultStatus holds the default value on creation for the status field.
	note.DefaultStatus = noteDescStatus.Default.(int8)
	// noteDescVersion is the schema descriptor for version field.
	noteDescVersion := noteMixinFields1[0].Descriptor()
	// note.DefaultVersion holds the default value on creation for the version field.
	note.DefaultVersion = noteDescVersion.Default.(int)
	// noteDescCreatedAt is the schema descriptor for created_at field.
	noteDescCreatedAt := noteMixinFields2[0].Descriptor()
	// note.DefaultCreatedAt holds the default value on creation for the created_at field.
	note.DefaultCreatedAt = noteDescCreatedAt.Default.(func() time.Time)
	// noteDescUpdatedAt is the schema descriptor for updated_at field.
	noteDescUpdatedAt := noteMixinFields2[1].Descriptor()
	// note.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	note.DefaultUpdatedAt = noteDescUpdatedAt.Default.(func() time.Time)
	// note.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	note.UpdateDefaultUpdatedAt = noteDescUpdatedAt.UpdateDefault.(func() time.Time)
	// noteDescType is the schema descriptor for type field.
	noteDescType := noteFields[1].Descriptor()
	// note.DefaultType holds the default value on creation for the type field.
	note.DefaultType = noteDescType.Default.(int)
	// noteDescHistory is the schema descriptor for history field.
	noteDescHistory := noteFields[5].Descriptor()
	// note.DefaultHistory holds the default value on creation for the history field.
	note.DefaultHistory = noteDescHistory.Default.([]types.TaskHistoryEntry)
	notificationMixin := schema.Notification{}.Mixin()
	notificationMixinHooks0 := notificationMixin[0].Hooks()
	notificationMixinHooks1 := notificationMixin[1].Hooks()
	notification.Hooks[0] = notificationMixinHooks0[0]
	notification.Hooks[1] = notificationMixinHooks1[0]
	notificationMixinInters0 := notificationMixin[0].Interceptors()
	notification.Interceptors[0] = notificationMixinInters0[0]
	notificationMixinFields0 := notificationMixin[0].Fields()
	_ = notificationMixinFields0
	notificationMixinFields1 := notificationMixin[1].Fields()
	_ = notificationMixinFields1
	notificationMixinFields2 := notificationMixin[2].Fields()
	_ = notificationMixinFields2
	notificationFields := schema.Notification{}.Fields()
	_ = notificationFields
	// notificationDescStatus is the schema descriptor for status field.
	notificationDescStatus := notificationMixinFields0[1].Descriptor()
	// notification.DefaultStatus holds the default value on creation for the status field.
	notification.DefaultStatus = notificationDescStatus.Default.(int8)
	// notificationDescVersion is the schema descriptor for version field.
	notificationDescVersion := notificationMixinFields1[0].Descriptor()
	// notification.DefaultVersion holds the default value on creation for the version field.
	notification.DefaultVersion = notificationDescVersion.Default.(int)
	// notificationDescCreatedAt is the schema descriptor for created_at field.
	notificationDescCreatedAt := notificationMixinFields2[0].Descriptor()
	// notification.DefaultCreatedAt holds the default value on creation for the created_at field.
	notification.DefaultCreatedAt = notificationDescCreatedAt.Default.(func() time.Time)
	// notificationDescUpdatedAt is the schema descriptor for updated_at field.
	notificationDescUpdatedAt := notificationMixinFields2[1].Descriptor()
	// notification.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	notification.DefaultUpdatedAt = notificationDescUpdatedAt.Default.(func() time.Time)
	// notification.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	notification.UpdateDefaultUpdatedAt = notificationDescUpdatedAt.UpdateDefault.(func() time.Time)
	// notificationDescIsRead is the schema descriptor for is_read field.
	notificationDescIsRead := notificationFields[5].Descriptor()
	// notification.DefaultIsRead holds the default value on creation for the is_read field.
	notification.DefaultIsRead = notificationDescIsRead.Default.(bool)
	otpMixin := schema.OTP{}.Mixin()
	otpMixinHooks0 := otpMixin[0].Hooks()
	otp.Hooks[0] = otpMixinHooks0[0]
	otpMixinFields0 := otpMixin[0].Fields()
	_ = otpMixinFields0
	otpMixinFields1 := otpMixin[1].Fields()
	_ = otpMixinFields1
	otpFields := schema.OTP{}.Fields()
	_ = otpFields
	// otpDescVersion is the schema descriptor for version field.
	otpDescVersion := otpMixinFields0[0].Descriptor()
	// otp.DefaultVersion holds the default value on creation for the version field.
	otp.DefaultVersion = otpDescVersion.Default.(int)
	// otpDescCreatedAt is the schema descriptor for created_at field.
	otpDescCreatedAt := otpMixinFields1[0].Descriptor()
	// otp.DefaultCreatedAt holds the default value on creation for the created_at field.
	otp.DefaultCreatedAt = otpDescCreatedAt.Default.(func() time.Time)
	// otpDescUpdatedAt is the schema descriptor for updated_at field.
	otpDescUpdatedAt := otpMixinFields1[1].Descriptor()
	// otp.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	otp.DefaultUpdatedAt = otpDescUpdatedAt.Default.(func() time.Time)
	// otp.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	otp.UpdateDefaultUpdatedAt = otpDescUpdatedAt.UpdateDefault.(func() time.Time)
	// otpDescIsUsed is the schema descriptor for is_used field.
	otpDescIsUsed := otpFields[4].Descriptor()
	// otp.DefaultIsUsed holds the default value on creation for the is_used field.
	otp.DefaultIsUsed = otpDescIsUsed.Default.(bool)
	operationMixin := schema.Operation{}.Mixin()
	operationMixinHooks0 := operationMixin[0].Hooks()
	operationMixinHooks1 := operationMixin[1].Hooks()
	operation.Hooks[0] = operationMixinHooks0[0]
	operation.Hooks[1] = operationMixinHooks1[0]
	operationMixinInters0 := operationMixin[0].Interceptors()
	operation.Interceptors[0] = operationMixinInters0[0]
	operationMixinFields0 := operationMixin[0].Fields()
	_ = operationMixinFields0
	operationMixinFields1 := operationMixin[1].Fields()
	_ = operationMixinFields1
	operationMixinFields2 := operationMixin[2].Fields()
	_ = operationMixinFields2
	operationFields := schema.Operation{}.Fields()
	_ = operationFields
	// operationDescStatus is the schema descriptor for status field.
	operationDescStatus := operationMixinFields0[1].Descriptor()
	// operation.DefaultStatus holds the default value on creation for the status field.
	operation.DefaultStatus = operationDescStatus.Default.(int8)
	// operationDescVersion is the schema descriptor for version field.
	operationDescVersion := operationMixinFields1[0].Descriptor()
	// operation.DefaultVersion holds the default value on creation for the version field.
	operation.DefaultVersion = operationDescVersion.Default.(int)
	// operationDescCreatedAt is the schema descriptor for created_at field.
	operationDescCreatedAt := operationMixinFields2[0].Descriptor()
	// operation.DefaultCreatedAt holds the default value on creation for the created_at field.
	operation.DefaultCreatedAt = operationDescCreatedAt.Default.(func() time.Time)
	// operationDescUpdatedAt is the schema descriptor for updated_at field.
	operationDescUpdatedAt := operationMixinFields2[1].Descriptor()
	// operation.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	operation.DefaultUpdatedAt = operationDescUpdatedAt.Default.(func() time.Time)
	// operation.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	operation.UpdateDefaultUpdatedAt = operationDescUpdatedAt.UpdateDefault.(func() time.Time)
	operationmaterialMixin := schema.OperationMaterial{}.Mixin()
	operationmaterialMixinHooks0 := operationmaterialMixin[0].Hooks()
	operationmaterial.Hooks[0] = operationmaterialMixinHooks0[0]
	operationmaterialMixinFields0 := operationmaterialMixin[0].Fields()
	_ = operationmaterialMixinFields0
	operationmaterialMixinFields1 := operationmaterialMixin[1].Fields()
	_ = operationmaterialMixinFields1
	operationmaterialFields := schema.OperationMaterial{}.Fields()
	_ = operationmaterialFields
	// operationmaterialDescVersion is the schema descriptor for version field.
	operationmaterialDescVersion := operationmaterialMixinFields0[0].Descriptor()
	// operationmaterial.DefaultVersion holds the default value on creation for the version field.
	operationmaterial.DefaultVersion = operationmaterialDescVersion.Default.(int)
	// operationmaterialDescCreatedAt is the schema descriptor for created_at field.
	operationmaterialDescCreatedAt := operationmaterialMixinFields1[0].Descriptor()
	// operationmaterial.DefaultCreatedAt holds the default value on creation for the created_at field.
	operationmaterial.DefaultCreatedAt = operationmaterialDescCreatedAt.Default.(func() time.Time)
	// operationmaterialDescUpdatedAt is the schema descriptor for updated_at field.
	operationmaterialDescUpdatedAt := operationmaterialMixinFields1[1].Descriptor()
	// operationmaterial.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	operationmaterial.DefaultUpdatedAt = operationmaterialDescUpdatedAt.Default.(func() time.Time)
	// operationmaterial.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	operationmaterial.UpdateDefaultUpdatedAt = operationmaterialDescUpdatedAt.UpdateDefault.(func() time.Time)
	organizationMixin := schema.Organization{}.Mixin()
	organizationMixinHooks0 := organizationMixin[0].Hooks()
	organizationMixinHooks1 := organizationMixin[1].Hooks()
	organization.Hooks[0] = organizationMixinHooks0[0]
	organization.Hooks[1] = organizationMixinHooks1[0]
	organizationMixinInters0 := organizationMixin[0].Interceptors()
	organization.Interceptors[0] = organizationMixinInters0[0]
	organizationMixinFields0 := organizationMixin[0].Fields()
	_ = organizationMixinFields0
	organizationMixinFields1 := organizationMixin[1].Fields()
	_ = organizationMixinFields1
	organizationMixinFields2 := organizationMixin[2].Fields()
	_ = organizationMixinFields2
	organizationFields := schema.Organization{}.Fields()
	_ = organizationFields
	// organizationDescStatus is the schema descriptor for status field.
	organizationDescStatus := organizationMixinFields0[1].Descriptor()
	// organization.DefaultStatus holds the default value on creation for the status field.
	organization.DefaultStatus = organizationDescStatus.Default.(int8)
	// organizationDescVersion is the schema descriptor for version field.
	organizationDescVersion := organizationMixinFields1[0].Descriptor()
	// organization.DefaultVersion holds the default value on creation for the version field.
	organization.DefaultVersion = organizationDescVersion.Default.(int)
	// organizationDescCreatedAt is the schema descriptor for created_at field.
	organizationDescCreatedAt := organizationMixinFields2[0].Descriptor()
	// organization.DefaultCreatedAt holds the default value on creation for the created_at field.
	organization.DefaultCreatedAt = organizationDescCreatedAt.Default.(func() time.Time)
	// organizationDescUpdatedAt is the schema descriptor for updated_at field.
	organizationDescUpdatedAt := organizationMixinFields2[1].Descriptor()
	// organization.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	organization.DefaultUpdatedAt = organizationDescUpdatedAt.Default.(func() time.Time)
	// organization.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	organization.UpdateDefaultUpdatedAt = organizationDescUpdatedAt.UpdateDefault.(func() time.Time)
	paymentMixin := schema.Payment{}.Mixin()
	paymentMixinHooks0 := paymentMixin[0].Hooks()
	paymentMixinHooks1 := paymentMixin[1].Hooks()
	payment.Hooks[0] = paymentMixinHooks0[0]
	payment.Hooks[1] = paymentMixinHooks1[0]
	paymentMixinInters0 := paymentMixin[0].Interceptors()
	payment.Interceptors[0] = paymentMixinInters0[0]
	paymentMixinFields0 := paymentMixin[0].Fields()
	_ = paymentMixinFields0
	paymentMixinFields1 := paymentMixin[1].Fields()
	_ = paymentMixinFields1
	paymentMixinFields2 := paymentMixin[2].Fields()
	_ = paymentMixinFields2
	paymentFields := schema.Payment{}.Fields()
	_ = paymentFields
	// paymentDescStatus is the schema descriptor for status field.
	paymentDescStatus := paymentMixinFields0[1].Descriptor()
	// payment.DefaultStatus holds the default value on creation for the status field.
	payment.DefaultStatus = paymentDescStatus.Default.(int8)
	// paymentDescVersion is the schema descriptor for version field.
	paymentDescVersion := paymentMixinFields1[0].Descriptor()
	// payment.DefaultVersion holds the default value on creation for the version field.
	payment.DefaultVersion = paymentDescVersion.Default.(int)
	// paymentDescCreatedAt is the schema descriptor for created_at field.
	paymentDescCreatedAt := paymentMixinFields2[0].Descriptor()
	// payment.DefaultCreatedAt holds the default value on creation for the created_at field.
	payment.DefaultCreatedAt = paymentDescCreatedAt.Default.(func() time.Time)
	// paymentDescUpdatedAt is the schema descriptor for updated_at field.
	paymentDescUpdatedAt := paymentMixinFields2[1].Descriptor()
	// payment.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	payment.DefaultUpdatedAt = paymentDescUpdatedAt.Default.(func() time.Time)
	// payment.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	payment.UpdateDefaultUpdatedAt = paymentDescUpdatedAt.UpdateDefault.(func() time.Time)
	// paymentDescTotalAmount is the schema descriptor for total_amount field.
	paymentDescTotalAmount := paymentFields[0].Descriptor()
	// payment.DefaultTotalAmount holds the default value on creation for the total_amount field.
	payment.DefaultTotalAmount = paymentDescTotalAmount.Default.(float64)
	// paymentDescCash is the schema descriptor for cash field.
	paymentDescCash := paymentFields[1].Descriptor()
	// payment.DefaultCash holds the default value on creation for the cash field.
	payment.DefaultCash = paymentDescCash.Default.(float64)
	// paymentDescCreditCard is the schema descriptor for credit_card field.
	paymentDescCreditCard := paymentFields[2].Descriptor()
	// payment.DefaultCreditCard holds the default value on creation for the credit_card field.
	payment.DefaultCreditCard = paymentDescCreditCard.Default.(float64)
	// paymentDescMpos is the schema descriptor for mpos field.
	paymentDescMpos := paymentFields[3].Descriptor()
	// payment.DefaultMpos holds the default value on creation for the mpos field.
	payment.DefaultMpos = paymentDescMpos.Default.(float64)
	// paymentDescBank is the schema descriptor for bank field.
	paymentDescBank := paymentFields[4].Descriptor()
	// payment.DefaultBank holds the default value on creation for the bank field.
	payment.DefaultBank = paymentDescBank.Default.(float64)
	// paymentDescMomo is the schema descriptor for momo field.
	paymentDescMomo := paymentFields[5].Descriptor()
	// payment.DefaultMomo holds the default value on creation for the momo field.
	payment.DefaultMomo = paymentDescMomo.Default.(float64)
	// paymentDescPaymentDate is the schema descriptor for payment_date field.
	paymentDescPaymentDate := paymentFields[7].Descriptor()
	// payment.DefaultPaymentDate holds the default value on creation for the payment_date field.
	payment.DefaultPaymentDate = paymentDescPaymentDate.Default.(func() time.Time)
	paymentallocationMixin := schema.PaymentAllocation{}.Mixin()
	paymentallocationMixinHooks0 := paymentallocationMixin[0].Hooks()
	paymentallocationMixinHooks1 := paymentallocationMixin[1].Hooks()
	paymentallocation.Hooks[0] = paymentallocationMixinHooks0[0]
	paymentallocation.Hooks[1] = paymentallocationMixinHooks1[0]
	paymentallocationMixinInters0 := paymentallocationMixin[0].Interceptors()
	paymentallocation.Interceptors[0] = paymentallocationMixinInters0[0]
	paymentallocationMixinFields0 := paymentallocationMixin[0].Fields()
	_ = paymentallocationMixinFields0
	paymentallocationMixinFields1 := paymentallocationMixin[1].Fields()
	_ = paymentallocationMixinFields1
	paymentallocationMixinFields2 := paymentallocationMixin[2].Fields()
	_ = paymentallocationMixinFields2
	paymentallocationFields := schema.PaymentAllocation{}.Fields()
	_ = paymentallocationFields
	// paymentallocationDescStatus is the schema descriptor for status field.
	paymentallocationDescStatus := paymentallocationMixinFields0[1].Descriptor()
	// paymentallocation.DefaultStatus holds the default value on creation for the status field.
	paymentallocation.DefaultStatus = paymentallocationDescStatus.Default.(int8)
	// paymentallocationDescVersion is the schema descriptor for version field.
	paymentallocationDescVersion := paymentallocationMixinFields1[0].Descriptor()
	// paymentallocation.DefaultVersion holds the default value on creation for the version field.
	paymentallocation.DefaultVersion = paymentallocationDescVersion.Default.(int)
	// paymentallocationDescCreatedAt is the schema descriptor for created_at field.
	paymentallocationDescCreatedAt := paymentallocationMixinFields2[0].Descriptor()
	// paymentallocation.DefaultCreatedAt holds the default value on creation for the created_at field.
	paymentallocation.DefaultCreatedAt = paymentallocationDescCreatedAt.Default.(func() time.Time)
	// paymentallocationDescUpdatedAt is the schema descriptor for updated_at field.
	paymentallocationDescUpdatedAt := paymentallocationMixinFields2[1].Descriptor()
	// paymentallocation.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	paymentallocation.DefaultUpdatedAt = paymentallocationDescUpdatedAt.Default.(func() time.Time)
	// paymentallocation.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	paymentallocation.UpdateDefaultUpdatedAt = paymentallocationDescUpdatedAt.UpdateDefault.(func() time.Time)
	// paymentallocationDescAmount is the schema descriptor for amount field.
	paymentallocationDescAmount := paymentallocationFields[7].Descriptor()
	// paymentallocation.DefaultAmount holds the default value on creation for the amount field.
	paymentallocation.DefaultAmount = paymentallocationDescAmount.Default.(float64)
	personMixin := schema.Person{}.Mixin()
	personMixinHooks0 := personMixin[0].Hooks()
	personMixinHooks1 := personMixin[1].Hooks()
	person.Hooks[0] = personMixinHooks0[0]
	person.Hooks[1] = personMixinHooks1[0]
	personMixinInters0 := personMixin[0].Interceptors()
	person.Interceptors[0] = personMixinInters0[0]
	personMixinFields0 := personMixin[0].Fields()
	_ = personMixinFields0
	personMixinFields1 := personMixin[1].Fields()
	_ = personMixinFields1
	personMixinFields2 := personMixin[2].Fields()
	_ = personMixinFields2
	personFields := schema.Person{}.Fields()
	_ = personFields
	// personDescStatus is the schema descriptor for status field.
	personDescStatus := personMixinFields0[1].Descriptor()
	// person.DefaultStatus holds the default value on creation for the status field.
	person.DefaultStatus = personDescStatus.Default.(int8)
	// personDescVersion is the schema descriptor for version field.
	personDescVersion := personMixinFields1[0].Descriptor()
	// person.DefaultVersion holds the default value on creation for the version field.
	person.DefaultVersion = personDescVersion.Default.(int)
	// personDescCreatedAt is the schema descriptor for created_at field.
	personDescCreatedAt := personMixinFields2[0].Descriptor()
	// person.DefaultCreatedAt holds the default value on creation for the created_at field.
	person.DefaultCreatedAt = personDescCreatedAt.Default.(func() time.Time)
	// personDescUpdatedAt is the schema descriptor for updated_at field.
	personDescUpdatedAt := personMixinFields2[1].Descriptor()
	// person.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	person.DefaultUpdatedAt = personDescUpdatedAt.Default.(func() time.Time)
	// person.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	person.UpdateDefaultUpdatedAt = personDescUpdatedAt.UpdateDefault.(func() time.Time)
	personassignmentMixin := schema.PersonAssignment{}.Mixin()
	personassignmentMixinFields0 := personassignmentMixin[0].Fields()
	_ = personassignmentMixinFields0
	personassignmentFields := schema.PersonAssignment{}.Fields()
	_ = personassignmentFields
	// personassignmentDescCreatedAt is the schema descriptor for created_at field.
	personassignmentDescCreatedAt := personassignmentMixinFields0[0].Descriptor()
	// personassignment.DefaultCreatedAt holds the default value on creation for the created_at field.
	personassignment.DefaultCreatedAt = personassignmentDescCreatedAt.Default.(func() time.Time)
	// personassignmentDescUpdatedAt is the schema descriptor for updated_at field.
	personassignmentDescUpdatedAt := personassignmentMixinFields0[1].Descriptor()
	// personassignment.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	personassignment.DefaultUpdatedAt = personassignmentDescUpdatedAt.Default.(func() time.Time)
	// personassignment.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	personassignment.UpdateDefaultUpdatedAt = personassignmentDescUpdatedAt.UpdateDefault.(func() time.Time)
	persondataMixin := schema.PersonData{}.Mixin()
	persondataMixinFields0 := persondataMixin[0].Fields()
	_ = persondataMixinFields0
	persondataFields := schema.PersonData{}.Fields()
	_ = persondataFields
	// persondataDescCreatedAt is the schema descriptor for created_at field.
	persondataDescCreatedAt := persondataMixinFields0[0].Descriptor()
	// persondata.DefaultCreatedAt holds the default value on creation for the created_at field.
	persondata.DefaultCreatedAt = persondataDescCreatedAt.Default.(func() time.Time)
	// persondataDescUpdatedAt is the schema descriptor for updated_at field.
	persondataDescUpdatedAt := persondataMixinFields0[1].Descriptor()
	// persondata.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	persondata.DefaultUpdatedAt = persondataDescUpdatedAt.Default.(func() time.Time)
	// persondata.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	persondata.UpdateDefaultUpdatedAt = persondataDescUpdatedAt.UpdateDefault.(func() time.Time)
	personhistoryMixin := schema.PersonHistory{}.Mixin()
	personhistoryMixinFields0 := personhistoryMixin[0].Fields()
	_ = personhistoryMixinFields0
	personhistoryFields := schema.PersonHistory{}.Fields()
	_ = personhistoryFields
	// personhistoryDescCreatedAt is the schema descriptor for created_at field.
	personhistoryDescCreatedAt := personhistoryMixinFields0[0].Descriptor()
	// personhistory.DefaultCreatedAt holds the default value on creation for the created_at field.
	personhistory.DefaultCreatedAt = personhistoryDescCreatedAt.Default.(func() time.Time)
	// personhistoryDescUpdatedAt is the schema descriptor for updated_at field.
	personhistoryDescUpdatedAt := personhistoryMixinFields0[1].Descriptor()
	// personhistory.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	personhistory.DefaultUpdatedAt = personhistoryDescUpdatedAt.Default.(func() time.Time)
	// personhistory.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	personhistory.UpdateDefaultUpdatedAt = personhistoryDescUpdatedAt.UpdateDefault.(func() time.Time)
	// personhistoryDescChangedAt is the schema descriptor for changed_at field.
	personhistoryDescChangedAt := personhistoryFields[6].Descriptor()
	// personhistory.DefaultChangedAt holds the default value on creation for the changed_at field.
	personhistory.DefaultChangedAt = personhistoryDescChangedAt.Default.(func() time.Time)
	personreferralMixin := schema.PersonReferral{}.Mixin()
	personreferralMixinFields0 := personreferralMixin[0].Fields()
	_ = personreferralMixinFields0
	personreferralFields := schema.PersonReferral{}.Fields()
	_ = personreferralFields
	// personreferralDescCreatedAt is the schema descriptor for created_at field.
	personreferralDescCreatedAt := personreferralMixinFields0[0].Descriptor()
	// personreferral.DefaultCreatedAt holds the default value on creation for the created_at field.
	personreferral.DefaultCreatedAt = personreferralDescCreatedAt.Default.(func() time.Time)
	// personreferralDescUpdatedAt is the schema descriptor for updated_at field.
	personreferralDescUpdatedAt := personreferralMixinFields0[1].Descriptor()
	// personreferral.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	personreferral.DefaultUpdatedAt = personreferralDescUpdatedAt.Default.(func() time.Time)
	// personreferral.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	personreferral.UpdateDefaultUpdatedAt = personreferralDescUpdatedAt.UpdateDefault.(func() time.Time)
	pipelineMixin := schema.Pipeline{}.Mixin()
	pipelineMixinHooks0 := pipelineMixin[0].Hooks()
	pipelineMixinHooks1 := pipelineMixin[1].Hooks()
	pipeline.Hooks[0] = pipelineMixinHooks0[0]
	pipeline.Hooks[1] = pipelineMixinHooks1[0]
	pipelineMixinInters0 := pipelineMixin[0].Interceptors()
	pipeline.Interceptors[0] = pipelineMixinInters0[0]
	pipelineMixinFields0 := pipelineMixin[0].Fields()
	_ = pipelineMixinFields0
	pipelineMixinFields1 := pipelineMixin[1].Fields()
	_ = pipelineMixinFields1
	pipelineMixinFields2 := pipelineMixin[2].Fields()
	_ = pipelineMixinFields2
	pipelineFields := schema.Pipeline{}.Fields()
	_ = pipelineFields
	// pipelineDescStatus is the schema descriptor for status field.
	pipelineDescStatus := pipelineMixinFields0[1].Descriptor()
	// pipeline.DefaultStatus holds the default value on creation for the status field.
	pipeline.DefaultStatus = pipelineDescStatus.Default.(int8)
	// pipelineDescVersion is the schema descriptor for version field.
	pipelineDescVersion := pipelineMixinFields1[0].Descriptor()
	// pipeline.DefaultVersion holds the default value on creation for the version field.
	pipeline.DefaultVersion = pipelineDescVersion.Default.(int)
	// pipelineDescCreatedAt is the schema descriptor for created_at field.
	pipelineDescCreatedAt := pipelineMixinFields2[0].Descriptor()
	// pipeline.DefaultCreatedAt holds the default value on creation for the created_at field.
	pipeline.DefaultCreatedAt = pipelineDescCreatedAt.Default.(func() time.Time)
	// pipelineDescUpdatedAt is the schema descriptor for updated_at field.
	pipelineDescUpdatedAt := pipelineMixinFields2[1].Descriptor()
	// pipeline.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	pipeline.DefaultUpdatedAt = pipelineDescUpdatedAt.Default.(func() time.Time)
	// pipeline.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	pipeline.UpdateDefaultUpdatedAt = pipelineDescUpdatedAt.UpdateDefault.(func() time.Time)
	productMixin := schema.Product{}.Mixin()
	productMixinHooks0 := productMixin[0].Hooks()
	productMixinHooks1 := productMixin[1].Hooks()
	product.Hooks[0] = productMixinHooks0[0]
	product.Hooks[1] = productMixinHooks1[0]
	productMixinInters0 := productMixin[0].Interceptors()
	product.Interceptors[0] = productMixinInters0[0]
	productMixinFields0 := productMixin[0].Fields()
	_ = productMixinFields0
	productMixinFields1 := productMixin[1].Fields()
	_ = productMixinFields1
	productMixinFields2 := productMixin[2].Fields()
	_ = productMixinFields2
	productFields := schema.Product{}.Fields()
	_ = productFields
	// productDescStatus is the schema descriptor for status field.
	productDescStatus := productMixinFields0[1].Descriptor()
	// product.DefaultStatus holds the default value on creation for the status field.
	product.DefaultStatus = productDescStatus.Default.(int8)
	// productDescVersion is the schema descriptor for version field.
	productDescVersion := productMixinFields1[0].Descriptor()
	// product.DefaultVersion holds the default value on creation for the version field.
	product.DefaultVersion = productDescVersion.Default.(int)
	// productDescCreatedAt is the schema descriptor for created_at field.
	productDescCreatedAt := productMixinFields2[0].Descriptor()
	// product.DefaultCreatedAt holds the default value on creation for the created_at field.
	product.DefaultCreatedAt = productDescCreatedAt.Default.(func() time.Time)
	// productDescUpdatedAt is the schema descriptor for updated_at field.
	productDescUpdatedAt := productMixinFields2[1].Descriptor()
	// product.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	product.DefaultUpdatedAt = productDescUpdatedAt.Default.(func() time.Time)
	// product.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	product.UpdateDefaultUpdatedAt = productDescUpdatedAt.UpdateDefault.(func() time.Time)
	// productDescPrice is the schema descriptor for price field.
	productDescPrice := productFields[3].Descriptor()
	// product.DefaultPrice holds the default value on creation for the price field.
	product.DefaultPrice = productDescPrice.Default.(int)
	// productDescQuantity is the schema descriptor for quantity field.
	productDescQuantity := productFields[5].Descriptor()
	// product.DefaultQuantity holds the default value on creation for the quantity field.
	product.DefaultQuantity = productDescQuantity.Default.(int)
	productoperationMixin := schema.ProductOperation{}.Mixin()
	productoperationMixinHooks0 := productoperationMixin[0].Hooks()
	productoperation.Hooks[0] = productoperationMixinHooks0[0]
	productoperationMixinFields0 := productoperationMixin[0].Fields()
	_ = productoperationMixinFields0
	productoperationMixinFields1 := productoperationMixin[1].Fields()
	_ = productoperationMixinFields1
	productoperationFields := schema.ProductOperation{}.Fields()
	_ = productoperationFields
	// productoperationDescVersion is the schema descriptor for version field.
	productoperationDescVersion := productoperationMixinFields0[0].Descriptor()
	// productoperation.DefaultVersion holds the default value on creation for the version field.
	productoperation.DefaultVersion = productoperationDescVersion.Default.(int)
	// productoperationDescCreatedAt is the schema descriptor for created_at field.
	productoperationDescCreatedAt := productoperationMixinFields1[0].Descriptor()
	// productoperation.DefaultCreatedAt holds the default value on creation for the created_at field.
	productoperation.DefaultCreatedAt = productoperationDescCreatedAt.Default.(func() time.Time)
	// productoperationDescUpdatedAt is the schema descriptor for updated_at field.
	productoperationDescUpdatedAt := productoperationMixinFields1[1].Descriptor()
	// productoperation.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	productoperation.DefaultUpdatedAt = productoperationDescUpdatedAt.Default.(func() time.Time)
	// productoperation.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	productoperation.UpdateDefaultUpdatedAt = productoperationDescUpdatedAt.UpdateDefault.(func() time.Time)
	referralMixin := schema.Referral{}.Mixin()
	referralMixinHooks0 := referralMixin[0].Hooks()
	referralMixinHooks1 := referralMixin[1].Hooks()
	referral.Hooks[0] = referralMixinHooks0[0]
	referral.Hooks[1] = referralMixinHooks1[0]
	referralMixinInters0 := referralMixin[0].Interceptors()
	referral.Interceptors[0] = referralMixinInters0[0]
	referralMixinFields0 := referralMixin[0].Fields()
	_ = referralMixinFields0
	referralMixinFields1 := referralMixin[1].Fields()
	_ = referralMixinFields1
	referralMixinFields2 := referralMixin[2].Fields()
	_ = referralMixinFields2
	referralFields := schema.Referral{}.Fields()
	_ = referralFields
	// referralDescStatus is the schema descriptor for status field.
	referralDescStatus := referralMixinFields0[1].Descriptor()
	// referral.DefaultStatus holds the default value on creation for the status field.
	referral.DefaultStatus = referralDescStatus.Default.(int8)
	// referralDescVersion is the schema descriptor for version field.
	referralDescVersion := referralMixinFields1[0].Descriptor()
	// referral.DefaultVersion holds the default value on creation for the version field.
	referral.DefaultVersion = referralDescVersion.Default.(int)
	// referralDescCreatedAt is the schema descriptor for created_at field.
	referralDescCreatedAt := referralMixinFields2[0].Descriptor()
	// referral.DefaultCreatedAt holds the default value on creation for the created_at field.
	referral.DefaultCreatedAt = referralDescCreatedAt.Default.(func() time.Time)
	// referralDescUpdatedAt is the schema descriptor for updated_at field.
	referralDescUpdatedAt := referralMixinFields2[1].Descriptor()
	// referral.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	referral.DefaultUpdatedAt = referralDescUpdatedAt.Default.(func() time.Time)
	// referral.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	referral.UpdateDefaultUpdatedAt = referralDescUpdatedAt.UpdateDefault.(func() time.Time)
	scheduleMixin := schema.Schedule{}.Mixin()
	scheduleMixinHooks0 := scheduleMixin[0].Hooks()
	scheduleMixinHooks1 := scheduleMixin[1].Hooks()
	schedule.Hooks[0] = scheduleMixinHooks0[0]
	schedule.Hooks[1] = scheduleMixinHooks1[0]
	scheduleMixinInters0 := scheduleMixin[0].Interceptors()
	schedule.Interceptors[0] = scheduleMixinInters0[0]
	scheduleMixinFields0 := scheduleMixin[0].Fields()
	_ = scheduleMixinFields0
	scheduleMixinFields1 := scheduleMixin[1].Fields()
	_ = scheduleMixinFields1
	scheduleMixinFields2 := scheduleMixin[2].Fields()
	_ = scheduleMixinFields2
	scheduleFields := schema.Schedule{}.Fields()
	_ = scheduleFields
	// scheduleDescStatus is the schema descriptor for status field.
	scheduleDescStatus := scheduleMixinFields0[1].Descriptor()
	// schedule.DefaultStatus holds the default value on creation for the status field.
	schedule.DefaultStatus = scheduleDescStatus.Default.(int8)
	// scheduleDescVersion is the schema descriptor for version field.
	scheduleDescVersion := scheduleMixinFields1[0].Descriptor()
	// schedule.DefaultVersion holds the default value on creation for the version field.
	schedule.DefaultVersion = scheduleDescVersion.Default.(int)
	// scheduleDescCreatedAt is the schema descriptor for created_at field.
	scheduleDescCreatedAt := scheduleMixinFields2[0].Descriptor()
	// schedule.DefaultCreatedAt holds the default value on creation for the created_at field.
	schedule.DefaultCreatedAt = scheduleDescCreatedAt.Default.(func() time.Time)
	// scheduleDescUpdatedAt is the schema descriptor for updated_at field.
	scheduleDescUpdatedAt := scheduleMixinFields2[1].Descriptor()
	// schedule.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	schedule.DefaultUpdatedAt = scheduleDescUpdatedAt.Default.(func() time.Time)
	// schedule.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	schedule.UpdateDefaultUpdatedAt = scheduleDescUpdatedAt.UpdateDefault.(func() time.Time)
	// scheduleDescStage is the schema descriptor for stage field.
	scheduleDescStage := scheduleFields[3].Descriptor()
	// schedule.DefaultStage holds the default value on creation for the stage field.
	schedule.DefaultStage = scheduleDescStage.Default.(int)
	settingMixin := schema.Setting{}.Mixin()
	settingMixinHooks0 := settingMixin[0].Hooks()
	settingMixinHooks1 := settingMixin[1].Hooks()
	setting.Hooks[0] = settingMixinHooks0[0]
	setting.Hooks[1] = settingMixinHooks1[0]
	settingMixinInters0 := settingMixin[0].Interceptors()
	setting.Interceptors[0] = settingMixinInters0[0]
	settingMixinFields0 := settingMixin[0].Fields()
	_ = settingMixinFields0
	settingMixinFields1 := settingMixin[1].Fields()
	_ = settingMixinFields1
	settingMixinFields2 := settingMixin[2].Fields()
	_ = settingMixinFields2
	settingFields := schema.Setting{}.Fields()
	_ = settingFields
	// settingDescStatus is the schema descriptor for status field.
	settingDescStatus := settingMixinFields0[1].Descriptor()
	// setting.DefaultStatus holds the default value on creation for the status field.
	setting.DefaultStatus = settingDescStatus.Default.(int8)
	// settingDescVersion is the schema descriptor for version field.
	settingDescVersion := settingMixinFields1[0].Descriptor()
	// setting.DefaultVersion holds the default value on creation for the version field.
	setting.DefaultVersion = settingDescVersion.Default.(int)
	// settingDescCreatedAt is the schema descriptor for created_at field.
	settingDescCreatedAt := settingMixinFields2[0].Descriptor()
	// setting.DefaultCreatedAt holds the default value on creation for the created_at field.
	setting.DefaultCreatedAt = settingDescCreatedAt.Default.(func() time.Time)
	// settingDescUpdatedAt is the schema descriptor for updated_at field.
	settingDescUpdatedAt := settingMixinFields2[1].Descriptor()
	// setting.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	setting.DefaultUpdatedAt = settingDescUpdatedAt.Default.(func() time.Time)
	// setting.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	setting.UpdateDefaultUpdatedAt = settingDescUpdatedAt.UpdateDefault.(func() time.Time)
	stageMixin := schema.Stage{}.Mixin()
	stageMixinHooks0 := stageMixin[0].Hooks()
	stageMixinHooks1 := stageMixin[1].Hooks()
	stage.Hooks[0] = stageMixinHooks0[0]
	stage.Hooks[1] = stageMixinHooks1[0]
	stageMixinInters0 := stageMixin[0].Interceptors()
	stage.Interceptors[0] = stageMixinInters0[0]
	stageMixinFields0 := stageMixin[0].Fields()
	_ = stageMixinFields0
	stageMixinFields1 := stageMixin[1].Fields()
	_ = stageMixinFields1
	stageMixinFields2 := stageMixin[2].Fields()
	_ = stageMixinFields2
	stageFields := schema.Stage{}.Fields()
	_ = stageFields
	// stageDescStatus is the schema descriptor for status field.
	stageDescStatus := stageMixinFields0[1].Descriptor()
	// stage.DefaultStatus holds the default value on creation for the status field.
	stage.DefaultStatus = stageDescStatus.Default.(int8)
	// stageDescVersion is the schema descriptor for version field.
	stageDescVersion := stageMixinFields1[0].Descriptor()
	// stage.DefaultVersion holds the default value on creation for the version field.
	stage.DefaultVersion = stageDescVersion.Default.(int)
	// stageDescCreatedAt is the schema descriptor for created_at field.
	stageDescCreatedAt := stageMixinFields2[0].Descriptor()
	// stage.DefaultCreatedAt holds the default value on creation for the created_at field.
	stage.DefaultCreatedAt = stageDescCreatedAt.Default.(func() time.Time)
	// stageDescUpdatedAt is the schema descriptor for updated_at field.
	stageDescUpdatedAt := stageMixinFields2[1].Descriptor()
	// stage.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	stage.DefaultUpdatedAt = stageDescUpdatedAt.Default.(func() time.Time)
	// stage.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	stage.UpdateDefaultUpdatedAt = stageDescUpdatedAt.UpdateDefault.(func() time.Time)
	tagMixin := schema.Tag{}.Mixin()
	tagMixinHooks0 := tagMixin[0].Hooks()
	tagMixinHooks1 := tagMixin[1].Hooks()
	tag.Hooks[0] = tagMixinHooks0[0]
	tag.Hooks[1] = tagMixinHooks1[0]
	tagMixinInters0 := tagMixin[0].Interceptors()
	tag.Interceptors[0] = tagMixinInters0[0]
	tagMixinFields0 := tagMixin[0].Fields()
	_ = tagMixinFields0
	tagMixinFields1 := tagMixin[1].Fields()
	_ = tagMixinFields1
	tagMixinFields2 := tagMixin[2].Fields()
	_ = tagMixinFields2
	tagFields := schema.Tag{}.Fields()
	_ = tagFields
	// tagDescStatus is the schema descriptor for status field.
	tagDescStatus := tagMixinFields0[1].Descriptor()
	// tag.DefaultStatus holds the default value on creation for the status field.
	tag.DefaultStatus = tagDescStatus.Default.(int8)
	// tagDescVersion is the schema descriptor for version field.
	tagDescVersion := tagMixinFields1[0].Descriptor()
	// tag.DefaultVersion holds the default value on creation for the version field.
	tag.DefaultVersion = tagDescVersion.Default.(int)
	// tagDescCreatedAt is the schema descriptor for created_at field.
	tagDescCreatedAt := tagMixinFields2[0].Descriptor()
	// tag.DefaultCreatedAt holds the default value on creation for the created_at field.
	tag.DefaultCreatedAt = tagDescCreatedAt.Default.(func() time.Time)
	// tagDescUpdatedAt is the schema descriptor for updated_at field.
	tagDescUpdatedAt := tagMixinFields2[1].Descriptor()
	// tag.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	tag.DefaultUpdatedAt = tagDescUpdatedAt.Default.(func() time.Time)
	// tag.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	tag.UpdateDefaultUpdatedAt = tagDescUpdatedAt.UpdateDefault.(func() time.Time)
	// tagDescName is the schema descriptor for name field.
	tagDescName := tagFields[0].Descriptor()
	// tag.NameValidator is a validator for the "name" field. It is called by the builders before save.
	tag.NameValidator = tagDescName.Validators[0].(func(string) error)
	// tagDescCategory is the schema descriptor for category field.
	tagDescCategory := tagFields[1].Descriptor()
	// tag.CategoryValidator is a validator for the "category" field. It is called by the builders before save.
	tag.CategoryValidator = tagDescCategory.Validators[0].(func(string) error)
	tagdealMixin := schema.TagDeal{}.Mixin()
	tagdealMixinFields0 := tagdealMixin[0].Fields()
	_ = tagdealMixinFields0
	tagdealFields := schema.TagDeal{}.Fields()
	_ = tagdealFields
	// tagdealDescCreatedAt is the schema descriptor for created_at field.
	tagdealDescCreatedAt := tagdealMixinFields0[0].Descriptor()
	// tagdeal.DefaultCreatedAt holds the default value on creation for the created_at field.
	tagdeal.DefaultCreatedAt = tagdealDescCreatedAt.Default.(func() time.Time)
	// tagdealDescUpdatedAt is the schema descriptor for updated_at field.
	tagdealDescUpdatedAt := tagdealMixinFields0[1].Descriptor()
	// tagdeal.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	tagdeal.DefaultUpdatedAt = tagdealDescUpdatedAt.Default.(func() time.Time)
	// tagdeal.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	tagdeal.UpdateDefaultUpdatedAt = tagdealDescUpdatedAt.UpdateDefault.(func() time.Time)
	tagpersonMixin := schema.TagPerson{}.Mixin()
	tagpersonMixinFields0 := tagpersonMixin[0].Fields()
	_ = tagpersonMixinFields0
	tagpersonFields := schema.TagPerson{}.Fields()
	_ = tagpersonFields
	// tagpersonDescCreatedAt is the schema descriptor for created_at field.
	tagpersonDescCreatedAt := tagpersonMixinFields0[0].Descriptor()
	// tagperson.DefaultCreatedAt holds the default value on creation for the created_at field.
	tagperson.DefaultCreatedAt = tagpersonDescCreatedAt.Default.(func() time.Time)
	// tagpersonDescUpdatedAt is the schema descriptor for updated_at field.
	tagpersonDescUpdatedAt := tagpersonMixinFields0[1].Descriptor()
	// tagperson.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	tagperson.DefaultUpdatedAt = tagpersonDescUpdatedAt.Default.(func() time.Time)
	// tagperson.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	tagperson.UpdateDefaultUpdatedAt = tagpersonDescUpdatedAt.UpdateDefault.(func() time.Time)
	taskMixin := schema.Task{}.Mixin()
	taskMixinHooks0 := taskMixin[0].Hooks()
	taskMixinHooks1 := taskMixin[1].Hooks()
	taskHooks := schema.Task{}.Hooks()
	task.Hooks[0] = taskMixinHooks0[0]
	task.Hooks[1] = taskMixinHooks1[0]
	task.Hooks[2] = taskHooks[0]
	taskMixinInters0 := taskMixin[0].Interceptors()
	task.Interceptors[0] = taskMixinInters0[0]
	taskMixinFields0 := taskMixin[0].Fields()
	_ = taskMixinFields0
	taskMixinFields1 := taskMixin[1].Fields()
	_ = taskMixinFields1
	taskMixinFields2 := taskMixin[2].Fields()
	_ = taskMixinFields2
	taskFields := schema.Task{}.Fields()
	_ = taskFields
	// taskDescStatus is the schema descriptor for status field.
	taskDescStatus := taskMixinFields0[1].Descriptor()
	// task.DefaultStatus holds the default value on creation for the status field.
	task.DefaultStatus = taskDescStatus.Default.(int8)
	// taskDescVersion is the schema descriptor for version field.
	taskDescVersion := taskMixinFields1[0].Descriptor()
	// task.DefaultVersion holds the default value on creation for the version field.
	task.DefaultVersion = taskDescVersion.Default.(int)
	// taskDescCreatedAt is the schema descriptor for created_at field.
	taskDescCreatedAt := taskMixinFields2[0].Descriptor()
	// task.DefaultCreatedAt holds the default value on creation for the created_at field.
	task.DefaultCreatedAt = taskDescCreatedAt.Default.(func() time.Time)
	// taskDescUpdatedAt is the schema descriptor for updated_at field.
	taskDescUpdatedAt := taskMixinFields2[1].Descriptor()
	// task.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	task.DefaultUpdatedAt = taskDescUpdatedAt.Default.(func() time.Time)
	// task.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	task.UpdateDefaultUpdatedAt = taskDescUpdatedAt.UpdateDefault.(func() time.Time)
	// taskDescCurrentSerial is the schema descriptor for current_serial field.
	taskDescCurrentSerial := taskFields[7].Descriptor()
	// task.DefaultCurrentSerial holds the default value on creation for the current_serial field.
	task.DefaultCurrentSerial = taskDescCurrentSerial.Default.(int)
	// taskDescHistory is the schema descriptor for history field.
	taskDescHistory := taskFields[16].Descriptor()
	// task.DefaultHistory holds the default value on creation for the history field.
	task.DefaultHistory = taskDescHistory.Default.([]types.TaskHistoryEntry)
	taskassignmentMixin := schema.TaskAssignment{}.Mixin()
	taskassignmentMixinHooks0 := taskassignmentMixin[0].Hooks()
	taskassignmentMixinHooks1 := taskassignmentMixin[1].Hooks()
	taskassignment.Hooks[0] = taskassignmentMixinHooks0[0]
	taskassignment.Hooks[1] = taskassignmentMixinHooks1[0]
	taskassignmentMixinInters0 := taskassignmentMixin[0].Interceptors()
	taskassignment.Interceptors[0] = taskassignmentMixinInters0[0]
	taskassignmentMixinFields0 := taskassignmentMixin[0].Fields()
	_ = taskassignmentMixinFields0
	taskassignmentMixinFields1 := taskassignmentMixin[1].Fields()
	_ = taskassignmentMixinFields1
	taskassignmentMixinFields2 := taskassignmentMixin[2].Fields()
	_ = taskassignmentMixinFields2
	taskassignmentFields := schema.TaskAssignment{}.Fields()
	_ = taskassignmentFields
	// taskassignmentDescStatus is the schema descriptor for status field.
	taskassignmentDescStatus := taskassignmentMixinFields0[1].Descriptor()
	// taskassignment.DefaultStatus holds the default value on creation for the status field.
	taskassignment.DefaultStatus = taskassignmentDescStatus.Default.(int8)
	// taskassignmentDescVersion is the schema descriptor for version field.
	taskassignmentDescVersion := taskassignmentMixinFields1[0].Descriptor()
	// taskassignment.DefaultVersion holds the default value on creation for the version field.
	taskassignment.DefaultVersion = taskassignmentDescVersion.Default.(int)
	// taskassignmentDescCreatedAt is the schema descriptor for created_at field.
	taskassignmentDescCreatedAt := taskassignmentMixinFields2[0].Descriptor()
	// taskassignment.DefaultCreatedAt holds the default value on creation for the created_at field.
	taskassignment.DefaultCreatedAt = taskassignmentDescCreatedAt.Default.(func() time.Time)
	// taskassignmentDescUpdatedAt is the schema descriptor for updated_at field.
	taskassignmentDescUpdatedAt := taskassignmentMixinFields2[1].Descriptor()
	// taskassignment.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	taskassignment.DefaultUpdatedAt = taskassignmentDescUpdatedAt.Default.(func() time.Time)
	// taskassignment.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	taskassignment.UpdateDefaultUpdatedAt = taskassignmentDescUpdatedAt.UpdateDefault.(func() time.Time)
	taskdepartmentMixin := schema.TaskDepartment{}.Mixin()
	taskdepartmentMixinHooks0 := taskdepartmentMixin[0].Hooks()
	taskdepartmentMixinHooks1 := taskdepartmentMixin[1].Hooks()
	taskdepartment.Hooks[0] = taskdepartmentMixinHooks0[0]
	taskdepartment.Hooks[1] = taskdepartmentMixinHooks1[0]
	taskdepartmentMixinInters0 := taskdepartmentMixin[0].Interceptors()
	taskdepartment.Interceptors[0] = taskdepartmentMixinInters0[0]
	taskdepartmentMixinFields0 := taskdepartmentMixin[0].Fields()
	_ = taskdepartmentMixinFields0
	taskdepartmentMixinFields1 := taskdepartmentMixin[1].Fields()
	_ = taskdepartmentMixinFields1
	taskdepartmentMixinFields2 := taskdepartmentMixin[2].Fields()
	_ = taskdepartmentMixinFields2
	taskdepartmentFields := schema.TaskDepartment{}.Fields()
	_ = taskdepartmentFields
	// taskdepartmentDescStatus is the schema descriptor for status field.
	taskdepartmentDescStatus := taskdepartmentMixinFields0[1].Descriptor()
	// taskdepartment.DefaultStatus holds the default value on creation for the status field.
	taskdepartment.DefaultStatus = taskdepartmentDescStatus.Default.(int8)
	// taskdepartmentDescVersion is the schema descriptor for version field.
	taskdepartmentDescVersion := taskdepartmentMixinFields1[0].Descriptor()
	// taskdepartment.DefaultVersion holds the default value on creation for the version field.
	taskdepartment.DefaultVersion = taskdepartmentDescVersion.Default.(int)
	// taskdepartmentDescCreatedAt is the schema descriptor for created_at field.
	taskdepartmentDescCreatedAt := taskdepartmentMixinFields2[0].Descriptor()
	// taskdepartment.DefaultCreatedAt holds the default value on creation for the created_at field.
	taskdepartment.DefaultCreatedAt = taskdepartmentDescCreatedAt.Default.(func() time.Time)
	// taskdepartmentDescUpdatedAt is the schema descriptor for updated_at field.
	taskdepartmentDescUpdatedAt := taskdepartmentMixinFields2[1].Descriptor()
	// taskdepartment.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	taskdepartment.DefaultUpdatedAt = taskdepartmentDescUpdatedAt.Default.(func() time.Time)
	// taskdepartment.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	taskdepartment.UpdateDefaultUpdatedAt = taskdepartmentDescUpdatedAt.UpdateDefault.(func() time.Time)
	taskhistoryMixin := schema.TaskHistory{}.Mixin()
	taskhistoryMixinFields0 := taskhistoryMixin[0].Fields()
	_ = taskhistoryMixinFields0
	taskhistoryFields := schema.TaskHistory{}.Fields()
	_ = taskhistoryFields
	// taskhistoryDescCreatedAt is the schema descriptor for created_at field.
	taskhistoryDescCreatedAt := taskhistoryMixinFields0[0].Descriptor()
	// taskhistory.DefaultCreatedAt holds the default value on creation for the created_at field.
	taskhistory.DefaultCreatedAt = taskhistoryDescCreatedAt.Default.(func() time.Time)
	// taskhistoryDescUpdatedAt is the schema descriptor for updated_at field.
	taskhistoryDescUpdatedAt := taskhistoryMixinFields0[1].Descriptor()
	// taskhistory.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	taskhistory.DefaultUpdatedAt = taskhistoryDescUpdatedAt.Default.(func() time.Time)
	// taskhistory.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	taskhistory.UpdateDefaultUpdatedAt = taskhistoryDescUpdatedAt.UpdateDefault.(func() time.Time)
	// taskhistoryDescChangedAt is the schema descriptor for changed_at field.
	taskhistoryDescChangedAt := taskhistoryFields[6].Descriptor()
	// taskhistory.DefaultChangedAt holds the default value on creation for the changed_at field.
	taskhistory.DefaultChangedAt = taskhistoryDescChangedAt.Default.(func() time.Time)
	tasknoteMixin := schema.TaskNote{}.Mixin()
	tasknoteMixinHooks0 := tasknoteMixin[0].Hooks()
	tasknoteMixinHooks1 := tasknoteMixin[1].Hooks()
	tasknote.Hooks[0] = tasknoteMixinHooks0[0]
	tasknote.Hooks[1] = tasknoteMixinHooks1[0]
	tasknoteMixinInters0 := tasknoteMixin[0].Interceptors()
	tasknote.Interceptors[0] = tasknoteMixinInters0[0]
	tasknoteMixinFields0 := tasknoteMixin[0].Fields()
	_ = tasknoteMixinFields0
	tasknoteMixinFields1 := tasknoteMixin[1].Fields()
	_ = tasknoteMixinFields1
	tasknoteMixinFields2 := tasknoteMixin[2].Fields()
	_ = tasknoteMixinFields2
	tasknoteFields := schema.TaskNote{}.Fields()
	_ = tasknoteFields
	// tasknoteDescStatus is the schema descriptor for status field.
	tasknoteDescStatus := tasknoteMixinFields0[1].Descriptor()
	// tasknote.DefaultStatus holds the default value on creation for the status field.
	tasknote.DefaultStatus = tasknoteDescStatus.Default.(int8)
	// tasknoteDescVersion is the schema descriptor for version field.
	tasknoteDescVersion := tasknoteMixinFields1[0].Descriptor()
	// tasknote.DefaultVersion holds the default value on creation for the version field.
	tasknote.DefaultVersion = tasknoteDescVersion.Default.(int)
	// tasknoteDescCreatedAt is the schema descriptor for created_at field.
	tasknoteDescCreatedAt := tasknoteMixinFields2[0].Descriptor()
	// tasknote.DefaultCreatedAt holds the default value on creation for the created_at field.
	tasknote.DefaultCreatedAt = tasknoteDescCreatedAt.Default.(func() time.Time)
	// tasknoteDescUpdatedAt is the schema descriptor for updated_at field.
	tasknoteDescUpdatedAt := tasknoteMixinFields2[1].Descriptor()
	// tasknote.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	tasknote.DefaultUpdatedAt = tasknoteDescUpdatedAt.Default.(func() time.Time)
	// tasknote.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	tasknote.UpdateDefaultUpdatedAt = tasknoteDescUpdatedAt.UpdateDefault.(func() time.Time)
	taskrecurringMixin := schema.TaskRecurring{}.Mixin()
	taskrecurringMixinFields0 := taskrecurringMixin[0].Fields()
	_ = taskrecurringMixinFields0
	taskrecurringFields := schema.TaskRecurring{}.Fields()
	_ = taskrecurringFields
	// taskrecurringDescCreatedAt is the schema descriptor for created_at field.
	taskrecurringDescCreatedAt := taskrecurringMixinFields0[0].Descriptor()
	// taskrecurring.DefaultCreatedAt holds the default value on creation for the created_at field.
	taskrecurring.DefaultCreatedAt = taskrecurringDescCreatedAt.Default.(func() time.Time)
	// taskrecurringDescUpdatedAt is the schema descriptor for updated_at field.
	taskrecurringDescUpdatedAt := taskrecurringMixinFields0[1].Descriptor()
	// taskrecurring.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	taskrecurring.DefaultUpdatedAt = taskrecurringDescUpdatedAt.Default.(func() time.Time)
	// taskrecurring.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	taskrecurring.UpdateDefaultUpdatedAt = taskrecurringDescUpdatedAt.UpdateDefault.(func() time.Time)
	termMixin := schema.Term{}.Mixin()
	termMixinHooks0 := termMixin[0].Hooks()
	termMixinHooks1 := termMixin[1].Hooks()
	term.Hooks[0] = termMixinHooks0[0]
	term.Hooks[1] = termMixinHooks1[0]
	termMixinInters0 := termMixin[0].Interceptors()
	term.Interceptors[0] = termMixinInters0[0]
	termMixinFields0 := termMixin[0].Fields()
	_ = termMixinFields0
	termMixinFields1 := termMixin[1].Fields()
	_ = termMixinFields1
	termMixinFields2 := termMixin[2].Fields()
	_ = termMixinFields2
	termFields := schema.Term{}.Fields()
	_ = termFields
	// termDescStatus is the schema descriptor for status field.
	termDescStatus := termMixinFields0[1].Descriptor()
	// term.DefaultStatus holds the default value on creation for the status field.
	term.DefaultStatus = termDescStatus.Default.(int8)
	// termDescVersion is the schema descriptor for version field.
	termDescVersion := termMixinFields1[0].Descriptor()
	// term.DefaultVersion holds the default value on creation for the version field.
	term.DefaultVersion = termDescVersion.Default.(int)
	// termDescCreatedAt is the schema descriptor for created_at field.
	termDescCreatedAt := termMixinFields2[0].Descriptor()
	// term.DefaultCreatedAt holds the default value on creation for the created_at field.
	term.DefaultCreatedAt = termDescCreatedAt.Default.(func() time.Time)
	// termDescUpdatedAt is the schema descriptor for updated_at field.
	termDescUpdatedAt := termMixinFields2[1].Descriptor()
	// term.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	term.DefaultUpdatedAt = termDescUpdatedAt.Default.(func() time.Time)
	// term.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	term.UpdateDefaultUpdatedAt = termDescUpdatedAt.UpdateDefault.(func() time.Time)
	// termDescWeight is the schema descriptor for weight field.
	termDescWeight := termFields[4].Descriptor()
	// term.DefaultWeight holds the default value on creation for the weight field.
	term.DefaultWeight = termDescWeight.Default.(int)
	trackMixin := schema.Track{}.Mixin()
	trackMixinHooks0 := trackMixin[0].Hooks()
	trackMixinHooks1 := trackMixin[1].Hooks()
	trackHooks := schema.Track{}.Hooks()
	track.Hooks[0] = trackMixinHooks0[0]
	track.Hooks[1] = trackMixinHooks1[0]
	track.Hooks[2] = trackHooks[0]
	trackMixinInters0 := trackMixin[0].Interceptors()
	track.Interceptors[0] = trackMixinInters0[0]
	trackMixinFields0 := trackMixin[0].Fields()
	_ = trackMixinFields0
	trackMixinFields1 := trackMixin[1].Fields()
	_ = trackMixinFields1
	trackMixinFields2 := trackMixin[2].Fields()
	_ = trackMixinFields2
	trackFields := schema.Track{}.Fields()
	_ = trackFields
	// trackDescStatus is the schema descriptor for status field.
	trackDescStatus := trackMixinFields0[1].Descriptor()
	// track.DefaultStatus holds the default value on creation for the status field.
	track.DefaultStatus = trackDescStatus.Default.(int8)
	// trackDescVersion is the schema descriptor for version field.
	trackDescVersion := trackMixinFields1[0].Descriptor()
	// track.DefaultVersion holds the default value on creation for the version field.
	track.DefaultVersion = trackDescVersion.Default.(int)
	// trackDescCreatedAt is the schema descriptor for created_at field.
	trackDescCreatedAt := trackMixinFields2[0].Descriptor()
	// track.DefaultCreatedAt holds the default value on creation for the created_at field.
	track.DefaultCreatedAt = trackDescCreatedAt.Default.(func() time.Time)
	// trackDescUpdatedAt is the schema descriptor for updated_at field.
	trackDescUpdatedAt := trackMixinFields2[1].Descriptor()
	// track.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	track.DefaultUpdatedAt = trackDescUpdatedAt.Default.(func() time.Time)
	// track.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	track.UpdateDefaultUpdatedAt = trackDescUpdatedAt.UpdateDefault.(func() time.Time)
	userMixin := schema.User{}.Mixin()
	userMixinHooks0 := userMixin[0].Hooks()
	userMixinHooks1 := userMixin[1].Hooks()
	user.Hooks[0] = userMixinHooks0[0]
	user.Hooks[1] = userMixinHooks1[0]
	userMixinInters0 := userMixin[0].Interceptors()
	user.Interceptors[0] = userMixinInters0[0]
	userMixinFields0 := userMixin[0].Fields()
	_ = userMixinFields0
	userMixinFields1 := userMixin[1].Fields()
	_ = userMixinFields1
	userMixinFields2 := userMixin[2].Fields()
	_ = userMixinFields2
	userFields := schema.User{}.Fields()
	_ = userFields
	// userDescStatus is the schema descriptor for status field.
	userDescStatus := userMixinFields0[1].Descriptor()
	// user.DefaultStatus holds the default value on creation for the status field.
	user.DefaultStatus = userDescStatus.Default.(int8)
	// userDescVersion is the schema descriptor for version field.
	userDescVersion := userMixinFields1[0].Descriptor()
	// user.DefaultVersion holds the default value on creation for the version field.
	user.DefaultVersion = userDescVersion.Default.(int)
	// userDescCreatedAt is the schema descriptor for created_at field.
	userDescCreatedAt := userMixinFields2[0].Descriptor()
	// user.DefaultCreatedAt holds the default value on creation for the created_at field.
	user.DefaultCreatedAt = userDescCreatedAt.Default.(func() time.Time)
	// userDescUpdatedAt is the schema descriptor for updated_at field.
	userDescUpdatedAt := userMixinFields2[1].Descriptor()
	// user.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	user.DefaultUpdatedAt = userDescUpdatedAt.Default.(func() time.Time)
	// user.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	user.UpdateDefaultUpdatedAt = userDescUpdatedAt.UpdateDefault.(func() time.Time)
	userdataMixin := schema.UserData{}.Mixin()
	userdataMixinFields0 := userdataMixin[0].Fields()
	_ = userdataMixinFields0
	userdataFields := schema.UserData{}.Fields()
	_ = userdataFields
	// userdataDescCreatedAt is the schema descriptor for created_at field.
	userdataDescCreatedAt := userdataMixinFields0[0].Descriptor()
	// userdata.DefaultCreatedAt holds the default value on creation for the created_at field.
	userdata.DefaultCreatedAt = userdataDescCreatedAt.Default.(func() time.Time)
	// userdataDescUpdatedAt is the schema descriptor for updated_at field.
	userdataDescUpdatedAt := userdataMixinFields0[1].Descriptor()
	// userdata.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	userdata.DefaultUpdatedAt = userdataDescUpdatedAt.Default.(func() time.Time)
	// userdata.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	userdata.UpdateDefaultUpdatedAt = userdataDescUpdatedAt.UpdateDefault.(func() time.Time)
}

const (
	Version = "v0.14.4"                                         // Version of ent codegen.
	Sum     = "h1:/DhDraSLXIkBhyiVoJeSshr4ZYi7femzhj6/TckzZuI=" // Sum of ent codegen.
)
