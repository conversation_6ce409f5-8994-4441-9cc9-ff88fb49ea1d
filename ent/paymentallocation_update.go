// Code generated by ent, DO NOT EDIT.

package ent

import (
	"bcare/ent/billitem"
	"bcare/ent/installment"
	"bcare/ent/payment"
	"bcare/ent/paymentallocation"
	"bcare/ent/predicate"
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// PaymentAllocationUpdate is the builder for updating PaymentAllocation entities.
type PaymentAllocationUpdate struct {
	config
	hooks     []Hook
	mutation  *PaymentAllocationMutation
	modifiers []func(*sql.UpdateBuilder)
}

// Where appends a list predicates to the PaymentAllocationUpdate builder.
func (pau *PaymentAllocationUpdate) Where(ps ...predicate.PaymentAllocation) *PaymentAllocationUpdate {
	pau.mutation.Where(ps...)
	return pau
}

// SetDeletedAt sets the "deleted_at" field.
func (pau *PaymentAllocationUpdate) SetDeletedAt(t time.Time) *PaymentAllocationUpdate {
	pau.mutation.SetDeletedAt(t)
	return pau
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (pau *PaymentAllocationUpdate) SetNillableDeletedAt(t *time.Time) *PaymentAllocationUpdate {
	if t != nil {
		pau.SetDeletedAt(*t)
	}
	return pau
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (pau *PaymentAllocationUpdate) ClearDeletedAt() *PaymentAllocationUpdate {
	pau.mutation.ClearDeletedAt()
	return pau
}

// SetStatus sets the "status" field.
func (pau *PaymentAllocationUpdate) SetStatus(i int8) *PaymentAllocationUpdate {
	pau.mutation.ResetStatus()
	pau.mutation.SetStatus(i)
	return pau
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (pau *PaymentAllocationUpdate) SetNillableStatus(i *int8) *PaymentAllocationUpdate {
	if i != nil {
		pau.SetStatus(*i)
	}
	return pau
}

// AddStatus adds i to the "status" field.
func (pau *PaymentAllocationUpdate) AddStatus(i int8) *PaymentAllocationUpdate {
	pau.mutation.AddStatus(i)
	return pau
}

// SetVersion sets the "version" field.
func (pau *PaymentAllocationUpdate) SetVersion(i int) *PaymentAllocationUpdate {
	pau.mutation.ResetVersion()
	pau.mutation.SetVersion(i)
	return pau
}

// SetNillableVersion sets the "version" field if the given value is not nil.
func (pau *PaymentAllocationUpdate) SetNillableVersion(i *int) *PaymentAllocationUpdate {
	if i != nil {
		pau.SetVersion(*i)
	}
	return pau
}

// AddVersion adds i to the "version" field.
func (pau *PaymentAllocationUpdate) AddVersion(i int) *PaymentAllocationUpdate {
	pau.mutation.AddVersion(i)
	return pau
}

// SetUpdatedAt sets the "updated_at" field.
func (pau *PaymentAllocationUpdate) SetUpdatedAt(t time.Time) *PaymentAllocationUpdate {
	pau.mutation.SetUpdatedAt(t)
	return pau
}

// SetPaymentID sets the "payment_id" field.
func (pau *PaymentAllocationUpdate) SetPaymentID(i int) *PaymentAllocationUpdate {
	pau.mutation.SetPaymentID(i)
	return pau
}

// SetNillablePaymentID sets the "payment_id" field if the given value is not nil.
func (pau *PaymentAllocationUpdate) SetNillablePaymentID(i *int) *PaymentAllocationUpdate {
	if i != nil {
		pau.SetPaymentID(*i)
	}
	return pau
}

// SetBillID sets the "bill_id" field.
func (pau *PaymentAllocationUpdate) SetBillID(i int) *PaymentAllocationUpdate {
	pau.mutation.ResetBillID()
	pau.mutation.SetBillID(i)
	return pau
}

// SetNillableBillID sets the "bill_id" field if the given value is not nil.
func (pau *PaymentAllocationUpdate) SetNillableBillID(i *int) *PaymentAllocationUpdate {
	if i != nil {
		pau.SetBillID(*i)
	}
	return pau
}

// AddBillID adds i to the "bill_id" field.
func (pau *PaymentAllocationUpdate) AddBillID(i int) *PaymentAllocationUpdate {
	pau.mutation.AddBillID(i)
	return pau
}

// ClearBillID clears the value of the "bill_id" field.
func (pau *PaymentAllocationUpdate) ClearBillID() *PaymentAllocationUpdate {
	pau.mutation.ClearBillID()
	return pau
}

// SetUserID sets the "user_id" field.
func (pau *PaymentAllocationUpdate) SetUserID(i int) *PaymentAllocationUpdate {
	pau.mutation.ResetUserID()
	pau.mutation.SetUserID(i)
	return pau
}

// SetNillableUserID sets the "user_id" field if the given value is not nil.
func (pau *PaymentAllocationUpdate) SetNillableUserID(i *int) *PaymentAllocationUpdate {
	if i != nil {
		pau.SetUserID(*i)
	}
	return pau
}

// AddUserID adds i to the "user_id" field.
func (pau *PaymentAllocationUpdate) AddUserID(i int) *PaymentAllocationUpdate {
	pau.mutation.AddUserID(i)
	return pau
}

// ClearUserID clears the value of the "user_id" field.
func (pau *PaymentAllocationUpdate) ClearUserID() *PaymentAllocationUpdate {
	pau.mutation.ClearUserID()
	return pau
}

// SetBillItemID sets the "bill_item_id" field.
func (pau *PaymentAllocationUpdate) SetBillItemID(i int) *PaymentAllocationUpdate {
	pau.mutation.SetBillItemID(i)
	return pau
}

// SetNillableBillItemID sets the "bill_item_id" field if the given value is not nil.
func (pau *PaymentAllocationUpdate) SetNillableBillItemID(i *int) *PaymentAllocationUpdate {
	if i != nil {
		pau.SetBillItemID(*i)
	}
	return pau
}

// ClearBillItemID clears the value of the "bill_item_id" field.
func (pau *PaymentAllocationUpdate) ClearBillItemID() *PaymentAllocationUpdate {
	pau.mutation.ClearBillItemID()
	return pau
}

// SetInstallmentID sets the "installment_id" field.
func (pau *PaymentAllocationUpdate) SetInstallmentID(i int) *PaymentAllocationUpdate {
	pau.mutation.SetInstallmentID(i)
	return pau
}

// SetNillableInstallmentID sets the "installment_id" field if the given value is not nil.
func (pau *PaymentAllocationUpdate) SetNillableInstallmentID(i *int) *PaymentAllocationUpdate {
	if i != nil {
		pau.SetInstallmentID(*i)
	}
	return pau
}

// ClearInstallmentID clears the value of the "installment_id" field.
func (pau *PaymentAllocationUpdate) ClearInstallmentID() *PaymentAllocationUpdate {
	pau.mutation.ClearInstallmentID()
	return pau
}

// SetNote sets the "note" field.
func (pau *PaymentAllocationUpdate) SetNote(s string) *PaymentAllocationUpdate {
	pau.mutation.SetNote(s)
	return pau
}

// SetNillableNote sets the "note" field if the given value is not nil.
func (pau *PaymentAllocationUpdate) SetNillableNote(s *string) *PaymentAllocationUpdate {
	if s != nil {
		pau.SetNote(*s)
	}
	return pau
}

// ClearNote clears the value of the "note" field.
func (pau *PaymentAllocationUpdate) ClearNote() *PaymentAllocationUpdate {
	pau.mutation.ClearNote()
	return pau
}

// SetState sets the "state" field.
func (pau *PaymentAllocationUpdate) SetState(s string) *PaymentAllocationUpdate {
	pau.mutation.SetState(s)
	return pau
}

// SetNillableState sets the "state" field if the given value is not nil.
func (pau *PaymentAllocationUpdate) SetNillableState(s *string) *PaymentAllocationUpdate {
	if s != nil {
		pau.SetState(*s)
	}
	return pau
}

// ClearState clears the value of the "state" field.
func (pau *PaymentAllocationUpdate) ClearState() *PaymentAllocationUpdate {
	pau.mutation.ClearState()
	return pau
}

// SetAmount sets the "amount" field.
func (pau *PaymentAllocationUpdate) SetAmount(f float64) *PaymentAllocationUpdate {
	pau.mutation.ResetAmount()
	pau.mutation.SetAmount(f)
	return pau
}

// SetNillableAmount sets the "amount" field if the given value is not nil.
func (pau *PaymentAllocationUpdate) SetNillableAmount(f *float64) *PaymentAllocationUpdate {
	if f != nil {
		pau.SetAmount(*f)
	}
	return pau
}

// AddAmount adds f to the "amount" field.
func (pau *PaymentAllocationUpdate) AddAmount(f float64) *PaymentAllocationUpdate {
	pau.mutation.AddAmount(f)
	return pau
}

// SetPayment sets the "payment" edge to the Payment entity.
func (pau *PaymentAllocationUpdate) SetPayment(p *Payment) *PaymentAllocationUpdate {
	return pau.SetPaymentID(p.ID)
}

// SetBillItem sets the "bill_item" edge to the BillItem entity.
func (pau *PaymentAllocationUpdate) SetBillItem(b *BillItem) *PaymentAllocationUpdate {
	return pau.SetBillItemID(b.ID)
}

// SetInstallment sets the "installment" edge to the Installment entity.
func (pau *PaymentAllocationUpdate) SetInstallment(i *Installment) *PaymentAllocationUpdate {
	return pau.SetInstallmentID(i.ID)
}

// Mutation returns the PaymentAllocationMutation object of the builder.
func (pau *PaymentAllocationUpdate) Mutation() *PaymentAllocationMutation {
	return pau.mutation
}

// ClearPayment clears the "payment" edge to the Payment entity.
func (pau *PaymentAllocationUpdate) ClearPayment() *PaymentAllocationUpdate {
	pau.mutation.ClearPayment()
	return pau
}

// ClearBillItem clears the "bill_item" edge to the BillItem entity.
func (pau *PaymentAllocationUpdate) ClearBillItem() *PaymentAllocationUpdate {
	pau.mutation.ClearBillItem()
	return pau
}

// ClearInstallment clears the "installment" edge to the Installment entity.
func (pau *PaymentAllocationUpdate) ClearInstallment() *PaymentAllocationUpdate {
	pau.mutation.ClearInstallment()
	return pau
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (pau *PaymentAllocationUpdate) Save(ctx context.Context) (int, error) {
	if err := pau.defaults(); err != nil {
		return 0, err
	}
	return withHooks(ctx, pau.sqlSave, pau.mutation, pau.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (pau *PaymentAllocationUpdate) SaveX(ctx context.Context) int {
	affected, err := pau.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (pau *PaymentAllocationUpdate) Exec(ctx context.Context) error {
	_, err := pau.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (pau *PaymentAllocationUpdate) ExecX(ctx context.Context) {
	if err := pau.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (pau *PaymentAllocationUpdate) defaults() error {
	if _, ok := pau.mutation.UpdatedAt(); !ok {
		if paymentallocation.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized paymentallocation.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := paymentallocation.UpdateDefaultUpdatedAt()
		pau.mutation.SetUpdatedAt(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (pau *PaymentAllocationUpdate) check() error {
	if pau.mutation.PaymentCleared() && len(pau.mutation.PaymentIDs()) > 0 {
		return errors.New(`ent: clearing a required unique edge "PaymentAllocation.payment"`)
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (pau *PaymentAllocationUpdate) Modify(modifiers ...func(u *sql.UpdateBuilder)) *PaymentAllocationUpdate {
	pau.modifiers = append(pau.modifiers, modifiers...)
	return pau
}

func (pau *PaymentAllocationUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := pau.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(paymentallocation.Table, paymentallocation.Columns, sqlgraph.NewFieldSpec(paymentallocation.FieldID, field.TypeInt))
	if ps := pau.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := pau.mutation.DeletedAt(); ok {
		_spec.SetField(paymentallocation.FieldDeletedAt, field.TypeTime, value)
	}
	if pau.mutation.DeletedAtCleared() {
		_spec.ClearField(paymentallocation.FieldDeletedAt, field.TypeTime)
	}
	if value, ok := pau.mutation.Status(); ok {
		_spec.SetField(paymentallocation.FieldStatus, field.TypeInt8, value)
	}
	if value, ok := pau.mutation.AddedStatus(); ok {
		_spec.AddField(paymentallocation.FieldStatus, field.TypeInt8, value)
	}
	if value, ok := pau.mutation.Version(); ok {
		_spec.SetField(paymentallocation.FieldVersion, field.TypeInt, value)
	}
	if value, ok := pau.mutation.AddedVersion(); ok {
		_spec.AddField(paymentallocation.FieldVersion, field.TypeInt, value)
	}
	if value, ok := pau.mutation.UpdatedAt(); ok {
		_spec.SetField(paymentallocation.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := pau.mutation.BillID(); ok {
		_spec.SetField(paymentallocation.FieldBillID, field.TypeInt, value)
	}
	if value, ok := pau.mutation.AddedBillID(); ok {
		_spec.AddField(paymentallocation.FieldBillID, field.TypeInt, value)
	}
	if pau.mutation.BillIDCleared() {
		_spec.ClearField(paymentallocation.FieldBillID, field.TypeInt)
	}
	if value, ok := pau.mutation.UserID(); ok {
		_spec.SetField(paymentallocation.FieldUserID, field.TypeInt, value)
	}
	if value, ok := pau.mutation.AddedUserID(); ok {
		_spec.AddField(paymentallocation.FieldUserID, field.TypeInt, value)
	}
	if pau.mutation.UserIDCleared() {
		_spec.ClearField(paymentallocation.FieldUserID, field.TypeInt)
	}
	if value, ok := pau.mutation.Note(); ok {
		_spec.SetField(paymentallocation.FieldNote, field.TypeString, value)
	}
	if pau.mutation.NoteCleared() {
		_spec.ClearField(paymentallocation.FieldNote, field.TypeString)
	}
	if value, ok := pau.mutation.State(); ok {
		_spec.SetField(paymentallocation.FieldState, field.TypeString, value)
	}
	if pau.mutation.StateCleared() {
		_spec.ClearField(paymentallocation.FieldState, field.TypeString)
	}
	if value, ok := pau.mutation.Amount(); ok {
		_spec.SetField(paymentallocation.FieldAmount, field.TypeFloat64, value)
	}
	if value, ok := pau.mutation.AddedAmount(); ok {
		_spec.AddField(paymentallocation.FieldAmount, field.TypeFloat64, value)
	}
	if pau.mutation.PaymentCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   paymentallocation.PaymentTable,
			Columns: []string{paymentallocation.PaymentColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(payment.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := pau.mutation.PaymentIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   paymentallocation.PaymentTable,
			Columns: []string{paymentallocation.PaymentColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(payment.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if pau.mutation.BillItemCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   paymentallocation.BillItemTable,
			Columns: []string{paymentallocation.BillItemColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(billitem.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := pau.mutation.BillItemIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   paymentallocation.BillItemTable,
			Columns: []string{paymentallocation.BillItemColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(billitem.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if pau.mutation.InstallmentCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   paymentallocation.InstallmentTable,
			Columns: []string{paymentallocation.InstallmentColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(installment.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := pau.mutation.InstallmentIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   paymentallocation.InstallmentTable,
			Columns: []string{paymentallocation.InstallmentColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(installment.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_spec.AddModifiers(pau.modifiers...)
	if n, err = sqlgraph.UpdateNodes(ctx, pau.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{paymentallocation.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	pau.mutation.done = true
	return n, nil
}

// PaymentAllocationUpdateOne is the builder for updating a single PaymentAllocation entity.
type PaymentAllocationUpdateOne struct {
	config
	fields    []string
	hooks     []Hook
	mutation  *PaymentAllocationMutation
	modifiers []func(*sql.UpdateBuilder)
}

// SetDeletedAt sets the "deleted_at" field.
func (pauo *PaymentAllocationUpdateOne) SetDeletedAt(t time.Time) *PaymentAllocationUpdateOne {
	pauo.mutation.SetDeletedAt(t)
	return pauo
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (pauo *PaymentAllocationUpdateOne) SetNillableDeletedAt(t *time.Time) *PaymentAllocationUpdateOne {
	if t != nil {
		pauo.SetDeletedAt(*t)
	}
	return pauo
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (pauo *PaymentAllocationUpdateOne) ClearDeletedAt() *PaymentAllocationUpdateOne {
	pauo.mutation.ClearDeletedAt()
	return pauo
}

// SetStatus sets the "status" field.
func (pauo *PaymentAllocationUpdateOne) SetStatus(i int8) *PaymentAllocationUpdateOne {
	pauo.mutation.ResetStatus()
	pauo.mutation.SetStatus(i)
	return pauo
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (pauo *PaymentAllocationUpdateOne) SetNillableStatus(i *int8) *PaymentAllocationUpdateOne {
	if i != nil {
		pauo.SetStatus(*i)
	}
	return pauo
}

// AddStatus adds i to the "status" field.
func (pauo *PaymentAllocationUpdateOne) AddStatus(i int8) *PaymentAllocationUpdateOne {
	pauo.mutation.AddStatus(i)
	return pauo
}

// SetVersion sets the "version" field.
func (pauo *PaymentAllocationUpdateOne) SetVersion(i int) *PaymentAllocationUpdateOne {
	pauo.mutation.ResetVersion()
	pauo.mutation.SetVersion(i)
	return pauo
}

// SetNillableVersion sets the "version" field if the given value is not nil.
func (pauo *PaymentAllocationUpdateOne) SetNillableVersion(i *int) *PaymentAllocationUpdateOne {
	if i != nil {
		pauo.SetVersion(*i)
	}
	return pauo
}

// AddVersion adds i to the "version" field.
func (pauo *PaymentAllocationUpdateOne) AddVersion(i int) *PaymentAllocationUpdateOne {
	pauo.mutation.AddVersion(i)
	return pauo
}

// SetUpdatedAt sets the "updated_at" field.
func (pauo *PaymentAllocationUpdateOne) SetUpdatedAt(t time.Time) *PaymentAllocationUpdateOne {
	pauo.mutation.SetUpdatedAt(t)
	return pauo
}

// SetPaymentID sets the "payment_id" field.
func (pauo *PaymentAllocationUpdateOne) SetPaymentID(i int) *PaymentAllocationUpdateOne {
	pauo.mutation.SetPaymentID(i)
	return pauo
}

// SetNillablePaymentID sets the "payment_id" field if the given value is not nil.
func (pauo *PaymentAllocationUpdateOne) SetNillablePaymentID(i *int) *PaymentAllocationUpdateOne {
	if i != nil {
		pauo.SetPaymentID(*i)
	}
	return pauo
}

// SetBillID sets the "bill_id" field.
func (pauo *PaymentAllocationUpdateOne) SetBillID(i int) *PaymentAllocationUpdateOne {
	pauo.mutation.ResetBillID()
	pauo.mutation.SetBillID(i)
	return pauo
}

// SetNillableBillID sets the "bill_id" field if the given value is not nil.
func (pauo *PaymentAllocationUpdateOne) SetNillableBillID(i *int) *PaymentAllocationUpdateOne {
	if i != nil {
		pauo.SetBillID(*i)
	}
	return pauo
}

// AddBillID adds i to the "bill_id" field.
func (pauo *PaymentAllocationUpdateOne) AddBillID(i int) *PaymentAllocationUpdateOne {
	pauo.mutation.AddBillID(i)
	return pauo
}

// ClearBillID clears the value of the "bill_id" field.
func (pauo *PaymentAllocationUpdateOne) ClearBillID() *PaymentAllocationUpdateOne {
	pauo.mutation.ClearBillID()
	return pauo
}

// SetUserID sets the "user_id" field.
func (pauo *PaymentAllocationUpdateOne) SetUserID(i int) *PaymentAllocationUpdateOne {
	pauo.mutation.ResetUserID()
	pauo.mutation.SetUserID(i)
	return pauo
}

// SetNillableUserID sets the "user_id" field if the given value is not nil.
func (pauo *PaymentAllocationUpdateOne) SetNillableUserID(i *int) *PaymentAllocationUpdateOne {
	if i != nil {
		pauo.SetUserID(*i)
	}
	return pauo
}

// AddUserID adds i to the "user_id" field.
func (pauo *PaymentAllocationUpdateOne) AddUserID(i int) *PaymentAllocationUpdateOne {
	pauo.mutation.AddUserID(i)
	return pauo
}

// ClearUserID clears the value of the "user_id" field.
func (pauo *PaymentAllocationUpdateOne) ClearUserID() *PaymentAllocationUpdateOne {
	pauo.mutation.ClearUserID()
	return pauo
}

// SetBillItemID sets the "bill_item_id" field.
func (pauo *PaymentAllocationUpdateOne) SetBillItemID(i int) *PaymentAllocationUpdateOne {
	pauo.mutation.SetBillItemID(i)
	return pauo
}

// SetNillableBillItemID sets the "bill_item_id" field if the given value is not nil.
func (pauo *PaymentAllocationUpdateOne) SetNillableBillItemID(i *int) *PaymentAllocationUpdateOne {
	if i != nil {
		pauo.SetBillItemID(*i)
	}
	return pauo
}

// ClearBillItemID clears the value of the "bill_item_id" field.
func (pauo *PaymentAllocationUpdateOne) ClearBillItemID() *PaymentAllocationUpdateOne {
	pauo.mutation.ClearBillItemID()
	return pauo
}

// SetInstallmentID sets the "installment_id" field.
func (pauo *PaymentAllocationUpdateOne) SetInstallmentID(i int) *PaymentAllocationUpdateOne {
	pauo.mutation.SetInstallmentID(i)
	return pauo
}

// SetNillableInstallmentID sets the "installment_id" field if the given value is not nil.
func (pauo *PaymentAllocationUpdateOne) SetNillableInstallmentID(i *int) *PaymentAllocationUpdateOne {
	if i != nil {
		pauo.SetInstallmentID(*i)
	}
	return pauo
}

// ClearInstallmentID clears the value of the "installment_id" field.
func (pauo *PaymentAllocationUpdateOne) ClearInstallmentID() *PaymentAllocationUpdateOne {
	pauo.mutation.ClearInstallmentID()
	return pauo
}

// SetNote sets the "note" field.
func (pauo *PaymentAllocationUpdateOne) SetNote(s string) *PaymentAllocationUpdateOne {
	pauo.mutation.SetNote(s)
	return pauo
}

// SetNillableNote sets the "note" field if the given value is not nil.
func (pauo *PaymentAllocationUpdateOne) SetNillableNote(s *string) *PaymentAllocationUpdateOne {
	if s != nil {
		pauo.SetNote(*s)
	}
	return pauo
}

// ClearNote clears the value of the "note" field.
func (pauo *PaymentAllocationUpdateOne) ClearNote() *PaymentAllocationUpdateOne {
	pauo.mutation.ClearNote()
	return pauo
}

// SetState sets the "state" field.
func (pauo *PaymentAllocationUpdateOne) SetState(s string) *PaymentAllocationUpdateOne {
	pauo.mutation.SetState(s)
	return pauo
}

// SetNillableState sets the "state" field if the given value is not nil.
func (pauo *PaymentAllocationUpdateOne) SetNillableState(s *string) *PaymentAllocationUpdateOne {
	if s != nil {
		pauo.SetState(*s)
	}
	return pauo
}

// ClearState clears the value of the "state" field.
func (pauo *PaymentAllocationUpdateOne) ClearState() *PaymentAllocationUpdateOne {
	pauo.mutation.ClearState()
	return pauo
}

// SetAmount sets the "amount" field.
func (pauo *PaymentAllocationUpdateOne) SetAmount(f float64) *PaymentAllocationUpdateOne {
	pauo.mutation.ResetAmount()
	pauo.mutation.SetAmount(f)
	return pauo
}

// SetNillableAmount sets the "amount" field if the given value is not nil.
func (pauo *PaymentAllocationUpdateOne) SetNillableAmount(f *float64) *PaymentAllocationUpdateOne {
	if f != nil {
		pauo.SetAmount(*f)
	}
	return pauo
}

// AddAmount adds f to the "amount" field.
func (pauo *PaymentAllocationUpdateOne) AddAmount(f float64) *PaymentAllocationUpdateOne {
	pauo.mutation.AddAmount(f)
	return pauo
}

// SetPayment sets the "payment" edge to the Payment entity.
func (pauo *PaymentAllocationUpdateOne) SetPayment(p *Payment) *PaymentAllocationUpdateOne {
	return pauo.SetPaymentID(p.ID)
}

// SetBillItem sets the "bill_item" edge to the BillItem entity.
func (pauo *PaymentAllocationUpdateOne) SetBillItem(b *BillItem) *PaymentAllocationUpdateOne {
	return pauo.SetBillItemID(b.ID)
}

// SetInstallment sets the "installment" edge to the Installment entity.
func (pauo *PaymentAllocationUpdateOne) SetInstallment(i *Installment) *PaymentAllocationUpdateOne {
	return pauo.SetInstallmentID(i.ID)
}

// Mutation returns the PaymentAllocationMutation object of the builder.
func (pauo *PaymentAllocationUpdateOne) Mutation() *PaymentAllocationMutation {
	return pauo.mutation
}

// ClearPayment clears the "payment" edge to the Payment entity.
func (pauo *PaymentAllocationUpdateOne) ClearPayment() *PaymentAllocationUpdateOne {
	pauo.mutation.ClearPayment()
	return pauo
}

// ClearBillItem clears the "bill_item" edge to the BillItem entity.
func (pauo *PaymentAllocationUpdateOne) ClearBillItem() *PaymentAllocationUpdateOne {
	pauo.mutation.ClearBillItem()
	return pauo
}

// ClearInstallment clears the "installment" edge to the Installment entity.
func (pauo *PaymentAllocationUpdateOne) ClearInstallment() *PaymentAllocationUpdateOne {
	pauo.mutation.ClearInstallment()
	return pauo
}

// Where appends a list predicates to the PaymentAllocationUpdate builder.
func (pauo *PaymentAllocationUpdateOne) Where(ps ...predicate.PaymentAllocation) *PaymentAllocationUpdateOne {
	pauo.mutation.Where(ps...)
	return pauo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (pauo *PaymentAllocationUpdateOne) Select(field string, fields ...string) *PaymentAllocationUpdateOne {
	pauo.fields = append([]string{field}, fields...)
	return pauo
}

// Save executes the query and returns the updated PaymentAllocation entity.
func (pauo *PaymentAllocationUpdateOne) Save(ctx context.Context) (*PaymentAllocation, error) {
	if err := pauo.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, pauo.sqlSave, pauo.mutation, pauo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (pauo *PaymentAllocationUpdateOne) SaveX(ctx context.Context) *PaymentAllocation {
	node, err := pauo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (pauo *PaymentAllocationUpdateOne) Exec(ctx context.Context) error {
	_, err := pauo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (pauo *PaymentAllocationUpdateOne) ExecX(ctx context.Context) {
	if err := pauo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (pauo *PaymentAllocationUpdateOne) defaults() error {
	if _, ok := pauo.mutation.UpdatedAt(); !ok {
		if paymentallocation.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized paymentallocation.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := paymentallocation.UpdateDefaultUpdatedAt()
		pauo.mutation.SetUpdatedAt(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (pauo *PaymentAllocationUpdateOne) check() error {
	if pauo.mutation.PaymentCleared() && len(pauo.mutation.PaymentIDs()) > 0 {
		return errors.New(`ent: clearing a required unique edge "PaymentAllocation.payment"`)
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (pauo *PaymentAllocationUpdateOne) Modify(modifiers ...func(u *sql.UpdateBuilder)) *PaymentAllocationUpdateOne {
	pauo.modifiers = append(pauo.modifiers, modifiers...)
	return pauo
}

func (pauo *PaymentAllocationUpdateOne) sqlSave(ctx context.Context) (_node *PaymentAllocation, err error) {
	if err := pauo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(paymentallocation.Table, paymentallocation.Columns, sqlgraph.NewFieldSpec(paymentallocation.FieldID, field.TypeInt))
	id, ok := pauo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "PaymentAllocation.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := pauo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, paymentallocation.FieldID)
		for _, f := range fields {
			if !paymentallocation.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != paymentallocation.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := pauo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := pauo.mutation.DeletedAt(); ok {
		_spec.SetField(paymentallocation.FieldDeletedAt, field.TypeTime, value)
	}
	if pauo.mutation.DeletedAtCleared() {
		_spec.ClearField(paymentallocation.FieldDeletedAt, field.TypeTime)
	}
	if value, ok := pauo.mutation.Status(); ok {
		_spec.SetField(paymentallocation.FieldStatus, field.TypeInt8, value)
	}
	if value, ok := pauo.mutation.AddedStatus(); ok {
		_spec.AddField(paymentallocation.FieldStatus, field.TypeInt8, value)
	}
	if value, ok := pauo.mutation.Version(); ok {
		_spec.SetField(paymentallocation.FieldVersion, field.TypeInt, value)
	}
	if value, ok := pauo.mutation.AddedVersion(); ok {
		_spec.AddField(paymentallocation.FieldVersion, field.TypeInt, value)
	}
	if value, ok := pauo.mutation.UpdatedAt(); ok {
		_spec.SetField(paymentallocation.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := pauo.mutation.BillID(); ok {
		_spec.SetField(paymentallocation.FieldBillID, field.TypeInt, value)
	}
	if value, ok := pauo.mutation.AddedBillID(); ok {
		_spec.AddField(paymentallocation.FieldBillID, field.TypeInt, value)
	}
	if pauo.mutation.BillIDCleared() {
		_spec.ClearField(paymentallocation.FieldBillID, field.TypeInt)
	}
	if value, ok := pauo.mutation.UserID(); ok {
		_spec.SetField(paymentallocation.FieldUserID, field.TypeInt, value)
	}
	if value, ok := pauo.mutation.AddedUserID(); ok {
		_spec.AddField(paymentallocation.FieldUserID, field.TypeInt, value)
	}
	if pauo.mutation.UserIDCleared() {
		_spec.ClearField(paymentallocation.FieldUserID, field.TypeInt)
	}
	if value, ok := pauo.mutation.Note(); ok {
		_spec.SetField(paymentallocation.FieldNote, field.TypeString, value)
	}
	if pauo.mutation.NoteCleared() {
		_spec.ClearField(paymentallocation.FieldNote, field.TypeString)
	}
	if value, ok := pauo.mutation.State(); ok {
		_spec.SetField(paymentallocation.FieldState, field.TypeString, value)
	}
	if pauo.mutation.StateCleared() {
		_spec.ClearField(paymentallocation.FieldState, field.TypeString)
	}
	if value, ok := pauo.mutation.Amount(); ok {
		_spec.SetField(paymentallocation.FieldAmount, field.TypeFloat64, value)
	}
	if value, ok := pauo.mutation.AddedAmount(); ok {
		_spec.AddField(paymentallocation.FieldAmount, field.TypeFloat64, value)
	}
	if pauo.mutation.PaymentCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   paymentallocation.PaymentTable,
			Columns: []string{paymentallocation.PaymentColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(payment.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := pauo.mutation.PaymentIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   paymentallocation.PaymentTable,
			Columns: []string{paymentallocation.PaymentColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(payment.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if pauo.mutation.BillItemCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   paymentallocation.BillItemTable,
			Columns: []string{paymentallocation.BillItemColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(billitem.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := pauo.mutation.BillItemIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   paymentallocation.BillItemTable,
			Columns: []string{paymentallocation.BillItemColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(billitem.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if pauo.mutation.InstallmentCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   paymentallocation.InstallmentTable,
			Columns: []string{paymentallocation.InstallmentColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(installment.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := pauo.mutation.InstallmentIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   paymentallocation.InstallmentTable,
			Columns: []string{paymentallocation.InstallmentColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(installment.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_spec.AddModifiers(pauo.modifiers...)
	_node = &PaymentAllocation{config: pauo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, pauo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{paymentallocation.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	pauo.mutation.done = true
	return _node, nil
}
