// Code generated by ent, DO NOT EDIT.

package ent

import (
	"bcare/ent/deal"
	"bcare/ent/dealuser"
	"bcare/ent/dealuserrating"
	"bcare/ent/predicate"
	"bcare/ent/user"
	"context"
	"database/sql/driver"
	"fmt"
	"math"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// DealUserQuery is the builder for querying DealUser entities.
type DealUserQuery struct {
	config
	ctx         *QueryContext
	order       []dealuser.OrderOption
	inters      []Interceptor
	predicates  []predicate.DealUser
	withDeal    *DealQuery
	withUser    *UserQuery
	withRatings *DealUserRatingQuery
	modifiers   []func(*sql.Selector)
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the DealUserQuery builder.
func (duq *DealUserQuery) Where(ps ...predicate.DealUser) *DealUserQuery {
	duq.predicates = append(duq.predicates, ps...)
	return duq
}

// Limit the number of records to be returned by this query.
func (duq *DealUserQuery) Limit(limit int) *DealUserQuery {
	duq.ctx.Limit = &limit
	return duq
}

// Offset to start from.
func (duq *DealUserQuery) Offset(offset int) *DealUserQuery {
	duq.ctx.Offset = &offset
	return duq
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (duq *DealUserQuery) Unique(unique bool) *DealUserQuery {
	duq.ctx.Unique = &unique
	return duq
}

// Order specifies how the records should be ordered.
func (duq *DealUserQuery) Order(o ...dealuser.OrderOption) *DealUserQuery {
	duq.order = append(duq.order, o...)
	return duq
}

// QueryDeal chains the current query on the "deal" edge.
func (duq *DealUserQuery) QueryDeal() *DealQuery {
	query := (&DealClient{config: duq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := duq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := duq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(dealuser.Table, dealuser.FieldID, selector),
			sqlgraph.To(deal.Table, deal.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, dealuser.DealTable, dealuser.DealColumn),
		)
		fromU = sqlgraph.SetNeighbors(duq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// QueryUser chains the current query on the "user" edge.
func (duq *DealUserQuery) QueryUser() *UserQuery {
	query := (&UserClient{config: duq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := duq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := duq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(dealuser.Table, dealuser.FieldID, selector),
			sqlgraph.To(user.Table, user.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, dealuser.UserTable, dealuser.UserColumn),
		)
		fromU = sqlgraph.SetNeighbors(duq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// QueryRatings chains the current query on the "ratings" edge.
func (duq *DealUserQuery) QueryRatings() *DealUserRatingQuery {
	query := (&DealUserRatingClient{config: duq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := duq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := duq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(dealuser.Table, dealuser.FieldID, selector),
			sqlgraph.To(dealuserrating.Table, dealuserrating.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, dealuser.RatingsTable, dealuser.RatingsColumn),
		)
		fromU = sqlgraph.SetNeighbors(duq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// First returns the first DealUser entity from the query.
// Returns a *NotFoundError when no DealUser was found.
func (duq *DealUserQuery) First(ctx context.Context) (*DealUser, error) {
	nodes, err := duq.Limit(1).All(setContextOp(ctx, duq.ctx, ent.OpQueryFirst))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{dealuser.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (duq *DealUserQuery) FirstX(ctx context.Context) *DealUser {
	node, err := duq.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first DealUser ID from the query.
// Returns a *NotFoundError when no DealUser ID was found.
func (duq *DealUserQuery) FirstID(ctx context.Context) (id int, err error) {
	var ids []int
	if ids, err = duq.Limit(1).IDs(setContextOp(ctx, duq.ctx, ent.OpQueryFirstID)); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{dealuser.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (duq *DealUserQuery) FirstIDX(ctx context.Context) int {
	id, err := duq.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single DealUser entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one DealUser entity is found.
// Returns a *NotFoundError when no DealUser entities are found.
func (duq *DealUserQuery) Only(ctx context.Context) (*DealUser, error) {
	nodes, err := duq.Limit(2).All(setContextOp(ctx, duq.ctx, ent.OpQueryOnly))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{dealuser.Label}
	default:
		return nil, &NotSingularError{dealuser.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (duq *DealUserQuery) OnlyX(ctx context.Context) *DealUser {
	node, err := duq.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only DealUser ID in the query.
// Returns a *NotSingularError when more than one DealUser ID is found.
// Returns a *NotFoundError when no entities are found.
func (duq *DealUserQuery) OnlyID(ctx context.Context) (id int, err error) {
	var ids []int
	if ids, err = duq.Limit(2).IDs(setContextOp(ctx, duq.ctx, ent.OpQueryOnlyID)); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{dealuser.Label}
	default:
		err = &NotSingularError{dealuser.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (duq *DealUserQuery) OnlyIDX(ctx context.Context) int {
	id, err := duq.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of DealUsers.
func (duq *DealUserQuery) All(ctx context.Context) ([]*DealUser, error) {
	ctx = setContextOp(ctx, duq.ctx, ent.OpQueryAll)
	if err := duq.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*DealUser, *DealUserQuery]()
	return withInterceptors[[]*DealUser](ctx, duq, qr, duq.inters)
}

// AllX is like All, but panics if an error occurs.
func (duq *DealUserQuery) AllX(ctx context.Context) []*DealUser {
	nodes, err := duq.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of DealUser IDs.
func (duq *DealUserQuery) IDs(ctx context.Context) (ids []int, err error) {
	if duq.ctx.Unique == nil && duq.path != nil {
		duq.Unique(true)
	}
	ctx = setContextOp(ctx, duq.ctx, ent.OpQueryIDs)
	if err = duq.Select(dealuser.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (duq *DealUserQuery) IDsX(ctx context.Context) []int {
	ids, err := duq.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (duq *DealUserQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, duq.ctx, ent.OpQueryCount)
	if err := duq.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, duq, querierCount[*DealUserQuery](), duq.inters)
}

// CountX is like Count, but panics if an error occurs.
func (duq *DealUserQuery) CountX(ctx context.Context) int {
	count, err := duq.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (duq *DealUserQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, duq.ctx, ent.OpQueryExist)
	switch _, err := duq.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("ent: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (duq *DealUserQuery) ExistX(ctx context.Context) bool {
	exist, err := duq.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the DealUserQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (duq *DealUserQuery) Clone() *DealUserQuery {
	if duq == nil {
		return nil
	}
	return &DealUserQuery{
		config:      duq.config,
		ctx:         duq.ctx.Clone(),
		order:       append([]dealuser.OrderOption{}, duq.order...),
		inters:      append([]Interceptor{}, duq.inters...),
		predicates:  append([]predicate.DealUser{}, duq.predicates...),
		withDeal:    duq.withDeal.Clone(),
		withUser:    duq.withUser.Clone(),
		withRatings: duq.withRatings.Clone(),
		// clone intermediate query.
		sql:       duq.sql.Clone(),
		path:      duq.path,
		modifiers: append([]func(*sql.Selector){}, duq.modifiers...),
	}
}

// WithDeal tells the query-builder to eager-load the nodes that are connected to
// the "deal" edge. The optional arguments are used to configure the query builder of the edge.
func (duq *DealUserQuery) WithDeal(opts ...func(*DealQuery)) *DealUserQuery {
	query := (&DealClient{config: duq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	duq.withDeal = query
	return duq
}

// WithUser tells the query-builder to eager-load the nodes that are connected to
// the "user" edge. The optional arguments are used to configure the query builder of the edge.
func (duq *DealUserQuery) WithUser(opts ...func(*UserQuery)) *DealUserQuery {
	query := (&UserClient{config: duq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	duq.withUser = query
	return duq
}

// WithRatings tells the query-builder to eager-load the nodes that are connected to
// the "ratings" edge. The optional arguments are used to configure the query builder of the edge.
func (duq *DealUserQuery) WithRatings(opts ...func(*DealUserRatingQuery)) *DealUserQuery {
	query := (&DealUserRatingClient{config: duq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	duq.withRatings = query
	return duq
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		DealID int `json:"deal_id,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.DealUser.Query().
//		GroupBy(dealuser.FieldDealID).
//		Aggregate(ent.Count()).
//		Scan(ctx, &v)
func (duq *DealUserQuery) GroupBy(field string, fields ...string) *DealUserGroupBy {
	duq.ctx.Fields = append([]string{field}, fields...)
	grbuild := &DealUserGroupBy{build: duq}
	grbuild.flds = &duq.ctx.Fields
	grbuild.label = dealuser.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		DealID int `json:"deal_id,omitempty"`
//	}
//
//	client.DealUser.Query().
//		Select(dealuser.FieldDealID).
//		Scan(ctx, &v)
func (duq *DealUserQuery) Select(fields ...string) *DealUserSelect {
	duq.ctx.Fields = append(duq.ctx.Fields, fields...)
	sbuild := &DealUserSelect{DealUserQuery: duq}
	sbuild.label = dealuser.Label
	sbuild.flds, sbuild.scan = &duq.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a DealUserSelect configured with the given aggregations.
func (duq *DealUserQuery) Aggregate(fns ...AggregateFunc) *DealUserSelect {
	return duq.Select().Aggregate(fns...)
}

func (duq *DealUserQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range duq.inters {
		if inter == nil {
			return fmt.Errorf("ent: uninitialized interceptor (forgotten import ent/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, duq); err != nil {
				return err
			}
		}
	}
	for _, f := range duq.ctx.Fields {
		if !dealuser.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
		}
	}
	if duq.path != nil {
		prev, err := duq.path(ctx)
		if err != nil {
			return err
		}
		duq.sql = prev
	}
	return nil
}

func (duq *DealUserQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*DealUser, error) {
	var (
		nodes       = []*DealUser{}
		_spec       = duq.querySpec()
		loadedTypes = [3]bool{
			duq.withDeal != nil,
			duq.withUser != nil,
			duq.withRatings != nil,
		}
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*DealUser).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &DealUser{config: duq.config}
		nodes = append(nodes, node)
		node.Edges.loadedTypes = loadedTypes
		return node.assignValues(columns, values)
	}
	if len(duq.modifiers) > 0 {
		_spec.Modifiers = duq.modifiers
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, duq.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	if query := duq.withDeal; query != nil {
		if err := duq.loadDeal(ctx, query, nodes, nil,
			func(n *DealUser, e *Deal) { n.Edges.Deal = e }); err != nil {
			return nil, err
		}
	}
	if query := duq.withUser; query != nil {
		if err := duq.loadUser(ctx, query, nodes, nil,
			func(n *DealUser, e *User) { n.Edges.User = e }); err != nil {
			return nil, err
		}
	}
	if query := duq.withRatings; query != nil {
		if err := duq.loadRatings(ctx, query, nodes,
			func(n *DealUser) { n.Edges.Ratings = []*DealUserRating{} },
			func(n *DealUser, e *DealUserRating) { n.Edges.Ratings = append(n.Edges.Ratings, e) }); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

func (duq *DealUserQuery) loadDeal(ctx context.Context, query *DealQuery, nodes []*DealUser, init func(*DealUser), assign func(*DealUser, *Deal)) error {
	ids := make([]int, 0, len(nodes))
	nodeids := make(map[int][]*DealUser)
	for i := range nodes {
		fk := nodes[i].DealID
		if _, ok := nodeids[fk]; !ok {
			ids = append(ids, fk)
		}
		nodeids[fk] = append(nodeids[fk], nodes[i])
	}
	if len(ids) == 0 {
		return nil
	}
	query.Where(deal.IDIn(ids...))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		nodes, ok := nodeids[n.ID]
		if !ok {
			return fmt.Errorf(`unexpected foreign-key "deal_id" returned %v`, n.ID)
		}
		for i := range nodes {
			assign(nodes[i], n)
		}
	}
	return nil
}
func (duq *DealUserQuery) loadUser(ctx context.Context, query *UserQuery, nodes []*DealUser, init func(*DealUser), assign func(*DealUser, *User)) error {
	ids := make([]int, 0, len(nodes))
	nodeids := make(map[int][]*DealUser)
	for i := range nodes {
		fk := nodes[i].UserID
		if _, ok := nodeids[fk]; !ok {
			ids = append(ids, fk)
		}
		nodeids[fk] = append(nodeids[fk], nodes[i])
	}
	if len(ids) == 0 {
		return nil
	}
	query.Where(user.IDIn(ids...))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		nodes, ok := nodeids[n.ID]
		if !ok {
			return fmt.Errorf(`unexpected foreign-key "user_id" returned %v`, n.ID)
		}
		for i := range nodes {
			assign(nodes[i], n)
		}
	}
	return nil
}
func (duq *DealUserQuery) loadRatings(ctx context.Context, query *DealUserRatingQuery, nodes []*DealUser, init func(*DealUser), assign func(*DealUser, *DealUserRating)) error {
	fks := make([]driver.Value, 0, len(nodes))
	nodeids := make(map[int]*DealUser)
	for i := range nodes {
		fks = append(fks, nodes[i].ID)
		nodeids[nodes[i].ID] = nodes[i]
		if init != nil {
			init(nodes[i])
		}
	}
	if len(query.ctx.Fields) > 0 {
		query.ctx.AppendFieldOnce(dealuserrating.FieldDealUserID)
	}
	query.Where(predicate.DealUserRating(func(s *sql.Selector) {
		s.Where(sql.InValues(s.C(dealuser.RatingsColumn), fks...))
	}))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		fk := n.DealUserID
		node, ok := nodeids[fk]
		if !ok {
			return fmt.Errorf(`unexpected referenced foreign-key "deal_user_id" returned %v for node %v`, fk, n.ID)
		}
		assign(node, n)
	}
	return nil
}

func (duq *DealUserQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := duq.querySpec()
	if len(duq.modifiers) > 0 {
		_spec.Modifiers = duq.modifiers
	}
	_spec.Node.Columns = duq.ctx.Fields
	if len(duq.ctx.Fields) > 0 {
		_spec.Unique = duq.ctx.Unique != nil && *duq.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, duq.driver, _spec)
}

func (duq *DealUserQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(dealuser.Table, dealuser.Columns, sqlgraph.NewFieldSpec(dealuser.FieldID, field.TypeInt))
	_spec.From = duq.sql
	if unique := duq.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if duq.path != nil {
		_spec.Unique = true
	}
	if fields := duq.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, dealuser.FieldID)
		for i := range fields {
			if fields[i] != dealuser.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
		if duq.withDeal != nil {
			_spec.Node.AddColumnOnce(dealuser.FieldDealID)
		}
		if duq.withUser != nil {
			_spec.Node.AddColumnOnce(dealuser.FieldUserID)
		}
	}
	if ps := duq.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := duq.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := duq.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := duq.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (duq *DealUserQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(duq.driver.Dialect())
	t1 := builder.Table(dealuser.Table)
	columns := duq.ctx.Fields
	if len(columns) == 0 {
		columns = dealuser.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if duq.sql != nil {
		selector = duq.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if duq.ctx.Unique != nil && *duq.ctx.Unique {
		selector.Distinct()
	}
	for _, m := range duq.modifiers {
		m(selector)
	}
	for _, p := range duq.predicates {
		p(selector)
	}
	for _, p := range duq.order {
		p(selector)
	}
	if offset := duq.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := duq.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// Modify adds a query modifier for attaching custom logic to queries.
func (duq *DealUserQuery) Modify(modifiers ...func(s *sql.Selector)) *DealUserSelect {
	duq.modifiers = append(duq.modifiers, modifiers...)
	return duq.Select()
}

// DealUserGroupBy is the group-by builder for DealUser entities.
type DealUserGroupBy struct {
	selector
	build *DealUserQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (dugb *DealUserGroupBy) Aggregate(fns ...AggregateFunc) *DealUserGroupBy {
	dugb.fns = append(dugb.fns, fns...)
	return dugb
}

// Scan applies the selector query and scans the result into the given value.
func (dugb *DealUserGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, dugb.build.ctx, ent.OpQueryGroupBy)
	if err := dugb.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*DealUserQuery, *DealUserGroupBy](ctx, dugb.build, dugb, dugb.build.inters, v)
}

func (dugb *DealUserGroupBy) sqlScan(ctx context.Context, root *DealUserQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(dugb.fns))
	for _, fn := range dugb.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*dugb.flds)+len(dugb.fns))
		for _, f := range *dugb.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*dugb.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := dugb.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// DealUserSelect is the builder for selecting fields of DealUser entities.
type DealUserSelect struct {
	*DealUserQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (dus *DealUserSelect) Aggregate(fns ...AggregateFunc) *DealUserSelect {
	dus.fns = append(dus.fns, fns...)
	return dus
}

// Scan applies the selector query and scans the result into the given value.
func (dus *DealUserSelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, dus.ctx, ent.OpQuerySelect)
	if err := dus.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*DealUserQuery, *DealUserSelect](ctx, dus.DealUserQuery, dus, dus.inters, v)
}

func (dus *DealUserSelect) sqlScan(ctx context.Context, root *DealUserQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(dus.fns))
	for _, fn := range dus.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*dus.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := dus.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// Modify adds a query modifier for attaching custom logic to queries.
func (dus *DealUserSelect) Modify(modifiers ...func(s *sql.Selector)) *DealUserSelect {
	dus.modifiers = append(dus.modifiers, modifiers...)
	return dus
}
