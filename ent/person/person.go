// Code generated by ent, DO NOT EDIT.

package person

import (
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

const (
	// Label holds the string label denoting the person type in the database.
	Label = "person"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldDeletedAt holds the string denoting the deleted_at field in the database.
	FieldDeletedAt = "deleted_at"
	// FieldStatus holds the string denoting the status field in the database.
	FieldStatus = "status"
	// FieldVersion holds the string denoting the version field in the database.
	FieldVersion = "version"
	// FieldCreatedAt holds the string denoting the created_at field in the database.
	FieldCreatedAt = "created_at"
	// FieldUpdatedAt holds the string denoting the updated_at field in the database.
	FieldUpdatedAt = "updated_at"
	// FieldFullName holds the string denoting the full_name field in the database.
	FieldFullName = "full_name"
	// FieldDateOfBirth holds the string denoting the date_of_birth field in the database.
	FieldDateOfBirth = "date_of_birth"
	// <PERSON><PERSON>ender holds the string denoting the gender field in the database.
	FieldGender = "gender"
	// FieldProvinceID holds the string denoting the province_id field in the database.
	FieldProvinceID = "province_id"
	// FieldDistrictID holds the string denoting the district_id field in the database.
	FieldDistrictID = "district_id"
	// FieldWardID holds the string denoting the ward_id field in the database.
	FieldWardID = "ward_id"
	// FieldAddressNumber holds the string denoting the address_number field in the database.
	FieldAddressNumber = "address_number"
	// FieldPhone holds the string denoting the phone field in the database.
	FieldPhone = "phone"
	// FieldEmail holds the string denoting the email field in the database.
	FieldEmail = "email"
	// FieldJobID holds the string denoting the job_id field in the database.
	FieldJobID = "job_id"
	// FieldUserID holds the string denoting the user_id field in the database.
	FieldUserID = "user_id"
	// FieldSourceID holds the string denoting the source_id field in the database.
	FieldSourceID = "source_id"
	// FieldPhoneConfirm holds the string denoting the phone_confirm field in the database.
	FieldPhoneConfirm = "phone_confirm"
	// FieldMailConfirm holds the string denoting the mail_confirm field in the database.
	FieldMailConfirm = "mail_confirm"
	// FieldPersonField holds the string denoting the person_field field in the database.
	FieldPersonField = "person_field"
	// EdgePersonStages holds the string denoting the person_stages edge name in mutations.
	EdgePersonStages = "person_stages"
	// EdgeDeals holds the string denoting the deals edge name in mutations.
	EdgeDeals = "deals"
	// EdgePlans holds the string denoting the plans edge name in mutations.
	EdgePlans = "plans"
	// EdgeCalls holds the string denoting the calls edge name in mutations.
	EdgeCalls = "calls"
	// EdgeTasks holds the string denoting the tasks edge name in mutations.
	EdgeTasks = "tasks"
	// EdgeIssues holds the string denoting the issues edge name in mutations.
	EdgeIssues = "issues"
	// EdgeAppointments holds the string denoting the appointments edge name in mutations.
	EdgeAppointments = "appointments"
	// EdgeTracks holds the string denoting the tracks edge name in mutations.
	EdgeTracks = "tracks"
	// EdgeMessageHistories holds the string denoting the message_histories edge name in mutations.
	EdgeMessageHistories = "message_histories"
	// EdgeAssignees holds the string denoting the assignees edge name in mutations.
	EdgeAssignees = "assignees"
	// EdgeJob holds the string denoting the job edge name in mutations.
	EdgeJob = "job"
	// EdgeSource holds the string denoting the source edge name in mutations.
	EdgeSource = "source"
	// EdgeCreator holds the string denoting the creator edge name in mutations.
	EdgeCreator = "creator"
	// EdgeReferredBy holds the string denoting the referred_by edge name in mutations.
	EdgeReferredBy = "referred_by"
	// EdgeAssignment holds the string denoting the assignment edge name in mutations.
	EdgeAssignment = "assignment"
	// Table holds the table name of the person in the database.
	Table = "person"
	// PersonStagesTable is the table that holds the person_stages relation/edge.
	PersonStagesTable = "person_stage"
	// PersonStagesInverseTable is the table name for the PersonStage entity.
	// It exists in this package in order to avoid circular dependency with the "personstage" package.
	PersonStagesInverseTable = "person_stage"
	// PersonStagesColumn is the table column denoting the person_stages relation/edge.
	PersonStagesColumn = "person_id"
	// DealsTable is the table that holds the deals relation/edge.
	DealsTable = "deal"
	// DealsInverseTable is the table name for the Deal entity.
	// It exists in this package in order to avoid circular dependency with the "deal" package.
	DealsInverseTable = "deal"
	// DealsColumn is the table column denoting the deals relation/edge.
	DealsColumn = "person_id"
	// PlansTable is the table that holds the plans relation/edge.
	PlansTable = "installment_plan"
	// PlansInverseTable is the table name for the InstallmentPlan entity.
	// It exists in this package in order to avoid circular dependency with the "installmentplan" package.
	PlansInverseTable = "installment_plan"
	// PlansColumn is the table column denoting the plans relation/edge.
	PlansColumn = "person_id"
	// CallsTable is the table that holds the calls relation/edge.
	CallsTable = "call"
	// CallsInverseTable is the table name for the Call entity.
	// It exists in this package in order to avoid circular dependency with the "call" package.
	CallsInverseTable = "call"
	// CallsColumn is the table column denoting the calls relation/edge.
	CallsColumn = "person_id"
	// TasksTable is the table that holds the tasks relation/edge.
	TasksTable = "task"
	// TasksInverseTable is the table name for the Task entity.
	// It exists in this package in order to avoid circular dependency with the "task" package.
	TasksInverseTable = "task"
	// TasksColumn is the table column denoting the tasks relation/edge.
	TasksColumn = "person_id"
	// IssuesTable is the table that holds the issues relation/edge.
	IssuesTable = "issue"
	// IssuesInverseTable is the table name for the Issue entity.
	// It exists in this package in order to avoid circular dependency with the "issue" package.
	IssuesInverseTable = "issue"
	// IssuesColumn is the table column denoting the issues relation/edge.
	IssuesColumn = "person_id"
	// AppointmentsTable is the table that holds the appointments relation/edge.
	AppointmentsTable = "appointment"
	// AppointmentsInverseTable is the table name for the Appointment entity.
	// It exists in this package in order to avoid circular dependency with the "appointment" package.
	AppointmentsInverseTable = "appointment"
	// AppointmentsColumn is the table column denoting the appointments relation/edge.
	AppointmentsColumn = "person_id"
	// TracksTable is the table that holds the tracks relation/edge.
	TracksTable = "track"
	// TracksInverseTable is the table name for the Track entity.
	// It exists in this package in order to avoid circular dependency with the "track" package.
	TracksInverseTable = "track"
	// TracksColumn is the table column denoting the tracks relation/edge.
	TracksColumn = "person_id"
	// MessageHistoriesTable is the table that holds the message_histories relation/edge.
	MessageHistoriesTable = "message_history"
	// MessageHistoriesInverseTable is the table name for the MessageHistory entity.
	// It exists in this package in order to avoid circular dependency with the "messagehistory" package.
	MessageHistoriesInverseTable = "message_history"
	// MessageHistoriesColumn is the table column denoting the message_histories relation/edge.
	MessageHistoriesColumn = "person_id"
	// AssigneesTable is the table that holds the assignees relation/edge. The primary key declared below.
	AssigneesTable = "person_assignment"
	// AssigneesInverseTable is the table name for the User entity.
	// It exists in this package in order to avoid circular dependency with the "user" package.
	AssigneesInverseTable = "user"
	// JobTable is the table that holds the job relation/edge.
	JobTable = "person"
	// JobInverseTable is the table name for the Term entity.
	// It exists in this package in order to avoid circular dependency with the "term" package.
	JobInverseTable = "term"
	// JobColumn is the table column denoting the job relation/edge.
	JobColumn = "job_id"
	// SourceTable is the table that holds the source relation/edge.
	SourceTable = "person"
	// SourceInverseTable is the table name for the Term entity.
	// It exists in this package in order to avoid circular dependency with the "term" package.
	SourceInverseTable = "term"
	// SourceColumn is the table column denoting the source relation/edge.
	SourceColumn = "source_id"
	// CreatorTable is the table that holds the creator relation/edge.
	CreatorTable = "person"
	// CreatorInverseTable is the table name for the User entity.
	// It exists in this package in order to avoid circular dependency with the "user" package.
	CreatorInverseTable = "user"
	// CreatorColumn is the table column denoting the creator relation/edge.
	CreatorColumn = "user_id"
	// ReferredByTable is the table that holds the referred_by relation/edge.
	ReferredByTable = "person_referral"
	// ReferredByInverseTable is the table name for the PersonReferral entity.
	// It exists in this package in order to avoid circular dependency with the "personreferral" package.
	ReferredByInverseTable = "person_referral"
	// ReferredByColumn is the table column denoting the referred_by relation/edge.
	ReferredByColumn = "referred_id"
	// AssignmentTable is the table that holds the assignment relation/edge.
	AssignmentTable = "person_assignment"
	// AssignmentInverseTable is the table name for the PersonAssignment entity.
	// It exists in this package in order to avoid circular dependency with the "personassignment" package.
	AssignmentInverseTable = "person_assignment"
	// AssignmentColumn is the table column denoting the assignment relation/edge.
	AssignmentColumn = "person_id"
)

// Columns holds all SQL columns for person fields.
var Columns = []string{
	FieldID,
	FieldDeletedAt,
	FieldStatus,
	FieldVersion,
	FieldCreatedAt,
	FieldUpdatedAt,
	FieldFullName,
	FieldDateOfBirth,
	FieldGender,
	FieldProvinceID,
	FieldDistrictID,
	FieldWardID,
	FieldAddressNumber,
	FieldPhone,
	FieldEmail,
	FieldJobID,
	FieldUserID,
	FieldSourceID,
	FieldPhoneConfirm,
	FieldMailConfirm,
	FieldPersonField,
}

var (
	// AssigneesPrimaryKey and AssigneesColumn2 are the table columns denoting the
	// primary key for the assignees relation (M2M).
	AssigneesPrimaryKey = []string{"person_id", "user_id"}
)

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

// Note that the variables below are initialized by the runtime
// package on the initialization of the application. Therefore,
// it should be imported in the main as follows:
//
//	import _ "bcare/ent/runtime"
var (
	Hooks        [2]ent.Hook
	Interceptors [1]ent.Interceptor
	// DefaultStatus holds the default value on creation for the "status" field.
	DefaultStatus int8
	// DefaultVersion holds the default value on creation for the "version" field.
	DefaultVersion int
	// DefaultCreatedAt holds the default value on creation for the "created_at" field.
	DefaultCreatedAt func() time.Time
	// DefaultUpdatedAt holds the default value on creation for the "updated_at" field.
	DefaultUpdatedAt func() time.Time
	// UpdateDefaultUpdatedAt holds the default value on update for the "updated_at" field.
	UpdateDefaultUpdatedAt func() time.Time
)

// OrderOption defines the ordering options for the Person queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByDeletedAt orders the results by the deleted_at field.
func ByDeletedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDeletedAt, opts...).ToFunc()
}

// ByStatus orders the results by the status field.
func ByStatus(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldStatus, opts...).ToFunc()
}

// ByVersion orders the results by the version field.
func ByVersion(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldVersion, opts...).ToFunc()
}

// ByCreatedAt orders the results by the created_at field.
func ByCreatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedAt, opts...).ToFunc()
}

// ByUpdatedAt orders the results by the updated_at field.
func ByUpdatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdatedAt, opts...).ToFunc()
}

// ByFullName orders the results by the full_name field.
func ByFullName(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldFullName, opts...).ToFunc()
}

// ByDateOfBirth orders the results by the date_of_birth field.
func ByDateOfBirth(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDateOfBirth, opts...).ToFunc()
}

// ByGender orders the results by the gender field.
func ByGender(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldGender, opts...).ToFunc()
}

// ByProvinceID orders the results by the province_id field.
func ByProvinceID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldProvinceID, opts...).ToFunc()
}

// ByDistrictID orders the results by the district_id field.
func ByDistrictID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDistrictID, opts...).ToFunc()
}

// ByWardID orders the results by the ward_id field.
func ByWardID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldWardID, opts...).ToFunc()
}

// ByAddressNumber orders the results by the address_number field.
func ByAddressNumber(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldAddressNumber, opts...).ToFunc()
}

// ByPhone orders the results by the phone field.
func ByPhone(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPhone, opts...).ToFunc()
}

// ByEmail orders the results by the email field.
func ByEmail(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldEmail, opts...).ToFunc()
}

// ByJobID orders the results by the job_id field.
func ByJobID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldJobID, opts...).ToFunc()
}

// ByUserID orders the results by the user_id field.
func ByUserID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUserID, opts...).ToFunc()
}

// BySourceID orders the results by the source_id field.
func BySourceID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldSourceID, opts...).ToFunc()
}

// ByPhoneConfirm orders the results by the phone_confirm field.
func ByPhoneConfirm(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPhoneConfirm, opts...).ToFunc()
}

// ByMailConfirm orders the results by the mail_confirm field.
func ByMailConfirm(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldMailConfirm, opts...).ToFunc()
}

// ByPersonStagesCount orders the results by person_stages count.
func ByPersonStagesCount(opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborsCount(s, newPersonStagesStep(), opts...)
	}
}

// ByPersonStages orders the results by person_stages terms.
func ByPersonStages(term sql.OrderTerm, terms ...sql.OrderTerm) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newPersonStagesStep(), append([]sql.OrderTerm{term}, terms...)...)
	}
}

// ByDealsCount orders the results by deals count.
func ByDealsCount(opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborsCount(s, newDealsStep(), opts...)
	}
}

// ByDeals orders the results by deals terms.
func ByDeals(term sql.OrderTerm, terms ...sql.OrderTerm) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newDealsStep(), append([]sql.OrderTerm{term}, terms...)...)
	}
}

// ByPlansCount orders the results by plans count.
func ByPlansCount(opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborsCount(s, newPlansStep(), opts...)
	}
}

// ByPlans orders the results by plans terms.
func ByPlans(term sql.OrderTerm, terms ...sql.OrderTerm) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newPlansStep(), append([]sql.OrderTerm{term}, terms...)...)
	}
}

// ByCallsCount orders the results by calls count.
func ByCallsCount(opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborsCount(s, newCallsStep(), opts...)
	}
}

// ByCalls orders the results by calls terms.
func ByCalls(term sql.OrderTerm, terms ...sql.OrderTerm) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newCallsStep(), append([]sql.OrderTerm{term}, terms...)...)
	}
}

// ByTasksCount orders the results by tasks count.
func ByTasksCount(opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborsCount(s, newTasksStep(), opts...)
	}
}

// ByTasks orders the results by tasks terms.
func ByTasks(term sql.OrderTerm, terms ...sql.OrderTerm) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newTasksStep(), append([]sql.OrderTerm{term}, terms...)...)
	}
}

// ByIssuesCount orders the results by issues count.
func ByIssuesCount(opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborsCount(s, newIssuesStep(), opts...)
	}
}

// ByIssues orders the results by issues terms.
func ByIssues(term sql.OrderTerm, terms ...sql.OrderTerm) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newIssuesStep(), append([]sql.OrderTerm{term}, terms...)...)
	}
}

// ByAppointmentsCount orders the results by appointments count.
func ByAppointmentsCount(opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborsCount(s, newAppointmentsStep(), opts...)
	}
}

// ByAppointments orders the results by appointments terms.
func ByAppointments(term sql.OrderTerm, terms ...sql.OrderTerm) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newAppointmentsStep(), append([]sql.OrderTerm{term}, terms...)...)
	}
}

// ByTracksCount orders the results by tracks count.
func ByTracksCount(opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborsCount(s, newTracksStep(), opts...)
	}
}

// ByTracks orders the results by tracks terms.
func ByTracks(term sql.OrderTerm, terms ...sql.OrderTerm) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newTracksStep(), append([]sql.OrderTerm{term}, terms...)...)
	}
}

// ByMessageHistoriesCount orders the results by message_histories count.
func ByMessageHistoriesCount(opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborsCount(s, newMessageHistoriesStep(), opts...)
	}
}

// ByMessageHistories orders the results by message_histories terms.
func ByMessageHistories(term sql.OrderTerm, terms ...sql.OrderTerm) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newMessageHistoriesStep(), append([]sql.OrderTerm{term}, terms...)...)
	}
}

// ByAssigneesCount orders the results by assignees count.
func ByAssigneesCount(opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborsCount(s, newAssigneesStep(), opts...)
	}
}

// ByAssignees orders the results by assignees terms.
func ByAssignees(term sql.OrderTerm, terms ...sql.OrderTerm) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newAssigneesStep(), append([]sql.OrderTerm{term}, terms...)...)
	}
}

// ByJobField orders the results by job field.
func ByJobField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newJobStep(), sql.OrderByField(field, opts...))
	}
}

// BySourceField orders the results by source field.
func BySourceField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newSourceStep(), sql.OrderByField(field, opts...))
	}
}

// ByCreatorField orders the results by creator field.
func ByCreatorField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newCreatorStep(), sql.OrderByField(field, opts...))
	}
}

// ByReferredByField orders the results by referred_by field.
func ByReferredByField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newReferredByStep(), sql.OrderByField(field, opts...))
	}
}

// ByAssignmentCount orders the results by assignment count.
func ByAssignmentCount(opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborsCount(s, newAssignmentStep(), opts...)
	}
}

// ByAssignment orders the results by assignment terms.
func ByAssignment(term sql.OrderTerm, terms ...sql.OrderTerm) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newAssignmentStep(), append([]sql.OrderTerm{term}, terms...)...)
	}
}
func newPersonStagesStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(PersonStagesInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.O2M, true, PersonStagesTable, PersonStagesColumn),
	)
}
func newDealsStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(DealsInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.O2M, true, DealsTable, DealsColumn),
	)
}
func newPlansStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(PlansInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.O2M, true, PlansTable, PlansColumn),
	)
}
func newCallsStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(CallsInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.O2M, true, CallsTable, CallsColumn),
	)
}
func newTasksStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(TasksInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.O2M, true, TasksTable, TasksColumn),
	)
}
func newIssuesStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(IssuesInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.O2M, true, IssuesTable, IssuesColumn),
	)
}
func newAppointmentsStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(AppointmentsInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.O2M, true, AppointmentsTable, AppointmentsColumn),
	)
}
func newTracksStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(TracksInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.O2M, true, TracksTable, TracksColumn),
	)
}
func newMessageHistoriesStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(MessageHistoriesInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.O2M, true, MessageHistoriesTable, MessageHistoriesColumn),
	)
}
func newAssigneesStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(AssigneesInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2M, false, AssigneesTable, AssigneesPrimaryKey...),
	)
}
func newJobStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(JobInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2O, false, JobTable, JobColumn),
	)
}
func newSourceStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(SourceInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2O, false, SourceTable, SourceColumn),
	)
}
func newCreatorStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(CreatorInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2O, false, CreatorTable, CreatorColumn),
	)
}
func newReferredByStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(ReferredByInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.O2O, false, ReferredByTable, ReferredByColumn),
	)
}
func newAssignmentStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(AssignmentInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.O2M, true, AssignmentTable, AssignmentColumn),
	)
}
