// Code generated by ent, DO NOT EDIT.

package person

import (
	"bcare/ent/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

// ID filters vertices based on their ID field.
func ID(id int) predicate.Person {
	return predicate.Person(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id int) predicate.Person {
	return predicate.Person(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id int) predicate.Person {
	return predicate.Person(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...int) predicate.Person {
	return predicate.Person(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...int) predicate.Person {
	return predicate.Person(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id int) predicate.Person {
	return predicate.Person(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id int) predicate.Person {
	return predicate.Person(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id int) predicate.Person {
	return predicate.Person(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id int) predicate.Person {
	return predicate.Person(sql.FieldLTE(FieldID, id))
}

// DeletedAt applies equality check predicate on the "deleted_at" field. It's identical to DeletedAtEQ.
func DeletedAt(v time.Time) predicate.Person {
	return predicate.Person(sql.FieldEQ(FieldDeletedAt, v))
}

// Status applies equality check predicate on the "status" field. It's identical to StatusEQ.
func Status(v int8) predicate.Person {
	return predicate.Person(sql.FieldEQ(FieldStatus, v))
}

// Version applies equality check predicate on the "version" field. It's identical to VersionEQ.
func Version(v int) predicate.Person {
	return predicate.Person(sql.FieldEQ(FieldVersion, v))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.Person {
	return predicate.Person(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.Person {
	return predicate.Person(sql.FieldEQ(FieldUpdatedAt, v))
}

// FullName applies equality check predicate on the "full_name" field. It's identical to FullNameEQ.
func FullName(v string) predicate.Person {
	return predicate.Person(sql.FieldEQ(FieldFullName, v))
}

// DateOfBirth applies equality check predicate on the "date_of_birth" field. It's identical to DateOfBirthEQ.
func DateOfBirth(v time.Time) predicate.Person {
	return predicate.Person(sql.FieldEQ(FieldDateOfBirth, v))
}

// Gender applies equality check predicate on the "gender" field. It's identical to GenderEQ.
func Gender(v string) predicate.Person {
	return predicate.Person(sql.FieldEQ(FieldGender, v))
}

// ProvinceID applies equality check predicate on the "province_id" field. It's identical to ProvinceIDEQ.
func ProvinceID(v int) predicate.Person {
	return predicate.Person(sql.FieldEQ(FieldProvinceID, v))
}

// DistrictID applies equality check predicate on the "district_id" field. It's identical to DistrictIDEQ.
func DistrictID(v int) predicate.Person {
	return predicate.Person(sql.FieldEQ(FieldDistrictID, v))
}

// WardID applies equality check predicate on the "ward_id" field. It's identical to WardIDEQ.
func WardID(v int) predicate.Person {
	return predicate.Person(sql.FieldEQ(FieldWardID, v))
}

// AddressNumber applies equality check predicate on the "address_number" field. It's identical to AddressNumberEQ.
func AddressNumber(v string) predicate.Person {
	return predicate.Person(sql.FieldEQ(FieldAddressNumber, v))
}

// Phone applies equality check predicate on the "phone" field. It's identical to PhoneEQ.
func Phone(v string) predicate.Person {
	return predicate.Person(sql.FieldEQ(FieldPhone, v))
}

// Email applies equality check predicate on the "email" field. It's identical to EmailEQ.
func Email(v string) predicate.Person {
	return predicate.Person(sql.FieldEQ(FieldEmail, v))
}

// JobID applies equality check predicate on the "job_id" field. It's identical to JobIDEQ.
func JobID(v int) predicate.Person {
	return predicate.Person(sql.FieldEQ(FieldJobID, v))
}

// UserID applies equality check predicate on the "user_id" field. It's identical to UserIDEQ.
func UserID(v int) predicate.Person {
	return predicate.Person(sql.FieldEQ(FieldUserID, v))
}

// SourceID applies equality check predicate on the "source_id" field. It's identical to SourceIDEQ.
func SourceID(v int) predicate.Person {
	return predicate.Person(sql.FieldEQ(FieldSourceID, v))
}

// PhoneConfirm applies equality check predicate on the "phone_confirm" field. It's identical to PhoneConfirmEQ.
func PhoneConfirm(v bool) predicate.Person {
	return predicate.Person(sql.FieldEQ(FieldPhoneConfirm, v))
}

// MailConfirm applies equality check predicate on the "mail_confirm" field. It's identical to MailConfirmEQ.
func MailConfirm(v bool) predicate.Person {
	return predicate.Person(sql.FieldEQ(FieldMailConfirm, v))
}

// DeletedAtEQ applies the EQ predicate on the "deleted_at" field.
func DeletedAtEQ(v time.Time) predicate.Person {
	return predicate.Person(sql.FieldEQ(FieldDeletedAt, v))
}

// DeletedAtNEQ applies the NEQ predicate on the "deleted_at" field.
func DeletedAtNEQ(v time.Time) predicate.Person {
	return predicate.Person(sql.FieldNEQ(FieldDeletedAt, v))
}

// DeletedAtIn applies the In predicate on the "deleted_at" field.
func DeletedAtIn(vs ...time.Time) predicate.Person {
	return predicate.Person(sql.FieldIn(FieldDeletedAt, vs...))
}

// DeletedAtNotIn applies the NotIn predicate on the "deleted_at" field.
func DeletedAtNotIn(vs ...time.Time) predicate.Person {
	return predicate.Person(sql.FieldNotIn(FieldDeletedAt, vs...))
}

// DeletedAtGT applies the GT predicate on the "deleted_at" field.
func DeletedAtGT(v time.Time) predicate.Person {
	return predicate.Person(sql.FieldGT(FieldDeletedAt, v))
}

// DeletedAtGTE applies the GTE predicate on the "deleted_at" field.
func DeletedAtGTE(v time.Time) predicate.Person {
	return predicate.Person(sql.FieldGTE(FieldDeletedAt, v))
}

// DeletedAtLT applies the LT predicate on the "deleted_at" field.
func DeletedAtLT(v time.Time) predicate.Person {
	return predicate.Person(sql.FieldLT(FieldDeletedAt, v))
}

// DeletedAtLTE applies the LTE predicate on the "deleted_at" field.
func DeletedAtLTE(v time.Time) predicate.Person {
	return predicate.Person(sql.FieldLTE(FieldDeletedAt, v))
}

// DeletedAtIsNil applies the IsNil predicate on the "deleted_at" field.
func DeletedAtIsNil() predicate.Person {
	return predicate.Person(sql.FieldIsNull(FieldDeletedAt))
}

// DeletedAtNotNil applies the NotNil predicate on the "deleted_at" field.
func DeletedAtNotNil() predicate.Person {
	return predicate.Person(sql.FieldNotNull(FieldDeletedAt))
}

// StatusEQ applies the EQ predicate on the "status" field.
func StatusEQ(v int8) predicate.Person {
	return predicate.Person(sql.FieldEQ(FieldStatus, v))
}

// StatusNEQ applies the NEQ predicate on the "status" field.
func StatusNEQ(v int8) predicate.Person {
	return predicate.Person(sql.FieldNEQ(FieldStatus, v))
}

// StatusIn applies the In predicate on the "status" field.
func StatusIn(vs ...int8) predicate.Person {
	return predicate.Person(sql.FieldIn(FieldStatus, vs...))
}

// StatusNotIn applies the NotIn predicate on the "status" field.
func StatusNotIn(vs ...int8) predicate.Person {
	return predicate.Person(sql.FieldNotIn(FieldStatus, vs...))
}

// StatusGT applies the GT predicate on the "status" field.
func StatusGT(v int8) predicate.Person {
	return predicate.Person(sql.FieldGT(FieldStatus, v))
}

// StatusGTE applies the GTE predicate on the "status" field.
func StatusGTE(v int8) predicate.Person {
	return predicate.Person(sql.FieldGTE(FieldStatus, v))
}

// StatusLT applies the LT predicate on the "status" field.
func StatusLT(v int8) predicate.Person {
	return predicate.Person(sql.FieldLT(FieldStatus, v))
}

// StatusLTE applies the LTE predicate on the "status" field.
func StatusLTE(v int8) predicate.Person {
	return predicate.Person(sql.FieldLTE(FieldStatus, v))
}

// VersionEQ applies the EQ predicate on the "version" field.
func VersionEQ(v int) predicate.Person {
	return predicate.Person(sql.FieldEQ(FieldVersion, v))
}

// VersionNEQ applies the NEQ predicate on the "version" field.
func VersionNEQ(v int) predicate.Person {
	return predicate.Person(sql.FieldNEQ(FieldVersion, v))
}

// VersionIn applies the In predicate on the "version" field.
func VersionIn(vs ...int) predicate.Person {
	return predicate.Person(sql.FieldIn(FieldVersion, vs...))
}

// VersionNotIn applies the NotIn predicate on the "version" field.
func VersionNotIn(vs ...int) predicate.Person {
	return predicate.Person(sql.FieldNotIn(FieldVersion, vs...))
}

// VersionGT applies the GT predicate on the "version" field.
func VersionGT(v int) predicate.Person {
	return predicate.Person(sql.FieldGT(FieldVersion, v))
}

// VersionGTE applies the GTE predicate on the "version" field.
func VersionGTE(v int) predicate.Person {
	return predicate.Person(sql.FieldGTE(FieldVersion, v))
}

// VersionLT applies the LT predicate on the "version" field.
func VersionLT(v int) predicate.Person {
	return predicate.Person(sql.FieldLT(FieldVersion, v))
}

// VersionLTE applies the LTE predicate on the "version" field.
func VersionLTE(v int) predicate.Person {
	return predicate.Person(sql.FieldLTE(FieldVersion, v))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.Person {
	return predicate.Person(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.Person {
	return predicate.Person(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.Person {
	return predicate.Person(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.Person {
	return predicate.Person(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.Person {
	return predicate.Person(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.Person {
	return predicate.Person(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.Person {
	return predicate.Person(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.Person {
	return predicate.Person(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.Person {
	return predicate.Person(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.Person {
	return predicate.Person(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.Person {
	return predicate.Person(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.Person {
	return predicate.Person(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.Person {
	return predicate.Person(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.Person {
	return predicate.Person(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.Person {
	return predicate.Person(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.Person {
	return predicate.Person(sql.FieldLTE(FieldUpdatedAt, v))
}

// FullNameEQ applies the EQ predicate on the "full_name" field.
func FullNameEQ(v string) predicate.Person {
	return predicate.Person(sql.FieldEQ(FieldFullName, v))
}

// FullNameNEQ applies the NEQ predicate on the "full_name" field.
func FullNameNEQ(v string) predicate.Person {
	return predicate.Person(sql.FieldNEQ(FieldFullName, v))
}

// FullNameIn applies the In predicate on the "full_name" field.
func FullNameIn(vs ...string) predicate.Person {
	return predicate.Person(sql.FieldIn(FieldFullName, vs...))
}

// FullNameNotIn applies the NotIn predicate on the "full_name" field.
func FullNameNotIn(vs ...string) predicate.Person {
	return predicate.Person(sql.FieldNotIn(FieldFullName, vs...))
}

// FullNameGT applies the GT predicate on the "full_name" field.
func FullNameGT(v string) predicate.Person {
	return predicate.Person(sql.FieldGT(FieldFullName, v))
}

// FullNameGTE applies the GTE predicate on the "full_name" field.
func FullNameGTE(v string) predicate.Person {
	return predicate.Person(sql.FieldGTE(FieldFullName, v))
}

// FullNameLT applies the LT predicate on the "full_name" field.
func FullNameLT(v string) predicate.Person {
	return predicate.Person(sql.FieldLT(FieldFullName, v))
}

// FullNameLTE applies the LTE predicate on the "full_name" field.
func FullNameLTE(v string) predicate.Person {
	return predicate.Person(sql.FieldLTE(FieldFullName, v))
}

// FullNameContains applies the Contains predicate on the "full_name" field.
func FullNameContains(v string) predicate.Person {
	return predicate.Person(sql.FieldContains(FieldFullName, v))
}

// FullNameHasPrefix applies the HasPrefix predicate on the "full_name" field.
func FullNameHasPrefix(v string) predicate.Person {
	return predicate.Person(sql.FieldHasPrefix(FieldFullName, v))
}

// FullNameHasSuffix applies the HasSuffix predicate on the "full_name" field.
func FullNameHasSuffix(v string) predicate.Person {
	return predicate.Person(sql.FieldHasSuffix(FieldFullName, v))
}

// FullNameEqualFold applies the EqualFold predicate on the "full_name" field.
func FullNameEqualFold(v string) predicate.Person {
	return predicate.Person(sql.FieldEqualFold(FieldFullName, v))
}

// FullNameContainsFold applies the ContainsFold predicate on the "full_name" field.
func FullNameContainsFold(v string) predicate.Person {
	return predicate.Person(sql.FieldContainsFold(FieldFullName, v))
}

// DateOfBirthEQ applies the EQ predicate on the "date_of_birth" field.
func DateOfBirthEQ(v time.Time) predicate.Person {
	return predicate.Person(sql.FieldEQ(FieldDateOfBirth, v))
}

// DateOfBirthNEQ applies the NEQ predicate on the "date_of_birth" field.
func DateOfBirthNEQ(v time.Time) predicate.Person {
	return predicate.Person(sql.FieldNEQ(FieldDateOfBirth, v))
}

// DateOfBirthIn applies the In predicate on the "date_of_birth" field.
func DateOfBirthIn(vs ...time.Time) predicate.Person {
	return predicate.Person(sql.FieldIn(FieldDateOfBirth, vs...))
}

// DateOfBirthNotIn applies the NotIn predicate on the "date_of_birth" field.
func DateOfBirthNotIn(vs ...time.Time) predicate.Person {
	return predicate.Person(sql.FieldNotIn(FieldDateOfBirth, vs...))
}

// DateOfBirthGT applies the GT predicate on the "date_of_birth" field.
func DateOfBirthGT(v time.Time) predicate.Person {
	return predicate.Person(sql.FieldGT(FieldDateOfBirth, v))
}

// DateOfBirthGTE applies the GTE predicate on the "date_of_birth" field.
func DateOfBirthGTE(v time.Time) predicate.Person {
	return predicate.Person(sql.FieldGTE(FieldDateOfBirth, v))
}

// DateOfBirthLT applies the LT predicate on the "date_of_birth" field.
func DateOfBirthLT(v time.Time) predicate.Person {
	return predicate.Person(sql.FieldLT(FieldDateOfBirth, v))
}

// DateOfBirthLTE applies the LTE predicate on the "date_of_birth" field.
func DateOfBirthLTE(v time.Time) predicate.Person {
	return predicate.Person(sql.FieldLTE(FieldDateOfBirth, v))
}

// DateOfBirthIsNil applies the IsNil predicate on the "date_of_birth" field.
func DateOfBirthIsNil() predicate.Person {
	return predicate.Person(sql.FieldIsNull(FieldDateOfBirth))
}

// DateOfBirthNotNil applies the NotNil predicate on the "date_of_birth" field.
func DateOfBirthNotNil() predicate.Person {
	return predicate.Person(sql.FieldNotNull(FieldDateOfBirth))
}

// GenderEQ applies the EQ predicate on the "gender" field.
func GenderEQ(v string) predicate.Person {
	return predicate.Person(sql.FieldEQ(FieldGender, v))
}

// GenderNEQ applies the NEQ predicate on the "gender" field.
func GenderNEQ(v string) predicate.Person {
	return predicate.Person(sql.FieldNEQ(FieldGender, v))
}

// GenderIn applies the In predicate on the "gender" field.
func GenderIn(vs ...string) predicate.Person {
	return predicate.Person(sql.FieldIn(FieldGender, vs...))
}

// GenderNotIn applies the NotIn predicate on the "gender" field.
func GenderNotIn(vs ...string) predicate.Person {
	return predicate.Person(sql.FieldNotIn(FieldGender, vs...))
}

// GenderGT applies the GT predicate on the "gender" field.
func GenderGT(v string) predicate.Person {
	return predicate.Person(sql.FieldGT(FieldGender, v))
}

// GenderGTE applies the GTE predicate on the "gender" field.
func GenderGTE(v string) predicate.Person {
	return predicate.Person(sql.FieldGTE(FieldGender, v))
}

// GenderLT applies the LT predicate on the "gender" field.
func GenderLT(v string) predicate.Person {
	return predicate.Person(sql.FieldLT(FieldGender, v))
}

// GenderLTE applies the LTE predicate on the "gender" field.
func GenderLTE(v string) predicate.Person {
	return predicate.Person(sql.FieldLTE(FieldGender, v))
}

// GenderContains applies the Contains predicate on the "gender" field.
func GenderContains(v string) predicate.Person {
	return predicate.Person(sql.FieldContains(FieldGender, v))
}

// GenderHasPrefix applies the HasPrefix predicate on the "gender" field.
func GenderHasPrefix(v string) predicate.Person {
	return predicate.Person(sql.FieldHasPrefix(FieldGender, v))
}

// GenderHasSuffix applies the HasSuffix predicate on the "gender" field.
func GenderHasSuffix(v string) predicate.Person {
	return predicate.Person(sql.FieldHasSuffix(FieldGender, v))
}

// GenderIsNil applies the IsNil predicate on the "gender" field.
func GenderIsNil() predicate.Person {
	return predicate.Person(sql.FieldIsNull(FieldGender))
}

// GenderNotNil applies the NotNil predicate on the "gender" field.
func GenderNotNil() predicate.Person {
	return predicate.Person(sql.FieldNotNull(FieldGender))
}

// GenderEqualFold applies the EqualFold predicate on the "gender" field.
func GenderEqualFold(v string) predicate.Person {
	return predicate.Person(sql.FieldEqualFold(FieldGender, v))
}

// GenderContainsFold applies the ContainsFold predicate on the "gender" field.
func GenderContainsFold(v string) predicate.Person {
	return predicate.Person(sql.FieldContainsFold(FieldGender, v))
}

// ProvinceIDEQ applies the EQ predicate on the "province_id" field.
func ProvinceIDEQ(v int) predicate.Person {
	return predicate.Person(sql.FieldEQ(FieldProvinceID, v))
}

// ProvinceIDNEQ applies the NEQ predicate on the "province_id" field.
func ProvinceIDNEQ(v int) predicate.Person {
	return predicate.Person(sql.FieldNEQ(FieldProvinceID, v))
}

// ProvinceIDIn applies the In predicate on the "province_id" field.
func ProvinceIDIn(vs ...int) predicate.Person {
	return predicate.Person(sql.FieldIn(FieldProvinceID, vs...))
}

// ProvinceIDNotIn applies the NotIn predicate on the "province_id" field.
func ProvinceIDNotIn(vs ...int) predicate.Person {
	return predicate.Person(sql.FieldNotIn(FieldProvinceID, vs...))
}

// ProvinceIDGT applies the GT predicate on the "province_id" field.
func ProvinceIDGT(v int) predicate.Person {
	return predicate.Person(sql.FieldGT(FieldProvinceID, v))
}

// ProvinceIDGTE applies the GTE predicate on the "province_id" field.
func ProvinceIDGTE(v int) predicate.Person {
	return predicate.Person(sql.FieldGTE(FieldProvinceID, v))
}

// ProvinceIDLT applies the LT predicate on the "province_id" field.
func ProvinceIDLT(v int) predicate.Person {
	return predicate.Person(sql.FieldLT(FieldProvinceID, v))
}

// ProvinceIDLTE applies the LTE predicate on the "province_id" field.
func ProvinceIDLTE(v int) predicate.Person {
	return predicate.Person(sql.FieldLTE(FieldProvinceID, v))
}

// ProvinceIDIsNil applies the IsNil predicate on the "province_id" field.
func ProvinceIDIsNil() predicate.Person {
	return predicate.Person(sql.FieldIsNull(FieldProvinceID))
}

// ProvinceIDNotNil applies the NotNil predicate on the "province_id" field.
func ProvinceIDNotNil() predicate.Person {
	return predicate.Person(sql.FieldNotNull(FieldProvinceID))
}

// DistrictIDEQ applies the EQ predicate on the "district_id" field.
func DistrictIDEQ(v int) predicate.Person {
	return predicate.Person(sql.FieldEQ(FieldDistrictID, v))
}

// DistrictIDNEQ applies the NEQ predicate on the "district_id" field.
func DistrictIDNEQ(v int) predicate.Person {
	return predicate.Person(sql.FieldNEQ(FieldDistrictID, v))
}

// DistrictIDIn applies the In predicate on the "district_id" field.
func DistrictIDIn(vs ...int) predicate.Person {
	return predicate.Person(sql.FieldIn(FieldDistrictID, vs...))
}

// DistrictIDNotIn applies the NotIn predicate on the "district_id" field.
func DistrictIDNotIn(vs ...int) predicate.Person {
	return predicate.Person(sql.FieldNotIn(FieldDistrictID, vs...))
}

// DistrictIDGT applies the GT predicate on the "district_id" field.
func DistrictIDGT(v int) predicate.Person {
	return predicate.Person(sql.FieldGT(FieldDistrictID, v))
}

// DistrictIDGTE applies the GTE predicate on the "district_id" field.
func DistrictIDGTE(v int) predicate.Person {
	return predicate.Person(sql.FieldGTE(FieldDistrictID, v))
}

// DistrictIDLT applies the LT predicate on the "district_id" field.
func DistrictIDLT(v int) predicate.Person {
	return predicate.Person(sql.FieldLT(FieldDistrictID, v))
}

// DistrictIDLTE applies the LTE predicate on the "district_id" field.
func DistrictIDLTE(v int) predicate.Person {
	return predicate.Person(sql.FieldLTE(FieldDistrictID, v))
}

// DistrictIDIsNil applies the IsNil predicate on the "district_id" field.
func DistrictIDIsNil() predicate.Person {
	return predicate.Person(sql.FieldIsNull(FieldDistrictID))
}

// DistrictIDNotNil applies the NotNil predicate on the "district_id" field.
func DistrictIDNotNil() predicate.Person {
	return predicate.Person(sql.FieldNotNull(FieldDistrictID))
}

// WardIDEQ applies the EQ predicate on the "ward_id" field.
func WardIDEQ(v int) predicate.Person {
	return predicate.Person(sql.FieldEQ(FieldWardID, v))
}

// WardIDNEQ applies the NEQ predicate on the "ward_id" field.
func WardIDNEQ(v int) predicate.Person {
	return predicate.Person(sql.FieldNEQ(FieldWardID, v))
}

// WardIDIn applies the In predicate on the "ward_id" field.
func WardIDIn(vs ...int) predicate.Person {
	return predicate.Person(sql.FieldIn(FieldWardID, vs...))
}

// WardIDNotIn applies the NotIn predicate on the "ward_id" field.
func WardIDNotIn(vs ...int) predicate.Person {
	return predicate.Person(sql.FieldNotIn(FieldWardID, vs...))
}

// WardIDGT applies the GT predicate on the "ward_id" field.
func WardIDGT(v int) predicate.Person {
	return predicate.Person(sql.FieldGT(FieldWardID, v))
}

// WardIDGTE applies the GTE predicate on the "ward_id" field.
func WardIDGTE(v int) predicate.Person {
	return predicate.Person(sql.FieldGTE(FieldWardID, v))
}

// WardIDLT applies the LT predicate on the "ward_id" field.
func WardIDLT(v int) predicate.Person {
	return predicate.Person(sql.FieldLT(FieldWardID, v))
}

// WardIDLTE applies the LTE predicate on the "ward_id" field.
func WardIDLTE(v int) predicate.Person {
	return predicate.Person(sql.FieldLTE(FieldWardID, v))
}

// WardIDIsNil applies the IsNil predicate on the "ward_id" field.
func WardIDIsNil() predicate.Person {
	return predicate.Person(sql.FieldIsNull(FieldWardID))
}

// WardIDNotNil applies the NotNil predicate on the "ward_id" field.
func WardIDNotNil() predicate.Person {
	return predicate.Person(sql.FieldNotNull(FieldWardID))
}

// AddressNumberEQ applies the EQ predicate on the "address_number" field.
func AddressNumberEQ(v string) predicate.Person {
	return predicate.Person(sql.FieldEQ(FieldAddressNumber, v))
}

// AddressNumberNEQ applies the NEQ predicate on the "address_number" field.
func AddressNumberNEQ(v string) predicate.Person {
	return predicate.Person(sql.FieldNEQ(FieldAddressNumber, v))
}

// AddressNumberIn applies the In predicate on the "address_number" field.
func AddressNumberIn(vs ...string) predicate.Person {
	return predicate.Person(sql.FieldIn(FieldAddressNumber, vs...))
}

// AddressNumberNotIn applies the NotIn predicate on the "address_number" field.
func AddressNumberNotIn(vs ...string) predicate.Person {
	return predicate.Person(sql.FieldNotIn(FieldAddressNumber, vs...))
}

// AddressNumberGT applies the GT predicate on the "address_number" field.
func AddressNumberGT(v string) predicate.Person {
	return predicate.Person(sql.FieldGT(FieldAddressNumber, v))
}

// AddressNumberGTE applies the GTE predicate on the "address_number" field.
func AddressNumberGTE(v string) predicate.Person {
	return predicate.Person(sql.FieldGTE(FieldAddressNumber, v))
}

// AddressNumberLT applies the LT predicate on the "address_number" field.
func AddressNumberLT(v string) predicate.Person {
	return predicate.Person(sql.FieldLT(FieldAddressNumber, v))
}

// AddressNumberLTE applies the LTE predicate on the "address_number" field.
func AddressNumberLTE(v string) predicate.Person {
	return predicate.Person(sql.FieldLTE(FieldAddressNumber, v))
}

// AddressNumberContains applies the Contains predicate on the "address_number" field.
func AddressNumberContains(v string) predicate.Person {
	return predicate.Person(sql.FieldContains(FieldAddressNumber, v))
}

// AddressNumberHasPrefix applies the HasPrefix predicate on the "address_number" field.
func AddressNumberHasPrefix(v string) predicate.Person {
	return predicate.Person(sql.FieldHasPrefix(FieldAddressNumber, v))
}

// AddressNumberHasSuffix applies the HasSuffix predicate on the "address_number" field.
func AddressNumberHasSuffix(v string) predicate.Person {
	return predicate.Person(sql.FieldHasSuffix(FieldAddressNumber, v))
}

// AddressNumberIsNil applies the IsNil predicate on the "address_number" field.
func AddressNumberIsNil() predicate.Person {
	return predicate.Person(sql.FieldIsNull(FieldAddressNumber))
}

// AddressNumberNotNil applies the NotNil predicate on the "address_number" field.
func AddressNumberNotNil() predicate.Person {
	return predicate.Person(sql.FieldNotNull(FieldAddressNumber))
}

// AddressNumberEqualFold applies the EqualFold predicate on the "address_number" field.
func AddressNumberEqualFold(v string) predicate.Person {
	return predicate.Person(sql.FieldEqualFold(FieldAddressNumber, v))
}

// AddressNumberContainsFold applies the ContainsFold predicate on the "address_number" field.
func AddressNumberContainsFold(v string) predicate.Person {
	return predicate.Person(sql.FieldContainsFold(FieldAddressNumber, v))
}

// PhoneEQ applies the EQ predicate on the "phone" field.
func PhoneEQ(v string) predicate.Person {
	return predicate.Person(sql.FieldEQ(FieldPhone, v))
}

// PhoneNEQ applies the NEQ predicate on the "phone" field.
func PhoneNEQ(v string) predicate.Person {
	return predicate.Person(sql.FieldNEQ(FieldPhone, v))
}

// PhoneIn applies the In predicate on the "phone" field.
func PhoneIn(vs ...string) predicate.Person {
	return predicate.Person(sql.FieldIn(FieldPhone, vs...))
}

// PhoneNotIn applies the NotIn predicate on the "phone" field.
func PhoneNotIn(vs ...string) predicate.Person {
	return predicate.Person(sql.FieldNotIn(FieldPhone, vs...))
}

// PhoneGT applies the GT predicate on the "phone" field.
func PhoneGT(v string) predicate.Person {
	return predicate.Person(sql.FieldGT(FieldPhone, v))
}

// PhoneGTE applies the GTE predicate on the "phone" field.
func PhoneGTE(v string) predicate.Person {
	return predicate.Person(sql.FieldGTE(FieldPhone, v))
}

// PhoneLT applies the LT predicate on the "phone" field.
func PhoneLT(v string) predicate.Person {
	return predicate.Person(sql.FieldLT(FieldPhone, v))
}

// PhoneLTE applies the LTE predicate on the "phone" field.
func PhoneLTE(v string) predicate.Person {
	return predicate.Person(sql.FieldLTE(FieldPhone, v))
}

// PhoneContains applies the Contains predicate on the "phone" field.
func PhoneContains(v string) predicate.Person {
	return predicate.Person(sql.FieldContains(FieldPhone, v))
}

// PhoneHasPrefix applies the HasPrefix predicate on the "phone" field.
func PhoneHasPrefix(v string) predicate.Person {
	return predicate.Person(sql.FieldHasPrefix(FieldPhone, v))
}

// PhoneHasSuffix applies the HasSuffix predicate on the "phone" field.
func PhoneHasSuffix(v string) predicate.Person {
	return predicate.Person(sql.FieldHasSuffix(FieldPhone, v))
}

// PhoneEqualFold applies the EqualFold predicate on the "phone" field.
func PhoneEqualFold(v string) predicate.Person {
	return predicate.Person(sql.FieldEqualFold(FieldPhone, v))
}

// PhoneContainsFold applies the ContainsFold predicate on the "phone" field.
func PhoneContainsFold(v string) predicate.Person {
	return predicate.Person(sql.FieldContainsFold(FieldPhone, v))
}

// EmailEQ applies the EQ predicate on the "email" field.
func EmailEQ(v string) predicate.Person {
	return predicate.Person(sql.FieldEQ(FieldEmail, v))
}

// EmailNEQ applies the NEQ predicate on the "email" field.
func EmailNEQ(v string) predicate.Person {
	return predicate.Person(sql.FieldNEQ(FieldEmail, v))
}

// EmailIn applies the In predicate on the "email" field.
func EmailIn(vs ...string) predicate.Person {
	return predicate.Person(sql.FieldIn(FieldEmail, vs...))
}

// EmailNotIn applies the NotIn predicate on the "email" field.
func EmailNotIn(vs ...string) predicate.Person {
	return predicate.Person(sql.FieldNotIn(FieldEmail, vs...))
}

// EmailGT applies the GT predicate on the "email" field.
func EmailGT(v string) predicate.Person {
	return predicate.Person(sql.FieldGT(FieldEmail, v))
}

// EmailGTE applies the GTE predicate on the "email" field.
func EmailGTE(v string) predicate.Person {
	return predicate.Person(sql.FieldGTE(FieldEmail, v))
}

// EmailLT applies the LT predicate on the "email" field.
func EmailLT(v string) predicate.Person {
	return predicate.Person(sql.FieldLT(FieldEmail, v))
}

// EmailLTE applies the LTE predicate on the "email" field.
func EmailLTE(v string) predicate.Person {
	return predicate.Person(sql.FieldLTE(FieldEmail, v))
}

// EmailContains applies the Contains predicate on the "email" field.
func EmailContains(v string) predicate.Person {
	return predicate.Person(sql.FieldContains(FieldEmail, v))
}

// EmailHasPrefix applies the HasPrefix predicate on the "email" field.
func EmailHasPrefix(v string) predicate.Person {
	return predicate.Person(sql.FieldHasPrefix(FieldEmail, v))
}

// EmailHasSuffix applies the HasSuffix predicate on the "email" field.
func EmailHasSuffix(v string) predicate.Person {
	return predicate.Person(sql.FieldHasSuffix(FieldEmail, v))
}

// EmailIsNil applies the IsNil predicate on the "email" field.
func EmailIsNil() predicate.Person {
	return predicate.Person(sql.FieldIsNull(FieldEmail))
}

// EmailNotNil applies the NotNil predicate on the "email" field.
func EmailNotNil() predicate.Person {
	return predicate.Person(sql.FieldNotNull(FieldEmail))
}

// EmailEqualFold applies the EqualFold predicate on the "email" field.
func EmailEqualFold(v string) predicate.Person {
	return predicate.Person(sql.FieldEqualFold(FieldEmail, v))
}

// EmailContainsFold applies the ContainsFold predicate on the "email" field.
func EmailContainsFold(v string) predicate.Person {
	return predicate.Person(sql.FieldContainsFold(FieldEmail, v))
}

// JobIDEQ applies the EQ predicate on the "job_id" field.
func JobIDEQ(v int) predicate.Person {
	return predicate.Person(sql.FieldEQ(FieldJobID, v))
}

// JobIDNEQ applies the NEQ predicate on the "job_id" field.
func JobIDNEQ(v int) predicate.Person {
	return predicate.Person(sql.FieldNEQ(FieldJobID, v))
}

// JobIDIn applies the In predicate on the "job_id" field.
func JobIDIn(vs ...int) predicate.Person {
	return predicate.Person(sql.FieldIn(FieldJobID, vs...))
}

// JobIDNotIn applies the NotIn predicate on the "job_id" field.
func JobIDNotIn(vs ...int) predicate.Person {
	return predicate.Person(sql.FieldNotIn(FieldJobID, vs...))
}

// JobIDIsNil applies the IsNil predicate on the "job_id" field.
func JobIDIsNil() predicate.Person {
	return predicate.Person(sql.FieldIsNull(FieldJobID))
}

// JobIDNotNil applies the NotNil predicate on the "job_id" field.
func JobIDNotNil() predicate.Person {
	return predicate.Person(sql.FieldNotNull(FieldJobID))
}

// UserIDEQ applies the EQ predicate on the "user_id" field.
func UserIDEQ(v int) predicate.Person {
	return predicate.Person(sql.FieldEQ(FieldUserID, v))
}

// UserIDNEQ applies the NEQ predicate on the "user_id" field.
func UserIDNEQ(v int) predicate.Person {
	return predicate.Person(sql.FieldNEQ(FieldUserID, v))
}

// UserIDIn applies the In predicate on the "user_id" field.
func UserIDIn(vs ...int) predicate.Person {
	return predicate.Person(sql.FieldIn(FieldUserID, vs...))
}

// UserIDNotIn applies the NotIn predicate on the "user_id" field.
func UserIDNotIn(vs ...int) predicate.Person {
	return predicate.Person(sql.FieldNotIn(FieldUserID, vs...))
}

// UserIDIsNil applies the IsNil predicate on the "user_id" field.
func UserIDIsNil() predicate.Person {
	return predicate.Person(sql.FieldIsNull(FieldUserID))
}

// UserIDNotNil applies the NotNil predicate on the "user_id" field.
func UserIDNotNil() predicate.Person {
	return predicate.Person(sql.FieldNotNull(FieldUserID))
}

// SourceIDEQ applies the EQ predicate on the "source_id" field.
func SourceIDEQ(v int) predicate.Person {
	return predicate.Person(sql.FieldEQ(FieldSourceID, v))
}

// SourceIDNEQ applies the NEQ predicate on the "source_id" field.
func SourceIDNEQ(v int) predicate.Person {
	return predicate.Person(sql.FieldNEQ(FieldSourceID, v))
}

// SourceIDIn applies the In predicate on the "source_id" field.
func SourceIDIn(vs ...int) predicate.Person {
	return predicate.Person(sql.FieldIn(FieldSourceID, vs...))
}

// SourceIDNotIn applies the NotIn predicate on the "source_id" field.
func SourceIDNotIn(vs ...int) predicate.Person {
	return predicate.Person(sql.FieldNotIn(FieldSourceID, vs...))
}

// SourceIDIsNil applies the IsNil predicate on the "source_id" field.
func SourceIDIsNil() predicate.Person {
	return predicate.Person(sql.FieldIsNull(FieldSourceID))
}

// SourceIDNotNil applies the NotNil predicate on the "source_id" field.
func SourceIDNotNil() predicate.Person {
	return predicate.Person(sql.FieldNotNull(FieldSourceID))
}

// PhoneConfirmEQ applies the EQ predicate on the "phone_confirm" field.
func PhoneConfirmEQ(v bool) predicate.Person {
	return predicate.Person(sql.FieldEQ(FieldPhoneConfirm, v))
}

// PhoneConfirmNEQ applies the NEQ predicate on the "phone_confirm" field.
func PhoneConfirmNEQ(v bool) predicate.Person {
	return predicate.Person(sql.FieldNEQ(FieldPhoneConfirm, v))
}

// PhoneConfirmIsNil applies the IsNil predicate on the "phone_confirm" field.
func PhoneConfirmIsNil() predicate.Person {
	return predicate.Person(sql.FieldIsNull(FieldPhoneConfirm))
}

// PhoneConfirmNotNil applies the NotNil predicate on the "phone_confirm" field.
func PhoneConfirmNotNil() predicate.Person {
	return predicate.Person(sql.FieldNotNull(FieldPhoneConfirm))
}

// MailConfirmEQ applies the EQ predicate on the "mail_confirm" field.
func MailConfirmEQ(v bool) predicate.Person {
	return predicate.Person(sql.FieldEQ(FieldMailConfirm, v))
}

// MailConfirmNEQ applies the NEQ predicate on the "mail_confirm" field.
func MailConfirmNEQ(v bool) predicate.Person {
	return predicate.Person(sql.FieldNEQ(FieldMailConfirm, v))
}

// MailConfirmIsNil applies the IsNil predicate on the "mail_confirm" field.
func MailConfirmIsNil() predicate.Person {
	return predicate.Person(sql.FieldIsNull(FieldMailConfirm))
}

// MailConfirmNotNil applies the NotNil predicate on the "mail_confirm" field.
func MailConfirmNotNil() predicate.Person {
	return predicate.Person(sql.FieldNotNull(FieldMailConfirm))
}

// PersonFieldIsNil applies the IsNil predicate on the "person_field" field.
func PersonFieldIsNil() predicate.Person {
	return predicate.Person(sql.FieldIsNull(FieldPersonField))
}

// PersonFieldNotNil applies the NotNil predicate on the "person_field" field.
func PersonFieldNotNil() predicate.Person {
	return predicate.Person(sql.FieldNotNull(FieldPersonField))
}

// HasPersonStages applies the HasEdge predicate on the "person_stages" edge.
func HasPersonStages() predicate.Person {
	return predicate.Person(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, PersonStagesTable, PersonStagesColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasPersonStagesWith applies the HasEdge predicate on the "person_stages" edge with a given conditions (other predicates).
func HasPersonStagesWith(preds ...predicate.PersonStage) predicate.Person {
	return predicate.Person(func(s *sql.Selector) {
		step := newPersonStagesStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasDeals applies the HasEdge predicate on the "deals" edge.
func HasDeals() predicate.Person {
	return predicate.Person(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, DealsTable, DealsColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasDealsWith applies the HasEdge predicate on the "deals" edge with a given conditions (other predicates).
func HasDealsWith(preds ...predicate.Deal) predicate.Person {
	return predicate.Person(func(s *sql.Selector) {
		step := newDealsStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasPlans applies the HasEdge predicate on the "plans" edge.
func HasPlans() predicate.Person {
	return predicate.Person(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, PlansTable, PlansColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasPlansWith applies the HasEdge predicate on the "plans" edge with a given conditions (other predicates).
func HasPlansWith(preds ...predicate.InstallmentPlan) predicate.Person {
	return predicate.Person(func(s *sql.Selector) {
		step := newPlansStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasCalls applies the HasEdge predicate on the "calls" edge.
func HasCalls() predicate.Person {
	return predicate.Person(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, CallsTable, CallsColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasCallsWith applies the HasEdge predicate on the "calls" edge with a given conditions (other predicates).
func HasCallsWith(preds ...predicate.Call) predicate.Person {
	return predicate.Person(func(s *sql.Selector) {
		step := newCallsStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasTasks applies the HasEdge predicate on the "tasks" edge.
func HasTasks() predicate.Person {
	return predicate.Person(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, TasksTable, TasksColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasTasksWith applies the HasEdge predicate on the "tasks" edge with a given conditions (other predicates).
func HasTasksWith(preds ...predicate.Task) predicate.Person {
	return predicate.Person(func(s *sql.Selector) {
		step := newTasksStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasIssues applies the HasEdge predicate on the "issues" edge.
func HasIssues() predicate.Person {
	return predicate.Person(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, IssuesTable, IssuesColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasIssuesWith applies the HasEdge predicate on the "issues" edge with a given conditions (other predicates).
func HasIssuesWith(preds ...predicate.Issue) predicate.Person {
	return predicate.Person(func(s *sql.Selector) {
		step := newIssuesStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasAppointments applies the HasEdge predicate on the "appointments" edge.
func HasAppointments() predicate.Person {
	return predicate.Person(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, AppointmentsTable, AppointmentsColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasAppointmentsWith applies the HasEdge predicate on the "appointments" edge with a given conditions (other predicates).
func HasAppointmentsWith(preds ...predicate.Appointment) predicate.Person {
	return predicate.Person(func(s *sql.Selector) {
		step := newAppointmentsStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasTracks applies the HasEdge predicate on the "tracks" edge.
func HasTracks() predicate.Person {
	return predicate.Person(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, TracksTable, TracksColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasTracksWith applies the HasEdge predicate on the "tracks" edge with a given conditions (other predicates).
func HasTracksWith(preds ...predicate.Track) predicate.Person {
	return predicate.Person(func(s *sql.Selector) {
		step := newTracksStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasMessageHistories applies the HasEdge predicate on the "message_histories" edge.
func HasMessageHistories() predicate.Person {
	return predicate.Person(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, MessageHistoriesTable, MessageHistoriesColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasMessageHistoriesWith applies the HasEdge predicate on the "message_histories" edge with a given conditions (other predicates).
func HasMessageHistoriesWith(preds ...predicate.MessageHistory) predicate.Person {
	return predicate.Person(func(s *sql.Selector) {
		step := newMessageHistoriesStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasAssignees applies the HasEdge predicate on the "assignees" edge.
func HasAssignees() predicate.Person {
	return predicate.Person(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2M, false, AssigneesTable, AssigneesPrimaryKey...),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasAssigneesWith applies the HasEdge predicate on the "assignees" edge with a given conditions (other predicates).
func HasAssigneesWith(preds ...predicate.User) predicate.Person {
	return predicate.Person(func(s *sql.Selector) {
		step := newAssigneesStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasJob applies the HasEdge predicate on the "job" edge.
func HasJob() predicate.Person {
	return predicate.Person(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, JobTable, JobColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasJobWith applies the HasEdge predicate on the "job" edge with a given conditions (other predicates).
func HasJobWith(preds ...predicate.Term) predicate.Person {
	return predicate.Person(func(s *sql.Selector) {
		step := newJobStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasSource applies the HasEdge predicate on the "source" edge.
func HasSource() predicate.Person {
	return predicate.Person(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, SourceTable, SourceColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasSourceWith applies the HasEdge predicate on the "source" edge with a given conditions (other predicates).
func HasSourceWith(preds ...predicate.Term) predicate.Person {
	return predicate.Person(func(s *sql.Selector) {
		step := newSourceStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasCreator applies the HasEdge predicate on the "creator" edge.
func HasCreator() predicate.Person {
	return predicate.Person(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, CreatorTable, CreatorColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasCreatorWith applies the HasEdge predicate on the "creator" edge with a given conditions (other predicates).
func HasCreatorWith(preds ...predicate.User) predicate.Person {
	return predicate.Person(func(s *sql.Selector) {
		step := newCreatorStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasReferredBy applies the HasEdge predicate on the "referred_by" edge.
func HasReferredBy() predicate.Person {
	return predicate.Person(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.O2O, false, ReferredByTable, ReferredByColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasReferredByWith applies the HasEdge predicate on the "referred_by" edge with a given conditions (other predicates).
func HasReferredByWith(preds ...predicate.PersonReferral) predicate.Person {
	return predicate.Person(func(s *sql.Selector) {
		step := newReferredByStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasAssignment applies the HasEdge predicate on the "assignment" edge.
func HasAssignment() predicate.Person {
	return predicate.Person(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, AssignmentTable, AssignmentColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasAssignmentWith applies the HasEdge predicate on the "assignment" edge with a given conditions (other predicates).
func HasAssignmentWith(preds ...predicate.PersonAssignment) predicate.Person {
	return predicate.Person(func(s *sql.Selector) {
		step := newAssignmentStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.Person) predicate.Person {
	return predicate.Person(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.Person) predicate.Person {
	return predicate.Person(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.Person) predicate.Person {
	return predicate.Person(sql.NotPredicates(p))
}
