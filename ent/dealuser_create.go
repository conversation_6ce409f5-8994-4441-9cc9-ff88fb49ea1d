// Code generated by ent, DO NOT EDIT.

package ent

import (
	"bcare/ent/deal"
	"bcare/ent/dealuser"
	"bcare/ent/dealuserrating"
	"bcare/ent/user"
	"context"
	"fmt"

	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// DealUserCreate is the builder for creating a DealUser entity.
type DealUserCreate struct {
	config
	mutation *DealUserMutation
	hooks    []Hook
}

// SetDealID sets the "deal_id" field.
func (duc *DealUserCreate) SetDealID(i int) *DealUserCreate {
	duc.mutation.SetDealID(i)
	return duc
}

// SetNillableDealID sets the "deal_id" field if the given value is not nil.
func (duc *DealUserCreate) SetNillableDealID(i *int) *DealUserCreate {
	if i != nil {
		duc.SetDealID(*i)
	}
	return duc
}

// SetUserID sets the "user_id" field.
func (duc *DealUserCreate) SetUserID(i int) *DealUserCreate {
	duc.mutation.SetUserID(i)
	return duc
}

// SetNillableUserID sets the "user_id" field if the given value is not nil.
func (duc *DealUserCreate) SetNillableUserID(i *int) *DealUserCreate {
	if i != nil {
		duc.SetUserID(*i)
	}
	return duc
}

// SetRole sets the "role" field.
func (duc *DealUserCreate) SetRole(d dealuser.Role) *DealUserCreate {
	duc.mutation.SetRole(d)
	return duc
}

// SetNillableRole sets the "role" field if the given value is not nil.
func (duc *DealUserCreate) SetNillableRole(d *dealuser.Role) *DealUserCreate {
	if d != nil {
		duc.SetRole(*d)
	}
	return duc
}

// SetPoint sets the "point" field.
func (duc *DealUserCreate) SetPoint(m map[string]interface{}) *DealUserCreate {
	duc.mutation.SetPoint(m)
	return duc
}

// SetDeal sets the "deal" edge to the Deal entity.
func (duc *DealUserCreate) SetDeal(d *Deal) *DealUserCreate {
	return duc.SetDealID(d.ID)
}

// SetUser sets the "user" edge to the User entity.
func (duc *DealUserCreate) SetUser(u *User) *DealUserCreate {
	return duc.SetUserID(u.ID)
}

// AddRatingIDs adds the "ratings" edge to the DealUserRating entity by IDs.
func (duc *DealUserCreate) AddRatingIDs(ids ...int) *DealUserCreate {
	duc.mutation.AddRatingIDs(ids...)
	return duc
}

// AddRatings adds the "ratings" edges to the DealUserRating entity.
func (duc *DealUserCreate) AddRatings(d ...*DealUserRating) *DealUserCreate {
	ids := make([]int, len(d))
	for i := range d {
		ids[i] = d[i].ID
	}
	return duc.AddRatingIDs(ids...)
}

// Mutation returns the DealUserMutation object of the builder.
func (duc *DealUserCreate) Mutation() *DealUserMutation {
	return duc.mutation
}

// Save creates the DealUser in the database.
func (duc *DealUserCreate) Save(ctx context.Context) (*DealUser, error) {
	return withHooks(ctx, duc.sqlSave, duc.mutation, duc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (duc *DealUserCreate) SaveX(ctx context.Context) *DealUser {
	v, err := duc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (duc *DealUserCreate) Exec(ctx context.Context) error {
	_, err := duc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (duc *DealUserCreate) ExecX(ctx context.Context) {
	if err := duc.Exec(ctx); err != nil {
		panic(err)
	}
}

// check runs all checks and user-defined validators on the builder.
func (duc *DealUserCreate) check() error {
	if v, ok := duc.mutation.Role(); ok {
		if err := dealuser.RoleValidator(v); err != nil {
			return &ValidationError{Name: "role", err: fmt.Errorf(`ent: validator failed for field "DealUser.role": %w`, err)}
		}
	}
	return nil
}

func (duc *DealUserCreate) sqlSave(ctx context.Context) (*DealUser, error) {
	if err := duc.check(); err != nil {
		return nil, err
	}
	_node, _spec := duc.createSpec()
	if err := sqlgraph.CreateNode(ctx, duc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	id := _spec.ID.Value.(int64)
	_node.ID = int(id)
	duc.mutation.id = &_node.ID
	duc.mutation.done = true
	return _node, nil
}

func (duc *DealUserCreate) createSpec() (*DealUser, *sqlgraph.CreateSpec) {
	var (
		_node = &DealUser{config: duc.config}
		_spec = sqlgraph.NewCreateSpec(dealuser.Table, sqlgraph.NewFieldSpec(dealuser.FieldID, field.TypeInt))
	)
	if value, ok := duc.mutation.Role(); ok {
		_spec.SetField(dealuser.FieldRole, field.TypeEnum, value)
		_node.Role = value
	}
	if value, ok := duc.mutation.Point(); ok {
		_spec.SetField(dealuser.FieldPoint, field.TypeJSON, value)
		_node.Point = value
	}
	if nodes := duc.mutation.DealIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   dealuser.DealTable,
			Columns: []string{dealuser.DealColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(deal.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.DealID = nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := duc.mutation.UserIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   dealuser.UserTable,
			Columns: []string{dealuser.UserColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.UserID = nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := duc.mutation.RatingsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   dealuser.RatingsTable,
			Columns: []string{dealuser.RatingsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(dealuserrating.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	return _node, _spec
}

// DealUserCreateBulk is the builder for creating many DealUser entities in bulk.
type DealUserCreateBulk struct {
	config
	err      error
	builders []*DealUserCreate
}

// Save creates the DealUser entities in the database.
func (ducb *DealUserCreateBulk) Save(ctx context.Context) ([]*DealUser, error) {
	if ducb.err != nil {
		return nil, ducb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(ducb.builders))
	nodes := make([]*DealUser, len(ducb.builders))
	mutators := make([]Mutator, len(ducb.builders))
	for i := range ducb.builders {
		func(i int, root context.Context) {
			builder := ducb.builders[i]
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*DealUserMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, ducb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, ducb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = int(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, ducb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (ducb *DealUserCreateBulk) SaveX(ctx context.Context) []*DealUser {
	v, err := ducb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (ducb *DealUserCreateBulk) Exec(ctx context.Context) error {
	_, err := ducb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (ducb *DealUserCreateBulk) ExecX(ctx context.Context) {
	if err := ducb.Exec(ctx); err != nil {
		panic(err)
	}
}
