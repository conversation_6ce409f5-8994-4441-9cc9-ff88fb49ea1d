// Copyright 2019-present Facebook Inc. All rights reserved.
// This source code is licensed under the Apache 2.0 license found
// in the LICENSE file in the root directory of this source tree.

//go:build ignore
// +build ignore

package main

import (
	"log"

	"entgo.io/ent/entc"
	"entgo.io/ent/entc/gen"
	"entgo.io/ent/schema/edge"
)

func main() {
	opts := []entc.Option{
		entc.Extensions(
			&EncodeExtension{},
		),
	}
	err := entc.Generate("./schema", &gen.Config{
		Templates: []*gen.Template{
			gen.MustParse(gen.NewTemplate("input").ParseFiles("template/input.tmpl")),
			gen.MustParse(gen.NewTemplate("query").ParseFiles("template/query0144.tmpl")),
			gen.MustParse(gen.NewTemplate("client").ParseFiles("template/client0144.tmpl")),
			// Add more templates as needed
		},
		Features: []gen.Feature{
			gen.FeatureSnapshot,
			gen.FeatureIntercept,
			gen.FeatureExecQuery,
			gen.FeatureModifier,
			// Add more features as needed
		},
	}, opts...)
	if err != nil {
		log.Fatalf("running ent codegen: %v", err)
	}
}

// EncodeExtension is an implementation of entc.Extension that adds a MarshalJSON
// method to each generated type <T> and inlines the Edges field to the top level JSON.
type EncodeExtension struct {
	entc.DefaultExtension
}

// Templates of the extension.
func (e *EncodeExtension) Templates() []*gen.Template {
	return []*gen.Template{
		gen.MustParse(gen.NewTemplate("model/additional/jsonencode").
			Parse(`
{{ if $.Edges }}
	// MarshalJSON implements the json.Marshaler interface.
	func ({{ $.Receiver }} *{{ $.Name }}) MarshalJSON() ([]byte, error) {
		type Alias {{ $.Name }}
		return json.Marshal(&struct {
			*Alias
			{{ $.Name }}Edges
		}{
			Alias: (*Alias)({{ $.Receiver }}),
			{{ $.Name }}Edges: {{ $.Receiver }}.Edges,
		})
	}
{{ end }}

{{ if $.Edges -}}
	// MarshalSimpleTime 
	func ({{ $.Receiver }} *{{ $.Name }}) MarshalSimpleTime() ([]byte, error) {
		type Alias {{ $.Name }}
		return json.Marshal(&struct {
			*Alias
			{{ $.Name }}Edges
			{{- range $field := $.Fields }}
				{{- if $field.IsTime }}
					{{ $field.StructField }} string ` + "`" + `json:"{{ $field.Name }},omitempty"` + "`" + `
				{{- end }}
			{{- end }}
		}{
			Alias: (*Alias)({{ $.Receiver }}),
			{{ $.Name }}Edges: {{ $.Receiver }}.Edges,
			{{- range $field := $.Fields }}
				{{- if $field.IsTime }}
					{{- if $field.Nillable }}
						{{ $field.StructField }}: func() string {
							if {{ $.Receiver }}.{{ $field.StructField }} != nil {
								str := {{ $.Receiver }}.{{ $field.StructField }}.Format("15:04 02/01/2006")
								return str
							}
							return ""
						}(),
					{{- else }}
						{{ $field.StructField }}: {{ $.Receiver }}.{{ $field.StructField }}.Format("15:04 02/01/2006"),
					{{- end }}
				{{- end }}
			{{- end }}
		})
	}
{{- end }}
`)),
	}
}

// Hooks of the extension.
func (e *EncodeExtension) Hooks() []gen.Hook {
	return []gen.Hook{
		func(next gen.Generator) gen.Generator {
			return gen.GenerateFunc(func(g *gen.Graph) error {
				tag := edge.Annotation{StructTag: `json:"-"`}
				for _, n := range g.Nodes {
					n.Annotations.Set(tag.Name(), tag)
				}
				return next.Generate(g)
			})
		},
	}
}

var _ entc.Extension = (*EncodeExtension)(nil)
