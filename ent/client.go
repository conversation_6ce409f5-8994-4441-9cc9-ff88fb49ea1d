// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"log"
	"reflect"

	"bcare/ent/migrate"

	"bcare/ent/appointment"
	"bcare/ent/appointmenthistory"
	"bcare/ent/attachment"
	"bcare/ent/attachmentdata"
	"bcare/ent/bill"
	"bcare/ent/billdata"
	"bcare/ent/billitem"
	"bcare/ent/bundle"
	"bcare/ent/call"
	"bcare/ent/casbinrule"
	"bcare/ent/cashflow"
	"bcare/ent/cashflowitem"
	"bcare/ent/cashflownote"
	"bcare/ent/dbsandentistdboappointmenttime"
	"bcare/ent/dbsandentistdbobill"
	"bcare/ent/dbsandentistdbobillitem"
	"bcare/ent/dbsandentistdbobillitemdetail"
	"bcare/ent/dbsandentistdbobillpayment"
	"bcare/ent/dbsandentistdbocustomer"
	"bcare/ent/deal"
	"bcare/ent/dealhistory"
	"bcare/ent/dealstagehistory"
	"bcare/ent/dealuser"
	"bcare/ent/dealuserrating"
	"bcare/ent/department"
	"bcare/ent/deposit"
	"bcare/ent/depositallocation"
	"bcare/ent/depositpayment"
	"bcare/ent/discount"
	"bcare/ent/discountusage"
	"bcare/ent/entityhistory"
	"bcare/ent/exportjob"
	"bcare/ent/file"
	"bcare/ent/fileusage"
	"bcare/ent/formsubmission"
	"bcare/ent/installment"
	"bcare/ent/installmentplan"
	"bcare/ent/issue"
	"bcare/ent/issuecomment"
	"bcare/ent/localdistrict"
	"bcare/ent/localprovince"
	"bcare/ent/localward"
	"bcare/ent/material"
	"bcare/ent/materialusage"
	"bcare/ent/messagehistory"
	"bcare/ent/note"
	"bcare/ent/notification"
	"bcare/ent/operation"
	"bcare/ent/operationmaterial"
	"bcare/ent/organization"
	"bcare/ent/otp"
	"bcare/ent/payment"
	"bcare/ent/paymentallocation"
	"bcare/ent/person"
	"bcare/ent/personassignment"
	"bcare/ent/persondata"
	"bcare/ent/personhistory"
	"bcare/ent/personreferral"
	"bcare/ent/pipeline"
	"bcare/ent/product"
	"bcare/ent/productoperation"
	"bcare/ent/referral"
	"bcare/ent/schedule"
	"bcare/ent/setting"
	"bcare/ent/stage"
	"bcare/ent/tag"
	"bcare/ent/tagdeal"
	"bcare/ent/tagperson"
	"bcare/ent/task"
	"bcare/ent/taskassignment"
	"bcare/ent/taskdepartment"
	"bcare/ent/taskhistory"
	"bcare/ent/tasknote"
	"bcare/ent/taskrecurring"
	"bcare/ent/term"
	"bcare/ent/track"
	"bcare/ent/user"
	"bcare/ent/userdata"

	"entgo.io/ent"
	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"

	stdsql "database/sql"
)

// Client is the client that holds all ent builders.
type Client struct {
	config
	// Schema is the client for creating, migrating and dropping schema.
	Schema *migrate.Schema
	// ActivityView is the client for interacting with the ActivityView builders.
	ActivityView *ActivityViewClient
	// Appointment is the client for interacting with the Appointment builders.
	Appointment *AppointmentClient
	// AppointmentHistory is the client for interacting with the AppointmentHistory builders.
	AppointmentHistory *AppointmentHistoryClient
	// Attachment is the client for interacting with the Attachment builders.
	Attachment *AttachmentClient
	// AttachmentData is the client for interacting with the AttachmentData builders.
	AttachmentData *AttachmentDataClient
	// AttachmentOperationReportView is the client for interacting with the AttachmentOperationReportView builders.
	AttachmentOperationReportView *AttachmentOperationReportViewClient
	// Bill is the client for interacting with the Bill builders.
	Bill *BillClient
	// BillData is the client for interacting with the BillData builders.
	BillData *BillDataClient
	// BillItem is the client for interacting with the BillItem builders.
	BillItem *BillItemClient
	// Bundle is the client for interacting with the Bundle builders.
	Bundle *BundleClient
	// Call is the client for interacting with the Call builders.
	Call *CallClient
	// CasbinRule is the client for interacting with the CasbinRule builders.
	CasbinRule *CasbinRuleClient
	// CashFlow is the client for interacting with the CashFlow builders.
	CashFlow *CashFlowClient
	// CashFlowItem is the client for interacting with the CashFlowItem builders.
	CashFlowItem *CashFlowItemClient
	// CashFlowNote is the client for interacting with the CashFlowNote builders.
	CashFlowNote *CashFlowNoteClient
	// DbsandentistDboAppointmentTime is the client for interacting with the DbsandentistDboAppointmentTime builders.
	DbsandentistDboAppointmentTime *DbsandentistDboAppointmentTimeClient
	// DbsandentistDboBill is the client for interacting with the DbsandentistDboBill builders.
	DbsandentistDboBill *DbsandentistDboBillClient
	// DbsandentistDboBillItem is the client for interacting with the DbsandentistDboBillItem builders.
	DbsandentistDboBillItem *DbsandentistDboBillItemClient
	// DbsandentistDboBillItemDetail is the client for interacting with the DbsandentistDboBillItemDetail builders.
	DbsandentistDboBillItemDetail *DbsandentistDboBillItemDetailClient
	// DbsandentistDboBillPayment is the client for interacting with the DbsandentistDboBillPayment builders.
	DbsandentistDboBillPayment *DbsandentistDboBillPaymentClient
	// DbsandentistDboCustomer is the client for interacting with the DbsandentistDboCustomer builders.
	DbsandentistDboCustomer *DbsandentistDboCustomerClient
	// Deal is the client for interacting with the Deal builders.
	Deal *DealClient
	// DealHistory is the client for interacting with the DealHistory builders.
	DealHistory *DealHistoryClient
	// DealStageHistory is the client for interacting with the DealStageHistory builders.
	DealStageHistory *DealStageHistoryClient
	// DealStageHistoryView is the client for interacting with the DealStageHistoryView builders.
	DealStageHistoryView *DealStageHistoryViewClient
	// DealUser is the client for interacting with the DealUser builders.
	DealUser *DealUserClient
	// DealUserRating is the client for interacting with the DealUserRating builders.
	DealUserRating *DealUserRatingClient
	// Department is the client for interacting with the Department builders.
	Department *DepartmentClient
	// Deposit is the client for interacting with the Deposit builders.
	Deposit *DepositClient
	// DepositAllocation is the client for interacting with the DepositAllocation builders.
	DepositAllocation *DepositAllocationClient
	// DepositPayment is the client for interacting with the DepositPayment builders.
	DepositPayment *DepositPaymentClient
	// Discount is the client for interacting with the Discount builders.
	Discount *DiscountClient
	// DiscountUsage is the client for interacting with the DiscountUsage builders.
	DiscountUsage *DiscountUsageClient
	// EntityHistory is the client for interacting with the EntityHistory builders.
	EntityHistory *EntityHistoryClient
	// ExportJob is the client for interacting with the ExportJob builders.
	ExportJob *ExportJobClient
	// File is the client for interacting with the File builders.
	File *FileClient
	// FileUsage is the client for interacting with the FileUsage builders.
	FileUsage *FileUsageClient
	// FormSubmission is the client for interacting with the FormSubmission builders.
	FormSubmission *FormSubmissionClient
	// Installment is the client for interacting with the Installment builders.
	Installment *InstallmentClient
	// InstallmentPlan is the client for interacting with the InstallmentPlan builders.
	InstallmentPlan *InstallmentPlanClient
	// Issue is the client for interacting with the Issue builders.
	Issue *IssueClient
	// IssueComment is the client for interacting with the IssueComment builders.
	IssueComment *IssueCommentClient
	// LocalDistrict is the client for interacting with the LocalDistrict builders.
	LocalDistrict *LocalDistrictClient
	// LocalProvince is the client for interacting with the LocalProvince builders.
	LocalProvince *LocalProvinceClient
	// LocalWard is the client for interacting with the LocalWard builders.
	LocalWard *LocalWardClient
	// Material is the client for interacting with the Material builders.
	Material *MaterialClient
	// MaterialUsage is the client for interacting with the MaterialUsage builders.
	MaterialUsage *MaterialUsageClient
	// MessageHistory is the client for interacting with the MessageHistory builders.
	MessageHistory *MessageHistoryClient
	// NewTracksReportView is the client for interacting with the NewTracksReportView builders.
	NewTracksReportView *NewTracksReportViewClient
	// Note is the client for interacting with the Note builders.
	Note *NoteClient
	// Notification is the client for interacting with the Notification builders.
	Notification *NotificationClient
	// OTP is the client for interacting with the OTP builders.
	OTP *OTPClient
	// Operation is the client for interacting with the Operation builders.
	Operation *OperationClient
	// OperationMaterial is the client for interacting with the OperationMaterial builders.
	OperationMaterial *OperationMaterialClient
	// Organization is the client for interacting with the Organization builders.
	Organization *OrganizationClient
	// Payment is the client for interacting with the Payment builders.
	Payment *PaymentClient
	// PaymentAllocation is the client for interacting with the PaymentAllocation builders.
	PaymentAllocation *PaymentAllocationClient
	// PaymentReportDetailView is the client for interacting with the PaymentReportDetailView builders.
	PaymentReportDetailView *PaymentReportDetailViewClient
	// PaymentReportView is the client for interacting with the PaymentReportView builders.
	PaymentReportView *PaymentReportViewClient
	// Person is the client for interacting with the Person builders.
	Person *PersonClient
	// PersonAssignment is the client for interacting with the PersonAssignment builders.
	PersonAssignment *PersonAssignmentClient
	// PersonData is the client for interacting with the PersonData builders.
	PersonData *PersonDataClient
	// PersonHistory is the client for interacting with the PersonHistory builders.
	PersonHistory *PersonHistoryClient
	// PersonQueryView is the client for interacting with the PersonQueryView builders.
	PersonQueryView *PersonQueryViewClient
	// PersonReferral is the client for interacting with the PersonReferral builders.
	PersonReferral *PersonReferralClient
	// PersonTimelineView is the client for interacting with the PersonTimelineView builders.
	PersonTimelineView *PersonTimelineViewClient
	// PhoneViewHistory is the client for interacting with the PhoneViewHistory builders.
	PhoneViewHistory *PhoneViewHistoryClient
	// Pipeline is the client for interacting with the Pipeline builders.
	Pipeline *PipelineClient
	// Product is the client for interacting with the Product builders.
	Product *ProductClient
	// ProductOperation is the client for interacting with the ProductOperation builders.
	ProductOperation *ProductOperationClient
	// Referral is the client for interacting with the Referral builders.
	Referral *ReferralClient
	// Schedule is the client for interacting with the Schedule builders.
	Schedule *ScheduleClient
	// Setting is the client for interacting with the Setting builders.
	Setting *SettingClient
	// Stage is the client for interacting with the Stage builders.
	Stage *StageClient
	// Tag is the client for interacting with the Tag builders.
	Tag *TagClient
	// TagDeal is the client for interacting with the TagDeal builders.
	TagDeal *TagDealClient
	// TagPerson is the client for interacting with the TagPerson builders.
	TagPerson *TagPersonClient
	// Task is the client for interacting with the Task builders.
	Task *TaskClient
	// TaskAssignment is the client for interacting with the TaskAssignment builders.
	TaskAssignment *TaskAssignmentClient
	// TaskAssignmentView is the client for interacting with the TaskAssignmentView builders.
	TaskAssignmentView *TaskAssignmentViewClient
	// TaskDepartment is the client for interacting with the TaskDepartment builders.
	TaskDepartment *TaskDepartmentClient
	// TaskHistory is the client for interacting with the TaskHistory builders.
	TaskHistory *TaskHistoryClient
	// TaskNote is the client for interacting with the TaskNote builders.
	TaskNote *TaskNoteClient
	// TaskRecurring is the client for interacting with the TaskRecurring builders.
	TaskRecurring *TaskRecurringClient
	// TaskSerialView is the client for interacting with the TaskSerialView builders.
	TaskSerialView *TaskSerialViewClient
	// Term is the client for interacting with the Term builders.
	Term *TermClient
	// Track is the client for interacting with the Track builders.
	Track *TrackClient
	// UnifiedHistoryView is the client for interacting with the UnifiedHistoryView builders.
	UnifiedHistoryView *UnifiedHistoryViewClient
	// User is the client for interacting with the User builders.
	User *UserClient
	// UserData is the client for interacting with the UserData builders.
	UserData *UserDataClient
}

// NewClient creates a new client configured with the given options.
func NewClient(opts ...Option) *Client {
	client := &Client{config: newConfig(opts...)}
	client.init()
	return client
}

func (c *Client) init() {
	c.Schema = migrate.NewSchema(c.driver)
	c.ActivityView = NewActivityViewClient(c.config)
	c.Appointment = NewAppointmentClient(c.config)
	c.AppointmentHistory = NewAppointmentHistoryClient(c.config)
	c.Attachment = NewAttachmentClient(c.config)
	c.AttachmentData = NewAttachmentDataClient(c.config)
	c.AttachmentOperationReportView = NewAttachmentOperationReportViewClient(c.config)
	c.Bill = NewBillClient(c.config)
	c.BillData = NewBillDataClient(c.config)
	c.BillItem = NewBillItemClient(c.config)
	c.Bundle = NewBundleClient(c.config)
	c.Call = NewCallClient(c.config)
	c.CasbinRule = NewCasbinRuleClient(c.config)
	c.CashFlow = NewCashFlowClient(c.config)
	c.CashFlowItem = NewCashFlowItemClient(c.config)
	c.CashFlowNote = NewCashFlowNoteClient(c.config)
	c.DbsandentistDboAppointmentTime = NewDbsandentistDboAppointmentTimeClient(c.config)
	c.DbsandentistDboBill = NewDbsandentistDboBillClient(c.config)
	c.DbsandentistDboBillItem = NewDbsandentistDboBillItemClient(c.config)
	c.DbsandentistDboBillItemDetail = NewDbsandentistDboBillItemDetailClient(c.config)
	c.DbsandentistDboBillPayment = NewDbsandentistDboBillPaymentClient(c.config)
	c.DbsandentistDboCustomer = NewDbsandentistDboCustomerClient(c.config)
	c.Deal = NewDealClient(c.config)
	c.DealHistory = NewDealHistoryClient(c.config)
	c.DealStageHistory = NewDealStageHistoryClient(c.config)
	c.DealStageHistoryView = NewDealStageHistoryViewClient(c.config)
	c.DealUser = NewDealUserClient(c.config)
	c.DealUserRating = NewDealUserRatingClient(c.config)
	c.Department = NewDepartmentClient(c.config)
	c.Deposit = NewDepositClient(c.config)
	c.DepositAllocation = NewDepositAllocationClient(c.config)
	c.DepositPayment = NewDepositPaymentClient(c.config)
	c.Discount = NewDiscountClient(c.config)
	c.DiscountUsage = NewDiscountUsageClient(c.config)
	c.EntityHistory = NewEntityHistoryClient(c.config)
	c.ExportJob = NewExportJobClient(c.config)
	c.File = NewFileClient(c.config)
	c.FileUsage = NewFileUsageClient(c.config)
	c.FormSubmission = NewFormSubmissionClient(c.config)
	c.Installment = NewInstallmentClient(c.config)
	c.InstallmentPlan = NewInstallmentPlanClient(c.config)
	c.Issue = NewIssueClient(c.config)
	c.IssueComment = NewIssueCommentClient(c.config)
	c.LocalDistrict = NewLocalDistrictClient(c.config)
	c.LocalProvince = NewLocalProvinceClient(c.config)
	c.LocalWard = NewLocalWardClient(c.config)
	c.Material = NewMaterialClient(c.config)
	c.MaterialUsage = NewMaterialUsageClient(c.config)
	c.MessageHistory = NewMessageHistoryClient(c.config)
	c.NewTracksReportView = NewNewTracksReportViewClient(c.config)
	c.Note = NewNoteClient(c.config)
	c.Notification = NewNotificationClient(c.config)
	c.OTP = NewOTPClient(c.config)
	c.Operation = NewOperationClient(c.config)
	c.OperationMaterial = NewOperationMaterialClient(c.config)
	c.Organization = NewOrganizationClient(c.config)
	c.Payment = NewPaymentClient(c.config)
	c.PaymentAllocation = NewPaymentAllocationClient(c.config)
	c.PaymentReportDetailView = NewPaymentReportDetailViewClient(c.config)
	c.PaymentReportView = NewPaymentReportViewClient(c.config)
	c.Person = NewPersonClient(c.config)
	c.PersonAssignment = NewPersonAssignmentClient(c.config)
	c.PersonData = NewPersonDataClient(c.config)
	c.PersonHistory = NewPersonHistoryClient(c.config)
	c.PersonQueryView = NewPersonQueryViewClient(c.config)
	c.PersonReferral = NewPersonReferralClient(c.config)
	c.PersonTimelineView = NewPersonTimelineViewClient(c.config)
	c.PhoneViewHistory = NewPhoneViewHistoryClient(c.config)
	c.Pipeline = NewPipelineClient(c.config)
	c.Product = NewProductClient(c.config)
	c.ProductOperation = NewProductOperationClient(c.config)
	c.Referral = NewReferralClient(c.config)
	c.Schedule = NewScheduleClient(c.config)
	c.Setting = NewSettingClient(c.config)
	c.Stage = NewStageClient(c.config)
	c.Tag = NewTagClient(c.config)
	c.TagDeal = NewTagDealClient(c.config)
	c.TagPerson = NewTagPersonClient(c.config)
	c.Task = NewTaskClient(c.config)
	c.TaskAssignment = NewTaskAssignmentClient(c.config)
	c.TaskAssignmentView = NewTaskAssignmentViewClient(c.config)
	c.TaskDepartment = NewTaskDepartmentClient(c.config)
	c.TaskHistory = NewTaskHistoryClient(c.config)
	c.TaskNote = NewTaskNoteClient(c.config)
	c.TaskRecurring = NewTaskRecurringClient(c.config)
	c.TaskSerialView = NewTaskSerialViewClient(c.config)
	c.Term = NewTermClient(c.config)
	c.Track = NewTrackClient(c.config)
	c.UnifiedHistoryView = NewUnifiedHistoryViewClient(c.config)
	c.User = NewUserClient(c.config)
	c.UserData = NewUserDataClient(c.config)
}

type (
	// config is the configuration for the client and its builder.
	config struct {
		// driver used for executing database requests.
		driver dialect.Driver
		// debug enable a debug logging.
		debug bool
		// log used for logging on debug mode.
		log func(...any)
		// hooks to execute on mutations.
		hooks *hooks
		// interceptors to execute on queries.
		inters *inters
	}
	// Option function to configure the client.
	Option func(*config)
)

// newConfig creates a new config for the client.
func newConfig(opts ...Option) config {
	cfg := config{log: log.Println, hooks: &hooks{}, inters: &inters{}}
	cfg.options(opts...)
	return cfg
}

// options applies the options on the config object.
func (c *config) options(opts ...Option) {
	for _, opt := range opts {
		opt(c)
	}
	if c.debug {
		c.driver = dialect.Debug(c.driver, c.log)
	}
}

// Debug enables debug logging on the ent.Driver.
func Debug() Option {
	return func(c *config) {
		c.debug = true
	}
}

// Log sets the logging function for debug mode.
func Log(fn func(...any)) Option {
	return func(c *config) {
		c.log = fn
	}
}

// Driver configures the client driver.
func Driver(driver dialect.Driver) Option {
	return func(c *config) {
		c.driver = driver
	}
}

// Open opens a database/sql.DB specified by the driver name and
// the data source name, and returns a new client attached to it.
// Optional parameters can be added for configuring the client.
func Open(driverName, dataSourceName string, options ...Option) (*Client, error) {
	switch driverName {
	case dialect.MySQL, dialect.Postgres, dialect.SQLite:
		drv, err := sql.Open(driverName, dataSourceName)
		if err != nil {
			return nil, err
		}
		return NewClient(append(options, Driver(drv))...), nil
	default:
		return nil, fmt.Errorf("unsupported driver: %q", driverName)
	}
}

// ErrTxStarted is returned when trying to start a new transaction from a transactional client.
var ErrTxStarted = errors.New("ent: cannot start a transaction within a transaction")

// Tx returns a new transactional client. The provided context
// is used until the transaction is committed or rolled back.
func (c *Client) Tx(ctx context.Context) (*Tx, error) {
	if _, ok := c.driver.(*txDriver); ok {
		return nil, ErrTxStarted
	}
	tx, err := newTx(ctx, c.driver)
	if err != nil {
		return nil, fmt.Errorf("ent: starting a transaction: %w", err)
	}
	cfg := c.config
	cfg.driver = tx
	return &Tx{
		ctx:                            ctx,
		config:                         cfg,
		ActivityView:                   NewActivityViewClient(cfg),
		Appointment:                    NewAppointmentClient(cfg),
		AppointmentHistory:             NewAppointmentHistoryClient(cfg),
		Attachment:                     NewAttachmentClient(cfg),
		AttachmentData:                 NewAttachmentDataClient(cfg),
		AttachmentOperationReportView:  NewAttachmentOperationReportViewClient(cfg),
		Bill:                           NewBillClient(cfg),
		BillData:                       NewBillDataClient(cfg),
		BillItem:                       NewBillItemClient(cfg),
		Bundle:                         NewBundleClient(cfg),
		Call:                           NewCallClient(cfg),
		CasbinRule:                     NewCasbinRuleClient(cfg),
		CashFlow:                       NewCashFlowClient(cfg),
		CashFlowItem:                   NewCashFlowItemClient(cfg),
		CashFlowNote:                   NewCashFlowNoteClient(cfg),
		DbsandentistDboAppointmentTime: NewDbsandentistDboAppointmentTimeClient(cfg),
		DbsandentistDboBill:            NewDbsandentistDboBillClient(cfg),
		DbsandentistDboBillItem:        NewDbsandentistDboBillItemClient(cfg),
		DbsandentistDboBillItemDetail:  NewDbsandentistDboBillItemDetailClient(cfg),
		DbsandentistDboBillPayment:     NewDbsandentistDboBillPaymentClient(cfg),
		DbsandentistDboCustomer:        NewDbsandentistDboCustomerClient(cfg),
		Deal:                           NewDealClient(cfg),
		DealHistory:                    NewDealHistoryClient(cfg),
		DealStageHistory:               NewDealStageHistoryClient(cfg),
		DealStageHistoryView:           NewDealStageHistoryViewClient(cfg),
		DealUser:                       NewDealUserClient(cfg),
		DealUserRating:                 NewDealUserRatingClient(cfg),
		Department:                     NewDepartmentClient(cfg),
		Deposit:                        NewDepositClient(cfg),
		DepositAllocation:              NewDepositAllocationClient(cfg),
		DepositPayment:                 NewDepositPaymentClient(cfg),
		Discount:                       NewDiscountClient(cfg),
		DiscountUsage:                  NewDiscountUsageClient(cfg),
		EntityHistory:                  NewEntityHistoryClient(cfg),
		ExportJob:                      NewExportJobClient(cfg),
		File:                           NewFileClient(cfg),
		FileUsage:                      NewFileUsageClient(cfg),
		FormSubmission:                 NewFormSubmissionClient(cfg),
		Installment:                    NewInstallmentClient(cfg),
		InstallmentPlan:                NewInstallmentPlanClient(cfg),
		Issue:                          NewIssueClient(cfg),
		IssueComment:                   NewIssueCommentClient(cfg),
		LocalDistrict:                  NewLocalDistrictClient(cfg),
		LocalProvince:                  NewLocalProvinceClient(cfg),
		LocalWard:                      NewLocalWardClient(cfg),
		Material:                       NewMaterialClient(cfg),
		MaterialUsage:                  NewMaterialUsageClient(cfg),
		MessageHistory:                 NewMessageHistoryClient(cfg),
		NewTracksReportView:            NewNewTracksReportViewClient(cfg),
		Note:                           NewNoteClient(cfg),
		Notification:                   NewNotificationClient(cfg),
		OTP:                            NewOTPClient(cfg),
		Operation:                      NewOperationClient(cfg),
		OperationMaterial:              NewOperationMaterialClient(cfg),
		Organization:                   NewOrganizationClient(cfg),
		Payment:                        NewPaymentClient(cfg),
		PaymentAllocation:              NewPaymentAllocationClient(cfg),
		PaymentReportDetailView:        NewPaymentReportDetailViewClient(cfg),
		PaymentReportView:              NewPaymentReportViewClient(cfg),
		Person:                         NewPersonClient(cfg),
		PersonAssignment:               NewPersonAssignmentClient(cfg),
		PersonData:                     NewPersonDataClient(cfg),
		PersonHistory:                  NewPersonHistoryClient(cfg),
		PersonQueryView:                NewPersonQueryViewClient(cfg),
		PersonReferral:                 NewPersonReferralClient(cfg),
		PersonTimelineView:             NewPersonTimelineViewClient(cfg),
		PhoneViewHistory:               NewPhoneViewHistoryClient(cfg),
		Pipeline:                       NewPipelineClient(cfg),
		Product:                        NewProductClient(cfg),
		ProductOperation:               NewProductOperationClient(cfg),
		Referral:                       NewReferralClient(cfg),
		Schedule:                       NewScheduleClient(cfg),
		Setting:                        NewSettingClient(cfg),
		Stage:                          NewStageClient(cfg),
		Tag:                            NewTagClient(cfg),
		TagDeal:                        NewTagDealClient(cfg),
		TagPerson:                      NewTagPersonClient(cfg),
		Task:                           NewTaskClient(cfg),
		TaskAssignment:                 NewTaskAssignmentClient(cfg),
		TaskAssignmentView:             NewTaskAssignmentViewClient(cfg),
		TaskDepartment:                 NewTaskDepartmentClient(cfg),
		TaskHistory:                    NewTaskHistoryClient(cfg),
		TaskNote:                       NewTaskNoteClient(cfg),
		TaskRecurring:                  NewTaskRecurringClient(cfg),
		TaskSerialView:                 NewTaskSerialViewClient(cfg),
		Term:                           NewTermClient(cfg),
		Track:                          NewTrackClient(cfg),
		UnifiedHistoryView:             NewUnifiedHistoryViewClient(cfg),
		User:                           NewUserClient(cfg),
		UserData:                       NewUserDataClient(cfg),
	}, nil
}

// BeginTx returns a transactional client with specified options.
func (c *Client) BeginTx(ctx context.Context, opts *sql.TxOptions) (*Tx, error) {
	if _, ok := c.driver.(*txDriver); ok {
		return nil, errors.New("ent: cannot start a transaction within a transaction")
	}
	tx, err := c.driver.(interface {
		BeginTx(context.Context, *sql.TxOptions) (dialect.Tx, error)
	}).BeginTx(ctx, opts)
	if err != nil {
		return nil, fmt.Errorf("ent: starting a transaction: %w", err)
	}
	cfg := c.config
	cfg.driver = &txDriver{tx: tx, drv: c.driver}
	return &Tx{
		ctx:                            ctx,
		config:                         cfg,
		ActivityView:                   NewActivityViewClient(cfg),
		Appointment:                    NewAppointmentClient(cfg),
		AppointmentHistory:             NewAppointmentHistoryClient(cfg),
		Attachment:                     NewAttachmentClient(cfg),
		AttachmentData:                 NewAttachmentDataClient(cfg),
		AttachmentOperationReportView:  NewAttachmentOperationReportViewClient(cfg),
		Bill:                           NewBillClient(cfg),
		BillData:                       NewBillDataClient(cfg),
		BillItem:                       NewBillItemClient(cfg),
		Bundle:                         NewBundleClient(cfg),
		Call:                           NewCallClient(cfg),
		CasbinRule:                     NewCasbinRuleClient(cfg),
		CashFlow:                       NewCashFlowClient(cfg),
		CashFlowItem:                   NewCashFlowItemClient(cfg),
		CashFlowNote:                   NewCashFlowNoteClient(cfg),
		DbsandentistDboAppointmentTime: NewDbsandentistDboAppointmentTimeClient(cfg),
		DbsandentistDboBill:            NewDbsandentistDboBillClient(cfg),
		DbsandentistDboBillItem:        NewDbsandentistDboBillItemClient(cfg),
		DbsandentistDboBillItemDetail:  NewDbsandentistDboBillItemDetailClient(cfg),
		DbsandentistDboBillPayment:     NewDbsandentistDboBillPaymentClient(cfg),
		DbsandentistDboCustomer:        NewDbsandentistDboCustomerClient(cfg),
		Deal:                           NewDealClient(cfg),
		DealHistory:                    NewDealHistoryClient(cfg),
		DealStageHistory:               NewDealStageHistoryClient(cfg),
		DealStageHistoryView:           NewDealStageHistoryViewClient(cfg),
		DealUser:                       NewDealUserClient(cfg),
		DealUserRating:                 NewDealUserRatingClient(cfg),
		Department:                     NewDepartmentClient(cfg),
		Deposit:                        NewDepositClient(cfg),
		DepositAllocation:              NewDepositAllocationClient(cfg),
		DepositPayment:                 NewDepositPaymentClient(cfg),
		Discount:                       NewDiscountClient(cfg),
		DiscountUsage:                  NewDiscountUsageClient(cfg),
		EntityHistory:                  NewEntityHistoryClient(cfg),
		ExportJob:                      NewExportJobClient(cfg),
		File:                           NewFileClient(cfg),
		FileUsage:                      NewFileUsageClient(cfg),
		FormSubmission:                 NewFormSubmissionClient(cfg),
		Installment:                    NewInstallmentClient(cfg),
		InstallmentPlan:                NewInstallmentPlanClient(cfg),
		Issue:                          NewIssueClient(cfg),
		IssueComment:                   NewIssueCommentClient(cfg),
		LocalDistrict:                  NewLocalDistrictClient(cfg),
		LocalProvince:                  NewLocalProvinceClient(cfg),
		LocalWard:                      NewLocalWardClient(cfg),
		Material:                       NewMaterialClient(cfg),
		MaterialUsage:                  NewMaterialUsageClient(cfg),
		MessageHistory:                 NewMessageHistoryClient(cfg),
		NewTracksReportView:            NewNewTracksReportViewClient(cfg),
		Note:                           NewNoteClient(cfg),
		Notification:                   NewNotificationClient(cfg),
		OTP:                            NewOTPClient(cfg),
		Operation:                      NewOperationClient(cfg),
		OperationMaterial:              NewOperationMaterialClient(cfg),
		Organization:                   NewOrganizationClient(cfg),
		Payment:                        NewPaymentClient(cfg),
		PaymentAllocation:              NewPaymentAllocationClient(cfg),
		PaymentReportDetailView:        NewPaymentReportDetailViewClient(cfg),
		PaymentReportView:              NewPaymentReportViewClient(cfg),
		Person:                         NewPersonClient(cfg),
		PersonAssignment:               NewPersonAssignmentClient(cfg),
		PersonData:                     NewPersonDataClient(cfg),
		PersonHistory:                  NewPersonHistoryClient(cfg),
		PersonQueryView:                NewPersonQueryViewClient(cfg),
		PersonReferral:                 NewPersonReferralClient(cfg),
		PersonTimelineView:             NewPersonTimelineViewClient(cfg),
		PhoneViewHistory:               NewPhoneViewHistoryClient(cfg),
		Pipeline:                       NewPipelineClient(cfg),
		Product:                        NewProductClient(cfg),
		ProductOperation:               NewProductOperationClient(cfg),
		Referral:                       NewReferralClient(cfg),
		Schedule:                       NewScheduleClient(cfg),
		Setting:                        NewSettingClient(cfg),
		Stage:                          NewStageClient(cfg),
		Tag:                            NewTagClient(cfg),
		TagDeal:                        NewTagDealClient(cfg),
		TagPerson:                      NewTagPersonClient(cfg),
		Task:                           NewTaskClient(cfg),
		TaskAssignment:                 NewTaskAssignmentClient(cfg),
		TaskAssignmentView:             NewTaskAssignmentViewClient(cfg),
		TaskDepartment:                 NewTaskDepartmentClient(cfg),
		TaskHistory:                    NewTaskHistoryClient(cfg),
		TaskNote:                       NewTaskNoteClient(cfg),
		TaskRecurring:                  NewTaskRecurringClient(cfg),
		TaskSerialView:                 NewTaskSerialViewClient(cfg),
		Term:                           NewTermClient(cfg),
		Track:                          NewTrackClient(cfg),
		UnifiedHistoryView:             NewUnifiedHistoryViewClient(cfg),
		User:                           NewUserClient(cfg),
		UserData:                       NewUserDataClient(cfg),
	}, nil
}

// Debug returns a new debug-client. It's used to get verbose logging on specific operations.
//
//	client.Debug().
//		ActivityView.
//		Query().
//		Count(ctx)
func (c *Client) Debug() *Client {
	if c.debug {
		return c
	}
	cfg := c.config
	cfg.driver = dialect.Debug(c.driver, c.log)
	client := &Client{config: cfg}
	client.init()
	return client
}

// Close closes the database connection and prevents new queries from starting.
func (c *Client) Close() error {
	return c.driver.Close()
}

// Use adds the mutation hooks to all the entity clients.
// In order to add hooks to a specific client, call: `client.Node.Use(...)`.
func (c *Client) Use(hooks ...Hook) {
	for _, n := range []interface{ Use(...Hook) }{
		c.Appointment, c.AppointmentHistory, c.Attachment, c.AttachmentData, c.Bill,
		c.BillData, c.BillItem, c.Bundle, c.Call, c.CasbinRule, c.CashFlow,
		c.CashFlowItem, c.CashFlowNote, c.DbsandentistDboAppointmentTime,
		c.DbsandentistDboBill, c.DbsandentistDboBillItem,
		c.DbsandentistDboBillItemDetail, c.DbsandentistDboBillPayment,
		c.DbsandentistDboCustomer, c.Deal, c.DealHistory, c.DealStageHistory,
		c.DealUser, c.DealUserRating, c.Department, c.Deposit, c.DepositAllocation,
		c.DepositPayment, c.Discount, c.DiscountUsage, c.EntityHistory, c.ExportJob,
		c.File, c.FileUsage, c.FormSubmission, c.Installment, c.InstallmentPlan,
		c.Issue, c.IssueComment, c.LocalDistrict, c.LocalProvince, c.LocalWard,
		c.Material, c.MaterialUsage, c.MessageHistory, c.Note, c.Notification, c.OTP,
		c.Operation, c.OperationMaterial, c.Organization, c.Payment,
		c.PaymentAllocation, c.Person, c.PersonAssignment, c.PersonData,
		c.PersonHistory, c.PersonReferral, c.Pipeline, c.Product, c.ProductOperation,
		c.Referral, c.Schedule, c.Setting, c.Stage, c.Tag, c.TagDeal, c.TagPerson,
		c.Task, c.TaskAssignment, c.TaskDepartment, c.TaskHistory, c.TaskNote,
		c.TaskRecurring, c.Term, c.Track, c.User, c.UserData,
	} {
		n.Use(hooks...)
	}
}

// Intercept adds the query interceptors to all the entity clients.
// In order to add interceptors to a specific client, call: `client.Node.Intercept(...)`.
func (c *Client) Intercept(interceptors ...Interceptor) {
	for _, n := range []interface{ Intercept(...Interceptor) }{
		c.ActivityView, c.Appointment, c.AppointmentHistory, c.Attachment,
		c.AttachmentData, c.AttachmentOperationReportView, c.Bill, c.BillData,
		c.BillItem, c.Bundle, c.Call, c.CasbinRule, c.CashFlow, c.CashFlowItem,
		c.CashFlowNote, c.DbsandentistDboAppointmentTime, c.DbsandentistDboBill,
		c.DbsandentistDboBillItem, c.DbsandentistDboBillItemDetail,
		c.DbsandentistDboBillPayment, c.DbsandentistDboCustomer, c.Deal, c.DealHistory,
		c.DealStageHistory, c.DealStageHistoryView, c.DealUser, c.DealUserRating,
		c.Department, c.Deposit, c.DepositAllocation, c.DepositPayment, c.Discount,
		c.DiscountUsage, c.EntityHistory, c.ExportJob, c.File, c.FileUsage,
		c.FormSubmission, c.Installment, c.InstallmentPlan, c.Issue, c.IssueComment,
		c.LocalDistrict, c.LocalProvince, c.LocalWard, c.Material, c.MaterialUsage,
		c.MessageHistory, c.NewTracksReportView, c.Note, c.Notification, c.OTP,
		c.Operation, c.OperationMaterial, c.Organization, c.Payment,
		c.PaymentAllocation, c.PaymentReportDetailView, c.PaymentReportView, c.Person,
		c.PersonAssignment, c.PersonData, c.PersonHistory, c.PersonQueryView,
		c.PersonReferral, c.PersonTimelineView, c.PhoneViewHistory, c.Pipeline,
		c.Product, c.ProductOperation, c.Referral, c.Schedule, c.Setting, c.Stage,
		c.Tag, c.TagDeal, c.TagPerson, c.Task, c.TaskAssignment, c.TaskAssignmentView,
		c.TaskDepartment, c.TaskHistory, c.TaskNote, c.TaskRecurring, c.TaskSerialView,
		c.Term, c.Track, c.UnifiedHistoryView, c.User, c.UserData,
	} {
		n.Intercept(interceptors...)
	}
}

// Mutate implements the ent.Mutator interface.
func (c *Client) Mutate(ctx context.Context, m Mutation) (Value, error) {
	switch m := m.(type) {
	case *AppointmentMutation:
		return c.Appointment.mutate(ctx, m)
	case *AppointmentHistoryMutation:
		return c.AppointmentHistory.mutate(ctx, m)
	case *AttachmentMutation:
		return c.Attachment.mutate(ctx, m)
	case *AttachmentDataMutation:
		return c.AttachmentData.mutate(ctx, m)
	case *BillMutation:
		return c.Bill.mutate(ctx, m)
	case *BillDataMutation:
		return c.BillData.mutate(ctx, m)
	case *BillItemMutation:
		return c.BillItem.mutate(ctx, m)
	case *BundleMutation:
		return c.Bundle.mutate(ctx, m)
	case *CallMutation:
		return c.Call.mutate(ctx, m)
	case *CasbinRuleMutation:
		return c.CasbinRule.mutate(ctx, m)
	case *CashFlowMutation:
		return c.CashFlow.mutate(ctx, m)
	case *CashFlowItemMutation:
		return c.CashFlowItem.mutate(ctx, m)
	case *CashFlowNoteMutation:
		return c.CashFlowNote.mutate(ctx, m)
	case *DbsandentistDboAppointmentTimeMutation:
		return c.DbsandentistDboAppointmentTime.mutate(ctx, m)
	case *DbsandentistDboBillMutation:
		return c.DbsandentistDboBill.mutate(ctx, m)
	case *DbsandentistDboBillItemMutation:
		return c.DbsandentistDboBillItem.mutate(ctx, m)
	case *DbsandentistDboBillItemDetailMutation:
		return c.DbsandentistDboBillItemDetail.mutate(ctx, m)
	case *DbsandentistDboBillPaymentMutation:
		return c.DbsandentistDboBillPayment.mutate(ctx, m)
	case *DbsandentistDboCustomerMutation:
		return c.DbsandentistDboCustomer.mutate(ctx, m)
	case *DealMutation:
		return c.Deal.mutate(ctx, m)
	case *DealHistoryMutation:
		return c.DealHistory.mutate(ctx, m)
	case *DealStageHistoryMutation:
		return c.DealStageHistory.mutate(ctx, m)
	case *DealUserMutation:
		return c.DealUser.mutate(ctx, m)
	case *DealUserRatingMutation:
		return c.DealUserRating.mutate(ctx, m)
	case *DepartmentMutation:
		return c.Department.mutate(ctx, m)
	case *DepositMutation:
		return c.Deposit.mutate(ctx, m)
	case *DepositAllocationMutation:
		return c.DepositAllocation.mutate(ctx, m)
	case *DepositPaymentMutation:
		return c.DepositPayment.mutate(ctx, m)
	case *DiscountMutation:
		return c.Discount.mutate(ctx, m)
	case *DiscountUsageMutation:
		return c.DiscountUsage.mutate(ctx, m)
	case *EntityHistoryMutation:
		return c.EntityHistory.mutate(ctx, m)
	case *ExportJobMutation:
		return c.ExportJob.mutate(ctx, m)
	case *FileMutation:
		return c.File.mutate(ctx, m)
	case *FileUsageMutation:
		return c.FileUsage.mutate(ctx, m)
	case *FormSubmissionMutation:
		return c.FormSubmission.mutate(ctx, m)
	case *InstallmentMutation:
		return c.Installment.mutate(ctx, m)
	case *InstallmentPlanMutation:
		return c.InstallmentPlan.mutate(ctx, m)
	case *IssueMutation:
		return c.Issue.mutate(ctx, m)
	case *IssueCommentMutation:
		return c.IssueComment.mutate(ctx, m)
	case *LocalDistrictMutation:
		return c.LocalDistrict.mutate(ctx, m)
	case *LocalProvinceMutation:
		return c.LocalProvince.mutate(ctx, m)
	case *LocalWardMutation:
		return c.LocalWard.mutate(ctx, m)
	case *MaterialMutation:
		return c.Material.mutate(ctx, m)
	case *MaterialUsageMutation:
		return c.MaterialUsage.mutate(ctx, m)
	case *MessageHistoryMutation:
		return c.MessageHistory.mutate(ctx, m)
	case *NoteMutation:
		return c.Note.mutate(ctx, m)
	case *NotificationMutation:
		return c.Notification.mutate(ctx, m)
	case *OTPMutation:
		return c.OTP.mutate(ctx, m)
	case *OperationMutation:
		return c.Operation.mutate(ctx, m)
	case *OperationMaterialMutation:
		return c.OperationMaterial.mutate(ctx, m)
	case *OrganizationMutation:
		return c.Organization.mutate(ctx, m)
	case *PaymentMutation:
		return c.Payment.mutate(ctx, m)
	case *PaymentAllocationMutation:
		return c.PaymentAllocation.mutate(ctx, m)
	case *PersonMutation:
		return c.Person.mutate(ctx, m)
	case *PersonAssignmentMutation:
		return c.PersonAssignment.mutate(ctx, m)
	case *PersonDataMutation:
		return c.PersonData.mutate(ctx, m)
	case *PersonHistoryMutation:
		return c.PersonHistory.mutate(ctx, m)
	case *PersonReferralMutation:
		return c.PersonReferral.mutate(ctx, m)
	case *PipelineMutation:
		return c.Pipeline.mutate(ctx, m)
	case *ProductMutation:
		return c.Product.mutate(ctx, m)
	case *ProductOperationMutation:
		return c.ProductOperation.mutate(ctx, m)
	case *ReferralMutation:
		return c.Referral.mutate(ctx, m)
	case *ScheduleMutation:
		return c.Schedule.mutate(ctx, m)
	case *SettingMutation:
		return c.Setting.mutate(ctx, m)
	case *StageMutation:
		return c.Stage.mutate(ctx, m)
	case *TagMutation:
		return c.Tag.mutate(ctx, m)
	case *TagDealMutation:
		return c.TagDeal.mutate(ctx, m)
	case *TagPersonMutation:
		return c.TagPerson.mutate(ctx, m)
	case *TaskMutation:
		return c.Task.mutate(ctx, m)
	case *TaskAssignmentMutation:
		return c.TaskAssignment.mutate(ctx, m)
	case *TaskDepartmentMutation:
		return c.TaskDepartment.mutate(ctx, m)
	case *TaskHistoryMutation:
		return c.TaskHistory.mutate(ctx, m)
	case *TaskNoteMutation:
		return c.TaskNote.mutate(ctx, m)
	case *TaskRecurringMutation:
		return c.TaskRecurring.mutate(ctx, m)
	case *TermMutation:
		return c.Term.mutate(ctx, m)
	case *TrackMutation:
		return c.Track.mutate(ctx, m)
	case *UserMutation:
		return c.User.mutate(ctx, m)
	case *UserDataMutation:
		return c.UserData.mutate(ctx, m)
	default:
		return nil, fmt.Errorf("ent: unknown mutation type %T", m)
	}
}

// ActivityViewClient is a client for the ActivityView schema.
type ActivityViewClient struct {
	config
}

// NewActivityViewClient returns a client for the ActivityView from the given config.
func NewActivityViewClient(c config) *ActivityViewClient {
	return &ActivityViewClient{config: c}
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `activityview.Intercept(f(g(h())))`.
func (c *ActivityViewClient) Intercept(interceptors ...Interceptor) {
	c.inters.ActivityView = append(c.inters.ActivityView, interceptors...)
}

// Query returns a query builder for ActivityView.
func (c *ActivityViewClient) Query() *ActivityViewQuery {
	return &ActivityViewQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeActivityView},
		inters: c.Interceptors(),
	}
}

// Interceptors returns the client interceptors.
func (c *ActivityViewClient) Interceptors() []Interceptor {
	return c.inters.ActivityView
}

// AppointmentClient is a client for the Appointment schema.
type AppointmentClient struct {
	config
}

// NewAppointmentClient returns a client for the Appointment from the given config.
func NewAppointmentClient(c config) *AppointmentClient {
	return &AppointmentClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `appointment.Hooks(f(g(h())))`.
func (c *AppointmentClient) Use(hooks ...Hook) {
	c.hooks.Appointment = append(c.hooks.Appointment, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `appointment.Intercept(f(g(h())))`.
func (c *AppointmentClient) Intercept(interceptors ...Interceptor) {
	c.inters.Appointment = append(c.inters.Appointment, interceptors...)
}

// Create returns a builder for creating a Appointment entity.
func (c *AppointmentClient) Create() *AppointmentCreate {
	mutation := newAppointmentMutation(c.config, OpCreate)
	return &AppointmentCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Appointment entities.
func (c *AppointmentClient) CreateBulk(builders ...*AppointmentCreate) *AppointmentCreateBulk {
	return &AppointmentCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *AppointmentClient) MapCreateBulk(slice any, setFunc func(*AppointmentCreate, int)) *AppointmentCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &AppointmentCreateBulk{err: fmt.Errorf("calling to AppointmentClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*AppointmentCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &AppointmentCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Appointment.
func (c *AppointmentClient) Update() *AppointmentUpdate {
	mutation := newAppointmentMutation(c.config, OpUpdate)
	return &AppointmentUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *AppointmentClient) UpdateOne(a *Appointment) *AppointmentUpdateOne {
	mutation := newAppointmentMutation(c.config, OpUpdateOne, withAppointment(a))
	return &AppointmentUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *AppointmentClient) UpdateOneID(id int) *AppointmentUpdateOne {
	mutation := newAppointmentMutation(c.config, OpUpdateOne, withAppointmentID(id))
	return &AppointmentUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Appointment.
func (c *AppointmentClient) Delete() *AppointmentDelete {
	mutation := newAppointmentMutation(c.config, OpDelete)
	return &AppointmentDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *AppointmentClient) DeleteOne(a *Appointment) *AppointmentDeleteOne {
	return c.DeleteOneID(a.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *AppointmentClient) DeleteOneID(id int) *AppointmentDeleteOne {
	builder := c.Delete().Where(appointment.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &AppointmentDeleteOne{builder}
}

// Query returns a query builder for Appointment.
func (c *AppointmentClient) Query() *AppointmentQuery {
	return &AppointmentQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeAppointment},
		inters: c.Interceptors(),
	}
}

// Get returns a Appointment entity by its id.
func (c *AppointmentClient) Get(ctx context.Context, id int) (*Appointment, error) {
	return c.Query().Where(appointment.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *AppointmentClient) GetX(ctx context.Context, id int) *Appointment {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryCreator queries the creator edge of a Appointment.
func (c *AppointmentClient) QueryCreator(a *Appointment) *UserQuery {
	query := (&UserClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := a.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(appointment.Table, appointment.FieldID, id),
			sqlgraph.To(user.Table, user.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, appointment.CreatorTable, appointment.CreatorColumn),
		)
		fromV = sqlgraph.Neighbors(a.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryPerson queries the person edge of a Appointment.
func (c *AppointmentClient) QueryPerson(a *Appointment) *PersonQuery {
	query := (&PersonClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := a.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(appointment.Table, appointment.FieldID, id),
			sqlgraph.To(person.Table, person.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, appointment.PersonTable, appointment.PersonColumn),
		)
		fromV = sqlgraph.Neighbors(a.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryDoctor queries the doctor edge of a Appointment.
func (c *AppointmentClient) QueryDoctor(a *Appointment) *UserQuery {
	query := (&UserClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := a.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(appointment.Table, appointment.FieldID, id),
			sqlgraph.To(user.Table, user.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, appointment.DoctorTable, appointment.DoctorColumn),
		)
		fromV = sqlgraph.Neighbors(a.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryTrack queries the track edge of a Appointment.
func (c *AppointmentClient) QueryTrack(a *Appointment) *TrackQuery {
	query := (&TrackClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := a.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(appointment.Table, appointment.FieldID, id),
			sqlgraph.To(track.Table, track.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, appointment.TrackTable, appointment.TrackColumn),
		)
		fromV = sqlgraph.Neighbors(a.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *AppointmentClient) Hooks() []Hook {
	hooks := c.hooks.Appointment
	return append(hooks[:len(hooks):len(hooks)], appointment.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *AppointmentClient) Interceptors() []Interceptor {
	inters := c.inters.Appointment
	return append(inters[:len(inters):len(inters)], appointment.Interceptors[:]...)
}

func (c *AppointmentClient) mutate(ctx context.Context, m *AppointmentMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&AppointmentCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&AppointmentUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&AppointmentUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&AppointmentDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Appointment mutation op: %q", m.Op())
	}
}

// AppointmentHistoryClient is a client for the AppointmentHistory schema.
type AppointmentHistoryClient struct {
	config
}

// NewAppointmentHistoryClient returns a client for the AppointmentHistory from the given config.
func NewAppointmentHistoryClient(c config) *AppointmentHistoryClient {
	return &AppointmentHistoryClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `appointmenthistory.Hooks(f(g(h())))`.
func (c *AppointmentHistoryClient) Use(hooks ...Hook) {
	c.hooks.AppointmentHistory = append(c.hooks.AppointmentHistory, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `appointmenthistory.Intercept(f(g(h())))`.
func (c *AppointmentHistoryClient) Intercept(interceptors ...Interceptor) {
	c.inters.AppointmentHistory = append(c.inters.AppointmentHistory, interceptors...)
}

// Create returns a builder for creating a AppointmentHistory entity.
func (c *AppointmentHistoryClient) Create() *AppointmentHistoryCreate {
	mutation := newAppointmentHistoryMutation(c.config, OpCreate)
	return &AppointmentHistoryCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of AppointmentHistory entities.
func (c *AppointmentHistoryClient) CreateBulk(builders ...*AppointmentHistoryCreate) *AppointmentHistoryCreateBulk {
	return &AppointmentHistoryCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *AppointmentHistoryClient) MapCreateBulk(slice any, setFunc func(*AppointmentHistoryCreate, int)) *AppointmentHistoryCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &AppointmentHistoryCreateBulk{err: fmt.Errorf("calling to AppointmentHistoryClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*AppointmentHistoryCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &AppointmentHistoryCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for AppointmentHistory.
func (c *AppointmentHistoryClient) Update() *AppointmentHistoryUpdate {
	mutation := newAppointmentHistoryMutation(c.config, OpUpdate)
	return &AppointmentHistoryUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *AppointmentHistoryClient) UpdateOne(ah *AppointmentHistory) *AppointmentHistoryUpdateOne {
	mutation := newAppointmentHistoryMutation(c.config, OpUpdateOne, withAppointmentHistory(ah))
	return &AppointmentHistoryUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *AppointmentHistoryClient) UpdateOneID(id int) *AppointmentHistoryUpdateOne {
	mutation := newAppointmentHistoryMutation(c.config, OpUpdateOne, withAppointmentHistoryID(id))
	return &AppointmentHistoryUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for AppointmentHistory.
func (c *AppointmentHistoryClient) Delete() *AppointmentHistoryDelete {
	mutation := newAppointmentHistoryMutation(c.config, OpDelete)
	return &AppointmentHistoryDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *AppointmentHistoryClient) DeleteOne(ah *AppointmentHistory) *AppointmentHistoryDeleteOne {
	return c.DeleteOneID(ah.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *AppointmentHistoryClient) DeleteOneID(id int) *AppointmentHistoryDeleteOne {
	builder := c.Delete().Where(appointmenthistory.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &AppointmentHistoryDeleteOne{builder}
}

// Query returns a query builder for AppointmentHistory.
func (c *AppointmentHistoryClient) Query() *AppointmentHistoryQuery {
	return &AppointmentHistoryQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeAppointmentHistory},
		inters: c.Interceptors(),
	}
}

// Get returns a AppointmentHistory entity by its id.
func (c *AppointmentHistoryClient) Get(ctx context.Context, id int) (*AppointmentHistory, error) {
	return c.Query().Where(appointmenthistory.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *AppointmentHistoryClient) GetX(ctx context.Context, id int) *AppointmentHistory {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *AppointmentHistoryClient) Hooks() []Hook {
	return c.hooks.AppointmentHistory
}

// Interceptors returns the client interceptors.
func (c *AppointmentHistoryClient) Interceptors() []Interceptor {
	return c.inters.AppointmentHistory
}

func (c *AppointmentHistoryClient) mutate(ctx context.Context, m *AppointmentHistoryMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&AppointmentHistoryCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&AppointmentHistoryUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&AppointmentHistoryUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&AppointmentHistoryDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown AppointmentHistory mutation op: %q", m.Op())
	}
}

// AttachmentClient is a client for the Attachment schema.
type AttachmentClient struct {
	config
}

// NewAttachmentClient returns a client for the Attachment from the given config.
func NewAttachmentClient(c config) *AttachmentClient {
	return &AttachmentClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `attachment.Hooks(f(g(h())))`.
func (c *AttachmentClient) Use(hooks ...Hook) {
	c.hooks.Attachment = append(c.hooks.Attachment, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `attachment.Intercept(f(g(h())))`.
func (c *AttachmentClient) Intercept(interceptors ...Interceptor) {
	c.inters.Attachment = append(c.inters.Attachment, interceptors...)
}

// Create returns a builder for creating a Attachment entity.
func (c *AttachmentClient) Create() *AttachmentCreate {
	mutation := newAttachmentMutation(c.config, OpCreate)
	return &AttachmentCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Attachment entities.
func (c *AttachmentClient) CreateBulk(builders ...*AttachmentCreate) *AttachmentCreateBulk {
	return &AttachmentCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *AttachmentClient) MapCreateBulk(slice any, setFunc func(*AttachmentCreate, int)) *AttachmentCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &AttachmentCreateBulk{err: fmt.Errorf("calling to AttachmentClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*AttachmentCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &AttachmentCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Attachment.
func (c *AttachmentClient) Update() *AttachmentUpdate {
	mutation := newAttachmentMutation(c.config, OpUpdate)
	return &AttachmentUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *AttachmentClient) UpdateOne(a *Attachment) *AttachmentUpdateOne {
	mutation := newAttachmentMutation(c.config, OpUpdateOne, withAttachment(a))
	return &AttachmentUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *AttachmentClient) UpdateOneID(id int) *AttachmentUpdateOne {
	mutation := newAttachmentMutation(c.config, OpUpdateOne, withAttachmentID(id))
	return &AttachmentUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Attachment.
func (c *AttachmentClient) Delete() *AttachmentDelete {
	mutation := newAttachmentMutation(c.config, OpDelete)
	return &AttachmentDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *AttachmentClient) DeleteOne(a *Attachment) *AttachmentDeleteOne {
	return c.DeleteOneID(a.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *AttachmentClient) DeleteOneID(id int) *AttachmentDeleteOne {
	builder := c.Delete().Where(attachment.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &AttachmentDeleteOne{builder}
}

// Query returns a query builder for Attachment.
func (c *AttachmentClient) Query() *AttachmentQuery {
	return &AttachmentQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeAttachment},
		inters: c.Interceptors(),
	}
}

// Get returns a Attachment entity by its id.
func (c *AttachmentClient) Get(ctx context.Context, id int) (*Attachment, error) {
	return c.Query().Where(attachment.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *AttachmentClient) GetX(ctx context.Context, id int) *Attachment {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryParent queries the parent edge of a Attachment.
func (c *AttachmentClient) QueryParent(a *Attachment) *AttachmentQuery {
	query := (&AttachmentClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := a.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(attachment.Table, attachment.FieldID, id),
			sqlgraph.To(attachment.Table, attachment.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, attachment.ParentTable, attachment.ParentColumn),
		)
		fromV = sqlgraph.Neighbors(a.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryChildren queries the children edge of a Attachment.
func (c *AttachmentClient) QueryChildren(a *Attachment) *AttachmentQuery {
	query := (&AttachmentClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := a.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(attachment.Table, attachment.FieldID, id),
			sqlgraph.To(attachment.Table, attachment.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, attachment.ChildrenTable, attachment.ChildrenColumn),
		)
		fromV = sqlgraph.Neighbors(a.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryDeal queries the deal edge of a Attachment.
func (c *AttachmentClient) QueryDeal(a *Attachment) *DealQuery {
	query := (&DealClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := a.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(attachment.Table, attachment.FieldID, id),
			sqlgraph.To(deal.Table, deal.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, attachment.DealTable, attachment.DealColumn),
		)
		fromV = sqlgraph.Neighbors(a.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryPerson queries the person edge of a Attachment.
func (c *AttachmentClient) QueryPerson(a *Attachment) *PersonQuery {
	query := (&PersonClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := a.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(attachment.Table, attachment.FieldID, id),
			sqlgraph.To(person.Table, person.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, attachment.PersonTable, attachment.PersonColumn),
		)
		fromV = sqlgraph.Neighbors(a.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryPlan queries the plan edge of a Attachment.
func (c *AttachmentClient) QueryPlan(a *Attachment) *InstallmentPlanQuery {
	query := (&InstallmentPlanClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := a.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(attachment.Table, attachment.FieldID, id),
			sqlgraph.To(installmentplan.Table, installmentplan.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, attachment.PlanTable, attachment.PlanColumn),
		)
		fromV = sqlgraph.Neighbors(a.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryCreator queries the creator edge of a Attachment.
func (c *AttachmentClient) QueryCreator(a *Attachment) *UserQuery {
	query := (&UserClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := a.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(attachment.Table, attachment.FieldID, id),
			sqlgraph.To(user.Table, user.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, attachment.CreatorTable, attachment.CreatorColumn),
		)
		fromV = sqlgraph.Neighbors(a.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryBillItem queries the bill_item edge of a Attachment.
func (c *AttachmentClient) QueryBillItem(a *Attachment) *BillItemQuery {
	query := (&BillItemClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := a.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(attachment.Table, attachment.FieldID, id),
			sqlgraph.To(billitem.Table, billitem.FieldID),
			sqlgraph.Edge(sqlgraph.O2O, false, attachment.BillItemTable, attachment.BillItemColumn),
		)
		fromV = sqlgraph.Neighbors(a.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryProduct queries the product edge of a Attachment.
func (c *AttachmentClient) QueryProduct(a *Attachment) *ProductQuery {
	query := (&ProductClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := a.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(attachment.Table, attachment.FieldID, id),
			sqlgraph.To(product.Table, product.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, attachment.ProductTable, attachment.ProductColumn),
		)
		fromV = sqlgraph.Neighbors(a.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryData queries the data edge of a Attachment.
func (c *AttachmentClient) QueryData(a *Attachment) *AttachmentDataQuery {
	query := (&AttachmentDataClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := a.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(attachment.Table, attachment.FieldID, id),
			sqlgraph.To(attachmentdata.Table, attachmentdata.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, attachment.DataTable, attachment.DataColumn),
		)
		fromV = sqlgraph.Neighbors(a.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryDepositAllocations queries the deposit_allocations edge of a Attachment.
func (c *AttachmentClient) QueryDepositAllocations(a *Attachment) *DepositAllocationQuery {
	query := (&DepositAllocationClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := a.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(attachment.Table, attachment.FieldID, id),
			sqlgraph.To(depositallocation.Table, depositallocation.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, attachment.DepositAllocationsTable, attachment.DepositAllocationsColumn),
		)
		fromV = sqlgraph.Neighbors(a.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryTrack queries the track edge of a Attachment.
func (c *AttachmentClient) QueryTrack(a *Attachment) *TrackQuery {
	query := (&TrackClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := a.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(attachment.Table, attachment.FieldID, id),
			sqlgraph.To(track.Table, track.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, attachment.TrackTable, attachment.TrackColumn),
		)
		fromV = sqlgraph.Neighbors(a.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *AttachmentClient) Hooks() []Hook {
	hooks := c.hooks.Attachment
	return append(hooks[:len(hooks):len(hooks)], attachment.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *AttachmentClient) Interceptors() []Interceptor {
	inters := c.inters.Attachment
	return append(inters[:len(inters):len(inters)], attachment.Interceptors[:]...)
}

func (c *AttachmentClient) mutate(ctx context.Context, m *AttachmentMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&AttachmentCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&AttachmentUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&AttachmentUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&AttachmentDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Attachment mutation op: %q", m.Op())
	}
}

// AttachmentDataClient is a client for the AttachmentData schema.
type AttachmentDataClient struct {
	config
}

// NewAttachmentDataClient returns a client for the AttachmentData from the given config.
func NewAttachmentDataClient(c config) *AttachmentDataClient {
	return &AttachmentDataClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `attachmentdata.Hooks(f(g(h())))`.
func (c *AttachmentDataClient) Use(hooks ...Hook) {
	c.hooks.AttachmentData = append(c.hooks.AttachmentData, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `attachmentdata.Intercept(f(g(h())))`.
func (c *AttachmentDataClient) Intercept(interceptors ...Interceptor) {
	c.inters.AttachmentData = append(c.inters.AttachmentData, interceptors...)
}

// Create returns a builder for creating a AttachmentData entity.
func (c *AttachmentDataClient) Create() *AttachmentDataCreate {
	mutation := newAttachmentDataMutation(c.config, OpCreate)
	return &AttachmentDataCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of AttachmentData entities.
func (c *AttachmentDataClient) CreateBulk(builders ...*AttachmentDataCreate) *AttachmentDataCreateBulk {
	return &AttachmentDataCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *AttachmentDataClient) MapCreateBulk(slice any, setFunc func(*AttachmentDataCreate, int)) *AttachmentDataCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &AttachmentDataCreateBulk{err: fmt.Errorf("calling to AttachmentDataClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*AttachmentDataCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &AttachmentDataCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for AttachmentData.
func (c *AttachmentDataClient) Update() *AttachmentDataUpdate {
	mutation := newAttachmentDataMutation(c.config, OpUpdate)
	return &AttachmentDataUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *AttachmentDataClient) UpdateOne(ad *AttachmentData) *AttachmentDataUpdateOne {
	mutation := newAttachmentDataMutation(c.config, OpUpdateOne, withAttachmentData(ad))
	return &AttachmentDataUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *AttachmentDataClient) UpdateOneID(id int) *AttachmentDataUpdateOne {
	mutation := newAttachmentDataMutation(c.config, OpUpdateOne, withAttachmentDataID(id))
	return &AttachmentDataUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for AttachmentData.
func (c *AttachmentDataClient) Delete() *AttachmentDataDelete {
	mutation := newAttachmentDataMutation(c.config, OpDelete)
	return &AttachmentDataDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *AttachmentDataClient) DeleteOne(ad *AttachmentData) *AttachmentDataDeleteOne {
	return c.DeleteOneID(ad.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *AttachmentDataClient) DeleteOneID(id int) *AttachmentDataDeleteOne {
	builder := c.Delete().Where(attachmentdata.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &AttachmentDataDeleteOne{builder}
}

// Query returns a query builder for AttachmentData.
func (c *AttachmentDataClient) Query() *AttachmentDataQuery {
	return &AttachmentDataQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeAttachmentData},
		inters: c.Interceptors(),
	}
}

// Get returns a AttachmentData entity by its id.
func (c *AttachmentDataClient) Get(ctx context.Context, id int) (*AttachmentData, error) {
	return c.Query().Where(attachmentdata.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *AttachmentDataClient) GetX(ctx context.Context, id int) *AttachmentData {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryCreator queries the creator edge of a AttachmentData.
func (c *AttachmentDataClient) QueryCreator(ad *AttachmentData) *UserQuery {
	query := (&UserClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := ad.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(attachmentdata.Table, attachmentdata.FieldID, id),
			sqlgraph.To(user.Table, user.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, attachmentdata.CreatorTable, attachmentdata.CreatorColumn),
		)
		fromV = sqlgraph.Neighbors(ad.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryParticipant queries the participant edge of a AttachmentData.
func (c *AttachmentDataClient) QueryParticipant(ad *AttachmentData) *UserQuery {
	query := (&UserClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := ad.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(attachmentdata.Table, attachmentdata.FieldID, id),
			sqlgraph.To(user.Table, user.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, attachmentdata.ParticipantTable, attachmentdata.ParticipantColumn),
		)
		fromV = sqlgraph.Neighbors(ad.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryAttachment queries the attachment edge of a AttachmentData.
func (c *AttachmentDataClient) QueryAttachment(ad *AttachmentData) *AttachmentQuery {
	query := (&AttachmentClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := ad.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(attachmentdata.Table, attachmentdata.FieldID, id),
			sqlgraph.To(attachment.Table, attachment.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, attachmentdata.AttachmentTable, attachmentdata.AttachmentColumn),
		)
		fromV = sqlgraph.Neighbors(ad.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *AttachmentDataClient) Hooks() []Hook {
	return c.hooks.AttachmentData
}

// Interceptors returns the client interceptors.
func (c *AttachmentDataClient) Interceptors() []Interceptor {
	return c.inters.AttachmentData
}

func (c *AttachmentDataClient) mutate(ctx context.Context, m *AttachmentDataMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&AttachmentDataCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&AttachmentDataUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&AttachmentDataUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&AttachmentDataDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown AttachmentData mutation op: %q", m.Op())
	}
}

// AttachmentOperationReportViewClient is a client for the AttachmentOperationReportView schema.
type AttachmentOperationReportViewClient struct {
	config
}

// NewAttachmentOperationReportViewClient returns a client for the AttachmentOperationReportView from the given config.
func NewAttachmentOperationReportViewClient(c config) *AttachmentOperationReportViewClient {
	return &AttachmentOperationReportViewClient{config: c}
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `attachmentoperationreportview.Intercept(f(g(h())))`.
func (c *AttachmentOperationReportViewClient) Intercept(interceptors ...Interceptor) {
	c.inters.AttachmentOperationReportView = append(c.inters.AttachmentOperationReportView, interceptors...)
}

// Query returns a query builder for AttachmentOperationReportView.
func (c *AttachmentOperationReportViewClient) Query() *AttachmentOperationReportViewQuery {
	return &AttachmentOperationReportViewQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeAttachmentOperationReportView},
		inters: c.Interceptors(),
	}
}

// Interceptors returns the client interceptors.
func (c *AttachmentOperationReportViewClient) Interceptors() []Interceptor {
	return c.inters.AttachmentOperationReportView
}

// BillClient is a client for the Bill schema.
type BillClient struct {
	config
}

// NewBillClient returns a client for the Bill from the given config.
func NewBillClient(c config) *BillClient {
	return &BillClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `bill.Hooks(f(g(h())))`.
func (c *BillClient) Use(hooks ...Hook) {
	c.hooks.Bill = append(c.hooks.Bill, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `bill.Intercept(f(g(h())))`.
func (c *BillClient) Intercept(interceptors ...Interceptor) {
	c.inters.Bill = append(c.inters.Bill, interceptors...)
}

// Create returns a builder for creating a Bill entity.
func (c *BillClient) Create() *BillCreate {
	mutation := newBillMutation(c.config, OpCreate)
	return &BillCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Bill entities.
func (c *BillClient) CreateBulk(builders ...*BillCreate) *BillCreateBulk {
	return &BillCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *BillClient) MapCreateBulk(slice any, setFunc func(*BillCreate, int)) *BillCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &BillCreateBulk{err: fmt.Errorf("calling to BillClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*BillCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &BillCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Bill.
func (c *BillClient) Update() *BillUpdate {
	mutation := newBillMutation(c.config, OpUpdate)
	return &BillUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *BillClient) UpdateOne(b *Bill) *BillUpdateOne {
	mutation := newBillMutation(c.config, OpUpdateOne, withBill(b))
	return &BillUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *BillClient) UpdateOneID(id int) *BillUpdateOne {
	mutation := newBillMutation(c.config, OpUpdateOne, withBillID(id))
	return &BillUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Bill.
func (c *BillClient) Delete() *BillDelete {
	mutation := newBillMutation(c.config, OpDelete)
	return &BillDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *BillClient) DeleteOne(b *Bill) *BillDeleteOne {
	return c.DeleteOneID(b.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *BillClient) DeleteOneID(id int) *BillDeleteOne {
	builder := c.Delete().Where(bill.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &BillDeleteOne{builder}
}

// Query returns a query builder for Bill.
func (c *BillClient) Query() *BillQuery {
	return &BillQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeBill},
		inters: c.Interceptors(),
	}
}

// Get returns a Bill entity by its id.
func (c *BillClient) Get(ctx context.Context, id int) (*Bill, error) {
	return c.Query().Where(bill.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *BillClient) GetX(ctx context.Context, id int) *Bill {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryDeal queries the deal edge of a Bill.
func (c *BillClient) QueryDeal(b *Bill) *DealQuery {
	query := (&DealClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := b.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(bill.Table, bill.FieldID, id),
			sqlgraph.To(deal.Table, deal.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, bill.DealTable, bill.DealColumn),
		)
		fromV = sqlgraph.Neighbors(b.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryPerson queries the person edge of a Bill.
func (c *BillClient) QueryPerson(b *Bill) *PersonQuery {
	query := (&PersonClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := b.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(bill.Table, bill.FieldID, id),
			sqlgraph.To(person.Table, person.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, bill.PersonTable, bill.PersonColumn),
		)
		fromV = sqlgraph.Neighbors(b.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryUser queries the user edge of a Bill.
func (c *BillClient) QueryUser(b *Bill) *UserQuery {
	query := (&UserClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := b.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(bill.Table, bill.FieldID, id),
			sqlgraph.To(user.Table, user.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, bill.UserTable, bill.UserColumn),
		)
		fromV = sqlgraph.Neighbors(b.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryTrack queries the track edge of a Bill.
func (c *BillClient) QueryTrack(b *Bill) *TrackQuery {
	query := (&TrackClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := b.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(bill.Table, bill.FieldID, id),
			sqlgraph.To(track.Table, track.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, bill.TrackTable, bill.TrackColumn),
		)
		fromV = sqlgraph.Neighbors(b.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryItems queries the items edge of a Bill.
func (c *BillClient) QueryItems(b *Bill) *BillItemQuery {
	query := (&BillItemClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := b.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(bill.Table, bill.FieldID, id),
			sqlgraph.To(billitem.Table, billitem.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, bill.ItemsTable, bill.ItemsColumn),
		)
		fromV = sqlgraph.Neighbors(b.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryData queries the data edge of a Bill.
func (c *BillClient) QueryData(b *Bill) *BillDataQuery {
	query := (&BillDataClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := b.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(bill.Table, bill.FieldID, id),
			sqlgraph.To(billdata.Table, billdata.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, bill.DataTable, bill.DataColumn),
		)
		fromV = sqlgraph.Neighbors(b.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryInstallmentPlans queries the installment_plans edge of a Bill.
func (c *BillClient) QueryInstallmentPlans(b *Bill) *InstallmentPlanQuery {
	query := (&InstallmentPlanClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := b.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(bill.Table, bill.FieldID, id),
			sqlgraph.To(installmentplan.Table, installmentplan.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, bill.InstallmentPlansTable, bill.InstallmentPlansColumn),
		)
		fromV = sqlgraph.Neighbors(b.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryPayments queries the payments edge of a Bill.
func (c *BillClient) QueryPayments(b *Bill) *PaymentQuery {
	query := (&PaymentClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := b.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(bill.Table, bill.FieldID, id),
			sqlgraph.To(payment.Table, payment.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, bill.PaymentsTable, bill.PaymentsColumn),
		)
		fromV = sqlgraph.Neighbors(b.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *BillClient) Hooks() []Hook {
	hooks := c.hooks.Bill
	return append(hooks[:len(hooks):len(hooks)], bill.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *BillClient) Interceptors() []Interceptor {
	inters := c.inters.Bill
	return append(inters[:len(inters):len(inters)], bill.Interceptors[:]...)
}

func (c *BillClient) mutate(ctx context.Context, m *BillMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&BillCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&BillUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&BillUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&BillDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Bill mutation op: %q", m.Op())
	}
}

// BillDataClient is a client for the BillData schema.
type BillDataClient struct {
	config
}

// NewBillDataClient returns a client for the BillData from the given config.
func NewBillDataClient(c config) *BillDataClient {
	return &BillDataClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `billdata.Hooks(f(g(h())))`.
func (c *BillDataClient) Use(hooks ...Hook) {
	c.hooks.BillData = append(c.hooks.BillData, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `billdata.Intercept(f(g(h())))`.
func (c *BillDataClient) Intercept(interceptors ...Interceptor) {
	c.inters.BillData = append(c.inters.BillData, interceptors...)
}

// Create returns a builder for creating a BillData entity.
func (c *BillDataClient) Create() *BillDataCreate {
	mutation := newBillDataMutation(c.config, OpCreate)
	return &BillDataCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of BillData entities.
func (c *BillDataClient) CreateBulk(builders ...*BillDataCreate) *BillDataCreateBulk {
	return &BillDataCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *BillDataClient) MapCreateBulk(slice any, setFunc func(*BillDataCreate, int)) *BillDataCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &BillDataCreateBulk{err: fmt.Errorf("calling to BillDataClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*BillDataCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &BillDataCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for BillData.
func (c *BillDataClient) Update() *BillDataUpdate {
	mutation := newBillDataMutation(c.config, OpUpdate)
	return &BillDataUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *BillDataClient) UpdateOne(bd *BillData) *BillDataUpdateOne {
	mutation := newBillDataMutation(c.config, OpUpdateOne, withBillData(bd))
	return &BillDataUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *BillDataClient) UpdateOneID(id int) *BillDataUpdateOne {
	mutation := newBillDataMutation(c.config, OpUpdateOne, withBillDataID(id))
	return &BillDataUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for BillData.
func (c *BillDataClient) Delete() *BillDataDelete {
	mutation := newBillDataMutation(c.config, OpDelete)
	return &BillDataDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *BillDataClient) DeleteOne(bd *BillData) *BillDataDeleteOne {
	return c.DeleteOneID(bd.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *BillDataClient) DeleteOneID(id int) *BillDataDeleteOne {
	builder := c.Delete().Where(billdata.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &BillDataDeleteOne{builder}
}

// Query returns a query builder for BillData.
func (c *BillDataClient) Query() *BillDataQuery {
	return &BillDataQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeBillData},
		inters: c.Interceptors(),
	}
}

// Get returns a BillData entity by its id.
func (c *BillDataClient) Get(ctx context.Context, id int) (*BillData, error) {
	return c.Query().Where(billdata.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *BillDataClient) GetX(ctx context.Context, id int) *BillData {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryCreator queries the creator edge of a BillData.
func (c *BillDataClient) QueryCreator(bd *BillData) *UserQuery {
	query := (&UserClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := bd.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(billdata.Table, billdata.FieldID, id),
			sqlgraph.To(user.Table, user.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, billdata.CreatorTable, billdata.CreatorColumn),
		)
		fromV = sqlgraph.Neighbors(bd.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryBill queries the bill edge of a BillData.
func (c *BillDataClient) QueryBill(bd *BillData) *BillQuery {
	query := (&BillClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := bd.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(billdata.Table, billdata.FieldID, id),
			sqlgraph.To(bill.Table, bill.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, billdata.BillTable, billdata.BillColumn),
		)
		fromV = sqlgraph.Neighbors(bd.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *BillDataClient) Hooks() []Hook {
	return c.hooks.BillData
}

// Interceptors returns the client interceptors.
func (c *BillDataClient) Interceptors() []Interceptor {
	return c.inters.BillData
}

func (c *BillDataClient) mutate(ctx context.Context, m *BillDataMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&BillDataCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&BillDataUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&BillDataUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&BillDataDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown BillData mutation op: %q", m.Op())
	}
}

// BillItemClient is a client for the BillItem schema.
type BillItemClient struct {
	config
}

// NewBillItemClient returns a client for the BillItem from the given config.
func NewBillItemClient(c config) *BillItemClient {
	return &BillItemClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `billitem.Hooks(f(g(h())))`.
func (c *BillItemClient) Use(hooks ...Hook) {
	c.hooks.BillItem = append(c.hooks.BillItem, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `billitem.Intercept(f(g(h())))`.
func (c *BillItemClient) Intercept(interceptors ...Interceptor) {
	c.inters.BillItem = append(c.inters.BillItem, interceptors...)
}

// Create returns a builder for creating a BillItem entity.
func (c *BillItemClient) Create() *BillItemCreate {
	mutation := newBillItemMutation(c.config, OpCreate)
	return &BillItemCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of BillItem entities.
func (c *BillItemClient) CreateBulk(builders ...*BillItemCreate) *BillItemCreateBulk {
	return &BillItemCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *BillItemClient) MapCreateBulk(slice any, setFunc func(*BillItemCreate, int)) *BillItemCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &BillItemCreateBulk{err: fmt.Errorf("calling to BillItemClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*BillItemCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &BillItemCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for BillItem.
func (c *BillItemClient) Update() *BillItemUpdate {
	mutation := newBillItemMutation(c.config, OpUpdate)
	return &BillItemUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *BillItemClient) UpdateOne(bi *BillItem) *BillItemUpdateOne {
	mutation := newBillItemMutation(c.config, OpUpdateOne, withBillItem(bi))
	return &BillItemUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *BillItemClient) UpdateOneID(id int) *BillItemUpdateOne {
	mutation := newBillItemMutation(c.config, OpUpdateOne, withBillItemID(id))
	return &BillItemUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for BillItem.
func (c *BillItemClient) Delete() *BillItemDelete {
	mutation := newBillItemMutation(c.config, OpDelete)
	return &BillItemDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *BillItemClient) DeleteOne(bi *BillItem) *BillItemDeleteOne {
	return c.DeleteOneID(bi.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *BillItemClient) DeleteOneID(id int) *BillItemDeleteOne {
	builder := c.Delete().Where(billitem.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &BillItemDeleteOne{builder}
}

// Query returns a query builder for BillItem.
func (c *BillItemClient) Query() *BillItemQuery {
	return &BillItemQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeBillItem},
		inters: c.Interceptors(),
	}
}

// Get returns a BillItem entity by its id.
func (c *BillItemClient) Get(ctx context.Context, id int) (*BillItem, error) {
	return c.Query().Where(billitem.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *BillItemClient) GetX(ctx context.Context, id int) *BillItem {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryInstallment queries the installment edge of a BillItem.
func (c *BillItemClient) QueryInstallment(bi *BillItem) *InstallmentQuery {
	query := (&InstallmentClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := bi.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(billitem.Table, billitem.FieldID, id),
			sqlgraph.To(installment.Table, installment.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, billitem.InstallmentTable, billitem.InstallmentColumn),
		)
		fromV = sqlgraph.Neighbors(bi.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryAttachment queries the attachment edge of a BillItem.
func (c *BillItemClient) QueryAttachment(bi *BillItem) *AttachmentQuery {
	query := (&AttachmentClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := bi.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(billitem.Table, billitem.FieldID, id),
			sqlgraph.To(attachment.Table, attachment.FieldID),
			sqlgraph.Edge(sqlgraph.O2O, true, billitem.AttachmentTable, billitem.AttachmentColumn),
		)
		fromV = sqlgraph.Neighbors(bi.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryBill queries the bill edge of a BillItem.
func (c *BillItemClient) QueryBill(bi *BillItem) *BillQuery {
	query := (&BillClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := bi.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(billitem.Table, billitem.FieldID, id),
			sqlgraph.To(bill.Table, bill.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, billitem.BillTable, billitem.BillColumn),
		)
		fromV = sqlgraph.Neighbors(bi.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryUser queries the user edge of a BillItem.
func (c *BillItemClient) QueryUser(bi *BillItem) *UserQuery {
	query := (&UserClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := bi.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(billitem.Table, billitem.FieldID, id),
			sqlgraph.To(user.Table, user.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, billitem.UserTable, billitem.UserColumn),
		)
		fromV = sqlgraph.Neighbors(bi.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryAllocations queries the allocations edge of a BillItem.
func (c *BillItemClient) QueryAllocations(bi *BillItem) *PaymentAllocationQuery {
	query := (&PaymentAllocationClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := bi.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(billitem.Table, billitem.FieldID, id),
			sqlgraph.To(paymentallocation.Table, paymentallocation.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, billitem.AllocationsTable, billitem.AllocationsColumn),
		)
		fromV = sqlgraph.Neighbors(bi.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryTrack queries the track edge of a BillItem.
func (c *BillItemClient) QueryTrack(bi *BillItem) *TrackQuery {
	query := (&TrackClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := bi.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(billitem.Table, billitem.FieldID, id),
			sqlgraph.To(track.Table, track.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, billitem.TrackTable, billitem.TrackColumn),
		)
		fromV = sqlgraph.Neighbors(bi.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *BillItemClient) Hooks() []Hook {
	hooks := c.hooks.BillItem
	return append(hooks[:len(hooks):len(hooks)], billitem.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *BillItemClient) Interceptors() []Interceptor {
	inters := c.inters.BillItem
	return append(inters[:len(inters):len(inters)], billitem.Interceptors[:]...)
}

func (c *BillItemClient) mutate(ctx context.Context, m *BillItemMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&BillItemCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&BillItemUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&BillItemUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&BillItemDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown BillItem mutation op: %q", m.Op())
	}
}

// BundleClient is a client for the Bundle schema.
type BundleClient struct {
	config
}

// NewBundleClient returns a client for the Bundle from the given config.
func NewBundleClient(c config) *BundleClient {
	return &BundleClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `bundle.Hooks(f(g(h())))`.
func (c *BundleClient) Use(hooks ...Hook) {
	c.hooks.Bundle = append(c.hooks.Bundle, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `bundle.Intercept(f(g(h())))`.
func (c *BundleClient) Intercept(interceptors ...Interceptor) {
	c.inters.Bundle = append(c.inters.Bundle, interceptors...)
}

// Create returns a builder for creating a Bundle entity.
func (c *BundleClient) Create() *BundleCreate {
	mutation := newBundleMutation(c.config, OpCreate)
	return &BundleCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Bundle entities.
func (c *BundleClient) CreateBulk(builders ...*BundleCreate) *BundleCreateBulk {
	return &BundleCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *BundleClient) MapCreateBulk(slice any, setFunc func(*BundleCreate, int)) *BundleCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &BundleCreateBulk{err: fmt.Errorf("calling to BundleClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*BundleCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &BundleCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Bundle.
func (c *BundleClient) Update() *BundleUpdate {
	mutation := newBundleMutation(c.config, OpUpdate)
	return &BundleUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *BundleClient) UpdateOne(b *Bundle) *BundleUpdateOne {
	mutation := newBundleMutation(c.config, OpUpdateOne, withBundle(b))
	return &BundleUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *BundleClient) UpdateOneID(id int) *BundleUpdateOne {
	mutation := newBundleMutation(c.config, OpUpdateOne, withBundleID(id))
	return &BundleUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Bundle.
func (c *BundleClient) Delete() *BundleDelete {
	mutation := newBundleMutation(c.config, OpDelete)
	return &BundleDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *BundleClient) DeleteOne(b *Bundle) *BundleDeleteOne {
	return c.DeleteOneID(b.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *BundleClient) DeleteOneID(id int) *BundleDeleteOne {
	builder := c.Delete().Where(bundle.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &BundleDeleteOne{builder}
}

// Query returns a query builder for Bundle.
func (c *BundleClient) Query() *BundleQuery {
	return &BundleQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeBundle},
		inters: c.Interceptors(),
	}
}

// Get returns a Bundle entity by its id.
func (c *BundleClient) Get(ctx context.Context, id int) (*Bundle, error) {
	return c.Query().Where(bundle.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *BundleClient) GetX(ctx context.Context, id int) *Bundle {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *BundleClient) Hooks() []Hook {
	hooks := c.hooks.Bundle
	return append(hooks[:len(hooks):len(hooks)], bundle.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *BundleClient) Interceptors() []Interceptor {
	inters := c.inters.Bundle
	return append(inters[:len(inters):len(inters)], bundle.Interceptors[:]...)
}

func (c *BundleClient) mutate(ctx context.Context, m *BundleMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&BundleCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&BundleUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&BundleUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&BundleDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Bundle mutation op: %q", m.Op())
	}
}

// CallClient is a client for the Call schema.
type CallClient struct {
	config
}

// NewCallClient returns a client for the Call from the given config.
func NewCallClient(c config) *CallClient {
	return &CallClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `call.Hooks(f(g(h())))`.
func (c *CallClient) Use(hooks ...Hook) {
	c.hooks.Call = append(c.hooks.Call, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `call.Intercept(f(g(h())))`.
func (c *CallClient) Intercept(interceptors ...Interceptor) {
	c.inters.Call = append(c.inters.Call, interceptors...)
}

// Create returns a builder for creating a Call entity.
func (c *CallClient) Create() *CallCreate {
	mutation := newCallMutation(c.config, OpCreate)
	return &CallCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Call entities.
func (c *CallClient) CreateBulk(builders ...*CallCreate) *CallCreateBulk {
	return &CallCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *CallClient) MapCreateBulk(slice any, setFunc func(*CallCreate, int)) *CallCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &CallCreateBulk{err: fmt.Errorf("calling to CallClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*CallCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &CallCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Call.
func (c *CallClient) Update() *CallUpdate {
	mutation := newCallMutation(c.config, OpUpdate)
	return &CallUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *CallClient) UpdateOne(ca *Call) *CallUpdateOne {
	mutation := newCallMutation(c.config, OpUpdateOne, withCall(ca))
	return &CallUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *CallClient) UpdateOneID(id int) *CallUpdateOne {
	mutation := newCallMutation(c.config, OpUpdateOne, withCallID(id))
	return &CallUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Call.
func (c *CallClient) Delete() *CallDelete {
	mutation := newCallMutation(c.config, OpDelete)
	return &CallDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *CallClient) DeleteOne(ca *Call) *CallDeleteOne {
	return c.DeleteOneID(ca.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *CallClient) DeleteOneID(id int) *CallDeleteOne {
	builder := c.Delete().Where(call.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &CallDeleteOne{builder}
}

// Query returns a query builder for Call.
func (c *CallClient) Query() *CallQuery {
	return &CallQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeCall},
		inters: c.Interceptors(),
	}
}

// Get returns a Call entity by its id.
func (c *CallClient) Get(ctx context.Context, id int) (*Call, error) {
	return c.Query().Where(call.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *CallClient) GetX(ctx context.Context, id int) *Call {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryPerson queries the person edge of a Call.
func (c *CallClient) QueryPerson(ca *Call) *PersonQuery {
	query := (&PersonClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := ca.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(call.Table, call.FieldID, id),
			sqlgraph.To(person.Table, person.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, call.PersonTable, call.PersonColumn),
		)
		fromV = sqlgraph.Neighbors(ca.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryUser queries the user edge of a Call.
func (c *CallClient) QueryUser(ca *Call) *UserQuery {
	query := (&UserClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := ca.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(call.Table, call.FieldID, id),
			sqlgraph.To(user.Table, user.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, call.UserTable, call.UserColumn),
		)
		fromV = sqlgraph.Neighbors(ca.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *CallClient) Hooks() []Hook {
	hooks := c.hooks.Call
	return append(hooks[:len(hooks):len(hooks)], call.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *CallClient) Interceptors() []Interceptor {
	inters := c.inters.Call
	return append(inters[:len(inters):len(inters)], call.Interceptors[:]...)
}

func (c *CallClient) mutate(ctx context.Context, m *CallMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&CallCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&CallUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&CallUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&CallDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Call mutation op: %q", m.Op())
	}
}

// CasbinRuleClient is a client for the CasbinRule schema.
type CasbinRuleClient struct {
	config
}

// NewCasbinRuleClient returns a client for the CasbinRule from the given config.
func NewCasbinRuleClient(c config) *CasbinRuleClient {
	return &CasbinRuleClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `casbinrule.Hooks(f(g(h())))`.
func (c *CasbinRuleClient) Use(hooks ...Hook) {
	c.hooks.CasbinRule = append(c.hooks.CasbinRule, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `casbinrule.Intercept(f(g(h())))`.
func (c *CasbinRuleClient) Intercept(interceptors ...Interceptor) {
	c.inters.CasbinRule = append(c.inters.CasbinRule, interceptors...)
}

// Create returns a builder for creating a CasbinRule entity.
func (c *CasbinRuleClient) Create() *CasbinRuleCreate {
	mutation := newCasbinRuleMutation(c.config, OpCreate)
	return &CasbinRuleCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of CasbinRule entities.
func (c *CasbinRuleClient) CreateBulk(builders ...*CasbinRuleCreate) *CasbinRuleCreateBulk {
	return &CasbinRuleCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *CasbinRuleClient) MapCreateBulk(slice any, setFunc func(*CasbinRuleCreate, int)) *CasbinRuleCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &CasbinRuleCreateBulk{err: fmt.Errorf("calling to CasbinRuleClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*CasbinRuleCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &CasbinRuleCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for CasbinRule.
func (c *CasbinRuleClient) Update() *CasbinRuleUpdate {
	mutation := newCasbinRuleMutation(c.config, OpUpdate)
	return &CasbinRuleUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *CasbinRuleClient) UpdateOne(cr *CasbinRule) *CasbinRuleUpdateOne {
	mutation := newCasbinRuleMutation(c.config, OpUpdateOne, withCasbinRule(cr))
	return &CasbinRuleUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *CasbinRuleClient) UpdateOneID(id int) *CasbinRuleUpdateOne {
	mutation := newCasbinRuleMutation(c.config, OpUpdateOne, withCasbinRuleID(id))
	return &CasbinRuleUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for CasbinRule.
func (c *CasbinRuleClient) Delete() *CasbinRuleDelete {
	mutation := newCasbinRuleMutation(c.config, OpDelete)
	return &CasbinRuleDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *CasbinRuleClient) DeleteOne(cr *CasbinRule) *CasbinRuleDeleteOne {
	return c.DeleteOneID(cr.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *CasbinRuleClient) DeleteOneID(id int) *CasbinRuleDeleteOne {
	builder := c.Delete().Where(casbinrule.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &CasbinRuleDeleteOne{builder}
}

// Query returns a query builder for CasbinRule.
func (c *CasbinRuleClient) Query() *CasbinRuleQuery {
	return &CasbinRuleQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeCasbinRule},
		inters: c.Interceptors(),
	}
}

// Get returns a CasbinRule entity by its id.
func (c *CasbinRuleClient) Get(ctx context.Context, id int) (*CasbinRule, error) {
	return c.Query().Where(casbinrule.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *CasbinRuleClient) GetX(ctx context.Context, id int) *CasbinRule {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *CasbinRuleClient) Hooks() []Hook {
	return c.hooks.CasbinRule
}

// Interceptors returns the client interceptors.
func (c *CasbinRuleClient) Interceptors() []Interceptor {
	return c.inters.CasbinRule
}

func (c *CasbinRuleClient) mutate(ctx context.Context, m *CasbinRuleMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&CasbinRuleCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&CasbinRuleUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&CasbinRuleUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&CasbinRuleDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown CasbinRule mutation op: %q", m.Op())
	}
}

// CashFlowClient is a client for the CashFlow schema.
type CashFlowClient struct {
	config
}

// NewCashFlowClient returns a client for the CashFlow from the given config.
func NewCashFlowClient(c config) *CashFlowClient {
	return &CashFlowClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `cashflow.Hooks(f(g(h())))`.
func (c *CashFlowClient) Use(hooks ...Hook) {
	c.hooks.CashFlow = append(c.hooks.CashFlow, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `cashflow.Intercept(f(g(h())))`.
func (c *CashFlowClient) Intercept(interceptors ...Interceptor) {
	c.inters.CashFlow = append(c.inters.CashFlow, interceptors...)
}

// Create returns a builder for creating a CashFlow entity.
func (c *CashFlowClient) Create() *CashFlowCreate {
	mutation := newCashFlowMutation(c.config, OpCreate)
	return &CashFlowCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of CashFlow entities.
func (c *CashFlowClient) CreateBulk(builders ...*CashFlowCreate) *CashFlowCreateBulk {
	return &CashFlowCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *CashFlowClient) MapCreateBulk(slice any, setFunc func(*CashFlowCreate, int)) *CashFlowCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &CashFlowCreateBulk{err: fmt.Errorf("calling to CashFlowClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*CashFlowCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &CashFlowCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for CashFlow.
func (c *CashFlowClient) Update() *CashFlowUpdate {
	mutation := newCashFlowMutation(c.config, OpUpdate)
	return &CashFlowUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *CashFlowClient) UpdateOne(cf *CashFlow) *CashFlowUpdateOne {
	mutation := newCashFlowMutation(c.config, OpUpdateOne, withCashFlow(cf))
	return &CashFlowUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *CashFlowClient) UpdateOneID(id int) *CashFlowUpdateOne {
	mutation := newCashFlowMutation(c.config, OpUpdateOne, withCashFlowID(id))
	return &CashFlowUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for CashFlow.
func (c *CashFlowClient) Delete() *CashFlowDelete {
	mutation := newCashFlowMutation(c.config, OpDelete)
	return &CashFlowDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *CashFlowClient) DeleteOne(cf *CashFlow) *CashFlowDeleteOne {
	return c.DeleteOneID(cf.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *CashFlowClient) DeleteOneID(id int) *CashFlowDeleteOne {
	builder := c.Delete().Where(cashflow.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &CashFlowDeleteOne{builder}
}

// Query returns a query builder for CashFlow.
func (c *CashFlowClient) Query() *CashFlowQuery {
	return &CashFlowQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeCashFlow},
		inters: c.Interceptors(),
	}
}

// Get returns a CashFlow entity by its id.
func (c *CashFlowClient) Get(ctx context.Context, id int) (*CashFlow, error) {
	return c.Query().Where(cashflow.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *CashFlowClient) GetX(ctx context.Context, id int) *CashFlow {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryCreator queries the creator edge of a CashFlow.
func (c *CashFlowClient) QueryCreator(cf *CashFlow) *UserQuery {
	query := (&UserClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := cf.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(cashflow.Table, cashflow.FieldID, id),
			sqlgraph.To(user.Table, user.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, cashflow.CreatorTable, cashflow.CreatorColumn),
		)
		fromV = sqlgraph.Neighbors(cf.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryApprover queries the approver edge of a CashFlow.
func (c *CashFlowClient) QueryApprover(cf *CashFlow) *UserQuery {
	query := (&UserClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := cf.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(cashflow.Table, cashflow.FieldID, id),
			sqlgraph.To(user.Table, user.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, cashflow.ApproverTable, cashflow.ApproverColumn),
		)
		fromV = sqlgraph.Neighbors(cf.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryCounterpart queries the counterpart edge of a CashFlow.
func (c *CashFlowClient) QueryCounterpart(cf *CashFlow) *UserQuery {
	query := (&UserClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := cf.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(cashflow.Table, cashflow.FieldID, id),
			sqlgraph.To(user.Table, user.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, cashflow.CounterpartTable, cashflow.CounterpartColumn),
		)
		fromV = sqlgraph.Neighbors(cf.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryRecipient queries the recipient edge of a CashFlow.
func (c *CashFlowClient) QueryRecipient(cf *CashFlow) *UserQuery {
	query := (&UserClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := cf.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(cashflow.Table, cashflow.FieldID, id),
			sqlgraph.To(user.Table, user.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, cashflow.RecipientTable, cashflow.RecipientColumn),
		)
		fromV = sqlgraph.Neighbors(cf.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryPayer queries the payer edge of a CashFlow.
func (c *CashFlowClient) QueryPayer(cf *CashFlow) *UserQuery {
	query := (&UserClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := cf.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(cashflow.Table, cashflow.FieldID, id),
			sqlgraph.To(user.Table, user.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, cashflow.PayerTable, cashflow.PayerColumn),
		)
		fromV = sqlgraph.Neighbors(cf.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryItems queries the items edge of a CashFlow.
func (c *CashFlowClient) QueryItems(cf *CashFlow) *CashFlowItemQuery {
	query := (&CashFlowItemClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := cf.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(cashflow.Table, cashflow.FieldID, id),
			sqlgraph.To(cashflowitem.Table, cashflowitem.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, cashflow.ItemsTable, cashflow.ItemsColumn),
		)
		fromV = sqlgraph.Neighbors(cf.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryNotes queries the notes edge of a CashFlow.
func (c *CashFlowClient) QueryNotes(cf *CashFlow) *CashFlowNoteQuery {
	query := (&CashFlowNoteClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := cf.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(cashflow.Table, cashflow.FieldID, id),
			sqlgraph.To(cashflownote.Table, cashflownote.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, cashflow.NotesTable, cashflow.NotesColumn),
		)
		fromV = sqlgraph.Neighbors(cf.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *CashFlowClient) Hooks() []Hook {
	hooks := c.hooks.CashFlow
	return append(hooks[:len(hooks):len(hooks)], cashflow.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *CashFlowClient) Interceptors() []Interceptor {
	inters := c.inters.CashFlow
	return append(inters[:len(inters):len(inters)], cashflow.Interceptors[:]...)
}

func (c *CashFlowClient) mutate(ctx context.Context, m *CashFlowMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&CashFlowCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&CashFlowUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&CashFlowUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&CashFlowDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown CashFlow mutation op: %q", m.Op())
	}
}

// CashFlowItemClient is a client for the CashFlowItem schema.
type CashFlowItemClient struct {
	config
}

// NewCashFlowItemClient returns a client for the CashFlowItem from the given config.
func NewCashFlowItemClient(c config) *CashFlowItemClient {
	return &CashFlowItemClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `cashflowitem.Hooks(f(g(h())))`.
func (c *CashFlowItemClient) Use(hooks ...Hook) {
	c.hooks.CashFlowItem = append(c.hooks.CashFlowItem, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `cashflowitem.Intercept(f(g(h())))`.
func (c *CashFlowItemClient) Intercept(interceptors ...Interceptor) {
	c.inters.CashFlowItem = append(c.inters.CashFlowItem, interceptors...)
}

// Create returns a builder for creating a CashFlowItem entity.
func (c *CashFlowItemClient) Create() *CashFlowItemCreate {
	mutation := newCashFlowItemMutation(c.config, OpCreate)
	return &CashFlowItemCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of CashFlowItem entities.
func (c *CashFlowItemClient) CreateBulk(builders ...*CashFlowItemCreate) *CashFlowItemCreateBulk {
	return &CashFlowItemCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *CashFlowItemClient) MapCreateBulk(slice any, setFunc func(*CashFlowItemCreate, int)) *CashFlowItemCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &CashFlowItemCreateBulk{err: fmt.Errorf("calling to CashFlowItemClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*CashFlowItemCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &CashFlowItemCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for CashFlowItem.
func (c *CashFlowItemClient) Update() *CashFlowItemUpdate {
	mutation := newCashFlowItemMutation(c.config, OpUpdate)
	return &CashFlowItemUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *CashFlowItemClient) UpdateOne(cfi *CashFlowItem) *CashFlowItemUpdateOne {
	mutation := newCashFlowItemMutation(c.config, OpUpdateOne, withCashFlowItem(cfi))
	return &CashFlowItemUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *CashFlowItemClient) UpdateOneID(id int) *CashFlowItemUpdateOne {
	mutation := newCashFlowItemMutation(c.config, OpUpdateOne, withCashFlowItemID(id))
	return &CashFlowItemUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for CashFlowItem.
func (c *CashFlowItemClient) Delete() *CashFlowItemDelete {
	mutation := newCashFlowItemMutation(c.config, OpDelete)
	return &CashFlowItemDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *CashFlowItemClient) DeleteOne(cfi *CashFlowItem) *CashFlowItemDeleteOne {
	return c.DeleteOneID(cfi.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *CashFlowItemClient) DeleteOneID(id int) *CashFlowItemDeleteOne {
	builder := c.Delete().Where(cashflowitem.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &CashFlowItemDeleteOne{builder}
}

// Query returns a query builder for CashFlowItem.
func (c *CashFlowItemClient) Query() *CashFlowItemQuery {
	return &CashFlowItemQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeCashFlowItem},
		inters: c.Interceptors(),
	}
}

// Get returns a CashFlowItem entity by its id.
func (c *CashFlowItemClient) Get(ctx context.Context, id int) (*CashFlowItem, error) {
	return c.Query().Where(cashflowitem.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *CashFlowItemClient) GetX(ctx context.Context, id int) *CashFlowItem {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryCashFlow queries the cash_flow edge of a CashFlowItem.
func (c *CashFlowItemClient) QueryCashFlow(cfi *CashFlowItem) *CashFlowQuery {
	query := (&CashFlowClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := cfi.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(cashflowitem.Table, cashflowitem.FieldID, id),
			sqlgraph.To(cashflow.Table, cashflow.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, cashflowitem.CashFlowTable, cashflowitem.CashFlowColumn),
		)
		fromV = sqlgraph.Neighbors(cfi.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryCategory queries the category edge of a CashFlowItem.
func (c *CashFlowItemClient) QueryCategory(cfi *CashFlowItem) *TermQuery {
	query := (&TermClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := cfi.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(cashflowitem.Table, cashflowitem.FieldID, id),
			sqlgraph.To(term.Table, term.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, cashflowitem.CategoryTable, cashflowitem.CategoryColumn),
		)
		fromV = sqlgraph.Neighbors(cfi.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryDepartment queries the department edge of a CashFlowItem.
func (c *CashFlowItemClient) QueryDepartment(cfi *CashFlowItem) *DepartmentQuery {
	query := (&DepartmentClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := cfi.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(cashflowitem.Table, cashflowitem.FieldID, id),
			sqlgraph.To(department.Table, department.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, cashflowitem.DepartmentTable, cashflowitem.DepartmentColumn),
		)
		fromV = sqlgraph.Neighbors(cfi.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *CashFlowItemClient) Hooks() []Hook {
	return c.hooks.CashFlowItem
}

// Interceptors returns the client interceptors.
func (c *CashFlowItemClient) Interceptors() []Interceptor {
	return c.inters.CashFlowItem
}

func (c *CashFlowItemClient) mutate(ctx context.Context, m *CashFlowItemMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&CashFlowItemCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&CashFlowItemUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&CashFlowItemUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&CashFlowItemDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown CashFlowItem mutation op: %q", m.Op())
	}
}

// CashFlowNoteClient is a client for the CashFlowNote schema.
type CashFlowNoteClient struct {
	config
}

// NewCashFlowNoteClient returns a client for the CashFlowNote from the given config.
func NewCashFlowNoteClient(c config) *CashFlowNoteClient {
	return &CashFlowNoteClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `cashflownote.Hooks(f(g(h())))`.
func (c *CashFlowNoteClient) Use(hooks ...Hook) {
	c.hooks.CashFlowNote = append(c.hooks.CashFlowNote, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `cashflownote.Intercept(f(g(h())))`.
func (c *CashFlowNoteClient) Intercept(interceptors ...Interceptor) {
	c.inters.CashFlowNote = append(c.inters.CashFlowNote, interceptors...)
}

// Create returns a builder for creating a CashFlowNote entity.
func (c *CashFlowNoteClient) Create() *CashFlowNoteCreate {
	mutation := newCashFlowNoteMutation(c.config, OpCreate)
	return &CashFlowNoteCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of CashFlowNote entities.
func (c *CashFlowNoteClient) CreateBulk(builders ...*CashFlowNoteCreate) *CashFlowNoteCreateBulk {
	return &CashFlowNoteCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *CashFlowNoteClient) MapCreateBulk(slice any, setFunc func(*CashFlowNoteCreate, int)) *CashFlowNoteCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &CashFlowNoteCreateBulk{err: fmt.Errorf("calling to CashFlowNoteClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*CashFlowNoteCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &CashFlowNoteCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for CashFlowNote.
func (c *CashFlowNoteClient) Update() *CashFlowNoteUpdate {
	mutation := newCashFlowNoteMutation(c.config, OpUpdate)
	return &CashFlowNoteUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *CashFlowNoteClient) UpdateOne(cfn *CashFlowNote) *CashFlowNoteUpdateOne {
	mutation := newCashFlowNoteMutation(c.config, OpUpdateOne, withCashFlowNote(cfn))
	return &CashFlowNoteUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *CashFlowNoteClient) UpdateOneID(id int) *CashFlowNoteUpdateOne {
	mutation := newCashFlowNoteMutation(c.config, OpUpdateOne, withCashFlowNoteID(id))
	return &CashFlowNoteUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for CashFlowNote.
func (c *CashFlowNoteClient) Delete() *CashFlowNoteDelete {
	mutation := newCashFlowNoteMutation(c.config, OpDelete)
	return &CashFlowNoteDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *CashFlowNoteClient) DeleteOne(cfn *CashFlowNote) *CashFlowNoteDeleteOne {
	return c.DeleteOneID(cfn.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *CashFlowNoteClient) DeleteOneID(id int) *CashFlowNoteDeleteOne {
	builder := c.Delete().Where(cashflownote.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &CashFlowNoteDeleteOne{builder}
}

// Query returns a query builder for CashFlowNote.
func (c *CashFlowNoteClient) Query() *CashFlowNoteQuery {
	return &CashFlowNoteQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeCashFlowNote},
		inters: c.Interceptors(),
	}
}

// Get returns a CashFlowNote entity by its id.
func (c *CashFlowNoteClient) Get(ctx context.Context, id int) (*CashFlowNote, error) {
	return c.Query().Where(cashflownote.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *CashFlowNoteClient) GetX(ctx context.Context, id int) *CashFlowNote {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryCreator queries the creator edge of a CashFlowNote.
func (c *CashFlowNoteClient) QueryCreator(cfn *CashFlowNote) *UserQuery {
	query := (&UserClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := cfn.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(cashflownote.Table, cashflownote.FieldID, id),
			sqlgraph.To(user.Table, user.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, cashflownote.CreatorTable, cashflownote.CreatorColumn),
		)
		fromV = sqlgraph.Neighbors(cfn.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryCashFlow queries the cash_flow edge of a CashFlowNote.
func (c *CashFlowNoteClient) QueryCashFlow(cfn *CashFlowNote) *CashFlowQuery {
	query := (&CashFlowClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := cfn.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(cashflownote.Table, cashflownote.FieldID, id),
			sqlgraph.To(cashflow.Table, cashflow.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, cashflownote.CashFlowTable, cashflownote.CashFlowColumn),
		)
		fromV = sqlgraph.Neighbors(cfn.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *CashFlowNoteClient) Hooks() []Hook {
	hooks := c.hooks.CashFlowNote
	return append(hooks[:len(hooks):len(hooks)], cashflownote.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *CashFlowNoteClient) Interceptors() []Interceptor {
	inters := c.inters.CashFlowNote
	return append(inters[:len(inters):len(inters)], cashflownote.Interceptors[:]...)
}

func (c *CashFlowNoteClient) mutate(ctx context.Context, m *CashFlowNoteMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&CashFlowNoteCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&CashFlowNoteUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&CashFlowNoteUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&CashFlowNoteDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown CashFlowNote mutation op: %q", m.Op())
	}
}

// DbsandentistDboAppointmentTimeClient is a client for the DbsandentistDboAppointmentTime schema.
type DbsandentistDboAppointmentTimeClient struct {
	config
}

// NewDbsandentistDboAppointmentTimeClient returns a client for the DbsandentistDboAppointmentTime from the given config.
func NewDbsandentistDboAppointmentTimeClient(c config) *DbsandentistDboAppointmentTimeClient {
	return &DbsandentistDboAppointmentTimeClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `dbsandentistdboappointmenttime.Hooks(f(g(h())))`.
func (c *DbsandentistDboAppointmentTimeClient) Use(hooks ...Hook) {
	c.hooks.DbsandentistDboAppointmentTime = append(c.hooks.DbsandentistDboAppointmentTime, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `dbsandentistdboappointmenttime.Intercept(f(g(h())))`.
func (c *DbsandentistDboAppointmentTimeClient) Intercept(interceptors ...Interceptor) {
	c.inters.DbsandentistDboAppointmentTime = append(c.inters.DbsandentistDboAppointmentTime, interceptors...)
}

// Create returns a builder for creating a DbsandentistDboAppointmentTime entity.
func (c *DbsandentistDboAppointmentTimeClient) Create() *DbsandentistDboAppointmentTimeCreate {
	mutation := newDbsandentistDboAppointmentTimeMutation(c.config, OpCreate)
	return &DbsandentistDboAppointmentTimeCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of DbsandentistDboAppointmentTime entities.
func (c *DbsandentistDboAppointmentTimeClient) CreateBulk(builders ...*DbsandentistDboAppointmentTimeCreate) *DbsandentistDboAppointmentTimeCreateBulk {
	return &DbsandentistDboAppointmentTimeCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *DbsandentistDboAppointmentTimeClient) MapCreateBulk(slice any, setFunc func(*DbsandentistDboAppointmentTimeCreate, int)) *DbsandentistDboAppointmentTimeCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &DbsandentistDboAppointmentTimeCreateBulk{err: fmt.Errorf("calling to DbsandentistDboAppointmentTimeClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*DbsandentistDboAppointmentTimeCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &DbsandentistDboAppointmentTimeCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for DbsandentistDboAppointmentTime.
func (c *DbsandentistDboAppointmentTimeClient) Update() *DbsandentistDboAppointmentTimeUpdate {
	mutation := newDbsandentistDboAppointmentTimeMutation(c.config, OpUpdate)
	return &DbsandentistDboAppointmentTimeUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *DbsandentistDboAppointmentTimeClient) UpdateOne(ddat *DbsandentistDboAppointmentTime) *DbsandentistDboAppointmentTimeUpdateOne {
	mutation := newDbsandentistDboAppointmentTimeMutation(c.config, OpUpdateOne, withDbsandentistDboAppointmentTime(ddat))
	return &DbsandentistDboAppointmentTimeUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *DbsandentistDboAppointmentTimeClient) UpdateOneID(id int) *DbsandentistDboAppointmentTimeUpdateOne {
	mutation := newDbsandentistDboAppointmentTimeMutation(c.config, OpUpdateOne, withDbsandentistDboAppointmentTimeID(id))
	return &DbsandentistDboAppointmentTimeUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for DbsandentistDboAppointmentTime.
func (c *DbsandentistDboAppointmentTimeClient) Delete() *DbsandentistDboAppointmentTimeDelete {
	mutation := newDbsandentistDboAppointmentTimeMutation(c.config, OpDelete)
	return &DbsandentistDboAppointmentTimeDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *DbsandentistDboAppointmentTimeClient) DeleteOne(ddat *DbsandentistDboAppointmentTime) *DbsandentistDboAppointmentTimeDeleteOne {
	return c.DeleteOneID(ddat.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *DbsandentistDboAppointmentTimeClient) DeleteOneID(id int) *DbsandentistDboAppointmentTimeDeleteOne {
	builder := c.Delete().Where(dbsandentistdboappointmenttime.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &DbsandentistDboAppointmentTimeDeleteOne{builder}
}

// Query returns a query builder for DbsandentistDboAppointmentTime.
func (c *DbsandentistDboAppointmentTimeClient) Query() *DbsandentistDboAppointmentTimeQuery {
	return &DbsandentistDboAppointmentTimeQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeDbsandentistDboAppointmentTime},
		inters: c.Interceptors(),
	}
}

// Get returns a DbsandentistDboAppointmentTime entity by its id.
func (c *DbsandentistDboAppointmentTimeClient) Get(ctx context.Context, id int) (*DbsandentistDboAppointmentTime, error) {
	return c.Query().Where(dbsandentistdboappointmenttime.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *DbsandentistDboAppointmentTimeClient) GetX(ctx context.Context, id int) *DbsandentistDboAppointmentTime {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *DbsandentistDboAppointmentTimeClient) Hooks() []Hook {
	return c.hooks.DbsandentistDboAppointmentTime
}

// Interceptors returns the client interceptors.
func (c *DbsandentistDboAppointmentTimeClient) Interceptors() []Interceptor {
	return c.inters.DbsandentistDboAppointmentTime
}

func (c *DbsandentistDboAppointmentTimeClient) mutate(ctx context.Context, m *DbsandentistDboAppointmentTimeMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&DbsandentistDboAppointmentTimeCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&DbsandentistDboAppointmentTimeUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&DbsandentistDboAppointmentTimeUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&DbsandentistDboAppointmentTimeDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown DbsandentistDboAppointmentTime mutation op: %q", m.Op())
	}
}

// DbsandentistDboBillClient is a client for the DbsandentistDboBill schema.
type DbsandentistDboBillClient struct {
	config
}

// NewDbsandentistDboBillClient returns a client for the DbsandentistDboBill from the given config.
func NewDbsandentistDboBillClient(c config) *DbsandentistDboBillClient {
	return &DbsandentistDboBillClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `dbsandentistdbobill.Hooks(f(g(h())))`.
func (c *DbsandentistDboBillClient) Use(hooks ...Hook) {
	c.hooks.DbsandentistDboBill = append(c.hooks.DbsandentistDboBill, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `dbsandentistdbobill.Intercept(f(g(h())))`.
func (c *DbsandentistDboBillClient) Intercept(interceptors ...Interceptor) {
	c.inters.DbsandentistDboBill = append(c.inters.DbsandentistDboBill, interceptors...)
}

// Create returns a builder for creating a DbsandentistDboBill entity.
func (c *DbsandentistDboBillClient) Create() *DbsandentistDboBillCreate {
	mutation := newDbsandentistDboBillMutation(c.config, OpCreate)
	return &DbsandentistDboBillCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of DbsandentistDboBill entities.
func (c *DbsandentistDboBillClient) CreateBulk(builders ...*DbsandentistDboBillCreate) *DbsandentistDboBillCreateBulk {
	return &DbsandentistDboBillCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *DbsandentistDboBillClient) MapCreateBulk(slice any, setFunc func(*DbsandentistDboBillCreate, int)) *DbsandentistDboBillCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &DbsandentistDboBillCreateBulk{err: fmt.Errorf("calling to DbsandentistDboBillClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*DbsandentistDboBillCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &DbsandentistDboBillCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for DbsandentistDboBill.
func (c *DbsandentistDboBillClient) Update() *DbsandentistDboBillUpdate {
	mutation := newDbsandentistDboBillMutation(c.config, OpUpdate)
	return &DbsandentistDboBillUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *DbsandentistDboBillClient) UpdateOne(ddb *DbsandentistDboBill) *DbsandentistDboBillUpdateOne {
	mutation := newDbsandentistDboBillMutation(c.config, OpUpdateOne, withDbsandentistDboBill(ddb))
	return &DbsandentistDboBillUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *DbsandentistDboBillClient) UpdateOneID(id int) *DbsandentistDboBillUpdateOne {
	mutation := newDbsandentistDboBillMutation(c.config, OpUpdateOne, withDbsandentistDboBillID(id))
	return &DbsandentistDboBillUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for DbsandentistDboBill.
func (c *DbsandentistDboBillClient) Delete() *DbsandentistDboBillDelete {
	mutation := newDbsandentistDboBillMutation(c.config, OpDelete)
	return &DbsandentistDboBillDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *DbsandentistDboBillClient) DeleteOne(ddb *DbsandentistDboBill) *DbsandentistDboBillDeleteOne {
	return c.DeleteOneID(ddb.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *DbsandentistDboBillClient) DeleteOneID(id int) *DbsandentistDboBillDeleteOne {
	builder := c.Delete().Where(dbsandentistdbobill.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &DbsandentistDboBillDeleteOne{builder}
}

// Query returns a query builder for DbsandentistDboBill.
func (c *DbsandentistDboBillClient) Query() *DbsandentistDboBillQuery {
	return &DbsandentistDboBillQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeDbsandentistDboBill},
		inters: c.Interceptors(),
	}
}

// Get returns a DbsandentistDboBill entity by its id.
func (c *DbsandentistDboBillClient) Get(ctx context.Context, id int) (*DbsandentistDboBill, error) {
	return c.Query().Where(dbsandentistdbobill.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *DbsandentistDboBillClient) GetX(ctx context.Context, id int) *DbsandentistDboBill {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *DbsandentistDboBillClient) Hooks() []Hook {
	return c.hooks.DbsandentistDboBill
}

// Interceptors returns the client interceptors.
func (c *DbsandentistDboBillClient) Interceptors() []Interceptor {
	return c.inters.DbsandentistDboBill
}

func (c *DbsandentistDboBillClient) mutate(ctx context.Context, m *DbsandentistDboBillMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&DbsandentistDboBillCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&DbsandentistDboBillUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&DbsandentistDboBillUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&DbsandentistDboBillDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown DbsandentistDboBill mutation op: %q", m.Op())
	}
}

// DbsandentistDboBillItemClient is a client for the DbsandentistDboBillItem schema.
type DbsandentistDboBillItemClient struct {
	config
}

// NewDbsandentistDboBillItemClient returns a client for the DbsandentistDboBillItem from the given config.
func NewDbsandentistDboBillItemClient(c config) *DbsandentistDboBillItemClient {
	return &DbsandentistDboBillItemClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `dbsandentistdbobillitem.Hooks(f(g(h())))`.
func (c *DbsandentistDboBillItemClient) Use(hooks ...Hook) {
	c.hooks.DbsandentistDboBillItem = append(c.hooks.DbsandentistDboBillItem, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `dbsandentistdbobillitem.Intercept(f(g(h())))`.
func (c *DbsandentistDboBillItemClient) Intercept(interceptors ...Interceptor) {
	c.inters.DbsandentistDboBillItem = append(c.inters.DbsandentistDboBillItem, interceptors...)
}

// Create returns a builder for creating a DbsandentistDboBillItem entity.
func (c *DbsandentistDboBillItemClient) Create() *DbsandentistDboBillItemCreate {
	mutation := newDbsandentistDboBillItemMutation(c.config, OpCreate)
	return &DbsandentistDboBillItemCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of DbsandentistDboBillItem entities.
func (c *DbsandentistDboBillItemClient) CreateBulk(builders ...*DbsandentistDboBillItemCreate) *DbsandentistDboBillItemCreateBulk {
	return &DbsandentistDboBillItemCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *DbsandentistDboBillItemClient) MapCreateBulk(slice any, setFunc func(*DbsandentistDboBillItemCreate, int)) *DbsandentistDboBillItemCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &DbsandentistDboBillItemCreateBulk{err: fmt.Errorf("calling to DbsandentistDboBillItemClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*DbsandentistDboBillItemCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &DbsandentistDboBillItemCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for DbsandentistDboBillItem.
func (c *DbsandentistDboBillItemClient) Update() *DbsandentistDboBillItemUpdate {
	mutation := newDbsandentistDboBillItemMutation(c.config, OpUpdate)
	return &DbsandentistDboBillItemUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *DbsandentistDboBillItemClient) UpdateOne(ddbi *DbsandentistDboBillItem) *DbsandentistDboBillItemUpdateOne {
	mutation := newDbsandentistDboBillItemMutation(c.config, OpUpdateOne, withDbsandentistDboBillItem(ddbi))
	return &DbsandentistDboBillItemUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *DbsandentistDboBillItemClient) UpdateOneID(id int) *DbsandentistDboBillItemUpdateOne {
	mutation := newDbsandentistDboBillItemMutation(c.config, OpUpdateOne, withDbsandentistDboBillItemID(id))
	return &DbsandentistDboBillItemUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for DbsandentistDboBillItem.
func (c *DbsandentistDboBillItemClient) Delete() *DbsandentistDboBillItemDelete {
	mutation := newDbsandentistDboBillItemMutation(c.config, OpDelete)
	return &DbsandentistDboBillItemDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *DbsandentistDboBillItemClient) DeleteOne(ddbi *DbsandentistDboBillItem) *DbsandentistDboBillItemDeleteOne {
	return c.DeleteOneID(ddbi.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *DbsandentistDboBillItemClient) DeleteOneID(id int) *DbsandentistDboBillItemDeleteOne {
	builder := c.Delete().Where(dbsandentistdbobillitem.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &DbsandentistDboBillItemDeleteOne{builder}
}

// Query returns a query builder for DbsandentistDboBillItem.
func (c *DbsandentistDboBillItemClient) Query() *DbsandentistDboBillItemQuery {
	return &DbsandentistDboBillItemQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeDbsandentistDboBillItem},
		inters: c.Interceptors(),
	}
}

// Get returns a DbsandentistDboBillItem entity by its id.
func (c *DbsandentistDboBillItemClient) Get(ctx context.Context, id int) (*DbsandentistDboBillItem, error) {
	return c.Query().Where(dbsandentistdbobillitem.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *DbsandentistDboBillItemClient) GetX(ctx context.Context, id int) *DbsandentistDboBillItem {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *DbsandentistDboBillItemClient) Hooks() []Hook {
	return c.hooks.DbsandentistDboBillItem
}

// Interceptors returns the client interceptors.
func (c *DbsandentistDboBillItemClient) Interceptors() []Interceptor {
	return c.inters.DbsandentistDboBillItem
}

func (c *DbsandentistDboBillItemClient) mutate(ctx context.Context, m *DbsandentistDboBillItemMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&DbsandentistDboBillItemCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&DbsandentistDboBillItemUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&DbsandentistDboBillItemUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&DbsandentistDboBillItemDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown DbsandentistDboBillItem mutation op: %q", m.Op())
	}
}

// DbsandentistDboBillItemDetailClient is a client for the DbsandentistDboBillItemDetail schema.
type DbsandentistDboBillItemDetailClient struct {
	config
}

// NewDbsandentistDboBillItemDetailClient returns a client for the DbsandentistDboBillItemDetail from the given config.
func NewDbsandentistDboBillItemDetailClient(c config) *DbsandentistDboBillItemDetailClient {
	return &DbsandentistDboBillItemDetailClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `dbsandentistdbobillitemdetail.Hooks(f(g(h())))`.
func (c *DbsandentistDboBillItemDetailClient) Use(hooks ...Hook) {
	c.hooks.DbsandentistDboBillItemDetail = append(c.hooks.DbsandentistDboBillItemDetail, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `dbsandentistdbobillitemdetail.Intercept(f(g(h())))`.
func (c *DbsandentistDboBillItemDetailClient) Intercept(interceptors ...Interceptor) {
	c.inters.DbsandentistDboBillItemDetail = append(c.inters.DbsandentistDboBillItemDetail, interceptors...)
}

// Create returns a builder for creating a DbsandentistDboBillItemDetail entity.
func (c *DbsandentistDboBillItemDetailClient) Create() *DbsandentistDboBillItemDetailCreate {
	mutation := newDbsandentistDboBillItemDetailMutation(c.config, OpCreate)
	return &DbsandentistDboBillItemDetailCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of DbsandentistDboBillItemDetail entities.
func (c *DbsandentistDboBillItemDetailClient) CreateBulk(builders ...*DbsandentistDboBillItemDetailCreate) *DbsandentistDboBillItemDetailCreateBulk {
	return &DbsandentistDboBillItemDetailCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *DbsandentistDboBillItemDetailClient) MapCreateBulk(slice any, setFunc func(*DbsandentistDboBillItemDetailCreate, int)) *DbsandentistDboBillItemDetailCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &DbsandentistDboBillItemDetailCreateBulk{err: fmt.Errorf("calling to DbsandentistDboBillItemDetailClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*DbsandentistDboBillItemDetailCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &DbsandentistDboBillItemDetailCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for DbsandentistDboBillItemDetail.
func (c *DbsandentistDboBillItemDetailClient) Update() *DbsandentistDboBillItemDetailUpdate {
	mutation := newDbsandentistDboBillItemDetailMutation(c.config, OpUpdate)
	return &DbsandentistDboBillItemDetailUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *DbsandentistDboBillItemDetailClient) UpdateOne(ddbid *DbsandentistDboBillItemDetail) *DbsandentistDboBillItemDetailUpdateOne {
	mutation := newDbsandentistDboBillItemDetailMutation(c.config, OpUpdateOne, withDbsandentistDboBillItemDetail(ddbid))
	return &DbsandentistDboBillItemDetailUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *DbsandentistDboBillItemDetailClient) UpdateOneID(id int) *DbsandentistDboBillItemDetailUpdateOne {
	mutation := newDbsandentistDboBillItemDetailMutation(c.config, OpUpdateOne, withDbsandentistDboBillItemDetailID(id))
	return &DbsandentistDboBillItemDetailUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for DbsandentistDboBillItemDetail.
func (c *DbsandentistDboBillItemDetailClient) Delete() *DbsandentistDboBillItemDetailDelete {
	mutation := newDbsandentistDboBillItemDetailMutation(c.config, OpDelete)
	return &DbsandentistDboBillItemDetailDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *DbsandentistDboBillItemDetailClient) DeleteOne(ddbid *DbsandentistDboBillItemDetail) *DbsandentistDboBillItemDetailDeleteOne {
	return c.DeleteOneID(ddbid.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *DbsandentistDboBillItemDetailClient) DeleteOneID(id int) *DbsandentistDboBillItemDetailDeleteOne {
	builder := c.Delete().Where(dbsandentistdbobillitemdetail.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &DbsandentistDboBillItemDetailDeleteOne{builder}
}

// Query returns a query builder for DbsandentistDboBillItemDetail.
func (c *DbsandentistDboBillItemDetailClient) Query() *DbsandentistDboBillItemDetailQuery {
	return &DbsandentistDboBillItemDetailQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeDbsandentistDboBillItemDetail},
		inters: c.Interceptors(),
	}
}

// Get returns a DbsandentistDboBillItemDetail entity by its id.
func (c *DbsandentistDboBillItemDetailClient) Get(ctx context.Context, id int) (*DbsandentistDboBillItemDetail, error) {
	return c.Query().Where(dbsandentistdbobillitemdetail.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *DbsandentistDboBillItemDetailClient) GetX(ctx context.Context, id int) *DbsandentistDboBillItemDetail {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *DbsandentistDboBillItemDetailClient) Hooks() []Hook {
	return c.hooks.DbsandentistDboBillItemDetail
}

// Interceptors returns the client interceptors.
func (c *DbsandentistDboBillItemDetailClient) Interceptors() []Interceptor {
	return c.inters.DbsandentistDboBillItemDetail
}

func (c *DbsandentistDboBillItemDetailClient) mutate(ctx context.Context, m *DbsandentistDboBillItemDetailMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&DbsandentistDboBillItemDetailCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&DbsandentistDboBillItemDetailUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&DbsandentistDboBillItemDetailUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&DbsandentistDboBillItemDetailDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown DbsandentistDboBillItemDetail mutation op: %q", m.Op())
	}
}

// DbsandentistDboBillPaymentClient is a client for the DbsandentistDboBillPayment schema.
type DbsandentistDboBillPaymentClient struct {
	config
}

// NewDbsandentistDboBillPaymentClient returns a client for the DbsandentistDboBillPayment from the given config.
func NewDbsandentistDboBillPaymentClient(c config) *DbsandentistDboBillPaymentClient {
	return &DbsandentistDboBillPaymentClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `dbsandentistdbobillpayment.Hooks(f(g(h())))`.
func (c *DbsandentistDboBillPaymentClient) Use(hooks ...Hook) {
	c.hooks.DbsandentistDboBillPayment = append(c.hooks.DbsandentistDboBillPayment, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `dbsandentistdbobillpayment.Intercept(f(g(h())))`.
func (c *DbsandentistDboBillPaymentClient) Intercept(interceptors ...Interceptor) {
	c.inters.DbsandentistDboBillPayment = append(c.inters.DbsandentistDboBillPayment, interceptors...)
}

// Create returns a builder for creating a DbsandentistDboBillPayment entity.
func (c *DbsandentistDboBillPaymentClient) Create() *DbsandentistDboBillPaymentCreate {
	mutation := newDbsandentistDboBillPaymentMutation(c.config, OpCreate)
	return &DbsandentistDboBillPaymentCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of DbsandentistDboBillPayment entities.
func (c *DbsandentistDboBillPaymentClient) CreateBulk(builders ...*DbsandentistDboBillPaymentCreate) *DbsandentistDboBillPaymentCreateBulk {
	return &DbsandentistDboBillPaymentCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *DbsandentistDboBillPaymentClient) MapCreateBulk(slice any, setFunc func(*DbsandentistDboBillPaymentCreate, int)) *DbsandentistDboBillPaymentCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &DbsandentistDboBillPaymentCreateBulk{err: fmt.Errorf("calling to DbsandentistDboBillPaymentClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*DbsandentistDboBillPaymentCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &DbsandentistDboBillPaymentCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for DbsandentistDboBillPayment.
func (c *DbsandentistDboBillPaymentClient) Update() *DbsandentistDboBillPaymentUpdate {
	mutation := newDbsandentistDboBillPaymentMutation(c.config, OpUpdate)
	return &DbsandentistDboBillPaymentUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *DbsandentistDboBillPaymentClient) UpdateOne(ddbp *DbsandentistDboBillPayment) *DbsandentistDboBillPaymentUpdateOne {
	mutation := newDbsandentistDboBillPaymentMutation(c.config, OpUpdateOne, withDbsandentistDboBillPayment(ddbp))
	return &DbsandentistDboBillPaymentUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *DbsandentistDboBillPaymentClient) UpdateOneID(id int) *DbsandentistDboBillPaymentUpdateOne {
	mutation := newDbsandentistDboBillPaymentMutation(c.config, OpUpdateOne, withDbsandentistDboBillPaymentID(id))
	return &DbsandentistDboBillPaymentUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for DbsandentistDboBillPayment.
func (c *DbsandentistDboBillPaymentClient) Delete() *DbsandentistDboBillPaymentDelete {
	mutation := newDbsandentistDboBillPaymentMutation(c.config, OpDelete)
	return &DbsandentistDboBillPaymentDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *DbsandentistDboBillPaymentClient) DeleteOne(ddbp *DbsandentistDboBillPayment) *DbsandentistDboBillPaymentDeleteOne {
	return c.DeleteOneID(ddbp.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *DbsandentistDboBillPaymentClient) DeleteOneID(id int) *DbsandentistDboBillPaymentDeleteOne {
	builder := c.Delete().Where(dbsandentistdbobillpayment.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &DbsandentistDboBillPaymentDeleteOne{builder}
}

// Query returns a query builder for DbsandentistDboBillPayment.
func (c *DbsandentistDboBillPaymentClient) Query() *DbsandentistDboBillPaymentQuery {
	return &DbsandentistDboBillPaymentQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeDbsandentistDboBillPayment},
		inters: c.Interceptors(),
	}
}

// Get returns a DbsandentistDboBillPayment entity by its id.
func (c *DbsandentistDboBillPaymentClient) Get(ctx context.Context, id int) (*DbsandentistDboBillPayment, error) {
	return c.Query().Where(dbsandentistdbobillpayment.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *DbsandentistDboBillPaymentClient) GetX(ctx context.Context, id int) *DbsandentistDboBillPayment {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *DbsandentistDboBillPaymentClient) Hooks() []Hook {
	return c.hooks.DbsandentistDboBillPayment
}

// Interceptors returns the client interceptors.
func (c *DbsandentistDboBillPaymentClient) Interceptors() []Interceptor {
	return c.inters.DbsandentistDboBillPayment
}

func (c *DbsandentistDboBillPaymentClient) mutate(ctx context.Context, m *DbsandentistDboBillPaymentMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&DbsandentistDboBillPaymentCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&DbsandentistDboBillPaymentUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&DbsandentistDboBillPaymentUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&DbsandentistDboBillPaymentDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown DbsandentistDboBillPayment mutation op: %q", m.Op())
	}
}

// DbsandentistDboCustomerClient is a client for the DbsandentistDboCustomer schema.
type DbsandentistDboCustomerClient struct {
	config
}

// NewDbsandentistDboCustomerClient returns a client for the DbsandentistDboCustomer from the given config.
func NewDbsandentistDboCustomerClient(c config) *DbsandentistDboCustomerClient {
	return &DbsandentistDboCustomerClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `dbsandentistdbocustomer.Hooks(f(g(h())))`.
func (c *DbsandentistDboCustomerClient) Use(hooks ...Hook) {
	c.hooks.DbsandentistDboCustomer = append(c.hooks.DbsandentistDboCustomer, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `dbsandentistdbocustomer.Intercept(f(g(h())))`.
func (c *DbsandentistDboCustomerClient) Intercept(interceptors ...Interceptor) {
	c.inters.DbsandentistDboCustomer = append(c.inters.DbsandentistDboCustomer, interceptors...)
}

// Create returns a builder for creating a DbsandentistDboCustomer entity.
func (c *DbsandentistDboCustomerClient) Create() *DbsandentistDboCustomerCreate {
	mutation := newDbsandentistDboCustomerMutation(c.config, OpCreate)
	return &DbsandentistDboCustomerCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of DbsandentistDboCustomer entities.
func (c *DbsandentistDboCustomerClient) CreateBulk(builders ...*DbsandentistDboCustomerCreate) *DbsandentistDboCustomerCreateBulk {
	return &DbsandentistDboCustomerCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *DbsandentistDboCustomerClient) MapCreateBulk(slice any, setFunc func(*DbsandentistDboCustomerCreate, int)) *DbsandentistDboCustomerCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &DbsandentistDboCustomerCreateBulk{err: fmt.Errorf("calling to DbsandentistDboCustomerClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*DbsandentistDboCustomerCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &DbsandentistDboCustomerCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for DbsandentistDboCustomer.
func (c *DbsandentistDboCustomerClient) Update() *DbsandentistDboCustomerUpdate {
	mutation := newDbsandentistDboCustomerMutation(c.config, OpUpdate)
	return &DbsandentistDboCustomerUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *DbsandentistDboCustomerClient) UpdateOne(ddc *DbsandentistDboCustomer) *DbsandentistDboCustomerUpdateOne {
	mutation := newDbsandentistDboCustomerMutation(c.config, OpUpdateOne, withDbsandentistDboCustomer(ddc))
	return &DbsandentistDboCustomerUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *DbsandentistDboCustomerClient) UpdateOneID(id int) *DbsandentistDboCustomerUpdateOne {
	mutation := newDbsandentistDboCustomerMutation(c.config, OpUpdateOne, withDbsandentistDboCustomerID(id))
	return &DbsandentistDboCustomerUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for DbsandentistDboCustomer.
func (c *DbsandentistDboCustomerClient) Delete() *DbsandentistDboCustomerDelete {
	mutation := newDbsandentistDboCustomerMutation(c.config, OpDelete)
	return &DbsandentistDboCustomerDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *DbsandentistDboCustomerClient) DeleteOne(ddc *DbsandentistDboCustomer) *DbsandentistDboCustomerDeleteOne {
	return c.DeleteOneID(ddc.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *DbsandentistDboCustomerClient) DeleteOneID(id int) *DbsandentistDboCustomerDeleteOne {
	builder := c.Delete().Where(dbsandentistdbocustomer.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &DbsandentistDboCustomerDeleteOne{builder}
}

// Query returns a query builder for DbsandentistDboCustomer.
func (c *DbsandentistDboCustomerClient) Query() *DbsandentistDboCustomerQuery {
	return &DbsandentistDboCustomerQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeDbsandentistDboCustomer},
		inters: c.Interceptors(),
	}
}

// Get returns a DbsandentistDboCustomer entity by its id.
func (c *DbsandentistDboCustomerClient) Get(ctx context.Context, id int) (*DbsandentistDboCustomer, error) {
	return c.Query().Where(dbsandentistdbocustomer.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *DbsandentistDboCustomerClient) GetX(ctx context.Context, id int) *DbsandentistDboCustomer {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *DbsandentistDboCustomerClient) Hooks() []Hook {
	return c.hooks.DbsandentistDboCustomer
}

// Interceptors returns the client interceptors.
func (c *DbsandentistDboCustomerClient) Interceptors() []Interceptor {
	return c.inters.DbsandentistDboCustomer
}

func (c *DbsandentistDboCustomerClient) mutate(ctx context.Context, m *DbsandentistDboCustomerMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&DbsandentistDboCustomerCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&DbsandentistDboCustomerUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&DbsandentistDboCustomerUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&DbsandentistDboCustomerDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown DbsandentistDboCustomer mutation op: %q", m.Op())
	}
}

// DealClient is a client for the Deal schema.
type DealClient struct {
	config
}

// NewDealClient returns a client for the Deal from the given config.
func NewDealClient(c config) *DealClient {
	return &DealClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `deal.Hooks(f(g(h())))`.
func (c *DealClient) Use(hooks ...Hook) {
	c.hooks.Deal = append(c.hooks.Deal, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `deal.Intercept(f(g(h())))`.
func (c *DealClient) Intercept(interceptors ...Interceptor) {
	c.inters.Deal = append(c.inters.Deal, interceptors...)
}

// Create returns a builder for creating a Deal entity.
func (c *DealClient) Create() *DealCreate {
	mutation := newDealMutation(c.config, OpCreate)
	return &DealCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Deal entities.
func (c *DealClient) CreateBulk(builders ...*DealCreate) *DealCreateBulk {
	return &DealCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *DealClient) MapCreateBulk(slice any, setFunc func(*DealCreate, int)) *DealCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &DealCreateBulk{err: fmt.Errorf("calling to DealClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*DealCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &DealCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Deal.
func (c *DealClient) Update() *DealUpdate {
	mutation := newDealMutation(c.config, OpUpdate)
	return &DealUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *DealClient) UpdateOne(d *Deal) *DealUpdateOne {
	mutation := newDealMutation(c.config, OpUpdateOne, withDeal(d))
	return &DealUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *DealClient) UpdateOneID(id int) *DealUpdateOne {
	mutation := newDealMutation(c.config, OpUpdateOne, withDealID(id))
	return &DealUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Deal.
func (c *DealClient) Delete() *DealDelete {
	mutation := newDealMutation(c.config, OpDelete)
	return &DealDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *DealClient) DeleteOne(d *Deal) *DealDeleteOne {
	return c.DeleteOneID(d.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *DealClient) DeleteOneID(id int) *DealDeleteOne {
	builder := c.Delete().Where(deal.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &DealDeleteOne{builder}
}

// Query returns a query builder for Deal.
func (c *DealClient) Query() *DealQuery {
	return &DealQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeDeal},
		inters: c.Interceptors(),
	}
}

// Get returns a Deal entity by its id.
func (c *DealClient) Get(ctx context.Context, id int) (*Deal, error) {
	return c.Query().Where(deal.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *DealClient) GetX(ctx context.Context, id int) *Deal {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryPerson queries the person edge of a Deal.
func (c *DealClient) QueryPerson(d *Deal) *PersonQuery {
	query := (&PersonClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := d.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(deal.Table, deal.FieldID, id),
			sqlgraph.To(person.Table, person.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, deal.PersonTable, deal.PersonColumn),
		)
		fromV = sqlgraph.Neighbors(d.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryStage queries the stage edge of a Deal.
func (c *DealClient) QueryStage(d *Deal) *StageQuery {
	query := (&StageClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := d.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(deal.Table, deal.FieldID, id),
			sqlgraph.To(stage.Table, stage.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, deal.StageTable, deal.StageColumn),
		)
		fromV = sqlgraph.Neighbors(d.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryAttachments queries the attachments edge of a Deal.
func (c *DealClient) QueryAttachments(d *Deal) *AttachmentQuery {
	query := (&AttachmentClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := d.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(deal.Table, deal.FieldID, id),
			sqlgraph.To(attachment.Table, attachment.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, deal.AttachmentsTable, deal.AttachmentsColumn),
		)
		fromV = sqlgraph.Neighbors(d.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryPlans queries the plans edge of a Deal.
func (c *DealClient) QueryPlans(d *Deal) *InstallmentPlanQuery {
	query := (&InstallmentPlanClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := d.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(deal.Table, deal.FieldID, id),
			sqlgraph.To(installmentplan.Table, installmentplan.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, deal.PlansTable, deal.PlansColumn),
		)
		fromV = sqlgraph.Neighbors(d.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryBills queries the bills edge of a Deal.
func (c *DealClient) QueryBills(d *Deal) *BillQuery {
	query := (&BillClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := d.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(deal.Table, deal.FieldID, id),
			sqlgraph.To(bill.Table, bill.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, deal.BillsTable, deal.BillsColumn),
		)
		fromV = sqlgraph.Neighbors(d.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryTracks queries the tracks edge of a Deal.
func (c *DealClient) QueryTracks(d *Deal) *TrackQuery {
	query := (&TrackClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := d.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(deal.Table, deal.FieldID, id),
			sqlgraph.To(track.Table, track.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, deal.TracksTable, deal.TracksColumn),
		)
		fromV = sqlgraph.Neighbors(d.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryTasks queries the tasks edge of a Deal.
func (c *DealClient) QueryTasks(d *Deal) *TaskQuery {
	query := (&TaskClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := d.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(deal.Table, deal.FieldID, id),
			sqlgraph.To(task.Table, task.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, deal.TasksTable, deal.TasksColumn),
		)
		fromV = sqlgraph.Neighbors(d.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryDealAssignment queries the deal_assignment edge of a Deal.
func (c *DealClient) QueryDealAssignment(d *Deal) *DealUserQuery {
	query := (&DealUserClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := d.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(deal.Table, deal.FieldID, id),
			sqlgraph.To(dealuser.Table, dealuser.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, deal.DealAssignmentTable, deal.DealAssignmentColumn),
		)
		fromV = sqlgraph.Neighbors(d.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryTags queries the tags edge of a Deal.
func (c *DealClient) QueryTags(d *Deal) *TagQuery {
	query := (&TagClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := d.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(deal.Table, deal.FieldID, id),
			sqlgraph.To(tag.Table, tag.FieldID),
			sqlgraph.Edge(sqlgraph.M2M, false, deal.TagsTable, deal.TagsPrimaryKey...),
		)
		fromV = sqlgraph.Neighbors(d.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryDeposits queries the deposits edge of a Deal.
func (c *DealClient) QueryDeposits(d *Deal) *DepositQuery {
	query := (&DepositClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := d.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(deal.Table, deal.FieldID, id),
			sqlgraph.To(deposit.Table, deposit.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, deal.DepositsTable, deal.DepositsColumn),
		)
		fromV = sqlgraph.Neighbors(d.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryDiscountUsages queries the discount_usages edge of a Deal.
func (c *DealClient) QueryDiscountUsages(d *Deal) *DiscountUsageQuery {
	query := (&DiscountUsageClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := d.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(deal.Table, deal.FieldID, id),
			sqlgraph.To(discountusage.Table, discountusage.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, deal.DiscountUsagesTable, deal.DiscountUsagesColumn),
		)
		fromV = sqlgraph.Neighbors(d.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryTagDeal queries the tag_deal edge of a Deal.
func (c *DealClient) QueryTagDeal(d *Deal) *TagDealQuery {
	query := (&TagDealClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := d.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(deal.Table, deal.FieldID, id),
			sqlgraph.To(tagdeal.Table, tagdeal.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, deal.TagDealTable, deal.TagDealColumn),
		)
		fromV = sqlgraph.Neighbors(d.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *DealClient) Hooks() []Hook {
	hooks := c.hooks.Deal
	return append(hooks[:len(hooks):len(hooks)], deal.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *DealClient) Interceptors() []Interceptor {
	inters := c.inters.Deal
	return append(inters[:len(inters):len(inters)], deal.Interceptors[:]...)
}

func (c *DealClient) mutate(ctx context.Context, m *DealMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&DealCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&DealUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&DealUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&DealDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Deal mutation op: %q", m.Op())
	}
}

// DealHistoryClient is a client for the DealHistory schema.
type DealHistoryClient struct {
	config
}

// NewDealHistoryClient returns a client for the DealHistory from the given config.
func NewDealHistoryClient(c config) *DealHistoryClient {
	return &DealHistoryClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `dealhistory.Hooks(f(g(h())))`.
func (c *DealHistoryClient) Use(hooks ...Hook) {
	c.hooks.DealHistory = append(c.hooks.DealHistory, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `dealhistory.Intercept(f(g(h())))`.
func (c *DealHistoryClient) Intercept(interceptors ...Interceptor) {
	c.inters.DealHistory = append(c.inters.DealHistory, interceptors...)
}

// Create returns a builder for creating a DealHistory entity.
func (c *DealHistoryClient) Create() *DealHistoryCreate {
	mutation := newDealHistoryMutation(c.config, OpCreate)
	return &DealHistoryCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of DealHistory entities.
func (c *DealHistoryClient) CreateBulk(builders ...*DealHistoryCreate) *DealHistoryCreateBulk {
	return &DealHistoryCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *DealHistoryClient) MapCreateBulk(slice any, setFunc func(*DealHistoryCreate, int)) *DealHistoryCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &DealHistoryCreateBulk{err: fmt.Errorf("calling to DealHistoryClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*DealHistoryCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &DealHistoryCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for DealHistory.
func (c *DealHistoryClient) Update() *DealHistoryUpdate {
	mutation := newDealHistoryMutation(c.config, OpUpdate)
	return &DealHistoryUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *DealHistoryClient) UpdateOne(dh *DealHistory) *DealHistoryUpdateOne {
	mutation := newDealHistoryMutation(c.config, OpUpdateOne, withDealHistory(dh))
	return &DealHistoryUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *DealHistoryClient) UpdateOneID(id int) *DealHistoryUpdateOne {
	mutation := newDealHistoryMutation(c.config, OpUpdateOne, withDealHistoryID(id))
	return &DealHistoryUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for DealHistory.
func (c *DealHistoryClient) Delete() *DealHistoryDelete {
	mutation := newDealHistoryMutation(c.config, OpDelete)
	return &DealHistoryDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *DealHistoryClient) DeleteOne(dh *DealHistory) *DealHistoryDeleteOne {
	return c.DeleteOneID(dh.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *DealHistoryClient) DeleteOneID(id int) *DealHistoryDeleteOne {
	builder := c.Delete().Where(dealhistory.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &DealHistoryDeleteOne{builder}
}

// Query returns a query builder for DealHistory.
func (c *DealHistoryClient) Query() *DealHistoryQuery {
	return &DealHistoryQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeDealHistory},
		inters: c.Interceptors(),
	}
}

// Get returns a DealHistory entity by its id.
func (c *DealHistoryClient) Get(ctx context.Context, id int) (*DealHistory, error) {
	return c.Query().Where(dealhistory.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *DealHistoryClient) GetX(ctx context.Context, id int) *DealHistory {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *DealHistoryClient) Hooks() []Hook {
	return c.hooks.DealHistory
}

// Interceptors returns the client interceptors.
func (c *DealHistoryClient) Interceptors() []Interceptor {
	return c.inters.DealHistory
}

func (c *DealHistoryClient) mutate(ctx context.Context, m *DealHistoryMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&DealHistoryCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&DealHistoryUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&DealHistoryUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&DealHistoryDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown DealHistory mutation op: %q", m.Op())
	}
}

// DealStageHistoryClient is a client for the DealStageHistory schema.
type DealStageHistoryClient struct {
	config
}

// NewDealStageHistoryClient returns a client for the DealStageHistory from the given config.
func NewDealStageHistoryClient(c config) *DealStageHistoryClient {
	return &DealStageHistoryClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `dealstagehistory.Hooks(f(g(h())))`.
func (c *DealStageHistoryClient) Use(hooks ...Hook) {
	c.hooks.DealStageHistory = append(c.hooks.DealStageHistory, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `dealstagehistory.Intercept(f(g(h())))`.
func (c *DealStageHistoryClient) Intercept(interceptors ...Interceptor) {
	c.inters.DealStageHistory = append(c.inters.DealStageHistory, interceptors...)
}

// Create returns a builder for creating a DealStageHistory entity.
func (c *DealStageHistoryClient) Create() *DealStageHistoryCreate {
	mutation := newDealStageHistoryMutation(c.config, OpCreate)
	return &DealStageHistoryCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of DealStageHistory entities.
func (c *DealStageHistoryClient) CreateBulk(builders ...*DealStageHistoryCreate) *DealStageHistoryCreateBulk {
	return &DealStageHistoryCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *DealStageHistoryClient) MapCreateBulk(slice any, setFunc func(*DealStageHistoryCreate, int)) *DealStageHistoryCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &DealStageHistoryCreateBulk{err: fmt.Errorf("calling to DealStageHistoryClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*DealStageHistoryCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &DealStageHistoryCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for DealStageHistory.
func (c *DealStageHistoryClient) Update() *DealStageHistoryUpdate {
	mutation := newDealStageHistoryMutation(c.config, OpUpdate)
	return &DealStageHistoryUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *DealStageHistoryClient) UpdateOne(dsh *DealStageHistory) *DealStageHistoryUpdateOne {
	mutation := newDealStageHistoryMutation(c.config, OpUpdateOne, withDealStageHistory(dsh))
	return &DealStageHistoryUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *DealStageHistoryClient) UpdateOneID(id int) *DealStageHistoryUpdateOne {
	mutation := newDealStageHistoryMutation(c.config, OpUpdateOne, withDealStageHistoryID(id))
	return &DealStageHistoryUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for DealStageHistory.
func (c *DealStageHistoryClient) Delete() *DealStageHistoryDelete {
	mutation := newDealStageHistoryMutation(c.config, OpDelete)
	return &DealStageHistoryDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *DealStageHistoryClient) DeleteOne(dsh *DealStageHistory) *DealStageHistoryDeleteOne {
	return c.DeleteOneID(dsh.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *DealStageHistoryClient) DeleteOneID(id int) *DealStageHistoryDeleteOne {
	builder := c.Delete().Where(dealstagehistory.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &DealStageHistoryDeleteOne{builder}
}

// Query returns a query builder for DealStageHistory.
func (c *DealStageHistoryClient) Query() *DealStageHistoryQuery {
	return &DealStageHistoryQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeDealStageHistory},
		inters: c.Interceptors(),
	}
}

// Get returns a DealStageHistory entity by its id.
func (c *DealStageHistoryClient) Get(ctx context.Context, id int) (*DealStageHistory, error) {
	return c.Query().Where(dealstagehistory.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *DealStageHistoryClient) GetX(ctx context.Context, id int) *DealStageHistory {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *DealStageHistoryClient) Hooks() []Hook {
	return c.hooks.DealStageHistory
}

// Interceptors returns the client interceptors.
func (c *DealStageHistoryClient) Interceptors() []Interceptor {
	return c.inters.DealStageHistory
}

func (c *DealStageHistoryClient) mutate(ctx context.Context, m *DealStageHistoryMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&DealStageHistoryCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&DealStageHistoryUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&DealStageHistoryUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&DealStageHistoryDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown DealStageHistory mutation op: %q", m.Op())
	}
}

// DealStageHistoryViewClient is a client for the DealStageHistoryView schema.
type DealStageHistoryViewClient struct {
	config
}

// NewDealStageHistoryViewClient returns a client for the DealStageHistoryView from the given config.
func NewDealStageHistoryViewClient(c config) *DealStageHistoryViewClient {
	return &DealStageHistoryViewClient{config: c}
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `dealstagehistoryview.Intercept(f(g(h())))`.
func (c *DealStageHistoryViewClient) Intercept(interceptors ...Interceptor) {
	c.inters.DealStageHistoryView = append(c.inters.DealStageHistoryView, interceptors...)
}

// Query returns a query builder for DealStageHistoryView.
func (c *DealStageHistoryViewClient) Query() *DealStageHistoryViewQuery {
	return &DealStageHistoryViewQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeDealStageHistoryView},
		inters: c.Interceptors(),
	}
}

// Interceptors returns the client interceptors.
func (c *DealStageHistoryViewClient) Interceptors() []Interceptor {
	return c.inters.DealStageHistoryView
}

// DealUserClient is a client for the DealUser schema.
type DealUserClient struct {
	config
}

// NewDealUserClient returns a client for the DealUser from the given config.
func NewDealUserClient(c config) *DealUserClient {
	return &DealUserClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `dealuser.Hooks(f(g(h())))`.
func (c *DealUserClient) Use(hooks ...Hook) {
	c.hooks.DealUser = append(c.hooks.DealUser, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `dealuser.Intercept(f(g(h())))`.
func (c *DealUserClient) Intercept(interceptors ...Interceptor) {
	c.inters.DealUser = append(c.inters.DealUser, interceptors...)
}

// Create returns a builder for creating a DealUser entity.
func (c *DealUserClient) Create() *DealUserCreate {
	mutation := newDealUserMutation(c.config, OpCreate)
	return &DealUserCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of DealUser entities.
func (c *DealUserClient) CreateBulk(builders ...*DealUserCreate) *DealUserCreateBulk {
	return &DealUserCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *DealUserClient) MapCreateBulk(slice any, setFunc func(*DealUserCreate, int)) *DealUserCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &DealUserCreateBulk{err: fmt.Errorf("calling to DealUserClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*DealUserCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &DealUserCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for DealUser.
func (c *DealUserClient) Update() *DealUserUpdate {
	mutation := newDealUserMutation(c.config, OpUpdate)
	return &DealUserUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *DealUserClient) UpdateOne(du *DealUser) *DealUserUpdateOne {
	mutation := newDealUserMutation(c.config, OpUpdateOne, withDealUser(du))
	return &DealUserUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *DealUserClient) UpdateOneID(id int) *DealUserUpdateOne {
	mutation := newDealUserMutation(c.config, OpUpdateOne, withDealUserID(id))
	return &DealUserUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for DealUser.
func (c *DealUserClient) Delete() *DealUserDelete {
	mutation := newDealUserMutation(c.config, OpDelete)
	return &DealUserDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *DealUserClient) DeleteOne(du *DealUser) *DealUserDeleteOne {
	return c.DeleteOneID(du.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *DealUserClient) DeleteOneID(id int) *DealUserDeleteOne {
	builder := c.Delete().Where(dealuser.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &DealUserDeleteOne{builder}
}

// Query returns a query builder for DealUser.
func (c *DealUserClient) Query() *DealUserQuery {
	return &DealUserQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeDealUser},
		inters: c.Interceptors(),
	}
}

// Get returns a DealUser entity by its id.
func (c *DealUserClient) Get(ctx context.Context, id int) (*DealUser, error) {
	return c.Query().Where(dealuser.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *DealUserClient) GetX(ctx context.Context, id int) *DealUser {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryDeal queries the deal edge of a DealUser.
func (c *DealUserClient) QueryDeal(du *DealUser) *DealQuery {
	query := (&DealClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := du.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(dealuser.Table, dealuser.FieldID, id),
			sqlgraph.To(deal.Table, deal.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, dealuser.DealTable, dealuser.DealColumn),
		)
		fromV = sqlgraph.Neighbors(du.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryUser queries the user edge of a DealUser.
func (c *DealUserClient) QueryUser(du *DealUser) *UserQuery {
	query := (&UserClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := du.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(dealuser.Table, dealuser.FieldID, id),
			sqlgraph.To(user.Table, user.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, dealuser.UserTable, dealuser.UserColumn),
		)
		fromV = sqlgraph.Neighbors(du.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryRatings queries the ratings edge of a DealUser.
func (c *DealUserClient) QueryRatings(du *DealUser) *DealUserRatingQuery {
	query := (&DealUserRatingClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := du.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(dealuser.Table, dealuser.FieldID, id),
			sqlgraph.To(dealuserrating.Table, dealuserrating.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, dealuser.RatingsTable, dealuser.RatingsColumn),
		)
		fromV = sqlgraph.Neighbors(du.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *DealUserClient) Hooks() []Hook {
	return c.hooks.DealUser
}

// Interceptors returns the client interceptors.
func (c *DealUserClient) Interceptors() []Interceptor {
	return c.inters.DealUser
}

func (c *DealUserClient) mutate(ctx context.Context, m *DealUserMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&DealUserCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&DealUserUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&DealUserUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&DealUserDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown DealUser mutation op: %q", m.Op())
	}
}

// DealUserRatingClient is a client for the DealUserRating schema.
type DealUserRatingClient struct {
	config
}

// NewDealUserRatingClient returns a client for the DealUserRating from the given config.
func NewDealUserRatingClient(c config) *DealUserRatingClient {
	return &DealUserRatingClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `dealuserrating.Hooks(f(g(h())))`.
func (c *DealUserRatingClient) Use(hooks ...Hook) {
	c.hooks.DealUserRating = append(c.hooks.DealUserRating, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `dealuserrating.Intercept(f(g(h())))`.
func (c *DealUserRatingClient) Intercept(interceptors ...Interceptor) {
	c.inters.DealUserRating = append(c.inters.DealUserRating, interceptors...)
}

// Create returns a builder for creating a DealUserRating entity.
func (c *DealUserRatingClient) Create() *DealUserRatingCreate {
	mutation := newDealUserRatingMutation(c.config, OpCreate)
	return &DealUserRatingCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of DealUserRating entities.
func (c *DealUserRatingClient) CreateBulk(builders ...*DealUserRatingCreate) *DealUserRatingCreateBulk {
	return &DealUserRatingCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *DealUserRatingClient) MapCreateBulk(slice any, setFunc func(*DealUserRatingCreate, int)) *DealUserRatingCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &DealUserRatingCreateBulk{err: fmt.Errorf("calling to DealUserRatingClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*DealUserRatingCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &DealUserRatingCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for DealUserRating.
func (c *DealUserRatingClient) Update() *DealUserRatingUpdate {
	mutation := newDealUserRatingMutation(c.config, OpUpdate)
	return &DealUserRatingUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *DealUserRatingClient) UpdateOne(dur *DealUserRating) *DealUserRatingUpdateOne {
	mutation := newDealUserRatingMutation(c.config, OpUpdateOne, withDealUserRating(dur))
	return &DealUserRatingUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *DealUserRatingClient) UpdateOneID(id int) *DealUserRatingUpdateOne {
	mutation := newDealUserRatingMutation(c.config, OpUpdateOne, withDealUserRatingID(id))
	return &DealUserRatingUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for DealUserRating.
func (c *DealUserRatingClient) Delete() *DealUserRatingDelete {
	mutation := newDealUserRatingMutation(c.config, OpDelete)
	return &DealUserRatingDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *DealUserRatingClient) DeleteOne(dur *DealUserRating) *DealUserRatingDeleteOne {
	return c.DeleteOneID(dur.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *DealUserRatingClient) DeleteOneID(id int) *DealUserRatingDeleteOne {
	builder := c.Delete().Where(dealuserrating.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &DealUserRatingDeleteOne{builder}
}

// Query returns a query builder for DealUserRating.
func (c *DealUserRatingClient) Query() *DealUserRatingQuery {
	return &DealUserRatingQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeDealUserRating},
		inters: c.Interceptors(),
	}
}

// Get returns a DealUserRating entity by its id.
func (c *DealUserRatingClient) Get(ctx context.Context, id int) (*DealUserRating, error) {
	return c.Query().Where(dealuserrating.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *DealUserRatingClient) GetX(ctx context.Context, id int) *DealUserRating {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryDealUser queries the deal_user edge of a DealUserRating.
func (c *DealUserRatingClient) QueryDealUser(dur *DealUserRating) *DealUserQuery {
	query := (&DealUserClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := dur.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(dealuserrating.Table, dealuserrating.FieldID, id),
			sqlgraph.To(dealuser.Table, dealuser.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, dealuserrating.DealUserTable, dealuserrating.DealUserColumn),
		)
		fromV = sqlgraph.Neighbors(dur.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *DealUserRatingClient) Hooks() []Hook {
	return c.hooks.DealUserRating
}

// Interceptors returns the client interceptors.
func (c *DealUserRatingClient) Interceptors() []Interceptor {
	return c.inters.DealUserRating
}

func (c *DealUserRatingClient) mutate(ctx context.Context, m *DealUserRatingMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&DealUserRatingCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&DealUserRatingUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&DealUserRatingUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&DealUserRatingDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown DealUserRating mutation op: %q", m.Op())
	}
}

// DepartmentClient is a client for the Department schema.
type DepartmentClient struct {
	config
}

// NewDepartmentClient returns a client for the Department from the given config.
func NewDepartmentClient(c config) *DepartmentClient {
	return &DepartmentClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `department.Hooks(f(g(h())))`.
func (c *DepartmentClient) Use(hooks ...Hook) {
	c.hooks.Department = append(c.hooks.Department, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `department.Intercept(f(g(h())))`.
func (c *DepartmentClient) Intercept(interceptors ...Interceptor) {
	c.inters.Department = append(c.inters.Department, interceptors...)
}

// Create returns a builder for creating a Department entity.
func (c *DepartmentClient) Create() *DepartmentCreate {
	mutation := newDepartmentMutation(c.config, OpCreate)
	return &DepartmentCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Department entities.
func (c *DepartmentClient) CreateBulk(builders ...*DepartmentCreate) *DepartmentCreateBulk {
	return &DepartmentCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *DepartmentClient) MapCreateBulk(slice any, setFunc func(*DepartmentCreate, int)) *DepartmentCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &DepartmentCreateBulk{err: fmt.Errorf("calling to DepartmentClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*DepartmentCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &DepartmentCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Department.
func (c *DepartmentClient) Update() *DepartmentUpdate {
	mutation := newDepartmentMutation(c.config, OpUpdate)
	return &DepartmentUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *DepartmentClient) UpdateOne(d *Department) *DepartmentUpdateOne {
	mutation := newDepartmentMutation(c.config, OpUpdateOne, withDepartment(d))
	return &DepartmentUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *DepartmentClient) UpdateOneID(id int) *DepartmentUpdateOne {
	mutation := newDepartmentMutation(c.config, OpUpdateOne, withDepartmentID(id))
	return &DepartmentUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Department.
func (c *DepartmentClient) Delete() *DepartmentDelete {
	mutation := newDepartmentMutation(c.config, OpDelete)
	return &DepartmentDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *DepartmentClient) DeleteOne(d *Department) *DepartmentDeleteOne {
	return c.DeleteOneID(d.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *DepartmentClient) DeleteOneID(id int) *DepartmentDeleteOne {
	builder := c.Delete().Where(department.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &DepartmentDeleteOne{builder}
}

// Query returns a query builder for Department.
func (c *DepartmentClient) Query() *DepartmentQuery {
	return &DepartmentQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeDepartment},
		inters: c.Interceptors(),
	}
}

// Get returns a Department entity by its id.
func (c *DepartmentClient) Get(ctx context.Context, id int) (*Department, error) {
	return c.Query().Where(department.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *DepartmentClient) GetX(ctx context.Context, id int) *Department {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryDepartmentAssignment queries the department_assignment edge of a Department.
func (c *DepartmentClient) QueryDepartmentAssignment(d *Department) *TaskDepartmentQuery {
	query := (&TaskDepartmentClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := d.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(department.Table, department.FieldID, id),
			sqlgraph.To(taskdepartment.Table, taskdepartment.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, department.DepartmentAssignmentTable, department.DepartmentAssignmentColumn),
		)
		fromV = sqlgraph.Neighbors(d.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *DepartmentClient) Hooks() []Hook {
	return c.hooks.Department
}

// Interceptors returns the client interceptors.
func (c *DepartmentClient) Interceptors() []Interceptor {
	return c.inters.Department
}

func (c *DepartmentClient) mutate(ctx context.Context, m *DepartmentMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&DepartmentCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&DepartmentUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&DepartmentUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&DepartmentDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Department mutation op: %q", m.Op())
	}
}

// DepositClient is a client for the Deposit schema.
type DepositClient struct {
	config
}

// NewDepositClient returns a client for the Deposit from the given config.
func NewDepositClient(c config) *DepositClient {
	return &DepositClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `deposit.Hooks(f(g(h())))`.
func (c *DepositClient) Use(hooks ...Hook) {
	c.hooks.Deposit = append(c.hooks.Deposit, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `deposit.Intercept(f(g(h())))`.
func (c *DepositClient) Intercept(interceptors ...Interceptor) {
	c.inters.Deposit = append(c.inters.Deposit, interceptors...)
}

// Create returns a builder for creating a Deposit entity.
func (c *DepositClient) Create() *DepositCreate {
	mutation := newDepositMutation(c.config, OpCreate)
	return &DepositCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Deposit entities.
func (c *DepositClient) CreateBulk(builders ...*DepositCreate) *DepositCreateBulk {
	return &DepositCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *DepositClient) MapCreateBulk(slice any, setFunc func(*DepositCreate, int)) *DepositCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &DepositCreateBulk{err: fmt.Errorf("calling to DepositClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*DepositCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &DepositCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Deposit.
func (c *DepositClient) Update() *DepositUpdate {
	mutation := newDepositMutation(c.config, OpUpdate)
	return &DepositUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *DepositClient) UpdateOne(d *Deposit) *DepositUpdateOne {
	mutation := newDepositMutation(c.config, OpUpdateOne, withDeposit(d))
	return &DepositUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *DepositClient) UpdateOneID(id int) *DepositUpdateOne {
	mutation := newDepositMutation(c.config, OpUpdateOne, withDepositID(id))
	return &DepositUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Deposit.
func (c *DepositClient) Delete() *DepositDelete {
	mutation := newDepositMutation(c.config, OpDelete)
	return &DepositDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *DepositClient) DeleteOne(d *Deposit) *DepositDeleteOne {
	return c.DeleteOneID(d.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *DepositClient) DeleteOneID(id int) *DepositDeleteOne {
	builder := c.Delete().Where(deposit.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &DepositDeleteOne{builder}
}

// Query returns a query builder for Deposit.
func (c *DepositClient) Query() *DepositQuery {
	return &DepositQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeDeposit},
		inters: c.Interceptors(),
	}
}

// Get returns a Deposit entity by its id.
func (c *DepositClient) Get(ctx context.Context, id int) (*Deposit, error) {
	return c.Query().Where(deposit.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *DepositClient) GetX(ctx context.Context, id int) *Deposit {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryDeal queries the deal edge of a Deposit.
func (c *DepositClient) QueryDeal(d *Deposit) *DealQuery {
	query := (&DealClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := d.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(deposit.Table, deposit.FieldID, id),
			sqlgraph.To(deal.Table, deal.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, deposit.DealTable, deposit.DealColumn),
		)
		fromV = sqlgraph.Neighbors(d.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryAllocations queries the allocations edge of a Deposit.
func (c *DepositClient) QueryAllocations(d *Deposit) *DepositAllocationQuery {
	query := (&DepositAllocationClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := d.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(deposit.Table, deposit.FieldID, id),
			sqlgraph.To(depositallocation.Table, depositallocation.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, deposit.AllocationsTable, deposit.AllocationsColumn),
		)
		fromV = sqlgraph.Neighbors(d.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryPayments queries the payments edge of a Deposit.
func (c *DepositClient) QueryPayments(d *Deposit) *DepositPaymentQuery {
	query := (&DepositPaymentClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := d.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(deposit.Table, deposit.FieldID, id),
			sqlgraph.To(depositpayment.Table, depositpayment.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, deposit.PaymentsTable, deposit.PaymentsColumn),
		)
		fromV = sqlgraph.Neighbors(d.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *DepositClient) Hooks() []Hook {
	hooks := c.hooks.Deposit
	return append(hooks[:len(hooks):len(hooks)], deposit.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *DepositClient) Interceptors() []Interceptor {
	inters := c.inters.Deposit
	return append(inters[:len(inters):len(inters)], deposit.Interceptors[:]...)
}

func (c *DepositClient) mutate(ctx context.Context, m *DepositMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&DepositCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&DepositUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&DepositUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&DepositDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Deposit mutation op: %q", m.Op())
	}
}

// DepositAllocationClient is a client for the DepositAllocation schema.
type DepositAllocationClient struct {
	config
}

// NewDepositAllocationClient returns a client for the DepositAllocation from the given config.
func NewDepositAllocationClient(c config) *DepositAllocationClient {
	return &DepositAllocationClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `depositallocation.Hooks(f(g(h())))`.
func (c *DepositAllocationClient) Use(hooks ...Hook) {
	c.hooks.DepositAllocation = append(c.hooks.DepositAllocation, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `depositallocation.Intercept(f(g(h())))`.
func (c *DepositAllocationClient) Intercept(interceptors ...Interceptor) {
	c.inters.DepositAllocation = append(c.inters.DepositAllocation, interceptors...)
}

// Create returns a builder for creating a DepositAllocation entity.
func (c *DepositAllocationClient) Create() *DepositAllocationCreate {
	mutation := newDepositAllocationMutation(c.config, OpCreate)
	return &DepositAllocationCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of DepositAllocation entities.
func (c *DepositAllocationClient) CreateBulk(builders ...*DepositAllocationCreate) *DepositAllocationCreateBulk {
	return &DepositAllocationCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *DepositAllocationClient) MapCreateBulk(slice any, setFunc func(*DepositAllocationCreate, int)) *DepositAllocationCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &DepositAllocationCreateBulk{err: fmt.Errorf("calling to DepositAllocationClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*DepositAllocationCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &DepositAllocationCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for DepositAllocation.
func (c *DepositAllocationClient) Update() *DepositAllocationUpdate {
	mutation := newDepositAllocationMutation(c.config, OpUpdate)
	return &DepositAllocationUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *DepositAllocationClient) UpdateOne(da *DepositAllocation) *DepositAllocationUpdateOne {
	mutation := newDepositAllocationMutation(c.config, OpUpdateOne, withDepositAllocation(da))
	return &DepositAllocationUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *DepositAllocationClient) UpdateOneID(id int) *DepositAllocationUpdateOne {
	mutation := newDepositAllocationMutation(c.config, OpUpdateOne, withDepositAllocationID(id))
	return &DepositAllocationUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for DepositAllocation.
func (c *DepositAllocationClient) Delete() *DepositAllocationDelete {
	mutation := newDepositAllocationMutation(c.config, OpDelete)
	return &DepositAllocationDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *DepositAllocationClient) DeleteOne(da *DepositAllocation) *DepositAllocationDeleteOne {
	return c.DeleteOneID(da.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *DepositAllocationClient) DeleteOneID(id int) *DepositAllocationDeleteOne {
	builder := c.Delete().Where(depositallocation.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &DepositAllocationDeleteOne{builder}
}

// Query returns a query builder for DepositAllocation.
func (c *DepositAllocationClient) Query() *DepositAllocationQuery {
	return &DepositAllocationQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeDepositAllocation},
		inters: c.Interceptors(),
	}
}

// Get returns a DepositAllocation entity by its id.
func (c *DepositAllocationClient) Get(ctx context.Context, id int) (*DepositAllocation, error) {
	return c.Query().Where(depositallocation.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *DepositAllocationClient) GetX(ctx context.Context, id int) *DepositAllocation {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryDeposit queries the deposit edge of a DepositAllocation.
func (c *DepositAllocationClient) QueryDeposit(da *DepositAllocation) *DepositQuery {
	query := (&DepositClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := da.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(depositallocation.Table, depositallocation.FieldID, id),
			sqlgraph.To(deposit.Table, deposit.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, depositallocation.DepositTable, depositallocation.DepositColumn),
		)
		fromV = sqlgraph.Neighbors(da.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryAttachment queries the attachment edge of a DepositAllocation.
func (c *DepositAllocationClient) QueryAttachment(da *DepositAllocation) *AttachmentQuery {
	query := (&AttachmentClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := da.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(depositallocation.Table, depositallocation.FieldID, id),
			sqlgraph.To(attachment.Table, attachment.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, depositallocation.AttachmentTable, depositallocation.AttachmentColumn),
		)
		fromV = sqlgraph.Neighbors(da.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *DepositAllocationClient) Hooks() []Hook {
	hooks := c.hooks.DepositAllocation
	return append(hooks[:len(hooks):len(hooks)], depositallocation.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *DepositAllocationClient) Interceptors() []Interceptor {
	inters := c.inters.DepositAllocation
	return append(inters[:len(inters):len(inters)], depositallocation.Interceptors[:]...)
}

func (c *DepositAllocationClient) mutate(ctx context.Context, m *DepositAllocationMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&DepositAllocationCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&DepositAllocationUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&DepositAllocationUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&DepositAllocationDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown DepositAllocation mutation op: %q", m.Op())
	}
}

// DepositPaymentClient is a client for the DepositPayment schema.
type DepositPaymentClient struct {
	config
}

// NewDepositPaymentClient returns a client for the DepositPayment from the given config.
func NewDepositPaymentClient(c config) *DepositPaymentClient {
	return &DepositPaymentClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `depositpayment.Hooks(f(g(h())))`.
func (c *DepositPaymentClient) Use(hooks ...Hook) {
	c.hooks.DepositPayment = append(c.hooks.DepositPayment, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `depositpayment.Intercept(f(g(h())))`.
func (c *DepositPaymentClient) Intercept(interceptors ...Interceptor) {
	c.inters.DepositPayment = append(c.inters.DepositPayment, interceptors...)
}

// Create returns a builder for creating a DepositPayment entity.
func (c *DepositPaymentClient) Create() *DepositPaymentCreate {
	mutation := newDepositPaymentMutation(c.config, OpCreate)
	return &DepositPaymentCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of DepositPayment entities.
func (c *DepositPaymentClient) CreateBulk(builders ...*DepositPaymentCreate) *DepositPaymentCreateBulk {
	return &DepositPaymentCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *DepositPaymentClient) MapCreateBulk(slice any, setFunc func(*DepositPaymentCreate, int)) *DepositPaymentCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &DepositPaymentCreateBulk{err: fmt.Errorf("calling to DepositPaymentClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*DepositPaymentCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &DepositPaymentCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for DepositPayment.
func (c *DepositPaymentClient) Update() *DepositPaymentUpdate {
	mutation := newDepositPaymentMutation(c.config, OpUpdate)
	return &DepositPaymentUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *DepositPaymentClient) UpdateOne(dp *DepositPayment) *DepositPaymentUpdateOne {
	mutation := newDepositPaymentMutation(c.config, OpUpdateOne, withDepositPayment(dp))
	return &DepositPaymentUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *DepositPaymentClient) UpdateOneID(id int) *DepositPaymentUpdateOne {
	mutation := newDepositPaymentMutation(c.config, OpUpdateOne, withDepositPaymentID(id))
	return &DepositPaymentUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for DepositPayment.
func (c *DepositPaymentClient) Delete() *DepositPaymentDelete {
	mutation := newDepositPaymentMutation(c.config, OpDelete)
	return &DepositPaymentDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *DepositPaymentClient) DeleteOne(dp *DepositPayment) *DepositPaymentDeleteOne {
	return c.DeleteOneID(dp.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *DepositPaymentClient) DeleteOneID(id int) *DepositPaymentDeleteOne {
	builder := c.Delete().Where(depositpayment.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &DepositPaymentDeleteOne{builder}
}

// Query returns a query builder for DepositPayment.
func (c *DepositPaymentClient) Query() *DepositPaymentQuery {
	return &DepositPaymentQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeDepositPayment},
		inters: c.Interceptors(),
	}
}

// Get returns a DepositPayment entity by its id.
func (c *DepositPaymentClient) Get(ctx context.Context, id int) (*DepositPayment, error) {
	return c.Query().Where(depositpayment.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *DepositPaymentClient) GetX(ctx context.Context, id int) *DepositPayment {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryDeposit queries the deposit edge of a DepositPayment.
func (c *DepositPaymentClient) QueryDeposit(dp *DepositPayment) *DepositQuery {
	query := (&DepositClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := dp.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(depositpayment.Table, depositpayment.FieldID, id),
			sqlgraph.To(deposit.Table, deposit.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, depositpayment.DepositTable, depositpayment.DepositColumn),
		)
		fromV = sqlgraph.Neighbors(dp.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryPayment queries the payment edge of a DepositPayment.
func (c *DepositPaymentClient) QueryPayment(dp *DepositPayment) *PaymentQuery {
	query := (&PaymentClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := dp.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(depositpayment.Table, depositpayment.FieldID, id),
			sqlgraph.To(payment.Table, payment.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, depositpayment.PaymentTable, depositpayment.PaymentColumn),
		)
		fromV = sqlgraph.Neighbors(dp.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *DepositPaymentClient) Hooks() []Hook {
	hooks := c.hooks.DepositPayment
	return append(hooks[:len(hooks):len(hooks)], depositpayment.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *DepositPaymentClient) Interceptors() []Interceptor {
	inters := c.inters.DepositPayment
	return append(inters[:len(inters):len(inters)], depositpayment.Interceptors[:]...)
}

func (c *DepositPaymentClient) mutate(ctx context.Context, m *DepositPaymentMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&DepositPaymentCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&DepositPaymentUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&DepositPaymentUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&DepositPaymentDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown DepositPayment mutation op: %q", m.Op())
	}
}

// DiscountClient is a client for the Discount schema.
type DiscountClient struct {
	config
}

// NewDiscountClient returns a client for the Discount from the given config.
func NewDiscountClient(c config) *DiscountClient {
	return &DiscountClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `discount.Hooks(f(g(h())))`.
func (c *DiscountClient) Use(hooks ...Hook) {
	c.hooks.Discount = append(c.hooks.Discount, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `discount.Intercept(f(g(h())))`.
func (c *DiscountClient) Intercept(interceptors ...Interceptor) {
	c.inters.Discount = append(c.inters.Discount, interceptors...)
}

// Create returns a builder for creating a Discount entity.
func (c *DiscountClient) Create() *DiscountCreate {
	mutation := newDiscountMutation(c.config, OpCreate)
	return &DiscountCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Discount entities.
func (c *DiscountClient) CreateBulk(builders ...*DiscountCreate) *DiscountCreateBulk {
	return &DiscountCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *DiscountClient) MapCreateBulk(slice any, setFunc func(*DiscountCreate, int)) *DiscountCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &DiscountCreateBulk{err: fmt.Errorf("calling to DiscountClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*DiscountCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &DiscountCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Discount.
func (c *DiscountClient) Update() *DiscountUpdate {
	mutation := newDiscountMutation(c.config, OpUpdate)
	return &DiscountUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *DiscountClient) UpdateOne(d *Discount) *DiscountUpdateOne {
	mutation := newDiscountMutation(c.config, OpUpdateOne, withDiscount(d))
	return &DiscountUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *DiscountClient) UpdateOneID(id int) *DiscountUpdateOne {
	mutation := newDiscountMutation(c.config, OpUpdateOne, withDiscountID(id))
	return &DiscountUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Discount.
func (c *DiscountClient) Delete() *DiscountDelete {
	mutation := newDiscountMutation(c.config, OpDelete)
	return &DiscountDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *DiscountClient) DeleteOne(d *Discount) *DiscountDeleteOne {
	return c.DeleteOneID(d.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *DiscountClient) DeleteOneID(id int) *DiscountDeleteOne {
	builder := c.Delete().Where(discount.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &DiscountDeleteOne{builder}
}

// Query returns a query builder for Discount.
func (c *DiscountClient) Query() *DiscountQuery {
	return &DiscountQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeDiscount},
		inters: c.Interceptors(),
	}
}

// Get returns a Discount entity by its id.
func (c *DiscountClient) Get(ctx context.Context, id int) (*Discount, error) {
	return c.Query().Where(discount.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *DiscountClient) GetX(ctx context.Context, id int) *Discount {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryDiscountUsages queries the discount_usages edge of a Discount.
func (c *DiscountClient) QueryDiscountUsages(d *Discount) *DiscountUsageQuery {
	query := (&DiscountUsageClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := d.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(discount.Table, discount.FieldID, id),
			sqlgraph.To(discountusage.Table, discountusage.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, discount.DiscountUsagesTable, discount.DiscountUsagesColumn),
		)
		fromV = sqlgraph.Neighbors(d.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *DiscountClient) Hooks() []Hook {
	hooks := c.hooks.Discount
	return append(hooks[:len(hooks):len(hooks)], discount.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *DiscountClient) Interceptors() []Interceptor {
	inters := c.inters.Discount
	return append(inters[:len(inters):len(inters)], discount.Interceptors[:]...)
}

func (c *DiscountClient) mutate(ctx context.Context, m *DiscountMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&DiscountCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&DiscountUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&DiscountUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&DiscountDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Discount mutation op: %q", m.Op())
	}
}

// DiscountUsageClient is a client for the DiscountUsage schema.
type DiscountUsageClient struct {
	config
}

// NewDiscountUsageClient returns a client for the DiscountUsage from the given config.
func NewDiscountUsageClient(c config) *DiscountUsageClient {
	return &DiscountUsageClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `discountusage.Hooks(f(g(h())))`.
func (c *DiscountUsageClient) Use(hooks ...Hook) {
	c.hooks.DiscountUsage = append(c.hooks.DiscountUsage, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `discountusage.Intercept(f(g(h())))`.
func (c *DiscountUsageClient) Intercept(interceptors ...Interceptor) {
	c.inters.DiscountUsage = append(c.inters.DiscountUsage, interceptors...)
}

// Create returns a builder for creating a DiscountUsage entity.
func (c *DiscountUsageClient) Create() *DiscountUsageCreate {
	mutation := newDiscountUsageMutation(c.config, OpCreate)
	return &DiscountUsageCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of DiscountUsage entities.
func (c *DiscountUsageClient) CreateBulk(builders ...*DiscountUsageCreate) *DiscountUsageCreateBulk {
	return &DiscountUsageCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *DiscountUsageClient) MapCreateBulk(slice any, setFunc func(*DiscountUsageCreate, int)) *DiscountUsageCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &DiscountUsageCreateBulk{err: fmt.Errorf("calling to DiscountUsageClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*DiscountUsageCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &DiscountUsageCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for DiscountUsage.
func (c *DiscountUsageClient) Update() *DiscountUsageUpdate {
	mutation := newDiscountUsageMutation(c.config, OpUpdate)
	return &DiscountUsageUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *DiscountUsageClient) UpdateOne(du *DiscountUsage) *DiscountUsageUpdateOne {
	mutation := newDiscountUsageMutation(c.config, OpUpdateOne, withDiscountUsage(du))
	return &DiscountUsageUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *DiscountUsageClient) UpdateOneID(id int) *DiscountUsageUpdateOne {
	mutation := newDiscountUsageMutation(c.config, OpUpdateOne, withDiscountUsageID(id))
	return &DiscountUsageUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for DiscountUsage.
func (c *DiscountUsageClient) Delete() *DiscountUsageDelete {
	mutation := newDiscountUsageMutation(c.config, OpDelete)
	return &DiscountUsageDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *DiscountUsageClient) DeleteOne(du *DiscountUsage) *DiscountUsageDeleteOne {
	return c.DeleteOneID(du.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *DiscountUsageClient) DeleteOneID(id int) *DiscountUsageDeleteOne {
	builder := c.Delete().Where(discountusage.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &DiscountUsageDeleteOne{builder}
}

// Query returns a query builder for DiscountUsage.
func (c *DiscountUsageClient) Query() *DiscountUsageQuery {
	return &DiscountUsageQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeDiscountUsage},
		inters: c.Interceptors(),
	}
}

// Get returns a DiscountUsage entity by its id.
func (c *DiscountUsageClient) Get(ctx context.Context, id int) (*DiscountUsage, error) {
	return c.Query().Where(discountusage.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *DiscountUsageClient) GetX(ctx context.Context, id int) *DiscountUsage {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryDiscount queries the discount edge of a DiscountUsage.
func (c *DiscountUsageClient) QueryDiscount(du *DiscountUsage) *DiscountQuery {
	query := (&DiscountClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := du.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(discountusage.Table, discountusage.FieldID, id),
			sqlgraph.To(discount.Table, discount.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, discountusage.DiscountTable, discountusage.DiscountColumn),
		)
		fromV = sqlgraph.Neighbors(du.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryDeal queries the deal edge of a DiscountUsage.
func (c *DiscountUsageClient) QueryDeal(du *DiscountUsage) *DealQuery {
	query := (&DealClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := du.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(discountusage.Table, discountusage.FieldID, id),
			sqlgraph.To(deal.Table, deal.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, discountusage.DealTable, discountusage.DealColumn),
		)
		fromV = sqlgraph.Neighbors(du.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *DiscountUsageClient) Hooks() []Hook {
	hooks := c.hooks.DiscountUsage
	return append(hooks[:len(hooks):len(hooks)], discountusage.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *DiscountUsageClient) Interceptors() []Interceptor {
	inters := c.inters.DiscountUsage
	return append(inters[:len(inters):len(inters)], discountusage.Interceptors[:]...)
}

func (c *DiscountUsageClient) mutate(ctx context.Context, m *DiscountUsageMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&DiscountUsageCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&DiscountUsageUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&DiscountUsageUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&DiscountUsageDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown DiscountUsage mutation op: %q", m.Op())
	}
}

// EntityHistoryClient is a client for the EntityHistory schema.
type EntityHistoryClient struct {
	config
}

// NewEntityHistoryClient returns a client for the EntityHistory from the given config.
func NewEntityHistoryClient(c config) *EntityHistoryClient {
	return &EntityHistoryClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `entityhistory.Hooks(f(g(h())))`.
func (c *EntityHistoryClient) Use(hooks ...Hook) {
	c.hooks.EntityHistory = append(c.hooks.EntityHistory, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `entityhistory.Intercept(f(g(h())))`.
func (c *EntityHistoryClient) Intercept(interceptors ...Interceptor) {
	c.inters.EntityHistory = append(c.inters.EntityHistory, interceptors...)
}

// Create returns a builder for creating a EntityHistory entity.
func (c *EntityHistoryClient) Create() *EntityHistoryCreate {
	mutation := newEntityHistoryMutation(c.config, OpCreate)
	return &EntityHistoryCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of EntityHistory entities.
func (c *EntityHistoryClient) CreateBulk(builders ...*EntityHistoryCreate) *EntityHistoryCreateBulk {
	return &EntityHistoryCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *EntityHistoryClient) MapCreateBulk(slice any, setFunc func(*EntityHistoryCreate, int)) *EntityHistoryCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &EntityHistoryCreateBulk{err: fmt.Errorf("calling to EntityHistoryClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*EntityHistoryCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &EntityHistoryCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for EntityHistory.
func (c *EntityHistoryClient) Update() *EntityHistoryUpdate {
	mutation := newEntityHistoryMutation(c.config, OpUpdate)
	return &EntityHistoryUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *EntityHistoryClient) UpdateOne(eh *EntityHistory) *EntityHistoryUpdateOne {
	mutation := newEntityHistoryMutation(c.config, OpUpdateOne, withEntityHistory(eh))
	return &EntityHistoryUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *EntityHistoryClient) UpdateOneID(id int) *EntityHistoryUpdateOne {
	mutation := newEntityHistoryMutation(c.config, OpUpdateOne, withEntityHistoryID(id))
	return &EntityHistoryUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for EntityHistory.
func (c *EntityHistoryClient) Delete() *EntityHistoryDelete {
	mutation := newEntityHistoryMutation(c.config, OpDelete)
	return &EntityHistoryDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *EntityHistoryClient) DeleteOne(eh *EntityHistory) *EntityHistoryDeleteOne {
	return c.DeleteOneID(eh.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *EntityHistoryClient) DeleteOneID(id int) *EntityHistoryDeleteOne {
	builder := c.Delete().Where(entityhistory.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &EntityHistoryDeleteOne{builder}
}

// Query returns a query builder for EntityHistory.
func (c *EntityHistoryClient) Query() *EntityHistoryQuery {
	return &EntityHistoryQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeEntityHistory},
		inters: c.Interceptors(),
	}
}

// Get returns a EntityHistory entity by its id.
func (c *EntityHistoryClient) Get(ctx context.Context, id int) (*EntityHistory, error) {
	return c.Query().Where(entityhistory.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *EntityHistoryClient) GetX(ctx context.Context, id int) *EntityHistory {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *EntityHistoryClient) Hooks() []Hook {
	return c.hooks.EntityHistory
}

// Interceptors returns the client interceptors.
func (c *EntityHistoryClient) Interceptors() []Interceptor {
	return c.inters.EntityHistory
}

func (c *EntityHistoryClient) mutate(ctx context.Context, m *EntityHistoryMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&EntityHistoryCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&EntityHistoryUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&EntityHistoryUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&EntityHistoryDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown EntityHistory mutation op: %q", m.Op())
	}
}

// ExportJobClient is a client for the ExportJob schema.
type ExportJobClient struct {
	config
}

// NewExportJobClient returns a client for the ExportJob from the given config.
func NewExportJobClient(c config) *ExportJobClient {
	return &ExportJobClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `exportjob.Hooks(f(g(h())))`.
func (c *ExportJobClient) Use(hooks ...Hook) {
	c.hooks.ExportJob = append(c.hooks.ExportJob, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `exportjob.Intercept(f(g(h())))`.
func (c *ExportJobClient) Intercept(interceptors ...Interceptor) {
	c.inters.ExportJob = append(c.inters.ExportJob, interceptors...)
}

// Create returns a builder for creating a ExportJob entity.
func (c *ExportJobClient) Create() *ExportJobCreate {
	mutation := newExportJobMutation(c.config, OpCreate)
	return &ExportJobCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of ExportJob entities.
func (c *ExportJobClient) CreateBulk(builders ...*ExportJobCreate) *ExportJobCreateBulk {
	return &ExportJobCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *ExportJobClient) MapCreateBulk(slice any, setFunc func(*ExportJobCreate, int)) *ExportJobCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &ExportJobCreateBulk{err: fmt.Errorf("calling to ExportJobClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*ExportJobCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &ExportJobCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for ExportJob.
func (c *ExportJobClient) Update() *ExportJobUpdate {
	mutation := newExportJobMutation(c.config, OpUpdate)
	return &ExportJobUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *ExportJobClient) UpdateOne(ej *ExportJob) *ExportJobUpdateOne {
	mutation := newExportJobMutation(c.config, OpUpdateOne, withExportJob(ej))
	return &ExportJobUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *ExportJobClient) UpdateOneID(id int) *ExportJobUpdateOne {
	mutation := newExportJobMutation(c.config, OpUpdateOne, withExportJobID(id))
	return &ExportJobUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for ExportJob.
func (c *ExportJobClient) Delete() *ExportJobDelete {
	mutation := newExportJobMutation(c.config, OpDelete)
	return &ExportJobDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *ExportJobClient) DeleteOne(ej *ExportJob) *ExportJobDeleteOne {
	return c.DeleteOneID(ej.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *ExportJobClient) DeleteOneID(id int) *ExportJobDeleteOne {
	builder := c.Delete().Where(exportjob.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &ExportJobDeleteOne{builder}
}

// Query returns a query builder for ExportJob.
func (c *ExportJobClient) Query() *ExportJobQuery {
	return &ExportJobQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeExportJob},
		inters: c.Interceptors(),
	}
}

// Get returns a ExportJob entity by its id.
func (c *ExportJobClient) Get(ctx context.Context, id int) (*ExportJob, error) {
	return c.Query().Where(exportjob.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *ExportJobClient) GetX(ctx context.Context, id int) *ExportJob {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryCreator queries the creator edge of a ExportJob.
func (c *ExportJobClient) QueryCreator(ej *ExportJob) *UserQuery {
	query := (&UserClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := ej.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(exportjob.Table, exportjob.FieldID, id),
			sqlgraph.To(user.Table, user.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, exportjob.CreatorTable, exportjob.CreatorColumn),
		)
		fromV = sqlgraph.Neighbors(ej.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *ExportJobClient) Hooks() []Hook {
	hooks := c.hooks.ExportJob
	return append(hooks[:len(hooks):len(hooks)], exportjob.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *ExportJobClient) Interceptors() []Interceptor {
	inters := c.inters.ExportJob
	return append(inters[:len(inters):len(inters)], exportjob.Interceptors[:]...)
}

func (c *ExportJobClient) mutate(ctx context.Context, m *ExportJobMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&ExportJobCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&ExportJobUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&ExportJobUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&ExportJobDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown ExportJob mutation op: %q", m.Op())
	}
}

// FileClient is a client for the File schema.
type FileClient struct {
	config
}

// NewFileClient returns a client for the File from the given config.
func NewFileClient(c config) *FileClient {
	return &FileClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `file.Hooks(f(g(h())))`.
func (c *FileClient) Use(hooks ...Hook) {
	c.hooks.File = append(c.hooks.File, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `file.Intercept(f(g(h())))`.
func (c *FileClient) Intercept(interceptors ...Interceptor) {
	c.inters.File = append(c.inters.File, interceptors...)
}

// Create returns a builder for creating a File entity.
func (c *FileClient) Create() *FileCreate {
	mutation := newFileMutation(c.config, OpCreate)
	return &FileCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of File entities.
func (c *FileClient) CreateBulk(builders ...*FileCreate) *FileCreateBulk {
	return &FileCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *FileClient) MapCreateBulk(slice any, setFunc func(*FileCreate, int)) *FileCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &FileCreateBulk{err: fmt.Errorf("calling to FileClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*FileCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &FileCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for File.
func (c *FileClient) Update() *FileUpdate {
	mutation := newFileMutation(c.config, OpUpdate)
	return &FileUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *FileClient) UpdateOne(f *File) *FileUpdateOne {
	mutation := newFileMutation(c.config, OpUpdateOne, withFile(f))
	return &FileUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *FileClient) UpdateOneID(id int) *FileUpdateOne {
	mutation := newFileMutation(c.config, OpUpdateOne, withFileID(id))
	return &FileUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for File.
func (c *FileClient) Delete() *FileDelete {
	mutation := newFileMutation(c.config, OpDelete)
	return &FileDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *FileClient) DeleteOne(f *File) *FileDeleteOne {
	return c.DeleteOneID(f.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *FileClient) DeleteOneID(id int) *FileDeleteOne {
	builder := c.Delete().Where(file.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &FileDeleteOne{builder}
}

// Query returns a query builder for File.
func (c *FileClient) Query() *FileQuery {
	return &FileQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeFile},
		inters: c.Interceptors(),
	}
}

// Get returns a File entity by its id.
func (c *FileClient) Get(ctx context.Context, id int) (*File, error) {
	return c.Query().Where(file.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *FileClient) GetX(ctx context.Context, id int) *File {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryFileUsages queries the file_usages edge of a File.
func (c *FileClient) QueryFileUsages(f *File) *FileUsageQuery {
	query := (&FileUsageClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := f.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(file.Table, file.FieldID, id),
			sqlgraph.To(fileusage.Table, fileusage.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, file.FileUsagesTable, file.FileUsagesColumn),
		)
		fromV = sqlgraph.Neighbors(f.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *FileClient) Hooks() []Hook {
	hooks := c.hooks.File
	return append(hooks[:len(hooks):len(hooks)], file.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *FileClient) Interceptors() []Interceptor {
	inters := c.inters.File
	return append(inters[:len(inters):len(inters)], file.Interceptors[:]...)
}

func (c *FileClient) mutate(ctx context.Context, m *FileMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&FileCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&FileUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&FileUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&FileDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown File mutation op: %q", m.Op())
	}
}

// FileUsageClient is a client for the FileUsage schema.
type FileUsageClient struct {
	config
}

// NewFileUsageClient returns a client for the FileUsage from the given config.
func NewFileUsageClient(c config) *FileUsageClient {
	return &FileUsageClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `fileusage.Hooks(f(g(h())))`.
func (c *FileUsageClient) Use(hooks ...Hook) {
	c.hooks.FileUsage = append(c.hooks.FileUsage, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `fileusage.Intercept(f(g(h())))`.
func (c *FileUsageClient) Intercept(interceptors ...Interceptor) {
	c.inters.FileUsage = append(c.inters.FileUsage, interceptors...)
}

// Create returns a builder for creating a FileUsage entity.
func (c *FileUsageClient) Create() *FileUsageCreate {
	mutation := newFileUsageMutation(c.config, OpCreate)
	return &FileUsageCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of FileUsage entities.
func (c *FileUsageClient) CreateBulk(builders ...*FileUsageCreate) *FileUsageCreateBulk {
	return &FileUsageCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *FileUsageClient) MapCreateBulk(slice any, setFunc func(*FileUsageCreate, int)) *FileUsageCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &FileUsageCreateBulk{err: fmt.Errorf("calling to FileUsageClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*FileUsageCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &FileUsageCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for FileUsage.
func (c *FileUsageClient) Update() *FileUsageUpdate {
	mutation := newFileUsageMutation(c.config, OpUpdate)
	return &FileUsageUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *FileUsageClient) UpdateOne(fu *FileUsage) *FileUsageUpdateOne {
	mutation := newFileUsageMutation(c.config, OpUpdateOne, withFileUsage(fu))
	return &FileUsageUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *FileUsageClient) UpdateOneID(id int) *FileUsageUpdateOne {
	mutation := newFileUsageMutation(c.config, OpUpdateOne, withFileUsageID(id))
	return &FileUsageUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for FileUsage.
func (c *FileUsageClient) Delete() *FileUsageDelete {
	mutation := newFileUsageMutation(c.config, OpDelete)
	return &FileUsageDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *FileUsageClient) DeleteOne(fu *FileUsage) *FileUsageDeleteOne {
	return c.DeleteOneID(fu.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *FileUsageClient) DeleteOneID(id int) *FileUsageDeleteOne {
	builder := c.Delete().Where(fileusage.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &FileUsageDeleteOne{builder}
}

// Query returns a query builder for FileUsage.
func (c *FileUsageClient) Query() *FileUsageQuery {
	return &FileUsageQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeFileUsage},
		inters: c.Interceptors(),
	}
}

// Get returns a FileUsage entity by its id.
func (c *FileUsageClient) Get(ctx context.Context, id int) (*FileUsage, error) {
	return c.Query().Where(fileusage.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *FileUsageClient) GetX(ctx context.Context, id int) *FileUsage {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryFile queries the file edge of a FileUsage.
func (c *FileUsageClient) QueryFile(fu *FileUsage) *FileQuery {
	query := (&FileClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := fu.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(fileusage.Table, fileusage.FieldID, id),
			sqlgraph.To(file.Table, file.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, fileusage.FileTable, fileusage.FileColumn),
		)
		fromV = sqlgraph.Neighbors(fu.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryTrack queries the track edge of a FileUsage.
func (c *FileUsageClient) QueryTrack(fu *FileUsage) *TrackQuery {
	query := (&TrackClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := fu.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(fileusage.Table, fileusage.FieldID, id),
			sqlgraph.To(track.Table, track.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, fileusage.TrackTable, fileusage.TrackColumn),
		)
		fromV = sqlgraph.Neighbors(fu.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *FileUsageClient) Hooks() []Hook {
	hooks := c.hooks.FileUsage
	return append(hooks[:len(hooks):len(hooks)], fileusage.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *FileUsageClient) Interceptors() []Interceptor {
	inters := c.inters.FileUsage
	return append(inters[:len(inters):len(inters)], fileusage.Interceptors[:]...)
}

func (c *FileUsageClient) mutate(ctx context.Context, m *FileUsageMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&FileUsageCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&FileUsageUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&FileUsageUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&FileUsageDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown FileUsage mutation op: %q", m.Op())
	}
}

// FormSubmissionClient is a client for the FormSubmission schema.
type FormSubmissionClient struct {
	config
}

// NewFormSubmissionClient returns a client for the FormSubmission from the given config.
func NewFormSubmissionClient(c config) *FormSubmissionClient {
	return &FormSubmissionClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `formsubmission.Hooks(f(g(h())))`.
func (c *FormSubmissionClient) Use(hooks ...Hook) {
	c.hooks.FormSubmission = append(c.hooks.FormSubmission, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `formsubmission.Intercept(f(g(h())))`.
func (c *FormSubmissionClient) Intercept(interceptors ...Interceptor) {
	c.inters.FormSubmission = append(c.inters.FormSubmission, interceptors...)
}

// Create returns a builder for creating a FormSubmission entity.
func (c *FormSubmissionClient) Create() *FormSubmissionCreate {
	mutation := newFormSubmissionMutation(c.config, OpCreate)
	return &FormSubmissionCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of FormSubmission entities.
func (c *FormSubmissionClient) CreateBulk(builders ...*FormSubmissionCreate) *FormSubmissionCreateBulk {
	return &FormSubmissionCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *FormSubmissionClient) MapCreateBulk(slice any, setFunc func(*FormSubmissionCreate, int)) *FormSubmissionCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &FormSubmissionCreateBulk{err: fmt.Errorf("calling to FormSubmissionClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*FormSubmissionCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &FormSubmissionCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for FormSubmission.
func (c *FormSubmissionClient) Update() *FormSubmissionUpdate {
	mutation := newFormSubmissionMutation(c.config, OpUpdate)
	return &FormSubmissionUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *FormSubmissionClient) UpdateOne(fs *FormSubmission) *FormSubmissionUpdateOne {
	mutation := newFormSubmissionMutation(c.config, OpUpdateOne, withFormSubmission(fs))
	return &FormSubmissionUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *FormSubmissionClient) UpdateOneID(id int) *FormSubmissionUpdateOne {
	mutation := newFormSubmissionMutation(c.config, OpUpdateOne, withFormSubmissionID(id))
	return &FormSubmissionUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for FormSubmission.
func (c *FormSubmissionClient) Delete() *FormSubmissionDelete {
	mutation := newFormSubmissionMutation(c.config, OpDelete)
	return &FormSubmissionDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *FormSubmissionClient) DeleteOne(fs *FormSubmission) *FormSubmissionDeleteOne {
	return c.DeleteOneID(fs.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *FormSubmissionClient) DeleteOneID(id int) *FormSubmissionDeleteOne {
	builder := c.Delete().Where(formsubmission.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &FormSubmissionDeleteOne{builder}
}

// Query returns a query builder for FormSubmission.
func (c *FormSubmissionClient) Query() *FormSubmissionQuery {
	return &FormSubmissionQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeFormSubmission},
		inters: c.Interceptors(),
	}
}

// Get returns a FormSubmission entity by its id.
func (c *FormSubmissionClient) Get(ctx context.Context, id int) (*FormSubmission, error) {
	return c.Query().Where(formsubmission.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *FormSubmissionClient) GetX(ctx context.Context, id int) *FormSubmission {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryPerson queries the person edge of a FormSubmission.
func (c *FormSubmissionClient) QueryPerson(fs *FormSubmission) *PersonQuery {
	query := (&PersonClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := fs.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(formsubmission.Table, formsubmission.FieldID, id),
			sqlgraph.To(person.Table, person.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, formsubmission.PersonTable, formsubmission.PersonColumn),
		)
		fromV = sqlgraph.Neighbors(fs.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *FormSubmissionClient) Hooks() []Hook {
	hooks := c.hooks.FormSubmission
	return append(hooks[:len(hooks):len(hooks)], formsubmission.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *FormSubmissionClient) Interceptors() []Interceptor {
	inters := c.inters.FormSubmission
	return append(inters[:len(inters):len(inters)], formsubmission.Interceptors[:]...)
}

func (c *FormSubmissionClient) mutate(ctx context.Context, m *FormSubmissionMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&FormSubmissionCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&FormSubmissionUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&FormSubmissionUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&FormSubmissionDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown FormSubmission mutation op: %q", m.Op())
	}
}

// InstallmentClient is a client for the Installment schema.
type InstallmentClient struct {
	config
}

// NewInstallmentClient returns a client for the Installment from the given config.
func NewInstallmentClient(c config) *InstallmentClient {
	return &InstallmentClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `installment.Hooks(f(g(h())))`.
func (c *InstallmentClient) Use(hooks ...Hook) {
	c.hooks.Installment = append(c.hooks.Installment, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `installment.Intercept(f(g(h())))`.
func (c *InstallmentClient) Intercept(interceptors ...Interceptor) {
	c.inters.Installment = append(c.inters.Installment, interceptors...)
}

// Create returns a builder for creating a Installment entity.
func (c *InstallmentClient) Create() *InstallmentCreate {
	mutation := newInstallmentMutation(c.config, OpCreate)
	return &InstallmentCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Installment entities.
func (c *InstallmentClient) CreateBulk(builders ...*InstallmentCreate) *InstallmentCreateBulk {
	return &InstallmentCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *InstallmentClient) MapCreateBulk(slice any, setFunc func(*InstallmentCreate, int)) *InstallmentCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &InstallmentCreateBulk{err: fmt.Errorf("calling to InstallmentClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*InstallmentCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &InstallmentCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Installment.
func (c *InstallmentClient) Update() *InstallmentUpdate {
	mutation := newInstallmentMutation(c.config, OpUpdate)
	return &InstallmentUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *InstallmentClient) UpdateOne(i *Installment) *InstallmentUpdateOne {
	mutation := newInstallmentMutation(c.config, OpUpdateOne, withInstallment(i))
	return &InstallmentUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *InstallmentClient) UpdateOneID(id int) *InstallmentUpdateOne {
	mutation := newInstallmentMutation(c.config, OpUpdateOne, withInstallmentID(id))
	return &InstallmentUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Installment.
func (c *InstallmentClient) Delete() *InstallmentDelete {
	mutation := newInstallmentMutation(c.config, OpDelete)
	return &InstallmentDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *InstallmentClient) DeleteOne(i *Installment) *InstallmentDeleteOne {
	return c.DeleteOneID(i.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *InstallmentClient) DeleteOneID(id int) *InstallmentDeleteOne {
	builder := c.Delete().Where(installment.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &InstallmentDeleteOne{builder}
}

// Query returns a query builder for Installment.
func (c *InstallmentClient) Query() *InstallmentQuery {
	return &InstallmentQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeInstallment},
		inters: c.Interceptors(),
	}
}

// Get returns a Installment entity by its id.
func (c *InstallmentClient) Get(ctx context.Context, id int) (*Installment, error) {
	return c.Query().Where(installment.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *InstallmentClient) GetX(ctx context.Context, id int) *Installment {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryPlan queries the plan edge of a Installment.
func (c *InstallmentClient) QueryPlan(i *Installment) *InstallmentPlanQuery {
	query := (&InstallmentPlanClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := i.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(installment.Table, installment.FieldID, id),
			sqlgraph.To(installmentplan.Table, installmentplan.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, installment.PlanTable, installment.PlanColumn),
		)
		fromV = sqlgraph.Neighbors(i.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryCreator queries the creator edge of a Installment.
func (c *InstallmentClient) QueryCreator(i *Installment) *UserQuery {
	query := (&UserClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := i.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(installment.Table, installment.FieldID, id),
			sqlgraph.To(user.Table, user.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, installment.CreatorTable, installment.CreatorColumn),
		)
		fromV = sqlgraph.Neighbors(i.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryAllocations queries the allocations edge of a Installment.
func (c *InstallmentClient) QueryAllocations(i *Installment) *PaymentAllocationQuery {
	query := (&PaymentAllocationClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := i.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(installment.Table, installment.FieldID, id),
			sqlgraph.To(paymentallocation.Table, paymentallocation.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, installment.AllocationsTable, installment.AllocationsColumn),
		)
		fromV = sqlgraph.Neighbors(i.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *InstallmentClient) Hooks() []Hook {
	hooks := c.hooks.Installment
	return append(hooks[:len(hooks):len(hooks)], installment.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *InstallmentClient) Interceptors() []Interceptor {
	inters := c.inters.Installment
	return append(inters[:len(inters):len(inters)], installment.Interceptors[:]...)
}

func (c *InstallmentClient) mutate(ctx context.Context, m *InstallmentMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&InstallmentCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&InstallmentUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&InstallmentUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&InstallmentDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Installment mutation op: %q", m.Op())
	}
}

// InstallmentPlanClient is a client for the InstallmentPlan schema.
type InstallmentPlanClient struct {
	config
}

// NewInstallmentPlanClient returns a client for the InstallmentPlan from the given config.
func NewInstallmentPlanClient(c config) *InstallmentPlanClient {
	return &InstallmentPlanClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `installmentplan.Hooks(f(g(h())))`.
func (c *InstallmentPlanClient) Use(hooks ...Hook) {
	c.hooks.InstallmentPlan = append(c.hooks.InstallmentPlan, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `installmentplan.Intercept(f(g(h())))`.
func (c *InstallmentPlanClient) Intercept(interceptors ...Interceptor) {
	c.inters.InstallmentPlan = append(c.inters.InstallmentPlan, interceptors...)
}

// Create returns a builder for creating a InstallmentPlan entity.
func (c *InstallmentPlanClient) Create() *InstallmentPlanCreate {
	mutation := newInstallmentPlanMutation(c.config, OpCreate)
	return &InstallmentPlanCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of InstallmentPlan entities.
func (c *InstallmentPlanClient) CreateBulk(builders ...*InstallmentPlanCreate) *InstallmentPlanCreateBulk {
	return &InstallmentPlanCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *InstallmentPlanClient) MapCreateBulk(slice any, setFunc func(*InstallmentPlanCreate, int)) *InstallmentPlanCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &InstallmentPlanCreateBulk{err: fmt.Errorf("calling to InstallmentPlanClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*InstallmentPlanCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &InstallmentPlanCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for InstallmentPlan.
func (c *InstallmentPlanClient) Update() *InstallmentPlanUpdate {
	mutation := newInstallmentPlanMutation(c.config, OpUpdate)
	return &InstallmentPlanUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *InstallmentPlanClient) UpdateOne(ip *InstallmentPlan) *InstallmentPlanUpdateOne {
	mutation := newInstallmentPlanMutation(c.config, OpUpdateOne, withInstallmentPlan(ip))
	return &InstallmentPlanUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *InstallmentPlanClient) UpdateOneID(id int) *InstallmentPlanUpdateOne {
	mutation := newInstallmentPlanMutation(c.config, OpUpdateOne, withInstallmentPlanID(id))
	return &InstallmentPlanUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for InstallmentPlan.
func (c *InstallmentPlanClient) Delete() *InstallmentPlanDelete {
	mutation := newInstallmentPlanMutation(c.config, OpDelete)
	return &InstallmentPlanDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *InstallmentPlanClient) DeleteOne(ip *InstallmentPlan) *InstallmentPlanDeleteOne {
	return c.DeleteOneID(ip.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *InstallmentPlanClient) DeleteOneID(id int) *InstallmentPlanDeleteOne {
	builder := c.Delete().Where(installmentplan.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &InstallmentPlanDeleteOne{builder}
}

// Query returns a query builder for InstallmentPlan.
func (c *InstallmentPlanClient) Query() *InstallmentPlanQuery {
	return &InstallmentPlanQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeInstallmentPlan},
		inters: c.Interceptors(),
	}
}

// Get returns a InstallmentPlan entity by its id.
func (c *InstallmentPlanClient) Get(ctx context.Context, id int) (*InstallmentPlan, error) {
	return c.Query().Where(installmentplan.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *InstallmentPlanClient) GetX(ctx context.Context, id int) *InstallmentPlan {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryPerson queries the person edge of a InstallmentPlan.
func (c *InstallmentPlanClient) QueryPerson(ip *InstallmentPlan) *PersonQuery {
	query := (&PersonClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := ip.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(installmentplan.Table, installmentplan.FieldID, id),
			sqlgraph.To(person.Table, person.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, installmentplan.PersonTable, installmentplan.PersonColumn),
		)
		fromV = sqlgraph.Neighbors(ip.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryDeal queries the deal edge of a InstallmentPlan.
func (c *InstallmentPlanClient) QueryDeal(ip *InstallmentPlan) *DealQuery {
	query := (&DealClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := ip.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(installmentplan.Table, installmentplan.FieldID, id),
			sqlgraph.To(deal.Table, deal.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, installmentplan.DealTable, installmentplan.DealColumn),
		)
		fromV = sqlgraph.Neighbors(ip.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryCreator queries the creator edge of a InstallmentPlan.
func (c *InstallmentPlanClient) QueryCreator(ip *InstallmentPlan) *UserQuery {
	query := (&UserClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := ip.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(installmentplan.Table, installmentplan.FieldID, id),
			sqlgraph.To(user.Table, user.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, installmentplan.CreatorTable, installmentplan.CreatorColumn),
		)
		fromV = sqlgraph.Neighbors(ip.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryAttachments queries the attachments edge of a InstallmentPlan.
func (c *InstallmentPlanClient) QueryAttachments(ip *InstallmentPlan) *AttachmentQuery {
	query := (&AttachmentClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := ip.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(installmentplan.Table, installmentplan.FieldID, id),
			sqlgraph.To(attachment.Table, attachment.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, installmentplan.AttachmentsTable, installmentplan.AttachmentsColumn),
		)
		fromV = sqlgraph.Neighbors(ip.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryInstallments queries the installments edge of a InstallmentPlan.
func (c *InstallmentPlanClient) QueryInstallments(ip *InstallmentPlan) *InstallmentQuery {
	query := (&InstallmentClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := ip.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(installmentplan.Table, installmentplan.FieldID, id),
			sqlgraph.To(installment.Table, installment.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, installmentplan.InstallmentsTable, installmentplan.InstallmentsColumn),
		)
		fromV = sqlgraph.Neighbors(ip.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryBill queries the bill edge of a InstallmentPlan.
func (c *InstallmentPlanClient) QueryBill(ip *InstallmentPlan) *BillQuery {
	query := (&BillClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := ip.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(installmentplan.Table, installmentplan.FieldID, id),
			sqlgraph.To(bill.Table, bill.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, installmentplan.BillTable, installmentplan.BillColumn),
		)
		fromV = sqlgraph.Neighbors(ip.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *InstallmentPlanClient) Hooks() []Hook {
	hooks := c.hooks.InstallmentPlan
	return append(hooks[:len(hooks):len(hooks)], installmentplan.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *InstallmentPlanClient) Interceptors() []Interceptor {
	inters := c.inters.InstallmentPlan
	return append(inters[:len(inters):len(inters)], installmentplan.Interceptors[:]...)
}

func (c *InstallmentPlanClient) mutate(ctx context.Context, m *InstallmentPlanMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&InstallmentPlanCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&InstallmentPlanUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&InstallmentPlanUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&InstallmentPlanDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown InstallmentPlan mutation op: %q", m.Op())
	}
}

// IssueClient is a client for the Issue schema.
type IssueClient struct {
	config
}

// NewIssueClient returns a client for the Issue from the given config.
func NewIssueClient(c config) *IssueClient {
	return &IssueClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `issue.Hooks(f(g(h())))`.
func (c *IssueClient) Use(hooks ...Hook) {
	c.hooks.Issue = append(c.hooks.Issue, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `issue.Intercept(f(g(h())))`.
func (c *IssueClient) Intercept(interceptors ...Interceptor) {
	c.inters.Issue = append(c.inters.Issue, interceptors...)
}

// Create returns a builder for creating a Issue entity.
func (c *IssueClient) Create() *IssueCreate {
	mutation := newIssueMutation(c.config, OpCreate)
	return &IssueCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Issue entities.
func (c *IssueClient) CreateBulk(builders ...*IssueCreate) *IssueCreateBulk {
	return &IssueCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *IssueClient) MapCreateBulk(slice any, setFunc func(*IssueCreate, int)) *IssueCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &IssueCreateBulk{err: fmt.Errorf("calling to IssueClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*IssueCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &IssueCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Issue.
func (c *IssueClient) Update() *IssueUpdate {
	mutation := newIssueMutation(c.config, OpUpdate)
	return &IssueUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *IssueClient) UpdateOne(i *Issue) *IssueUpdateOne {
	mutation := newIssueMutation(c.config, OpUpdateOne, withIssue(i))
	return &IssueUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *IssueClient) UpdateOneID(id int) *IssueUpdateOne {
	mutation := newIssueMutation(c.config, OpUpdateOne, withIssueID(id))
	return &IssueUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Issue.
func (c *IssueClient) Delete() *IssueDelete {
	mutation := newIssueMutation(c.config, OpDelete)
	return &IssueDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *IssueClient) DeleteOne(i *Issue) *IssueDeleteOne {
	return c.DeleteOneID(i.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *IssueClient) DeleteOneID(id int) *IssueDeleteOne {
	builder := c.Delete().Where(issue.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &IssueDeleteOne{builder}
}

// Query returns a query builder for Issue.
func (c *IssueClient) Query() *IssueQuery {
	return &IssueQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeIssue},
		inters: c.Interceptors(),
	}
}

// Get returns a Issue entity by its id.
func (c *IssueClient) Get(ctx context.Context, id int) (*Issue, error) {
	return c.Query().Where(issue.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *IssueClient) GetX(ctx context.Context, id int) *Issue {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryComments queries the comments edge of a Issue.
func (c *IssueClient) QueryComments(i *Issue) *IssueCommentQuery {
	query := (&IssueCommentClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := i.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(issue.Table, issue.FieldID, id),
			sqlgraph.To(issuecomment.Table, issuecomment.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, issue.CommentsTable, issue.CommentsColumn),
		)
		fromV = sqlgraph.Neighbors(i.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryPerson queries the person edge of a Issue.
func (c *IssueClient) QueryPerson(i *Issue) *PersonQuery {
	query := (&PersonClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := i.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(issue.Table, issue.FieldID, id),
			sqlgraph.To(person.Table, person.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, issue.PersonTable, issue.PersonColumn),
		)
		fromV = sqlgraph.Neighbors(i.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *IssueClient) Hooks() []Hook {
	hooks := c.hooks.Issue
	return append(hooks[:len(hooks):len(hooks)], issue.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *IssueClient) Interceptors() []Interceptor {
	inters := c.inters.Issue
	return append(inters[:len(inters):len(inters)], issue.Interceptors[:]...)
}

func (c *IssueClient) mutate(ctx context.Context, m *IssueMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&IssueCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&IssueUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&IssueUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&IssueDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Issue mutation op: %q", m.Op())
	}
}

// IssueCommentClient is a client for the IssueComment schema.
type IssueCommentClient struct {
	config
}

// NewIssueCommentClient returns a client for the IssueComment from the given config.
func NewIssueCommentClient(c config) *IssueCommentClient {
	return &IssueCommentClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `issuecomment.Hooks(f(g(h())))`.
func (c *IssueCommentClient) Use(hooks ...Hook) {
	c.hooks.IssueComment = append(c.hooks.IssueComment, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `issuecomment.Intercept(f(g(h())))`.
func (c *IssueCommentClient) Intercept(interceptors ...Interceptor) {
	c.inters.IssueComment = append(c.inters.IssueComment, interceptors...)
}

// Create returns a builder for creating a IssueComment entity.
func (c *IssueCommentClient) Create() *IssueCommentCreate {
	mutation := newIssueCommentMutation(c.config, OpCreate)
	return &IssueCommentCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of IssueComment entities.
func (c *IssueCommentClient) CreateBulk(builders ...*IssueCommentCreate) *IssueCommentCreateBulk {
	return &IssueCommentCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *IssueCommentClient) MapCreateBulk(slice any, setFunc func(*IssueCommentCreate, int)) *IssueCommentCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &IssueCommentCreateBulk{err: fmt.Errorf("calling to IssueCommentClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*IssueCommentCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &IssueCommentCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for IssueComment.
func (c *IssueCommentClient) Update() *IssueCommentUpdate {
	mutation := newIssueCommentMutation(c.config, OpUpdate)
	return &IssueCommentUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *IssueCommentClient) UpdateOne(ic *IssueComment) *IssueCommentUpdateOne {
	mutation := newIssueCommentMutation(c.config, OpUpdateOne, withIssueComment(ic))
	return &IssueCommentUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *IssueCommentClient) UpdateOneID(id int) *IssueCommentUpdateOne {
	mutation := newIssueCommentMutation(c.config, OpUpdateOne, withIssueCommentID(id))
	return &IssueCommentUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for IssueComment.
func (c *IssueCommentClient) Delete() *IssueCommentDelete {
	mutation := newIssueCommentMutation(c.config, OpDelete)
	return &IssueCommentDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *IssueCommentClient) DeleteOne(ic *IssueComment) *IssueCommentDeleteOne {
	return c.DeleteOneID(ic.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *IssueCommentClient) DeleteOneID(id int) *IssueCommentDeleteOne {
	builder := c.Delete().Where(issuecomment.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &IssueCommentDeleteOne{builder}
}

// Query returns a query builder for IssueComment.
func (c *IssueCommentClient) Query() *IssueCommentQuery {
	return &IssueCommentQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeIssueComment},
		inters: c.Interceptors(),
	}
}

// Get returns a IssueComment entity by its id.
func (c *IssueCommentClient) Get(ctx context.Context, id int) (*IssueComment, error) {
	return c.Query().Where(issuecomment.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *IssueCommentClient) GetX(ctx context.Context, id int) *IssueComment {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryCreator queries the creator edge of a IssueComment.
func (c *IssueCommentClient) QueryCreator(ic *IssueComment) *UserQuery {
	query := (&UserClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := ic.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(issuecomment.Table, issuecomment.FieldID, id),
			sqlgraph.To(user.Table, user.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, issuecomment.CreatorTable, issuecomment.CreatorColumn),
		)
		fromV = sqlgraph.Neighbors(ic.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryIssue queries the issue edge of a IssueComment.
func (c *IssueCommentClient) QueryIssue(ic *IssueComment) *IssueQuery {
	query := (&IssueClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := ic.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(issuecomment.Table, issuecomment.FieldID, id),
			sqlgraph.To(issue.Table, issue.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, issuecomment.IssueTable, issuecomment.IssueColumn),
		)
		fromV = sqlgraph.Neighbors(ic.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *IssueCommentClient) Hooks() []Hook {
	hooks := c.hooks.IssueComment
	return append(hooks[:len(hooks):len(hooks)], issuecomment.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *IssueCommentClient) Interceptors() []Interceptor {
	inters := c.inters.IssueComment
	return append(inters[:len(inters):len(inters)], issuecomment.Interceptors[:]...)
}

func (c *IssueCommentClient) mutate(ctx context.Context, m *IssueCommentMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&IssueCommentCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&IssueCommentUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&IssueCommentUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&IssueCommentDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown IssueComment mutation op: %q", m.Op())
	}
}

// LocalDistrictClient is a client for the LocalDistrict schema.
type LocalDistrictClient struct {
	config
}

// NewLocalDistrictClient returns a client for the LocalDistrict from the given config.
func NewLocalDistrictClient(c config) *LocalDistrictClient {
	return &LocalDistrictClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `localdistrict.Hooks(f(g(h())))`.
func (c *LocalDistrictClient) Use(hooks ...Hook) {
	c.hooks.LocalDistrict = append(c.hooks.LocalDistrict, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `localdistrict.Intercept(f(g(h())))`.
func (c *LocalDistrictClient) Intercept(interceptors ...Interceptor) {
	c.inters.LocalDistrict = append(c.inters.LocalDistrict, interceptors...)
}

// Create returns a builder for creating a LocalDistrict entity.
func (c *LocalDistrictClient) Create() *LocalDistrictCreate {
	mutation := newLocalDistrictMutation(c.config, OpCreate)
	return &LocalDistrictCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of LocalDistrict entities.
func (c *LocalDistrictClient) CreateBulk(builders ...*LocalDistrictCreate) *LocalDistrictCreateBulk {
	return &LocalDistrictCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *LocalDistrictClient) MapCreateBulk(slice any, setFunc func(*LocalDistrictCreate, int)) *LocalDistrictCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &LocalDistrictCreateBulk{err: fmt.Errorf("calling to LocalDistrictClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*LocalDistrictCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &LocalDistrictCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for LocalDistrict.
func (c *LocalDistrictClient) Update() *LocalDistrictUpdate {
	mutation := newLocalDistrictMutation(c.config, OpUpdate)
	return &LocalDistrictUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *LocalDistrictClient) UpdateOne(ld *LocalDistrict) *LocalDistrictUpdateOne {
	mutation := newLocalDistrictMutation(c.config, OpUpdateOne, withLocalDistrict(ld))
	return &LocalDistrictUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *LocalDistrictClient) UpdateOneID(id int) *LocalDistrictUpdateOne {
	mutation := newLocalDistrictMutation(c.config, OpUpdateOne, withLocalDistrictID(id))
	return &LocalDistrictUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for LocalDistrict.
func (c *LocalDistrictClient) Delete() *LocalDistrictDelete {
	mutation := newLocalDistrictMutation(c.config, OpDelete)
	return &LocalDistrictDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *LocalDistrictClient) DeleteOne(ld *LocalDistrict) *LocalDistrictDeleteOne {
	return c.DeleteOneID(ld.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *LocalDistrictClient) DeleteOneID(id int) *LocalDistrictDeleteOne {
	builder := c.Delete().Where(localdistrict.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &LocalDistrictDeleteOne{builder}
}

// Query returns a query builder for LocalDistrict.
func (c *LocalDistrictClient) Query() *LocalDistrictQuery {
	return &LocalDistrictQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeLocalDistrict},
		inters: c.Interceptors(),
	}
}

// Get returns a LocalDistrict entity by its id.
func (c *LocalDistrictClient) Get(ctx context.Context, id int) (*LocalDistrict, error) {
	return c.Query().Where(localdistrict.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *LocalDistrictClient) GetX(ctx context.Context, id int) *LocalDistrict {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *LocalDistrictClient) Hooks() []Hook {
	return c.hooks.LocalDistrict
}

// Interceptors returns the client interceptors.
func (c *LocalDistrictClient) Interceptors() []Interceptor {
	return c.inters.LocalDistrict
}

func (c *LocalDistrictClient) mutate(ctx context.Context, m *LocalDistrictMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&LocalDistrictCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&LocalDistrictUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&LocalDistrictUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&LocalDistrictDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown LocalDistrict mutation op: %q", m.Op())
	}
}

// LocalProvinceClient is a client for the LocalProvince schema.
type LocalProvinceClient struct {
	config
}

// NewLocalProvinceClient returns a client for the LocalProvince from the given config.
func NewLocalProvinceClient(c config) *LocalProvinceClient {
	return &LocalProvinceClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `localprovince.Hooks(f(g(h())))`.
func (c *LocalProvinceClient) Use(hooks ...Hook) {
	c.hooks.LocalProvince = append(c.hooks.LocalProvince, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `localprovince.Intercept(f(g(h())))`.
func (c *LocalProvinceClient) Intercept(interceptors ...Interceptor) {
	c.inters.LocalProvince = append(c.inters.LocalProvince, interceptors...)
}

// Create returns a builder for creating a LocalProvince entity.
func (c *LocalProvinceClient) Create() *LocalProvinceCreate {
	mutation := newLocalProvinceMutation(c.config, OpCreate)
	return &LocalProvinceCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of LocalProvince entities.
func (c *LocalProvinceClient) CreateBulk(builders ...*LocalProvinceCreate) *LocalProvinceCreateBulk {
	return &LocalProvinceCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *LocalProvinceClient) MapCreateBulk(slice any, setFunc func(*LocalProvinceCreate, int)) *LocalProvinceCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &LocalProvinceCreateBulk{err: fmt.Errorf("calling to LocalProvinceClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*LocalProvinceCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &LocalProvinceCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for LocalProvince.
func (c *LocalProvinceClient) Update() *LocalProvinceUpdate {
	mutation := newLocalProvinceMutation(c.config, OpUpdate)
	return &LocalProvinceUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *LocalProvinceClient) UpdateOne(lp *LocalProvince) *LocalProvinceUpdateOne {
	mutation := newLocalProvinceMutation(c.config, OpUpdateOne, withLocalProvince(lp))
	return &LocalProvinceUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *LocalProvinceClient) UpdateOneID(id int) *LocalProvinceUpdateOne {
	mutation := newLocalProvinceMutation(c.config, OpUpdateOne, withLocalProvinceID(id))
	return &LocalProvinceUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for LocalProvince.
func (c *LocalProvinceClient) Delete() *LocalProvinceDelete {
	mutation := newLocalProvinceMutation(c.config, OpDelete)
	return &LocalProvinceDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *LocalProvinceClient) DeleteOne(lp *LocalProvince) *LocalProvinceDeleteOne {
	return c.DeleteOneID(lp.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *LocalProvinceClient) DeleteOneID(id int) *LocalProvinceDeleteOne {
	builder := c.Delete().Where(localprovince.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &LocalProvinceDeleteOne{builder}
}

// Query returns a query builder for LocalProvince.
func (c *LocalProvinceClient) Query() *LocalProvinceQuery {
	return &LocalProvinceQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeLocalProvince},
		inters: c.Interceptors(),
	}
}

// Get returns a LocalProvince entity by its id.
func (c *LocalProvinceClient) Get(ctx context.Context, id int) (*LocalProvince, error) {
	return c.Query().Where(localprovince.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *LocalProvinceClient) GetX(ctx context.Context, id int) *LocalProvince {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *LocalProvinceClient) Hooks() []Hook {
	return c.hooks.LocalProvince
}

// Interceptors returns the client interceptors.
func (c *LocalProvinceClient) Interceptors() []Interceptor {
	return c.inters.LocalProvince
}

func (c *LocalProvinceClient) mutate(ctx context.Context, m *LocalProvinceMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&LocalProvinceCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&LocalProvinceUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&LocalProvinceUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&LocalProvinceDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown LocalProvince mutation op: %q", m.Op())
	}
}

// LocalWardClient is a client for the LocalWard schema.
type LocalWardClient struct {
	config
}

// NewLocalWardClient returns a client for the LocalWard from the given config.
func NewLocalWardClient(c config) *LocalWardClient {
	return &LocalWardClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `localward.Hooks(f(g(h())))`.
func (c *LocalWardClient) Use(hooks ...Hook) {
	c.hooks.LocalWard = append(c.hooks.LocalWard, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `localward.Intercept(f(g(h())))`.
func (c *LocalWardClient) Intercept(interceptors ...Interceptor) {
	c.inters.LocalWard = append(c.inters.LocalWard, interceptors...)
}

// Create returns a builder for creating a LocalWard entity.
func (c *LocalWardClient) Create() *LocalWardCreate {
	mutation := newLocalWardMutation(c.config, OpCreate)
	return &LocalWardCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of LocalWard entities.
func (c *LocalWardClient) CreateBulk(builders ...*LocalWardCreate) *LocalWardCreateBulk {
	return &LocalWardCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *LocalWardClient) MapCreateBulk(slice any, setFunc func(*LocalWardCreate, int)) *LocalWardCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &LocalWardCreateBulk{err: fmt.Errorf("calling to LocalWardClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*LocalWardCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &LocalWardCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for LocalWard.
func (c *LocalWardClient) Update() *LocalWardUpdate {
	mutation := newLocalWardMutation(c.config, OpUpdate)
	return &LocalWardUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *LocalWardClient) UpdateOne(lw *LocalWard) *LocalWardUpdateOne {
	mutation := newLocalWardMutation(c.config, OpUpdateOne, withLocalWard(lw))
	return &LocalWardUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *LocalWardClient) UpdateOneID(id int) *LocalWardUpdateOne {
	mutation := newLocalWardMutation(c.config, OpUpdateOne, withLocalWardID(id))
	return &LocalWardUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for LocalWard.
func (c *LocalWardClient) Delete() *LocalWardDelete {
	mutation := newLocalWardMutation(c.config, OpDelete)
	return &LocalWardDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *LocalWardClient) DeleteOne(lw *LocalWard) *LocalWardDeleteOne {
	return c.DeleteOneID(lw.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *LocalWardClient) DeleteOneID(id int) *LocalWardDeleteOne {
	builder := c.Delete().Where(localward.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &LocalWardDeleteOne{builder}
}

// Query returns a query builder for LocalWard.
func (c *LocalWardClient) Query() *LocalWardQuery {
	return &LocalWardQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeLocalWard},
		inters: c.Interceptors(),
	}
}

// Get returns a LocalWard entity by its id.
func (c *LocalWardClient) Get(ctx context.Context, id int) (*LocalWard, error) {
	return c.Query().Where(localward.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *LocalWardClient) GetX(ctx context.Context, id int) *LocalWard {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *LocalWardClient) Hooks() []Hook {
	return c.hooks.LocalWard
}

// Interceptors returns the client interceptors.
func (c *LocalWardClient) Interceptors() []Interceptor {
	return c.inters.LocalWard
}

func (c *LocalWardClient) mutate(ctx context.Context, m *LocalWardMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&LocalWardCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&LocalWardUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&LocalWardUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&LocalWardDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown LocalWard mutation op: %q", m.Op())
	}
}

// MaterialClient is a client for the Material schema.
type MaterialClient struct {
	config
}

// NewMaterialClient returns a client for the Material from the given config.
func NewMaterialClient(c config) *MaterialClient {
	return &MaterialClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `material.Hooks(f(g(h())))`.
func (c *MaterialClient) Use(hooks ...Hook) {
	c.hooks.Material = append(c.hooks.Material, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `material.Intercept(f(g(h())))`.
func (c *MaterialClient) Intercept(interceptors ...Interceptor) {
	c.inters.Material = append(c.inters.Material, interceptors...)
}

// Create returns a builder for creating a Material entity.
func (c *MaterialClient) Create() *MaterialCreate {
	mutation := newMaterialMutation(c.config, OpCreate)
	return &MaterialCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Material entities.
func (c *MaterialClient) CreateBulk(builders ...*MaterialCreate) *MaterialCreateBulk {
	return &MaterialCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *MaterialClient) MapCreateBulk(slice any, setFunc func(*MaterialCreate, int)) *MaterialCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &MaterialCreateBulk{err: fmt.Errorf("calling to MaterialClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*MaterialCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &MaterialCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Material.
func (c *MaterialClient) Update() *MaterialUpdate {
	mutation := newMaterialMutation(c.config, OpUpdate)
	return &MaterialUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *MaterialClient) UpdateOne(m *Material) *MaterialUpdateOne {
	mutation := newMaterialMutation(c.config, OpUpdateOne, withMaterial(m))
	return &MaterialUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *MaterialClient) UpdateOneID(id int) *MaterialUpdateOne {
	mutation := newMaterialMutation(c.config, OpUpdateOne, withMaterialID(id))
	return &MaterialUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Material.
func (c *MaterialClient) Delete() *MaterialDelete {
	mutation := newMaterialMutation(c.config, OpDelete)
	return &MaterialDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *MaterialClient) DeleteOne(m *Material) *MaterialDeleteOne {
	return c.DeleteOneID(m.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *MaterialClient) DeleteOneID(id int) *MaterialDeleteOne {
	builder := c.Delete().Where(material.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &MaterialDeleteOne{builder}
}

// Query returns a query builder for Material.
func (c *MaterialClient) Query() *MaterialQuery {
	return &MaterialQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeMaterial},
		inters: c.Interceptors(),
	}
}

// Get returns a Material entity by its id.
func (c *MaterialClient) Get(ctx context.Context, id int) (*Material, error) {
	return c.Query().Where(material.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *MaterialClient) GetX(ctx context.Context, id int) *Material {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *MaterialClient) Hooks() []Hook {
	hooks := c.hooks.Material
	return append(hooks[:len(hooks):len(hooks)], material.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *MaterialClient) Interceptors() []Interceptor {
	inters := c.inters.Material
	return append(inters[:len(inters):len(inters)], material.Interceptors[:]...)
}

func (c *MaterialClient) mutate(ctx context.Context, m *MaterialMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&MaterialCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&MaterialUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&MaterialUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&MaterialDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Material mutation op: %q", m.Op())
	}
}

// MaterialUsageClient is a client for the MaterialUsage schema.
type MaterialUsageClient struct {
	config
}

// NewMaterialUsageClient returns a client for the MaterialUsage from the given config.
func NewMaterialUsageClient(c config) *MaterialUsageClient {
	return &MaterialUsageClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `materialusage.Hooks(f(g(h())))`.
func (c *MaterialUsageClient) Use(hooks ...Hook) {
	c.hooks.MaterialUsage = append(c.hooks.MaterialUsage, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `materialusage.Intercept(f(g(h())))`.
func (c *MaterialUsageClient) Intercept(interceptors ...Interceptor) {
	c.inters.MaterialUsage = append(c.inters.MaterialUsage, interceptors...)
}

// Create returns a builder for creating a MaterialUsage entity.
func (c *MaterialUsageClient) Create() *MaterialUsageCreate {
	mutation := newMaterialUsageMutation(c.config, OpCreate)
	return &MaterialUsageCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of MaterialUsage entities.
func (c *MaterialUsageClient) CreateBulk(builders ...*MaterialUsageCreate) *MaterialUsageCreateBulk {
	return &MaterialUsageCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *MaterialUsageClient) MapCreateBulk(slice any, setFunc func(*MaterialUsageCreate, int)) *MaterialUsageCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &MaterialUsageCreateBulk{err: fmt.Errorf("calling to MaterialUsageClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*MaterialUsageCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &MaterialUsageCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for MaterialUsage.
func (c *MaterialUsageClient) Update() *MaterialUsageUpdate {
	mutation := newMaterialUsageMutation(c.config, OpUpdate)
	return &MaterialUsageUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *MaterialUsageClient) UpdateOne(mu *MaterialUsage) *MaterialUsageUpdateOne {
	mutation := newMaterialUsageMutation(c.config, OpUpdateOne, withMaterialUsage(mu))
	return &MaterialUsageUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *MaterialUsageClient) UpdateOneID(id int) *MaterialUsageUpdateOne {
	mutation := newMaterialUsageMutation(c.config, OpUpdateOne, withMaterialUsageID(id))
	return &MaterialUsageUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for MaterialUsage.
func (c *MaterialUsageClient) Delete() *MaterialUsageDelete {
	mutation := newMaterialUsageMutation(c.config, OpDelete)
	return &MaterialUsageDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *MaterialUsageClient) DeleteOne(mu *MaterialUsage) *MaterialUsageDeleteOne {
	return c.DeleteOneID(mu.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *MaterialUsageClient) DeleteOneID(id int) *MaterialUsageDeleteOne {
	builder := c.Delete().Where(materialusage.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &MaterialUsageDeleteOne{builder}
}

// Query returns a query builder for MaterialUsage.
func (c *MaterialUsageClient) Query() *MaterialUsageQuery {
	return &MaterialUsageQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeMaterialUsage},
		inters: c.Interceptors(),
	}
}

// Get returns a MaterialUsage entity by its id.
func (c *MaterialUsageClient) Get(ctx context.Context, id int) (*MaterialUsage, error) {
	return c.Query().Where(materialusage.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *MaterialUsageClient) GetX(ctx context.Context, id int) *MaterialUsage {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryAttachment queries the attachment edge of a MaterialUsage.
func (c *MaterialUsageClient) QueryAttachment(mu *MaterialUsage) *AttachmentQuery {
	query := (&AttachmentClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := mu.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(materialusage.Table, materialusage.FieldID, id),
			sqlgraph.To(attachment.Table, attachment.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, materialusage.AttachmentTable, materialusage.AttachmentColumn),
		)
		fromV = sqlgraph.Neighbors(mu.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryMaterial queries the material edge of a MaterialUsage.
func (c *MaterialUsageClient) QueryMaterial(mu *MaterialUsage) *MaterialQuery {
	query := (&MaterialClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := mu.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(materialusage.Table, materialusage.FieldID, id),
			sqlgraph.To(material.Table, material.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, materialusage.MaterialTable, materialusage.MaterialColumn),
		)
		fromV = sqlgraph.Neighbors(mu.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryOperation queries the operation edge of a MaterialUsage.
func (c *MaterialUsageClient) QueryOperation(mu *MaterialUsage) *OperationQuery {
	query := (&OperationClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := mu.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(materialusage.Table, materialusage.FieldID, id),
			sqlgraph.To(operation.Table, operation.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, materialusage.OperationTable, materialusage.OperationColumn),
		)
		fromV = sqlgraph.Neighbors(mu.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryUser queries the user edge of a MaterialUsage.
func (c *MaterialUsageClient) QueryUser(mu *MaterialUsage) *UserQuery {
	query := (&UserClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := mu.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(materialusage.Table, materialusage.FieldID, id),
			sqlgraph.To(user.Table, user.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, materialusage.UserTable, materialusage.UserColumn),
		)
		fromV = sqlgraph.Neighbors(mu.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *MaterialUsageClient) Hooks() []Hook {
	hooks := c.hooks.MaterialUsage
	return append(hooks[:len(hooks):len(hooks)], materialusage.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *MaterialUsageClient) Interceptors() []Interceptor {
	inters := c.inters.MaterialUsage
	return append(inters[:len(inters):len(inters)], materialusage.Interceptors[:]...)
}

func (c *MaterialUsageClient) mutate(ctx context.Context, m *MaterialUsageMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&MaterialUsageCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&MaterialUsageUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&MaterialUsageUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&MaterialUsageDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown MaterialUsage mutation op: %q", m.Op())
	}
}

// MessageHistoryClient is a client for the MessageHistory schema.
type MessageHistoryClient struct {
	config
}

// NewMessageHistoryClient returns a client for the MessageHistory from the given config.
func NewMessageHistoryClient(c config) *MessageHistoryClient {
	return &MessageHistoryClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `messagehistory.Hooks(f(g(h())))`.
func (c *MessageHistoryClient) Use(hooks ...Hook) {
	c.hooks.MessageHistory = append(c.hooks.MessageHistory, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `messagehistory.Intercept(f(g(h())))`.
func (c *MessageHistoryClient) Intercept(interceptors ...Interceptor) {
	c.inters.MessageHistory = append(c.inters.MessageHistory, interceptors...)
}

// Create returns a builder for creating a MessageHistory entity.
func (c *MessageHistoryClient) Create() *MessageHistoryCreate {
	mutation := newMessageHistoryMutation(c.config, OpCreate)
	return &MessageHistoryCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of MessageHistory entities.
func (c *MessageHistoryClient) CreateBulk(builders ...*MessageHistoryCreate) *MessageHistoryCreateBulk {
	return &MessageHistoryCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *MessageHistoryClient) MapCreateBulk(slice any, setFunc func(*MessageHistoryCreate, int)) *MessageHistoryCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &MessageHistoryCreateBulk{err: fmt.Errorf("calling to MessageHistoryClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*MessageHistoryCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &MessageHistoryCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for MessageHistory.
func (c *MessageHistoryClient) Update() *MessageHistoryUpdate {
	mutation := newMessageHistoryMutation(c.config, OpUpdate)
	return &MessageHistoryUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *MessageHistoryClient) UpdateOne(mh *MessageHistory) *MessageHistoryUpdateOne {
	mutation := newMessageHistoryMutation(c.config, OpUpdateOne, withMessageHistory(mh))
	return &MessageHistoryUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *MessageHistoryClient) UpdateOneID(id int) *MessageHistoryUpdateOne {
	mutation := newMessageHistoryMutation(c.config, OpUpdateOne, withMessageHistoryID(id))
	return &MessageHistoryUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for MessageHistory.
func (c *MessageHistoryClient) Delete() *MessageHistoryDelete {
	mutation := newMessageHistoryMutation(c.config, OpDelete)
	return &MessageHistoryDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *MessageHistoryClient) DeleteOne(mh *MessageHistory) *MessageHistoryDeleteOne {
	return c.DeleteOneID(mh.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *MessageHistoryClient) DeleteOneID(id int) *MessageHistoryDeleteOne {
	builder := c.Delete().Where(messagehistory.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &MessageHistoryDeleteOne{builder}
}

// Query returns a query builder for MessageHistory.
func (c *MessageHistoryClient) Query() *MessageHistoryQuery {
	return &MessageHistoryQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeMessageHistory},
		inters: c.Interceptors(),
	}
}

// Get returns a MessageHistory entity by its id.
func (c *MessageHistoryClient) Get(ctx context.Context, id int) (*MessageHistory, error) {
	return c.Query().Where(messagehistory.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *MessageHistoryClient) GetX(ctx context.Context, id int) *MessageHistory {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryPerson queries the person edge of a MessageHistory.
func (c *MessageHistoryClient) QueryPerson(mh *MessageHistory) *PersonQuery {
	query := (&PersonClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := mh.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(messagehistory.Table, messagehistory.FieldID, id),
			sqlgraph.To(person.Table, person.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, messagehistory.PersonTable, messagehistory.PersonColumn),
		)
		fromV = sqlgraph.Neighbors(mh.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryUser queries the user edge of a MessageHistory.
func (c *MessageHistoryClient) QueryUser(mh *MessageHistory) *UserQuery {
	query := (&UserClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := mh.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(messagehistory.Table, messagehistory.FieldID, id),
			sqlgraph.To(user.Table, user.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, messagehistory.UserTable, messagehistory.UserColumn),
		)
		fromV = sqlgraph.Neighbors(mh.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *MessageHistoryClient) Hooks() []Hook {
	hooks := c.hooks.MessageHistory
	return append(hooks[:len(hooks):len(hooks)], messagehistory.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *MessageHistoryClient) Interceptors() []Interceptor {
	inters := c.inters.MessageHistory
	return append(inters[:len(inters):len(inters)], messagehistory.Interceptors[:]...)
}

func (c *MessageHistoryClient) mutate(ctx context.Context, m *MessageHistoryMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&MessageHistoryCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&MessageHistoryUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&MessageHistoryUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&MessageHistoryDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown MessageHistory mutation op: %q", m.Op())
	}
}

// NewTracksReportViewClient is a client for the NewTracksReportView schema.
type NewTracksReportViewClient struct {
	config
}

// NewNewTracksReportViewClient returns a client for the NewTracksReportView from the given config.
func NewNewTracksReportViewClient(c config) *NewTracksReportViewClient {
	return &NewTracksReportViewClient{config: c}
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `newtracksreportview.Intercept(f(g(h())))`.
func (c *NewTracksReportViewClient) Intercept(interceptors ...Interceptor) {
	c.inters.NewTracksReportView = append(c.inters.NewTracksReportView, interceptors...)
}

// Query returns a query builder for NewTracksReportView.
func (c *NewTracksReportViewClient) Query() *NewTracksReportViewQuery {
	return &NewTracksReportViewQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeNewTracksReportView},
		inters: c.Interceptors(),
	}
}

// Interceptors returns the client interceptors.
func (c *NewTracksReportViewClient) Interceptors() []Interceptor {
	return c.inters.NewTracksReportView
}

// NoteClient is a client for the Note schema.
type NoteClient struct {
	config
}

// NewNoteClient returns a client for the Note from the given config.
func NewNoteClient(c config) *NoteClient {
	return &NoteClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `note.Hooks(f(g(h())))`.
func (c *NoteClient) Use(hooks ...Hook) {
	c.hooks.Note = append(c.hooks.Note, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `note.Intercept(f(g(h())))`.
func (c *NoteClient) Intercept(interceptors ...Interceptor) {
	c.inters.Note = append(c.inters.Note, interceptors...)
}

// Create returns a builder for creating a Note entity.
func (c *NoteClient) Create() *NoteCreate {
	mutation := newNoteMutation(c.config, OpCreate)
	return &NoteCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Note entities.
func (c *NoteClient) CreateBulk(builders ...*NoteCreate) *NoteCreateBulk {
	return &NoteCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *NoteClient) MapCreateBulk(slice any, setFunc func(*NoteCreate, int)) *NoteCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &NoteCreateBulk{err: fmt.Errorf("calling to NoteClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*NoteCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &NoteCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Note.
func (c *NoteClient) Update() *NoteUpdate {
	mutation := newNoteMutation(c.config, OpUpdate)
	return &NoteUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *NoteClient) UpdateOne(n *Note) *NoteUpdateOne {
	mutation := newNoteMutation(c.config, OpUpdateOne, withNote(n))
	return &NoteUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *NoteClient) UpdateOneID(id int) *NoteUpdateOne {
	mutation := newNoteMutation(c.config, OpUpdateOne, withNoteID(id))
	return &NoteUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Note.
func (c *NoteClient) Delete() *NoteDelete {
	mutation := newNoteMutation(c.config, OpDelete)
	return &NoteDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *NoteClient) DeleteOne(n *Note) *NoteDeleteOne {
	return c.DeleteOneID(n.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *NoteClient) DeleteOneID(id int) *NoteDeleteOne {
	builder := c.Delete().Where(note.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &NoteDeleteOne{builder}
}

// Query returns a query builder for Note.
func (c *NoteClient) Query() *NoteQuery {
	return &NoteQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeNote},
		inters: c.Interceptors(),
	}
}

// Get returns a Note entity by its id.
func (c *NoteClient) Get(ctx context.Context, id int) (*Note, error) {
	return c.Query().Where(note.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *NoteClient) GetX(ctx context.Context, id int) *Note {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryCreator queries the creator edge of a Note.
func (c *NoteClient) QueryCreator(n *Note) *UserQuery {
	query := (&UserClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := n.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(note.Table, note.FieldID, id),
			sqlgraph.To(user.Table, user.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, note.CreatorTable, note.CreatorColumn),
		)
		fromV = sqlgraph.Neighbors(n.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryPerson queries the person edge of a Note.
func (c *NoteClient) QueryPerson(n *Note) *PersonQuery {
	query := (&PersonClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := n.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(note.Table, note.FieldID, id),
			sqlgraph.To(person.Table, person.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, note.PersonTable, note.PersonColumn),
		)
		fromV = sqlgraph.Neighbors(n.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryDeal queries the deal edge of a Note.
func (c *NoteClient) QueryDeal(n *Note) *DealQuery {
	query := (&DealClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := n.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(note.Table, note.FieldID, id),
			sqlgraph.To(deal.Table, deal.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, note.DealTable, note.DealColumn),
		)
		fromV = sqlgraph.Neighbors(n.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *NoteClient) Hooks() []Hook {
	hooks := c.hooks.Note
	return append(hooks[:len(hooks):len(hooks)], note.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *NoteClient) Interceptors() []Interceptor {
	inters := c.inters.Note
	return append(inters[:len(inters):len(inters)], note.Interceptors[:]...)
}

func (c *NoteClient) mutate(ctx context.Context, m *NoteMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&NoteCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&NoteUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&NoteUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&NoteDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Note mutation op: %q", m.Op())
	}
}

// NotificationClient is a client for the Notification schema.
type NotificationClient struct {
	config
}

// NewNotificationClient returns a client for the Notification from the given config.
func NewNotificationClient(c config) *NotificationClient {
	return &NotificationClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `notification.Hooks(f(g(h())))`.
func (c *NotificationClient) Use(hooks ...Hook) {
	c.hooks.Notification = append(c.hooks.Notification, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `notification.Intercept(f(g(h())))`.
func (c *NotificationClient) Intercept(interceptors ...Interceptor) {
	c.inters.Notification = append(c.inters.Notification, interceptors...)
}

// Create returns a builder for creating a Notification entity.
func (c *NotificationClient) Create() *NotificationCreate {
	mutation := newNotificationMutation(c.config, OpCreate)
	return &NotificationCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Notification entities.
func (c *NotificationClient) CreateBulk(builders ...*NotificationCreate) *NotificationCreateBulk {
	return &NotificationCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *NotificationClient) MapCreateBulk(slice any, setFunc func(*NotificationCreate, int)) *NotificationCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &NotificationCreateBulk{err: fmt.Errorf("calling to NotificationClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*NotificationCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &NotificationCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Notification.
func (c *NotificationClient) Update() *NotificationUpdate {
	mutation := newNotificationMutation(c.config, OpUpdate)
	return &NotificationUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *NotificationClient) UpdateOne(n *Notification) *NotificationUpdateOne {
	mutation := newNotificationMutation(c.config, OpUpdateOne, withNotification(n))
	return &NotificationUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *NotificationClient) UpdateOneID(id int) *NotificationUpdateOne {
	mutation := newNotificationMutation(c.config, OpUpdateOne, withNotificationID(id))
	return &NotificationUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Notification.
func (c *NotificationClient) Delete() *NotificationDelete {
	mutation := newNotificationMutation(c.config, OpDelete)
	return &NotificationDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *NotificationClient) DeleteOne(n *Notification) *NotificationDeleteOne {
	return c.DeleteOneID(n.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *NotificationClient) DeleteOneID(id int) *NotificationDeleteOne {
	builder := c.Delete().Where(notification.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &NotificationDeleteOne{builder}
}

// Query returns a query builder for Notification.
func (c *NotificationClient) Query() *NotificationQuery {
	return &NotificationQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeNotification},
		inters: c.Interceptors(),
	}
}

// Get returns a Notification entity by its id.
func (c *NotificationClient) Get(ctx context.Context, id int) (*Notification, error) {
	return c.Query().Where(notification.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *NotificationClient) GetX(ctx context.Context, id int) *Notification {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryUser queries the user edge of a Notification.
func (c *NotificationClient) QueryUser(n *Notification) *UserQuery {
	query := (&UserClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := n.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(notification.Table, notification.FieldID, id),
			sqlgraph.To(user.Table, user.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, notification.UserTable, notification.UserColumn),
		)
		fromV = sqlgraph.Neighbors(n.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *NotificationClient) Hooks() []Hook {
	hooks := c.hooks.Notification
	return append(hooks[:len(hooks):len(hooks)], notification.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *NotificationClient) Interceptors() []Interceptor {
	inters := c.inters.Notification
	return append(inters[:len(inters):len(inters)], notification.Interceptors[:]...)
}

func (c *NotificationClient) mutate(ctx context.Context, m *NotificationMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&NotificationCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&NotificationUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&NotificationUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&NotificationDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Notification mutation op: %q", m.Op())
	}
}

// OTPClient is a client for the OTP schema.
type OTPClient struct {
	config
}

// NewOTPClient returns a client for the OTP from the given config.
func NewOTPClient(c config) *OTPClient {
	return &OTPClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `otp.Hooks(f(g(h())))`.
func (c *OTPClient) Use(hooks ...Hook) {
	c.hooks.OTP = append(c.hooks.OTP, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `otp.Intercept(f(g(h())))`.
func (c *OTPClient) Intercept(interceptors ...Interceptor) {
	c.inters.OTP = append(c.inters.OTP, interceptors...)
}

// Create returns a builder for creating a OTP entity.
func (c *OTPClient) Create() *OTPCreate {
	mutation := newOTPMutation(c.config, OpCreate)
	return &OTPCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of OTP entities.
func (c *OTPClient) CreateBulk(builders ...*OTPCreate) *OTPCreateBulk {
	return &OTPCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *OTPClient) MapCreateBulk(slice any, setFunc func(*OTPCreate, int)) *OTPCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &OTPCreateBulk{err: fmt.Errorf("calling to OTPClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*OTPCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &OTPCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for OTP.
func (c *OTPClient) Update() *OTPUpdate {
	mutation := newOTPMutation(c.config, OpUpdate)
	return &OTPUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *OTPClient) UpdateOne(o *OTP) *OTPUpdateOne {
	mutation := newOTPMutation(c.config, OpUpdateOne, withOTP(o))
	return &OTPUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *OTPClient) UpdateOneID(id int) *OTPUpdateOne {
	mutation := newOTPMutation(c.config, OpUpdateOne, withOTPID(id))
	return &OTPUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for OTP.
func (c *OTPClient) Delete() *OTPDelete {
	mutation := newOTPMutation(c.config, OpDelete)
	return &OTPDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *OTPClient) DeleteOne(o *OTP) *OTPDeleteOne {
	return c.DeleteOneID(o.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *OTPClient) DeleteOneID(id int) *OTPDeleteOne {
	builder := c.Delete().Where(otp.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &OTPDeleteOne{builder}
}

// Query returns a query builder for OTP.
func (c *OTPClient) Query() *OTPQuery {
	return &OTPQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeOTP},
		inters: c.Interceptors(),
	}
}

// Get returns a OTP entity by its id.
func (c *OTPClient) Get(ctx context.Context, id int) (*OTP, error) {
	return c.Query().Where(otp.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *OTPClient) GetX(ctx context.Context, id int) *OTP {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *OTPClient) Hooks() []Hook {
	hooks := c.hooks.OTP
	return append(hooks[:len(hooks):len(hooks)], otp.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *OTPClient) Interceptors() []Interceptor {
	return c.inters.OTP
}

func (c *OTPClient) mutate(ctx context.Context, m *OTPMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&OTPCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&OTPUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&OTPUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&OTPDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown OTP mutation op: %q", m.Op())
	}
}

// OperationClient is a client for the Operation schema.
type OperationClient struct {
	config
}

// NewOperationClient returns a client for the Operation from the given config.
func NewOperationClient(c config) *OperationClient {
	return &OperationClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `operation.Hooks(f(g(h())))`.
func (c *OperationClient) Use(hooks ...Hook) {
	c.hooks.Operation = append(c.hooks.Operation, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `operation.Intercept(f(g(h())))`.
func (c *OperationClient) Intercept(interceptors ...Interceptor) {
	c.inters.Operation = append(c.inters.Operation, interceptors...)
}

// Create returns a builder for creating a Operation entity.
func (c *OperationClient) Create() *OperationCreate {
	mutation := newOperationMutation(c.config, OpCreate)
	return &OperationCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Operation entities.
func (c *OperationClient) CreateBulk(builders ...*OperationCreate) *OperationCreateBulk {
	return &OperationCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *OperationClient) MapCreateBulk(slice any, setFunc func(*OperationCreate, int)) *OperationCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &OperationCreateBulk{err: fmt.Errorf("calling to OperationClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*OperationCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &OperationCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Operation.
func (c *OperationClient) Update() *OperationUpdate {
	mutation := newOperationMutation(c.config, OpUpdate)
	return &OperationUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *OperationClient) UpdateOne(o *Operation) *OperationUpdateOne {
	mutation := newOperationMutation(c.config, OpUpdateOne, withOperation(o))
	return &OperationUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *OperationClient) UpdateOneID(id int) *OperationUpdateOne {
	mutation := newOperationMutation(c.config, OpUpdateOne, withOperationID(id))
	return &OperationUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Operation.
func (c *OperationClient) Delete() *OperationDelete {
	mutation := newOperationMutation(c.config, OpDelete)
	return &OperationDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *OperationClient) DeleteOne(o *Operation) *OperationDeleteOne {
	return c.DeleteOneID(o.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *OperationClient) DeleteOneID(id int) *OperationDeleteOne {
	builder := c.Delete().Where(operation.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &OperationDeleteOne{builder}
}

// Query returns a query builder for Operation.
func (c *OperationClient) Query() *OperationQuery {
	return &OperationQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeOperation},
		inters: c.Interceptors(),
	}
}

// Get returns a Operation entity by its id.
func (c *OperationClient) Get(ctx context.Context, id int) (*Operation, error) {
	return c.Query().Where(operation.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *OperationClient) GetX(ctx context.Context, id int) *Operation {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryAssignedProduct queries the assigned_product edge of a Operation.
func (c *OperationClient) QueryAssignedProduct(o *Operation) *ProductQuery {
	query := (&ProductClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := o.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(operation.Table, operation.FieldID, id),
			sqlgraph.To(product.Table, product.FieldID),
			sqlgraph.Edge(sqlgraph.M2M, true, operation.AssignedProductTable, operation.AssignedProductPrimaryKey...),
		)
		fromV = sqlgraph.Neighbors(o.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryOperationMaterials queries the operation_materials edge of a Operation.
func (c *OperationClient) QueryOperationMaterials(o *Operation) *OperationMaterialQuery {
	query := (&OperationMaterialClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := o.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(operation.Table, operation.FieldID, id),
			sqlgraph.To(operationmaterial.Table, operationmaterial.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, operation.OperationMaterialsTable, operation.OperationMaterialsColumn),
		)
		fromV = sqlgraph.Neighbors(o.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryProductOperation queries the product_operation edge of a Operation.
func (c *OperationClient) QueryProductOperation(o *Operation) *ProductOperationQuery {
	query := (&ProductOperationClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := o.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(operation.Table, operation.FieldID, id),
			sqlgraph.To(productoperation.Table, productoperation.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, operation.ProductOperationTable, operation.ProductOperationColumn),
		)
		fromV = sqlgraph.Neighbors(o.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *OperationClient) Hooks() []Hook {
	hooks := c.hooks.Operation
	return append(hooks[:len(hooks):len(hooks)], operation.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *OperationClient) Interceptors() []Interceptor {
	inters := c.inters.Operation
	return append(inters[:len(inters):len(inters)], operation.Interceptors[:]...)
}

func (c *OperationClient) mutate(ctx context.Context, m *OperationMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&OperationCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&OperationUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&OperationUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&OperationDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Operation mutation op: %q", m.Op())
	}
}

// OperationMaterialClient is a client for the OperationMaterial schema.
type OperationMaterialClient struct {
	config
}

// NewOperationMaterialClient returns a client for the OperationMaterial from the given config.
func NewOperationMaterialClient(c config) *OperationMaterialClient {
	return &OperationMaterialClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `operationmaterial.Hooks(f(g(h())))`.
func (c *OperationMaterialClient) Use(hooks ...Hook) {
	c.hooks.OperationMaterial = append(c.hooks.OperationMaterial, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `operationmaterial.Intercept(f(g(h())))`.
func (c *OperationMaterialClient) Intercept(interceptors ...Interceptor) {
	c.inters.OperationMaterial = append(c.inters.OperationMaterial, interceptors...)
}

// Create returns a builder for creating a OperationMaterial entity.
func (c *OperationMaterialClient) Create() *OperationMaterialCreate {
	mutation := newOperationMaterialMutation(c.config, OpCreate)
	return &OperationMaterialCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of OperationMaterial entities.
func (c *OperationMaterialClient) CreateBulk(builders ...*OperationMaterialCreate) *OperationMaterialCreateBulk {
	return &OperationMaterialCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *OperationMaterialClient) MapCreateBulk(slice any, setFunc func(*OperationMaterialCreate, int)) *OperationMaterialCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &OperationMaterialCreateBulk{err: fmt.Errorf("calling to OperationMaterialClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*OperationMaterialCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &OperationMaterialCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for OperationMaterial.
func (c *OperationMaterialClient) Update() *OperationMaterialUpdate {
	mutation := newOperationMaterialMutation(c.config, OpUpdate)
	return &OperationMaterialUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *OperationMaterialClient) UpdateOne(om *OperationMaterial) *OperationMaterialUpdateOne {
	mutation := newOperationMaterialMutation(c.config, OpUpdateOne, withOperationMaterial(om))
	return &OperationMaterialUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *OperationMaterialClient) UpdateOneID(id int) *OperationMaterialUpdateOne {
	mutation := newOperationMaterialMutation(c.config, OpUpdateOne, withOperationMaterialID(id))
	return &OperationMaterialUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for OperationMaterial.
func (c *OperationMaterialClient) Delete() *OperationMaterialDelete {
	mutation := newOperationMaterialMutation(c.config, OpDelete)
	return &OperationMaterialDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *OperationMaterialClient) DeleteOne(om *OperationMaterial) *OperationMaterialDeleteOne {
	return c.DeleteOneID(om.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *OperationMaterialClient) DeleteOneID(id int) *OperationMaterialDeleteOne {
	builder := c.Delete().Where(operationmaterial.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &OperationMaterialDeleteOne{builder}
}

// Query returns a query builder for OperationMaterial.
func (c *OperationMaterialClient) Query() *OperationMaterialQuery {
	return &OperationMaterialQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeOperationMaterial},
		inters: c.Interceptors(),
	}
}

// Get returns a OperationMaterial entity by its id.
func (c *OperationMaterialClient) Get(ctx context.Context, id int) (*OperationMaterial, error) {
	return c.Query().Where(operationmaterial.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *OperationMaterialClient) GetX(ctx context.Context, id int) *OperationMaterial {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryOperation queries the operation edge of a OperationMaterial.
func (c *OperationMaterialClient) QueryOperation(om *OperationMaterial) *OperationQuery {
	query := (&OperationClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := om.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(operationmaterial.Table, operationmaterial.FieldID, id),
			sqlgraph.To(operation.Table, operation.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, operationmaterial.OperationTable, operationmaterial.OperationColumn),
		)
		fromV = sqlgraph.Neighbors(om.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryMaterial queries the material edge of a OperationMaterial.
func (c *OperationMaterialClient) QueryMaterial(om *OperationMaterial) *MaterialQuery {
	query := (&MaterialClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := om.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(operationmaterial.Table, operationmaterial.FieldID, id),
			sqlgraph.To(material.Table, material.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, operationmaterial.MaterialTable, operationmaterial.MaterialColumn),
		)
		fromV = sqlgraph.Neighbors(om.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *OperationMaterialClient) Hooks() []Hook {
	hooks := c.hooks.OperationMaterial
	return append(hooks[:len(hooks):len(hooks)], operationmaterial.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *OperationMaterialClient) Interceptors() []Interceptor {
	return c.inters.OperationMaterial
}

func (c *OperationMaterialClient) mutate(ctx context.Context, m *OperationMaterialMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&OperationMaterialCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&OperationMaterialUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&OperationMaterialUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&OperationMaterialDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown OperationMaterial mutation op: %q", m.Op())
	}
}

// OrganizationClient is a client for the Organization schema.
type OrganizationClient struct {
	config
}

// NewOrganizationClient returns a client for the Organization from the given config.
func NewOrganizationClient(c config) *OrganizationClient {
	return &OrganizationClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `organization.Hooks(f(g(h())))`.
func (c *OrganizationClient) Use(hooks ...Hook) {
	c.hooks.Organization = append(c.hooks.Organization, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `organization.Intercept(f(g(h())))`.
func (c *OrganizationClient) Intercept(interceptors ...Interceptor) {
	c.inters.Organization = append(c.inters.Organization, interceptors...)
}

// Create returns a builder for creating a Organization entity.
func (c *OrganizationClient) Create() *OrganizationCreate {
	mutation := newOrganizationMutation(c.config, OpCreate)
	return &OrganizationCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Organization entities.
func (c *OrganizationClient) CreateBulk(builders ...*OrganizationCreate) *OrganizationCreateBulk {
	return &OrganizationCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *OrganizationClient) MapCreateBulk(slice any, setFunc func(*OrganizationCreate, int)) *OrganizationCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &OrganizationCreateBulk{err: fmt.Errorf("calling to OrganizationClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*OrganizationCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &OrganizationCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Organization.
func (c *OrganizationClient) Update() *OrganizationUpdate {
	mutation := newOrganizationMutation(c.config, OpUpdate)
	return &OrganizationUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *OrganizationClient) UpdateOne(o *Organization) *OrganizationUpdateOne {
	mutation := newOrganizationMutation(c.config, OpUpdateOne, withOrganization(o))
	return &OrganizationUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *OrganizationClient) UpdateOneID(id int) *OrganizationUpdateOne {
	mutation := newOrganizationMutation(c.config, OpUpdateOne, withOrganizationID(id))
	return &OrganizationUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Organization.
func (c *OrganizationClient) Delete() *OrganizationDelete {
	mutation := newOrganizationMutation(c.config, OpDelete)
	return &OrganizationDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *OrganizationClient) DeleteOne(o *Organization) *OrganizationDeleteOne {
	return c.DeleteOneID(o.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *OrganizationClient) DeleteOneID(id int) *OrganizationDeleteOne {
	builder := c.Delete().Where(organization.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &OrganizationDeleteOne{builder}
}

// Query returns a query builder for Organization.
func (c *OrganizationClient) Query() *OrganizationQuery {
	return &OrganizationQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeOrganization},
		inters: c.Interceptors(),
	}
}

// Get returns a Organization entity by its id.
func (c *OrganizationClient) Get(ctx context.Context, id int) (*Organization, error) {
	return c.Query().Where(organization.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *OrganizationClient) GetX(ctx context.Context, id int) *Organization {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *OrganizationClient) Hooks() []Hook {
	hooks := c.hooks.Organization
	return append(hooks[:len(hooks):len(hooks)], organization.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *OrganizationClient) Interceptors() []Interceptor {
	inters := c.inters.Organization
	return append(inters[:len(inters):len(inters)], organization.Interceptors[:]...)
}

func (c *OrganizationClient) mutate(ctx context.Context, m *OrganizationMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&OrganizationCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&OrganizationUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&OrganizationUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&OrganizationDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Organization mutation op: %q", m.Op())
	}
}

// PaymentClient is a client for the Payment schema.
type PaymentClient struct {
	config
}

// NewPaymentClient returns a client for the Payment from the given config.
func NewPaymentClient(c config) *PaymentClient {
	return &PaymentClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `payment.Hooks(f(g(h())))`.
func (c *PaymentClient) Use(hooks ...Hook) {
	c.hooks.Payment = append(c.hooks.Payment, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `payment.Intercept(f(g(h())))`.
func (c *PaymentClient) Intercept(interceptors ...Interceptor) {
	c.inters.Payment = append(c.inters.Payment, interceptors...)
}

// Create returns a builder for creating a Payment entity.
func (c *PaymentClient) Create() *PaymentCreate {
	mutation := newPaymentMutation(c.config, OpCreate)
	return &PaymentCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Payment entities.
func (c *PaymentClient) CreateBulk(builders ...*PaymentCreate) *PaymentCreateBulk {
	return &PaymentCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *PaymentClient) MapCreateBulk(slice any, setFunc func(*PaymentCreate, int)) *PaymentCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &PaymentCreateBulk{err: fmt.Errorf("calling to PaymentClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*PaymentCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &PaymentCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Payment.
func (c *PaymentClient) Update() *PaymentUpdate {
	mutation := newPaymentMutation(c.config, OpUpdate)
	return &PaymentUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *PaymentClient) UpdateOne(pa *Payment) *PaymentUpdateOne {
	mutation := newPaymentMutation(c.config, OpUpdateOne, withPayment(pa))
	return &PaymentUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *PaymentClient) UpdateOneID(id int) *PaymentUpdateOne {
	mutation := newPaymentMutation(c.config, OpUpdateOne, withPaymentID(id))
	return &PaymentUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Payment.
func (c *PaymentClient) Delete() *PaymentDelete {
	mutation := newPaymentMutation(c.config, OpDelete)
	return &PaymentDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *PaymentClient) DeleteOne(pa *Payment) *PaymentDeleteOne {
	return c.DeleteOneID(pa.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *PaymentClient) DeleteOneID(id int) *PaymentDeleteOne {
	builder := c.Delete().Where(payment.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &PaymentDeleteOne{builder}
}

// Query returns a query builder for Payment.
func (c *PaymentClient) Query() *PaymentQuery {
	return &PaymentQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypePayment},
		inters: c.Interceptors(),
	}
}

// Get returns a Payment entity by its id.
func (c *PaymentClient) Get(ctx context.Context, id int) (*Payment, error) {
	return c.Query().Where(payment.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *PaymentClient) GetX(ctx context.Context, id int) *Payment {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryBill queries the bill edge of a Payment.
func (c *PaymentClient) QueryBill(pa *Payment) *BillQuery {
	query := (&BillClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := pa.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(payment.Table, payment.FieldID, id),
			sqlgraph.To(bill.Table, bill.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, payment.BillTable, payment.BillColumn),
		)
		fromV = sqlgraph.Neighbors(pa.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryAllocations queries the allocations edge of a Payment.
func (c *PaymentClient) QueryAllocations(pa *Payment) *PaymentAllocationQuery {
	query := (&PaymentAllocationClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := pa.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(payment.Table, payment.FieldID, id),
			sqlgraph.To(paymentallocation.Table, paymentallocation.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, payment.AllocationsTable, payment.AllocationsColumn),
		)
		fromV = sqlgraph.Neighbors(pa.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *PaymentClient) Hooks() []Hook {
	hooks := c.hooks.Payment
	return append(hooks[:len(hooks):len(hooks)], payment.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *PaymentClient) Interceptors() []Interceptor {
	inters := c.inters.Payment
	return append(inters[:len(inters):len(inters)], payment.Interceptors[:]...)
}

func (c *PaymentClient) mutate(ctx context.Context, m *PaymentMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&PaymentCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&PaymentUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&PaymentUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&PaymentDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Payment mutation op: %q", m.Op())
	}
}

// PaymentAllocationClient is a client for the PaymentAllocation schema.
type PaymentAllocationClient struct {
	config
}

// NewPaymentAllocationClient returns a client for the PaymentAllocation from the given config.
func NewPaymentAllocationClient(c config) *PaymentAllocationClient {
	return &PaymentAllocationClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `paymentallocation.Hooks(f(g(h())))`.
func (c *PaymentAllocationClient) Use(hooks ...Hook) {
	c.hooks.PaymentAllocation = append(c.hooks.PaymentAllocation, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `paymentallocation.Intercept(f(g(h())))`.
func (c *PaymentAllocationClient) Intercept(interceptors ...Interceptor) {
	c.inters.PaymentAllocation = append(c.inters.PaymentAllocation, interceptors...)
}

// Create returns a builder for creating a PaymentAllocation entity.
func (c *PaymentAllocationClient) Create() *PaymentAllocationCreate {
	mutation := newPaymentAllocationMutation(c.config, OpCreate)
	return &PaymentAllocationCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of PaymentAllocation entities.
func (c *PaymentAllocationClient) CreateBulk(builders ...*PaymentAllocationCreate) *PaymentAllocationCreateBulk {
	return &PaymentAllocationCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *PaymentAllocationClient) MapCreateBulk(slice any, setFunc func(*PaymentAllocationCreate, int)) *PaymentAllocationCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &PaymentAllocationCreateBulk{err: fmt.Errorf("calling to PaymentAllocationClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*PaymentAllocationCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &PaymentAllocationCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for PaymentAllocation.
func (c *PaymentAllocationClient) Update() *PaymentAllocationUpdate {
	mutation := newPaymentAllocationMutation(c.config, OpUpdate)
	return &PaymentAllocationUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *PaymentAllocationClient) UpdateOne(pa *PaymentAllocation) *PaymentAllocationUpdateOne {
	mutation := newPaymentAllocationMutation(c.config, OpUpdateOne, withPaymentAllocation(pa))
	return &PaymentAllocationUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *PaymentAllocationClient) UpdateOneID(id int) *PaymentAllocationUpdateOne {
	mutation := newPaymentAllocationMutation(c.config, OpUpdateOne, withPaymentAllocationID(id))
	return &PaymentAllocationUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for PaymentAllocation.
func (c *PaymentAllocationClient) Delete() *PaymentAllocationDelete {
	mutation := newPaymentAllocationMutation(c.config, OpDelete)
	return &PaymentAllocationDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *PaymentAllocationClient) DeleteOne(pa *PaymentAllocation) *PaymentAllocationDeleteOne {
	return c.DeleteOneID(pa.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *PaymentAllocationClient) DeleteOneID(id int) *PaymentAllocationDeleteOne {
	builder := c.Delete().Where(paymentallocation.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &PaymentAllocationDeleteOne{builder}
}

// Query returns a query builder for PaymentAllocation.
func (c *PaymentAllocationClient) Query() *PaymentAllocationQuery {
	return &PaymentAllocationQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypePaymentAllocation},
		inters: c.Interceptors(),
	}
}

// Get returns a PaymentAllocation entity by its id.
func (c *PaymentAllocationClient) Get(ctx context.Context, id int) (*PaymentAllocation, error) {
	return c.Query().Where(paymentallocation.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *PaymentAllocationClient) GetX(ctx context.Context, id int) *PaymentAllocation {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryPayment queries the payment edge of a PaymentAllocation.
func (c *PaymentAllocationClient) QueryPayment(pa *PaymentAllocation) *PaymentQuery {
	query := (&PaymentClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := pa.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(paymentallocation.Table, paymentallocation.FieldID, id),
			sqlgraph.To(payment.Table, payment.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, paymentallocation.PaymentTable, paymentallocation.PaymentColumn),
		)
		fromV = sqlgraph.Neighbors(pa.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryBillItem queries the bill_item edge of a PaymentAllocation.
func (c *PaymentAllocationClient) QueryBillItem(pa *PaymentAllocation) *BillItemQuery {
	query := (&BillItemClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := pa.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(paymentallocation.Table, paymentallocation.FieldID, id),
			sqlgraph.To(billitem.Table, billitem.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, paymentallocation.BillItemTable, paymentallocation.BillItemColumn),
		)
		fromV = sqlgraph.Neighbors(pa.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryInstallment queries the installment edge of a PaymentAllocation.
func (c *PaymentAllocationClient) QueryInstallment(pa *PaymentAllocation) *InstallmentQuery {
	query := (&InstallmentClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := pa.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(paymentallocation.Table, paymentallocation.FieldID, id),
			sqlgraph.To(installment.Table, installment.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, paymentallocation.InstallmentTable, paymentallocation.InstallmentColumn),
		)
		fromV = sqlgraph.Neighbors(pa.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *PaymentAllocationClient) Hooks() []Hook {
	hooks := c.hooks.PaymentAllocation
	return append(hooks[:len(hooks):len(hooks)], paymentallocation.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *PaymentAllocationClient) Interceptors() []Interceptor {
	inters := c.inters.PaymentAllocation
	return append(inters[:len(inters):len(inters)], paymentallocation.Interceptors[:]...)
}

func (c *PaymentAllocationClient) mutate(ctx context.Context, m *PaymentAllocationMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&PaymentAllocationCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&PaymentAllocationUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&PaymentAllocationUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&PaymentAllocationDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown PaymentAllocation mutation op: %q", m.Op())
	}
}

// PaymentReportDetailViewClient is a client for the PaymentReportDetailView schema.
type PaymentReportDetailViewClient struct {
	config
}

// NewPaymentReportDetailViewClient returns a client for the PaymentReportDetailView from the given config.
func NewPaymentReportDetailViewClient(c config) *PaymentReportDetailViewClient {
	return &PaymentReportDetailViewClient{config: c}
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `paymentreportdetailview.Intercept(f(g(h())))`.
func (c *PaymentReportDetailViewClient) Intercept(interceptors ...Interceptor) {
	c.inters.PaymentReportDetailView = append(c.inters.PaymentReportDetailView, interceptors...)
}

// Query returns a query builder for PaymentReportDetailView.
func (c *PaymentReportDetailViewClient) Query() *PaymentReportDetailViewQuery {
	return &PaymentReportDetailViewQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypePaymentReportDetailView},
		inters: c.Interceptors(),
	}
}

// Interceptors returns the client interceptors.
func (c *PaymentReportDetailViewClient) Interceptors() []Interceptor {
	return c.inters.PaymentReportDetailView
}

// PaymentReportViewClient is a client for the PaymentReportView schema.
type PaymentReportViewClient struct {
	config
}

// NewPaymentReportViewClient returns a client for the PaymentReportView from the given config.
func NewPaymentReportViewClient(c config) *PaymentReportViewClient {
	return &PaymentReportViewClient{config: c}
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `paymentreportview.Intercept(f(g(h())))`.
func (c *PaymentReportViewClient) Intercept(interceptors ...Interceptor) {
	c.inters.PaymentReportView = append(c.inters.PaymentReportView, interceptors...)
}

// Query returns a query builder for PaymentReportView.
func (c *PaymentReportViewClient) Query() *PaymentReportViewQuery {
	return &PaymentReportViewQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypePaymentReportView},
		inters: c.Interceptors(),
	}
}

// Interceptors returns the client interceptors.
func (c *PaymentReportViewClient) Interceptors() []Interceptor {
	return c.inters.PaymentReportView
}

// PersonClient is a client for the Person schema.
type PersonClient struct {
	config
}

// NewPersonClient returns a client for the Person from the given config.
func NewPersonClient(c config) *PersonClient {
	return &PersonClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `person.Hooks(f(g(h())))`.
func (c *PersonClient) Use(hooks ...Hook) {
	c.hooks.Person = append(c.hooks.Person, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `person.Intercept(f(g(h())))`.
func (c *PersonClient) Intercept(interceptors ...Interceptor) {
	c.inters.Person = append(c.inters.Person, interceptors...)
}

// Create returns a builder for creating a Person entity.
func (c *PersonClient) Create() *PersonCreate {
	mutation := newPersonMutation(c.config, OpCreate)
	return &PersonCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Person entities.
func (c *PersonClient) CreateBulk(builders ...*PersonCreate) *PersonCreateBulk {
	return &PersonCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *PersonClient) MapCreateBulk(slice any, setFunc func(*PersonCreate, int)) *PersonCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &PersonCreateBulk{err: fmt.Errorf("calling to PersonClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*PersonCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &PersonCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Person.
func (c *PersonClient) Update() *PersonUpdate {
	mutation := newPersonMutation(c.config, OpUpdate)
	return &PersonUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *PersonClient) UpdateOne(pe *Person) *PersonUpdateOne {
	mutation := newPersonMutation(c.config, OpUpdateOne, withPerson(pe))
	return &PersonUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *PersonClient) UpdateOneID(id int) *PersonUpdateOne {
	mutation := newPersonMutation(c.config, OpUpdateOne, withPersonID(id))
	return &PersonUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Person.
func (c *PersonClient) Delete() *PersonDelete {
	mutation := newPersonMutation(c.config, OpDelete)
	return &PersonDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *PersonClient) DeleteOne(pe *Person) *PersonDeleteOne {
	return c.DeleteOneID(pe.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *PersonClient) DeleteOneID(id int) *PersonDeleteOne {
	builder := c.Delete().Where(person.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &PersonDeleteOne{builder}
}

// Query returns a query builder for Person.
func (c *PersonClient) Query() *PersonQuery {
	return &PersonQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypePerson},
		inters: c.Interceptors(),
	}
}

// Get returns a Person entity by its id.
func (c *PersonClient) Get(ctx context.Context, id int) (*Person, error) {
	return c.Query().Where(person.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *PersonClient) GetX(ctx context.Context, id int) *Person {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryDeals queries the deals edge of a Person.
func (c *PersonClient) QueryDeals(pe *Person) *DealQuery {
	query := (&DealClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := pe.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(person.Table, person.FieldID, id),
			sqlgraph.To(deal.Table, deal.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, person.DealsTable, person.DealsColumn),
		)
		fromV = sqlgraph.Neighbors(pe.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryTags queries the tags edge of a Person.
func (c *PersonClient) QueryTags(pe *Person) *TagQuery {
	query := (&TagClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := pe.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(person.Table, person.FieldID, id),
			sqlgraph.To(tag.Table, tag.FieldID),
			sqlgraph.Edge(sqlgraph.M2M, false, person.TagsTable, person.TagsPrimaryKey...),
		)
		fromV = sqlgraph.Neighbors(pe.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryFormSubmissions queries the form_submissions edge of a Person.
func (c *PersonClient) QueryFormSubmissions(pe *Person) *FormSubmissionQuery {
	query := (&FormSubmissionClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := pe.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(person.Table, person.FieldID, id),
			sqlgraph.To(formsubmission.Table, formsubmission.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, person.FormSubmissionsTable, person.FormSubmissionsColumn),
		)
		fromV = sqlgraph.Neighbors(pe.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryPlans queries the plans edge of a Person.
func (c *PersonClient) QueryPlans(pe *Person) *InstallmentPlanQuery {
	query := (&InstallmentPlanClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := pe.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(person.Table, person.FieldID, id),
			sqlgraph.To(installmentplan.Table, installmentplan.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, person.PlansTable, person.PlansColumn),
		)
		fromV = sqlgraph.Neighbors(pe.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryCalls queries the calls edge of a Person.
func (c *PersonClient) QueryCalls(pe *Person) *CallQuery {
	query := (&CallClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := pe.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(person.Table, person.FieldID, id),
			sqlgraph.To(call.Table, call.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, person.CallsTable, person.CallsColumn),
		)
		fromV = sqlgraph.Neighbors(pe.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryTasks queries the tasks edge of a Person.
func (c *PersonClient) QueryTasks(pe *Person) *TaskQuery {
	query := (&TaskClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := pe.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(person.Table, person.FieldID, id),
			sqlgraph.To(task.Table, task.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, person.TasksTable, person.TasksColumn),
		)
		fromV = sqlgraph.Neighbors(pe.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryIssues queries the issues edge of a Person.
func (c *PersonClient) QueryIssues(pe *Person) *IssueQuery {
	query := (&IssueClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := pe.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(person.Table, person.FieldID, id),
			sqlgraph.To(issue.Table, issue.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, person.IssuesTable, person.IssuesColumn),
		)
		fromV = sqlgraph.Neighbors(pe.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryAppointments queries the appointments edge of a Person.
func (c *PersonClient) QueryAppointments(pe *Person) *AppointmentQuery {
	query := (&AppointmentClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := pe.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(person.Table, person.FieldID, id),
			sqlgraph.To(appointment.Table, appointment.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, person.AppointmentsTable, person.AppointmentsColumn),
		)
		fromV = sqlgraph.Neighbors(pe.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryTracks queries the tracks edge of a Person.
func (c *PersonClient) QueryTracks(pe *Person) *TrackQuery {
	query := (&TrackClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := pe.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(person.Table, person.FieldID, id),
			sqlgraph.To(track.Table, track.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, person.TracksTable, person.TracksColumn),
		)
		fromV = sqlgraph.Neighbors(pe.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryMessageHistories queries the message_histories edge of a Person.
func (c *PersonClient) QueryMessageHistories(pe *Person) *MessageHistoryQuery {
	query := (&MessageHistoryClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := pe.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(person.Table, person.FieldID, id),
			sqlgraph.To(messagehistory.Table, messagehistory.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, person.MessageHistoriesTable, person.MessageHistoriesColumn),
		)
		fromV = sqlgraph.Neighbors(pe.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryAssignees queries the assignees edge of a Person.
func (c *PersonClient) QueryAssignees(pe *Person) *UserQuery {
	query := (&UserClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := pe.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(person.Table, person.FieldID, id),
			sqlgraph.To(user.Table, user.FieldID),
			sqlgraph.Edge(sqlgraph.M2M, false, person.AssigneesTable, person.AssigneesPrimaryKey...),
		)
		fromV = sqlgraph.Neighbors(pe.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryJob queries the job edge of a Person.
func (c *PersonClient) QueryJob(pe *Person) *TermQuery {
	query := (&TermClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := pe.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(person.Table, person.FieldID, id),
			sqlgraph.To(term.Table, term.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, person.JobTable, person.JobColumn),
		)
		fromV = sqlgraph.Neighbors(pe.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QuerySource queries the source edge of a Person.
func (c *PersonClient) QuerySource(pe *Person) *TermQuery {
	query := (&TermClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := pe.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(person.Table, person.FieldID, id),
			sqlgraph.To(term.Table, term.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, person.SourceTable, person.SourceColumn),
		)
		fromV = sqlgraph.Neighbors(pe.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryCreator queries the creator edge of a Person.
func (c *PersonClient) QueryCreator(pe *Person) *UserQuery {
	query := (&UserClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := pe.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(person.Table, person.FieldID, id),
			sqlgraph.To(user.Table, user.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, person.CreatorTable, person.CreatorColumn),
		)
		fromV = sqlgraph.Neighbors(pe.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryReferredBy queries the referred_by edge of a Person.
func (c *PersonClient) QueryReferredBy(pe *Person) *PersonReferralQuery {
	query := (&PersonReferralClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := pe.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(person.Table, person.FieldID, id),
			sqlgraph.To(personreferral.Table, personreferral.FieldID),
			sqlgraph.Edge(sqlgraph.O2O, false, person.ReferredByTable, person.ReferredByColumn),
		)
		fromV = sqlgraph.Neighbors(pe.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryTagPerson queries the tag_person edge of a Person.
func (c *PersonClient) QueryTagPerson(pe *Person) *TagPersonQuery {
	query := (&TagPersonClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := pe.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(person.Table, person.FieldID, id),
			sqlgraph.To(tagperson.Table, tagperson.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, person.TagPersonTable, person.TagPersonColumn),
		)
		fromV = sqlgraph.Neighbors(pe.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryAssignment queries the assignment edge of a Person.
func (c *PersonClient) QueryAssignment(pe *Person) *PersonAssignmentQuery {
	query := (&PersonAssignmentClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := pe.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(person.Table, person.FieldID, id),
			sqlgraph.To(personassignment.Table, personassignment.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, person.AssignmentTable, person.AssignmentColumn),
		)
		fromV = sqlgraph.Neighbors(pe.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *PersonClient) Hooks() []Hook {
	hooks := c.hooks.Person
	return append(hooks[:len(hooks):len(hooks)], person.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *PersonClient) Interceptors() []Interceptor {
	inters := c.inters.Person
	return append(inters[:len(inters):len(inters)], person.Interceptors[:]...)
}

func (c *PersonClient) mutate(ctx context.Context, m *PersonMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&PersonCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&PersonUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&PersonUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&PersonDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Person mutation op: %q", m.Op())
	}
}

// PersonAssignmentClient is a client for the PersonAssignment schema.
type PersonAssignmentClient struct {
	config
}

// NewPersonAssignmentClient returns a client for the PersonAssignment from the given config.
func NewPersonAssignmentClient(c config) *PersonAssignmentClient {
	return &PersonAssignmentClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `personassignment.Hooks(f(g(h())))`.
func (c *PersonAssignmentClient) Use(hooks ...Hook) {
	c.hooks.PersonAssignment = append(c.hooks.PersonAssignment, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `personassignment.Intercept(f(g(h())))`.
func (c *PersonAssignmentClient) Intercept(interceptors ...Interceptor) {
	c.inters.PersonAssignment = append(c.inters.PersonAssignment, interceptors...)
}

// Create returns a builder for creating a PersonAssignment entity.
func (c *PersonAssignmentClient) Create() *PersonAssignmentCreate {
	mutation := newPersonAssignmentMutation(c.config, OpCreate)
	return &PersonAssignmentCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of PersonAssignment entities.
func (c *PersonAssignmentClient) CreateBulk(builders ...*PersonAssignmentCreate) *PersonAssignmentCreateBulk {
	return &PersonAssignmentCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *PersonAssignmentClient) MapCreateBulk(slice any, setFunc func(*PersonAssignmentCreate, int)) *PersonAssignmentCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &PersonAssignmentCreateBulk{err: fmt.Errorf("calling to PersonAssignmentClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*PersonAssignmentCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &PersonAssignmentCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for PersonAssignment.
func (c *PersonAssignmentClient) Update() *PersonAssignmentUpdate {
	mutation := newPersonAssignmentMutation(c.config, OpUpdate)
	return &PersonAssignmentUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *PersonAssignmentClient) UpdateOne(pa *PersonAssignment) *PersonAssignmentUpdateOne {
	mutation := newPersonAssignmentMutation(c.config, OpUpdateOne, withPersonAssignment(pa))
	return &PersonAssignmentUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *PersonAssignmentClient) UpdateOneID(id int) *PersonAssignmentUpdateOne {
	mutation := newPersonAssignmentMutation(c.config, OpUpdateOne, withPersonAssignmentID(id))
	return &PersonAssignmentUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for PersonAssignment.
func (c *PersonAssignmentClient) Delete() *PersonAssignmentDelete {
	mutation := newPersonAssignmentMutation(c.config, OpDelete)
	return &PersonAssignmentDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *PersonAssignmentClient) DeleteOne(pa *PersonAssignment) *PersonAssignmentDeleteOne {
	return c.DeleteOneID(pa.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *PersonAssignmentClient) DeleteOneID(id int) *PersonAssignmentDeleteOne {
	builder := c.Delete().Where(personassignment.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &PersonAssignmentDeleteOne{builder}
}

// Query returns a query builder for PersonAssignment.
func (c *PersonAssignmentClient) Query() *PersonAssignmentQuery {
	return &PersonAssignmentQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypePersonAssignment},
		inters: c.Interceptors(),
	}
}

// Get returns a PersonAssignment entity by its id.
func (c *PersonAssignmentClient) Get(ctx context.Context, id int) (*PersonAssignment, error) {
	return c.Query().Where(personassignment.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *PersonAssignmentClient) GetX(ctx context.Context, id int) *PersonAssignment {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryPerson queries the person edge of a PersonAssignment.
func (c *PersonAssignmentClient) QueryPerson(pa *PersonAssignment) *PersonQuery {
	query := (&PersonClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := pa.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(personassignment.Table, personassignment.FieldID, id),
			sqlgraph.To(person.Table, person.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, personassignment.PersonTable, personassignment.PersonColumn),
		)
		fromV = sqlgraph.Neighbors(pa.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryUser queries the user edge of a PersonAssignment.
func (c *PersonAssignmentClient) QueryUser(pa *PersonAssignment) *UserQuery {
	query := (&UserClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := pa.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(personassignment.Table, personassignment.FieldID, id),
			sqlgraph.To(user.Table, user.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, personassignment.UserTable, personassignment.UserColumn),
		)
		fromV = sqlgraph.Neighbors(pa.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *PersonAssignmentClient) Hooks() []Hook {
	return c.hooks.PersonAssignment
}

// Interceptors returns the client interceptors.
func (c *PersonAssignmentClient) Interceptors() []Interceptor {
	return c.inters.PersonAssignment
}

func (c *PersonAssignmentClient) mutate(ctx context.Context, m *PersonAssignmentMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&PersonAssignmentCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&PersonAssignmentUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&PersonAssignmentUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&PersonAssignmentDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown PersonAssignment mutation op: %q", m.Op())
	}
}

// PersonDataClient is a client for the PersonData schema.
type PersonDataClient struct {
	config
}

// NewPersonDataClient returns a client for the PersonData from the given config.
func NewPersonDataClient(c config) *PersonDataClient {
	return &PersonDataClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `persondata.Hooks(f(g(h())))`.
func (c *PersonDataClient) Use(hooks ...Hook) {
	c.hooks.PersonData = append(c.hooks.PersonData, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `persondata.Intercept(f(g(h())))`.
func (c *PersonDataClient) Intercept(interceptors ...Interceptor) {
	c.inters.PersonData = append(c.inters.PersonData, interceptors...)
}

// Create returns a builder for creating a PersonData entity.
func (c *PersonDataClient) Create() *PersonDataCreate {
	mutation := newPersonDataMutation(c.config, OpCreate)
	return &PersonDataCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of PersonData entities.
func (c *PersonDataClient) CreateBulk(builders ...*PersonDataCreate) *PersonDataCreateBulk {
	return &PersonDataCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *PersonDataClient) MapCreateBulk(slice any, setFunc func(*PersonDataCreate, int)) *PersonDataCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &PersonDataCreateBulk{err: fmt.Errorf("calling to PersonDataClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*PersonDataCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &PersonDataCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for PersonData.
func (c *PersonDataClient) Update() *PersonDataUpdate {
	mutation := newPersonDataMutation(c.config, OpUpdate)
	return &PersonDataUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *PersonDataClient) UpdateOne(pd *PersonData) *PersonDataUpdateOne {
	mutation := newPersonDataMutation(c.config, OpUpdateOne, withPersonData(pd))
	return &PersonDataUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *PersonDataClient) UpdateOneID(id int) *PersonDataUpdateOne {
	mutation := newPersonDataMutation(c.config, OpUpdateOne, withPersonDataID(id))
	return &PersonDataUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for PersonData.
func (c *PersonDataClient) Delete() *PersonDataDelete {
	mutation := newPersonDataMutation(c.config, OpDelete)
	return &PersonDataDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *PersonDataClient) DeleteOne(pd *PersonData) *PersonDataDeleteOne {
	return c.DeleteOneID(pd.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *PersonDataClient) DeleteOneID(id int) *PersonDataDeleteOne {
	builder := c.Delete().Where(persondata.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &PersonDataDeleteOne{builder}
}

// Query returns a query builder for PersonData.
func (c *PersonDataClient) Query() *PersonDataQuery {
	return &PersonDataQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypePersonData},
		inters: c.Interceptors(),
	}
}

// Get returns a PersonData entity by its id.
func (c *PersonDataClient) Get(ctx context.Context, id int) (*PersonData, error) {
	return c.Query().Where(persondata.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *PersonDataClient) GetX(ctx context.Context, id int) *PersonData {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryPerson queries the person edge of a PersonData.
func (c *PersonDataClient) QueryPerson(pd *PersonData) *PersonQuery {
	query := (&PersonClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := pd.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(persondata.Table, persondata.FieldID, id),
			sqlgraph.To(person.Table, person.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, persondata.PersonTable, persondata.PersonColumn),
		)
		fromV = sqlgraph.Neighbors(pd.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *PersonDataClient) Hooks() []Hook {
	return c.hooks.PersonData
}

// Interceptors returns the client interceptors.
func (c *PersonDataClient) Interceptors() []Interceptor {
	return c.inters.PersonData
}

func (c *PersonDataClient) mutate(ctx context.Context, m *PersonDataMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&PersonDataCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&PersonDataUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&PersonDataUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&PersonDataDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown PersonData mutation op: %q", m.Op())
	}
}

// PersonHistoryClient is a client for the PersonHistory schema.
type PersonHistoryClient struct {
	config
}

// NewPersonHistoryClient returns a client for the PersonHistory from the given config.
func NewPersonHistoryClient(c config) *PersonHistoryClient {
	return &PersonHistoryClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `personhistory.Hooks(f(g(h())))`.
func (c *PersonHistoryClient) Use(hooks ...Hook) {
	c.hooks.PersonHistory = append(c.hooks.PersonHistory, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `personhistory.Intercept(f(g(h())))`.
func (c *PersonHistoryClient) Intercept(interceptors ...Interceptor) {
	c.inters.PersonHistory = append(c.inters.PersonHistory, interceptors...)
}

// Create returns a builder for creating a PersonHistory entity.
func (c *PersonHistoryClient) Create() *PersonHistoryCreate {
	mutation := newPersonHistoryMutation(c.config, OpCreate)
	return &PersonHistoryCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of PersonHistory entities.
func (c *PersonHistoryClient) CreateBulk(builders ...*PersonHistoryCreate) *PersonHistoryCreateBulk {
	return &PersonHistoryCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *PersonHistoryClient) MapCreateBulk(slice any, setFunc func(*PersonHistoryCreate, int)) *PersonHistoryCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &PersonHistoryCreateBulk{err: fmt.Errorf("calling to PersonHistoryClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*PersonHistoryCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &PersonHistoryCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for PersonHistory.
func (c *PersonHistoryClient) Update() *PersonHistoryUpdate {
	mutation := newPersonHistoryMutation(c.config, OpUpdate)
	return &PersonHistoryUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *PersonHistoryClient) UpdateOne(ph *PersonHistory) *PersonHistoryUpdateOne {
	mutation := newPersonHistoryMutation(c.config, OpUpdateOne, withPersonHistory(ph))
	return &PersonHistoryUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *PersonHistoryClient) UpdateOneID(id int) *PersonHistoryUpdateOne {
	mutation := newPersonHistoryMutation(c.config, OpUpdateOne, withPersonHistoryID(id))
	return &PersonHistoryUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for PersonHistory.
func (c *PersonHistoryClient) Delete() *PersonHistoryDelete {
	mutation := newPersonHistoryMutation(c.config, OpDelete)
	return &PersonHistoryDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *PersonHistoryClient) DeleteOne(ph *PersonHistory) *PersonHistoryDeleteOne {
	return c.DeleteOneID(ph.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *PersonHistoryClient) DeleteOneID(id int) *PersonHistoryDeleteOne {
	builder := c.Delete().Where(personhistory.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &PersonHistoryDeleteOne{builder}
}

// Query returns a query builder for PersonHistory.
func (c *PersonHistoryClient) Query() *PersonHistoryQuery {
	return &PersonHistoryQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypePersonHistory},
		inters: c.Interceptors(),
	}
}

// Get returns a PersonHistory entity by its id.
func (c *PersonHistoryClient) Get(ctx context.Context, id int) (*PersonHistory, error) {
	return c.Query().Where(personhistory.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *PersonHistoryClient) GetX(ctx context.Context, id int) *PersonHistory {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *PersonHistoryClient) Hooks() []Hook {
	return c.hooks.PersonHistory
}

// Interceptors returns the client interceptors.
func (c *PersonHistoryClient) Interceptors() []Interceptor {
	return c.inters.PersonHistory
}

func (c *PersonHistoryClient) mutate(ctx context.Context, m *PersonHistoryMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&PersonHistoryCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&PersonHistoryUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&PersonHistoryUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&PersonHistoryDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown PersonHistory mutation op: %q", m.Op())
	}
}

// PersonQueryViewClient is a client for the PersonQueryView schema.
type PersonQueryViewClient struct {
	config
}

// NewPersonQueryViewClient returns a client for the PersonQueryView from the given config.
func NewPersonQueryViewClient(c config) *PersonQueryViewClient {
	return &PersonQueryViewClient{config: c}
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `personqueryview.Intercept(f(g(h())))`.
func (c *PersonQueryViewClient) Intercept(interceptors ...Interceptor) {
	c.inters.PersonQueryView = append(c.inters.PersonQueryView, interceptors...)
}

// Query returns a query builder for PersonQueryView.
func (c *PersonQueryViewClient) Query() *PersonQueryViewQuery {
	return &PersonQueryViewQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypePersonQueryView},
		inters: c.Interceptors(),
	}
}

// Interceptors returns the client interceptors.
func (c *PersonQueryViewClient) Interceptors() []Interceptor {
	return c.inters.PersonQueryView
}

// PersonReferralClient is a client for the PersonReferral schema.
type PersonReferralClient struct {
	config
}

// NewPersonReferralClient returns a client for the PersonReferral from the given config.
func NewPersonReferralClient(c config) *PersonReferralClient {
	return &PersonReferralClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `personreferral.Hooks(f(g(h())))`.
func (c *PersonReferralClient) Use(hooks ...Hook) {
	c.hooks.PersonReferral = append(c.hooks.PersonReferral, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `personreferral.Intercept(f(g(h())))`.
func (c *PersonReferralClient) Intercept(interceptors ...Interceptor) {
	c.inters.PersonReferral = append(c.inters.PersonReferral, interceptors...)
}

// Create returns a builder for creating a PersonReferral entity.
func (c *PersonReferralClient) Create() *PersonReferralCreate {
	mutation := newPersonReferralMutation(c.config, OpCreate)
	return &PersonReferralCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of PersonReferral entities.
func (c *PersonReferralClient) CreateBulk(builders ...*PersonReferralCreate) *PersonReferralCreateBulk {
	return &PersonReferralCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *PersonReferralClient) MapCreateBulk(slice any, setFunc func(*PersonReferralCreate, int)) *PersonReferralCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &PersonReferralCreateBulk{err: fmt.Errorf("calling to PersonReferralClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*PersonReferralCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &PersonReferralCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for PersonReferral.
func (c *PersonReferralClient) Update() *PersonReferralUpdate {
	mutation := newPersonReferralMutation(c.config, OpUpdate)
	return &PersonReferralUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *PersonReferralClient) UpdateOne(pr *PersonReferral) *PersonReferralUpdateOne {
	mutation := newPersonReferralMutation(c.config, OpUpdateOne, withPersonReferral(pr))
	return &PersonReferralUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *PersonReferralClient) UpdateOneID(id int) *PersonReferralUpdateOne {
	mutation := newPersonReferralMutation(c.config, OpUpdateOne, withPersonReferralID(id))
	return &PersonReferralUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for PersonReferral.
func (c *PersonReferralClient) Delete() *PersonReferralDelete {
	mutation := newPersonReferralMutation(c.config, OpDelete)
	return &PersonReferralDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *PersonReferralClient) DeleteOne(pr *PersonReferral) *PersonReferralDeleteOne {
	return c.DeleteOneID(pr.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *PersonReferralClient) DeleteOneID(id int) *PersonReferralDeleteOne {
	builder := c.Delete().Where(personreferral.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &PersonReferralDeleteOne{builder}
}

// Query returns a query builder for PersonReferral.
func (c *PersonReferralClient) Query() *PersonReferralQuery {
	return &PersonReferralQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypePersonReferral},
		inters: c.Interceptors(),
	}
}

// Get returns a PersonReferral entity by its id.
func (c *PersonReferralClient) Get(ctx context.Context, id int) (*PersonReferral, error) {
	return c.Query().Where(personreferral.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *PersonReferralClient) GetX(ctx context.Context, id int) *PersonReferral {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryReferred queries the referred edge of a PersonReferral.
func (c *PersonReferralClient) QueryReferred(pr *PersonReferral) *PersonQuery {
	query := (&PersonClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := pr.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(personreferral.Table, personreferral.FieldID, id),
			sqlgraph.To(person.Table, person.FieldID),
			sqlgraph.Edge(sqlgraph.O2O, true, personreferral.ReferredTable, personreferral.ReferredColumn),
		)
		fromV = sqlgraph.Neighbors(pr.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *PersonReferralClient) Hooks() []Hook {
	return c.hooks.PersonReferral
}

// Interceptors returns the client interceptors.
func (c *PersonReferralClient) Interceptors() []Interceptor {
	return c.inters.PersonReferral
}

func (c *PersonReferralClient) mutate(ctx context.Context, m *PersonReferralMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&PersonReferralCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&PersonReferralUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&PersonReferralUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&PersonReferralDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown PersonReferral mutation op: %q", m.Op())
	}
}

// PersonTimelineViewClient is a client for the PersonTimelineView schema.
type PersonTimelineViewClient struct {
	config
}

// NewPersonTimelineViewClient returns a client for the PersonTimelineView from the given config.
func NewPersonTimelineViewClient(c config) *PersonTimelineViewClient {
	return &PersonTimelineViewClient{config: c}
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `persontimelineview.Intercept(f(g(h())))`.
func (c *PersonTimelineViewClient) Intercept(interceptors ...Interceptor) {
	c.inters.PersonTimelineView = append(c.inters.PersonTimelineView, interceptors...)
}

// Query returns a query builder for PersonTimelineView.
func (c *PersonTimelineViewClient) Query() *PersonTimelineViewQuery {
	return &PersonTimelineViewQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypePersonTimelineView},
		inters: c.Interceptors(),
	}
}

// Interceptors returns the client interceptors.
func (c *PersonTimelineViewClient) Interceptors() []Interceptor {
	return c.inters.PersonTimelineView
}

// PhoneViewHistoryClient is a client for the PhoneViewHistory schema.
type PhoneViewHistoryClient struct {
	config
}

// NewPhoneViewHistoryClient returns a client for the PhoneViewHistory from the given config.
func NewPhoneViewHistoryClient(c config) *PhoneViewHistoryClient {
	return &PhoneViewHistoryClient{config: c}
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `phoneviewhistory.Intercept(f(g(h())))`.
func (c *PhoneViewHistoryClient) Intercept(interceptors ...Interceptor) {
	c.inters.PhoneViewHistory = append(c.inters.PhoneViewHistory, interceptors...)
}

// Query returns a query builder for PhoneViewHistory.
func (c *PhoneViewHistoryClient) Query() *PhoneViewHistoryQuery {
	return &PhoneViewHistoryQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypePhoneViewHistory},
		inters: c.Interceptors(),
	}
}

// Interceptors returns the client interceptors.
func (c *PhoneViewHistoryClient) Interceptors() []Interceptor {
	return c.inters.PhoneViewHistory
}

// PipelineClient is a client for the Pipeline schema.
type PipelineClient struct {
	config
}

// NewPipelineClient returns a client for the Pipeline from the given config.
func NewPipelineClient(c config) *PipelineClient {
	return &PipelineClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `pipeline.Hooks(f(g(h())))`.
func (c *PipelineClient) Use(hooks ...Hook) {
	c.hooks.Pipeline = append(c.hooks.Pipeline, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `pipeline.Intercept(f(g(h())))`.
func (c *PipelineClient) Intercept(interceptors ...Interceptor) {
	c.inters.Pipeline = append(c.inters.Pipeline, interceptors...)
}

// Create returns a builder for creating a Pipeline entity.
func (c *PipelineClient) Create() *PipelineCreate {
	mutation := newPipelineMutation(c.config, OpCreate)
	return &PipelineCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Pipeline entities.
func (c *PipelineClient) CreateBulk(builders ...*PipelineCreate) *PipelineCreateBulk {
	return &PipelineCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *PipelineClient) MapCreateBulk(slice any, setFunc func(*PipelineCreate, int)) *PipelineCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &PipelineCreateBulk{err: fmt.Errorf("calling to PipelineClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*PipelineCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &PipelineCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Pipeline.
func (c *PipelineClient) Update() *PipelineUpdate {
	mutation := newPipelineMutation(c.config, OpUpdate)
	return &PipelineUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *PipelineClient) UpdateOne(pi *Pipeline) *PipelineUpdateOne {
	mutation := newPipelineMutation(c.config, OpUpdateOne, withPipeline(pi))
	return &PipelineUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *PipelineClient) UpdateOneID(id int) *PipelineUpdateOne {
	mutation := newPipelineMutation(c.config, OpUpdateOne, withPipelineID(id))
	return &PipelineUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Pipeline.
func (c *PipelineClient) Delete() *PipelineDelete {
	mutation := newPipelineMutation(c.config, OpDelete)
	return &PipelineDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *PipelineClient) DeleteOne(pi *Pipeline) *PipelineDeleteOne {
	return c.DeleteOneID(pi.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *PipelineClient) DeleteOneID(id int) *PipelineDeleteOne {
	builder := c.Delete().Where(pipeline.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &PipelineDeleteOne{builder}
}

// Query returns a query builder for Pipeline.
func (c *PipelineClient) Query() *PipelineQuery {
	return &PipelineQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypePipeline},
		inters: c.Interceptors(),
	}
}

// Get returns a Pipeline entity by its id.
func (c *PipelineClient) Get(ctx context.Context, id int) (*Pipeline, error) {
	return c.Query().Where(pipeline.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *PipelineClient) GetX(ctx context.Context, id int) *Pipeline {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryStages queries the stages edge of a Pipeline.
func (c *PipelineClient) QueryStages(pi *Pipeline) *StageQuery {
	query := (&StageClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := pi.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(pipeline.Table, pipeline.FieldID, id),
			sqlgraph.To(stage.Table, stage.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, pipeline.StagesTable, pipeline.StagesColumn),
		)
		fromV = sqlgraph.Neighbors(pi.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *PipelineClient) Hooks() []Hook {
	hooks := c.hooks.Pipeline
	return append(hooks[:len(hooks):len(hooks)], pipeline.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *PipelineClient) Interceptors() []Interceptor {
	inters := c.inters.Pipeline
	return append(inters[:len(inters):len(inters)], pipeline.Interceptors[:]...)
}

func (c *PipelineClient) mutate(ctx context.Context, m *PipelineMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&PipelineCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&PipelineUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&PipelineUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&PipelineDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Pipeline mutation op: %q", m.Op())
	}
}

// ProductClient is a client for the Product schema.
type ProductClient struct {
	config
}

// NewProductClient returns a client for the Product from the given config.
func NewProductClient(c config) *ProductClient {
	return &ProductClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `product.Hooks(f(g(h())))`.
func (c *ProductClient) Use(hooks ...Hook) {
	c.hooks.Product = append(c.hooks.Product, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `product.Intercept(f(g(h())))`.
func (c *ProductClient) Intercept(interceptors ...Interceptor) {
	c.inters.Product = append(c.inters.Product, interceptors...)
}

// Create returns a builder for creating a Product entity.
func (c *ProductClient) Create() *ProductCreate {
	mutation := newProductMutation(c.config, OpCreate)
	return &ProductCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Product entities.
func (c *ProductClient) CreateBulk(builders ...*ProductCreate) *ProductCreateBulk {
	return &ProductCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *ProductClient) MapCreateBulk(slice any, setFunc func(*ProductCreate, int)) *ProductCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &ProductCreateBulk{err: fmt.Errorf("calling to ProductClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*ProductCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &ProductCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Product.
func (c *ProductClient) Update() *ProductUpdate {
	mutation := newProductMutation(c.config, OpUpdate)
	return &ProductUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *ProductClient) UpdateOne(pr *Product) *ProductUpdateOne {
	mutation := newProductMutation(c.config, OpUpdateOne, withProduct(pr))
	return &ProductUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *ProductClient) UpdateOneID(id int) *ProductUpdateOne {
	mutation := newProductMutation(c.config, OpUpdateOne, withProductID(id))
	return &ProductUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Product.
func (c *ProductClient) Delete() *ProductDelete {
	mutation := newProductMutation(c.config, OpDelete)
	return &ProductDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *ProductClient) DeleteOne(pr *Product) *ProductDeleteOne {
	return c.DeleteOneID(pr.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *ProductClient) DeleteOneID(id int) *ProductDeleteOne {
	builder := c.Delete().Where(product.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &ProductDeleteOne{builder}
}

// Query returns a query builder for Product.
func (c *ProductClient) Query() *ProductQuery {
	return &ProductQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeProduct},
		inters: c.Interceptors(),
	}
}

// Get returns a Product entity by its id.
func (c *ProductClient) Get(ctx context.Context, id int) (*Product, error) {
	return c.Query().Where(product.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *ProductClient) GetX(ctx context.Context, id int) *Product {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryUnit queries the unit edge of a Product.
func (c *ProductClient) QueryUnit(pr *Product) *TermQuery {
	query := (&TermClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := pr.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(product.Table, product.FieldID, id),
			sqlgraph.To(term.Table, term.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, product.UnitTable, product.UnitColumn),
		)
		fromV = sqlgraph.Neighbors(pr.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryGroup queries the group edge of a Product.
func (c *ProductClient) QueryGroup(pr *Product) *TermQuery {
	query := (&TermClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := pr.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(product.Table, product.FieldID, id),
			sqlgraph.To(term.Table, term.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, product.GroupTable, product.GroupColumn),
		)
		fromV = sqlgraph.Neighbors(pr.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryCategory queries the category edge of a Product.
func (c *ProductClient) QueryCategory(pr *Product) *TermQuery {
	query := (&TermClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := pr.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(product.Table, product.FieldID, id),
			sqlgraph.To(term.Table, term.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, product.CategoryTable, product.CategoryColumn),
		)
		fromV = sqlgraph.Neighbors(pr.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryAttachments queries the attachments edge of a Product.
func (c *ProductClient) QueryAttachments(pr *Product) *AttachmentQuery {
	query := (&AttachmentClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := pr.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(product.Table, product.FieldID, id),
			sqlgraph.To(attachment.Table, attachment.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, product.AttachmentsTable, product.AttachmentsColumn),
		)
		fromV = sqlgraph.Neighbors(pr.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryOperations queries the operations edge of a Product.
func (c *ProductClient) QueryOperations(pr *Product) *OperationQuery {
	query := (&OperationClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := pr.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(product.Table, product.FieldID, id),
			sqlgraph.To(operation.Table, operation.FieldID),
			sqlgraph.Edge(sqlgraph.M2M, false, product.OperationsTable, product.OperationsPrimaryKey...),
		)
		fromV = sqlgraph.Neighbors(pr.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryAssignendOperations queries the assignend_operations edge of a Product.
func (c *ProductClient) QueryAssignendOperations(pr *Product) *ProductOperationQuery {
	query := (&ProductOperationClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := pr.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(product.Table, product.FieldID, id),
			sqlgraph.To(productoperation.Table, productoperation.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, product.AssignendOperationsTable, product.AssignendOperationsColumn),
		)
		fromV = sqlgraph.Neighbors(pr.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *ProductClient) Hooks() []Hook {
	hooks := c.hooks.Product
	return append(hooks[:len(hooks):len(hooks)], product.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *ProductClient) Interceptors() []Interceptor {
	inters := c.inters.Product
	return append(inters[:len(inters):len(inters)], product.Interceptors[:]...)
}

func (c *ProductClient) mutate(ctx context.Context, m *ProductMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&ProductCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&ProductUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&ProductUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&ProductDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Product mutation op: %q", m.Op())
	}
}

// ProductOperationClient is a client for the ProductOperation schema.
type ProductOperationClient struct {
	config
}

// NewProductOperationClient returns a client for the ProductOperation from the given config.
func NewProductOperationClient(c config) *ProductOperationClient {
	return &ProductOperationClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `productoperation.Hooks(f(g(h())))`.
func (c *ProductOperationClient) Use(hooks ...Hook) {
	c.hooks.ProductOperation = append(c.hooks.ProductOperation, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `productoperation.Intercept(f(g(h())))`.
func (c *ProductOperationClient) Intercept(interceptors ...Interceptor) {
	c.inters.ProductOperation = append(c.inters.ProductOperation, interceptors...)
}

// Create returns a builder for creating a ProductOperation entity.
func (c *ProductOperationClient) Create() *ProductOperationCreate {
	mutation := newProductOperationMutation(c.config, OpCreate)
	return &ProductOperationCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of ProductOperation entities.
func (c *ProductOperationClient) CreateBulk(builders ...*ProductOperationCreate) *ProductOperationCreateBulk {
	return &ProductOperationCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *ProductOperationClient) MapCreateBulk(slice any, setFunc func(*ProductOperationCreate, int)) *ProductOperationCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &ProductOperationCreateBulk{err: fmt.Errorf("calling to ProductOperationClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*ProductOperationCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &ProductOperationCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for ProductOperation.
func (c *ProductOperationClient) Update() *ProductOperationUpdate {
	mutation := newProductOperationMutation(c.config, OpUpdate)
	return &ProductOperationUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *ProductOperationClient) UpdateOne(po *ProductOperation) *ProductOperationUpdateOne {
	mutation := newProductOperationMutation(c.config, OpUpdateOne, withProductOperation(po))
	return &ProductOperationUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *ProductOperationClient) UpdateOneID(id int) *ProductOperationUpdateOne {
	mutation := newProductOperationMutation(c.config, OpUpdateOne, withProductOperationID(id))
	return &ProductOperationUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for ProductOperation.
func (c *ProductOperationClient) Delete() *ProductOperationDelete {
	mutation := newProductOperationMutation(c.config, OpDelete)
	return &ProductOperationDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *ProductOperationClient) DeleteOne(po *ProductOperation) *ProductOperationDeleteOne {
	return c.DeleteOneID(po.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *ProductOperationClient) DeleteOneID(id int) *ProductOperationDeleteOne {
	builder := c.Delete().Where(productoperation.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &ProductOperationDeleteOne{builder}
}

// Query returns a query builder for ProductOperation.
func (c *ProductOperationClient) Query() *ProductOperationQuery {
	return &ProductOperationQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeProductOperation},
		inters: c.Interceptors(),
	}
}

// Get returns a ProductOperation entity by its id.
func (c *ProductOperationClient) Get(ctx context.Context, id int) (*ProductOperation, error) {
	return c.Query().Where(productoperation.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *ProductOperationClient) GetX(ctx context.Context, id int) *ProductOperation {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryProduct queries the product edge of a ProductOperation.
func (c *ProductOperationClient) QueryProduct(po *ProductOperation) *ProductQuery {
	query := (&ProductClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := po.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(productoperation.Table, productoperation.FieldID, id),
			sqlgraph.To(product.Table, product.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, productoperation.ProductTable, productoperation.ProductColumn),
		)
		fromV = sqlgraph.Neighbors(po.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryOperation queries the operation edge of a ProductOperation.
func (c *ProductOperationClient) QueryOperation(po *ProductOperation) *OperationQuery {
	query := (&OperationClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := po.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(productoperation.Table, productoperation.FieldID, id),
			sqlgraph.To(operation.Table, operation.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, productoperation.OperationTable, productoperation.OperationColumn),
		)
		fromV = sqlgraph.Neighbors(po.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *ProductOperationClient) Hooks() []Hook {
	hooks := c.hooks.ProductOperation
	return append(hooks[:len(hooks):len(hooks)], productoperation.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *ProductOperationClient) Interceptors() []Interceptor {
	return c.inters.ProductOperation
}

func (c *ProductOperationClient) mutate(ctx context.Context, m *ProductOperationMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&ProductOperationCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&ProductOperationUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&ProductOperationUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&ProductOperationDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown ProductOperation mutation op: %q", m.Op())
	}
}

// ReferralClient is a client for the Referral schema.
type ReferralClient struct {
	config
}

// NewReferralClient returns a client for the Referral from the given config.
func NewReferralClient(c config) *ReferralClient {
	return &ReferralClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `referral.Hooks(f(g(h())))`.
func (c *ReferralClient) Use(hooks ...Hook) {
	c.hooks.Referral = append(c.hooks.Referral, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `referral.Intercept(f(g(h())))`.
func (c *ReferralClient) Intercept(interceptors ...Interceptor) {
	c.inters.Referral = append(c.inters.Referral, interceptors...)
}

// Create returns a builder for creating a Referral entity.
func (c *ReferralClient) Create() *ReferralCreate {
	mutation := newReferralMutation(c.config, OpCreate)
	return &ReferralCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Referral entities.
func (c *ReferralClient) CreateBulk(builders ...*ReferralCreate) *ReferralCreateBulk {
	return &ReferralCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *ReferralClient) MapCreateBulk(slice any, setFunc func(*ReferralCreate, int)) *ReferralCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &ReferralCreateBulk{err: fmt.Errorf("calling to ReferralClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*ReferralCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &ReferralCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Referral.
func (c *ReferralClient) Update() *ReferralUpdate {
	mutation := newReferralMutation(c.config, OpUpdate)
	return &ReferralUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *ReferralClient) UpdateOne(r *Referral) *ReferralUpdateOne {
	mutation := newReferralMutation(c.config, OpUpdateOne, withReferral(r))
	return &ReferralUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *ReferralClient) UpdateOneID(id int) *ReferralUpdateOne {
	mutation := newReferralMutation(c.config, OpUpdateOne, withReferralID(id))
	return &ReferralUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Referral.
func (c *ReferralClient) Delete() *ReferralDelete {
	mutation := newReferralMutation(c.config, OpDelete)
	return &ReferralDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *ReferralClient) DeleteOne(r *Referral) *ReferralDeleteOne {
	return c.DeleteOneID(r.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *ReferralClient) DeleteOneID(id int) *ReferralDeleteOne {
	builder := c.Delete().Where(referral.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &ReferralDeleteOne{builder}
}

// Query returns a query builder for Referral.
func (c *ReferralClient) Query() *ReferralQuery {
	return &ReferralQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeReferral},
		inters: c.Interceptors(),
	}
}

// Get returns a Referral entity by its id.
func (c *ReferralClient) Get(ctx context.Context, id int) (*Referral, error) {
	return c.Query().Where(referral.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *ReferralClient) GetX(ctx context.Context, id int) *Referral {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *ReferralClient) Hooks() []Hook {
	hooks := c.hooks.Referral
	return append(hooks[:len(hooks):len(hooks)], referral.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *ReferralClient) Interceptors() []Interceptor {
	inters := c.inters.Referral
	return append(inters[:len(inters):len(inters)], referral.Interceptors[:]...)
}

func (c *ReferralClient) mutate(ctx context.Context, m *ReferralMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&ReferralCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&ReferralUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&ReferralUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&ReferralDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Referral mutation op: %q", m.Op())
	}
}

// ScheduleClient is a client for the Schedule schema.
type ScheduleClient struct {
	config
}

// NewScheduleClient returns a client for the Schedule from the given config.
func NewScheduleClient(c config) *ScheduleClient {
	return &ScheduleClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `schedule.Hooks(f(g(h())))`.
func (c *ScheduleClient) Use(hooks ...Hook) {
	c.hooks.Schedule = append(c.hooks.Schedule, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `schedule.Intercept(f(g(h())))`.
func (c *ScheduleClient) Intercept(interceptors ...Interceptor) {
	c.inters.Schedule = append(c.inters.Schedule, interceptors...)
}

// Create returns a builder for creating a Schedule entity.
func (c *ScheduleClient) Create() *ScheduleCreate {
	mutation := newScheduleMutation(c.config, OpCreate)
	return &ScheduleCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Schedule entities.
func (c *ScheduleClient) CreateBulk(builders ...*ScheduleCreate) *ScheduleCreateBulk {
	return &ScheduleCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *ScheduleClient) MapCreateBulk(slice any, setFunc func(*ScheduleCreate, int)) *ScheduleCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &ScheduleCreateBulk{err: fmt.Errorf("calling to ScheduleClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*ScheduleCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &ScheduleCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Schedule.
func (c *ScheduleClient) Update() *ScheduleUpdate {
	mutation := newScheduleMutation(c.config, OpUpdate)
	return &ScheduleUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *ScheduleClient) UpdateOne(s *Schedule) *ScheduleUpdateOne {
	mutation := newScheduleMutation(c.config, OpUpdateOne, withSchedule(s))
	return &ScheduleUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *ScheduleClient) UpdateOneID(id int) *ScheduleUpdateOne {
	mutation := newScheduleMutation(c.config, OpUpdateOne, withScheduleID(id))
	return &ScheduleUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Schedule.
func (c *ScheduleClient) Delete() *ScheduleDelete {
	mutation := newScheduleMutation(c.config, OpDelete)
	return &ScheduleDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *ScheduleClient) DeleteOne(s *Schedule) *ScheduleDeleteOne {
	return c.DeleteOneID(s.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *ScheduleClient) DeleteOneID(id int) *ScheduleDeleteOne {
	builder := c.Delete().Where(schedule.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &ScheduleDeleteOne{builder}
}

// Query returns a query builder for Schedule.
func (c *ScheduleClient) Query() *ScheduleQuery {
	return &ScheduleQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeSchedule},
		inters: c.Interceptors(),
	}
}

// Get returns a Schedule entity by its id.
func (c *ScheduleClient) Get(ctx context.Context, id int) (*Schedule, error) {
	return c.Query().Where(schedule.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *ScheduleClient) GetX(ctx context.Context, id int) *Schedule {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryUser queries the user edge of a Schedule.
func (c *ScheduleClient) QueryUser(s *Schedule) *UserQuery {
	query := (&UserClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := s.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(schedule.Table, schedule.FieldID, id),
			sqlgraph.To(user.Table, user.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, schedule.UserTable, schedule.UserColumn),
		)
		fromV = sqlgraph.Neighbors(s.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *ScheduleClient) Hooks() []Hook {
	hooks := c.hooks.Schedule
	return append(hooks[:len(hooks):len(hooks)], schedule.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *ScheduleClient) Interceptors() []Interceptor {
	inters := c.inters.Schedule
	return append(inters[:len(inters):len(inters)], schedule.Interceptors[:]...)
}

func (c *ScheduleClient) mutate(ctx context.Context, m *ScheduleMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&ScheduleCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&ScheduleUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&ScheduleUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&ScheduleDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Schedule mutation op: %q", m.Op())
	}
}

// SettingClient is a client for the Setting schema.
type SettingClient struct {
	config
}

// NewSettingClient returns a client for the Setting from the given config.
func NewSettingClient(c config) *SettingClient {
	return &SettingClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `setting.Hooks(f(g(h())))`.
func (c *SettingClient) Use(hooks ...Hook) {
	c.hooks.Setting = append(c.hooks.Setting, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `setting.Intercept(f(g(h())))`.
func (c *SettingClient) Intercept(interceptors ...Interceptor) {
	c.inters.Setting = append(c.inters.Setting, interceptors...)
}

// Create returns a builder for creating a Setting entity.
func (c *SettingClient) Create() *SettingCreate {
	mutation := newSettingMutation(c.config, OpCreate)
	return &SettingCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Setting entities.
func (c *SettingClient) CreateBulk(builders ...*SettingCreate) *SettingCreateBulk {
	return &SettingCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *SettingClient) MapCreateBulk(slice any, setFunc func(*SettingCreate, int)) *SettingCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &SettingCreateBulk{err: fmt.Errorf("calling to SettingClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*SettingCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &SettingCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Setting.
func (c *SettingClient) Update() *SettingUpdate {
	mutation := newSettingMutation(c.config, OpUpdate)
	return &SettingUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *SettingClient) UpdateOne(s *Setting) *SettingUpdateOne {
	mutation := newSettingMutation(c.config, OpUpdateOne, withSetting(s))
	return &SettingUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *SettingClient) UpdateOneID(id int) *SettingUpdateOne {
	mutation := newSettingMutation(c.config, OpUpdateOne, withSettingID(id))
	return &SettingUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Setting.
func (c *SettingClient) Delete() *SettingDelete {
	mutation := newSettingMutation(c.config, OpDelete)
	return &SettingDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *SettingClient) DeleteOne(s *Setting) *SettingDeleteOne {
	return c.DeleteOneID(s.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *SettingClient) DeleteOneID(id int) *SettingDeleteOne {
	builder := c.Delete().Where(setting.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &SettingDeleteOne{builder}
}

// Query returns a query builder for Setting.
func (c *SettingClient) Query() *SettingQuery {
	return &SettingQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeSetting},
		inters: c.Interceptors(),
	}
}

// Get returns a Setting entity by its id.
func (c *SettingClient) Get(ctx context.Context, id int) (*Setting, error) {
	return c.Query().Where(setting.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *SettingClient) GetX(ctx context.Context, id int) *Setting {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *SettingClient) Hooks() []Hook {
	hooks := c.hooks.Setting
	return append(hooks[:len(hooks):len(hooks)], setting.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *SettingClient) Interceptors() []Interceptor {
	inters := c.inters.Setting
	return append(inters[:len(inters):len(inters)], setting.Interceptors[:]...)
}

func (c *SettingClient) mutate(ctx context.Context, m *SettingMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&SettingCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&SettingUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&SettingUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&SettingDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Setting mutation op: %q", m.Op())
	}
}

// StageClient is a client for the Stage schema.
type StageClient struct {
	config
}

// NewStageClient returns a client for the Stage from the given config.
func NewStageClient(c config) *StageClient {
	return &StageClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `stage.Hooks(f(g(h())))`.
func (c *StageClient) Use(hooks ...Hook) {
	c.hooks.Stage = append(c.hooks.Stage, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `stage.Intercept(f(g(h())))`.
func (c *StageClient) Intercept(interceptors ...Interceptor) {
	c.inters.Stage = append(c.inters.Stage, interceptors...)
}

// Create returns a builder for creating a Stage entity.
func (c *StageClient) Create() *StageCreate {
	mutation := newStageMutation(c.config, OpCreate)
	return &StageCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Stage entities.
func (c *StageClient) CreateBulk(builders ...*StageCreate) *StageCreateBulk {
	return &StageCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *StageClient) MapCreateBulk(slice any, setFunc func(*StageCreate, int)) *StageCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &StageCreateBulk{err: fmt.Errorf("calling to StageClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*StageCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &StageCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Stage.
func (c *StageClient) Update() *StageUpdate {
	mutation := newStageMutation(c.config, OpUpdate)
	return &StageUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *StageClient) UpdateOne(s *Stage) *StageUpdateOne {
	mutation := newStageMutation(c.config, OpUpdateOne, withStage(s))
	return &StageUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *StageClient) UpdateOneID(id int) *StageUpdateOne {
	mutation := newStageMutation(c.config, OpUpdateOne, withStageID(id))
	return &StageUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Stage.
func (c *StageClient) Delete() *StageDelete {
	mutation := newStageMutation(c.config, OpDelete)
	return &StageDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *StageClient) DeleteOne(s *Stage) *StageDeleteOne {
	return c.DeleteOneID(s.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *StageClient) DeleteOneID(id int) *StageDeleteOne {
	builder := c.Delete().Where(stage.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &StageDeleteOne{builder}
}

// Query returns a query builder for Stage.
func (c *StageClient) Query() *StageQuery {
	return &StageQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeStage},
		inters: c.Interceptors(),
	}
}

// Get returns a Stage entity by its id.
func (c *StageClient) Get(ctx context.Context, id int) (*Stage, error) {
	return c.Query().Where(stage.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *StageClient) GetX(ctx context.Context, id int) *Stage {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryParent queries the parent edge of a Stage.
func (c *StageClient) QueryParent(s *Stage) *StageQuery {
	query := (&StageClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := s.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(stage.Table, stage.FieldID, id),
			sqlgraph.To(stage.Table, stage.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, stage.ParentTable, stage.ParentColumn),
		)
		fromV = sqlgraph.Neighbors(s.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryChildren queries the children edge of a Stage.
func (c *StageClient) QueryChildren(s *Stage) *StageQuery {
	query := (&StageClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := s.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(stage.Table, stage.FieldID, id),
			sqlgraph.To(stage.Table, stage.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, stage.ChildrenTable, stage.ChildrenColumn),
		)
		fromV = sqlgraph.Neighbors(s.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryDeals queries the deals edge of a Stage.
func (c *StageClient) QueryDeals(s *Stage) *DealQuery {
	query := (&DealClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := s.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(stage.Table, stage.FieldID, id),
			sqlgraph.To(deal.Table, deal.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, stage.DealsTable, stage.DealsColumn),
		)
		fromV = sqlgraph.Neighbors(s.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryPipeline queries the pipeline edge of a Stage.
func (c *StageClient) QueryPipeline(s *Stage) *PipelineQuery {
	query := (&PipelineClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := s.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(stage.Table, stage.FieldID, id),
			sqlgraph.To(pipeline.Table, pipeline.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, stage.PipelineTable, stage.PipelineColumn),
		)
		fromV = sqlgraph.Neighbors(s.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *StageClient) Hooks() []Hook {
	hooks := c.hooks.Stage
	return append(hooks[:len(hooks):len(hooks)], stage.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *StageClient) Interceptors() []Interceptor {
	inters := c.inters.Stage
	return append(inters[:len(inters):len(inters)], stage.Interceptors[:]...)
}

func (c *StageClient) mutate(ctx context.Context, m *StageMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&StageCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&StageUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&StageUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&StageDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Stage mutation op: %q", m.Op())
	}
}

// TagClient is a client for the Tag schema.
type TagClient struct {
	config
}

// NewTagClient returns a client for the Tag from the given config.
func NewTagClient(c config) *TagClient {
	return &TagClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `tag.Hooks(f(g(h())))`.
func (c *TagClient) Use(hooks ...Hook) {
	c.hooks.Tag = append(c.hooks.Tag, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `tag.Intercept(f(g(h())))`.
func (c *TagClient) Intercept(interceptors ...Interceptor) {
	c.inters.Tag = append(c.inters.Tag, interceptors...)
}

// Create returns a builder for creating a Tag entity.
func (c *TagClient) Create() *TagCreate {
	mutation := newTagMutation(c.config, OpCreate)
	return &TagCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Tag entities.
func (c *TagClient) CreateBulk(builders ...*TagCreate) *TagCreateBulk {
	return &TagCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *TagClient) MapCreateBulk(slice any, setFunc func(*TagCreate, int)) *TagCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &TagCreateBulk{err: fmt.Errorf("calling to TagClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*TagCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &TagCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Tag.
func (c *TagClient) Update() *TagUpdate {
	mutation := newTagMutation(c.config, OpUpdate)
	return &TagUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *TagClient) UpdateOne(t *Tag) *TagUpdateOne {
	mutation := newTagMutation(c.config, OpUpdateOne, withTag(t))
	return &TagUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *TagClient) UpdateOneID(id int) *TagUpdateOne {
	mutation := newTagMutation(c.config, OpUpdateOne, withTagID(id))
	return &TagUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Tag.
func (c *TagClient) Delete() *TagDelete {
	mutation := newTagMutation(c.config, OpDelete)
	return &TagDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *TagClient) DeleteOne(t *Tag) *TagDeleteOne {
	return c.DeleteOneID(t.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *TagClient) DeleteOneID(id int) *TagDeleteOne {
	builder := c.Delete().Where(tag.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &TagDeleteOne{builder}
}

// Query returns a query builder for Tag.
func (c *TagClient) Query() *TagQuery {
	return &TagQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeTag},
		inters: c.Interceptors(),
	}
}

// Get returns a Tag entity by its id.
func (c *TagClient) Get(ctx context.Context, id int) (*Tag, error) {
	return c.Query().Where(tag.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *TagClient) GetX(ctx context.Context, id int) *Tag {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryPersonTags queries the person_tags edge of a Tag.
func (c *TagClient) QueryPersonTags(t *Tag) *TagPersonQuery {
	query := (&TagPersonClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := t.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(tag.Table, tag.FieldID, id),
			sqlgraph.To(tagperson.Table, tagperson.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, tag.PersonTagsTable, tag.PersonTagsColumn),
		)
		fromV = sqlgraph.Neighbors(t.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryDealTags queries the deal_tags edge of a Tag.
func (c *TagClient) QueryDealTags(t *Tag) *TagDealQuery {
	query := (&TagDealClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := t.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(tag.Table, tag.FieldID, id),
			sqlgraph.To(tagdeal.Table, tagdeal.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, tag.DealTagsTable, tag.DealTagsColumn),
		)
		fromV = sqlgraph.Neighbors(t.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryTagsPerson queries the tags_person edge of a Tag.
func (c *TagClient) QueryTagsPerson(t *Tag) *PersonQuery {
	query := (&PersonClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := t.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(tag.Table, tag.FieldID, id),
			sqlgraph.To(person.Table, person.FieldID),
			sqlgraph.Edge(sqlgraph.M2M, true, tag.TagsPersonTable, tag.TagsPersonPrimaryKey...),
		)
		fromV = sqlgraph.Neighbors(t.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryTagsDeal queries the tags_deal edge of a Tag.
func (c *TagClient) QueryTagsDeal(t *Tag) *DealQuery {
	query := (&DealClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := t.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(tag.Table, tag.FieldID, id),
			sqlgraph.To(deal.Table, deal.FieldID),
			sqlgraph.Edge(sqlgraph.M2M, true, tag.TagsDealTable, tag.TagsDealPrimaryKey...),
		)
		fromV = sqlgraph.Neighbors(t.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryTagPerson queries the tag_person edge of a Tag.
func (c *TagClient) QueryTagPerson(t *Tag) *TagPersonQuery {
	query := (&TagPersonClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := t.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(tag.Table, tag.FieldID, id),
			sqlgraph.To(tagperson.Table, tagperson.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, tag.TagPersonTable, tag.TagPersonColumn),
		)
		fromV = sqlgraph.Neighbors(t.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryTagDeal queries the tag_deal edge of a Tag.
func (c *TagClient) QueryTagDeal(t *Tag) *TagDealQuery {
	query := (&TagDealClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := t.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(tag.Table, tag.FieldID, id),
			sqlgraph.To(tagdeal.Table, tagdeal.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, tag.TagDealTable, tag.TagDealColumn),
		)
		fromV = sqlgraph.Neighbors(t.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *TagClient) Hooks() []Hook {
	hooks := c.hooks.Tag
	return append(hooks[:len(hooks):len(hooks)], tag.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *TagClient) Interceptors() []Interceptor {
	inters := c.inters.Tag
	return append(inters[:len(inters):len(inters)], tag.Interceptors[:]...)
}

func (c *TagClient) mutate(ctx context.Context, m *TagMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&TagCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&TagUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&TagUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&TagDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Tag mutation op: %q", m.Op())
	}
}

// TagDealClient is a client for the TagDeal schema.
type TagDealClient struct {
	config
}

// NewTagDealClient returns a client for the TagDeal from the given config.
func NewTagDealClient(c config) *TagDealClient {
	return &TagDealClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `tagdeal.Hooks(f(g(h())))`.
func (c *TagDealClient) Use(hooks ...Hook) {
	c.hooks.TagDeal = append(c.hooks.TagDeal, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `tagdeal.Intercept(f(g(h())))`.
func (c *TagDealClient) Intercept(interceptors ...Interceptor) {
	c.inters.TagDeal = append(c.inters.TagDeal, interceptors...)
}

// Create returns a builder for creating a TagDeal entity.
func (c *TagDealClient) Create() *TagDealCreate {
	mutation := newTagDealMutation(c.config, OpCreate)
	return &TagDealCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of TagDeal entities.
func (c *TagDealClient) CreateBulk(builders ...*TagDealCreate) *TagDealCreateBulk {
	return &TagDealCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *TagDealClient) MapCreateBulk(slice any, setFunc func(*TagDealCreate, int)) *TagDealCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &TagDealCreateBulk{err: fmt.Errorf("calling to TagDealClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*TagDealCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &TagDealCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for TagDeal.
func (c *TagDealClient) Update() *TagDealUpdate {
	mutation := newTagDealMutation(c.config, OpUpdate)
	return &TagDealUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *TagDealClient) UpdateOne(td *TagDeal) *TagDealUpdateOne {
	mutation := newTagDealMutation(c.config, OpUpdateOne, withTagDeal(td))
	return &TagDealUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *TagDealClient) UpdateOneID(id int) *TagDealUpdateOne {
	mutation := newTagDealMutation(c.config, OpUpdateOne, withTagDealID(id))
	return &TagDealUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for TagDeal.
func (c *TagDealClient) Delete() *TagDealDelete {
	mutation := newTagDealMutation(c.config, OpDelete)
	return &TagDealDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *TagDealClient) DeleteOne(td *TagDeal) *TagDealDeleteOne {
	return c.DeleteOneID(td.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *TagDealClient) DeleteOneID(id int) *TagDealDeleteOne {
	builder := c.Delete().Where(tagdeal.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &TagDealDeleteOne{builder}
}

// Query returns a query builder for TagDeal.
func (c *TagDealClient) Query() *TagDealQuery {
	return &TagDealQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeTagDeal},
		inters: c.Interceptors(),
	}
}

// Get returns a TagDeal entity by its id.
func (c *TagDealClient) Get(ctx context.Context, id int) (*TagDeal, error) {
	return c.Query().Where(tagdeal.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *TagDealClient) GetX(ctx context.Context, id int) *TagDeal {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryDeal queries the deal edge of a TagDeal.
func (c *TagDealClient) QueryDeal(td *TagDeal) *DealQuery {
	query := (&DealClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := td.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(tagdeal.Table, tagdeal.FieldID, id),
			sqlgraph.To(deal.Table, deal.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, tagdeal.DealTable, tagdeal.DealColumn),
		)
		fromV = sqlgraph.Neighbors(td.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryTag queries the tag edge of a TagDeal.
func (c *TagDealClient) QueryTag(td *TagDeal) *TagQuery {
	query := (&TagClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := td.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(tagdeal.Table, tagdeal.FieldID, id),
			sqlgraph.To(tag.Table, tag.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, tagdeal.TagTable, tagdeal.TagColumn),
		)
		fromV = sqlgraph.Neighbors(td.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *TagDealClient) Hooks() []Hook {
	return c.hooks.TagDeal
}

// Interceptors returns the client interceptors.
func (c *TagDealClient) Interceptors() []Interceptor {
	return c.inters.TagDeal
}

func (c *TagDealClient) mutate(ctx context.Context, m *TagDealMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&TagDealCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&TagDealUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&TagDealUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&TagDealDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown TagDeal mutation op: %q", m.Op())
	}
}

// TagPersonClient is a client for the TagPerson schema.
type TagPersonClient struct {
	config
}

// NewTagPersonClient returns a client for the TagPerson from the given config.
func NewTagPersonClient(c config) *TagPersonClient {
	return &TagPersonClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `tagperson.Hooks(f(g(h())))`.
func (c *TagPersonClient) Use(hooks ...Hook) {
	c.hooks.TagPerson = append(c.hooks.TagPerson, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `tagperson.Intercept(f(g(h())))`.
func (c *TagPersonClient) Intercept(interceptors ...Interceptor) {
	c.inters.TagPerson = append(c.inters.TagPerson, interceptors...)
}

// Create returns a builder for creating a TagPerson entity.
func (c *TagPersonClient) Create() *TagPersonCreate {
	mutation := newTagPersonMutation(c.config, OpCreate)
	return &TagPersonCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of TagPerson entities.
func (c *TagPersonClient) CreateBulk(builders ...*TagPersonCreate) *TagPersonCreateBulk {
	return &TagPersonCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *TagPersonClient) MapCreateBulk(slice any, setFunc func(*TagPersonCreate, int)) *TagPersonCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &TagPersonCreateBulk{err: fmt.Errorf("calling to TagPersonClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*TagPersonCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &TagPersonCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for TagPerson.
func (c *TagPersonClient) Update() *TagPersonUpdate {
	mutation := newTagPersonMutation(c.config, OpUpdate)
	return &TagPersonUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *TagPersonClient) UpdateOne(tp *TagPerson) *TagPersonUpdateOne {
	mutation := newTagPersonMutation(c.config, OpUpdateOne, withTagPerson(tp))
	return &TagPersonUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *TagPersonClient) UpdateOneID(id int) *TagPersonUpdateOne {
	mutation := newTagPersonMutation(c.config, OpUpdateOne, withTagPersonID(id))
	return &TagPersonUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for TagPerson.
func (c *TagPersonClient) Delete() *TagPersonDelete {
	mutation := newTagPersonMutation(c.config, OpDelete)
	return &TagPersonDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *TagPersonClient) DeleteOne(tp *TagPerson) *TagPersonDeleteOne {
	return c.DeleteOneID(tp.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *TagPersonClient) DeleteOneID(id int) *TagPersonDeleteOne {
	builder := c.Delete().Where(tagperson.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &TagPersonDeleteOne{builder}
}

// Query returns a query builder for TagPerson.
func (c *TagPersonClient) Query() *TagPersonQuery {
	return &TagPersonQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeTagPerson},
		inters: c.Interceptors(),
	}
}

// Get returns a TagPerson entity by its id.
func (c *TagPersonClient) Get(ctx context.Context, id int) (*TagPerson, error) {
	return c.Query().Where(tagperson.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *TagPersonClient) GetX(ctx context.Context, id int) *TagPerson {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryPerson queries the person edge of a TagPerson.
func (c *TagPersonClient) QueryPerson(tp *TagPerson) *PersonQuery {
	query := (&PersonClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := tp.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(tagperson.Table, tagperson.FieldID, id),
			sqlgraph.To(person.Table, person.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, tagperson.PersonTable, tagperson.PersonColumn),
		)
		fromV = sqlgraph.Neighbors(tp.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryTag queries the tag edge of a TagPerson.
func (c *TagPersonClient) QueryTag(tp *TagPerson) *TagQuery {
	query := (&TagClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := tp.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(tagperson.Table, tagperson.FieldID, id),
			sqlgraph.To(tag.Table, tag.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, tagperson.TagTable, tagperson.TagColumn),
		)
		fromV = sqlgraph.Neighbors(tp.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *TagPersonClient) Hooks() []Hook {
	return c.hooks.TagPerson
}

// Interceptors returns the client interceptors.
func (c *TagPersonClient) Interceptors() []Interceptor {
	return c.inters.TagPerson
}

func (c *TagPersonClient) mutate(ctx context.Context, m *TagPersonMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&TagPersonCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&TagPersonUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&TagPersonUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&TagPersonDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown TagPerson mutation op: %q", m.Op())
	}
}

// TaskClient is a client for the Task schema.
type TaskClient struct {
	config
}

// NewTaskClient returns a client for the Task from the given config.
func NewTaskClient(c config) *TaskClient {
	return &TaskClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `task.Hooks(f(g(h())))`.
func (c *TaskClient) Use(hooks ...Hook) {
	c.hooks.Task = append(c.hooks.Task, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `task.Intercept(f(g(h())))`.
func (c *TaskClient) Intercept(interceptors ...Interceptor) {
	c.inters.Task = append(c.inters.Task, interceptors...)
}

// Create returns a builder for creating a Task entity.
func (c *TaskClient) Create() *TaskCreate {
	mutation := newTaskMutation(c.config, OpCreate)
	return &TaskCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Task entities.
func (c *TaskClient) CreateBulk(builders ...*TaskCreate) *TaskCreateBulk {
	return &TaskCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *TaskClient) MapCreateBulk(slice any, setFunc func(*TaskCreate, int)) *TaskCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &TaskCreateBulk{err: fmt.Errorf("calling to TaskClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*TaskCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &TaskCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Task.
func (c *TaskClient) Update() *TaskUpdate {
	mutation := newTaskMutation(c.config, OpUpdate)
	return &TaskUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *TaskClient) UpdateOne(t *Task) *TaskUpdateOne {
	mutation := newTaskMutation(c.config, OpUpdateOne, withTask(t))
	return &TaskUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *TaskClient) UpdateOneID(id int) *TaskUpdateOne {
	mutation := newTaskMutation(c.config, OpUpdateOne, withTaskID(id))
	return &TaskUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Task.
func (c *TaskClient) Delete() *TaskDelete {
	mutation := newTaskMutation(c.config, OpDelete)
	return &TaskDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *TaskClient) DeleteOne(t *Task) *TaskDeleteOne {
	return c.DeleteOneID(t.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *TaskClient) DeleteOneID(id int) *TaskDeleteOne {
	builder := c.Delete().Where(task.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &TaskDeleteOne{builder}
}

// Query returns a query builder for Task.
func (c *TaskClient) Query() *TaskQuery {
	return &TaskQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeTask},
		inters: c.Interceptors(),
	}
}

// Get returns a Task entity by its id.
func (c *TaskClient) Get(ctx context.Context, id int) (*Task, error) {
	return c.Query().Where(task.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *TaskClient) GetX(ctx context.Context, id int) *Task {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryParent queries the parent edge of a Task.
func (c *TaskClient) QueryParent(t *Task) *TaskQuery {
	query := (&TaskClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := t.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(task.Table, task.FieldID, id),
			sqlgraph.To(task.Table, task.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, task.ParentTable, task.ParentColumn),
		)
		fromV = sqlgraph.Neighbors(t.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryChildren queries the children edge of a Task.
func (c *TaskClient) QueryChildren(t *Task) *TaskQuery {
	query := (&TaskClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := t.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(task.Table, task.FieldID, id),
			sqlgraph.To(task.Table, task.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, task.ChildrenTable, task.ChildrenColumn),
		)
		fromV = sqlgraph.Neighbors(t.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryPerson queries the person edge of a Task.
func (c *TaskClient) QueryPerson(t *Task) *PersonQuery {
	query := (&PersonClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := t.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(task.Table, task.FieldID, id),
			sqlgraph.To(person.Table, person.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, task.PersonTable, task.PersonColumn),
		)
		fromV = sqlgraph.Neighbors(t.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryDeal queries the deal edge of a Task.
func (c *TaskClient) QueryDeal(t *Task) *DealQuery {
	query := (&DealClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := t.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(task.Table, task.FieldID, id),
			sqlgraph.To(deal.Table, deal.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, task.DealTable, task.DealColumn),
		)
		fromV = sqlgraph.Neighbors(t.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryAppointment queries the appointment edge of a Task.
func (c *TaskClient) QueryAppointment(t *Task) *AppointmentQuery {
	query := (&AppointmentClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := t.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(task.Table, task.FieldID, id),
			sqlgraph.To(appointment.Table, appointment.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, task.AppointmentTable, task.AppointmentColumn),
		)
		fromV = sqlgraph.Neighbors(t.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryCreator queries the creator edge of a Task.
func (c *TaskClient) QueryCreator(t *Task) *UserQuery {
	query := (&UserClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := t.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(task.Table, task.FieldID, id),
			sqlgraph.To(user.Table, user.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, task.CreatorTable, task.CreatorColumn),
		)
		fromV = sqlgraph.Neighbors(t.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryAssignments queries the assignments edge of a Task.
func (c *TaskClient) QueryAssignments(t *Task) *TaskAssignmentQuery {
	query := (&TaskAssignmentClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := t.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(task.Table, task.FieldID, id),
			sqlgraph.To(taskassignment.Table, taskassignment.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, task.AssignmentsTable, task.AssignmentsColumn),
		)
		fromV = sqlgraph.Neighbors(t.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryDepartmentAssignments queries the department_assignments edge of a Task.
func (c *TaskClient) QueryDepartmentAssignments(t *Task) *TaskDepartmentQuery {
	query := (&TaskDepartmentClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := t.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(task.Table, task.FieldID, id),
			sqlgraph.To(taskdepartment.Table, taskdepartment.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, task.DepartmentAssignmentsTable, task.DepartmentAssignmentsColumn),
		)
		fromV = sqlgraph.Neighbors(t.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryNotes queries the notes edge of a Task.
func (c *TaskClient) QueryNotes(t *Task) *TaskNoteQuery {
	query := (&TaskNoteClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := t.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(task.Table, task.FieldID, id),
			sqlgraph.To(tasknote.Table, tasknote.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, task.NotesTable, task.NotesColumn),
		)
		fromV = sqlgraph.Neighbors(t.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryRecurring queries the recurring edge of a Task.
func (c *TaskClient) QueryRecurring(t *Task) *TaskRecurringQuery {
	query := (&TaskRecurringClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := t.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(task.Table, task.FieldID, id),
			sqlgraph.To(taskrecurring.Table, taskrecurring.FieldID),
			sqlgraph.Edge(sqlgraph.O2O, false, task.RecurringTable, task.RecurringColumn),
		)
		fromV = sqlgraph.Neighbors(t.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *TaskClient) Hooks() []Hook {
	hooks := c.hooks.Task
	return append(hooks[:len(hooks):len(hooks)], task.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *TaskClient) Interceptors() []Interceptor {
	inters := c.inters.Task
	return append(inters[:len(inters):len(inters)], task.Interceptors[:]...)
}

func (c *TaskClient) mutate(ctx context.Context, m *TaskMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&TaskCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&TaskUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&TaskUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&TaskDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Task mutation op: %q", m.Op())
	}
}

// TaskAssignmentClient is a client for the TaskAssignment schema.
type TaskAssignmentClient struct {
	config
}

// NewTaskAssignmentClient returns a client for the TaskAssignment from the given config.
func NewTaskAssignmentClient(c config) *TaskAssignmentClient {
	return &TaskAssignmentClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `taskassignment.Hooks(f(g(h())))`.
func (c *TaskAssignmentClient) Use(hooks ...Hook) {
	c.hooks.TaskAssignment = append(c.hooks.TaskAssignment, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `taskassignment.Intercept(f(g(h())))`.
func (c *TaskAssignmentClient) Intercept(interceptors ...Interceptor) {
	c.inters.TaskAssignment = append(c.inters.TaskAssignment, interceptors...)
}

// Create returns a builder for creating a TaskAssignment entity.
func (c *TaskAssignmentClient) Create() *TaskAssignmentCreate {
	mutation := newTaskAssignmentMutation(c.config, OpCreate)
	return &TaskAssignmentCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of TaskAssignment entities.
func (c *TaskAssignmentClient) CreateBulk(builders ...*TaskAssignmentCreate) *TaskAssignmentCreateBulk {
	return &TaskAssignmentCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *TaskAssignmentClient) MapCreateBulk(slice any, setFunc func(*TaskAssignmentCreate, int)) *TaskAssignmentCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &TaskAssignmentCreateBulk{err: fmt.Errorf("calling to TaskAssignmentClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*TaskAssignmentCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &TaskAssignmentCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for TaskAssignment.
func (c *TaskAssignmentClient) Update() *TaskAssignmentUpdate {
	mutation := newTaskAssignmentMutation(c.config, OpUpdate)
	return &TaskAssignmentUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *TaskAssignmentClient) UpdateOne(ta *TaskAssignment) *TaskAssignmentUpdateOne {
	mutation := newTaskAssignmentMutation(c.config, OpUpdateOne, withTaskAssignment(ta))
	return &TaskAssignmentUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *TaskAssignmentClient) UpdateOneID(id int) *TaskAssignmentUpdateOne {
	mutation := newTaskAssignmentMutation(c.config, OpUpdateOne, withTaskAssignmentID(id))
	return &TaskAssignmentUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for TaskAssignment.
func (c *TaskAssignmentClient) Delete() *TaskAssignmentDelete {
	mutation := newTaskAssignmentMutation(c.config, OpDelete)
	return &TaskAssignmentDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *TaskAssignmentClient) DeleteOne(ta *TaskAssignment) *TaskAssignmentDeleteOne {
	return c.DeleteOneID(ta.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *TaskAssignmentClient) DeleteOneID(id int) *TaskAssignmentDeleteOne {
	builder := c.Delete().Where(taskassignment.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &TaskAssignmentDeleteOne{builder}
}

// Query returns a query builder for TaskAssignment.
func (c *TaskAssignmentClient) Query() *TaskAssignmentQuery {
	return &TaskAssignmentQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeTaskAssignment},
		inters: c.Interceptors(),
	}
}

// Get returns a TaskAssignment entity by its id.
func (c *TaskAssignmentClient) Get(ctx context.Context, id int) (*TaskAssignment, error) {
	return c.Query().Where(taskassignment.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *TaskAssignmentClient) GetX(ctx context.Context, id int) *TaskAssignment {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryTask queries the task edge of a TaskAssignment.
func (c *TaskAssignmentClient) QueryTask(ta *TaskAssignment) *TaskQuery {
	query := (&TaskClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := ta.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(taskassignment.Table, taskassignment.FieldID, id),
			sqlgraph.To(task.Table, task.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, taskassignment.TaskTable, taskassignment.TaskColumn),
		)
		fromV = sqlgraph.Neighbors(ta.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryUser queries the user edge of a TaskAssignment.
func (c *TaskAssignmentClient) QueryUser(ta *TaskAssignment) *UserQuery {
	query := (&UserClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := ta.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(taskassignment.Table, taskassignment.FieldID, id),
			sqlgraph.To(user.Table, user.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, taskassignment.UserTable, taskassignment.UserColumn),
		)
		fromV = sqlgraph.Neighbors(ta.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *TaskAssignmentClient) Hooks() []Hook {
	hooks := c.hooks.TaskAssignment
	return append(hooks[:len(hooks):len(hooks)], taskassignment.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *TaskAssignmentClient) Interceptors() []Interceptor {
	inters := c.inters.TaskAssignment
	return append(inters[:len(inters):len(inters)], taskassignment.Interceptors[:]...)
}

func (c *TaskAssignmentClient) mutate(ctx context.Context, m *TaskAssignmentMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&TaskAssignmentCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&TaskAssignmentUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&TaskAssignmentUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&TaskAssignmentDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown TaskAssignment mutation op: %q", m.Op())
	}
}

// TaskAssignmentViewClient is a client for the TaskAssignmentView schema.
type TaskAssignmentViewClient struct {
	config
}

// NewTaskAssignmentViewClient returns a client for the TaskAssignmentView from the given config.
func NewTaskAssignmentViewClient(c config) *TaskAssignmentViewClient {
	return &TaskAssignmentViewClient{config: c}
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `taskassignmentview.Intercept(f(g(h())))`.
func (c *TaskAssignmentViewClient) Intercept(interceptors ...Interceptor) {
	c.inters.TaskAssignmentView = append(c.inters.TaskAssignmentView, interceptors...)
}

// Query returns a query builder for TaskAssignmentView.
func (c *TaskAssignmentViewClient) Query() *TaskAssignmentViewQuery {
	return &TaskAssignmentViewQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeTaskAssignmentView},
		inters: c.Interceptors(),
	}
}

// Interceptors returns the client interceptors.
func (c *TaskAssignmentViewClient) Interceptors() []Interceptor {
	return c.inters.TaskAssignmentView
}

// TaskDepartmentClient is a client for the TaskDepartment schema.
type TaskDepartmentClient struct {
	config
}

// NewTaskDepartmentClient returns a client for the TaskDepartment from the given config.
func NewTaskDepartmentClient(c config) *TaskDepartmentClient {
	return &TaskDepartmentClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `taskdepartment.Hooks(f(g(h())))`.
func (c *TaskDepartmentClient) Use(hooks ...Hook) {
	c.hooks.TaskDepartment = append(c.hooks.TaskDepartment, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `taskdepartment.Intercept(f(g(h())))`.
func (c *TaskDepartmentClient) Intercept(interceptors ...Interceptor) {
	c.inters.TaskDepartment = append(c.inters.TaskDepartment, interceptors...)
}

// Create returns a builder for creating a TaskDepartment entity.
func (c *TaskDepartmentClient) Create() *TaskDepartmentCreate {
	mutation := newTaskDepartmentMutation(c.config, OpCreate)
	return &TaskDepartmentCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of TaskDepartment entities.
func (c *TaskDepartmentClient) CreateBulk(builders ...*TaskDepartmentCreate) *TaskDepartmentCreateBulk {
	return &TaskDepartmentCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *TaskDepartmentClient) MapCreateBulk(slice any, setFunc func(*TaskDepartmentCreate, int)) *TaskDepartmentCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &TaskDepartmentCreateBulk{err: fmt.Errorf("calling to TaskDepartmentClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*TaskDepartmentCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &TaskDepartmentCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for TaskDepartment.
func (c *TaskDepartmentClient) Update() *TaskDepartmentUpdate {
	mutation := newTaskDepartmentMutation(c.config, OpUpdate)
	return &TaskDepartmentUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *TaskDepartmentClient) UpdateOne(td *TaskDepartment) *TaskDepartmentUpdateOne {
	mutation := newTaskDepartmentMutation(c.config, OpUpdateOne, withTaskDepartment(td))
	return &TaskDepartmentUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *TaskDepartmentClient) UpdateOneID(id int) *TaskDepartmentUpdateOne {
	mutation := newTaskDepartmentMutation(c.config, OpUpdateOne, withTaskDepartmentID(id))
	return &TaskDepartmentUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for TaskDepartment.
func (c *TaskDepartmentClient) Delete() *TaskDepartmentDelete {
	mutation := newTaskDepartmentMutation(c.config, OpDelete)
	return &TaskDepartmentDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *TaskDepartmentClient) DeleteOne(td *TaskDepartment) *TaskDepartmentDeleteOne {
	return c.DeleteOneID(td.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *TaskDepartmentClient) DeleteOneID(id int) *TaskDepartmentDeleteOne {
	builder := c.Delete().Where(taskdepartment.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &TaskDepartmentDeleteOne{builder}
}

// Query returns a query builder for TaskDepartment.
func (c *TaskDepartmentClient) Query() *TaskDepartmentQuery {
	return &TaskDepartmentQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeTaskDepartment},
		inters: c.Interceptors(),
	}
}

// Get returns a TaskDepartment entity by its id.
func (c *TaskDepartmentClient) Get(ctx context.Context, id int) (*TaskDepartment, error) {
	return c.Query().Where(taskdepartment.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *TaskDepartmentClient) GetX(ctx context.Context, id int) *TaskDepartment {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryTask queries the task edge of a TaskDepartment.
func (c *TaskDepartmentClient) QueryTask(td *TaskDepartment) *TaskQuery {
	query := (&TaskClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := td.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(taskdepartment.Table, taskdepartment.FieldID, id),
			sqlgraph.To(task.Table, task.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, taskdepartment.TaskTable, taskdepartment.TaskColumn),
		)
		fromV = sqlgraph.Neighbors(td.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryDepartment queries the department edge of a TaskDepartment.
func (c *TaskDepartmentClient) QueryDepartment(td *TaskDepartment) *DepartmentQuery {
	query := (&DepartmentClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := td.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(taskdepartment.Table, taskdepartment.FieldID, id),
			sqlgraph.To(department.Table, department.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, taskdepartment.DepartmentTable, taskdepartment.DepartmentColumn),
		)
		fromV = sqlgraph.Neighbors(td.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryCompleter queries the completer edge of a TaskDepartment.
func (c *TaskDepartmentClient) QueryCompleter(td *TaskDepartment) *UserQuery {
	query := (&UserClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := td.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(taskdepartment.Table, taskdepartment.FieldID, id),
			sqlgraph.To(user.Table, user.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, taskdepartment.CompleterTable, taskdepartment.CompleterColumn),
		)
		fromV = sqlgraph.Neighbors(td.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *TaskDepartmentClient) Hooks() []Hook {
	hooks := c.hooks.TaskDepartment
	return append(hooks[:len(hooks):len(hooks)], taskdepartment.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *TaskDepartmentClient) Interceptors() []Interceptor {
	inters := c.inters.TaskDepartment
	return append(inters[:len(inters):len(inters)], taskdepartment.Interceptors[:]...)
}

func (c *TaskDepartmentClient) mutate(ctx context.Context, m *TaskDepartmentMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&TaskDepartmentCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&TaskDepartmentUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&TaskDepartmentUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&TaskDepartmentDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown TaskDepartment mutation op: %q", m.Op())
	}
}

// TaskHistoryClient is a client for the TaskHistory schema.
type TaskHistoryClient struct {
	config
}

// NewTaskHistoryClient returns a client for the TaskHistory from the given config.
func NewTaskHistoryClient(c config) *TaskHistoryClient {
	return &TaskHistoryClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `taskhistory.Hooks(f(g(h())))`.
func (c *TaskHistoryClient) Use(hooks ...Hook) {
	c.hooks.TaskHistory = append(c.hooks.TaskHistory, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `taskhistory.Intercept(f(g(h())))`.
func (c *TaskHistoryClient) Intercept(interceptors ...Interceptor) {
	c.inters.TaskHistory = append(c.inters.TaskHistory, interceptors...)
}

// Create returns a builder for creating a TaskHistory entity.
func (c *TaskHistoryClient) Create() *TaskHistoryCreate {
	mutation := newTaskHistoryMutation(c.config, OpCreate)
	return &TaskHistoryCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of TaskHistory entities.
func (c *TaskHistoryClient) CreateBulk(builders ...*TaskHistoryCreate) *TaskHistoryCreateBulk {
	return &TaskHistoryCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *TaskHistoryClient) MapCreateBulk(slice any, setFunc func(*TaskHistoryCreate, int)) *TaskHistoryCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &TaskHistoryCreateBulk{err: fmt.Errorf("calling to TaskHistoryClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*TaskHistoryCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &TaskHistoryCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for TaskHistory.
func (c *TaskHistoryClient) Update() *TaskHistoryUpdate {
	mutation := newTaskHistoryMutation(c.config, OpUpdate)
	return &TaskHistoryUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *TaskHistoryClient) UpdateOne(th *TaskHistory) *TaskHistoryUpdateOne {
	mutation := newTaskHistoryMutation(c.config, OpUpdateOne, withTaskHistory(th))
	return &TaskHistoryUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *TaskHistoryClient) UpdateOneID(id int) *TaskHistoryUpdateOne {
	mutation := newTaskHistoryMutation(c.config, OpUpdateOne, withTaskHistoryID(id))
	return &TaskHistoryUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for TaskHistory.
func (c *TaskHistoryClient) Delete() *TaskHistoryDelete {
	mutation := newTaskHistoryMutation(c.config, OpDelete)
	return &TaskHistoryDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *TaskHistoryClient) DeleteOne(th *TaskHistory) *TaskHistoryDeleteOne {
	return c.DeleteOneID(th.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *TaskHistoryClient) DeleteOneID(id int) *TaskHistoryDeleteOne {
	builder := c.Delete().Where(taskhistory.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &TaskHistoryDeleteOne{builder}
}

// Query returns a query builder for TaskHistory.
func (c *TaskHistoryClient) Query() *TaskHistoryQuery {
	return &TaskHistoryQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeTaskHistory},
		inters: c.Interceptors(),
	}
}

// Get returns a TaskHistory entity by its id.
func (c *TaskHistoryClient) Get(ctx context.Context, id int) (*TaskHistory, error) {
	return c.Query().Where(taskhistory.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *TaskHistoryClient) GetX(ctx context.Context, id int) *TaskHistory {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *TaskHistoryClient) Hooks() []Hook {
	return c.hooks.TaskHistory
}

// Interceptors returns the client interceptors.
func (c *TaskHistoryClient) Interceptors() []Interceptor {
	return c.inters.TaskHistory
}

func (c *TaskHistoryClient) mutate(ctx context.Context, m *TaskHistoryMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&TaskHistoryCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&TaskHistoryUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&TaskHistoryUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&TaskHistoryDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown TaskHistory mutation op: %q", m.Op())
	}
}

// TaskNoteClient is a client for the TaskNote schema.
type TaskNoteClient struct {
	config
}

// NewTaskNoteClient returns a client for the TaskNote from the given config.
func NewTaskNoteClient(c config) *TaskNoteClient {
	return &TaskNoteClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `tasknote.Hooks(f(g(h())))`.
func (c *TaskNoteClient) Use(hooks ...Hook) {
	c.hooks.TaskNote = append(c.hooks.TaskNote, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `tasknote.Intercept(f(g(h())))`.
func (c *TaskNoteClient) Intercept(interceptors ...Interceptor) {
	c.inters.TaskNote = append(c.inters.TaskNote, interceptors...)
}

// Create returns a builder for creating a TaskNote entity.
func (c *TaskNoteClient) Create() *TaskNoteCreate {
	mutation := newTaskNoteMutation(c.config, OpCreate)
	return &TaskNoteCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of TaskNote entities.
func (c *TaskNoteClient) CreateBulk(builders ...*TaskNoteCreate) *TaskNoteCreateBulk {
	return &TaskNoteCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *TaskNoteClient) MapCreateBulk(slice any, setFunc func(*TaskNoteCreate, int)) *TaskNoteCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &TaskNoteCreateBulk{err: fmt.Errorf("calling to TaskNoteClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*TaskNoteCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &TaskNoteCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for TaskNote.
func (c *TaskNoteClient) Update() *TaskNoteUpdate {
	mutation := newTaskNoteMutation(c.config, OpUpdate)
	return &TaskNoteUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *TaskNoteClient) UpdateOne(tn *TaskNote) *TaskNoteUpdateOne {
	mutation := newTaskNoteMutation(c.config, OpUpdateOne, withTaskNote(tn))
	return &TaskNoteUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *TaskNoteClient) UpdateOneID(id int) *TaskNoteUpdateOne {
	mutation := newTaskNoteMutation(c.config, OpUpdateOne, withTaskNoteID(id))
	return &TaskNoteUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for TaskNote.
func (c *TaskNoteClient) Delete() *TaskNoteDelete {
	mutation := newTaskNoteMutation(c.config, OpDelete)
	return &TaskNoteDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *TaskNoteClient) DeleteOne(tn *TaskNote) *TaskNoteDeleteOne {
	return c.DeleteOneID(tn.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *TaskNoteClient) DeleteOneID(id int) *TaskNoteDeleteOne {
	builder := c.Delete().Where(tasknote.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &TaskNoteDeleteOne{builder}
}

// Query returns a query builder for TaskNote.
func (c *TaskNoteClient) Query() *TaskNoteQuery {
	return &TaskNoteQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeTaskNote},
		inters: c.Interceptors(),
	}
}

// Get returns a TaskNote entity by its id.
func (c *TaskNoteClient) Get(ctx context.Context, id int) (*TaskNote, error) {
	return c.Query().Where(tasknote.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *TaskNoteClient) GetX(ctx context.Context, id int) *TaskNote {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryCreator queries the creator edge of a TaskNote.
func (c *TaskNoteClient) QueryCreator(tn *TaskNote) *UserQuery {
	query := (&UserClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := tn.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(tasknote.Table, tasknote.FieldID, id),
			sqlgraph.To(user.Table, user.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, tasknote.CreatorTable, tasknote.CreatorColumn),
		)
		fromV = sqlgraph.Neighbors(tn.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryTask queries the task edge of a TaskNote.
func (c *TaskNoteClient) QueryTask(tn *TaskNote) *TaskQuery {
	query := (&TaskClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := tn.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(tasknote.Table, tasknote.FieldID, id),
			sqlgraph.To(task.Table, task.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, tasknote.TaskTable, tasknote.TaskColumn),
		)
		fromV = sqlgraph.Neighbors(tn.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *TaskNoteClient) Hooks() []Hook {
	hooks := c.hooks.TaskNote
	return append(hooks[:len(hooks):len(hooks)], tasknote.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *TaskNoteClient) Interceptors() []Interceptor {
	inters := c.inters.TaskNote
	return append(inters[:len(inters):len(inters)], tasknote.Interceptors[:]...)
}

func (c *TaskNoteClient) mutate(ctx context.Context, m *TaskNoteMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&TaskNoteCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&TaskNoteUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&TaskNoteUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&TaskNoteDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown TaskNote mutation op: %q", m.Op())
	}
}

// TaskRecurringClient is a client for the TaskRecurring schema.
type TaskRecurringClient struct {
	config
}

// NewTaskRecurringClient returns a client for the TaskRecurring from the given config.
func NewTaskRecurringClient(c config) *TaskRecurringClient {
	return &TaskRecurringClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `taskrecurring.Hooks(f(g(h())))`.
func (c *TaskRecurringClient) Use(hooks ...Hook) {
	c.hooks.TaskRecurring = append(c.hooks.TaskRecurring, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `taskrecurring.Intercept(f(g(h())))`.
func (c *TaskRecurringClient) Intercept(interceptors ...Interceptor) {
	c.inters.TaskRecurring = append(c.inters.TaskRecurring, interceptors...)
}

// Create returns a builder for creating a TaskRecurring entity.
func (c *TaskRecurringClient) Create() *TaskRecurringCreate {
	mutation := newTaskRecurringMutation(c.config, OpCreate)
	return &TaskRecurringCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of TaskRecurring entities.
func (c *TaskRecurringClient) CreateBulk(builders ...*TaskRecurringCreate) *TaskRecurringCreateBulk {
	return &TaskRecurringCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *TaskRecurringClient) MapCreateBulk(slice any, setFunc func(*TaskRecurringCreate, int)) *TaskRecurringCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &TaskRecurringCreateBulk{err: fmt.Errorf("calling to TaskRecurringClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*TaskRecurringCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &TaskRecurringCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for TaskRecurring.
func (c *TaskRecurringClient) Update() *TaskRecurringUpdate {
	mutation := newTaskRecurringMutation(c.config, OpUpdate)
	return &TaskRecurringUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *TaskRecurringClient) UpdateOne(tr *TaskRecurring) *TaskRecurringUpdateOne {
	mutation := newTaskRecurringMutation(c.config, OpUpdateOne, withTaskRecurring(tr))
	return &TaskRecurringUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *TaskRecurringClient) UpdateOneID(id int) *TaskRecurringUpdateOne {
	mutation := newTaskRecurringMutation(c.config, OpUpdateOne, withTaskRecurringID(id))
	return &TaskRecurringUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for TaskRecurring.
func (c *TaskRecurringClient) Delete() *TaskRecurringDelete {
	mutation := newTaskRecurringMutation(c.config, OpDelete)
	return &TaskRecurringDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *TaskRecurringClient) DeleteOne(tr *TaskRecurring) *TaskRecurringDeleteOne {
	return c.DeleteOneID(tr.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *TaskRecurringClient) DeleteOneID(id int) *TaskRecurringDeleteOne {
	builder := c.Delete().Where(taskrecurring.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &TaskRecurringDeleteOne{builder}
}

// Query returns a query builder for TaskRecurring.
func (c *TaskRecurringClient) Query() *TaskRecurringQuery {
	return &TaskRecurringQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeTaskRecurring},
		inters: c.Interceptors(),
	}
}

// Get returns a TaskRecurring entity by its id.
func (c *TaskRecurringClient) Get(ctx context.Context, id int) (*TaskRecurring, error) {
	return c.Query().Where(taskrecurring.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *TaskRecurringClient) GetX(ctx context.Context, id int) *TaskRecurring {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryTask queries the task edge of a TaskRecurring.
func (c *TaskRecurringClient) QueryTask(tr *TaskRecurring) *TaskQuery {
	query := (&TaskClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := tr.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(taskrecurring.Table, taskrecurring.FieldID, id),
			sqlgraph.To(task.Table, task.FieldID),
			sqlgraph.Edge(sqlgraph.O2O, true, taskrecurring.TaskTable, taskrecurring.TaskColumn),
		)
		fromV = sqlgraph.Neighbors(tr.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *TaskRecurringClient) Hooks() []Hook {
	return c.hooks.TaskRecurring
}

// Interceptors returns the client interceptors.
func (c *TaskRecurringClient) Interceptors() []Interceptor {
	return c.inters.TaskRecurring
}

func (c *TaskRecurringClient) mutate(ctx context.Context, m *TaskRecurringMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&TaskRecurringCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&TaskRecurringUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&TaskRecurringUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&TaskRecurringDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown TaskRecurring mutation op: %q", m.Op())
	}
}

// TaskSerialViewClient is a client for the TaskSerialView schema.
type TaskSerialViewClient struct {
	config
}

// NewTaskSerialViewClient returns a client for the TaskSerialView from the given config.
func NewTaskSerialViewClient(c config) *TaskSerialViewClient {
	return &TaskSerialViewClient{config: c}
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `taskserialview.Intercept(f(g(h())))`.
func (c *TaskSerialViewClient) Intercept(interceptors ...Interceptor) {
	c.inters.TaskSerialView = append(c.inters.TaskSerialView, interceptors...)
}

// Query returns a query builder for TaskSerialView.
func (c *TaskSerialViewClient) Query() *TaskSerialViewQuery {
	return &TaskSerialViewQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeTaskSerialView},
		inters: c.Interceptors(),
	}
}

// Interceptors returns the client interceptors.
func (c *TaskSerialViewClient) Interceptors() []Interceptor {
	return c.inters.TaskSerialView
}

// TermClient is a client for the Term schema.
type TermClient struct {
	config
}

// NewTermClient returns a client for the Term from the given config.
func NewTermClient(c config) *TermClient {
	return &TermClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `term.Hooks(f(g(h())))`.
func (c *TermClient) Use(hooks ...Hook) {
	c.hooks.Term = append(c.hooks.Term, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `term.Intercept(f(g(h())))`.
func (c *TermClient) Intercept(interceptors ...Interceptor) {
	c.inters.Term = append(c.inters.Term, interceptors...)
}

// Create returns a builder for creating a Term entity.
func (c *TermClient) Create() *TermCreate {
	mutation := newTermMutation(c.config, OpCreate)
	return &TermCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Term entities.
func (c *TermClient) CreateBulk(builders ...*TermCreate) *TermCreateBulk {
	return &TermCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *TermClient) MapCreateBulk(slice any, setFunc func(*TermCreate, int)) *TermCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &TermCreateBulk{err: fmt.Errorf("calling to TermClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*TermCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &TermCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Term.
func (c *TermClient) Update() *TermUpdate {
	mutation := newTermMutation(c.config, OpUpdate)
	return &TermUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *TermClient) UpdateOne(t *Term) *TermUpdateOne {
	mutation := newTermMutation(c.config, OpUpdateOne, withTerm(t))
	return &TermUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *TermClient) UpdateOneID(id int) *TermUpdateOne {
	mutation := newTermMutation(c.config, OpUpdateOne, withTermID(id))
	return &TermUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Term.
func (c *TermClient) Delete() *TermDelete {
	mutation := newTermMutation(c.config, OpDelete)
	return &TermDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *TermClient) DeleteOne(t *Term) *TermDeleteOne {
	return c.DeleteOneID(t.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *TermClient) DeleteOneID(id int) *TermDeleteOne {
	builder := c.Delete().Where(term.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &TermDeleteOne{builder}
}

// Query returns a query builder for Term.
func (c *TermClient) Query() *TermQuery {
	return &TermQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeTerm},
		inters: c.Interceptors(),
	}
}

// Get returns a Term entity by its id.
func (c *TermClient) Get(ctx context.Context, id int) (*Term, error) {
	return c.Query().Where(term.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *TermClient) GetX(ctx context.Context, id int) *Term {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryUnits queries the units edge of a Term.
func (c *TermClient) QueryUnits(t *Term) *ProductQuery {
	query := (&ProductClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := t.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(term.Table, term.FieldID, id),
			sqlgraph.To(product.Table, product.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, term.UnitsTable, term.UnitsColumn),
		)
		fromV = sqlgraph.Neighbors(t.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryGroups queries the groups edge of a Term.
func (c *TermClient) QueryGroups(t *Term) *ProductQuery {
	query := (&ProductClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := t.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(term.Table, term.FieldID, id),
			sqlgraph.To(product.Table, product.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, term.GroupsTable, term.GroupsColumn),
		)
		fromV = sqlgraph.Neighbors(t.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryCategories queries the categories edge of a Term.
func (c *TermClient) QueryCategories(t *Term) *ProductQuery {
	query := (&ProductClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := t.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(term.Table, term.FieldID, id),
			sqlgraph.To(product.Table, product.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, term.CategoriesTable, term.CategoriesColumn),
		)
		fromV = sqlgraph.Neighbors(t.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *TermClient) Hooks() []Hook {
	hooks := c.hooks.Term
	return append(hooks[:len(hooks):len(hooks)], term.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *TermClient) Interceptors() []Interceptor {
	inters := c.inters.Term
	return append(inters[:len(inters):len(inters)], term.Interceptors[:]...)
}

func (c *TermClient) mutate(ctx context.Context, m *TermMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&TermCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&TermUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&TermUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&TermDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Term mutation op: %q", m.Op())
	}
}

// TrackClient is a client for the Track schema.
type TrackClient struct {
	config
}

// NewTrackClient returns a client for the Track from the given config.
func NewTrackClient(c config) *TrackClient {
	return &TrackClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `track.Hooks(f(g(h())))`.
func (c *TrackClient) Use(hooks ...Hook) {
	c.hooks.Track = append(c.hooks.Track, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `track.Intercept(f(g(h())))`.
func (c *TrackClient) Intercept(interceptors ...Interceptor) {
	c.inters.Track = append(c.inters.Track, interceptors...)
}

// Create returns a builder for creating a Track entity.
func (c *TrackClient) Create() *TrackCreate {
	mutation := newTrackMutation(c.config, OpCreate)
	return &TrackCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Track entities.
func (c *TrackClient) CreateBulk(builders ...*TrackCreate) *TrackCreateBulk {
	return &TrackCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *TrackClient) MapCreateBulk(slice any, setFunc func(*TrackCreate, int)) *TrackCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &TrackCreateBulk{err: fmt.Errorf("calling to TrackClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*TrackCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &TrackCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Track.
func (c *TrackClient) Update() *TrackUpdate {
	mutation := newTrackMutation(c.config, OpUpdate)
	return &TrackUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *TrackClient) UpdateOne(t *Track) *TrackUpdateOne {
	mutation := newTrackMutation(c.config, OpUpdateOne, withTrack(t))
	return &TrackUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *TrackClient) UpdateOneID(id int) *TrackUpdateOne {
	mutation := newTrackMutation(c.config, OpUpdateOne, withTrackID(id))
	return &TrackUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Track.
func (c *TrackClient) Delete() *TrackDelete {
	mutation := newTrackMutation(c.config, OpDelete)
	return &TrackDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *TrackClient) DeleteOne(t *Track) *TrackDeleteOne {
	return c.DeleteOneID(t.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *TrackClient) DeleteOneID(id int) *TrackDeleteOne {
	builder := c.Delete().Where(track.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &TrackDeleteOne{builder}
}

// Query returns a query builder for Track.
func (c *TrackClient) Query() *TrackQuery {
	return &TrackQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeTrack},
		inters: c.Interceptors(),
	}
}

// Get returns a Track entity by its id.
func (c *TrackClient) Get(ctx context.Context, id int) (*Track, error) {
	return c.Query().Where(track.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *TrackClient) GetX(ctx context.Context, id int) *Track {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryDeal queries the deal edge of a Track.
func (c *TrackClient) QueryDeal(t *Track) *DealQuery {
	query := (&DealClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := t.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(track.Table, track.FieldID, id),
			sqlgraph.To(deal.Table, deal.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, track.DealTable, track.DealColumn),
		)
		fromV = sqlgraph.Neighbors(t.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryUser queries the user edge of a Track.
func (c *TrackClient) QueryUser(t *Track) *UserQuery {
	query := (&UserClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := t.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(track.Table, track.FieldID, id),
			sqlgraph.To(user.Table, user.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, track.UserTable, track.UserColumn),
		)
		fromV = sqlgraph.Neighbors(t.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryPerson queries the person edge of a Track.
func (c *TrackClient) QueryPerson(t *Track) *PersonQuery {
	query := (&PersonClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := t.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(track.Table, track.FieldID, id),
			sqlgraph.To(person.Table, person.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, track.PersonTable, track.PersonColumn),
		)
		fromV = sqlgraph.Neighbors(t.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryPipeline queries the pipeline edge of a Track.
func (c *TrackClient) QueryPipeline(t *Track) *PipelineQuery {
	query := (&PipelineClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := t.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(track.Table, track.FieldID, id),
			sqlgraph.To(pipeline.Table, pipeline.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, track.PipelineTable, track.PipelineColumn),
		)
		fromV = sqlgraph.Neighbors(t.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryStage queries the stage edge of a Track.
func (c *TrackClient) QueryStage(t *Track) *StageQuery {
	query := (&StageClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := t.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(track.Table, track.FieldID, id),
			sqlgraph.To(stage.Table, stage.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, track.StageTable, track.StageColumn),
		)
		fromV = sqlgraph.Neighbors(t.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryAppointments queries the appointments edge of a Track.
func (c *TrackClient) QueryAppointments(t *Track) *AppointmentQuery {
	query := (&AppointmentClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := t.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(track.Table, track.FieldID, id),
			sqlgraph.To(appointment.Table, appointment.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, track.AppointmentsTable, track.AppointmentsColumn),
		)
		fromV = sqlgraph.Neighbors(t.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryFileUsages queries the file_usages edge of a Track.
func (c *TrackClient) QueryFileUsages(t *Track) *FileUsageQuery {
	query := (&FileUsageClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := t.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(track.Table, track.FieldID, id),
			sqlgraph.To(fileusage.Table, fileusage.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, track.FileUsagesTable, track.FileUsagesColumn),
		)
		fromV = sqlgraph.Neighbors(t.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryAttachments queries the attachments edge of a Track.
func (c *TrackClient) QueryAttachments(t *Track) *AttachmentQuery {
	query := (&AttachmentClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := t.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(track.Table, track.FieldID, id),
			sqlgraph.To(attachment.Table, attachment.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, track.AttachmentsTable, track.AttachmentsColumn),
		)
		fromV = sqlgraph.Neighbors(t.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryBillItems queries the bill_items edge of a Track.
func (c *TrackClient) QueryBillItems(t *Track) *BillItemQuery {
	query := (&BillItemClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := t.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(track.Table, track.FieldID, id),
			sqlgraph.To(billitem.Table, billitem.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, track.BillItemsTable, track.BillItemsColumn),
		)
		fromV = sqlgraph.Neighbors(t.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *TrackClient) Hooks() []Hook {
	hooks := c.hooks.Track
	return append(hooks[:len(hooks):len(hooks)], track.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *TrackClient) Interceptors() []Interceptor {
	inters := c.inters.Track
	return append(inters[:len(inters):len(inters)], track.Interceptors[:]...)
}

func (c *TrackClient) mutate(ctx context.Context, m *TrackMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&TrackCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&TrackUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&TrackUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&TrackDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Track mutation op: %q", m.Op())
	}
}

// UnifiedHistoryViewClient is a client for the UnifiedHistoryView schema.
type UnifiedHistoryViewClient struct {
	config
}

// NewUnifiedHistoryViewClient returns a client for the UnifiedHistoryView from the given config.
func NewUnifiedHistoryViewClient(c config) *UnifiedHistoryViewClient {
	return &UnifiedHistoryViewClient{config: c}
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `unifiedhistoryview.Intercept(f(g(h())))`.
func (c *UnifiedHistoryViewClient) Intercept(interceptors ...Interceptor) {
	c.inters.UnifiedHistoryView = append(c.inters.UnifiedHistoryView, interceptors...)
}

// Query returns a query builder for UnifiedHistoryView.
func (c *UnifiedHistoryViewClient) Query() *UnifiedHistoryViewQuery {
	return &UnifiedHistoryViewQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeUnifiedHistoryView},
		inters: c.Interceptors(),
	}
}

// Interceptors returns the client interceptors.
func (c *UnifiedHistoryViewClient) Interceptors() []Interceptor {
	return c.inters.UnifiedHistoryView
}

// UserClient is a client for the User schema.
type UserClient struct {
	config
}

// NewUserClient returns a client for the User from the given config.
func NewUserClient(c config) *UserClient {
	return &UserClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `user.Hooks(f(g(h())))`.
func (c *UserClient) Use(hooks ...Hook) {
	c.hooks.User = append(c.hooks.User, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `user.Intercept(f(g(h())))`.
func (c *UserClient) Intercept(interceptors ...Interceptor) {
	c.inters.User = append(c.inters.User, interceptors...)
}

// Create returns a builder for creating a User entity.
func (c *UserClient) Create() *UserCreate {
	mutation := newUserMutation(c.config, OpCreate)
	return &UserCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of User entities.
func (c *UserClient) CreateBulk(builders ...*UserCreate) *UserCreateBulk {
	return &UserCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *UserClient) MapCreateBulk(slice any, setFunc func(*UserCreate, int)) *UserCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &UserCreateBulk{err: fmt.Errorf("calling to UserClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*UserCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &UserCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for User.
func (c *UserClient) Update() *UserUpdate {
	mutation := newUserMutation(c.config, OpUpdate)
	return &UserUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *UserClient) UpdateOne(u *User) *UserUpdateOne {
	mutation := newUserMutation(c.config, OpUpdateOne, withUser(u))
	return &UserUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *UserClient) UpdateOneID(id int) *UserUpdateOne {
	mutation := newUserMutation(c.config, OpUpdateOne, withUserID(id))
	return &UserUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for User.
func (c *UserClient) Delete() *UserDelete {
	mutation := newUserMutation(c.config, OpDelete)
	return &UserDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *UserClient) DeleteOne(u *User) *UserDeleteOne {
	return c.DeleteOneID(u.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *UserClient) DeleteOneID(id int) *UserDeleteOne {
	builder := c.Delete().Where(user.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &UserDeleteOne{builder}
}

// Query returns a query builder for User.
func (c *UserClient) Query() *UserQuery {
	return &UserQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeUser},
		inters: c.Interceptors(),
	}
}

// Get returns a User entity by its id.
func (c *UserClient) Get(ctx context.Context, id int) (*User, error) {
	return c.Query().Where(user.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *UserClient) GetX(ctx context.Context, id int) *User {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryAssignment queries the assignment edge of a User.
func (c *UserClient) QueryAssignment(u *User) *TaskAssignmentQuery {
	query := (&TaskAssignmentClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := u.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(user.Table, user.FieldID, id),
			sqlgraph.To(taskassignment.Table, taskassignment.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, user.AssignmentTable, user.AssignmentColumn),
		)
		fromV = sqlgraph.Neighbors(u.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryAssignedPerson queries the assigned_person edge of a User.
func (c *UserClient) QueryAssignedPerson(u *User) *PersonQuery {
	query := (&PersonClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := u.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(user.Table, user.FieldID, id),
			sqlgraph.To(person.Table, person.FieldID),
			sqlgraph.Edge(sqlgraph.M2M, true, user.AssignedPersonTable, user.AssignedPersonPrimaryKey...),
		)
		fromV = sqlgraph.Neighbors(u.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryNotes queries the notes edge of a User.
func (c *UserClient) QueryNotes(u *User) *NoteQuery {
	query := (&NoteClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := u.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(user.Table, user.FieldID, id),
			sqlgraph.To(note.Table, note.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, user.NotesTable, user.NotesColumn),
		)
		fromV = sqlgraph.Neighbors(u.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryTaskNotes queries the task_notes edge of a User.
func (c *UserClient) QueryTaskNotes(u *User) *TaskNoteQuery {
	query := (&TaskNoteClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := u.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(user.Table, user.FieldID, id),
			sqlgraph.To(tasknote.Table, tasknote.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, user.TaskNotesTable, user.TaskNotesColumn),
		)
		fromV = sqlgraph.Neighbors(u.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryAppointments queries the appointments edge of a User.
func (c *UserClient) QueryAppointments(u *User) *AppointmentQuery {
	query := (&AppointmentClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := u.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(user.Table, user.FieldID, id),
			sqlgraph.To(appointment.Table, appointment.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, user.AppointmentsTable, user.AppointmentsColumn),
		)
		fromV = sqlgraph.Neighbors(u.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryCalls queries the calls edge of a User.
func (c *UserClient) QueryCalls(u *User) *CallQuery {
	query := (&CallClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := u.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(user.Table, user.FieldID, id),
			sqlgraph.To(call.Table, call.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, user.CallsTable, user.CallsColumn),
		)
		fromV = sqlgraph.Neighbors(u.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QuerySchedules queries the schedules edge of a User.
func (c *UserClient) QuerySchedules(u *User) *ScheduleQuery {
	query := (&ScheduleClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := u.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(user.Table, user.FieldID, id),
			sqlgraph.To(schedule.Table, schedule.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, user.SchedulesTable, user.SchedulesColumn),
		)
		fromV = sqlgraph.Neighbors(u.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryMessageHistories queries the message_histories edge of a User.
func (c *UserClient) QueryMessageHistories(u *User) *MessageHistoryQuery {
	query := (&MessageHistoryClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := u.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(user.Table, user.FieldID, id),
			sqlgraph.To(messagehistory.Table, messagehistory.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, user.MessageHistoriesTable, user.MessageHistoriesColumn),
		)
		fromV = sqlgraph.Neighbors(u.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryData queries the data edge of a User.
func (c *UserClient) QueryData(u *User) *UserDataQuery {
	query := (&UserDataClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := u.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(user.Table, user.FieldID, id),
			sqlgraph.To(userdata.Table, userdata.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, user.DataTable, user.DataColumn),
		)
		fromV = sqlgraph.Neighbors(u.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryExportJobs queries the export_jobs edge of a User.
func (c *UserClient) QueryExportJobs(u *User) *ExportJobQuery {
	query := (&ExportJobClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := u.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(user.Table, user.FieldID, id),
			sqlgraph.To(exportjob.Table, exportjob.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, user.ExportJobsTable, user.ExportJobsColumn),
		)
		fromV = sqlgraph.Neighbors(u.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryNotifications queries the notifications edge of a User.
func (c *UserClient) QueryNotifications(u *User) *NotificationQuery {
	query := (&NotificationClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := u.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(user.Table, user.FieldID, id),
			sqlgraph.To(notification.Table, notification.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, user.NotificationsTable, user.NotificationsColumn),
		)
		fromV = sqlgraph.Neighbors(u.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryPersonAssignment queries the person_assignment edge of a User.
func (c *UserClient) QueryPersonAssignment(u *User) *PersonAssignmentQuery {
	query := (&PersonAssignmentClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := u.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(user.Table, user.FieldID, id),
			sqlgraph.To(personassignment.Table, personassignment.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, user.PersonAssignmentTable, user.PersonAssignmentColumn),
		)
		fromV = sqlgraph.Neighbors(u.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *UserClient) Hooks() []Hook {
	hooks := c.hooks.User
	return append(hooks[:len(hooks):len(hooks)], user.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *UserClient) Interceptors() []Interceptor {
	inters := c.inters.User
	return append(inters[:len(inters):len(inters)], user.Interceptors[:]...)
}

func (c *UserClient) mutate(ctx context.Context, m *UserMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&UserCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&UserUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&UserUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&UserDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown User mutation op: %q", m.Op())
	}
}

// UserDataClient is a client for the UserData schema.
type UserDataClient struct {
	config
}

// NewUserDataClient returns a client for the UserData from the given config.
func NewUserDataClient(c config) *UserDataClient {
	return &UserDataClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `userdata.Hooks(f(g(h())))`.
func (c *UserDataClient) Use(hooks ...Hook) {
	c.hooks.UserData = append(c.hooks.UserData, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `userdata.Intercept(f(g(h())))`.
func (c *UserDataClient) Intercept(interceptors ...Interceptor) {
	c.inters.UserData = append(c.inters.UserData, interceptors...)
}

// Create returns a builder for creating a UserData entity.
func (c *UserDataClient) Create() *UserDataCreate {
	mutation := newUserDataMutation(c.config, OpCreate)
	return &UserDataCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of UserData entities.
func (c *UserDataClient) CreateBulk(builders ...*UserDataCreate) *UserDataCreateBulk {
	return &UserDataCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *UserDataClient) MapCreateBulk(slice any, setFunc func(*UserDataCreate, int)) *UserDataCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &UserDataCreateBulk{err: fmt.Errorf("calling to UserDataClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*UserDataCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &UserDataCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for UserData.
func (c *UserDataClient) Update() *UserDataUpdate {
	mutation := newUserDataMutation(c.config, OpUpdate)
	return &UserDataUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *UserDataClient) UpdateOne(ud *UserData) *UserDataUpdateOne {
	mutation := newUserDataMutation(c.config, OpUpdateOne, withUserData(ud))
	return &UserDataUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *UserDataClient) UpdateOneID(id int) *UserDataUpdateOne {
	mutation := newUserDataMutation(c.config, OpUpdateOne, withUserDataID(id))
	return &UserDataUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for UserData.
func (c *UserDataClient) Delete() *UserDataDelete {
	mutation := newUserDataMutation(c.config, OpDelete)
	return &UserDataDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *UserDataClient) DeleteOne(ud *UserData) *UserDataDeleteOne {
	return c.DeleteOneID(ud.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *UserDataClient) DeleteOneID(id int) *UserDataDeleteOne {
	builder := c.Delete().Where(userdata.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &UserDataDeleteOne{builder}
}

// Query returns a query builder for UserData.
func (c *UserDataClient) Query() *UserDataQuery {
	return &UserDataQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeUserData},
		inters: c.Interceptors(),
	}
}

// Get returns a UserData entity by its id.
func (c *UserDataClient) Get(ctx context.Context, id int) (*UserData, error) {
	return c.Query().Where(userdata.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *UserDataClient) GetX(ctx context.Context, id int) *UserData {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryUser queries the user edge of a UserData.
func (c *UserDataClient) QueryUser(ud *UserData) *UserQuery {
	query := (&UserClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := ud.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(userdata.Table, userdata.FieldID, id),
			sqlgraph.To(user.Table, user.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, userdata.UserTable, userdata.UserColumn),
		)
		fromV = sqlgraph.Neighbors(ud.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *UserDataClient) Hooks() []Hook {
	return c.hooks.UserData
}

// Interceptors returns the client interceptors.
func (c *UserDataClient) Interceptors() []Interceptor {
	return c.inters.UserData
}

func (c *UserDataClient) mutate(ctx context.Context, m *UserDataMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&UserDataCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&UserDataUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&UserDataUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&UserDataDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown UserData mutation op: %q", m.Op())
	}
}

// hooks and interceptors per client, for fast access.
type (
	hooks struct {
		Appointment, AppointmentHistory, Attachment, AttachmentData, Bill, BillData,
		BillItem, Bundle, Call, CasbinRule, CashFlow, CashFlowItem, CashFlowNote,
		DbsandentistDboAppointmentTime, DbsandentistDboBill, DbsandentistDboBillItem,
		DbsandentistDboBillItemDetail, DbsandentistDboBillPayment,
		DbsandentistDboCustomer, Deal, DealHistory, DealStageHistory, DealUser,
		DealUserRating, Department, Deposit, DepositAllocation, DepositPayment,
		Discount, DiscountUsage, EntityHistory, ExportJob, File, FileUsage,
		FormSubmission, Installment, InstallmentPlan, Issue, IssueComment,
		LocalDistrict, LocalProvince, LocalWard, Material, MaterialUsage,
		MessageHistory, Note, Notification, OTP, Operation, OperationMaterial,
		Organization, Payment, PaymentAllocation, Person, PersonAssignment, PersonData,
		PersonHistory, PersonReferral, Pipeline, Product, ProductOperation, Referral,
		Schedule, Setting, Stage, Tag, TagDeal, TagPerson, Task, TaskAssignment,
		TaskDepartment, TaskHistory, TaskNote, TaskRecurring, Term, Track, User,
		UserData []ent.Hook
	}
	inters struct {
		ActivityView, Appointment, AppointmentHistory, Attachment, AttachmentData,
		AttachmentOperationReportView, Bill, BillData, BillItem, Bundle, Call,
		CasbinRule, CashFlow, CashFlowItem, CashFlowNote,
		DbsandentistDboAppointmentTime, DbsandentistDboBill, DbsandentistDboBillItem,
		DbsandentistDboBillItemDetail, DbsandentistDboBillPayment,
		DbsandentistDboCustomer, Deal, DealHistory, DealStageHistory,
		DealStageHistoryView, DealUser, DealUserRating, Department, Deposit,
		DepositAllocation, DepositPayment, Discount, DiscountUsage, EntityHistory,
		ExportJob, File, FileUsage, FormSubmission, Installment, InstallmentPlan,
		Issue, IssueComment, LocalDistrict, LocalProvince, LocalWard, Material,
		MaterialUsage, MessageHistory, NewTracksReportView, Note, Notification, OTP,
		Operation, OperationMaterial, Organization, Payment, PaymentAllocation,
		PaymentReportDetailView, PaymentReportView, Person, PersonAssignment,
		PersonData, PersonHistory, PersonQueryView, PersonReferral, PersonTimelineView,
		PhoneViewHistory, Pipeline, Product, ProductOperation, Referral, Schedule,
		Setting, Stage, Tag, TagDeal, TagPerson, Task, TaskAssignment,
		TaskAssignmentView, TaskDepartment, TaskHistory, TaskNote, TaskRecurring,
		TaskSerialView, Term, Track, UnifiedHistoryView, User,
		UserData []ent.Interceptor
	}
)

// ExecContext allows calling the underlying ExecContext method of the driver if it is supported by it.
// See, database/sql#DB.ExecContext for more information.
func (c *config) ExecContext(ctx context.Context, query string, args ...any) (stdsql.Result, error) {
	ex, ok := c.driver.(interface {
		ExecContext(context.Context, string, ...any) (stdsql.Result, error)
	})
	if !ok {
		return nil, fmt.Errorf("Driver.ExecContext is not supported")
	}
	return ex.ExecContext(ctx, query, args...)
}

// QueryContext allows calling the underlying QueryContext method of the driver if it is supported by it.
// See, database/sql#DB.QueryContext for more information.
func (c *config) QueryContext(ctx context.Context, query string, args ...any) (*stdsql.Rows, error) {
	q, ok := c.driver.(interface {
		QueryContext(context.Context, string, ...any) (*stdsql.Rows, error)
	})
	if !ok {
		return nil, fmt.Errorf("Driver.QueryContext is not supported")
	}
	return q.QueryContext(ctx, query, args...)
}
