// Code generated by ent, DO NOT EDIT.

package ent

import (
	"bcare/ent/attachment"
	"bcare/ent/bill"
	"bcare/ent/deal"
	"bcare/ent/dealuser"
	"bcare/ent/deposit"
	"bcare/ent/discountusage"
	"bcare/ent/installmentplan"
	"bcare/ent/person"
	"bcare/ent/stage"
	"bcare/ent/tag"
	"bcare/ent/tagdeal"
	"bcare/ent/task"
	"bcare/ent/track"
	"bcare/ent/types"
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// DealCreate is the builder for creating a Deal entity.
type DealCreate struct {
	config
	mutation *DealMutation
	hooks    []Hook
}

// SetDeletedAt sets the "deleted_at" field.
func (dc *DealCreate) SetDeletedAt(t time.Time) *DealCreate {
	dc.mutation.SetDeletedAt(t)
	return dc
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (dc *DealCreate) SetNillableDeletedAt(t *time.Time) *DealCreate {
	if t != nil {
		dc.SetDeletedAt(*t)
	}
	return dc
}

// SetStatus sets the "status" field.
func (dc *DealCreate) SetStatus(i int8) *DealCreate {
	dc.mutation.SetStatus(i)
	return dc
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (dc *DealCreate) SetNillableStatus(i *int8) *DealCreate {
	if i != nil {
		dc.SetStatus(*i)
	}
	return dc
}

// SetVersion sets the "version" field.
func (dc *DealCreate) SetVersion(i int) *DealCreate {
	dc.mutation.SetVersion(i)
	return dc
}

// SetNillableVersion sets the "version" field if the given value is not nil.
func (dc *DealCreate) SetNillableVersion(i *int) *DealCreate {
	if i != nil {
		dc.SetVersion(*i)
	}
	return dc
}

// SetCreatedAt sets the "created_at" field.
func (dc *DealCreate) SetCreatedAt(t time.Time) *DealCreate {
	dc.mutation.SetCreatedAt(t)
	return dc
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (dc *DealCreate) SetNillableCreatedAt(t *time.Time) *DealCreate {
	if t != nil {
		dc.SetCreatedAt(*t)
	}
	return dc
}

// SetUpdatedAt sets the "updated_at" field.
func (dc *DealCreate) SetUpdatedAt(t time.Time) *DealCreate {
	dc.mutation.SetUpdatedAt(t)
	return dc
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (dc *DealCreate) SetNillableUpdatedAt(t *time.Time) *DealCreate {
	if t != nil {
		dc.SetUpdatedAt(*t)
	}
	return dc
}

// SetPersonID sets the "person_id" field.
func (dc *DealCreate) SetPersonID(i int) *DealCreate {
	dc.mutation.SetPersonID(i)
	return dc
}

// SetNillablePersonID sets the "person_id" field if the given value is not nil.
func (dc *DealCreate) SetNillablePersonID(i *int) *DealCreate {
	if i != nil {
		dc.SetPersonID(*i)
	}
	return dc
}

// SetParentDealID sets the "parent_deal_id" field.
func (dc *DealCreate) SetParentDealID(i int) *DealCreate {
	dc.mutation.SetParentDealID(i)
	return dc
}

// SetNillableParentDealID sets the "parent_deal_id" field if the given value is not nil.
func (dc *DealCreate) SetNillableParentDealID(i *int) *DealCreate {
	if i != nil {
		dc.SetParentDealID(*i)
	}
	return dc
}

// SetTotalAmount sets the "total_amount" field.
func (dc *DealCreate) SetTotalAmount(f float64) *DealCreate {
	dc.mutation.SetTotalAmount(f)
	return dc
}

// SetNillableTotalAmount sets the "total_amount" field if the given value is not nil.
func (dc *DealCreate) SetNillableTotalAmount(f *float64) *DealCreate {
	if f != nil {
		dc.SetTotalAmount(*f)
	}
	return dc
}

// SetStageID sets the "stage_id" field.
func (dc *DealCreate) SetStageID(i int) *DealCreate {
	dc.mutation.SetStageID(i)
	return dc
}

// SetNillableStageID sets the "stage_id" field if the given value is not nil.
func (dc *DealCreate) SetNillableStageID(i *int) *DealCreate {
	if i != nil {
		dc.SetStageID(*i)
	}
	return dc
}

// SetStageHistory sets the "stage_history" field.
func (dc *DealCreate) SetStageHistory(the []types.StageHistoryEntry) *DealCreate {
	dc.mutation.SetStageHistory(the)
	return dc
}

// SetName sets the "name" field.
func (dc *DealCreate) SetName(s string) *DealCreate {
	dc.mutation.SetName(s)
	return dc
}

// SetNillableName sets the "name" field if the given value is not nil.
func (dc *DealCreate) SetNillableName(s *string) *DealCreate {
	if s != nil {
		dc.SetName(*s)
	}
	return dc
}

// SetState sets the "state" field.
func (dc *DealCreate) SetState(d deal.State) *DealCreate {
	dc.mutation.SetState(d)
	return dc
}

// SetNillableState sets the "state" field if the given value is not nil.
func (dc *DealCreate) SetNillableState(d *deal.State) *DealCreate {
	if d != nil {
		dc.SetState(*d)
	}
	return dc
}

// SetPerson sets the "person" edge to the Person entity.
func (dc *DealCreate) SetPerson(p *Person) *DealCreate {
	return dc.SetPersonID(p.ID)
}

// SetStage sets the "stage" edge to the Stage entity.
func (dc *DealCreate) SetStage(s *Stage) *DealCreate {
	return dc.SetStageID(s.ID)
}

// AddAttachmentIDs adds the "attachments" edge to the Attachment entity by IDs.
func (dc *DealCreate) AddAttachmentIDs(ids ...int) *DealCreate {
	dc.mutation.AddAttachmentIDs(ids...)
	return dc
}

// AddAttachments adds the "attachments" edges to the Attachment entity.
func (dc *DealCreate) AddAttachments(a ...*Attachment) *DealCreate {
	ids := make([]int, len(a))
	for i := range a {
		ids[i] = a[i].ID
	}
	return dc.AddAttachmentIDs(ids...)
}

// AddPlanIDs adds the "plans" edge to the InstallmentPlan entity by IDs.
func (dc *DealCreate) AddPlanIDs(ids ...int) *DealCreate {
	dc.mutation.AddPlanIDs(ids...)
	return dc
}

// AddPlans adds the "plans" edges to the InstallmentPlan entity.
func (dc *DealCreate) AddPlans(i ...*InstallmentPlan) *DealCreate {
	ids := make([]int, len(i))
	for j := range i {
		ids[j] = i[j].ID
	}
	return dc.AddPlanIDs(ids...)
}

// AddBillIDs adds the "bills" edge to the Bill entity by IDs.
func (dc *DealCreate) AddBillIDs(ids ...int) *DealCreate {
	dc.mutation.AddBillIDs(ids...)
	return dc
}

// AddBills adds the "bills" edges to the Bill entity.
func (dc *DealCreate) AddBills(b ...*Bill) *DealCreate {
	ids := make([]int, len(b))
	for i := range b {
		ids[i] = b[i].ID
	}
	return dc.AddBillIDs(ids...)
}

// AddTrackIDs adds the "tracks" edge to the Track entity by IDs.
func (dc *DealCreate) AddTrackIDs(ids ...int) *DealCreate {
	dc.mutation.AddTrackIDs(ids...)
	return dc
}

// AddTracks adds the "tracks" edges to the Track entity.
func (dc *DealCreate) AddTracks(t ...*Track) *DealCreate {
	ids := make([]int, len(t))
	for i := range t {
		ids[i] = t[i].ID
	}
	return dc.AddTrackIDs(ids...)
}

// AddTaskIDs adds the "tasks" edge to the Task entity by IDs.
func (dc *DealCreate) AddTaskIDs(ids ...int) *DealCreate {
	dc.mutation.AddTaskIDs(ids...)
	return dc
}

// AddTasks adds the "tasks" edges to the Task entity.
func (dc *DealCreate) AddTasks(t ...*Task) *DealCreate {
	ids := make([]int, len(t))
	for i := range t {
		ids[i] = t[i].ID
	}
	return dc.AddTaskIDs(ids...)
}

// AddDealAssignmentIDs adds the "deal_assignment" edge to the DealUser entity by IDs.
func (dc *DealCreate) AddDealAssignmentIDs(ids ...int) *DealCreate {
	dc.mutation.AddDealAssignmentIDs(ids...)
	return dc
}

// AddDealAssignment adds the "deal_assignment" edges to the DealUser entity.
func (dc *DealCreate) AddDealAssignment(d ...*DealUser) *DealCreate {
	ids := make([]int, len(d))
	for i := range d {
		ids[i] = d[i].ID
	}
	return dc.AddDealAssignmentIDs(ids...)
}

// AddTagIDs adds the "tags" edge to the Tag entity by IDs.
func (dc *DealCreate) AddTagIDs(ids ...int) *DealCreate {
	dc.mutation.AddTagIDs(ids...)
	return dc
}

// AddTags adds the "tags" edges to the Tag entity.
func (dc *DealCreate) AddTags(t ...*Tag) *DealCreate {
	ids := make([]int, len(t))
	for i := range t {
		ids[i] = t[i].ID
	}
	return dc.AddTagIDs(ids...)
}

// AddDepositIDs adds the "deposits" edge to the Deposit entity by IDs.
func (dc *DealCreate) AddDepositIDs(ids ...int) *DealCreate {
	dc.mutation.AddDepositIDs(ids...)
	return dc
}

// AddDeposits adds the "deposits" edges to the Deposit entity.
func (dc *DealCreate) AddDeposits(d ...*Deposit) *DealCreate {
	ids := make([]int, len(d))
	for i := range d {
		ids[i] = d[i].ID
	}
	return dc.AddDepositIDs(ids...)
}

// AddDiscountUsageIDs adds the "discount_usages" edge to the DiscountUsage entity by IDs.
func (dc *DealCreate) AddDiscountUsageIDs(ids ...int) *DealCreate {
	dc.mutation.AddDiscountUsageIDs(ids...)
	return dc
}

// AddDiscountUsages adds the "discount_usages" edges to the DiscountUsage entity.
func (dc *DealCreate) AddDiscountUsages(d ...*DiscountUsage) *DealCreate {
	ids := make([]int, len(d))
	for i := range d {
		ids[i] = d[i].ID
	}
	return dc.AddDiscountUsageIDs(ids...)
}

// AddTagDealIDs adds the "tag_deal" edge to the TagDeal entity by IDs.
func (dc *DealCreate) AddTagDealIDs(ids ...int) *DealCreate {
	dc.mutation.AddTagDealIDs(ids...)
	return dc
}

// AddTagDeal adds the "tag_deal" edges to the TagDeal entity.
func (dc *DealCreate) AddTagDeal(t ...*TagDeal) *DealCreate {
	ids := make([]int, len(t))
	for i := range t {
		ids[i] = t[i].ID
	}
	return dc.AddTagDealIDs(ids...)
}

// Mutation returns the DealMutation object of the builder.
func (dc *DealCreate) Mutation() *DealMutation {
	return dc.mutation
}

// Save creates the Deal in the database.
func (dc *DealCreate) Save(ctx context.Context) (*Deal, error) {
	if err := dc.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, dc.sqlSave, dc.mutation, dc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (dc *DealCreate) SaveX(ctx context.Context) *Deal {
	v, err := dc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (dc *DealCreate) Exec(ctx context.Context) error {
	_, err := dc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (dc *DealCreate) ExecX(ctx context.Context) {
	if err := dc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (dc *DealCreate) defaults() error {
	if _, ok := dc.mutation.Status(); !ok {
		v := deal.DefaultStatus
		dc.mutation.SetStatus(v)
	}
	if _, ok := dc.mutation.Version(); !ok {
		v := deal.DefaultVersion
		dc.mutation.SetVersion(v)
	}
	if _, ok := dc.mutation.CreatedAt(); !ok {
		if deal.DefaultCreatedAt == nil {
			return fmt.Errorf("ent: uninitialized deal.DefaultCreatedAt (forgotten import ent/runtime?)")
		}
		v := deal.DefaultCreatedAt()
		dc.mutation.SetCreatedAt(v)
	}
	if _, ok := dc.mutation.UpdatedAt(); !ok {
		if deal.DefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized deal.DefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := deal.DefaultUpdatedAt()
		dc.mutation.SetUpdatedAt(v)
	}
	if _, ok := dc.mutation.TotalAmount(); !ok {
		v := deal.DefaultTotalAmount
		dc.mutation.SetTotalAmount(v)
	}
	if _, ok := dc.mutation.State(); !ok {
		v := deal.DefaultState
		dc.mutation.SetState(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (dc *DealCreate) check() error {
	if _, ok := dc.mutation.Status(); !ok {
		return &ValidationError{Name: "status", err: errors.New(`ent: missing required field "Deal.status"`)}
	}
	if _, ok := dc.mutation.Version(); !ok {
		return &ValidationError{Name: "version", err: errors.New(`ent: missing required field "Deal.version"`)}
	}
	if _, ok := dc.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "Deal.created_at"`)}
	}
	if _, ok := dc.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "Deal.updated_at"`)}
	}
	if _, ok := dc.mutation.TotalAmount(); !ok {
		return &ValidationError{Name: "total_amount", err: errors.New(`ent: missing required field "Deal.total_amount"`)}
	}
	if _, ok := dc.mutation.State(); !ok {
		return &ValidationError{Name: "state", err: errors.New(`ent: missing required field "Deal.state"`)}
	}
	if v, ok := dc.mutation.State(); ok {
		if err := deal.StateValidator(v); err != nil {
			return &ValidationError{Name: "state", err: fmt.Errorf(`ent: validator failed for field "Deal.state": %w`, err)}
		}
	}
	return nil
}

func (dc *DealCreate) sqlSave(ctx context.Context) (*Deal, error) {
	if err := dc.check(); err != nil {
		return nil, err
	}
	_node, _spec := dc.createSpec()
	if err := sqlgraph.CreateNode(ctx, dc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	id := _spec.ID.Value.(int64)
	_node.ID = int(id)
	dc.mutation.id = &_node.ID
	dc.mutation.done = true
	return _node, nil
}

func (dc *DealCreate) createSpec() (*Deal, *sqlgraph.CreateSpec) {
	var (
		_node = &Deal{config: dc.config}
		_spec = sqlgraph.NewCreateSpec(deal.Table, sqlgraph.NewFieldSpec(deal.FieldID, field.TypeInt))
	)
	if value, ok := dc.mutation.DeletedAt(); ok {
		_spec.SetField(deal.FieldDeletedAt, field.TypeTime, value)
		_node.DeletedAt = value
	}
	if value, ok := dc.mutation.Status(); ok {
		_spec.SetField(deal.FieldStatus, field.TypeInt8, value)
		_node.Status = value
	}
	if value, ok := dc.mutation.Version(); ok {
		_spec.SetField(deal.FieldVersion, field.TypeInt, value)
		_node.Version = value
	}
	if value, ok := dc.mutation.CreatedAt(); ok {
		_spec.SetField(deal.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := dc.mutation.UpdatedAt(); ok {
		_spec.SetField(deal.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	if value, ok := dc.mutation.ParentDealID(); ok {
		_spec.SetField(deal.FieldParentDealID, field.TypeInt, value)
		_node.ParentDealID = &value
	}
	if value, ok := dc.mutation.TotalAmount(); ok {
		_spec.SetField(deal.FieldTotalAmount, field.TypeFloat64, value)
		_node.TotalAmount = value
	}
	if value, ok := dc.mutation.StageHistory(); ok {
		_spec.SetField(deal.FieldStageHistory, field.TypeJSON, value)
		_node.StageHistory = value
	}
	if value, ok := dc.mutation.Name(); ok {
		_spec.SetField(deal.FieldName, field.TypeString, value)
		_node.Name = value
	}
	if value, ok := dc.mutation.State(); ok {
		_spec.SetField(deal.FieldState, field.TypeEnum, value)
		_node.State = value
	}
	if nodes := dc.mutation.PersonIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   deal.PersonTable,
			Columns: []string{deal.PersonColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(person.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.PersonID = nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := dc.mutation.StageIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   deal.StageTable,
			Columns: []string{deal.StageColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(stage.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.StageID = &nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := dc.mutation.AttachmentsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   deal.AttachmentsTable,
			Columns: []string{deal.AttachmentsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(attachment.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := dc.mutation.PlansIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   deal.PlansTable,
			Columns: []string{deal.PlansColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(installmentplan.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := dc.mutation.BillsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   deal.BillsTable,
			Columns: []string{deal.BillsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(bill.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := dc.mutation.TracksIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   deal.TracksTable,
			Columns: []string{deal.TracksColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(track.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := dc.mutation.TasksIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   deal.TasksTable,
			Columns: []string{deal.TasksColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(task.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := dc.mutation.DealAssignmentIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   deal.DealAssignmentTable,
			Columns: []string{deal.DealAssignmentColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(dealuser.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := dc.mutation.TagsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: false,
			Table:   deal.TagsTable,
			Columns: deal.TagsPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tag.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		createE := &TagDealCreate{config: dc.config, mutation: newTagDealMutation(dc.config, OpCreate)}
		createE.defaults()
		_, specE := createE.createSpec()
		edge.Target.Fields = specE.Fields
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := dc.mutation.DepositsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   deal.DepositsTable,
			Columns: []string{deal.DepositsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(deposit.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := dc.mutation.DiscountUsagesIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   deal.DiscountUsagesTable,
			Columns: []string{deal.DiscountUsagesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(discountusage.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := dc.mutation.TagDealIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   deal.TagDealTable,
			Columns: []string{deal.TagDealColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tagdeal.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	return _node, _spec
}

// DealCreateBulk is the builder for creating many Deal entities in bulk.
type DealCreateBulk struct {
	config
	err      error
	builders []*DealCreate
}

// Save creates the Deal entities in the database.
func (dcb *DealCreateBulk) Save(ctx context.Context) ([]*Deal, error) {
	if dcb.err != nil {
		return nil, dcb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(dcb.builders))
	nodes := make([]*Deal, len(dcb.builders))
	mutators := make([]Mutator, len(dcb.builders))
	for i := range dcb.builders {
		func(i int, root context.Context) {
			builder := dcb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*DealMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, dcb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, dcb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = int(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, dcb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (dcb *DealCreateBulk) SaveX(ctx context.Context) []*Deal {
	v, err := dcb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (dcb *DealCreateBulk) Exec(ctx context.Context) error {
	_, err := dcb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (dcb *DealCreateBulk) ExecX(ctx context.Context) {
	if err := dcb.Exec(ctx); err != nil {
		panic(err)
	}
}
