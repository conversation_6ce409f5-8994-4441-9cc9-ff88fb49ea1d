// Code generated by ent, DO NOT EDIT.

package messagehistory

import (
	"fmt"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

const (
	// Label holds the string label denoting the messagehistory type in the database.
	Label = "message_history"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldDeletedAt holds the string denoting the deleted_at field in the database.
	FieldDeletedAt = "deleted_at"
	// FieldStatus holds the string denoting the status field in the database.
	FieldStatus = "status"
	// FieldVersion holds the string denoting the version field in the database.
	FieldVersion = "version"
	// FieldCreatedAt holds the string denoting the created_at field in the database.
	FieldCreatedAt = "created_at"
	// FieldUpdatedAt holds the string denoting the updated_at field in the database.
	FieldUpdatedAt = "updated_at"
	// FieldPersonID holds the string denoting the person_id field in the database.
	FieldPersonID = "person_id"
	// FieldUserID holds the string denoting the user_id field in the database.
	FieldUserID = "user_id"
	// FieldMessageID holds the string denoting the message_id field in the database.
	FieldMessageID = "message_id"
	// FieldPhone holds the string denoting the phone field in the database.
	FieldPhone = "phone"
	// FieldType holds the string denoting the type field in the database.
	FieldType = "type"
	// FieldContent holds the string denoting the content field in the database.
	FieldContent = "content"
	// FieldZnsData holds the string denoting the zns_data field in the database.
	FieldZnsData = "zns_data"
	// FieldErrorCode holds the string denoting the error_code field in the database.
	FieldErrorCode = "error_code"
	// FieldMessageStatus holds the string denoting the message_status field in the database.
	FieldMessageStatus = "message_status"
	// FieldDeliveredAt holds the string denoting the delivered_at field in the database.
	FieldDeliveredAt = "delivered_at"
	// EdgePerson holds the string denoting the person edge name in mutations.
	EdgePerson = "person"
	// EdgeUser holds the string denoting the user edge name in mutations.
	EdgeUser = "user"
	// Table holds the table name of the messagehistory in the database.
	Table = "message_history"
	// PersonTable is the table that holds the person relation/edge.
	PersonTable = "message_history"
	// PersonInverseTable is the table name for the Person entity.
	// It exists in this package in order to avoid circular dependency with the "person" package.
	PersonInverseTable = "person"
	// PersonColumn is the table column denoting the person relation/edge.
	PersonColumn = "person_id"
	// UserTable is the table that holds the user relation/edge.
	UserTable = "message_history"
	// UserInverseTable is the table name for the User entity.
	// It exists in this package in order to avoid circular dependency with the "user" package.
	UserInverseTable = "user"
	// UserColumn is the table column denoting the user relation/edge.
	UserColumn = "user_id"
)

// Columns holds all SQL columns for messagehistory fields.
var Columns = []string{
	FieldID,
	FieldDeletedAt,
	FieldStatus,
	FieldVersion,
	FieldCreatedAt,
	FieldUpdatedAt,
	FieldPersonID,
	FieldUserID,
	FieldMessageID,
	FieldPhone,
	FieldType,
	FieldContent,
	FieldZnsData,
	FieldErrorCode,
	FieldMessageStatus,
	FieldDeliveredAt,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

// Note that the variables below are initialized by the runtime
// package on the initialization of the application. Therefore,
// it should be imported in the main as follows:
//
//	import _ "bcare/ent/runtime"
var (
	Hooks        [2]ent.Hook
	Interceptors [1]ent.Interceptor
	// DefaultStatus holds the default value on creation for the "status" field.
	DefaultStatus int8
	// DefaultVersion holds the default value on creation for the "version" field.
	DefaultVersion int
	// DefaultCreatedAt holds the default value on creation for the "created_at" field.
	DefaultCreatedAt func() time.Time
	// DefaultUpdatedAt holds the default value on creation for the "updated_at" field.
	DefaultUpdatedAt func() time.Time
	// UpdateDefaultUpdatedAt holds the default value on update for the "updated_at" field.
	UpdateDefaultUpdatedAt func() time.Time
)

// Type defines the type for the "type" enum field.
type Type string

// Type values.
const (
	TypeSms   Type = "sms"
	TypeZns   Type = "zns"
	TypeEmail Type = "email"
)

func (_type Type) String() string {
	return string(_type)
}

// TypeValidator is a validator for the "type" field enum values. It is called by the builders before save.
func TypeValidator(_type Type) error {
	switch _type {
	case TypeSms, TypeZns, TypeEmail:
		return nil
	default:
		return fmt.Errorf("messagehistory: invalid enum value for type field: %q", _type)
	}
}

// MessageStatus defines the type for the "message_status" enum field.
type MessageStatus string

// MessageStatus values.
const (
	MessageStatusSent      MessageStatus = "sent"
	MessageStatusFailed    MessageStatus = "failed"
	MessageStatusDelivered MessageStatus = "delivered"
)

func (ms MessageStatus) String() string {
	return string(ms)
}

// MessageStatusValidator is a validator for the "message_status" field enum values. It is called by the builders before save.
func MessageStatusValidator(ms MessageStatus) error {
	switch ms {
	case MessageStatusSent, MessageStatusFailed, MessageStatusDelivered:
		return nil
	default:
		return fmt.Errorf("messagehistory: invalid enum value for message_status field: %q", ms)
	}
}

// OrderOption defines the ordering options for the MessageHistory queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByDeletedAt orders the results by the deleted_at field.
func ByDeletedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDeletedAt, opts...).ToFunc()
}

// ByStatus orders the results by the status field.
func ByStatus(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldStatus, opts...).ToFunc()
}

// ByVersion orders the results by the version field.
func ByVersion(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldVersion, opts...).ToFunc()
}

// ByCreatedAt orders the results by the created_at field.
func ByCreatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedAt, opts...).ToFunc()
}

// ByUpdatedAt orders the results by the updated_at field.
func ByUpdatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdatedAt, opts...).ToFunc()
}

// ByPersonID orders the results by the person_id field.
func ByPersonID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPersonID, opts...).ToFunc()
}

// ByUserID orders the results by the user_id field.
func ByUserID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUserID, opts...).ToFunc()
}

// ByMessageID orders the results by the message_id field.
func ByMessageID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldMessageID, opts...).ToFunc()
}

// ByPhone orders the results by the phone field.
func ByPhone(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPhone, opts...).ToFunc()
}

// ByType orders the results by the type field.
func ByType(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldType, opts...).ToFunc()
}

// ByContent orders the results by the content field.
func ByContent(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldContent, opts...).ToFunc()
}

// ByZnsData orders the results by the zns_data field.
func ByZnsData(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldZnsData, opts...).ToFunc()
}

// ByErrorCode orders the results by the error_code field.
func ByErrorCode(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldErrorCode, opts...).ToFunc()
}

// ByMessageStatus orders the results by the message_status field.
func ByMessageStatus(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldMessageStatus, opts...).ToFunc()
}

// ByDeliveredAt orders the results by the delivered_at field.
func ByDeliveredAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDeliveredAt, opts...).ToFunc()
}

// ByPersonField orders the results by person field.
func ByPersonField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newPersonStep(), sql.OrderByField(field, opts...))
	}
}

// ByUserField orders the results by user field.
func ByUserField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newUserStep(), sql.OrderByField(field, opts...))
	}
}
func newPersonStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(PersonInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2O, false, PersonTable, PersonColumn),
	)
}
func newUserStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(UserInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2O, false, UserTable, UserColumn),
	)
}
