// Code generated by ent, DO NOT EDIT.

package ent

import (
	"bcare/ent/appointment"
	"bcare/ent/call"
	"bcare/ent/exportjob"
	"bcare/ent/messagehistory"
	"bcare/ent/note"
	"bcare/ent/notification"
	"bcare/ent/person"
	"bcare/ent/personassignment"
	"bcare/ent/predicate"
	"bcare/ent/schedule"
	"bcare/ent/taskassignment"
	"bcare/ent/tasknote"
	"bcare/ent/user"
	"bcare/ent/userdata"
	"context"
	"database/sql/driver"
	"fmt"
	"math"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// UserQuery is the builder for querying User entities.
type UserQuery struct {
	config
	ctx                  *QueryContext
	order                []user.OrderOption
	inters               []Interceptor
	predicates           []predicate.User
	withAssignment       *TaskAssignmentQuery
	withAssigned<PERSON>erson   *PersonQuery
	withNotes            *NoteQuery
	withTaskNotes        *TaskNoteQuery
	withAppointments     *AppointmentQuery
	withCalls            *CallQuery
	withSchedules        *ScheduleQuery
	withMessageHistories *MessageHistoryQuery
	withData             *UserDataQuery
	withExportJobs       *ExportJobQuery
	withNotifications    *NotificationQuery
	withPersonAssignment *PersonAssignmentQuery
	modifiers            []func(*sql.Selector)
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the UserQuery builder.
func (uq *UserQuery) Where(ps ...predicate.User) *UserQuery {
	uq.predicates = append(uq.predicates, ps...)
	return uq
}

// Limit the number of records to be returned by this query.
func (uq *UserQuery) Limit(limit int) *UserQuery {
	uq.ctx.Limit = &limit
	return uq
}

// Offset to start from.
func (uq *UserQuery) Offset(offset int) *UserQuery {
	uq.ctx.Offset = &offset
	return uq
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (uq *UserQuery) Unique(unique bool) *UserQuery {
	uq.ctx.Unique = &unique
	return uq
}

// Order specifies how the records should be ordered.
func (uq *UserQuery) Order(o ...user.OrderOption) *UserQuery {
	uq.order = append(uq.order, o...)
	return uq
}

// QueryAssignment chains the current query on the "assignment" edge.
func (uq *UserQuery) QueryAssignment() *TaskAssignmentQuery {
	query := (&TaskAssignmentClient{config: uq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := uq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := uq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(user.Table, user.FieldID, selector),
			sqlgraph.To(taskassignment.Table, taskassignment.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, user.AssignmentTable, user.AssignmentColumn),
		)
		fromU = sqlgraph.SetNeighbors(uq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// QueryAssignedPerson chains the current query on the "assigned_person" edge.
func (uq *UserQuery) QueryAssignedPerson() *PersonQuery {
	query := (&PersonClient{config: uq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := uq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := uq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(user.Table, user.FieldID, selector),
			sqlgraph.To(person.Table, person.FieldID),
			sqlgraph.Edge(sqlgraph.M2M, true, user.AssignedPersonTable, user.AssignedPersonPrimaryKey...),
		)
		fromU = sqlgraph.SetNeighbors(uq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// QueryNotes chains the current query on the "notes" edge.
func (uq *UserQuery) QueryNotes() *NoteQuery {
	query := (&NoteClient{config: uq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := uq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := uq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(user.Table, user.FieldID, selector),
			sqlgraph.To(note.Table, note.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, user.NotesTable, user.NotesColumn),
		)
		fromU = sqlgraph.SetNeighbors(uq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// QueryTaskNotes chains the current query on the "task_notes" edge.
func (uq *UserQuery) QueryTaskNotes() *TaskNoteQuery {
	query := (&TaskNoteClient{config: uq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := uq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := uq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(user.Table, user.FieldID, selector),
			sqlgraph.To(tasknote.Table, tasknote.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, user.TaskNotesTable, user.TaskNotesColumn),
		)
		fromU = sqlgraph.SetNeighbors(uq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// QueryAppointments chains the current query on the "appointments" edge.
func (uq *UserQuery) QueryAppointments() *AppointmentQuery {
	query := (&AppointmentClient{config: uq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := uq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := uq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(user.Table, user.FieldID, selector),
			sqlgraph.To(appointment.Table, appointment.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, user.AppointmentsTable, user.AppointmentsColumn),
		)
		fromU = sqlgraph.SetNeighbors(uq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// QueryCalls chains the current query on the "calls" edge.
func (uq *UserQuery) QueryCalls() *CallQuery {
	query := (&CallClient{config: uq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := uq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := uq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(user.Table, user.FieldID, selector),
			sqlgraph.To(call.Table, call.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, user.CallsTable, user.CallsColumn),
		)
		fromU = sqlgraph.SetNeighbors(uq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// QuerySchedules chains the current query on the "schedules" edge.
func (uq *UserQuery) QuerySchedules() *ScheduleQuery {
	query := (&ScheduleClient{config: uq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := uq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := uq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(user.Table, user.FieldID, selector),
			sqlgraph.To(schedule.Table, schedule.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, user.SchedulesTable, user.SchedulesColumn),
		)
		fromU = sqlgraph.SetNeighbors(uq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// QueryMessageHistories chains the current query on the "message_histories" edge.
func (uq *UserQuery) QueryMessageHistories() *MessageHistoryQuery {
	query := (&MessageHistoryClient{config: uq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := uq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := uq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(user.Table, user.FieldID, selector),
			sqlgraph.To(messagehistory.Table, messagehistory.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, user.MessageHistoriesTable, user.MessageHistoriesColumn),
		)
		fromU = sqlgraph.SetNeighbors(uq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// QueryData chains the current query on the "data" edge.
func (uq *UserQuery) QueryData() *UserDataQuery {
	query := (&UserDataClient{config: uq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := uq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := uq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(user.Table, user.FieldID, selector),
			sqlgraph.To(userdata.Table, userdata.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, user.DataTable, user.DataColumn),
		)
		fromU = sqlgraph.SetNeighbors(uq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// QueryExportJobs chains the current query on the "export_jobs" edge.
func (uq *UserQuery) QueryExportJobs() *ExportJobQuery {
	query := (&ExportJobClient{config: uq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := uq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := uq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(user.Table, user.FieldID, selector),
			sqlgraph.To(exportjob.Table, exportjob.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, user.ExportJobsTable, user.ExportJobsColumn),
		)
		fromU = sqlgraph.SetNeighbors(uq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// QueryNotifications chains the current query on the "notifications" edge.
func (uq *UserQuery) QueryNotifications() *NotificationQuery {
	query := (&NotificationClient{config: uq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := uq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := uq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(user.Table, user.FieldID, selector),
			sqlgraph.To(notification.Table, notification.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, user.NotificationsTable, user.NotificationsColumn),
		)
		fromU = sqlgraph.SetNeighbors(uq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// QueryPersonAssignment chains the current query on the "person_assignment" edge.
func (uq *UserQuery) QueryPersonAssignment() *PersonAssignmentQuery {
	query := (&PersonAssignmentClient{config: uq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := uq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := uq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(user.Table, user.FieldID, selector),
			sqlgraph.To(personassignment.Table, personassignment.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, user.PersonAssignmentTable, user.PersonAssignmentColumn),
		)
		fromU = sqlgraph.SetNeighbors(uq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// First returns the first User entity from the query.
// Returns a *NotFoundError when no User was found.
func (uq *UserQuery) First(ctx context.Context) (*User, error) {
	nodes, err := uq.Limit(1).All(setContextOp(ctx, uq.ctx, ent.OpQueryFirst))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{user.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (uq *UserQuery) FirstX(ctx context.Context) *User {
	node, err := uq.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first User ID from the query.
// Returns a *NotFoundError when no User ID was found.
func (uq *UserQuery) FirstID(ctx context.Context) (id int, err error) {
	var ids []int
	if ids, err = uq.Limit(1).IDs(setContextOp(ctx, uq.ctx, ent.OpQueryFirstID)); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{user.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (uq *UserQuery) FirstIDX(ctx context.Context) int {
	id, err := uq.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single User entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one User entity is found.
// Returns a *NotFoundError when no User entities are found.
func (uq *UserQuery) Only(ctx context.Context) (*User, error) {
	nodes, err := uq.Limit(2).All(setContextOp(ctx, uq.ctx, ent.OpQueryOnly))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{user.Label}
	default:
		return nil, &NotSingularError{user.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (uq *UserQuery) OnlyX(ctx context.Context) *User {
	node, err := uq.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only User ID in the query.
// Returns a *NotSingularError when more than one User ID is found.
// Returns a *NotFoundError when no entities are found.
func (uq *UserQuery) OnlyID(ctx context.Context) (id int, err error) {
	var ids []int
	if ids, err = uq.Limit(2).IDs(setContextOp(ctx, uq.ctx, ent.OpQueryOnlyID)); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{user.Label}
	default:
		err = &NotSingularError{user.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (uq *UserQuery) OnlyIDX(ctx context.Context) int {
	id, err := uq.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of Users.
func (uq *UserQuery) All(ctx context.Context) ([]*User, error) {
	ctx = setContextOp(ctx, uq.ctx, ent.OpQueryAll)
	if err := uq.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*User, *UserQuery]()
	return withInterceptors[[]*User](ctx, uq, qr, uq.inters)
}

// AllX is like All, but panics if an error occurs.
func (uq *UserQuery) AllX(ctx context.Context) []*User {
	nodes, err := uq.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of User IDs.
func (uq *UserQuery) IDs(ctx context.Context) (ids []int, err error) {
	if uq.ctx.Unique == nil && uq.path != nil {
		uq.Unique(true)
	}
	ctx = setContextOp(ctx, uq.ctx, ent.OpQueryIDs)
	if err = uq.Select(user.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (uq *UserQuery) IDsX(ctx context.Context) []int {
	ids, err := uq.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (uq *UserQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, uq.ctx, ent.OpQueryCount)
	if err := uq.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, uq, querierCount[*UserQuery](), uq.inters)
}

// CountX is like Count, but panics if an error occurs.
func (uq *UserQuery) CountX(ctx context.Context) int {
	count, err := uq.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (uq *UserQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, uq.ctx, ent.OpQueryExist)
	switch _, err := uq.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("ent: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (uq *UserQuery) ExistX(ctx context.Context) bool {
	exist, err := uq.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the UserQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (uq *UserQuery) Clone() *UserQuery {
	if uq == nil {
		return nil
	}
	return &UserQuery{
		config:               uq.config,
		ctx:                  uq.ctx.Clone(),
		order:                append([]user.OrderOption{}, uq.order...),
		inters:               append([]Interceptor{}, uq.inters...),
		predicates:           append([]predicate.User{}, uq.predicates...),
		withAssignment:       uq.withAssignment.Clone(),
		withAssignedPerson:   uq.withAssignedPerson.Clone(),
		withNotes:            uq.withNotes.Clone(),
		withTaskNotes:        uq.withTaskNotes.Clone(),
		withAppointments:     uq.withAppointments.Clone(),
		withCalls:            uq.withCalls.Clone(),
		withSchedules:        uq.withSchedules.Clone(),
		withMessageHistories: uq.withMessageHistories.Clone(),
		withData:             uq.withData.Clone(),
		withExportJobs:       uq.withExportJobs.Clone(),
		withNotifications:    uq.withNotifications.Clone(),
		withPersonAssignment: uq.withPersonAssignment.Clone(),
		// clone intermediate query.
		sql:       uq.sql.Clone(),
		path:      uq.path,
		modifiers: append([]func(*sql.Selector){}, uq.modifiers...),
	}
}

// WithAssignment tells the query-builder to eager-load the nodes that are connected to
// the "assignment" edge. The optional arguments are used to configure the query builder of the edge.
func (uq *UserQuery) WithAssignment(opts ...func(*TaskAssignmentQuery)) *UserQuery {
	query := (&TaskAssignmentClient{config: uq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	uq.withAssignment = query
	return uq
}

// WithAssignedPerson tells the query-builder to eager-load the nodes that are connected to
// the "assigned_person" edge. The optional arguments are used to configure the query builder of the edge.
func (uq *UserQuery) WithAssignedPerson(opts ...func(*PersonQuery)) *UserQuery {
	query := (&PersonClient{config: uq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	uq.withAssignedPerson = query
	return uq
}

// WithNotes tells the query-builder to eager-load the nodes that are connected to
// the "notes" edge. The optional arguments are used to configure the query builder of the edge.
func (uq *UserQuery) WithNotes(opts ...func(*NoteQuery)) *UserQuery {
	query := (&NoteClient{config: uq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	uq.withNotes = query
	return uq
}

// WithTaskNotes tells the query-builder to eager-load the nodes that are connected to
// the "task_notes" edge. The optional arguments are used to configure the query builder of the edge.
func (uq *UserQuery) WithTaskNotes(opts ...func(*TaskNoteQuery)) *UserQuery {
	query := (&TaskNoteClient{config: uq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	uq.withTaskNotes = query
	return uq
}

// WithAppointments tells the query-builder to eager-load the nodes that are connected to
// the "appointments" edge. The optional arguments are used to configure the query builder of the edge.
func (uq *UserQuery) WithAppointments(opts ...func(*AppointmentQuery)) *UserQuery {
	query := (&AppointmentClient{config: uq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	uq.withAppointments = query
	return uq
}

// WithCalls tells the query-builder to eager-load the nodes that are connected to
// the "calls" edge. The optional arguments are used to configure the query builder of the edge.
func (uq *UserQuery) WithCalls(opts ...func(*CallQuery)) *UserQuery {
	query := (&CallClient{config: uq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	uq.withCalls = query
	return uq
}

// WithSchedules tells the query-builder to eager-load the nodes that are connected to
// the "schedules" edge. The optional arguments are used to configure the query builder of the edge.
func (uq *UserQuery) WithSchedules(opts ...func(*ScheduleQuery)) *UserQuery {
	query := (&ScheduleClient{config: uq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	uq.withSchedules = query
	return uq
}

// WithMessageHistories tells the query-builder to eager-load the nodes that are connected to
// the "message_histories" edge. The optional arguments are used to configure the query builder of the edge.
func (uq *UserQuery) WithMessageHistories(opts ...func(*MessageHistoryQuery)) *UserQuery {
	query := (&MessageHistoryClient{config: uq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	uq.withMessageHistories = query
	return uq
}

// WithData tells the query-builder to eager-load the nodes that are connected to
// the "data" edge. The optional arguments are used to configure the query builder of the edge.
func (uq *UserQuery) WithData(opts ...func(*UserDataQuery)) *UserQuery {
	query := (&UserDataClient{config: uq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	uq.withData = query
	return uq
}

// WithExportJobs tells the query-builder to eager-load the nodes that are connected to
// the "export_jobs" edge. The optional arguments are used to configure the query builder of the edge.
func (uq *UserQuery) WithExportJobs(opts ...func(*ExportJobQuery)) *UserQuery {
	query := (&ExportJobClient{config: uq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	uq.withExportJobs = query
	return uq
}

// WithNotifications tells the query-builder to eager-load the nodes that are connected to
// the "notifications" edge. The optional arguments are used to configure the query builder of the edge.
func (uq *UserQuery) WithNotifications(opts ...func(*NotificationQuery)) *UserQuery {
	query := (&NotificationClient{config: uq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	uq.withNotifications = query
	return uq
}

// WithPersonAssignment tells the query-builder to eager-load the nodes that are connected to
// the "person_assignment" edge. The optional arguments are used to configure the query builder of the edge.
func (uq *UserQuery) WithPersonAssignment(opts ...func(*PersonAssignmentQuery)) *UserQuery {
	query := (&PersonAssignmentClient{config: uq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	uq.withPersonAssignment = query
	return uq
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		DeletedAt time.Time `json:"deleted_at,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.User.Query().
//		GroupBy(user.FieldDeletedAt).
//		Aggregate(ent.Count()).
//		Scan(ctx, &v)
func (uq *UserQuery) GroupBy(field string, fields ...string) *UserGroupBy {
	uq.ctx.Fields = append([]string{field}, fields...)
	grbuild := &UserGroupBy{build: uq}
	grbuild.flds = &uq.ctx.Fields
	grbuild.label = user.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		DeletedAt time.Time `json:"deleted_at,omitempty"`
//	}
//
//	client.User.Query().
//		Select(user.FieldDeletedAt).
//		Scan(ctx, &v)
func (uq *UserQuery) Select(fields ...string) *UserSelect {
	uq.ctx.Fields = append(uq.ctx.Fields, fields...)
	sbuild := &UserSelect{UserQuery: uq}
	sbuild.label = user.Label
	sbuild.flds, sbuild.scan = &uq.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a UserSelect configured with the given aggregations.
func (uq *UserQuery) Aggregate(fns ...AggregateFunc) *UserSelect {
	return uq.Select().Aggregate(fns...)
}

func (uq *UserQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range uq.inters {
		if inter == nil {
			return fmt.Errorf("ent: uninitialized interceptor (forgotten import ent/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, uq); err != nil {
				return err
			}
		}
	}
	for _, f := range uq.ctx.Fields {
		if !user.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
		}
	}
	if uq.path != nil {
		prev, err := uq.path(ctx)
		if err != nil {
			return err
		}
		uq.sql = prev
	}
	return nil
}

func (uq *UserQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*User, error) {
	var (
		nodes       = []*User{}
		_spec       = uq.querySpec()
		loadedTypes = [12]bool{
			uq.withAssignment != nil,
			uq.withAssignedPerson != nil,
			uq.withNotes != nil,
			uq.withTaskNotes != nil,
			uq.withAppointments != nil,
			uq.withCalls != nil,
			uq.withSchedules != nil,
			uq.withMessageHistories != nil,
			uq.withData != nil,
			uq.withExportJobs != nil,
			uq.withNotifications != nil,
			uq.withPersonAssignment != nil,
		}
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*User).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &User{config: uq.config}
		nodes = append(nodes, node)
		node.Edges.loadedTypes = loadedTypes
		return node.assignValues(columns, values)
	}
	if len(uq.modifiers) > 0 {
		_spec.Modifiers = uq.modifiers
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, uq.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	if query := uq.withAssignment; query != nil {
		if err := uq.loadAssignment(ctx, query, nodes,
			func(n *User) { n.Edges.Assignment = []*TaskAssignment{} },
			func(n *User, e *TaskAssignment) { n.Edges.Assignment = append(n.Edges.Assignment, e) }); err != nil {
			return nil, err
		}
	}
	if query := uq.withAssignedPerson; query != nil {
		if err := uq.loadAssignedPerson(ctx, query, nodes,
			func(n *User) { n.Edges.AssignedPerson = []*Person{} },
			func(n *User, e *Person) { n.Edges.AssignedPerson = append(n.Edges.AssignedPerson, e) }); err != nil {
			return nil, err
		}
	}
	if query := uq.withNotes; query != nil {
		if err := uq.loadNotes(ctx, query, nodes,
			func(n *User) { n.Edges.Notes = []*Note{} },
			func(n *User, e *Note) { n.Edges.Notes = append(n.Edges.Notes, e) }); err != nil {
			return nil, err
		}
	}
	if query := uq.withTaskNotes; query != nil {
		if err := uq.loadTaskNotes(ctx, query, nodes,
			func(n *User) { n.Edges.TaskNotes = []*TaskNote{} },
			func(n *User, e *TaskNote) { n.Edges.TaskNotes = append(n.Edges.TaskNotes, e) }); err != nil {
			return nil, err
		}
	}
	if query := uq.withAppointments; query != nil {
		if err := uq.loadAppointments(ctx, query, nodes,
			func(n *User) { n.Edges.Appointments = []*Appointment{} },
			func(n *User, e *Appointment) { n.Edges.Appointments = append(n.Edges.Appointments, e) }); err != nil {
			return nil, err
		}
	}
	if query := uq.withCalls; query != nil {
		if err := uq.loadCalls(ctx, query, nodes,
			func(n *User) { n.Edges.Calls = []*Call{} },
			func(n *User, e *Call) { n.Edges.Calls = append(n.Edges.Calls, e) }); err != nil {
			return nil, err
		}
	}
	if query := uq.withSchedules; query != nil {
		if err := uq.loadSchedules(ctx, query, nodes,
			func(n *User) { n.Edges.Schedules = []*Schedule{} },
			func(n *User, e *Schedule) { n.Edges.Schedules = append(n.Edges.Schedules, e) }); err != nil {
			return nil, err
		}
	}
	if query := uq.withMessageHistories; query != nil {
		if err := uq.loadMessageHistories(ctx, query, nodes,
			func(n *User) { n.Edges.MessageHistories = []*MessageHistory{} },
			func(n *User, e *MessageHistory) { n.Edges.MessageHistories = append(n.Edges.MessageHistories, e) }); err != nil {
			return nil, err
		}
	}
	if query := uq.withData; query != nil {
		if err := uq.loadData(ctx, query, nodes,
			func(n *User) { n.Edges.Data = []*UserData{} },
			func(n *User, e *UserData) { n.Edges.Data = append(n.Edges.Data, e) }); err != nil {
			return nil, err
		}
	}
	if query := uq.withExportJobs; query != nil {
		if err := uq.loadExportJobs(ctx, query, nodes,
			func(n *User) { n.Edges.ExportJobs = []*ExportJob{} },
			func(n *User, e *ExportJob) { n.Edges.ExportJobs = append(n.Edges.ExportJobs, e) }); err != nil {
			return nil, err
		}
	}
	if query := uq.withNotifications; query != nil {
		if err := uq.loadNotifications(ctx, query, nodes,
			func(n *User) { n.Edges.Notifications = []*Notification{} },
			func(n *User, e *Notification) { n.Edges.Notifications = append(n.Edges.Notifications, e) }); err != nil {
			return nil, err
		}
	}
	if query := uq.withPersonAssignment; query != nil {
		if err := uq.loadPersonAssignment(ctx, query, nodes,
			func(n *User) { n.Edges.PersonAssignment = []*PersonAssignment{} },
			func(n *User, e *PersonAssignment) { n.Edges.PersonAssignment = append(n.Edges.PersonAssignment, e) }); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

func (uq *UserQuery) loadAssignment(ctx context.Context, query *TaskAssignmentQuery, nodes []*User, init func(*User), assign func(*User, *TaskAssignment)) error {
	fks := make([]driver.Value, 0, len(nodes))
	nodeids := make(map[int]*User)
	for i := range nodes {
		fks = append(fks, nodes[i].ID)
		nodeids[nodes[i].ID] = nodes[i]
		if init != nil {
			init(nodes[i])
		}
	}
	if len(query.ctx.Fields) > 0 {
		query.ctx.AppendFieldOnce(taskassignment.FieldUserID)
	}
	query.Where(predicate.TaskAssignment(func(s *sql.Selector) {
		s.Where(sql.InValues(s.C(user.AssignmentColumn), fks...))
	}))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		fk := n.UserID
		node, ok := nodeids[fk]
		if !ok {
			return fmt.Errorf(`unexpected referenced foreign-key "user_id" returned %v for node %v`, fk, n.ID)
		}
		assign(node, n)
	}
	return nil
}
func (uq *UserQuery) loadAssignedPerson(ctx context.Context, query *PersonQuery, nodes []*User, init func(*User), assign func(*User, *Person)) error {
	edgeIDs := make([]driver.Value, len(nodes))
	byID := make(map[int]*User)
	nids := make(map[int]map[*User]struct{})
	for i, node := range nodes {
		edgeIDs[i] = node.ID
		byID[node.ID] = node
		if init != nil {
			init(node)
		}
	}
	query.Where(func(s *sql.Selector) {
		joinT := sql.Table(user.AssignedPersonTable)
		s.Join(joinT).On(s.C(person.FieldID), joinT.C(user.AssignedPersonPrimaryKey[0]))
		s.Where(sql.InValues(joinT.C(user.AssignedPersonPrimaryKey[1]), edgeIDs...))
		columns := s.SelectedColumns()
		s.Select(joinT.C(user.AssignedPersonPrimaryKey[1]))
		s.AppendSelect(columns...)
		s.SetDistinct(false)
	})
	if err := query.prepareQuery(ctx); err != nil {
		return err
	}
	qr := QuerierFunc(func(ctx context.Context, q Query) (Value, error) {
		return query.sqlAll(ctx, func(_ context.Context, spec *sqlgraph.QuerySpec) {
			assign := spec.Assign
			values := spec.ScanValues
			spec.ScanValues = func(columns []string) ([]any, error) {
				values, err := values(columns[1:])
				if err != nil {
					return nil, err
				}
				return append([]any{new(sql.NullInt64)}, values...), nil
			}
			spec.Assign = func(columns []string, values []any) error {
				outValue := int(values[0].(*sql.NullInt64).Int64)
				inValue := int(values[1].(*sql.NullInt64).Int64)
				if nids[inValue] == nil {
					nids[inValue] = map[*User]struct{}{byID[outValue]: {}}
					return assign(columns[1:], values[1:])
				}
				nids[inValue][byID[outValue]] = struct{}{}
				return nil
			}
		})
	})
	neighbors, err := withInterceptors[[]*Person](ctx, query, qr, query.inters)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		nodes, ok := nids[n.ID]
		if !ok {
			return fmt.Errorf(`unexpected "assigned_person" node returned %v`, n.ID)
		}
		for kn := range nodes {
			assign(kn, n)
		}
	}
	return nil
}
func (uq *UserQuery) loadNotes(ctx context.Context, query *NoteQuery, nodes []*User, init func(*User), assign func(*User, *Note)) error {
	fks := make([]driver.Value, 0, len(nodes))
	nodeids := make(map[int]*User)
	for i := range nodes {
		fks = append(fks, nodes[i].ID)
		nodeids[nodes[i].ID] = nodes[i]
		if init != nil {
			init(nodes[i])
		}
	}
	if len(query.ctx.Fields) > 0 {
		query.ctx.AppendFieldOnce(note.FieldUserID)
	}
	query.Where(predicate.Note(func(s *sql.Selector) {
		s.Where(sql.InValues(s.C(user.NotesColumn), fks...))
	}))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		fk := n.UserID
		node, ok := nodeids[fk]
		if !ok {
			return fmt.Errorf(`unexpected referenced foreign-key "user_id" returned %v for node %v`, fk, n.ID)
		}
		assign(node, n)
	}
	return nil
}
func (uq *UserQuery) loadTaskNotes(ctx context.Context, query *TaskNoteQuery, nodes []*User, init func(*User), assign func(*User, *TaskNote)) error {
	fks := make([]driver.Value, 0, len(nodes))
	nodeids := make(map[int]*User)
	for i := range nodes {
		fks = append(fks, nodes[i].ID)
		nodeids[nodes[i].ID] = nodes[i]
		if init != nil {
			init(nodes[i])
		}
	}
	if len(query.ctx.Fields) > 0 {
		query.ctx.AppendFieldOnce(tasknote.FieldUserID)
	}
	query.Where(predicate.TaskNote(func(s *sql.Selector) {
		s.Where(sql.InValues(s.C(user.TaskNotesColumn), fks...))
	}))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		fk := n.UserID
		node, ok := nodeids[fk]
		if !ok {
			return fmt.Errorf(`unexpected referenced foreign-key "user_id" returned %v for node %v`, fk, n.ID)
		}
		assign(node, n)
	}
	return nil
}
func (uq *UserQuery) loadAppointments(ctx context.Context, query *AppointmentQuery, nodes []*User, init func(*User), assign func(*User, *Appointment)) error {
	fks := make([]driver.Value, 0, len(nodes))
	nodeids := make(map[int]*User)
	for i := range nodes {
		fks = append(fks, nodes[i].ID)
		nodeids[nodes[i].ID] = nodes[i]
		if init != nil {
			init(nodes[i])
		}
	}
	if len(query.ctx.Fields) > 0 {
		query.ctx.AppendFieldOnce(appointment.FieldDoctorID)
	}
	query.Where(predicate.Appointment(func(s *sql.Selector) {
		s.Where(sql.InValues(s.C(user.AppointmentsColumn), fks...))
	}))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		fk := n.DoctorID
		node, ok := nodeids[fk]
		if !ok {
			return fmt.Errorf(`unexpected referenced foreign-key "doctor_id" returned %v for node %v`, fk, n.ID)
		}
		assign(node, n)
	}
	return nil
}
func (uq *UserQuery) loadCalls(ctx context.Context, query *CallQuery, nodes []*User, init func(*User), assign func(*User, *Call)) error {
	fks := make([]driver.Value, 0, len(nodes))
	nodeids := make(map[int]*User)
	for i := range nodes {
		fks = append(fks, nodes[i].ID)
		nodeids[nodes[i].ID] = nodes[i]
		if init != nil {
			init(nodes[i])
		}
	}
	if len(query.ctx.Fields) > 0 {
		query.ctx.AppendFieldOnce(call.FieldUserID)
	}
	query.Where(predicate.Call(func(s *sql.Selector) {
		s.Where(sql.InValues(s.C(user.CallsColumn), fks...))
	}))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		fk := n.UserID
		node, ok := nodeids[fk]
		if !ok {
			return fmt.Errorf(`unexpected referenced foreign-key "user_id" returned %v for node %v`, fk, n.ID)
		}
		assign(node, n)
	}
	return nil
}
func (uq *UserQuery) loadSchedules(ctx context.Context, query *ScheduleQuery, nodes []*User, init func(*User), assign func(*User, *Schedule)) error {
	fks := make([]driver.Value, 0, len(nodes))
	nodeids := make(map[int]*User)
	for i := range nodes {
		fks = append(fks, nodes[i].ID)
		nodeids[nodes[i].ID] = nodes[i]
		if init != nil {
			init(nodes[i])
		}
	}
	if len(query.ctx.Fields) > 0 {
		query.ctx.AppendFieldOnce(schedule.FieldUserID)
	}
	query.Where(predicate.Schedule(func(s *sql.Selector) {
		s.Where(sql.InValues(s.C(user.SchedulesColumn), fks...))
	}))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		fk := n.UserID
		node, ok := nodeids[fk]
		if !ok {
			return fmt.Errorf(`unexpected referenced foreign-key "user_id" returned %v for node %v`, fk, n.ID)
		}
		assign(node, n)
	}
	return nil
}
func (uq *UserQuery) loadMessageHistories(ctx context.Context, query *MessageHistoryQuery, nodes []*User, init func(*User), assign func(*User, *MessageHistory)) error {
	fks := make([]driver.Value, 0, len(nodes))
	nodeids := make(map[int]*User)
	for i := range nodes {
		fks = append(fks, nodes[i].ID)
		nodeids[nodes[i].ID] = nodes[i]
		if init != nil {
			init(nodes[i])
		}
	}
	if len(query.ctx.Fields) > 0 {
		query.ctx.AppendFieldOnce(messagehistory.FieldUserID)
	}
	query.Where(predicate.MessageHistory(func(s *sql.Selector) {
		s.Where(sql.InValues(s.C(user.MessageHistoriesColumn), fks...))
	}))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		fk := n.UserID
		node, ok := nodeids[fk]
		if !ok {
			return fmt.Errorf(`unexpected referenced foreign-key "user_id" returned %v for node %v`, fk, n.ID)
		}
		assign(node, n)
	}
	return nil
}
func (uq *UserQuery) loadData(ctx context.Context, query *UserDataQuery, nodes []*User, init func(*User), assign func(*User, *UserData)) error {
	fks := make([]driver.Value, 0, len(nodes))
	nodeids := make(map[int]*User)
	for i := range nodes {
		fks = append(fks, nodes[i].ID)
		nodeids[nodes[i].ID] = nodes[i]
		if init != nil {
			init(nodes[i])
		}
	}
	if len(query.ctx.Fields) > 0 {
		query.ctx.AppendFieldOnce(userdata.FieldUserID)
	}
	query.Where(predicate.UserData(func(s *sql.Selector) {
		s.Where(sql.InValues(s.C(user.DataColumn), fks...))
	}))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		fk := n.UserID
		node, ok := nodeids[fk]
		if !ok {
			return fmt.Errorf(`unexpected referenced foreign-key "user_id" returned %v for node %v`, fk, n.ID)
		}
		assign(node, n)
	}
	return nil
}
func (uq *UserQuery) loadExportJobs(ctx context.Context, query *ExportJobQuery, nodes []*User, init func(*User), assign func(*User, *ExportJob)) error {
	fks := make([]driver.Value, 0, len(nodes))
	nodeids := make(map[int]*User)
	for i := range nodes {
		fks = append(fks, nodes[i].ID)
		nodeids[nodes[i].ID] = nodes[i]
		if init != nil {
			init(nodes[i])
		}
	}
	if len(query.ctx.Fields) > 0 {
		query.ctx.AppendFieldOnce(exportjob.FieldUserID)
	}
	query.Where(predicate.ExportJob(func(s *sql.Selector) {
		s.Where(sql.InValues(s.C(user.ExportJobsColumn), fks...))
	}))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		fk := n.UserID
		node, ok := nodeids[fk]
		if !ok {
			return fmt.Errorf(`unexpected referenced foreign-key "user_id" returned %v for node %v`, fk, n.ID)
		}
		assign(node, n)
	}
	return nil
}
func (uq *UserQuery) loadNotifications(ctx context.Context, query *NotificationQuery, nodes []*User, init func(*User), assign func(*User, *Notification)) error {
	fks := make([]driver.Value, 0, len(nodes))
	nodeids := make(map[int]*User)
	for i := range nodes {
		fks = append(fks, nodes[i].ID)
		nodeids[nodes[i].ID] = nodes[i]
		if init != nil {
			init(nodes[i])
		}
	}
	if len(query.ctx.Fields) > 0 {
		query.ctx.AppendFieldOnce(notification.FieldUserID)
	}
	query.Where(predicate.Notification(func(s *sql.Selector) {
		s.Where(sql.InValues(s.C(user.NotificationsColumn), fks...))
	}))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		fk := n.UserID
		node, ok := nodeids[fk]
		if !ok {
			return fmt.Errorf(`unexpected referenced foreign-key "user_id" returned %v for node %v`, fk, n.ID)
		}
		assign(node, n)
	}
	return nil
}
func (uq *UserQuery) loadPersonAssignment(ctx context.Context, query *PersonAssignmentQuery, nodes []*User, init func(*User), assign func(*User, *PersonAssignment)) error {
	fks := make([]driver.Value, 0, len(nodes))
	nodeids := make(map[int]*User)
	for i := range nodes {
		fks = append(fks, nodes[i].ID)
		nodeids[nodes[i].ID] = nodes[i]
		if init != nil {
			init(nodes[i])
		}
	}
	if len(query.ctx.Fields) > 0 {
		query.ctx.AppendFieldOnce(personassignment.FieldUserID)
	}
	query.Where(predicate.PersonAssignment(func(s *sql.Selector) {
		s.Where(sql.InValues(s.C(user.PersonAssignmentColumn), fks...))
	}))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		fk := n.UserID
		node, ok := nodeids[fk]
		if !ok {
			return fmt.Errorf(`unexpected referenced foreign-key "user_id" returned %v for node %v`, fk, n.ID)
		}
		assign(node, n)
	}
	return nil
}

func (uq *UserQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := uq.querySpec()
	if len(uq.modifiers) > 0 {
		_spec.Modifiers = uq.modifiers
	}
	_spec.Node.Columns = uq.ctx.Fields
	if len(uq.ctx.Fields) > 0 {
		_spec.Unique = uq.ctx.Unique != nil && *uq.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, uq.driver, _spec)
}

func (uq *UserQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(user.Table, user.Columns, sqlgraph.NewFieldSpec(user.FieldID, field.TypeInt))
	_spec.From = uq.sql
	if unique := uq.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if uq.path != nil {
		_spec.Unique = true
	}
	if fields := uq.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, user.FieldID)
		for i := range fields {
			if fields[i] != user.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
	}
	if ps := uq.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := uq.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := uq.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := uq.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (uq *UserQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(uq.driver.Dialect())
	t1 := builder.Table(user.Table)
	columns := uq.ctx.Fields
	if len(columns) == 0 {
		columns = user.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if uq.sql != nil {
		selector = uq.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if uq.ctx.Unique != nil && *uq.ctx.Unique {
		selector.Distinct()
	}
	for _, m := range uq.modifiers {
		m(selector)
	}
	for _, p := range uq.predicates {
		p(selector)
	}
	for _, p := range uq.order {
		p(selector)
	}
	if offset := uq.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := uq.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// Modify adds a query modifier for attaching custom logic to queries.
func (uq *UserQuery) Modify(modifiers ...func(s *sql.Selector)) *UserSelect {
	uq.modifiers = append(uq.modifiers, modifiers...)
	return uq.Select()
}

// UserGroupBy is the group-by builder for User entities.
type UserGroupBy struct {
	selector
	build *UserQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (ugb *UserGroupBy) Aggregate(fns ...AggregateFunc) *UserGroupBy {
	ugb.fns = append(ugb.fns, fns...)
	return ugb
}

// Scan applies the selector query and scans the result into the given value.
func (ugb *UserGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, ugb.build.ctx, ent.OpQueryGroupBy)
	if err := ugb.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*UserQuery, *UserGroupBy](ctx, ugb.build, ugb, ugb.build.inters, v)
}

func (ugb *UserGroupBy) sqlScan(ctx context.Context, root *UserQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(ugb.fns))
	for _, fn := range ugb.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*ugb.flds)+len(ugb.fns))
		for _, f := range *ugb.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*ugb.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := ugb.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// UserSelect is the builder for selecting fields of User entities.
type UserSelect struct {
	*UserQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (us *UserSelect) Aggregate(fns ...AggregateFunc) *UserSelect {
	us.fns = append(us.fns, fns...)
	return us
}

// Scan applies the selector query and scans the result into the given value.
func (us *UserSelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, us.ctx, ent.OpQuerySelect)
	if err := us.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*UserQuery, *UserSelect](ctx, us.UserQuery, us, us.inters, v)
}

func (us *UserSelect) sqlScan(ctx context.Context, root *UserQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(us.fns))
	for _, fn := range us.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*us.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := us.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// Modify adds a query modifier for attaching custom logic to queries.
func (us *UserSelect) Modify(modifiers ...func(s *sql.Selector)) *UserSelect {
	us.modifiers = append(us.modifiers, modifiers...)
	return us
}
