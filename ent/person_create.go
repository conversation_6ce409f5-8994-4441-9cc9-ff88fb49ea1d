// Code generated by ent, DO NOT EDIT.

package ent

import (
	"bcare/ent/appointment"
	"bcare/ent/call"
	"bcare/ent/deal"
	"bcare/ent/formsubmission"
	"bcare/ent/installmentplan"
	"bcare/ent/issue"
	"bcare/ent/messagehistory"
	"bcare/ent/person"
	"bcare/ent/personassignment"
	"bcare/ent/personreferral"
	"bcare/ent/tag"
	"bcare/ent/tagperson"
	"bcare/ent/task"
	"bcare/ent/term"
	"bcare/ent/track"
	"bcare/ent/types"
	"bcare/ent/user"
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// PersonCreate is the builder for creating a Person entity.
type PersonCreate struct {
	config
	mutation *PersonMutation
	hooks    []Hook
}

// SetDeletedAt sets the "deleted_at" field.
func (pc *PersonCreate) SetDeletedAt(t time.Time) *PersonCreate {
	pc.mutation.SetDeletedAt(t)
	return pc
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (pc *PersonCreate) SetNillableDeletedAt(t *time.Time) *PersonCreate {
	if t != nil {
		pc.SetDeletedAt(*t)
	}
	return pc
}

// SetStatus sets the "status" field.
func (pc *PersonCreate) SetStatus(i int8) *PersonCreate {
	pc.mutation.SetStatus(i)
	return pc
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (pc *PersonCreate) SetNillableStatus(i *int8) *PersonCreate {
	if i != nil {
		pc.SetStatus(*i)
	}
	return pc
}

// SetVersion sets the "version" field.
func (pc *PersonCreate) SetVersion(i int) *PersonCreate {
	pc.mutation.SetVersion(i)
	return pc
}

// SetNillableVersion sets the "version" field if the given value is not nil.
func (pc *PersonCreate) SetNillableVersion(i *int) *PersonCreate {
	if i != nil {
		pc.SetVersion(*i)
	}
	return pc
}

// SetCreatedAt sets the "created_at" field.
func (pc *PersonCreate) SetCreatedAt(t time.Time) *PersonCreate {
	pc.mutation.SetCreatedAt(t)
	return pc
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (pc *PersonCreate) SetNillableCreatedAt(t *time.Time) *PersonCreate {
	if t != nil {
		pc.SetCreatedAt(*t)
	}
	return pc
}

// SetUpdatedAt sets the "updated_at" field.
func (pc *PersonCreate) SetUpdatedAt(t time.Time) *PersonCreate {
	pc.mutation.SetUpdatedAt(t)
	return pc
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (pc *PersonCreate) SetNillableUpdatedAt(t *time.Time) *PersonCreate {
	if t != nil {
		pc.SetUpdatedAt(*t)
	}
	return pc
}

// SetFullName sets the "full_name" field.
func (pc *PersonCreate) SetFullName(s string) *PersonCreate {
	pc.mutation.SetFullName(s)
	return pc
}

// SetDateOfBirth sets the "date_of_birth" field.
func (pc *PersonCreate) SetDateOfBirth(t time.Time) *PersonCreate {
	pc.mutation.SetDateOfBirth(t)
	return pc
}

// SetNillableDateOfBirth sets the "date_of_birth" field if the given value is not nil.
func (pc *PersonCreate) SetNillableDateOfBirth(t *time.Time) *PersonCreate {
	if t != nil {
		pc.SetDateOfBirth(*t)
	}
	return pc
}

// SetGender sets the "gender" field.
func (pc *PersonCreate) SetGender(s string) *PersonCreate {
	pc.mutation.SetGender(s)
	return pc
}

// SetNillableGender sets the "gender" field if the given value is not nil.
func (pc *PersonCreate) SetNillableGender(s *string) *PersonCreate {
	if s != nil {
		pc.SetGender(*s)
	}
	return pc
}

// SetProvinceID sets the "province_id" field.
func (pc *PersonCreate) SetProvinceID(i int) *PersonCreate {
	pc.mutation.SetProvinceID(i)
	return pc
}

// SetNillableProvinceID sets the "province_id" field if the given value is not nil.
func (pc *PersonCreate) SetNillableProvinceID(i *int) *PersonCreate {
	if i != nil {
		pc.SetProvinceID(*i)
	}
	return pc
}

// SetDistrictID sets the "district_id" field.
func (pc *PersonCreate) SetDistrictID(i int) *PersonCreate {
	pc.mutation.SetDistrictID(i)
	return pc
}

// SetNillableDistrictID sets the "district_id" field if the given value is not nil.
func (pc *PersonCreate) SetNillableDistrictID(i *int) *PersonCreate {
	if i != nil {
		pc.SetDistrictID(*i)
	}
	return pc
}

// SetWardID sets the "ward_id" field.
func (pc *PersonCreate) SetWardID(i int) *PersonCreate {
	pc.mutation.SetWardID(i)
	return pc
}

// SetNillableWardID sets the "ward_id" field if the given value is not nil.
func (pc *PersonCreate) SetNillableWardID(i *int) *PersonCreate {
	if i != nil {
		pc.SetWardID(*i)
	}
	return pc
}

// SetAddressNumber sets the "address_number" field.
func (pc *PersonCreate) SetAddressNumber(s string) *PersonCreate {
	pc.mutation.SetAddressNumber(s)
	return pc
}

// SetNillableAddressNumber sets the "address_number" field if the given value is not nil.
func (pc *PersonCreate) SetNillableAddressNumber(s *string) *PersonCreate {
	if s != nil {
		pc.SetAddressNumber(*s)
	}
	return pc
}

// SetPhone sets the "phone" field.
func (pc *PersonCreate) SetPhone(s string) *PersonCreate {
	pc.mutation.SetPhone(s)
	return pc
}

// SetEmail sets the "email" field.
func (pc *PersonCreate) SetEmail(s string) *PersonCreate {
	pc.mutation.SetEmail(s)
	return pc
}

// SetNillableEmail sets the "email" field if the given value is not nil.
func (pc *PersonCreate) SetNillableEmail(s *string) *PersonCreate {
	if s != nil {
		pc.SetEmail(*s)
	}
	return pc
}

// SetJobID sets the "job_id" field.
func (pc *PersonCreate) SetJobID(i int) *PersonCreate {
	pc.mutation.SetJobID(i)
	return pc
}

// SetNillableJobID sets the "job_id" field if the given value is not nil.
func (pc *PersonCreate) SetNillableJobID(i *int) *PersonCreate {
	if i != nil {
		pc.SetJobID(*i)
	}
	return pc
}

// SetUserID sets the "user_id" field.
func (pc *PersonCreate) SetUserID(i int) *PersonCreate {
	pc.mutation.SetUserID(i)
	return pc
}

// SetNillableUserID sets the "user_id" field if the given value is not nil.
func (pc *PersonCreate) SetNillableUserID(i *int) *PersonCreate {
	if i != nil {
		pc.SetUserID(*i)
	}
	return pc
}

// SetSourceID sets the "source_id" field.
func (pc *PersonCreate) SetSourceID(i int) *PersonCreate {
	pc.mutation.SetSourceID(i)
	return pc
}

// SetNillableSourceID sets the "source_id" field if the given value is not nil.
func (pc *PersonCreate) SetNillableSourceID(i *int) *PersonCreate {
	if i != nil {
		pc.SetSourceID(*i)
	}
	return pc
}

// SetPhoneConfirm sets the "phone_confirm" field.
func (pc *PersonCreate) SetPhoneConfirm(b bool) *PersonCreate {
	pc.mutation.SetPhoneConfirm(b)
	return pc
}

// SetNillablePhoneConfirm sets the "phone_confirm" field if the given value is not nil.
func (pc *PersonCreate) SetNillablePhoneConfirm(b *bool) *PersonCreate {
	if b != nil {
		pc.SetPhoneConfirm(*b)
	}
	return pc
}

// SetMailConfirm sets the "mail_confirm" field.
func (pc *PersonCreate) SetMailConfirm(b bool) *PersonCreate {
	pc.mutation.SetMailConfirm(b)
	return pc
}

// SetNillableMailConfirm sets the "mail_confirm" field if the given value is not nil.
func (pc *PersonCreate) SetNillableMailConfirm(b *bool) *PersonCreate {
	if b != nil {
		pc.SetMailConfirm(*b)
	}
	return pc
}

// SetPersonField sets the "person_field" field.
func (pc *PersonCreate) SetPersonField(tm *types.PersonMeta) *PersonCreate {
	pc.mutation.SetPersonField(tm)
	return pc
}

// AddDealIDs adds the "deals" edge to the Deal entity by IDs.
func (pc *PersonCreate) AddDealIDs(ids ...int) *PersonCreate {
	pc.mutation.AddDealIDs(ids...)
	return pc
}

// AddDeals adds the "deals" edges to the Deal entity.
func (pc *PersonCreate) AddDeals(d ...*Deal) *PersonCreate {
	ids := make([]int, len(d))
	for i := range d {
		ids[i] = d[i].ID
	}
	return pc.AddDealIDs(ids...)
}

// AddTagIDs adds the "tags" edge to the Tag entity by IDs.
func (pc *PersonCreate) AddTagIDs(ids ...int) *PersonCreate {
	pc.mutation.AddTagIDs(ids...)
	return pc
}

// AddTags adds the "tags" edges to the Tag entity.
func (pc *PersonCreate) AddTags(t ...*Tag) *PersonCreate {
	ids := make([]int, len(t))
	for i := range t {
		ids[i] = t[i].ID
	}
	return pc.AddTagIDs(ids...)
}

// AddFormSubmissionIDs adds the "form_submissions" edge to the FormSubmission entity by IDs.
func (pc *PersonCreate) AddFormSubmissionIDs(ids ...int) *PersonCreate {
	pc.mutation.AddFormSubmissionIDs(ids...)
	return pc
}

// AddFormSubmissions adds the "form_submissions" edges to the FormSubmission entity.
func (pc *PersonCreate) AddFormSubmissions(f ...*FormSubmission) *PersonCreate {
	ids := make([]int, len(f))
	for i := range f {
		ids[i] = f[i].ID
	}
	return pc.AddFormSubmissionIDs(ids...)
}

// AddPlanIDs adds the "plans" edge to the InstallmentPlan entity by IDs.
func (pc *PersonCreate) AddPlanIDs(ids ...int) *PersonCreate {
	pc.mutation.AddPlanIDs(ids...)
	return pc
}

// AddPlans adds the "plans" edges to the InstallmentPlan entity.
func (pc *PersonCreate) AddPlans(i ...*InstallmentPlan) *PersonCreate {
	ids := make([]int, len(i))
	for j := range i {
		ids[j] = i[j].ID
	}
	return pc.AddPlanIDs(ids...)
}

// AddCallIDs adds the "calls" edge to the Call entity by IDs.
func (pc *PersonCreate) AddCallIDs(ids ...int) *PersonCreate {
	pc.mutation.AddCallIDs(ids...)
	return pc
}

// AddCalls adds the "calls" edges to the Call entity.
func (pc *PersonCreate) AddCalls(c ...*Call) *PersonCreate {
	ids := make([]int, len(c))
	for i := range c {
		ids[i] = c[i].ID
	}
	return pc.AddCallIDs(ids...)
}

// AddTaskIDs adds the "tasks" edge to the Task entity by IDs.
func (pc *PersonCreate) AddTaskIDs(ids ...int) *PersonCreate {
	pc.mutation.AddTaskIDs(ids...)
	return pc
}

// AddTasks adds the "tasks" edges to the Task entity.
func (pc *PersonCreate) AddTasks(t ...*Task) *PersonCreate {
	ids := make([]int, len(t))
	for i := range t {
		ids[i] = t[i].ID
	}
	return pc.AddTaskIDs(ids...)
}

// AddIssueIDs adds the "issues" edge to the Issue entity by IDs.
func (pc *PersonCreate) AddIssueIDs(ids ...int) *PersonCreate {
	pc.mutation.AddIssueIDs(ids...)
	return pc
}

// AddIssues adds the "issues" edges to the Issue entity.
func (pc *PersonCreate) AddIssues(i ...*Issue) *PersonCreate {
	ids := make([]int, len(i))
	for j := range i {
		ids[j] = i[j].ID
	}
	return pc.AddIssueIDs(ids...)
}

// AddAppointmentIDs adds the "appointments" edge to the Appointment entity by IDs.
func (pc *PersonCreate) AddAppointmentIDs(ids ...int) *PersonCreate {
	pc.mutation.AddAppointmentIDs(ids...)
	return pc
}

// AddAppointments adds the "appointments" edges to the Appointment entity.
func (pc *PersonCreate) AddAppointments(a ...*Appointment) *PersonCreate {
	ids := make([]int, len(a))
	for i := range a {
		ids[i] = a[i].ID
	}
	return pc.AddAppointmentIDs(ids...)
}

// AddTrackIDs adds the "tracks" edge to the Track entity by IDs.
func (pc *PersonCreate) AddTrackIDs(ids ...int) *PersonCreate {
	pc.mutation.AddTrackIDs(ids...)
	return pc
}

// AddTracks adds the "tracks" edges to the Track entity.
func (pc *PersonCreate) AddTracks(t ...*Track) *PersonCreate {
	ids := make([]int, len(t))
	for i := range t {
		ids[i] = t[i].ID
	}
	return pc.AddTrackIDs(ids...)
}

// AddMessageHistoryIDs adds the "message_histories" edge to the MessageHistory entity by IDs.
func (pc *PersonCreate) AddMessageHistoryIDs(ids ...int) *PersonCreate {
	pc.mutation.AddMessageHistoryIDs(ids...)
	return pc
}

// AddMessageHistories adds the "message_histories" edges to the MessageHistory entity.
func (pc *PersonCreate) AddMessageHistories(m ...*MessageHistory) *PersonCreate {
	ids := make([]int, len(m))
	for i := range m {
		ids[i] = m[i].ID
	}
	return pc.AddMessageHistoryIDs(ids...)
}

// AddAssigneeIDs adds the "assignees" edge to the User entity by IDs.
func (pc *PersonCreate) AddAssigneeIDs(ids ...int) *PersonCreate {
	pc.mutation.AddAssigneeIDs(ids...)
	return pc
}

// AddAssignees adds the "assignees" edges to the User entity.
func (pc *PersonCreate) AddAssignees(u ...*User) *PersonCreate {
	ids := make([]int, len(u))
	for i := range u {
		ids[i] = u[i].ID
	}
	return pc.AddAssigneeIDs(ids...)
}

// SetJob sets the "job" edge to the Term entity.
func (pc *PersonCreate) SetJob(t *Term) *PersonCreate {
	return pc.SetJobID(t.ID)
}

// SetSource sets the "source" edge to the Term entity.
func (pc *PersonCreate) SetSource(t *Term) *PersonCreate {
	return pc.SetSourceID(t.ID)
}

// SetCreatorID sets the "creator" edge to the User entity by ID.
func (pc *PersonCreate) SetCreatorID(id int) *PersonCreate {
	pc.mutation.SetCreatorID(id)
	return pc
}

// SetNillableCreatorID sets the "creator" edge to the User entity by ID if the given value is not nil.
func (pc *PersonCreate) SetNillableCreatorID(id *int) *PersonCreate {
	if id != nil {
		pc = pc.SetCreatorID(*id)
	}
	return pc
}

// SetCreator sets the "creator" edge to the User entity.
func (pc *PersonCreate) SetCreator(u *User) *PersonCreate {
	return pc.SetCreatorID(u.ID)
}

// SetReferredByID sets the "referred_by" edge to the PersonReferral entity by ID.
func (pc *PersonCreate) SetReferredByID(id int) *PersonCreate {
	pc.mutation.SetReferredByID(id)
	return pc
}

// SetNillableReferredByID sets the "referred_by" edge to the PersonReferral entity by ID if the given value is not nil.
func (pc *PersonCreate) SetNillableReferredByID(id *int) *PersonCreate {
	if id != nil {
		pc = pc.SetReferredByID(*id)
	}
	return pc
}

// SetReferredBy sets the "referred_by" edge to the PersonReferral entity.
func (pc *PersonCreate) SetReferredBy(p *PersonReferral) *PersonCreate {
	return pc.SetReferredByID(p.ID)
}

// AddTagPersonIDs adds the "tag_person" edge to the TagPerson entity by IDs.
func (pc *PersonCreate) AddTagPersonIDs(ids ...int) *PersonCreate {
	pc.mutation.AddTagPersonIDs(ids...)
	return pc
}

// AddTagPerson adds the "tag_person" edges to the TagPerson entity.
func (pc *PersonCreate) AddTagPerson(t ...*TagPerson) *PersonCreate {
	ids := make([]int, len(t))
	for i := range t {
		ids[i] = t[i].ID
	}
	return pc.AddTagPersonIDs(ids...)
}

// AddAssignmentIDs adds the "assignment" edge to the PersonAssignment entity by IDs.
func (pc *PersonCreate) AddAssignmentIDs(ids ...int) *PersonCreate {
	pc.mutation.AddAssignmentIDs(ids...)
	return pc
}

// AddAssignment adds the "assignment" edges to the PersonAssignment entity.
func (pc *PersonCreate) AddAssignment(p ...*PersonAssignment) *PersonCreate {
	ids := make([]int, len(p))
	for i := range p {
		ids[i] = p[i].ID
	}
	return pc.AddAssignmentIDs(ids...)
}

// Mutation returns the PersonMutation object of the builder.
func (pc *PersonCreate) Mutation() *PersonMutation {
	return pc.mutation
}

// Save creates the Person in the database.
func (pc *PersonCreate) Save(ctx context.Context) (*Person, error) {
	if err := pc.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, pc.sqlSave, pc.mutation, pc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (pc *PersonCreate) SaveX(ctx context.Context) *Person {
	v, err := pc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (pc *PersonCreate) Exec(ctx context.Context) error {
	_, err := pc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (pc *PersonCreate) ExecX(ctx context.Context) {
	if err := pc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (pc *PersonCreate) defaults() error {
	if _, ok := pc.mutation.Status(); !ok {
		v := person.DefaultStatus
		pc.mutation.SetStatus(v)
	}
	if _, ok := pc.mutation.Version(); !ok {
		v := person.DefaultVersion
		pc.mutation.SetVersion(v)
	}
	if _, ok := pc.mutation.CreatedAt(); !ok {
		if person.DefaultCreatedAt == nil {
			return fmt.Errorf("ent: uninitialized person.DefaultCreatedAt (forgotten import ent/runtime?)")
		}
		v := person.DefaultCreatedAt()
		pc.mutation.SetCreatedAt(v)
	}
	if _, ok := pc.mutation.UpdatedAt(); !ok {
		if person.DefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized person.DefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := person.DefaultUpdatedAt()
		pc.mutation.SetUpdatedAt(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (pc *PersonCreate) check() error {
	if _, ok := pc.mutation.Status(); !ok {
		return &ValidationError{Name: "status", err: errors.New(`ent: missing required field "Person.status"`)}
	}
	if _, ok := pc.mutation.Version(); !ok {
		return &ValidationError{Name: "version", err: errors.New(`ent: missing required field "Person.version"`)}
	}
	if _, ok := pc.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "Person.created_at"`)}
	}
	if _, ok := pc.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "Person.updated_at"`)}
	}
	if _, ok := pc.mutation.FullName(); !ok {
		return &ValidationError{Name: "full_name", err: errors.New(`ent: missing required field "Person.full_name"`)}
	}
	if _, ok := pc.mutation.Phone(); !ok {
		return &ValidationError{Name: "phone", err: errors.New(`ent: missing required field "Person.phone"`)}
	}
	return nil
}

func (pc *PersonCreate) sqlSave(ctx context.Context) (*Person, error) {
	if err := pc.check(); err != nil {
		return nil, err
	}
	_node, _spec := pc.createSpec()
	if err := sqlgraph.CreateNode(ctx, pc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	id := _spec.ID.Value.(int64)
	_node.ID = int(id)
	pc.mutation.id = &_node.ID
	pc.mutation.done = true
	return _node, nil
}

func (pc *PersonCreate) createSpec() (*Person, *sqlgraph.CreateSpec) {
	var (
		_node = &Person{config: pc.config}
		_spec = sqlgraph.NewCreateSpec(person.Table, sqlgraph.NewFieldSpec(person.FieldID, field.TypeInt))
	)
	if value, ok := pc.mutation.DeletedAt(); ok {
		_spec.SetField(person.FieldDeletedAt, field.TypeTime, value)
		_node.DeletedAt = value
	}
	if value, ok := pc.mutation.Status(); ok {
		_spec.SetField(person.FieldStatus, field.TypeInt8, value)
		_node.Status = value
	}
	if value, ok := pc.mutation.Version(); ok {
		_spec.SetField(person.FieldVersion, field.TypeInt, value)
		_node.Version = value
	}
	if value, ok := pc.mutation.CreatedAt(); ok {
		_spec.SetField(person.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := pc.mutation.UpdatedAt(); ok {
		_spec.SetField(person.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	if value, ok := pc.mutation.FullName(); ok {
		_spec.SetField(person.FieldFullName, field.TypeString, value)
		_node.FullName = value
	}
	if value, ok := pc.mutation.DateOfBirth(); ok {
		_spec.SetField(person.FieldDateOfBirth, field.TypeTime, value)
		_node.DateOfBirth = &value
	}
	if value, ok := pc.mutation.Gender(); ok {
		_spec.SetField(person.FieldGender, field.TypeString, value)
		_node.Gender = value
	}
	if value, ok := pc.mutation.ProvinceID(); ok {
		_spec.SetField(person.FieldProvinceID, field.TypeInt, value)
		_node.ProvinceID = value
	}
	if value, ok := pc.mutation.DistrictID(); ok {
		_spec.SetField(person.FieldDistrictID, field.TypeInt, value)
		_node.DistrictID = value
	}
	if value, ok := pc.mutation.WardID(); ok {
		_spec.SetField(person.FieldWardID, field.TypeInt, value)
		_node.WardID = value
	}
	if value, ok := pc.mutation.AddressNumber(); ok {
		_spec.SetField(person.FieldAddressNumber, field.TypeString, value)
		_node.AddressNumber = value
	}
	if value, ok := pc.mutation.Phone(); ok {
		_spec.SetField(person.FieldPhone, field.TypeString, value)
		_node.Phone = value
	}
	if value, ok := pc.mutation.Email(); ok {
		_spec.SetField(person.FieldEmail, field.TypeString, value)
		_node.Email = value
	}
	if value, ok := pc.mutation.PhoneConfirm(); ok {
		_spec.SetField(person.FieldPhoneConfirm, field.TypeBool, value)
		_node.PhoneConfirm = value
	}
	if value, ok := pc.mutation.MailConfirm(); ok {
		_spec.SetField(person.FieldMailConfirm, field.TypeBool, value)
		_node.MailConfirm = value
	}
	if value, ok := pc.mutation.PersonField(); ok {
		_spec.SetField(person.FieldPersonField, field.TypeJSON, value)
		_node.PersonField = value
	}
	if nodes := pc.mutation.DealsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   person.DealsTable,
			Columns: []string{person.DealsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(deal.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := pc.mutation.TagsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: false,
			Table:   person.TagsTable,
			Columns: person.TagsPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tag.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		createE := &TagPersonCreate{config: pc.config, mutation: newTagPersonMutation(pc.config, OpCreate)}
		createE.defaults()
		_, specE := createE.createSpec()
		edge.Target.Fields = specE.Fields
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := pc.mutation.FormSubmissionsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   person.FormSubmissionsTable,
			Columns: []string{person.FormSubmissionsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(formsubmission.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := pc.mutation.PlansIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   person.PlansTable,
			Columns: []string{person.PlansColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(installmentplan.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := pc.mutation.CallsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   person.CallsTable,
			Columns: []string{person.CallsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(call.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := pc.mutation.TasksIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   person.TasksTable,
			Columns: []string{person.TasksColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(task.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := pc.mutation.IssuesIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   person.IssuesTable,
			Columns: []string{person.IssuesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(issue.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := pc.mutation.AppointmentsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   person.AppointmentsTable,
			Columns: []string{person.AppointmentsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(appointment.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := pc.mutation.TracksIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   person.TracksTable,
			Columns: []string{person.TracksColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(track.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := pc.mutation.MessageHistoriesIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   person.MessageHistoriesTable,
			Columns: []string{person.MessageHistoriesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(messagehistory.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := pc.mutation.AssigneesIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: false,
			Table:   person.AssigneesTable,
			Columns: person.AssigneesPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		createE := &PersonAssignmentCreate{config: pc.config, mutation: newPersonAssignmentMutation(pc.config, OpCreate)}
		createE.defaults()
		_, specE := createE.createSpec()
		edge.Target.Fields = specE.Fields
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := pc.mutation.JobIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   person.JobTable,
			Columns: []string{person.JobColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(term.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.JobID = &nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := pc.mutation.SourceIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   person.SourceTable,
			Columns: []string{person.SourceColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(term.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.SourceID = &nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := pc.mutation.CreatorIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   person.CreatorTable,
			Columns: []string{person.CreatorColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.UserID = nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := pc.mutation.ReferredByIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2O,
			Inverse: false,
			Table:   person.ReferredByTable,
			Columns: []string{person.ReferredByColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(personreferral.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := pc.mutation.TagPersonIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   person.TagPersonTable,
			Columns: []string{person.TagPersonColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tagperson.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := pc.mutation.AssignmentIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   person.AssignmentTable,
			Columns: []string{person.AssignmentColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(personassignment.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	return _node, _spec
}

// PersonCreateBulk is the builder for creating many Person entities in bulk.
type PersonCreateBulk struct {
	config
	err      error
	builders []*PersonCreate
}

// Save creates the Person entities in the database.
func (pcb *PersonCreateBulk) Save(ctx context.Context) ([]*Person, error) {
	if pcb.err != nil {
		return nil, pcb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(pcb.builders))
	nodes := make([]*Person, len(pcb.builders))
	mutators := make([]Mutator, len(pcb.builders))
	for i := range pcb.builders {
		func(i int, root context.Context) {
			builder := pcb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*PersonMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, pcb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, pcb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = int(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, pcb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (pcb *PersonCreateBulk) SaveX(ctx context.Context) []*Person {
	v, err := pcb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (pcb *PersonCreateBulk) Exec(ctx context.Context) error {
	_, err := pcb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (pcb *PersonCreateBulk) ExecX(ctx context.Context) {
	if err := pcb.Exec(ctx); err != nil {
		panic(err)
	}
}
