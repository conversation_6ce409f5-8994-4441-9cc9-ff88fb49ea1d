// Code generated by ent, DO NOT EDIT.

package dealuser

import (
	"bcare/ent/predicate"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

// ID filters vertices based on their ID field.
func ID(id int) predicate.DealUser {
	return predicate.DealUser(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id int) predicate.DealUser {
	return predicate.DealUser(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id int) predicate.DealUser {
	return predicate.DealUser(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...int) predicate.DealUser {
	return predicate.DealUser(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...int) predicate.DealUser {
	return predicate.DealUser(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id int) predicate.DealUser {
	return predicate.DealUser(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id int) predicate.DealUser {
	return predicate.DealUser(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id int) predicate.DealUser {
	return predicate.DealUser(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id int) predicate.DealUser {
	return predicate.DealUser(sql.FieldLTE(FieldID, id))
}

// DealID applies equality check predicate on the "deal_id" field. It's identical to DealIDEQ.
func DealID(v int) predicate.DealUser {
	return predicate.DealUser(sql.FieldEQ(FieldDealID, v))
}

// UserID applies equality check predicate on the "user_id" field. It's identical to UserIDEQ.
func UserID(v int) predicate.DealUser {
	return predicate.DealUser(sql.FieldEQ(FieldUserID, v))
}

// DealIDEQ applies the EQ predicate on the "deal_id" field.
func DealIDEQ(v int) predicate.DealUser {
	return predicate.DealUser(sql.FieldEQ(FieldDealID, v))
}

// DealIDNEQ applies the NEQ predicate on the "deal_id" field.
func DealIDNEQ(v int) predicate.DealUser {
	return predicate.DealUser(sql.FieldNEQ(FieldDealID, v))
}

// DealIDIn applies the In predicate on the "deal_id" field.
func DealIDIn(vs ...int) predicate.DealUser {
	return predicate.DealUser(sql.FieldIn(FieldDealID, vs...))
}

// DealIDNotIn applies the NotIn predicate on the "deal_id" field.
func DealIDNotIn(vs ...int) predicate.DealUser {
	return predicate.DealUser(sql.FieldNotIn(FieldDealID, vs...))
}

// UserIDEQ applies the EQ predicate on the "user_id" field.
func UserIDEQ(v int) predicate.DealUser {
	return predicate.DealUser(sql.FieldEQ(FieldUserID, v))
}

// UserIDNEQ applies the NEQ predicate on the "user_id" field.
func UserIDNEQ(v int) predicate.DealUser {
	return predicate.DealUser(sql.FieldNEQ(FieldUserID, v))
}

// UserIDIn applies the In predicate on the "user_id" field.
func UserIDIn(vs ...int) predicate.DealUser {
	return predicate.DealUser(sql.FieldIn(FieldUserID, vs...))
}

// UserIDNotIn applies the NotIn predicate on the "user_id" field.
func UserIDNotIn(vs ...int) predicate.DealUser {
	return predicate.DealUser(sql.FieldNotIn(FieldUserID, vs...))
}

// RoleEQ applies the EQ predicate on the "role" field.
func RoleEQ(v Role) predicate.DealUser {
	return predicate.DealUser(sql.FieldEQ(FieldRole, v))
}

// RoleNEQ applies the NEQ predicate on the "role" field.
func RoleNEQ(v Role) predicate.DealUser {
	return predicate.DealUser(sql.FieldNEQ(FieldRole, v))
}

// RoleIn applies the In predicate on the "role" field.
func RoleIn(vs ...Role) predicate.DealUser {
	return predicate.DealUser(sql.FieldIn(FieldRole, vs...))
}

// RoleNotIn applies the NotIn predicate on the "role" field.
func RoleNotIn(vs ...Role) predicate.DealUser {
	return predicate.DealUser(sql.FieldNotIn(FieldRole, vs...))
}

// RoleIsNil applies the IsNil predicate on the "role" field.
func RoleIsNil() predicate.DealUser {
	return predicate.DealUser(sql.FieldIsNull(FieldRole))
}

// RoleNotNil applies the NotNil predicate on the "role" field.
func RoleNotNil() predicate.DealUser {
	return predicate.DealUser(sql.FieldNotNull(FieldRole))
}

// HasDeal applies the HasEdge predicate on the "deal" edge.
func HasDeal() predicate.DealUser {
	return predicate.DealUser(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, DealTable, DealColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasDealWith applies the HasEdge predicate on the "deal" edge with a given conditions (other predicates).
func HasDealWith(preds ...predicate.Deal) predicate.DealUser {
	return predicate.DealUser(func(s *sql.Selector) {
		step := newDealStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasUser applies the HasEdge predicate on the "user" edge.
func HasUser() predicate.DealUser {
	return predicate.DealUser(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, UserTable, UserColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasUserWith applies the HasEdge predicate on the "user" edge with a given conditions (other predicates).
func HasUserWith(preds ...predicate.User) predicate.DealUser {
	return predicate.DealUser(func(s *sql.Selector) {
		step := newUserStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.DealUser) predicate.DealUser {
	return predicate.DealUser(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.DealUser) predicate.DealUser {
	return predicate.DealUser(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.DealUser) predicate.DealUser {
	return predicate.DealUser(sql.NotPredicates(p))
}
