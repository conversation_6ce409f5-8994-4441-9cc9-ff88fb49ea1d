// Code generated by ent, DO NOT EDIT.

package dealuser

import (
	"fmt"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

const (
	// Label holds the string label denoting the dealuser type in the database.
	Label = "deal_user"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldDealID holds the string denoting the deal_id field in the database.
	FieldDealID = "deal_id"
	// FieldUserID holds the string denoting the user_id field in the database.
	FieldUserID = "user_id"
	// FieldRole holds the string denoting the role field in the database.
	FieldRole = "role"
	// EdgeDeal holds the string denoting the deal edge name in mutations.
	EdgeDeal = "deal"
	// EdgeUser holds the string denoting the user edge name in mutations.
	EdgeUser = "user"
	// Table holds the table name of the dealuser in the database.
	Table = "deal_user"
	// DealTable is the table that holds the deal relation/edge.
	DealTable = "deal_user"
	// DealInverseTable is the table name for the Deal entity.
	// It exists in this package in order to avoid circular dependency with the "deal" package.
	DealInverseTable = "deal"
	// DealColumn is the table column denoting the deal relation/edge.
	DealColumn = "deal_id"
	// UserTable is the table that holds the user relation/edge.
	UserTable = "deal_user"
	// UserInverseTable is the table name for the User entity.
	// It exists in this package in order to avoid circular dependency with the "user" package.
	UserInverseTable = "user"
	// UserColumn is the table column denoting the user relation/edge.
	UserColumn = "user_id"
)

// Columns holds all SQL columns for dealuser fields.
var Columns = []string{
	FieldID,
	FieldDealID,
	FieldUserID,
	FieldRole,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

// Role defines the type for the "role" enum field.
type Role string

// Role values.
const (
	RoleTreatmentDoctor  Role = "treatment_doctor"
	RoleConsultantDoctor Role = "consultant_doctor"
	RoleAdvisor          Role = "advisor"
	RoleAssistant        Role = "assistant"
	RoleXrayTechnician   Role = "xray_technician"
	RoleDoctorAssistant  Role = "doctor_assistant"
)

func (r Role) String() string {
	return string(r)
}

// RoleValidator is a validator for the "role" field enum values. It is called by the builders before save.
func RoleValidator(r Role) error {
	switch r {
	case RoleTreatmentDoctor, RoleConsultantDoctor, RoleAdvisor, RoleAssistant, RoleXrayTechnician, RoleDoctorAssistant:
		return nil
	default:
		return fmt.Errorf("dealuser: invalid enum value for role field: %q", r)
	}
}

// OrderOption defines the ordering options for the DealUser queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByDealID orders the results by the deal_id field.
func ByDealID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDealID, opts...).ToFunc()
}

// ByUserID orders the results by the user_id field.
func ByUserID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUserID, opts...).ToFunc()
}

// ByRole orders the results by the role field.
func ByRole(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldRole, opts...).ToFunc()
}

// ByDealField orders the results by deal field.
func ByDealField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newDealStep(), sql.OrderByField(field, opts...))
	}
}

// ByUserField orders the results by user field.
func ByUserField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newUserStep(), sql.OrderByField(field, opts...))
	}
}
func newDealStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(DealInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2O, false, DealTable, DealColumn),
	)
}
func newUserStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(UserInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2O, false, UserTable, UserColumn),
	)
}
