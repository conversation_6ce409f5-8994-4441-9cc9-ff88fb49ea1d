// Code generated by ent, DO NOT EDIT.

package ent

import (
	"bcare/ent/appointment"
	"bcare/ent/call"
	"bcare/ent/exportjob"
	"bcare/ent/messagehistory"
	"bcare/ent/note"
	"bcare/ent/notification"
	"bcare/ent/person"
	"bcare/ent/personassignment"
	"bcare/ent/predicate"
	"bcare/ent/schedule"
	"bcare/ent/taskassignment"
	"bcare/ent/tasknote"
	"bcare/ent/user"
	"bcare/ent/userdata"
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// UserUpdate is the builder for updating User entities.
type UserUpdate struct {
	config
	hooks     []Hook
	mutation  *UserMutation
	modifiers []func(*sql.UpdateBuilder)
}

// Where appends a list predicates to the UserUpdate builder.
func (uu *UserUpdate) Where(ps ...predicate.User) *UserUpdate {
	uu.mutation.Where(ps...)
	return uu
}

// SetDeletedAt sets the "deleted_at" field.
func (uu *UserUpdate) SetDeletedAt(t time.Time) *UserUpdate {
	uu.mutation.SetDeletedAt(t)
	return uu
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (uu *UserUpdate) SetNillableDeletedAt(t *time.Time) *UserUpdate {
	if t != nil {
		uu.SetDeletedAt(*t)
	}
	return uu
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (uu *UserUpdate) ClearDeletedAt() *UserUpdate {
	uu.mutation.ClearDeletedAt()
	return uu
}

// SetStatus sets the "status" field.
func (uu *UserUpdate) SetStatus(i int8) *UserUpdate {
	uu.mutation.ResetStatus()
	uu.mutation.SetStatus(i)
	return uu
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (uu *UserUpdate) SetNillableStatus(i *int8) *UserUpdate {
	if i != nil {
		uu.SetStatus(*i)
	}
	return uu
}

// AddStatus adds i to the "status" field.
func (uu *UserUpdate) AddStatus(i int8) *UserUpdate {
	uu.mutation.AddStatus(i)
	return uu
}

// SetVersion sets the "version" field.
func (uu *UserUpdate) SetVersion(i int) *UserUpdate {
	uu.mutation.ResetVersion()
	uu.mutation.SetVersion(i)
	return uu
}

// SetNillableVersion sets the "version" field if the given value is not nil.
func (uu *UserUpdate) SetNillableVersion(i *int) *UserUpdate {
	if i != nil {
		uu.SetVersion(*i)
	}
	return uu
}

// AddVersion adds i to the "version" field.
func (uu *UserUpdate) AddVersion(i int) *UserUpdate {
	uu.mutation.AddVersion(i)
	return uu
}

// SetUpdatedAt sets the "updated_at" field.
func (uu *UserUpdate) SetUpdatedAt(t time.Time) *UserUpdate {
	uu.mutation.SetUpdatedAt(t)
	return uu
}

// SetUsername sets the "username" field.
func (uu *UserUpdate) SetUsername(s string) *UserUpdate {
	uu.mutation.SetUsername(s)
	return uu
}

// SetNillableUsername sets the "username" field if the given value is not nil.
func (uu *UserUpdate) SetNillableUsername(s *string) *UserUpdate {
	if s != nil {
		uu.SetUsername(*s)
	}
	return uu
}

// SetPassword sets the "password" field.
func (uu *UserUpdate) SetPassword(s string) *UserUpdate {
	uu.mutation.SetPassword(s)
	return uu
}

// SetNillablePassword sets the "password" field if the given value is not nil.
func (uu *UserUpdate) SetNillablePassword(s *string) *UserUpdate {
	if s != nil {
		uu.SetPassword(*s)
	}
	return uu
}

// SetPhone sets the "phone" field.
func (uu *UserUpdate) SetPhone(s string) *UserUpdate {
	uu.mutation.SetPhone(s)
	return uu
}

// SetNillablePhone sets the "phone" field if the given value is not nil.
func (uu *UserUpdate) SetNillablePhone(s *string) *UserUpdate {
	if s != nil {
		uu.SetPhone(*s)
	}
	return uu
}

// ClearPhone clears the value of the "phone" field.
func (uu *UserUpdate) ClearPhone() *UserUpdate {
	uu.mutation.ClearPhone()
	return uu
}

// SetEmail sets the "email" field.
func (uu *UserUpdate) SetEmail(s string) *UserUpdate {
	uu.mutation.SetEmail(s)
	return uu
}

// SetNillableEmail sets the "email" field if the given value is not nil.
func (uu *UserUpdate) SetNillableEmail(s *string) *UserUpdate {
	if s != nil {
		uu.SetEmail(*s)
	}
	return uu
}

// ClearEmail clears the value of the "email" field.
func (uu *UserUpdate) ClearEmail() *UserUpdate {
	uu.mutation.ClearEmail()
	return uu
}

// SetEmailConfirmed sets the "email_confirmed" field.
func (uu *UserUpdate) SetEmailConfirmed(b bool) *UserUpdate {
	uu.mutation.SetEmailConfirmed(b)
	return uu
}

// SetNillableEmailConfirmed sets the "email_confirmed" field if the given value is not nil.
func (uu *UserUpdate) SetNillableEmailConfirmed(b *bool) *UserUpdate {
	if b != nil {
		uu.SetEmailConfirmed(*b)
	}
	return uu
}

// ClearEmailConfirmed clears the value of the "email_confirmed" field.
func (uu *UserUpdate) ClearEmailConfirmed() *UserUpdate {
	uu.mutation.ClearEmailConfirmed()
	return uu
}

// SetName sets the "name" field.
func (uu *UserUpdate) SetName(s string) *UserUpdate {
	uu.mutation.SetName(s)
	return uu
}

// SetNillableName sets the "name" field if the given value is not nil.
func (uu *UserUpdate) SetNillableName(s *string) *UserUpdate {
	if s != nil {
		uu.SetName(*s)
	}
	return uu
}

// ClearName clears the value of the "name" field.
func (uu *UserUpdate) ClearName() *UserUpdate {
	uu.mutation.ClearName()
	return uu
}

// SetGender sets the "gender" field.
func (uu *UserUpdate) SetGender(s string) *UserUpdate {
	uu.mutation.SetGender(s)
	return uu
}

// SetNillableGender sets the "gender" field if the given value is not nil.
func (uu *UserUpdate) SetNillableGender(s *string) *UserUpdate {
	if s != nil {
		uu.SetGender(*s)
	}
	return uu
}

// SetDepartmentID sets the "department_id" field.
func (uu *UserUpdate) SetDepartmentID(i int) *UserUpdate {
	uu.mutation.ResetDepartmentID()
	uu.mutation.SetDepartmentID(i)
	return uu
}

// SetNillableDepartmentID sets the "department_id" field if the given value is not nil.
func (uu *UserUpdate) SetNillableDepartmentID(i *int) *UserUpdate {
	if i != nil {
		uu.SetDepartmentID(*i)
	}
	return uu
}

// AddDepartmentID adds i to the "department_id" field.
func (uu *UserUpdate) AddDepartmentID(i int) *UserUpdate {
	uu.mutation.AddDepartmentID(i)
	return uu
}

// ClearDepartmentID clears the value of the "department_id" field.
func (uu *UserUpdate) ClearDepartmentID() *UserUpdate {
	uu.mutation.ClearDepartmentID()
	return uu
}

// SetDepartmentPosition sets the "department_position" field.
func (uu *UserUpdate) SetDepartmentPosition(s string) *UserUpdate {
	uu.mutation.SetDepartmentPosition(s)
	return uu
}

// SetNillableDepartmentPosition sets the "department_position" field if the given value is not nil.
func (uu *UserUpdate) SetNillableDepartmentPosition(s *string) *UserUpdate {
	if s != nil {
		uu.SetDepartmentPosition(*s)
	}
	return uu
}

// ClearDepartmentPosition clears the value of the "department_position" field.
func (uu *UserUpdate) ClearDepartmentPosition() *UserUpdate {
	uu.mutation.ClearDepartmentPosition()
	return uu
}

// SetProfileImage sets the "profile_image" field.
func (uu *UserUpdate) SetProfileImage(s string) *UserUpdate {
	uu.mutation.SetProfileImage(s)
	return uu
}

// SetNillableProfileImage sets the "profile_image" field if the given value is not nil.
func (uu *UserUpdate) SetNillableProfileImage(s *string) *UserUpdate {
	if s != nil {
		uu.SetProfileImage(*s)
	}
	return uu
}

// ClearProfileImage clears the value of the "profile_image" field.
func (uu *UserUpdate) ClearProfileImage() *UserUpdate {
	uu.mutation.ClearProfileImage()
	return uu
}

// SetSuspendedAt sets the "suspended_at" field.
func (uu *UserUpdate) SetSuspendedAt(t time.Time) *UserUpdate {
	uu.mutation.SetSuspendedAt(t)
	return uu
}

// SetNillableSuspendedAt sets the "suspended_at" field if the given value is not nil.
func (uu *UserUpdate) SetNillableSuspendedAt(t *time.Time) *UserUpdate {
	if t != nil {
		uu.SetSuspendedAt(*t)
	}
	return uu
}

// ClearSuspendedAt clears the value of the "suspended_at" field.
func (uu *UserUpdate) ClearSuspendedAt() *UserUpdate {
	uu.mutation.ClearSuspendedAt()
	return uu
}

// SetState sets the "state" field.
func (uu *UserUpdate) SetState(u user.State) *UserUpdate {
	uu.mutation.SetState(u)
	return uu
}

// SetNillableState sets the "state" field if the given value is not nil.
func (uu *UserUpdate) SetNillableState(u *user.State) *UserUpdate {
	if u != nil {
		uu.SetState(*u)
	}
	return uu
}

// AddAssignmentIDs adds the "assignment" edge to the TaskAssignment entity by IDs.
func (uu *UserUpdate) AddAssignmentIDs(ids ...int) *UserUpdate {
	uu.mutation.AddAssignmentIDs(ids...)
	return uu
}

// AddAssignment adds the "assignment" edges to the TaskAssignment entity.
func (uu *UserUpdate) AddAssignment(t ...*TaskAssignment) *UserUpdate {
	ids := make([]int, len(t))
	for i := range t {
		ids[i] = t[i].ID
	}
	return uu.AddAssignmentIDs(ids...)
}

// AddAssignedPersonIDs adds the "assigned_person" edge to the Person entity by IDs.
func (uu *UserUpdate) AddAssignedPersonIDs(ids ...int) *UserUpdate {
	uu.mutation.AddAssignedPersonIDs(ids...)
	return uu
}

// AddAssignedPerson adds the "assigned_person" edges to the Person entity.
func (uu *UserUpdate) AddAssignedPerson(p ...*Person) *UserUpdate {
	ids := make([]int, len(p))
	for i := range p {
		ids[i] = p[i].ID
	}
	return uu.AddAssignedPersonIDs(ids...)
}

// AddNoteIDs adds the "notes" edge to the Note entity by IDs.
func (uu *UserUpdate) AddNoteIDs(ids ...int) *UserUpdate {
	uu.mutation.AddNoteIDs(ids...)
	return uu
}

// AddNotes adds the "notes" edges to the Note entity.
func (uu *UserUpdate) AddNotes(n ...*Note) *UserUpdate {
	ids := make([]int, len(n))
	for i := range n {
		ids[i] = n[i].ID
	}
	return uu.AddNoteIDs(ids...)
}

// AddTaskNoteIDs adds the "task_notes" edge to the TaskNote entity by IDs.
func (uu *UserUpdate) AddTaskNoteIDs(ids ...int) *UserUpdate {
	uu.mutation.AddTaskNoteIDs(ids...)
	return uu
}

// AddTaskNotes adds the "task_notes" edges to the TaskNote entity.
func (uu *UserUpdate) AddTaskNotes(t ...*TaskNote) *UserUpdate {
	ids := make([]int, len(t))
	for i := range t {
		ids[i] = t[i].ID
	}
	return uu.AddTaskNoteIDs(ids...)
}

// AddAppointmentIDs adds the "appointments" edge to the Appointment entity by IDs.
func (uu *UserUpdate) AddAppointmentIDs(ids ...int) *UserUpdate {
	uu.mutation.AddAppointmentIDs(ids...)
	return uu
}

// AddAppointments adds the "appointments" edges to the Appointment entity.
func (uu *UserUpdate) AddAppointments(a ...*Appointment) *UserUpdate {
	ids := make([]int, len(a))
	for i := range a {
		ids[i] = a[i].ID
	}
	return uu.AddAppointmentIDs(ids...)
}

// AddCallIDs adds the "calls" edge to the Call entity by IDs.
func (uu *UserUpdate) AddCallIDs(ids ...int) *UserUpdate {
	uu.mutation.AddCallIDs(ids...)
	return uu
}

// AddCalls adds the "calls" edges to the Call entity.
func (uu *UserUpdate) AddCalls(c ...*Call) *UserUpdate {
	ids := make([]int, len(c))
	for i := range c {
		ids[i] = c[i].ID
	}
	return uu.AddCallIDs(ids...)
}

// AddScheduleIDs adds the "schedules" edge to the Schedule entity by IDs.
func (uu *UserUpdate) AddScheduleIDs(ids ...int) *UserUpdate {
	uu.mutation.AddScheduleIDs(ids...)
	return uu
}

// AddSchedules adds the "schedules" edges to the Schedule entity.
func (uu *UserUpdate) AddSchedules(s ...*Schedule) *UserUpdate {
	ids := make([]int, len(s))
	for i := range s {
		ids[i] = s[i].ID
	}
	return uu.AddScheduleIDs(ids...)
}

// AddMessageHistoryIDs adds the "message_histories" edge to the MessageHistory entity by IDs.
func (uu *UserUpdate) AddMessageHistoryIDs(ids ...int) *UserUpdate {
	uu.mutation.AddMessageHistoryIDs(ids...)
	return uu
}

// AddMessageHistories adds the "message_histories" edges to the MessageHistory entity.
func (uu *UserUpdate) AddMessageHistories(m ...*MessageHistory) *UserUpdate {
	ids := make([]int, len(m))
	for i := range m {
		ids[i] = m[i].ID
	}
	return uu.AddMessageHistoryIDs(ids...)
}

// AddDatumIDs adds the "data" edge to the UserData entity by IDs.
func (uu *UserUpdate) AddDatumIDs(ids ...int) *UserUpdate {
	uu.mutation.AddDatumIDs(ids...)
	return uu
}

// AddData adds the "data" edges to the UserData entity.
func (uu *UserUpdate) AddData(u ...*UserData) *UserUpdate {
	ids := make([]int, len(u))
	for i := range u {
		ids[i] = u[i].ID
	}
	return uu.AddDatumIDs(ids...)
}

// AddExportJobIDs adds the "export_jobs" edge to the ExportJob entity by IDs.
func (uu *UserUpdate) AddExportJobIDs(ids ...int) *UserUpdate {
	uu.mutation.AddExportJobIDs(ids...)
	return uu
}

// AddExportJobs adds the "export_jobs" edges to the ExportJob entity.
func (uu *UserUpdate) AddExportJobs(e ...*ExportJob) *UserUpdate {
	ids := make([]int, len(e))
	for i := range e {
		ids[i] = e[i].ID
	}
	return uu.AddExportJobIDs(ids...)
}

// AddNotificationIDs adds the "notifications" edge to the Notification entity by IDs.
func (uu *UserUpdate) AddNotificationIDs(ids ...int) *UserUpdate {
	uu.mutation.AddNotificationIDs(ids...)
	return uu
}

// AddNotifications adds the "notifications" edges to the Notification entity.
func (uu *UserUpdate) AddNotifications(n ...*Notification) *UserUpdate {
	ids := make([]int, len(n))
	for i := range n {
		ids[i] = n[i].ID
	}
	return uu.AddNotificationIDs(ids...)
}

// AddPersonAssignmentIDs adds the "person_assignment" edge to the PersonAssignment entity by IDs.
func (uu *UserUpdate) AddPersonAssignmentIDs(ids ...int) *UserUpdate {
	uu.mutation.AddPersonAssignmentIDs(ids...)
	return uu
}

// AddPersonAssignment adds the "person_assignment" edges to the PersonAssignment entity.
func (uu *UserUpdate) AddPersonAssignment(p ...*PersonAssignment) *UserUpdate {
	ids := make([]int, len(p))
	for i := range p {
		ids[i] = p[i].ID
	}
	return uu.AddPersonAssignmentIDs(ids...)
}

// Mutation returns the UserMutation object of the builder.
func (uu *UserUpdate) Mutation() *UserMutation {
	return uu.mutation
}

// ClearAssignment clears all "assignment" edges to the TaskAssignment entity.
func (uu *UserUpdate) ClearAssignment() *UserUpdate {
	uu.mutation.ClearAssignment()
	return uu
}

// RemoveAssignmentIDs removes the "assignment" edge to TaskAssignment entities by IDs.
func (uu *UserUpdate) RemoveAssignmentIDs(ids ...int) *UserUpdate {
	uu.mutation.RemoveAssignmentIDs(ids...)
	return uu
}

// RemoveAssignment removes "assignment" edges to TaskAssignment entities.
func (uu *UserUpdate) RemoveAssignment(t ...*TaskAssignment) *UserUpdate {
	ids := make([]int, len(t))
	for i := range t {
		ids[i] = t[i].ID
	}
	return uu.RemoveAssignmentIDs(ids...)
}

// ClearAssignedPerson clears all "assigned_person" edges to the Person entity.
func (uu *UserUpdate) ClearAssignedPerson() *UserUpdate {
	uu.mutation.ClearAssignedPerson()
	return uu
}

// RemoveAssignedPersonIDs removes the "assigned_person" edge to Person entities by IDs.
func (uu *UserUpdate) RemoveAssignedPersonIDs(ids ...int) *UserUpdate {
	uu.mutation.RemoveAssignedPersonIDs(ids...)
	return uu
}

// RemoveAssignedPerson removes "assigned_person" edges to Person entities.
func (uu *UserUpdate) RemoveAssignedPerson(p ...*Person) *UserUpdate {
	ids := make([]int, len(p))
	for i := range p {
		ids[i] = p[i].ID
	}
	return uu.RemoveAssignedPersonIDs(ids...)
}

// ClearNotes clears all "notes" edges to the Note entity.
func (uu *UserUpdate) ClearNotes() *UserUpdate {
	uu.mutation.ClearNotes()
	return uu
}

// RemoveNoteIDs removes the "notes" edge to Note entities by IDs.
func (uu *UserUpdate) RemoveNoteIDs(ids ...int) *UserUpdate {
	uu.mutation.RemoveNoteIDs(ids...)
	return uu
}

// RemoveNotes removes "notes" edges to Note entities.
func (uu *UserUpdate) RemoveNotes(n ...*Note) *UserUpdate {
	ids := make([]int, len(n))
	for i := range n {
		ids[i] = n[i].ID
	}
	return uu.RemoveNoteIDs(ids...)
}

// ClearTaskNotes clears all "task_notes" edges to the TaskNote entity.
func (uu *UserUpdate) ClearTaskNotes() *UserUpdate {
	uu.mutation.ClearTaskNotes()
	return uu
}

// RemoveTaskNoteIDs removes the "task_notes" edge to TaskNote entities by IDs.
func (uu *UserUpdate) RemoveTaskNoteIDs(ids ...int) *UserUpdate {
	uu.mutation.RemoveTaskNoteIDs(ids...)
	return uu
}

// RemoveTaskNotes removes "task_notes" edges to TaskNote entities.
func (uu *UserUpdate) RemoveTaskNotes(t ...*TaskNote) *UserUpdate {
	ids := make([]int, len(t))
	for i := range t {
		ids[i] = t[i].ID
	}
	return uu.RemoveTaskNoteIDs(ids...)
}

// ClearAppointments clears all "appointments" edges to the Appointment entity.
func (uu *UserUpdate) ClearAppointments() *UserUpdate {
	uu.mutation.ClearAppointments()
	return uu
}

// RemoveAppointmentIDs removes the "appointments" edge to Appointment entities by IDs.
func (uu *UserUpdate) RemoveAppointmentIDs(ids ...int) *UserUpdate {
	uu.mutation.RemoveAppointmentIDs(ids...)
	return uu
}

// RemoveAppointments removes "appointments" edges to Appointment entities.
func (uu *UserUpdate) RemoveAppointments(a ...*Appointment) *UserUpdate {
	ids := make([]int, len(a))
	for i := range a {
		ids[i] = a[i].ID
	}
	return uu.RemoveAppointmentIDs(ids...)
}

// ClearCalls clears all "calls" edges to the Call entity.
func (uu *UserUpdate) ClearCalls() *UserUpdate {
	uu.mutation.ClearCalls()
	return uu
}

// RemoveCallIDs removes the "calls" edge to Call entities by IDs.
func (uu *UserUpdate) RemoveCallIDs(ids ...int) *UserUpdate {
	uu.mutation.RemoveCallIDs(ids...)
	return uu
}

// RemoveCalls removes "calls" edges to Call entities.
func (uu *UserUpdate) RemoveCalls(c ...*Call) *UserUpdate {
	ids := make([]int, len(c))
	for i := range c {
		ids[i] = c[i].ID
	}
	return uu.RemoveCallIDs(ids...)
}

// ClearSchedules clears all "schedules" edges to the Schedule entity.
func (uu *UserUpdate) ClearSchedules() *UserUpdate {
	uu.mutation.ClearSchedules()
	return uu
}

// RemoveScheduleIDs removes the "schedules" edge to Schedule entities by IDs.
func (uu *UserUpdate) RemoveScheduleIDs(ids ...int) *UserUpdate {
	uu.mutation.RemoveScheduleIDs(ids...)
	return uu
}

// RemoveSchedules removes "schedules" edges to Schedule entities.
func (uu *UserUpdate) RemoveSchedules(s ...*Schedule) *UserUpdate {
	ids := make([]int, len(s))
	for i := range s {
		ids[i] = s[i].ID
	}
	return uu.RemoveScheduleIDs(ids...)
}

// ClearMessageHistories clears all "message_histories" edges to the MessageHistory entity.
func (uu *UserUpdate) ClearMessageHistories() *UserUpdate {
	uu.mutation.ClearMessageHistories()
	return uu
}

// RemoveMessageHistoryIDs removes the "message_histories" edge to MessageHistory entities by IDs.
func (uu *UserUpdate) RemoveMessageHistoryIDs(ids ...int) *UserUpdate {
	uu.mutation.RemoveMessageHistoryIDs(ids...)
	return uu
}

// RemoveMessageHistories removes "message_histories" edges to MessageHistory entities.
func (uu *UserUpdate) RemoveMessageHistories(m ...*MessageHistory) *UserUpdate {
	ids := make([]int, len(m))
	for i := range m {
		ids[i] = m[i].ID
	}
	return uu.RemoveMessageHistoryIDs(ids...)
}

// ClearData clears all "data" edges to the UserData entity.
func (uu *UserUpdate) ClearData() *UserUpdate {
	uu.mutation.ClearData()
	return uu
}

// RemoveDatumIDs removes the "data" edge to UserData entities by IDs.
func (uu *UserUpdate) RemoveDatumIDs(ids ...int) *UserUpdate {
	uu.mutation.RemoveDatumIDs(ids...)
	return uu
}

// RemoveData removes "data" edges to UserData entities.
func (uu *UserUpdate) RemoveData(u ...*UserData) *UserUpdate {
	ids := make([]int, len(u))
	for i := range u {
		ids[i] = u[i].ID
	}
	return uu.RemoveDatumIDs(ids...)
}

// ClearExportJobs clears all "export_jobs" edges to the ExportJob entity.
func (uu *UserUpdate) ClearExportJobs() *UserUpdate {
	uu.mutation.ClearExportJobs()
	return uu
}

// RemoveExportJobIDs removes the "export_jobs" edge to ExportJob entities by IDs.
func (uu *UserUpdate) RemoveExportJobIDs(ids ...int) *UserUpdate {
	uu.mutation.RemoveExportJobIDs(ids...)
	return uu
}

// RemoveExportJobs removes "export_jobs" edges to ExportJob entities.
func (uu *UserUpdate) RemoveExportJobs(e ...*ExportJob) *UserUpdate {
	ids := make([]int, len(e))
	for i := range e {
		ids[i] = e[i].ID
	}
	return uu.RemoveExportJobIDs(ids...)
}

// ClearNotifications clears all "notifications" edges to the Notification entity.
func (uu *UserUpdate) ClearNotifications() *UserUpdate {
	uu.mutation.ClearNotifications()
	return uu
}

// RemoveNotificationIDs removes the "notifications" edge to Notification entities by IDs.
func (uu *UserUpdate) RemoveNotificationIDs(ids ...int) *UserUpdate {
	uu.mutation.RemoveNotificationIDs(ids...)
	return uu
}

// RemoveNotifications removes "notifications" edges to Notification entities.
func (uu *UserUpdate) RemoveNotifications(n ...*Notification) *UserUpdate {
	ids := make([]int, len(n))
	for i := range n {
		ids[i] = n[i].ID
	}
	return uu.RemoveNotificationIDs(ids...)
}

// ClearPersonAssignment clears all "person_assignment" edges to the PersonAssignment entity.
func (uu *UserUpdate) ClearPersonAssignment() *UserUpdate {
	uu.mutation.ClearPersonAssignment()
	return uu
}

// RemovePersonAssignmentIDs removes the "person_assignment" edge to PersonAssignment entities by IDs.
func (uu *UserUpdate) RemovePersonAssignmentIDs(ids ...int) *UserUpdate {
	uu.mutation.RemovePersonAssignmentIDs(ids...)
	return uu
}

// RemovePersonAssignment removes "person_assignment" edges to PersonAssignment entities.
func (uu *UserUpdate) RemovePersonAssignment(p ...*PersonAssignment) *UserUpdate {
	ids := make([]int, len(p))
	for i := range p {
		ids[i] = p[i].ID
	}
	return uu.RemovePersonAssignmentIDs(ids...)
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (uu *UserUpdate) Save(ctx context.Context) (int, error) {
	if err := uu.defaults(); err != nil {
		return 0, err
	}
	return withHooks(ctx, uu.sqlSave, uu.mutation, uu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (uu *UserUpdate) SaveX(ctx context.Context) int {
	affected, err := uu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (uu *UserUpdate) Exec(ctx context.Context) error {
	_, err := uu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (uu *UserUpdate) ExecX(ctx context.Context) {
	if err := uu.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (uu *UserUpdate) defaults() error {
	if _, ok := uu.mutation.UpdatedAt(); !ok {
		if user.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized user.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := user.UpdateDefaultUpdatedAt()
		uu.mutation.SetUpdatedAt(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (uu *UserUpdate) check() error {
	if v, ok := uu.mutation.State(); ok {
		if err := user.StateValidator(v); err != nil {
			return &ValidationError{Name: "state", err: fmt.Errorf(`ent: validator failed for field "User.state": %w`, err)}
		}
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (uu *UserUpdate) Modify(modifiers ...func(u *sql.UpdateBuilder)) *UserUpdate {
	uu.modifiers = append(uu.modifiers, modifiers...)
	return uu
}

func (uu *UserUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := uu.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(user.Table, user.Columns, sqlgraph.NewFieldSpec(user.FieldID, field.TypeInt))
	if ps := uu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := uu.mutation.DeletedAt(); ok {
		_spec.SetField(user.FieldDeletedAt, field.TypeTime, value)
	}
	if uu.mutation.DeletedAtCleared() {
		_spec.ClearField(user.FieldDeletedAt, field.TypeTime)
	}
	if value, ok := uu.mutation.Status(); ok {
		_spec.SetField(user.FieldStatus, field.TypeInt8, value)
	}
	if value, ok := uu.mutation.AddedStatus(); ok {
		_spec.AddField(user.FieldStatus, field.TypeInt8, value)
	}
	if value, ok := uu.mutation.Version(); ok {
		_spec.SetField(user.FieldVersion, field.TypeInt, value)
	}
	if value, ok := uu.mutation.AddedVersion(); ok {
		_spec.AddField(user.FieldVersion, field.TypeInt, value)
	}
	if value, ok := uu.mutation.UpdatedAt(); ok {
		_spec.SetField(user.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := uu.mutation.Username(); ok {
		_spec.SetField(user.FieldUsername, field.TypeString, value)
	}
	if value, ok := uu.mutation.Password(); ok {
		_spec.SetField(user.FieldPassword, field.TypeString, value)
	}
	if value, ok := uu.mutation.Phone(); ok {
		_spec.SetField(user.FieldPhone, field.TypeString, value)
	}
	if uu.mutation.PhoneCleared() {
		_spec.ClearField(user.FieldPhone, field.TypeString)
	}
	if value, ok := uu.mutation.Email(); ok {
		_spec.SetField(user.FieldEmail, field.TypeString, value)
	}
	if uu.mutation.EmailCleared() {
		_spec.ClearField(user.FieldEmail, field.TypeString)
	}
	if value, ok := uu.mutation.EmailConfirmed(); ok {
		_spec.SetField(user.FieldEmailConfirmed, field.TypeBool, value)
	}
	if uu.mutation.EmailConfirmedCleared() {
		_spec.ClearField(user.FieldEmailConfirmed, field.TypeBool)
	}
	if value, ok := uu.mutation.Name(); ok {
		_spec.SetField(user.FieldName, field.TypeString, value)
	}
	if uu.mutation.NameCleared() {
		_spec.ClearField(user.FieldName, field.TypeString)
	}
	if value, ok := uu.mutation.Gender(); ok {
		_spec.SetField(user.FieldGender, field.TypeString, value)
	}
	if value, ok := uu.mutation.DepartmentID(); ok {
		_spec.SetField(user.FieldDepartmentID, field.TypeInt, value)
	}
	if value, ok := uu.mutation.AddedDepartmentID(); ok {
		_spec.AddField(user.FieldDepartmentID, field.TypeInt, value)
	}
	if uu.mutation.DepartmentIDCleared() {
		_spec.ClearField(user.FieldDepartmentID, field.TypeInt)
	}
	if value, ok := uu.mutation.DepartmentPosition(); ok {
		_spec.SetField(user.FieldDepartmentPosition, field.TypeString, value)
	}
	if uu.mutation.DepartmentPositionCleared() {
		_spec.ClearField(user.FieldDepartmentPosition, field.TypeString)
	}
	if value, ok := uu.mutation.ProfileImage(); ok {
		_spec.SetField(user.FieldProfileImage, field.TypeString, value)
	}
	if uu.mutation.ProfileImageCleared() {
		_spec.ClearField(user.FieldProfileImage, field.TypeString)
	}
	if value, ok := uu.mutation.SuspendedAt(); ok {
		_spec.SetField(user.FieldSuspendedAt, field.TypeTime, value)
	}
	if uu.mutation.SuspendedAtCleared() {
		_spec.ClearField(user.FieldSuspendedAt, field.TypeTime)
	}
	if value, ok := uu.mutation.State(); ok {
		_spec.SetField(user.FieldState, field.TypeEnum, value)
	}
	if uu.mutation.AssignmentCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   user.AssignmentTable,
			Columns: []string{user.AssignmentColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(taskassignment.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uu.mutation.RemovedAssignmentIDs(); len(nodes) > 0 && !uu.mutation.AssignmentCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   user.AssignmentTable,
			Columns: []string{user.AssignmentColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(taskassignment.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uu.mutation.AssignmentIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   user.AssignmentTable,
			Columns: []string{user.AssignmentColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(taskassignment.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if uu.mutation.AssignedPersonCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: true,
			Table:   user.AssignedPersonTable,
			Columns: user.AssignedPersonPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(person.FieldID, field.TypeInt),
			},
		}
		createE := &PersonAssignmentCreate{config: uu.config, mutation: newPersonAssignmentMutation(uu.config, OpCreate)}
		createE.defaults()
		_, specE := createE.createSpec()
		edge.Target.Fields = specE.Fields
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uu.mutation.RemovedAssignedPersonIDs(); len(nodes) > 0 && !uu.mutation.AssignedPersonCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: true,
			Table:   user.AssignedPersonTable,
			Columns: user.AssignedPersonPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(person.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		createE := &PersonAssignmentCreate{config: uu.config, mutation: newPersonAssignmentMutation(uu.config, OpCreate)}
		createE.defaults()
		_, specE := createE.createSpec()
		edge.Target.Fields = specE.Fields
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uu.mutation.AssignedPersonIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: true,
			Table:   user.AssignedPersonTable,
			Columns: user.AssignedPersonPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(person.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		createE := &PersonAssignmentCreate{config: uu.config, mutation: newPersonAssignmentMutation(uu.config, OpCreate)}
		createE.defaults()
		_, specE := createE.createSpec()
		edge.Target.Fields = specE.Fields
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if uu.mutation.NotesCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   user.NotesTable,
			Columns: []string{user.NotesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(note.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uu.mutation.RemovedNotesIDs(); len(nodes) > 0 && !uu.mutation.NotesCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   user.NotesTable,
			Columns: []string{user.NotesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(note.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uu.mutation.NotesIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   user.NotesTable,
			Columns: []string{user.NotesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(note.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if uu.mutation.TaskNotesCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   user.TaskNotesTable,
			Columns: []string{user.TaskNotesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tasknote.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uu.mutation.RemovedTaskNotesIDs(); len(nodes) > 0 && !uu.mutation.TaskNotesCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   user.TaskNotesTable,
			Columns: []string{user.TaskNotesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tasknote.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uu.mutation.TaskNotesIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   user.TaskNotesTable,
			Columns: []string{user.TaskNotesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tasknote.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if uu.mutation.AppointmentsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   user.AppointmentsTable,
			Columns: []string{user.AppointmentsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(appointment.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uu.mutation.RemovedAppointmentsIDs(); len(nodes) > 0 && !uu.mutation.AppointmentsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   user.AppointmentsTable,
			Columns: []string{user.AppointmentsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(appointment.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uu.mutation.AppointmentsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   user.AppointmentsTable,
			Columns: []string{user.AppointmentsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(appointment.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if uu.mutation.CallsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   user.CallsTable,
			Columns: []string{user.CallsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(call.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uu.mutation.RemovedCallsIDs(); len(nodes) > 0 && !uu.mutation.CallsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   user.CallsTable,
			Columns: []string{user.CallsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(call.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uu.mutation.CallsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   user.CallsTable,
			Columns: []string{user.CallsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(call.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if uu.mutation.SchedulesCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   user.SchedulesTable,
			Columns: []string{user.SchedulesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(schedule.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uu.mutation.RemovedSchedulesIDs(); len(nodes) > 0 && !uu.mutation.SchedulesCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   user.SchedulesTable,
			Columns: []string{user.SchedulesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(schedule.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uu.mutation.SchedulesIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   user.SchedulesTable,
			Columns: []string{user.SchedulesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(schedule.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if uu.mutation.MessageHistoriesCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   user.MessageHistoriesTable,
			Columns: []string{user.MessageHistoriesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(messagehistory.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uu.mutation.RemovedMessageHistoriesIDs(); len(nodes) > 0 && !uu.mutation.MessageHistoriesCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   user.MessageHistoriesTable,
			Columns: []string{user.MessageHistoriesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(messagehistory.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uu.mutation.MessageHistoriesIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   user.MessageHistoriesTable,
			Columns: []string{user.MessageHistoriesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(messagehistory.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if uu.mutation.DataCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   user.DataTable,
			Columns: []string{user.DataColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(userdata.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uu.mutation.RemovedDataIDs(); len(nodes) > 0 && !uu.mutation.DataCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   user.DataTable,
			Columns: []string{user.DataColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(userdata.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uu.mutation.DataIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   user.DataTable,
			Columns: []string{user.DataColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(userdata.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if uu.mutation.ExportJobsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   user.ExportJobsTable,
			Columns: []string{user.ExportJobsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(exportjob.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uu.mutation.RemovedExportJobsIDs(); len(nodes) > 0 && !uu.mutation.ExportJobsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   user.ExportJobsTable,
			Columns: []string{user.ExportJobsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(exportjob.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uu.mutation.ExportJobsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   user.ExportJobsTable,
			Columns: []string{user.ExportJobsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(exportjob.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if uu.mutation.NotificationsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   user.NotificationsTable,
			Columns: []string{user.NotificationsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(notification.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uu.mutation.RemovedNotificationsIDs(); len(nodes) > 0 && !uu.mutation.NotificationsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   user.NotificationsTable,
			Columns: []string{user.NotificationsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(notification.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uu.mutation.NotificationsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   user.NotificationsTable,
			Columns: []string{user.NotificationsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(notification.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if uu.mutation.PersonAssignmentCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   user.PersonAssignmentTable,
			Columns: []string{user.PersonAssignmentColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(personassignment.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uu.mutation.RemovedPersonAssignmentIDs(); len(nodes) > 0 && !uu.mutation.PersonAssignmentCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   user.PersonAssignmentTable,
			Columns: []string{user.PersonAssignmentColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(personassignment.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uu.mutation.PersonAssignmentIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   user.PersonAssignmentTable,
			Columns: []string{user.PersonAssignmentColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(personassignment.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_spec.AddModifiers(uu.modifiers...)
	if n, err = sqlgraph.UpdateNodes(ctx, uu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{user.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	uu.mutation.done = true
	return n, nil
}

// UserUpdateOne is the builder for updating a single User entity.
type UserUpdateOne struct {
	config
	fields    []string
	hooks     []Hook
	mutation  *UserMutation
	modifiers []func(*sql.UpdateBuilder)
}

// SetDeletedAt sets the "deleted_at" field.
func (uuo *UserUpdateOne) SetDeletedAt(t time.Time) *UserUpdateOne {
	uuo.mutation.SetDeletedAt(t)
	return uuo
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (uuo *UserUpdateOne) SetNillableDeletedAt(t *time.Time) *UserUpdateOne {
	if t != nil {
		uuo.SetDeletedAt(*t)
	}
	return uuo
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (uuo *UserUpdateOne) ClearDeletedAt() *UserUpdateOne {
	uuo.mutation.ClearDeletedAt()
	return uuo
}

// SetStatus sets the "status" field.
func (uuo *UserUpdateOne) SetStatus(i int8) *UserUpdateOne {
	uuo.mutation.ResetStatus()
	uuo.mutation.SetStatus(i)
	return uuo
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (uuo *UserUpdateOne) SetNillableStatus(i *int8) *UserUpdateOne {
	if i != nil {
		uuo.SetStatus(*i)
	}
	return uuo
}

// AddStatus adds i to the "status" field.
func (uuo *UserUpdateOne) AddStatus(i int8) *UserUpdateOne {
	uuo.mutation.AddStatus(i)
	return uuo
}

// SetVersion sets the "version" field.
func (uuo *UserUpdateOne) SetVersion(i int) *UserUpdateOne {
	uuo.mutation.ResetVersion()
	uuo.mutation.SetVersion(i)
	return uuo
}

// SetNillableVersion sets the "version" field if the given value is not nil.
func (uuo *UserUpdateOne) SetNillableVersion(i *int) *UserUpdateOne {
	if i != nil {
		uuo.SetVersion(*i)
	}
	return uuo
}

// AddVersion adds i to the "version" field.
func (uuo *UserUpdateOne) AddVersion(i int) *UserUpdateOne {
	uuo.mutation.AddVersion(i)
	return uuo
}

// SetUpdatedAt sets the "updated_at" field.
func (uuo *UserUpdateOne) SetUpdatedAt(t time.Time) *UserUpdateOne {
	uuo.mutation.SetUpdatedAt(t)
	return uuo
}

// SetUsername sets the "username" field.
func (uuo *UserUpdateOne) SetUsername(s string) *UserUpdateOne {
	uuo.mutation.SetUsername(s)
	return uuo
}

// SetNillableUsername sets the "username" field if the given value is not nil.
func (uuo *UserUpdateOne) SetNillableUsername(s *string) *UserUpdateOne {
	if s != nil {
		uuo.SetUsername(*s)
	}
	return uuo
}

// SetPassword sets the "password" field.
func (uuo *UserUpdateOne) SetPassword(s string) *UserUpdateOne {
	uuo.mutation.SetPassword(s)
	return uuo
}

// SetNillablePassword sets the "password" field if the given value is not nil.
func (uuo *UserUpdateOne) SetNillablePassword(s *string) *UserUpdateOne {
	if s != nil {
		uuo.SetPassword(*s)
	}
	return uuo
}

// SetPhone sets the "phone" field.
func (uuo *UserUpdateOne) SetPhone(s string) *UserUpdateOne {
	uuo.mutation.SetPhone(s)
	return uuo
}

// SetNillablePhone sets the "phone" field if the given value is not nil.
func (uuo *UserUpdateOne) SetNillablePhone(s *string) *UserUpdateOne {
	if s != nil {
		uuo.SetPhone(*s)
	}
	return uuo
}

// ClearPhone clears the value of the "phone" field.
func (uuo *UserUpdateOne) ClearPhone() *UserUpdateOne {
	uuo.mutation.ClearPhone()
	return uuo
}

// SetEmail sets the "email" field.
func (uuo *UserUpdateOne) SetEmail(s string) *UserUpdateOne {
	uuo.mutation.SetEmail(s)
	return uuo
}

// SetNillableEmail sets the "email" field if the given value is not nil.
func (uuo *UserUpdateOne) SetNillableEmail(s *string) *UserUpdateOne {
	if s != nil {
		uuo.SetEmail(*s)
	}
	return uuo
}

// ClearEmail clears the value of the "email" field.
func (uuo *UserUpdateOne) ClearEmail() *UserUpdateOne {
	uuo.mutation.ClearEmail()
	return uuo
}

// SetEmailConfirmed sets the "email_confirmed" field.
func (uuo *UserUpdateOne) SetEmailConfirmed(b bool) *UserUpdateOne {
	uuo.mutation.SetEmailConfirmed(b)
	return uuo
}

// SetNillableEmailConfirmed sets the "email_confirmed" field if the given value is not nil.
func (uuo *UserUpdateOne) SetNillableEmailConfirmed(b *bool) *UserUpdateOne {
	if b != nil {
		uuo.SetEmailConfirmed(*b)
	}
	return uuo
}

// ClearEmailConfirmed clears the value of the "email_confirmed" field.
func (uuo *UserUpdateOne) ClearEmailConfirmed() *UserUpdateOne {
	uuo.mutation.ClearEmailConfirmed()
	return uuo
}

// SetName sets the "name" field.
func (uuo *UserUpdateOne) SetName(s string) *UserUpdateOne {
	uuo.mutation.SetName(s)
	return uuo
}

// SetNillableName sets the "name" field if the given value is not nil.
func (uuo *UserUpdateOne) SetNillableName(s *string) *UserUpdateOne {
	if s != nil {
		uuo.SetName(*s)
	}
	return uuo
}

// ClearName clears the value of the "name" field.
func (uuo *UserUpdateOne) ClearName() *UserUpdateOne {
	uuo.mutation.ClearName()
	return uuo
}

// SetGender sets the "gender" field.
func (uuo *UserUpdateOne) SetGender(s string) *UserUpdateOne {
	uuo.mutation.SetGender(s)
	return uuo
}

// SetNillableGender sets the "gender" field if the given value is not nil.
func (uuo *UserUpdateOne) SetNillableGender(s *string) *UserUpdateOne {
	if s != nil {
		uuo.SetGender(*s)
	}
	return uuo
}

// SetDepartmentID sets the "department_id" field.
func (uuo *UserUpdateOne) SetDepartmentID(i int) *UserUpdateOne {
	uuo.mutation.ResetDepartmentID()
	uuo.mutation.SetDepartmentID(i)
	return uuo
}

// SetNillableDepartmentID sets the "department_id" field if the given value is not nil.
func (uuo *UserUpdateOne) SetNillableDepartmentID(i *int) *UserUpdateOne {
	if i != nil {
		uuo.SetDepartmentID(*i)
	}
	return uuo
}

// AddDepartmentID adds i to the "department_id" field.
func (uuo *UserUpdateOne) AddDepartmentID(i int) *UserUpdateOne {
	uuo.mutation.AddDepartmentID(i)
	return uuo
}

// ClearDepartmentID clears the value of the "department_id" field.
func (uuo *UserUpdateOne) ClearDepartmentID() *UserUpdateOne {
	uuo.mutation.ClearDepartmentID()
	return uuo
}

// SetDepartmentPosition sets the "department_position" field.
func (uuo *UserUpdateOne) SetDepartmentPosition(s string) *UserUpdateOne {
	uuo.mutation.SetDepartmentPosition(s)
	return uuo
}

// SetNillableDepartmentPosition sets the "department_position" field if the given value is not nil.
func (uuo *UserUpdateOne) SetNillableDepartmentPosition(s *string) *UserUpdateOne {
	if s != nil {
		uuo.SetDepartmentPosition(*s)
	}
	return uuo
}

// ClearDepartmentPosition clears the value of the "department_position" field.
func (uuo *UserUpdateOne) ClearDepartmentPosition() *UserUpdateOne {
	uuo.mutation.ClearDepartmentPosition()
	return uuo
}

// SetProfileImage sets the "profile_image" field.
func (uuo *UserUpdateOne) SetProfileImage(s string) *UserUpdateOne {
	uuo.mutation.SetProfileImage(s)
	return uuo
}

// SetNillableProfileImage sets the "profile_image" field if the given value is not nil.
func (uuo *UserUpdateOne) SetNillableProfileImage(s *string) *UserUpdateOne {
	if s != nil {
		uuo.SetProfileImage(*s)
	}
	return uuo
}

// ClearProfileImage clears the value of the "profile_image" field.
func (uuo *UserUpdateOne) ClearProfileImage() *UserUpdateOne {
	uuo.mutation.ClearProfileImage()
	return uuo
}

// SetSuspendedAt sets the "suspended_at" field.
func (uuo *UserUpdateOne) SetSuspendedAt(t time.Time) *UserUpdateOne {
	uuo.mutation.SetSuspendedAt(t)
	return uuo
}

// SetNillableSuspendedAt sets the "suspended_at" field if the given value is not nil.
func (uuo *UserUpdateOne) SetNillableSuspendedAt(t *time.Time) *UserUpdateOne {
	if t != nil {
		uuo.SetSuspendedAt(*t)
	}
	return uuo
}

// ClearSuspendedAt clears the value of the "suspended_at" field.
func (uuo *UserUpdateOne) ClearSuspendedAt() *UserUpdateOne {
	uuo.mutation.ClearSuspendedAt()
	return uuo
}

// SetState sets the "state" field.
func (uuo *UserUpdateOne) SetState(u user.State) *UserUpdateOne {
	uuo.mutation.SetState(u)
	return uuo
}

// SetNillableState sets the "state" field if the given value is not nil.
func (uuo *UserUpdateOne) SetNillableState(u *user.State) *UserUpdateOne {
	if u != nil {
		uuo.SetState(*u)
	}
	return uuo
}

// AddAssignmentIDs adds the "assignment" edge to the TaskAssignment entity by IDs.
func (uuo *UserUpdateOne) AddAssignmentIDs(ids ...int) *UserUpdateOne {
	uuo.mutation.AddAssignmentIDs(ids...)
	return uuo
}

// AddAssignment adds the "assignment" edges to the TaskAssignment entity.
func (uuo *UserUpdateOne) AddAssignment(t ...*TaskAssignment) *UserUpdateOne {
	ids := make([]int, len(t))
	for i := range t {
		ids[i] = t[i].ID
	}
	return uuo.AddAssignmentIDs(ids...)
}

// AddAssignedPersonIDs adds the "assigned_person" edge to the Person entity by IDs.
func (uuo *UserUpdateOne) AddAssignedPersonIDs(ids ...int) *UserUpdateOne {
	uuo.mutation.AddAssignedPersonIDs(ids...)
	return uuo
}

// AddAssignedPerson adds the "assigned_person" edges to the Person entity.
func (uuo *UserUpdateOne) AddAssignedPerson(p ...*Person) *UserUpdateOne {
	ids := make([]int, len(p))
	for i := range p {
		ids[i] = p[i].ID
	}
	return uuo.AddAssignedPersonIDs(ids...)
}

// AddNoteIDs adds the "notes" edge to the Note entity by IDs.
func (uuo *UserUpdateOne) AddNoteIDs(ids ...int) *UserUpdateOne {
	uuo.mutation.AddNoteIDs(ids...)
	return uuo
}

// AddNotes adds the "notes" edges to the Note entity.
func (uuo *UserUpdateOne) AddNotes(n ...*Note) *UserUpdateOne {
	ids := make([]int, len(n))
	for i := range n {
		ids[i] = n[i].ID
	}
	return uuo.AddNoteIDs(ids...)
}

// AddTaskNoteIDs adds the "task_notes" edge to the TaskNote entity by IDs.
func (uuo *UserUpdateOne) AddTaskNoteIDs(ids ...int) *UserUpdateOne {
	uuo.mutation.AddTaskNoteIDs(ids...)
	return uuo
}

// AddTaskNotes adds the "task_notes" edges to the TaskNote entity.
func (uuo *UserUpdateOne) AddTaskNotes(t ...*TaskNote) *UserUpdateOne {
	ids := make([]int, len(t))
	for i := range t {
		ids[i] = t[i].ID
	}
	return uuo.AddTaskNoteIDs(ids...)
}

// AddAppointmentIDs adds the "appointments" edge to the Appointment entity by IDs.
func (uuo *UserUpdateOne) AddAppointmentIDs(ids ...int) *UserUpdateOne {
	uuo.mutation.AddAppointmentIDs(ids...)
	return uuo
}

// AddAppointments adds the "appointments" edges to the Appointment entity.
func (uuo *UserUpdateOne) AddAppointments(a ...*Appointment) *UserUpdateOne {
	ids := make([]int, len(a))
	for i := range a {
		ids[i] = a[i].ID
	}
	return uuo.AddAppointmentIDs(ids...)
}

// AddCallIDs adds the "calls" edge to the Call entity by IDs.
func (uuo *UserUpdateOne) AddCallIDs(ids ...int) *UserUpdateOne {
	uuo.mutation.AddCallIDs(ids...)
	return uuo
}

// AddCalls adds the "calls" edges to the Call entity.
func (uuo *UserUpdateOne) AddCalls(c ...*Call) *UserUpdateOne {
	ids := make([]int, len(c))
	for i := range c {
		ids[i] = c[i].ID
	}
	return uuo.AddCallIDs(ids...)
}

// AddScheduleIDs adds the "schedules" edge to the Schedule entity by IDs.
func (uuo *UserUpdateOne) AddScheduleIDs(ids ...int) *UserUpdateOne {
	uuo.mutation.AddScheduleIDs(ids...)
	return uuo
}

// AddSchedules adds the "schedules" edges to the Schedule entity.
func (uuo *UserUpdateOne) AddSchedules(s ...*Schedule) *UserUpdateOne {
	ids := make([]int, len(s))
	for i := range s {
		ids[i] = s[i].ID
	}
	return uuo.AddScheduleIDs(ids...)
}

// AddMessageHistoryIDs adds the "message_histories" edge to the MessageHistory entity by IDs.
func (uuo *UserUpdateOne) AddMessageHistoryIDs(ids ...int) *UserUpdateOne {
	uuo.mutation.AddMessageHistoryIDs(ids...)
	return uuo
}

// AddMessageHistories adds the "message_histories" edges to the MessageHistory entity.
func (uuo *UserUpdateOne) AddMessageHistories(m ...*MessageHistory) *UserUpdateOne {
	ids := make([]int, len(m))
	for i := range m {
		ids[i] = m[i].ID
	}
	return uuo.AddMessageHistoryIDs(ids...)
}

// AddDatumIDs adds the "data" edge to the UserData entity by IDs.
func (uuo *UserUpdateOne) AddDatumIDs(ids ...int) *UserUpdateOne {
	uuo.mutation.AddDatumIDs(ids...)
	return uuo
}

// AddData adds the "data" edges to the UserData entity.
func (uuo *UserUpdateOne) AddData(u ...*UserData) *UserUpdateOne {
	ids := make([]int, len(u))
	for i := range u {
		ids[i] = u[i].ID
	}
	return uuo.AddDatumIDs(ids...)
}

// AddExportJobIDs adds the "export_jobs" edge to the ExportJob entity by IDs.
func (uuo *UserUpdateOne) AddExportJobIDs(ids ...int) *UserUpdateOne {
	uuo.mutation.AddExportJobIDs(ids...)
	return uuo
}

// AddExportJobs adds the "export_jobs" edges to the ExportJob entity.
func (uuo *UserUpdateOne) AddExportJobs(e ...*ExportJob) *UserUpdateOne {
	ids := make([]int, len(e))
	for i := range e {
		ids[i] = e[i].ID
	}
	return uuo.AddExportJobIDs(ids...)
}

// AddNotificationIDs adds the "notifications" edge to the Notification entity by IDs.
func (uuo *UserUpdateOne) AddNotificationIDs(ids ...int) *UserUpdateOne {
	uuo.mutation.AddNotificationIDs(ids...)
	return uuo
}

// AddNotifications adds the "notifications" edges to the Notification entity.
func (uuo *UserUpdateOne) AddNotifications(n ...*Notification) *UserUpdateOne {
	ids := make([]int, len(n))
	for i := range n {
		ids[i] = n[i].ID
	}
	return uuo.AddNotificationIDs(ids...)
}

// AddPersonAssignmentIDs adds the "person_assignment" edge to the PersonAssignment entity by IDs.
func (uuo *UserUpdateOne) AddPersonAssignmentIDs(ids ...int) *UserUpdateOne {
	uuo.mutation.AddPersonAssignmentIDs(ids...)
	return uuo
}

// AddPersonAssignment adds the "person_assignment" edges to the PersonAssignment entity.
func (uuo *UserUpdateOne) AddPersonAssignment(p ...*PersonAssignment) *UserUpdateOne {
	ids := make([]int, len(p))
	for i := range p {
		ids[i] = p[i].ID
	}
	return uuo.AddPersonAssignmentIDs(ids...)
}

// Mutation returns the UserMutation object of the builder.
func (uuo *UserUpdateOne) Mutation() *UserMutation {
	return uuo.mutation
}

// ClearAssignment clears all "assignment" edges to the TaskAssignment entity.
func (uuo *UserUpdateOne) ClearAssignment() *UserUpdateOne {
	uuo.mutation.ClearAssignment()
	return uuo
}

// RemoveAssignmentIDs removes the "assignment" edge to TaskAssignment entities by IDs.
func (uuo *UserUpdateOne) RemoveAssignmentIDs(ids ...int) *UserUpdateOne {
	uuo.mutation.RemoveAssignmentIDs(ids...)
	return uuo
}

// RemoveAssignment removes "assignment" edges to TaskAssignment entities.
func (uuo *UserUpdateOne) RemoveAssignment(t ...*TaskAssignment) *UserUpdateOne {
	ids := make([]int, len(t))
	for i := range t {
		ids[i] = t[i].ID
	}
	return uuo.RemoveAssignmentIDs(ids...)
}

// ClearAssignedPerson clears all "assigned_person" edges to the Person entity.
func (uuo *UserUpdateOne) ClearAssignedPerson() *UserUpdateOne {
	uuo.mutation.ClearAssignedPerson()
	return uuo
}

// RemoveAssignedPersonIDs removes the "assigned_person" edge to Person entities by IDs.
func (uuo *UserUpdateOne) RemoveAssignedPersonIDs(ids ...int) *UserUpdateOne {
	uuo.mutation.RemoveAssignedPersonIDs(ids...)
	return uuo
}

// RemoveAssignedPerson removes "assigned_person" edges to Person entities.
func (uuo *UserUpdateOne) RemoveAssignedPerson(p ...*Person) *UserUpdateOne {
	ids := make([]int, len(p))
	for i := range p {
		ids[i] = p[i].ID
	}
	return uuo.RemoveAssignedPersonIDs(ids...)
}

// ClearNotes clears all "notes" edges to the Note entity.
func (uuo *UserUpdateOne) ClearNotes() *UserUpdateOne {
	uuo.mutation.ClearNotes()
	return uuo
}

// RemoveNoteIDs removes the "notes" edge to Note entities by IDs.
func (uuo *UserUpdateOne) RemoveNoteIDs(ids ...int) *UserUpdateOne {
	uuo.mutation.RemoveNoteIDs(ids...)
	return uuo
}

// RemoveNotes removes "notes" edges to Note entities.
func (uuo *UserUpdateOne) RemoveNotes(n ...*Note) *UserUpdateOne {
	ids := make([]int, len(n))
	for i := range n {
		ids[i] = n[i].ID
	}
	return uuo.RemoveNoteIDs(ids...)
}

// ClearTaskNotes clears all "task_notes" edges to the TaskNote entity.
func (uuo *UserUpdateOne) ClearTaskNotes() *UserUpdateOne {
	uuo.mutation.ClearTaskNotes()
	return uuo
}

// RemoveTaskNoteIDs removes the "task_notes" edge to TaskNote entities by IDs.
func (uuo *UserUpdateOne) RemoveTaskNoteIDs(ids ...int) *UserUpdateOne {
	uuo.mutation.RemoveTaskNoteIDs(ids...)
	return uuo
}

// RemoveTaskNotes removes "task_notes" edges to TaskNote entities.
func (uuo *UserUpdateOne) RemoveTaskNotes(t ...*TaskNote) *UserUpdateOne {
	ids := make([]int, len(t))
	for i := range t {
		ids[i] = t[i].ID
	}
	return uuo.RemoveTaskNoteIDs(ids...)
}

// ClearAppointments clears all "appointments" edges to the Appointment entity.
func (uuo *UserUpdateOne) ClearAppointments() *UserUpdateOne {
	uuo.mutation.ClearAppointments()
	return uuo
}

// RemoveAppointmentIDs removes the "appointments" edge to Appointment entities by IDs.
func (uuo *UserUpdateOne) RemoveAppointmentIDs(ids ...int) *UserUpdateOne {
	uuo.mutation.RemoveAppointmentIDs(ids...)
	return uuo
}

// RemoveAppointments removes "appointments" edges to Appointment entities.
func (uuo *UserUpdateOne) RemoveAppointments(a ...*Appointment) *UserUpdateOne {
	ids := make([]int, len(a))
	for i := range a {
		ids[i] = a[i].ID
	}
	return uuo.RemoveAppointmentIDs(ids...)
}

// ClearCalls clears all "calls" edges to the Call entity.
func (uuo *UserUpdateOne) ClearCalls() *UserUpdateOne {
	uuo.mutation.ClearCalls()
	return uuo
}

// RemoveCallIDs removes the "calls" edge to Call entities by IDs.
func (uuo *UserUpdateOne) RemoveCallIDs(ids ...int) *UserUpdateOne {
	uuo.mutation.RemoveCallIDs(ids...)
	return uuo
}

// RemoveCalls removes "calls" edges to Call entities.
func (uuo *UserUpdateOne) RemoveCalls(c ...*Call) *UserUpdateOne {
	ids := make([]int, len(c))
	for i := range c {
		ids[i] = c[i].ID
	}
	return uuo.RemoveCallIDs(ids...)
}

// ClearSchedules clears all "schedules" edges to the Schedule entity.
func (uuo *UserUpdateOne) ClearSchedules() *UserUpdateOne {
	uuo.mutation.ClearSchedules()
	return uuo
}

// RemoveScheduleIDs removes the "schedules" edge to Schedule entities by IDs.
func (uuo *UserUpdateOne) RemoveScheduleIDs(ids ...int) *UserUpdateOne {
	uuo.mutation.RemoveScheduleIDs(ids...)
	return uuo
}

// RemoveSchedules removes "schedules" edges to Schedule entities.
func (uuo *UserUpdateOne) RemoveSchedules(s ...*Schedule) *UserUpdateOne {
	ids := make([]int, len(s))
	for i := range s {
		ids[i] = s[i].ID
	}
	return uuo.RemoveScheduleIDs(ids...)
}

// ClearMessageHistories clears all "message_histories" edges to the MessageHistory entity.
func (uuo *UserUpdateOne) ClearMessageHistories() *UserUpdateOne {
	uuo.mutation.ClearMessageHistories()
	return uuo
}

// RemoveMessageHistoryIDs removes the "message_histories" edge to MessageHistory entities by IDs.
func (uuo *UserUpdateOne) RemoveMessageHistoryIDs(ids ...int) *UserUpdateOne {
	uuo.mutation.RemoveMessageHistoryIDs(ids...)
	return uuo
}

// RemoveMessageHistories removes "message_histories" edges to MessageHistory entities.
func (uuo *UserUpdateOne) RemoveMessageHistories(m ...*MessageHistory) *UserUpdateOne {
	ids := make([]int, len(m))
	for i := range m {
		ids[i] = m[i].ID
	}
	return uuo.RemoveMessageHistoryIDs(ids...)
}

// ClearData clears all "data" edges to the UserData entity.
func (uuo *UserUpdateOne) ClearData() *UserUpdateOne {
	uuo.mutation.ClearData()
	return uuo
}

// RemoveDatumIDs removes the "data" edge to UserData entities by IDs.
func (uuo *UserUpdateOne) RemoveDatumIDs(ids ...int) *UserUpdateOne {
	uuo.mutation.RemoveDatumIDs(ids...)
	return uuo
}

// RemoveData removes "data" edges to UserData entities.
func (uuo *UserUpdateOne) RemoveData(u ...*UserData) *UserUpdateOne {
	ids := make([]int, len(u))
	for i := range u {
		ids[i] = u[i].ID
	}
	return uuo.RemoveDatumIDs(ids...)
}

// ClearExportJobs clears all "export_jobs" edges to the ExportJob entity.
func (uuo *UserUpdateOne) ClearExportJobs() *UserUpdateOne {
	uuo.mutation.ClearExportJobs()
	return uuo
}

// RemoveExportJobIDs removes the "export_jobs" edge to ExportJob entities by IDs.
func (uuo *UserUpdateOne) RemoveExportJobIDs(ids ...int) *UserUpdateOne {
	uuo.mutation.RemoveExportJobIDs(ids...)
	return uuo
}

// RemoveExportJobs removes "export_jobs" edges to ExportJob entities.
func (uuo *UserUpdateOne) RemoveExportJobs(e ...*ExportJob) *UserUpdateOne {
	ids := make([]int, len(e))
	for i := range e {
		ids[i] = e[i].ID
	}
	return uuo.RemoveExportJobIDs(ids...)
}

// ClearNotifications clears all "notifications" edges to the Notification entity.
func (uuo *UserUpdateOne) ClearNotifications() *UserUpdateOne {
	uuo.mutation.ClearNotifications()
	return uuo
}

// RemoveNotificationIDs removes the "notifications" edge to Notification entities by IDs.
func (uuo *UserUpdateOne) RemoveNotificationIDs(ids ...int) *UserUpdateOne {
	uuo.mutation.RemoveNotificationIDs(ids...)
	return uuo
}

// RemoveNotifications removes "notifications" edges to Notification entities.
func (uuo *UserUpdateOne) RemoveNotifications(n ...*Notification) *UserUpdateOne {
	ids := make([]int, len(n))
	for i := range n {
		ids[i] = n[i].ID
	}
	return uuo.RemoveNotificationIDs(ids...)
}

// ClearPersonAssignment clears all "person_assignment" edges to the PersonAssignment entity.
func (uuo *UserUpdateOne) ClearPersonAssignment() *UserUpdateOne {
	uuo.mutation.ClearPersonAssignment()
	return uuo
}

// RemovePersonAssignmentIDs removes the "person_assignment" edge to PersonAssignment entities by IDs.
func (uuo *UserUpdateOne) RemovePersonAssignmentIDs(ids ...int) *UserUpdateOne {
	uuo.mutation.RemovePersonAssignmentIDs(ids...)
	return uuo
}

// RemovePersonAssignment removes "person_assignment" edges to PersonAssignment entities.
func (uuo *UserUpdateOne) RemovePersonAssignment(p ...*PersonAssignment) *UserUpdateOne {
	ids := make([]int, len(p))
	for i := range p {
		ids[i] = p[i].ID
	}
	return uuo.RemovePersonAssignmentIDs(ids...)
}

// Where appends a list predicates to the UserUpdate builder.
func (uuo *UserUpdateOne) Where(ps ...predicate.User) *UserUpdateOne {
	uuo.mutation.Where(ps...)
	return uuo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (uuo *UserUpdateOne) Select(field string, fields ...string) *UserUpdateOne {
	uuo.fields = append([]string{field}, fields...)
	return uuo
}

// Save executes the query and returns the updated User entity.
func (uuo *UserUpdateOne) Save(ctx context.Context) (*User, error) {
	if err := uuo.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, uuo.sqlSave, uuo.mutation, uuo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (uuo *UserUpdateOne) SaveX(ctx context.Context) *User {
	node, err := uuo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (uuo *UserUpdateOne) Exec(ctx context.Context) error {
	_, err := uuo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (uuo *UserUpdateOne) ExecX(ctx context.Context) {
	if err := uuo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (uuo *UserUpdateOne) defaults() error {
	if _, ok := uuo.mutation.UpdatedAt(); !ok {
		if user.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized user.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := user.UpdateDefaultUpdatedAt()
		uuo.mutation.SetUpdatedAt(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (uuo *UserUpdateOne) check() error {
	if v, ok := uuo.mutation.State(); ok {
		if err := user.StateValidator(v); err != nil {
			return &ValidationError{Name: "state", err: fmt.Errorf(`ent: validator failed for field "User.state": %w`, err)}
		}
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (uuo *UserUpdateOne) Modify(modifiers ...func(u *sql.UpdateBuilder)) *UserUpdateOne {
	uuo.modifiers = append(uuo.modifiers, modifiers...)
	return uuo
}

func (uuo *UserUpdateOne) sqlSave(ctx context.Context) (_node *User, err error) {
	if err := uuo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(user.Table, user.Columns, sqlgraph.NewFieldSpec(user.FieldID, field.TypeInt))
	id, ok := uuo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "User.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := uuo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, user.FieldID)
		for _, f := range fields {
			if !user.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != user.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := uuo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := uuo.mutation.DeletedAt(); ok {
		_spec.SetField(user.FieldDeletedAt, field.TypeTime, value)
	}
	if uuo.mutation.DeletedAtCleared() {
		_spec.ClearField(user.FieldDeletedAt, field.TypeTime)
	}
	if value, ok := uuo.mutation.Status(); ok {
		_spec.SetField(user.FieldStatus, field.TypeInt8, value)
	}
	if value, ok := uuo.mutation.AddedStatus(); ok {
		_spec.AddField(user.FieldStatus, field.TypeInt8, value)
	}
	if value, ok := uuo.mutation.Version(); ok {
		_spec.SetField(user.FieldVersion, field.TypeInt, value)
	}
	if value, ok := uuo.mutation.AddedVersion(); ok {
		_spec.AddField(user.FieldVersion, field.TypeInt, value)
	}
	if value, ok := uuo.mutation.UpdatedAt(); ok {
		_spec.SetField(user.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := uuo.mutation.Username(); ok {
		_spec.SetField(user.FieldUsername, field.TypeString, value)
	}
	if value, ok := uuo.mutation.Password(); ok {
		_spec.SetField(user.FieldPassword, field.TypeString, value)
	}
	if value, ok := uuo.mutation.Phone(); ok {
		_spec.SetField(user.FieldPhone, field.TypeString, value)
	}
	if uuo.mutation.PhoneCleared() {
		_spec.ClearField(user.FieldPhone, field.TypeString)
	}
	if value, ok := uuo.mutation.Email(); ok {
		_spec.SetField(user.FieldEmail, field.TypeString, value)
	}
	if uuo.mutation.EmailCleared() {
		_spec.ClearField(user.FieldEmail, field.TypeString)
	}
	if value, ok := uuo.mutation.EmailConfirmed(); ok {
		_spec.SetField(user.FieldEmailConfirmed, field.TypeBool, value)
	}
	if uuo.mutation.EmailConfirmedCleared() {
		_spec.ClearField(user.FieldEmailConfirmed, field.TypeBool)
	}
	if value, ok := uuo.mutation.Name(); ok {
		_spec.SetField(user.FieldName, field.TypeString, value)
	}
	if uuo.mutation.NameCleared() {
		_spec.ClearField(user.FieldName, field.TypeString)
	}
	if value, ok := uuo.mutation.Gender(); ok {
		_spec.SetField(user.FieldGender, field.TypeString, value)
	}
	if value, ok := uuo.mutation.DepartmentID(); ok {
		_spec.SetField(user.FieldDepartmentID, field.TypeInt, value)
	}
	if value, ok := uuo.mutation.AddedDepartmentID(); ok {
		_spec.AddField(user.FieldDepartmentID, field.TypeInt, value)
	}
	if uuo.mutation.DepartmentIDCleared() {
		_spec.ClearField(user.FieldDepartmentID, field.TypeInt)
	}
	if value, ok := uuo.mutation.DepartmentPosition(); ok {
		_spec.SetField(user.FieldDepartmentPosition, field.TypeString, value)
	}
	if uuo.mutation.DepartmentPositionCleared() {
		_spec.ClearField(user.FieldDepartmentPosition, field.TypeString)
	}
	if value, ok := uuo.mutation.ProfileImage(); ok {
		_spec.SetField(user.FieldProfileImage, field.TypeString, value)
	}
	if uuo.mutation.ProfileImageCleared() {
		_spec.ClearField(user.FieldProfileImage, field.TypeString)
	}
	if value, ok := uuo.mutation.SuspendedAt(); ok {
		_spec.SetField(user.FieldSuspendedAt, field.TypeTime, value)
	}
	if uuo.mutation.SuspendedAtCleared() {
		_spec.ClearField(user.FieldSuspendedAt, field.TypeTime)
	}
	if value, ok := uuo.mutation.State(); ok {
		_spec.SetField(user.FieldState, field.TypeEnum, value)
	}
	if uuo.mutation.AssignmentCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   user.AssignmentTable,
			Columns: []string{user.AssignmentColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(taskassignment.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uuo.mutation.RemovedAssignmentIDs(); len(nodes) > 0 && !uuo.mutation.AssignmentCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   user.AssignmentTable,
			Columns: []string{user.AssignmentColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(taskassignment.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uuo.mutation.AssignmentIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   user.AssignmentTable,
			Columns: []string{user.AssignmentColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(taskassignment.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if uuo.mutation.AssignedPersonCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: true,
			Table:   user.AssignedPersonTable,
			Columns: user.AssignedPersonPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(person.FieldID, field.TypeInt),
			},
		}
		createE := &PersonAssignmentCreate{config: uuo.config, mutation: newPersonAssignmentMutation(uuo.config, OpCreate)}
		createE.defaults()
		_, specE := createE.createSpec()
		edge.Target.Fields = specE.Fields
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uuo.mutation.RemovedAssignedPersonIDs(); len(nodes) > 0 && !uuo.mutation.AssignedPersonCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: true,
			Table:   user.AssignedPersonTable,
			Columns: user.AssignedPersonPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(person.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		createE := &PersonAssignmentCreate{config: uuo.config, mutation: newPersonAssignmentMutation(uuo.config, OpCreate)}
		createE.defaults()
		_, specE := createE.createSpec()
		edge.Target.Fields = specE.Fields
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uuo.mutation.AssignedPersonIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: true,
			Table:   user.AssignedPersonTable,
			Columns: user.AssignedPersonPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(person.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		createE := &PersonAssignmentCreate{config: uuo.config, mutation: newPersonAssignmentMutation(uuo.config, OpCreate)}
		createE.defaults()
		_, specE := createE.createSpec()
		edge.Target.Fields = specE.Fields
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if uuo.mutation.NotesCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   user.NotesTable,
			Columns: []string{user.NotesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(note.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uuo.mutation.RemovedNotesIDs(); len(nodes) > 0 && !uuo.mutation.NotesCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   user.NotesTable,
			Columns: []string{user.NotesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(note.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uuo.mutation.NotesIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   user.NotesTable,
			Columns: []string{user.NotesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(note.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if uuo.mutation.TaskNotesCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   user.TaskNotesTable,
			Columns: []string{user.TaskNotesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tasknote.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uuo.mutation.RemovedTaskNotesIDs(); len(nodes) > 0 && !uuo.mutation.TaskNotesCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   user.TaskNotesTable,
			Columns: []string{user.TaskNotesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tasknote.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uuo.mutation.TaskNotesIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   user.TaskNotesTable,
			Columns: []string{user.TaskNotesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tasknote.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if uuo.mutation.AppointmentsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   user.AppointmentsTable,
			Columns: []string{user.AppointmentsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(appointment.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uuo.mutation.RemovedAppointmentsIDs(); len(nodes) > 0 && !uuo.mutation.AppointmentsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   user.AppointmentsTable,
			Columns: []string{user.AppointmentsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(appointment.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uuo.mutation.AppointmentsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   user.AppointmentsTable,
			Columns: []string{user.AppointmentsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(appointment.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if uuo.mutation.CallsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   user.CallsTable,
			Columns: []string{user.CallsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(call.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uuo.mutation.RemovedCallsIDs(); len(nodes) > 0 && !uuo.mutation.CallsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   user.CallsTable,
			Columns: []string{user.CallsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(call.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uuo.mutation.CallsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   user.CallsTable,
			Columns: []string{user.CallsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(call.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if uuo.mutation.SchedulesCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   user.SchedulesTable,
			Columns: []string{user.SchedulesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(schedule.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uuo.mutation.RemovedSchedulesIDs(); len(nodes) > 0 && !uuo.mutation.SchedulesCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   user.SchedulesTable,
			Columns: []string{user.SchedulesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(schedule.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uuo.mutation.SchedulesIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   user.SchedulesTable,
			Columns: []string{user.SchedulesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(schedule.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if uuo.mutation.MessageHistoriesCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   user.MessageHistoriesTable,
			Columns: []string{user.MessageHistoriesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(messagehistory.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uuo.mutation.RemovedMessageHistoriesIDs(); len(nodes) > 0 && !uuo.mutation.MessageHistoriesCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   user.MessageHistoriesTable,
			Columns: []string{user.MessageHistoriesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(messagehistory.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uuo.mutation.MessageHistoriesIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   user.MessageHistoriesTable,
			Columns: []string{user.MessageHistoriesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(messagehistory.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if uuo.mutation.DataCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   user.DataTable,
			Columns: []string{user.DataColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(userdata.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uuo.mutation.RemovedDataIDs(); len(nodes) > 0 && !uuo.mutation.DataCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   user.DataTable,
			Columns: []string{user.DataColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(userdata.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uuo.mutation.DataIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   user.DataTable,
			Columns: []string{user.DataColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(userdata.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if uuo.mutation.ExportJobsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   user.ExportJobsTable,
			Columns: []string{user.ExportJobsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(exportjob.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uuo.mutation.RemovedExportJobsIDs(); len(nodes) > 0 && !uuo.mutation.ExportJobsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   user.ExportJobsTable,
			Columns: []string{user.ExportJobsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(exportjob.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uuo.mutation.ExportJobsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   user.ExportJobsTable,
			Columns: []string{user.ExportJobsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(exportjob.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if uuo.mutation.NotificationsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   user.NotificationsTable,
			Columns: []string{user.NotificationsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(notification.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uuo.mutation.RemovedNotificationsIDs(); len(nodes) > 0 && !uuo.mutation.NotificationsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   user.NotificationsTable,
			Columns: []string{user.NotificationsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(notification.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uuo.mutation.NotificationsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   user.NotificationsTable,
			Columns: []string{user.NotificationsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(notification.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if uuo.mutation.PersonAssignmentCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   user.PersonAssignmentTable,
			Columns: []string{user.PersonAssignmentColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(personassignment.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uuo.mutation.RemovedPersonAssignmentIDs(); len(nodes) > 0 && !uuo.mutation.PersonAssignmentCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   user.PersonAssignmentTable,
			Columns: []string{user.PersonAssignmentColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(personassignment.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uuo.mutation.PersonAssignmentIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   user.PersonAssignmentTable,
			Columns: []string{user.PersonAssignmentColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(personassignment.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_spec.AddModifiers(uuo.modifiers...)
	_node = &User{config: uuo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, uuo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{user.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	uuo.mutation.done = true
	return _node, nil
}
