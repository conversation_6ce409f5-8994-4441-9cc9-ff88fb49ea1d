// Code generated by ent, DO NOT EDIT.

package track

import (
	"bcare/ent/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

// ID filters vertices based on their ID field.
func ID(id int) predicate.Track {
	return predicate.Track(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id int) predicate.Track {
	return predicate.Track(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id int) predicate.Track {
	return predicate.Track(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...int) predicate.Track {
	return predicate.Track(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...int) predicate.Track {
	return predicate.Track(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id int) predicate.Track {
	return predicate.Track(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id int) predicate.Track {
	return predicate.Track(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id int) predicate.Track {
	return predicate.Track(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id int) predicate.Track {
	return predicate.Track(sql.FieldLTE(FieldID, id))
}

// DeletedAt applies equality check predicate on the "deleted_at" field. It's identical to DeletedAtEQ.
func DeletedAt(v time.Time) predicate.Track {
	return predicate.Track(sql.FieldEQ(FieldDeletedAt, v))
}

// Status applies equality check predicate on the "status" field. It's identical to StatusEQ.
func Status(v int8) predicate.Track {
	return predicate.Track(sql.FieldEQ(FieldStatus, v))
}

// Version applies equality check predicate on the "version" field. It's identical to VersionEQ.
func Version(v int) predicate.Track {
	return predicate.Track(sql.FieldEQ(FieldVersion, v))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.Track {
	return predicate.Track(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.Track {
	return predicate.Track(sql.FieldEQ(FieldUpdatedAt, v))
}

// Begin applies equality check predicate on the "begin" field. It's identical to BeginEQ.
func Begin(v time.Time) predicate.Track {
	return predicate.Track(sql.FieldEQ(FieldBegin, v))
}

// End applies equality check predicate on the "end" field. It's identical to EndEQ.
func End(v time.Time) predicate.Track {
	return predicate.Track(sql.FieldEQ(FieldEnd, v))
}

// DealID applies equality check predicate on the "deal_id" field. It's identical to DealIDEQ.
func DealID(v int) predicate.Track {
	return predicate.Track(sql.FieldEQ(FieldDealID, v))
}

// PipelineID applies equality check predicate on the "pipeline_id" field. It's identical to PipelineIDEQ.
func PipelineID(v int) predicate.Track {
	return predicate.Track(sql.FieldEQ(FieldPipelineID, v))
}

// StageID applies equality check predicate on the "stage_id" field. It's identical to StageIDEQ.
func StageID(v int) predicate.Track {
	return predicate.Track(sql.FieldEQ(FieldStageID, v))
}

// UserID applies equality check predicate on the "user_id" field. It's identical to UserIDEQ.
func UserID(v int) predicate.Track {
	return predicate.Track(sql.FieldEQ(FieldUserID, v))
}

// PersonID applies equality check predicate on the "person_id" field. It's identical to PersonIDEQ.
func PersonID(v int) predicate.Track {
	return predicate.Track(sql.FieldEQ(FieldPersonID, v))
}

// Weight applies equality check predicate on the "weight" field. It's identical to WeightEQ.
func Weight(v float64) predicate.Track {
	return predicate.Track(sql.FieldEQ(FieldWeight, v))
}

// DeletedAtEQ applies the EQ predicate on the "deleted_at" field.
func DeletedAtEQ(v time.Time) predicate.Track {
	return predicate.Track(sql.FieldEQ(FieldDeletedAt, v))
}

// DeletedAtNEQ applies the NEQ predicate on the "deleted_at" field.
func DeletedAtNEQ(v time.Time) predicate.Track {
	return predicate.Track(sql.FieldNEQ(FieldDeletedAt, v))
}

// DeletedAtIn applies the In predicate on the "deleted_at" field.
func DeletedAtIn(vs ...time.Time) predicate.Track {
	return predicate.Track(sql.FieldIn(FieldDeletedAt, vs...))
}

// DeletedAtNotIn applies the NotIn predicate on the "deleted_at" field.
func DeletedAtNotIn(vs ...time.Time) predicate.Track {
	return predicate.Track(sql.FieldNotIn(FieldDeletedAt, vs...))
}

// DeletedAtGT applies the GT predicate on the "deleted_at" field.
func DeletedAtGT(v time.Time) predicate.Track {
	return predicate.Track(sql.FieldGT(FieldDeletedAt, v))
}

// DeletedAtGTE applies the GTE predicate on the "deleted_at" field.
func DeletedAtGTE(v time.Time) predicate.Track {
	return predicate.Track(sql.FieldGTE(FieldDeletedAt, v))
}

// DeletedAtLT applies the LT predicate on the "deleted_at" field.
func DeletedAtLT(v time.Time) predicate.Track {
	return predicate.Track(sql.FieldLT(FieldDeletedAt, v))
}

// DeletedAtLTE applies the LTE predicate on the "deleted_at" field.
func DeletedAtLTE(v time.Time) predicate.Track {
	return predicate.Track(sql.FieldLTE(FieldDeletedAt, v))
}

// DeletedAtIsNil applies the IsNil predicate on the "deleted_at" field.
func DeletedAtIsNil() predicate.Track {
	return predicate.Track(sql.FieldIsNull(FieldDeletedAt))
}

// DeletedAtNotNil applies the NotNil predicate on the "deleted_at" field.
func DeletedAtNotNil() predicate.Track {
	return predicate.Track(sql.FieldNotNull(FieldDeletedAt))
}

// StatusEQ applies the EQ predicate on the "status" field.
func StatusEQ(v int8) predicate.Track {
	return predicate.Track(sql.FieldEQ(FieldStatus, v))
}

// StatusNEQ applies the NEQ predicate on the "status" field.
func StatusNEQ(v int8) predicate.Track {
	return predicate.Track(sql.FieldNEQ(FieldStatus, v))
}

// StatusIn applies the In predicate on the "status" field.
func StatusIn(vs ...int8) predicate.Track {
	return predicate.Track(sql.FieldIn(FieldStatus, vs...))
}

// StatusNotIn applies the NotIn predicate on the "status" field.
func StatusNotIn(vs ...int8) predicate.Track {
	return predicate.Track(sql.FieldNotIn(FieldStatus, vs...))
}

// StatusGT applies the GT predicate on the "status" field.
func StatusGT(v int8) predicate.Track {
	return predicate.Track(sql.FieldGT(FieldStatus, v))
}

// StatusGTE applies the GTE predicate on the "status" field.
func StatusGTE(v int8) predicate.Track {
	return predicate.Track(sql.FieldGTE(FieldStatus, v))
}

// StatusLT applies the LT predicate on the "status" field.
func StatusLT(v int8) predicate.Track {
	return predicate.Track(sql.FieldLT(FieldStatus, v))
}

// StatusLTE applies the LTE predicate on the "status" field.
func StatusLTE(v int8) predicate.Track {
	return predicate.Track(sql.FieldLTE(FieldStatus, v))
}

// VersionEQ applies the EQ predicate on the "version" field.
func VersionEQ(v int) predicate.Track {
	return predicate.Track(sql.FieldEQ(FieldVersion, v))
}

// VersionNEQ applies the NEQ predicate on the "version" field.
func VersionNEQ(v int) predicate.Track {
	return predicate.Track(sql.FieldNEQ(FieldVersion, v))
}

// VersionIn applies the In predicate on the "version" field.
func VersionIn(vs ...int) predicate.Track {
	return predicate.Track(sql.FieldIn(FieldVersion, vs...))
}

// VersionNotIn applies the NotIn predicate on the "version" field.
func VersionNotIn(vs ...int) predicate.Track {
	return predicate.Track(sql.FieldNotIn(FieldVersion, vs...))
}

// VersionGT applies the GT predicate on the "version" field.
func VersionGT(v int) predicate.Track {
	return predicate.Track(sql.FieldGT(FieldVersion, v))
}

// VersionGTE applies the GTE predicate on the "version" field.
func VersionGTE(v int) predicate.Track {
	return predicate.Track(sql.FieldGTE(FieldVersion, v))
}

// VersionLT applies the LT predicate on the "version" field.
func VersionLT(v int) predicate.Track {
	return predicate.Track(sql.FieldLT(FieldVersion, v))
}

// VersionLTE applies the LTE predicate on the "version" field.
func VersionLTE(v int) predicate.Track {
	return predicate.Track(sql.FieldLTE(FieldVersion, v))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.Track {
	return predicate.Track(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.Track {
	return predicate.Track(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.Track {
	return predicate.Track(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.Track {
	return predicate.Track(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.Track {
	return predicate.Track(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.Track {
	return predicate.Track(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.Track {
	return predicate.Track(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.Track {
	return predicate.Track(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.Track {
	return predicate.Track(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.Track {
	return predicate.Track(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.Track {
	return predicate.Track(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.Track {
	return predicate.Track(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.Track {
	return predicate.Track(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.Track {
	return predicate.Track(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.Track {
	return predicate.Track(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.Track {
	return predicate.Track(sql.FieldLTE(FieldUpdatedAt, v))
}

// BeginEQ applies the EQ predicate on the "begin" field.
func BeginEQ(v time.Time) predicate.Track {
	return predicate.Track(sql.FieldEQ(FieldBegin, v))
}

// BeginNEQ applies the NEQ predicate on the "begin" field.
func BeginNEQ(v time.Time) predicate.Track {
	return predicate.Track(sql.FieldNEQ(FieldBegin, v))
}

// BeginIn applies the In predicate on the "begin" field.
func BeginIn(vs ...time.Time) predicate.Track {
	return predicate.Track(sql.FieldIn(FieldBegin, vs...))
}

// BeginNotIn applies the NotIn predicate on the "begin" field.
func BeginNotIn(vs ...time.Time) predicate.Track {
	return predicate.Track(sql.FieldNotIn(FieldBegin, vs...))
}

// BeginGT applies the GT predicate on the "begin" field.
func BeginGT(v time.Time) predicate.Track {
	return predicate.Track(sql.FieldGT(FieldBegin, v))
}

// BeginGTE applies the GTE predicate on the "begin" field.
func BeginGTE(v time.Time) predicate.Track {
	return predicate.Track(sql.FieldGTE(FieldBegin, v))
}

// BeginLT applies the LT predicate on the "begin" field.
func BeginLT(v time.Time) predicate.Track {
	return predicate.Track(sql.FieldLT(FieldBegin, v))
}

// BeginLTE applies the LTE predicate on the "begin" field.
func BeginLTE(v time.Time) predicate.Track {
	return predicate.Track(sql.FieldLTE(FieldBegin, v))
}

// BeginIsNil applies the IsNil predicate on the "begin" field.
func BeginIsNil() predicate.Track {
	return predicate.Track(sql.FieldIsNull(FieldBegin))
}

// BeginNotNil applies the NotNil predicate on the "begin" field.
func BeginNotNil() predicate.Track {
	return predicate.Track(sql.FieldNotNull(FieldBegin))
}

// EndEQ applies the EQ predicate on the "end" field.
func EndEQ(v time.Time) predicate.Track {
	return predicate.Track(sql.FieldEQ(FieldEnd, v))
}

// EndNEQ applies the NEQ predicate on the "end" field.
func EndNEQ(v time.Time) predicate.Track {
	return predicate.Track(sql.FieldNEQ(FieldEnd, v))
}

// EndIn applies the In predicate on the "end" field.
func EndIn(vs ...time.Time) predicate.Track {
	return predicate.Track(sql.FieldIn(FieldEnd, vs...))
}

// EndNotIn applies the NotIn predicate on the "end" field.
func EndNotIn(vs ...time.Time) predicate.Track {
	return predicate.Track(sql.FieldNotIn(FieldEnd, vs...))
}

// EndGT applies the GT predicate on the "end" field.
func EndGT(v time.Time) predicate.Track {
	return predicate.Track(sql.FieldGT(FieldEnd, v))
}

// EndGTE applies the GTE predicate on the "end" field.
func EndGTE(v time.Time) predicate.Track {
	return predicate.Track(sql.FieldGTE(FieldEnd, v))
}

// EndLT applies the LT predicate on the "end" field.
func EndLT(v time.Time) predicate.Track {
	return predicate.Track(sql.FieldLT(FieldEnd, v))
}

// EndLTE applies the LTE predicate on the "end" field.
func EndLTE(v time.Time) predicate.Track {
	return predicate.Track(sql.FieldLTE(FieldEnd, v))
}

// EndIsNil applies the IsNil predicate on the "end" field.
func EndIsNil() predicate.Track {
	return predicate.Track(sql.FieldIsNull(FieldEnd))
}

// EndNotNil applies the NotNil predicate on the "end" field.
func EndNotNil() predicate.Track {
	return predicate.Track(sql.FieldNotNull(FieldEnd))
}

// DealIDEQ applies the EQ predicate on the "deal_id" field.
func DealIDEQ(v int) predicate.Track {
	return predicate.Track(sql.FieldEQ(FieldDealID, v))
}

// DealIDNEQ applies the NEQ predicate on the "deal_id" field.
func DealIDNEQ(v int) predicate.Track {
	return predicate.Track(sql.FieldNEQ(FieldDealID, v))
}

// DealIDIn applies the In predicate on the "deal_id" field.
func DealIDIn(vs ...int) predicate.Track {
	return predicate.Track(sql.FieldIn(FieldDealID, vs...))
}

// DealIDNotIn applies the NotIn predicate on the "deal_id" field.
func DealIDNotIn(vs ...int) predicate.Track {
	return predicate.Track(sql.FieldNotIn(FieldDealID, vs...))
}

// DealIDIsNil applies the IsNil predicate on the "deal_id" field.
func DealIDIsNil() predicate.Track {
	return predicate.Track(sql.FieldIsNull(FieldDealID))
}

// DealIDNotNil applies the NotNil predicate on the "deal_id" field.
func DealIDNotNil() predicate.Track {
	return predicate.Track(sql.FieldNotNull(FieldDealID))
}

// PipelineIDEQ applies the EQ predicate on the "pipeline_id" field.
func PipelineIDEQ(v int) predicate.Track {
	return predicate.Track(sql.FieldEQ(FieldPipelineID, v))
}

// PipelineIDNEQ applies the NEQ predicate on the "pipeline_id" field.
func PipelineIDNEQ(v int) predicate.Track {
	return predicate.Track(sql.FieldNEQ(FieldPipelineID, v))
}

// PipelineIDIn applies the In predicate on the "pipeline_id" field.
func PipelineIDIn(vs ...int) predicate.Track {
	return predicate.Track(sql.FieldIn(FieldPipelineID, vs...))
}

// PipelineIDNotIn applies the NotIn predicate on the "pipeline_id" field.
func PipelineIDNotIn(vs ...int) predicate.Track {
	return predicate.Track(sql.FieldNotIn(FieldPipelineID, vs...))
}

// PipelineIDIsNil applies the IsNil predicate on the "pipeline_id" field.
func PipelineIDIsNil() predicate.Track {
	return predicate.Track(sql.FieldIsNull(FieldPipelineID))
}

// PipelineIDNotNil applies the NotNil predicate on the "pipeline_id" field.
func PipelineIDNotNil() predicate.Track {
	return predicate.Track(sql.FieldNotNull(FieldPipelineID))
}

// StageIDEQ applies the EQ predicate on the "stage_id" field.
func StageIDEQ(v int) predicate.Track {
	return predicate.Track(sql.FieldEQ(FieldStageID, v))
}

// StageIDNEQ applies the NEQ predicate on the "stage_id" field.
func StageIDNEQ(v int) predicate.Track {
	return predicate.Track(sql.FieldNEQ(FieldStageID, v))
}

// StageIDIn applies the In predicate on the "stage_id" field.
func StageIDIn(vs ...int) predicate.Track {
	return predicate.Track(sql.FieldIn(FieldStageID, vs...))
}

// StageIDNotIn applies the NotIn predicate on the "stage_id" field.
func StageIDNotIn(vs ...int) predicate.Track {
	return predicate.Track(sql.FieldNotIn(FieldStageID, vs...))
}

// StageIDIsNil applies the IsNil predicate on the "stage_id" field.
func StageIDIsNil() predicate.Track {
	return predicate.Track(sql.FieldIsNull(FieldStageID))
}

// StageIDNotNil applies the NotNil predicate on the "stage_id" field.
func StageIDNotNil() predicate.Track {
	return predicate.Track(sql.FieldNotNull(FieldStageID))
}

// UserIDEQ applies the EQ predicate on the "user_id" field.
func UserIDEQ(v int) predicate.Track {
	return predicate.Track(sql.FieldEQ(FieldUserID, v))
}

// UserIDNEQ applies the NEQ predicate on the "user_id" field.
func UserIDNEQ(v int) predicate.Track {
	return predicate.Track(sql.FieldNEQ(FieldUserID, v))
}

// UserIDIn applies the In predicate on the "user_id" field.
func UserIDIn(vs ...int) predicate.Track {
	return predicate.Track(sql.FieldIn(FieldUserID, vs...))
}

// UserIDNotIn applies the NotIn predicate on the "user_id" field.
func UserIDNotIn(vs ...int) predicate.Track {
	return predicate.Track(sql.FieldNotIn(FieldUserID, vs...))
}

// UserIDIsNil applies the IsNil predicate on the "user_id" field.
func UserIDIsNil() predicate.Track {
	return predicate.Track(sql.FieldIsNull(FieldUserID))
}

// UserIDNotNil applies the NotNil predicate on the "user_id" field.
func UserIDNotNil() predicate.Track {
	return predicate.Track(sql.FieldNotNull(FieldUserID))
}

// PersonIDEQ applies the EQ predicate on the "person_id" field.
func PersonIDEQ(v int) predicate.Track {
	return predicate.Track(sql.FieldEQ(FieldPersonID, v))
}

// PersonIDNEQ applies the NEQ predicate on the "person_id" field.
func PersonIDNEQ(v int) predicate.Track {
	return predicate.Track(sql.FieldNEQ(FieldPersonID, v))
}

// PersonIDIn applies the In predicate on the "person_id" field.
func PersonIDIn(vs ...int) predicate.Track {
	return predicate.Track(sql.FieldIn(FieldPersonID, vs...))
}

// PersonIDNotIn applies the NotIn predicate on the "person_id" field.
func PersonIDNotIn(vs ...int) predicate.Track {
	return predicate.Track(sql.FieldNotIn(FieldPersonID, vs...))
}

// PersonIDIsNil applies the IsNil predicate on the "person_id" field.
func PersonIDIsNil() predicate.Track {
	return predicate.Track(sql.FieldIsNull(FieldPersonID))
}

// PersonIDNotNil applies the NotNil predicate on the "person_id" field.
func PersonIDNotNil() predicate.Track {
	return predicate.Track(sql.FieldNotNull(FieldPersonID))
}

// WeightEQ applies the EQ predicate on the "weight" field.
func WeightEQ(v float64) predicate.Track {
	return predicate.Track(sql.FieldEQ(FieldWeight, v))
}

// WeightNEQ applies the NEQ predicate on the "weight" field.
func WeightNEQ(v float64) predicate.Track {
	return predicate.Track(sql.FieldNEQ(FieldWeight, v))
}

// WeightIn applies the In predicate on the "weight" field.
func WeightIn(vs ...float64) predicate.Track {
	return predicate.Track(sql.FieldIn(FieldWeight, vs...))
}

// WeightNotIn applies the NotIn predicate on the "weight" field.
func WeightNotIn(vs ...float64) predicate.Track {
	return predicate.Track(sql.FieldNotIn(FieldWeight, vs...))
}

// WeightGT applies the GT predicate on the "weight" field.
func WeightGT(v float64) predicate.Track {
	return predicate.Track(sql.FieldGT(FieldWeight, v))
}

// WeightGTE applies the GTE predicate on the "weight" field.
func WeightGTE(v float64) predicate.Track {
	return predicate.Track(sql.FieldGTE(FieldWeight, v))
}

// WeightLT applies the LT predicate on the "weight" field.
func WeightLT(v float64) predicate.Track {
	return predicate.Track(sql.FieldLT(FieldWeight, v))
}

// WeightLTE applies the LTE predicate on the "weight" field.
func WeightLTE(v float64) predicate.Track {
	return predicate.Track(sql.FieldLTE(FieldWeight, v))
}

// WeightIsNil applies the IsNil predicate on the "weight" field.
func WeightIsNil() predicate.Track {
	return predicate.Track(sql.FieldIsNull(FieldWeight))
}

// WeightNotNil applies the NotNil predicate on the "weight" field.
func WeightNotNil() predicate.Track {
	return predicate.Track(sql.FieldNotNull(FieldWeight))
}

// MetaIsNil applies the IsNil predicate on the "meta" field.
func MetaIsNil() predicate.Track {
	return predicate.Track(sql.FieldIsNull(FieldMeta))
}

// MetaNotNil applies the NotNil predicate on the "meta" field.
func MetaNotNil() predicate.Track {
	return predicate.Track(sql.FieldNotNull(FieldMeta))
}

// StageHistoryIsNil applies the IsNil predicate on the "stage_history" field.
func StageHistoryIsNil() predicate.Track {
	return predicate.Track(sql.FieldIsNull(FieldStageHistory))
}

// StageHistoryNotNil applies the NotNil predicate on the "stage_history" field.
func StageHistoryNotNil() predicate.Track {
	return predicate.Track(sql.FieldNotNull(FieldStageHistory))
}

// HasDeal applies the HasEdge predicate on the "deal" edge.
func HasDeal() predicate.Track {
	return predicate.Track(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, DealTable, DealColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasDealWith applies the HasEdge predicate on the "deal" edge with a given conditions (other predicates).
func HasDealWith(preds ...predicate.Deal) predicate.Track {
	return predicate.Track(func(s *sql.Selector) {
		step := newDealStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasUser applies the HasEdge predicate on the "user" edge.
func HasUser() predicate.Track {
	return predicate.Track(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, UserTable, UserColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasUserWith applies the HasEdge predicate on the "user" edge with a given conditions (other predicates).
func HasUserWith(preds ...predicate.User) predicate.Track {
	return predicate.Track(func(s *sql.Selector) {
		step := newUserStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasPerson applies the HasEdge predicate on the "person" edge.
func HasPerson() predicate.Track {
	return predicate.Track(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, PersonTable, PersonColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasPersonWith applies the HasEdge predicate on the "person" edge with a given conditions (other predicates).
func HasPersonWith(preds ...predicate.Person) predicate.Track {
	return predicate.Track(func(s *sql.Selector) {
		step := newPersonStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasPipeline applies the HasEdge predicate on the "pipeline" edge.
func HasPipeline() predicate.Track {
	return predicate.Track(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, PipelineTable, PipelineColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasPipelineWith applies the HasEdge predicate on the "pipeline" edge with a given conditions (other predicates).
func HasPipelineWith(preds ...predicate.Pipeline) predicate.Track {
	return predicate.Track(func(s *sql.Selector) {
		step := newPipelineStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasStage applies the HasEdge predicate on the "stage" edge.
func HasStage() predicate.Track {
	return predicate.Track(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, StageTable, StageColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasStageWith applies the HasEdge predicate on the "stage" edge with a given conditions (other predicates).
func HasStageWith(preds ...predicate.Stage) predicate.Track {
	return predicate.Track(func(s *sql.Selector) {
		step := newStageStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasAppointments applies the HasEdge predicate on the "appointments" edge.
func HasAppointments() predicate.Track {
	return predicate.Track(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, AppointmentsTable, AppointmentsColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasAppointmentsWith applies the HasEdge predicate on the "appointments" edge with a given conditions (other predicates).
func HasAppointmentsWith(preds ...predicate.Appointment) predicate.Track {
	return predicate.Track(func(s *sql.Selector) {
		step := newAppointmentsStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasFileUsages applies the HasEdge predicate on the "file_usages" edge.
func HasFileUsages() predicate.Track {
	return predicate.Track(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, FileUsagesTable, FileUsagesColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasFileUsagesWith applies the HasEdge predicate on the "file_usages" edge with a given conditions (other predicates).
func HasFileUsagesWith(preds ...predicate.FileUsage) predicate.Track {
	return predicate.Track(func(s *sql.Selector) {
		step := newFileUsagesStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasAttachments applies the HasEdge predicate on the "attachments" edge.
func HasAttachments() predicate.Track {
	return predicate.Track(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, AttachmentsTable, AttachmentsColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasAttachmentsWith applies the HasEdge predicate on the "attachments" edge with a given conditions (other predicates).
func HasAttachmentsWith(preds ...predicate.Attachment) predicate.Track {
	return predicate.Track(func(s *sql.Selector) {
		step := newAttachmentsStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasBillItems applies the HasEdge predicate on the "bill_items" edge.
func HasBillItems() predicate.Track {
	return predicate.Track(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, BillItemsTable, BillItemsColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasBillItemsWith applies the HasEdge predicate on the "bill_items" edge with a given conditions (other predicates).
func HasBillItemsWith(preds ...predicate.BillItem) predicate.Track {
	return predicate.Track(func(s *sql.Selector) {
		step := newBillItemsStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.Track) predicate.Track {
	return predicate.Track(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.Track) predicate.Track {
	return predicate.Track(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.Track) predicate.Track {
	return predicate.Track(sql.NotPredicates(p))
}
