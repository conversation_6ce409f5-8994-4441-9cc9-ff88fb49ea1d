// Code generated by ent, DO NOT EDIT.

package track

import (
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

const (
	// Label holds the string label denoting the track type in the database.
	Label = "track"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldDeletedAt holds the string denoting the deleted_at field in the database.
	FieldDeletedAt = "deleted_at"
	// FieldStatus holds the string denoting the status field in the database.
	FieldStatus = "status"
	// FieldVersion holds the string denoting the version field in the database.
	FieldVersion = "version"
	// FieldCreatedAt holds the string denoting the created_at field in the database.
	FieldCreatedAt = "created_at"
	// FieldUpdatedAt holds the string denoting the updated_at field in the database.
	FieldUpdatedAt = "updated_at"
	// FieldBegin holds the string denoting the begin field in the database.
	FieldBegin = "begin"
	// FieldEnd holds the string denoting the end field in the database.
	FieldEnd = "end"
	// FieldDealID holds the string denoting the deal_id field in the database.
	FieldDealID = "deal_id"
	// FieldPipelineID holds the string denoting the pipeline_id field in the database.
	FieldPipelineID = "pipeline_id"
	// FieldStageID holds the string denoting the stage_id field in the database.
	FieldStageID = "stage_id"
	// FieldUserID holds the string denoting the user_id field in the database.
	FieldUserID = "user_id"
	// FieldPersonID holds the string denoting the person_id field in the database.
	FieldPersonID = "person_id"
	// FieldWeight holds the string denoting the weight field in the database.
	FieldWeight = "weight"
	// FieldMeta holds the string denoting the meta field in the database.
	FieldMeta = "meta"
	// FieldStageHistory holds the string denoting the stage_history field in the database.
	FieldStageHistory = "stage_history"
	// EdgeDeal holds the string denoting the deal edge name in mutations.
	EdgeDeal = "deal"
	// EdgeUser holds the string denoting the user edge name in mutations.
	EdgeUser = "user"
	// EdgePerson holds the string denoting the person edge name in mutations.
	EdgePerson = "person"
	// EdgePipeline holds the string denoting the pipeline edge name in mutations.
	EdgePipeline = "pipeline"
	// EdgeStage holds the string denoting the stage edge name in mutations.
	EdgeStage = "stage"
	// EdgeAppointments holds the string denoting the appointments edge name in mutations.
	EdgeAppointments = "appointments"
	// EdgeFileUsages holds the string denoting the file_usages edge name in mutations.
	EdgeFileUsages = "file_usages"
	// EdgeAttachments holds the string denoting the attachments edge name in mutations.
	EdgeAttachments = "attachments"
	// EdgeBillItems holds the string denoting the bill_items edge name in mutations.
	EdgeBillItems = "bill_items"
	// Table holds the table name of the track in the database.
	Table = "track"
	// DealTable is the table that holds the deal relation/edge.
	DealTable = "track"
	// DealInverseTable is the table name for the Deal entity.
	// It exists in this package in order to avoid circular dependency with the "deal" package.
	DealInverseTable = "deal"
	// DealColumn is the table column denoting the deal relation/edge.
	DealColumn = "deal_id"
	// UserTable is the table that holds the user relation/edge.
	UserTable = "track"
	// UserInverseTable is the table name for the User entity.
	// It exists in this package in order to avoid circular dependency with the "user" package.
	UserInverseTable = "user"
	// UserColumn is the table column denoting the user relation/edge.
	UserColumn = "user_id"
	// PersonTable is the table that holds the person relation/edge.
	PersonTable = "track"
	// PersonInverseTable is the table name for the Person entity.
	// It exists in this package in order to avoid circular dependency with the "person" package.
	PersonInverseTable = "person"
	// PersonColumn is the table column denoting the person relation/edge.
	PersonColumn = "person_id"
	// PipelineTable is the table that holds the pipeline relation/edge.
	PipelineTable = "track"
	// PipelineInverseTable is the table name for the Pipeline entity.
	// It exists in this package in order to avoid circular dependency with the "pipeline" package.
	PipelineInverseTable = "pipeline"
	// PipelineColumn is the table column denoting the pipeline relation/edge.
	PipelineColumn = "pipeline_id"
	// StageTable is the table that holds the stage relation/edge.
	StageTable = "track"
	// StageInverseTable is the table name for the Stage entity.
	// It exists in this package in order to avoid circular dependency with the "stage" package.
	StageInverseTable = "stage"
	// StageColumn is the table column denoting the stage relation/edge.
	StageColumn = "stage_id"
	// AppointmentsTable is the table that holds the appointments relation/edge.
	AppointmentsTable = "appointment"
	// AppointmentsInverseTable is the table name for the Appointment entity.
	// It exists in this package in order to avoid circular dependency with the "appointment" package.
	AppointmentsInverseTable = "appointment"
	// AppointmentsColumn is the table column denoting the appointments relation/edge.
	AppointmentsColumn = "track_id"
	// FileUsagesTable is the table that holds the file_usages relation/edge.
	FileUsagesTable = "file_usage"
	// FileUsagesInverseTable is the table name for the FileUsage entity.
	// It exists in this package in order to avoid circular dependency with the "fileusage" package.
	FileUsagesInverseTable = "file_usage"
	// FileUsagesColumn is the table column denoting the file_usages relation/edge.
	FileUsagesColumn = "track_id"
	// AttachmentsTable is the table that holds the attachments relation/edge.
	AttachmentsTable = "attachment"
	// AttachmentsInverseTable is the table name for the Attachment entity.
	// It exists in this package in order to avoid circular dependency with the "attachment" package.
	AttachmentsInverseTable = "attachment"
	// AttachmentsColumn is the table column denoting the attachments relation/edge.
	AttachmentsColumn = "track_id"
	// BillItemsTable is the table that holds the bill_items relation/edge.
	BillItemsTable = "bill_item"
	// BillItemsInverseTable is the table name for the BillItem entity.
	// It exists in this package in order to avoid circular dependency with the "billitem" package.
	BillItemsInverseTable = "bill_item"
	// BillItemsColumn is the table column denoting the bill_items relation/edge.
	BillItemsColumn = "track_id"
)

// Columns holds all SQL columns for track fields.
var Columns = []string{
	FieldID,
	FieldDeletedAt,
	FieldStatus,
	FieldVersion,
	FieldCreatedAt,
	FieldUpdatedAt,
	FieldBegin,
	FieldEnd,
	FieldDealID,
	FieldPipelineID,
	FieldStageID,
	FieldUserID,
	FieldPersonID,
	FieldWeight,
	FieldMeta,
	FieldStageHistory,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

// Note that the variables below are initialized by the runtime
// package on the initialization of the application. Therefore,
// it should be imported in the main as follows:
//
//	import _ "bcare/ent/runtime"
var (
	Hooks        [3]ent.Hook
	Interceptors [1]ent.Interceptor
	// DefaultStatus holds the default value on creation for the "status" field.
	DefaultStatus int8
	// DefaultVersion holds the default value on creation for the "version" field.
	DefaultVersion int
	// DefaultCreatedAt holds the default value on creation for the "created_at" field.
	DefaultCreatedAt func() time.Time
	// DefaultUpdatedAt holds the default value on creation for the "updated_at" field.
	DefaultUpdatedAt func() time.Time
	// UpdateDefaultUpdatedAt holds the default value on update for the "updated_at" field.
	UpdateDefaultUpdatedAt func() time.Time
)

// OrderOption defines the ordering options for the Track queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByDeletedAt orders the results by the deleted_at field.
func ByDeletedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDeletedAt, opts...).ToFunc()
}

// ByStatus orders the results by the status field.
func ByStatus(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldStatus, opts...).ToFunc()
}

// ByVersion orders the results by the version field.
func ByVersion(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldVersion, opts...).ToFunc()
}

// ByCreatedAt orders the results by the created_at field.
func ByCreatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedAt, opts...).ToFunc()
}

// ByUpdatedAt orders the results by the updated_at field.
func ByUpdatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdatedAt, opts...).ToFunc()
}

// ByBegin orders the results by the begin field.
func ByBegin(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldBegin, opts...).ToFunc()
}

// ByEnd orders the results by the end field.
func ByEnd(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldEnd, opts...).ToFunc()
}

// ByDealID orders the results by the deal_id field.
func ByDealID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDealID, opts...).ToFunc()
}

// ByPipelineID orders the results by the pipeline_id field.
func ByPipelineID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPipelineID, opts...).ToFunc()
}

// ByStageID orders the results by the stage_id field.
func ByStageID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldStageID, opts...).ToFunc()
}

// ByUserID orders the results by the user_id field.
func ByUserID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUserID, opts...).ToFunc()
}

// ByPersonID orders the results by the person_id field.
func ByPersonID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPersonID, opts...).ToFunc()
}

// ByWeight orders the results by the weight field.
func ByWeight(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldWeight, opts...).ToFunc()
}

// ByDealField orders the results by deal field.
func ByDealField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newDealStep(), sql.OrderByField(field, opts...))
	}
}

// ByUserField orders the results by user field.
func ByUserField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newUserStep(), sql.OrderByField(field, opts...))
	}
}

// ByPersonField orders the results by person field.
func ByPersonField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newPersonStep(), sql.OrderByField(field, opts...))
	}
}

// ByPipelineField orders the results by pipeline field.
func ByPipelineField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newPipelineStep(), sql.OrderByField(field, opts...))
	}
}

// ByStageField orders the results by stage field.
func ByStageField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newStageStep(), sql.OrderByField(field, opts...))
	}
}

// ByAppointmentsCount orders the results by appointments count.
func ByAppointmentsCount(opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborsCount(s, newAppointmentsStep(), opts...)
	}
}

// ByAppointments orders the results by appointments terms.
func ByAppointments(term sql.OrderTerm, terms ...sql.OrderTerm) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newAppointmentsStep(), append([]sql.OrderTerm{term}, terms...)...)
	}
}

// ByFileUsagesCount orders the results by file_usages count.
func ByFileUsagesCount(opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborsCount(s, newFileUsagesStep(), opts...)
	}
}

// ByFileUsages orders the results by file_usages terms.
func ByFileUsages(term sql.OrderTerm, terms ...sql.OrderTerm) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newFileUsagesStep(), append([]sql.OrderTerm{term}, terms...)...)
	}
}

// ByAttachmentsCount orders the results by attachments count.
func ByAttachmentsCount(opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborsCount(s, newAttachmentsStep(), opts...)
	}
}

// ByAttachments orders the results by attachments terms.
func ByAttachments(term sql.OrderTerm, terms ...sql.OrderTerm) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newAttachmentsStep(), append([]sql.OrderTerm{term}, terms...)...)
	}
}

// ByBillItemsCount orders the results by bill_items count.
func ByBillItemsCount(opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborsCount(s, newBillItemsStep(), opts...)
	}
}

// ByBillItems orders the results by bill_items terms.
func ByBillItems(term sql.OrderTerm, terms ...sql.OrderTerm) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newBillItemsStep(), append([]sql.OrderTerm{term}, terms...)...)
	}
}
func newDealStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(DealInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2O, false, DealTable, DealColumn),
	)
}
func newUserStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(UserInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2O, false, UserTable, UserColumn),
	)
}
func newPersonStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(PersonInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2O, false, PersonTable, PersonColumn),
	)
}
func newPipelineStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(PipelineInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2O, false, PipelineTable, PipelineColumn),
	)
}
func newStageStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(StageInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2O, false, StageTable, StageColumn),
	)
}
func newAppointmentsStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(AppointmentsInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.O2M, true, AppointmentsTable, AppointmentsColumn),
	)
}
func newFileUsagesStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(FileUsagesInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.O2M, true, FileUsagesTable, FileUsagesColumn),
	)
}
func newAttachmentsStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(AttachmentsInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.O2M, true, AttachmentsTable, AttachmentsColumn),
	)
}
func newBillItemsStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(BillItemsInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.O2M, true, BillItemsTable, BillItemsColumn),
	)
}
