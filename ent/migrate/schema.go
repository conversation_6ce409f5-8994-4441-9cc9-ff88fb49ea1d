// Code generated by ent, DO NOT EDIT.

package migrate

import (
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/dialect/sql/schema"
	"entgo.io/ent/schema/field"
)

var (
	// AppointmentColumns holds the columns for the "appointment" table.
	AppointmentColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "deleted_at", Type: field.TypeTime, Nullable: true},
		{Name: "status", Type: field.TypeInt8, Default: 2},
		{Name: "version", Type: field.TypeInt, Default: 1},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "title", Type: field.TypeString, Nullable: true},
		{Name: "start_time", Type: field.TypeTime, Nullable: true},
		{Name: "end_time", Type: field.TypeTime, Nullable: true},
		{Name: "arrived_at", Type: field.TypeTime, Nullable: true},
		{Name: "notes", Type: field.TypeString, Nullable: true, Size: 2147483647},
		{Name: "type", Type: field.TypeInt8, Nullable: true},
		{Name: "reminder_status", Type: field.TypeInt8, Nullable: true, Default: -1},
		{Name: "extra_notes", Type: field.TypeString, Nullable: true},
		{Name: "history", Type: field.TypeJSON, Nullable: true},
		{Name: "creator_id", Type: field.TypeInt, Nullable: true},
		{Name: "person_id", Type: field.TypeInt, Nullable: true},
		{Name: "doctor_id", Type: field.TypeInt, Nullable: true},
		{Name: "track_id", Type: field.TypeInt, Nullable: true},
	}
	// AppointmentTable holds the schema information for the "appointment" table.
	AppointmentTable = &schema.Table{
		Name:       "appointment",
		Columns:    AppointmentColumns,
		PrimaryKey: []*schema.Column{AppointmentColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "appointment_user_creator",
				Columns:    []*schema.Column{AppointmentColumns[15]},
				RefColumns: []*schema.Column{UserColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "appointment_person_person",
				Columns:    []*schema.Column{AppointmentColumns[16]},
				RefColumns: []*schema.Column{PersonColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "appointment_user_doctor",
				Columns:    []*schema.Column{AppointmentColumns[17]},
				RefColumns: []*schema.Column{UserColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "appointment_track_track",
				Columns:    []*schema.Column{AppointmentColumns[18]},
				RefColumns: []*schema.Column{TrackColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
		Indexes: []*schema.Index{
			{
				Name:    "appointment_notes_start_time",
				Unique:  false,
				Columns: []*schema.Column{AppointmentColumns[10], AppointmentColumns[7]},
			},
			{
				Name:    "appointment_start_time",
				Unique:  false,
				Columns: []*schema.Column{AppointmentColumns[7]},
			},
			{
				Name:    "appointment_person_id",
				Unique:  false,
				Columns: []*schema.Column{AppointmentColumns[16]},
			},
			{
				Name:    "appointment_track_id",
				Unique:  false,
				Columns: []*schema.Column{AppointmentColumns[18]},
			},
			{
				Name:    "appointment_deleted_at_status",
				Unique:  false,
				Columns: []*schema.Column{AppointmentColumns[1], AppointmentColumns[2]},
			},
		},
	}
	// AttachmentColumns holds the columns for the "attachment" table.
	AttachmentColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "deleted_at", Type: field.TypeTime, Nullable: true},
		{Name: "status", Type: field.TypeInt8, Default: 2},
		{Name: "version", Type: field.TypeInt, Default: 1},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "brand_id", Type: field.TypeInt, Default: 0},
		{Name: "quantity", Type: field.TypeInt, Default: 0},
		{Name: "price", Type: field.TypeFloat64, Default: 0},
		{Name: "discount", Type: field.TypeFloat64, Default: 0},
		{Name: "note", Type: field.TypeString, Nullable: true},
		{Name: "title", Type: field.TypeString, Nullable: true},
		{Name: "kind", Type: field.TypeEnum, Enums: []string{"product", "treatment", "operation", "proposal"}, Default: "product"},
		{Name: "parent_id", Type: field.TypeInt, Nullable: true},
		{Name: "deal_id", Type: field.TypeInt, Nullable: true},
		{Name: "person_id", Type: field.TypeInt, Nullable: true},
		{Name: "plan_id", Type: field.TypeInt, Nullable: true},
		{Name: "user_id", Type: field.TypeInt, Nullable: true},
		{Name: "product_id", Type: field.TypeInt, Nullable: true},
		{Name: "track_id", Type: field.TypeInt, Nullable: true},
	}
	// AttachmentTable holds the schema information for the "attachment" table.
	AttachmentTable = &schema.Table{
		Name:       "attachment",
		Columns:    AttachmentColumns,
		PrimaryKey: []*schema.Column{AttachmentColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "attachment_attachment_children",
				Columns:    []*schema.Column{AttachmentColumns[13]},
				RefColumns: []*schema.Column{AttachmentColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "attachment_deal_deal",
				Columns:    []*schema.Column{AttachmentColumns[14]},
				RefColumns: []*schema.Column{DealColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "attachment_person_person",
				Columns:    []*schema.Column{AttachmentColumns[15]},
				RefColumns: []*schema.Column{PersonColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "attachment_installment_plan_plan",
				Columns:    []*schema.Column{AttachmentColumns[16]},
				RefColumns: []*schema.Column{InstallmentPlanColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "attachment_user_creator",
				Columns:    []*schema.Column{AttachmentColumns[17]},
				RefColumns: []*schema.Column{UserColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "attachment_product_product",
				Columns:    []*schema.Column{AttachmentColumns[18]},
				RefColumns: []*schema.Column{ProductColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "attachment_track_track",
				Columns:    []*schema.Column{AttachmentColumns[19]},
				RefColumns: []*schema.Column{TrackColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
		Indexes: []*schema.Index{
			{
				Name:    "attachment_kind",
				Unique:  false,
				Columns: []*schema.Column{AttachmentColumns[12]},
			},
			{
				Name:    "attachment_person_id",
				Unique:  false,
				Columns: []*schema.Column{AttachmentColumns[15]},
			},
			{
				Name:    "attachment_deal_id",
				Unique:  false,
				Columns: []*schema.Column{AttachmentColumns[14]},
			},
			{
				Name:    "attachment_plan_id",
				Unique:  false,
				Columns: []*schema.Column{AttachmentColumns[16]},
			},
			{
				Name:    "attachment_user_id",
				Unique:  false,
				Columns: []*schema.Column{AttachmentColumns[17]},
			},
			{
				Name:    "attachment_status",
				Unique:  false,
				Columns: []*schema.Column{AttachmentColumns[2]},
			},
			{
				Name:    "attachment_deleted_at",
				Unique:  false,
				Columns: []*schema.Column{AttachmentColumns[1]},
			},
			{
				Name:    "attachment_created_at",
				Unique:  false,
				Columns: []*schema.Column{AttachmentColumns[4]},
			},
			{
				Name:    "attachment_parent_id",
				Unique:  false,
				Columns: []*schema.Column{AttachmentColumns[13]},
			},
		},
	}
	// AttachmentDataColumns holds the columns for the "attachment_data" table.
	AttachmentDataColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "kind", Type: field.TypeString},
		{Name: "data", Type: field.TypeJSON, Nullable: true},
		{Name: "user_id", Type: field.TypeInt},
		{Name: "participant_id", Type: field.TypeInt, Nullable: true},
		{Name: "attachment_id", Type: field.TypeInt},
	}
	// AttachmentDataTable holds the schema information for the "attachment_data" table.
	AttachmentDataTable = &schema.Table{
		Name:       "attachment_data",
		Columns:    AttachmentDataColumns,
		PrimaryKey: []*schema.Column{AttachmentDataColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "attachment_data_user_creator",
				Columns:    []*schema.Column{AttachmentDataColumns[5]},
				RefColumns: []*schema.Column{UserColumns[0]},
				OnDelete:   schema.NoAction,
			},
			{
				Symbol:     "attachment_data_user_participant",
				Columns:    []*schema.Column{AttachmentDataColumns[6]},
				RefColumns: []*schema.Column{UserColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "attachment_data_attachment_attachment",
				Columns:    []*schema.Column{AttachmentDataColumns[7]},
				RefColumns: []*schema.Column{AttachmentColumns[0]},
				OnDelete:   schema.NoAction,
			},
		},
		Indexes: []*schema.Index{
			{
				Name:    "attachmentdata_attachment_id_participant_id_kind",
				Unique:  true,
				Columns: []*schema.Column{AttachmentDataColumns[7], AttachmentDataColumns[6], AttachmentDataColumns[3]},
			},
		},
	}
	// BillColumns holds the columns for the "bill" table.
	BillColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "deleted_at", Type: field.TypeTime, Nullable: true},
		{Name: "status", Type: field.TypeInt8, Default: 2},
		{Name: "version", Type: field.TypeInt, Default: 1},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "brand_id", Type: field.TypeInt, Default: 0},
		{Name: "debt_remaining", Type: field.TypeFloat64, Default: 0},
		{Name: "debt_payment", Type: field.TypeFloat64, Default: 0},
		{Name: "discount", Type: field.TypeFloat64, Default: 0},
		{Name: "refund", Type: field.TypeFloat64, Default: 0},
		{Name: "state", Type: field.TypeEnum, Enums: []string{"draft", "active", "paid", "partially_paid", "cancelled", "refunded"}, Default: "draft"},
		{Name: "deal_id", Type: field.TypeInt, Nullable: true},
		{Name: "person_id", Type: field.TypeInt, Nullable: true},
		{Name: "user_id", Type: field.TypeInt, Nullable: true},
		{Name: "track_id", Type: field.TypeInt, Nullable: true},
	}
	// BillTable holds the schema information for the "bill" table.
	BillTable = &schema.Table{
		Name:       "bill",
		Columns:    BillColumns,
		PrimaryKey: []*schema.Column{BillColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "bill_deal_deal",
				Columns:    []*schema.Column{BillColumns[12]},
				RefColumns: []*schema.Column{DealColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "bill_person_person",
				Columns:    []*schema.Column{BillColumns[13]},
				RefColumns: []*schema.Column{PersonColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "bill_user_user",
				Columns:    []*schema.Column{BillColumns[14]},
				RefColumns: []*schema.Column{UserColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "bill_track_track",
				Columns:    []*schema.Column{BillColumns[15]},
				RefColumns: []*schema.Column{TrackColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
		Indexes: []*schema.Index{
			{
				Name:    "bill_person_id",
				Unique:  false,
				Columns: []*schema.Column{BillColumns[13]},
			},
			{
				Name:    "bill_deal_id",
				Unique:  false,
				Columns: []*schema.Column{BillColumns[12]},
			},
			{
				Name:    "bill_created_at",
				Unique:  false,
				Columns: []*schema.Column{BillColumns[4]},
			},
			{
				Name:    "bill_deleted_at",
				Unique:  false,
				Columns: []*schema.Column{BillColumns[1]},
			},
		},
	}
	// BillDataColumns holds the columns for the "bill_data" table.
	BillDataColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "kind", Type: field.TypeString},
		{Name: "data", Type: field.TypeJSON, Nullable: true},
		{Name: "user_id", Type: field.TypeInt},
		{Name: "bill_id", Type: field.TypeInt},
	}
	// BillDataTable holds the schema information for the "bill_data" table.
	BillDataTable = &schema.Table{
		Name:       "bill_data",
		Columns:    BillDataColumns,
		PrimaryKey: []*schema.Column{BillDataColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "bill_data_user_creator",
				Columns:    []*schema.Column{BillDataColumns[5]},
				RefColumns: []*schema.Column{UserColumns[0]},
				OnDelete:   schema.NoAction,
			},
			{
				Symbol:     "bill_data_bill_bill",
				Columns:    []*schema.Column{BillDataColumns[6]},
				RefColumns: []*schema.Column{BillColumns[0]},
				OnDelete:   schema.NoAction,
			},
		},
		Indexes: []*schema.Index{
			{
				Name:    "billdata_bill_id_kind",
				Unique:  true,
				Columns: []*schema.Column{BillDataColumns[6], BillDataColumns[3]},
			},
		},
	}
	// BillItemColumns holds the columns for the "bill_item" table.
	BillItemColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "deleted_at", Type: field.TypeTime, Nullable: true},
		{Name: "status", Type: field.TypeInt8, Default: 2},
		{Name: "version", Type: field.TypeInt, Default: 1},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "amount", Type: field.TypeFloat64, Default: 0},
		{Name: "note", Type: field.TypeString, Nullable: true},
		{Name: "attachment_id", Type: field.TypeInt, Unique: true, Nullable: true},
		{Name: "installment_id", Type: field.TypeInt, Nullable: true},
		{Name: "bill_id", Type: field.TypeInt, Nullable: true},
		{Name: "user_id", Type: field.TypeInt, Nullable: true},
		{Name: "track_id", Type: field.TypeInt, Nullable: true},
	}
	// BillItemTable holds the schema information for the "bill_item" table.
	BillItemTable = &schema.Table{
		Name:       "bill_item",
		Columns:    BillItemColumns,
		PrimaryKey: []*schema.Column{BillItemColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "bill_item_attachment_bill_item",
				Columns:    []*schema.Column{BillItemColumns[8]},
				RefColumns: []*schema.Column{AttachmentColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "bill_item_installment_installment",
				Columns:    []*schema.Column{BillItemColumns[9]},
				RefColumns: []*schema.Column{InstallmentColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "bill_item_bill_bill",
				Columns:    []*schema.Column{BillItemColumns[10]},
				RefColumns: []*schema.Column{BillColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "bill_item_user_user",
				Columns:    []*schema.Column{BillItemColumns[11]},
				RefColumns: []*schema.Column{UserColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "bill_item_track_track",
				Columns:    []*schema.Column{BillItemColumns[12]},
				RefColumns: []*schema.Column{TrackColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
		Indexes: []*schema.Index{
			{
				Name:    "billitem_bill_id",
				Unique:  false,
				Columns: []*schema.Column{BillItemColumns[10]},
			},
			{
				Name:    "billitem_attachment_id",
				Unique:  false,
				Columns: []*schema.Column{BillItemColumns[8]},
			},
			{
				Name:    "billitem_installment_id",
				Unique:  false,
				Columns: []*schema.Column{BillItemColumns[9]},
			},
			{
				Name:    "billitem_status",
				Unique:  false,
				Columns: []*schema.Column{BillItemColumns[2]},
			},
			{
				Name:    "billitem_deleted_at",
				Unique:  false,
				Columns: []*schema.Column{BillItemColumns[1]},
			},
		},
	}
	// BundleColumns holds the columns for the "bundle" table.
	BundleColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "deleted_at", Type: field.TypeTime, Nullable: true},
		{Name: "status", Type: field.TypeInt8, Default: 2},
		{Name: "version", Type: field.TypeInt, Default: 1},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "machine_name", Type: field.TypeString, Unique: true},
		{Name: "name", Type: field.TypeString},
		{Name: "type", Type: field.TypeString},
		{Name: "description", Type: field.TypeString},
	}
	// BundleTable holds the schema information for the "bundle" table.
	BundleTable = &schema.Table{
		Name:       "bundle",
		Columns:    BundleColumns,
		PrimaryKey: []*schema.Column{BundleColumns[0]},
	}
	// CallColumns holds the columns for the "call" table.
	CallColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "deleted_at", Type: field.TypeTime, Nullable: true},
		{Name: "status", Type: field.TypeInt8, Default: 2},
		{Name: "version", Type: field.TypeInt, Default: 1},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "call_id", Type: field.TypeString},
		{Name: "start_time", Type: field.TypeTime},
		{Name: "duration", Type: field.TypeInt, Default: 0},
		{Name: "watting_time", Type: field.TypeInt, Default: 0},
		{Name: "direction", Type: field.TypeString, Nullable: true},
		{Name: "source", Type: field.TypeString, Nullable: true},
		{Name: "destination", Type: field.TypeString, Nullable: true},
		{Name: "recording_file", Type: field.TypeString, Nullable: true},
		{Name: "uuid", Type: field.TypeString, Nullable: true},
		{Name: "kind", Type: field.TypeString, Nullable: true},
		{Name: "score", Type: field.TypeFloat32, Nullable: true},
		{Name: "pdd", Type: field.TypeInt, Default: 0},
		{Name: "tta", Type: field.TypeInt, Default: 0},
		{Name: "rating", Type: field.TypeInt, Default: 0},
		{Name: "feedback", Type: field.TypeString, Nullable: true, Size: 2147483647},
		{Name: "call_status", Type: field.TypeString, Nullable: true},
		{Name: "person_id", Type: field.TypeInt, Nullable: true},
		{Name: "user_id", Type: field.TypeInt, Nullable: true},
	}
	// CallTable holds the schema information for the "call" table.
	CallTable = &schema.Table{
		Name:       "call",
		Columns:    CallColumns,
		PrimaryKey: []*schema.Column{CallColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "call_person_person",
				Columns:    []*schema.Column{CallColumns[22]},
				RefColumns: []*schema.Column{PersonColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "call_user_user",
				Columns:    []*schema.Column{CallColumns[23]},
				RefColumns: []*schema.Column{UserColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
		Indexes: []*schema.Index{
			{
				Name:    "call_call_id_uuid_status",
				Unique:  true,
				Columns: []*schema.Column{CallColumns[6], CallColumns[14], CallColumns[2]},
			},
			{
				Name:    "call_person_id",
				Unique:  false,
				Columns: []*schema.Column{CallColumns[22]},
			},
			{
				Name:    "call_user_id",
				Unique:  false,
				Columns: []*schema.Column{CallColumns[23]},
			},
			{
				Name:    "call_uuid",
				Unique:  false,
				Columns: []*schema.Column{CallColumns[14]},
			},
		},
	}
	// CasbinRuleColumns holds the columns for the "casbin_rule" table.
	CasbinRuleColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "ptype", Type: field.TypeString, Default: ""},
		{Name: "v0", Type: field.TypeString, Default: ""},
		{Name: "v1", Type: field.TypeString, Default: ""},
		{Name: "v2", Type: field.TypeString, Default: ""},
		{Name: "v3", Type: field.TypeString, Default: ""},
		{Name: "v4", Type: field.TypeString, Default: ""},
		{Name: "v5", Type: field.TypeString, Default: ""},
	}
	// CasbinRuleTable holds the schema information for the "casbin_rule" table.
	CasbinRuleTable = &schema.Table{
		Name:       "casbin_rule",
		Columns:    CasbinRuleColumns,
		PrimaryKey: []*schema.Column{CasbinRuleColumns[0]},
	}
	// DealColumns holds the columns for the "deal" table.
	DealColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "deleted_at", Type: field.TypeTime, Nullable: true},
		{Name: "status", Type: field.TypeInt8, Default: 2},
		{Name: "version", Type: field.TypeInt, Default: 1},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "parent_deal_id", Type: field.TypeInt, Nullable: true},
		{Name: "total_amount", Type: field.TypeFloat64, Default: 0},
		{Name: "stage_history", Type: field.TypeJSON, Nullable: true},
		{Name: "name", Type: field.TypeString, Nullable: true},
		{Name: "state", Type: field.TypeEnum, Enums: []string{"draft", "active", "paying", "won", "lost", "cancelled"}, Default: "draft"},
		{Name: "person_id", Type: field.TypeInt, Nullable: true},
		{Name: "stage_id", Type: field.TypeInt, Nullable: true},
	}
	// DealTable holds the schema information for the "deal" table.
	DealTable = &schema.Table{
		Name:       "deal",
		Columns:    DealColumns,
		PrimaryKey: []*schema.Column{DealColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "deal_person_person",
				Columns:    []*schema.Column{DealColumns[11]},
				RefColumns: []*schema.Column{PersonColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "deal_stage_stage",
				Columns:    []*schema.Column{DealColumns[12]},
				RefColumns: []*schema.Column{StageColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
		Indexes: []*schema.Index{
			{
				Name:    "deal_person_id",
				Unique:  false,
				Columns: []*schema.Column{DealColumns[11]},
			},
			{
				Name:    "deal_stage_id_status_deleted_at",
				Unique:  false,
				Columns: []*schema.Column{DealColumns[12], DealColumns[2], DealColumns[1]},
			},
		},
	}
	// DealUserColumns holds the columns for the "deal_user" table.
	DealUserColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "role", Type: field.TypeEnum, Nullable: true, Enums: []string{"treatment_doctor", "consultant_doctor", "advisor", "assistant", "xray_technician", "doctor_assistant"}},
		{Name: "deal_id", Type: field.TypeInt},
		{Name: "user_id", Type: field.TypeInt},
	}
	// DealUserTable holds the schema information for the "deal_user" table.
	DealUserTable = &schema.Table{
		Name:       "deal_user",
		Columns:    DealUserColumns,
		PrimaryKey: []*schema.Column{DealUserColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "deal_user_deal_deal",
				Columns:    []*schema.Column{DealUserColumns[2]},
				RefColumns: []*schema.Column{DealColumns[0]},
				OnDelete:   schema.NoAction,
			},
			{
				Symbol:     "deal_user_user_user",
				Columns:    []*schema.Column{DealUserColumns[3]},
				RefColumns: []*schema.Column{UserColumns[0]},
				OnDelete:   schema.NoAction,
			},
		},
		Indexes: []*schema.Index{
			{
				Name:    "dealuser_deal_id",
				Unique:  false,
				Columns: []*schema.Column{DealUserColumns[2]},
			},
			{
				Name:    "dealuser_user_id",
				Unique:  false,
				Columns: []*schema.Column{DealUserColumns[3]},
			},
			{
				Name:    "dealuser_deal_id_user_id",
				Unique:  true,
				Columns: []*schema.Column{DealUserColumns[2], DealUserColumns[3]},
			},
		},
	}
	// DepartmentColumns holds the columns for the "department" table.
	DepartmentColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "name", Type: field.TypeString},
		{Name: "description", Type: field.TypeString, Nullable: true},
	}
	// DepartmentTable holds the schema information for the "department" table.
	DepartmentTable = &schema.Table{
		Name:       "department",
		Columns:    DepartmentColumns,
		PrimaryKey: []*schema.Column{DepartmentColumns[0]},
		Indexes: []*schema.Index{
			{
				Name:    "department_name",
				Unique:  false,
				Columns: []*schema.Column{DepartmentColumns[3]},
			},
		},
	}
	// DepositsColumns holds the columns for the "deposits" table.
	DepositsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "deleted_at", Type: field.TypeTime, Nullable: true},
		{Name: "status", Type: field.TypeInt8, Default: 2},
		{Name: "version", Type: field.TypeInt, Default: 1},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "total_amount", Type: field.TypeFloat64},
		{Name: "paid_amount", Type: field.TypeFloat64, Default: 0},
		{Name: "remaining_amount", Type: field.TypeFloat64},
		{Name: "state", Type: field.TypeEnum, Enums: []string{"pending", "active", "converted", "refund", "cancelled"}, Default: "pending"},
		{Name: "description", Type: field.TypeString, Nullable: true, Size: 500},
		{Name: "created_by", Type: field.TypeInt, Nullable: true},
		{Name: "updated_by", Type: field.TypeInt, Nullable: true},
		{Name: "deal_id", Type: field.TypeInt, Nullable: true},
	}
	// DepositsTable holds the schema information for the "deposits" table.
	DepositsTable = &schema.Table{
		Name:       "deposits",
		Columns:    DepositsColumns,
		PrimaryKey: []*schema.Column{DepositsColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "deposits_deal_deal",
				Columns:    []*schema.Column{DepositsColumns[13]},
				RefColumns: []*schema.Column{DealColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
		Indexes: []*schema.Index{
			{
				Name:    "deposit_deal_id",
				Unique:  false,
				Columns: []*schema.Column{DepositsColumns[13]},
			},
			{
				Name:    "deposit_state",
				Unique:  false,
				Columns: []*schema.Column{DepositsColumns[9]},
			},
		},
	}
	// DepositAllocationsColumns holds the columns for the "deposit_allocations" table.
	DepositAllocationsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "deleted_at", Type: field.TypeTime, Nullable: true},
		{Name: "status", Type: field.TypeInt8, Default: 2},
		{Name: "version", Type: field.TypeInt, Default: 1},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "deal_id", Type: field.TypeInt, Nullable: true},
		{Name: "amount", Type: field.TypeFloat64},
		{Name: "state", Type: field.TypeEnum, Enums: []string{"active", "converted", "refund", "cancelled"}, Default: "active"},
		{Name: "created_by", Type: field.TypeInt, Nullable: true},
		{Name: "updated_by", Type: field.TypeInt, Nullable: true},
		{Name: "deposit_id", Type: field.TypeInt, Nullable: true},
		{Name: "attachment_id", Type: field.TypeInt, Nullable: true},
	}
	// DepositAllocationsTable holds the schema information for the "deposit_allocations" table.
	DepositAllocationsTable = &schema.Table{
		Name:       "deposit_allocations",
		Columns:    DepositAllocationsColumns,
		PrimaryKey: []*schema.Column{DepositAllocationsColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "deposit_allocations_deposits_deposit",
				Columns:    []*schema.Column{DepositAllocationsColumns[11]},
				RefColumns: []*schema.Column{DepositsColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "deposit_allocations_attachment_attachment",
				Columns:    []*schema.Column{DepositAllocationsColumns[12]},
				RefColumns: []*schema.Column{AttachmentColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
		Indexes: []*schema.Index{
			{
				Name:    "depositallocation_deposit_id",
				Unique:  false,
				Columns: []*schema.Column{DepositAllocationsColumns[11]},
			},
			{
				Name:    "depositallocation_attachment_id",
				Unique:  false,
				Columns: []*schema.Column{DepositAllocationsColumns[12]},
			},
		},
	}
	// DepositPaymentsColumns holds the columns for the "deposit_payments" table.
	DepositPaymentsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "deleted_at", Type: field.TypeTime, Nullable: true},
		{Name: "status", Type: field.TypeInt8, Default: 2},
		{Name: "version", Type: field.TypeInt, Default: 1},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "amount", Type: field.TypeFloat64},
		{Name: "conversion_date", Type: field.TypeTime},
		{Name: "state", Type: field.TypeEnum, Enums: []string{"active", "completed", "cancelled"}, Default: "active"},
		{Name: "note", Type: field.TypeString, Nullable: true, Size: 500},
		{Name: "created_by", Type: field.TypeInt, Nullable: true},
		{Name: "updated_by", Type: field.TypeInt, Nullable: true},
		{Name: "deposit_id", Type: field.TypeInt, Nullable: true},
		{Name: "payment_id", Type: field.TypeInt, Nullable: true},
	}
	// DepositPaymentsTable holds the schema information for the "deposit_payments" table.
	DepositPaymentsTable = &schema.Table{
		Name:       "deposit_payments",
		Columns:    DepositPaymentsColumns,
		PrimaryKey: []*schema.Column{DepositPaymentsColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "deposit_payments_deposits_deposit",
				Columns:    []*schema.Column{DepositPaymentsColumns[12]},
				RefColumns: []*schema.Column{DepositsColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "deposit_payments_payment_payment",
				Columns:    []*schema.Column{DepositPaymentsColumns[13]},
				RefColumns: []*schema.Column{PaymentColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
		Indexes: []*schema.Index{
			{
				Name:    "depositpayment_deposit_id",
				Unique:  false,
				Columns: []*schema.Column{DepositPaymentsColumns[12]},
			},
			{
				Name:    "depositpayment_payment_id",
				Unique:  false,
				Columns: []*schema.Column{DepositPaymentsColumns[13]},
			},
		},
	}
	// DiscountColumns holds the columns for the "discount" table.
	DiscountColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "deleted_at", Type: field.TypeTime, Nullable: true},
		{Name: "status", Type: field.TypeInt8, Default: 2},
		{Name: "version", Type: field.TypeInt, Default: 1},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "name", Type: field.TypeString},
		{Name: "type", Type: field.TypeString},
		{Name: "value", Type: field.TypeFloat64},
		{Name: "scope", Type: field.TypeString},
		{Name: "condition", Type: field.TypeString, Nullable: true},
		{Name: "usage_type", Type: field.TypeString},
		{Name: "description", Type: field.TypeString, Nullable: true},
		{Name: "meta", Type: field.TypeJSON, Nullable: true},
		{Name: "start", Type: field.TypeTime, Nullable: true},
		{Name: "end", Type: field.TypeTime, Nullable: true},
	}
	// DiscountTable holds the schema information for the "discount" table.
	DiscountTable = &schema.Table{
		Name:       "discount",
		Columns:    DiscountColumns,
		PrimaryKey: []*schema.Column{DiscountColumns[0]},
	}
	// DiscountUsageColumns holds the columns for the "discount_usage" table.
	DiscountUsageColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "deleted_at", Type: field.TypeTime, Nullable: true},
		{Name: "status", Type: field.TypeInt8, Default: 2},
		{Name: "version", Type: field.TypeInt, Default: 1},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "user_id", Type: field.TypeInt, Nullable: true},
		{Name: "person_id", Type: field.TypeInt, Nullable: true},
		{Name: "attachment_id", Type: field.TypeInt, Nullable: true},
		{Name: "entity_type", Type: field.TypeString, Nullable: true},
		{Name: "usage_count", Type: field.TypeInt, Default: 0},
		{Name: "value", Type: field.TypeFloat64, Default: 0},
		{Name: "discount_id", Type: field.TypeInt},
		{Name: "deal_id", Type: field.TypeInt, Nullable: true},
	}
	// DiscountUsageTable holds the schema information for the "discount_usage" table.
	DiscountUsageTable = &schema.Table{
		Name:       "discount_usage",
		Columns:    DiscountUsageColumns,
		PrimaryKey: []*schema.Column{DiscountUsageColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "discount_usage_discount_discount",
				Columns:    []*schema.Column{DiscountUsageColumns[12]},
				RefColumns: []*schema.Column{DiscountColumns[0]},
				OnDelete:   schema.NoAction,
			},
			{
				Symbol:     "discount_usage_deal_deal",
				Columns:    []*schema.Column{DiscountUsageColumns[13]},
				RefColumns: []*schema.Column{DealColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
		Indexes: []*schema.Index{
			{
				Name:    "discountusage_discount_id",
				Unique:  false,
				Columns: []*schema.Column{DiscountUsageColumns[12]},
			},
			{
				Name:    "discountusage_user_id",
				Unique:  false,
				Columns: []*schema.Column{DiscountUsageColumns[6]},
			},
			{
				Name:    "discountusage_person_id",
				Unique:  false,
				Columns: []*schema.Column{DiscountUsageColumns[7]},
			},
			{
				Name:    "discountusage_deal_id",
				Unique:  false,
				Columns: []*schema.Column{DiscountUsageColumns[13]},
			},
			{
				Name:    "discountusage_attachment_id",
				Unique:  false,
				Columns: []*schema.Column{DiscountUsageColumns[8]},
			},
		},
	}
	// FileColumns holds the columns for the "file" table.
	FileColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "deleted_at", Type: field.TypeTime, Nullable: true},
		{Name: "status", Type: field.TypeInt8, Default: 2},
		{Name: "version", Type: field.TypeInt, Default: 1},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "name", Type: field.TypeString, Nullable: true},
		{Name: "kind", Type: field.TypeString, Nullable: true},
		{Name: "type", Type: field.TypeString, Nullable: true},
		{Name: "size", Type: field.TypeInt, Default: 0},
		{Name: "path", Type: field.TypeString},
		{Name: "user_id", Type: field.TypeInt, Nullable: true},
		{Name: "storage", Type: field.TypeString},
		{Name: "meta", Type: field.TypeJSON, Nullable: true},
	}
	// FileTable holds the schema information for the "file" table.
	FileTable = &schema.Table{
		Name:       "file",
		Columns:    FileColumns,
		PrimaryKey: []*schema.Column{FileColumns[0]},
	}
	// FileUsageColumns holds the columns for the "file_usage" table.
	FileUsageColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "deleted_at", Type: field.TypeTime, Nullable: true},
		{Name: "status", Type: field.TypeInt8, Default: 2},
		{Name: "version", Type: field.TypeInt, Default: 1},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "entity_type", Type: field.TypeString},
		{Name: "entity_id", Type: field.TypeInt},
		{Name: "usage_type", Type: field.TypeString, Nullable: true},
		{Name: "usage_meta", Type: field.TypeJSON, Nullable: true},
		{Name: "file_id", Type: field.TypeInt},
		{Name: "track_id", Type: field.TypeInt, Nullable: true},
	}
	// FileUsageTable holds the schema information for the "file_usage" table.
	FileUsageTable = &schema.Table{
		Name:       "file_usage",
		Columns:    FileUsageColumns,
		PrimaryKey: []*schema.Column{FileUsageColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "file_usage_file_file",
				Columns:    []*schema.Column{FileUsageColumns[10]},
				RefColumns: []*schema.Column{FileColumns[0]},
				OnDelete:   schema.NoAction,
			},
			{
				Symbol:     "file_usage_track_track",
				Columns:    []*schema.Column{FileUsageColumns[11]},
				RefColumns: []*schema.Column{TrackColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
		Indexes: []*schema.Index{
			{
				Name:    "fileusage_entity_type_entity_id",
				Unique:  false,
				Columns: []*schema.Column{FileUsageColumns[6], FileUsageColumns[7]},
			},
		},
	}
	// InstallmentColumns holds the columns for the "installment" table.
	InstallmentColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "deleted_at", Type: field.TypeTime, Nullable: true},
		{Name: "status", Type: field.TypeInt8, Default: 2},
		{Name: "version", Type: field.TypeInt, Default: 1},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "installment_number", Type: field.TypeInt, Default: 0},
		{Name: "amount", Type: field.TypeFloat64, Default: 0},
		{Name: "note", Type: field.TypeString, Nullable: true},
		{Name: "name", Type: field.TypeString, Nullable: true},
		{Name: "person_id", Type: field.TypeInt, Nullable: true},
		{Name: "transaction_type", Type: field.TypeInt, Default: 0},
		{Name: "kind", Type: field.TypeEnum, Nullable: true, Enums: []string{"down_payment", "sequence_payment", "refund_payment"}},
		{Name: "paid_at", Type: field.TypeTime, Nullable: true},
		{Name: "plan_id", Type: field.TypeInt, Nullable: true},
		{Name: "user_id", Type: field.TypeInt, Nullable: true},
	}
	// InstallmentTable holds the schema information for the "installment" table.
	InstallmentTable = &schema.Table{
		Name:       "installment",
		Columns:    InstallmentColumns,
		PrimaryKey: []*schema.Column{InstallmentColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "installment_installment_plan_plan",
				Columns:    []*schema.Column{InstallmentColumns[14]},
				RefColumns: []*schema.Column{InstallmentPlanColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "installment_user_creator",
				Columns:    []*schema.Column{InstallmentColumns[15]},
				RefColumns: []*schema.Column{UserColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
		Indexes: []*schema.Index{
			{
				Name:    "installment_plan_id",
				Unique:  false,
				Columns: []*schema.Column{InstallmentColumns[14]},
			},
			{
				Name:    "installment_person_id",
				Unique:  false,
				Columns: []*schema.Column{InstallmentColumns[10]},
			},
		},
	}
	// InstallmentPlanColumns holds the columns for the "installment_plan" table.
	InstallmentPlanColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "deleted_at", Type: field.TypeTime, Nullable: true},
		{Name: "status", Type: field.TypeInt8, Default: 2},
		{Name: "version", Type: field.TypeInt, Default: 1},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "name", Type: field.TypeString, Nullable: true, Default: "Trả góp"},
		{Name: "total_amount", Type: field.TypeFloat64, Default: 0},
		{Name: "down_payment", Type: field.TypeFloat64, Default: 0},
		{Name: "total_installments", Type: field.TypeInt, Default: 0},
		{Name: "state", Type: field.TypeEnum, Enums: []string{"draft", "active", "completed", "cancelled"}, Default: "draft"},
		{Name: "person_id", Type: field.TypeInt, Nullable: true},
		{Name: "deal_id", Type: field.TypeInt, Nullable: true},
		{Name: "user_id", Type: field.TypeInt, Nullable: true},
		{Name: "bill_id", Type: field.TypeInt, Nullable: true},
	}
	// InstallmentPlanTable holds the schema information for the "installment_plan" table.
	InstallmentPlanTable = &schema.Table{
		Name:       "installment_plan",
		Columns:    InstallmentPlanColumns,
		PrimaryKey: []*schema.Column{InstallmentPlanColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "installment_plan_person_person",
				Columns:    []*schema.Column{InstallmentPlanColumns[11]},
				RefColumns: []*schema.Column{PersonColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "installment_plan_deal_deal",
				Columns:    []*schema.Column{InstallmentPlanColumns[12]},
				RefColumns: []*schema.Column{DealColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "installment_plan_user_creator",
				Columns:    []*schema.Column{InstallmentPlanColumns[13]},
				RefColumns: []*schema.Column{UserColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "installment_plan_bill_bill",
				Columns:    []*schema.Column{InstallmentPlanColumns[14]},
				RefColumns: []*schema.Column{BillColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
		Indexes: []*schema.Index{
			{
				Name:    "installmentplan_person_id",
				Unique:  false,
				Columns: []*schema.Column{InstallmentPlanColumns[11]},
			},
			{
				Name:    "installmentplan_deal_id",
				Unique:  false,
				Columns: []*schema.Column{InstallmentPlanColumns[12]},
			},
		},
	}
	// IssueColumns holds the columns for the "issue" table.
	IssueColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "deleted_at", Type: field.TypeTime, Nullable: true},
		{Name: "status", Type: field.TypeInt8, Default: 2},
		{Name: "version", Type: field.TypeInt, Default: 1},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "title", Type: field.TypeString},
		{Name: "description", Type: field.TypeString, Nullable: true},
		{Name: "type", Type: field.TypeEnum, Enums: []string{"complain", "refund", "warranty", "inquiry", "orther"}},
		{Name: "progress", Type: field.TypeEnum, Enums: []string{"open", "in_progress", "resolved", "closed"}},
		{Name: "priority", Type: field.TypeEnum, Enums: []string{"low", "medium", "high"}},
		{Name: "person_id", Type: field.TypeInt, Nullable: true},
	}
	// IssueTable holds the schema information for the "issue" table.
	IssueTable = &schema.Table{
		Name:       "issue",
		Columns:    IssueColumns,
		PrimaryKey: []*schema.Column{IssueColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "issue_person_person",
				Columns:    []*schema.Column{IssueColumns[11]},
				RefColumns: []*schema.Column{PersonColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
		Indexes: []*schema.Index{
			{
				Name:    "issue_person_id_deleted_at_status",
				Unique:  false,
				Columns: []*schema.Column{IssueColumns[11], IssueColumns[1], IssueColumns[2]},
			},
		},
	}
	// IssueCommentColumns holds the columns for the "issue_comment" table.
	IssueCommentColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "deleted_at", Type: field.TypeTime, Nullable: true},
		{Name: "status", Type: field.TypeInt8, Default: 2},
		{Name: "version", Type: field.TypeInt, Default: 1},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "comment", Type: field.TypeString},
		{Name: "user_id", Type: field.TypeInt, Nullable: true},
		{Name: "issue_id", Type: field.TypeInt, Nullable: true},
	}
	// IssueCommentTable holds the schema information for the "issue_comment" table.
	IssueCommentTable = &schema.Table{
		Name:       "issue_comment",
		Columns:    IssueCommentColumns,
		PrimaryKey: []*schema.Column{IssueCommentColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "issue_comment_user_creator",
				Columns:    []*schema.Column{IssueCommentColumns[7]},
				RefColumns: []*schema.Column{UserColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "issue_comment_issue_issue",
				Columns:    []*schema.Column{IssueCommentColumns[8]},
				RefColumns: []*schema.Column{IssueColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
	}
	// LocalDistrictColumns holds the columns for the "local_district" table.
	LocalDistrictColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "name", Type: field.TypeString, Nullable: true},
		{Name: "prefix", Type: field.TypeString, Nullable: true},
		{Name: "province_id", Type: field.TypeInt, Nullable: true},
	}
	// LocalDistrictTable holds the schema information for the "local_district" table.
	LocalDistrictTable = &schema.Table{
		Name:       "local_district",
		Columns:    LocalDistrictColumns,
		PrimaryKey: []*schema.Column{LocalDistrictColumns[0]},
	}
	// LocalProvinceColumns holds the columns for the "local_province" table.
	LocalProvinceColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "name", Type: field.TypeString, Nullable: true},
		{Name: "code", Type: field.TypeString, Nullable: true},
	}
	// LocalProvinceTable holds the schema information for the "local_province" table.
	LocalProvinceTable = &schema.Table{
		Name:       "local_province",
		Columns:    LocalProvinceColumns,
		PrimaryKey: []*schema.Column{LocalProvinceColumns[0]},
	}
	// LocalWardColumns holds the columns for the "local_ward" table.
	LocalWardColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "name", Type: field.TypeString},
		{Name: "prefix", Type: field.TypeString, Nullable: true},
		{Name: "province_id", Type: field.TypeInt, Nullable: true},
		{Name: "district_id", Type: field.TypeInt, Nullable: true},
	}
	// LocalWardTable holds the schema information for the "local_ward" table.
	LocalWardTable = &schema.Table{
		Name:       "local_ward",
		Columns:    LocalWardColumns,
		PrimaryKey: []*schema.Column{LocalWardColumns[0]},
	}
	// MessageHistoryColumns holds the columns for the "message_history" table.
	MessageHistoryColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "deleted_at", Type: field.TypeTime, Nullable: true},
		{Name: "status", Type: field.TypeInt8, Default: 2},
		{Name: "version", Type: field.TypeInt, Default: 1},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "message_id", Type: field.TypeString, Nullable: true},
		{Name: "phone", Type: field.TypeString, Nullable: true},
		{Name: "type", Type: field.TypeEnum, Nullable: true, Enums: []string{"sms", "zns"}},
		{Name: "content", Type: field.TypeString, Nullable: true, Size: 2147483647},
		{Name: "zns_data", Type: field.TypeString, Nullable: true, Size: 2147483647},
		{Name: "error_code", Type: field.TypeInt, Nullable: true},
		{Name: "message_status", Type: field.TypeEnum, Nullable: true, Enums: []string{"sent", "failed", "delivered"}},
		{Name: "delivered_at", Type: field.TypeTime, Nullable: true},
		{Name: "person_id", Type: field.TypeInt, Nullable: true},
		{Name: "user_id", Type: field.TypeInt, Nullable: true},
	}
	// MessageHistoryTable holds the schema information for the "message_history" table.
	MessageHistoryTable = &schema.Table{
		Name:       "message_history",
		Columns:    MessageHistoryColumns,
		PrimaryKey: []*schema.Column{MessageHistoryColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "message_history_person_person",
				Columns:    []*schema.Column{MessageHistoryColumns[14]},
				RefColumns: []*schema.Column{PersonColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "message_history_user_user",
				Columns:    []*schema.Column{MessageHistoryColumns[15]},
				RefColumns: []*schema.Column{UserColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
	}
	// NoteColumns holds the columns for the "note" table.
	NoteColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "deleted_at", Type: field.TypeTime, Nullable: true},
		{Name: "status", Type: field.TypeInt8, Default: 2},
		{Name: "version", Type: field.TypeInt, Default: 1},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "body", Type: field.TypeString, Nullable: true, Size: 2147483647},
		{Name: "type", Type: field.TypeInt, Default: 0},
		{Name: "history", Type: field.TypeJSON, Nullable: true},
		{Name: "user_id", Type: field.TypeInt, Nullable: true},
		{Name: "person_id", Type: field.TypeInt, Nullable: true},
		{Name: "deal_id", Type: field.TypeInt, Nullable: true},
	}
	// NoteTable holds the schema information for the "note" table.
	NoteTable = &schema.Table{
		Name:       "note",
		Columns:    NoteColumns,
		PrimaryKey: []*schema.Column{NoteColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "note_user_creator",
				Columns:    []*schema.Column{NoteColumns[9]},
				RefColumns: []*schema.Column{UserColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "note_person_person",
				Columns:    []*schema.Column{NoteColumns[10]},
				RefColumns: []*schema.Column{PersonColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "note_deal_deal",
				Columns:    []*schema.Column{NoteColumns[11]},
				RefColumns: []*schema.Column{DealColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
		Indexes: []*schema.Index{
			{
				Name:    "note_person_id_deleted_at_status_created_at",
				Unique:  false,
				Columns: []*schema.Column{NoteColumns[10], NoteColumns[1], NoteColumns[2], NoteColumns[4]},
			},
		},
	}
	// OtpColumns holds the columns for the "otp" table.
	OtpColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "version", Type: field.TypeInt, Default: 1},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "user_id", Type: field.TypeInt},
		{Name: "code", Type: field.TypeString},
		{Name: "purpose", Type: field.TypeString},
		{Name: "expires_at", Type: field.TypeTime},
		{Name: "is_used", Type: field.TypeBool, Default: false},
		{Name: "sent_to", Type: field.TypeString},
	}
	// OtpTable holds the schema information for the "otp" table.
	OtpTable = &schema.Table{
		Name:       "otp",
		Columns:    OtpColumns,
		PrimaryKey: []*schema.Column{OtpColumns[0]},
	}
	// OperationColumns holds the columns for the "operation" table.
	OperationColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "deleted_at", Type: field.TypeTime, Nullable: true},
		{Name: "status", Type: field.TypeInt8, Default: 2},
		{Name: "version", Type: field.TypeInt, Default: 1},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "name", Type: field.TypeString, Unique: true},
		{Name: "description", Type: field.TypeString, Nullable: true, Size: 2147483647},
		{Name: "duration", Type: field.TypeInt, Nullable: true},
		{Name: "group", Type: field.TypeJSON, Nullable: true},
	}
	// OperationTable holds the schema information for the "operation" table.
	OperationTable = &schema.Table{
		Name:       "operation",
		Columns:    OperationColumns,
		PrimaryKey: []*schema.Column{OperationColumns[0]},
	}
	// OrganizationColumns holds the columns for the "organization" table.
	OrganizationColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "deleted_at", Type: field.TypeTime, Nullable: true},
		{Name: "status", Type: field.TypeInt8, Default: 2},
		{Name: "version", Type: field.TypeInt, Default: 1},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "full_name", Type: field.TypeString},
		{Name: "province_id", Type: field.TypeInt, Nullable: true},
		{Name: "district_id", Type: field.TypeInt, Nullable: true},
		{Name: "ward_id", Type: field.TypeInt, Nullable: true},
		{Name: "address_number", Type: field.TypeString, Nullable: true},
		{Name: "phone", Type: field.TypeString, Nullable: true},
		{Name: "email", Type: field.TypeString, Nullable: true},
	}
	// OrganizationTable holds the schema information for the "organization" table.
	OrganizationTable = &schema.Table{
		Name:       "organization",
		Columns:    OrganizationColumns,
		PrimaryKey: []*schema.Column{OrganizationColumns[0]},
	}
	// PaymentColumns holds the columns for the "payment" table.
	PaymentColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "deleted_at", Type: field.TypeTime, Nullable: true},
		{Name: "status", Type: field.TypeInt8, Default: 2},
		{Name: "version", Type: field.TypeInt, Default: 1},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "total_amount", Type: field.TypeFloat64, Default: 0},
		{Name: "cash", Type: field.TypeFloat64, Default: 0},
		{Name: "credit_card", Type: field.TypeFloat64, Default: 0},
		{Name: "mpos", Type: field.TypeFloat64, Default: 0},
		{Name: "bank", Type: field.TypeFloat64, Default: 0},
		{Name: "momo", Type: field.TypeFloat64, Default: 0},
		{Name: "state", Type: field.TypeEnum, Enums: []string{"draft", "pending", "completed", "failed"}, Default: "pending"},
		{Name: "payment_date", Type: field.TypeTime},
		{Name: "person_id", Type: field.TypeInt, Nullable: true},
		{Name: "user_id", Type: field.TypeInt, Nullable: true},
		{Name: "deal_id", Type: field.TypeInt, Nullable: true},
		{Name: "bill_id", Type: field.TypeInt, Nullable: true},
	}
	// PaymentTable holds the schema information for the "payment" table.
	PaymentTable = &schema.Table{
		Name:       "payment",
		Columns:    PaymentColumns,
		PrimaryKey: []*schema.Column{PaymentColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "payment_bill_bill",
				Columns:    []*schema.Column{PaymentColumns[17]},
				RefColumns: []*schema.Column{BillColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
		Indexes: []*schema.Index{
			{
				Name:    "payment_bill_id",
				Unique:  false,
				Columns: []*schema.Column{PaymentColumns[17]},
			},
			{
				Name:    "payment_person_id",
				Unique:  false,
				Columns: []*schema.Column{PaymentColumns[14]},
			},
			{
				Name:    "payment_payment_date",
				Unique:  false,
				Columns: []*schema.Column{PaymentColumns[13]},
			},
			{
				Name:    "payment_status",
				Unique:  false,
				Columns: []*schema.Column{PaymentColumns[2]},
			},
			{
				Name:    "payment_deleted_at",
				Unique:  false,
				Columns: []*schema.Column{PaymentColumns[1]},
			},
		},
	}
	// PaymentAllocationColumns holds the columns for the "payment_allocation" table.
	PaymentAllocationColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "deleted_at", Type: field.TypeTime, Nullable: true},
		{Name: "status", Type: field.TypeInt8, Default: 2},
		{Name: "version", Type: field.TypeInt, Default: 1},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "bill_id", Type: field.TypeInt, Nullable: true},
		{Name: "user_id", Type: field.TypeInt, Nullable: true},
		{Name: "note", Type: field.TypeString, Nullable: true},
		{Name: "amount", Type: field.TypeFloat64, Default: 0},
		{Name: "payment_id", Type: field.TypeInt},
		{Name: "bill_item_id", Type: field.TypeInt, Nullable: true},
		{Name: "installment_id", Type: field.TypeInt, Nullable: true},
	}
	// PaymentAllocationTable holds the schema information for the "payment_allocation" table.
	PaymentAllocationTable = &schema.Table{
		Name:       "payment_allocation",
		Columns:    PaymentAllocationColumns,
		PrimaryKey: []*schema.Column{PaymentAllocationColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "payment_allocation_payment_payment",
				Columns:    []*schema.Column{PaymentAllocationColumns[10]},
				RefColumns: []*schema.Column{PaymentColumns[0]},
				OnDelete:   schema.NoAction,
			},
			{
				Symbol:     "payment_allocation_bill_item_bill_item",
				Columns:    []*schema.Column{PaymentAllocationColumns[11]},
				RefColumns: []*schema.Column{BillItemColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "payment_allocation_installment_installment",
				Columns:    []*schema.Column{PaymentAllocationColumns[12]},
				RefColumns: []*schema.Column{InstallmentColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
		Indexes: []*schema.Index{
			{
				Name:    "paymentallocation_payment_id",
				Unique:  false,
				Columns: []*schema.Column{PaymentAllocationColumns[10]},
			},
			{
				Name:    "paymentallocation_bill_item_id",
				Unique:  false,
				Columns: []*schema.Column{PaymentAllocationColumns[11]},
			},
			{
				Name:    "paymentallocation_status",
				Unique:  false,
				Columns: []*schema.Column{PaymentAllocationColumns[2]},
			},
			{
				Name:    "paymentallocation_deleted_at",
				Unique:  false,
				Columns: []*schema.Column{PaymentAllocationColumns[1]},
			},
		},
	}
	// PersonColumns holds the columns for the "person" table.
	PersonColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "deleted_at", Type: field.TypeTime, Nullable: true},
		{Name: "status", Type: field.TypeInt8, Default: 2},
		{Name: "version", Type: field.TypeInt, Default: 1},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "full_name", Type: field.TypeString},
		{Name: "date_of_birth", Type: field.TypeTime, Nullable: true},
		{Name: "gender", Type: field.TypeString, Nullable: true},
		{Name: "province_id", Type: field.TypeInt, Nullable: true},
		{Name: "district_id", Type: field.TypeInt, Nullable: true},
		{Name: "ward_id", Type: field.TypeInt, Nullable: true},
		{Name: "address_number", Type: field.TypeString, Nullable: true},
		{Name: "phone", Type: field.TypeString},
		{Name: "email", Type: field.TypeString, Nullable: true},
		{Name: "phone_confirm", Type: field.TypeBool, Nullable: true},
		{Name: "mail_confirm", Type: field.TypeBool, Nullable: true},
		{Name: "person_field", Type: field.TypeJSON, Nullable: true},
		{Name: "job_id", Type: field.TypeInt, Nullable: true},
		{Name: "source_id", Type: field.TypeInt, Nullable: true},
		{Name: "user_id", Type: field.TypeInt, Nullable: true},
	}
	// PersonTable holds the schema information for the "person" table.
	PersonTable = &schema.Table{
		Name:       "person",
		Columns:    PersonColumns,
		PrimaryKey: []*schema.Column{PersonColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "person_term_job",
				Columns:    []*schema.Column{PersonColumns[18]},
				RefColumns: []*schema.Column{TermColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "person_term_source",
				Columns:    []*schema.Column{PersonColumns[19]},
				RefColumns: []*schema.Column{TermColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "person_user_creator",
				Columns:    []*schema.Column{PersonColumns[20]},
				RefColumns: []*schema.Column{UserColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
		Indexes: []*schema.Index{
			{
				Name:    "person_full_name_phone_status",
				Unique:  true,
				Columns: []*schema.Column{PersonColumns[6], PersonColumns[13], PersonColumns[2]},
			},
			{
				Name:    "person_full_name",
				Unique:  false,
				Columns: []*schema.Column{PersonColumns[6]},
			},
			{
				Name:    "person_phone",
				Unique:  false,
				Columns: []*schema.Column{PersonColumns[13]},
			},
			{
				Name:    "person_deleted_at_status",
				Unique:  false,
				Columns: []*schema.Column{PersonColumns[1], PersonColumns[2]},
			},
		},
	}
	// PersonAssignmentColumns holds the columns for the "person_assignment" table.
	PersonAssignmentColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "role", Type: field.TypeEnum, Nullable: true, Enums: []string{"doctor", "counselor", "sale", "customer_care"}},
		{Name: "person_id", Type: field.TypeInt},
		{Name: "user_id", Type: field.TypeInt},
	}
	// PersonAssignmentTable holds the schema information for the "person_assignment" table.
	PersonAssignmentTable = &schema.Table{
		Name:       "person_assignment",
		Columns:    PersonAssignmentColumns,
		PrimaryKey: []*schema.Column{PersonAssignmentColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "person_assignment_person_person",
				Columns:    []*schema.Column{PersonAssignmentColumns[4]},
				RefColumns: []*schema.Column{PersonColumns[0]},
				OnDelete:   schema.NoAction,
			},
			{
				Symbol:     "person_assignment_user_user",
				Columns:    []*schema.Column{PersonAssignmentColumns[5]},
				RefColumns: []*schema.Column{UserColumns[0]},
				OnDelete:   schema.NoAction,
			},
		},
		Indexes: []*schema.Index{
			{
				Name:    "personassignment_person_id",
				Unique:  false,
				Columns: []*schema.Column{PersonAssignmentColumns[4]},
			},
			{
				Name:    "personassignment_user_id",
				Unique:  false,
				Columns: []*schema.Column{PersonAssignmentColumns[5]},
			},
			{
				Name:    "personassignment_person_id_user_id",
				Unique:  true,
				Columns: []*schema.Column{PersonAssignmentColumns[4], PersonAssignmentColumns[5]},
			},
		},
	}
	// PersonDataColumns holds the columns for the "person_data" table.
	PersonDataColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "kind", Type: field.TypeString},
		{Name: "data", Type: field.TypeJSON, Nullable: true},
		{Name: "person_id", Type: field.TypeInt},
	}
	// PersonDataTable holds the schema information for the "person_data" table.
	PersonDataTable = &schema.Table{
		Name:       "person_data",
		Columns:    PersonDataColumns,
		PrimaryKey: []*schema.Column{PersonDataColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "person_data_person_person",
				Columns:    []*schema.Column{PersonDataColumns[5]},
				RefColumns: []*schema.Column{PersonColumns[0]},
				OnDelete:   schema.NoAction,
			},
		},
		Indexes: []*schema.Index{
			{
				Name:    "persondata_person_id_kind",
				Unique:  true,
				Columns: []*schema.Column{PersonDataColumns[5], PersonDataColumns[3]},
			},
		},
	}
	// PersonReferralColumns holds the columns for the "person_referral" table.
	PersonReferralColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "note", Type: field.TypeString, Nullable: true},
		{Name: "relationship", Type: field.TypeString, Nullable: true},
		{Name: "referrer_id", Type: field.TypeInt},
		{Name: "referred_id", Type: field.TypeInt, Unique: true},
	}
	// PersonReferralTable holds the schema information for the "person_referral" table.
	PersonReferralTable = &schema.Table{
		Name:       "person_referral",
		Columns:    PersonReferralColumns,
		PrimaryKey: []*schema.Column{PersonReferralColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "person_referral_person_referred_by",
				Columns:    []*schema.Column{PersonReferralColumns[6]},
				RefColumns: []*schema.Column{PersonColumns[0]},
				OnDelete:   schema.NoAction,
			},
		},
	}
	// PersonStageColumns holds the columns for the "person_stage" table.
	PersonStageColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "deleted_at", Type: field.TypeTime, Nullable: true},
		{Name: "status", Type: field.TypeInt8, Default: 2},
		{Name: "version", Type: field.TypeInt, Default: 1},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "expected_end_date", Type: field.TypeTime, Nullable: true},
		{Name: "user_id", Type: field.TypeInt, Nullable: true},
		{Name: "person_id", Type: field.TypeInt, Nullable: true},
		{Name: "stage_id", Type: field.TypeInt, Nullable: true},
	}
	// PersonStageTable holds the schema information for the "person_stage" table.
	PersonStageTable = &schema.Table{
		Name:       "person_stage",
		Columns:    PersonStageColumns,
		PrimaryKey: []*schema.Column{PersonStageColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "person_stage_user_user",
				Columns:    []*schema.Column{PersonStageColumns[7]},
				RefColumns: []*schema.Column{UserColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "person_stage_person_person",
				Columns:    []*schema.Column{PersonStageColumns[8]},
				RefColumns: []*schema.Column{PersonColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "person_stage_stage_stage",
				Columns:    []*schema.Column{PersonStageColumns[9]},
				RefColumns: []*schema.Column{StageColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
	}
	// PipelineColumns holds the columns for the "pipeline" table.
	PipelineColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "deleted_at", Type: field.TypeTime, Nullable: true},
		{Name: "status", Type: field.TypeInt8, Default: 2},
		{Name: "version", Type: field.TypeInt, Default: 1},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "name", Type: field.TypeString},
		{Name: "description", Type: field.TypeString, Nullable: true},
		{Name: "user_id", Type: field.TypeInt, Nullable: true},
	}
	// PipelineTable holds the schema information for the "pipeline" table.
	PipelineTable = &schema.Table{
		Name:       "pipeline",
		Columns:    PipelineColumns,
		PrimaryKey: []*schema.Column{PipelineColumns[0]},
	}
	// ProductColumns holds the columns for the "product" table.
	ProductColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "deleted_at", Type: field.TypeTime, Nullable: true},
		{Name: "status", Type: field.TypeInt8, Default: 2},
		{Name: "version", Type: field.TypeInt, Default: 1},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "name", Type: field.TypeString},
		{Name: "code", Type: field.TypeString, Nullable: true},
		{Name: "description", Type: field.TypeString, Nullable: true, Size: 2147483647},
		{Name: "price", Type: field.TypeInt, Default: 0},
		{Name: "type", Type: field.TypeString, Nullable: true},
		{Name: "quantity", Type: field.TypeInt, Default: 0},
		{Name: "sku", Type: field.TypeString, Nullable: true},
		{Name: "attributes", Type: field.TypeJSON, Nullable: true},
		{Name: "categories", Type: field.TypeJSON, Nullable: true},
		{Name: "image", Type: field.TypeJSON, Nullable: true},
		{Name: "revision", Type: field.TypeJSON, Nullable: true},
		{Name: "collection", Type: field.TypeJSON, Nullable: true},
		{Name: "unit_id", Type: field.TypeInt, Nullable: true},
		{Name: "group_id", Type: field.TypeInt, Nullable: true},
		{Name: "category_id", Type: field.TypeInt, Nullable: true},
	}
	// ProductTable holds the schema information for the "product" table.
	ProductTable = &schema.Table{
		Name:       "product",
		Columns:    ProductColumns,
		PrimaryKey: []*schema.Column{ProductColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "product_term_unit",
				Columns:    []*schema.Column{ProductColumns[18]},
				RefColumns: []*schema.Column{TermColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "product_term_group",
				Columns:    []*schema.Column{ProductColumns[19]},
				RefColumns: []*schema.Column{TermColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "product_term_category",
				Columns:    []*schema.Column{ProductColumns[20]},
				RefColumns: []*schema.Column{TermColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
		Indexes: []*schema.Index{
			{
				Name:    "product_name_code_status",
				Unique:  true,
				Columns: []*schema.Column{ProductColumns[6], ProductColumns[7], ProductColumns[2]},
			},
		},
	}
	// ProductOperationColumns holds the columns for the "product_operation" table.
	ProductOperationColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "version", Type: field.TypeInt, Default: 1},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "order_sequence", Type: field.TypeInt, Nullable: true},
		{Name: "product_id", Type: field.TypeInt},
		{Name: "operation_id", Type: field.TypeInt},
	}
	// ProductOperationTable holds the schema information for the "product_operation" table.
	ProductOperationTable = &schema.Table{
		Name:       "product_operation",
		Columns:    ProductOperationColumns,
		PrimaryKey: []*schema.Column{ProductOperationColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "product_operation_product_product",
				Columns:    []*schema.Column{ProductOperationColumns[5]},
				RefColumns: []*schema.Column{ProductColumns[0]},
				OnDelete:   schema.NoAction,
			},
			{
				Symbol:     "product_operation_operation_operation",
				Columns:    []*schema.Column{ProductOperationColumns[6]},
				RefColumns: []*schema.Column{OperationColumns[0]},
				OnDelete:   schema.NoAction,
			},
		},
		Indexes: []*schema.Index{
			{
				Name:    "productoperation_product_id_operation_id",
				Unique:  true,
				Columns: []*schema.Column{ProductOperationColumns[5], ProductOperationColumns[6]},
			},
		},
	}
	// ReferralColumns holds the columns for the "referral" table.
	ReferralColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "deleted_at", Type: field.TypeTime, Nullable: true},
		{Name: "status", Type: field.TypeInt8, Default: 2},
		{Name: "version", Type: field.TypeInt, Default: 1},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "referred_person_id", Type: field.TypeInt, Unique: true},
		{Name: "notes", Type: field.TypeString, Nullable: true, Size: 2147483647},
		{Name: "entity_type", Type: field.TypeString, Nullable: true},
		{Name: "entity_id", Type: field.TypeInt, Nullable: true},
		{Name: "referrer_relationship", Type: field.TypeString, Nullable: true},
	}
	// ReferralTable holds the schema information for the "referral" table.
	ReferralTable = &schema.Table{
		Name:       "referral",
		Columns:    ReferralColumns,
		PrimaryKey: []*schema.Column{ReferralColumns[0]},
		Indexes: []*schema.Index{
			{
				Name:    "referral_referred_person_id",
				Unique:  false,
				Columns: []*schema.Column{ReferralColumns[6]},
			},
			{
				Name:    "referral_entity_type",
				Unique:  false,
				Columns: []*schema.Column{ReferralColumns[8]},
			},
			{
				Name:    "referral_entity_id",
				Unique:  false,
				Columns: []*schema.Column{ReferralColumns[9]},
			},
		},
	}
	// ScheduleColumns holds the columns for the "schedule" table.
	ScheduleColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "deleted_at", Type: field.TypeTime, Nullable: true},
		{Name: "status", Type: field.TypeInt8, Default: 2},
		{Name: "version", Type: field.TypeInt, Default: 1},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "start_time", Type: field.TypeTime, Nullable: true},
		{Name: "end_time", Type: field.TypeTime, Nullable: true},
		{Name: "stage", Type: field.TypeInt, Default: 0},
		{Name: "user_id", Type: field.TypeInt, Nullable: true},
	}
	// ScheduleTable holds the schema information for the "schedule" table.
	ScheduleTable = &schema.Table{
		Name:       "schedule",
		Columns:    ScheduleColumns,
		PrimaryKey: []*schema.Column{ScheduleColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "schedule_user_user",
				Columns:    []*schema.Column{ScheduleColumns[9]},
				RefColumns: []*schema.Column{UserColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
		Indexes: []*schema.Index{
			{
				Name:    "schedule_user_id",
				Unique:  false,
				Columns: []*schema.Column{ScheduleColumns[9]},
			},
		},
	}
	// SettingColumns holds the columns for the "setting" table.
	SettingColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "deleted_at", Type: field.TypeTime, Nullable: true},
		{Name: "status", Type: field.TypeInt8, Default: 2},
		{Name: "version", Type: field.TypeInt, Default: 1},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "category", Type: field.TypeString},
		{Name: "name", Type: field.TypeString},
		{Name: "value", Type: field.TypeJSON, Nullable: true},
		{Name: "description", Type: field.TypeString, Nullable: true},
	}
	// SettingTable holds the schema information for the "setting" table.
	SettingTable = &schema.Table{
		Name:       "setting",
		Columns:    SettingColumns,
		PrimaryKey: []*schema.Column{SettingColumns[0]},
	}
	// StageColumns holds the columns for the "stage" table.
	StageColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "deleted_at", Type: field.TypeTime, Nullable: true},
		{Name: "status", Type: field.TypeInt8, Default: 2},
		{Name: "version", Type: field.TypeInt, Default: 1},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "name", Type: field.TypeString},
		{Name: "order_number", Type: field.TypeInt, Nullable: true},
		{Name: "meta", Type: field.TypeString, Nullable: true},
		{Name: "parent_stage_id", Type: field.TypeInt, Nullable: true},
		{Name: "pipeline_id", Type: field.TypeInt, Nullable: true},
	}
	// StageTable holds the schema information for the "stage" table.
	StageTable = &schema.Table{
		Name:       "stage",
		Columns:    StageColumns,
		PrimaryKey: []*schema.Column{StageColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "stage_stage_children",
				Columns:    []*schema.Column{StageColumns[9]},
				RefColumns: []*schema.Column{StageColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "stage_pipeline_pipeline",
				Columns:    []*schema.Column{StageColumns[10]},
				RefColumns: []*schema.Column{PipelineColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
	}
	// TaskColumns holds the columns for the "task" table.
	TaskColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "deleted_at", Type: field.TypeTime, Nullable: true},
		{Name: "status", Type: field.TypeInt8, Default: 2},
		{Name: "version", Type: field.TypeInt, Default: 1},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "title", Type: field.TypeString},
		{Name: "note", Type: field.TypeString, Nullable: true, Size: 2147483647},
		{Name: "start_date", Type: field.TypeTime, Nullable: true},
		{Name: "due_date", Type: field.TypeTime, Nullable: true},
		{Name: "end_date", Type: field.TypeTime, Nullable: true},
		{Name: "type", Type: field.TypeString, Nullable: true},
		{Name: "priority", Type: field.TypeInt, Nullable: true},
		{Name: "current_serial", Type: field.TypeInt, Default: 1},
		{Name: "department_id", Type: field.TypeInt, Nullable: true},
		{Name: "state", Type: field.TypeEnum, Nullable: true, Enums: []string{"new_task", "in_progress", "overdue", "awaiting_approval", "completed", "cancelled", "cancelled_in_progress", "completed_early"}},
		{Name: "completed_at", Type: field.TypeTime, Nullable: true},
		{Name: "history", Type: field.TypeJSON, Nullable: true},
		{Name: "parent_id", Type: field.TypeInt, Nullable: true},
		{Name: "person_id", Type: field.TypeInt, Nullable: true},
		{Name: "deal_id", Type: field.TypeInt, Nullable: true},
		{Name: "appointment_id", Type: field.TypeInt, Nullable: true},
		{Name: "creator_id", Type: field.TypeInt, Nullable: true},
	}
	// TaskTable holds the schema information for the "task" table.
	TaskTable = &schema.Table{
		Name:       "task",
		Columns:    TaskColumns,
		PrimaryKey: []*schema.Column{TaskColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "task_task_children",
				Columns:    []*schema.Column{TaskColumns[18]},
				RefColumns: []*schema.Column{TaskColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "task_person_person",
				Columns:    []*schema.Column{TaskColumns[19]},
				RefColumns: []*schema.Column{PersonColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "task_deal_deal",
				Columns:    []*schema.Column{TaskColumns[20]},
				RefColumns: []*schema.Column{DealColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "task_appointment_appointment",
				Columns:    []*schema.Column{TaskColumns[21]},
				RefColumns: []*schema.Column{AppointmentColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "task_user_creator",
				Columns:    []*schema.Column{TaskColumns[22]},
				RefColumns: []*schema.Column{UserColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
		Indexes: []*schema.Index{
			{
				Name:    "task_person_id",
				Unique:  false,
				Columns: []*schema.Column{TaskColumns[19]},
			},
			{
				Name:    "task_creator_id",
				Unique:  false,
				Columns: []*schema.Column{TaskColumns[22]},
			},
		},
	}
	// TaskAssignmentColumns holds the columns for the "task_assignment" table.
	TaskAssignmentColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "deleted_at", Type: field.TypeTime, Nullable: true},
		{Name: "status", Type: field.TypeInt8, Default: 2},
		{Name: "version", Type: field.TypeInt, Default: 1},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "serial", Type: field.TypeInt},
		{Name: "due_at", Type: field.TypeTime},
		{Name: "started_at", Type: field.TypeTime, Nullable: true},
		{Name: "completed_at", Type: field.TypeTime, Nullable: true},
		{Name: "state", Type: field.TypeEnum, Enums: []string{"new_task", "in_progress", "overdue", "awaiting_approval", "completed", "cancelled", "cancelled_in_progress", "completed_early"}, Default: "new_task"},
		{Name: "role", Type: field.TypeEnum, Nullable: true, Enums: []string{"primary", "contributor", "reviewer"}},
		{Name: "task_id", Type: field.TypeInt, Nullable: true},
		{Name: "user_id", Type: field.TypeInt, Nullable: true},
	}
	// TaskAssignmentTable holds the schema information for the "task_assignment" table.
	TaskAssignmentTable = &schema.Table{
		Name:       "task_assignment",
		Columns:    TaskAssignmentColumns,
		PrimaryKey: []*schema.Column{TaskAssignmentColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "task_assignment_task_task",
				Columns:    []*schema.Column{TaskAssignmentColumns[12]},
				RefColumns: []*schema.Column{TaskColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "task_assignment_user_user",
				Columns:    []*schema.Column{TaskAssignmentColumns[13]},
				RefColumns: []*schema.Column{UserColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
		Indexes: []*schema.Index{
			{
				Name:    "taskassignment_task_id",
				Unique:  false,
				Columns: []*schema.Column{TaskAssignmentColumns[12]},
			},
			{
				Name:    "taskassignment_user_id",
				Unique:  false,
				Columns: []*schema.Column{TaskAssignmentColumns[13]},
			},
		},
	}
	// TaskDepartmentColumns holds the columns for the "task_department" table.
	TaskDepartmentColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "deleted_at", Type: field.TypeTime, Nullable: true},
		{Name: "status", Type: field.TypeInt8, Default: 2},
		{Name: "version", Type: field.TypeInt, Default: 1},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "serial", Type: field.TypeInt},
		{Name: "due_at", Type: field.TypeTime},
		{Name: "started_at", Type: field.TypeTime, Nullable: true},
		{Name: "completed_at", Type: field.TypeTime, Nullable: true},
		{Name: "state", Type: field.TypeEnum, Enums: []string{"new_task", "in_progress", "overdue", "awaiting_approval", "completed", "cancelled", "cancelled_in_progress", "completed_early"}, Default: "new_task"},
		{Name: "role", Type: field.TypeEnum, Nullable: true, Enums: []string{"primary", "contributor", "reviewer"}},
		{Name: "task_id", Type: field.TypeInt, Nullable: true},
		{Name: "department_id", Type: field.TypeInt, Nullable: true},
		{Name: "complete_by", Type: field.TypeInt, Nullable: true},
	}
	// TaskDepartmentTable holds the schema information for the "task_department" table.
	TaskDepartmentTable = &schema.Table{
		Name:       "task_department",
		Columns:    TaskDepartmentColumns,
		PrimaryKey: []*schema.Column{TaskDepartmentColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "task_department_task_task",
				Columns:    []*schema.Column{TaskDepartmentColumns[12]},
				RefColumns: []*schema.Column{TaskColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "task_department_department_department",
				Columns:    []*schema.Column{TaskDepartmentColumns[13]},
				RefColumns: []*schema.Column{DepartmentColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "task_department_user_completer",
				Columns:    []*schema.Column{TaskDepartmentColumns[14]},
				RefColumns: []*schema.Column{UserColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
		Indexes: []*schema.Index{
			{
				Name:    "taskdepartment_task_id",
				Unique:  false,
				Columns: []*schema.Column{TaskDepartmentColumns[12]},
			},
			{
				Name:    "taskdepartment_department_id",
				Unique:  false,
				Columns: []*schema.Column{TaskDepartmentColumns[13]},
			},
			{
				Name:    "taskdepartment_complete_by",
				Unique:  false,
				Columns: []*schema.Column{TaskDepartmentColumns[14]},
			},
		},
	}
	// TaskNoteColumns holds the columns for the "task_note" table.
	TaskNoteColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "deleted_at", Type: field.TypeTime, Nullable: true},
		{Name: "status", Type: field.TypeInt8, Default: 2},
		{Name: "version", Type: field.TypeInt, Default: 1},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "body", Type: field.TypeString, Nullable: true, Size: 2147483647},
		{Name: "user_id", Type: field.TypeInt, Nullable: true},
		{Name: "task_id", Type: field.TypeInt, Nullable: true},
	}
	// TaskNoteTable holds the schema information for the "task_note" table.
	TaskNoteTable = &schema.Table{
		Name:       "task_note",
		Columns:    TaskNoteColumns,
		PrimaryKey: []*schema.Column{TaskNoteColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "task_note_user_creator",
				Columns:    []*schema.Column{TaskNoteColumns[7]},
				RefColumns: []*schema.Column{UserColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "task_note_task_task",
				Columns:    []*schema.Column{TaskNoteColumns[8]},
				RefColumns: []*schema.Column{TaskColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
		Indexes: []*schema.Index{
			{
				Name:    "tasknote_task_id",
				Unique:  false,
				Columns: []*schema.Column{TaskNoteColumns[8]},
			},
			{
				Name:    "tasknote_user_id",
				Unique:  false,
				Columns: []*schema.Column{TaskNoteColumns[7]},
			},
		},
	}
	// TaskRecurringColumns holds the columns for the "task_recurring" table.
	TaskRecurringColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "cron_expression", Type: field.TypeString},
		{Name: "next_occurrence", Type: field.TypeTime, Nullable: true},
		{Name: "last_occurrence", Type: field.TypeTime, Nullable: true},
		{Name: "task_id", Type: field.TypeInt, Unique: true},
	}
	// TaskRecurringTable holds the schema information for the "task_recurring" table.
	TaskRecurringTable = &schema.Table{
		Name:       "task_recurring",
		Columns:    TaskRecurringColumns,
		PrimaryKey: []*schema.Column{TaskRecurringColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "task_recurring_task_recurring",
				Columns:    []*schema.Column{TaskRecurringColumns[6]},
				RefColumns: []*schema.Column{TaskColumns[0]},
				OnDelete:   schema.NoAction,
			},
		},
		Indexes: []*schema.Index{
			{
				Name:    "taskrecurring_next_occurrence",
				Unique:  false,
				Columns: []*schema.Column{TaskRecurringColumns[4]},
			},
			{
				Name:    "taskrecurring_task_id",
				Unique:  false,
				Columns: []*schema.Column{TaskRecurringColumns[6]},
			},
		},
	}
	// TermColumns holds the columns for the "term" table.
	TermColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "deleted_at", Type: field.TypeTime, Nullable: true},
		{Name: "status", Type: field.TypeInt8, Default: 2},
		{Name: "version", Type: field.TypeInt, Default: 1},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "name", Type: field.TypeString},
		{Name: "bundle", Type: field.TypeString},
		{Name: "description", Type: field.TypeString, Nullable: true, Size: 2147483647},
		{Name: "body", Type: field.TypeString, Nullable: true, Size: 2147483647},
		{Name: "weight", Type: field.TypeInt, Default: 0},
	}
	// TermTable holds the schema information for the "term" table.
	TermTable = &schema.Table{
		Name:       "term",
		Columns:    TermColumns,
		PrimaryKey: []*schema.Column{TermColumns[0]},
		Indexes: []*schema.Index{
			{
				Name:    "term_bundle",
				Unique:  false,
				Columns: []*schema.Column{TermColumns[7]},
			},
		},
	}
	// TrackColumns holds the columns for the "track" table.
	TrackColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "deleted_at", Type: field.TypeTime, Nullable: true},
		{Name: "status", Type: field.TypeInt8, Default: 2},
		{Name: "version", Type: field.TypeInt, Default: 1},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "begin", Type: field.TypeTime, Nullable: true},
		{Name: "end", Type: field.TypeTime, Nullable: true},
		{Name: "weight", Type: field.TypeFloat64, Nullable: true},
		{Name: "meta", Type: field.TypeJSON, Nullable: true},
		{Name: "stage_history", Type: field.TypeJSON, Nullable: true},
		{Name: "deal_id", Type: field.TypeInt, Nullable: true},
		{Name: "user_id", Type: field.TypeInt, Nullable: true},
		{Name: "person_id", Type: field.TypeInt, Nullable: true},
		{Name: "pipeline_id", Type: field.TypeInt, Nullable: true},
		{Name: "stage_id", Type: field.TypeInt, Nullable: true},
	}
	// TrackTable holds the schema information for the "track" table.
	TrackTable = &schema.Table{
		Name:       "track",
		Columns:    TrackColumns,
		PrimaryKey: []*schema.Column{TrackColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "track_deal_deal",
				Columns:    []*schema.Column{TrackColumns[11]},
				RefColumns: []*schema.Column{DealColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "track_user_user",
				Columns:    []*schema.Column{TrackColumns[12]},
				RefColumns: []*schema.Column{UserColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "track_person_person",
				Columns:    []*schema.Column{TrackColumns[13]},
				RefColumns: []*schema.Column{PersonColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "track_pipeline_pipeline",
				Columns:    []*schema.Column{TrackColumns[14]},
				RefColumns: []*schema.Column{PipelineColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "track_stage_stage",
				Columns:    []*schema.Column{TrackColumns[15]},
				RefColumns: []*schema.Column{StageColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
		Indexes: []*schema.Index{
			{
				Name:    "track_deal_id_pipeline_id",
				Unique:  true,
				Columns: []*schema.Column{TrackColumns[11], TrackColumns[14]},
				Annotation: &entsql.IndexAnnotation{
					Where: "\"end\" IS NULL",
				},
			},
			{
				Name:    "track_user_id",
				Unique:  false,
				Columns: []*schema.Column{TrackColumns[12]},
			},
			{
				Name:    "track_deal_id",
				Unique:  false,
				Columns: []*schema.Column{TrackColumns[11]},
			},
			{
				Name:    "track_person_id",
				Unique:  false,
				Columns: []*schema.Column{TrackColumns[13]},
			},
			{
				Name:    "track_pipeline_id",
				Unique:  false,
				Columns: []*schema.Column{TrackColumns[14]},
			},
		},
	}
	// UserColumns holds the columns for the "user" table.
	UserColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "deleted_at", Type: field.TypeTime, Nullable: true},
		{Name: "status", Type: field.TypeInt8, Default: 2},
		{Name: "version", Type: field.TypeInt, Default: 1},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "username", Type: field.TypeString, Unique: true},
		{Name: "password", Type: field.TypeString},
		{Name: "phone", Type: field.TypeString, Nullable: true},
		{Name: "email", Type: field.TypeString, Nullable: true},
		{Name: "email_confirmed", Type: field.TypeBool, Nullable: true},
		{Name: "name", Type: field.TypeString, Nullable: true},
		{Name: "gender", Type: field.TypeString},
		{Name: "department_id", Type: field.TypeInt, Nullable: true},
		{Name: "department_position", Type: field.TypeString, Nullable: true},
		{Name: "profile_image", Type: field.TypeString, Nullable: true},
		{Name: "suspended_at", Type: field.TypeTime, Nullable: true},
		{Name: "state", Type: field.TypeEnum, Enums: []string{"online", "offline", "inactive", "busy"}, Default: "offline"},
	}
	// UserTable holds the schema information for the "user" table.
	UserTable = &schema.Table{
		Name:       "user",
		Columns:    UserColumns,
		PrimaryKey: []*schema.Column{UserColumns[0]},
		Indexes: []*schema.Index{
			{
				Name:    "user_deleted_at_status",
				Unique:  false,
				Columns: []*schema.Column{UserColumns[1], UserColumns[2]},
			},
			{
				Name:    "user_username_status",
				Unique:  true,
				Columns: []*schema.Column{UserColumns[6], UserColumns[2]},
			},
		},
	}
	// UserDataColumns holds the columns for the "user_data" table.
	UserDataColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "kind", Type: field.TypeString},
		{Name: "data", Type: field.TypeJSON, Nullable: true},
		{Name: "user_id", Type: field.TypeInt},
	}
	// UserDataTable holds the schema information for the "user_data" table.
	UserDataTable = &schema.Table{
		Name:       "user_data",
		Columns:    UserDataColumns,
		PrimaryKey: []*schema.Column{UserDataColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "user_data_user_user",
				Columns:    []*schema.Column{UserDataColumns[5]},
				RefColumns: []*schema.Column{UserColumns[0]},
				OnDelete:   schema.NoAction,
			},
		},
		Indexes: []*schema.Index{
			{
				Name:    "userdata_user_id_kind",
				Unique:  true,
				Columns: []*schema.Column{UserDataColumns[5], UserDataColumns[3]},
			},
		},
	}
	// Tables holds all the tables in the schema.
	Tables = []*schema.Table{
		AppointmentTable,
		AttachmentTable,
		AttachmentDataTable,
		BillTable,
		BillDataTable,
		BillItemTable,
		BundleTable,
		CallTable,
		CasbinRuleTable,
		DealTable,
		DealUserTable,
		DepartmentTable,
		DepositsTable,
		DepositAllocationsTable,
		DepositPaymentsTable,
		DiscountTable,
		DiscountUsageTable,
		FileTable,
		FileUsageTable,
		InstallmentTable,
		InstallmentPlanTable,
		IssueTable,
		IssueCommentTable,
		LocalDistrictTable,
		LocalProvinceTable,
		LocalWardTable,
		MessageHistoryTable,
		NoteTable,
		OtpTable,
		OperationTable,
		OrganizationTable,
		PaymentTable,
		PaymentAllocationTable,
		PersonTable,
		PersonAssignmentTable,
		PersonDataTable,
		PersonReferralTable,
		PersonStageTable,
		PipelineTable,
		ProductTable,
		ProductOperationTable,
		ReferralTable,
		ScheduleTable,
		SettingTable,
		StageTable,
		TaskTable,
		TaskAssignmentTable,
		TaskDepartmentTable,
		TaskNoteTable,
		TaskRecurringTable,
		TermTable,
		TrackTable,
		UserTable,
		UserDataTable,
	}
)

func init() {
	AppointmentTable.ForeignKeys[0].RefTable = UserTable
	AppointmentTable.ForeignKeys[1].RefTable = PersonTable
	AppointmentTable.ForeignKeys[2].RefTable = UserTable
	AppointmentTable.ForeignKeys[3].RefTable = TrackTable
	AppointmentTable.Annotation = &entsql.Annotation{
		Table: "appointment",
	}
	AttachmentTable.ForeignKeys[0].RefTable = AttachmentTable
	AttachmentTable.ForeignKeys[1].RefTable = DealTable
	AttachmentTable.ForeignKeys[2].RefTable = PersonTable
	AttachmentTable.ForeignKeys[3].RefTable = InstallmentPlanTable
	AttachmentTable.ForeignKeys[4].RefTable = UserTable
	AttachmentTable.ForeignKeys[5].RefTable = ProductTable
	AttachmentTable.ForeignKeys[6].RefTable = TrackTable
	AttachmentTable.Annotation = &entsql.Annotation{
		Table: "attachment",
	}
	AttachmentDataTable.ForeignKeys[0].RefTable = UserTable
	AttachmentDataTable.ForeignKeys[1].RefTable = UserTable
	AttachmentDataTable.ForeignKeys[2].RefTable = AttachmentTable
	AttachmentDataTable.Annotation = &entsql.Annotation{
		Table: "attachment_data",
	}
	BillTable.ForeignKeys[0].RefTable = DealTable
	BillTable.ForeignKeys[1].RefTable = PersonTable
	BillTable.ForeignKeys[2].RefTable = UserTable
	BillTable.ForeignKeys[3].RefTable = TrackTable
	BillTable.Annotation = &entsql.Annotation{
		Table: "bill",
	}
	BillDataTable.ForeignKeys[0].RefTable = UserTable
	BillDataTable.ForeignKeys[1].RefTable = BillTable
	BillDataTable.Annotation = &entsql.Annotation{
		Table: "bill_data",
	}
	BillItemTable.ForeignKeys[0].RefTable = AttachmentTable
	BillItemTable.ForeignKeys[1].RefTable = InstallmentTable
	BillItemTable.ForeignKeys[2].RefTable = BillTable
	BillItemTable.ForeignKeys[3].RefTable = UserTable
	BillItemTable.ForeignKeys[4].RefTable = TrackTable
	BillItemTable.Annotation = &entsql.Annotation{
		Table: "bill_item",
	}
	BundleTable.Annotation = &entsql.Annotation{
		Table: "bundle",
	}
	CallTable.ForeignKeys[0].RefTable = PersonTable
	CallTable.ForeignKeys[1].RefTable = UserTable
	CallTable.Annotation = &entsql.Annotation{
		Table: "call",
	}
	CasbinRuleTable.Annotation = &entsql.Annotation{
		Table: "casbin_rule",
	}
	DealTable.ForeignKeys[0].RefTable = PersonTable
	DealTable.ForeignKeys[1].RefTable = StageTable
	DealTable.Annotation = &entsql.Annotation{
		Table: "deal",
	}
	DealUserTable.ForeignKeys[0].RefTable = DealTable
	DealUserTable.ForeignKeys[1].RefTable = UserTable
	DealUserTable.Annotation = &entsql.Annotation{
		Table: "deal_user",
	}
	DepartmentTable.Annotation = &entsql.Annotation{
		Table: "department",
	}
	DepositsTable.ForeignKeys[0].RefTable = DealTable
	DepositsTable.Annotation = &entsql.Annotation{
		Table: "deposits",
	}
	DepositAllocationsTable.ForeignKeys[0].RefTable = DepositsTable
	DepositAllocationsTable.ForeignKeys[1].RefTable = AttachmentTable
	DepositAllocationsTable.Annotation = &entsql.Annotation{
		Table: "deposit_allocations",
	}
	DepositPaymentsTable.ForeignKeys[0].RefTable = DepositsTable
	DepositPaymentsTable.ForeignKeys[1].RefTable = PaymentTable
	DepositPaymentsTable.Annotation = &entsql.Annotation{
		Table: "deposit_payments",
	}
	DiscountTable.Annotation = &entsql.Annotation{
		Table: "discount",
	}
	DiscountUsageTable.ForeignKeys[0].RefTable = DiscountTable
	DiscountUsageTable.ForeignKeys[1].RefTable = DealTable
	DiscountUsageTable.Annotation = &entsql.Annotation{
		Table: "discount_usage",
	}
	FileTable.Annotation = &entsql.Annotation{
		Table: "file",
	}
	FileUsageTable.ForeignKeys[0].RefTable = FileTable
	FileUsageTable.ForeignKeys[1].RefTable = TrackTable
	FileUsageTable.Annotation = &entsql.Annotation{
		Table: "file_usage",
	}
	InstallmentTable.ForeignKeys[0].RefTable = InstallmentPlanTable
	InstallmentTable.ForeignKeys[1].RefTable = UserTable
	InstallmentTable.Annotation = &entsql.Annotation{
		Table: "installment",
	}
	InstallmentPlanTable.ForeignKeys[0].RefTable = PersonTable
	InstallmentPlanTable.ForeignKeys[1].RefTable = DealTable
	InstallmentPlanTable.ForeignKeys[2].RefTable = UserTable
	InstallmentPlanTable.ForeignKeys[3].RefTable = BillTable
	InstallmentPlanTable.Annotation = &entsql.Annotation{
		Table: "installment_plan",
	}
	IssueTable.ForeignKeys[0].RefTable = PersonTable
	IssueTable.Annotation = &entsql.Annotation{
		Table: "issue",
	}
	IssueCommentTable.ForeignKeys[0].RefTable = UserTable
	IssueCommentTable.ForeignKeys[1].RefTable = IssueTable
	IssueCommentTable.Annotation = &entsql.Annotation{
		Table: "issue_comment",
	}
	LocalDistrictTable.Annotation = &entsql.Annotation{
		Table: "local_district",
	}
	LocalProvinceTable.Annotation = &entsql.Annotation{
		Table: "local_province",
	}
	LocalWardTable.Annotation = &entsql.Annotation{
		Table: "local_ward",
	}
	MessageHistoryTable.ForeignKeys[0].RefTable = PersonTable
	MessageHistoryTable.ForeignKeys[1].RefTable = UserTable
	MessageHistoryTable.Annotation = &entsql.Annotation{
		Table: "message_history",
	}
	NoteTable.ForeignKeys[0].RefTable = UserTable
	NoteTable.ForeignKeys[1].RefTable = PersonTable
	NoteTable.ForeignKeys[2].RefTable = DealTable
	NoteTable.Annotation = &entsql.Annotation{
		Table: "note",
	}
	OtpTable.Annotation = &entsql.Annotation{
		Table: "otp",
	}
	OperationTable.Annotation = &entsql.Annotation{
		Table: "operation",
	}
	OrganizationTable.Annotation = &entsql.Annotation{
		Table: "organization",
	}
	PaymentTable.ForeignKeys[0].RefTable = BillTable
	PaymentTable.Annotation = &entsql.Annotation{
		Table: "payment",
	}
	PaymentAllocationTable.ForeignKeys[0].RefTable = PaymentTable
	PaymentAllocationTable.ForeignKeys[1].RefTable = BillItemTable
	PaymentAllocationTable.ForeignKeys[2].RefTable = InstallmentTable
	PaymentAllocationTable.Annotation = &entsql.Annotation{
		Table: "payment_allocation",
	}
	PersonTable.ForeignKeys[0].RefTable = TermTable
	PersonTable.ForeignKeys[1].RefTable = TermTable
	PersonTable.ForeignKeys[2].RefTable = UserTable
	PersonTable.Annotation = &entsql.Annotation{
		Table: "person",
	}
	PersonAssignmentTable.ForeignKeys[0].RefTable = PersonTable
	PersonAssignmentTable.ForeignKeys[1].RefTable = UserTable
	PersonAssignmentTable.Annotation = &entsql.Annotation{
		Table: "person_assignment",
	}
	PersonDataTable.ForeignKeys[0].RefTable = PersonTable
	PersonDataTable.Annotation = &entsql.Annotation{
		Table: "person_data",
	}
	PersonReferralTable.ForeignKeys[0].RefTable = PersonTable
	PersonReferralTable.Annotation = &entsql.Annotation{
		Table: "person_referral",
	}
	PersonStageTable.ForeignKeys[0].RefTable = UserTable
	PersonStageTable.ForeignKeys[1].RefTable = PersonTable
	PersonStageTable.ForeignKeys[2].RefTable = StageTable
	PersonStageTable.Annotation = &entsql.Annotation{
		Table: "person_stage",
	}
	PipelineTable.Annotation = &entsql.Annotation{
		Table: "pipeline",
	}
	ProductTable.ForeignKeys[0].RefTable = TermTable
	ProductTable.ForeignKeys[1].RefTable = TermTable
	ProductTable.ForeignKeys[2].RefTable = TermTable
	ProductTable.Annotation = &entsql.Annotation{
		Table: "product",
	}
	ProductOperationTable.ForeignKeys[0].RefTable = ProductTable
	ProductOperationTable.ForeignKeys[1].RefTable = OperationTable
	ProductOperationTable.Annotation = &entsql.Annotation{
		Table: "product_operation",
	}
	ReferralTable.Annotation = &entsql.Annotation{
		Table: "referral",
	}
	ScheduleTable.ForeignKeys[0].RefTable = UserTable
	ScheduleTable.Annotation = &entsql.Annotation{
		Table: "schedule",
	}
	SettingTable.Annotation = &entsql.Annotation{
		Table: "setting",
	}
	StageTable.ForeignKeys[0].RefTable = StageTable
	StageTable.ForeignKeys[1].RefTable = PipelineTable
	StageTable.Annotation = &entsql.Annotation{
		Table: "stage",
	}
	TaskTable.ForeignKeys[0].RefTable = TaskTable
	TaskTable.ForeignKeys[1].RefTable = PersonTable
	TaskTable.ForeignKeys[2].RefTable = DealTable
	TaskTable.ForeignKeys[3].RefTable = AppointmentTable
	TaskTable.ForeignKeys[4].RefTable = UserTable
	TaskTable.Annotation = &entsql.Annotation{
		Table: "task",
	}
	TaskAssignmentTable.ForeignKeys[0].RefTable = TaskTable
	TaskAssignmentTable.ForeignKeys[1].RefTable = UserTable
	TaskAssignmentTable.Annotation = &entsql.Annotation{
		Table: "task_assignment",
	}
	TaskDepartmentTable.ForeignKeys[0].RefTable = TaskTable
	TaskDepartmentTable.ForeignKeys[1].RefTable = DepartmentTable
	TaskDepartmentTable.ForeignKeys[2].RefTable = UserTable
	TaskDepartmentTable.Annotation = &entsql.Annotation{
		Table: "task_department",
	}
	TaskNoteTable.ForeignKeys[0].RefTable = UserTable
	TaskNoteTable.ForeignKeys[1].RefTable = TaskTable
	TaskNoteTable.Annotation = &entsql.Annotation{
		Table: "task_note",
	}
	TaskRecurringTable.ForeignKeys[0].RefTable = TaskTable
	TaskRecurringTable.Annotation = &entsql.Annotation{
		Table: "task_recurring",
	}
	TermTable.Annotation = &entsql.Annotation{
		Table: "term",
	}
	TrackTable.ForeignKeys[0].RefTable = DealTable
	TrackTable.ForeignKeys[1].RefTable = UserTable
	TrackTable.ForeignKeys[2].RefTable = PersonTable
	TrackTable.ForeignKeys[3].RefTable = PipelineTable
	TrackTable.ForeignKeys[4].RefTable = StageTable
	TrackTable.Annotation = &entsql.Annotation{
		Table: "track",
	}
	UserTable.Annotation = &entsql.Annotation{
		Table: "user",
	}
	UserDataTable.ForeignKeys[0].RefTable = UserTable
	UserDataTable.Annotation = &entsql.Annotation{
		Table: "user_data",
	}
}
