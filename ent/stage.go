// Code generated by ent, DO NOT EDIT.

package ent

import (
	"bcare/ent/pipeline"
	"bcare/ent/stage"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

// Stage is the model entity for the Stage schema.
type Stage struct {
	config `json:"-"`
	// ID of the ent.
	ID int `json:"id,omitempty"`
	// DeletedAt holds the value of the "deleted_at" field.
	DeletedAt time.Time `json:"deleted_at,omitempty"`
	// Status holds the value of the "status" field.
	Status int8 `json:"status,omitempty"`
	// Version holds the value of the "version" field.
	Version int `json:"version,omitempty"`
	// CreatedAt holds the value of the "created_at" field.
	CreatedAt time.Time `json:"created_at,omitempty"`
	// UpdatedAt holds the value of the "updated_at" field.
	UpdatedAt time.Time `json:"updated_at,omitempty"`
	// Name holds the value of the "name" field.
	Name string `json:"name,omitempty"`
	// PipelineID holds the value of the "pipeline_id" field.
	PipelineID int `json:"pipeline_id,omitempty"`
	// OrderNumber holds the value of the "order_number" field.
	OrderNumber int `json:"order_number,omitempty"`
	// ParentStageID holds the value of the "parent_stage_id" field.
	ParentStageID *int `json:"parent_stage_id,omitempty"`
	// Meta holds the value of the "meta" field.
	Meta string `json:"meta,omitempty"`
	// Edges holds the relations/edges for other nodes in the graph.
	// The values are being populated by the StageQuery when eager-loading is set.
	Edges        StageEdges `json:"-"`
	selectValues sql.SelectValues
}

// StageEdges holds the relations/edges for other nodes in the graph.
type StageEdges struct {
	// Parent holds the value of the parent edge.
	Parent *Stage `json:"parent,omitempty"`
	// Children holds the value of the children edge.
	Children []*Stage `json:"children,omitempty"`
	// Deals holds the value of the deals edge.
	Deals []*Deal `json:"deals,omitempty"`
	// Pipeline holds the value of the pipeline edge.
	Pipeline *Pipeline `json:"pipeline,omitempty"`
	// loadedTypes holds the information for reporting if a
	// type was loaded (or requested) in eager-loading or not.
	loadedTypes [4]bool
}

// ParentOrErr returns the Parent value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e StageEdges) ParentOrErr() (*Stage, error) {
	if e.Parent != nil {
		return e.Parent, nil
	} else if e.loadedTypes[0] {
		return nil, &NotFoundError{label: stage.Label}
	}
	return nil, &NotLoadedError{edge: "parent"}
}

// ChildrenOrErr returns the Children value or an error if the edge
// was not loaded in eager-loading.
func (e StageEdges) ChildrenOrErr() ([]*Stage, error) {
	if e.loadedTypes[1] {
		return e.Children, nil
	}
	return nil, &NotLoadedError{edge: "children"}
}

// DealsOrErr returns the Deals value or an error if the edge
// was not loaded in eager-loading.
func (e StageEdges) DealsOrErr() ([]*Deal, error) {
	if e.loadedTypes[2] {
		return e.Deals, nil
	}
	return nil, &NotLoadedError{edge: "deals"}
}

// PipelineOrErr returns the Pipeline value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e StageEdges) PipelineOrErr() (*Pipeline, error) {
	if e.Pipeline != nil {
		return e.Pipeline, nil
	} else if e.loadedTypes[3] {
		return nil, &NotFoundError{label: pipeline.Label}
	}
	return nil, &NotLoadedError{edge: "pipeline"}
}

// scanValues returns the types for scanning values from sql.Rows.
func (*Stage) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case stage.FieldID, stage.FieldStatus, stage.FieldVersion, stage.FieldPipelineID, stage.FieldOrderNumber, stage.FieldParentStageID:
			values[i] = new(sql.NullInt64)
		case stage.FieldName, stage.FieldMeta:
			values[i] = new(sql.NullString)
		case stage.FieldDeletedAt, stage.FieldCreatedAt, stage.FieldUpdatedAt:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the Stage fields.
func (s *Stage) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case stage.FieldID:
			value, ok := values[i].(*sql.NullInt64)
			if !ok {
				return fmt.Errorf("unexpected type %T for field id", value)
			}
			s.ID = int(value.Int64)
		case stage.FieldDeletedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field deleted_at", values[i])
			} else if value.Valid {
				s.DeletedAt = value.Time
			}
		case stage.FieldStatus:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field status", values[i])
			} else if value.Valid {
				s.Status = int8(value.Int64)
			}
		case stage.FieldVersion:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field version", values[i])
			} else if value.Valid {
				s.Version = int(value.Int64)
			}
		case stage.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				s.CreatedAt = value.Time
			}
		case stage.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				s.UpdatedAt = value.Time
			}
		case stage.FieldName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field name", values[i])
			} else if value.Valid {
				s.Name = value.String
			}
		case stage.FieldPipelineID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field pipeline_id", values[i])
			} else if value.Valid {
				s.PipelineID = int(value.Int64)
			}
		case stage.FieldOrderNumber:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field order_number", values[i])
			} else if value.Valid {
				s.OrderNumber = int(value.Int64)
			}
		case stage.FieldParentStageID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field parent_stage_id", values[i])
			} else if value.Valid {
				s.ParentStageID = new(int)
				*s.ParentStageID = int(value.Int64)
			}
		case stage.FieldMeta:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field meta", values[i])
			} else if value.Valid {
				s.Meta = value.String
			}
		default:
			s.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the Stage.
// This includes values selected through modifiers, order, etc.
func (s *Stage) Value(name string) (ent.Value, error) {
	return s.selectValues.Get(name)
}

// QueryParent queries the "parent" edge of the Stage entity.
func (s *Stage) QueryParent() *StageQuery {
	return NewStageClient(s.config).QueryParent(s)
}

// QueryChildren queries the "children" edge of the Stage entity.
func (s *Stage) QueryChildren() *StageQuery {
	return NewStageClient(s.config).QueryChildren(s)
}

// QueryDeals queries the "deals" edge of the Stage entity.
func (s *Stage) QueryDeals() *DealQuery {
	return NewStageClient(s.config).QueryDeals(s)
}

// QueryPipeline queries the "pipeline" edge of the Stage entity.
func (s *Stage) QueryPipeline() *PipelineQuery {
	return NewStageClient(s.config).QueryPipeline(s)
}

// Update returns a builder for updating this Stage.
// Note that you need to call Stage.Unwrap() before calling this method if this Stage
// was returned from a transaction, and the transaction was committed or rolled back.
func (s *Stage) Update() *StageUpdateOne {
	return NewStageClient(s.config).UpdateOne(s)
}

// Unwrap unwraps the Stage entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (s *Stage) Unwrap() *Stage {
	_tx, ok := s.config.driver.(*txDriver)
	if !ok {
		panic("ent: Stage is not a transactional entity")
	}
	s.config.driver = _tx.drv
	return s
}

// String implements the fmt.Stringer.
func (s *Stage) String() string {
	var builder strings.Builder
	builder.WriteString("Stage(")
	builder.WriteString(fmt.Sprintf("id=%v, ", s.ID))
	builder.WriteString("deleted_at=")
	builder.WriteString(s.DeletedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("status=")
	builder.WriteString(fmt.Sprintf("%v", s.Status))
	builder.WriteString(", ")
	builder.WriteString("version=")
	builder.WriteString(fmt.Sprintf("%v", s.Version))
	builder.WriteString(", ")
	builder.WriteString("created_at=")
	builder.WriteString(s.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(s.UpdatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("name=")
	builder.WriteString(s.Name)
	builder.WriteString(", ")
	builder.WriteString("pipeline_id=")
	builder.WriteString(fmt.Sprintf("%v", s.PipelineID))
	builder.WriteString(", ")
	builder.WriteString("order_number=")
	builder.WriteString(fmt.Sprintf("%v", s.OrderNumber))
	builder.WriteString(", ")
	if v := s.ParentStageID; v != nil {
		builder.WriteString("parent_stage_id=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteString(", ")
	builder.WriteString("meta=")
	builder.WriteString(s.Meta)
	builder.WriteByte(')')
	return builder.String()
}

// MarshalJSON implements the json.Marshaler interface.
func (s *Stage) MarshalJSON() ([]byte, error) {
	type Alias Stage
	return json.Marshal(&struct {
		*Alias
		StageEdges
	}{
		Alias:      (*Alias)(s),
		StageEdges: s.Edges,
	})
}

// MarshalSimpleTime
func (s *Stage) MarshalSimpleTime() ([]byte, error) {
	type Alias Stage
	return json.Marshal(&struct {
		*Alias
		StageEdges
		DeletedAt string `json:"deleted_at,omitempty"`
		CreatedAt string `json:"created_at,omitempty"`
		UpdatedAt string `json:"updated_at,omitempty"`
	}{
		Alias:      (*Alias)(s),
		StageEdges: s.Edges,
		DeletedAt:  s.DeletedAt.Format("15:04 02/01/2006"),
		CreatedAt:  s.CreatedAt.Format("15:04 02/01/2006"),
		UpdatedAt:  s.UpdatedAt.Format("15:04 02/01/2006"),
	})
}

// Stages is a parsable slice of Stage.
type Stages []*Stage
