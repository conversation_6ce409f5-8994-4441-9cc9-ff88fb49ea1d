// Code generated by ent, DO NOT EDIT.

package ent

import (
	"bcare/ent/appointment"
	"bcare/ent/attachment"
	"bcare/ent/billitem"
	"bcare/ent/deal"
	"bcare/ent/fileusage"
	"bcare/ent/person"
	"bcare/ent/pipeline"
	"bcare/ent/predicate"
	"bcare/ent/stage"
	"bcare/ent/track"
	"bcare/ent/types"
	"bcare/ent/user"
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/dialect/sql/sqljson"
	"entgo.io/ent/schema/field"
)

// TrackUpdate is the builder for updating Track entities.
type TrackUpdate struct {
	config
	hooks     []Hook
	mutation  *TrackMutation
	modifiers []func(*sql.UpdateBuilder)
}

// Where appends a list predicates to the TrackUpdate builder.
func (tu *TrackUpdate) Where(ps ...predicate.Track) *TrackUpdate {
	tu.mutation.Where(ps...)
	return tu
}

// SetDeletedAt sets the "deleted_at" field.
func (tu *TrackUpdate) SetDeletedAt(t time.Time) *TrackUpdate {
	tu.mutation.SetDeletedAt(t)
	return tu
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (tu *TrackUpdate) SetNillableDeletedAt(t *time.Time) *TrackUpdate {
	if t != nil {
		tu.SetDeletedAt(*t)
	}
	return tu
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (tu *TrackUpdate) ClearDeletedAt() *TrackUpdate {
	tu.mutation.ClearDeletedAt()
	return tu
}

// SetStatus sets the "status" field.
func (tu *TrackUpdate) SetStatus(i int8) *TrackUpdate {
	tu.mutation.ResetStatus()
	tu.mutation.SetStatus(i)
	return tu
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (tu *TrackUpdate) SetNillableStatus(i *int8) *TrackUpdate {
	if i != nil {
		tu.SetStatus(*i)
	}
	return tu
}

// AddStatus adds i to the "status" field.
func (tu *TrackUpdate) AddStatus(i int8) *TrackUpdate {
	tu.mutation.AddStatus(i)
	return tu
}

// SetVersion sets the "version" field.
func (tu *TrackUpdate) SetVersion(i int) *TrackUpdate {
	tu.mutation.ResetVersion()
	tu.mutation.SetVersion(i)
	return tu
}

// SetNillableVersion sets the "version" field if the given value is not nil.
func (tu *TrackUpdate) SetNillableVersion(i *int) *TrackUpdate {
	if i != nil {
		tu.SetVersion(*i)
	}
	return tu
}

// AddVersion adds i to the "version" field.
func (tu *TrackUpdate) AddVersion(i int) *TrackUpdate {
	tu.mutation.AddVersion(i)
	return tu
}

// SetUpdatedAt sets the "updated_at" field.
func (tu *TrackUpdate) SetUpdatedAt(t time.Time) *TrackUpdate {
	tu.mutation.SetUpdatedAt(t)
	return tu
}

// SetBegin sets the "begin" field.
func (tu *TrackUpdate) SetBegin(t time.Time) *TrackUpdate {
	tu.mutation.SetBegin(t)
	return tu
}

// SetNillableBegin sets the "begin" field if the given value is not nil.
func (tu *TrackUpdate) SetNillableBegin(t *time.Time) *TrackUpdate {
	if t != nil {
		tu.SetBegin(*t)
	}
	return tu
}

// ClearBegin clears the value of the "begin" field.
func (tu *TrackUpdate) ClearBegin() *TrackUpdate {
	tu.mutation.ClearBegin()
	return tu
}

// SetEnd sets the "end" field.
func (tu *TrackUpdate) SetEnd(t time.Time) *TrackUpdate {
	tu.mutation.SetEnd(t)
	return tu
}

// SetNillableEnd sets the "end" field if the given value is not nil.
func (tu *TrackUpdate) SetNillableEnd(t *time.Time) *TrackUpdate {
	if t != nil {
		tu.SetEnd(*t)
	}
	return tu
}

// ClearEnd clears the value of the "end" field.
func (tu *TrackUpdate) ClearEnd() *TrackUpdate {
	tu.mutation.ClearEnd()
	return tu
}

// SetDealID sets the "deal_id" field.
func (tu *TrackUpdate) SetDealID(i int) *TrackUpdate {
	tu.mutation.SetDealID(i)
	return tu
}

// SetNillableDealID sets the "deal_id" field if the given value is not nil.
func (tu *TrackUpdate) SetNillableDealID(i *int) *TrackUpdate {
	if i != nil {
		tu.SetDealID(*i)
	}
	return tu
}

// ClearDealID clears the value of the "deal_id" field.
func (tu *TrackUpdate) ClearDealID() *TrackUpdate {
	tu.mutation.ClearDealID()
	return tu
}

// SetPipelineID sets the "pipeline_id" field.
func (tu *TrackUpdate) SetPipelineID(i int) *TrackUpdate {
	tu.mutation.SetPipelineID(i)
	return tu
}

// SetNillablePipelineID sets the "pipeline_id" field if the given value is not nil.
func (tu *TrackUpdate) SetNillablePipelineID(i *int) *TrackUpdate {
	if i != nil {
		tu.SetPipelineID(*i)
	}
	return tu
}

// ClearPipelineID clears the value of the "pipeline_id" field.
func (tu *TrackUpdate) ClearPipelineID() *TrackUpdate {
	tu.mutation.ClearPipelineID()
	return tu
}

// SetStageID sets the "stage_id" field.
func (tu *TrackUpdate) SetStageID(i int) *TrackUpdate {
	tu.mutation.SetStageID(i)
	return tu
}

// SetNillableStageID sets the "stage_id" field if the given value is not nil.
func (tu *TrackUpdate) SetNillableStageID(i *int) *TrackUpdate {
	if i != nil {
		tu.SetStageID(*i)
	}
	return tu
}

// ClearStageID clears the value of the "stage_id" field.
func (tu *TrackUpdate) ClearStageID() *TrackUpdate {
	tu.mutation.ClearStageID()
	return tu
}

// SetUserID sets the "user_id" field.
func (tu *TrackUpdate) SetUserID(i int) *TrackUpdate {
	tu.mutation.SetUserID(i)
	return tu
}

// SetNillableUserID sets the "user_id" field if the given value is not nil.
func (tu *TrackUpdate) SetNillableUserID(i *int) *TrackUpdate {
	if i != nil {
		tu.SetUserID(*i)
	}
	return tu
}

// ClearUserID clears the value of the "user_id" field.
func (tu *TrackUpdate) ClearUserID() *TrackUpdate {
	tu.mutation.ClearUserID()
	return tu
}

// SetPersonID sets the "person_id" field.
func (tu *TrackUpdate) SetPersonID(i int) *TrackUpdate {
	tu.mutation.SetPersonID(i)
	return tu
}

// SetNillablePersonID sets the "person_id" field if the given value is not nil.
func (tu *TrackUpdate) SetNillablePersonID(i *int) *TrackUpdate {
	if i != nil {
		tu.SetPersonID(*i)
	}
	return tu
}

// ClearPersonID clears the value of the "person_id" field.
func (tu *TrackUpdate) ClearPersonID() *TrackUpdate {
	tu.mutation.ClearPersonID()
	return tu
}

// SetWeight sets the "weight" field.
func (tu *TrackUpdate) SetWeight(f float64) *TrackUpdate {
	tu.mutation.ResetWeight()
	tu.mutation.SetWeight(f)
	return tu
}

// SetNillableWeight sets the "weight" field if the given value is not nil.
func (tu *TrackUpdate) SetNillableWeight(f *float64) *TrackUpdate {
	if f != nil {
		tu.SetWeight(*f)
	}
	return tu
}

// AddWeight adds f to the "weight" field.
func (tu *TrackUpdate) AddWeight(f float64) *TrackUpdate {
	tu.mutation.AddWeight(f)
	return tu
}

// ClearWeight clears the value of the "weight" field.
func (tu *TrackUpdate) ClearWeight() *TrackUpdate {
	tu.mutation.ClearWeight()
	return tu
}

// SetMeta sets the "meta" field.
func (tu *TrackUpdate) SetMeta(s struct{}) *TrackUpdate {
	tu.mutation.SetMeta(s)
	return tu
}

// SetNillableMeta sets the "meta" field if the given value is not nil.
func (tu *TrackUpdate) SetNillableMeta(s *struct{}) *TrackUpdate {
	if s != nil {
		tu.SetMeta(*s)
	}
	return tu
}

// ClearMeta clears the value of the "meta" field.
func (tu *TrackUpdate) ClearMeta() *TrackUpdate {
	tu.mutation.ClearMeta()
	return tu
}

// SetState sets the "state" field.
func (tu *TrackUpdate) SetState(t track.State) *TrackUpdate {
	tu.mutation.SetState(t)
	return tu
}

// SetNillableState sets the "state" field if the given value is not nil.
func (tu *TrackUpdate) SetNillableState(t *track.State) *TrackUpdate {
	if t != nil {
		tu.SetState(*t)
	}
	return tu
}

// ClearState clears the value of the "state" field.
func (tu *TrackUpdate) ClearState() *TrackUpdate {
	tu.mutation.ClearState()
	return tu
}

// SetDealStageID sets the "deal_stage_id" field.
func (tu *TrackUpdate) SetDealStageID(i int) *TrackUpdate {
	tu.mutation.ResetDealStageID()
	tu.mutation.SetDealStageID(i)
	return tu
}

// SetNillableDealStageID sets the "deal_stage_id" field if the given value is not nil.
func (tu *TrackUpdate) SetNillableDealStageID(i *int) *TrackUpdate {
	if i != nil {
		tu.SetDealStageID(*i)
	}
	return tu
}

// AddDealStageID adds i to the "deal_stage_id" field.
func (tu *TrackUpdate) AddDealStageID(i int) *TrackUpdate {
	tu.mutation.AddDealStageID(i)
	return tu
}

// ClearDealStageID clears the value of the "deal_stage_id" field.
func (tu *TrackUpdate) ClearDealStageID() *TrackUpdate {
	tu.mutation.ClearDealStageID()
	return tu
}

// SetStageHistory sets the "stage_history" field.
func (tu *TrackUpdate) SetStageHistory(the []types.StageHistoryEntry) *TrackUpdate {
	tu.mutation.SetStageHistory(the)
	return tu
}

// AppendStageHistory appends the to the "stage_history" field.
func (tu *TrackUpdate) AppendStageHistory(the []types.StageHistoryEntry) *TrackUpdate {
	tu.mutation.AppendStageHistory(the)
	return tu
}

// ClearStageHistory clears the value of the "stage_history" field.
func (tu *TrackUpdate) ClearStageHistory() *TrackUpdate {
	tu.mutation.ClearStageHistory()
	return tu
}

// SetDeal sets the "deal" edge to the Deal entity.
func (tu *TrackUpdate) SetDeal(d *Deal) *TrackUpdate {
	return tu.SetDealID(d.ID)
}

// SetUser sets the "user" edge to the User entity.
func (tu *TrackUpdate) SetUser(u *User) *TrackUpdate {
	return tu.SetUserID(u.ID)
}

// SetPerson sets the "person" edge to the Person entity.
func (tu *TrackUpdate) SetPerson(p *Person) *TrackUpdate {
	return tu.SetPersonID(p.ID)
}

// SetPipeline sets the "pipeline" edge to the Pipeline entity.
func (tu *TrackUpdate) SetPipeline(p *Pipeline) *TrackUpdate {
	return tu.SetPipelineID(p.ID)
}

// SetStage sets the "stage" edge to the Stage entity.
func (tu *TrackUpdate) SetStage(s *Stage) *TrackUpdate {
	return tu.SetStageID(s.ID)
}

// AddAppointmentIDs adds the "appointments" edge to the Appointment entity by IDs.
func (tu *TrackUpdate) AddAppointmentIDs(ids ...int) *TrackUpdate {
	tu.mutation.AddAppointmentIDs(ids...)
	return tu
}

// AddAppointments adds the "appointments" edges to the Appointment entity.
func (tu *TrackUpdate) AddAppointments(a ...*Appointment) *TrackUpdate {
	ids := make([]int, len(a))
	for i := range a {
		ids[i] = a[i].ID
	}
	return tu.AddAppointmentIDs(ids...)
}

// AddFileUsageIDs adds the "file_usages" edge to the FileUsage entity by IDs.
func (tu *TrackUpdate) AddFileUsageIDs(ids ...int) *TrackUpdate {
	tu.mutation.AddFileUsageIDs(ids...)
	return tu
}

// AddFileUsages adds the "file_usages" edges to the FileUsage entity.
func (tu *TrackUpdate) AddFileUsages(f ...*FileUsage) *TrackUpdate {
	ids := make([]int, len(f))
	for i := range f {
		ids[i] = f[i].ID
	}
	return tu.AddFileUsageIDs(ids...)
}

// AddAttachmentIDs adds the "attachments" edge to the Attachment entity by IDs.
func (tu *TrackUpdate) AddAttachmentIDs(ids ...int) *TrackUpdate {
	tu.mutation.AddAttachmentIDs(ids...)
	return tu
}

// AddAttachments adds the "attachments" edges to the Attachment entity.
func (tu *TrackUpdate) AddAttachments(a ...*Attachment) *TrackUpdate {
	ids := make([]int, len(a))
	for i := range a {
		ids[i] = a[i].ID
	}
	return tu.AddAttachmentIDs(ids...)
}

// AddBillItemIDs adds the "bill_items" edge to the BillItem entity by IDs.
func (tu *TrackUpdate) AddBillItemIDs(ids ...int) *TrackUpdate {
	tu.mutation.AddBillItemIDs(ids...)
	return tu
}

// AddBillItems adds the "bill_items" edges to the BillItem entity.
func (tu *TrackUpdate) AddBillItems(b ...*BillItem) *TrackUpdate {
	ids := make([]int, len(b))
	for i := range b {
		ids[i] = b[i].ID
	}
	return tu.AddBillItemIDs(ids...)
}

// Mutation returns the TrackMutation object of the builder.
func (tu *TrackUpdate) Mutation() *TrackMutation {
	return tu.mutation
}

// ClearDeal clears the "deal" edge to the Deal entity.
func (tu *TrackUpdate) ClearDeal() *TrackUpdate {
	tu.mutation.ClearDeal()
	return tu
}

// ClearUser clears the "user" edge to the User entity.
func (tu *TrackUpdate) ClearUser() *TrackUpdate {
	tu.mutation.ClearUser()
	return tu
}

// ClearPerson clears the "person" edge to the Person entity.
func (tu *TrackUpdate) ClearPerson() *TrackUpdate {
	tu.mutation.ClearPerson()
	return tu
}

// ClearPipeline clears the "pipeline" edge to the Pipeline entity.
func (tu *TrackUpdate) ClearPipeline() *TrackUpdate {
	tu.mutation.ClearPipeline()
	return tu
}

// ClearStage clears the "stage" edge to the Stage entity.
func (tu *TrackUpdate) ClearStage() *TrackUpdate {
	tu.mutation.ClearStage()
	return tu
}

// ClearAppointments clears all "appointments" edges to the Appointment entity.
func (tu *TrackUpdate) ClearAppointments() *TrackUpdate {
	tu.mutation.ClearAppointments()
	return tu
}

// RemoveAppointmentIDs removes the "appointments" edge to Appointment entities by IDs.
func (tu *TrackUpdate) RemoveAppointmentIDs(ids ...int) *TrackUpdate {
	tu.mutation.RemoveAppointmentIDs(ids...)
	return tu
}

// RemoveAppointments removes "appointments" edges to Appointment entities.
func (tu *TrackUpdate) RemoveAppointments(a ...*Appointment) *TrackUpdate {
	ids := make([]int, len(a))
	for i := range a {
		ids[i] = a[i].ID
	}
	return tu.RemoveAppointmentIDs(ids...)
}

// ClearFileUsages clears all "file_usages" edges to the FileUsage entity.
func (tu *TrackUpdate) ClearFileUsages() *TrackUpdate {
	tu.mutation.ClearFileUsages()
	return tu
}

// RemoveFileUsageIDs removes the "file_usages" edge to FileUsage entities by IDs.
func (tu *TrackUpdate) RemoveFileUsageIDs(ids ...int) *TrackUpdate {
	tu.mutation.RemoveFileUsageIDs(ids...)
	return tu
}

// RemoveFileUsages removes "file_usages" edges to FileUsage entities.
func (tu *TrackUpdate) RemoveFileUsages(f ...*FileUsage) *TrackUpdate {
	ids := make([]int, len(f))
	for i := range f {
		ids[i] = f[i].ID
	}
	return tu.RemoveFileUsageIDs(ids...)
}

// ClearAttachments clears all "attachments" edges to the Attachment entity.
func (tu *TrackUpdate) ClearAttachments() *TrackUpdate {
	tu.mutation.ClearAttachments()
	return tu
}

// RemoveAttachmentIDs removes the "attachments" edge to Attachment entities by IDs.
func (tu *TrackUpdate) RemoveAttachmentIDs(ids ...int) *TrackUpdate {
	tu.mutation.RemoveAttachmentIDs(ids...)
	return tu
}

// RemoveAttachments removes "attachments" edges to Attachment entities.
func (tu *TrackUpdate) RemoveAttachments(a ...*Attachment) *TrackUpdate {
	ids := make([]int, len(a))
	for i := range a {
		ids[i] = a[i].ID
	}
	return tu.RemoveAttachmentIDs(ids...)
}

// ClearBillItems clears all "bill_items" edges to the BillItem entity.
func (tu *TrackUpdate) ClearBillItems() *TrackUpdate {
	tu.mutation.ClearBillItems()
	return tu
}

// RemoveBillItemIDs removes the "bill_items" edge to BillItem entities by IDs.
func (tu *TrackUpdate) RemoveBillItemIDs(ids ...int) *TrackUpdate {
	tu.mutation.RemoveBillItemIDs(ids...)
	return tu
}

// RemoveBillItems removes "bill_items" edges to BillItem entities.
func (tu *TrackUpdate) RemoveBillItems(b ...*BillItem) *TrackUpdate {
	ids := make([]int, len(b))
	for i := range b {
		ids[i] = b[i].ID
	}
	return tu.RemoveBillItemIDs(ids...)
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (tu *TrackUpdate) Save(ctx context.Context) (int, error) {
	if err := tu.defaults(); err != nil {
		return 0, err
	}
	return withHooks(ctx, tu.sqlSave, tu.mutation, tu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (tu *TrackUpdate) SaveX(ctx context.Context) int {
	affected, err := tu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (tu *TrackUpdate) Exec(ctx context.Context) error {
	_, err := tu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (tu *TrackUpdate) ExecX(ctx context.Context) {
	if err := tu.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (tu *TrackUpdate) defaults() error {
	if _, ok := tu.mutation.UpdatedAt(); !ok {
		if track.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized track.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := track.UpdateDefaultUpdatedAt()
		tu.mutation.SetUpdatedAt(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (tu *TrackUpdate) check() error {
	if v, ok := tu.mutation.State(); ok {
		if err := track.StateValidator(v); err != nil {
			return &ValidationError{Name: "state", err: fmt.Errorf(`ent: validator failed for field "Track.state": %w`, err)}
		}
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (tu *TrackUpdate) Modify(modifiers ...func(u *sql.UpdateBuilder)) *TrackUpdate {
	tu.modifiers = append(tu.modifiers, modifiers...)
	return tu
}

func (tu *TrackUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := tu.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(track.Table, track.Columns, sqlgraph.NewFieldSpec(track.FieldID, field.TypeInt))
	if ps := tu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := tu.mutation.DeletedAt(); ok {
		_spec.SetField(track.FieldDeletedAt, field.TypeTime, value)
	}
	if tu.mutation.DeletedAtCleared() {
		_spec.ClearField(track.FieldDeletedAt, field.TypeTime)
	}
	if value, ok := tu.mutation.Status(); ok {
		_spec.SetField(track.FieldStatus, field.TypeInt8, value)
	}
	if value, ok := tu.mutation.AddedStatus(); ok {
		_spec.AddField(track.FieldStatus, field.TypeInt8, value)
	}
	if value, ok := tu.mutation.Version(); ok {
		_spec.SetField(track.FieldVersion, field.TypeInt, value)
	}
	if value, ok := tu.mutation.AddedVersion(); ok {
		_spec.AddField(track.FieldVersion, field.TypeInt, value)
	}
	if value, ok := tu.mutation.UpdatedAt(); ok {
		_spec.SetField(track.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := tu.mutation.Begin(); ok {
		_spec.SetField(track.FieldBegin, field.TypeTime, value)
	}
	if tu.mutation.BeginCleared() {
		_spec.ClearField(track.FieldBegin, field.TypeTime)
	}
	if value, ok := tu.mutation.End(); ok {
		_spec.SetField(track.FieldEnd, field.TypeTime, value)
	}
	if tu.mutation.EndCleared() {
		_spec.ClearField(track.FieldEnd, field.TypeTime)
	}
	if value, ok := tu.mutation.Weight(); ok {
		_spec.SetField(track.FieldWeight, field.TypeFloat64, value)
	}
	if value, ok := tu.mutation.AddedWeight(); ok {
		_spec.AddField(track.FieldWeight, field.TypeFloat64, value)
	}
	if tu.mutation.WeightCleared() {
		_spec.ClearField(track.FieldWeight, field.TypeFloat64)
	}
	if value, ok := tu.mutation.Meta(); ok {
		_spec.SetField(track.FieldMeta, field.TypeJSON, value)
	}
	if tu.mutation.MetaCleared() {
		_spec.ClearField(track.FieldMeta, field.TypeJSON)
	}
	if value, ok := tu.mutation.State(); ok {
		_spec.SetField(track.FieldState, field.TypeEnum, value)
	}
	if tu.mutation.StateCleared() {
		_spec.ClearField(track.FieldState, field.TypeEnum)
	}
	if value, ok := tu.mutation.DealStageID(); ok {
		_spec.SetField(track.FieldDealStageID, field.TypeInt, value)
	}
	if value, ok := tu.mutation.AddedDealStageID(); ok {
		_spec.AddField(track.FieldDealStageID, field.TypeInt, value)
	}
	if tu.mutation.DealStageIDCleared() {
		_spec.ClearField(track.FieldDealStageID, field.TypeInt)
	}
	if value, ok := tu.mutation.StageHistory(); ok {
		_spec.SetField(track.FieldStageHistory, field.TypeJSON, value)
	}
	if value, ok := tu.mutation.AppendedStageHistory(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, track.FieldStageHistory, value)
		})
	}
	if tu.mutation.StageHistoryCleared() {
		_spec.ClearField(track.FieldStageHistory, field.TypeJSON)
	}
	if tu.mutation.DealCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   track.DealTable,
			Columns: []string{track.DealColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(deal.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := tu.mutation.DealIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   track.DealTable,
			Columns: []string{track.DealColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(deal.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if tu.mutation.UserCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   track.UserTable,
			Columns: []string{track.UserColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := tu.mutation.UserIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   track.UserTable,
			Columns: []string{track.UserColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if tu.mutation.PersonCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   track.PersonTable,
			Columns: []string{track.PersonColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(person.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := tu.mutation.PersonIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   track.PersonTable,
			Columns: []string{track.PersonColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(person.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if tu.mutation.PipelineCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   track.PipelineTable,
			Columns: []string{track.PipelineColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(pipeline.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := tu.mutation.PipelineIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   track.PipelineTable,
			Columns: []string{track.PipelineColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(pipeline.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if tu.mutation.StageCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   track.StageTable,
			Columns: []string{track.StageColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(stage.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := tu.mutation.StageIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   track.StageTable,
			Columns: []string{track.StageColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(stage.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if tu.mutation.AppointmentsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   track.AppointmentsTable,
			Columns: []string{track.AppointmentsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(appointment.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := tu.mutation.RemovedAppointmentsIDs(); len(nodes) > 0 && !tu.mutation.AppointmentsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   track.AppointmentsTable,
			Columns: []string{track.AppointmentsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(appointment.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := tu.mutation.AppointmentsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   track.AppointmentsTable,
			Columns: []string{track.AppointmentsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(appointment.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if tu.mutation.FileUsagesCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   track.FileUsagesTable,
			Columns: []string{track.FileUsagesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(fileusage.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := tu.mutation.RemovedFileUsagesIDs(); len(nodes) > 0 && !tu.mutation.FileUsagesCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   track.FileUsagesTable,
			Columns: []string{track.FileUsagesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(fileusage.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := tu.mutation.FileUsagesIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   track.FileUsagesTable,
			Columns: []string{track.FileUsagesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(fileusage.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if tu.mutation.AttachmentsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   track.AttachmentsTable,
			Columns: []string{track.AttachmentsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(attachment.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := tu.mutation.RemovedAttachmentsIDs(); len(nodes) > 0 && !tu.mutation.AttachmentsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   track.AttachmentsTable,
			Columns: []string{track.AttachmentsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(attachment.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := tu.mutation.AttachmentsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   track.AttachmentsTable,
			Columns: []string{track.AttachmentsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(attachment.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if tu.mutation.BillItemsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   track.BillItemsTable,
			Columns: []string{track.BillItemsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(billitem.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := tu.mutation.RemovedBillItemsIDs(); len(nodes) > 0 && !tu.mutation.BillItemsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   track.BillItemsTable,
			Columns: []string{track.BillItemsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(billitem.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := tu.mutation.BillItemsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   track.BillItemsTable,
			Columns: []string{track.BillItemsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(billitem.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_spec.AddModifiers(tu.modifiers...)
	if n, err = sqlgraph.UpdateNodes(ctx, tu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{track.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	tu.mutation.done = true
	return n, nil
}

// TrackUpdateOne is the builder for updating a single Track entity.
type TrackUpdateOne struct {
	config
	fields    []string
	hooks     []Hook
	mutation  *TrackMutation
	modifiers []func(*sql.UpdateBuilder)
}

// SetDeletedAt sets the "deleted_at" field.
func (tuo *TrackUpdateOne) SetDeletedAt(t time.Time) *TrackUpdateOne {
	tuo.mutation.SetDeletedAt(t)
	return tuo
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (tuo *TrackUpdateOne) SetNillableDeletedAt(t *time.Time) *TrackUpdateOne {
	if t != nil {
		tuo.SetDeletedAt(*t)
	}
	return tuo
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (tuo *TrackUpdateOne) ClearDeletedAt() *TrackUpdateOne {
	tuo.mutation.ClearDeletedAt()
	return tuo
}

// SetStatus sets the "status" field.
func (tuo *TrackUpdateOne) SetStatus(i int8) *TrackUpdateOne {
	tuo.mutation.ResetStatus()
	tuo.mutation.SetStatus(i)
	return tuo
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (tuo *TrackUpdateOne) SetNillableStatus(i *int8) *TrackUpdateOne {
	if i != nil {
		tuo.SetStatus(*i)
	}
	return tuo
}

// AddStatus adds i to the "status" field.
func (tuo *TrackUpdateOne) AddStatus(i int8) *TrackUpdateOne {
	tuo.mutation.AddStatus(i)
	return tuo
}

// SetVersion sets the "version" field.
func (tuo *TrackUpdateOne) SetVersion(i int) *TrackUpdateOne {
	tuo.mutation.ResetVersion()
	tuo.mutation.SetVersion(i)
	return tuo
}

// SetNillableVersion sets the "version" field if the given value is not nil.
func (tuo *TrackUpdateOne) SetNillableVersion(i *int) *TrackUpdateOne {
	if i != nil {
		tuo.SetVersion(*i)
	}
	return tuo
}

// AddVersion adds i to the "version" field.
func (tuo *TrackUpdateOne) AddVersion(i int) *TrackUpdateOne {
	tuo.mutation.AddVersion(i)
	return tuo
}

// SetUpdatedAt sets the "updated_at" field.
func (tuo *TrackUpdateOne) SetUpdatedAt(t time.Time) *TrackUpdateOne {
	tuo.mutation.SetUpdatedAt(t)
	return tuo
}

// SetBegin sets the "begin" field.
func (tuo *TrackUpdateOne) SetBegin(t time.Time) *TrackUpdateOne {
	tuo.mutation.SetBegin(t)
	return tuo
}

// SetNillableBegin sets the "begin" field if the given value is not nil.
func (tuo *TrackUpdateOne) SetNillableBegin(t *time.Time) *TrackUpdateOne {
	if t != nil {
		tuo.SetBegin(*t)
	}
	return tuo
}

// ClearBegin clears the value of the "begin" field.
func (tuo *TrackUpdateOne) ClearBegin() *TrackUpdateOne {
	tuo.mutation.ClearBegin()
	return tuo
}

// SetEnd sets the "end" field.
func (tuo *TrackUpdateOne) SetEnd(t time.Time) *TrackUpdateOne {
	tuo.mutation.SetEnd(t)
	return tuo
}

// SetNillableEnd sets the "end" field if the given value is not nil.
func (tuo *TrackUpdateOne) SetNillableEnd(t *time.Time) *TrackUpdateOne {
	if t != nil {
		tuo.SetEnd(*t)
	}
	return tuo
}

// ClearEnd clears the value of the "end" field.
func (tuo *TrackUpdateOne) ClearEnd() *TrackUpdateOne {
	tuo.mutation.ClearEnd()
	return tuo
}

// SetDealID sets the "deal_id" field.
func (tuo *TrackUpdateOne) SetDealID(i int) *TrackUpdateOne {
	tuo.mutation.SetDealID(i)
	return tuo
}

// SetNillableDealID sets the "deal_id" field if the given value is not nil.
func (tuo *TrackUpdateOne) SetNillableDealID(i *int) *TrackUpdateOne {
	if i != nil {
		tuo.SetDealID(*i)
	}
	return tuo
}

// ClearDealID clears the value of the "deal_id" field.
func (tuo *TrackUpdateOne) ClearDealID() *TrackUpdateOne {
	tuo.mutation.ClearDealID()
	return tuo
}

// SetPipelineID sets the "pipeline_id" field.
func (tuo *TrackUpdateOne) SetPipelineID(i int) *TrackUpdateOne {
	tuo.mutation.SetPipelineID(i)
	return tuo
}

// SetNillablePipelineID sets the "pipeline_id" field if the given value is not nil.
func (tuo *TrackUpdateOne) SetNillablePipelineID(i *int) *TrackUpdateOne {
	if i != nil {
		tuo.SetPipelineID(*i)
	}
	return tuo
}

// ClearPipelineID clears the value of the "pipeline_id" field.
func (tuo *TrackUpdateOne) ClearPipelineID() *TrackUpdateOne {
	tuo.mutation.ClearPipelineID()
	return tuo
}

// SetStageID sets the "stage_id" field.
func (tuo *TrackUpdateOne) SetStageID(i int) *TrackUpdateOne {
	tuo.mutation.SetStageID(i)
	return tuo
}

// SetNillableStageID sets the "stage_id" field if the given value is not nil.
func (tuo *TrackUpdateOne) SetNillableStageID(i *int) *TrackUpdateOne {
	if i != nil {
		tuo.SetStageID(*i)
	}
	return tuo
}

// ClearStageID clears the value of the "stage_id" field.
func (tuo *TrackUpdateOne) ClearStageID() *TrackUpdateOne {
	tuo.mutation.ClearStageID()
	return tuo
}

// SetUserID sets the "user_id" field.
func (tuo *TrackUpdateOne) SetUserID(i int) *TrackUpdateOne {
	tuo.mutation.SetUserID(i)
	return tuo
}

// SetNillableUserID sets the "user_id" field if the given value is not nil.
func (tuo *TrackUpdateOne) SetNillableUserID(i *int) *TrackUpdateOne {
	if i != nil {
		tuo.SetUserID(*i)
	}
	return tuo
}

// ClearUserID clears the value of the "user_id" field.
func (tuo *TrackUpdateOne) ClearUserID() *TrackUpdateOne {
	tuo.mutation.ClearUserID()
	return tuo
}

// SetPersonID sets the "person_id" field.
func (tuo *TrackUpdateOne) SetPersonID(i int) *TrackUpdateOne {
	tuo.mutation.SetPersonID(i)
	return tuo
}

// SetNillablePersonID sets the "person_id" field if the given value is not nil.
func (tuo *TrackUpdateOne) SetNillablePersonID(i *int) *TrackUpdateOne {
	if i != nil {
		tuo.SetPersonID(*i)
	}
	return tuo
}

// ClearPersonID clears the value of the "person_id" field.
func (tuo *TrackUpdateOne) ClearPersonID() *TrackUpdateOne {
	tuo.mutation.ClearPersonID()
	return tuo
}

// SetWeight sets the "weight" field.
func (tuo *TrackUpdateOne) SetWeight(f float64) *TrackUpdateOne {
	tuo.mutation.ResetWeight()
	tuo.mutation.SetWeight(f)
	return tuo
}

// SetNillableWeight sets the "weight" field if the given value is not nil.
func (tuo *TrackUpdateOne) SetNillableWeight(f *float64) *TrackUpdateOne {
	if f != nil {
		tuo.SetWeight(*f)
	}
	return tuo
}

// AddWeight adds f to the "weight" field.
func (tuo *TrackUpdateOne) AddWeight(f float64) *TrackUpdateOne {
	tuo.mutation.AddWeight(f)
	return tuo
}

// ClearWeight clears the value of the "weight" field.
func (tuo *TrackUpdateOne) ClearWeight() *TrackUpdateOne {
	tuo.mutation.ClearWeight()
	return tuo
}

// SetMeta sets the "meta" field.
func (tuo *TrackUpdateOne) SetMeta(s struct{}) *TrackUpdateOne {
	tuo.mutation.SetMeta(s)
	return tuo
}

// SetNillableMeta sets the "meta" field if the given value is not nil.
func (tuo *TrackUpdateOne) SetNillableMeta(s *struct{}) *TrackUpdateOne {
	if s != nil {
		tuo.SetMeta(*s)
	}
	return tuo
}

// ClearMeta clears the value of the "meta" field.
func (tuo *TrackUpdateOne) ClearMeta() *TrackUpdateOne {
	tuo.mutation.ClearMeta()
	return tuo
}

// SetState sets the "state" field.
func (tuo *TrackUpdateOne) SetState(t track.State) *TrackUpdateOne {
	tuo.mutation.SetState(t)
	return tuo
}

// SetNillableState sets the "state" field if the given value is not nil.
func (tuo *TrackUpdateOne) SetNillableState(t *track.State) *TrackUpdateOne {
	if t != nil {
		tuo.SetState(*t)
	}
	return tuo
}

// ClearState clears the value of the "state" field.
func (tuo *TrackUpdateOne) ClearState() *TrackUpdateOne {
	tuo.mutation.ClearState()
	return tuo
}

// SetDealStageID sets the "deal_stage_id" field.
func (tuo *TrackUpdateOne) SetDealStageID(i int) *TrackUpdateOne {
	tuo.mutation.ResetDealStageID()
	tuo.mutation.SetDealStageID(i)
	return tuo
}

// SetNillableDealStageID sets the "deal_stage_id" field if the given value is not nil.
func (tuo *TrackUpdateOne) SetNillableDealStageID(i *int) *TrackUpdateOne {
	if i != nil {
		tuo.SetDealStageID(*i)
	}
	return tuo
}

// AddDealStageID adds i to the "deal_stage_id" field.
func (tuo *TrackUpdateOne) AddDealStageID(i int) *TrackUpdateOne {
	tuo.mutation.AddDealStageID(i)
	return tuo
}

// ClearDealStageID clears the value of the "deal_stage_id" field.
func (tuo *TrackUpdateOne) ClearDealStageID() *TrackUpdateOne {
	tuo.mutation.ClearDealStageID()
	return tuo
}

// SetStageHistory sets the "stage_history" field.
func (tuo *TrackUpdateOne) SetStageHistory(the []types.StageHistoryEntry) *TrackUpdateOne {
	tuo.mutation.SetStageHistory(the)
	return tuo
}

// AppendStageHistory appends the to the "stage_history" field.
func (tuo *TrackUpdateOne) AppendStageHistory(the []types.StageHistoryEntry) *TrackUpdateOne {
	tuo.mutation.AppendStageHistory(the)
	return tuo
}

// ClearStageHistory clears the value of the "stage_history" field.
func (tuo *TrackUpdateOne) ClearStageHistory() *TrackUpdateOne {
	tuo.mutation.ClearStageHistory()
	return tuo
}

// SetDeal sets the "deal" edge to the Deal entity.
func (tuo *TrackUpdateOne) SetDeal(d *Deal) *TrackUpdateOne {
	return tuo.SetDealID(d.ID)
}

// SetUser sets the "user" edge to the User entity.
func (tuo *TrackUpdateOne) SetUser(u *User) *TrackUpdateOne {
	return tuo.SetUserID(u.ID)
}

// SetPerson sets the "person" edge to the Person entity.
func (tuo *TrackUpdateOne) SetPerson(p *Person) *TrackUpdateOne {
	return tuo.SetPersonID(p.ID)
}

// SetPipeline sets the "pipeline" edge to the Pipeline entity.
func (tuo *TrackUpdateOne) SetPipeline(p *Pipeline) *TrackUpdateOne {
	return tuo.SetPipelineID(p.ID)
}

// SetStage sets the "stage" edge to the Stage entity.
func (tuo *TrackUpdateOne) SetStage(s *Stage) *TrackUpdateOne {
	return tuo.SetStageID(s.ID)
}

// AddAppointmentIDs adds the "appointments" edge to the Appointment entity by IDs.
func (tuo *TrackUpdateOne) AddAppointmentIDs(ids ...int) *TrackUpdateOne {
	tuo.mutation.AddAppointmentIDs(ids...)
	return tuo
}

// AddAppointments adds the "appointments" edges to the Appointment entity.
func (tuo *TrackUpdateOne) AddAppointments(a ...*Appointment) *TrackUpdateOne {
	ids := make([]int, len(a))
	for i := range a {
		ids[i] = a[i].ID
	}
	return tuo.AddAppointmentIDs(ids...)
}

// AddFileUsageIDs adds the "file_usages" edge to the FileUsage entity by IDs.
func (tuo *TrackUpdateOne) AddFileUsageIDs(ids ...int) *TrackUpdateOne {
	tuo.mutation.AddFileUsageIDs(ids...)
	return tuo
}

// AddFileUsages adds the "file_usages" edges to the FileUsage entity.
func (tuo *TrackUpdateOne) AddFileUsages(f ...*FileUsage) *TrackUpdateOne {
	ids := make([]int, len(f))
	for i := range f {
		ids[i] = f[i].ID
	}
	return tuo.AddFileUsageIDs(ids...)
}

// AddAttachmentIDs adds the "attachments" edge to the Attachment entity by IDs.
func (tuo *TrackUpdateOne) AddAttachmentIDs(ids ...int) *TrackUpdateOne {
	tuo.mutation.AddAttachmentIDs(ids...)
	return tuo
}

// AddAttachments adds the "attachments" edges to the Attachment entity.
func (tuo *TrackUpdateOne) AddAttachments(a ...*Attachment) *TrackUpdateOne {
	ids := make([]int, len(a))
	for i := range a {
		ids[i] = a[i].ID
	}
	return tuo.AddAttachmentIDs(ids...)
}

// AddBillItemIDs adds the "bill_items" edge to the BillItem entity by IDs.
func (tuo *TrackUpdateOne) AddBillItemIDs(ids ...int) *TrackUpdateOne {
	tuo.mutation.AddBillItemIDs(ids...)
	return tuo
}

// AddBillItems adds the "bill_items" edges to the BillItem entity.
func (tuo *TrackUpdateOne) AddBillItems(b ...*BillItem) *TrackUpdateOne {
	ids := make([]int, len(b))
	for i := range b {
		ids[i] = b[i].ID
	}
	return tuo.AddBillItemIDs(ids...)
}

// Mutation returns the TrackMutation object of the builder.
func (tuo *TrackUpdateOne) Mutation() *TrackMutation {
	return tuo.mutation
}

// ClearDeal clears the "deal" edge to the Deal entity.
func (tuo *TrackUpdateOne) ClearDeal() *TrackUpdateOne {
	tuo.mutation.ClearDeal()
	return tuo
}

// ClearUser clears the "user" edge to the User entity.
func (tuo *TrackUpdateOne) ClearUser() *TrackUpdateOne {
	tuo.mutation.ClearUser()
	return tuo
}

// ClearPerson clears the "person" edge to the Person entity.
func (tuo *TrackUpdateOne) ClearPerson() *TrackUpdateOne {
	tuo.mutation.ClearPerson()
	return tuo
}

// ClearPipeline clears the "pipeline" edge to the Pipeline entity.
func (tuo *TrackUpdateOne) ClearPipeline() *TrackUpdateOne {
	tuo.mutation.ClearPipeline()
	return tuo
}

// ClearStage clears the "stage" edge to the Stage entity.
func (tuo *TrackUpdateOne) ClearStage() *TrackUpdateOne {
	tuo.mutation.ClearStage()
	return tuo
}

// ClearAppointments clears all "appointments" edges to the Appointment entity.
func (tuo *TrackUpdateOne) ClearAppointments() *TrackUpdateOne {
	tuo.mutation.ClearAppointments()
	return tuo
}

// RemoveAppointmentIDs removes the "appointments" edge to Appointment entities by IDs.
func (tuo *TrackUpdateOne) RemoveAppointmentIDs(ids ...int) *TrackUpdateOne {
	tuo.mutation.RemoveAppointmentIDs(ids...)
	return tuo
}

// RemoveAppointments removes "appointments" edges to Appointment entities.
func (tuo *TrackUpdateOne) RemoveAppointments(a ...*Appointment) *TrackUpdateOne {
	ids := make([]int, len(a))
	for i := range a {
		ids[i] = a[i].ID
	}
	return tuo.RemoveAppointmentIDs(ids...)
}

// ClearFileUsages clears all "file_usages" edges to the FileUsage entity.
func (tuo *TrackUpdateOne) ClearFileUsages() *TrackUpdateOne {
	tuo.mutation.ClearFileUsages()
	return tuo
}

// RemoveFileUsageIDs removes the "file_usages" edge to FileUsage entities by IDs.
func (tuo *TrackUpdateOne) RemoveFileUsageIDs(ids ...int) *TrackUpdateOne {
	tuo.mutation.RemoveFileUsageIDs(ids...)
	return tuo
}

// RemoveFileUsages removes "file_usages" edges to FileUsage entities.
func (tuo *TrackUpdateOne) RemoveFileUsages(f ...*FileUsage) *TrackUpdateOne {
	ids := make([]int, len(f))
	for i := range f {
		ids[i] = f[i].ID
	}
	return tuo.RemoveFileUsageIDs(ids...)
}

// ClearAttachments clears all "attachments" edges to the Attachment entity.
func (tuo *TrackUpdateOne) ClearAttachments() *TrackUpdateOne {
	tuo.mutation.ClearAttachments()
	return tuo
}

// RemoveAttachmentIDs removes the "attachments" edge to Attachment entities by IDs.
func (tuo *TrackUpdateOne) RemoveAttachmentIDs(ids ...int) *TrackUpdateOne {
	tuo.mutation.RemoveAttachmentIDs(ids...)
	return tuo
}

// RemoveAttachments removes "attachments" edges to Attachment entities.
func (tuo *TrackUpdateOne) RemoveAttachments(a ...*Attachment) *TrackUpdateOne {
	ids := make([]int, len(a))
	for i := range a {
		ids[i] = a[i].ID
	}
	return tuo.RemoveAttachmentIDs(ids...)
}

// ClearBillItems clears all "bill_items" edges to the BillItem entity.
func (tuo *TrackUpdateOne) ClearBillItems() *TrackUpdateOne {
	tuo.mutation.ClearBillItems()
	return tuo
}

// RemoveBillItemIDs removes the "bill_items" edge to BillItem entities by IDs.
func (tuo *TrackUpdateOne) RemoveBillItemIDs(ids ...int) *TrackUpdateOne {
	tuo.mutation.RemoveBillItemIDs(ids...)
	return tuo
}

// RemoveBillItems removes "bill_items" edges to BillItem entities.
func (tuo *TrackUpdateOne) RemoveBillItems(b ...*BillItem) *TrackUpdateOne {
	ids := make([]int, len(b))
	for i := range b {
		ids[i] = b[i].ID
	}
	return tuo.RemoveBillItemIDs(ids...)
}

// Where appends a list predicates to the TrackUpdate builder.
func (tuo *TrackUpdateOne) Where(ps ...predicate.Track) *TrackUpdateOne {
	tuo.mutation.Where(ps...)
	return tuo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (tuo *TrackUpdateOne) Select(field string, fields ...string) *TrackUpdateOne {
	tuo.fields = append([]string{field}, fields...)
	return tuo
}

// Save executes the query and returns the updated Track entity.
func (tuo *TrackUpdateOne) Save(ctx context.Context) (*Track, error) {
	if err := tuo.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, tuo.sqlSave, tuo.mutation, tuo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (tuo *TrackUpdateOne) SaveX(ctx context.Context) *Track {
	node, err := tuo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (tuo *TrackUpdateOne) Exec(ctx context.Context) error {
	_, err := tuo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (tuo *TrackUpdateOne) ExecX(ctx context.Context) {
	if err := tuo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (tuo *TrackUpdateOne) defaults() error {
	if _, ok := tuo.mutation.UpdatedAt(); !ok {
		if track.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized track.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := track.UpdateDefaultUpdatedAt()
		tuo.mutation.SetUpdatedAt(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (tuo *TrackUpdateOne) check() error {
	if v, ok := tuo.mutation.State(); ok {
		if err := track.StateValidator(v); err != nil {
			return &ValidationError{Name: "state", err: fmt.Errorf(`ent: validator failed for field "Track.state": %w`, err)}
		}
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (tuo *TrackUpdateOne) Modify(modifiers ...func(u *sql.UpdateBuilder)) *TrackUpdateOne {
	tuo.modifiers = append(tuo.modifiers, modifiers...)
	return tuo
}

func (tuo *TrackUpdateOne) sqlSave(ctx context.Context) (_node *Track, err error) {
	if err := tuo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(track.Table, track.Columns, sqlgraph.NewFieldSpec(track.FieldID, field.TypeInt))
	id, ok := tuo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "Track.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := tuo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, track.FieldID)
		for _, f := range fields {
			if !track.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != track.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := tuo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := tuo.mutation.DeletedAt(); ok {
		_spec.SetField(track.FieldDeletedAt, field.TypeTime, value)
	}
	if tuo.mutation.DeletedAtCleared() {
		_spec.ClearField(track.FieldDeletedAt, field.TypeTime)
	}
	if value, ok := tuo.mutation.Status(); ok {
		_spec.SetField(track.FieldStatus, field.TypeInt8, value)
	}
	if value, ok := tuo.mutation.AddedStatus(); ok {
		_spec.AddField(track.FieldStatus, field.TypeInt8, value)
	}
	if value, ok := tuo.mutation.Version(); ok {
		_spec.SetField(track.FieldVersion, field.TypeInt, value)
	}
	if value, ok := tuo.mutation.AddedVersion(); ok {
		_spec.AddField(track.FieldVersion, field.TypeInt, value)
	}
	if value, ok := tuo.mutation.UpdatedAt(); ok {
		_spec.SetField(track.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := tuo.mutation.Begin(); ok {
		_spec.SetField(track.FieldBegin, field.TypeTime, value)
	}
	if tuo.mutation.BeginCleared() {
		_spec.ClearField(track.FieldBegin, field.TypeTime)
	}
	if value, ok := tuo.mutation.End(); ok {
		_spec.SetField(track.FieldEnd, field.TypeTime, value)
	}
	if tuo.mutation.EndCleared() {
		_spec.ClearField(track.FieldEnd, field.TypeTime)
	}
	if value, ok := tuo.mutation.Weight(); ok {
		_spec.SetField(track.FieldWeight, field.TypeFloat64, value)
	}
	if value, ok := tuo.mutation.AddedWeight(); ok {
		_spec.AddField(track.FieldWeight, field.TypeFloat64, value)
	}
	if tuo.mutation.WeightCleared() {
		_spec.ClearField(track.FieldWeight, field.TypeFloat64)
	}
	if value, ok := tuo.mutation.Meta(); ok {
		_spec.SetField(track.FieldMeta, field.TypeJSON, value)
	}
	if tuo.mutation.MetaCleared() {
		_spec.ClearField(track.FieldMeta, field.TypeJSON)
	}
	if value, ok := tuo.mutation.State(); ok {
		_spec.SetField(track.FieldState, field.TypeEnum, value)
	}
	if tuo.mutation.StateCleared() {
		_spec.ClearField(track.FieldState, field.TypeEnum)
	}
	if value, ok := tuo.mutation.DealStageID(); ok {
		_spec.SetField(track.FieldDealStageID, field.TypeInt, value)
	}
	if value, ok := tuo.mutation.AddedDealStageID(); ok {
		_spec.AddField(track.FieldDealStageID, field.TypeInt, value)
	}
	if tuo.mutation.DealStageIDCleared() {
		_spec.ClearField(track.FieldDealStageID, field.TypeInt)
	}
	if value, ok := tuo.mutation.StageHistory(); ok {
		_spec.SetField(track.FieldStageHistory, field.TypeJSON, value)
	}
	if value, ok := tuo.mutation.AppendedStageHistory(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, track.FieldStageHistory, value)
		})
	}
	if tuo.mutation.StageHistoryCleared() {
		_spec.ClearField(track.FieldStageHistory, field.TypeJSON)
	}
	if tuo.mutation.DealCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   track.DealTable,
			Columns: []string{track.DealColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(deal.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := tuo.mutation.DealIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   track.DealTable,
			Columns: []string{track.DealColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(deal.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if tuo.mutation.UserCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   track.UserTable,
			Columns: []string{track.UserColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := tuo.mutation.UserIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   track.UserTable,
			Columns: []string{track.UserColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if tuo.mutation.PersonCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   track.PersonTable,
			Columns: []string{track.PersonColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(person.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := tuo.mutation.PersonIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   track.PersonTable,
			Columns: []string{track.PersonColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(person.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if tuo.mutation.PipelineCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   track.PipelineTable,
			Columns: []string{track.PipelineColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(pipeline.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := tuo.mutation.PipelineIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   track.PipelineTable,
			Columns: []string{track.PipelineColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(pipeline.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if tuo.mutation.StageCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   track.StageTable,
			Columns: []string{track.StageColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(stage.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := tuo.mutation.StageIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   track.StageTable,
			Columns: []string{track.StageColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(stage.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if tuo.mutation.AppointmentsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   track.AppointmentsTable,
			Columns: []string{track.AppointmentsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(appointment.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := tuo.mutation.RemovedAppointmentsIDs(); len(nodes) > 0 && !tuo.mutation.AppointmentsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   track.AppointmentsTable,
			Columns: []string{track.AppointmentsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(appointment.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := tuo.mutation.AppointmentsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   track.AppointmentsTable,
			Columns: []string{track.AppointmentsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(appointment.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if tuo.mutation.FileUsagesCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   track.FileUsagesTable,
			Columns: []string{track.FileUsagesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(fileusage.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := tuo.mutation.RemovedFileUsagesIDs(); len(nodes) > 0 && !tuo.mutation.FileUsagesCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   track.FileUsagesTable,
			Columns: []string{track.FileUsagesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(fileusage.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := tuo.mutation.FileUsagesIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   track.FileUsagesTable,
			Columns: []string{track.FileUsagesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(fileusage.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if tuo.mutation.AttachmentsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   track.AttachmentsTable,
			Columns: []string{track.AttachmentsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(attachment.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := tuo.mutation.RemovedAttachmentsIDs(); len(nodes) > 0 && !tuo.mutation.AttachmentsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   track.AttachmentsTable,
			Columns: []string{track.AttachmentsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(attachment.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := tuo.mutation.AttachmentsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   track.AttachmentsTable,
			Columns: []string{track.AttachmentsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(attachment.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if tuo.mutation.BillItemsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   track.BillItemsTable,
			Columns: []string{track.BillItemsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(billitem.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := tuo.mutation.RemovedBillItemsIDs(); len(nodes) > 0 && !tuo.mutation.BillItemsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   track.BillItemsTable,
			Columns: []string{track.BillItemsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(billitem.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := tuo.mutation.BillItemsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   track.BillItemsTable,
			Columns: []string{track.BillItemsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(billitem.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_spec.AddModifiers(tuo.modifiers...)
	_node = &Track{config: tuo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, tuo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{track.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	tuo.mutation.done = true
	return _node, nil
}
