// Code generated by ent, DO NOT EDIT.

package ent

import (
	"bcare/ent/deal"
	"bcare/ent/pipeline"
	"bcare/ent/predicate"
	"bcare/ent/stage"
	"context"
	"database/sql/driver"
	"fmt"
	"math"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// StageQuery is the builder for querying Stage entities.
type StageQuery struct {
	config
	ctx          *QueryContext
	order        []stage.OrderOption
	inters       []Interceptor
	predicates   []predicate.Stage
	withParent   *StageQuery
	withChildren *StageQuery
	withDeals    *DealQuery
	withPipeline *PipelineQuery
	modifiers    []func(*sql.Selector)
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the StageQuery builder.
func (sq *StageQuery) Where(ps ...predicate.Stage) *StageQuery {
	sq.predicates = append(sq.predicates, ps...)
	return sq
}

// Limit the number of records to be returned by this query.
func (sq *StageQuery) Limit(limit int) *StageQuery {
	sq.ctx.Limit = &limit
	return sq
}

// Offset to start from.
func (sq *StageQuery) Offset(offset int) *StageQuery {
	sq.ctx.Offset = &offset
	return sq
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (sq *StageQuery) Unique(unique bool) *StageQuery {
	sq.ctx.Unique = &unique
	return sq
}

// Order specifies how the records should be ordered.
func (sq *StageQuery) Order(o ...stage.OrderOption) *StageQuery {
	sq.order = append(sq.order, o...)
	return sq
}

// QueryParent chains the current query on the "parent" edge.
func (sq *StageQuery) QueryParent() *StageQuery {
	query := (&StageClient{config: sq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := sq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := sq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(stage.Table, stage.FieldID, selector),
			sqlgraph.To(stage.Table, stage.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, stage.ParentTable, stage.ParentColumn),
		)
		fromU = sqlgraph.SetNeighbors(sq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// QueryChildren chains the current query on the "children" edge.
func (sq *StageQuery) QueryChildren() *StageQuery {
	query := (&StageClient{config: sq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := sq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := sq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(stage.Table, stage.FieldID, selector),
			sqlgraph.To(stage.Table, stage.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, stage.ChildrenTable, stage.ChildrenColumn),
		)
		fromU = sqlgraph.SetNeighbors(sq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// QueryDeals chains the current query on the "deals" edge.
func (sq *StageQuery) QueryDeals() *DealQuery {
	query := (&DealClient{config: sq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := sq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := sq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(stage.Table, stage.FieldID, selector),
			sqlgraph.To(deal.Table, deal.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, stage.DealsTable, stage.DealsColumn),
		)
		fromU = sqlgraph.SetNeighbors(sq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// QueryPipeline chains the current query on the "pipeline" edge.
func (sq *StageQuery) QueryPipeline() *PipelineQuery {
	query := (&PipelineClient{config: sq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := sq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := sq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(stage.Table, stage.FieldID, selector),
			sqlgraph.To(pipeline.Table, pipeline.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, stage.PipelineTable, stage.PipelineColumn),
		)
		fromU = sqlgraph.SetNeighbors(sq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// First returns the first Stage entity from the query.
// Returns a *NotFoundError when no Stage was found.
func (sq *StageQuery) First(ctx context.Context) (*Stage, error) {
	nodes, err := sq.Limit(1).All(setContextOp(ctx, sq.ctx, ent.OpQueryFirst))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{stage.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (sq *StageQuery) FirstX(ctx context.Context) *Stage {
	node, err := sq.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first Stage ID from the query.
// Returns a *NotFoundError when no Stage ID was found.
func (sq *StageQuery) FirstID(ctx context.Context) (id int, err error) {
	var ids []int
	if ids, err = sq.Limit(1).IDs(setContextOp(ctx, sq.ctx, ent.OpQueryFirstID)); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{stage.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (sq *StageQuery) FirstIDX(ctx context.Context) int {
	id, err := sq.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single Stage entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one Stage entity is found.
// Returns a *NotFoundError when no Stage entities are found.
func (sq *StageQuery) Only(ctx context.Context) (*Stage, error) {
	nodes, err := sq.Limit(2).All(setContextOp(ctx, sq.ctx, ent.OpQueryOnly))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{stage.Label}
	default:
		return nil, &NotSingularError{stage.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (sq *StageQuery) OnlyX(ctx context.Context) *Stage {
	node, err := sq.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only Stage ID in the query.
// Returns a *NotSingularError when more than one Stage ID is found.
// Returns a *NotFoundError when no entities are found.
func (sq *StageQuery) OnlyID(ctx context.Context) (id int, err error) {
	var ids []int
	if ids, err = sq.Limit(2).IDs(setContextOp(ctx, sq.ctx, ent.OpQueryOnlyID)); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{stage.Label}
	default:
		err = &NotSingularError{stage.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (sq *StageQuery) OnlyIDX(ctx context.Context) int {
	id, err := sq.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of Stages.
func (sq *StageQuery) All(ctx context.Context) ([]*Stage, error) {
	ctx = setContextOp(ctx, sq.ctx, ent.OpQueryAll)
	if err := sq.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*Stage, *StageQuery]()
	return withInterceptors[[]*Stage](ctx, sq, qr, sq.inters)
}

// AllX is like All, but panics if an error occurs.
func (sq *StageQuery) AllX(ctx context.Context) []*Stage {
	nodes, err := sq.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of Stage IDs.
func (sq *StageQuery) IDs(ctx context.Context) (ids []int, err error) {
	if sq.ctx.Unique == nil && sq.path != nil {
		sq.Unique(true)
	}
	ctx = setContextOp(ctx, sq.ctx, ent.OpQueryIDs)
	if err = sq.Select(stage.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (sq *StageQuery) IDsX(ctx context.Context) []int {
	ids, err := sq.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (sq *StageQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, sq.ctx, ent.OpQueryCount)
	if err := sq.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, sq, querierCount[*StageQuery](), sq.inters)
}

// CountX is like Count, but panics if an error occurs.
func (sq *StageQuery) CountX(ctx context.Context) int {
	count, err := sq.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (sq *StageQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, sq.ctx, ent.OpQueryExist)
	switch _, err := sq.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("ent: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (sq *StageQuery) ExistX(ctx context.Context) bool {
	exist, err := sq.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the StageQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (sq *StageQuery) Clone() *StageQuery {
	if sq == nil {
		return nil
	}
	return &StageQuery{
		config:       sq.config,
		ctx:          sq.ctx.Clone(),
		order:        append([]stage.OrderOption{}, sq.order...),
		inters:       append([]Interceptor{}, sq.inters...),
		predicates:   append([]predicate.Stage{}, sq.predicates...),
		withParent:   sq.withParent.Clone(),
		withChildren: sq.withChildren.Clone(),
		withDeals:    sq.withDeals.Clone(),
		withPipeline: sq.withPipeline.Clone(),
		// clone intermediate query.
		sql:       sq.sql.Clone(),
		path:      sq.path,
		modifiers: append([]func(*sql.Selector){}, sq.modifiers...),
	}
}

// WithParent tells the query-builder to eager-load the nodes that are connected to
// the "parent" edge. The optional arguments are used to configure the query builder of the edge.
func (sq *StageQuery) WithParent(opts ...func(*StageQuery)) *StageQuery {
	query := (&StageClient{config: sq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	sq.withParent = query
	return sq
}

// WithChildren tells the query-builder to eager-load the nodes that are connected to
// the "children" edge. The optional arguments are used to configure the query builder of the edge.
func (sq *StageQuery) WithChildren(opts ...func(*StageQuery)) *StageQuery {
	query := (&StageClient{config: sq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	sq.withChildren = query
	return sq
}

// WithDeals tells the query-builder to eager-load the nodes that are connected to
// the "deals" edge. The optional arguments are used to configure the query builder of the edge.
func (sq *StageQuery) WithDeals(opts ...func(*DealQuery)) *StageQuery {
	query := (&DealClient{config: sq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	sq.withDeals = query
	return sq
}

// WithPipeline tells the query-builder to eager-load the nodes that are connected to
// the "pipeline" edge. The optional arguments are used to configure the query builder of the edge.
func (sq *StageQuery) WithPipeline(opts ...func(*PipelineQuery)) *StageQuery {
	query := (&PipelineClient{config: sq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	sq.withPipeline = query
	return sq
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		DeletedAt time.Time `json:"deleted_at,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.Stage.Query().
//		GroupBy(stage.FieldDeletedAt).
//		Aggregate(ent.Count()).
//		Scan(ctx, &v)
func (sq *StageQuery) GroupBy(field string, fields ...string) *StageGroupBy {
	sq.ctx.Fields = append([]string{field}, fields...)
	grbuild := &StageGroupBy{build: sq}
	grbuild.flds = &sq.ctx.Fields
	grbuild.label = stage.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		DeletedAt time.Time `json:"deleted_at,omitempty"`
//	}
//
//	client.Stage.Query().
//		Select(stage.FieldDeletedAt).
//		Scan(ctx, &v)
func (sq *StageQuery) Select(fields ...string) *StageSelect {
	sq.ctx.Fields = append(sq.ctx.Fields, fields...)
	sbuild := &StageSelect{StageQuery: sq}
	sbuild.label = stage.Label
	sbuild.flds, sbuild.scan = &sq.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a StageSelect configured with the given aggregations.
func (sq *StageQuery) Aggregate(fns ...AggregateFunc) *StageSelect {
	return sq.Select().Aggregate(fns...)
}

func (sq *StageQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range sq.inters {
		if inter == nil {
			return fmt.Errorf("ent: uninitialized interceptor (forgotten import ent/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, sq); err != nil {
				return err
			}
		}
	}
	for _, f := range sq.ctx.Fields {
		if !stage.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
		}
	}
	if sq.path != nil {
		prev, err := sq.path(ctx)
		if err != nil {
			return err
		}
		sq.sql = prev
	}
	return nil
}

func (sq *StageQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*Stage, error) {
	var (
		nodes       = []*Stage{}
		_spec       = sq.querySpec()
		loadedTypes = [4]bool{
			sq.withParent != nil,
			sq.withChildren != nil,
			sq.withDeals != nil,
			sq.withPipeline != nil,
		}
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*Stage).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &Stage{config: sq.config}
		nodes = append(nodes, node)
		node.Edges.loadedTypes = loadedTypes
		return node.assignValues(columns, values)
	}
	if len(sq.modifiers) > 0 {
		_spec.Modifiers = sq.modifiers
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, sq.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	if query := sq.withParent; query != nil {
		if err := sq.loadParent(ctx, query, nodes, nil,
			func(n *Stage, e *Stage) { n.Edges.Parent = e }); err != nil {
			return nil, err
		}
	}
	if query := sq.withChildren; query != nil {
		if err := sq.loadChildren(ctx, query, nodes,
			func(n *Stage) { n.Edges.Children = []*Stage{} },
			func(n *Stage, e *Stage) { n.Edges.Children = append(n.Edges.Children, e) }); err != nil {
			return nil, err
		}
	}
	if query := sq.withDeals; query != nil {
		if err := sq.loadDeals(ctx, query, nodes,
			func(n *Stage) { n.Edges.Deals = []*Deal{} },
			func(n *Stage, e *Deal) { n.Edges.Deals = append(n.Edges.Deals, e) }); err != nil {
			return nil, err
		}
	}
	if query := sq.withPipeline; query != nil {
		if err := sq.loadPipeline(ctx, query, nodes, nil,
			func(n *Stage, e *Pipeline) { n.Edges.Pipeline = e }); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

func (sq *StageQuery) loadParent(ctx context.Context, query *StageQuery, nodes []*Stage, init func(*Stage), assign func(*Stage, *Stage)) error {
	ids := make([]int, 0, len(nodes))
	nodeids := make(map[int][]*Stage)
	for i := range nodes {
		if nodes[i].ParentStageID == nil {
			continue
		}
		fk := *nodes[i].ParentStageID
		if _, ok := nodeids[fk]; !ok {
			ids = append(ids, fk)
		}
		nodeids[fk] = append(nodeids[fk], nodes[i])
	}
	if len(ids) == 0 {
		return nil
	}
	query.Where(stage.IDIn(ids...))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		nodes, ok := nodeids[n.ID]
		if !ok {
			return fmt.Errorf(`unexpected foreign-key "parent_stage_id" returned %v`, n.ID)
		}
		for i := range nodes {
			assign(nodes[i], n)
		}
	}
	return nil
}
func (sq *StageQuery) loadChildren(ctx context.Context, query *StageQuery, nodes []*Stage, init func(*Stage), assign func(*Stage, *Stage)) error {
	fks := make([]driver.Value, 0, len(nodes))
	nodeids := make(map[int]*Stage)
	for i := range nodes {
		fks = append(fks, nodes[i].ID)
		nodeids[nodes[i].ID] = nodes[i]
		if init != nil {
			init(nodes[i])
		}
	}
	if len(query.ctx.Fields) > 0 {
		query.ctx.AppendFieldOnce(stage.FieldParentStageID)
	}
	query.Where(predicate.Stage(func(s *sql.Selector) {
		s.Where(sql.InValues(s.C(stage.ChildrenColumn), fks...))
	}))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		fk := n.ParentStageID
		if fk == nil {
			return fmt.Errorf(`foreign-key "parent_stage_id" is nil for node %v`, n.ID)
		}
		node, ok := nodeids[*fk]
		if !ok {
			return fmt.Errorf(`unexpected referenced foreign-key "parent_stage_id" returned %v for node %v`, *fk, n.ID)
		}
		assign(node, n)
	}
	return nil
}
func (sq *StageQuery) loadDeals(ctx context.Context, query *DealQuery, nodes []*Stage, init func(*Stage), assign func(*Stage, *Deal)) error {
	fks := make([]driver.Value, 0, len(nodes))
	nodeids := make(map[int]*Stage)
	for i := range nodes {
		fks = append(fks, nodes[i].ID)
		nodeids[nodes[i].ID] = nodes[i]
		if init != nil {
			init(nodes[i])
		}
	}
	if len(query.ctx.Fields) > 0 {
		query.ctx.AppendFieldOnce(deal.FieldStageID)
	}
	query.Where(predicate.Deal(func(s *sql.Selector) {
		s.Where(sql.InValues(s.C(stage.DealsColumn), fks...))
	}))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		fk := n.StageID
		if fk == nil {
			return fmt.Errorf(`foreign-key "stage_id" is nil for node %v`, n.ID)
		}
		node, ok := nodeids[*fk]
		if !ok {
			return fmt.Errorf(`unexpected referenced foreign-key "stage_id" returned %v for node %v`, *fk, n.ID)
		}
		assign(node, n)
	}
	return nil
}
func (sq *StageQuery) loadPipeline(ctx context.Context, query *PipelineQuery, nodes []*Stage, init func(*Stage), assign func(*Stage, *Pipeline)) error {
	ids := make([]int, 0, len(nodes))
	nodeids := make(map[int][]*Stage)
	for i := range nodes {
		fk := nodes[i].PipelineID
		if _, ok := nodeids[fk]; !ok {
			ids = append(ids, fk)
		}
		nodeids[fk] = append(nodeids[fk], nodes[i])
	}
	if len(ids) == 0 {
		return nil
	}
	query.Where(pipeline.IDIn(ids...))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		nodes, ok := nodeids[n.ID]
		if !ok {
			return fmt.Errorf(`unexpected foreign-key "pipeline_id" returned %v`, n.ID)
		}
		for i := range nodes {
			assign(nodes[i], n)
		}
	}
	return nil
}

func (sq *StageQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := sq.querySpec()
	if len(sq.modifiers) > 0 {
		_spec.Modifiers = sq.modifiers
	}
	_spec.Node.Columns = sq.ctx.Fields
	if len(sq.ctx.Fields) > 0 {
		_spec.Unique = sq.ctx.Unique != nil && *sq.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, sq.driver, _spec)
}

func (sq *StageQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(stage.Table, stage.Columns, sqlgraph.NewFieldSpec(stage.FieldID, field.TypeInt))
	_spec.From = sq.sql
	if unique := sq.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if sq.path != nil {
		_spec.Unique = true
	}
	if fields := sq.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, stage.FieldID)
		for i := range fields {
			if fields[i] != stage.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
		if sq.withParent != nil {
			_spec.Node.AddColumnOnce(stage.FieldParentStageID)
		}
		if sq.withPipeline != nil {
			_spec.Node.AddColumnOnce(stage.FieldPipelineID)
		}
	}
	if ps := sq.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := sq.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := sq.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := sq.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (sq *StageQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(sq.driver.Dialect())
	t1 := builder.Table(stage.Table)
	columns := sq.ctx.Fields
	if len(columns) == 0 {
		columns = stage.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if sq.sql != nil {
		selector = sq.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if sq.ctx.Unique != nil && *sq.ctx.Unique {
		selector.Distinct()
	}
	for _, m := range sq.modifiers {
		m(selector)
	}
	for _, p := range sq.predicates {
		p(selector)
	}
	for _, p := range sq.order {
		p(selector)
	}
	if offset := sq.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := sq.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// Modify adds a query modifier for attaching custom logic to queries.
func (sq *StageQuery) Modify(modifiers ...func(s *sql.Selector)) *StageSelect {
	sq.modifiers = append(sq.modifiers, modifiers...)
	return sq.Select()
}

// StageGroupBy is the group-by builder for Stage entities.
type StageGroupBy struct {
	selector
	build *StageQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (sgb *StageGroupBy) Aggregate(fns ...AggregateFunc) *StageGroupBy {
	sgb.fns = append(sgb.fns, fns...)
	return sgb
}

// Scan applies the selector query and scans the result into the given value.
func (sgb *StageGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, sgb.build.ctx, ent.OpQueryGroupBy)
	if err := sgb.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*StageQuery, *StageGroupBy](ctx, sgb.build, sgb, sgb.build.inters, v)
}

func (sgb *StageGroupBy) sqlScan(ctx context.Context, root *StageQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(sgb.fns))
	for _, fn := range sgb.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*sgb.flds)+len(sgb.fns))
		for _, f := range *sgb.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*sgb.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := sgb.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// StageSelect is the builder for selecting fields of Stage entities.
type StageSelect struct {
	*StageQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (ss *StageSelect) Aggregate(fns ...AggregateFunc) *StageSelect {
	ss.fns = append(ss.fns, fns...)
	return ss
}

// Scan applies the selector query and scans the result into the given value.
func (ss *StageSelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, ss.ctx, ent.OpQuerySelect)
	if err := ss.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*StageQuery, *StageSelect](ctx, ss.StageQuery, ss, ss.inters, v)
}

func (ss *StageSelect) sqlScan(ctx context.Context, root *StageQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(ss.fns))
	for _, fn := range ss.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*ss.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := ss.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// Modify adds a query modifier for attaching custom logic to queries.
func (ss *StageSelect) Modify(modifiers ...func(s *sql.Selector)) *StageSelect {
	ss.modifiers = append(ss.modifiers, modifiers...)
	return ss
}
