// Code generated by ent, DO NOT EDIT.

package personqueryview

import (
	"entgo.io/ent/dialect/sql"
)

const (
	// Label holds the string label denoting the personqueryview type in the database.
	Label = "person_query_view"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldPersonCode holds the string denoting the person_code field in the database.
	FieldPersonCode = "person_code"
	// FieldFullName holds the string denoting the full_name field in the database.
	FieldFullName = "full_name"
	// FieldDateOfBirth holds the string denoting the date_of_birth field in the database.
	FieldDateOfBirth = "date_of_birth"
	// FieldCreatedAt holds the string denoting the created_at field in the database.
	FieldCreatedAt = "created_at"
	// FieldAppointmentTime holds the string denoting the appointment_time field in the database.
	FieldAppointmentTime = "appointment_time"
	// FieldDeletedAt holds the string denoting the deleted_at field in the database.
	FieldDeletedAt = "deleted_at"
	// FieldGender holds the string denoting the gender field in the database.
	FieldGender = "gender"
	// FieldProvinceID holds the string denoting the province_id field in the database.
	FieldProvinceID = "province_id"
	// FieldDistrictID holds the string denoting the district_id field in the database.
	FieldDistrictID = "district_id"
	// FieldWardID holds the string denoting the ward_id field in the database.
	FieldWardID = "ward_id"
	// FieldAddressNumber holds the string denoting the address_number field in the database.
	FieldAddressNumber = "address_number"
	// FieldPhone holds the string denoting the phone field in the database.
	FieldPhone = "phone"
	// FieldCreatorName holds the string denoting the creator_name field in the database.
	FieldCreatorName = "creator_name"
	// FieldPersonSource holds the string denoting the person_source field in the database.
	FieldPersonSource = "person_source"
	// FieldDoctorName holds the string denoting the doctor_name field in the database.
	FieldDoctorName = "doctor_name"
	// FieldTreatmentName holds the string denoting the treatment_name field in the database.
	FieldTreatmentName = "treatment_name"
	// FieldEmail holds the string denoting the email field in the database.
	FieldEmail = "email"
	// FieldJobID holds the string denoting the job_id field in the database.
	FieldJobID = "job_id"
	// FieldUserID holds the string denoting the user_id field in the database.
	FieldUserID = "user_id"
	// FieldSourceID holds the string denoting the source_id field in the database.
	FieldSourceID = "source_id"
	// FieldPhoneConfirm holds the string denoting the phone_confirm field in the database.
	FieldPhoneConfirm = "phone_confirm"
	// FieldMailConfirm holds the string denoting the mail_confirm field in the database.
	FieldMailConfirm = "mail_confirm"
	// FieldPersonField holds the string denoting the person_field field in the database.
	FieldPersonField = "person_field"
	// FieldStageName holds the string denoting the stage_name field in the database.
	FieldStageName = "stage_name"
	// FieldBankAccountName holds the string denoting the bank_account_name field in the database.
	FieldBankAccountName = "bank_account_name"
	// FieldBankAccountNumber holds the string denoting the bank_account_number field in the database.
	FieldBankAccountNumber = "bank_account_number"
	// FieldBank holds the string denoting the bank field in the database.
	FieldBank = "bank"
	// FieldBankBranch holds the string denoting the bank_branch field in the database.
	FieldBankBranch = "bank_branch"
	// FieldStageID holds the string denoting the stage_id field in the database.
	FieldStageID = "stage_id"
	// FieldStageParentID holds the string denoting the stage_parent_id field in the database.
	FieldStageParentID = "stage_parent_id"
	// FieldSaleID holds the string denoting the sale_id field in the database.
	FieldSaleID = "sale_id"
	// FieldSale holds the string denoting the sale field in the database.
	FieldSale = "sale"
	// Table holds the table name of the personqueryview in the database.
	Table = "person_query_views"
)

// Columns holds all SQL columns for personqueryview fields.
var Columns = []string{
	FieldID,
	FieldPersonCode,
	FieldFullName,
	FieldDateOfBirth,
	FieldCreatedAt,
	FieldAppointmentTime,
	FieldDeletedAt,
	FieldGender,
	FieldProvinceID,
	FieldDistrictID,
	FieldWardID,
	FieldAddressNumber,
	FieldPhone,
	FieldCreatorName,
	FieldPersonSource,
	FieldDoctorName,
	FieldTreatmentName,
	FieldEmail,
	FieldJobID,
	FieldUserID,
	FieldSourceID,
	FieldPhoneConfirm,
	FieldMailConfirm,
	FieldPersonField,
	FieldStageName,
	FieldBankAccountName,
	FieldBankAccountNumber,
	FieldBank,
	FieldBankBranch,
	FieldStageID,
	FieldStageParentID,
	FieldSaleID,
	FieldSale,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

// OrderOption defines the ordering options for the PersonQueryView queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByPersonCode orders the results by the person_code field.
func ByPersonCode(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPersonCode, opts...).ToFunc()
}

// ByFullName orders the results by the full_name field.
func ByFullName(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldFullName, opts...).ToFunc()
}

// ByDateOfBirth orders the results by the date_of_birth field.
func ByDateOfBirth(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDateOfBirth, opts...).ToFunc()
}

// ByCreatedAt orders the results by the created_at field.
func ByCreatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedAt, opts...).ToFunc()
}

// ByAppointmentTime orders the results by the appointment_time field.
func ByAppointmentTime(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldAppointmentTime, opts...).ToFunc()
}

// ByDeletedAt orders the results by the deleted_at field.
func ByDeletedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDeletedAt, opts...).ToFunc()
}

// ByGender orders the results by the gender field.
func ByGender(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldGender, opts...).ToFunc()
}

// ByProvinceID orders the results by the province_id field.
func ByProvinceID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldProvinceID, opts...).ToFunc()
}

// ByDistrictID orders the results by the district_id field.
func ByDistrictID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDistrictID, opts...).ToFunc()
}

// ByWardID orders the results by the ward_id field.
func ByWardID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldWardID, opts...).ToFunc()
}

// ByAddressNumber orders the results by the address_number field.
func ByAddressNumber(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldAddressNumber, opts...).ToFunc()
}

// ByPhone orders the results by the phone field.
func ByPhone(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPhone, opts...).ToFunc()
}

// ByCreatorName orders the results by the creator_name field.
func ByCreatorName(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatorName, opts...).ToFunc()
}

// ByPersonSource orders the results by the person_source field.
func ByPersonSource(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPersonSource, opts...).ToFunc()
}

// ByDoctorName orders the results by the doctor_name field.
func ByDoctorName(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDoctorName, opts...).ToFunc()
}

// ByTreatmentName orders the results by the treatment_name field.
func ByTreatmentName(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldTreatmentName, opts...).ToFunc()
}

// ByEmail orders the results by the email field.
func ByEmail(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldEmail, opts...).ToFunc()
}

// ByJobID orders the results by the job_id field.
func ByJobID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldJobID, opts...).ToFunc()
}

// ByUserID orders the results by the user_id field.
func ByUserID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUserID, opts...).ToFunc()
}

// BySourceID orders the results by the source_id field.
func BySourceID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldSourceID, opts...).ToFunc()
}

// ByPhoneConfirm orders the results by the phone_confirm field.
func ByPhoneConfirm(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPhoneConfirm, opts...).ToFunc()
}

// ByMailConfirm orders the results by the mail_confirm field.
func ByMailConfirm(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldMailConfirm, opts...).ToFunc()
}

// ByStageName orders the results by the stage_name field.
func ByStageName(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldStageName, opts...).ToFunc()
}

// ByBankAccountName orders the results by the bank_account_name field.
func ByBankAccountName(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldBankAccountName, opts...).ToFunc()
}

// ByBankAccountNumber orders the results by the bank_account_number field.
func ByBankAccountNumber(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldBankAccountNumber, opts...).ToFunc()
}

// ByBank orders the results by the bank field.
func ByBank(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldBank, opts...).ToFunc()
}

// ByBankBranch orders the results by the bank_branch field.
func ByBankBranch(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldBankBranch, opts...).ToFunc()
}

// ByStageID orders the results by the stage_id field.
func ByStageID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldStageID, opts...).ToFunc()
}

// ByStageParentID orders the results by the stage_parent_id field.
func ByStageParentID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldStageParentID, opts...).ToFunc()
}

// BySaleID orders the results by the sale_id field.
func BySaleID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldSaleID, opts...).ToFunc()
}
