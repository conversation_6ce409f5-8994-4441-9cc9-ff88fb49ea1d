// Code generated by ent, DO NOT EDIT.

package personqueryview

import (
	"bcare/ent/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
)

// ID applies equality check predicate on the "id" field. It's identical to IDEQ.
func ID(v int) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldEQ(FieldID, v))
}

// PersonCode applies equality check predicate on the "person_code" field. It's identical to PersonCodeEQ.
func PersonCode(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldEQ(FieldPersonCode, v))
}

// FullName applies equality check predicate on the "full_name" field. It's identical to FullNameEQ.
func FullName(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldEQ(FieldFullName, v))
}

// DateOfBirth applies equality check predicate on the "date_of_birth" field. It's identical to DateOfBirthEQ.
func DateOfBirth(v time.Time) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldEQ(FieldDateOfBirth, v))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldEQ(FieldCreatedAt, v))
}

// AppointmentTime applies equality check predicate on the "appointment_time" field. It's identical to AppointmentTimeEQ.
func AppointmentTime(v time.Time) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldEQ(FieldAppointmentTime, v))
}

// DeletedAt applies equality check predicate on the "deleted_at" field. It's identical to DeletedAtEQ.
func DeletedAt(v time.Time) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldEQ(FieldDeletedAt, v))
}

// Gender applies equality check predicate on the "gender" field. It's identical to GenderEQ.
func Gender(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldEQ(FieldGender, v))
}

// ProvinceID applies equality check predicate on the "province_id" field. It's identical to ProvinceIDEQ.
func ProvinceID(v int) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldEQ(FieldProvinceID, v))
}

// DistrictID applies equality check predicate on the "district_id" field. It's identical to DistrictIDEQ.
func DistrictID(v int) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldEQ(FieldDistrictID, v))
}

// WardID applies equality check predicate on the "ward_id" field. It's identical to WardIDEQ.
func WardID(v int) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldEQ(FieldWardID, v))
}

// AddressNumber applies equality check predicate on the "address_number" field. It's identical to AddressNumberEQ.
func AddressNumber(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldEQ(FieldAddressNumber, v))
}

// Phone applies equality check predicate on the "phone" field. It's identical to PhoneEQ.
func Phone(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldEQ(FieldPhone, v))
}

// CreatorName applies equality check predicate on the "creator_name" field. It's identical to CreatorNameEQ.
func CreatorName(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldEQ(FieldCreatorName, v))
}

// PersonSource applies equality check predicate on the "person_source" field. It's identical to PersonSourceEQ.
func PersonSource(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldEQ(FieldPersonSource, v))
}

// DoctorName applies equality check predicate on the "doctor_name" field. It's identical to DoctorNameEQ.
func DoctorName(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldEQ(FieldDoctorName, v))
}

// TreatmentName applies equality check predicate on the "treatment_name" field. It's identical to TreatmentNameEQ.
func TreatmentName(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldEQ(FieldTreatmentName, v))
}

// Email applies equality check predicate on the "email" field. It's identical to EmailEQ.
func Email(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldEQ(FieldEmail, v))
}

// JobID applies equality check predicate on the "job_id" field. It's identical to JobIDEQ.
func JobID(v int) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldEQ(FieldJobID, v))
}

// UserID applies equality check predicate on the "user_id" field. It's identical to UserIDEQ.
func UserID(v int) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldEQ(FieldUserID, v))
}

// SourceID applies equality check predicate on the "source_id" field. It's identical to SourceIDEQ.
func SourceID(v int) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldEQ(FieldSourceID, v))
}

// PhoneConfirm applies equality check predicate on the "phone_confirm" field. It's identical to PhoneConfirmEQ.
func PhoneConfirm(v bool) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldEQ(FieldPhoneConfirm, v))
}

// MailConfirm applies equality check predicate on the "mail_confirm" field. It's identical to MailConfirmEQ.
func MailConfirm(v bool) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldEQ(FieldMailConfirm, v))
}

// StageName applies equality check predicate on the "stage_name" field. It's identical to StageNameEQ.
func StageName(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldEQ(FieldStageName, v))
}

// BankAccountName applies equality check predicate on the "bank_account_name" field. It's identical to BankAccountNameEQ.
func BankAccountName(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldEQ(FieldBankAccountName, v))
}

// BankAccountNumber applies equality check predicate on the "bank_account_number" field. It's identical to BankAccountNumberEQ.
func BankAccountNumber(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldEQ(FieldBankAccountNumber, v))
}

// Bank applies equality check predicate on the "bank" field. It's identical to BankEQ.
func Bank(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldEQ(FieldBank, v))
}

// BankBranch applies equality check predicate on the "bank_branch" field. It's identical to BankBranchEQ.
func BankBranch(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldEQ(FieldBankBranch, v))
}

// StageID applies equality check predicate on the "stage_id" field. It's identical to StageIDEQ.
func StageID(v int) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldEQ(FieldStageID, v))
}

// StageParentID applies equality check predicate on the "stage_parent_id" field. It's identical to StageParentIDEQ.
func StageParentID(v int) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldEQ(FieldStageParentID, v))
}

// SaleID applies equality check predicate on the "sale_id" field. It's identical to SaleIDEQ.
func SaleID(v int) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldEQ(FieldSaleID, v))
}

// IDEQ applies the EQ predicate on the "id" field.
func IDEQ(v int) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldEQ(FieldID, v))
}

// IDNEQ applies the NEQ predicate on the "id" field.
func IDNEQ(v int) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldNEQ(FieldID, v))
}

// IDIn applies the In predicate on the "id" field.
func IDIn(vs ...int) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldIn(FieldID, vs...))
}

// IDNotIn applies the NotIn predicate on the "id" field.
func IDNotIn(vs ...int) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldNotIn(FieldID, vs...))
}

// IDGT applies the GT predicate on the "id" field.
func IDGT(v int) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldGT(FieldID, v))
}

// IDGTE applies the GTE predicate on the "id" field.
func IDGTE(v int) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldGTE(FieldID, v))
}

// IDLT applies the LT predicate on the "id" field.
func IDLT(v int) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldLT(FieldID, v))
}

// IDLTE applies the LTE predicate on the "id" field.
func IDLTE(v int) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldLTE(FieldID, v))
}

// IDIsNil applies the IsNil predicate on the "id" field.
func IDIsNil() predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldIsNull(FieldID))
}

// IDNotNil applies the NotNil predicate on the "id" field.
func IDNotNil() predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldNotNull(FieldID))
}

// PersonCodeEQ applies the EQ predicate on the "person_code" field.
func PersonCodeEQ(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldEQ(FieldPersonCode, v))
}

// PersonCodeNEQ applies the NEQ predicate on the "person_code" field.
func PersonCodeNEQ(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldNEQ(FieldPersonCode, v))
}

// PersonCodeIn applies the In predicate on the "person_code" field.
func PersonCodeIn(vs ...string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldIn(FieldPersonCode, vs...))
}

// PersonCodeNotIn applies the NotIn predicate on the "person_code" field.
func PersonCodeNotIn(vs ...string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldNotIn(FieldPersonCode, vs...))
}

// PersonCodeGT applies the GT predicate on the "person_code" field.
func PersonCodeGT(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldGT(FieldPersonCode, v))
}

// PersonCodeGTE applies the GTE predicate on the "person_code" field.
func PersonCodeGTE(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldGTE(FieldPersonCode, v))
}

// PersonCodeLT applies the LT predicate on the "person_code" field.
func PersonCodeLT(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldLT(FieldPersonCode, v))
}

// PersonCodeLTE applies the LTE predicate on the "person_code" field.
func PersonCodeLTE(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldLTE(FieldPersonCode, v))
}

// PersonCodeContains applies the Contains predicate on the "person_code" field.
func PersonCodeContains(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldContains(FieldPersonCode, v))
}

// PersonCodeHasPrefix applies the HasPrefix predicate on the "person_code" field.
func PersonCodeHasPrefix(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldHasPrefix(FieldPersonCode, v))
}

// PersonCodeHasSuffix applies the HasSuffix predicate on the "person_code" field.
func PersonCodeHasSuffix(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldHasSuffix(FieldPersonCode, v))
}

// PersonCodeEqualFold applies the EqualFold predicate on the "person_code" field.
func PersonCodeEqualFold(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldEqualFold(FieldPersonCode, v))
}

// PersonCodeContainsFold applies the ContainsFold predicate on the "person_code" field.
func PersonCodeContainsFold(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldContainsFold(FieldPersonCode, v))
}

// FullNameEQ applies the EQ predicate on the "full_name" field.
func FullNameEQ(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldEQ(FieldFullName, v))
}

// FullNameNEQ applies the NEQ predicate on the "full_name" field.
func FullNameNEQ(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldNEQ(FieldFullName, v))
}

// FullNameIn applies the In predicate on the "full_name" field.
func FullNameIn(vs ...string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldIn(FieldFullName, vs...))
}

// FullNameNotIn applies the NotIn predicate on the "full_name" field.
func FullNameNotIn(vs ...string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldNotIn(FieldFullName, vs...))
}

// FullNameGT applies the GT predicate on the "full_name" field.
func FullNameGT(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldGT(FieldFullName, v))
}

// FullNameGTE applies the GTE predicate on the "full_name" field.
func FullNameGTE(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldGTE(FieldFullName, v))
}

// FullNameLT applies the LT predicate on the "full_name" field.
func FullNameLT(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldLT(FieldFullName, v))
}

// FullNameLTE applies the LTE predicate on the "full_name" field.
func FullNameLTE(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldLTE(FieldFullName, v))
}

// FullNameContains applies the Contains predicate on the "full_name" field.
func FullNameContains(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldContains(FieldFullName, v))
}

// FullNameHasPrefix applies the HasPrefix predicate on the "full_name" field.
func FullNameHasPrefix(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldHasPrefix(FieldFullName, v))
}

// FullNameHasSuffix applies the HasSuffix predicate on the "full_name" field.
func FullNameHasSuffix(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldHasSuffix(FieldFullName, v))
}

// FullNameEqualFold applies the EqualFold predicate on the "full_name" field.
func FullNameEqualFold(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldEqualFold(FieldFullName, v))
}

// FullNameContainsFold applies the ContainsFold predicate on the "full_name" field.
func FullNameContainsFold(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldContainsFold(FieldFullName, v))
}

// DateOfBirthEQ applies the EQ predicate on the "date_of_birth" field.
func DateOfBirthEQ(v time.Time) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldEQ(FieldDateOfBirth, v))
}

// DateOfBirthNEQ applies the NEQ predicate on the "date_of_birth" field.
func DateOfBirthNEQ(v time.Time) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldNEQ(FieldDateOfBirth, v))
}

// DateOfBirthIn applies the In predicate on the "date_of_birth" field.
func DateOfBirthIn(vs ...time.Time) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldIn(FieldDateOfBirth, vs...))
}

// DateOfBirthNotIn applies the NotIn predicate on the "date_of_birth" field.
func DateOfBirthNotIn(vs ...time.Time) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldNotIn(FieldDateOfBirth, vs...))
}

// DateOfBirthGT applies the GT predicate on the "date_of_birth" field.
func DateOfBirthGT(v time.Time) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldGT(FieldDateOfBirth, v))
}

// DateOfBirthGTE applies the GTE predicate on the "date_of_birth" field.
func DateOfBirthGTE(v time.Time) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldGTE(FieldDateOfBirth, v))
}

// DateOfBirthLT applies the LT predicate on the "date_of_birth" field.
func DateOfBirthLT(v time.Time) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldLT(FieldDateOfBirth, v))
}

// DateOfBirthLTE applies the LTE predicate on the "date_of_birth" field.
func DateOfBirthLTE(v time.Time) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldLTE(FieldDateOfBirth, v))
}

// DateOfBirthIsNil applies the IsNil predicate on the "date_of_birth" field.
func DateOfBirthIsNil() predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldIsNull(FieldDateOfBirth))
}

// DateOfBirthNotNil applies the NotNil predicate on the "date_of_birth" field.
func DateOfBirthNotNil() predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldNotNull(FieldDateOfBirth))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldLTE(FieldCreatedAt, v))
}

// CreatedAtIsNil applies the IsNil predicate on the "created_at" field.
func CreatedAtIsNil() predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldIsNull(FieldCreatedAt))
}

// CreatedAtNotNil applies the NotNil predicate on the "created_at" field.
func CreatedAtNotNil() predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldNotNull(FieldCreatedAt))
}

// AppointmentTimeEQ applies the EQ predicate on the "appointment_time" field.
func AppointmentTimeEQ(v time.Time) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldEQ(FieldAppointmentTime, v))
}

// AppointmentTimeNEQ applies the NEQ predicate on the "appointment_time" field.
func AppointmentTimeNEQ(v time.Time) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldNEQ(FieldAppointmentTime, v))
}

// AppointmentTimeIn applies the In predicate on the "appointment_time" field.
func AppointmentTimeIn(vs ...time.Time) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldIn(FieldAppointmentTime, vs...))
}

// AppointmentTimeNotIn applies the NotIn predicate on the "appointment_time" field.
func AppointmentTimeNotIn(vs ...time.Time) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldNotIn(FieldAppointmentTime, vs...))
}

// AppointmentTimeGT applies the GT predicate on the "appointment_time" field.
func AppointmentTimeGT(v time.Time) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldGT(FieldAppointmentTime, v))
}

// AppointmentTimeGTE applies the GTE predicate on the "appointment_time" field.
func AppointmentTimeGTE(v time.Time) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldGTE(FieldAppointmentTime, v))
}

// AppointmentTimeLT applies the LT predicate on the "appointment_time" field.
func AppointmentTimeLT(v time.Time) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldLT(FieldAppointmentTime, v))
}

// AppointmentTimeLTE applies the LTE predicate on the "appointment_time" field.
func AppointmentTimeLTE(v time.Time) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldLTE(FieldAppointmentTime, v))
}

// AppointmentTimeIsNil applies the IsNil predicate on the "appointment_time" field.
func AppointmentTimeIsNil() predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldIsNull(FieldAppointmentTime))
}

// AppointmentTimeNotNil applies the NotNil predicate on the "appointment_time" field.
func AppointmentTimeNotNil() predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldNotNull(FieldAppointmentTime))
}

// DeletedAtEQ applies the EQ predicate on the "deleted_at" field.
func DeletedAtEQ(v time.Time) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldEQ(FieldDeletedAt, v))
}

// DeletedAtNEQ applies the NEQ predicate on the "deleted_at" field.
func DeletedAtNEQ(v time.Time) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldNEQ(FieldDeletedAt, v))
}

// DeletedAtIn applies the In predicate on the "deleted_at" field.
func DeletedAtIn(vs ...time.Time) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldIn(FieldDeletedAt, vs...))
}

// DeletedAtNotIn applies the NotIn predicate on the "deleted_at" field.
func DeletedAtNotIn(vs ...time.Time) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldNotIn(FieldDeletedAt, vs...))
}

// DeletedAtGT applies the GT predicate on the "deleted_at" field.
func DeletedAtGT(v time.Time) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldGT(FieldDeletedAt, v))
}

// DeletedAtGTE applies the GTE predicate on the "deleted_at" field.
func DeletedAtGTE(v time.Time) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldGTE(FieldDeletedAt, v))
}

// DeletedAtLT applies the LT predicate on the "deleted_at" field.
func DeletedAtLT(v time.Time) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldLT(FieldDeletedAt, v))
}

// DeletedAtLTE applies the LTE predicate on the "deleted_at" field.
func DeletedAtLTE(v time.Time) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldLTE(FieldDeletedAt, v))
}

// DeletedAtIsNil applies the IsNil predicate on the "deleted_at" field.
func DeletedAtIsNil() predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldIsNull(FieldDeletedAt))
}

// DeletedAtNotNil applies the NotNil predicate on the "deleted_at" field.
func DeletedAtNotNil() predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldNotNull(FieldDeletedAt))
}

// GenderEQ applies the EQ predicate on the "gender" field.
func GenderEQ(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldEQ(FieldGender, v))
}

// GenderNEQ applies the NEQ predicate on the "gender" field.
func GenderNEQ(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldNEQ(FieldGender, v))
}

// GenderIn applies the In predicate on the "gender" field.
func GenderIn(vs ...string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldIn(FieldGender, vs...))
}

// GenderNotIn applies the NotIn predicate on the "gender" field.
func GenderNotIn(vs ...string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldNotIn(FieldGender, vs...))
}

// GenderGT applies the GT predicate on the "gender" field.
func GenderGT(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldGT(FieldGender, v))
}

// GenderGTE applies the GTE predicate on the "gender" field.
func GenderGTE(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldGTE(FieldGender, v))
}

// GenderLT applies the LT predicate on the "gender" field.
func GenderLT(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldLT(FieldGender, v))
}

// GenderLTE applies the LTE predicate on the "gender" field.
func GenderLTE(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldLTE(FieldGender, v))
}

// GenderContains applies the Contains predicate on the "gender" field.
func GenderContains(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldContains(FieldGender, v))
}

// GenderHasPrefix applies the HasPrefix predicate on the "gender" field.
func GenderHasPrefix(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldHasPrefix(FieldGender, v))
}

// GenderHasSuffix applies the HasSuffix predicate on the "gender" field.
func GenderHasSuffix(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldHasSuffix(FieldGender, v))
}

// GenderIsNil applies the IsNil predicate on the "gender" field.
func GenderIsNil() predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldIsNull(FieldGender))
}

// GenderNotNil applies the NotNil predicate on the "gender" field.
func GenderNotNil() predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldNotNull(FieldGender))
}

// GenderEqualFold applies the EqualFold predicate on the "gender" field.
func GenderEqualFold(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldEqualFold(FieldGender, v))
}

// GenderContainsFold applies the ContainsFold predicate on the "gender" field.
func GenderContainsFold(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldContainsFold(FieldGender, v))
}

// ProvinceIDEQ applies the EQ predicate on the "province_id" field.
func ProvinceIDEQ(v int) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldEQ(FieldProvinceID, v))
}

// ProvinceIDNEQ applies the NEQ predicate on the "province_id" field.
func ProvinceIDNEQ(v int) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldNEQ(FieldProvinceID, v))
}

// ProvinceIDIn applies the In predicate on the "province_id" field.
func ProvinceIDIn(vs ...int) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldIn(FieldProvinceID, vs...))
}

// ProvinceIDNotIn applies the NotIn predicate on the "province_id" field.
func ProvinceIDNotIn(vs ...int) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldNotIn(FieldProvinceID, vs...))
}

// ProvinceIDGT applies the GT predicate on the "province_id" field.
func ProvinceIDGT(v int) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldGT(FieldProvinceID, v))
}

// ProvinceIDGTE applies the GTE predicate on the "province_id" field.
func ProvinceIDGTE(v int) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldGTE(FieldProvinceID, v))
}

// ProvinceIDLT applies the LT predicate on the "province_id" field.
func ProvinceIDLT(v int) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldLT(FieldProvinceID, v))
}

// ProvinceIDLTE applies the LTE predicate on the "province_id" field.
func ProvinceIDLTE(v int) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldLTE(FieldProvinceID, v))
}

// ProvinceIDIsNil applies the IsNil predicate on the "province_id" field.
func ProvinceIDIsNil() predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldIsNull(FieldProvinceID))
}

// ProvinceIDNotNil applies the NotNil predicate on the "province_id" field.
func ProvinceIDNotNil() predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldNotNull(FieldProvinceID))
}

// DistrictIDEQ applies the EQ predicate on the "district_id" field.
func DistrictIDEQ(v int) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldEQ(FieldDistrictID, v))
}

// DistrictIDNEQ applies the NEQ predicate on the "district_id" field.
func DistrictIDNEQ(v int) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldNEQ(FieldDistrictID, v))
}

// DistrictIDIn applies the In predicate on the "district_id" field.
func DistrictIDIn(vs ...int) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldIn(FieldDistrictID, vs...))
}

// DistrictIDNotIn applies the NotIn predicate on the "district_id" field.
func DistrictIDNotIn(vs ...int) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldNotIn(FieldDistrictID, vs...))
}

// DistrictIDGT applies the GT predicate on the "district_id" field.
func DistrictIDGT(v int) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldGT(FieldDistrictID, v))
}

// DistrictIDGTE applies the GTE predicate on the "district_id" field.
func DistrictIDGTE(v int) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldGTE(FieldDistrictID, v))
}

// DistrictIDLT applies the LT predicate on the "district_id" field.
func DistrictIDLT(v int) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldLT(FieldDistrictID, v))
}

// DistrictIDLTE applies the LTE predicate on the "district_id" field.
func DistrictIDLTE(v int) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldLTE(FieldDistrictID, v))
}

// DistrictIDIsNil applies the IsNil predicate on the "district_id" field.
func DistrictIDIsNil() predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldIsNull(FieldDistrictID))
}

// DistrictIDNotNil applies the NotNil predicate on the "district_id" field.
func DistrictIDNotNil() predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldNotNull(FieldDistrictID))
}

// WardIDEQ applies the EQ predicate on the "ward_id" field.
func WardIDEQ(v int) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldEQ(FieldWardID, v))
}

// WardIDNEQ applies the NEQ predicate on the "ward_id" field.
func WardIDNEQ(v int) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldNEQ(FieldWardID, v))
}

// WardIDIn applies the In predicate on the "ward_id" field.
func WardIDIn(vs ...int) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldIn(FieldWardID, vs...))
}

// WardIDNotIn applies the NotIn predicate on the "ward_id" field.
func WardIDNotIn(vs ...int) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldNotIn(FieldWardID, vs...))
}

// WardIDGT applies the GT predicate on the "ward_id" field.
func WardIDGT(v int) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldGT(FieldWardID, v))
}

// WardIDGTE applies the GTE predicate on the "ward_id" field.
func WardIDGTE(v int) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldGTE(FieldWardID, v))
}

// WardIDLT applies the LT predicate on the "ward_id" field.
func WardIDLT(v int) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldLT(FieldWardID, v))
}

// WardIDLTE applies the LTE predicate on the "ward_id" field.
func WardIDLTE(v int) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldLTE(FieldWardID, v))
}

// WardIDIsNil applies the IsNil predicate on the "ward_id" field.
func WardIDIsNil() predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldIsNull(FieldWardID))
}

// WardIDNotNil applies the NotNil predicate on the "ward_id" field.
func WardIDNotNil() predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldNotNull(FieldWardID))
}

// AddressNumberEQ applies the EQ predicate on the "address_number" field.
func AddressNumberEQ(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldEQ(FieldAddressNumber, v))
}

// AddressNumberNEQ applies the NEQ predicate on the "address_number" field.
func AddressNumberNEQ(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldNEQ(FieldAddressNumber, v))
}

// AddressNumberIn applies the In predicate on the "address_number" field.
func AddressNumberIn(vs ...string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldIn(FieldAddressNumber, vs...))
}

// AddressNumberNotIn applies the NotIn predicate on the "address_number" field.
func AddressNumberNotIn(vs ...string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldNotIn(FieldAddressNumber, vs...))
}

// AddressNumberGT applies the GT predicate on the "address_number" field.
func AddressNumberGT(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldGT(FieldAddressNumber, v))
}

// AddressNumberGTE applies the GTE predicate on the "address_number" field.
func AddressNumberGTE(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldGTE(FieldAddressNumber, v))
}

// AddressNumberLT applies the LT predicate on the "address_number" field.
func AddressNumberLT(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldLT(FieldAddressNumber, v))
}

// AddressNumberLTE applies the LTE predicate on the "address_number" field.
func AddressNumberLTE(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldLTE(FieldAddressNumber, v))
}

// AddressNumberContains applies the Contains predicate on the "address_number" field.
func AddressNumberContains(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldContains(FieldAddressNumber, v))
}

// AddressNumberHasPrefix applies the HasPrefix predicate on the "address_number" field.
func AddressNumberHasPrefix(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldHasPrefix(FieldAddressNumber, v))
}

// AddressNumberHasSuffix applies the HasSuffix predicate on the "address_number" field.
func AddressNumberHasSuffix(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldHasSuffix(FieldAddressNumber, v))
}

// AddressNumberIsNil applies the IsNil predicate on the "address_number" field.
func AddressNumberIsNil() predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldIsNull(FieldAddressNumber))
}

// AddressNumberNotNil applies the NotNil predicate on the "address_number" field.
func AddressNumberNotNil() predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldNotNull(FieldAddressNumber))
}

// AddressNumberEqualFold applies the EqualFold predicate on the "address_number" field.
func AddressNumberEqualFold(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldEqualFold(FieldAddressNumber, v))
}

// AddressNumberContainsFold applies the ContainsFold predicate on the "address_number" field.
func AddressNumberContainsFold(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldContainsFold(FieldAddressNumber, v))
}

// PhoneEQ applies the EQ predicate on the "phone" field.
func PhoneEQ(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldEQ(FieldPhone, v))
}

// PhoneNEQ applies the NEQ predicate on the "phone" field.
func PhoneNEQ(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldNEQ(FieldPhone, v))
}

// PhoneIn applies the In predicate on the "phone" field.
func PhoneIn(vs ...string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldIn(FieldPhone, vs...))
}

// PhoneNotIn applies the NotIn predicate on the "phone" field.
func PhoneNotIn(vs ...string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldNotIn(FieldPhone, vs...))
}

// PhoneGT applies the GT predicate on the "phone" field.
func PhoneGT(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldGT(FieldPhone, v))
}

// PhoneGTE applies the GTE predicate on the "phone" field.
func PhoneGTE(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldGTE(FieldPhone, v))
}

// PhoneLT applies the LT predicate on the "phone" field.
func PhoneLT(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldLT(FieldPhone, v))
}

// PhoneLTE applies the LTE predicate on the "phone" field.
func PhoneLTE(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldLTE(FieldPhone, v))
}

// PhoneContains applies the Contains predicate on the "phone" field.
func PhoneContains(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldContains(FieldPhone, v))
}

// PhoneHasPrefix applies the HasPrefix predicate on the "phone" field.
func PhoneHasPrefix(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldHasPrefix(FieldPhone, v))
}

// PhoneHasSuffix applies the HasSuffix predicate on the "phone" field.
func PhoneHasSuffix(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldHasSuffix(FieldPhone, v))
}

// PhoneEqualFold applies the EqualFold predicate on the "phone" field.
func PhoneEqualFold(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldEqualFold(FieldPhone, v))
}

// PhoneContainsFold applies the ContainsFold predicate on the "phone" field.
func PhoneContainsFold(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldContainsFold(FieldPhone, v))
}

// CreatorNameEQ applies the EQ predicate on the "creator_name" field.
func CreatorNameEQ(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldEQ(FieldCreatorName, v))
}

// CreatorNameNEQ applies the NEQ predicate on the "creator_name" field.
func CreatorNameNEQ(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldNEQ(FieldCreatorName, v))
}

// CreatorNameIn applies the In predicate on the "creator_name" field.
func CreatorNameIn(vs ...string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldIn(FieldCreatorName, vs...))
}

// CreatorNameNotIn applies the NotIn predicate on the "creator_name" field.
func CreatorNameNotIn(vs ...string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldNotIn(FieldCreatorName, vs...))
}

// CreatorNameGT applies the GT predicate on the "creator_name" field.
func CreatorNameGT(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldGT(FieldCreatorName, v))
}

// CreatorNameGTE applies the GTE predicate on the "creator_name" field.
func CreatorNameGTE(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldGTE(FieldCreatorName, v))
}

// CreatorNameLT applies the LT predicate on the "creator_name" field.
func CreatorNameLT(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldLT(FieldCreatorName, v))
}

// CreatorNameLTE applies the LTE predicate on the "creator_name" field.
func CreatorNameLTE(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldLTE(FieldCreatorName, v))
}

// CreatorNameContains applies the Contains predicate on the "creator_name" field.
func CreatorNameContains(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldContains(FieldCreatorName, v))
}

// CreatorNameHasPrefix applies the HasPrefix predicate on the "creator_name" field.
func CreatorNameHasPrefix(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldHasPrefix(FieldCreatorName, v))
}

// CreatorNameHasSuffix applies the HasSuffix predicate on the "creator_name" field.
func CreatorNameHasSuffix(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldHasSuffix(FieldCreatorName, v))
}

// CreatorNameEqualFold applies the EqualFold predicate on the "creator_name" field.
func CreatorNameEqualFold(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldEqualFold(FieldCreatorName, v))
}

// CreatorNameContainsFold applies the ContainsFold predicate on the "creator_name" field.
func CreatorNameContainsFold(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldContainsFold(FieldCreatorName, v))
}

// PersonSourceEQ applies the EQ predicate on the "person_source" field.
func PersonSourceEQ(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldEQ(FieldPersonSource, v))
}

// PersonSourceNEQ applies the NEQ predicate on the "person_source" field.
func PersonSourceNEQ(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldNEQ(FieldPersonSource, v))
}

// PersonSourceIn applies the In predicate on the "person_source" field.
func PersonSourceIn(vs ...string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldIn(FieldPersonSource, vs...))
}

// PersonSourceNotIn applies the NotIn predicate on the "person_source" field.
func PersonSourceNotIn(vs ...string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldNotIn(FieldPersonSource, vs...))
}

// PersonSourceGT applies the GT predicate on the "person_source" field.
func PersonSourceGT(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldGT(FieldPersonSource, v))
}

// PersonSourceGTE applies the GTE predicate on the "person_source" field.
func PersonSourceGTE(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldGTE(FieldPersonSource, v))
}

// PersonSourceLT applies the LT predicate on the "person_source" field.
func PersonSourceLT(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldLT(FieldPersonSource, v))
}

// PersonSourceLTE applies the LTE predicate on the "person_source" field.
func PersonSourceLTE(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldLTE(FieldPersonSource, v))
}

// PersonSourceContains applies the Contains predicate on the "person_source" field.
func PersonSourceContains(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldContains(FieldPersonSource, v))
}

// PersonSourceHasPrefix applies the HasPrefix predicate on the "person_source" field.
func PersonSourceHasPrefix(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldHasPrefix(FieldPersonSource, v))
}

// PersonSourceHasSuffix applies the HasSuffix predicate on the "person_source" field.
func PersonSourceHasSuffix(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldHasSuffix(FieldPersonSource, v))
}

// PersonSourceEqualFold applies the EqualFold predicate on the "person_source" field.
func PersonSourceEqualFold(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldEqualFold(FieldPersonSource, v))
}

// PersonSourceContainsFold applies the ContainsFold predicate on the "person_source" field.
func PersonSourceContainsFold(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldContainsFold(FieldPersonSource, v))
}

// DoctorNameEQ applies the EQ predicate on the "doctor_name" field.
func DoctorNameEQ(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldEQ(FieldDoctorName, v))
}

// DoctorNameNEQ applies the NEQ predicate on the "doctor_name" field.
func DoctorNameNEQ(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldNEQ(FieldDoctorName, v))
}

// DoctorNameIn applies the In predicate on the "doctor_name" field.
func DoctorNameIn(vs ...string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldIn(FieldDoctorName, vs...))
}

// DoctorNameNotIn applies the NotIn predicate on the "doctor_name" field.
func DoctorNameNotIn(vs ...string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldNotIn(FieldDoctorName, vs...))
}

// DoctorNameGT applies the GT predicate on the "doctor_name" field.
func DoctorNameGT(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldGT(FieldDoctorName, v))
}

// DoctorNameGTE applies the GTE predicate on the "doctor_name" field.
func DoctorNameGTE(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldGTE(FieldDoctorName, v))
}

// DoctorNameLT applies the LT predicate on the "doctor_name" field.
func DoctorNameLT(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldLT(FieldDoctorName, v))
}

// DoctorNameLTE applies the LTE predicate on the "doctor_name" field.
func DoctorNameLTE(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldLTE(FieldDoctorName, v))
}

// DoctorNameContains applies the Contains predicate on the "doctor_name" field.
func DoctorNameContains(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldContains(FieldDoctorName, v))
}

// DoctorNameHasPrefix applies the HasPrefix predicate on the "doctor_name" field.
func DoctorNameHasPrefix(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldHasPrefix(FieldDoctorName, v))
}

// DoctorNameHasSuffix applies the HasSuffix predicate on the "doctor_name" field.
func DoctorNameHasSuffix(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldHasSuffix(FieldDoctorName, v))
}

// DoctorNameEqualFold applies the EqualFold predicate on the "doctor_name" field.
func DoctorNameEqualFold(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldEqualFold(FieldDoctorName, v))
}

// DoctorNameContainsFold applies the ContainsFold predicate on the "doctor_name" field.
func DoctorNameContainsFold(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldContainsFold(FieldDoctorName, v))
}

// TreatmentNameEQ applies the EQ predicate on the "treatment_name" field.
func TreatmentNameEQ(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldEQ(FieldTreatmentName, v))
}

// TreatmentNameNEQ applies the NEQ predicate on the "treatment_name" field.
func TreatmentNameNEQ(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldNEQ(FieldTreatmentName, v))
}

// TreatmentNameIn applies the In predicate on the "treatment_name" field.
func TreatmentNameIn(vs ...string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldIn(FieldTreatmentName, vs...))
}

// TreatmentNameNotIn applies the NotIn predicate on the "treatment_name" field.
func TreatmentNameNotIn(vs ...string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldNotIn(FieldTreatmentName, vs...))
}

// TreatmentNameGT applies the GT predicate on the "treatment_name" field.
func TreatmentNameGT(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldGT(FieldTreatmentName, v))
}

// TreatmentNameGTE applies the GTE predicate on the "treatment_name" field.
func TreatmentNameGTE(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldGTE(FieldTreatmentName, v))
}

// TreatmentNameLT applies the LT predicate on the "treatment_name" field.
func TreatmentNameLT(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldLT(FieldTreatmentName, v))
}

// TreatmentNameLTE applies the LTE predicate on the "treatment_name" field.
func TreatmentNameLTE(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldLTE(FieldTreatmentName, v))
}

// TreatmentNameContains applies the Contains predicate on the "treatment_name" field.
func TreatmentNameContains(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldContains(FieldTreatmentName, v))
}

// TreatmentNameHasPrefix applies the HasPrefix predicate on the "treatment_name" field.
func TreatmentNameHasPrefix(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldHasPrefix(FieldTreatmentName, v))
}

// TreatmentNameHasSuffix applies the HasSuffix predicate on the "treatment_name" field.
func TreatmentNameHasSuffix(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldHasSuffix(FieldTreatmentName, v))
}

// TreatmentNameEqualFold applies the EqualFold predicate on the "treatment_name" field.
func TreatmentNameEqualFold(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldEqualFold(FieldTreatmentName, v))
}

// TreatmentNameContainsFold applies the ContainsFold predicate on the "treatment_name" field.
func TreatmentNameContainsFold(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldContainsFold(FieldTreatmentName, v))
}

// EmailEQ applies the EQ predicate on the "email" field.
func EmailEQ(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldEQ(FieldEmail, v))
}

// EmailNEQ applies the NEQ predicate on the "email" field.
func EmailNEQ(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldNEQ(FieldEmail, v))
}

// EmailIn applies the In predicate on the "email" field.
func EmailIn(vs ...string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldIn(FieldEmail, vs...))
}

// EmailNotIn applies the NotIn predicate on the "email" field.
func EmailNotIn(vs ...string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldNotIn(FieldEmail, vs...))
}

// EmailGT applies the GT predicate on the "email" field.
func EmailGT(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldGT(FieldEmail, v))
}

// EmailGTE applies the GTE predicate on the "email" field.
func EmailGTE(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldGTE(FieldEmail, v))
}

// EmailLT applies the LT predicate on the "email" field.
func EmailLT(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldLT(FieldEmail, v))
}

// EmailLTE applies the LTE predicate on the "email" field.
func EmailLTE(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldLTE(FieldEmail, v))
}

// EmailContains applies the Contains predicate on the "email" field.
func EmailContains(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldContains(FieldEmail, v))
}

// EmailHasPrefix applies the HasPrefix predicate on the "email" field.
func EmailHasPrefix(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldHasPrefix(FieldEmail, v))
}

// EmailHasSuffix applies the HasSuffix predicate on the "email" field.
func EmailHasSuffix(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldHasSuffix(FieldEmail, v))
}

// EmailIsNil applies the IsNil predicate on the "email" field.
func EmailIsNil() predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldIsNull(FieldEmail))
}

// EmailNotNil applies the NotNil predicate on the "email" field.
func EmailNotNil() predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldNotNull(FieldEmail))
}

// EmailEqualFold applies the EqualFold predicate on the "email" field.
func EmailEqualFold(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldEqualFold(FieldEmail, v))
}

// EmailContainsFold applies the ContainsFold predicate on the "email" field.
func EmailContainsFold(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldContainsFold(FieldEmail, v))
}

// JobIDEQ applies the EQ predicate on the "job_id" field.
func JobIDEQ(v int) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldEQ(FieldJobID, v))
}

// JobIDNEQ applies the NEQ predicate on the "job_id" field.
func JobIDNEQ(v int) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldNEQ(FieldJobID, v))
}

// JobIDIn applies the In predicate on the "job_id" field.
func JobIDIn(vs ...int) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldIn(FieldJobID, vs...))
}

// JobIDNotIn applies the NotIn predicate on the "job_id" field.
func JobIDNotIn(vs ...int) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldNotIn(FieldJobID, vs...))
}

// JobIDGT applies the GT predicate on the "job_id" field.
func JobIDGT(v int) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldGT(FieldJobID, v))
}

// JobIDGTE applies the GTE predicate on the "job_id" field.
func JobIDGTE(v int) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldGTE(FieldJobID, v))
}

// JobIDLT applies the LT predicate on the "job_id" field.
func JobIDLT(v int) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldLT(FieldJobID, v))
}

// JobIDLTE applies the LTE predicate on the "job_id" field.
func JobIDLTE(v int) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldLTE(FieldJobID, v))
}

// JobIDIsNil applies the IsNil predicate on the "job_id" field.
func JobIDIsNil() predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldIsNull(FieldJobID))
}

// JobIDNotNil applies the NotNil predicate on the "job_id" field.
func JobIDNotNil() predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldNotNull(FieldJobID))
}

// UserIDEQ applies the EQ predicate on the "user_id" field.
func UserIDEQ(v int) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldEQ(FieldUserID, v))
}

// UserIDNEQ applies the NEQ predicate on the "user_id" field.
func UserIDNEQ(v int) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldNEQ(FieldUserID, v))
}

// UserIDIn applies the In predicate on the "user_id" field.
func UserIDIn(vs ...int) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldIn(FieldUserID, vs...))
}

// UserIDNotIn applies the NotIn predicate on the "user_id" field.
func UserIDNotIn(vs ...int) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldNotIn(FieldUserID, vs...))
}

// UserIDGT applies the GT predicate on the "user_id" field.
func UserIDGT(v int) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldGT(FieldUserID, v))
}

// UserIDGTE applies the GTE predicate on the "user_id" field.
func UserIDGTE(v int) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldGTE(FieldUserID, v))
}

// UserIDLT applies the LT predicate on the "user_id" field.
func UserIDLT(v int) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldLT(FieldUserID, v))
}

// UserIDLTE applies the LTE predicate on the "user_id" field.
func UserIDLTE(v int) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldLTE(FieldUserID, v))
}

// UserIDIsNil applies the IsNil predicate on the "user_id" field.
func UserIDIsNil() predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldIsNull(FieldUserID))
}

// UserIDNotNil applies the NotNil predicate on the "user_id" field.
func UserIDNotNil() predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldNotNull(FieldUserID))
}

// SourceIDEQ applies the EQ predicate on the "source_id" field.
func SourceIDEQ(v int) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldEQ(FieldSourceID, v))
}

// SourceIDNEQ applies the NEQ predicate on the "source_id" field.
func SourceIDNEQ(v int) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldNEQ(FieldSourceID, v))
}

// SourceIDIn applies the In predicate on the "source_id" field.
func SourceIDIn(vs ...int) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldIn(FieldSourceID, vs...))
}

// SourceIDNotIn applies the NotIn predicate on the "source_id" field.
func SourceIDNotIn(vs ...int) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldNotIn(FieldSourceID, vs...))
}

// SourceIDGT applies the GT predicate on the "source_id" field.
func SourceIDGT(v int) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldGT(FieldSourceID, v))
}

// SourceIDGTE applies the GTE predicate on the "source_id" field.
func SourceIDGTE(v int) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldGTE(FieldSourceID, v))
}

// SourceIDLT applies the LT predicate on the "source_id" field.
func SourceIDLT(v int) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldLT(FieldSourceID, v))
}

// SourceIDLTE applies the LTE predicate on the "source_id" field.
func SourceIDLTE(v int) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldLTE(FieldSourceID, v))
}

// SourceIDIsNil applies the IsNil predicate on the "source_id" field.
func SourceIDIsNil() predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldIsNull(FieldSourceID))
}

// SourceIDNotNil applies the NotNil predicate on the "source_id" field.
func SourceIDNotNil() predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldNotNull(FieldSourceID))
}

// PhoneConfirmEQ applies the EQ predicate on the "phone_confirm" field.
func PhoneConfirmEQ(v bool) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldEQ(FieldPhoneConfirm, v))
}

// PhoneConfirmNEQ applies the NEQ predicate on the "phone_confirm" field.
func PhoneConfirmNEQ(v bool) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldNEQ(FieldPhoneConfirm, v))
}

// PhoneConfirmIsNil applies the IsNil predicate on the "phone_confirm" field.
func PhoneConfirmIsNil() predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldIsNull(FieldPhoneConfirm))
}

// PhoneConfirmNotNil applies the NotNil predicate on the "phone_confirm" field.
func PhoneConfirmNotNil() predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldNotNull(FieldPhoneConfirm))
}

// MailConfirmEQ applies the EQ predicate on the "mail_confirm" field.
func MailConfirmEQ(v bool) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldEQ(FieldMailConfirm, v))
}

// MailConfirmNEQ applies the NEQ predicate on the "mail_confirm" field.
func MailConfirmNEQ(v bool) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldNEQ(FieldMailConfirm, v))
}

// MailConfirmIsNil applies the IsNil predicate on the "mail_confirm" field.
func MailConfirmIsNil() predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldIsNull(FieldMailConfirm))
}

// MailConfirmNotNil applies the NotNil predicate on the "mail_confirm" field.
func MailConfirmNotNil() predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldNotNull(FieldMailConfirm))
}

// PersonFieldIsNil applies the IsNil predicate on the "person_field" field.
func PersonFieldIsNil() predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldIsNull(FieldPersonField))
}

// PersonFieldNotNil applies the NotNil predicate on the "person_field" field.
func PersonFieldNotNil() predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldNotNull(FieldPersonField))
}

// StageNameEQ applies the EQ predicate on the "stage_name" field.
func StageNameEQ(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldEQ(FieldStageName, v))
}

// StageNameNEQ applies the NEQ predicate on the "stage_name" field.
func StageNameNEQ(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldNEQ(FieldStageName, v))
}

// StageNameIn applies the In predicate on the "stage_name" field.
func StageNameIn(vs ...string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldIn(FieldStageName, vs...))
}

// StageNameNotIn applies the NotIn predicate on the "stage_name" field.
func StageNameNotIn(vs ...string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldNotIn(FieldStageName, vs...))
}

// StageNameGT applies the GT predicate on the "stage_name" field.
func StageNameGT(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldGT(FieldStageName, v))
}

// StageNameGTE applies the GTE predicate on the "stage_name" field.
func StageNameGTE(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldGTE(FieldStageName, v))
}

// StageNameLT applies the LT predicate on the "stage_name" field.
func StageNameLT(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldLT(FieldStageName, v))
}

// StageNameLTE applies the LTE predicate on the "stage_name" field.
func StageNameLTE(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldLTE(FieldStageName, v))
}

// StageNameContains applies the Contains predicate on the "stage_name" field.
func StageNameContains(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldContains(FieldStageName, v))
}

// StageNameHasPrefix applies the HasPrefix predicate on the "stage_name" field.
func StageNameHasPrefix(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldHasPrefix(FieldStageName, v))
}

// StageNameHasSuffix applies the HasSuffix predicate on the "stage_name" field.
func StageNameHasSuffix(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldHasSuffix(FieldStageName, v))
}

// StageNameIsNil applies the IsNil predicate on the "stage_name" field.
func StageNameIsNil() predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldIsNull(FieldStageName))
}

// StageNameNotNil applies the NotNil predicate on the "stage_name" field.
func StageNameNotNil() predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldNotNull(FieldStageName))
}

// StageNameEqualFold applies the EqualFold predicate on the "stage_name" field.
func StageNameEqualFold(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldEqualFold(FieldStageName, v))
}

// StageNameContainsFold applies the ContainsFold predicate on the "stage_name" field.
func StageNameContainsFold(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldContainsFold(FieldStageName, v))
}

// BankAccountNameEQ applies the EQ predicate on the "bank_account_name" field.
func BankAccountNameEQ(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldEQ(FieldBankAccountName, v))
}

// BankAccountNameNEQ applies the NEQ predicate on the "bank_account_name" field.
func BankAccountNameNEQ(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldNEQ(FieldBankAccountName, v))
}

// BankAccountNameIn applies the In predicate on the "bank_account_name" field.
func BankAccountNameIn(vs ...string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldIn(FieldBankAccountName, vs...))
}

// BankAccountNameNotIn applies the NotIn predicate on the "bank_account_name" field.
func BankAccountNameNotIn(vs ...string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldNotIn(FieldBankAccountName, vs...))
}

// BankAccountNameGT applies the GT predicate on the "bank_account_name" field.
func BankAccountNameGT(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldGT(FieldBankAccountName, v))
}

// BankAccountNameGTE applies the GTE predicate on the "bank_account_name" field.
func BankAccountNameGTE(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldGTE(FieldBankAccountName, v))
}

// BankAccountNameLT applies the LT predicate on the "bank_account_name" field.
func BankAccountNameLT(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldLT(FieldBankAccountName, v))
}

// BankAccountNameLTE applies the LTE predicate on the "bank_account_name" field.
func BankAccountNameLTE(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldLTE(FieldBankAccountName, v))
}

// BankAccountNameContains applies the Contains predicate on the "bank_account_name" field.
func BankAccountNameContains(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldContains(FieldBankAccountName, v))
}

// BankAccountNameHasPrefix applies the HasPrefix predicate on the "bank_account_name" field.
func BankAccountNameHasPrefix(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldHasPrefix(FieldBankAccountName, v))
}

// BankAccountNameHasSuffix applies the HasSuffix predicate on the "bank_account_name" field.
func BankAccountNameHasSuffix(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldHasSuffix(FieldBankAccountName, v))
}

// BankAccountNameIsNil applies the IsNil predicate on the "bank_account_name" field.
func BankAccountNameIsNil() predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldIsNull(FieldBankAccountName))
}

// BankAccountNameNotNil applies the NotNil predicate on the "bank_account_name" field.
func BankAccountNameNotNil() predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldNotNull(FieldBankAccountName))
}

// BankAccountNameEqualFold applies the EqualFold predicate on the "bank_account_name" field.
func BankAccountNameEqualFold(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldEqualFold(FieldBankAccountName, v))
}

// BankAccountNameContainsFold applies the ContainsFold predicate on the "bank_account_name" field.
func BankAccountNameContainsFold(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldContainsFold(FieldBankAccountName, v))
}

// BankAccountNumberEQ applies the EQ predicate on the "bank_account_number" field.
func BankAccountNumberEQ(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldEQ(FieldBankAccountNumber, v))
}

// BankAccountNumberNEQ applies the NEQ predicate on the "bank_account_number" field.
func BankAccountNumberNEQ(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldNEQ(FieldBankAccountNumber, v))
}

// BankAccountNumberIn applies the In predicate on the "bank_account_number" field.
func BankAccountNumberIn(vs ...string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldIn(FieldBankAccountNumber, vs...))
}

// BankAccountNumberNotIn applies the NotIn predicate on the "bank_account_number" field.
func BankAccountNumberNotIn(vs ...string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldNotIn(FieldBankAccountNumber, vs...))
}

// BankAccountNumberGT applies the GT predicate on the "bank_account_number" field.
func BankAccountNumberGT(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldGT(FieldBankAccountNumber, v))
}

// BankAccountNumberGTE applies the GTE predicate on the "bank_account_number" field.
func BankAccountNumberGTE(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldGTE(FieldBankAccountNumber, v))
}

// BankAccountNumberLT applies the LT predicate on the "bank_account_number" field.
func BankAccountNumberLT(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldLT(FieldBankAccountNumber, v))
}

// BankAccountNumberLTE applies the LTE predicate on the "bank_account_number" field.
func BankAccountNumberLTE(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldLTE(FieldBankAccountNumber, v))
}

// BankAccountNumberContains applies the Contains predicate on the "bank_account_number" field.
func BankAccountNumberContains(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldContains(FieldBankAccountNumber, v))
}

// BankAccountNumberHasPrefix applies the HasPrefix predicate on the "bank_account_number" field.
func BankAccountNumberHasPrefix(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldHasPrefix(FieldBankAccountNumber, v))
}

// BankAccountNumberHasSuffix applies the HasSuffix predicate on the "bank_account_number" field.
func BankAccountNumberHasSuffix(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldHasSuffix(FieldBankAccountNumber, v))
}

// BankAccountNumberIsNil applies the IsNil predicate on the "bank_account_number" field.
func BankAccountNumberIsNil() predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldIsNull(FieldBankAccountNumber))
}

// BankAccountNumberNotNil applies the NotNil predicate on the "bank_account_number" field.
func BankAccountNumberNotNil() predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldNotNull(FieldBankAccountNumber))
}

// BankAccountNumberEqualFold applies the EqualFold predicate on the "bank_account_number" field.
func BankAccountNumberEqualFold(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldEqualFold(FieldBankAccountNumber, v))
}

// BankAccountNumberContainsFold applies the ContainsFold predicate on the "bank_account_number" field.
func BankAccountNumberContainsFold(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldContainsFold(FieldBankAccountNumber, v))
}

// BankEQ applies the EQ predicate on the "bank" field.
func BankEQ(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldEQ(FieldBank, v))
}

// BankNEQ applies the NEQ predicate on the "bank" field.
func BankNEQ(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldNEQ(FieldBank, v))
}

// BankIn applies the In predicate on the "bank" field.
func BankIn(vs ...string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldIn(FieldBank, vs...))
}

// BankNotIn applies the NotIn predicate on the "bank" field.
func BankNotIn(vs ...string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldNotIn(FieldBank, vs...))
}

// BankGT applies the GT predicate on the "bank" field.
func BankGT(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldGT(FieldBank, v))
}

// BankGTE applies the GTE predicate on the "bank" field.
func BankGTE(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldGTE(FieldBank, v))
}

// BankLT applies the LT predicate on the "bank" field.
func BankLT(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldLT(FieldBank, v))
}

// BankLTE applies the LTE predicate on the "bank" field.
func BankLTE(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldLTE(FieldBank, v))
}

// BankContains applies the Contains predicate on the "bank" field.
func BankContains(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldContains(FieldBank, v))
}

// BankHasPrefix applies the HasPrefix predicate on the "bank" field.
func BankHasPrefix(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldHasPrefix(FieldBank, v))
}

// BankHasSuffix applies the HasSuffix predicate on the "bank" field.
func BankHasSuffix(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldHasSuffix(FieldBank, v))
}

// BankIsNil applies the IsNil predicate on the "bank" field.
func BankIsNil() predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldIsNull(FieldBank))
}

// BankNotNil applies the NotNil predicate on the "bank" field.
func BankNotNil() predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldNotNull(FieldBank))
}

// BankEqualFold applies the EqualFold predicate on the "bank" field.
func BankEqualFold(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldEqualFold(FieldBank, v))
}

// BankContainsFold applies the ContainsFold predicate on the "bank" field.
func BankContainsFold(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldContainsFold(FieldBank, v))
}

// BankBranchEQ applies the EQ predicate on the "bank_branch" field.
func BankBranchEQ(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldEQ(FieldBankBranch, v))
}

// BankBranchNEQ applies the NEQ predicate on the "bank_branch" field.
func BankBranchNEQ(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldNEQ(FieldBankBranch, v))
}

// BankBranchIn applies the In predicate on the "bank_branch" field.
func BankBranchIn(vs ...string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldIn(FieldBankBranch, vs...))
}

// BankBranchNotIn applies the NotIn predicate on the "bank_branch" field.
func BankBranchNotIn(vs ...string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldNotIn(FieldBankBranch, vs...))
}

// BankBranchGT applies the GT predicate on the "bank_branch" field.
func BankBranchGT(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldGT(FieldBankBranch, v))
}

// BankBranchGTE applies the GTE predicate on the "bank_branch" field.
func BankBranchGTE(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldGTE(FieldBankBranch, v))
}

// BankBranchLT applies the LT predicate on the "bank_branch" field.
func BankBranchLT(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldLT(FieldBankBranch, v))
}

// BankBranchLTE applies the LTE predicate on the "bank_branch" field.
func BankBranchLTE(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldLTE(FieldBankBranch, v))
}

// BankBranchContains applies the Contains predicate on the "bank_branch" field.
func BankBranchContains(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldContains(FieldBankBranch, v))
}

// BankBranchHasPrefix applies the HasPrefix predicate on the "bank_branch" field.
func BankBranchHasPrefix(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldHasPrefix(FieldBankBranch, v))
}

// BankBranchHasSuffix applies the HasSuffix predicate on the "bank_branch" field.
func BankBranchHasSuffix(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldHasSuffix(FieldBankBranch, v))
}

// BankBranchIsNil applies the IsNil predicate on the "bank_branch" field.
func BankBranchIsNil() predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldIsNull(FieldBankBranch))
}

// BankBranchNotNil applies the NotNil predicate on the "bank_branch" field.
func BankBranchNotNil() predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldNotNull(FieldBankBranch))
}

// BankBranchEqualFold applies the EqualFold predicate on the "bank_branch" field.
func BankBranchEqualFold(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldEqualFold(FieldBankBranch, v))
}

// BankBranchContainsFold applies the ContainsFold predicate on the "bank_branch" field.
func BankBranchContainsFold(v string) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldContainsFold(FieldBankBranch, v))
}

// StageIDEQ applies the EQ predicate on the "stage_id" field.
func StageIDEQ(v int) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldEQ(FieldStageID, v))
}

// StageIDNEQ applies the NEQ predicate on the "stage_id" field.
func StageIDNEQ(v int) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldNEQ(FieldStageID, v))
}

// StageIDIn applies the In predicate on the "stage_id" field.
func StageIDIn(vs ...int) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldIn(FieldStageID, vs...))
}

// StageIDNotIn applies the NotIn predicate on the "stage_id" field.
func StageIDNotIn(vs ...int) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldNotIn(FieldStageID, vs...))
}

// StageIDGT applies the GT predicate on the "stage_id" field.
func StageIDGT(v int) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldGT(FieldStageID, v))
}

// StageIDGTE applies the GTE predicate on the "stage_id" field.
func StageIDGTE(v int) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldGTE(FieldStageID, v))
}

// StageIDLT applies the LT predicate on the "stage_id" field.
func StageIDLT(v int) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldLT(FieldStageID, v))
}

// StageIDLTE applies the LTE predicate on the "stage_id" field.
func StageIDLTE(v int) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldLTE(FieldStageID, v))
}

// StageIDIsNil applies the IsNil predicate on the "stage_id" field.
func StageIDIsNil() predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldIsNull(FieldStageID))
}

// StageIDNotNil applies the NotNil predicate on the "stage_id" field.
func StageIDNotNil() predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldNotNull(FieldStageID))
}

// StageParentIDEQ applies the EQ predicate on the "stage_parent_id" field.
func StageParentIDEQ(v int) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldEQ(FieldStageParentID, v))
}

// StageParentIDNEQ applies the NEQ predicate on the "stage_parent_id" field.
func StageParentIDNEQ(v int) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldNEQ(FieldStageParentID, v))
}

// StageParentIDIn applies the In predicate on the "stage_parent_id" field.
func StageParentIDIn(vs ...int) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldIn(FieldStageParentID, vs...))
}

// StageParentIDNotIn applies the NotIn predicate on the "stage_parent_id" field.
func StageParentIDNotIn(vs ...int) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldNotIn(FieldStageParentID, vs...))
}

// StageParentIDGT applies the GT predicate on the "stage_parent_id" field.
func StageParentIDGT(v int) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldGT(FieldStageParentID, v))
}

// StageParentIDGTE applies the GTE predicate on the "stage_parent_id" field.
func StageParentIDGTE(v int) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldGTE(FieldStageParentID, v))
}

// StageParentIDLT applies the LT predicate on the "stage_parent_id" field.
func StageParentIDLT(v int) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldLT(FieldStageParentID, v))
}

// StageParentIDLTE applies the LTE predicate on the "stage_parent_id" field.
func StageParentIDLTE(v int) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldLTE(FieldStageParentID, v))
}

// StageParentIDIsNil applies the IsNil predicate on the "stage_parent_id" field.
func StageParentIDIsNil() predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldIsNull(FieldStageParentID))
}

// StageParentIDNotNil applies the NotNil predicate on the "stage_parent_id" field.
func StageParentIDNotNil() predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldNotNull(FieldStageParentID))
}

// SaleIDEQ applies the EQ predicate on the "sale_id" field.
func SaleIDEQ(v int) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldEQ(FieldSaleID, v))
}

// SaleIDNEQ applies the NEQ predicate on the "sale_id" field.
func SaleIDNEQ(v int) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldNEQ(FieldSaleID, v))
}

// SaleIDIn applies the In predicate on the "sale_id" field.
func SaleIDIn(vs ...int) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldIn(FieldSaleID, vs...))
}

// SaleIDNotIn applies the NotIn predicate on the "sale_id" field.
func SaleIDNotIn(vs ...int) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldNotIn(FieldSaleID, vs...))
}

// SaleIDGT applies the GT predicate on the "sale_id" field.
func SaleIDGT(v int) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldGT(FieldSaleID, v))
}

// SaleIDGTE applies the GTE predicate on the "sale_id" field.
func SaleIDGTE(v int) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldGTE(FieldSaleID, v))
}

// SaleIDLT applies the LT predicate on the "sale_id" field.
func SaleIDLT(v int) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldLT(FieldSaleID, v))
}

// SaleIDLTE applies the LTE predicate on the "sale_id" field.
func SaleIDLTE(v int) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldLTE(FieldSaleID, v))
}

// SaleIDIsNil applies the IsNil predicate on the "sale_id" field.
func SaleIDIsNil() predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldIsNull(FieldSaleID))
}

// SaleIDNotNil applies the NotNil predicate on the "sale_id" field.
func SaleIDNotNil() predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldNotNull(FieldSaleID))
}

// SaleIsNil applies the IsNil predicate on the "sale" field.
func SaleIsNil() predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldIsNull(FieldSale))
}

// SaleNotNil applies the NotNil predicate on the "sale" field.
func SaleNotNil() predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.FieldNotNull(FieldSale))
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.PersonQueryView) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.PersonQueryView) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.PersonQueryView) predicate.PersonQueryView {
	return predicate.PersonQueryView(sql.NotPredicates(p))
}
