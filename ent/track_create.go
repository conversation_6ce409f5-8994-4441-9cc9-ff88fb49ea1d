// Code generated by ent, DO NOT EDIT.

package ent

import (
	"bcare/ent/appointment"
	"bcare/ent/attachment"
	"bcare/ent/billitem"
	"bcare/ent/deal"
	"bcare/ent/fileusage"
	"bcare/ent/person"
	"bcare/ent/pipeline"
	"bcare/ent/stage"
	"bcare/ent/track"
	"bcare/ent/types"
	"bcare/ent/user"
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// TrackCreate is the builder for creating a Track entity.
type TrackCreate struct {
	config
	mutation *TrackMutation
	hooks    []Hook
}

// SetDeletedAt sets the "deleted_at" field.
func (tc *TrackCreate) SetDeletedAt(t time.Time) *TrackCreate {
	tc.mutation.SetDeletedAt(t)
	return tc
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (tc *TrackCreate) SetNillableDeletedAt(t *time.Time) *TrackCreate {
	if t != nil {
		tc.SetDeletedAt(*t)
	}
	return tc
}

// SetStatus sets the "status" field.
func (tc *TrackCreate) SetStatus(i int8) *TrackCreate {
	tc.mutation.SetStatus(i)
	return tc
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (tc *TrackCreate) SetNillableStatus(i *int8) *TrackCreate {
	if i != nil {
		tc.SetStatus(*i)
	}
	return tc
}

// SetVersion sets the "version" field.
func (tc *TrackCreate) SetVersion(i int) *TrackCreate {
	tc.mutation.SetVersion(i)
	return tc
}

// SetNillableVersion sets the "version" field if the given value is not nil.
func (tc *TrackCreate) SetNillableVersion(i *int) *TrackCreate {
	if i != nil {
		tc.SetVersion(*i)
	}
	return tc
}

// SetCreatedAt sets the "created_at" field.
func (tc *TrackCreate) SetCreatedAt(t time.Time) *TrackCreate {
	tc.mutation.SetCreatedAt(t)
	return tc
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (tc *TrackCreate) SetNillableCreatedAt(t *time.Time) *TrackCreate {
	if t != nil {
		tc.SetCreatedAt(*t)
	}
	return tc
}

// SetUpdatedAt sets the "updated_at" field.
func (tc *TrackCreate) SetUpdatedAt(t time.Time) *TrackCreate {
	tc.mutation.SetUpdatedAt(t)
	return tc
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (tc *TrackCreate) SetNillableUpdatedAt(t *time.Time) *TrackCreate {
	if t != nil {
		tc.SetUpdatedAt(*t)
	}
	return tc
}

// SetBegin sets the "begin" field.
func (tc *TrackCreate) SetBegin(t time.Time) *TrackCreate {
	tc.mutation.SetBegin(t)
	return tc
}

// SetNillableBegin sets the "begin" field if the given value is not nil.
func (tc *TrackCreate) SetNillableBegin(t *time.Time) *TrackCreate {
	if t != nil {
		tc.SetBegin(*t)
	}
	return tc
}

// SetEnd sets the "end" field.
func (tc *TrackCreate) SetEnd(t time.Time) *TrackCreate {
	tc.mutation.SetEnd(t)
	return tc
}

// SetNillableEnd sets the "end" field if the given value is not nil.
func (tc *TrackCreate) SetNillableEnd(t *time.Time) *TrackCreate {
	if t != nil {
		tc.SetEnd(*t)
	}
	return tc
}

// SetDealID sets the "deal_id" field.
func (tc *TrackCreate) SetDealID(i int) *TrackCreate {
	tc.mutation.SetDealID(i)
	return tc
}

// SetNillableDealID sets the "deal_id" field if the given value is not nil.
func (tc *TrackCreate) SetNillableDealID(i *int) *TrackCreate {
	if i != nil {
		tc.SetDealID(*i)
	}
	return tc
}

// SetPipelineID sets the "pipeline_id" field.
func (tc *TrackCreate) SetPipelineID(i int) *TrackCreate {
	tc.mutation.SetPipelineID(i)
	return tc
}

// SetNillablePipelineID sets the "pipeline_id" field if the given value is not nil.
func (tc *TrackCreate) SetNillablePipelineID(i *int) *TrackCreate {
	if i != nil {
		tc.SetPipelineID(*i)
	}
	return tc
}

// SetStageID sets the "stage_id" field.
func (tc *TrackCreate) SetStageID(i int) *TrackCreate {
	tc.mutation.SetStageID(i)
	return tc
}

// SetNillableStageID sets the "stage_id" field if the given value is not nil.
func (tc *TrackCreate) SetNillableStageID(i *int) *TrackCreate {
	if i != nil {
		tc.SetStageID(*i)
	}
	return tc
}

// SetUserID sets the "user_id" field.
func (tc *TrackCreate) SetUserID(i int) *TrackCreate {
	tc.mutation.SetUserID(i)
	return tc
}

// SetNillableUserID sets the "user_id" field if the given value is not nil.
func (tc *TrackCreate) SetNillableUserID(i *int) *TrackCreate {
	if i != nil {
		tc.SetUserID(*i)
	}
	return tc
}

// SetPersonID sets the "person_id" field.
func (tc *TrackCreate) SetPersonID(i int) *TrackCreate {
	tc.mutation.SetPersonID(i)
	return tc
}

// SetNillablePersonID sets the "person_id" field if the given value is not nil.
func (tc *TrackCreate) SetNillablePersonID(i *int) *TrackCreate {
	if i != nil {
		tc.SetPersonID(*i)
	}
	return tc
}

// SetWeight sets the "weight" field.
func (tc *TrackCreate) SetWeight(f float64) *TrackCreate {
	tc.mutation.SetWeight(f)
	return tc
}

// SetNillableWeight sets the "weight" field if the given value is not nil.
func (tc *TrackCreate) SetNillableWeight(f *float64) *TrackCreate {
	if f != nil {
		tc.SetWeight(*f)
	}
	return tc
}

// SetMeta sets the "meta" field.
func (tc *TrackCreate) SetMeta(s struct{}) *TrackCreate {
	tc.mutation.SetMeta(s)
	return tc
}

// SetNillableMeta sets the "meta" field if the given value is not nil.
func (tc *TrackCreate) SetNillableMeta(s *struct{}) *TrackCreate {
	if s != nil {
		tc.SetMeta(*s)
	}
	return tc
}

// SetState sets the "state" field.
func (tc *TrackCreate) SetState(t track.State) *TrackCreate {
	tc.mutation.SetState(t)
	return tc
}

// SetNillableState sets the "state" field if the given value is not nil.
func (tc *TrackCreate) SetNillableState(t *track.State) *TrackCreate {
	if t != nil {
		tc.SetState(*t)
	}
	return tc
}

// SetDealStageID sets the "deal_stage_id" field.
func (tc *TrackCreate) SetDealStageID(i int) *TrackCreate {
	tc.mutation.SetDealStageID(i)
	return tc
}

// SetNillableDealStageID sets the "deal_stage_id" field if the given value is not nil.
func (tc *TrackCreate) SetNillableDealStageID(i *int) *TrackCreate {
	if i != nil {
		tc.SetDealStageID(*i)
	}
	return tc
}

// SetStageHistory sets the "stage_history" field.
func (tc *TrackCreate) SetStageHistory(the []types.StageHistoryEntry) *TrackCreate {
	tc.mutation.SetStageHistory(the)
	return tc
}

// SetDeal sets the "deal" edge to the Deal entity.
func (tc *TrackCreate) SetDeal(d *Deal) *TrackCreate {
	return tc.SetDealID(d.ID)
}

// SetUser sets the "user" edge to the User entity.
func (tc *TrackCreate) SetUser(u *User) *TrackCreate {
	return tc.SetUserID(u.ID)
}

// SetPerson sets the "person" edge to the Person entity.
func (tc *TrackCreate) SetPerson(p *Person) *TrackCreate {
	return tc.SetPersonID(p.ID)
}

// SetPipeline sets the "pipeline" edge to the Pipeline entity.
func (tc *TrackCreate) SetPipeline(p *Pipeline) *TrackCreate {
	return tc.SetPipelineID(p.ID)
}

// SetStage sets the "stage" edge to the Stage entity.
func (tc *TrackCreate) SetStage(s *Stage) *TrackCreate {
	return tc.SetStageID(s.ID)
}

// AddAppointmentIDs adds the "appointments" edge to the Appointment entity by IDs.
func (tc *TrackCreate) AddAppointmentIDs(ids ...int) *TrackCreate {
	tc.mutation.AddAppointmentIDs(ids...)
	return tc
}

// AddAppointments adds the "appointments" edges to the Appointment entity.
func (tc *TrackCreate) AddAppointments(a ...*Appointment) *TrackCreate {
	ids := make([]int, len(a))
	for i := range a {
		ids[i] = a[i].ID
	}
	return tc.AddAppointmentIDs(ids...)
}

// AddFileUsageIDs adds the "file_usages" edge to the FileUsage entity by IDs.
func (tc *TrackCreate) AddFileUsageIDs(ids ...int) *TrackCreate {
	tc.mutation.AddFileUsageIDs(ids...)
	return tc
}

// AddFileUsages adds the "file_usages" edges to the FileUsage entity.
func (tc *TrackCreate) AddFileUsages(f ...*FileUsage) *TrackCreate {
	ids := make([]int, len(f))
	for i := range f {
		ids[i] = f[i].ID
	}
	return tc.AddFileUsageIDs(ids...)
}

// AddAttachmentIDs adds the "attachments" edge to the Attachment entity by IDs.
func (tc *TrackCreate) AddAttachmentIDs(ids ...int) *TrackCreate {
	tc.mutation.AddAttachmentIDs(ids...)
	return tc
}

// AddAttachments adds the "attachments" edges to the Attachment entity.
func (tc *TrackCreate) AddAttachments(a ...*Attachment) *TrackCreate {
	ids := make([]int, len(a))
	for i := range a {
		ids[i] = a[i].ID
	}
	return tc.AddAttachmentIDs(ids...)
}

// AddBillItemIDs adds the "bill_items" edge to the BillItem entity by IDs.
func (tc *TrackCreate) AddBillItemIDs(ids ...int) *TrackCreate {
	tc.mutation.AddBillItemIDs(ids...)
	return tc
}

// AddBillItems adds the "bill_items" edges to the BillItem entity.
func (tc *TrackCreate) AddBillItems(b ...*BillItem) *TrackCreate {
	ids := make([]int, len(b))
	for i := range b {
		ids[i] = b[i].ID
	}
	return tc.AddBillItemIDs(ids...)
}

// Mutation returns the TrackMutation object of the builder.
func (tc *TrackCreate) Mutation() *TrackMutation {
	return tc.mutation
}

// Save creates the Track in the database.
func (tc *TrackCreate) Save(ctx context.Context) (*Track, error) {
	if err := tc.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, tc.sqlSave, tc.mutation, tc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (tc *TrackCreate) SaveX(ctx context.Context) *Track {
	v, err := tc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (tc *TrackCreate) Exec(ctx context.Context) error {
	_, err := tc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (tc *TrackCreate) ExecX(ctx context.Context) {
	if err := tc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (tc *TrackCreate) defaults() error {
	if _, ok := tc.mutation.Status(); !ok {
		v := track.DefaultStatus
		tc.mutation.SetStatus(v)
	}
	if _, ok := tc.mutation.Version(); !ok {
		v := track.DefaultVersion
		tc.mutation.SetVersion(v)
	}
	if _, ok := tc.mutation.CreatedAt(); !ok {
		if track.DefaultCreatedAt == nil {
			return fmt.Errorf("ent: uninitialized track.DefaultCreatedAt (forgotten import ent/runtime?)")
		}
		v := track.DefaultCreatedAt()
		tc.mutation.SetCreatedAt(v)
	}
	if _, ok := tc.mutation.UpdatedAt(); !ok {
		if track.DefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized track.DefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := track.DefaultUpdatedAt()
		tc.mutation.SetUpdatedAt(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (tc *TrackCreate) check() error {
	if _, ok := tc.mutation.Status(); !ok {
		return &ValidationError{Name: "status", err: errors.New(`ent: missing required field "Track.status"`)}
	}
	if _, ok := tc.mutation.Version(); !ok {
		return &ValidationError{Name: "version", err: errors.New(`ent: missing required field "Track.version"`)}
	}
	if _, ok := tc.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "Track.created_at"`)}
	}
	if _, ok := tc.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "Track.updated_at"`)}
	}
	if v, ok := tc.mutation.State(); ok {
		if err := track.StateValidator(v); err != nil {
			return &ValidationError{Name: "state", err: fmt.Errorf(`ent: validator failed for field "Track.state": %w`, err)}
		}
	}
	return nil
}

func (tc *TrackCreate) sqlSave(ctx context.Context) (*Track, error) {
	if err := tc.check(); err != nil {
		return nil, err
	}
	_node, _spec := tc.createSpec()
	if err := sqlgraph.CreateNode(ctx, tc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	id := _spec.ID.Value.(int64)
	_node.ID = int(id)
	tc.mutation.id = &_node.ID
	tc.mutation.done = true
	return _node, nil
}

func (tc *TrackCreate) createSpec() (*Track, *sqlgraph.CreateSpec) {
	var (
		_node = &Track{config: tc.config}
		_spec = sqlgraph.NewCreateSpec(track.Table, sqlgraph.NewFieldSpec(track.FieldID, field.TypeInt))
	)
	if value, ok := tc.mutation.DeletedAt(); ok {
		_spec.SetField(track.FieldDeletedAt, field.TypeTime, value)
		_node.DeletedAt = value
	}
	if value, ok := tc.mutation.Status(); ok {
		_spec.SetField(track.FieldStatus, field.TypeInt8, value)
		_node.Status = value
	}
	if value, ok := tc.mutation.Version(); ok {
		_spec.SetField(track.FieldVersion, field.TypeInt, value)
		_node.Version = value
	}
	if value, ok := tc.mutation.CreatedAt(); ok {
		_spec.SetField(track.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := tc.mutation.UpdatedAt(); ok {
		_spec.SetField(track.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	if value, ok := tc.mutation.Begin(); ok {
		_spec.SetField(track.FieldBegin, field.TypeTime, value)
		_node.Begin = value
	}
	if value, ok := tc.mutation.End(); ok {
		_spec.SetField(track.FieldEnd, field.TypeTime, value)
		_node.End = value
	}
	if value, ok := tc.mutation.Weight(); ok {
		_spec.SetField(track.FieldWeight, field.TypeFloat64, value)
		_node.Weight = value
	}
	if value, ok := tc.mutation.Meta(); ok {
		_spec.SetField(track.FieldMeta, field.TypeJSON, value)
		_node.Meta = value
	}
	if value, ok := tc.mutation.State(); ok {
		_spec.SetField(track.FieldState, field.TypeEnum, value)
		_node.State = value
	}
	if value, ok := tc.mutation.DealStageID(); ok {
		_spec.SetField(track.FieldDealStageID, field.TypeInt, value)
		_node.DealStageID = value
	}
	if value, ok := tc.mutation.StageHistory(); ok {
		_spec.SetField(track.FieldStageHistory, field.TypeJSON, value)
		_node.StageHistory = value
	}
	if nodes := tc.mutation.DealIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   track.DealTable,
			Columns: []string{track.DealColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(deal.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.DealID = &nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := tc.mutation.UserIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   track.UserTable,
			Columns: []string{track.UserColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.UserID = nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := tc.mutation.PersonIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   track.PersonTable,
			Columns: []string{track.PersonColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(person.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.PersonID = nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := tc.mutation.PipelineIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   track.PipelineTable,
			Columns: []string{track.PipelineColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(pipeline.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.PipelineID = &nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := tc.mutation.StageIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   track.StageTable,
			Columns: []string{track.StageColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(stage.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.StageID = &nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := tc.mutation.AppointmentsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   track.AppointmentsTable,
			Columns: []string{track.AppointmentsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(appointment.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := tc.mutation.FileUsagesIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   track.FileUsagesTable,
			Columns: []string{track.FileUsagesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(fileusage.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := tc.mutation.AttachmentsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   track.AttachmentsTable,
			Columns: []string{track.AttachmentsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(attachment.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := tc.mutation.BillItemsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   track.BillItemsTable,
			Columns: []string{track.BillItemsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(billitem.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	return _node, _spec
}

// TrackCreateBulk is the builder for creating many Track entities in bulk.
type TrackCreateBulk struct {
	config
	err      error
	builders []*TrackCreate
}

// Save creates the Track entities in the database.
func (tcb *TrackCreateBulk) Save(ctx context.Context) ([]*Track, error) {
	if tcb.err != nil {
		return nil, tcb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(tcb.builders))
	nodes := make([]*Track, len(tcb.builders))
	mutators := make([]Mutator, len(tcb.builders))
	for i := range tcb.builders {
		func(i int, root context.Context) {
			builder := tcb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*TrackMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, tcb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, tcb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = int(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, tcb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (tcb *TrackCreateBulk) SaveX(ctx context.Context) []*Track {
	v, err := tcb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (tcb *TrackCreateBulk) Exec(ctx context.Context) error {
	_, err := tcb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (tcb *TrackCreateBulk) ExecX(ctx context.Context) {
	if err := tcb.Exec(ctx); err != nil {
		panic(err)
	}
}
