// Code generated by entimport, DO NOT EDIT.

package schema

import (
	"bcare/common/ctxdata"
	bent "bcare/ent"
	"bcare/ent/appointment"
	"bcare/ent/hook"
	"bcare/ent/types"
	"context"
	"entgo.io/ent"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
	"entgo.io/ent/schema/index"
	"fmt"
	"time"
)

type Appointment struct {
	ent.Schema
}

func (Appointment) Fields() []ent.Field {
	return []ent.Field{
		field.String("title").Optional(),
		field.Time("start_time").Optional(),
		field.Time("end_time").Optional(),
		field.Time("arrived_at").Optional().Nillable(),
		field.Text("notes").Optional(),
		field.Int8("type").Optional(),
		field.Int("person_id").Optional(),
		field.Int("doctor_id").Optional(),
		field.Int("creator_id").Optional().Nillable(),
		field.Int("track_id").Optional(),
		field.Int8("reminder_status").Optional().Default(-1),
		field.String("extra_notes").Optional().Nillable(),
		field.JSON("history", []types.TaskHistoryEntry{}).
			Optional().
			Default([]types.TaskHistoryEntry{})}
}
func (Appointment) Edges() []ent.Edge {
	return []ent.Edge{
		edge.To("creator", User.Type).
			Field("creator_id").
			Unique(),
		edge.To("person", Person.Type).Field("person_id").Unique(),
		edge.To("doctor", User.Type).Field("doctor_id").Unique(),
		edge.To("track", Track.Type).Unique().
			Field("track_id"),
	}
}
func (Appointment) Annotations() []schema.Annotation {
	return []schema.Annotation{entsql.Annotation{Table: "appointment"}}
}

func (Appointment) Mixin() []ent.Mixin {
	return []ent.Mixin{
		SoftDeleteMixin{},
		VersionMixin{},
		TimeMixin{},
	}
}
func (Appointment) Indexes() []ent.Index {
	return []ent.Index{
		index.Fields("notes", "start_time"),
		index.Fields("start_time"),
		index.Fields("person_id"),
		index.Fields("track_id"),
		index.Fields("deleted_at", "status"),
	}
}

func (Appointment) Hooks() []ent.Hook {
	return []ent.Hook{
		hook.On(
			func(next ent.Mutator) ent.Mutator {
				return hook.AppointmentFunc(func(ctx context.Context, m *bent.AppointmentMutation) (ent.Value, error) {
					userID := ctxdata.GetUidFromCtx(ctx)

					var affectedIDs []int
					if m.Op().Is(ent.OpUpdateOne) {
						id, _ := m.ID()
						affectedIDs = []int{id}
					} else {
						var err error
						affectedIDs, err = m.IDs(ctx)
						if err != nil {
							return nil, fmt.Errorf("failed to get affected IDs: %w", err)
						}
					}
					skipSoftDelete := SkipSoftDelete(ctx)
					oldAppointments, err := m.Client().Appointment.Query().
						Where(appointment.IDIn(affectedIDs...)).
						All(skipSoftDelete)
					if err != nil {
						return nil, fmt.Errorf("failed to fetch old appointments: %w", err)
					}

					oldAppointmentMap := make(map[int]*bent.Appointment)
					for _, a := range oldAppointments {
						oldAppointmentMap[a.ID] = a
					}

					value, err := next.Mutate(ctx, m)
					if err != nil {
						return nil, err
					}

					for _, appointmentID := range affectedIDs {
						oldAppointment := oldAppointmentMap[appointmentID]
						changes := []types.TaskFieldChange{}

						// Track title changes
						if title, exists := m.Title(); exists {
							if oldAppointment.Title != title {
								changes = append(changes, types.TaskFieldChange{
									Field:    "title",
									OldValue: oldAppointment.Title,
									NewValue: title,
								})
							}
						}

						// Track start_time changes
						if startTime, exists := m.StartTime(); exists {
							if !oldAppointment.StartTime.Equal(startTime) {
								changes = append(changes, types.TaskFieldChange{
									Field:    "start_time",
									OldValue: oldAppointment.StartTime,
									NewValue: startTime,
								})
							}
						}

						// Track end_time changes
						if endTime, exists := m.EndTime(); exists {
							if !oldAppointment.EndTime.Equal(endTime) {
								changes = append(changes, types.TaskFieldChange{
									Field:    "end_time",
									OldValue: oldAppointment.EndTime,
									NewValue: endTime,
								})
							}
						}

						// Track arrived_at changes
						if arrivedAt, exists := m.ArrivedAt(); exists {
							if (oldAppointment.ArrivedAt == nil && !arrivedAt.IsZero()) ||
								(oldAppointment.ArrivedAt != nil && arrivedAt.IsZero()) ||
								(oldAppointment.ArrivedAt != nil && !arrivedAt.IsZero() && !oldAppointment.ArrivedAt.Equal(arrivedAt)) {
								changes = append(changes, types.TaskFieldChange{
									Field:    "arrived_at",
									OldValue: oldAppointment.ArrivedAt,
									NewValue: arrivedAt,
								})
							}
						}
						if m.ArrivedAtCleared() {
							changes = append(changes, types.TaskFieldChange{
								Field:    "arrived_at",
								OldValue: oldAppointment.ArrivedAt,
								NewValue: nil,
							})
						}

						// Track notes changes
						if notes, exists := m.Notes(); exists {
							if oldAppointment.Notes != notes {
								changes = append(changes, types.TaskFieldChange{
									Field:    "notes",
									OldValue: oldAppointment.Notes,
									NewValue: notes,
								})
							}
						}
						if m.NotesCleared() {
							changes = append(changes, types.TaskFieldChange{
								Field:    "notes",
								OldValue: oldAppointment.Notes,
								NewValue: nil,
							})
						}

						// Track type changes
						if typeVal, exists := m.GetType(); exists {
							if oldAppointment.Type != typeVal {
								changes = append(changes, types.TaskFieldChange{
									Field:    "type",
									OldValue: oldAppointment.Type,
									NewValue: typeVal,
								})
							}
						}

						// Track doctor_id changes
						if doctorID, exists := m.DoctorID(); exists {
							if oldAppointment.DoctorID != doctorID {
								changes = append(changes, types.TaskFieldChange{
									Field:    "doctor_id",
									OldValue: oldAppointment.DoctorID,
									NewValue: doctorID,
								})
							}
						}

						// Track reminder_status changes
						if reminderStatus, exists := m.ReminderStatus(); exists {
							if oldAppointment.ReminderStatus != reminderStatus {
								changes = append(changes, types.TaskFieldChange{
									Field:    "reminder_status",
									OldValue: oldAppointment.ReminderStatus,
									NewValue: reminderStatus,
								})
							}
						}

						// Track extra_notes changes
						if extraNotes, exists := m.ExtraNotes(); exists {
							if oldAppointment.ExtraNotes != nil && *oldAppointment.ExtraNotes != extraNotes {
								changes = append(changes, types.TaskFieldChange{
									Field:    "extra_notes",
									OldValue: oldAppointment.ExtraNotes,
									NewValue: extraNotes,
								})
							}
						}
						if m.ExtraNotesCleared() {
							changes = append(changes, types.TaskFieldChange{
								Field:    "extra_notes",
								OldValue: oldAppointment.ExtraNotes,
								NewValue: nil,
							})
						}
						status, exists := m.Status()
						if exists {
							if oldAppointment.Status != status {
								oldStatusStr := types.AppointmentStatus[oldAppointment.Status]
								newStatusStr := types.AppointmentStatus[status]
								changes = append(changes, types.TaskFieldChange{
									Field:    "status",
									OldValue: oldStatusStr,
									NewValue: newStatusStr,
								})
							}
						}

						if len(changes) > 0 {
							newEntry := types.TaskHistoryEntry{
								UserID:    userID,
								Changes:   changes,
								Timestamp: time.Now(),
							}

							currentHistory := oldAppointment.History
							updatedHistory := append(currentHistory, newEntry)

							err := m.Client().Appointment.UpdateOneID(oldAppointment.ID).
								SetHistory(updatedHistory).
								Exec(skipSoftDelete)
							if err != nil {
								return nil, fmt.Errorf("failed to update history for appointment %d: %w", appointmentID, err)
							}
						}
					}

					return value, nil
				})
			},
			ent.OpUpdateOne|ent.OpUpdate,
		),
	}
}
