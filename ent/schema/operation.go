package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
)

type Operation struct {
	ent.Schema
}

func (Operation) Fields() []ent.Field {
	return []ent.Field{
		field.String("name").Unique().Comment("Operation name"),
		field.Text("description").Optional().Comment("Operation description"),
		field.Int("duration").Optional().Comment("Operation duration"),
		field.Strings("group").Optional(),
	}
}
func (Operation) Edges() []ent.Edge {
	return []ent.Edge{
		edge.From("assigned_product", Product.Type).
			Ref("operations").
			Through("product_operation", ProductOperation.Type),
	}
}
func (Operation) Annotations() []schema.Annotation {
	return []schema.Annotation{entsql.Annotation{Table: "operation"}}
}
func (Operation) Mixin() []ent.Mixin {
	return []ent.Mixin{
		SoftDeleteMixin{},
		VersionMixin{},
		TimeMixin{},
	}
}
