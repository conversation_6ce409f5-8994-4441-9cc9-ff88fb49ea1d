// Code generated by entimport, DO NOT EDIT.

package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
	"entgo.io/ent/schema/index"
)

type FileUsage struct {
	ent.Schema
}

func (FileUsage) Fields() []ent.Field {
	return []ent.Field{field.Int("file_id"),
		field.String("entity_type"),
		field.Int("entity_id"),
		field.String("usage_type").Optional(),
		field.JSON("usage_meta", struct{}{}).Optional(),
		field.Int("track_id").Optional(),
	}
}
func (FileUsage) Edges() []ent.Edge {
	return []ent.Edge{
		edge.To("file", File.Type).
			Required().
			Unique().
			Field("file_id"),
		edge.To("track", Track.Type).
			Unique().
			Field("track_id"),
	}
	return nil
}
func (FileUsage) Annotations() []schema.Annotation {
	return []schema.Annotation{entsql.Annotation{Table: "file_usage"}}
}

func (FileUsage) Mixin() []ent.Mixin {
	return []ent.Mixin{
		SoftDeleteMixin{},
		VersionMixin{},
		TimeMixin{},
	}
}

func (FileUsage) Indexes() []ent.Index {
	return []ent.Index{
		index.Fields("entity_type", "entity_id"),
	}
}