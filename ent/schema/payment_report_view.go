package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/schema/field"
)

// PaymentReportView represents the payment_report_view
type PaymentReportView struct {
	ent.View
}

// Fields of the PaymentReportView
func (PaymentReportView) Fields() []ent.Field {
	return []ent.Field{
		field.Time("created_at"),
		field.Int("id"),
		field.String("person_code"),
		field.String("province"),
		field.String("district"),
		field.String("ward"),
		field.String("address_number"),
		field.String("full_name"),
		field.String("job_name"),
		field.String("birthday"),
		field.JSON("person", map[string]interface{}{}).Optional(),
		field.String("phone"),
		field.String("product_names").Optional(),
		field.String("group_names").Optional(),
		field.Float("income"),
		field.Float("expense"),
		field.Float("total_amount"),
		field.Float("cash").Optional(),
		field.Float("bank").Optional(),
		field.Float("mpos").Optional(),
		field.Float("credit_card").Optional(),
		field.Float("momo").Optional(),
		field.Float("product_amount").Optional().Comment("Total amount of products (category 32)"),
		field.Float("general_service_amount").Optional().Comment("Total amount of general services (category 27)"),
		field.Float("gxtn_amount").Optional().Comment("Total amount of general services (category 27)"),
		field.Float("veneer_amount").Optional().Comment("Total amount of general services (category 27)"),
		field.Float("phs_amount").Optional().Comment("Total amount of general services (category 31)"),
		field.Float("orthodontic_amount").Optional().Comment("Total amount of orthodontic services (category 28 or has deal_plan)"),
		field.Float("implant_amount").Optional().Comment("Total amount of implant services (category 29 or no deal_plan)"),
		field.Float("other_amount").Optional().Comment("Total amount of other categories"),
		field.String("doctor_name").Optional().Nillable(),
		field.String("creator_name").Optional().Nillable(),
		field.JSON("doctor", map[string]interface{}{}).Optional(),
	}
}

// Config of the view
func (PaymentReportView) Config() ent.Config {
	return ent.Config{
		Table: "payment_report_view",
	}
}
