// Code generated by entimport, DO NOT EDIT.

package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
	"entgo.io/ent/schema/index"
)

type Schedule struct {
	ent.Schema
}

func (Schedule) Fields() []ent.Field {
	return []ent.Field{
		field.Int("user_id").Optional(),
		field.Time("start_time").Optional(),
		field.Time("end_time").Optional(),
		field.Int("stage").Default(0)}
}
func (Schedule) Edges() []ent.Edge {
	return []ent.Edge{
		edge.To("user", User.Type).
			Unique().
			Field("user_id"),
	}
}

func (Schedule) Annotations() []schema.Annotation {
	return []schema.Annotation{entsql.Annotation{Table: "schedule"}}
}

func (Schedule) Mixin() []ent.Mixin {
	return []ent.Mixin{
		SoftDeleteMixin{},
		VersionMixin{},
		TimeMixin{},
	}
}

func (Schedule) Indexes() []ent.Index {
	return []ent.Index{
		index.Fields("user_id"),
	}
}