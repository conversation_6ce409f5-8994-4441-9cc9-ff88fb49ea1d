// Code generated by entimport, DO NOT EDIT.

package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
)

type MessageHistory struct {
	ent.Schema
}

func (MessageHistory) Fields() []ent.Field {
	return []ent.Field{
		field.Int("person_id").Optional(),
		field.Int("user_id").Optional(),
		field.String("message_id").Optional(),
		field.String("phone").Optional(),
		field.Enum("type").Optional().Values("sms", "zns"),
		field.Text("content").Optional(),
		field.Text("zns_data").Optional(),
		field.Int("error_code").Optional(),
		field.Enum("message_status").Optional().Values("sent", "failed", "delivered"),
		field.Time("delivered_at").Optional().Nillable(),
	}

}
func (MessageHistory) Edges() []ent.Edge {
	return []ent.Edge{
		edge.To("person", Person.Type).
			Field("person_id").
			Unique(),
		edge.To("user", User.Type).
			Field("user_id").
			Unique(),
	}
}
func (MessageHistory) Annotations() []schema.Annotation {
	return []schema.Annotation{entsql.Annotation{Table: "message_history"}}
}

func (MessageHistory) Mixin() []ent.Mixin {
	return []ent.Mixin{
		SoftDeleteMixin{},
		VersionMixin{},
		TimeMixin{},
	}
}
