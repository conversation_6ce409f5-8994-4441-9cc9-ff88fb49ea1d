package schema

import (
	"bcare/ent/types"
	"entgo.io/ent"
	"entgo.io/ent/schema/field"
)

// PersonQueryView đ<PERSON>i diện cho PersonQueryView.
type PersonQueryView struct {
	ent.View
}

// Fields của PersonTimelineView.
func (PersonQueryView) Fields() []ent.Field {
	return []ent.Field{
		field.Int("id").Optional(),
		field.String("person_code"),
		field.String("full_name"),
		field.Time("date_of_birth").Optional().Nillable(),
		field.Time("created_at").Optional(),
		field.Time("appointment_time").Optional(),
		field.Time("deleted_at").Optional(),
		field.String("gender").Optional(),
		field.Int("province_id").Optional(),
		field.Int("district_id").Optional(),
		field.Int("ward_id").Optional(),
		field.String("address_number").Optional(),
		field.String("phone"),
		field.String("creator_name"),
		field.String("person_source"),
		field.String("doctor_name"),
		field.String("treatment_name"),
		field.String("email").Optional(),
		field.Int("job_id").Nillable().Optional(),
		field.Int("user_id").Optional(),
		field.Int("source_id").Nillable().Optional(),
		field.Bool("phone_confirm").Optional(),
		field.Bool("mail_confirm").Optional(),
		field.JSON("person_field", &types.PersonMeta{}).Optional(),
		field.String("stage_name").Optional(),
		field.String("bank_account_name").Optional(),
		field.String("bank_account_number").Optional(),
		field.String("bank").Optional(),
		field.String("bank_branch").Optional(),
		field.Int("stage_id").Optional(),
		field.Int("stage_parent_id").Optional(),
		field.Int("sale_id").Optional(),
		field.JSON("sale", map[string]interface{}{}).Optional(),
	}
}
