// Code generated by entimport, DO NOT EDIT.

package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
)

type Discount struct {
	ent.Schema
}

func (Discount) Fields() []ent.Field {
	return []ent.Field{field.String("name"),
		field.String("type"),
		field.Float("value"),
		field.String("scope"),
		field.String("condition").Optional(),
		field.String("usage_type"),
		field.String("description").Optional(),
		field.JSON("meta", struct{}{}).Optional(),
		field.Time("start").Optional(),
		field.Time("end").Optional(),
	}
}
func (Discount) Edges() []ent.Edge {
	return []ent.Edge{
		edge.From("discount_usages", DiscountUsage.Type).Ref("discount"),
	}
}
func (Discount) Annotations() []schema.Annotation {
	return []schema.Annotation{entsql.Annotation{Table: "discount"}}
}

func (Discount) Mixin() []ent.Mixin {
	return []ent.Mixin{
		SoftDeleteMixin{},
		VersionMixin{},
		TimeMixin{},
	}
}
