package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
	"entgo.io/ent/schema/index"
)

type InstallmentPlan struct {
	ent.Schema
}

func (InstallmentPlan) Fields() []ent.Field {
	return []ent.Field{
		field.Int("person_id").Optional(),
		field.Int("deal_id").Nillable().Optional(),
		field.Int("bill_id").Optional().Nillable(),
		field.Int("user_id").Optional(),
		field.String("name").Optional().Default("Trả góp"),
		field.Float("total_amount").Default(0).Comment("Tổng số tiền khách phải thanh toán"),
		field.Float("down_payment").Default(0).Comment("Tổng số tiền đặt cọc"),
		field.Int("total_installments").Default(0).Comment("Tổng số lần phải thanh toán"),
		field.Enum("state").
			Values(
				"draft",
				"active",
				"completed",
				"cancelled",
			).
			Default("draft"),
	}
}

func (InstallmentPlan) Edges() []ent.Edge {
	return []ent.Edge{
		edge.To("person", Person.Type).
			Field("person_id").
			Unique(),
		edge.To("deal", Deal.Type).
			Field("deal_id").
			Unique(),
		edge.To("creator", User.Type).
			Field("user_id").
			Unique(),
		edge.From("attachments", Attachment.Type).Ref("plan"),
		edge.From("installments", Installment.Type).Ref("plan"),
		edge.To("bill", Bill.Type).
			Field("bill_id").
			Unique(),
	}
}

func (InstallmentPlan) Annotations() []schema.Annotation {
	return []schema.Annotation{entsql.Annotation{Table: "installment_plan"}}
}

func (InstallmentPlan) Mixin() []ent.Mixin {
	return []ent.Mixin{
		SoftDeleteMixin{},
		VersionMixin{},
		TimeMixin{},
	}
}

func (InstallmentPlan) Indexes() []ent.Index {
	return []ent.Index{
		index.Fields("person_id"),
		index.Fields("deal_id"),
		index.Fields("deal_id", "deleted_at", "status"),
	}
}
