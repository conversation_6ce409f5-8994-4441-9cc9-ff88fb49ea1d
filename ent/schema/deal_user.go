// Code generated by entimport, DO NOT EDIT.

package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
	"entgo.io/ent/schema/index"
)

type DealUser struct {
	ent.Schema
}

func (DealUser) Fields() []ent.Field {
	return []ent.Field{
		field.Int("deal_id"),
		field.Int("user_id"),
		field.Enum("role").Optional().Values("treatment_doctor", "consultant_doctor", "advisor", "assistant", "xray_technician", "doctor_assistant"),
	}
}
func (DealUser) Edges() []ent.Edge {
	return []ent.Edge{
		edge.To("deal", Deal.Type).
			Required().
			Unique().
			Field("deal_id"),
		edge.To("user", User.Type).
			Required().
			Unique().
			Field("user_id"),
	}
}
func (DealUser) Annotations() []schema.Annotation {
	return []schema.Annotation{entsql.Annotation{Table: "deal_user"}}
}

func (DealUser) Indexes() []ent.Index {
	return []ent.Index{
		index.Fields("deal_id"),
		index.Fields("user_id"),
	}
}