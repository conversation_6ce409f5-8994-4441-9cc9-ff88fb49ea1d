// Code generated by entimport, DO NOT EDIT.

package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
	"entgo.io/ent/schema/index"
)

type TaskAssignment struct {
	ent.Schema
}

func (TaskAssignment) Fields() []ent.Field {
	return []ent.Field{
		field.Int("task_id").Optional(),
		field.Int("serial").Immutable(),
		field.Int("user_id").Optional(),
		field.Time("due_at"),
		field.Time("started_at").Default(nil).Optional(),
		field.Time("completed_at").Default(nil).Optional().Nillable(),
		field.Enum("state").Default("new_task").Values("new_task", "in_progress", "overdue", "awaiting_approval", "completed", "cancelled", "cancelled_in_progress", "completed_early"),
		field.Enum("role").Optional().Values("primary", "contributor", "reviewer"),
	}
}
func (TaskAssignment) Edges() []ent.Edge {
	return []ent.Edge{
		edge.To("task", Task.Type).
			Field("task_id").
			Unique(),

		edge.To("user", User.Type).
			Field("user_id").
			Unique(),
	}
}
func (TaskAssignment) Annotations() []schema.Annotation {
	return []schema.Annotation{entsql.Annotation{Table: "task_assignment"}}
}

func (TaskAssignment) Mixin() []ent.Mixin {
	return []ent.Mixin{
		SoftDeleteMixin{},
		VersionMixin{},
		TimeMixin{},
	}
}

func (TaskAssignment) Indexes() []ent.Index {
	return []ent.Index{
		index.Fields("task_id"),
		index.Fields("user_id"),
	}
}
