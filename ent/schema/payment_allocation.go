package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
	"entgo.io/ent/schema/index"
)

type PaymentAllocation struct {
	ent.Schema
}

func (PaymentAllocation) Fields() []ent.Field {
	return []ent.Field{
		field.Int("payment_id"),
		field.Int("bill_id").Optional(),
		field.Int("user_id").Optional(),
		field.Int("bill_item_id").Optional().Nillable(),
		field.Int("installment_id").Optional().Nillable(),
		field.String("note").Optional(),
		field.String("state").Optional(),
		field.Float("amount").Default(0),
	}
}

func (PaymentAllocation) Edges() []ent.Edge {
	return []ent.Edge{
		edge.To("payment", Payment.Type).
			Unique().
			Required().
			Field("payment_id"),
		edge.To("bill_item", BillItem.Type).
			Unique().
			Field("bill_item_id"),
		edge.To("installment", Installment.Type).
			Unique().
			Field("installment_id"),
	}
}

func (PaymentAllocation) Annotations() []schema.Annotation {
	return []schema.Annotation{
		entsql.Annotation{Table: "payment_allocation"},
	}
}

func (PaymentAllocation) Mixin() []ent.Mixin {
	return []ent.Mixin{
		SoftDeleteMixin{},
		VersionMixin{},
		TimeMixin{},
	}
}

func (PaymentAllocation) Indexes() []ent.Index {
	return []ent.Index{
		index.Fields("payment_id"),
		index.Fields("bill_item_id"),
		index.Fields("status"),
		index.Fields("deleted_at"),
	}
}
