// Code generated by entimport, DO NOT EDIT.

package schema

import (
	"bcare/common/ctxdata"
	bent "bcare/ent"
	"bcare/ent/hook"
	"bcare/ent/task"
	"bcare/ent/types"
	"context"
	"entgo.io/ent/schema/index"
	"fmt"
	"log"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
)

type Task struct {
	ent.Schema
}

func (Task) Fields() []ent.Field {
	return []ent.Field{
		field.String("title"),
		field.Text("note").Optional(),
		field.Time("start_date").Optional(),
		field.Time("due_date").Optional().Default(nil).Nillable(),
		field.Time("end_date").Default(nil).Optional().Nillable(),
		field.String("type").Optional(),
		field.Int("priority").Optional(),
		field.Int("current_serial").Default(1),
		field.Int("parent_id").Nillable().Optional(),
		field.Int("person_id").Nillable().Optional(),
		field.Int("deal_id").Nillable().Optional(),
		field.Int("appointment_id").Nillable().Optional(),
		field.Int("department_id").Nillable().Optional(),
		field.Int("creator_id").Optional(),
		field.Enum("state").
			Optional().
			Values(
				"new_task",
				"in_progress",
				"overdue",
				"awaiting_approval",
				"completed",
				"cancelled",
				"cancelled_in_progress",
				"completed_early",
			),
		field.Time("completed_at").Default(nil).Optional().Nillable(),
		field.JSON("history", []types.TaskHistoryEntry{}).
			Optional().
			Default([]types.TaskHistoryEntry{}),
	}
}

func (Task) Edges() []ent.Edge {
	return []ent.Edge{
		edge.To("children", Task.Type).
			From("parent").
			Field("parent_id").
			Unique(),
		edge.To("person", Person.Type).
			Field("person_id").
			Unique(),
		edge.To("deal", Deal.Type).
			Field("deal_id").
			Unique(),
		edge.To("appointment", Appointment.Type).
			Field("appointment_id").
			Unique(),
		edge.To("creator", User.Type).
			Field("creator_id").
			Unique(),
		edge.From("assignments", TaskAssignment.Type).
			Ref("task"),
		edge.From("department_assignments", TaskDepartment.Type).
			Ref("task"),
		edge.From("notes", TaskNote.Type).
			Ref("task"),
		edge.To("recurring", TaskRecurring.Type).
			Unique(),
	}
}

func (Task) Annotations() []schema.Annotation {
	return []schema.Annotation{
		entsql.Annotation{Table: "task"},
	}
}

func (Task) Mixin() []ent.Mixin {
	return []ent.Mixin{
		SoftDeleteMixin{},
		VersionMixin{},
		TimeMixin{},
	}
}

func (Task) Indexes() []ent.Index {
	return []ent.Index{
		index.Fields("person_id"),
		index.Fields("creator_id"),
	}
}

func (Task) Hooks() []ent.Hook {
	return []ent.Hook{
		hook.On(
			func(next ent.Mutator) ent.Mutator {
				return hook.TaskFunc(func(ctx context.Context, m *bent.TaskMutation) (ent.Value, error) {
					userID := ctxdata.GetUidFromCtx(ctx)

					var affectedIDs []int
					if m.Op().Is(ent.OpUpdateOne) {
						id, _ := m.ID()
						affectedIDs = []int{id}
					} else {
						var err error
						affectedIDs, err = m.IDs(ctx)
						if err != nil {
							return nil, fmt.Errorf("failed to get affected IDs: %w", err)
						}
					}

					oldTasks, err := m.Client().Task.Query().
						Where(task.IDIn(affectedIDs...)).
						All(ctx)
					if err != nil {
						return nil, fmt.Errorf("failed to fetch old tasks: %w", err)
					}

					oldTaskMap := make(map[int]*bent.Task)
					for _, t := range oldTasks {
						oldTaskMap[t.ID] = t
					}

					value, err := next.Mutate(ctx, m)
					if err != nil {
						return nil, err
					}

					for _, taskID := range affectedIDs {
						oldTask := oldTaskMap[taskID]
						changes := []types.TaskFieldChange{}

						// Track title changes
						if title, exists := m.Title(); exists {
							if oldTask.Title != title {
								changes = append(changes, types.TaskFieldChange{
									Field:    "title",
									OldValue: oldTask.Title,
									NewValue: title,
								})
							}
						}

						// Track note changes
						if note, exists := m.Note(); exists {
							if oldTask.Note != note {
								changes = append(changes, types.TaskFieldChange{
									Field:    "note",
									OldValue: oldTask.Note,
									NewValue: note,
								})
							}
						}
						if m.NoteCleared() {
							changes = append(changes, types.TaskFieldChange{
								Field:    "note",
								OldValue: oldTask.Note,
								NewValue: nil,
							})
						}

						// Track start_date changes
						if startDate, exists := m.StartDate(); exists {
							if (oldTask.StartDate.IsZero() && !startDate.IsZero()) ||
								(oldTask.StartDate.IsZero() && startDate.IsZero()) ||
								(oldTask.StartDate.IsZero() && !startDate.IsZero() && !oldTask.StartDate.Equal(startDate)) {
								changes = append(changes, types.TaskFieldChange{
									Field:    "start_date",
									OldValue: oldTask.StartDate,
									NewValue: startDate,
								})
							}
						}
						if m.StartDateCleared() {
							changes = append(changes, types.TaskFieldChange{
								Field:    "start_date",
								OldValue: oldTask.StartDate,
								NewValue: nil,
							})
						}

						// Track due_date changes
						if dueDate, exists := m.DueDate(); exists {
							if (oldTask.DueDate == nil && !dueDate.IsZero()) ||
								(oldTask.DueDate != nil && dueDate.IsZero()) ||
								(oldTask.DueDate != nil && !dueDate.IsZero() && !oldTask.DueDate.Equal(dueDate)) {
								changes = append(changes, types.TaskFieldChange{
									Field:    "due_date",
									OldValue: oldTask.DueDate,
									NewValue: dueDate,
								})
							}
						}
						if m.DueDateCleared() {
							changes = append(changes, types.TaskFieldChange{
								Field:    "due_date",
								OldValue: oldTask.DueDate,
								NewValue: nil,
							})
						}

						// Track end_date changes
						if endDate, exists := m.EndDate(); exists {
							if (oldTask.EndDate == nil && !endDate.IsZero()) ||
								(oldTask.EndDate != nil && endDate.IsZero()) ||
								(oldTask.EndDate != nil && !endDate.IsZero() && !oldTask.EndDate.Equal(endDate)) {
								changes = append(changes, types.TaskFieldChange{
									Field:    "end_date",
									OldValue: oldTask.EndDate,
									NewValue: endDate,
								})
							}
						}
						if m.EndDateCleared() {
							changes = append(changes, types.TaskFieldChange{
								Field:    "end_date",
								OldValue: oldTask.EndDate,
								NewValue: nil,
							})
						}

						// Track type changes
						if typeVal, exists := m.GetType(); exists {
							if oldTask.Type != typeVal {
								changes = append(changes, types.TaskFieldChange{
									Field:    "type",
									OldValue: oldTask.Type,
									NewValue: typeVal,
								})
							}
						}
						if m.TypeCleared() {
							changes = append(changes, types.TaskFieldChange{
								Field:    "type",
								OldValue: oldTask.Type,
								NewValue: nil,
							})
						}

						// Track priority changes
						if priority, exists := m.Priority(); exists {
							if oldTask.Priority != priority {
								changes = append(changes, types.TaskFieldChange{
									Field:    "priority",
									OldValue: oldTask.Priority,
									NewValue: priority,
								})
							}
						}
						if m.PriorityCleared() {
							changes = append(changes, types.TaskFieldChange{
								Field:    "priority",
								OldValue: oldTask.Priority,
								NewValue: nil,
							})
						}

						// Track current_serial changes
						if currentSerial, exists := m.CurrentSerial(); exists {
							if oldTask.CurrentSerial != currentSerial {
								changes = append(changes, types.TaskFieldChange{
									Field:    "current_serial",
									OldValue: oldTask.CurrentSerial,
									NewValue: currentSerial,
								})
							}
						}

						// Track parent_id changes
						if parentID, exists := m.ParentID(); exists {
							if *oldTask.ParentID != parentID {
								changes = append(changes, types.TaskFieldChange{
									Field:    "parent_id",
									OldValue: oldTask.ParentID,
									NewValue: parentID,
								})
							}
						}
						if m.ParentIDCleared() {
							changes = append(changes, types.TaskFieldChange{
								Field:    "parent_id",
								OldValue: oldTask.ParentID,
								NewValue: nil,
							})
						}

						// Track person_id changes
						if personID, exists := m.PersonID(); exists {
							if *oldTask.PersonID != personID {
								changes = append(changes, types.TaskFieldChange{
									Field:    "person_id",
									OldValue: oldTask.PersonID,
									NewValue: personID,
								})
							}
						}
						if m.PersonIDCleared() {
							changes = append(changes, types.TaskFieldChange{
								Field:    "person_id",
								OldValue: oldTask.PersonID,
								NewValue: nil,
							})
						}

						// Track deal_id changes
						if dealID, exists := m.DealID(); exists {
							if *oldTask.DealID != dealID {
								changes = append(changes, types.TaskFieldChange{
									Field:    "deal_id",
									OldValue: oldTask.DealID,
									NewValue: dealID,
								})
							}
						}
						if m.DealIDCleared() {
							changes = append(changes, types.TaskFieldChange{
								Field:    "deal_id",
								OldValue: oldTask.DealID,
								NewValue: nil,
							})
						}

						// Track appointment_id changes
						if appointmentID, exists := m.AppointmentID(); exists {
							if *oldTask.AppointmentID != appointmentID {
								changes = append(changes, types.TaskFieldChange{
									Field:    "appointment_id",
									OldValue: oldTask.AppointmentID,
									NewValue: appointmentID,
								})
							}
						}
						if m.AppointmentIDCleared() {
							changes = append(changes, types.TaskFieldChange{
								Field:    "appointment_id",
								OldValue: oldTask.AppointmentID,
								NewValue: nil,
							})
						}

						// Track department_id changes
						if departmentID, exists := m.DepartmentID(); exists {
							if *oldTask.DepartmentID != departmentID {
								changes = append(changes, types.TaskFieldChange{
									Field:    "department_id",
									OldValue: oldTask.DepartmentID,
									NewValue: departmentID,
								})
							}
						}
						if m.DepartmentIDCleared() {
							changes = append(changes, types.TaskFieldChange{
								Field:    "department_id",
								OldValue: oldTask.DepartmentID,
								NewValue: nil,
							})
						}

						// Track creator_id changes
						if creatorID, exists := m.CreatorID(); exists {
							if oldTask.CreatorID != creatorID {
								changes = append(changes, types.TaskFieldChange{
									Field:    "creator_id",
									OldValue: oldTask.CreatorID,
									NewValue: creatorID,
								})
							}
						}
						if m.CreatorIDCleared() {
							changes = append(changes, types.TaskFieldChange{
								Field:    "creator_id",
								OldValue: oldTask.CreatorID,
								NewValue: nil,
							})
						}

						// Track state changes
						if state, exists := m.State(); exists {
							if oldTask.State != state {
								changes = append(changes, types.TaskFieldChange{
									Field:    "state",
									OldValue: oldTask.State,
									NewValue: state,
								})
							}
						}
						if m.StateCleared() {
							changes = append(changes, types.TaskFieldChange{
								Field:    "state",
								OldValue: oldTask.State,
								NewValue: nil,
							})
						}

						// Track completed_at changes
						if completedAt, exists := m.CompletedAt(); exists {
							log.Println(completedAt)
							log.Println(oldTask.CompletedAt)
							if (oldTask.CompletedAt == nil && !completedAt.IsZero()) ||
								(oldTask.CompletedAt != nil && completedAt.IsZero()) ||
								(oldTask.CompletedAt != nil && !completedAt.IsZero() && !oldTask.CompletedAt.Equal(completedAt)) {
								changes = append(changes, types.TaskFieldChange{
									Field:    "completed_at",
									OldValue: oldTask.CompletedAt,
									NewValue: completedAt,
								})
							}
						}
						if m.CompletedAtCleared() {
							if oldTask.CompletedAt != nil {
								changes = append(changes, types.TaskFieldChange{
									Field:    "completed_at",
									OldValue: oldTask.CompletedAt,
									NewValue: nil,
								})
							}
						}

						if len(changes) > 0 {
							newEntry := types.TaskHistoryEntry{
								UserID:    userID,
								Changes:   changes,
								Timestamp: time.Now(),
							}

							currentHistory := oldTask.History
							updatedHistory := append(currentHistory, newEntry)

							err := m.Client().Task.UpdateOneID(oldTask.ID).
								SetHistory(updatedHistory).
								Exec(ctx)
							if err != nil {
								return nil, fmt.Errorf("failed to update history for task %d: %w", taskID, err)
							}
						}
					}

					return value, nil
				})
			},
			ent.OpUpdateOne|ent.OpUpdate,
		),
	}
}