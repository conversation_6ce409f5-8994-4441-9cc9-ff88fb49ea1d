// Code generated by entimport, DO NOT EDIT.

package schema

import (
	"entgo.io/ent/schema/index"

	"entgo.io/ent"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
)

type User struct {
	ent.Schema
}

func (User) Fields() []ent.Field {
	return []ent.Field{
		field.String("username").Unique().Comment("The username"),
		field.String("password").Comment("The user password"),
		field.String("phone").Optional().Comment("The mobile phone number"),
		field.String("email").Optional().Comment("The email"),
		field.Bool("email_confirmed").Optional(),
		field.String("name").Optional().Comment("The full name"),
		field.String("gender").Comment("gender,male|female|unknown"),
		field.Int("department_id").Optional().Comment("Department"),
		field.String("department_position").Optional().Comment("Department"),
		field.String("profile_image").Optional(),
		field.Time("suspended_at").Optional(),
		field.Enum("state").
			Values(
				"online",    // user is online and active
				"offline",   // user is logged out or disconnected
				"inactive", // user is logged in but not active for a while
				"busy",       // user is busy and doesn't want to be disturbed
			).
			Default("offline"),
	}
}
func (User) Edges() []ent.Edge {
	return []ent.Edge{

		edge.From("assignment", TaskAssignment.Type).Ref("user"),
		edge.From("assigned_person", Person.Type).
			Ref("assignees").
			Through("person_assignment", PersonAssignment.Type),
		edge.From("assigned_deal", Deal.Type).
			Ref("assignees").
			Through("deal_assignment", DealUser.Type),
		edge.From("notes", Note.Type).Ref("creator"),
		edge.From("task_notes", TaskNote.Type).Ref("creator"),
		edge.From("appointments", Appointment.Type).Ref("doctor"),
		edge.From("calls", Call.Type).Ref("user"),
		edge.From("schedules", Schedule.Type).Ref("user"),
		edge.From("message_histories", MessageHistory.Type).Ref("user"),
		edge.From("data", UserData.Type).Ref("user"),
	}
}
func (User) Annotations() []schema.Annotation {
	return []schema.Annotation{entsql.Annotation{Table: "user"}}
}

func (User) Mixin() []ent.Mixin {
	return []ent.Mixin{
		SoftDeleteMixin{},
		VersionMixin{},
		TimeMixin{},
	}
}

func (User) Indexes() []ent.Index {
	return []ent.Index{
		index.Fields("deleted_at", "status"),
		index.Fields("username", "status").Unique(),
	}
}
