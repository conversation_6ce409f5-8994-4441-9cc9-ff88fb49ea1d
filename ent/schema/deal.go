// Code generated by entimport, DO NOT EDIT.

package schema

import (
	"bcare/common/ctxdata"
	bent "bcare/ent"
	"bcare/ent/hook"
	"bcare/ent/types"
	"context"
	"encoding/json"
	"entgo.io/ent"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
	"entgo.io/ent/schema/index"
	"time"
)

type Deal struct {
	ent.Schema
}

func (Deal) Fields() []ent.Field {
	return []ent.Field{
		// Giữ nguyên các field hiện tại
		field.Int("person_id").Optional(),
		field.Int("parent_deal_id").Nillable().Optional(),
		field.Float("total_amount").Default(0),
		field.Int("stage_id").Nillable().Optional(),
		field.JSON("stage_history", []types.StageHistoryEntry{}).Optional(),
		field.String("name").Optional(),

		// Thêm state mới
		field.Enum("state").
			Values(
				"draft",     // Nháp
				"active",    // Đang thực hiện
				"paying",    // Đang thanh toán
				"won",       // Đã chốt deal
				"lost",      // Mất deal
				"cancelled", // Hủy
			).
			Default("draft"),
	}
}

func (Deal) Edges() []ent.Edge {
	return []ent.Edge{
		edge.To("person", Person.Type).
			Field("person_id").
			Unique(),
		edge.To("stage", Stage.Type).
			Field("stage_id").
			Unique(),
		edge.From("attachments", Attachment.Type).Ref("deal"),
		edge.From("plans", InstallmentPlan.Type).Ref("deal"),
		edge.From("bills", Bill.Type).Ref("deal"),
		edge.From("tracks", Track.Type).Ref("deal"),
		edge.From("tasks", Task.Type).Ref("deal"),
		edge.To("assignees", User.Type).Through("deal_assignment", DealUser.Type),
		edge.From("deposits", Deposit.Type).
			Ref("deal"),
		edge.From("discount_usages", DiscountUsage.Type).Ref("deal"),

	}
}
func (Deal) Annotations() []schema.Annotation {
	return []schema.Annotation{entsql.Annotation{Table: "deal"}}
}

func (Deal) Mixin() []ent.Mixin {
	return []ent.Mixin{
		SoftDeleteMixin{},
		VersionMixin{},
		TimeMixin{},
	}
}

func (Deal) Hooks() []ent.Hook {
	return []ent.Hook{
		hook.On(
			func(next ent.Mutator) ent.Mutator {
				return hook.DealFunc(func(ctx context.Context, m *bent.DealMutation) (ent.Value, error) {
					if skip, _ := ctx.Value(skipStageHistoryKey{}).(bool); skip {
						return next.Mutate(ctx, m)
					}

					// Kiểm tra xem stage_id có thay đổi không
					if stageID, exists := m.StageID(); exists {
						oldStageID, err := m.OldStageID(ctx)
						if err != nil {
							return nil, err
						}

						// Nếu stageID mới và cũ bằng nhau, không làm gì cả
						if (oldStageID == nil && stageID == 0) || (oldStageID != nil && stageID != 0 && *oldStageID == stageID) {
							return next.Mutate(ctx, m) // Không cần cập nhật lịch sử
						}

						// Khởi tạo lịch sử giai đoạn
						var history []types.StageHistoryEntry
						if oldHistory, exists := m.OldStageHistory(ctx); exists == nil {
							oldHistoryJSON, err := json.Marshal(oldHistory)
							if err != nil {
								return nil, err
							}

							if err := json.Unmarshal(oldHistoryJSON, &history); err != nil {
								return nil, err
							}
						}

						now := time.Now()

						// Cập nhật entry cuối cùng (nếu có)
						if len(history) > 0 {
							history[len(history)-1].ExitedAt = &now
						}

						// Lấy thông tin bổ sung từ context
						userId := ctxdata.GetUidFromCtx(ctx)
						changeReasonValue := ctx.Value("change_reason")

						// Lấy giá trị mặc định nếu giá trị từ context bị thiếu
						changeReason := ""
						if changeReasonValue != nil {
							changeReason, _ = changeReasonValue.(string)
						}

						// Tạo con trỏ cho stageID
						var stageIDPtr *int
						if stageID != 0 {
							stageIDPtr = &stageID
						}

						// Thêm entry mới
						newEntry := types.StageHistoryEntry{
							StageID:   stageIDPtr, // Cập nhật stageID mới (có thể là nil)
							EnteredAt: now,
							UserId:    userId,
							Reason:    changeReason,
						}
						history = append(history, newEntry)

						// Cập nhật stage_history
						newHistoryJSON, err := json.Marshal(history)
						if err != nil {
							return nil, err
						}
						var newHistory []types.StageHistoryEntry
						if err := json.Unmarshal(newHistoryJSON, &newHistory); err != nil {
							return nil, err
						}
						m.SetStageHistory(newHistory)
					}
					return next.Mutate(ctx, m)
				})
			},
			ent.OpUpdateOne,
		),
	}
}

type skipStageHistoryKey struct{}

// SkipStageHistory returns a new context that skips the stage history recording.
func SkipStageHistory(parent context.Context) context.Context {
	return context.WithValue(parent, skipStageHistoryKey{}, true)
}

// deal
func (Deal) Indexes() []ent.Index {
	return []ent.Index{
		index.Fields("person_id"),
		index.Fields("stage_id", "status", "deleted_at"),
	}
}
