// Code generated by entimport, DO NOT EDIT.

package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
)

type Stage struct {
	ent.Schema
}

func (Stage) Fields() []ent.Field {
	return []ent.Field{
		field.String("name"),
		field.Int("pipeline_id").Optional(),
		field.Int("order_number").Optional(),
		field.Int("parent_stage_id").Nillable().Optional(),
		field.String("meta").Optional()}
}
func (Stage) Edges() []ent.Edge {
	return []ent.Edge{
		edge.To("children", Stage.Type).From("parent").Field("parent_stage_id").Unique(),
		edge.From("deals", Deal.Type).Ref("stage"),
		edge.From("person_stages", PersonStage.Type).Ref("stage"),
		edge.To("pipeline", Pipeline.Type).Field("pipeline_id").Unique(),
	}
}
func (Stage) Annotations() []schema.Annotation {
	return []schema.Annotation{entsql.Annotation{Table: "stage"}}
}

func (Stage) Mixin() []ent.Mixin {
	return []ent.Mixin{
		SoftDeleteMixin{},
		VersionMixin{},
		TimeMixin{},
	}
}
