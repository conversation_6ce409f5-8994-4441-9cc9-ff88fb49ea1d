// Code generated by entimport, DO NOT EDIT.

package schema

import (
	"bcare/common/ctxdata"
	bent "bcare/ent"
	"bcare/ent/hook"
	"bcare/ent/types"
	"context"
	"encoding/json"
	"entgo.io/ent"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
	"entgo.io/ent/schema/index"
	"time"
)

type Track struct {
	ent.Schema
}

func (Track) Fields() []ent.Field {
	return []ent.Field{
		field.Time("begin").Optional(),
		field.Time("end").Optional(),
		field.Int("deal_id").Optional().Nillable(),
		field.Int("pipeline_id").Optional().Nillable(),
		field.Int("stage_id").Optional().Nillable(),
		field.Int("user_id").Optional(),
		field.Int("person_id").Optional(),
		field.Float("weight").Optional(),
		field.JSON("meta", struct{}{}).Optional(),
		field.JSON("stage_history", []types.StageHistoryEntry{}).Optional(),
	}

}
func (Track) Edges() []ent.Edge {
	return []ent.Edge{
		edge.To("deal", Deal.Type).
			Unique().
			Field("deal_id"),

		edge.To("user", User.Type).
			Unique().
			Field("user_id"),

		edge.To("person", Person.Type).
			Unique().
			Field("person_id"),

		edge.To("pipeline", Pipeline.Type).
			Unique().
			Field("pipeline_id"),
		edge.To("stage", Stage.Type).
			Unique().
			Field("stage_id"),
		edge.From("appointments", Appointment.Type).
			Ref("track"),
		edge.From("file_usages", FileUsage.Type).Ref("track"),
		edge.From("attachments", Attachment.Type).Ref("track"),
		edge.From("bill_items", BillItem.Type).Ref("track"),
	}
}
func (Track) Annotations() []schema.Annotation {
	return []schema.Annotation{entsql.Annotation{Table: "track"}}
}

func (Track) Mixin() []ent.Mixin {
	return []ent.Mixin{
		SoftDeleteMixin{},
		VersionMixin{},
		TimeMixin{},
	}
}

func (Track) Indexes() []ent.Index {
	return []ent.Index{
		index.Fields("deal_id", "pipeline_id").
			Unique().
			Annotations(
				entsql.IndexWhere("\"end\" IS NULL"),
			),
		index.Fields("user_id"),
		index.Fields("deal_id"),
		index.Fields("person_id"),
		index.Fields("pipeline_id"),
	}
}

func (Track) Hooks() []ent.Hook {
	return []ent.Hook{
		hook.On(
			func(next ent.Mutator) ent.Mutator {
				return hook.TrackFunc(func(ctx context.Context, m *bent.TrackMutation) (ent.Value, error) {
					// Skip if context contains skip flag
					if skip, _ := ctx.Value(skipTrackStageHistoryKey{}).(bool); skip {
						return next.Mutate(ctx, m)
					}

					// Check if stage_id has changed
					if stageID, exists := m.StageID(); exists {
						oldStageID, err := m.OldStageID(ctx)

						// Nếu stageID mới và cũ bằng nhau, không làm gì cả
						if (oldStageID == nil && stageID == 0) || (oldStageID != nil && stageID != 0 && *oldStageID == stageID) {
							return next.Mutate(ctx, m) // Không cần cập nhật lịch sử
						}

						// Khởi tạo lịch sử giai đoạn
						var history []types.StageHistoryEntry
						if oldHistory, exists := m.OldStageHistory(ctx); exists == nil {
							oldHistoryJSON, err := json.Marshal(oldHistory)
							if err != nil {
								return nil, err
							}

							if err := json.Unmarshal(oldHistoryJSON, &history); err != nil {
								return nil, err
							}
						}

						now := time.Now()

						// Update exit time for the last entry
						if len(history) > 0 {
							history[len(history)-1].ExitedAt = &now
						}

						// Get additional info from context
						userId := ctxdata.GetUidFromCtx(ctx)
						changeReasonValue := ctx.Value("change_reason")

						// Set default values if context values are missing
						changeReason := ""
						if changeReasonValue != nil {
							changeReason, _ = changeReasonValue.(string)
						}

						// Tạo con trỏ cho stageID
						var stageIDPtr *int
						if stageID != 0 {
							stageIDPtr = &stageID
						}

						// Thêm entry mới
						newEntry := types.StageHistoryEntry{
							StageID:   stageIDPtr, // Cập nhật stageID mới (có thể là nil)
							EnteredAt: now,
							UserId:    userId,
							Reason:    changeReason,
						}

						history = append(history, newEntry)

						// Cập nhật stage_history
						newHistoryJSON, err := json.Marshal(history)
						if err != nil {
							return nil, err
						}
						var newHistory []types.StageHistoryEntry
						if err := json.Unmarshal(newHistoryJSON, &newHistory); err != nil {
							return nil, err
						}
						m.SetStageHistory(newHistory)
					}
					return next.Mutate(ctx, m)
				})
			},
			ent.OpUpdateOne,
		),
	}
}

// skipStageHistoryKey is used as context key
type skipTrackStageHistoryKey struct{}

// SkipTrackStageHistory returns a new context that skips the stage history recording.
func SkipTrackStageHistory(parent context.Context) context.Context {
	return context.WithValue(parent, skipTrackStageHistoryKey{}, true)
}
