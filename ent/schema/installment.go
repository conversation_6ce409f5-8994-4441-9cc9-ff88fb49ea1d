// Code generated by entimport, DO NOT EDIT.

package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
	"entgo.io/ent/schema/index"
)

type Installment struct {
	ent.Schema
}

func (Installment) Fields() []ent.Field {
	return []ent.Field{
		field.Int("plan_id").Optional(),
		field.Int("installment_number").Default(0),
		field.Float("amount").Default(0),
		field.String("note").Optional(),
		field.String("name").Optional(),
		field.Int("person_id").Nillable().Optional(),
		field.Int("user_id").Optional(),
		field.Int("transaction_type").Default(0),
		field.Enum("kind").Values("down_payment", "sequence_payment", "refund_payment").Optional(),
		field.Time("paid_at").Nillable().Optional(),
	}
}
func (Installment) Edges() []ent.Edge {
	return []ent.Edge{
		edge.To("plan", InstallmentPlan.Type).
			Field("plan_id").
			Unique(),
		edge.To("creator", User.Type).
			Field("user_id").
			Unique(),
		edge.From("allocations", PaymentAllocation.Type).
			Ref("installment"),
	}
}
func (Installment) Annotations() []schema.Annotation {
	return []schema.Annotation{entsql.Annotation{Table: "installment"}}
}

func (Installment) Mixin() []ent.Mixin {
	return []ent.Mixin{
		SoftDeleteMixin{},
		VersionMixin{},
		TimeMixin{},
	}
}

func (Installment) Indexes() []ent.Index {
	return []ent.Index{
		index.Fields("plan_id"),
		index.Fields("person_id"),
	}
}