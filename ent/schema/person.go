// Code generated by entimport, DO NOT EDIT.

package schema

import (
	"bcare/ent/schema/edge_annotation"
	"bcare/ent/types"
	"entgo.io/ent/schema/index"

	"entgo.io/ent"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
)

type Person struct {
	ent.Schema
}

func (Person) Fields() []ent.Field {
	return []ent.Field{
		field.String("full_name"),
		field.Time("date_of_birth").Optional().Nillable(),
		field.String("gender").Optional(),
		field.Int("province_id").Optional(),
		field.Int("district_id").Optional(),
		field.Int("ward_id").Optional(),
		field.String("address_number").Optional(),
		field.String("phone"),
		field.String("email").Optional(),
		field.Int("job_id").Nillable().Optional(),
		field.Int("user_id").Optional(),
		field.Int("source_id").Nillable().Optional(),
		field.Bool("phone_confirm").Optional(),
		field.Bool("mail_confirm").Optional(),
		field.JSON("person_field", &types.PersonMeta{}).Optional(),
	}
}
func (Person) Edges() []ent.Edge {
	return []ent.Edge{
		edge.From("person_stages", PersonStage.Type).Ref("person"),
		edge.From("deals", Deal.Type).Ref("person"),
		edge.From("plans", InstallmentPlan.Type).Ref("person"),
		edge.From("calls", Call.Type).Ref("person"),
		edge.From("tasks", Task.Type).Ref("person"),
		edge.From("issues", Issue.Type).Ref("person"),
		edge.From("appointments", Appointment.Type).Ref("person"),
		edge.From("tracks", Track.Type).Ref("person"),
		edge.From("message_histories", MessageHistory.Type).Ref("person"),
		edge.To("assignees", User.Type).Through("assignment", PersonAssignment.Type),
		//edge.To("person_field", PersonField.Type).Unique(),
		edge.To("job", Term.Type).Field("job_id").
			Annotations(edge_annotation.Annotation{
				Constraint: []string{`term.Bundle("nghe_nghiep")`},
			}).Unique(),
		edge.To("source", Term.Type).Field("source_id").
			Annotations(edge_annotation.Annotation{
				Constraint: []string{`term.Bundle("nguon")`},
			}).Unique(),
		edge.To("creator", User.Type).Field("user_id").Unique(),
		edge.To("referred_by", PersonReferral.Type).Unique(),
	}
}
func (Person) Annotations() []schema.Annotation {
	return []schema.Annotation{entsql.Annotation{Table: "person"}}
}

func (Person) Mixin() []ent.Mixin {
	return []ent.Mixin{
		SoftDeleteMixin{},
		VersionMixin{},
		TimeMixin{},
	}
}

// Indexes of the User.
func (Person) Indexes() []ent.Index {
	return []ent.Index{
		index.Fields("full_name", "phone", "status").Unique(),
		index.Fields("full_name"),
		index.Fields("phone"),
		index.Fields( "deleted_at", "status"),
	}
}
