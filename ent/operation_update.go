// Code generated by ent, DO NOT EDIT.

package ent

import (
	"bcare/ent/operation"
	"bcare/ent/predicate"
	"bcare/ent/product"
	"bcare/ent/productoperation"
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/dialect/sql/sqljson"
	"entgo.io/ent/schema/field"
)

// OperationUpdate is the builder for updating Operation entities.
type OperationUpdate struct {
	config
	hooks     []Hook
	mutation  *OperationMutation
	modifiers []func(*sql.UpdateBuilder)
}

// Where appends a list predicates to the OperationUpdate builder.
func (ou *OperationUpdate) Where(ps ...predicate.Operation) *OperationUpdate {
	ou.mutation.Where(ps...)
	return ou
}

// SetDeletedAt sets the "deleted_at" field.
func (ou *OperationUpdate) SetDeletedAt(t time.Time) *OperationUpdate {
	ou.mutation.SetDeletedAt(t)
	return ou
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (ou *OperationUpdate) SetNillableDeletedAt(t *time.Time) *OperationUpdate {
	if t != nil {
		ou.SetDeletedAt(*t)
	}
	return ou
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (ou *OperationUpdate) ClearDeletedAt() *OperationUpdate {
	ou.mutation.ClearDeletedAt()
	return ou
}

// SetStatus sets the "status" field.
func (ou *OperationUpdate) SetStatus(i int8) *OperationUpdate {
	ou.mutation.ResetStatus()
	ou.mutation.SetStatus(i)
	return ou
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (ou *OperationUpdate) SetNillableStatus(i *int8) *OperationUpdate {
	if i != nil {
		ou.SetStatus(*i)
	}
	return ou
}

// AddStatus adds i to the "status" field.
func (ou *OperationUpdate) AddStatus(i int8) *OperationUpdate {
	ou.mutation.AddStatus(i)
	return ou
}

// SetVersion sets the "version" field.
func (ou *OperationUpdate) SetVersion(i int) *OperationUpdate {
	ou.mutation.ResetVersion()
	ou.mutation.SetVersion(i)
	return ou
}

// SetNillableVersion sets the "version" field if the given value is not nil.
func (ou *OperationUpdate) SetNillableVersion(i *int) *OperationUpdate {
	if i != nil {
		ou.SetVersion(*i)
	}
	return ou
}

// AddVersion adds i to the "version" field.
func (ou *OperationUpdate) AddVersion(i int) *OperationUpdate {
	ou.mutation.AddVersion(i)
	return ou
}

// SetUpdatedAt sets the "updated_at" field.
func (ou *OperationUpdate) SetUpdatedAt(t time.Time) *OperationUpdate {
	ou.mutation.SetUpdatedAt(t)
	return ou
}

// SetName sets the "name" field.
func (ou *OperationUpdate) SetName(s string) *OperationUpdate {
	ou.mutation.SetName(s)
	return ou
}

// SetNillableName sets the "name" field if the given value is not nil.
func (ou *OperationUpdate) SetNillableName(s *string) *OperationUpdate {
	if s != nil {
		ou.SetName(*s)
	}
	return ou
}

// SetDescription sets the "description" field.
func (ou *OperationUpdate) SetDescription(s string) *OperationUpdate {
	ou.mutation.SetDescription(s)
	return ou
}

// SetNillableDescription sets the "description" field if the given value is not nil.
func (ou *OperationUpdate) SetNillableDescription(s *string) *OperationUpdate {
	if s != nil {
		ou.SetDescription(*s)
	}
	return ou
}

// ClearDescription clears the value of the "description" field.
func (ou *OperationUpdate) ClearDescription() *OperationUpdate {
	ou.mutation.ClearDescription()
	return ou
}

// SetDuration sets the "duration" field.
func (ou *OperationUpdate) SetDuration(i int) *OperationUpdate {
	ou.mutation.ResetDuration()
	ou.mutation.SetDuration(i)
	return ou
}

// SetNillableDuration sets the "duration" field if the given value is not nil.
func (ou *OperationUpdate) SetNillableDuration(i *int) *OperationUpdate {
	if i != nil {
		ou.SetDuration(*i)
	}
	return ou
}

// AddDuration adds i to the "duration" field.
func (ou *OperationUpdate) AddDuration(i int) *OperationUpdate {
	ou.mutation.AddDuration(i)
	return ou
}

// ClearDuration clears the value of the "duration" field.
func (ou *OperationUpdate) ClearDuration() *OperationUpdate {
	ou.mutation.ClearDuration()
	return ou
}

// SetGroup sets the "group" field.
func (ou *OperationUpdate) SetGroup(s []string) *OperationUpdate {
	ou.mutation.SetGroup(s)
	return ou
}

// AppendGroup appends s to the "group" field.
func (ou *OperationUpdate) AppendGroup(s []string) *OperationUpdate {
	ou.mutation.AppendGroup(s)
	return ou
}

// ClearGroup clears the value of the "group" field.
func (ou *OperationUpdate) ClearGroup() *OperationUpdate {
	ou.mutation.ClearGroup()
	return ou
}

// AddAssignedProductIDs adds the "assigned_product" edge to the Product entity by IDs.
func (ou *OperationUpdate) AddAssignedProductIDs(ids ...int) *OperationUpdate {
	ou.mutation.AddAssignedProductIDs(ids...)
	return ou
}

// AddAssignedProduct adds the "assigned_product" edges to the Product entity.
func (ou *OperationUpdate) AddAssignedProduct(p ...*Product) *OperationUpdate {
	ids := make([]int, len(p))
	for i := range p {
		ids[i] = p[i].ID
	}
	return ou.AddAssignedProductIDs(ids...)
}

// AddProductOperationIDs adds the "product_operation" edge to the ProductOperation entity by IDs.
func (ou *OperationUpdate) AddProductOperationIDs(ids ...int) *OperationUpdate {
	ou.mutation.AddProductOperationIDs(ids...)
	return ou
}

// AddProductOperation adds the "product_operation" edges to the ProductOperation entity.
func (ou *OperationUpdate) AddProductOperation(p ...*ProductOperation) *OperationUpdate {
	ids := make([]int, len(p))
	for i := range p {
		ids[i] = p[i].ID
	}
	return ou.AddProductOperationIDs(ids...)
}

// Mutation returns the OperationMutation object of the builder.
func (ou *OperationUpdate) Mutation() *OperationMutation {
	return ou.mutation
}

// ClearAssignedProduct clears all "assigned_product" edges to the Product entity.
func (ou *OperationUpdate) ClearAssignedProduct() *OperationUpdate {
	ou.mutation.ClearAssignedProduct()
	return ou
}

// RemoveAssignedProductIDs removes the "assigned_product" edge to Product entities by IDs.
func (ou *OperationUpdate) RemoveAssignedProductIDs(ids ...int) *OperationUpdate {
	ou.mutation.RemoveAssignedProductIDs(ids...)
	return ou
}

// RemoveAssignedProduct removes "assigned_product" edges to Product entities.
func (ou *OperationUpdate) RemoveAssignedProduct(p ...*Product) *OperationUpdate {
	ids := make([]int, len(p))
	for i := range p {
		ids[i] = p[i].ID
	}
	return ou.RemoveAssignedProductIDs(ids...)
}

// ClearProductOperation clears all "product_operation" edges to the ProductOperation entity.
func (ou *OperationUpdate) ClearProductOperation() *OperationUpdate {
	ou.mutation.ClearProductOperation()
	return ou
}

// RemoveProductOperationIDs removes the "product_operation" edge to ProductOperation entities by IDs.
func (ou *OperationUpdate) RemoveProductOperationIDs(ids ...int) *OperationUpdate {
	ou.mutation.RemoveProductOperationIDs(ids...)
	return ou
}

// RemoveProductOperation removes "product_operation" edges to ProductOperation entities.
func (ou *OperationUpdate) RemoveProductOperation(p ...*ProductOperation) *OperationUpdate {
	ids := make([]int, len(p))
	for i := range p {
		ids[i] = p[i].ID
	}
	return ou.RemoveProductOperationIDs(ids...)
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (ou *OperationUpdate) Save(ctx context.Context) (int, error) {
	if err := ou.defaults(); err != nil {
		return 0, err
	}
	return withHooks(ctx, ou.sqlSave, ou.mutation, ou.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (ou *OperationUpdate) SaveX(ctx context.Context) int {
	affected, err := ou.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (ou *OperationUpdate) Exec(ctx context.Context) error {
	_, err := ou.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (ou *OperationUpdate) ExecX(ctx context.Context) {
	if err := ou.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (ou *OperationUpdate) defaults() error {
	if _, ok := ou.mutation.UpdatedAt(); !ok {
		if operation.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized operation.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := operation.UpdateDefaultUpdatedAt()
		ou.mutation.SetUpdatedAt(v)
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (ou *OperationUpdate) Modify(modifiers ...func(u *sql.UpdateBuilder)) *OperationUpdate {
	ou.modifiers = append(ou.modifiers, modifiers...)
	return ou
}

func (ou *OperationUpdate) sqlSave(ctx context.Context) (n int, err error) {
	_spec := sqlgraph.NewUpdateSpec(operation.Table, operation.Columns, sqlgraph.NewFieldSpec(operation.FieldID, field.TypeInt))
	if ps := ou.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := ou.mutation.DeletedAt(); ok {
		_spec.SetField(operation.FieldDeletedAt, field.TypeTime, value)
	}
	if ou.mutation.DeletedAtCleared() {
		_spec.ClearField(operation.FieldDeletedAt, field.TypeTime)
	}
	if value, ok := ou.mutation.Status(); ok {
		_spec.SetField(operation.FieldStatus, field.TypeInt8, value)
	}
	if value, ok := ou.mutation.AddedStatus(); ok {
		_spec.AddField(operation.FieldStatus, field.TypeInt8, value)
	}
	if value, ok := ou.mutation.Version(); ok {
		_spec.SetField(operation.FieldVersion, field.TypeInt, value)
	}
	if value, ok := ou.mutation.AddedVersion(); ok {
		_spec.AddField(operation.FieldVersion, field.TypeInt, value)
	}
	if value, ok := ou.mutation.UpdatedAt(); ok {
		_spec.SetField(operation.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := ou.mutation.Name(); ok {
		_spec.SetField(operation.FieldName, field.TypeString, value)
	}
	if value, ok := ou.mutation.Description(); ok {
		_spec.SetField(operation.FieldDescription, field.TypeString, value)
	}
	if ou.mutation.DescriptionCleared() {
		_spec.ClearField(operation.FieldDescription, field.TypeString)
	}
	if value, ok := ou.mutation.Duration(); ok {
		_spec.SetField(operation.FieldDuration, field.TypeInt, value)
	}
	if value, ok := ou.mutation.AddedDuration(); ok {
		_spec.AddField(operation.FieldDuration, field.TypeInt, value)
	}
	if ou.mutation.DurationCleared() {
		_spec.ClearField(operation.FieldDuration, field.TypeInt)
	}
	if value, ok := ou.mutation.Group(); ok {
		_spec.SetField(operation.FieldGroup, field.TypeJSON, value)
	}
	if value, ok := ou.mutation.AppendedGroup(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, operation.FieldGroup, value)
		})
	}
	if ou.mutation.GroupCleared() {
		_spec.ClearField(operation.FieldGroup, field.TypeJSON)
	}
	if ou.mutation.AssignedProductCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: true,
			Table:   operation.AssignedProductTable,
			Columns: operation.AssignedProductPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(product.FieldID, field.TypeInt),
			},
		}
		createE := &ProductOperationCreate{config: ou.config, mutation: newProductOperationMutation(ou.config, OpCreate)}
		_ = createE.defaults()
		_, specE := createE.createSpec()
		edge.Target.Fields = specE.Fields
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := ou.mutation.RemovedAssignedProductIDs(); len(nodes) > 0 && !ou.mutation.AssignedProductCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: true,
			Table:   operation.AssignedProductTable,
			Columns: operation.AssignedProductPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(product.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		createE := &ProductOperationCreate{config: ou.config, mutation: newProductOperationMutation(ou.config, OpCreate)}
		_ = createE.defaults()
		_, specE := createE.createSpec()
		edge.Target.Fields = specE.Fields
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := ou.mutation.AssignedProductIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: true,
			Table:   operation.AssignedProductTable,
			Columns: operation.AssignedProductPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(product.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		createE := &ProductOperationCreate{config: ou.config, mutation: newProductOperationMutation(ou.config, OpCreate)}
		_ = createE.defaults()
		_, specE := createE.createSpec()
		edge.Target.Fields = specE.Fields
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if ou.mutation.ProductOperationCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   operation.ProductOperationTable,
			Columns: []string{operation.ProductOperationColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(productoperation.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := ou.mutation.RemovedProductOperationIDs(); len(nodes) > 0 && !ou.mutation.ProductOperationCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   operation.ProductOperationTable,
			Columns: []string{operation.ProductOperationColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(productoperation.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := ou.mutation.ProductOperationIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   operation.ProductOperationTable,
			Columns: []string{operation.ProductOperationColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(productoperation.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_spec.AddModifiers(ou.modifiers...)
	if n, err = sqlgraph.UpdateNodes(ctx, ou.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{operation.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	ou.mutation.done = true
	return n, nil
}

// OperationUpdateOne is the builder for updating a single Operation entity.
type OperationUpdateOne struct {
	config
	fields    []string
	hooks     []Hook
	mutation  *OperationMutation
	modifiers []func(*sql.UpdateBuilder)
}

// SetDeletedAt sets the "deleted_at" field.
func (ouo *OperationUpdateOne) SetDeletedAt(t time.Time) *OperationUpdateOne {
	ouo.mutation.SetDeletedAt(t)
	return ouo
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (ouo *OperationUpdateOne) SetNillableDeletedAt(t *time.Time) *OperationUpdateOne {
	if t != nil {
		ouo.SetDeletedAt(*t)
	}
	return ouo
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (ouo *OperationUpdateOne) ClearDeletedAt() *OperationUpdateOne {
	ouo.mutation.ClearDeletedAt()
	return ouo
}

// SetStatus sets the "status" field.
func (ouo *OperationUpdateOne) SetStatus(i int8) *OperationUpdateOne {
	ouo.mutation.ResetStatus()
	ouo.mutation.SetStatus(i)
	return ouo
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (ouo *OperationUpdateOne) SetNillableStatus(i *int8) *OperationUpdateOne {
	if i != nil {
		ouo.SetStatus(*i)
	}
	return ouo
}

// AddStatus adds i to the "status" field.
func (ouo *OperationUpdateOne) AddStatus(i int8) *OperationUpdateOne {
	ouo.mutation.AddStatus(i)
	return ouo
}

// SetVersion sets the "version" field.
func (ouo *OperationUpdateOne) SetVersion(i int) *OperationUpdateOne {
	ouo.mutation.ResetVersion()
	ouo.mutation.SetVersion(i)
	return ouo
}

// SetNillableVersion sets the "version" field if the given value is not nil.
func (ouo *OperationUpdateOne) SetNillableVersion(i *int) *OperationUpdateOne {
	if i != nil {
		ouo.SetVersion(*i)
	}
	return ouo
}

// AddVersion adds i to the "version" field.
func (ouo *OperationUpdateOne) AddVersion(i int) *OperationUpdateOne {
	ouo.mutation.AddVersion(i)
	return ouo
}

// SetUpdatedAt sets the "updated_at" field.
func (ouo *OperationUpdateOne) SetUpdatedAt(t time.Time) *OperationUpdateOne {
	ouo.mutation.SetUpdatedAt(t)
	return ouo
}

// SetName sets the "name" field.
func (ouo *OperationUpdateOne) SetName(s string) *OperationUpdateOne {
	ouo.mutation.SetName(s)
	return ouo
}

// SetNillableName sets the "name" field if the given value is not nil.
func (ouo *OperationUpdateOne) SetNillableName(s *string) *OperationUpdateOne {
	if s != nil {
		ouo.SetName(*s)
	}
	return ouo
}

// SetDescription sets the "description" field.
func (ouo *OperationUpdateOne) SetDescription(s string) *OperationUpdateOne {
	ouo.mutation.SetDescription(s)
	return ouo
}

// SetNillableDescription sets the "description" field if the given value is not nil.
func (ouo *OperationUpdateOne) SetNillableDescription(s *string) *OperationUpdateOne {
	if s != nil {
		ouo.SetDescription(*s)
	}
	return ouo
}

// ClearDescription clears the value of the "description" field.
func (ouo *OperationUpdateOne) ClearDescription() *OperationUpdateOne {
	ouo.mutation.ClearDescription()
	return ouo
}

// SetDuration sets the "duration" field.
func (ouo *OperationUpdateOne) SetDuration(i int) *OperationUpdateOne {
	ouo.mutation.ResetDuration()
	ouo.mutation.SetDuration(i)
	return ouo
}

// SetNillableDuration sets the "duration" field if the given value is not nil.
func (ouo *OperationUpdateOne) SetNillableDuration(i *int) *OperationUpdateOne {
	if i != nil {
		ouo.SetDuration(*i)
	}
	return ouo
}

// AddDuration adds i to the "duration" field.
func (ouo *OperationUpdateOne) AddDuration(i int) *OperationUpdateOne {
	ouo.mutation.AddDuration(i)
	return ouo
}

// ClearDuration clears the value of the "duration" field.
func (ouo *OperationUpdateOne) ClearDuration() *OperationUpdateOne {
	ouo.mutation.ClearDuration()
	return ouo
}

// SetGroup sets the "group" field.
func (ouo *OperationUpdateOne) SetGroup(s []string) *OperationUpdateOne {
	ouo.mutation.SetGroup(s)
	return ouo
}

// AppendGroup appends s to the "group" field.
func (ouo *OperationUpdateOne) AppendGroup(s []string) *OperationUpdateOne {
	ouo.mutation.AppendGroup(s)
	return ouo
}

// ClearGroup clears the value of the "group" field.
func (ouo *OperationUpdateOne) ClearGroup() *OperationUpdateOne {
	ouo.mutation.ClearGroup()
	return ouo
}

// AddAssignedProductIDs adds the "assigned_product" edge to the Product entity by IDs.
func (ouo *OperationUpdateOne) AddAssignedProductIDs(ids ...int) *OperationUpdateOne {
	ouo.mutation.AddAssignedProductIDs(ids...)
	return ouo
}

// AddAssignedProduct adds the "assigned_product" edges to the Product entity.
func (ouo *OperationUpdateOne) AddAssignedProduct(p ...*Product) *OperationUpdateOne {
	ids := make([]int, len(p))
	for i := range p {
		ids[i] = p[i].ID
	}
	return ouo.AddAssignedProductIDs(ids...)
}

// AddProductOperationIDs adds the "product_operation" edge to the ProductOperation entity by IDs.
func (ouo *OperationUpdateOne) AddProductOperationIDs(ids ...int) *OperationUpdateOne {
	ouo.mutation.AddProductOperationIDs(ids...)
	return ouo
}

// AddProductOperation adds the "product_operation" edges to the ProductOperation entity.
func (ouo *OperationUpdateOne) AddProductOperation(p ...*ProductOperation) *OperationUpdateOne {
	ids := make([]int, len(p))
	for i := range p {
		ids[i] = p[i].ID
	}
	return ouo.AddProductOperationIDs(ids...)
}

// Mutation returns the OperationMutation object of the builder.
func (ouo *OperationUpdateOne) Mutation() *OperationMutation {
	return ouo.mutation
}

// ClearAssignedProduct clears all "assigned_product" edges to the Product entity.
func (ouo *OperationUpdateOne) ClearAssignedProduct() *OperationUpdateOne {
	ouo.mutation.ClearAssignedProduct()
	return ouo
}

// RemoveAssignedProductIDs removes the "assigned_product" edge to Product entities by IDs.
func (ouo *OperationUpdateOne) RemoveAssignedProductIDs(ids ...int) *OperationUpdateOne {
	ouo.mutation.RemoveAssignedProductIDs(ids...)
	return ouo
}

// RemoveAssignedProduct removes "assigned_product" edges to Product entities.
func (ouo *OperationUpdateOne) RemoveAssignedProduct(p ...*Product) *OperationUpdateOne {
	ids := make([]int, len(p))
	for i := range p {
		ids[i] = p[i].ID
	}
	return ouo.RemoveAssignedProductIDs(ids...)
}

// ClearProductOperation clears all "product_operation" edges to the ProductOperation entity.
func (ouo *OperationUpdateOne) ClearProductOperation() *OperationUpdateOne {
	ouo.mutation.ClearProductOperation()
	return ouo
}

// RemoveProductOperationIDs removes the "product_operation" edge to ProductOperation entities by IDs.
func (ouo *OperationUpdateOne) RemoveProductOperationIDs(ids ...int) *OperationUpdateOne {
	ouo.mutation.RemoveProductOperationIDs(ids...)
	return ouo
}

// RemoveProductOperation removes "product_operation" edges to ProductOperation entities.
func (ouo *OperationUpdateOne) RemoveProductOperation(p ...*ProductOperation) *OperationUpdateOne {
	ids := make([]int, len(p))
	for i := range p {
		ids[i] = p[i].ID
	}
	return ouo.RemoveProductOperationIDs(ids...)
}

// Where appends a list predicates to the OperationUpdate builder.
func (ouo *OperationUpdateOne) Where(ps ...predicate.Operation) *OperationUpdateOne {
	ouo.mutation.Where(ps...)
	return ouo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (ouo *OperationUpdateOne) Select(field string, fields ...string) *OperationUpdateOne {
	ouo.fields = append([]string{field}, fields...)
	return ouo
}

// Save executes the query and returns the updated Operation entity.
func (ouo *OperationUpdateOne) Save(ctx context.Context) (*Operation, error) {
	if err := ouo.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, ouo.sqlSave, ouo.mutation, ouo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (ouo *OperationUpdateOne) SaveX(ctx context.Context) *Operation {
	node, err := ouo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (ouo *OperationUpdateOne) Exec(ctx context.Context) error {
	_, err := ouo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (ouo *OperationUpdateOne) ExecX(ctx context.Context) {
	if err := ouo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (ouo *OperationUpdateOne) defaults() error {
	if _, ok := ouo.mutation.UpdatedAt(); !ok {
		if operation.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized operation.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := operation.UpdateDefaultUpdatedAt()
		ouo.mutation.SetUpdatedAt(v)
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (ouo *OperationUpdateOne) Modify(modifiers ...func(u *sql.UpdateBuilder)) *OperationUpdateOne {
	ouo.modifiers = append(ouo.modifiers, modifiers...)
	return ouo
}

func (ouo *OperationUpdateOne) sqlSave(ctx context.Context) (_node *Operation, err error) {
	_spec := sqlgraph.NewUpdateSpec(operation.Table, operation.Columns, sqlgraph.NewFieldSpec(operation.FieldID, field.TypeInt))
	id, ok := ouo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "Operation.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := ouo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, operation.FieldID)
		for _, f := range fields {
			if !operation.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != operation.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := ouo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := ouo.mutation.DeletedAt(); ok {
		_spec.SetField(operation.FieldDeletedAt, field.TypeTime, value)
	}
	if ouo.mutation.DeletedAtCleared() {
		_spec.ClearField(operation.FieldDeletedAt, field.TypeTime)
	}
	if value, ok := ouo.mutation.Status(); ok {
		_spec.SetField(operation.FieldStatus, field.TypeInt8, value)
	}
	if value, ok := ouo.mutation.AddedStatus(); ok {
		_spec.AddField(operation.FieldStatus, field.TypeInt8, value)
	}
	if value, ok := ouo.mutation.Version(); ok {
		_spec.SetField(operation.FieldVersion, field.TypeInt, value)
	}
	if value, ok := ouo.mutation.AddedVersion(); ok {
		_spec.AddField(operation.FieldVersion, field.TypeInt, value)
	}
	if value, ok := ouo.mutation.UpdatedAt(); ok {
		_spec.SetField(operation.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := ouo.mutation.Name(); ok {
		_spec.SetField(operation.FieldName, field.TypeString, value)
	}
	if value, ok := ouo.mutation.Description(); ok {
		_spec.SetField(operation.FieldDescription, field.TypeString, value)
	}
	if ouo.mutation.DescriptionCleared() {
		_spec.ClearField(operation.FieldDescription, field.TypeString)
	}
	if value, ok := ouo.mutation.Duration(); ok {
		_spec.SetField(operation.FieldDuration, field.TypeInt, value)
	}
	if value, ok := ouo.mutation.AddedDuration(); ok {
		_spec.AddField(operation.FieldDuration, field.TypeInt, value)
	}
	if ouo.mutation.DurationCleared() {
		_spec.ClearField(operation.FieldDuration, field.TypeInt)
	}
	if value, ok := ouo.mutation.Group(); ok {
		_spec.SetField(operation.FieldGroup, field.TypeJSON, value)
	}
	if value, ok := ouo.mutation.AppendedGroup(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, operation.FieldGroup, value)
		})
	}
	if ouo.mutation.GroupCleared() {
		_spec.ClearField(operation.FieldGroup, field.TypeJSON)
	}
	if ouo.mutation.AssignedProductCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: true,
			Table:   operation.AssignedProductTable,
			Columns: operation.AssignedProductPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(product.FieldID, field.TypeInt),
			},
		}
		createE := &ProductOperationCreate{config: ouo.config, mutation: newProductOperationMutation(ouo.config, OpCreate)}
		_ = createE.defaults()
		_, specE := createE.createSpec()
		edge.Target.Fields = specE.Fields
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := ouo.mutation.RemovedAssignedProductIDs(); len(nodes) > 0 && !ouo.mutation.AssignedProductCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: true,
			Table:   operation.AssignedProductTable,
			Columns: operation.AssignedProductPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(product.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		createE := &ProductOperationCreate{config: ouo.config, mutation: newProductOperationMutation(ouo.config, OpCreate)}
		_ = createE.defaults()
		_, specE := createE.createSpec()
		edge.Target.Fields = specE.Fields
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := ouo.mutation.AssignedProductIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: true,
			Table:   operation.AssignedProductTable,
			Columns: operation.AssignedProductPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(product.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		createE := &ProductOperationCreate{config: ouo.config, mutation: newProductOperationMutation(ouo.config, OpCreate)}
		_ = createE.defaults()
		_, specE := createE.createSpec()
		edge.Target.Fields = specE.Fields
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if ouo.mutation.ProductOperationCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   operation.ProductOperationTable,
			Columns: []string{operation.ProductOperationColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(productoperation.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := ouo.mutation.RemovedProductOperationIDs(); len(nodes) > 0 && !ouo.mutation.ProductOperationCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   operation.ProductOperationTable,
			Columns: []string{operation.ProductOperationColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(productoperation.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := ouo.mutation.ProductOperationIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   operation.ProductOperationTable,
			Columns: []string{operation.ProductOperationColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(productoperation.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_spec.AddModifiers(ouo.modifiers...)
	_node = &Operation{config: ouo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, ouo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{operation.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	ouo.mutation.done = true
	return _node, nil
}
