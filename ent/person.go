// Code generated by ent, DO NOT EDIT.

package ent

import (
	"bcare/ent/person"
	"bcare/ent/personreferral"
	"bcare/ent/term"
	"bcare/ent/types"
	"bcare/ent/user"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

// Person is the model entity for the Person schema.
type Person struct {
	config `json:"-"`
	// ID of the ent.
	ID int `json:"id,omitempty"`
	// DeletedAt holds the value of the "deleted_at" field.
	DeletedAt time.Time `json:"deleted_at,omitempty"`
	// Status holds the value of the "status" field.
	Status int8 `json:"status,omitempty"`
	// Version holds the value of the "version" field.
	Version int `json:"version,omitempty"`
	// CreatedAt holds the value of the "created_at" field.
	CreatedAt time.Time `json:"created_at,omitempty"`
	// UpdatedAt holds the value of the "updated_at" field.
	UpdatedAt time.Time `json:"updated_at,omitempty"`
	// FullName holds the value of the "full_name" field.
	FullName string `json:"full_name,omitempty"`
	// DateOfBirth holds the value of the "date_of_birth" field.
	DateOfBirth *time.Time `json:"date_of_birth,omitempty"`
	// Gender holds the value of the "gender" field.
	Gender string `json:"gender,omitempty"`
	// ProvinceID holds the value of the "province_id" field.
	ProvinceID int `json:"province_id,omitempty"`
	// DistrictID holds the value of the "district_id" field.
	DistrictID int `json:"district_id,omitempty"`
	// WardID holds the value of the "ward_id" field.
	WardID int `json:"ward_id,omitempty"`
	// AddressNumber holds the value of the "address_number" field.
	AddressNumber string `json:"address_number,omitempty"`
	// Phone holds the value of the "phone" field.
	Phone string `json:"phone,omitempty"`
	// Email holds the value of the "email" field.
	Email string `json:"email,omitempty"`
	// JobID holds the value of the "job_id" field.
	JobID *int `json:"job_id,omitempty"`
	// UserID holds the value of the "user_id" field.
	UserID int `json:"user_id,omitempty"`
	// SourceID holds the value of the "source_id" field.
	SourceID *int `json:"source_id,omitempty"`
	// PhoneConfirm holds the value of the "phone_confirm" field.
	PhoneConfirm bool `json:"phone_confirm,omitempty"`
	// MailConfirm holds the value of the "mail_confirm" field.
	MailConfirm bool `json:"mail_confirm,omitempty"`
	// PersonField holds the value of the "person_field" field.
	PersonField *types.PersonMeta `json:"person_field,omitempty"`
	// Edges holds the relations/edges for other nodes in the graph.
	// The values are being populated by the PersonQuery when eager-loading is set.
	Edges        PersonEdges `json:"-"`
	selectValues sql.SelectValues
}

// PersonEdges holds the relations/edges for other nodes in the graph.
type PersonEdges struct {
	// Deals holds the value of the deals edge.
	Deals []*Deal `json:"deals,omitempty"`
	// Tags holds the value of the tags edge.
	Tags []*Tag `json:"tags,omitempty"`
	// FormSubmissions holds the value of the form_submissions edge.
	FormSubmissions []*FormSubmission `json:"form_submissions,omitempty"`
	// Plans holds the value of the plans edge.
	Plans []*InstallmentPlan `json:"plans,omitempty"`
	// Calls holds the value of the calls edge.
	Calls []*Call `json:"calls,omitempty"`
	// Tasks holds the value of the tasks edge.
	Tasks []*Task `json:"tasks,omitempty"`
	// Issues holds the value of the issues edge.
	Issues []*Issue `json:"issues,omitempty"`
	// Appointments holds the value of the appointments edge.
	Appointments []*Appointment `json:"appointments,omitempty"`
	// Tracks holds the value of the tracks edge.
	Tracks []*Track `json:"tracks,omitempty"`
	// MessageHistories holds the value of the message_histories edge.
	MessageHistories []*MessageHistory `json:"message_histories,omitempty"`
	// Assignees holds the value of the assignees edge.
	Assignees []*User `json:"assignees,omitempty"`
	// Job holds the value of the job edge.
	Job *Term `json:"job,omitempty"`
	// Source holds the value of the source edge.
	Source *Term `json:"source,omitempty"`
	// Creator holds the value of the creator edge.
	Creator *User `json:"creator,omitempty"`
	// ReferredBy holds the value of the referred_by edge.
	ReferredBy *PersonReferral `json:"referred_by,omitempty"`
	// TagPerson holds the value of the tag_person edge.
	TagPerson []*TagPerson `json:"tag_person,omitempty"`
	// Assignment holds the value of the assignment edge.
	Assignment []*PersonAssignment `json:"assignment,omitempty"`
	// loadedTypes holds the information for reporting if a
	// type was loaded (or requested) in eager-loading or not.
	loadedTypes [17]bool
}

// DealsOrErr returns the Deals value or an error if the edge
// was not loaded in eager-loading.
func (e PersonEdges) DealsOrErr() ([]*Deal, error) {
	if e.loadedTypes[0] {
		return e.Deals, nil
	}
	return nil, &NotLoadedError{edge: "deals"}
}

// TagsOrErr returns the Tags value or an error if the edge
// was not loaded in eager-loading.
func (e PersonEdges) TagsOrErr() ([]*Tag, error) {
	if e.loadedTypes[1] {
		return e.Tags, nil
	}
	return nil, &NotLoadedError{edge: "tags"}
}

// FormSubmissionsOrErr returns the FormSubmissions value or an error if the edge
// was not loaded in eager-loading.
func (e PersonEdges) FormSubmissionsOrErr() ([]*FormSubmission, error) {
	if e.loadedTypes[2] {
		return e.FormSubmissions, nil
	}
	return nil, &NotLoadedError{edge: "form_submissions"}
}

// PlansOrErr returns the Plans value or an error if the edge
// was not loaded in eager-loading.
func (e PersonEdges) PlansOrErr() ([]*InstallmentPlan, error) {
	if e.loadedTypes[3] {
		return e.Plans, nil
	}
	return nil, &NotLoadedError{edge: "plans"}
}

// CallsOrErr returns the Calls value or an error if the edge
// was not loaded in eager-loading.
func (e PersonEdges) CallsOrErr() ([]*Call, error) {
	if e.loadedTypes[4] {
		return e.Calls, nil
	}
	return nil, &NotLoadedError{edge: "calls"}
}

// TasksOrErr returns the Tasks value or an error if the edge
// was not loaded in eager-loading.
func (e PersonEdges) TasksOrErr() ([]*Task, error) {
	if e.loadedTypes[5] {
		return e.Tasks, nil
	}
	return nil, &NotLoadedError{edge: "tasks"}
}

// IssuesOrErr returns the Issues value or an error if the edge
// was not loaded in eager-loading.
func (e PersonEdges) IssuesOrErr() ([]*Issue, error) {
	if e.loadedTypes[6] {
		return e.Issues, nil
	}
	return nil, &NotLoadedError{edge: "issues"}
}

// AppointmentsOrErr returns the Appointments value or an error if the edge
// was not loaded in eager-loading.
func (e PersonEdges) AppointmentsOrErr() ([]*Appointment, error) {
	if e.loadedTypes[7] {
		return e.Appointments, nil
	}
	return nil, &NotLoadedError{edge: "appointments"}
}

// TracksOrErr returns the Tracks value or an error if the edge
// was not loaded in eager-loading.
func (e PersonEdges) TracksOrErr() ([]*Track, error) {
	if e.loadedTypes[8] {
		return e.Tracks, nil
	}
	return nil, &NotLoadedError{edge: "tracks"}
}

// MessageHistoriesOrErr returns the MessageHistories value or an error if the edge
// was not loaded in eager-loading.
func (e PersonEdges) MessageHistoriesOrErr() ([]*MessageHistory, error) {
	if e.loadedTypes[9] {
		return e.MessageHistories, nil
	}
	return nil, &NotLoadedError{edge: "message_histories"}
}

// AssigneesOrErr returns the Assignees value or an error if the edge
// was not loaded in eager-loading.
func (e PersonEdges) AssigneesOrErr() ([]*User, error) {
	if e.loadedTypes[10] {
		return e.Assignees, nil
	}
	return nil, &NotLoadedError{edge: "assignees"}
}

// JobOrErr returns the Job value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e PersonEdges) JobOrErr() (*Term, error) {
	if e.Job != nil {
		return e.Job, nil
	} else if e.loadedTypes[11] {
		return nil, &NotFoundError{label: term.Label}
	}
	return nil, &NotLoadedError{edge: "job"}
}

// SourceOrErr returns the Source value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e PersonEdges) SourceOrErr() (*Term, error) {
	if e.Source != nil {
		return e.Source, nil
	} else if e.loadedTypes[12] {
		return nil, &NotFoundError{label: term.Label}
	}
	return nil, &NotLoadedError{edge: "source"}
}

// CreatorOrErr returns the Creator value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e PersonEdges) CreatorOrErr() (*User, error) {
	if e.Creator != nil {
		return e.Creator, nil
	} else if e.loadedTypes[13] {
		return nil, &NotFoundError{label: user.Label}
	}
	return nil, &NotLoadedError{edge: "creator"}
}

// ReferredByOrErr returns the ReferredBy value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e PersonEdges) ReferredByOrErr() (*PersonReferral, error) {
	if e.ReferredBy != nil {
		return e.ReferredBy, nil
	} else if e.loadedTypes[14] {
		return nil, &NotFoundError{label: personreferral.Label}
	}
	return nil, &NotLoadedError{edge: "referred_by"}
}

// TagPersonOrErr returns the TagPerson value or an error if the edge
// was not loaded in eager-loading.
func (e PersonEdges) TagPersonOrErr() ([]*TagPerson, error) {
	if e.loadedTypes[15] {
		return e.TagPerson, nil
	}
	return nil, &NotLoadedError{edge: "tag_person"}
}

// AssignmentOrErr returns the Assignment value or an error if the edge
// was not loaded in eager-loading.
func (e PersonEdges) AssignmentOrErr() ([]*PersonAssignment, error) {
	if e.loadedTypes[16] {
		return e.Assignment, nil
	}
	return nil, &NotLoadedError{edge: "assignment"}
}

// scanValues returns the types for scanning values from sql.Rows.
func (*Person) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case person.FieldPersonField:
			values[i] = new([]byte)
		case person.FieldPhoneConfirm, person.FieldMailConfirm:
			values[i] = new(sql.NullBool)
		case person.FieldID, person.FieldStatus, person.FieldVersion, person.FieldProvinceID, person.FieldDistrictID, person.FieldWardID, person.FieldJobID, person.FieldUserID, person.FieldSourceID:
			values[i] = new(sql.NullInt64)
		case person.FieldFullName, person.FieldGender, person.FieldAddressNumber, person.FieldPhone, person.FieldEmail:
			values[i] = new(sql.NullString)
		case person.FieldDeletedAt, person.FieldCreatedAt, person.FieldUpdatedAt, person.FieldDateOfBirth:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the Person fields.
func (pe *Person) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case person.FieldID:
			value, ok := values[i].(*sql.NullInt64)
			if !ok {
				return fmt.Errorf("unexpected type %T for field id", value)
			}
			pe.ID = int(value.Int64)
		case person.FieldDeletedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field deleted_at", values[i])
			} else if value.Valid {
				pe.DeletedAt = value.Time
			}
		case person.FieldStatus:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field status", values[i])
			} else if value.Valid {
				pe.Status = int8(value.Int64)
			}
		case person.FieldVersion:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field version", values[i])
			} else if value.Valid {
				pe.Version = int(value.Int64)
			}
		case person.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				pe.CreatedAt = value.Time
			}
		case person.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				pe.UpdatedAt = value.Time
			}
		case person.FieldFullName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field full_name", values[i])
			} else if value.Valid {
				pe.FullName = value.String
			}
		case person.FieldDateOfBirth:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field date_of_birth", values[i])
			} else if value.Valid {
				pe.DateOfBirth = new(time.Time)
				*pe.DateOfBirth = value.Time
			}
		case person.FieldGender:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field gender", values[i])
			} else if value.Valid {
				pe.Gender = value.String
			}
		case person.FieldProvinceID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field province_id", values[i])
			} else if value.Valid {
				pe.ProvinceID = int(value.Int64)
			}
		case person.FieldDistrictID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field district_id", values[i])
			} else if value.Valid {
				pe.DistrictID = int(value.Int64)
			}
		case person.FieldWardID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field ward_id", values[i])
			} else if value.Valid {
				pe.WardID = int(value.Int64)
			}
		case person.FieldAddressNumber:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field address_number", values[i])
			} else if value.Valid {
				pe.AddressNumber = value.String
			}
		case person.FieldPhone:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field phone", values[i])
			} else if value.Valid {
				pe.Phone = value.String
			}
		case person.FieldEmail:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field email", values[i])
			} else if value.Valid {
				pe.Email = value.String
			}
		case person.FieldJobID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field job_id", values[i])
			} else if value.Valid {
				pe.JobID = new(int)
				*pe.JobID = int(value.Int64)
			}
		case person.FieldUserID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field user_id", values[i])
			} else if value.Valid {
				pe.UserID = int(value.Int64)
			}
		case person.FieldSourceID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field source_id", values[i])
			} else if value.Valid {
				pe.SourceID = new(int)
				*pe.SourceID = int(value.Int64)
			}
		case person.FieldPhoneConfirm:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field phone_confirm", values[i])
			} else if value.Valid {
				pe.PhoneConfirm = value.Bool
			}
		case person.FieldMailConfirm:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field mail_confirm", values[i])
			} else if value.Valid {
				pe.MailConfirm = value.Bool
			}
		case person.FieldPersonField:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field person_field", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &pe.PersonField); err != nil {
					return fmt.Errorf("unmarshal field person_field: %w", err)
				}
			}
		default:
			pe.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the Person.
// This includes values selected through modifiers, order, etc.
func (pe *Person) Value(name string) (ent.Value, error) {
	return pe.selectValues.Get(name)
}

// QueryDeals queries the "deals" edge of the Person entity.
func (pe *Person) QueryDeals() *DealQuery {
	return NewPersonClient(pe.config).QueryDeals(pe)
}

// QueryTags queries the "tags" edge of the Person entity.
func (pe *Person) QueryTags() *TagQuery {
	return NewPersonClient(pe.config).QueryTags(pe)
}

// QueryFormSubmissions queries the "form_submissions" edge of the Person entity.
func (pe *Person) QueryFormSubmissions() *FormSubmissionQuery {
	return NewPersonClient(pe.config).QueryFormSubmissions(pe)
}

// QueryPlans queries the "plans" edge of the Person entity.
func (pe *Person) QueryPlans() *InstallmentPlanQuery {
	return NewPersonClient(pe.config).QueryPlans(pe)
}

// QueryCalls queries the "calls" edge of the Person entity.
func (pe *Person) QueryCalls() *CallQuery {
	return NewPersonClient(pe.config).QueryCalls(pe)
}

// QueryTasks queries the "tasks" edge of the Person entity.
func (pe *Person) QueryTasks() *TaskQuery {
	return NewPersonClient(pe.config).QueryTasks(pe)
}

// QueryIssues queries the "issues" edge of the Person entity.
func (pe *Person) QueryIssues() *IssueQuery {
	return NewPersonClient(pe.config).QueryIssues(pe)
}

// QueryAppointments queries the "appointments" edge of the Person entity.
func (pe *Person) QueryAppointments() *AppointmentQuery {
	return NewPersonClient(pe.config).QueryAppointments(pe)
}

// QueryTracks queries the "tracks" edge of the Person entity.
func (pe *Person) QueryTracks() *TrackQuery {
	return NewPersonClient(pe.config).QueryTracks(pe)
}

// QueryMessageHistories queries the "message_histories" edge of the Person entity.
func (pe *Person) QueryMessageHistories() *MessageHistoryQuery {
	return NewPersonClient(pe.config).QueryMessageHistories(pe)
}

// QueryAssignees queries the "assignees" edge of the Person entity.
func (pe *Person) QueryAssignees() *UserQuery {
	return NewPersonClient(pe.config).QueryAssignees(pe)
}

// QueryJob queries the "job" edge of the Person entity.
func (pe *Person) QueryJob() *TermQuery {
	return NewPersonClient(pe.config).QueryJob(pe)
}

// QuerySource queries the "source" edge of the Person entity.
func (pe *Person) QuerySource() *TermQuery {
	return NewPersonClient(pe.config).QuerySource(pe)
}

// QueryCreator queries the "creator" edge of the Person entity.
func (pe *Person) QueryCreator() *UserQuery {
	return NewPersonClient(pe.config).QueryCreator(pe)
}

// QueryReferredBy queries the "referred_by" edge of the Person entity.
func (pe *Person) QueryReferredBy() *PersonReferralQuery {
	return NewPersonClient(pe.config).QueryReferredBy(pe)
}

// QueryTagPerson queries the "tag_person" edge of the Person entity.
func (pe *Person) QueryTagPerson() *TagPersonQuery {
	return NewPersonClient(pe.config).QueryTagPerson(pe)
}

// QueryAssignment queries the "assignment" edge of the Person entity.
func (pe *Person) QueryAssignment() *PersonAssignmentQuery {
	return NewPersonClient(pe.config).QueryAssignment(pe)
}

// Update returns a builder for updating this Person.
// Note that you need to call Person.Unwrap() before calling this method if this Person
// was returned from a transaction, and the transaction was committed or rolled back.
func (pe *Person) Update() *PersonUpdateOne {
	return NewPersonClient(pe.config).UpdateOne(pe)
}

// Unwrap unwraps the Person entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (pe *Person) Unwrap() *Person {
	_tx, ok := pe.config.driver.(*txDriver)
	if !ok {
		panic("ent: Person is not a transactional entity")
	}
	pe.config.driver = _tx.drv
	return pe
}

// String implements the fmt.Stringer.
func (pe *Person) String() string {
	var builder strings.Builder
	builder.WriteString("Person(")
	builder.WriteString(fmt.Sprintf("id=%v, ", pe.ID))
	builder.WriteString("deleted_at=")
	builder.WriteString(pe.DeletedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("status=")
	builder.WriteString(fmt.Sprintf("%v", pe.Status))
	builder.WriteString(", ")
	builder.WriteString("version=")
	builder.WriteString(fmt.Sprintf("%v", pe.Version))
	builder.WriteString(", ")
	builder.WriteString("created_at=")
	builder.WriteString(pe.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(pe.UpdatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("full_name=")
	builder.WriteString(pe.FullName)
	builder.WriteString(", ")
	if v := pe.DateOfBirth; v != nil {
		builder.WriteString("date_of_birth=")
		builder.WriteString(v.Format(time.ANSIC))
	}
	builder.WriteString(", ")
	builder.WriteString("gender=")
	builder.WriteString(pe.Gender)
	builder.WriteString(", ")
	builder.WriteString("province_id=")
	builder.WriteString(fmt.Sprintf("%v", pe.ProvinceID))
	builder.WriteString(", ")
	builder.WriteString("district_id=")
	builder.WriteString(fmt.Sprintf("%v", pe.DistrictID))
	builder.WriteString(", ")
	builder.WriteString("ward_id=")
	builder.WriteString(fmt.Sprintf("%v", pe.WardID))
	builder.WriteString(", ")
	builder.WriteString("address_number=")
	builder.WriteString(pe.AddressNumber)
	builder.WriteString(", ")
	builder.WriteString("phone=")
	builder.WriteString(pe.Phone)
	builder.WriteString(", ")
	builder.WriteString("email=")
	builder.WriteString(pe.Email)
	builder.WriteString(", ")
	if v := pe.JobID; v != nil {
		builder.WriteString("job_id=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteString(", ")
	builder.WriteString("user_id=")
	builder.WriteString(fmt.Sprintf("%v", pe.UserID))
	builder.WriteString(", ")
	if v := pe.SourceID; v != nil {
		builder.WriteString("source_id=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteString(", ")
	builder.WriteString("phone_confirm=")
	builder.WriteString(fmt.Sprintf("%v", pe.PhoneConfirm))
	builder.WriteString(", ")
	builder.WriteString("mail_confirm=")
	builder.WriteString(fmt.Sprintf("%v", pe.MailConfirm))
	builder.WriteString(", ")
	builder.WriteString("person_field=")
	builder.WriteString(fmt.Sprintf("%v", pe.PersonField))
	builder.WriteByte(')')
	return builder.String()
}

// MarshalJSON implements the json.Marshaler interface.
func (pe *Person) MarshalJSON() ([]byte, error) {
	type Alias Person
	return json.Marshal(&struct {
		*Alias
		PersonEdges
	}{
		Alias:       (*Alias)(pe),
		PersonEdges: pe.Edges,
	})
}

// MarshalSimpleTime
func (pe *Person) MarshalSimpleTime() ([]byte, error) {
	type Alias Person
	return json.Marshal(&struct {
		*Alias
		PersonEdges
		DeletedAt   string `json:"deleted_at,omitempty"`
		CreatedAt   string `json:"created_at,omitempty"`
		UpdatedAt   string `json:"updated_at,omitempty"`
		DateOfBirth string `json:"date_of_birth,omitempty"`
	}{
		Alias:       (*Alias)(pe),
		PersonEdges: pe.Edges,
		DeletedAt:   pe.DeletedAt.Format("15:04 02/01/2006"),
		CreatedAt:   pe.CreatedAt.Format("15:04 02/01/2006"),
		UpdatedAt:   pe.UpdatedAt.Format("15:04 02/01/2006"),
		DateOfBirth: func() string {
			if pe.DateOfBirth != nil {
				str := pe.DateOfBirth.Format("15:04 02/01/2006")
				return str
			}
			return ""
		}(),
	})
}

// Persons is a parsable slice of Person.
type Persons []*Person
