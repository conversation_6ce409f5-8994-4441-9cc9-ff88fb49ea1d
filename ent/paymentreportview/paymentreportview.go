// Code generated by ent, DO NOT EDIT.

package paymentreportview

import (
	"entgo.io/ent/dialect/sql"
)

const (
	// Label holds the string label denoting the paymentreportview type in the database.
	Label = "payment_report_view"
	// FieldCreatedAt holds the string denoting the created_at field in the database.
	FieldCreatedAt = "created_at"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldPersonCode holds the string denoting the person_code field in the database.
	FieldPersonCode = "person_code"
	// FieldProvince holds the string denoting the province field in the database.
	FieldProvince = "province"
	// FieldDistrict holds the string denoting the district field in the database.
	FieldDistrict = "district"
	// FieldWard holds the string denoting the ward field in the database.
	FieldWard = "ward"
	// FieldAddressNumber holds the string denoting the address_number field in the database.
	FieldAddressNumber = "address_number"
	// FieldFullName holds the string denoting the full_name field in the database.
	FieldFullName = "full_name"
	// FieldJobName holds the string denoting the job_name field in the database.
	FieldJobName = "job_name"
	// FieldBirthday holds the string denoting the birthday field in the database.
	FieldBirthday = "birthday"
	// FieldPerson holds the string denoting the person field in the database.
	FieldPerson = "person"
	// FieldPhone holds the string denoting the phone field in the database.
	FieldPhone = "phone"
	// FieldProductNames holds the string denoting the product_names field in the database.
	FieldProductNames = "product_names"
	// FieldGroupNames holds the string denoting the group_names field in the database.
	FieldGroupNames = "group_names"
	// FieldIncome holds the string denoting the income field in the database.
	FieldIncome = "income"
	// FieldExpense holds the string denoting the expense field in the database.
	FieldExpense = "expense"
	// FieldTotalAmount holds the string denoting the total_amount field in the database.
	FieldTotalAmount = "total_amount"
	// FieldCash holds the string denoting the cash field in the database.
	FieldCash = "cash"
	// FieldBank holds the string denoting the bank field in the database.
	FieldBank = "bank"
	// FieldMpos holds the string denoting the mpos field in the database.
	FieldMpos = "mpos"
	// FieldCreditCard holds the string denoting the credit_card field in the database.
	FieldCreditCard = "credit_card"
	// FieldMomo holds the string denoting the momo field in the database.
	FieldMomo = "momo"
	// FieldProductAmount holds the string denoting the product_amount field in the database.
	FieldProductAmount = "product_amount"
	// FieldGeneralServiceAmount holds the string denoting the general_service_amount field in the database.
	FieldGeneralServiceAmount = "general_service_amount"
	// FieldGxtnAmount holds the string denoting the gxtn_amount field in the database.
	FieldGxtnAmount = "gxtn_amount"
	// FieldVeneerAmount holds the string denoting the veneer_amount field in the database.
	FieldVeneerAmount = "veneer_amount"
	// FieldPhsAmount holds the string denoting the phs_amount field in the database.
	FieldPhsAmount = "phs_amount"
	// FieldOrthodonticAmount holds the string denoting the orthodontic_amount field in the database.
	FieldOrthodonticAmount = "orthodontic_amount"
	// FieldImplantAmount holds the string denoting the implant_amount field in the database.
	FieldImplantAmount = "implant_amount"
	// FieldOtherAmount holds the string denoting the other_amount field in the database.
	FieldOtherAmount = "other_amount"
	// FieldDoctorName holds the string denoting the doctor_name field in the database.
	FieldDoctorName = "doctor_name"
	// FieldCreatorName holds the string denoting the creator_name field in the database.
	FieldCreatorName = "creator_name"
	// FieldDoctor holds the string denoting the doctor field in the database.
	FieldDoctor = "doctor"
	// Table holds the table name of the paymentreportview in the database.
	Table = "payment_report_view"
)

// Columns holds all SQL columns for paymentreportview fields.
var Columns = []string{
	FieldCreatedAt,
	FieldID,
	FieldPersonCode,
	FieldProvince,
	FieldDistrict,
	FieldWard,
	FieldAddressNumber,
	FieldFullName,
	FieldJobName,
	FieldBirthday,
	FieldPerson,
	FieldPhone,
	FieldProductNames,
	FieldGroupNames,
	FieldIncome,
	FieldExpense,
	FieldTotalAmount,
	FieldCash,
	FieldBank,
	FieldMpos,
	FieldCreditCard,
	FieldMomo,
	FieldProductAmount,
	FieldGeneralServiceAmount,
	FieldGxtnAmount,
	FieldVeneerAmount,
	FieldPhsAmount,
	FieldOrthodonticAmount,
	FieldImplantAmount,
	FieldOtherAmount,
	FieldDoctorName,
	FieldCreatorName,
	FieldDoctor,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

// OrderOption defines the ordering options for the PaymentReportView queries.
type OrderOption func(*sql.Selector)

// ByCreatedAt orders the results by the created_at field.
func ByCreatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedAt, opts...).ToFunc()
}

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByPersonCode orders the results by the person_code field.
func ByPersonCode(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPersonCode, opts...).ToFunc()
}

// ByProvince orders the results by the province field.
func ByProvince(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldProvince, opts...).ToFunc()
}

// ByDistrict orders the results by the district field.
func ByDistrict(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDistrict, opts...).ToFunc()
}

// ByWard orders the results by the ward field.
func ByWard(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldWard, opts...).ToFunc()
}

// ByAddressNumber orders the results by the address_number field.
func ByAddressNumber(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldAddressNumber, opts...).ToFunc()
}

// ByFullName orders the results by the full_name field.
func ByFullName(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldFullName, opts...).ToFunc()
}

// ByJobName orders the results by the job_name field.
func ByJobName(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldJobName, opts...).ToFunc()
}

// ByBirthday orders the results by the birthday field.
func ByBirthday(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldBirthday, opts...).ToFunc()
}

// ByPhone orders the results by the phone field.
func ByPhone(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPhone, opts...).ToFunc()
}

// ByProductNames orders the results by the product_names field.
func ByProductNames(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldProductNames, opts...).ToFunc()
}

// ByGroupNames orders the results by the group_names field.
func ByGroupNames(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldGroupNames, opts...).ToFunc()
}

// ByIncome orders the results by the income field.
func ByIncome(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldIncome, opts...).ToFunc()
}

// ByExpense orders the results by the expense field.
func ByExpense(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldExpense, opts...).ToFunc()
}

// ByTotalAmount orders the results by the total_amount field.
func ByTotalAmount(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldTotalAmount, opts...).ToFunc()
}

// ByCash orders the results by the cash field.
func ByCash(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCash, opts...).ToFunc()
}

// ByBank orders the results by the bank field.
func ByBank(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldBank, opts...).ToFunc()
}

// ByMpos orders the results by the mpos field.
func ByMpos(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldMpos, opts...).ToFunc()
}

// ByCreditCard orders the results by the credit_card field.
func ByCreditCard(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreditCard, opts...).ToFunc()
}

// ByMomo orders the results by the momo field.
func ByMomo(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldMomo, opts...).ToFunc()
}

// ByProductAmount orders the results by the product_amount field.
func ByProductAmount(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldProductAmount, opts...).ToFunc()
}

// ByGeneralServiceAmount orders the results by the general_service_amount field.
func ByGeneralServiceAmount(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldGeneralServiceAmount, opts...).ToFunc()
}

// ByGxtnAmount orders the results by the gxtn_amount field.
func ByGxtnAmount(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldGxtnAmount, opts...).ToFunc()
}

// ByVeneerAmount orders the results by the veneer_amount field.
func ByVeneerAmount(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldVeneerAmount, opts...).ToFunc()
}

// ByPhsAmount orders the results by the phs_amount field.
func ByPhsAmount(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPhsAmount, opts...).ToFunc()
}

// ByOrthodonticAmount orders the results by the orthodontic_amount field.
func ByOrthodonticAmount(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldOrthodonticAmount, opts...).ToFunc()
}

// ByImplantAmount orders the results by the implant_amount field.
func ByImplantAmount(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldImplantAmount, opts...).ToFunc()
}

// ByOtherAmount orders the results by the other_amount field.
func ByOtherAmount(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldOtherAmount, opts...).ToFunc()
}

// ByDoctorName orders the results by the doctor_name field.
func ByDoctorName(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDoctorName, opts...).ToFunc()
}

// ByCreatorName orders the results by the creator_name field.
func ByCreatorName(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatorName, opts...).ToFunc()
}
