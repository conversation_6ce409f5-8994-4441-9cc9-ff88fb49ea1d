// Code generated by ent, DO NOT EDIT.

package paymentreportview

import (
	"bcare/ent/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
)

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldEQ(FieldCreatedAt, v))
}

// ID applies equality check predicate on the "id" field. It's identical to IDEQ.
func ID(v int) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldEQ(FieldID, v))
}

// PersonCode applies equality check predicate on the "person_code" field. It's identical to PersonCodeEQ.
func PersonCode(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldEQ(FieldPersonCode, v))
}

// Province applies equality check predicate on the "province" field. It's identical to ProvinceEQ.
func Province(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldEQ(FieldProvince, v))
}

// District applies equality check predicate on the "district" field. It's identical to DistrictEQ.
func District(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldEQ(FieldDistrict, v))
}

// Ward applies equality check predicate on the "ward" field. It's identical to WardEQ.
func Ward(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldEQ(FieldWard, v))
}

// AddressNumber applies equality check predicate on the "address_number" field. It's identical to AddressNumberEQ.
func AddressNumber(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldEQ(FieldAddressNumber, v))
}

// FullName applies equality check predicate on the "full_name" field. It's identical to FullNameEQ.
func FullName(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldEQ(FieldFullName, v))
}

// JobName applies equality check predicate on the "job_name" field. It's identical to JobNameEQ.
func JobName(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldEQ(FieldJobName, v))
}

// Birthday applies equality check predicate on the "birthday" field. It's identical to BirthdayEQ.
func Birthday(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldEQ(FieldBirthday, v))
}

// Phone applies equality check predicate on the "phone" field. It's identical to PhoneEQ.
func Phone(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldEQ(FieldPhone, v))
}

// ProductNames applies equality check predicate on the "product_names" field. It's identical to ProductNamesEQ.
func ProductNames(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldEQ(FieldProductNames, v))
}

// GroupNames applies equality check predicate on the "group_names" field. It's identical to GroupNamesEQ.
func GroupNames(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldEQ(FieldGroupNames, v))
}

// Income applies equality check predicate on the "income" field. It's identical to IncomeEQ.
func Income(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldEQ(FieldIncome, v))
}

// Expense applies equality check predicate on the "expense" field. It's identical to ExpenseEQ.
func Expense(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldEQ(FieldExpense, v))
}

// TotalAmount applies equality check predicate on the "total_amount" field. It's identical to TotalAmountEQ.
func TotalAmount(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldEQ(FieldTotalAmount, v))
}

// Cash applies equality check predicate on the "cash" field. It's identical to CashEQ.
func Cash(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldEQ(FieldCash, v))
}

// Bank applies equality check predicate on the "bank" field. It's identical to BankEQ.
func Bank(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldEQ(FieldBank, v))
}

// Mpos applies equality check predicate on the "mpos" field. It's identical to MposEQ.
func Mpos(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldEQ(FieldMpos, v))
}

// CreditCard applies equality check predicate on the "credit_card" field. It's identical to CreditCardEQ.
func CreditCard(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldEQ(FieldCreditCard, v))
}

// Momo applies equality check predicate on the "momo" field. It's identical to MomoEQ.
func Momo(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldEQ(FieldMomo, v))
}

// ProductAmount applies equality check predicate on the "product_amount" field. It's identical to ProductAmountEQ.
func ProductAmount(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldEQ(FieldProductAmount, v))
}

// GeneralServiceAmount applies equality check predicate on the "general_service_amount" field. It's identical to GeneralServiceAmountEQ.
func GeneralServiceAmount(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldEQ(FieldGeneralServiceAmount, v))
}

// GxtnAmount applies equality check predicate on the "gxtn_amount" field. It's identical to GxtnAmountEQ.
func GxtnAmount(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldEQ(FieldGxtnAmount, v))
}

// VeneerAmount applies equality check predicate on the "veneer_amount" field. It's identical to VeneerAmountEQ.
func VeneerAmount(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldEQ(FieldVeneerAmount, v))
}

// PhsAmount applies equality check predicate on the "phs_amount" field. It's identical to PhsAmountEQ.
func PhsAmount(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldEQ(FieldPhsAmount, v))
}

// OrthodonticAmount applies equality check predicate on the "orthodontic_amount" field. It's identical to OrthodonticAmountEQ.
func OrthodonticAmount(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldEQ(FieldOrthodonticAmount, v))
}

// ImplantAmount applies equality check predicate on the "implant_amount" field. It's identical to ImplantAmountEQ.
func ImplantAmount(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldEQ(FieldImplantAmount, v))
}

// OtherAmount applies equality check predicate on the "other_amount" field. It's identical to OtherAmountEQ.
func OtherAmount(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldEQ(FieldOtherAmount, v))
}

// DoctorName applies equality check predicate on the "doctor_name" field. It's identical to DoctorNameEQ.
func DoctorName(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldEQ(FieldDoctorName, v))
}

// CreatorName applies equality check predicate on the "creator_name" field. It's identical to CreatorNameEQ.
func CreatorName(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldEQ(FieldCreatorName, v))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldLTE(FieldCreatedAt, v))
}

// IDEQ applies the EQ predicate on the "id" field.
func IDEQ(v int) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldEQ(FieldID, v))
}

// IDNEQ applies the NEQ predicate on the "id" field.
func IDNEQ(v int) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldNEQ(FieldID, v))
}

// IDIn applies the In predicate on the "id" field.
func IDIn(vs ...int) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldIn(FieldID, vs...))
}

// IDNotIn applies the NotIn predicate on the "id" field.
func IDNotIn(vs ...int) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldNotIn(FieldID, vs...))
}

// IDGT applies the GT predicate on the "id" field.
func IDGT(v int) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldGT(FieldID, v))
}

// IDGTE applies the GTE predicate on the "id" field.
func IDGTE(v int) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldGTE(FieldID, v))
}

// IDLT applies the LT predicate on the "id" field.
func IDLT(v int) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldLT(FieldID, v))
}

// IDLTE applies the LTE predicate on the "id" field.
func IDLTE(v int) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldLTE(FieldID, v))
}

// PersonCodeEQ applies the EQ predicate on the "person_code" field.
func PersonCodeEQ(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldEQ(FieldPersonCode, v))
}

// PersonCodeNEQ applies the NEQ predicate on the "person_code" field.
func PersonCodeNEQ(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldNEQ(FieldPersonCode, v))
}

// PersonCodeIn applies the In predicate on the "person_code" field.
func PersonCodeIn(vs ...string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldIn(FieldPersonCode, vs...))
}

// PersonCodeNotIn applies the NotIn predicate on the "person_code" field.
func PersonCodeNotIn(vs ...string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldNotIn(FieldPersonCode, vs...))
}

// PersonCodeGT applies the GT predicate on the "person_code" field.
func PersonCodeGT(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldGT(FieldPersonCode, v))
}

// PersonCodeGTE applies the GTE predicate on the "person_code" field.
func PersonCodeGTE(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldGTE(FieldPersonCode, v))
}

// PersonCodeLT applies the LT predicate on the "person_code" field.
func PersonCodeLT(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldLT(FieldPersonCode, v))
}

// PersonCodeLTE applies the LTE predicate on the "person_code" field.
func PersonCodeLTE(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldLTE(FieldPersonCode, v))
}

// PersonCodeContains applies the Contains predicate on the "person_code" field.
func PersonCodeContains(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldContains(FieldPersonCode, v))
}

// PersonCodeHasPrefix applies the HasPrefix predicate on the "person_code" field.
func PersonCodeHasPrefix(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldHasPrefix(FieldPersonCode, v))
}

// PersonCodeHasSuffix applies the HasSuffix predicate on the "person_code" field.
func PersonCodeHasSuffix(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldHasSuffix(FieldPersonCode, v))
}

// PersonCodeEqualFold applies the EqualFold predicate on the "person_code" field.
func PersonCodeEqualFold(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldEqualFold(FieldPersonCode, v))
}

// PersonCodeContainsFold applies the ContainsFold predicate on the "person_code" field.
func PersonCodeContainsFold(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldContainsFold(FieldPersonCode, v))
}

// ProvinceEQ applies the EQ predicate on the "province" field.
func ProvinceEQ(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldEQ(FieldProvince, v))
}

// ProvinceNEQ applies the NEQ predicate on the "province" field.
func ProvinceNEQ(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldNEQ(FieldProvince, v))
}

// ProvinceIn applies the In predicate on the "province" field.
func ProvinceIn(vs ...string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldIn(FieldProvince, vs...))
}

// ProvinceNotIn applies the NotIn predicate on the "province" field.
func ProvinceNotIn(vs ...string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldNotIn(FieldProvince, vs...))
}

// ProvinceGT applies the GT predicate on the "province" field.
func ProvinceGT(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldGT(FieldProvince, v))
}

// ProvinceGTE applies the GTE predicate on the "province" field.
func ProvinceGTE(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldGTE(FieldProvince, v))
}

// ProvinceLT applies the LT predicate on the "province" field.
func ProvinceLT(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldLT(FieldProvince, v))
}

// ProvinceLTE applies the LTE predicate on the "province" field.
func ProvinceLTE(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldLTE(FieldProvince, v))
}

// ProvinceContains applies the Contains predicate on the "province" field.
func ProvinceContains(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldContains(FieldProvince, v))
}

// ProvinceHasPrefix applies the HasPrefix predicate on the "province" field.
func ProvinceHasPrefix(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldHasPrefix(FieldProvince, v))
}

// ProvinceHasSuffix applies the HasSuffix predicate on the "province" field.
func ProvinceHasSuffix(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldHasSuffix(FieldProvince, v))
}

// ProvinceEqualFold applies the EqualFold predicate on the "province" field.
func ProvinceEqualFold(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldEqualFold(FieldProvince, v))
}

// ProvinceContainsFold applies the ContainsFold predicate on the "province" field.
func ProvinceContainsFold(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldContainsFold(FieldProvince, v))
}

// DistrictEQ applies the EQ predicate on the "district" field.
func DistrictEQ(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldEQ(FieldDistrict, v))
}

// DistrictNEQ applies the NEQ predicate on the "district" field.
func DistrictNEQ(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldNEQ(FieldDistrict, v))
}

// DistrictIn applies the In predicate on the "district" field.
func DistrictIn(vs ...string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldIn(FieldDistrict, vs...))
}

// DistrictNotIn applies the NotIn predicate on the "district" field.
func DistrictNotIn(vs ...string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldNotIn(FieldDistrict, vs...))
}

// DistrictGT applies the GT predicate on the "district" field.
func DistrictGT(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldGT(FieldDistrict, v))
}

// DistrictGTE applies the GTE predicate on the "district" field.
func DistrictGTE(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldGTE(FieldDistrict, v))
}

// DistrictLT applies the LT predicate on the "district" field.
func DistrictLT(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldLT(FieldDistrict, v))
}

// DistrictLTE applies the LTE predicate on the "district" field.
func DistrictLTE(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldLTE(FieldDistrict, v))
}

// DistrictContains applies the Contains predicate on the "district" field.
func DistrictContains(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldContains(FieldDistrict, v))
}

// DistrictHasPrefix applies the HasPrefix predicate on the "district" field.
func DistrictHasPrefix(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldHasPrefix(FieldDistrict, v))
}

// DistrictHasSuffix applies the HasSuffix predicate on the "district" field.
func DistrictHasSuffix(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldHasSuffix(FieldDistrict, v))
}

// DistrictEqualFold applies the EqualFold predicate on the "district" field.
func DistrictEqualFold(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldEqualFold(FieldDistrict, v))
}

// DistrictContainsFold applies the ContainsFold predicate on the "district" field.
func DistrictContainsFold(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldContainsFold(FieldDistrict, v))
}

// WardEQ applies the EQ predicate on the "ward" field.
func WardEQ(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldEQ(FieldWard, v))
}

// WardNEQ applies the NEQ predicate on the "ward" field.
func WardNEQ(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldNEQ(FieldWard, v))
}

// WardIn applies the In predicate on the "ward" field.
func WardIn(vs ...string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldIn(FieldWard, vs...))
}

// WardNotIn applies the NotIn predicate on the "ward" field.
func WardNotIn(vs ...string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldNotIn(FieldWard, vs...))
}

// WardGT applies the GT predicate on the "ward" field.
func WardGT(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldGT(FieldWard, v))
}

// WardGTE applies the GTE predicate on the "ward" field.
func WardGTE(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldGTE(FieldWard, v))
}

// WardLT applies the LT predicate on the "ward" field.
func WardLT(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldLT(FieldWard, v))
}

// WardLTE applies the LTE predicate on the "ward" field.
func WardLTE(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldLTE(FieldWard, v))
}

// WardContains applies the Contains predicate on the "ward" field.
func WardContains(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldContains(FieldWard, v))
}

// WardHasPrefix applies the HasPrefix predicate on the "ward" field.
func WardHasPrefix(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldHasPrefix(FieldWard, v))
}

// WardHasSuffix applies the HasSuffix predicate on the "ward" field.
func WardHasSuffix(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldHasSuffix(FieldWard, v))
}

// WardEqualFold applies the EqualFold predicate on the "ward" field.
func WardEqualFold(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldEqualFold(FieldWard, v))
}

// WardContainsFold applies the ContainsFold predicate on the "ward" field.
func WardContainsFold(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldContainsFold(FieldWard, v))
}

// AddressNumberEQ applies the EQ predicate on the "address_number" field.
func AddressNumberEQ(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldEQ(FieldAddressNumber, v))
}

// AddressNumberNEQ applies the NEQ predicate on the "address_number" field.
func AddressNumberNEQ(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldNEQ(FieldAddressNumber, v))
}

// AddressNumberIn applies the In predicate on the "address_number" field.
func AddressNumberIn(vs ...string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldIn(FieldAddressNumber, vs...))
}

// AddressNumberNotIn applies the NotIn predicate on the "address_number" field.
func AddressNumberNotIn(vs ...string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldNotIn(FieldAddressNumber, vs...))
}

// AddressNumberGT applies the GT predicate on the "address_number" field.
func AddressNumberGT(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldGT(FieldAddressNumber, v))
}

// AddressNumberGTE applies the GTE predicate on the "address_number" field.
func AddressNumberGTE(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldGTE(FieldAddressNumber, v))
}

// AddressNumberLT applies the LT predicate on the "address_number" field.
func AddressNumberLT(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldLT(FieldAddressNumber, v))
}

// AddressNumberLTE applies the LTE predicate on the "address_number" field.
func AddressNumberLTE(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldLTE(FieldAddressNumber, v))
}

// AddressNumberContains applies the Contains predicate on the "address_number" field.
func AddressNumberContains(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldContains(FieldAddressNumber, v))
}

// AddressNumberHasPrefix applies the HasPrefix predicate on the "address_number" field.
func AddressNumberHasPrefix(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldHasPrefix(FieldAddressNumber, v))
}

// AddressNumberHasSuffix applies the HasSuffix predicate on the "address_number" field.
func AddressNumberHasSuffix(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldHasSuffix(FieldAddressNumber, v))
}

// AddressNumberEqualFold applies the EqualFold predicate on the "address_number" field.
func AddressNumberEqualFold(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldEqualFold(FieldAddressNumber, v))
}

// AddressNumberContainsFold applies the ContainsFold predicate on the "address_number" field.
func AddressNumberContainsFold(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldContainsFold(FieldAddressNumber, v))
}

// FullNameEQ applies the EQ predicate on the "full_name" field.
func FullNameEQ(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldEQ(FieldFullName, v))
}

// FullNameNEQ applies the NEQ predicate on the "full_name" field.
func FullNameNEQ(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldNEQ(FieldFullName, v))
}

// FullNameIn applies the In predicate on the "full_name" field.
func FullNameIn(vs ...string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldIn(FieldFullName, vs...))
}

// FullNameNotIn applies the NotIn predicate on the "full_name" field.
func FullNameNotIn(vs ...string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldNotIn(FieldFullName, vs...))
}

// FullNameGT applies the GT predicate on the "full_name" field.
func FullNameGT(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldGT(FieldFullName, v))
}

// FullNameGTE applies the GTE predicate on the "full_name" field.
func FullNameGTE(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldGTE(FieldFullName, v))
}

// FullNameLT applies the LT predicate on the "full_name" field.
func FullNameLT(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldLT(FieldFullName, v))
}

// FullNameLTE applies the LTE predicate on the "full_name" field.
func FullNameLTE(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldLTE(FieldFullName, v))
}

// FullNameContains applies the Contains predicate on the "full_name" field.
func FullNameContains(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldContains(FieldFullName, v))
}

// FullNameHasPrefix applies the HasPrefix predicate on the "full_name" field.
func FullNameHasPrefix(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldHasPrefix(FieldFullName, v))
}

// FullNameHasSuffix applies the HasSuffix predicate on the "full_name" field.
func FullNameHasSuffix(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldHasSuffix(FieldFullName, v))
}

// FullNameEqualFold applies the EqualFold predicate on the "full_name" field.
func FullNameEqualFold(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldEqualFold(FieldFullName, v))
}

// FullNameContainsFold applies the ContainsFold predicate on the "full_name" field.
func FullNameContainsFold(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldContainsFold(FieldFullName, v))
}

// JobNameEQ applies the EQ predicate on the "job_name" field.
func JobNameEQ(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldEQ(FieldJobName, v))
}

// JobNameNEQ applies the NEQ predicate on the "job_name" field.
func JobNameNEQ(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldNEQ(FieldJobName, v))
}

// JobNameIn applies the In predicate on the "job_name" field.
func JobNameIn(vs ...string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldIn(FieldJobName, vs...))
}

// JobNameNotIn applies the NotIn predicate on the "job_name" field.
func JobNameNotIn(vs ...string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldNotIn(FieldJobName, vs...))
}

// JobNameGT applies the GT predicate on the "job_name" field.
func JobNameGT(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldGT(FieldJobName, v))
}

// JobNameGTE applies the GTE predicate on the "job_name" field.
func JobNameGTE(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldGTE(FieldJobName, v))
}

// JobNameLT applies the LT predicate on the "job_name" field.
func JobNameLT(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldLT(FieldJobName, v))
}

// JobNameLTE applies the LTE predicate on the "job_name" field.
func JobNameLTE(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldLTE(FieldJobName, v))
}

// JobNameContains applies the Contains predicate on the "job_name" field.
func JobNameContains(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldContains(FieldJobName, v))
}

// JobNameHasPrefix applies the HasPrefix predicate on the "job_name" field.
func JobNameHasPrefix(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldHasPrefix(FieldJobName, v))
}

// JobNameHasSuffix applies the HasSuffix predicate on the "job_name" field.
func JobNameHasSuffix(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldHasSuffix(FieldJobName, v))
}

// JobNameEqualFold applies the EqualFold predicate on the "job_name" field.
func JobNameEqualFold(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldEqualFold(FieldJobName, v))
}

// JobNameContainsFold applies the ContainsFold predicate on the "job_name" field.
func JobNameContainsFold(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldContainsFold(FieldJobName, v))
}

// BirthdayEQ applies the EQ predicate on the "birthday" field.
func BirthdayEQ(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldEQ(FieldBirthday, v))
}

// BirthdayNEQ applies the NEQ predicate on the "birthday" field.
func BirthdayNEQ(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldNEQ(FieldBirthday, v))
}

// BirthdayIn applies the In predicate on the "birthday" field.
func BirthdayIn(vs ...string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldIn(FieldBirthday, vs...))
}

// BirthdayNotIn applies the NotIn predicate on the "birthday" field.
func BirthdayNotIn(vs ...string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldNotIn(FieldBirthday, vs...))
}

// BirthdayGT applies the GT predicate on the "birthday" field.
func BirthdayGT(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldGT(FieldBirthday, v))
}

// BirthdayGTE applies the GTE predicate on the "birthday" field.
func BirthdayGTE(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldGTE(FieldBirthday, v))
}

// BirthdayLT applies the LT predicate on the "birthday" field.
func BirthdayLT(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldLT(FieldBirthday, v))
}

// BirthdayLTE applies the LTE predicate on the "birthday" field.
func BirthdayLTE(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldLTE(FieldBirthday, v))
}

// BirthdayContains applies the Contains predicate on the "birthday" field.
func BirthdayContains(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldContains(FieldBirthday, v))
}

// BirthdayHasPrefix applies the HasPrefix predicate on the "birthday" field.
func BirthdayHasPrefix(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldHasPrefix(FieldBirthday, v))
}

// BirthdayHasSuffix applies the HasSuffix predicate on the "birthday" field.
func BirthdayHasSuffix(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldHasSuffix(FieldBirthday, v))
}

// BirthdayEqualFold applies the EqualFold predicate on the "birthday" field.
func BirthdayEqualFold(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldEqualFold(FieldBirthday, v))
}

// BirthdayContainsFold applies the ContainsFold predicate on the "birthday" field.
func BirthdayContainsFold(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldContainsFold(FieldBirthday, v))
}

// PersonIsNil applies the IsNil predicate on the "person" field.
func PersonIsNil() predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldIsNull(FieldPerson))
}

// PersonNotNil applies the NotNil predicate on the "person" field.
func PersonNotNil() predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldNotNull(FieldPerson))
}

// PhoneEQ applies the EQ predicate on the "phone" field.
func PhoneEQ(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldEQ(FieldPhone, v))
}

// PhoneNEQ applies the NEQ predicate on the "phone" field.
func PhoneNEQ(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldNEQ(FieldPhone, v))
}

// PhoneIn applies the In predicate on the "phone" field.
func PhoneIn(vs ...string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldIn(FieldPhone, vs...))
}

// PhoneNotIn applies the NotIn predicate on the "phone" field.
func PhoneNotIn(vs ...string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldNotIn(FieldPhone, vs...))
}

// PhoneGT applies the GT predicate on the "phone" field.
func PhoneGT(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldGT(FieldPhone, v))
}

// PhoneGTE applies the GTE predicate on the "phone" field.
func PhoneGTE(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldGTE(FieldPhone, v))
}

// PhoneLT applies the LT predicate on the "phone" field.
func PhoneLT(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldLT(FieldPhone, v))
}

// PhoneLTE applies the LTE predicate on the "phone" field.
func PhoneLTE(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldLTE(FieldPhone, v))
}

// PhoneContains applies the Contains predicate on the "phone" field.
func PhoneContains(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldContains(FieldPhone, v))
}

// PhoneHasPrefix applies the HasPrefix predicate on the "phone" field.
func PhoneHasPrefix(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldHasPrefix(FieldPhone, v))
}

// PhoneHasSuffix applies the HasSuffix predicate on the "phone" field.
func PhoneHasSuffix(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldHasSuffix(FieldPhone, v))
}

// PhoneEqualFold applies the EqualFold predicate on the "phone" field.
func PhoneEqualFold(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldEqualFold(FieldPhone, v))
}

// PhoneContainsFold applies the ContainsFold predicate on the "phone" field.
func PhoneContainsFold(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldContainsFold(FieldPhone, v))
}

// ProductNamesEQ applies the EQ predicate on the "product_names" field.
func ProductNamesEQ(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldEQ(FieldProductNames, v))
}

// ProductNamesNEQ applies the NEQ predicate on the "product_names" field.
func ProductNamesNEQ(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldNEQ(FieldProductNames, v))
}

// ProductNamesIn applies the In predicate on the "product_names" field.
func ProductNamesIn(vs ...string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldIn(FieldProductNames, vs...))
}

// ProductNamesNotIn applies the NotIn predicate on the "product_names" field.
func ProductNamesNotIn(vs ...string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldNotIn(FieldProductNames, vs...))
}

// ProductNamesGT applies the GT predicate on the "product_names" field.
func ProductNamesGT(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldGT(FieldProductNames, v))
}

// ProductNamesGTE applies the GTE predicate on the "product_names" field.
func ProductNamesGTE(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldGTE(FieldProductNames, v))
}

// ProductNamesLT applies the LT predicate on the "product_names" field.
func ProductNamesLT(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldLT(FieldProductNames, v))
}

// ProductNamesLTE applies the LTE predicate on the "product_names" field.
func ProductNamesLTE(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldLTE(FieldProductNames, v))
}

// ProductNamesContains applies the Contains predicate on the "product_names" field.
func ProductNamesContains(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldContains(FieldProductNames, v))
}

// ProductNamesHasPrefix applies the HasPrefix predicate on the "product_names" field.
func ProductNamesHasPrefix(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldHasPrefix(FieldProductNames, v))
}

// ProductNamesHasSuffix applies the HasSuffix predicate on the "product_names" field.
func ProductNamesHasSuffix(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldHasSuffix(FieldProductNames, v))
}

// ProductNamesIsNil applies the IsNil predicate on the "product_names" field.
func ProductNamesIsNil() predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldIsNull(FieldProductNames))
}

// ProductNamesNotNil applies the NotNil predicate on the "product_names" field.
func ProductNamesNotNil() predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldNotNull(FieldProductNames))
}

// ProductNamesEqualFold applies the EqualFold predicate on the "product_names" field.
func ProductNamesEqualFold(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldEqualFold(FieldProductNames, v))
}

// ProductNamesContainsFold applies the ContainsFold predicate on the "product_names" field.
func ProductNamesContainsFold(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldContainsFold(FieldProductNames, v))
}

// GroupNamesEQ applies the EQ predicate on the "group_names" field.
func GroupNamesEQ(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldEQ(FieldGroupNames, v))
}

// GroupNamesNEQ applies the NEQ predicate on the "group_names" field.
func GroupNamesNEQ(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldNEQ(FieldGroupNames, v))
}

// GroupNamesIn applies the In predicate on the "group_names" field.
func GroupNamesIn(vs ...string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldIn(FieldGroupNames, vs...))
}

// GroupNamesNotIn applies the NotIn predicate on the "group_names" field.
func GroupNamesNotIn(vs ...string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldNotIn(FieldGroupNames, vs...))
}

// GroupNamesGT applies the GT predicate on the "group_names" field.
func GroupNamesGT(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldGT(FieldGroupNames, v))
}

// GroupNamesGTE applies the GTE predicate on the "group_names" field.
func GroupNamesGTE(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldGTE(FieldGroupNames, v))
}

// GroupNamesLT applies the LT predicate on the "group_names" field.
func GroupNamesLT(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldLT(FieldGroupNames, v))
}

// GroupNamesLTE applies the LTE predicate on the "group_names" field.
func GroupNamesLTE(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldLTE(FieldGroupNames, v))
}

// GroupNamesContains applies the Contains predicate on the "group_names" field.
func GroupNamesContains(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldContains(FieldGroupNames, v))
}

// GroupNamesHasPrefix applies the HasPrefix predicate on the "group_names" field.
func GroupNamesHasPrefix(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldHasPrefix(FieldGroupNames, v))
}

// GroupNamesHasSuffix applies the HasSuffix predicate on the "group_names" field.
func GroupNamesHasSuffix(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldHasSuffix(FieldGroupNames, v))
}

// GroupNamesIsNil applies the IsNil predicate on the "group_names" field.
func GroupNamesIsNil() predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldIsNull(FieldGroupNames))
}

// GroupNamesNotNil applies the NotNil predicate on the "group_names" field.
func GroupNamesNotNil() predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldNotNull(FieldGroupNames))
}

// GroupNamesEqualFold applies the EqualFold predicate on the "group_names" field.
func GroupNamesEqualFold(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldEqualFold(FieldGroupNames, v))
}

// GroupNamesContainsFold applies the ContainsFold predicate on the "group_names" field.
func GroupNamesContainsFold(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldContainsFold(FieldGroupNames, v))
}

// IncomeEQ applies the EQ predicate on the "income" field.
func IncomeEQ(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldEQ(FieldIncome, v))
}

// IncomeNEQ applies the NEQ predicate on the "income" field.
func IncomeNEQ(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldNEQ(FieldIncome, v))
}

// IncomeIn applies the In predicate on the "income" field.
func IncomeIn(vs ...float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldIn(FieldIncome, vs...))
}

// IncomeNotIn applies the NotIn predicate on the "income" field.
func IncomeNotIn(vs ...float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldNotIn(FieldIncome, vs...))
}

// IncomeGT applies the GT predicate on the "income" field.
func IncomeGT(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldGT(FieldIncome, v))
}

// IncomeGTE applies the GTE predicate on the "income" field.
func IncomeGTE(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldGTE(FieldIncome, v))
}

// IncomeLT applies the LT predicate on the "income" field.
func IncomeLT(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldLT(FieldIncome, v))
}

// IncomeLTE applies the LTE predicate on the "income" field.
func IncomeLTE(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldLTE(FieldIncome, v))
}

// ExpenseEQ applies the EQ predicate on the "expense" field.
func ExpenseEQ(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldEQ(FieldExpense, v))
}

// ExpenseNEQ applies the NEQ predicate on the "expense" field.
func ExpenseNEQ(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldNEQ(FieldExpense, v))
}

// ExpenseIn applies the In predicate on the "expense" field.
func ExpenseIn(vs ...float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldIn(FieldExpense, vs...))
}

// ExpenseNotIn applies the NotIn predicate on the "expense" field.
func ExpenseNotIn(vs ...float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldNotIn(FieldExpense, vs...))
}

// ExpenseGT applies the GT predicate on the "expense" field.
func ExpenseGT(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldGT(FieldExpense, v))
}

// ExpenseGTE applies the GTE predicate on the "expense" field.
func ExpenseGTE(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldGTE(FieldExpense, v))
}

// ExpenseLT applies the LT predicate on the "expense" field.
func ExpenseLT(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldLT(FieldExpense, v))
}

// ExpenseLTE applies the LTE predicate on the "expense" field.
func ExpenseLTE(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldLTE(FieldExpense, v))
}

// TotalAmountEQ applies the EQ predicate on the "total_amount" field.
func TotalAmountEQ(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldEQ(FieldTotalAmount, v))
}

// TotalAmountNEQ applies the NEQ predicate on the "total_amount" field.
func TotalAmountNEQ(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldNEQ(FieldTotalAmount, v))
}

// TotalAmountIn applies the In predicate on the "total_amount" field.
func TotalAmountIn(vs ...float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldIn(FieldTotalAmount, vs...))
}

// TotalAmountNotIn applies the NotIn predicate on the "total_amount" field.
func TotalAmountNotIn(vs ...float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldNotIn(FieldTotalAmount, vs...))
}

// TotalAmountGT applies the GT predicate on the "total_amount" field.
func TotalAmountGT(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldGT(FieldTotalAmount, v))
}

// TotalAmountGTE applies the GTE predicate on the "total_amount" field.
func TotalAmountGTE(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldGTE(FieldTotalAmount, v))
}

// TotalAmountLT applies the LT predicate on the "total_amount" field.
func TotalAmountLT(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldLT(FieldTotalAmount, v))
}

// TotalAmountLTE applies the LTE predicate on the "total_amount" field.
func TotalAmountLTE(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldLTE(FieldTotalAmount, v))
}

// CashEQ applies the EQ predicate on the "cash" field.
func CashEQ(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldEQ(FieldCash, v))
}

// CashNEQ applies the NEQ predicate on the "cash" field.
func CashNEQ(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldNEQ(FieldCash, v))
}

// CashIn applies the In predicate on the "cash" field.
func CashIn(vs ...float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldIn(FieldCash, vs...))
}

// CashNotIn applies the NotIn predicate on the "cash" field.
func CashNotIn(vs ...float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldNotIn(FieldCash, vs...))
}

// CashGT applies the GT predicate on the "cash" field.
func CashGT(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldGT(FieldCash, v))
}

// CashGTE applies the GTE predicate on the "cash" field.
func CashGTE(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldGTE(FieldCash, v))
}

// CashLT applies the LT predicate on the "cash" field.
func CashLT(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldLT(FieldCash, v))
}

// CashLTE applies the LTE predicate on the "cash" field.
func CashLTE(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldLTE(FieldCash, v))
}

// CashIsNil applies the IsNil predicate on the "cash" field.
func CashIsNil() predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldIsNull(FieldCash))
}

// CashNotNil applies the NotNil predicate on the "cash" field.
func CashNotNil() predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldNotNull(FieldCash))
}

// BankEQ applies the EQ predicate on the "bank" field.
func BankEQ(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldEQ(FieldBank, v))
}

// BankNEQ applies the NEQ predicate on the "bank" field.
func BankNEQ(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldNEQ(FieldBank, v))
}

// BankIn applies the In predicate on the "bank" field.
func BankIn(vs ...float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldIn(FieldBank, vs...))
}

// BankNotIn applies the NotIn predicate on the "bank" field.
func BankNotIn(vs ...float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldNotIn(FieldBank, vs...))
}

// BankGT applies the GT predicate on the "bank" field.
func BankGT(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldGT(FieldBank, v))
}

// BankGTE applies the GTE predicate on the "bank" field.
func BankGTE(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldGTE(FieldBank, v))
}

// BankLT applies the LT predicate on the "bank" field.
func BankLT(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldLT(FieldBank, v))
}

// BankLTE applies the LTE predicate on the "bank" field.
func BankLTE(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldLTE(FieldBank, v))
}

// BankIsNil applies the IsNil predicate on the "bank" field.
func BankIsNil() predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldIsNull(FieldBank))
}

// BankNotNil applies the NotNil predicate on the "bank" field.
func BankNotNil() predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldNotNull(FieldBank))
}

// MposEQ applies the EQ predicate on the "mpos" field.
func MposEQ(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldEQ(FieldMpos, v))
}

// MposNEQ applies the NEQ predicate on the "mpos" field.
func MposNEQ(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldNEQ(FieldMpos, v))
}

// MposIn applies the In predicate on the "mpos" field.
func MposIn(vs ...float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldIn(FieldMpos, vs...))
}

// MposNotIn applies the NotIn predicate on the "mpos" field.
func MposNotIn(vs ...float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldNotIn(FieldMpos, vs...))
}

// MposGT applies the GT predicate on the "mpos" field.
func MposGT(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldGT(FieldMpos, v))
}

// MposGTE applies the GTE predicate on the "mpos" field.
func MposGTE(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldGTE(FieldMpos, v))
}

// MposLT applies the LT predicate on the "mpos" field.
func MposLT(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldLT(FieldMpos, v))
}

// MposLTE applies the LTE predicate on the "mpos" field.
func MposLTE(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldLTE(FieldMpos, v))
}

// MposIsNil applies the IsNil predicate on the "mpos" field.
func MposIsNil() predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldIsNull(FieldMpos))
}

// MposNotNil applies the NotNil predicate on the "mpos" field.
func MposNotNil() predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldNotNull(FieldMpos))
}

// CreditCardEQ applies the EQ predicate on the "credit_card" field.
func CreditCardEQ(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldEQ(FieldCreditCard, v))
}

// CreditCardNEQ applies the NEQ predicate on the "credit_card" field.
func CreditCardNEQ(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldNEQ(FieldCreditCard, v))
}

// CreditCardIn applies the In predicate on the "credit_card" field.
func CreditCardIn(vs ...float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldIn(FieldCreditCard, vs...))
}

// CreditCardNotIn applies the NotIn predicate on the "credit_card" field.
func CreditCardNotIn(vs ...float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldNotIn(FieldCreditCard, vs...))
}

// CreditCardGT applies the GT predicate on the "credit_card" field.
func CreditCardGT(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldGT(FieldCreditCard, v))
}

// CreditCardGTE applies the GTE predicate on the "credit_card" field.
func CreditCardGTE(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldGTE(FieldCreditCard, v))
}

// CreditCardLT applies the LT predicate on the "credit_card" field.
func CreditCardLT(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldLT(FieldCreditCard, v))
}

// CreditCardLTE applies the LTE predicate on the "credit_card" field.
func CreditCardLTE(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldLTE(FieldCreditCard, v))
}

// CreditCardIsNil applies the IsNil predicate on the "credit_card" field.
func CreditCardIsNil() predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldIsNull(FieldCreditCard))
}

// CreditCardNotNil applies the NotNil predicate on the "credit_card" field.
func CreditCardNotNil() predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldNotNull(FieldCreditCard))
}

// MomoEQ applies the EQ predicate on the "momo" field.
func MomoEQ(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldEQ(FieldMomo, v))
}

// MomoNEQ applies the NEQ predicate on the "momo" field.
func MomoNEQ(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldNEQ(FieldMomo, v))
}

// MomoIn applies the In predicate on the "momo" field.
func MomoIn(vs ...float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldIn(FieldMomo, vs...))
}

// MomoNotIn applies the NotIn predicate on the "momo" field.
func MomoNotIn(vs ...float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldNotIn(FieldMomo, vs...))
}

// MomoGT applies the GT predicate on the "momo" field.
func MomoGT(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldGT(FieldMomo, v))
}

// MomoGTE applies the GTE predicate on the "momo" field.
func MomoGTE(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldGTE(FieldMomo, v))
}

// MomoLT applies the LT predicate on the "momo" field.
func MomoLT(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldLT(FieldMomo, v))
}

// MomoLTE applies the LTE predicate on the "momo" field.
func MomoLTE(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldLTE(FieldMomo, v))
}

// MomoIsNil applies the IsNil predicate on the "momo" field.
func MomoIsNil() predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldIsNull(FieldMomo))
}

// MomoNotNil applies the NotNil predicate on the "momo" field.
func MomoNotNil() predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldNotNull(FieldMomo))
}

// ProductAmountEQ applies the EQ predicate on the "product_amount" field.
func ProductAmountEQ(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldEQ(FieldProductAmount, v))
}

// ProductAmountNEQ applies the NEQ predicate on the "product_amount" field.
func ProductAmountNEQ(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldNEQ(FieldProductAmount, v))
}

// ProductAmountIn applies the In predicate on the "product_amount" field.
func ProductAmountIn(vs ...float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldIn(FieldProductAmount, vs...))
}

// ProductAmountNotIn applies the NotIn predicate on the "product_amount" field.
func ProductAmountNotIn(vs ...float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldNotIn(FieldProductAmount, vs...))
}

// ProductAmountGT applies the GT predicate on the "product_amount" field.
func ProductAmountGT(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldGT(FieldProductAmount, v))
}

// ProductAmountGTE applies the GTE predicate on the "product_amount" field.
func ProductAmountGTE(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldGTE(FieldProductAmount, v))
}

// ProductAmountLT applies the LT predicate on the "product_amount" field.
func ProductAmountLT(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldLT(FieldProductAmount, v))
}

// ProductAmountLTE applies the LTE predicate on the "product_amount" field.
func ProductAmountLTE(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldLTE(FieldProductAmount, v))
}

// ProductAmountIsNil applies the IsNil predicate on the "product_amount" field.
func ProductAmountIsNil() predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldIsNull(FieldProductAmount))
}

// ProductAmountNotNil applies the NotNil predicate on the "product_amount" field.
func ProductAmountNotNil() predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldNotNull(FieldProductAmount))
}

// GeneralServiceAmountEQ applies the EQ predicate on the "general_service_amount" field.
func GeneralServiceAmountEQ(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldEQ(FieldGeneralServiceAmount, v))
}

// GeneralServiceAmountNEQ applies the NEQ predicate on the "general_service_amount" field.
func GeneralServiceAmountNEQ(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldNEQ(FieldGeneralServiceAmount, v))
}

// GeneralServiceAmountIn applies the In predicate on the "general_service_amount" field.
func GeneralServiceAmountIn(vs ...float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldIn(FieldGeneralServiceAmount, vs...))
}

// GeneralServiceAmountNotIn applies the NotIn predicate on the "general_service_amount" field.
func GeneralServiceAmountNotIn(vs ...float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldNotIn(FieldGeneralServiceAmount, vs...))
}

// GeneralServiceAmountGT applies the GT predicate on the "general_service_amount" field.
func GeneralServiceAmountGT(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldGT(FieldGeneralServiceAmount, v))
}

// GeneralServiceAmountGTE applies the GTE predicate on the "general_service_amount" field.
func GeneralServiceAmountGTE(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldGTE(FieldGeneralServiceAmount, v))
}

// GeneralServiceAmountLT applies the LT predicate on the "general_service_amount" field.
func GeneralServiceAmountLT(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldLT(FieldGeneralServiceAmount, v))
}

// GeneralServiceAmountLTE applies the LTE predicate on the "general_service_amount" field.
func GeneralServiceAmountLTE(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldLTE(FieldGeneralServiceAmount, v))
}

// GeneralServiceAmountIsNil applies the IsNil predicate on the "general_service_amount" field.
func GeneralServiceAmountIsNil() predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldIsNull(FieldGeneralServiceAmount))
}

// GeneralServiceAmountNotNil applies the NotNil predicate on the "general_service_amount" field.
func GeneralServiceAmountNotNil() predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldNotNull(FieldGeneralServiceAmount))
}

// GxtnAmountEQ applies the EQ predicate on the "gxtn_amount" field.
func GxtnAmountEQ(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldEQ(FieldGxtnAmount, v))
}

// GxtnAmountNEQ applies the NEQ predicate on the "gxtn_amount" field.
func GxtnAmountNEQ(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldNEQ(FieldGxtnAmount, v))
}

// GxtnAmountIn applies the In predicate on the "gxtn_amount" field.
func GxtnAmountIn(vs ...float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldIn(FieldGxtnAmount, vs...))
}

// GxtnAmountNotIn applies the NotIn predicate on the "gxtn_amount" field.
func GxtnAmountNotIn(vs ...float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldNotIn(FieldGxtnAmount, vs...))
}

// GxtnAmountGT applies the GT predicate on the "gxtn_amount" field.
func GxtnAmountGT(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldGT(FieldGxtnAmount, v))
}

// GxtnAmountGTE applies the GTE predicate on the "gxtn_amount" field.
func GxtnAmountGTE(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldGTE(FieldGxtnAmount, v))
}

// GxtnAmountLT applies the LT predicate on the "gxtn_amount" field.
func GxtnAmountLT(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldLT(FieldGxtnAmount, v))
}

// GxtnAmountLTE applies the LTE predicate on the "gxtn_amount" field.
func GxtnAmountLTE(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldLTE(FieldGxtnAmount, v))
}

// GxtnAmountIsNil applies the IsNil predicate on the "gxtn_amount" field.
func GxtnAmountIsNil() predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldIsNull(FieldGxtnAmount))
}

// GxtnAmountNotNil applies the NotNil predicate on the "gxtn_amount" field.
func GxtnAmountNotNil() predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldNotNull(FieldGxtnAmount))
}

// VeneerAmountEQ applies the EQ predicate on the "veneer_amount" field.
func VeneerAmountEQ(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldEQ(FieldVeneerAmount, v))
}

// VeneerAmountNEQ applies the NEQ predicate on the "veneer_amount" field.
func VeneerAmountNEQ(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldNEQ(FieldVeneerAmount, v))
}

// VeneerAmountIn applies the In predicate on the "veneer_amount" field.
func VeneerAmountIn(vs ...float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldIn(FieldVeneerAmount, vs...))
}

// VeneerAmountNotIn applies the NotIn predicate on the "veneer_amount" field.
func VeneerAmountNotIn(vs ...float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldNotIn(FieldVeneerAmount, vs...))
}

// VeneerAmountGT applies the GT predicate on the "veneer_amount" field.
func VeneerAmountGT(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldGT(FieldVeneerAmount, v))
}

// VeneerAmountGTE applies the GTE predicate on the "veneer_amount" field.
func VeneerAmountGTE(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldGTE(FieldVeneerAmount, v))
}

// VeneerAmountLT applies the LT predicate on the "veneer_amount" field.
func VeneerAmountLT(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldLT(FieldVeneerAmount, v))
}

// VeneerAmountLTE applies the LTE predicate on the "veneer_amount" field.
func VeneerAmountLTE(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldLTE(FieldVeneerAmount, v))
}

// VeneerAmountIsNil applies the IsNil predicate on the "veneer_amount" field.
func VeneerAmountIsNil() predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldIsNull(FieldVeneerAmount))
}

// VeneerAmountNotNil applies the NotNil predicate on the "veneer_amount" field.
func VeneerAmountNotNil() predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldNotNull(FieldVeneerAmount))
}

// PhsAmountEQ applies the EQ predicate on the "phs_amount" field.
func PhsAmountEQ(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldEQ(FieldPhsAmount, v))
}

// PhsAmountNEQ applies the NEQ predicate on the "phs_amount" field.
func PhsAmountNEQ(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldNEQ(FieldPhsAmount, v))
}

// PhsAmountIn applies the In predicate on the "phs_amount" field.
func PhsAmountIn(vs ...float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldIn(FieldPhsAmount, vs...))
}

// PhsAmountNotIn applies the NotIn predicate on the "phs_amount" field.
func PhsAmountNotIn(vs ...float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldNotIn(FieldPhsAmount, vs...))
}

// PhsAmountGT applies the GT predicate on the "phs_amount" field.
func PhsAmountGT(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldGT(FieldPhsAmount, v))
}

// PhsAmountGTE applies the GTE predicate on the "phs_amount" field.
func PhsAmountGTE(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldGTE(FieldPhsAmount, v))
}

// PhsAmountLT applies the LT predicate on the "phs_amount" field.
func PhsAmountLT(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldLT(FieldPhsAmount, v))
}

// PhsAmountLTE applies the LTE predicate on the "phs_amount" field.
func PhsAmountLTE(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldLTE(FieldPhsAmount, v))
}

// PhsAmountIsNil applies the IsNil predicate on the "phs_amount" field.
func PhsAmountIsNil() predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldIsNull(FieldPhsAmount))
}

// PhsAmountNotNil applies the NotNil predicate on the "phs_amount" field.
func PhsAmountNotNil() predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldNotNull(FieldPhsAmount))
}

// OrthodonticAmountEQ applies the EQ predicate on the "orthodontic_amount" field.
func OrthodonticAmountEQ(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldEQ(FieldOrthodonticAmount, v))
}

// OrthodonticAmountNEQ applies the NEQ predicate on the "orthodontic_amount" field.
func OrthodonticAmountNEQ(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldNEQ(FieldOrthodonticAmount, v))
}

// OrthodonticAmountIn applies the In predicate on the "orthodontic_amount" field.
func OrthodonticAmountIn(vs ...float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldIn(FieldOrthodonticAmount, vs...))
}

// OrthodonticAmountNotIn applies the NotIn predicate on the "orthodontic_amount" field.
func OrthodonticAmountNotIn(vs ...float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldNotIn(FieldOrthodonticAmount, vs...))
}

// OrthodonticAmountGT applies the GT predicate on the "orthodontic_amount" field.
func OrthodonticAmountGT(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldGT(FieldOrthodonticAmount, v))
}

// OrthodonticAmountGTE applies the GTE predicate on the "orthodontic_amount" field.
func OrthodonticAmountGTE(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldGTE(FieldOrthodonticAmount, v))
}

// OrthodonticAmountLT applies the LT predicate on the "orthodontic_amount" field.
func OrthodonticAmountLT(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldLT(FieldOrthodonticAmount, v))
}

// OrthodonticAmountLTE applies the LTE predicate on the "orthodontic_amount" field.
func OrthodonticAmountLTE(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldLTE(FieldOrthodonticAmount, v))
}

// OrthodonticAmountIsNil applies the IsNil predicate on the "orthodontic_amount" field.
func OrthodonticAmountIsNil() predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldIsNull(FieldOrthodonticAmount))
}

// OrthodonticAmountNotNil applies the NotNil predicate on the "orthodontic_amount" field.
func OrthodonticAmountNotNil() predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldNotNull(FieldOrthodonticAmount))
}

// ImplantAmountEQ applies the EQ predicate on the "implant_amount" field.
func ImplantAmountEQ(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldEQ(FieldImplantAmount, v))
}

// ImplantAmountNEQ applies the NEQ predicate on the "implant_amount" field.
func ImplantAmountNEQ(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldNEQ(FieldImplantAmount, v))
}

// ImplantAmountIn applies the In predicate on the "implant_amount" field.
func ImplantAmountIn(vs ...float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldIn(FieldImplantAmount, vs...))
}

// ImplantAmountNotIn applies the NotIn predicate on the "implant_amount" field.
func ImplantAmountNotIn(vs ...float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldNotIn(FieldImplantAmount, vs...))
}

// ImplantAmountGT applies the GT predicate on the "implant_amount" field.
func ImplantAmountGT(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldGT(FieldImplantAmount, v))
}

// ImplantAmountGTE applies the GTE predicate on the "implant_amount" field.
func ImplantAmountGTE(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldGTE(FieldImplantAmount, v))
}

// ImplantAmountLT applies the LT predicate on the "implant_amount" field.
func ImplantAmountLT(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldLT(FieldImplantAmount, v))
}

// ImplantAmountLTE applies the LTE predicate on the "implant_amount" field.
func ImplantAmountLTE(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldLTE(FieldImplantAmount, v))
}

// ImplantAmountIsNil applies the IsNil predicate on the "implant_amount" field.
func ImplantAmountIsNil() predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldIsNull(FieldImplantAmount))
}

// ImplantAmountNotNil applies the NotNil predicate on the "implant_amount" field.
func ImplantAmountNotNil() predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldNotNull(FieldImplantAmount))
}

// OtherAmountEQ applies the EQ predicate on the "other_amount" field.
func OtherAmountEQ(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldEQ(FieldOtherAmount, v))
}

// OtherAmountNEQ applies the NEQ predicate on the "other_amount" field.
func OtherAmountNEQ(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldNEQ(FieldOtherAmount, v))
}

// OtherAmountIn applies the In predicate on the "other_amount" field.
func OtherAmountIn(vs ...float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldIn(FieldOtherAmount, vs...))
}

// OtherAmountNotIn applies the NotIn predicate on the "other_amount" field.
func OtherAmountNotIn(vs ...float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldNotIn(FieldOtherAmount, vs...))
}

// OtherAmountGT applies the GT predicate on the "other_amount" field.
func OtherAmountGT(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldGT(FieldOtherAmount, v))
}

// OtherAmountGTE applies the GTE predicate on the "other_amount" field.
func OtherAmountGTE(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldGTE(FieldOtherAmount, v))
}

// OtherAmountLT applies the LT predicate on the "other_amount" field.
func OtherAmountLT(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldLT(FieldOtherAmount, v))
}

// OtherAmountLTE applies the LTE predicate on the "other_amount" field.
func OtherAmountLTE(v float64) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldLTE(FieldOtherAmount, v))
}

// OtherAmountIsNil applies the IsNil predicate on the "other_amount" field.
func OtherAmountIsNil() predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldIsNull(FieldOtherAmount))
}

// OtherAmountNotNil applies the NotNil predicate on the "other_amount" field.
func OtherAmountNotNil() predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldNotNull(FieldOtherAmount))
}

// DoctorNameEQ applies the EQ predicate on the "doctor_name" field.
func DoctorNameEQ(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldEQ(FieldDoctorName, v))
}

// DoctorNameNEQ applies the NEQ predicate on the "doctor_name" field.
func DoctorNameNEQ(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldNEQ(FieldDoctorName, v))
}

// DoctorNameIn applies the In predicate on the "doctor_name" field.
func DoctorNameIn(vs ...string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldIn(FieldDoctorName, vs...))
}

// DoctorNameNotIn applies the NotIn predicate on the "doctor_name" field.
func DoctorNameNotIn(vs ...string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldNotIn(FieldDoctorName, vs...))
}

// DoctorNameGT applies the GT predicate on the "doctor_name" field.
func DoctorNameGT(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldGT(FieldDoctorName, v))
}

// DoctorNameGTE applies the GTE predicate on the "doctor_name" field.
func DoctorNameGTE(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldGTE(FieldDoctorName, v))
}

// DoctorNameLT applies the LT predicate on the "doctor_name" field.
func DoctorNameLT(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldLT(FieldDoctorName, v))
}

// DoctorNameLTE applies the LTE predicate on the "doctor_name" field.
func DoctorNameLTE(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldLTE(FieldDoctorName, v))
}

// DoctorNameContains applies the Contains predicate on the "doctor_name" field.
func DoctorNameContains(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldContains(FieldDoctorName, v))
}

// DoctorNameHasPrefix applies the HasPrefix predicate on the "doctor_name" field.
func DoctorNameHasPrefix(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldHasPrefix(FieldDoctorName, v))
}

// DoctorNameHasSuffix applies the HasSuffix predicate on the "doctor_name" field.
func DoctorNameHasSuffix(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldHasSuffix(FieldDoctorName, v))
}

// DoctorNameIsNil applies the IsNil predicate on the "doctor_name" field.
func DoctorNameIsNil() predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldIsNull(FieldDoctorName))
}

// DoctorNameNotNil applies the NotNil predicate on the "doctor_name" field.
func DoctorNameNotNil() predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldNotNull(FieldDoctorName))
}

// DoctorNameEqualFold applies the EqualFold predicate on the "doctor_name" field.
func DoctorNameEqualFold(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldEqualFold(FieldDoctorName, v))
}

// DoctorNameContainsFold applies the ContainsFold predicate on the "doctor_name" field.
func DoctorNameContainsFold(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldContainsFold(FieldDoctorName, v))
}

// CreatorNameEQ applies the EQ predicate on the "creator_name" field.
func CreatorNameEQ(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldEQ(FieldCreatorName, v))
}

// CreatorNameNEQ applies the NEQ predicate on the "creator_name" field.
func CreatorNameNEQ(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldNEQ(FieldCreatorName, v))
}

// CreatorNameIn applies the In predicate on the "creator_name" field.
func CreatorNameIn(vs ...string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldIn(FieldCreatorName, vs...))
}

// CreatorNameNotIn applies the NotIn predicate on the "creator_name" field.
func CreatorNameNotIn(vs ...string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldNotIn(FieldCreatorName, vs...))
}

// CreatorNameGT applies the GT predicate on the "creator_name" field.
func CreatorNameGT(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldGT(FieldCreatorName, v))
}

// CreatorNameGTE applies the GTE predicate on the "creator_name" field.
func CreatorNameGTE(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldGTE(FieldCreatorName, v))
}

// CreatorNameLT applies the LT predicate on the "creator_name" field.
func CreatorNameLT(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldLT(FieldCreatorName, v))
}

// CreatorNameLTE applies the LTE predicate on the "creator_name" field.
func CreatorNameLTE(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldLTE(FieldCreatorName, v))
}

// CreatorNameContains applies the Contains predicate on the "creator_name" field.
func CreatorNameContains(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldContains(FieldCreatorName, v))
}

// CreatorNameHasPrefix applies the HasPrefix predicate on the "creator_name" field.
func CreatorNameHasPrefix(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldHasPrefix(FieldCreatorName, v))
}

// CreatorNameHasSuffix applies the HasSuffix predicate on the "creator_name" field.
func CreatorNameHasSuffix(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldHasSuffix(FieldCreatorName, v))
}

// CreatorNameIsNil applies the IsNil predicate on the "creator_name" field.
func CreatorNameIsNil() predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldIsNull(FieldCreatorName))
}

// CreatorNameNotNil applies the NotNil predicate on the "creator_name" field.
func CreatorNameNotNil() predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldNotNull(FieldCreatorName))
}

// CreatorNameEqualFold applies the EqualFold predicate on the "creator_name" field.
func CreatorNameEqualFold(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldEqualFold(FieldCreatorName, v))
}

// CreatorNameContainsFold applies the ContainsFold predicate on the "creator_name" field.
func CreatorNameContainsFold(v string) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldContainsFold(FieldCreatorName, v))
}

// DoctorIsNil applies the IsNil predicate on the "doctor" field.
func DoctorIsNil() predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldIsNull(FieldDoctor))
}

// DoctorNotNil applies the NotNil predicate on the "doctor" field.
func DoctorNotNil() predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.FieldNotNull(FieldDoctor))
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.PaymentReportView) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.PaymentReportView) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.PaymentReportView) predicate.PaymentReportView {
	return predicate.PaymentReportView(sql.NotPredicates(p))
}
