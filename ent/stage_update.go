// Code generated by ent, DO NOT EDIT.

package ent

import (
	"bcare/ent/deal"
	"bcare/ent/pipeline"
	"bcare/ent/predicate"
	"bcare/ent/stage"
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// StageUpdate is the builder for updating Stage entities.
type StageUpdate struct {
	config
	hooks     []Hook
	mutation  *StageMutation
	modifiers []func(*sql.UpdateBuilder)
}

// Where appends a list predicates to the StageUpdate builder.
func (su *StageUpdate) Where(ps ...predicate.Stage) *StageUpdate {
	su.mutation.Where(ps...)
	return su
}

// SetDeletedAt sets the "deleted_at" field.
func (su *StageUpdate) SetDeletedAt(t time.Time) *StageUpdate {
	su.mutation.SetDeletedAt(t)
	return su
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (su *StageUpdate) SetNillableDeletedAt(t *time.Time) *StageUpdate {
	if t != nil {
		su.SetDeletedAt(*t)
	}
	return su
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (su *StageUpdate) ClearDeletedAt() *StageUpdate {
	su.mutation.ClearDeletedAt()
	return su
}

// SetStatus sets the "status" field.
func (su *StageUpdate) SetStatus(i int8) *StageUpdate {
	su.mutation.ResetStatus()
	su.mutation.SetStatus(i)
	return su
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (su *StageUpdate) SetNillableStatus(i *int8) *StageUpdate {
	if i != nil {
		su.SetStatus(*i)
	}
	return su
}

// AddStatus adds i to the "status" field.
func (su *StageUpdate) AddStatus(i int8) *StageUpdate {
	su.mutation.AddStatus(i)
	return su
}

// SetVersion sets the "version" field.
func (su *StageUpdate) SetVersion(i int) *StageUpdate {
	su.mutation.ResetVersion()
	su.mutation.SetVersion(i)
	return su
}

// SetNillableVersion sets the "version" field if the given value is not nil.
func (su *StageUpdate) SetNillableVersion(i *int) *StageUpdate {
	if i != nil {
		su.SetVersion(*i)
	}
	return su
}

// AddVersion adds i to the "version" field.
func (su *StageUpdate) AddVersion(i int) *StageUpdate {
	su.mutation.AddVersion(i)
	return su
}

// SetUpdatedAt sets the "updated_at" field.
func (su *StageUpdate) SetUpdatedAt(t time.Time) *StageUpdate {
	su.mutation.SetUpdatedAt(t)
	return su
}

// SetName sets the "name" field.
func (su *StageUpdate) SetName(s string) *StageUpdate {
	su.mutation.SetName(s)
	return su
}

// SetNillableName sets the "name" field if the given value is not nil.
func (su *StageUpdate) SetNillableName(s *string) *StageUpdate {
	if s != nil {
		su.SetName(*s)
	}
	return su
}

// SetPipelineID sets the "pipeline_id" field.
func (su *StageUpdate) SetPipelineID(i int) *StageUpdate {
	su.mutation.SetPipelineID(i)
	return su
}

// SetNillablePipelineID sets the "pipeline_id" field if the given value is not nil.
func (su *StageUpdate) SetNillablePipelineID(i *int) *StageUpdate {
	if i != nil {
		su.SetPipelineID(*i)
	}
	return su
}

// ClearPipelineID clears the value of the "pipeline_id" field.
func (su *StageUpdate) ClearPipelineID() *StageUpdate {
	su.mutation.ClearPipelineID()
	return su
}

// SetOrderNumber sets the "order_number" field.
func (su *StageUpdate) SetOrderNumber(i int) *StageUpdate {
	su.mutation.ResetOrderNumber()
	su.mutation.SetOrderNumber(i)
	return su
}

// SetNillableOrderNumber sets the "order_number" field if the given value is not nil.
func (su *StageUpdate) SetNillableOrderNumber(i *int) *StageUpdate {
	if i != nil {
		su.SetOrderNumber(*i)
	}
	return su
}

// AddOrderNumber adds i to the "order_number" field.
func (su *StageUpdate) AddOrderNumber(i int) *StageUpdate {
	su.mutation.AddOrderNumber(i)
	return su
}

// ClearOrderNumber clears the value of the "order_number" field.
func (su *StageUpdate) ClearOrderNumber() *StageUpdate {
	su.mutation.ClearOrderNumber()
	return su
}

// SetParentStageID sets the "parent_stage_id" field.
func (su *StageUpdate) SetParentStageID(i int) *StageUpdate {
	su.mutation.SetParentStageID(i)
	return su
}

// SetNillableParentStageID sets the "parent_stage_id" field if the given value is not nil.
func (su *StageUpdate) SetNillableParentStageID(i *int) *StageUpdate {
	if i != nil {
		su.SetParentStageID(*i)
	}
	return su
}

// ClearParentStageID clears the value of the "parent_stage_id" field.
func (su *StageUpdate) ClearParentStageID() *StageUpdate {
	su.mutation.ClearParentStageID()
	return su
}

// SetMeta sets the "meta" field.
func (su *StageUpdate) SetMeta(s string) *StageUpdate {
	su.mutation.SetMeta(s)
	return su
}

// SetNillableMeta sets the "meta" field if the given value is not nil.
func (su *StageUpdate) SetNillableMeta(s *string) *StageUpdate {
	if s != nil {
		su.SetMeta(*s)
	}
	return su
}

// ClearMeta clears the value of the "meta" field.
func (su *StageUpdate) ClearMeta() *StageUpdate {
	su.mutation.ClearMeta()
	return su
}

// SetParentID sets the "parent" edge to the Stage entity by ID.
func (su *StageUpdate) SetParentID(id int) *StageUpdate {
	su.mutation.SetParentID(id)
	return su
}

// SetNillableParentID sets the "parent" edge to the Stage entity by ID if the given value is not nil.
func (su *StageUpdate) SetNillableParentID(id *int) *StageUpdate {
	if id != nil {
		su = su.SetParentID(*id)
	}
	return su
}

// SetParent sets the "parent" edge to the Stage entity.
func (su *StageUpdate) SetParent(s *Stage) *StageUpdate {
	return su.SetParentID(s.ID)
}

// AddChildIDs adds the "children" edge to the Stage entity by IDs.
func (su *StageUpdate) AddChildIDs(ids ...int) *StageUpdate {
	su.mutation.AddChildIDs(ids...)
	return su
}

// AddChildren adds the "children" edges to the Stage entity.
func (su *StageUpdate) AddChildren(s ...*Stage) *StageUpdate {
	ids := make([]int, len(s))
	for i := range s {
		ids[i] = s[i].ID
	}
	return su.AddChildIDs(ids...)
}

// AddDealIDs adds the "deals" edge to the Deal entity by IDs.
func (su *StageUpdate) AddDealIDs(ids ...int) *StageUpdate {
	su.mutation.AddDealIDs(ids...)
	return su
}

// AddDeals adds the "deals" edges to the Deal entity.
func (su *StageUpdate) AddDeals(d ...*Deal) *StageUpdate {
	ids := make([]int, len(d))
	for i := range d {
		ids[i] = d[i].ID
	}
	return su.AddDealIDs(ids...)
}

// SetPipeline sets the "pipeline" edge to the Pipeline entity.
func (su *StageUpdate) SetPipeline(p *Pipeline) *StageUpdate {
	return su.SetPipelineID(p.ID)
}

// Mutation returns the StageMutation object of the builder.
func (su *StageUpdate) Mutation() *StageMutation {
	return su.mutation
}

// ClearParent clears the "parent" edge to the Stage entity.
func (su *StageUpdate) ClearParent() *StageUpdate {
	su.mutation.ClearParent()
	return su
}

// ClearChildren clears all "children" edges to the Stage entity.
func (su *StageUpdate) ClearChildren() *StageUpdate {
	su.mutation.ClearChildren()
	return su
}

// RemoveChildIDs removes the "children" edge to Stage entities by IDs.
func (su *StageUpdate) RemoveChildIDs(ids ...int) *StageUpdate {
	su.mutation.RemoveChildIDs(ids...)
	return su
}

// RemoveChildren removes "children" edges to Stage entities.
func (su *StageUpdate) RemoveChildren(s ...*Stage) *StageUpdate {
	ids := make([]int, len(s))
	for i := range s {
		ids[i] = s[i].ID
	}
	return su.RemoveChildIDs(ids...)
}

// ClearDeals clears all "deals" edges to the Deal entity.
func (su *StageUpdate) ClearDeals() *StageUpdate {
	su.mutation.ClearDeals()
	return su
}

// RemoveDealIDs removes the "deals" edge to Deal entities by IDs.
func (su *StageUpdate) RemoveDealIDs(ids ...int) *StageUpdate {
	su.mutation.RemoveDealIDs(ids...)
	return su
}

// RemoveDeals removes "deals" edges to Deal entities.
func (su *StageUpdate) RemoveDeals(d ...*Deal) *StageUpdate {
	ids := make([]int, len(d))
	for i := range d {
		ids[i] = d[i].ID
	}
	return su.RemoveDealIDs(ids...)
}

// ClearPipeline clears the "pipeline" edge to the Pipeline entity.
func (su *StageUpdate) ClearPipeline() *StageUpdate {
	su.mutation.ClearPipeline()
	return su
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (su *StageUpdate) Save(ctx context.Context) (int, error) {
	if err := su.defaults(); err != nil {
		return 0, err
	}
	return withHooks(ctx, su.sqlSave, su.mutation, su.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (su *StageUpdate) SaveX(ctx context.Context) int {
	affected, err := su.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (su *StageUpdate) Exec(ctx context.Context) error {
	_, err := su.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (su *StageUpdate) ExecX(ctx context.Context) {
	if err := su.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (su *StageUpdate) defaults() error {
	if _, ok := su.mutation.UpdatedAt(); !ok {
		if stage.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized stage.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := stage.UpdateDefaultUpdatedAt()
		su.mutation.SetUpdatedAt(v)
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (su *StageUpdate) Modify(modifiers ...func(u *sql.UpdateBuilder)) *StageUpdate {
	su.modifiers = append(su.modifiers, modifiers...)
	return su
}

func (su *StageUpdate) sqlSave(ctx context.Context) (n int, err error) {
	_spec := sqlgraph.NewUpdateSpec(stage.Table, stage.Columns, sqlgraph.NewFieldSpec(stage.FieldID, field.TypeInt))
	if ps := su.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := su.mutation.DeletedAt(); ok {
		_spec.SetField(stage.FieldDeletedAt, field.TypeTime, value)
	}
	if su.mutation.DeletedAtCleared() {
		_spec.ClearField(stage.FieldDeletedAt, field.TypeTime)
	}
	if value, ok := su.mutation.Status(); ok {
		_spec.SetField(stage.FieldStatus, field.TypeInt8, value)
	}
	if value, ok := su.mutation.AddedStatus(); ok {
		_spec.AddField(stage.FieldStatus, field.TypeInt8, value)
	}
	if value, ok := su.mutation.Version(); ok {
		_spec.SetField(stage.FieldVersion, field.TypeInt, value)
	}
	if value, ok := su.mutation.AddedVersion(); ok {
		_spec.AddField(stage.FieldVersion, field.TypeInt, value)
	}
	if value, ok := su.mutation.UpdatedAt(); ok {
		_spec.SetField(stage.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := su.mutation.Name(); ok {
		_spec.SetField(stage.FieldName, field.TypeString, value)
	}
	if value, ok := su.mutation.OrderNumber(); ok {
		_spec.SetField(stage.FieldOrderNumber, field.TypeInt, value)
	}
	if value, ok := su.mutation.AddedOrderNumber(); ok {
		_spec.AddField(stage.FieldOrderNumber, field.TypeInt, value)
	}
	if su.mutation.OrderNumberCleared() {
		_spec.ClearField(stage.FieldOrderNumber, field.TypeInt)
	}
	if value, ok := su.mutation.Meta(); ok {
		_spec.SetField(stage.FieldMeta, field.TypeString, value)
	}
	if su.mutation.MetaCleared() {
		_spec.ClearField(stage.FieldMeta, field.TypeString)
	}
	if su.mutation.ParentCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   stage.ParentTable,
			Columns: []string{stage.ParentColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(stage.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := su.mutation.ParentIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   stage.ParentTable,
			Columns: []string{stage.ParentColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(stage.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if su.mutation.ChildrenCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   stage.ChildrenTable,
			Columns: []string{stage.ChildrenColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(stage.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := su.mutation.RemovedChildrenIDs(); len(nodes) > 0 && !su.mutation.ChildrenCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   stage.ChildrenTable,
			Columns: []string{stage.ChildrenColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(stage.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := su.mutation.ChildrenIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   stage.ChildrenTable,
			Columns: []string{stage.ChildrenColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(stage.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if su.mutation.DealsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   stage.DealsTable,
			Columns: []string{stage.DealsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(deal.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := su.mutation.RemovedDealsIDs(); len(nodes) > 0 && !su.mutation.DealsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   stage.DealsTable,
			Columns: []string{stage.DealsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(deal.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := su.mutation.DealsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   stage.DealsTable,
			Columns: []string{stage.DealsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(deal.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if su.mutation.PipelineCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   stage.PipelineTable,
			Columns: []string{stage.PipelineColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(pipeline.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := su.mutation.PipelineIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   stage.PipelineTable,
			Columns: []string{stage.PipelineColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(pipeline.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_spec.AddModifiers(su.modifiers...)
	if n, err = sqlgraph.UpdateNodes(ctx, su.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{stage.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	su.mutation.done = true
	return n, nil
}

// StageUpdateOne is the builder for updating a single Stage entity.
type StageUpdateOne struct {
	config
	fields    []string
	hooks     []Hook
	mutation  *StageMutation
	modifiers []func(*sql.UpdateBuilder)
}

// SetDeletedAt sets the "deleted_at" field.
func (suo *StageUpdateOne) SetDeletedAt(t time.Time) *StageUpdateOne {
	suo.mutation.SetDeletedAt(t)
	return suo
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (suo *StageUpdateOne) SetNillableDeletedAt(t *time.Time) *StageUpdateOne {
	if t != nil {
		suo.SetDeletedAt(*t)
	}
	return suo
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (suo *StageUpdateOne) ClearDeletedAt() *StageUpdateOne {
	suo.mutation.ClearDeletedAt()
	return suo
}

// SetStatus sets the "status" field.
func (suo *StageUpdateOne) SetStatus(i int8) *StageUpdateOne {
	suo.mutation.ResetStatus()
	suo.mutation.SetStatus(i)
	return suo
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (suo *StageUpdateOne) SetNillableStatus(i *int8) *StageUpdateOne {
	if i != nil {
		suo.SetStatus(*i)
	}
	return suo
}

// AddStatus adds i to the "status" field.
func (suo *StageUpdateOne) AddStatus(i int8) *StageUpdateOne {
	suo.mutation.AddStatus(i)
	return suo
}

// SetVersion sets the "version" field.
func (suo *StageUpdateOne) SetVersion(i int) *StageUpdateOne {
	suo.mutation.ResetVersion()
	suo.mutation.SetVersion(i)
	return suo
}

// SetNillableVersion sets the "version" field if the given value is not nil.
func (suo *StageUpdateOne) SetNillableVersion(i *int) *StageUpdateOne {
	if i != nil {
		suo.SetVersion(*i)
	}
	return suo
}

// AddVersion adds i to the "version" field.
func (suo *StageUpdateOne) AddVersion(i int) *StageUpdateOne {
	suo.mutation.AddVersion(i)
	return suo
}

// SetUpdatedAt sets the "updated_at" field.
func (suo *StageUpdateOne) SetUpdatedAt(t time.Time) *StageUpdateOne {
	suo.mutation.SetUpdatedAt(t)
	return suo
}

// SetName sets the "name" field.
func (suo *StageUpdateOne) SetName(s string) *StageUpdateOne {
	suo.mutation.SetName(s)
	return suo
}

// SetNillableName sets the "name" field if the given value is not nil.
func (suo *StageUpdateOne) SetNillableName(s *string) *StageUpdateOne {
	if s != nil {
		suo.SetName(*s)
	}
	return suo
}

// SetPipelineID sets the "pipeline_id" field.
func (suo *StageUpdateOne) SetPipelineID(i int) *StageUpdateOne {
	suo.mutation.SetPipelineID(i)
	return suo
}

// SetNillablePipelineID sets the "pipeline_id" field if the given value is not nil.
func (suo *StageUpdateOne) SetNillablePipelineID(i *int) *StageUpdateOne {
	if i != nil {
		suo.SetPipelineID(*i)
	}
	return suo
}

// ClearPipelineID clears the value of the "pipeline_id" field.
func (suo *StageUpdateOne) ClearPipelineID() *StageUpdateOne {
	suo.mutation.ClearPipelineID()
	return suo
}

// SetOrderNumber sets the "order_number" field.
func (suo *StageUpdateOne) SetOrderNumber(i int) *StageUpdateOne {
	suo.mutation.ResetOrderNumber()
	suo.mutation.SetOrderNumber(i)
	return suo
}

// SetNillableOrderNumber sets the "order_number" field if the given value is not nil.
func (suo *StageUpdateOne) SetNillableOrderNumber(i *int) *StageUpdateOne {
	if i != nil {
		suo.SetOrderNumber(*i)
	}
	return suo
}

// AddOrderNumber adds i to the "order_number" field.
func (suo *StageUpdateOne) AddOrderNumber(i int) *StageUpdateOne {
	suo.mutation.AddOrderNumber(i)
	return suo
}

// ClearOrderNumber clears the value of the "order_number" field.
func (suo *StageUpdateOne) ClearOrderNumber() *StageUpdateOne {
	suo.mutation.ClearOrderNumber()
	return suo
}

// SetParentStageID sets the "parent_stage_id" field.
func (suo *StageUpdateOne) SetParentStageID(i int) *StageUpdateOne {
	suo.mutation.SetParentStageID(i)
	return suo
}

// SetNillableParentStageID sets the "parent_stage_id" field if the given value is not nil.
func (suo *StageUpdateOne) SetNillableParentStageID(i *int) *StageUpdateOne {
	if i != nil {
		suo.SetParentStageID(*i)
	}
	return suo
}

// ClearParentStageID clears the value of the "parent_stage_id" field.
func (suo *StageUpdateOne) ClearParentStageID() *StageUpdateOne {
	suo.mutation.ClearParentStageID()
	return suo
}

// SetMeta sets the "meta" field.
func (suo *StageUpdateOne) SetMeta(s string) *StageUpdateOne {
	suo.mutation.SetMeta(s)
	return suo
}

// SetNillableMeta sets the "meta" field if the given value is not nil.
func (suo *StageUpdateOne) SetNillableMeta(s *string) *StageUpdateOne {
	if s != nil {
		suo.SetMeta(*s)
	}
	return suo
}

// ClearMeta clears the value of the "meta" field.
func (suo *StageUpdateOne) ClearMeta() *StageUpdateOne {
	suo.mutation.ClearMeta()
	return suo
}

// SetParentID sets the "parent" edge to the Stage entity by ID.
func (suo *StageUpdateOne) SetParentID(id int) *StageUpdateOne {
	suo.mutation.SetParentID(id)
	return suo
}

// SetNillableParentID sets the "parent" edge to the Stage entity by ID if the given value is not nil.
func (suo *StageUpdateOne) SetNillableParentID(id *int) *StageUpdateOne {
	if id != nil {
		suo = suo.SetParentID(*id)
	}
	return suo
}

// SetParent sets the "parent" edge to the Stage entity.
func (suo *StageUpdateOne) SetParent(s *Stage) *StageUpdateOne {
	return suo.SetParentID(s.ID)
}

// AddChildIDs adds the "children" edge to the Stage entity by IDs.
func (suo *StageUpdateOne) AddChildIDs(ids ...int) *StageUpdateOne {
	suo.mutation.AddChildIDs(ids...)
	return suo
}

// AddChildren adds the "children" edges to the Stage entity.
func (suo *StageUpdateOne) AddChildren(s ...*Stage) *StageUpdateOne {
	ids := make([]int, len(s))
	for i := range s {
		ids[i] = s[i].ID
	}
	return suo.AddChildIDs(ids...)
}

// AddDealIDs adds the "deals" edge to the Deal entity by IDs.
func (suo *StageUpdateOne) AddDealIDs(ids ...int) *StageUpdateOne {
	suo.mutation.AddDealIDs(ids...)
	return suo
}

// AddDeals adds the "deals" edges to the Deal entity.
func (suo *StageUpdateOne) AddDeals(d ...*Deal) *StageUpdateOne {
	ids := make([]int, len(d))
	for i := range d {
		ids[i] = d[i].ID
	}
	return suo.AddDealIDs(ids...)
}

// SetPipeline sets the "pipeline" edge to the Pipeline entity.
func (suo *StageUpdateOne) SetPipeline(p *Pipeline) *StageUpdateOne {
	return suo.SetPipelineID(p.ID)
}

// Mutation returns the StageMutation object of the builder.
func (suo *StageUpdateOne) Mutation() *StageMutation {
	return suo.mutation
}

// ClearParent clears the "parent" edge to the Stage entity.
func (suo *StageUpdateOne) ClearParent() *StageUpdateOne {
	suo.mutation.ClearParent()
	return suo
}

// ClearChildren clears all "children" edges to the Stage entity.
func (suo *StageUpdateOne) ClearChildren() *StageUpdateOne {
	suo.mutation.ClearChildren()
	return suo
}

// RemoveChildIDs removes the "children" edge to Stage entities by IDs.
func (suo *StageUpdateOne) RemoveChildIDs(ids ...int) *StageUpdateOne {
	suo.mutation.RemoveChildIDs(ids...)
	return suo
}

// RemoveChildren removes "children" edges to Stage entities.
func (suo *StageUpdateOne) RemoveChildren(s ...*Stage) *StageUpdateOne {
	ids := make([]int, len(s))
	for i := range s {
		ids[i] = s[i].ID
	}
	return suo.RemoveChildIDs(ids...)
}

// ClearDeals clears all "deals" edges to the Deal entity.
func (suo *StageUpdateOne) ClearDeals() *StageUpdateOne {
	suo.mutation.ClearDeals()
	return suo
}

// RemoveDealIDs removes the "deals" edge to Deal entities by IDs.
func (suo *StageUpdateOne) RemoveDealIDs(ids ...int) *StageUpdateOne {
	suo.mutation.RemoveDealIDs(ids...)
	return suo
}

// RemoveDeals removes "deals" edges to Deal entities.
func (suo *StageUpdateOne) RemoveDeals(d ...*Deal) *StageUpdateOne {
	ids := make([]int, len(d))
	for i := range d {
		ids[i] = d[i].ID
	}
	return suo.RemoveDealIDs(ids...)
}

// ClearPipeline clears the "pipeline" edge to the Pipeline entity.
func (suo *StageUpdateOne) ClearPipeline() *StageUpdateOne {
	suo.mutation.ClearPipeline()
	return suo
}

// Where appends a list predicates to the StageUpdate builder.
func (suo *StageUpdateOne) Where(ps ...predicate.Stage) *StageUpdateOne {
	suo.mutation.Where(ps...)
	return suo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (suo *StageUpdateOne) Select(field string, fields ...string) *StageUpdateOne {
	suo.fields = append([]string{field}, fields...)
	return suo
}

// Save executes the query and returns the updated Stage entity.
func (suo *StageUpdateOne) Save(ctx context.Context) (*Stage, error) {
	if err := suo.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, suo.sqlSave, suo.mutation, suo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (suo *StageUpdateOne) SaveX(ctx context.Context) *Stage {
	node, err := suo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (suo *StageUpdateOne) Exec(ctx context.Context) error {
	_, err := suo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (suo *StageUpdateOne) ExecX(ctx context.Context) {
	if err := suo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (suo *StageUpdateOne) defaults() error {
	if _, ok := suo.mutation.UpdatedAt(); !ok {
		if stage.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized stage.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := stage.UpdateDefaultUpdatedAt()
		suo.mutation.SetUpdatedAt(v)
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (suo *StageUpdateOne) Modify(modifiers ...func(u *sql.UpdateBuilder)) *StageUpdateOne {
	suo.modifiers = append(suo.modifiers, modifiers...)
	return suo
}

func (suo *StageUpdateOne) sqlSave(ctx context.Context) (_node *Stage, err error) {
	_spec := sqlgraph.NewUpdateSpec(stage.Table, stage.Columns, sqlgraph.NewFieldSpec(stage.FieldID, field.TypeInt))
	id, ok := suo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "Stage.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := suo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, stage.FieldID)
		for _, f := range fields {
			if !stage.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != stage.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := suo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := suo.mutation.DeletedAt(); ok {
		_spec.SetField(stage.FieldDeletedAt, field.TypeTime, value)
	}
	if suo.mutation.DeletedAtCleared() {
		_spec.ClearField(stage.FieldDeletedAt, field.TypeTime)
	}
	if value, ok := suo.mutation.Status(); ok {
		_spec.SetField(stage.FieldStatus, field.TypeInt8, value)
	}
	if value, ok := suo.mutation.AddedStatus(); ok {
		_spec.AddField(stage.FieldStatus, field.TypeInt8, value)
	}
	if value, ok := suo.mutation.Version(); ok {
		_spec.SetField(stage.FieldVersion, field.TypeInt, value)
	}
	if value, ok := suo.mutation.AddedVersion(); ok {
		_spec.AddField(stage.FieldVersion, field.TypeInt, value)
	}
	if value, ok := suo.mutation.UpdatedAt(); ok {
		_spec.SetField(stage.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := suo.mutation.Name(); ok {
		_spec.SetField(stage.FieldName, field.TypeString, value)
	}
	if value, ok := suo.mutation.OrderNumber(); ok {
		_spec.SetField(stage.FieldOrderNumber, field.TypeInt, value)
	}
	if value, ok := suo.mutation.AddedOrderNumber(); ok {
		_spec.AddField(stage.FieldOrderNumber, field.TypeInt, value)
	}
	if suo.mutation.OrderNumberCleared() {
		_spec.ClearField(stage.FieldOrderNumber, field.TypeInt)
	}
	if value, ok := suo.mutation.Meta(); ok {
		_spec.SetField(stage.FieldMeta, field.TypeString, value)
	}
	if suo.mutation.MetaCleared() {
		_spec.ClearField(stage.FieldMeta, field.TypeString)
	}
	if suo.mutation.ParentCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   stage.ParentTable,
			Columns: []string{stage.ParentColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(stage.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := suo.mutation.ParentIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   stage.ParentTable,
			Columns: []string{stage.ParentColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(stage.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if suo.mutation.ChildrenCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   stage.ChildrenTable,
			Columns: []string{stage.ChildrenColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(stage.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := suo.mutation.RemovedChildrenIDs(); len(nodes) > 0 && !suo.mutation.ChildrenCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   stage.ChildrenTable,
			Columns: []string{stage.ChildrenColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(stage.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := suo.mutation.ChildrenIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   stage.ChildrenTable,
			Columns: []string{stage.ChildrenColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(stage.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if suo.mutation.DealsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   stage.DealsTable,
			Columns: []string{stage.DealsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(deal.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := suo.mutation.RemovedDealsIDs(); len(nodes) > 0 && !suo.mutation.DealsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   stage.DealsTable,
			Columns: []string{stage.DealsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(deal.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := suo.mutation.DealsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   stage.DealsTable,
			Columns: []string{stage.DealsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(deal.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if suo.mutation.PipelineCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   stage.PipelineTable,
			Columns: []string{stage.PipelineColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(pipeline.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := suo.mutation.PipelineIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   stage.PipelineTable,
			Columns: []string{stage.PipelineColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(pipeline.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_spec.AddModifiers(suo.modifiers...)
	_node = &Stage{config: suo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, suo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{stage.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	suo.mutation.done = true
	return _node, nil
}
