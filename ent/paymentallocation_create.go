// Code generated by ent, DO NOT EDIT.

package ent

import (
	"bcare/ent/billitem"
	"bcare/ent/installment"
	"bcare/ent/payment"
	"bcare/ent/paymentallocation"
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// PaymentAllocationCreate is the builder for creating a PaymentAllocation entity.
type PaymentAllocationCreate struct {
	config
	mutation *PaymentAllocationMutation
	hooks    []Hook
}

// SetDeletedAt sets the "deleted_at" field.
func (pac *PaymentAllocationCreate) SetDeletedAt(t time.Time) *PaymentAllocationCreate {
	pac.mutation.SetDeletedAt(t)
	return pac
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (pac *PaymentAllocationCreate) SetNillableDeletedAt(t *time.Time) *PaymentAllocationCreate {
	if t != nil {
		pac.SetDeletedAt(*t)
	}
	return pac
}

// SetStatus sets the "status" field.
func (pac *PaymentAllocationCreate) SetStatus(i int8) *PaymentAllocationCreate {
	pac.mutation.SetStatus(i)
	return pac
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (pac *PaymentAllocationCreate) SetNillableStatus(i *int8) *PaymentAllocationCreate {
	if i != nil {
		pac.SetStatus(*i)
	}
	return pac
}

// SetVersion sets the "version" field.
func (pac *PaymentAllocationCreate) SetVersion(i int) *PaymentAllocationCreate {
	pac.mutation.SetVersion(i)
	return pac
}

// SetNillableVersion sets the "version" field if the given value is not nil.
func (pac *PaymentAllocationCreate) SetNillableVersion(i *int) *PaymentAllocationCreate {
	if i != nil {
		pac.SetVersion(*i)
	}
	return pac
}

// SetCreatedAt sets the "created_at" field.
func (pac *PaymentAllocationCreate) SetCreatedAt(t time.Time) *PaymentAllocationCreate {
	pac.mutation.SetCreatedAt(t)
	return pac
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (pac *PaymentAllocationCreate) SetNillableCreatedAt(t *time.Time) *PaymentAllocationCreate {
	if t != nil {
		pac.SetCreatedAt(*t)
	}
	return pac
}

// SetUpdatedAt sets the "updated_at" field.
func (pac *PaymentAllocationCreate) SetUpdatedAt(t time.Time) *PaymentAllocationCreate {
	pac.mutation.SetUpdatedAt(t)
	return pac
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (pac *PaymentAllocationCreate) SetNillableUpdatedAt(t *time.Time) *PaymentAllocationCreate {
	if t != nil {
		pac.SetUpdatedAt(*t)
	}
	return pac
}

// SetPaymentID sets the "payment_id" field.
func (pac *PaymentAllocationCreate) SetPaymentID(i int) *PaymentAllocationCreate {
	pac.mutation.SetPaymentID(i)
	return pac
}

// SetBillID sets the "bill_id" field.
func (pac *PaymentAllocationCreate) SetBillID(i int) *PaymentAllocationCreate {
	pac.mutation.SetBillID(i)
	return pac
}

// SetNillableBillID sets the "bill_id" field if the given value is not nil.
func (pac *PaymentAllocationCreate) SetNillableBillID(i *int) *PaymentAllocationCreate {
	if i != nil {
		pac.SetBillID(*i)
	}
	return pac
}

// SetUserID sets the "user_id" field.
func (pac *PaymentAllocationCreate) SetUserID(i int) *PaymentAllocationCreate {
	pac.mutation.SetUserID(i)
	return pac
}

// SetNillableUserID sets the "user_id" field if the given value is not nil.
func (pac *PaymentAllocationCreate) SetNillableUserID(i *int) *PaymentAllocationCreate {
	if i != nil {
		pac.SetUserID(*i)
	}
	return pac
}

// SetBillItemID sets the "bill_item_id" field.
func (pac *PaymentAllocationCreate) SetBillItemID(i int) *PaymentAllocationCreate {
	pac.mutation.SetBillItemID(i)
	return pac
}

// SetNillableBillItemID sets the "bill_item_id" field if the given value is not nil.
func (pac *PaymentAllocationCreate) SetNillableBillItemID(i *int) *PaymentAllocationCreate {
	if i != nil {
		pac.SetBillItemID(*i)
	}
	return pac
}

// SetInstallmentID sets the "installment_id" field.
func (pac *PaymentAllocationCreate) SetInstallmentID(i int) *PaymentAllocationCreate {
	pac.mutation.SetInstallmentID(i)
	return pac
}

// SetNillableInstallmentID sets the "installment_id" field if the given value is not nil.
func (pac *PaymentAllocationCreate) SetNillableInstallmentID(i *int) *PaymentAllocationCreate {
	if i != nil {
		pac.SetInstallmentID(*i)
	}
	return pac
}

// SetNote sets the "note" field.
func (pac *PaymentAllocationCreate) SetNote(s string) *PaymentAllocationCreate {
	pac.mutation.SetNote(s)
	return pac
}

// SetNillableNote sets the "note" field if the given value is not nil.
func (pac *PaymentAllocationCreate) SetNillableNote(s *string) *PaymentAllocationCreate {
	if s != nil {
		pac.SetNote(*s)
	}
	return pac
}

// SetState sets the "state" field.
func (pac *PaymentAllocationCreate) SetState(s string) *PaymentAllocationCreate {
	pac.mutation.SetState(s)
	return pac
}

// SetNillableState sets the "state" field if the given value is not nil.
func (pac *PaymentAllocationCreate) SetNillableState(s *string) *PaymentAllocationCreate {
	if s != nil {
		pac.SetState(*s)
	}
	return pac
}

// SetAmount sets the "amount" field.
func (pac *PaymentAllocationCreate) SetAmount(f float64) *PaymentAllocationCreate {
	pac.mutation.SetAmount(f)
	return pac
}

// SetNillableAmount sets the "amount" field if the given value is not nil.
func (pac *PaymentAllocationCreate) SetNillableAmount(f *float64) *PaymentAllocationCreate {
	if f != nil {
		pac.SetAmount(*f)
	}
	return pac
}

// SetPayment sets the "payment" edge to the Payment entity.
func (pac *PaymentAllocationCreate) SetPayment(p *Payment) *PaymentAllocationCreate {
	return pac.SetPaymentID(p.ID)
}

// SetBillItem sets the "bill_item" edge to the BillItem entity.
func (pac *PaymentAllocationCreate) SetBillItem(b *BillItem) *PaymentAllocationCreate {
	return pac.SetBillItemID(b.ID)
}

// SetInstallment sets the "installment" edge to the Installment entity.
func (pac *PaymentAllocationCreate) SetInstallment(i *Installment) *PaymentAllocationCreate {
	return pac.SetInstallmentID(i.ID)
}

// Mutation returns the PaymentAllocationMutation object of the builder.
func (pac *PaymentAllocationCreate) Mutation() *PaymentAllocationMutation {
	return pac.mutation
}

// Save creates the PaymentAllocation in the database.
func (pac *PaymentAllocationCreate) Save(ctx context.Context) (*PaymentAllocation, error) {
	if err := pac.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, pac.sqlSave, pac.mutation, pac.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (pac *PaymentAllocationCreate) SaveX(ctx context.Context) *PaymentAllocation {
	v, err := pac.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (pac *PaymentAllocationCreate) Exec(ctx context.Context) error {
	_, err := pac.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (pac *PaymentAllocationCreate) ExecX(ctx context.Context) {
	if err := pac.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (pac *PaymentAllocationCreate) defaults() error {
	if _, ok := pac.mutation.Status(); !ok {
		v := paymentallocation.DefaultStatus
		pac.mutation.SetStatus(v)
	}
	if _, ok := pac.mutation.Version(); !ok {
		v := paymentallocation.DefaultVersion
		pac.mutation.SetVersion(v)
	}
	if _, ok := pac.mutation.CreatedAt(); !ok {
		if paymentallocation.DefaultCreatedAt == nil {
			return fmt.Errorf("ent: uninitialized paymentallocation.DefaultCreatedAt (forgotten import ent/runtime?)")
		}
		v := paymentallocation.DefaultCreatedAt()
		pac.mutation.SetCreatedAt(v)
	}
	if _, ok := pac.mutation.UpdatedAt(); !ok {
		if paymentallocation.DefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized paymentallocation.DefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := paymentallocation.DefaultUpdatedAt()
		pac.mutation.SetUpdatedAt(v)
	}
	if _, ok := pac.mutation.Amount(); !ok {
		v := paymentallocation.DefaultAmount
		pac.mutation.SetAmount(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (pac *PaymentAllocationCreate) check() error {
	if _, ok := pac.mutation.Status(); !ok {
		return &ValidationError{Name: "status", err: errors.New(`ent: missing required field "PaymentAllocation.status"`)}
	}
	if _, ok := pac.mutation.Version(); !ok {
		return &ValidationError{Name: "version", err: errors.New(`ent: missing required field "PaymentAllocation.version"`)}
	}
	if _, ok := pac.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "PaymentAllocation.created_at"`)}
	}
	if _, ok := pac.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "PaymentAllocation.updated_at"`)}
	}
	if _, ok := pac.mutation.PaymentID(); !ok {
		return &ValidationError{Name: "payment_id", err: errors.New(`ent: missing required field "PaymentAllocation.payment_id"`)}
	}
	if _, ok := pac.mutation.Amount(); !ok {
		return &ValidationError{Name: "amount", err: errors.New(`ent: missing required field "PaymentAllocation.amount"`)}
	}
	if len(pac.mutation.PaymentIDs()) == 0 {
		return &ValidationError{Name: "payment", err: errors.New(`ent: missing required edge "PaymentAllocation.payment"`)}
	}
	return nil
}

func (pac *PaymentAllocationCreate) sqlSave(ctx context.Context) (*PaymentAllocation, error) {
	if err := pac.check(); err != nil {
		return nil, err
	}
	_node, _spec := pac.createSpec()
	if err := sqlgraph.CreateNode(ctx, pac.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	id := _spec.ID.Value.(int64)
	_node.ID = int(id)
	pac.mutation.id = &_node.ID
	pac.mutation.done = true
	return _node, nil
}

func (pac *PaymentAllocationCreate) createSpec() (*PaymentAllocation, *sqlgraph.CreateSpec) {
	var (
		_node = &PaymentAllocation{config: pac.config}
		_spec = sqlgraph.NewCreateSpec(paymentallocation.Table, sqlgraph.NewFieldSpec(paymentallocation.FieldID, field.TypeInt))
	)
	if value, ok := pac.mutation.DeletedAt(); ok {
		_spec.SetField(paymentallocation.FieldDeletedAt, field.TypeTime, value)
		_node.DeletedAt = value
	}
	if value, ok := pac.mutation.Status(); ok {
		_spec.SetField(paymentallocation.FieldStatus, field.TypeInt8, value)
		_node.Status = value
	}
	if value, ok := pac.mutation.Version(); ok {
		_spec.SetField(paymentallocation.FieldVersion, field.TypeInt, value)
		_node.Version = value
	}
	if value, ok := pac.mutation.CreatedAt(); ok {
		_spec.SetField(paymentallocation.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := pac.mutation.UpdatedAt(); ok {
		_spec.SetField(paymentallocation.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	if value, ok := pac.mutation.BillID(); ok {
		_spec.SetField(paymentallocation.FieldBillID, field.TypeInt, value)
		_node.BillID = value
	}
	if value, ok := pac.mutation.UserID(); ok {
		_spec.SetField(paymentallocation.FieldUserID, field.TypeInt, value)
		_node.UserID = value
	}
	if value, ok := pac.mutation.Note(); ok {
		_spec.SetField(paymentallocation.FieldNote, field.TypeString, value)
		_node.Note = value
	}
	if value, ok := pac.mutation.State(); ok {
		_spec.SetField(paymentallocation.FieldState, field.TypeString, value)
		_node.State = value
	}
	if value, ok := pac.mutation.Amount(); ok {
		_spec.SetField(paymentallocation.FieldAmount, field.TypeFloat64, value)
		_node.Amount = value
	}
	if nodes := pac.mutation.PaymentIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   paymentallocation.PaymentTable,
			Columns: []string{paymentallocation.PaymentColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(payment.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.PaymentID = nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := pac.mutation.BillItemIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   paymentallocation.BillItemTable,
			Columns: []string{paymentallocation.BillItemColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(billitem.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.BillItemID = &nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := pac.mutation.InstallmentIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   paymentallocation.InstallmentTable,
			Columns: []string{paymentallocation.InstallmentColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(installment.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.InstallmentID = &nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	return _node, _spec
}

// PaymentAllocationCreateBulk is the builder for creating many PaymentAllocation entities in bulk.
type PaymentAllocationCreateBulk struct {
	config
	err      error
	builders []*PaymentAllocationCreate
}

// Save creates the PaymentAllocation entities in the database.
func (pacb *PaymentAllocationCreateBulk) Save(ctx context.Context) ([]*PaymentAllocation, error) {
	if pacb.err != nil {
		return nil, pacb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(pacb.builders))
	nodes := make([]*PaymentAllocation, len(pacb.builders))
	mutators := make([]Mutator, len(pacb.builders))
	for i := range pacb.builders {
		func(i int, root context.Context) {
			builder := pacb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*PaymentAllocationMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, pacb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, pacb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = int(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, pacb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (pacb *PaymentAllocationCreateBulk) SaveX(ctx context.Context) []*PaymentAllocation {
	v, err := pacb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (pacb *PaymentAllocationCreateBulk) Exec(ctx context.Context) error {
	_, err := pacb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (pacb *PaymentAllocationCreateBulk) ExecX(ctx context.Context) {
	if err := pacb.Exec(ctx); err != nil {
		panic(err)
	}
}
