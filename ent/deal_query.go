// Code generated by ent, DO NOT EDIT.

package ent

import (
	"bcare/ent/attachment"
	"bcare/ent/bill"
	"bcare/ent/deal"
	"bcare/ent/dealuser"
	"bcare/ent/deposit"
	"bcare/ent/discountusage"
	"bcare/ent/installmentplan"
	"bcare/ent/person"
	"bcare/ent/predicate"
	"bcare/ent/stage"
	"bcare/ent/tag"
	"bcare/ent/tagdeal"
	"bcare/ent/task"
	"bcare/ent/track"
	"context"
	"database/sql/driver"
	"fmt"
	"math"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// DealQuery is the builder for querying Deal entities.
type DealQuery struct {
	config
	ctx                *QueryContext
	order              []deal.OrderOption
	inters             []Interceptor
	predicates         []predicate.Deal
	withPerson         *PersonQuery
	withStage          *StageQuery
	withAttachments    *AttachmentQuery
	withPlans          *InstallmentPlanQuery
	withBills          *BillQuery
	withTracks         *TrackQuery
	withTasks          *TaskQuery
	withDealAssignment *DealUserQuery
	withTags           *TagQuery
	withDeposits       *DepositQuery
	withDiscountUsages *DiscountUsageQuery
	withTagDeal        *TagDealQuery
	modifiers          []func(*sql.Selector)
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the DealQuery builder.
func (dq *DealQuery) Where(ps ...predicate.Deal) *DealQuery {
	dq.predicates = append(dq.predicates, ps...)
	return dq
}

// Limit the number of records to be returned by this query.
func (dq *DealQuery) Limit(limit int) *DealQuery {
	dq.ctx.Limit = &limit
	return dq
}

// Offset to start from.
func (dq *DealQuery) Offset(offset int) *DealQuery {
	dq.ctx.Offset = &offset
	return dq
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (dq *DealQuery) Unique(unique bool) *DealQuery {
	dq.ctx.Unique = &unique
	return dq
}

// Order specifies how the records should be ordered.
func (dq *DealQuery) Order(o ...deal.OrderOption) *DealQuery {
	dq.order = append(dq.order, o...)
	return dq
}

// QueryPerson chains the current query on the "person" edge.
func (dq *DealQuery) QueryPerson() *PersonQuery {
	query := (&PersonClient{config: dq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := dq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := dq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(deal.Table, deal.FieldID, selector),
			sqlgraph.To(person.Table, person.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, deal.PersonTable, deal.PersonColumn),
		)
		fromU = sqlgraph.SetNeighbors(dq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// QueryStage chains the current query on the "stage" edge.
func (dq *DealQuery) QueryStage() *StageQuery {
	query := (&StageClient{config: dq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := dq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := dq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(deal.Table, deal.FieldID, selector),
			sqlgraph.To(stage.Table, stage.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, deal.StageTable, deal.StageColumn),
		)
		fromU = sqlgraph.SetNeighbors(dq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// QueryAttachments chains the current query on the "attachments" edge.
func (dq *DealQuery) QueryAttachments() *AttachmentQuery {
	query := (&AttachmentClient{config: dq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := dq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := dq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(deal.Table, deal.FieldID, selector),
			sqlgraph.To(attachment.Table, attachment.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, deal.AttachmentsTable, deal.AttachmentsColumn),
		)
		fromU = sqlgraph.SetNeighbors(dq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// QueryPlans chains the current query on the "plans" edge.
func (dq *DealQuery) QueryPlans() *InstallmentPlanQuery {
	query := (&InstallmentPlanClient{config: dq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := dq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := dq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(deal.Table, deal.FieldID, selector),
			sqlgraph.To(installmentplan.Table, installmentplan.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, deal.PlansTable, deal.PlansColumn),
		)
		fromU = sqlgraph.SetNeighbors(dq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// QueryBills chains the current query on the "bills" edge.
func (dq *DealQuery) QueryBills() *BillQuery {
	query := (&BillClient{config: dq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := dq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := dq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(deal.Table, deal.FieldID, selector),
			sqlgraph.To(bill.Table, bill.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, deal.BillsTable, deal.BillsColumn),
		)
		fromU = sqlgraph.SetNeighbors(dq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// QueryTracks chains the current query on the "tracks" edge.
func (dq *DealQuery) QueryTracks() *TrackQuery {
	query := (&TrackClient{config: dq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := dq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := dq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(deal.Table, deal.FieldID, selector),
			sqlgraph.To(track.Table, track.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, deal.TracksTable, deal.TracksColumn),
		)
		fromU = sqlgraph.SetNeighbors(dq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// QueryTasks chains the current query on the "tasks" edge.
func (dq *DealQuery) QueryTasks() *TaskQuery {
	query := (&TaskClient{config: dq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := dq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := dq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(deal.Table, deal.FieldID, selector),
			sqlgraph.To(task.Table, task.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, deal.TasksTable, deal.TasksColumn),
		)
		fromU = sqlgraph.SetNeighbors(dq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// QueryDealAssignment chains the current query on the "deal_assignment" edge.
func (dq *DealQuery) QueryDealAssignment() *DealUserQuery {
	query := (&DealUserClient{config: dq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := dq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := dq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(deal.Table, deal.FieldID, selector),
			sqlgraph.To(dealuser.Table, dealuser.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, deal.DealAssignmentTable, deal.DealAssignmentColumn),
		)
		fromU = sqlgraph.SetNeighbors(dq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// QueryTags chains the current query on the "tags" edge.
func (dq *DealQuery) QueryTags() *TagQuery {
	query := (&TagClient{config: dq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := dq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := dq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(deal.Table, deal.FieldID, selector),
			sqlgraph.To(tag.Table, tag.FieldID),
			sqlgraph.Edge(sqlgraph.M2M, false, deal.TagsTable, deal.TagsPrimaryKey...),
		)
		fromU = sqlgraph.SetNeighbors(dq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// QueryDeposits chains the current query on the "deposits" edge.
func (dq *DealQuery) QueryDeposits() *DepositQuery {
	query := (&DepositClient{config: dq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := dq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := dq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(deal.Table, deal.FieldID, selector),
			sqlgraph.To(deposit.Table, deposit.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, deal.DepositsTable, deal.DepositsColumn),
		)
		fromU = sqlgraph.SetNeighbors(dq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// QueryDiscountUsages chains the current query on the "discount_usages" edge.
func (dq *DealQuery) QueryDiscountUsages() *DiscountUsageQuery {
	query := (&DiscountUsageClient{config: dq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := dq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := dq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(deal.Table, deal.FieldID, selector),
			sqlgraph.To(discountusage.Table, discountusage.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, deal.DiscountUsagesTable, deal.DiscountUsagesColumn),
		)
		fromU = sqlgraph.SetNeighbors(dq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// QueryTagDeal chains the current query on the "tag_deal" edge.
func (dq *DealQuery) QueryTagDeal() *TagDealQuery {
	query := (&TagDealClient{config: dq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := dq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := dq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(deal.Table, deal.FieldID, selector),
			sqlgraph.To(tagdeal.Table, tagdeal.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, deal.TagDealTable, deal.TagDealColumn),
		)
		fromU = sqlgraph.SetNeighbors(dq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// First returns the first Deal entity from the query.
// Returns a *NotFoundError when no Deal was found.
func (dq *DealQuery) First(ctx context.Context) (*Deal, error) {
	nodes, err := dq.Limit(1).All(setContextOp(ctx, dq.ctx, ent.OpQueryFirst))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{deal.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (dq *DealQuery) FirstX(ctx context.Context) *Deal {
	node, err := dq.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first Deal ID from the query.
// Returns a *NotFoundError when no Deal ID was found.
func (dq *DealQuery) FirstID(ctx context.Context) (id int, err error) {
	var ids []int
	if ids, err = dq.Limit(1).IDs(setContextOp(ctx, dq.ctx, ent.OpQueryFirstID)); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{deal.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (dq *DealQuery) FirstIDX(ctx context.Context) int {
	id, err := dq.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single Deal entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one Deal entity is found.
// Returns a *NotFoundError when no Deal entities are found.
func (dq *DealQuery) Only(ctx context.Context) (*Deal, error) {
	nodes, err := dq.Limit(2).All(setContextOp(ctx, dq.ctx, ent.OpQueryOnly))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{deal.Label}
	default:
		return nil, &NotSingularError{deal.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (dq *DealQuery) OnlyX(ctx context.Context) *Deal {
	node, err := dq.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only Deal ID in the query.
// Returns a *NotSingularError when more than one Deal ID is found.
// Returns a *NotFoundError when no entities are found.
func (dq *DealQuery) OnlyID(ctx context.Context) (id int, err error) {
	var ids []int
	if ids, err = dq.Limit(2).IDs(setContextOp(ctx, dq.ctx, ent.OpQueryOnlyID)); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{deal.Label}
	default:
		err = &NotSingularError{deal.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (dq *DealQuery) OnlyIDX(ctx context.Context) int {
	id, err := dq.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of Deals.
func (dq *DealQuery) All(ctx context.Context) ([]*Deal, error) {
	ctx = setContextOp(ctx, dq.ctx, ent.OpQueryAll)
	if err := dq.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*Deal, *DealQuery]()
	return withInterceptors[[]*Deal](ctx, dq, qr, dq.inters)
}

// AllX is like All, but panics if an error occurs.
func (dq *DealQuery) AllX(ctx context.Context) []*Deal {
	nodes, err := dq.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of Deal IDs.
func (dq *DealQuery) IDs(ctx context.Context) (ids []int, err error) {
	if dq.ctx.Unique == nil && dq.path != nil {
		dq.Unique(true)
	}
	ctx = setContextOp(ctx, dq.ctx, ent.OpQueryIDs)
	if err = dq.Select(deal.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (dq *DealQuery) IDsX(ctx context.Context) []int {
	ids, err := dq.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (dq *DealQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, dq.ctx, ent.OpQueryCount)
	if err := dq.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, dq, querierCount[*DealQuery](), dq.inters)
}

// CountX is like Count, but panics if an error occurs.
func (dq *DealQuery) CountX(ctx context.Context) int {
	count, err := dq.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (dq *DealQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, dq.ctx, ent.OpQueryExist)
	switch _, err := dq.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("ent: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (dq *DealQuery) ExistX(ctx context.Context) bool {
	exist, err := dq.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the DealQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (dq *DealQuery) Clone() *DealQuery {
	if dq == nil {
		return nil
	}
	return &DealQuery{
		config:             dq.config,
		ctx:                dq.ctx.Clone(),
		order:              append([]deal.OrderOption{}, dq.order...),
		inters:             append([]Interceptor{}, dq.inters...),
		predicates:         append([]predicate.Deal{}, dq.predicates...),
		withPerson:         dq.withPerson.Clone(),
		withStage:          dq.withStage.Clone(),
		withAttachments:    dq.withAttachments.Clone(),
		withPlans:          dq.withPlans.Clone(),
		withBills:          dq.withBills.Clone(),
		withTracks:         dq.withTracks.Clone(),
		withTasks:          dq.withTasks.Clone(),
		withDealAssignment: dq.withDealAssignment.Clone(),
		withTags:           dq.withTags.Clone(),
		withDeposits:       dq.withDeposits.Clone(),
		withDiscountUsages: dq.withDiscountUsages.Clone(),
		withTagDeal:        dq.withTagDeal.Clone(),
		// clone intermediate query.
		sql:       dq.sql.Clone(),
		path:      dq.path,
		modifiers: append([]func(*sql.Selector){}, dq.modifiers...),
	}
}

// WithPerson tells the query-builder to eager-load the nodes that are connected to
// the "person" edge. The optional arguments are used to configure the query builder of the edge.
func (dq *DealQuery) WithPerson(opts ...func(*PersonQuery)) *DealQuery {
	query := (&PersonClient{config: dq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	dq.withPerson = query
	return dq
}

// WithStage tells the query-builder to eager-load the nodes that are connected to
// the "stage" edge. The optional arguments are used to configure the query builder of the edge.
func (dq *DealQuery) WithStage(opts ...func(*StageQuery)) *DealQuery {
	query := (&StageClient{config: dq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	dq.withStage = query
	return dq
}

// WithAttachments tells the query-builder to eager-load the nodes that are connected to
// the "attachments" edge. The optional arguments are used to configure the query builder of the edge.
func (dq *DealQuery) WithAttachments(opts ...func(*AttachmentQuery)) *DealQuery {
	query := (&AttachmentClient{config: dq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	dq.withAttachments = query
	return dq
}

// WithPlans tells the query-builder to eager-load the nodes that are connected to
// the "plans" edge. The optional arguments are used to configure the query builder of the edge.
func (dq *DealQuery) WithPlans(opts ...func(*InstallmentPlanQuery)) *DealQuery {
	query := (&InstallmentPlanClient{config: dq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	dq.withPlans = query
	return dq
}

// WithBills tells the query-builder to eager-load the nodes that are connected to
// the "bills" edge. The optional arguments are used to configure the query builder of the edge.
func (dq *DealQuery) WithBills(opts ...func(*BillQuery)) *DealQuery {
	query := (&BillClient{config: dq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	dq.withBills = query
	return dq
}

// WithTracks tells the query-builder to eager-load the nodes that are connected to
// the "tracks" edge. The optional arguments are used to configure the query builder of the edge.
func (dq *DealQuery) WithTracks(opts ...func(*TrackQuery)) *DealQuery {
	query := (&TrackClient{config: dq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	dq.withTracks = query
	return dq
}

// WithTasks tells the query-builder to eager-load the nodes that are connected to
// the "tasks" edge. The optional arguments are used to configure the query builder of the edge.
func (dq *DealQuery) WithTasks(opts ...func(*TaskQuery)) *DealQuery {
	query := (&TaskClient{config: dq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	dq.withTasks = query
	return dq
}

// WithDealAssignment tells the query-builder to eager-load the nodes that are connected to
// the "deal_assignment" edge. The optional arguments are used to configure the query builder of the edge.
func (dq *DealQuery) WithDealAssignment(opts ...func(*DealUserQuery)) *DealQuery {
	query := (&DealUserClient{config: dq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	dq.withDealAssignment = query
	return dq
}

// WithTags tells the query-builder to eager-load the nodes that are connected to
// the "tags" edge. The optional arguments are used to configure the query builder of the edge.
func (dq *DealQuery) WithTags(opts ...func(*TagQuery)) *DealQuery {
	query := (&TagClient{config: dq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	dq.withTags = query
	return dq
}

// WithDeposits tells the query-builder to eager-load the nodes that are connected to
// the "deposits" edge. The optional arguments are used to configure the query builder of the edge.
func (dq *DealQuery) WithDeposits(opts ...func(*DepositQuery)) *DealQuery {
	query := (&DepositClient{config: dq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	dq.withDeposits = query
	return dq
}

// WithDiscountUsages tells the query-builder to eager-load the nodes that are connected to
// the "discount_usages" edge. The optional arguments are used to configure the query builder of the edge.
func (dq *DealQuery) WithDiscountUsages(opts ...func(*DiscountUsageQuery)) *DealQuery {
	query := (&DiscountUsageClient{config: dq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	dq.withDiscountUsages = query
	return dq
}

// WithTagDeal tells the query-builder to eager-load the nodes that are connected to
// the "tag_deal" edge. The optional arguments are used to configure the query builder of the edge.
func (dq *DealQuery) WithTagDeal(opts ...func(*TagDealQuery)) *DealQuery {
	query := (&TagDealClient{config: dq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	dq.withTagDeal = query
	return dq
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		DeletedAt time.Time `json:"deleted_at,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.Deal.Query().
//		GroupBy(deal.FieldDeletedAt).
//		Aggregate(ent.Count()).
//		Scan(ctx, &v)
func (dq *DealQuery) GroupBy(field string, fields ...string) *DealGroupBy {
	dq.ctx.Fields = append([]string{field}, fields...)
	grbuild := &DealGroupBy{build: dq}
	grbuild.flds = &dq.ctx.Fields
	grbuild.label = deal.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		DeletedAt time.Time `json:"deleted_at,omitempty"`
//	}
//
//	client.Deal.Query().
//		Select(deal.FieldDeletedAt).
//		Scan(ctx, &v)
func (dq *DealQuery) Select(fields ...string) *DealSelect {
	dq.ctx.Fields = append(dq.ctx.Fields, fields...)
	sbuild := &DealSelect{DealQuery: dq}
	sbuild.label = deal.Label
	sbuild.flds, sbuild.scan = &dq.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a DealSelect configured with the given aggregations.
func (dq *DealQuery) Aggregate(fns ...AggregateFunc) *DealSelect {
	return dq.Select().Aggregate(fns...)
}

func (dq *DealQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range dq.inters {
		if inter == nil {
			return fmt.Errorf("ent: uninitialized interceptor (forgotten import ent/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, dq); err != nil {
				return err
			}
		}
	}
	for _, f := range dq.ctx.Fields {
		if !deal.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
		}
	}
	if dq.path != nil {
		prev, err := dq.path(ctx)
		if err != nil {
			return err
		}
		dq.sql = prev
	}
	return nil
}

func (dq *DealQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*Deal, error) {
	var (
		nodes       = []*Deal{}
		_spec       = dq.querySpec()
		loadedTypes = [12]bool{
			dq.withPerson != nil,
			dq.withStage != nil,
			dq.withAttachments != nil,
			dq.withPlans != nil,
			dq.withBills != nil,
			dq.withTracks != nil,
			dq.withTasks != nil,
			dq.withDealAssignment != nil,
			dq.withTags != nil,
			dq.withDeposits != nil,
			dq.withDiscountUsages != nil,
			dq.withTagDeal != nil,
		}
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*Deal).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &Deal{config: dq.config}
		nodes = append(nodes, node)
		node.Edges.loadedTypes = loadedTypes
		return node.assignValues(columns, values)
	}
	if len(dq.modifiers) > 0 {
		_spec.Modifiers = dq.modifiers
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, dq.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	if query := dq.withPerson; query != nil {
		if err := dq.loadPerson(ctx, query, nodes, nil,
			func(n *Deal, e *Person) { n.Edges.Person = e }); err != nil {
			return nil, err
		}
	}
	if query := dq.withStage; query != nil {
		if err := dq.loadStage(ctx, query, nodes, nil,
			func(n *Deal, e *Stage) { n.Edges.Stage = e }); err != nil {
			return nil, err
		}
	}
	if query := dq.withAttachments; query != nil {
		if err := dq.loadAttachments(ctx, query, nodes,
			func(n *Deal) { n.Edges.Attachments = []*Attachment{} },
			func(n *Deal, e *Attachment) { n.Edges.Attachments = append(n.Edges.Attachments, e) }); err != nil {
			return nil, err
		}
	}
	if query := dq.withPlans; query != nil {
		if err := dq.loadPlans(ctx, query, nodes,
			func(n *Deal) { n.Edges.Plans = []*InstallmentPlan{} },
			func(n *Deal, e *InstallmentPlan) { n.Edges.Plans = append(n.Edges.Plans, e) }); err != nil {
			return nil, err
		}
	}
	if query := dq.withBills; query != nil {
		if err := dq.loadBills(ctx, query, nodes,
			func(n *Deal) { n.Edges.Bills = []*Bill{} },
			func(n *Deal, e *Bill) { n.Edges.Bills = append(n.Edges.Bills, e) }); err != nil {
			return nil, err
		}
	}
	if query := dq.withTracks; query != nil {
		if err := dq.loadTracks(ctx, query, nodes,
			func(n *Deal) { n.Edges.Tracks = []*Track{} },
			func(n *Deal, e *Track) { n.Edges.Tracks = append(n.Edges.Tracks, e) }); err != nil {
			return nil, err
		}
	}
	if query := dq.withTasks; query != nil {
		if err := dq.loadTasks(ctx, query, nodes,
			func(n *Deal) { n.Edges.Tasks = []*Task{} },
			func(n *Deal, e *Task) { n.Edges.Tasks = append(n.Edges.Tasks, e) }); err != nil {
			return nil, err
		}
	}
	if query := dq.withDealAssignment; query != nil {
		if err := dq.loadDealAssignment(ctx, query, nodes,
			func(n *Deal) { n.Edges.DealAssignment = []*DealUser{} },
			func(n *Deal, e *DealUser) { n.Edges.DealAssignment = append(n.Edges.DealAssignment, e) }); err != nil {
			return nil, err
		}
	}
	if query := dq.withTags; query != nil {
		if err := dq.loadTags(ctx, query, nodes,
			func(n *Deal) { n.Edges.Tags = []*Tag{} },
			func(n *Deal, e *Tag) { n.Edges.Tags = append(n.Edges.Tags, e) }); err != nil {
			return nil, err
		}
	}
	if query := dq.withDeposits; query != nil {
		if err := dq.loadDeposits(ctx, query, nodes,
			func(n *Deal) { n.Edges.Deposits = []*Deposit{} },
			func(n *Deal, e *Deposit) { n.Edges.Deposits = append(n.Edges.Deposits, e) }); err != nil {
			return nil, err
		}
	}
	if query := dq.withDiscountUsages; query != nil {
		if err := dq.loadDiscountUsages(ctx, query, nodes,
			func(n *Deal) { n.Edges.DiscountUsages = []*DiscountUsage{} },
			func(n *Deal, e *DiscountUsage) { n.Edges.DiscountUsages = append(n.Edges.DiscountUsages, e) }); err != nil {
			return nil, err
		}
	}
	if query := dq.withTagDeal; query != nil {
		if err := dq.loadTagDeal(ctx, query, nodes,
			func(n *Deal) { n.Edges.TagDeal = []*TagDeal{} },
			func(n *Deal, e *TagDeal) { n.Edges.TagDeal = append(n.Edges.TagDeal, e) }); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

func (dq *DealQuery) loadPerson(ctx context.Context, query *PersonQuery, nodes []*Deal, init func(*Deal), assign func(*Deal, *Person)) error {
	ids := make([]int, 0, len(nodes))
	nodeids := make(map[int][]*Deal)
	for i := range nodes {
		fk := nodes[i].PersonID
		if _, ok := nodeids[fk]; !ok {
			ids = append(ids, fk)
		}
		nodeids[fk] = append(nodeids[fk], nodes[i])
	}
	if len(ids) == 0 {
		return nil
	}
	query.Where(person.IDIn(ids...))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		nodes, ok := nodeids[n.ID]
		if !ok {
			return fmt.Errorf(`unexpected foreign-key "person_id" returned %v`, n.ID)
		}
		for i := range nodes {
			assign(nodes[i], n)
		}
	}
	return nil
}
func (dq *DealQuery) loadStage(ctx context.Context, query *StageQuery, nodes []*Deal, init func(*Deal), assign func(*Deal, *Stage)) error {
	ids := make([]int, 0, len(nodes))
	nodeids := make(map[int][]*Deal)
	for i := range nodes {
		if nodes[i].StageID == nil {
			continue
		}
		fk := *nodes[i].StageID
		if _, ok := nodeids[fk]; !ok {
			ids = append(ids, fk)
		}
		nodeids[fk] = append(nodeids[fk], nodes[i])
	}
	if len(ids) == 0 {
		return nil
	}
	query.Where(stage.IDIn(ids...))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		nodes, ok := nodeids[n.ID]
		if !ok {
			return fmt.Errorf(`unexpected foreign-key "stage_id" returned %v`, n.ID)
		}
		for i := range nodes {
			assign(nodes[i], n)
		}
	}
	return nil
}
func (dq *DealQuery) loadAttachments(ctx context.Context, query *AttachmentQuery, nodes []*Deal, init func(*Deal), assign func(*Deal, *Attachment)) error {
	fks := make([]driver.Value, 0, len(nodes))
	nodeids := make(map[int]*Deal)
	for i := range nodes {
		fks = append(fks, nodes[i].ID)
		nodeids[nodes[i].ID] = nodes[i]
		if init != nil {
			init(nodes[i])
		}
	}
	if len(query.ctx.Fields) > 0 {
		query.ctx.AppendFieldOnce(attachment.FieldDealID)
	}
	query.Where(predicate.Attachment(func(s *sql.Selector) {
		s.Where(sql.InValues(s.C(deal.AttachmentsColumn), fks...))
	}))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		fk := n.DealID
		if fk == nil {
			return fmt.Errorf(`foreign-key "deal_id" is nil for node %v`, n.ID)
		}
		node, ok := nodeids[*fk]
		if !ok {
			return fmt.Errorf(`unexpected referenced foreign-key "deal_id" returned %v for node %v`, *fk, n.ID)
		}
		assign(node, n)
	}
	return nil
}
func (dq *DealQuery) loadPlans(ctx context.Context, query *InstallmentPlanQuery, nodes []*Deal, init func(*Deal), assign func(*Deal, *InstallmentPlan)) error {
	fks := make([]driver.Value, 0, len(nodes))
	nodeids := make(map[int]*Deal)
	for i := range nodes {
		fks = append(fks, nodes[i].ID)
		nodeids[nodes[i].ID] = nodes[i]
		if init != nil {
			init(nodes[i])
		}
	}
	if len(query.ctx.Fields) > 0 {
		query.ctx.AppendFieldOnce(installmentplan.FieldDealID)
	}
	query.Where(predicate.InstallmentPlan(func(s *sql.Selector) {
		s.Where(sql.InValues(s.C(deal.PlansColumn), fks...))
	}))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		fk := n.DealID
		if fk == nil {
			return fmt.Errorf(`foreign-key "deal_id" is nil for node %v`, n.ID)
		}
		node, ok := nodeids[*fk]
		if !ok {
			return fmt.Errorf(`unexpected referenced foreign-key "deal_id" returned %v for node %v`, *fk, n.ID)
		}
		assign(node, n)
	}
	return nil
}
func (dq *DealQuery) loadBills(ctx context.Context, query *BillQuery, nodes []*Deal, init func(*Deal), assign func(*Deal, *Bill)) error {
	fks := make([]driver.Value, 0, len(nodes))
	nodeids := make(map[int]*Deal)
	for i := range nodes {
		fks = append(fks, nodes[i].ID)
		nodeids[nodes[i].ID] = nodes[i]
		if init != nil {
			init(nodes[i])
		}
	}
	if len(query.ctx.Fields) > 0 {
		query.ctx.AppendFieldOnce(bill.FieldDealID)
	}
	query.Where(predicate.Bill(func(s *sql.Selector) {
		s.Where(sql.InValues(s.C(deal.BillsColumn), fks...))
	}))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		fk := n.DealID
		if fk == nil {
			return fmt.Errorf(`foreign-key "deal_id" is nil for node %v`, n.ID)
		}
		node, ok := nodeids[*fk]
		if !ok {
			return fmt.Errorf(`unexpected referenced foreign-key "deal_id" returned %v for node %v`, *fk, n.ID)
		}
		assign(node, n)
	}
	return nil
}
func (dq *DealQuery) loadTracks(ctx context.Context, query *TrackQuery, nodes []*Deal, init func(*Deal), assign func(*Deal, *Track)) error {
	fks := make([]driver.Value, 0, len(nodes))
	nodeids := make(map[int]*Deal)
	for i := range nodes {
		fks = append(fks, nodes[i].ID)
		nodeids[nodes[i].ID] = nodes[i]
		if init != nil {
			init(nodes[i])
		}
	}
	if len(query.ctx.Fields) > 0 {
		query.ctx.AppendFieldOnce(track.FieldDealID)
	}
	query.Where(predicate.Track(func(s *sql.Selector) {
		s.Where(sql.InValues(s.C(deal.TracksColumn), fks...))
	}))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		fk := n.DealID
		if fk == nil {
			return fmt.Errorf(`foreign-key "deal_id" is nil for node %v`, n.ID)
		}
		node, ok := nodeids[*fk]
		if !ok {
			return fmt.Errorf(`unexpected referenced foreign-key "deal_id" returned %v for node %v`, *fk, n.ID)
		}
		assign(node, n)
	}
	return nil
}
func (dq *DealQuery) loadTasks(ctx context.Context, query *TaskQuery, nodes []*Deal, init func(*Deal), assign func(*Deal, *Task)) error {
	fks := make([]driver.Value, 0, len(nodes))
	nodeids := make(map[int]*Deal)
	for i := range nodes {
		fks = append(fks, nodes[i].ID)
		nodeids[nodes[i].ID] = nodes[i]
		if init != nil {
			init(nodes[i])
		}
	}
	if len(query.ctx.Fields) > 0 {
		query.ctx.AppendFieldOnce(task.FieldDealID)
	}
	query.Where(predicate.Task(func(s *sql.Selector) {
		s.Where(sql.InValues(s.C(deal.TasksColumn), fks...))
	}))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		fk := n.DealID
		if fk == nil {
			return fmt.Errorf(`foreign-key "deal_id" is nil for node %v`, n.ID)
		}
		node, ok := nodeids[*fk]
		if !ok {
			return fmt.Errorf(`unexpected referenced foreign-key "deal_id" returned %v for node %v`, *fk, n.ID)
		}
		assign(node, n)
	}
	return nil
}
func (dq *DealQuery) loadDealAssignment(ctx context.Context, query *DealUserQuery, nodes []*Deal, init func(*Deal), assign func(*Deal, *DealUser)) error {
	fks := make([]driver.Value, 0, len(nodes))
	nodeids := make(map[int]*Deal)
	for i := range nodes {
		fks = append(fks, nodes[i].ID)
		nodeids[nodes[i].ID] = nodes[i]
		if init != nil {
			init(nodes[i])
		}
	}
	if len(query.ctx.Fields) > 0 {
		query.ctx.AppendFieldOnce(dealuser.FieldDealID)
	}
	query.Where(predicate.DealUser(func(s *sql.Selector) {
		s.Where(sql.InValues(s.C(deal.DealAssignmentColumn), fks...))
	}))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		fk := n.DealID
		node, ok := nodeids[fk]
		if !ok {
			return fmt.Errorf(`unexpected referenced foreign-key "deal_id" returned %v for node %v`, fk, n.ID)
		}
		assign(node, n)
	}
	return nil
}
func (dq *DealQuery) loadTags(ctx context.Context, query *TagQuery, nodes []*Deal, init func(*Deal), assign func(*Deal, *Tag)) error {
	edgeIDs := make([]driver.Value, len(nodes))
	byID := make(map[int]*Deal)
	nids := make(map[int]map[*Deal]struct{})
	for i, node := range nodes {
		edgeIDs[i] = node.ID
		byID[node.ID] = node
		if init != nil {
			init(node)
		}
	}
	query.Where(func(s *sql.Selector) {
		joinT := sql.Table(deal.TagsTable)
		s.Join(joinT).On(s.C(tag.FieldID), joinT.C(deal.TagsPrimaryKey[1]))
		s.Where(sql.InValues(joinT.C(deal.TagsPrimaryKey[0]), edgeIDs...))
		columns := s.SelectedColumns()
		s.Select(joinT.C(deal.TagsPrimaryKey[0]))
		s.AppendSelect(columns...)
		s.SetDistinct(false)
	})
	if err := query.prepareQuery(ctx); err != nil {
		return err
	}
	qr := QuerierFunc(func(ctx context.Context, q Query) (Value, error) {
		return query.sqlAll(ctx, func(_ context.Context, spec *sqlgraph.QuerySpec) {
			assign := spec.Assign
			values := spec.ScanValues
			spec.ScanValues = func(columns []string) ([]any, error) {
				values, err := values(columns[1:])
				if err != nil {
					return nil, err
				}
				return append([]any{new(sql.NullInt64)}, values...), nil
			}
			spec.Assign = func(columns []string, values []any) error {
				outValue := int(values[0].(*sql.NullInt64).Int64)
				inValue := int(values[1].(*sql.NullInt64).Int64)
				if nids[inValue] == nil {
					nids[inValue] = map[*Deal]struct{}{byID[outValue]: {}}
					return assign(columns[1:], values[1:])
				}
				nids[inValue][byID[outValue]] = struct{}{}
				return nil
			}
		})
	})
	neighbors, err := withInterceptors[[]*Tag](ctx, query, qr, query.inters)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		nodes, ok := nids[n.ID]
		if !ok {
			return fmt.Errorf(`unexpected "tags" node returned %v`, n.ID)
		}
		for kn := range nodes {
			assign(kn, n)
		}
	}
	return nil
}
func (dq *DealQuery) loadDeposits(ctx context.Context, query *DepositQuery, nodes []*Deal, init func(*Deal), assign func(*Deal, *Deposit)) error {
	fks := make([]driver.Value, 0, len(nodes))
	nodeids := make(map[int]*Deal)
	for i := range nodes {
		fks = append(fks, nodes[i].ID)
		nodeids[nodes[i].ID] = nodes[i]
		if init != nil {
			init(nodes[i])
		}
	}
	if len(query.ctx.Fields) > 0 {
		query.ctx.AppendFieldOnce(deposit.FieldDealID)
	}
	query.Where(predicate.Deposit(func(s *sql.Selector) {
		s.Where(sql.InValues(s.C(deal.DepositsColumn), fks...))
	}))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		fk := n.DealID
		node, ok := nodeids[fk]
		if !ok {
			return fmt.Errorf(`unexpected referenced foreign-key "deal_id" returned %v for node %v`, fk, n.ID)
		}
		assign(node, n)
	}
	return nil
}
func (dq *DealQuery) loadDiscountUsages(ctx context.Context, query *DiscountUsageQuery, nodes []*Deal, init func(*Deal), assign func(*Deal, *DiscountUsage)) error {
	fks := make([]driver.Value, 0, len(nodes))
	nodeids := make(map[int]*Deal)
	for i := range nodes {
		fks = append(fks, nodes[i].ID)
		nodeids[nodes[i].ID] = nodes[i]
		if init != nil {
			init(nodes[i])
		}
	}
	if len(query.ctx.Fields) > 0 {
		query.ctx.AppendFieldOnce(discountusage.FieldDealID)
	}
	query.Where(predicate.DiscountUsage(func(s *sql.Selector) {
		s.Where(sql.InValues(s.C(deal.DiscountUsagesColumn), fks...))
	}))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		fk := n.DealID
		if fk == nil {
			return fmt.Errorf(`foreign-key "deal_id" is nil for node %v`, n.ID)
		}
		node, ok := nodeids[*fk]
		if !ok {
			return fmt.Errorf(`unexpected referenced foreign-key "deal_id" returned %v for node %v`, *fk, n.ID)
		}
		assign(node, n)
	}
	return nil
}
func (dq *DealQuery) loadTagDeal(ctx context.Context, query *TagDealQuery, nodes []*Deal, init func(*Deal), assign func(*Deal, *TagDeal)) error {
	fks := make([]driver.Value, 0, len(nodes))
	nodeids := make(map[int]*Deal)
	for i := range nodes {
		fks = append(fks, nodes[i].ID)
		nodeids[nodes[i].ID] = nodes[i]
		if init != nil {
			init(nodes[i])
		}
	}
	if len(query.ctx.Fields) > 0 {
		query.ctx.AppendFieldOnce(tagdeal.FieldDealID)
	}
	query.Where(predicate.TagDeal(func(s *sql.Selector) {
		s.Where(sql.InValues(s.C(deal.TagDealColumn), fks...))
	}))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		fk := n.DealID
		node, ok := nodeids[fk]
		if !ok {
			return fmt.Errorf(`unexpected referenced foreign-key "deal_id" returned %v for node %v`, fk, n.ID)
		}
		assign(node, n)
	}
	return nil
}

func (dq *DealQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := dq.querySpec()
	if len(dq.modifiers) > 0 {
		_spec.Modifiers = dq.modifiers
	}
	_spec.Node.Columns = dq.ctx.Fields
	if len(dq.ctx.Fields) > 0 {
		_spec.Unique = dq.ctx.Unique != nil && *dq.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, dq.driver, _spec)
}

func (dq *DealQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(deal.Table, deal.Columns, sqlgraph.NewFieldSpec(deal.FieldID, field.TypeInt))
	_spec.From = dq.sql
	if unique := dq.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if dq.path != nil {
		_spec.Unique = true
	}
	if fields := dq.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, deal.FieldID)
		for i := range fields {
			if fields[i] != deal.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
		if dq.withPerson != nil {
			_spec.Node.AddColumnOnce(deal.FieldPersonID)
		}
		if dq.withStage != nil {
			_spec.Node.AddColumnOnce(deal.FieldStageID)
		}
	}
	if ps := dq.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := dq.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := dq.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := dq.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (dq *DealQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(dq.driver.Dialect())
	t1 := builder.Table(deal.Table)
	columns := dq.ctx.Fields
	if len(columns) == 0 {
		columns = deal.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if dq.sql != nil {
		selector = dq.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if dq.ctx.Unique != nil && *dq.ctx.Unique {
		selector.Distinct()
	}
	for _, m := range dq.modifiers {
		m(selector)
	}
	for _, p := range dq.predicates {
		p(selector)
	}
	for _, p := range dq.order {
		p(selector)
	}
	if offset := dq.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := dq.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// Modify adds a query modifier for attaching custom logic to queries.
func (dq *DealQuery) Modify(modifiers ...func(s *sql.Selector)) *DealSelect {
	dq.modifiers = append(dq.modifiers, modifiers...)
	return dq.Select()
}

// DealGroupBy is the group-by builder for Deal entities.
type DealGroupBy struct {
	selector
	build *DealQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (dgb *DealGroupBy) Aggregate(fns ...AggregateFunc) *DealGroupBy {
	dgb.fns = append(dgb.fns, fns...)
	return dgb
}

// Scan applies the selector query and scans the result into the given value.
func (dgb *DealGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, dgb.build.ctx, ent.OpQueryGroupBy)
	if err := dgb.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*DealQuery, *DealGroupBy](ctx, dgb.build, dgb, dgb.build.inters, v)
}

func (dgb *DealGroupBy) sqlScan(ctx context.Context, root *DealQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(dgb.fns))
	for _, fn := range dgb.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*dgb.flds)+len(dgb.fns))
		for _, f := range *dgb.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*dgb.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := dgb.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// DealSelect is the builder for selecting fields of Deal entities.
type DealSelect struct {
	*DealQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (ds *DealSelect) Aggregate(fns ...AggregateFunc) *DealSelect {
	ds.fns = append(ds.fns, fns...)
	return ds
}

// Scan applies the selector query and scans the result into the given value.
func (ds *DealSelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, ds.ctx, ent.OpQuerySelect)
	if err := ds.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*DealQuery, *DealSelect](ctx, ds.DealQuery, ds, ds.inters, v)
}

func (ds *DealSelect) sqlScan(ctx context.Context, root *DealQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(ds.fns))
	for _, fn := range ds.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*ds.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := ds.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// Modify adds a query modifier for attaching custom logic to queries.
func (ds *DealSelect) Modify(modifiers ...func(s *sql.Selector)) *DealSelect {
	ds.modifiers = append(ds.modifiers, modifiers...)
	return ds
}
