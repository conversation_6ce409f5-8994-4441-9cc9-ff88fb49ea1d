// Code generated by ent, DO NOT EDIT.

package hook

import (
	"bcare/ent"
	"context"
	"fmt"
)

// The AppointmentFunc type is an adapter to allow the use of ordinary
// function as Appointment mutator.
type AppointmentFunc func(context.Context, *ent.AppointmentMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f AppointmentFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.AppointmentMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.AppointmentMutation", m)
}

// The AttachmentFunc type is an adapter to allow the use of ordinary
// function as Attachment mutator.
type AttachmentFunc func(context.Context, *ent.AttachmentMutation) (ent.Value, error)

// Mu<PERSON> calls f(ctx, m).
func (f AttachmentFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.AttachmentMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.AttachmentMutation", m)
}

// The AttachmentDataFunc type is an adapter to allow the use of ordinary
// function as AttachmentData mutator.
type AttachmentDataFunc func(context.Context, *ent.AttachmentDataMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f AttachmentDataFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.AttachmentDataMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.AttachmentDataMutation", m)
}

// The BillFunc type is an adapter to allow the use of ordinary
// function as Bill mutator.
type BillFunc func(context.Context, *ent.BillMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f BillFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.BillMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.BillMutation", m)
}

// The BillDataFunc type is an adapter to allow the use of ordinary
// function as BillData mutator.
type BillDataFunc func(context.Context, *ent.BillDataMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f BillDataFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.BillDataMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.BillDataMutation", m)
}

// The BillItemFunc type is an adapter to allow the use of ordinary
// function as BillItem mutator.
type BillItemFunc func(context.Context, *ent.BillItemMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f BillItemFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.BillItemMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.BillItemMutation", m)
}

// The BundleFunc type is an adapter to allow the use of ordinary
// function as Bundle mutator.
type BundleFunc func(context.Context, *ent.BundleMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f BundleFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.BundleMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.BundleMutation", m)
}

// The CallFunc type is an adapter to allow the use of ordinary
// function as Call mutator.
type CallFunc func(context.Context, *ent.CallMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f CallFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.CallMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.CallMutation", m)
}

// The CasbinRuleFunc type is an adapter to allow the use of ordinary
// function as CasbinRule mutator.
type CasbinRuleFunc func(context.Context, *ent.CasbinRuleMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f CasbinRuleFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.CasbinRuleMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.CasbinRuleMutation", m)
}

// The DealFunc type is an adapter to allow the use of ordinary
// function as Deal mutator.
type DealFunc func(context.Context, *ent.DealMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f DealFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.DealMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.DealMutation", m)
}

// The DealUserFunc type is an adapter to allow the use of ordinary
// function as DealUser mutator.
type DealUserFunc func(context.Context, *ent.DealUserMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f DealUserFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.DealUserMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.DealUserMutation", m)
}

// The DepartmentFunc type is an adapter to allow the use of ordinary
// function as Department mutator.
type DepartmentFunc func(context.Context, *ent.DepartmentMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f DepartmentFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.DepartmentMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.DepartmentMutation", m)
}

// The DepositFunc type is an adapter to allow the use of ordinary
// function as Deposit mutator.
type DepositFunc func(context.Context, *ent.DepositMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f DepositFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.DepositMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.DepositMutation", m)
}

// The DepositAllocationFunc type is an adapter to allow the use of ordinary
// function as DepositAllocation mutator.
type DepositAllocationFunc func(context.Context, *ent.DepositAllocationMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f DepositAllocationFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.DepositAllocationMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.DepositAllocationMutation", m)
}

// The DepositPaymentFunc type is an adapter to allow the use of ordinary
// function as DepositPayment mutator.
type DepositPaymentFunc func(context.Context, *ent.DepositPaymentMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f DepositPaymentFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.DepositPaymentMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.DepositPaymentMutation", m)
}

// The DiscountFunc type is an adapter to allow the use of ordinary
// function as Discount mutator.
type DiscountFunc func(context.Context, *ent.DiscountMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f DiscountFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.DiscountMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.DiscountMutation", m)
}

// The DiscountUsageFunc type is an adapter to allow the use of ordinary
// function as DiscountUsage mutator.
type DiscountUsageFunc func(context.Context, *ent.DiscountUsageMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f DiscountUsageFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.DiscountUsageMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.DiscountUsageMutation", m)
}

// The FileFunc type is an adapter to allow the use of ordinary
// function as File mutator.
type FileFunc func(context.Context, *ent.FileMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f FileFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.FileMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.FileMutation", m)
}

// The FileUsageFunc type is an adapter to allow the use of ordinary
// function as FileUsage mutator.
type FileUsageFunc func(context.Context, *ent.FileUsageMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f FileUsageFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.FileUsageMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.FileUsageMutation", m)
}

// The InstallmentFunc type is an adapter to allow the use of ordinary
// function as Installment mutator.
type InstallmentFunc func(context.Context, *ent.InstallmentMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f InstallmentFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.InstallmentMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.InstallmentMutation", m)
}

// The InstallmentPlanFunc type is an adapter to allow the use of ordinary
// function as InstallmentPlan mutator.
type InstallmentPlanFunc func(context.Context, *ent.InstallmentPlanMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f InstallmentPlanFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.InstallmentPlanMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.InstallmentPlanMutation", m)
}

// The IssueFunc type is an adapter to allow the use of ordinary
// function as Issue mutator.
type IssueFunc func(context.Context, *ent.IssueMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f IssueFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.IssueMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.IssueMutation", m)
}

// The IssueCommentFunc type is an adapter to allow the use of ordinary
// function as IssueComment mutator.
type IssueCommentFunc func(context.Context, *ent.IssueCommentMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f IssueCommentFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.IssueCommentMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.IssueCommentMutation", m)
}

// The LocalDistrictFunc type is an adapter to allow the use of ordinary
// function as LocalDistrict mutator.
type LocalDistrictFunc func(context.Context, *ent.LocalDistrictMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f LocalDistrictFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.LocalDistrictMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.LocalDistrictMutation", m)
}

// The LocalProvinceFunc type is an adapter to allow the use of ordinary
// function as LocalProvince mutator.
type LocalProvinceFunc func(context.Context, *ent.LocalProvinceMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f LocalProvinceFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.LocalProvinceMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.LocalProvinceMutation", m)
}

// The LocalWardFunc type is an adapter to allow the use of ordinary
// function as LocalWard mutator.
type LocalWardFunc func(context.Context, *ent.LocalWardMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f LocalWardFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.LocalWardMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.LocalWardMutation", m)
}

// The MessageHistoryFunc type is an adapter to allow the use of ordinary
// function as MessageHistory mutator.
type MessageHistoryFunc func(context.Context, *ent.MessageHistoryMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f MessageHistoryFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.MessageHistoryMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.MessageHistoryMutation", m)
}

// The NoteFunc type is an adapter to allow the use of ordinary
// function as Note mutator.
type NoteFunc func(context.Context, *ent.NoteMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f NoteFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.NoteMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.NoteMutation", m)
}

// The OTPFunc type is an adapter to allow the use of ordinary
// function as OTP mutator.
type OTPFunc func(context.Context, *ent.OTPMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f OTPFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.OTPMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.OTPMutation", m)
}

// The OperationFunc type is an adapter to allow the use of ordinary
// function as Operation mutator.
type OperationFunc func(context.Context, *ent.OperationMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f OperationFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.OperationMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.OperationMutation", m)
}

// The OrganizationFunc type is an adapter to allow the use of ordinary
// function as Organization mutator.
type OrganizationFunc func(context.Context, *ent.OrganizationMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f OrganizationFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.OrganizationMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.OrganizationMutation", m)
}

// The PaymentFunc type is an adapter to allow the use of ordinary
// function as Payment mutator.
type PaymentFunc func(context.Context, *ent.PaymentMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f PaymentFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.PaymentMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.PaymentMutation", m)
}

// The PaymentAllocationFunc type is an adapter to allow the use of ordinary
// function as PaymentAllocation mutator.
type PaymentAllocationFunc func(context.Context, *ent.PaymentAllocationMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f PaymentAllocationFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.PaymentAllocationMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.PaymentAllocationMutation", m)
}

// The PersonFunc type is an adapter to allow the use of ordinary
// function as Person mutator.
type PersonFunc func(context.Context, *ent.PersonMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f PersonFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.PersonMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.PersonMutation", m)
}

// The PersonAssignmentFunc type is an adapter to allow the use of ordinary
// function as PersonAssignment mutator.
type PersonAssignmentFunc func(context.Context, *ent.PersonAssignmentMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f PersonAssignmentFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.PersonAssignmentMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.PersonAssignmentMutation", m)
}

// The PersonDataFunc type is an adapter to allow the use of ordinary
// function as PersonData mutator.
type PersonDataFunc func(context.Context, *ent.PersonDataMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f PersonDataFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.PersonDataMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.PersonDataMutation", m)
}

// The PersonReferralFunc type is an adapter to allow the use of ordinary
// function as PersonReferral mutator.
type PersonReferralFunc func(context.Context, *ent.PersonReferralMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f PersonReferralFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.PersonReferralMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.PersonReferralMutation", m)
}

// The PersonStageFunc type is an adapter to allow the use of ordinary
// function as PersonStage mutator.
type PersonStageFunc func(context.Context, *ent.PersonStageMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f PersonStageFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.PersonStageMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.PersonStageMutation", m)
}

// The PipelineFunc type is an adapter to allow the use of ordinary
// function as Pipeline mutator.
type PipelineFunc func(context.Context, *ent.PipelineMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f PipelineFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.PipelineMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.PipelineMutation", m)
}

// The ProductFunc type is an adapter to allow the use of ordinary
// function as Product mutator.
type ProductFunc func(context.Context, *ent.ProductMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f ProductFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.ProductMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.ProductMutation", m)
}

// The ProductOperationFunc type is an adapter to allow the use of ordinary
// function as ProductOperation mutator.
type ProductOperationFunc func(context.Context, *ent.ProductOperationMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f ProductOperationFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.ProductOperationMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.ProductOperationMutation", m)
}

// The ReferralFunc type is an adapter to allow the use of ordinary
// function as Referral mutator.
type ReferralFunc func(context.Context, *ent.ReferralMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f ReferralFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.ReferralMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.ReferralMutation", m)
}

// The ScheduleFunc type is an adapter to allow the use of ordinary
// function as Schedule mutator.
type ScheduleFunc func(context.Context, *ent.ScheduleMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f ScheduleFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.ScheduleMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.ScheduleMutation", m)
}

// The SettingFunc type is an adapter to allow the use of ordinary
// function as Setting mutator.
type SettingFunc func(context.Context, *ent.SettingMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f SettingFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.SettingMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.SettingMutation", m)
}

// The StageFunc type is an adapter to allow the use of ordinary
// function as Stage mutator.
type StageFunc func(context.Context, *ent.StageMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f StageFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.StageMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.StageMutation", m)
}

// The TaskFunc type is an adapter to allow the use of ordinary
// function as Task mutator.
type TaskFunc func(context.Context, *ent.TaskMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f TaskFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.TaskMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.TaskMutation", m)
}

// The TaskAssignmentFunc type is an adapter to allow the use of ordinary
// function as TaskAssignment mutator.
type TaskAssignmentFunc func(context.Context, *ent.TaskAssignmentMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f TaskAssignmentFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.TaskAssignmentMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.TaskAssignmentMutation", m)
}

// The TaskDepartmentFunc type is an adapter to allow the use of ordinary
// function as TaskDepartment mutator.
type TaskDepartmentFunc func(context.Context, *ent.TaskDepartmentMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f TaskDepartmentFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.TaskDepartmentMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.TaskDepartmentMutation", m)
}

// The TaskNoteFunc type is an adapter to allow the use of ordinary
// function as TaskNote mutator.
type TaskNoteFunc func(context.Context, *ent.TaskNoteMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f TaskNoteFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.TaskNoteMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.TaskNoteMutation", m)
}

// The TaskRecurringFunc type is an adapter to allow the use of ordinary
// function as TaskRecurring mutator.
type TaskRecurringFunc func(context.Context, *ent.TaskRecurringMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f TaskRecurringFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.TaskRecurringMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.TaskRecurringMutation", m)
}

// The TermFunc type is an adapter to allow the use of ordinary
// function as Term mutator.
type TermFunc func(context.Context, *ent.TermMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f TermFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.TermMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.TermMutation", m)
}

// The TrackFunc type is an adapter to allow the use of ordinary
// function as Track mutator.
type TrackFunc func(context.Context, *ent.TrackMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f TrackFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.TrackMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.TrackMutation", m)
}

// The UserFunc type is an adapter to allow the use of ordinary
// function as User mutator.
type UserFunc func(context.Context, *ent.UserMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f UserFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.UserMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.UserMutation", m)
}

// The UserDataFunc type is an adapter to allow the use of ordinary
// function as UserData mutator.
type UserDataFunc func(context.Context, *ent.UserDataMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f UserDataFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.UserDataMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.UserDataMutation", m)
}

// Condition is a hook condition function.
type Condition func(context.Context, ent.Mutation) bool

// And groups conditions with the AND operator.
func And(first, second Condition, rest ...Condition) Condition {
	return func(ctx context.Context, m ent.Mutation) bool {
		if !first(ctx, m) || !second(ctx, m) {
			return false
		}
		for _, cond := range rest {
			if !cond(ctx, m) {
				return false
			}
		}
		return true
	}
}

// Or groups conditions with the OR operator.
func Or(first, second Condition, rest ...Condition) Condition {
	return func(ctx context.Context, m ent.Mutation) bool {
		if first(ctx, m) || second(ctx, m) {
			return true
		}
		for _, cond := range rest {
			if cond(ctx, m) {
				return true
			}
		}
		return false
	}
}

// Not negates a given condition.
func Not(cond Condition) Condition {
	return func(ctx context.Context, m ent.Mutation) bool {
		return !cond(ctx, m)
	}
}

// HasOp is a condition testing mutation operation.
func HasOp(op ent.Op) Condition {
	return func(_ context.Context, m ent.Mutation) bool {
		return m.Op().Is(op)
	}
}

// HasAddedFields is a condition validating `.AddedField` on fields.
func HasAddedFields(field string, fields ...string) Condition {
	return func(_ context.Context, m ent.Mutation) bool {
		if _, exists := m.AddedField(field); !exists {
			return false
		}
		for _, field := range fields {
			if _, exists := m.AddedField(field); !exists {
				return false
			}
		}
		return true
	}
}

// HasClearedFields is a condition validating `.FieldCleared` on fields.
func HasClearedFields(field string, fields ...string) Condition {
	return func(_ context.Context, m ent.Mutation) bool {
		if exists := m.FieldCleared(field); !exists {
			return false
		}
		for _, field := range fields {
			if exists := m.FieldCleared(field); !exists {
				return false
			}
		}
		return true
	}
}

// HasFields is a condition validating `.Field` on fields.
func HasFields(field string, fields ...string) Condition {
	return func(_ context.Context, m ent.Mutation) bool {
		if _, exists := m.Field(field); !exists {
			return false
		}
		for _, field := range fields {
			if _, exists := m.Field(field); !exists {
				return false
			}
		}
		return true
	}
}

// If executes the given hook under condition.
//
//	hook.If(ComputeAverage, And(HasFields(...), HasAddedFields(...)))
func If(hk ent.Hook, cond Condition) ent.Hook {
	return func(next ent.Mutator) ent.Mutator {
		return ent.MutateFunc(func(ctx context.Context, m ent.Mutation) (ent.Value, error) {
			if cond(ctx, m) {
				return hk(next).Mutate(ctx, m)
			}
			return next.Mutate(ctx, m)
		})
	}
}

// On executes the given hook only for the given operation.
//
//	hook.On(Log, ent.Delete|ent.Create)
func On(hk ent.Hook, op ent.Op) ent.Hook {
	return If(hk, HasOp(op))
}

// Unless skips the given hook only for the given operation.
//
//	hook.Unless(Log, ent.Update|ent.UpdateOne)
func Unless(hk ent.Hook, op ent.Op) ent.Hook {
	return If(hk, Not(HasOp(op)))
}

// FixedError is a hook returning a fixed error.
func FixedError(err error) ent.Hook {
	return func(ent.Mutator) ent.Mutator {
		return ent.MutateFunc(func(context.Context, ent.Mutation) (ent.Value, error) {
			return nil, err
		})
	}
}

// Reject returns a hook that rejects all operations that match op.
//
//	func (T) Hooks() []ent.Hook {
//		return []ent.Hook{
//			Reject(ent.Delete|ent.Update),
//		}
//	}
func Reject(op ent.Op) ent.Hook {
	hk := FixedError(fmt.Errorf("%s operation is not allowed", op))
	return On(hk, op)
}

// Chain acts as a list of hooks and is effectively immutable.
// Once created, it will always hold the same set of hooks in the same order.
type Chain struct {
	hooks []ent.Hook
}

// NewChain creates a new chain of hooks.
func NewChain(hooks ...ent.Hook) Chain {
	return Chain{append([]ent.Hook(nil), hooks...)}
}

// Hook chains the list of hooks and returns the final hook.
func (c Chain) Hook() ent.Hook {
	return func(mutator ent.Mutator) ent.Mutator {
		for i := len(c.hooks) - 1; i >= 0; i-- {
			mutator = c.hooks[i](mutator)
		}
		return mutator
	}
}

// Append extends a chain, adding the specified hook
// as the last ones in the mutation flow.
func (c Chain) Append(hooks ...ent.Hook) Chain {
	newHooks := make([]ent.Hook, 0, len(c.hooks)+len(hooks))
	newHooks = append(newHooks, c.hooks...)
	newHooks = append(newHooks, hooks...)
	return Chain{newHooks}
}

// Extend extends a chain, adding the specified chain
// as the last ones in the mutation flow.
func (c Chain) Extend(chain Chain) Chain {
	return c.Append(chain.hooks...)
}
