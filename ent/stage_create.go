// Code generated by ent, DO NOT EDIT.

package ent

import (
	"bcare/ent/deal"
	"bcare/ent/pipeline"
	"bcare/ent/stage"
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// StageCreate is the builder for creating a Stage entity.
type StageCreate struct {
	config
	mutation *StageMutation
	hooks    []Hook
}

// SetDeletedAt sets the "deleted_at" field.
func (sc *StageCreate) SetDeletedAt(t time.Time) *StageCreate {
	sc.mutation.SetDeletedAt(t)
	return sc
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (sc *StageCreate) SetNillableDeletedAt(t *time.Time) *StageCreate {
	if t != nil {
		sc.SetDeletedAt(*t)
	}
	return sc
}

// SetStatus sets the "status" field.
func (sc *StageCreate) SetStatus(i int8) *StageCreate {
	sc.mutation.SetStatus(i)
	return sc
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (sc *StageCreate) SetNillableStatus(i *int8) *StageCreate {
	if i != nil {
		sc.SetStatus(*i)
	}
	return sc
}

// SetVersion sets the "version" field.
func (sc *StageCreate) SetVersion(i int) *StageCreate {
	sc.mutation.SetVersion(i)
	return sc
}

// SetNillableVersion sets the "version" field if the given value is not nil.
func (sc *StageCreate) SetNillableVersion(i *int) *StageCreate {
	if i != nil {
		sc.SetVersion(*i)
	}
	return sc
}

// SetCreatedAt sets the "created_at" field.
func (sc *StageCreate) SetCreatedAt(t time.Time) *StageCreate {
	sc.mutation.SetCreatedAt(t)
	return sc
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (sc *StageCreate) SetNillableCreatedAt(t *time.Time) *StageCreate {
	if t != nil {
		sc.SetCreatedAt(*t)
	}
	return sc
}

// SetUpdatedAt sets the "updated_at" field.
func (sc *StageCreate) SetUpdatedAt(t time.Time) *StageCreate {
	sc.mutation.SetUpdatedAt(t)
	return sc
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (sc *StageCreate) SetNillableUpdatedAt(t *time.Time) *StageCreate {
	if t != nil {
		sc.SetUpdatedAt(*t)
	}
	return sc
}

// SetName sets the "name" field.
func (sc *StageCreate) SetName(s string) *StageCreate {
	sc.mutation.SetName(s)
	return sc
}

// SetPipelineID sets the "pipeline_id" field.
func (sc *StageCreate) SetPipelineID(i int) *StageCreate {
	sc.mutation.SetPipelineID(i)
	return sc
}

// SetNillablePipelineID sets the "pipeline_id" field if the given value is not nil.
func (sc *StageCreate) SetNillablePipelineID(i *int) *StageCreate {
	if i != nil {
		sc.SetPipelineID(*i)
	}
	return sc
}

// SetOrderNumber sets the "order_number" field.
func (sc *StageCreate) SetOrderNumber(i int) *StageCreate {
	sc.mutation.SetOrderNumber(i)
	return sc
}

// SetNillableOrderNumber sets the "order_number" field if the given value is not nil.
func (sc *StageCreate) SetNillableOrderNumber(i *int) *StageCreate {
	if i != nil {
		sc.SetOrderNumber(*i)
	}
	return sc
}

// SetParentStageID sets the "parent_stage_id" field.
func (sc *StageCreate) SetParentStageID(i int) *StageCreate {
	sc.mutation.SetParentStageID(i)
	return sc
}

// SetNillableParentStageID sets the "parent_stage_id" field if the given value is not nil.
func (sc *StageCreate) SetNillableParentStageID(i *int) *StageCreate {
	if i != nil {
		sc.SetParentStageID(*i)
	}
	return sc
}

// SetMeta sets the "meta" field.
func (sc *StageCreate) SetMeta(s string) *StageCreate {
	sc.mutation.SetMeta(s)
	return sc
}

// SetNillableMeta sets the "meta" field if the given value is not nil.
func (sc *StageCreate) SetNillableMeta(s *string) *StageCreate {
	if s != nil {
		sc.SetMeta(*s)
	}
	return sc
}

// SetParentID sets the "parent" edge to the Stage entity by ID.
func (sc *StageCreate) SetParentID(id int) *StageCreate {
	sc.mutation.SetParentID(id)
	return sc
}

// SetNillableParentID sets the "parent" edge to the Stage entity by ID if the given value is not nil.
func (sc *StageCreate) SetNillableParentID(id *int) *StageCreate {
	if id != nil {
		sc = sc.SetParentID(*id)
	}
	return sc
}

// SetParent sets the "parent" edge to the Stage entity.
func (sc *StageCreate) SetParent(s *Stage) *StageCreate {
	return sc.SetParentID(s.ID)
}

// AddChildIDs adds the "children" edge to the Stage entity by IDs.
func (sc *StageCreate) AddChildIDs(ids ...int) *StageCreate {
	sc.mutation.AddChildIDs(ids...)
	return sc
}

// AddChildren adds the "children" edges to the Stage entity.
func (sc *StageCreate) AddChildren(s ...*Stage) *StageCreate {
	ids := make([]int, len(s))
	for i := range s {
		ids[i] = s[i].ID
	}
	return sc.AddChildIDs(ids...)
}

// AddDealIDs adds the "deals" edge to the Deal entity by IDs.
func (sc *StageCreate) AddDealIDs(ids ...int) *StageCreate {
	sc.mutation.AddDealIDs(ids...)
	return sc
}

// AddDeals adds the "deals" edges to the Deal entity.
func (sc *StageCreate) AddDeals(d ...*Deal) *StageCreate {
	ids := make([]int, len(d))
	for i := range d {
		ids[i] = d[i].ID
	}
	return sc.AddDealIDs(ids...)
}

// SetPipeline sets the "pipeline" edge to the Pipeline entity.
func (sc *StageCreate) SetPipeline(p *Pipeline) *StageCreate {
	return sc.SetPipelineID(p.ID)
}

// Mutation returns the StageMutation object of the builder.
func (sc *StageCreate) Mutation() *StageMutation {
	return sc.mutation
}

// Save creates the Stage in the database.
func (sc *StageCreate) Save(ctx context.Context) (*Stage, error) {
	if err := sc.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, sc.sqlSave, sc.mutation, sc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (sc *StageCreate) SaveX(ctx context.Context) *Stage {
	v, err := sc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (sc *StageCreate) Exec(ctx context.Context) error {
	_, err := sc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (sc *StageCreate) ExecX(ctx context.Context) {
	if err := sc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (sc *StageCreate) defaults() error {
	if _, ok := sc.mutation.Status(); !ok {
		v := stage.DefaultStatus
		sc.mutation.SetStatus(v)
	}
	if _, ok := sc.mutation.Version(); !ok {
		v := stage.DefaultVersion
		sc.mutation.SetVersion(v)
	}
	if _, ok := sc.mutation.CreatedAt(); !ok {
		if stage.DefaultCreatedAt == nil {
			return fmt.Errorf("ent: uninitialized stage.DefaultCreatedAt (forgotten import ent/runtime?)")
		}
		v := stage.DefaultCreatedAt()
		sc.mutation.SetCreatedAt(v)
	}
	if _, ok := sc.mutation.UpdatedAt(); !ok {
		if stage.DefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized stage.DefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := stage.DefaultUpdatedAt()
		sc.mutation.SetUpdatedAt(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (sc *StageCreate) check() error {
	if _, ok := sc.mutation.Status(); !ok {
		return &ValidationError{Name: "status", err: errors.New(`ent: missing required field "Stage.status"`)}
	}
	if _, ok := sc.mutation.Version(); !ok {
		return &ValidationError{Name: "version", err: errors.New(`ent: missing required field "Stage.version"`)}
	}
	if _, ok := sc.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "Stage.created_at"`)}
	}
	if _, ok := sc.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "Stage.updated_at"`)}
	}
	if _, ok := sc.mutation.Name(); !ok {
		return &ValidationError{Name: "name", err: errors.New(`ent: missing required field "Stage.name"`)}
	}
	return nil
}

func (sc *StageCreate) sqlSave(ctx context.Context) (*Stage, error) {
	if err := sc.check(); err != nil {
		return nil, err
	}
	_node, _spec := sc.createSpec()
	if err := sqlgraph.CreateNode(ctx, sc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	id := _spec.ID.Value.(int64)
	_node.ID = int(id)
	sc.mutation.id = &_node.ID
	sc.mutation.done = true
	return _node, nil
}

func (sc *StageCreate) createSpec() (*Stage, *sqlgraph.CreateSpec) {
	var (
		_node = &Stage{config: sc.config}
		_spec = sqlgraph.NewCreateSpec(stage.Table, sqlgraph.NewFieldSpec(stage.FieldID, field.TypeInt))
	)
	if value, ok := sc.mutation.DeletedAt(); ok {
		_spec.SetField(stage.FieldDeletedAt, field.TypeTime, value)
		_node.DeletedAt = value
	}
	if value, ok := sc.mutation.Status(); ok {
		_spec.SetField(stage.FieldStatus, field.TypeInt8, value)
		_node.Status = value
	}
	if value, ok := sc.mutation.Version(); ok {
		_spec.SetField(stage.FieldVersion, field.TypeInt, value)
		_node.Version = value
	}
	if value, ok := sc.mutation.CreatedAt(); ok {
		_spec.SetField(stage.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := sc.mutation.UpdatedAt(); ok {
		_spec.SetField(stage.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	if value, ok := sc.mutation.Name(); ok {
		_spec.SetField(stage.FieldName, field.TypeString, value)
		_node.Name = value
	}
	if value, ok := sc.mutation.OrderNumber(); ok {
		_spec.SetField(stage.FieldOrderNumber, field.TypeInt, value)
		_node.OrderNumber = value
	}
	if value, ok := sc.mutation.Meta(); ok {
		_spec.SetField(stage.FieldMeta, field.TypeString, value)
		_node.Meta = value
	}
	if nodes := sc.mutation.ParentIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   stage.ParentTable,
			Columns: []string{stage.ParentColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(stage.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.ParentStageID = &nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := sc.mutation.ChildrenIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   stage.ChildrenTable,
			Columns: []string{stage.ChildrenColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(stage.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := sc.mutation.DealsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   stage.DealsTable,
			Columns: []string{stage.DealsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(deal.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := sc.mutation.PipelineIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   stage.PipelineTable,
			Columns: []string{stage.PipelineColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(pipeline.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.PipelineID = nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	return _node, _spec
}

// StageCreateBulk is the builder for creating many Stage entities in bulk.
type StageCreateBulk struct {
	config
	err      error
	builders []*StageCreate
}

// Save creates the Stage entities in the database.
func (scb *StageCreateBulk) Save(ctx context.Context) ([]*Stage, error) {
	if scb.err != nil {
		return nil, scb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(scb.builders))
	nodes := make([]*Stage, len(scb.builders))
	mutators := make([]Mutator, len(scb.builders))
	for i := range scb.builders {
		func(i int, root context.Context) {
			builder := scb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*StageMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, scb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, scb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = int(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, scb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (scb *StageCreateBulk) SaveX(ctx context.Context) []*Stage {
	v, err := scb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (scb *StageCreateBulk) Exec(ctx context.Context) error {
	_, err := scb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (scb *StageCreateBulk) ExecX(ctx context.Context) {
	if err := scb.Exec(ctx); err != nil {
		panic(err)
	}
}
