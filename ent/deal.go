// Code generated by ent, DO NOT EDIT.

package ent

import (
	"bcare/ent/deal"
	"bcare/ent/person"
	"bcare/ent/stage"
	"bcare/ent/types"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

// Deal is the model entity for the Deal schema.
type Deal struct {
	config `json:"-"`
	// ID of the ent.
	ID int `json:"id,omitempty"`
	// DeletedAt holds the value of the "deleted_at" field.
	DeletedAt time.Time `json:"deleted_at,omitempty"`
	// Status holds the value of the "status" field.
	Status int8 `json:"status,omitempty"`
	// Version holds the value of the "version" field.
	Version int `json:"version,omitempty"`
	// CreatedAt holds the value of the "created_at" field.
	CreatedAt time.Time `json:"created_at,omitempty"`
	// UpdatedAt holds the value of the "updated_at" field.
	UpdatedAt time.Time `json:"updated_at,omitempty"`
	// PersonID holds the value of the "person_id" field.
	PersonID int `json:"person_id,omitempty"`
	// ParentDealID holds the value of the "parent_deal_id" field.
	ParentDealID *int `json:"parent_deal_id,omitempty"`
	// TotalAmount holds the value of the "total_amount" field.
	TotalAmount float64 `json:"total_amount,omitempty"`
	// StageID holds the value of the "stage_id" field.
	StageID *int `json:"stage_id,omitempty"`
	// StageHistory holds the value of the "stage_history" field.
	StageHistory []types.StageHistoryEntry `json:"stage_history,omitempty"`
	// Name holds the value of the "name" field.
	Name string `json:"name,omitempty"`
	// State holds the value of the "state" field.
	State deal.State `json:"state,omitempty"`
	// Edges holds the relations/edges for other nodes in the graph.
	// The values are being populated by the DealQuery when eager-loading is set.
	Edges        DealEdges `json:"-"`
	selectValues sql.SelectValues
}

// DealEdges holds the relations/edges for other nodes in the graph.
type DealEdges struct {
	// Person holds the value of the person edge.
	Person *Person `json:"person,omitempty"`
	// Stage holds the value of the stage edge.
	Stage *Stage `json:"stage,omitempty"`
	// Attachments holds the value of the attachments edge.
	Attachments []*Attachment `json:"attachments,omitempty"`
	// Plans holds the value of the plans edge.
	Plans []*InstallmentPlan `json:"plans,omitempty"`
	// Bills holds the value of the bills edge.
	Bills []*Bill `json:"bills,omitempty"`
	// Tracks holds the value of the tracks edge.
	Tracks []*Track `json:"tracks,omitempty"`
	// Tasks holds the value of the tasks edge.
	Tasks []*Task `json:"tasks,omitempty"`
	// DealAssignment holds the value of the deal_assignment edge.
	DealAssignment []*DealUser `json:"deal_assignment,omitempty"`
	// Tags holds the value of the tags edge.
	Tags []*Tag `json:"tags,omitempty"`
	// Deposits holds the value of the deposits edge.
	Deposits []*Deposit `json:"deposits,omitempty"`
	// DiscountUsages holds the value of the discount_usages edge.
	DiscountUsages []*DiscountUsage `json:"discount_usages,omitempty"`
	// TagDeal holds the value of the tag_deal edge.
	TagDeal []*TagDeal `json:"tag_deal,omitempty"`
	// loadedTypes holds the information for reporting if a
	// type was loaded (or requested) in eager-loading or not.
	loadedTypes [12]bool
}

// PersonOrErr returns the Person value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e DealEdges) PersonOrErr() (*Person, error) {
	if e.Person != nil {
		return e.Person, nil
	} else if e.loadedTypes[0] {
		return nil, &NotFoundError{label: person.Label}
	}
	return nil, &NotLoadedError{edge: "person"}
}

// StageOrErr returns the Stage value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e DealEdges) StageOrErr() (*Stage, error) {
	if e.Stage != nil {
		return e.Stage, nil
	} else if e.loadedTypes[1] {
		return nil, &NotFoundError{label: stage.Label}
	}
	return nil, &NotLoadedError{edge: "stage"}
}

// AttachmentsOrErr returns the Attachments value or an error if the edge
// was not loaded in eager-loading.
func (e DealEdges) AttachmentsOrErr() ([]*Attachment, error) {
	if e.loadedTypes[2] {
		return e.Attachments, nil
	}
	return nil, &NotLoadedError{edge: "attachments"}
}

// PlansOrErr returns the Plans value or an error if the edge
// was not loaded in eager-loading.
func (e DealEdges) PlansOrErr() ([]*InstallmentPlan, error) {
	if e.loadedTypes[3] {
		return e.Plans, nil
	}
	return nil, &NotLoadedError{edge: "plans"}
}

// BillsOrErr returns the Bills value or an error if the edge
// was not loaded in eager-loading.
func (e DealEdges) BillsOrErr() ([]*Bill, error) {
	if e.loadedTypes[4] {
		return e.Bills, nil
	}
	return nil, &NotLoadedError{edge: "bills"}
}

// TracksOrErr returns the Tracks value or an error if the edge
// was not loaded in eager-loading.
func (e DealEdges) TracksOrErr() ([]*Track, error) {
	if e.loadedTypes[5] {
		return e.Tracks, nil
	}
	return nil, &NotLoadedError{edge: "tracks"}
}

// TasksOrErr returns the Tasks value or an error if the edge
// was not loaded in eager-loading.
func (e DealEdges) TasksOrErr() ([]*Task, error) {
	if e.loadedTypes[6] {
		return e.Tasks, nil
	}
	return nil, &NotLoadedError{edge: "tasks"}
}

// DealAssignmentOrErr returns the DealAssignment value or an error if the edge
// was not loaded in eager-loading.
func (e DealEdges) DealAssignmentOrErr() ([]*DealUser, error) {
	if e.loadedTypes[7] {
		return e.DealAssignment, nil
	}
	return nil, &NotLoadedError{edge: "deal_assignment"}
}

// TagsOrErr returns the Tags value or an error if the edge
// was not loaded in eager-loading.
func (e DealEdges) TagsOrErr() ([]*Tag, error) {
	if e.loadedTypes[8] {
		return e.Tags, nil
	}
	return nil, &NotLoadedError{edge: "tags"}
}

// DepositsOrErr returns the Deposits value or an error if the edge
// was not loaded in eager-loading.
func (e DealEdges) DepositsOrErr() ([]*Deposit, error) {
	if e.loadedTypes[9] {
		return e.Deposits, nil
	}
	return nil, &NotLoadedError{edge: "deposits"}
}

// DiscountUsagesOrErr returns the DiscountUsages value or an error if the edge
// was not loaded in eager-loading.
func (e DealEdges) DiscountUsagesOrErr() ([]*DiscountUsage, error) {
	if e.loadedTypes[10] {
		return e.DiscountUsages, nil
	}
	return nil, &NotLoadedError{edge: "discount_usages"}
}

// TagDealOrErr returns the TagDeal value or an error if the edge
// was not loaded in eager-loading.
func (e DealEdges) TagDealOrErr() ([]*TagDeal, error) {
	if e.loadedTypes[11] {
		return e.TagDeal, nil
	}
	return nil, &NotLoadedError{edge: "tag_deal"}
}

// scanValues returns the types for scanning values from sql.Rows.
func (*Deal) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case deal.FieldStageHistory:
			values[i] = new([]byte)
		case deal.FieldTotalAmount:
			values[i] = new(sql.NullFloat64)
		case deal.FieldID, deal.FieldStatus, deal.FieldVersion, deal.FieldPersonID, deal.FieldParentDealID, deal.FieldStageID:
			values[i] = new(sql.NullInt64)
		case deal.FieldName, deal.FieldState:
			values[i] = new(sql.NullString)
		case deal.FieldDeletedAt, deal.FieldCreatedAt, deal.FieldUpdatedAt:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the Deal fields.
func (d *Deal) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case deal.FieldID:
			value, ok := values[i].(*sql.NullInt64)
			if !ok {
				return fmt.Errorf("unexpected type %T for field id", value)
			}
			d.ID = int(value.Int64)
		case deal.FieldDeletedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field deleted_at", values[i])
			} else if value.Valid {
				d.DeletedAt = value.Time
			}
		case deal.FieldStatus:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field status", values[i])
			} else if value.Valid {
				d.Status = int8(value.Int64)
			}
		case deal.FieldVersion:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field version", values[i])
			} else if value.Valid {
				d.Version = int(value.Int64)
			}
		case deal.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				d.CreatedAt = value.Time
			}
		case deal.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				d.UpdatedAt = value.Time
			}
		case deal.FieldPersonID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field person_id", values[i])
			} else if value.Valid {
				d.PersonID = int(value.Int64)
			}
		case deal.FieldParentDealID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field parent_deal_id", values[i])
			} else if value.Valid {
				d.ParentDealID = new(int)
				*d.ParentDealID = int(value.Int64)
			}
		case deal.FieldTotalAmount:
			if value, ok := values[i].(*sql.NullFloat64); !ok {
				return fmt.Errorf("unexpected type %T for field total_amount", values[i])
			} else if value.Valid {
				d.TotalAmount = value.Float64
			}
		case deal.FieldStageID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field stage_id", values[i])
			} else if value.Valid {
				d.StageID = new(int)
				*d.StageID = int(value.Int64)
			}
		case deal.FieldStageHistory:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field stage_history", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &d.StageHistory); err != nil {
					return fmt.Errorf("unmarshal field stage_history: %w", err)
				}
			}
		case deal.FieldName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field name", values[i])
			} else if value.Valid {
				d.Name = value.String
			}
		case deal.FieldState:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field state", values[i])
			} else if value.Valid {
				d.State = deal.State(value.String)
			}
		default:
			d.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the Deal.
// This includes values selected through modifiers, order, etc.
func (d *Deal) Value(name string) (ent.Value, error) {
	return d.selectValues.Get(name)
}

// QueryPerson queries the "person" edge of the Deal entity.
func (d *Deal) QueryPerson() *PersonQuery {
	return NewDealClient(d.config).QueryPerson(d)
}

// QueryStage queries the "stage" edge of the Deal entity.
func (d *Deal) QueryStage() *StageQuery {
	return NewDealClient(d.config).QueryStage(d)
}

// QueryAttachments queries the "attachments" edge of the Deal entity.
func (d *Deal) QueryAttachments() *AttachmentQuery {
	return NewDealClient(d.config).QueryAttachments(d)
}

// QueryPlans queries the "plans" edge of the Deal entity.
func (d *Deal) QueryPlans() *InstallmentPlanQuery {
	return NewDealClient(d.config).QueryPlans(d)
}

// QueryBills queries the "bills" edge of the Deal entity.
func (d *Deal) QueryBills() *BillQuery {
	return NewDealClient(d.config).QueryBills(d)
}

// QueryTracks queries the "tracks" edge of the Deal entity.
func (d *Deal) QueryTracks() *TrackQuery {
	return NewDealClient(d.config).QueryTracks(d)
}

// QueryTasks queries the "tasks" edge of the Deal entity.
func (d *Deal) QueryTasks() *TaskQuery {
	return NewDealClient(d.config).QueryTasks(d)
}

// QueryDealAssignment queries the "deal_assignment" edge of the Deal entity.
func (d *Deal) QueryDealAssignment() *DealUserQuery {
	return NewDealClient(d.config).QueryDealAssignment(d)
}

// QueryTags queries the "tags" edge of the Deal entity.
func (d *Deal) QueryTags() *TagQuery {
	return NewDealClient(d.config).QueryTags(d)
}

// QueryDeposits queries the "deposits" edge of the Deal entity.
func (d *Deal) QueryDeposits() *DepositQuery {
	return NewDealClient(d.config).QueryDeposits(d)
}

// QueryDiscountUsages queries the "discount_usages" edge of the Deal entity.
func (d *Deal) QueryDiscountUsages() *DiscountUsageQuery {
	return NewDealClient(d.config).QueryDiscountUsages(d)
}

// QueryTagDeal queries the "tag_deal" edge of the Deal entity.
func (d *Deal) QueryTagDeal() *TagDealQuery {
	return NewDealClient(d.config).QueryTagDeal(d)
}

// Update returns a builder for updating this Deal.
// Note that you need to call Deal.Unwrap() before calling this method if this Deal
// was returned from a transaction, and the transaction was committed or rolled back.
func (d *Deal) Update() *DealUpdateOne {
	return NewDealClient(d.config).UpdateOne(d)
}

// Unwrap unwraps the Deal entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (d *Deal) Unwrap() *Deal {
	_tx, ok := d.config.driver.(*txDriver)
	if !ok {
		panic("ent: Deal is not a transactional entity")
	}
	d.config.driver = _tx.drv
	return d
}

// String implements the fmt.Stringer.
func (d *Deal) String() string {
	var builder strings.Builder
	builder.WriteString("Deal(")
	builder.WriteString(fmt.Sprintf("id=%v, ", d.ID))
	builder.WriteString("deleted_at=")
	builder.WriteString(d.DeletedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("status=")
	builder.WriteString(fmt.Sprintf("%v", d.Status))
	builder.WriteString(", ")
	builder.WriteString("version=")
	builder.WriteString(fmt.Sprintf("%v", d.Version))
	builder.WriteString(", ")
	builder.WriteString("created_at=")
	builder.WriteString(d.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(d.UpdatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("person_id=")
	builder.WriteString(fmt.Sprintf("%v", d.PersonID))
	builder.WriteString(", ")
	if v := d.ParentDealID; v != nil {
		builder.WriteString("parent_deal_id=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteString(", ")
	builder.WriteString("total_amount=")
	builder.WriteString(fmt.Sprintf("%v", d.TotalAmount))
	builder.WriteString(", ")
	if v := d.StageID; v != nil {
		builder.WriteString("stage_id=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteString(", ")
	builder.WriteString("stage_history=")
	builder.WriteString(fmt.Sprintf("%v", d.StageHistory))
	builder.WriteString(", ")
	builder.WriteString("name=")
	builder.WriteString(d.Name)
	builder.WriteString(", ")
	builder.WriteString("state=")
	builder.WriteString(fmt.Sprintf("%v", d.State))
	builder.WriteByte(')')
	return builder.String()
}

// MarshalJSON implements the json.Marshaler interface.
func (d *Deal) MarshalJSON() ([]byte, error) {
	type Alias Deal
	return json.Marshal(&struct {
		*Alias
		DealEdges
	}{
		Alias:     (*Alias)(d),
		DealEdges: d.Edges,
	})
}

// MarshalSimpleTime
func (d *Deal) MarshalSimpleTime() ([]byte, error) {
	type Alias Deal
	return json.Marshal(&struct {
		*Alias
		DealEdges
		DeletedAt string `json:"deleted_at,omitempty"`
		CreatedAt string `json:"created_at,omitempty"`
		UpdatedAt string `json:"updated_at,omitempty"`
	}{
		Alias:     (*Alias)(d),
		DealEdges: d.Edges,
		DeletedAt: d.DeletedAt.Format("15:04 02/01/2006"),
		CreatedAt: d.CreatedAt.Format("15:04 02/01/2006"),
		UpdatedAt: d.UpdatedAt.Format("15:04 02/01/2006"),
	})
}

// Deals is a parsable slice of Deal.
type Deals []*Deal
