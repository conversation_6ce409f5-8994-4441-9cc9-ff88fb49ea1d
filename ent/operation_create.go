// Code generated by ent, DO NOT EDIT.

package ent

import (
	"bcare/ent/operation"
	"bcare/ent/product"
	"bcare/ent/productoperation"
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// OperationCreate is the builder for creating a Operation entity.
type OperationCreate struct {
	config
	mutation *OperationMutation
	hooks    []Hook
}

// SetDeletedAt sets the "deleted_at" field.
func (oc *OperationCreate) SetDeletedAt(t time.Time) *OperationCreate {
	oc.mutation.SetDeletedAt(t)
	return oc
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (oc *OperationCreate) SetNillableDeletedAt(t *time.Time) *OperationCreate {
	if t != nil {
		oc.SetDeletedAt(*t)
	}
	return oc
}

// SetStatus sets the "status" field.
func (oc *OperationCreate) SetStatus(i int8) *OperationCreate {
	oc.mutation.SetStatus(i)
	return oc
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (oc *OperationCreate) SetNillableStatus(i *int8) *OperationCreate {
	if i != nil {
		oc.SetStatus(*i)
	}
	return oc
}

// SetVersion sets the "version" field.
func (oc *OperationCreate) SetVersion(i int) *OperationCreate {
	oc.mutation.SetVersion(i)
	return oc
}

// SetNillableVersion sets the "version" field if the given value is not nil.
func (oc *OperationCreate) SetNillableVersion(i *int) *OperationCreate {
	if i != nil {
		oc.SetVersion(*i)
	}
	return oc
}

// SetCreatedAt sets the "created_at" field.
func (oc *OperationCreate) SetCreatedAt(t time.Time) *OperationCreate {
	oc.mutation.SetCreatedAt(t)
	return oc
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (oc *OperationCreate) SetNillableCreatedAt(t *time.Time) *OperationCreate {
	if t != nil {
		oc.SetCreatedAt(*t)
	}
	return oc
}

// SetUpdatedAt sets the "updated_at" field.
func (oc *OperationCreate) SetUpdatedAt(t time.Time) *OperationCreate {
	oc.mutation.SetUpdatedAt(t)
	return oc
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (oc *OperationCreate) SetNillableUpdatedAt(t *time.Time) *OperationCreate {
	if t != nil {
		oc.SetUpdatedAt(*t)
	}
	return oc
}

// SetName sets the "name" field.
func (oc *OperationCreate) SetName(s string) *OperationCreate {
	oc.mutation.SetName(s)
	return oc
}

// SetDescription sets the "description" field.
func (oc *OperationCreate) SetDescription(s string) *OperationCreate {
	oc.mutation.SetDescription(s)
	return oc
}

// SetNillableDescription sets the "description" field if the given value is not nil.
func (oc *OperationCreate) SetNillableDescription(s *string) *OperationCreate {
	if s != nil {
		oc.SetDescription(*s)
	}
	return oc
}

// SetDuration sets the "duration" field.
func (oc *OperationCreate) SetDuration(i int) *OperationCreate {
	oc.mutation.SetDuration(i)
	return oc
}

// SetNillableDuration sets the "duration" field if the given value is not nil.
func (oc *OperationCreate) SetNillableDuration(i *int) *OperationCreate {
	if i != nil {
		oc.SetDuration(*i)
	}
	return oc
}

// SetGroup sets the "group" field.
func (oc *OperationCreate) SetGroup(s []string) *OperationCreate {
	oc.mutation.SetGroup(s)
	return oc
}

// AddAssignedProductIDs adds the "assigned_product" edge to the Product entity by IDs.
func (oc *OperationCreate) AddAssignedProductIDs(ids ...int) *OperationCreate {
	oc.mutation.AddAssignedProductIDs(ids...)
	return oc
}

// AddAssignedProduct adds the "assigned_product" edges to the Product entity.
func (oc *OperationCreate) AddAssignedProduct(p ...*Product) *OperationCreate {
	ids := make([]int, len(p))
	for i := range p {
		ids[i] = p[i].ID
	}
	return oc.AddAssignedProductIDs(ids...)
}

// AddProductOperationIDs adds the "product_operation" edge to the ProductOperation entity by IDs.
func (oc *OperationCreate) AddProductOperationIDs(ids ...int) *OperationCreate {
	oc.mutation.AddProductOperationIDs(ids...)
	return oc
}

// AddProductOperation adds the "product_operation" edges to the ProductOperation entity.
func (oc *OperationCreate) AddProductOperation(p ...*ProductOperation) *OperationCreate {
	ids := make([]int, len(p))
	for i := range p {
		ids[i] = p[i].ID
	}
	return oc.AddProductOperationIDs(ids...)
}

// Mutation returns the OperationMutation object of the builder.
func (oc *OperationCreate) Mutation() *OperationMutation {
	return oc.mutation
}

// Save creates the Operation in the database.
func (oc *OperationCreate) Save(ctx context.Context) (*Operation, error) {
	if err := oc.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, oc.sqlSave, oc.mutation, oc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (oc *OperationCreate) SaveX(ctx context.Context) *Operation {
	v, err := oc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (oc *OperationCreate) Exec(ctx context.Context) error {
	_, err := oc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (oc *OperationCreate) ExecX(ctx context.Context) {
	if err := oc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (oc *OperationCreate) defaults() error {
	if _, ok := oc.mutation.Status(); !ok {
		v := operation.DefaultStatus
		oc.mutation.SetStatus(v)
	}
	if _, ok := oc.mutation.Version(); !ok {
		v := operation.DefaultVersion
		oc.mutation.SetVersion(v)
	}
	if _, ok := oc.mutation.CreatedAt(); !ok {
		if operation.DefaultCreatedAt == nil {
			return fmt.Errorf("ent: uninitialized operation.DefaultCreatedAt (forgotten import ent/runtime?)")
		}
		v := operation.DefaultCreatedAt()
		oc.mutation.SetCreatedAt(v)
	}
	if _, ok := oc.mutation.UpdatedAt(); !ok {
		if operation.DefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized operation.DefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := operation.DefaultUpdatedAt()
		oc.mutation.SetUpdatedAt(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (oc *OperationCreate) check() error {
	if _, ok := oc.mutation.Status(); !ok {
		return &ValidationError{Name: "status", err: errors.New(`ent: missing required field "Operation.status"`)}
	}
	if _, ok := oc.mutation.Version(); !ok {
		return &ValidationError{Name: "version", err: errors.New(`ent: missing required field "Operation.version"`)}
	}
	if _, ok := oc.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "Operation.created_at"`)}
	}
	if _, ok := oc.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "Operation.updated_at"`)}
	}
	if _, ok := oc.mutation.Name(); !ok {
		return &ValidationError{Name: "name", err: errors.New(`ent: missing required field "Operation.name"`)}
	}
	return nil
}

func (oc *OperationCreate) sqlSave(ctx context.Context) (*Operation, error) {
	if err := oc.check(); err != nil {
		return nil, err
	}
	_node, _spec := oc.createSpec()
	if err := sqlgraph.CreateNode(ctx, oc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	id := _spec.ID.Value.(int64)
	_node.ID = int(id)
	oc.mutation.id = &_node.ID
	oc.mutation.done = true
	return _node, nil
}

func (oc *OperationCreate) createSpec() (*Operation, *sqlgraph.CreateSpec) {
	var (
		_node = &Operation{config: oc.config}
		_spec = sqlgraph.NewCreateSpec(operation.Table, sqlgraph.NewFieldSpec(operation.FieldID, field.TypeInt))
	)
	if value, ok := oc.mutation.DeletedAt(); ok {
		_spec.SetField(operation.FieldDeletedAt, field.TypeTime, value)
		_node.DeletedAt = value
	}
	if value, ok := oc.mutation.Status(); ok {
		_spec.SetField(operation.FieldStatus, field.TypeInt8, value)
		_node.Status = value
	}
	if value, ok := oc.mutation.Version(); ok {
		_spec.SetField(operation.FieldVersion, field.TypeInt, value)
		_node.Version = value
	}
	if value, ok := oc.mutation.CreatedAt(); ok {
		_spec.SetField(operation.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := oc.mutation.UpdatedAt(); ok {
		_spec.SetField(operation.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	if value, ok := oc.mutation.Name(); ok {
		_spec.SetField(operation.FieldName, field.TypeString, value)
		_node.Name = value
	}
	if value, ok := oc.mutation.Description(); ok {
		_spec.SetField(operation.FieldDescription, field.TypeString, value)
		_node.Description = value
	}
	if value, ok := oc.mutation.Duration(); ok {
		_spec.SetField(operation.FieldDuration, field.TypeInt, value)
		_node.Duration = value
	}
	if value, ok := oc.mutation.Group(); ok {
		_spec.SetField(operation.FieldGroup, field.TypeJSON, value)
		_node.Group = value
	}
	if nodes := oc.mutation.AssignedProductIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: true,
			Table:   operation.AssignedProductTable,
			Columns: operation.AssignedProductPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(product.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		createE := &ProductOperationCreate{config: oc.config, mutation: newProductOperationMutation(oc.config, OpCreate)}
		_ = createE.defaults()
		_, specE := createE.createSpec()
		edge.Target.Fields = specE.Fields
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := oc.mutation.ProductOperationIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   operation.ProductOperationTable,
			Columns: []string{operation.ProductOperationColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(productoperation.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	return _node, _spec
}

// OperationCreateBulk is the builder for creating many Operation entities in bulk.
type OperationCreateBulk struct {
	config
	err      error
	builders []*OperationCreate
}

// Save creates the Operation entities in the database.
func (ocb *OperationCreateBulk) Save(ctx context.Context) ([]*Operation, error) {
	if ocb.err != nil {
		return nil, ocb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(ocb.builders))
	nodes := make([]*Operation, len(ocb.builders))
	mutators := make([]Mutator, len(ocb.builders))
	for i := range ocb.builders {
		func(i int, root context.Context) {
			builder := ocb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*OperationMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, ocb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, ocb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = int(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, ocb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (ocb *OperationCreateBulk) SaveX(ctx context.Context) []*Operation {
	v, err := ocb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (ocb *OperationCreateBulk) Exec(ctx context.Context) error {
	_, err := ocb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (ocb *OperationCreateBulk) ExecX(ctx context.Context) {
	if err := ocb.Exec(ctx); err != nil {
		panic(err)
	}
}
