// Code generated by ent, DO NOT EDIT.

package intercept

import (
	"context"
	"fmt"

	"bcare/ent"
	"bcare/ent/activityview"
	"bcare/ent/appointment"
	"bcare/ent/appointmenthistory"
	"bcare/ent/attachment"
	"bcare/ent/attachmentdata"
	"bcare/ent/attachmentoperationreportview"
	"bcare/ent/bill"
	"bcare/ent/billdata"
	"bcare/ent/billitem"
	"bcare/ent/bundle"
	"bcare/ent/call"
	"bcare/ent/casbinrule"
	"bcare/ent/cashflow"
	"bcare/ent/cashflowitem"
	"bcare/ent/cashflownote"
	"bcare/ent/dbsandentistdboappointmenttime"
	"bcare/ent/dbsandentistdbobill"
	"bcare/ent/dbsandentistdbobillitem"
	"bcare/ent/dbsandentistdbobillitemdetail"
	"bcare/ent/dbsandentistdbobillpayment"
	"bcare/ent/dbsandentistdbocustomer"
	"bcare/ent/deal"
	"bcare/ent/dealhistory"
	"bcare/ent/dealstagehistory"
	"bcare/ent/dealstagehistoryview"
	"bcare/ent/dealuser"
	"bcare/ent/dealuserrating"
	"bcare/ent/department"
	"bcare/ent/deposit"
	"bcare/ent/depositallocation"
	"bcare/ent/depositpayment"
	"bcare/ent/discount"
	"bcare/ent/discountusage"
	"bcare/ent/entityhistory"
	"bcare/ent/exportjob"
	"bcare/ent/file"
	"bcare/ent/fileusage"
	"bcare/ent/formsubmission"
	"bcare/ent/installment"
	"bcare/ent/installmentplan"
	"bcare/ent/issue"
	"bcare/ent/issuecomment"
	"bcare/ent/localdistrict"
	"bcare/ent/localprovince"
	"bcare/ent/localward"
	"bcare/ent/material"
	"bcare/ent/materialusage"
	"bcare/ent/messagehistory"
	"bcare/ent/newtracksreportview"
	"bcare/ent/note"
	"bcare/ent/notification"
	"bcare/ent/operation"
	"bcare/ent/operationmaterial"
	"bcare/ent/organization"
	"bcare/ent/otp"
	"bcare/ent/payment"
	"bcare/ent/paymentallocation"
	"bcare/ent/paymentreportdetailview"
	"bcare/ent/paymentreportview"
	"bcare/ent/person"
	"bcare/ent/personassignment"
	"bcare/ent/persondata"
	"bcare/ent/personhistory"
	"bcare/ent/personqueryview"
	"bcare/ent/personreferral"
	"bcare/ent/persontimelineview"
	"bcare/ent/phoneviewhistory"
	"bcare/ent/pipeline"
	"bcare/ent/predicate"
	"bcare/ent/product"
	"bcare/ent/productoperation"
	"bcare/ent/referral"
	"bcare/ent/schedule"
	"bcare/ent/setting"
	"bcare/ent/stage"
	"bcare/ent/tag"
	"bcare/ent/tagdeal"
	"bcare/ent/tagperson"
	"bcare/ent/task"
	"bcare/ent/taskassignment"
	"bcare/ent/taskassignmentview"
	"bcare/ent/taskdepartment"
	"bcare/ent/taskhistory"
	"bcare/ent/tasknote"
	"bcare/ent/taskrecurring"
	"bcare/ent/taskserialview"
	"bcare/ent/term"
	"bcare/ent/track"
	"bcare/ent/unifiedhistoryview"
	"bcare/ent/user"
	"bcare/ent/userdata"

	"entgo.io/ent/dialect/sql"
)

// The Query interface represents an operation that queries a graph.
// By using this interface, users can write generic code that manipulates
// query builders of different types.
type Query interface {
	// Type returns the string representation of the query type.
	Type() string
	// Limit the number of records to be returned by this query.
	Limit(int)
	// Offset to start from.
	Offset(int)
	// Unique configures the query builder to filter duplicate records.
	Unique(bool)
	// Order specifies how the records should be ordered.
	Order(...func(*sql.Selector))
	// WhereP appends storage-level predicates to the query builder. Using this method, users
	// can use type-assertion to append predicates that do not depend on any generated package.
	WhereP(...func(*sql.Selector))
}

// The Func type is an adapter that allows ordinary functions to be used as interceptors.
// Unlike traversal functions, interceptors are skipped during graph traversals. Note that the
// implementation of Func is different from the one defined in entgo.io/ent.InterceptFunc.
type Func func(context.Context, Query) error

// Intercept calls f(ctx, q) and then applied the next Querier.
func (f Func) Intercept(next ent.Querier) ent.Querier {
	return ent.QuerierFunc(func(ctx context.Context, q ent.Query) (ent.Value, error) {
		query, err := NewQuery(q)
		if err != nil {
			return nil, err
		}
		if err := f(ctx, query); err != nil {
			return nil, err
		}
		return next.Query(ctx, q)
	})
}

// The TraverseFunc type is an adapter to allow the use of ordinary function as Traverser.
// If f is a function with the appropriate signature, TraverseFunc(f) is a Traverser that calls f.
type TraverseFunc func(context.Context, Query) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseFunc) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseFunc) Traverse(ctx context.Context, q ent.Query) error {
	query, err := NewQuery(q)
	if err != nil {
		return err
	}
	return f(ctx, query)
}

// The ActivityViewFunc type is an adapter to allow the use of ordinary function as a Querier.
type ActivityViewFunc func(context.Context, *ent.ActivityViewQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f ActivityViewFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.ActivityViewQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.ActivityViewQuery", q)
}

// The TraverseActivityView type is an adapter to allow the use of ordinary function as Traverser.
type TraverseActivityView func(context.Context, *ent.ActivityViewQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseActivityView) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseActivityView) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.ActivityViewQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.ActivityViewQuery", q)
}

// The AppointmentFunc type is an adapter to allow the use of ordinary function as a Querier.
type AppointmentFunc func(context.Context, *ent.AppointmentQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f AppointmentFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.AppointmentQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.AppointmentQuery", q)
}

// The TraverseAppointment type is an adapter to allow the use of ordinary function as Traverser.
type TraverseAppointment func(context.Context, *ent.AppointmentQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseAppointment) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseAppointment) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.AppointmentQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.AppointmentQuery", q)
}

// The AppointmentHistoryFunc type is an adapter to allow the use of ordinary function as a Querier.
type AppointmentHistoryFunc func(context.Context, *ent.AppointmentHistoryQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f AppointmentHistoryFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.AppointmentHistoryQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.AppointmentHistoryQuery", q)
}

// The TraverseAppointmentHistory type is an adapter to allow the use of ordinary function as Traverser.
type TraverseAppointmentHistory func(context.Context, *ent.AppointmentHistoryQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseAppointmentHistory) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseAppointmentHistory) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.AppointmentHistoryQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.AppointmentHistoryQuery", q)
}

// The AttachmentFunc type is an adapter to allow the use of ordinary function as a Querier.
type AttachmentFunc func(context.Context, *ent.AttachmentQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f AttachmentFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.AttachmentQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.AttachmentQuery", q)
}

// The TraverseAttachment type is an adapter to allow the use of ordinary function as Traverser.
type TraverseAttachment func(context.Context, *ent.AttachmentQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseAttachment) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseAttachment) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.AttachmentQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.AttachmentQuery", q)
}

// The AttachmentDataFunc type is an adapter to allow the use of ordinary function as a Querier.
type AttachmentDataFunc func(context.Context, *ent.AttachmentDataQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f AttachmentDataFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.AttachmentDataQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.AttachmentDataQuery", q)
}

// The TraverseAttachmentData type is an adapter to allow the use of ordinary function as Traverser.
type TraverseAttachmentData func(context.Context, *ent.AttachmentDataQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseAttachmentData) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseAttachmentData) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.AttachmentDataQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.AttachmentDataQuery", q)
}

// The AttachmentOperationReportViewFunc type is an adapter to allow the use of ordinary function as a Querier.
type AttachmentOperationReportViewFunc func(context.Context, *ent.AttachmentOperationReportViewQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f AttachmentOperationReportViewFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.AttachmentOperationReportViewQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.AttachmentOperationReportViewQuery", q)
}

// The TraverseAttachmentOperationReportView type is an adapter to allow the use of ordinary function as Traverser.
type TraverseAttachmentOperationReportView func(context.Context, *ent.AttachmentOperationReportViewQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseAttachmentOperationReportView) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseAttachmentOperationReportView) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.AttachmentOperationReportViewQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.AttachmentOperationReportViewQuery", q)
}

// The BillFunc type is an adapter to allow the use of ordinary function as a Querier.
type BillFunc func(context.Context, *ent.BillQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f BillFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.BillQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.BillQuery", q)
}

// The TraverseBill type is an adapter to allow the use of ordinary function as Traverser.
type TraverseBill func(context.Context, *ent.BillQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseBill) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseBill) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.BillQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.BillQuery", q)
}

// The BillDataFunc type is an adapter to allow the use of ordinary function as a Querier.
type BillDataFunc func(context.Context, *ent.BillDataQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f BillDataFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.BillDataQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.BillDataQuery", q)
}

// The TraverseBillData type is an adapter to allow the use of ordinary function as Traverser.
type TraverseBillData func(context.Context, *ent.BillDataQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseBillData) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseBillData) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.BillDataQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.BillDataQuery", q)
}

// The BillItemFunc type is an adapter to allow the use of ordinary function as a Querier.
type BillItemFunc func(context.Context, *ent.BillItemQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f BillItemFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.BillItemQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.BillItemQuery", q)
}

// The TraverseBillItem type is an adapter to allow the use of ordinary function as Traverser.
type TraverseBillItem func(context.Context, *ent.BillItemQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseBillItem) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseBillItem) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.BillItemQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.BillItemQuery", q)
}

// The BundleFunc type is an adapter to allow the use of ordinary function as a Querier.
type BundleFunc func(context.Context, *ent.BundleQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f BundleFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.BundleQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.BundleQuery", q)
}

// The TraverseBundle type is an adapter to allow the use of ordinary function as Traverser.
type TraverseBundle func(context.Context, *ent.BundleQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseBundle) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseBundle) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.BundleQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.BundleQuery", q)
}

// The CallFunc type is an adapter to allow the use of ordinary function as a Querier.
type CallFunc func(context.Context, *ent.CallQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f CallFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.CallQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.CallQuery", q)
}

// The TraverseCall type is an adapter to allow the use of ordinary function as Traverser.
type TraverseCall func(context.Context, *ent.CallQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseCall) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseCall) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.CallQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.CallQuery", q)
}

// The CasbinRuleFunc type is an adapter to allow the use of ordinary function as a Querier.
type CasbinRuleFunc func(context.Context, *ent.CasbinRuleQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f CasbinRuleFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.CasbinRuleQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.CasbinRuleQuery", q)
}

// The TraverseCasbinRule type is an adapter to allow the use of ordinary function as Traverser.
type TraverseCasbinRule func(context.Context, *ent.CasbinRuleQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseCasbinRule) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseCasbinRule) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.CasbinRuleQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.CasbinRuleQuery", q)
}

// The CashFlowFunc type is an adapter to allow the use of ordinary function as a Querier.
type CashFlowFunc func(context.Context, *ent.CashFlowQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f CashFlowFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.CashFlowQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.CashFlowQuery", q)
}

// The TraverseCashFlow type is an adapter to allow the use of ordinary function as Traverser.
type TraverseCashFlow func(context.Context, *ent.CashFlowQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseCashFlow) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseCashFlow) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.CashFlowQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.CashFlowQuery", q)
}

// The CashFlowItemFunc type is an adapter to allow the use of ordinary function as a Querier.
type CashFlowItemFunc func(context.Context, *ent.CashFlowItemQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f CashFlowItemFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.CashFlowItemQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.CashFlowItemQuery", q)
}

// The TraverseCashFlowItem type is an adapter to allow the use of ordinary function as Traverser.
type TraverseCashFlowItem func(context.Context, *ent.CashFlowItemQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseCashFlowItem) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseCashFlowItem) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.CashFlowItemQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.CashFlowItemQuery", q)
}

// The CashFlowNoteFunc type is an adapter to allow the use of ordinary function as a Querier.
type CashFlowNoteFunc func(context.Context, *ent.CashFlowNoteQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f CashFlowNoteFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.CashFlowNoteQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.CashFlowNoteQuery", q)
}

// The TraverseCashFlowNote type is an adapter to allow the use of ordinary function as Traverser.
type TraverseCashFlowNote func(context.Context, *ent.CashFlowNoteQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseCashFlowNote) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseCashFlowNote) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.CashFlowNoteQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.CashFlowNoteQuery", q)
}

// The DbsandentistDboAppointmentTimeFunc type is an adapter to allow the use of ordinary function as a Querier.
type DbsandentistDboAppointmentTimeFunc func(context.Context, *ent.DbsandentistDboAppointmentTimeQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f DbsandentistDboAppointmentTimeFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.DbsandentistDboAppointmentTimeQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.DbsandentistDboAppointmentTimeQuery", q)
}

// The TraverseDbsandentistDboAppointmentTime type is an adapter to allow the use of ordinary function as Traverser.
type TraverseDbsandentistDboAppointmentTime func(context.Context, *ent.DbsandentistDboAppointmentTimeQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseDbsandentistDboAppointmentTime) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseDbsandentistDboAppointmentTime) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.DbsandentistDboAppointmentTimeQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.DbsandentistDboAppointmentTimeQuery", q)
}

// The DbsandentistDboBillFunc type is an adapter to allow the use of ordinary function as a Querier.
type DbsandentistDboBillFunc func(context.Context, *ent.DbsandentistDboBillQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f DbsandentistDboBillFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.DbsandentistDboBillQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.DbsandentistDboBillQuery", q)
}

// The TraverseDbsandentistDboBill type is an adapter to allow the use of ordinary function as Traverser.
type TraverseDbsandentistDboBill func(context.Context, *ent.DbsandentistDboBillQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseDbsandentistDboBill) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseDbsandentistDboBill) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.DbsandentistDboBillQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.DbsandentistDboBillQuery", q)
}

// The DbsandentistDboBillItemFunc type is an adapter to allow the use of ordinary function as a Querier.
type DbsandentistDboBillItemFunc func(context.Context, *ent.DbsandentistDboBillItemQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f DbsandentistDboBillItemFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.DbsandentistDboBillItemQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.DbsandentistDboBillItemQuery", q)
}

// The TraverseDbsandentistDboBillItem type is an adapter to allow the use of ordinary function as Traverser.
type TraverseDbsandentistDboBillItem func(context.Context, *ent.DbsandentistDboBillItemQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseDbsandentistDboBillItem) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseDbsandentistDboBillItem) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.DbsandentistDboBillItemQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.DbsandentistDboBillItemQuery", q)
}

// The DbsandentistDboBillItemDetailFunc type is an adapter to allow the use of ordinary function as a Querier.
type DbsandentistDboBillItemDetailFunc func(context.Context, *ent.DbsandentistDboBillItemDetailQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f DbsandentistDboBillItemDetailFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.DbsandentistDboBillItemDetailQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.DbsandentistDboBillItemDetailQuery", q)
}

// The TraverseDbsandentistDboBillItemDetail type is an adapter to allow the use of ordinary function as Traverser.
type TraverseDbsandentistDboBillItemDetail func(context.Context, *ent.DbsandentistDboBillItemDetailQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseDbsandentistDboBillItemDetail) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseDbsandentistDboBillItemDetail) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.DbsandentistDboBillItemDetailQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.DbsandentistDboBillItemDetailQuery", q)
}

// The DbsandentistDboBillPaymentFunc type is an adapter to allow the use of ordinary function as a Querier.
type DbsandentistDboBillPaymentFunc func(context.Context, *ent.DbsandentistDboBillPaymentQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f DbsandentistDboBillPaymentFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.DbsandentistDboBillPaymentQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.DbsandentistDboBillPaymentQuery", q)
}

// The TraverseDbsandentistDboBillPayment type is an adapter to allow the use of ordinary function as Traverser.
type TraverseDbsandentistDboBillPayment func(context.Context, *ent.DbsandentistDboBillPaymentQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseDbsandentistDboBillPayment) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseDbsandentistDboBillPayment) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.DbsandentistDboBillPaymentQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.DbsandentistDboBillPaymentQuery", q)
}

// The DbsandentistDboCustomerFunc type is an adapter to allow the use of ordinary function as a Querier.
type DbsandentistDboCustomerFunc func(context.Context, *ent.DbsandentistDboCustomerQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f DbsandentistDboCustomerFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.DbsandentistDboCustomerQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.DbsandentistDboCustomerQuery", q)
}

// The TraverseDbsandentistDboCustomer type is an adapter to allow the use of ordinary function as Traverser.
type TraverseDbsandentistDboCustomer func(context.Context, *ent.DbsandentistDboCustomerQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseDbsandentistDboCustomer) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseDbsandentistDboCustomer) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.DbsandentistDboCustomerQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.DbsandentistDboCustomerQuery", q)
}

// The DealFunc type is an adapter to allow the use of ordinary function as a Querier.
type DealFunc func(context.Context, *ent.DealQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f DealFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.DealQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.DealQuery", q)
}

// The TraverseDeal type is an adapter to allow the use of ordinary function as Traverser.
type TraverseDeal func(context.Context, *ent.DealQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseDeal) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseDeal) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.DealQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.DealQuery", q)
}

// The DealHistoryFunc type is an adapter to allow the use of ordinary function as a Querier.
type DealHistoryFunc func(context.Context, *ent.DealHistoryQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f DealHistoryFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.DealHistoryQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.DealHistoryQuery", q)
}

// The TraverseDealHistory type is an adapter to allow the use of ordinary function as Traverser.
type TraverseDealHistory func(context.Context, *ent.DealHistoryQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseDealHistory) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseDealHistory) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.DealHistoryQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.DealHistoryQuery", q)
}

// The DealStageHistoryFunc type is an adapter to allow the use of ordinary function as a Querier.
type DealStageHistoryFunc func(context.Context, *ent.DealStageHistoryQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f DealStageHistoryFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.DealStageHistoryQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.DealStageHistoryQuery", q)
}

// The TraverseDealStageHistory type is an adapter to allow the use of ordinary function as Traverser.
type TraverseDealStageHistory func(context.Context, *ent.DealStageHistoryQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseDealStageHistory) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseDealStageHistory) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.DealStageHistoryQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.DealStageHistoryQuery", q)
}

// The DealStageHistoryViewFunc type is an adapter to allow the use of ordinary function as a Querier.
type DealStageHistoryViewFunc func(context.Context, *ent.DealStageHistoryViewQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f DealStageHistoryViewFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.DealStageHistoryViewQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.DealStageHistoryViewQuery", q)
}

// The TraverseDealStageHistoryView type is an adapter to allow the use of ordinary function as Traverser.
type TraverseDealStageHistoryView func(context.Context, *ent.DealStageHistoryViewQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseDealStageHistoryView) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseDealStageHistoryView) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.DealStageHistoryViewQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.DealStageHistoryViewQuery", q)
}

// The DealUserFunc type is an adapter to allow the use of ordinary function as a Querier.
type DealUserFunc func(context.Context, *ent.DealUserQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f DealUserFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.DealUserQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.DealUserQuery", q)
}

// The TraverseDealUser type is an adapter to allow the use of ordinary function as Traverser.
type TraverseDealUser func(context.Context, *ent.DealUserQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseDealUser) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseDealUser) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.DealUserQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.DealUserQuery", q)
}

// The DealUserRatingFunc type is an adapter to allow the use of ordinary function as a Querier.
type DealUserRatingFunc func(context.Context, *ent.DealUserRatingQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f DealUserRatingFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.DealUserRatingQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.DealUserRatingQuery", q)
}

// The TraverseDealUserRating type is an adapter to allow the use of ordinary function as Traverser.
type TraverseDealUserRating func(context.Context, *ent.DealUserRatingQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseDealUserRating) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseDealUserRating) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.DealUserRatingQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.DealUserRatingQuery", q)
}

// The DepartmentFunc type is an adapter to allow the use of ordinary function as a Querier.
type DepartmentFunc func(context.Context, *ent.DepartmentQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f DepartmentFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.DepartmentQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.DepartmentQuery", q)
}

// The TraverseDepartment type is an adapter to allow the use of ordinary function as Traverser.
type TraverseDepartment func(context.Context, *ent.DepartmentQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseDepartment) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseDepartment) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.DepartmentQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.DepartmentQuery", q)
}

// The DepositFunc type is an adapter to allow the use of ordinary function as a Querier.
type DepositFunc func(context.Context, *ent.DepositQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f DepositFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.DepositQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.DepositQuery", q)
}

// The TraverseDeposit type is an adapter to allow the use of ordinary function as Traverser.
type TraverseDeposit func(context.Context, *ent.DepositQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseDeposit) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseDeposit) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.DepositQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.DepositQuery", q)
}

// The DepositAllocationFunc type is an adapter to allow the use of ordinary function as a Querier.
type DepositAllocationFunc func(context.Context, *ent.DepositAllocationQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f DepositAllocationFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.DepositAllocationQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.DepositAllocationQuery", q)
}

// The TraverseDepositAllocation type is an adapter to allow the use of ordinary function as Traverser.
type TraverseDepositAllocation func(context.Context, *ent.DepositAllocationQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseDepositAllocation) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseDepositAllocation) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.DepositAllocationQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.DepositAllocationQuery", q)
}

// The DepositPaymentFunc type is an adapter to allow the use of ordinary function as a Querier.
type DepositPaymentFunc func(context.Context, *ent.DepositPaymentQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f DepositPaymentFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.DepositPaymentQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.DepositPaymentQuery", q)
}

// The TraverseDepositPayment type is an adapter to allow the use of ordinary function as Traverser.
type TraverseDepositPayment func(context.Context, *ent.DepositPaymentQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseDepositPayment) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseDepositPayment) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.DepositPaymentQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.DepositPaymentQuery", q)
}

// The DiscountFunc type is an adapter to allow the use of ordinary function as a Querier.
type DiscountFunc func(context.Context, *ent.DiscountQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f DiscountFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.DiscountQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.DiscountQuery", q)
}

// The TraverseDiscount type is an adapter to allow the use of ordinary function as Traverser.
type TraverseDiscount func(context.Context, *ent.DiscountQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseDiscount) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseDiscount) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.DiscountQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.DiscountQuery", q)
}

// The DiscountUsageFunc type is an adapter to allow the use of ordinary function as a Querier.
type DiscountUsageFunc func(context.Context, *ent.DiscountUsageQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f DiscountUsageFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.DiscountUsageQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.DiscountUsageQuery", q)
}

// The TraverseDiscountUsage type is an adapter to allow the use of ordinary function as Traverser.
type TraverseDiscountUsage func(context.Context, *ent.DiscountUsageQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseDiscountUsage) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseDiscountUsage) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.DiscountUsageQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.DiscountUsageQuery", q)
}

// The EntityHistoryFunc type is an adapter to allow the use of ordinary function as a Querier.
type EntityHistoryFunc func(context.Context, *ent.EntityHistoryQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f EntityHistoryFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.EntityHistoryQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.EntityHistoryQuery", q)
}

// The TraverseEntityHistory type is an adapter to allow the use of ordinary function as Traverser.
type TraverseEntityHistory func(context.Context, *ent.EntityHistoryQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseEntityHistory) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseEntityHistory) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.EntityHistoryQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.EntityHistoryQuery", q)
}

// The ExportJobFunc type is an adapter to allow the use of ordinary function as a Querier.
type ExportJobFunc func(context.Context, *ent.ExportJobQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f ExportJobFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.ExportJobQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.ExportJobQuery", q)
}

// The TraverseExportJob type is an adapter to allow the use of ordinary function as Traverser.
type TraverseExportJob func(context.Context, *ent.ExportJobQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseExportJob) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseExportJob) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.ExportJobQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.ExportJobQuery", q)
}

// The FileFunc type is an adapter to allow the use of ordinary function as a Querier.
type FileFunc func(context.Context, *ent.FileQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f FileFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.FileQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.FileQuery", q)
}

// The TraverseFile type is an adapter to allow the use of ordinary function as Traverser.
type TraverseFile func(context.Context, *ent.FileQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseFile) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseFile) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.FileQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.FileQuery", q)
}

// The FileUsageFunc type is an adapter to allow the use of ordinary function as a Querier.
type FileUsageFunc func(context.Context, *ent.FileUsageQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f FileUsageFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.FileUsageQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.FileUsageQuery", q)
}

// The TraverseFileUsage type is an adapter to allow the use of ordinary function as Traverser.
type TraverseFileUsage func(context.Context, *ent.FileUsageQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseFileUsage) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseFileUsage) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.FileUsageQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.FileUsageQuery", q)
}

// The FormSubmissionFunc type is an adapter to allow the use of ordinary function as a Querier.
type FormSubmissionFunc func(context.Context, *ent.FormSubmissionQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f FormSubmissionFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.FormSubmissionQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.FormSubmissionQuery", q)
}

// The TraverseFormSubmission type is an adapter to allow the use of ordinary function as Traverser.
type TraverseFormSubmission func(context.Context, *ent.FormSubmissionQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseFormSubmission) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseFormSubmission) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.FormSubmissionQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.FormSubmissionQuery", q)
}

// The InstallmentFunc type is an adapter to allow the use of ordinary function as a Querier.
type InstallmentFunc func(context.Context, *ent.InstallmentQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f InstallmentFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.InstallmentQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.InstallmentQuery", q)
}

// The TraverseInstallment type is an adapter to allow the use of ordinary function as Traverser.
type TraverseInstallment func(context.Context, *ent.InstallmentQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseInstallment) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseInstallment) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.InstallmentQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.InstallmentQuery", q)
}

// The InstallmentPlanFunc type is an adapter to allow the use of ordinary function as a Querier.
type InstallmentPlanFunc func(context.Context, *ent.InstallmentPlanQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f InstallmentPlanFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.InstallmentPlanQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.InstallmentPlanQuery", q)
}

// The TraverseInstallmentPlan type is an adapter to allow the use of ordinary function as Traverser.
type TraverseInstallmentPlan func(context.Context, *ent.InstallmentPlanQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseInstallmentPlan) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseInstallmentPlan) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.InstallmentPlanQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.InstallmentPlanQuery", q)
}

// The IssueFunc type is an adapter to allow the use of ordinary function as a Querier.
type IssueFunc func(context.Context, *ent.IssueQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f IssueFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.IssueQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.IssueQuery", q)
}

// The TraverseIssue type is an adapter to allow the use of ordinary function as Traverser.
type TraverseIssue func(context.Context, *ent.IssueQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseIssue) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseIssue) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.IssueQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.IssueQuery", q)
}

// The IssueCommentFunc type is an adapter to allow the use of ordinary function as a Querier.
type IssueCommentFunc func(context.Context, *ent.IssueCommentQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f IssueCommentFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.IssueCommentQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.IssueCommentQuery", q)
}

// The TraverseIssueComment type is an adapter to allow the use of ordinary function as Traverser.
type TraverseIssueComment func(context.Context, *ent.IssueCommentQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseIssueComment) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseIssueComment) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.IssueCommentQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.IssueCommentQuery", q)
}

// The LocalDistrictFunc type is an adapter to allow the use of ordinary function as a Querier.
type LocalDistrictFunc func(context.Context, *ent.LocalDistrictQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f LocalDistrictFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.LocalDistrictQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.LocalDistrictQuery", q)
}

// The TraverseLocalDistrict type is an adapter to allow the use of ordinary function as Traverser.
type TraverseLocalDistrict func(context.Context, *ent.LocalDistrictQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseLocalDistrict) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseLocalDistrict) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.LocalDistrictQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.LocalDistrictQuery", q)
}

// The LocalProvinceFunc type is an adapter to allow the use of ordinary function as a Querier.
type LocalProvinceFunc func(context.Context, *ent.LocalProvinceQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f LocalProvinceFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.LocalProvinceQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.LocalProvinceQuery", q)
}

// The TraverseLocalProvince type is an adapter to allow the use of ordinary function as Traverser.
type TraverseLocalProvince func(context.Context, *ent.LocalProvinceQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseLocalProvince) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseLocalProvince) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.LocalProvinceQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.LocalProvinceQuery", q)
}

// The LocalWardFunc type is an adapter to allow the use of ordinary function as a Querier.
type LocalWardFunc func(context.Context, *ent.LocalWardQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f LocalWardFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.LocalWardQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.LocalWardQuery", q)
}

// The TraverseLocalWard type is an adapter to allow the use of ordinary function as Traverser.
type TraverseLocalWard func(context.Context, *ent.LocalWardQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseLocalWard) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseLocalWard) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.LocalWardQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.LocalWardQuery", q)
}

// The MaterialFunc type is an adapter to allow the use of ordinary function as a Querier.
type MaterialFunc func(context.Context, *ent.MaterialQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f MaterialFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.MaterialQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.MaterialQuery", q)
}

// The TraverseMaterial type is an adapter to allow the use of ordinary function as Traverser.
type TraverseMaterial func(context.Context, *ent.MaterialQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseMaterial) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseMaterial) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.MaterialQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.MaterialQuery", q)
}

// The MaterialUsageFunc type is an adapter to allow the use of ordinary function as a Querier.
type MaterialUsageFunc func(context.Context, *ent.MaterialUsageQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f MaterialUsageFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.MaterialUsageQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.MaterialUsageQuery", q)
}

// The TraverseMaterialUsage type is an adapter to allow the use of ordinary function as Traverser.
type TraverseMaterialUsage func(context.Context, *ent.MaterialUsageQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseMaterialUsage) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseMaterialUsage) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.MaterialUsageQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.MaterialUsageQuery", q)
}

// The MessageHistoryFunc type is an adapter to allow the use of ordinary function as a Querier.
type MessageHistoryFunc func(context.Context, *ent.MessageHistoryQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f MessageHistoryFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.MessageHistoryQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.MessageHistoryQuery", q)
}

// The TraverseMessageHistory type is an adapter to allow the use of ordinary function as Traverser.
type TraverseMessageHistory func(context.Context, *ent.MessageHistoryQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseMessageHistory) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseMessageHistory) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.MessageHistoryQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.MessageHistoryQuery", q)
}

// The NewTracksReportViewFunc type is an adapter to allow the use of ordinary function as a Querier.
type NewTracksReportViewFunc func(context.Context, *ent.NewTracksReportViewQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f NewTracksReportViewFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.NewTracksReportViewQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.NewTracksReportViewQuery", q)
}

// The TraverseNewTracksReportView type is an adapter to allow the use of ordinary function as Traverser.
type TraverseNewTracksReportView func(context.Context, *ent.NewTracksReportViewQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseNewTracksReportView) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseNewTracksReportView) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.NewTracksReportViewQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.NewTracksReportViewQuery", q)
}

// The NoteFunc type is an adapter to allow the use of ordinary function as a Querier.
type NoteFunc func(context.Context, *ent.NoteQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f NoteFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.NoteQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.NoteQuery", q)
}

// The TraverseNote type is an adapter to allow the use of ordinary function as Traverser.
type TraverseNote func(context.Context, *ent.NoteQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseNote) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseNote) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.NoteQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.NoteQuery", q)
}

// The NotificationFunc type is an adapter to allow the use of ordinary function as a Querier.
type NotificationFunc func(context.Context, *ent.NotificationQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f NotificationFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.NotificationQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.NotificationQuery", q)
}

// The TraverseNotification type is an adapter to allow the use of ordinary function as Traverser.
type TraverseNotification func(context.Context, *ent.NotificationQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseNotification) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseNotification) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.NotificationQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.NotificationQuery", q)
}

// The OTPFunc type is an adapter to allow the use of ordinary function as a Querier.
type OTPFunc func(context.Context, *ent.OTPQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f OTPFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.OTPQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.OTPQuery", q)
}

// The TraverseOTP type is an adapter to allow the use of ordinary function as Traverser.
type TraverseOTP func(context.Context, *ent.OTPQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseOTP) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseOTP) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.OTPQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.OTPQuery", q)
}

// The OperationFunc type is an adapter to allow the use of ordinary function as a Querier.
type OperationFunc func(context.Context, *ent.OperationQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f OperationFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.OperationQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.OperationQuery", q)
}

// The TraverseOperation type is an adapter to allow the use of ordinary function as Traverser.
type TraverseOperation func(context.Context, *ent.OperationQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseOperation) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseOperation) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.OperationQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.OperationQuery", q)
}

// The OperationMaterialFunc type is an adapter to allow the use of ordinary function as a Querier.
type OperationMaterialFunc func(context.Context, *ent.OperationMaterialQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f OperationMaterialFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.OperationMaterialQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.OperationMaterialQuery", q)
}

// The TraverseOperationMaterial type is an adapter to allow the use of ordinary function as Traverser.
type TraverseOperationMaterial func(context.Context, *ent.OperationMaterialQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseOperationMaterial) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseOperationMaterial) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.OperationMaterialQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.OperationMaterialQuery", q)
}

// The OrganizationFunc type is an adapter to allow the use of ordinary function as a Querier.
type OrganizationFunc func(context.Context, *ent.OrganizationQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f OrganizationFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.OrganizationQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.OrganizationQuery", q)
}

// The TraverseOrganization type is an adapter to allow the use of ordinary function as Traverser.
type TraverseOrganization func(context.Context, *ent.OrganizationQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseOrganization) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseOrganization) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.OrganizationQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.OrganizationQuery", q)
}

// The PaymentFunc type is an adapter to allow the use of ordinary function as a Querier.
type PaymentFunc func(context.Context, *ent.PaymentQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f PaymentFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.PaymentQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.PaymentQuery", q)
}

// The TraversePayment type is an adapter to allow the use of ordinary function as Traverser.
type TraversePayment func(context.Context, *ent.PaymentQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraversePayment) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraversePayment) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.PaymentQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.PaymentQuery", q)
}

// The PaymentAllocationFunc type is an adapter to allow the use of ordinary function as a Querier.
type PaymentAllocationFunc func(context.Context, *ent.PaymentAllocationQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f PaymentAllocationFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.PaymentAllocationQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.PaymentAllocationQuery", q)
}

// The TraversePaymentAllocation type is an adapter to allow the use of ordinary function as Traverser.
type TraversePaymentAllocation func(context.Context, *ent.PaymentAllocationQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraversePaymentAllocation) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraversePaymentAllocation) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.PaymentAllocationQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.PaymentAllocationQuery", q)
}

// The PaymentReportDetailViewFunc type is an adapter to allow the use of ordinary function as a Querier.
type PaymentReportDetailViewFunc func(context.Context, *ent.PaymentReportDetailViewQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f PaymentReportDetailViewFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.PaymentReportDetailViewQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.PaymentReportDetailViewQuery", q)
}

// The TraversePaymentReportDetailView type is an adapter to allow the use of ordinary function as Traverser.
type TraversePaymentReportDetailView func(context.Context, *ent.PaymentReportDetailViewQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraversePaymentReportDetailView) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraversePaymentReportDetailView) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.PaymentReportDetailViewQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.PaymentReportDetailViewQuery", q)
}

// The PaymentReportViewFunc type is an adapter to allow the use of ordinary function as a Querier.
type PaymentReportViewFunc func(context.Context, *ent.PaymentReportViewQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f PaymentReportViewFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.PaymentReportViewQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.PaymentReportViewQuery", q)
}

// The TraversePaymentReportView type is an adapter to allow the use of ordinary function as Traverser.
type TraversePaymentReportView func(context.Context, *ent.PaymentReportViewQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraversePaymentReportView) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraversePaymentReportView) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.PaymentReportViewQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.PaymentReportViewQuery", q)
}

// The PersonFunc type is an adapter to allow the use of ordinary function as a Querier.
type PersonFunc func(context.Context, *ent.PersonQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f PersonFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.PersonQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.PersonQuery", q)
}

// The TraversePerson type is an adapter to allow the use of ordinary function as Traverser.
type TraversePerson func(context.Context, *ent.PersonQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraversePerson) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraversePerson) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.PersonQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.PersonQuery", q)
}

// The PersonAssignmentFunc type is an adapter to allow the use of ordinary function as a Querier.
type PersonAssignmentFunc func(context.Context, *ent.PersonAssignmentQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f PersonAssignmentFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.PersonAssignmentQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.PersonAssignmentQuery", q)
}

// The TraversePersonAssignment type is an adapter to allow the use of ordinary function as Traverser.
type TraversePersonAssignment func(context.Context, *ent.PersonAssignmentQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraversePersonAssignment) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraversePersonAssignment) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.PersonAssignmentQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.PersonAssignmentQuery", q)
}

// The PersonDataFunc type is an adapter to allow the use of ordinary function as a Querier.
type PersonDataFunc func(context.Context, *ent.PersonDataQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f PersonDataFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.PersonDataQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.PersonDataQuery", q)
}

// The TraversePersonData type is an adapter to allow the use of ordinary function as Traverser.
type TraversePersonData func(context.Context, *ent.PersonDataQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraversePersonData) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraversePersonData) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.PersonDataQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.PersonDataQuery", q)
}

// The PersonHistoryFunc type is an adapter to allow the use of ordinary function as a Querier.
type PersonHistoryFunc func(context.Context, *ent.PersonHistoryQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f PersonHistoryFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.PersonHistoryQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.PersonHistoryQuery", q)
}

// The TraversePersonHistory type is an adapter to allow the use of ordinary function as Traverser.
type TraversePersonHistory func(context.Context, *ent.PersonHistoryQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraversePersonHistory) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraversePersonHistory) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.PersonHistoryQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.PersonHistoryQuery", q)
}

// The PersonQueryViewFunc type is an adapter to allow the use of ordinary function as a Querier.
type PersonQueryViewFunc func(context.Context, *ent.PersonQueryViewQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f PersonQueryViewFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.PersonQueryViewQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.PersonQueryViewQuery", q)
}

// The TraversePersonQueryView type is an adapter to allow the use of ordinary function as Traverser.
type TraversePersonQueryView func(context.Context, *ent.PersonQueryViewQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraversePersonQueryView) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraversePersonQueryView) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.PersonQueryViewQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.PersonQueryViewQuery", q)
}

// The PersonReferralFunc type is an adapter to allow the use of ordinary function as a Querier.
type PersonReferralFunc func(context.Context, *ent.PersonReferralQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f PersonReferralFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.PersonReferralQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.PersonReferralQuery", q)
}

// The TraversePersonReferral type is an adapter to allow the use of ordinary function as Traverser.
type TraversePersonReferral func(context.Context, *ent.PersonReferralQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraversePersonReferral) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraversePersonReferral) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.PersonReferralQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.PersonReferralQuery", q)
}

// The PersonTimelineViewFunc type is an adapter to allow the use of ordinary function as a Querier.
type PersonTimelineViewFunc func(context.Context, *ent.PersonTimelineViewQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f PersonTimelineViewFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.PersonTimelineViewQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.PersonTimelineViewQuery", q)
}

// The TraversePersonTimelineView type is an adapter to allow the use of ordinary function as Traverser.
type TraversePersonTimelineView func(context.Context, *ent.PersonTimelineViewQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraversePersonTimelineView) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraversePersonTimelineView) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.PersonTimelineViewQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.PersonTimelineViewQuery", q)
}

// The PhoneViewHistoryFunc type is an adapter to allow the use of ordinary function as a Querier.
type PhoneViewHistoryFunc func(context.Context, *ent.PhoneViewHistoryQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f PhoneViewHistoryFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.PhoneViewHistoryQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.PhoneViewHistoryQuery", q)
}

// The TraversePhoneViewHistory type is an adapter to allow the use of ordinary function as Traverser.
type TraversePhoneViewHistory func(context.Context, *ent.PhoneViewHistoryQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraversePhoneViewHistory) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraversePhoneViewHistory) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.PhoneViewHistoryQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.PhoneViewHistoryQuery", q)
}

// The PipelineFunc type is an adapter to allow the use of ordinary function as a Querier.
type PipelineFunc func(context.Context, *ent.PipelineQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f PipelineFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.PipelineQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.PipelineQuery", q)
}

// The TraversePipeline type is an adapter to allow the use of ordinary function as Traverser.
type TraversePipeline func(context.Context, *ent.PipelineQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraversePipeline) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraversePipeline) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.PipelineQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.PipelineQuery", q)
}

// The ProductFunc type is an adapter to allow the use of ordinary function as a Querier.
type ProductFunc func(context.Context, *ent.ProductQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f ProductFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.ProductQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.ProductQuery", q)
}

// The TraverseProduct type is an adapter to allow the use of ordinary function as Traverser.
type TraverseProduct func(context.Context, *ent.ProductQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseProduct) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseProduct) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.ProductQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.ProductQuery", q)
}

// The ProductOperationFunc type is an adapter to allow the use of ordinary function as a Querier.
type ProductOperationFunc func(context.Context, *ent.ProductOperationQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f ProductOperationFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.ProductOperationQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.ProductOperationQuery", q)
}

// The TraverseProductOperation type is an adapter to allow the use of ordinary function as Traverser.
type TraverseProductOperation func(context.Context, *ent.ProductOperationQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseProductOperation) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseProductOperation) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.ProductOperationQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.ProductOperationQuery", q)
}

// The ReferralFunc type is an adapter to allow the use of ordinary function as a Querier.
type ReferralFunc func(context.Context, *ent.ReferralQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f ReferralFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.ReferralQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.ReferralQuery", q)
}

// The TraverseReferral type is an adapter to allow the use of ordinary function as Traverser.
type TraverseReferral func(context.Context, *ent.ReferralQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseReferral) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseReferral) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.ReferralQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.ReferralQuery", q)
}

// The ScheduleFunc type is an adapter to allow the use of ordinary function as a Querier.
type ScheduleFunc func(context.Context, *ent.ScheduleQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f ScheduleFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.ScheduleQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.ScheduleQuery", q)
}

// The TraverseSchedule type is an adapter to allow the use of ordinary function as Traverser.
type TraverseSchedule func(context.Context, *ent.ScheduleQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseSchedule) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseSchedule) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.ScheduleQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.ScheduleQuery", q)
}

// The SettingFunc type is an adapter to allow the use of ordinary function as a Querier.
type SettingFunc func(context.Context, *ent.SettingQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f SettingFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.SettingQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.SettingQuery", q)
}

// The TraverseSetting type is an adapter to allow the use of ordinary function as Traverser.
type TraverseSetting func(context.Context, *ent.SettingQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseSetting) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseSetting) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.SettingQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.SettingQuery", q)
}

// The StageFunc type is an adapter to allow the use of ordinary function as a Querier.
type StageFunc func(context.Context, *ent.StageQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f StageFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.StageQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.StageQuery", q)
}

// The TraverseStage type is an adapter to allow the use of ordinary function as Traverser.
type TraverseStage func(context.Context, *ent.StageQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseStage) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseStage) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.StageQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.StageQuery", q)
}

// The TagFunc type is an adapter to allow the use of ordinary function as a Querier.
type TagFunc func(context.Context, *ent.TagQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f TagFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.TagQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.TagQuery", q)
}

// The TraverseTag type is an adapter to allow the use of ordinary function as Traverser.
type TraverseTag func(context.Context, *ent.TagQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseTag) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseTag) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.TagQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.TagQuery", q)
}

// The TagDealFunc type is an adapter to allow the use of ordinary function as a Querier.
type TagDealFunc func(context.Context, *ent.TagDealQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f TagDealFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.TagDealQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.TagDealQuery", q)
}

// The TraverseTagDeal type is an adapter to allow the use of ordinary function as Traverser.
type TraverseTagDeal func(context.Context, *ent.TagDealQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseTagDeal) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseTagDeal) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.TagDealQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.TagDealQuery", q)
}

// The TagPersonFunc type is an adapter to allow the use of ordinary function as a Querier.
type TagPersonFunc func(context.Context, *ent.TagPersonQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f TagPersonFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.TagPersonQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.TagPersonQuery", q)
}

// The TraverseTagPerson type is an adapter to allow the use of ordinary function as Traverser.
type TraverseTagPerson func(context.Context, *ent.TagPersonQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseTagPerson) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseTagPerson) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.TagPersonQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.TagPersonQuery", q)
}

// The TaskFunc type is an adapter to allow the use of ordinary function as a Querier.
type TaskFunc func(context.Context, *ent.TaskQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f TaskFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.TaskQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.TaskQuery", q)
}

// The TraverseTask type is an adapter to allow the use of ordinary function as Traverser.
type TraverseTask func(context.Context, *ent.TaskQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseTask) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseTask) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.TaskQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.TaskQuery", q)
}

// The TaskAssignmentFunc type is an adapter to allow the use of ordinary function as a Querier.
type TaskAssignmentFunc func(context.Context, *ent.TaskAssignmentQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f TaskAssignmentFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.TaskAssignmentQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.TaskAssignmentQuery", q)
}

// The TraverseTaskAssignment type is an adapter to allow the use of ordinary function as Traverser.
type TraverseTaskAssignment func(context.Context, *ent.TaskAssignmentQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseTaskAssignment) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseTaskAssignment) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.TaskAssignmentQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.TaskAssignmentQuery", q)
}

// The TaskAssignmentViewFunc type is an adapter to allow the use of ordinary function as a Querier.
type TaskAssignmentViewFunc func(context.Context, *ent.TaskAssignmentViewQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f TaskAssignmentViewFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.TaskAssignmentViewQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.TaskAssignmentViewQuery", q)
}

// The TraverseTaskAssignmentView type is an adapter to allow the use of ordinary function as Traverser.
type TraverseTaskAssignmentView func(context.Context, *ent.TaskAssignmentViewQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseTaskAssignmentView) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseTaskAssignmentView) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.TaskAssignmentViewQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.TaskAssignmentViewQuery", q)
}

// The TaskDepartmentFunc type is an adapter to allow the use of ordinary function as a Querier.
type TaskDepartmentFunc func(context.Context, *ent.TaskDepartmentQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f TaskDepartmentFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.TaskDepartmentQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.TaskDepartmentQuery", q)
}

// The TraverseTaskDepartment type is an adapter to allow the use of ordinary function as Traverser.
type TraverseTaskDepartment func(context.Context, *ent.TaskDepartmentQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseTaskDepartment) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseTaskDepartment) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.TaskDepartmentQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.TaskDepartmentQuery", q)
}

// The TaskHistoryFunc type is an adapter to allow the use of ordinary function as a Querier.
type TaskHistoryFunc func(context.Context, *ent.TaskHistoryQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f TaskHistoryFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.TaskHistoryQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.TaskHistoryQuery", q)
}

// The TraverseTaskHistory type is an adapter to allow the use of ordinary function as Traverser.
type TraverseTaskHistory func(context.Context, *ent.TaskHistoryQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseTaskHistory) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseTaskHistory) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.TaskHistoryQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.TaskHistoryQuery", q)
}

// The TaskNoteFunc type is an adapter to allow the use of ordinary function as a Querier.
type TaskNoteFunc func(context.Context, *ent.TaskNoteQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f TaskNoteFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.TaskNoteQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.TaskNoteQuery", q)
}

// The TraverseTaskNote type is an adapter to allow the use of ordinary function as Traverser.
type TraverseTaskNote func(context.Context, *ent.TaskNoteQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseTaskNote) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseTaskNote) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.TaskNoteQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.TaskNoteQuery", q)
}

// The TaskRecurringFunc type is an adapter to allow the use of ordinary function as a Querier.
type TaskRecurringFunc func(context.Context, *ent.TaskRecurringQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f TaskRecurringFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.TaskRecurringQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.TaskRecurringQuery", q)
}

// The TraverseTaskRecurring type is an adapter to allow the use of ordinary function as Traverser.
type TraverseTaskRecurring func(context.Context, *ent.TaskRecurringQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseTaskRecurring) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseTaskRecurring) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.TaskRecurringQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.TaskRecurringQuery", q)
}

// The TaskSerialViewFunc type is an adapter to allow the use of ordinary function as a Querier.
type TaskSerialViewFunc func(context.Context, *ent.TaskSerialViewQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f TaskSerialViewFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.TaskSerialViewQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.TaskSerialViewQuery", q)
}

// The TraverseTaskSerialView type is an adapter to allow the use of ordinary function as Traverser.
type TraverseTaskSerialView func(context.Context, *ent.TaskSerialViewQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseTaskSerialView) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseTaskSerialView) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.TaskSerialViewQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.TaskSerialViewQuery", q)
}

// The TermFunc type is an adapter to allow the use of ordinary function as a Querier.
type TermFunc func(context.Context, *ent.TermQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f TermFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.TermQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.TermQuery", q)
}

// The TraverseTerm type is an adapter to allow the use of ordinary function as Traverser.
type TraverseTerm func(context.Context, *ent.TermQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseTerm) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseTerm) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.TermQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.TermQuery", q)
}

// The TrackFunc type is an adapter to allow the use of ordinary function as a Querier.
type TrackFunc func(context.Context, *ent.TrackQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f TrackFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.TrackQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.TrackQuery", q)
}

// The TraverseTrack type is an adapter to allow the use of ordinary function as Traverser.
type TraverseTrack func(context.Context, *ent.TrackQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseTrack) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseTrack) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.TrackQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.TrackQuery", q)
}

// The UnifiedHistoryViewFunc type is an adapter to allow the use of ordinary function as a Querier.
type UnifiedHistoryViewFunc func(context.Context, *ent.UnifiedHistoryViewQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f UnifiedHistoryViewFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.UnifiedHistoryViewQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.UnifiedHistoryViewQuery", q)
}

// The TraverseUnifiedHistoryView type is an adapter to allow the use of ordinary function as Traverser.
type TraverseUnifiedHistoryView func(context.Context, *ent.UnifiedHistoryViewQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseUnifiedHistoryView) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseUnifiedHistoryView) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.UnifiedHistoryViewQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.UnifiedHistoryViewQuery", q)
}

// The UserFunc type is an adapter to allow the use of ordinary function as a Querier.
type UserFunc func(context.Context, *ent.UserQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f UserFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.UserQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.UserQuery", q)
}

// The TraverseUser type is an adapter to allow the use of ordinary function as Traverser.
type TraverseUser func(context.Context, *ent.UserQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseUser) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseUser) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.UserQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.UserQuery", q)
}

// The UserDataFunc type is an adapter to allow the use of ordinary function as a Querier.
type UserDataFunc func(context.Context, *ent.UserDataQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f UserDataFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.UserDataQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.UserDataQuery", q)
}

// The TraverseUserData type is an adapter to allow the use of ordinary function as Traverser.
type TraverseUserData func(context.Context, *ent.UserDataQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseUserData) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseUserData) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.UserDataQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.UserDataQuery", q)
}

// NewQuery returns the generic Query interface for the given typed query.
func NewQuery(q ent.Query) (Query, error) {
	switch q := q.(type) {
	case *ent.ActivityViewQuery:
		return &query[*ent.ActivityViewQuery, predicate.ActivityView, activityview.OrderOption]{typ: ent.TypeActivityView, tq: q}, nil
	case *ent.AppointmentQuery:
		return &query[*ent.AppointmentQuery, predicate.Appointment, appointment.OrderOption]{typ: ent.TypeAppointment, tq: q}, nil
	case *ent.AppointmentHistoryQuery:
		return &query[*ent.AppointmentHistoryQuery, predicate.AppointmentHistory, appointmenthistory.OrderOption]{typ: ent.TypeAppointmentHistory, tq: q}, nil
	case *ent.AttachmentQuery:
		return &query[*ent.AttachmentQuery, predicate.Attachment, attachment.OrderOption]{typ: ent.TypeAttachment, tq: q}, nil
	case *ent.AttachmentDataQuery:
		return &query[*ent.AttachmentDataQuery, predicate.AttachmentData, attachmentdata.OrderOption]{typ: ent.TypeAttachmentData, tq: q}, nil
	case *ent.AttachmentOperationReportViewQuery:
		return &query[*ent.AttachmentOperationReportViewQuery, predicate.AttachmentOperationReportView, attachmentoperationreportview.OrderOption]{typ: ent.TypeAttachmentOperationReportView, tq: q}, nil
	case *ent.BillQuery:
		return &query[*ent.BillQuery, predicate.Bill, bill.OrderOption]{typ: ent.TypeBill, tq: q}, nil
	case *ent.BillDataQuery:
		return &query[*ent.BillDataQuery, predicate.BillData, billdata.OrderOption]{typ: ent.TypeBillData, tq: q}, nil
	case *ent.BillItemQuery:
		return &query[*ent.BillItemQuery, predicate.BillItem, billitem.OrderOption]{typ: ent.TypeBillItem, tq: q}, nil
	case *ent.BundleQuery:
		return &query[*ent.BundleQuery, predicate.Bundle, bundle.OrderOption]{typ: ent.TypeBundle, tq: q}, nil
	case *ent.CallQuery:
		return &query[*ent.CallQuery, predicate.Call, call.OrderOption]{typ: ent.TypeCall, tq: q}, nil
	case *ent.CasbinRuleQuery:
		return &query[*ent.CasbinRuleQuery, predicate.CasbinRule, casbinrule.OrderOption]{typ: ent.TypeCasbinRule, tq: q}, nil
	case *ent.CashFlowQuery:
		return &query[*ent.CashFlowQuery, predicate.CashFlow, cashflow.OrderOption]{typ: ent.TypeCashFlow, tq: q}, nil
	case *ent.CashFlowItemQuery:
		return &query[*ent.CashFlowItemQuery, predicate.CashFlowItem, cashflowitem.OrderOption]{typ: ent.TypeCashFlowItem, tq: q}, nil
	case *ent.CashFlowNoteQuery:
		return &query[*ent.CashFlowNoteQuery, predicate.CashFlowNote, cashflownote.OrderOption]{typ: ent.TypeCashFlowNote, tq: q}, nil
	case *ent.DbsandentistDboAppointmentTimeQuery:
		return &query[*ent.DbsandentistDboAppointmentTimeQuery, predicate.DbsandentistDboAppointmentTime, dbsandentistdboappointmenttime.OrderOption]{typ: ent.TypeDbsandentistDboAppointmentTime, tq: q}, nil
	case *ent.DbsandentistDboBillQuery:
		return &query[*ent.DbsandentistDboBillQuery, predicate.DbsandentistDboBill, dbsandentistdbobill.OrderOption]{typ: ent.TypeDbsandentistDboBill, tq: q}, nil
	case *ent.DbsandentistDboBillItemQuery:
		return &query[*ent.DbsandentistDboBillItemQuery, predicate.DbsandentistDboBillItem, dbsandentistdbobillitem.OrderOption]{typ: ent.TypeDbsandentistDboBillItem, tq: q}, nil
	case *ent.DbsandentistDboBillItemDetailQuery:
		return &query[*ent.DbsandentistDboBillItemDetailQuery, predicate.DbsandentistDboBillItemDetail, dbsandentistdbobillitemdetail.OrderOption]{typ: ent.TypeDbsandentistDboBillItemDetail, tq: q}, nil
	case *ent.DbsandentistDboBillPaymentQuery:
		return &query[*ent.DbsandentistDboBillPaymentQuery, predicate.DbsandentistDboBillPayment, dbsandentistdbobillpayment.OrderOption]{typ: ent.TypeDbsandentistDboBillPayment, tq: q}, nil
	case *ent.DbsandentistDboCustomerQuery:
		return &query[*ent.DbsandentistDboCustomerQuery, predicate.DbsandentistDboCustomer, dbsandentistdbocustomer.OrderOption]{typ: ent.TypeDbsandentistDboCustomer, tq: q}, nil
	case *ent.DealQuery:
		return &query[*ent.DealQuery, predicate.Deal, deal.OrderOption]{typ: ent.TypeDeal, tq: q}, nil
	case *ent.DealHistoryQuery:
		return &query[*ent.DealHistoryQuery, predicate.DealHistory, dealhistory.OrderOption]{typ: ent.TypeDealHistory, tq: q}, nil
	case *ent.DealStageHistoryQuery:
		return &query[*ent.DealStageHistoryQuery, predicate.DealStageHistory, dealstagehistory.OrderOption]{typ: ent.TypeDealStageHistory, tq: q}, nil
	case *ent.DealStageHistoryViewQuery:
		return &query[*ent.DealStageHistoryViewQuery, predicate.DealStageHistoryView, dealstagehistoryview.OrderOption]{typ: ent.TypeDealStageHistoryView, tq: q}, nil
	case *ent.DealUserQuery:
		return &query[*ent.DealUserQuery, predicate.DealUser, dealuser.OrderOption]{typ: ent.TypeDealUser, tq: q}, nil
	case *ent.DealUserRatingQuery:
		return &query[*ent.DealUserRatingQuery, predicate.DealUserRating, dealuserrating.OrderOption]{typ: ent.TypeDealUserRating, tq: q}, nil
	case *ent.DepartmentQuery:
		return &query[*ent.DepartmentQuery, predicate.Department, department.OrderOption]{typ: ent.TypeDepartment, tq: q}, nil
	case *ent.DepositQuery:
		return &query[*ent.DepositQuery, predicate.Deposit, deposit.OrderOption]{typ: ent.TypeDeposit, tq: q}, nil
	case *ent.DepositAllocationQuery:
		return &query[*ent.DepositAllocationQuery, predicate.DepositAllocation, depositallocation.OrderOption]{typ: ent.TypeDepositAllocation, tq: q}, nil
	case *ent.DepositPaymentQuery:
		return &query[*ent.DepositPaymentQuery, predicate.DepositPayment, depositpayment.OrderOption]{typ: ent.TypeDepositPayment, tq: q}, nil
	case *ent.DiscountQuery:
		return &query[*ent.DiscountQuery, predicate.Discount, discount.OrderOption]{typ: ent.TypeDiscount, tq: q}, nil
	case *ent.DiscountUsageQuery:
		return &query[*ent.DiscountUsageQuery, predicate.DiscountUsage, discountusage.OrderOption]{typ: ent.TypeDiscountUsage, tq: q}, nil
	case *ent.EntityHistoryQuery:
		return &query[*ent.EntityHistoryQuery, predicate.EntityHistory, entityhistory.OrderOption]{typ: ent.TypeEntityHistory, tq: q}, nil
	case *ent.ExportJobQuery:
		return &query[*ent.ExportJobQuery, predicate.ExportJob, exportjob.OrderOption]{typ: ent.TypeExportJob, tq: q}, nil
	case *ent.FileQuery:
		return &query[*ent.FileQuery, predicate.File, file.OrderOption]{typ: ent.TypeFile, tq: q}, nil
	case *ent.FileUsageQuery:
		return &query[*ent.FileUsageQuery, predicate.FileUsage, fileusage.OrderOption]{typ: ent.TypeFileUsage, tq: q}, nil
	case *ent.FormSubmissionQuery:
		return &query[*ent.FormSubmissionQuery, predicate.FormSubmission, formsubmission.OrderOption]{typ: ent.TypeFormSubmission, tq: q}, nil
	case *ent.InstallmentQuery:
		return &query[*ent.InstallmentQuery, predicate.Installment, installment.OrderOption]{typ: ent.TypeInstallment, tq: q}, nil
	case *ent.InstallmentPlanQuery:
		return &query[*ent.InstallmentPlanQuery, predicate.InstallmentPlan, installmentplan.OrderOption]{typ: ent.TypeInstallmentPlan, tq: q}, nil
	case *ent.IssueQuery:
		return &query[*ent.IssueQuery, predicate.Issue, issue.OrderOption]{typ: ent.TypeIssue, tq: q}, nil
	case *ent.IssueCommentQuery:
		return &query[*ent.IssueCommentQuery, predicate.IssueComment, issuecomment.OrderOption]{typ: ent.TypeIssueComment, tq: q}, nil
	case *ent.LocalDistrictQuery:
		return &query[*ent.LocalDistrictQuery, predicate.LocalDistrict, localdistrict.OrderOption]{typ: ent.TypeLocalDistrict, tq: q}, nil
	case *ent.LocalProvinceQuery:
		return &query[*ent.LocalProvinceQuery, predicate.LocalProvince, localprovince.OrderOption]{typ: ent.TypeLocalProvince, tq: q}, nil
	case *ent.LocalWardQuery:
		return &query[*ent.LocalWardQuery, predicate.LocalWard, localward.OrderOption]{typ: ent.TypeLocalWard, tq: q}, nil
	case *ent.MaterialQuery:
		return &query[*ent.MaterialQuery, predicate.Material, material.OrderOption]{typ: ent.TypeMaterial, tq: q}, nil
	case *ent.MaterialUsageQuery:
		return &query[*ent.MaterialUsageQuery, predicate.MaterialUsage, materialusage.OrderOption]{typ: ent.TypeMaterialUsage, tq: q}, nil
	case *ent.MessageHistoryQuery:
		return &query[*ent.MessageHistoryQuery, predicate.MessageHistory, messagehistory.OrderOption]{typ: ent.TypeMessageHistory, tq: q}, nil
	case *ent.NewTracksReportViewQuery:
		return &query[*ent.NewTracksReportViewQuery, predicate.NewTracksReportView, newtracksreportview.OrderOption]{typ: ent.TypeNewTracksReportView, tq: q}, nil
	case *ent.NoteQuery:
		return &query[*ent.NoteQuery, predicate.Note, note.OrderOption]{typ: ent.TypeNote, tq: q}, nil
	case *ent.NotificationQuery:
		return &query[*ent.NotificationQuery, predicate.Notification, notification.OrderOption]{typ: ent.TypeNotification, tq: q}, nil
	case *ent.OTPQuery:
		return &query[*ent.OTPQuery, predicate.OTP, otp.OrderOption]{typ: ent.TypeOTP, tq: q}, nil
	case *ent.OperationQuery:
		return &query[*ent.OperationQuery, predicate.Operation, operation.OrderOption]{typ: ent.TypeOperation, tq: q}, nil
	case *ent.OperationMaterialQuery:
		return &query[*ent.OperationMaterialQuery, predicate.OperationMaterial, operationmaterial.OrderOption]{typ: ent.TypeOperationMaterial, tq: q}, nil
	case *ent.OrganizationQuery:
		return &query[*ent.OrganizationQuery, predicate.Organization, organization.OrderOption]{typ: ent.TypeOrganization, tq: q}, nil
	case *ent.PaymentQuery:
		return &query[*ent.PaymentQuery, predicate.Payment, payment.OrderOption]{typ: ent.TypePayment, tq: q}, nil
	case *ent.PaymentAllocationQuery:
		return &query[*ent.PaymentAllocationQuery, predicate.PaymentAllocation, paymentallocation.OrderOption]{typ: ent.TypePaymentAllocation, tq: q}, nil
	case *ent.PaymentReportDetailViewQuery:
		return &query[*ent.PaymentReportDetailViewQuery, predicate.PaymentReportDetailView, paymentreportdetailview.OrderOption]{typ: ent.TypePaymentReportDetailView, tq: q}, nil
	case *ent.PaymentReportViewQuery:
		return &query[*ent.PaymentReportViewQuery, predicate.PaymentReportView, paymentreportview.OrderOption]{typ: ent.TypePaymentReportView, tq: q}, nil
	case *ent.PersonQuery:
		return &query[*ent.PersonQuery, predicate.Person, person.OrderOption]{typ: ent.TypePerson, tq: q}, nil
	case *ent.PersonAssignmentQuery:
		return &query[*ent.PersonAssignmentQuery, predicate.PersonAssignment, personassignment.OrderOption]{typ: ent.TypePersonAssignment, tq: q}, nil
	case *ent.PersonDataQuery:
		return &query[*ent.PersonDataQuery, predicate.PersonData, persondata.OrderOption]{typ: ent.TypePersonData, tq: q}, nil
	case *ent.PersonHistoryQuery:
		return &query[*ent.PersonHistoryQuery, predicate.PersonHistory, personhistory.OrderOption]{typ: ent.TypePersonHistory, tq: q}, nil
	case *ent.PersonQueryViewQuery:
		return &query[*ent.PersonQueryViewQuery, predicate.PersonQueryView, personqueryview.OrderOption]{typ: ent.TypePersonQueryView, tq: q}, nil
	case *ent.PersonReferralQuery:
		return &query[*ent.PersonReferralQuery, predicate.PersonReferral, personreferral.OrderOption]{typ: ent.TypePersonReferral, tq: q}, nil
	case *ent.PersonTimelineViewQuery:
		return &query[*ent.PersonTimelineViewQuery, predicate.PersonTimelineView, persontimelineview.OrderOption]{typ: ent.TypePersonTimelineView, tq: q}, nil
	case *ent.PhoneViewHistoryQuery:
		return &query[*ent.PhoneViewHistoryQuery, predicate.PhoneViewHistory, phoneviewhistory.OrderOption]{typ: ent.TypePhoneViewHistory, tq: q}, nil
	case *ent.PipelineQuery:
		return &query[*ent.PipelineQuery, predicate.Pipeline, pipeline.OrderOption]{typ: ent.TypePipeline, tq: q}, nil
	case *ent.ProductQuery:
		return &query[*ent.ProductQuery, predicate.Product, product.OrderOption]{typ: ent.TypeProduct, tq: q}, nil
	case *ent.ProductOperationQuery:
		return &query[*ent.ProductOperationQuery, predicate.ProductOperation, productoperation.OrderOption]{typ: ent.TypeProductOperation, tq: q}, nil
	case *ent.ReferralQuery:
		return &query[*ent.ReferralQuery, predicate.Referral, referral.OrderOption]{typ: ent.TypeReferral, tq: q}, nil
	case *ent.ScheduleQuery:
		return &query[*ent.ScheduleQuery, predicate.Schedule, schedule.OrderOption]{typ: ent.TypeSchedule, tq: q}, nil
	case *ent.SettingQuery:
		return &query[*ent.SettingQuery, predicate.Setting, setting.OrderOption]{typ: ent.TypeSetting, tq: q}, nil
	case *ent.StageQuery:
		return &query[*ent.StageQuery, predicate.Stage, stage.OrderOption]{typ: ent.TypeStage, tq: q}, nil
	case *ent.TagQuery:
		return &query[*ent.TagQuery, predicate.Tag, tag.OrderOption]{typ: ent.TypeTag, tq: q}, nil
	case *ent.TagDealQuery:
		return &query[*ent.TagDealQuery, predicate.TagDeal, tagdeal.OrderOption]{typ: ent.TypeTagDeal, tq: q}, nil
	case *ent.TagPersonQuery:
		return &query[*ent.TagPersonQuery, predicate.TagPerson, tagperson.OrderOption]{typ: ent.TypeTagPerson, tq: q}, nil
	case *ent.TaskQuery:
		return &query[*ent.TaskQuery, predicate.Task, task.OrderOption]{typ: ent.TypeTask, tq: q}, nil
	case *ent.TaskAssignmentQuery:
		return &query[*ent.TaskAssignmentQuery, predicate.TaskAssignment, taskassignment.OrderOption]{typ: ent.TypeTaskAssignment, tq: q}, nil
	case *ent.TaskAssignmentViewQuery:
		return &query[*ent.TaskAssignmentViewQuery, predicate.TaskAssignmentView, taskassignmentview.OrderOption]{typ: ent.TypeTaskAssignmentView, tq: q}, nil
	case *ent.TaskDepartmentQuery:
		return &query[*ent.TaskDepartmentQuery, predicate.TaskDepartment, taskdepartment.OrderOption]{typ: ent.TypeTaskDepartment, tq: q}, nil
	case *ent.TaskHistoryQuery:
		return &query[*ent.TaskHistoryQuery, predicate.TaskHistory, taskhistory.OrderOption]{typ: ent.TypeTaskHistory, tq: q}, nil
	case *ent.TaskNoteQuery:
		return &query[*ent.TaskNoteQuery, predicate.TaskNote, tasknote.OrderOption]{typ: ent.TypeTaskNote, tq: q}, nil
	case *ent.TaskRecurringQuery:
		return &query[*ent.TaskRecurringQuery, predicate.TaskRecurring, taskrecurring.OrderOption]{typ: ent.TypeTaskRecurring, tq: q}, nil
	case *ent.TaskSerialViewQuery:
		return &query[*ent.TaskSerialViewQuery, predicate.TaskSerialView, taskserialview.OrderOption]{typ: ent.TypeTaskSerialView, tq: q}, nil
	case *ent.TermQuery:
		return &query[*ent.TermQuery, predicate.Term, term.OrderOption]{typ: ent.TypeTerm, tq: q}, nil
	case *ent.TrackQuery:
		return &query[*ent.TrackQuery, predicate.Track, track.OrderOption]{typ: ent.TypeTrack, tq: q}, nil
	case *ent.UnifiedHistoryViewQuery:
		return &query[*ent.UnifiedHistoryViewQuery, predicate.UnifiedHistoryView, unifiedhistoryview.OrderOption]{typ: ent.TypeUnifiedHistoryView, tq: q}, nil
	case *ent.UserQuery:
		return &query[*ent.UserQuery, predicate.User, user.OrderOption]{typ: ent.TypeUser, tq: q}, nil
	case *ent.UserDataQuery:
		return &query[*ent.UserDataQuery, predicate.UserData, userdata.OrderOption]{typ: ent.TypeUserData, tq: q}, nil
	default:
		return nil, fmt.Errorf("unknown query type %T", q)
	}
}

type query[T any, P ~func(*sql.Selector), R ~func(*sql.Selector)] struct {
	typ string
	tq  interface {
		Limit(int) T
		Offset(int) T
		Unique(bool) T
		Order(...R) T
		Where(...P) T
	}
}

func (q query[T, P, R]) Type() string {
	return q.typ
}

func (q query[T, P, R]) Limit(limit int) {
	q.tq.Limit(limit)
}

func (q query[T, P, R]) Offset(offset int) {
	q.tq.Offset(offset)
}

func (q query[T, P, R]) Unique(unique bool) {
	q.tq.Unique(unique)
}

func (q query[T, P, R]) Order(orders ...func(*sql.Selector)) {
	rs := make([]R, len(orders))
	for i := range orders {
		rs[i] = orders[i]
	}
	q.tq.Order(rs...)
}

func (q query[T, P, R]) WhereP(ps ...func(*sql.Selector)) {
	p := make([]P, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	q.tq.Where(p...)
}
