// Code generated by ent, DO NOT EDIT.

package ent

import (
	"bcare/ent/user"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

// User is the model entity for the User schema.
type User struct {
	config `json:"-"`
	// ID of the ent.
	ID int `json:"id,omitempty"`
	// DeletedAt holds the value of the "deleted_at" field.
	DeletedAt time.Time `json:"deleted_at,omitempty"`
	// Status holds the value of the "status" field.
	Status int8 `json:"status,omitempty"`
	// Version holds the value of the "version" field.
	Version int `json:"version,omitempty"`
	// CreatedAt holds the value of the "created_at" field.
	CreatedAt time.Time `json:"created_at,omitempty"`
	// UpdatedAt holds the value of the "updated_at" field.
	UpdatedAt time.Time `json:"updated_at,omitempty"`
	// The username
	Username string `json:"username,omitempty"`
	// The user password
	Password string `json:"password,omitempty"`
	// The mobile phone number
	Phone string `json:"phone,omitempty"`
	// The email
	Email string `json:"email,omitempty"`
	// EmailConfirmed holds the value of the "email_confirmed" field.
	EmailConfirmed bool `json:"email_confirmed,omitempty"`
	// The full name
	Name string `json:"name,omitempty"`
	// gender,male|female|unknown
	Gender string `json:"gender,omitempty"`
	// Department
	DepartmentID int `json:"department_id,omitempty"`
	// Department
	DepartmentPosition string `json:"department_position,omitempty"`
	// ProfileImage holds the value of the "profile_image" field.
	ProfileImage string `json:"profile_image,omitempty"`
	// SuspendedAt holds the value of the "suspended_at" field.
	SuspendedAt time.Time `json:"suspended_at,omitempty"`
	// State holds the value of the "state" field.
	State user.State `json:"state,omitempty"`
	// Edges holds the relations/edges for other nodes in the graph.
	// The values are being populated by the UserQuery when eager-loading is set.
	Edges        UserEdges `json:"-"`
	selectValues sql.SelectValues
}

// UserEdges holds the relations/edges for other nodes in the graph.
type UserEdges struct {
	// Assignment holds the value of the assignment edge.
	Assignment []*TaskAssignment `json:"assignment,omitempty"`
	// AssignedPerson holds the value of the assigned_person edge.
	AssignedPerson []*Person `json:"assigned_person,omitempty"`
	// Notes holds the value of the notes edge.
	Notes []*Note `json:"notes,omitempty"`
	// TaskNotes holds the value of the task_notes edge.
	TaskNotes []*TaskNote `json:"task_notes,omitempty"`
	// Appointments holds the value of the appointments edge.
	Appointments []*Appointment `json:"appointments,omitempty"`
	// Calls holds the value of the calls edge.
	Calls []*Call `json:"calls,omitempty"`
	// Schedules holds the value of the schedules edge.
	Schedules []*Schedule `json:"schedules,omitempty"`
	// MessageHistories holds the value of the message_histories edge.
	MessageHistories []*MessageHistory `json:"message_histories,omitempty"`
	// Data holds the value of the data edge.
	Data []*UserData `json:"data,omitempty"`
	// ExportJobs holds the value of the export_jobs edge.
	ExportJobs []*ExportJob `json:"export_jobs,omitempty"`
	// Notifications holds the value of the notifications edge.
	Notifications []*Notification `json:"notifications,omitempty"`
	// PersonAssignment holds the value of the person_assignment edge.
	PersonAssignment []*PersonAssignment `json:"person_assignment,omitempty"`
	// loadedTypes holds the information for reporting if a
	// type was loaded (or requested) in eager-loading or not.
	loadedTypes [12]bool
}

// AssignmentOrErr returns the Assignment value or an error if the edge
// was not loaded in eager-loading.
func (e UserEdges) AssignmentOrErr() ([]*TaskAssignment, error) {
	if e.loadedTypes[0] {
		return e.Assignment, nil
	}
	return nil, &NotLoadedError{edge: "assignment"}
}

// AssignedPersonOrErr returns the AssignedPerson value or an error if the edge
// was not loaded in eager-loading.
func (e UserEdges) AssignedPersonOrErr() ([]*Person, error) {
	if e.loadedTypes[1] {
		return e.AssignedPerson, nil
	}
	return nil, &NotLoadedError{edge: "assigned_person"}
}

// NotesOrErr returns the Notes value or an error if the edge
// was not loaded in eager-loading.
func (e UserEdges) NotesOrErr() ([]*Note, error) {
	if e.loadedTypes[2] {
		return e.Notes, nil
	}
	return nil, &NotLoadedError{edge: "notes"}
}

// TaskNotesOrErr returns the TaskNotes value or an error if the edge
// was not loaded in eager-loading.
func (e UserEdges) TaskNotesOrErr() ([]*TaskNote, error) {
	if e.loadedTypes[3] {
		return e.TaskNotes, nil
	}
	return nil, &NotLoadedError{edge: "task_notes"}
}

// AppointmentsOrErr returns the Appointments value or an error if the edge
// was not loaded in eager-loading.
func (e UserEdges) AppointmentsOrErr() ([]*Appointment, error) {
	if e.loadedTypes[4] {
		return e.Appointments, nil
	}
	return nil, &NotLoadedError{edge: "appointments"}
}

// CallsOrErr returns the Calls value or an error if the edge
// was not loaded in eager-loading.
func (e UserEdges) CallsOrErr() ([]*Call, error) {
	if e.loadedTypes[5] {
		return e.Calls, nil
	}
	return nil, &NotLoadedError{edge: "calls"}
}

// SchedulesOrErr returns the Schedules value or an error if the edge
// was not loaded in eager-loading.
func (e UserEdges) SchedulesOrErr() ([]*Schedule, error) {
	if e.loadedTypes[6] {
		return e.Schedules, nil
	}
	return nil, &NotLoadedError{edge: "schedules"}
}

// MessageHistoriesOrErr returns the MessageHistories value or an error if the edge
// was not loaded in eager-loading.
func (e UserEdges) MessageHistoriesOrErr() ([]*MessageHistory, error) {
	if e.loadedTypes[7] {
		return e.MessageHistories, nil
	}
	return nil, &NotLoadedError{edge: "message_histories"}
}

// DataOrErr returns the Data value or an error if the edge
// was not loaded in eager-loading.
func (e UserEdges) DataOrErr() ([]*UserData, error) {
	if e.loadedTypes[8] {
		return e.Data, nil
	}
	return nil, &NotLoadedError{edge: "data"}
}

// ExportJobsOrErr returns the ExportJobs value or an error if the edge
// was not loaded in eager-loading.
func (e UserEdges) ExportJobsOrErr() ([]*ExportJob, error) {
	if e.loadedTypes[9] {
		return e.ExportJobs, nil
	}
	return nil, &NotLoadedError{edge: "export_jobs"}
}

// NotificationsOrErr returns the Notifications value or an error if the edge
// was not loaded in eager-loading.
func (e UserEdges) NotificationsOrErr() ([]*Notification, error) {
	if e.loadedTypes[10] {
		return e.Notifications, nil
	}
	return nil, &NotLoadedError{edge: "notifications"}
}

// PersonAssignmentOrErr returns the PersonAssignment value or an error if the edge
// was not loaded in eager-loading.
func (e UserEdges) PersonAssignmentOrErr() ([]*PersonAssignment, error) {
	if e.loadedTypes[11] {
		return e.PersonAssignment, nil
	}
	return nil, &NotLoadedError{edge: "person_assignment"}
}

// scanValues returns the types for scanning values from sql.Rows.
func (*User) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case user.FieldEmailConfirmed:
			values[i] = new(sql.NullBool)
		case user.FieldID, user.FieldStatus, user.FieldVersion, user.FieldDepartmentID:
			values[i] = new(sql.NullInt64)
		case user.FieldUsername, user.FieldPassword, user.FieldPhone, user.FieldEmail, user.FieldName, user.FieldGender, user.FieldDepartmentPosition, user.FieldProfileImage, user.FieldState:
			values[i] = new(sql.NullString)
		case user.FieldDeletedAt, user.FieldCreatedAt, user.FieldUpdatedAt, user.FieldSuspendedAt:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the User fields.
func (u *User) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case user.FieldID:
			value, ok := values[i].(*sql.NullInt64)
			if !ok {
				return fmt.Errorf("unexpected type %T for field id", value)
			}
			u.ID = int(value.Int64)
		case user.FieldDeletedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field deleted_at", values[i])
			} else if value.Valid {
				u.DeletedAt = value.Time
			}
		case user.FieldStatus:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field status", values[i])
			} else if value.Valid {
				u.Status = int8(value.Int64)
			}
		case user.FieldVersion:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field version", values[i])
			} else if value.Valid {
				u.Version = int(value.Int64)
			}
		case user.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				u.CreatedAt = value.Time
			}
		case user.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				u.UpdatedAt = value.Time
			}
		case user.FieldUsername:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field username", values[i])
			} else if value.Valid {
				u.Username = value.String
			}
		case user.FieldPassword:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field password", values[i])
			} else if value.Valid {
				u.Password = value.String
			}
		case user.FieldPhone:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field phone", values[i])
			} else if value.Valid {
				u.Phone = value.String
			}
		case user.FieldEmail:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field email", values[i])
			} else if value.Valid {
				u.Email = value.String
			}
		case user.FieldEmailConfirmed:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field email_confirmed", values[i])
			} else if value.Valid {
				u.EmailConfirmed = value.Bool
			}
		case user.FieldName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field name", values[i])
			} else if value.Valid {
				u.Name = value.String
			}
		case user.FieldGender:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field gender", values[i])
			} else if value.Valid {
				u.Gender = value.String
			}
		case user.FieldDepartmentID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field department_id", values[i])
			} else if value.Valid {
				u.DepartmentID = int(value.Int64)
			}
		case user.FieldDepartmentPosition:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field department_position", values[i])
			} else if value.Valid {
				u.DepartmentPosition = value.String
			}
		case user.FieldProfileImage:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field profile_image", values[i])
			} else if value.Valid {
				u.ProfileImage = value.String
			}
		case user.FieldSuspendedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field suspended_at", values[i])
			} else if value.Valid {
				u.SuspendedAt = value.Time
			}
		case user.FieldState:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field state", values[i])
			} else if value.Valid {
				u.State = user.State(value.String)
			}
		default:
			u.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the User.
// This includes values selected through modifiers, order, etc.
func (u *User) Value(name string) (ent.Value, error) {
	return u.selectValues.Get(name)
}

// QueryAssignment queries the "assignment" edge of the User entity.
func (u *User) QueryAssignment() *TaskAssignmentQuery {
	return NewUserClient(u.config).QueryAssignment(u)
}

// QueryAssignedPerson queries the "assigned_person" edge of the User entity.
func (u *User) QueryAssignedPerson() *PersonQuery {
	return NewUserClient(u.config).QueryAssignedPerson(u)
}

// QueryNotes queries the "notes" edge of the User entity.
func (u *User) QueryNotes() *NoteQuery {
	return NewUserClient(u.config).QueryNotes(u)
}

// QueryTaskNotes queries the "task_notes" edge of the User entity.
func (u *User) QueryTaskNotes() *TaskNoteQuery {
	return NewUserClient(u.config).QueryTaskNotes(u)
}

// QueryAppointments queries the "appointments" edge of the User entity.
func (u *User) QueryAppointments() *AppointmentQuery {
	return NewUserClient(u.config).QueryAppointments(u)
}

// QueryCalls queries the "calls" edge of the User entity.
func (u *User) QueryCalls() *CallQuery {
	return NewUserClient(u.config).QueryCalls(u)
}

// QuerySchedules queries the "schedules" edge of the User entity.
func (u *User) QuerySchedules() *ScheduleQuery {
	return NewUserClient(u.config).QuerySchedules(u)
}

// QueryMessageHistories queries the "message_histories" edge of the User entity.
func (u *User) QueryMessageHistories() *MessageHistoryQuery {
	return NewUserClient(u.config).QueryMessageHistories(u)
}

// QueryData queries the "data" edge of the User entity.
func (u *User) QueryData() *UserDataQuery {
	return NewUserClient(u.config).QueryData(u)
}

// QueryExportJobs queries the "export_jobs" edge of the User entity.
func (u *User) QueryExportJobs() *ExportJobQuery {
	return NewUserClient(u.config).QueryExportJobs(u)
}

// QueryNotifications queries the "notifications" edge of the User entity.
func (u *User) QueryNotifications() *NotificationQuery {
	return NewUserClient(u.config).QueryNotifications(u)
}

// QueryPersonAssignment queries the "person_assignment" edge of the User entity.
func (u *User) QueryPersonAssignment() *PersonAssignmentQuery {
	return NewUserClient(u.config).QueryPersonAssignment(u)
}

// Update returns a builder for updating this User.
// Note that you need to call User.Unwrap() before calling this method if this User
// was returned from a transaction, and the transaction was committed or rolled back.
func (u *User) Update() *UserUpdateOne {
	return NewUserClient(u.config).UpdateOne(u)
}

// Unwrap unwraps the User entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (u *User) Unwrap() *User {
	_tx, ok := u.config.driver.(*txDriver)
	if !ok {
		panic("ent: User is not a transactional entity")
	}
	u.config.driver = _tx.drv
	return u
}

// String implements the fmt.Stringer.
func (u *User) String() string {
	var builder strings.Builder
	builder.WriteString("User(")
	builder.WriteString(fmt.Sprintf("id=%v, ", u.ID))
	builder.WriteString("deleted_at=")
	builder.WriteString(u.DeletedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("status=")
	builder.WriteString(fmt.Sprintf("%v", u.Status))
	builder.WriteString(", ")
	builder.WriteString("version=")
	builder.WriteString(fmt.Sprintf("%v", u.Version))
	builder.WriteString(", ")
	builder.WriteString("created_at=")
	builder.WriteString(u.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(u.UpdatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("username=")
	builder.WriteString(u.Username)
	builder.WriteString(", ")
	builder.WriteString("password=")
	builder.WriteString(u.Password)
	builder.WriteString(", ")
	builder.WriteString("phone=")
	builder.WriteString(u.Phone)
	builder.WriteString(", ")
	builder.WriteString("email=")
	builder.WriteString(u.Email)
	builder.WriteString(", ")
	builder.WriteString("email_confirmed=")
	builder.WriteString(fmt.Sprintf("%v", u.EmailConfirmed))
	builder.WriteString(", ")
	builder.WriteString("name=")
	builder.WriteString(u.Name)
	builder.WriteString(", ")
	builder.WriteString("gender=")
	builder.WriteString(u.Gender)
	builder.WriteString(", ")
	builder.WriteString("department_id=")
	builder.WriteString(fmt.Sprintf("%v", u.DepartmentID))
	builder.WriteString(", ")
	builder.WriteString("department_position=")
	builder.WriteString(u.DepartmentPosition)
	builder.WriteString(", ")
	builder.WriteString("profile_image=")
	builder.WriteString(u.ProfileImage)
	builder.WriteString(", ")
	builder.WriteString("suspended_at=")
	builder.WriteString(u.SuspendedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("state=")
	builder.WriteString(fmt.Sprintf("%v", u.State))
	builder.WriteByte(')')
	return builder.String()
}

// MarshalJSON implements the json.Marshaler interface.
func (u *User) MarshalJSON() ([]byte, error) {
	type Alias User
	return json.Marshal(&struct {
		*Alias
		UserEdges
	}{
		Alias:     (*Alias)(u),
		UserEdges: u.Edges,
	})
}

// MarshalSimpleTime
func (u *User) MarshalSimpleTime() ([]byte, error) {
	type Alias User
	return json.Marshal(&struct {
		*Alias
		UserEdges
		DeletedAt   string `json:"deleted_at,omitempty"`
		CreatedAt   string `json:"created_at,omitempty"`
		UpdatedAt   string `json:"updated_at,omitempty"`
		SuspendedAt string `json:"suspended_at,omitempty"`
	}{
		Alias:       (*Alias)(u),
		UserEdges:   u.Edges,
		DeletedAt:   u.DeletedAt.Format("15:04 02/01/2006"),
		CreatedAt:   u.CreatedAt.Format("15:04 02/01/2006"),
		UpdatedAt:   u.UpdatedAt.Format("15:04 02/01/2006"),
		SuspendedAt: u.SuspendedAt.Format("15:04 02/01/2006"),
	})
}

// Users is a parsable slice of User.
type Users []*User
