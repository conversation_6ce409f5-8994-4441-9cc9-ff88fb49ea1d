// Code generated by ent, DO NOT EDIT.

package ent

import (
	"bcare/ent/discount"
	"bcare/ent/discountusage"
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// DiscountCreate is the builder for creating a Discount entity.
type DiscountCreate struct {
	config
	mutation *DiscountMutation
	hooks    []Hook
}

// SetDeletedAt sets the "deleted_at" field.
func (dc *DiscountCreate) SetDeletedAt(t time.Time) *DiscountCreate {
	dc.mutation.SetDeletedAt(t)
	return dc
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (dc *DiscountCreate) SetNillableDeletedAt(t *time.Time) *DiscountCreate {
	if t != nil {
		dc.SetDeletedAt(*t)
	}
	return dc
}

// SetStatus sets the "status" field.
func (dc *DiscountCreate) SetStatus(i int8) *DiscountCreate {
	dc.mutation.SetStatus(i)
	return dc
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (dc *DiscountCreate) SetNillableStatus(i *int8) *DiscountCreate {
	if i != nil {
		dc.SetStatus(*i)
	}
	return dc
}

// SetVersion sets the "version" field.
func (dc *DiscountCreate) SetVersion(i int) *DiscountCreate {
	dc.mutation.SetVersion(i)
	return dc
}

// SetNillableVersion sets the "version" field if the given value is not nil.
func (dc *DiscountCreate) SetNillableVersion(i *int) *DiscountCreate {
	if i != nil {
		dc.SetVersion(*i)
	}
	return dc
}

// SetCreatedAt sets the "created_at" field.
func (dc *DiscountCreate) SetCreatedAt(t time.Time) *DiscountCreate {
	dc.mutation.SetCreatedAt(t)
	return dc
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (dc *DiscountCreate) SetNillableCreatedAt(t *time.Time) *DiscountCreate {
	if t != nil {
		dc.SetCreatedAt(*t)
	}
	return dc
}

// SetUpdatedAt sets the "updated_at" field.
func (dc *DiscountCreate) SetUpdatedAt(t time.Time) *DiscountCreate {
	dc.mutation.SetUpdatedAt(t)
	return dc
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (dc *DiscountCreate) SetNillableUpdatedAt(t *time.Time) *DiscountCreate {
	if t != nil {
		dc.SetUpdatedAt(*t)
	}
	return dc
}

// SetName sets the "name" field.
func (dc *DiscountCreate) SetName(s string) *DiscountCreate {
	dc.mutation.SetName(s)
	return dc
}

// SetType sets the "type" field.
func (dc *DiscountCreate) SetType(s string) *DiscountCreate {
	dc.mutation.SetType(s)
	return dc
}

// SetValue sets the "value" field.
func (dc *DiscountCreate) SetValue(f float64) *DiscountCreate {
	dc.mutation.SetValue(f)
	return dc
}

// SetScope sets the "scope" field.
func (dc *DiscountCreate) SetScope(s string) *DiscountCreate {
	dc.mutation.SetScope(s)
	return dc
}

// SetCondition sets the "condition" field.
func (dc *DiscountCreate) SetCondition(s string) *DiscountCreate {
	dc.mutation.SetCondition(s)
	return dc
}

// SetNillableCondition sets the "condition" field if the given value is not nil.
func (dc *DiscountCreate) SetNillableCondition(s *string) *DiscountCreate {
	if s != nil {
		dc.SetCondition(*s)
	}
	return dc
}

// SetUsageType sets the "usage_type" field.
func (dc *DiscountCreate) SetUsageType(s string) *DiscountCreate {
	dc.mutation.SetUsageType(s)
	return dc
}

// SetDescription sets the "description" field.
func (dc *DiscountCreate) SetDescription(s string) *DiscountCreate {
	dc.mutation.SetDescription(s)
	return dc
}

// SetNillableDescription sets the "description" field if the given value is not nil.
func (dc *DiscountCreate) SetNillableDescription(s *string) *DiscountCreate {
	if s != nil {
		dc.SetDescription(*s)
	}
	return dc
}

// SetMeta sets the "meta" field.
func (dc *DiscountCreate) SetMeta(m map[string]interface{}) *DiscountCreate {
	dc.mutation.SetMeta(m)
	return dc
}

// SetStart sets the "start" field.
func (dc *DiscountCreate) SetStart(t time.Time) *DiscountCreate {
	dc.mutation.SetStart(t)
	return dc
}

// SetNillableStart sets the "start" field if the given value is not nil.
func (dc *DiscountCreate) SetNillableStart(t *time.Time) *DiscountCreate {
	if t != nil {
		dc.SetStart(*t)
	}
	return dc
}

// SetEnd sets the "end" field.
func (dc *DiscountCreate) SetEnd(t time.Time) *DiscountCreate {
	dc.mutation.SetEnd(t)
	return dc
}

// SetNillableEnd sets the "end" field if the given value is not nil.
func (dc *DiscountCreate) SetNillableEnd(t *time.Time) *DiscountCreate {
	if t != nil {
		dc.SetEnd(*t)
	}
	return dc
}

// AddDiscountUsageIDs adds the "discount_usages" edge to the DiscountUsage entity by IDs.
func (dc *DiscountCreate) AddDiscountUsageIDs(ids ...int) *DiscountCreate {
	dc.mutation.AddDiscountUsageIDs(ids...)
	return dc
}

// AddDiscountUsages adds the "discount_usages" edges to the DiscountUsage entity.
func (dc *DiscountCreate) AddDiscountUsages(d ...*DiscountUsage) *DiscountCreate {
	ids := make([]int, len(d))
	for i := range d {
		ids[i] = d[i].ID
	}
	return dc.AddDiscountUsageIDs(ids...)
}

// Mutation returns the DiscountMutation object of the builder.
func (dc *DiscountCreate) Mutation() *DiscountMutation {
	return dc.mutation
}

// Save creates the Discount in the database.
func (dc *DiscountCreate) Save(ctx context.Context) (*Discount, error) {
	if err := dc.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, dc.sqlSave, dc.mutation, dc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (dc *DiscountCreate) SaveX(ctx context.Context) *Discount {
	v, err := dc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (dc *DiscountCreate) Exec(ctx context.Context) error {
	_, err := dc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (dc *DiscountCreate) ExecX(ctx context.Context) {
	if err := dc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (dc *DiscountCreate) defaults() error {
	if _, ok := dc.mutation.Status(); !ok {
		v := discount.DefaultStatus
		dc.mutation.SetStatus(v)
	}
	if _, ok := dc.mutation.Version(); !ok {
		v := discount.DefaultVersion
		dc.mutation.SetVersion(v)
	}
	if _, ok := dc.mutation.CreatedAt(); !ok {
		if discount.DefaultCreatedAt == nil {
			return fmt.Errorf("ent: uninitialized discount.DefaultCreatedAt (forgotten import ent/runtime?)")
		}
		v := discount.DefaultCreatedAt()
		dc.mutation.SetCreatedAt(v)
	}
	if _, ok := dc.mutation.UpdatedAt(); !ok {
		if discount.DefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized discount.DefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := discount.DefaultUpdatedAt()
		dc.mutation.SetUpdatedAt(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (dc *DiscountCreate) check() error {
	if _, ok := dc.mutation.Status(); !ok {
		return &ValidationError{Name: "status", err: errors.New(`ent: missing required field "Discount.status"`)}
	}
	if _, ok := dc.mutation.Version(); !ok {
		return &ValidationError{Name: "version", err: errors.New(`ent: missing required field "Discount.version"`)}
	}
	if _, ok := dc.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "Discount.created_at"`)}
	}
	if _, ok := dc.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "Discount.updated_at"`)}
	}
	if _, ok := dc.mutation.Name(); !ok {
		return &ValidationError{Name: "name", err: errors.New(`ent: missing required field "Discount.name"`)}
	}
	if _, ok := dc.mutation.GetType(); !ok {
		return &ValidationError{Name: "type", err: errors.New(`ent: missing required field "Discount.type"`)}
	}
	if _, ok := dc.mutation.Value(); !ok {
		return &ValidationError{Name: "value", err: errors.New(`ent: missing required field "Discount.value"`)}
	}
	if _, ok := dc.mutation.Scope(); !ok {
		return &ValidationError{Name: "scope", err: errors.New(`ent: missing required field "Discount.scope"`)}
	}
	if _, ok := dc.mutation.UsageType(); !ok {
		return &ValidationError{Name: "usage_type", err: errors.New(`ent: missing required field "Discount.usage_type"`)}
	}
	return nil
}

func (dc *DiscountCreate) sqlSave(ctx context.Context) (*Discount, error) {
	if err := dc.check(); err != nil {
		return nil, err
	}
	_node, _spec := dc.createSpec()
	if err := sqlgraph.CreateNode(ctx, dc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	id := _spec.ID.Value.(int64)
	_node.ID = int(id)
	dc.mutation.id = &_node.ID
	dc.mutation.done = true
	return _node, nil
}

func (dc *DiscountCreate) createSpec() (*Discount, *sqlgraph.CreateSpec) {
	var (
		_node = &Discount{config: dc.config}
		_spec = sqlgraph.NewCreateSpec(discount.Table, sqlgraph.NewFieldSpec(discount.FieldID, field.TypeInt))
	)
	if value, ok := dc.mutation.DeletedAt(); ok {
		_spec.SetField(discount.FieldDeletedAt, field.TypeTime, value)
		_node.DeletedAt = value
	}
	if value, ok := dc.mutation.Status(); ok {
		_spec.SetField(discount.FieldStatus, field.TypeInt8, value)
		_node.Status = value
	}
	if value, ok := dc.mutation.Version(); ok {
		_spec.SetField(discount.FieldVersion, field.TypeInt, value)
		_node.Version = value
	}
	if value, ok := dc.mutation.CreatedAt(); ok {
		_spec.SetField(discount.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := dc.mutation.UpdatedAt(); ok {
		_spec.SetField(discount.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	if value, ok := dc.mutation.Name(); ok {
		_spec.SetField(discount.FieldName, field.TypeString, value)
		_node.Name = value
	}
	if value, ok := dc.mutation.GetType(); ok {
		_spec.SetField(discount.FieldType, field.TypeString, value)
		_node.Type = value
	}
	if value, ok := dc.mutation.Value(); ok {
		_spec.SetField(discount.FieldValue, field.TypeFloat64, value)
		_node.Value = value
	}
	if value, ok := dc.mutation.Scope(); ok {
		_spec.SetField(discount.FieldScope, field.TypeString, value)
		_node.Scope = value
	}
	if value, ok := dc.mutation.Condition(); ok {
		_spec.SetField(discount.FieldCondition, field.TypeString, value)
		_node.Condition = value
	}
	if value, ok := dc.mutation.UsageType(); ok {
		_spec.SetField(discount.FieldUsageType, field.TypeString, value)
		_node.UsageType = value
	}
	if value, ok := dc.mutation.Description(); ok {
		_spec.SetField(discount.FieldDescription, field.TypeString, value)
		_node.Description = value
	}
	if value, ok := dc.mutation.Meta(); ok {
		_spec.SetField(discount.FieldMeta, field.TypeJSON, value)
		_node.Meta = value
	}
	if value, ok := dc.mutation.Start(); ok {
		_spec.SetField(discount.FieldStart, field.TypeTime, value)
		_node.Start = value
	}
	if value, ok := dc.mutation.End(); ok {
		_spec.SetField(discount.FieldEnd, field.TypeTime, value)
		_node.End = value
	}
	if nodes := dc.mutation.DiscountUsagesIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: true,
			Table:   discount.DiscountUsagesTable,
			Columns: []string{discount.DiscountUsagesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(discountusage.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	return _node, _spec
}

// DiscountCreateBulk is the builder for creating many Discount entities in bulk.
type DiscountCreateBulk struct {
	config
	err      error
	builders []*DiscountCreate
}

// Save creates the Discount entities in the database.
func (dcb *DiscountCreateBulk) Save(ctx context.Context) ([]*Discount, error) {
	if dcb.err != nil {
		return nil, dcb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(dcb.builders))
	nodes := make([]*Discount, len(dcb.builders))
	mutators := make([]Mutator, len(dcb.builders))
	for i := range dcb.builders {
		func(i int, root context.Context) {
			builder := dcb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*DiscountMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, dcb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, dcb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = int(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, dcb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (dcb *DiscountCreateBulk) SaveX(ctx context.Context) []*Discount {
	v, err := dcb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (dcb *DiscountCreateBulk) Exec(ctx context.Context) error {
	_, err := dcb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (dcb *DiscountCreateBulk) ExecX(ctx context.Context) {
	if err := dcb.Exec(ctx); err != nil {
		panic(err)
	}
}
