// Code generated by ent, DO NOT EDIT.

package ent

import (
	"bcare/ent/appointment"
	"bcare/ent/call"
	"bcare/ent/deal"
	"bcare/ent/formsubmission"
	"bcare/ent/installmentplan"
	"bcare/ent/issue"
	"bcare/ent/messagehistory"
	"bcare/ent/person"
	"bcare/ent/personassignment"
	"bcare/ent/personreferral"
	"bcare/ent/predicate"
	"bcare/ent/tag"
	"bcare/ent/tagperson"
	"bcare/ent/task"
	"bcare/ent/term"
	"bcare/ent/track"
	"bcare/ent/user"
	"context"
	"database/sql/driver"
	"fmt"
	"math"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// PersonQuery is the builder for querying Person entities.
type PersonQuery struct {
	config
	ctx                  *QueryContext
	order                []person.OrderOption
	inters               []Interceptor
	predicates           []predicate.Person
	withDeals            *DealQuery
	withTags             *TagQuery
	withFormSubmissions  *FormSubmissionQuery
	withPlans            *InstallmentPlanQuery
	withCalls            *CallQuery
	withTasks            *TaskQuery
	withIssues           *IssueQuery
	withAppointments     *AppointmentQuery
	withTracks           *TrackQuery
	withMessageHistories *MessageHistoryQuery
	withAssignees        *UserQuery
	withJob              *TermQuery
	withSource           *TermQuery
	withCreator          *UserQuery
	withReferredBy       *PersonReferralQuery
	withTagPerson        *TagPersonQuery
	withAssignment       *PersonAssignmentQuery
	modifiers            []func(*sql.Selector)
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the PersonQuery builder.
func (pq *PersonQuery) Where(ps ...predicate.Person) *PersonQuery {
	pq.predicates = append(pq.predicates, ps...)
	return pq
}

// Limit the number of records to be returned by this query.
func (pq *PersonQuery) Limit(limit int) *PersonQuery {
	pq.ctx.Limit = &limit
	return pq
}

// Offset to start from.
func (pq *PersonQuery) Offset(offset int) *PersonQuery {
	pq.ctx.Offset = &offset
	return pq
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (pq *PersonQuery) Unique(unique bool) *PersonQuery {
	pq.ctx.Unique = &unique
	return pq
}

// Order specifies how the records should be ordered.
func (pq *PersonQuery) Order(o ...person.OrderOption) *PersonQuery {
	pq.order = append(pq.order, o...)
	return pq
}

// QueryDeals chains the current query on the "deals" edge.
func (pq *PersonQuery) QueryDeals() *DealQuery {
	query := (&DealClient{config: pq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := pq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := pq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(person.Table, person.FieldID, selector),
			sqlgraph.To(deal.Table, deal.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, person.DealsTable, person.DealsColumn),
		)
		fromU = sqlgraph.SetNeighbors(pq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// QueryTags chains the current query on the "tags" edge.
func (pq *PersonQuery) QueryTags() *TagQuery {
	query := (&TagClient{config: pq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := pq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := pq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(person.Table, person.FieldID, selector),
			sqlgraph.To(tag.Table, tag.FieldID),
			sqlgraph.Edge(sqlgraph.M2M, false, person.TagsTable, person.TagsPrimaryKey...),
		)
		fromU = sqlgraph.SetNeighbors(pq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// QueryFormSubmissions chains the current query on the "form_submissions" edge.
func (pq *PersonQuery) QueryFormSubmissions() *FormSubmissionQuery {
	query := (&FormSubmissionClient{config: pq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := pq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := pq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(person.Table, person.FieldID, selector),
			sqlgraph.To(formsubmission.Table, formsubmission.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, person.FormSubmissionsTable, person.FormSubmissionsColumn),
		)
		fromU = sqlgraph.SetNeighbors(pq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// QueryPlans chains the current query on the "plans" edge.
func (pq *PersonQuery) QueryPlans() *InstallmentPlanQuery {
	query := (&InstallmentPlanClient{config: pq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := pq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := pq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(person.Table, person.FieldID, selector),
			sqlgraph.To(installmentplan.Table, installmentplan.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, person.PlansTable, person.PlansColumn),
		)
		fromU = sqlgraph.SetNeighbors(pq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// QueryCalls chains the current query on the "calls" edge.
func (pq *PersonQuery) QueryCalls() *CallQuery {
	query := (&CallClient{config: pq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := pq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := pq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(person.Table, person.FieldID, selector),
			sqlgraph.To(call.Table, call.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, person.CallsTable, person.CallsColumn),
		)
		fromU = sqlgraph.SetNeighbors(pq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// QueryTasks chains the current query on the "tasks" edge.
func (pq *PersonQuery) QueryTasks() *TaskQuery {
	query := (&TaskClient{config: pq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := pq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := pq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(person.Table, person.FieldID, selector),
			sqlgraph.To(task.Table, task.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, person.TasksTable, person.TasksColumn),
		)
		fromU = sqlgraph.SetNeighbors(pq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// QueryIssues chains the current query on the "issues" edge.
func (pq *PersonQuery) QueryIssues() *IssueQuery {
	query := (&IssueClient{config: pq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := pq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := pq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(person.Table, person.FieldID, selector),
			sqlgraph.To(issue.Table, issue.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, person.IssuesTable, person.IssuesColumn),
		)
		fromU = sqlgraph.SetNeighbors(pq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// QueryAppointments chains the current query on the "appointments" edge.
func (pq *PersonQuery) QueryAppointments() *AppointmentQuery {
	query := (&AppointmentClient{config: pq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := pq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := pq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(person.Table, person.FieldID, selector),
			sqlgraph.To(appointment.Table, appointment.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, person.AppointmentsTable, person.AppointmentsColumn),
		)
		fromU = sqlgraph.SetNeighbors(pq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// QueryTracks chains the current query on the "tracks" edge.
func (pq *PersonQuery) QueryTracks() *TrackQuery {
	query := (&TrackClient{config: pq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := pq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := pq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(person.Table, person.FieldID, selector),
			sqlgraph.To(track.Table, track.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, person.TracksTable, person.TracksColumn),
		)
		fromU = sqlgraph.SetNeighbors(pq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// QueryMessageHistories chains the current query on the "message_histories" edge.
func (pq *PersonQuery) QueryMessageHistories() *MessageHistoryQuery {
	query := (&MessageHistoryClient{config: pq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := pq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := pq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(person.Table, person.FieldID, selector),
			sqlgraph.To(messagehistory.Table, messagehistory.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, person.MessageHistoriesTable, person.MessageHistoriesColumn),
		)
		fromU = sqlgraph.SetNeighbors(pq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// QueryAssignees chains the current query on the "assignees" edge.
func (pq *PersonQuery) QueryAssignees() *UserQuery {
	query := (&UserClient{config: pq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := pq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := pq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(person.Table, person.FieldID, selector),
			sqlgraph.To(user.Table, user.FieldID),
			sqlgraph.Edge(sqlgraph.M2M, false, person.AssigneesTable, person.AssigneesPrimaryKey...),
		)
		fromU = sqlgraph.SetNeighbors(pq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// QueryJob chains the current query on the "job" edge.
func (pq *PersonQuery) QueryJob() *TermQuery {
	query := (&TermClient{config: pq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := pq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := pq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(person.Table, person.FieldID, selector),
			sqlgraph.To(term.Table, term.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, person.JobTable, person.JobColumn),
		)
		fromU = sqlgraph.SetNeighbors(pq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// QuerySource chains the current query on the "source" edge.
func (pq *PersonQuery) QuerySource() *TermQuery {
	query := (&TermClient{config: pq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := pq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := pq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(person.Table, person.FieldID, selector),
			sqlgraph.To(term.Table, term.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, person.SourceTable, person.SourceColumn),
		)
		fromU = sqlgraph.SetNeighbors(pq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// QueryCreator chains the current query on the "creator" edge.
func (pq *PersonQuery) QueryCreator() *UserQuery {
	query := (&UserClient{config: pq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := pq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := pq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(person.Table, person.FieldID, selector),
			sqlgraph.To(user.Table, user.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, person.CreatorTable, person.CreatorColumn),
		)
		fromU = sqlgraph.SetNeighbors(pq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// QueryReferredBy chains the current query on the "referred_by" edge.
func (pq *PersonQuery) QueryReferredBy() *PersonReferralQuery {
	query := (&PersonReferralClient{config: pq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := pq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := pq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(person.Table, person.FieldID, selector),
			sqlgraph.To(personreferral.Table, personreferral.FieldID),
			sqlgraph.Edge(sqlgraph.O2O, false, person.ReferredByTable, person.ReferredByColumn),
		)
		fromU = sqlgraph.SetNeighbors(pq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// QueryTagPerson chains the current query on the "tag_person" edge.
func (pq *PersonQuery) QueryTagPerson() *TagPersonQuery {
	query := (&TagPersonClient{config: pq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := pq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := pq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(person.Table, person.FieldID, selector),
			sqlgraph.To(tagperson.Table, tagperson.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, person.TagPersonTable, person.TagPersonColumn),
		)
		fromU = sqlgraph.SetNeighbors(pq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// QueryAssignment chains the current query on the "assignment" edge.
func (pq *PersonQuery) QueryAssignment() *PersonAssignmentQuery {
	query := (&PersonAssignmentClient{config: pq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := pq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := pq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(person.Table, person.FieldID, selector),
			sqlgraph.To(personassignment.Table, personassignment.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, person.AssignmentTable, person.AssignmentColumn),
		)
		fromU = sqlgraph.SetNeighbors(pq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// First returns the first Person entity from the query.
// Returns a *NotFoundError when no Person was found.
func (pq *PersonQuery) First(ctx context.Context) (*Person, error) {
	nodes, err := pq.Limit(1).All(setContextOp(ctx, pq.ctx, ent.OpQueryFirst))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{person.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (pq *PersonQuery) FirstX(ctx context.Context) *Person {
	node, err := pq.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first Person ID from the query.
// Returns a *NotFoundError when no Person ID was found.
func (pq *PersonQuery) FirstID(ctx context.Context) (id int, err error) {
	var ids []int
	if ids, err = pq.Limit(1).IDs(setContextOp(ctx, pq.ctx, ent.OpQueryFirstID)); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{person.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (pq *PersonQuery) FirstIDX(ctx context.Context) int {
	id, err := pq.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single Person entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one Person entity is found.
// Returns a *NotFoundError when no Person entities are found.
func (pq *PersonQuery) Only(ctx context.Context) (*Person, error) {
	nodes, err := pq.Limit(2).All(setContextOp(ctx, pq.ctx, ent.OpQueryOnly))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{person.Label}
	default:
		return nil, &NotSingularError{person.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (pq *PersonQuery) OnlyX(ctx context.Context) *Person {
	node, err := pq.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only Person ID in the query.
// Returns a *NotSingularError when more than one Person ID is found.
// Returns a *NotFoundError when no entities are found.
func (pq *PersonQuery) OnlyID(ctx context.Context) (id int, err error) {
	var ids []int
	if ids, err = pq.Limit(2).IDs(setContextOp(ctx, pq.ctx, ent.OpQueryOnlyID)); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{person.Label}
	default:
		err = &NotSingularError{person.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (pq *PersonQuery) OnlyIDX(ctx context.Context) int {
	id, err := pq.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of Persons.
func (pq *PersonQuery) All(ctx context.Context) ([]*Person, error) {
	ctx = setContextOp(ctx, pq.ctx, ent.OpQueryAll)
	if err := pq.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*Person, *PersonQuery]()
	return withInterceptors[[]*Person](ctx, pq, qr, pq.inters)
}

// AllX is like All, but panics if an error occurs.
func (pq *PersonQuery) AllX(ctx context.Context) []*Person {
	nodes, err := pq.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of Person IDs.
func (pq *PersonQuery) IDs(ctx context.Context) (ids []int, err error) {
	if pq.ctx.Unique == nil && pq.path != nil {
		pq.Unique(true)
	}
	ctx = setContextOp(ctx, pq.ctx, ent.OpQueryIDs)
	if err = pq.Select(person.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (pq *PersonQuery) IDsX(ctx context.Context) []int {
	ids, err := pq.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (pq *PersonQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, pq.ctx, ent.OpQueryCount)
	if err := pq.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, pq, querierCount[*PersonQuery](), pq.inters)
}

// CountX is like Count, but panics if an error occurs.
func (pq *PersonQuery) CountX(ctx context.Context) int {
	count, err := pq.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (pq *PersonQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, pq.ctx, ent.OpQueryExist)
	switch _, err := pq.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("ent: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (pq *PersonQuery) ExistX(ctx context.Context) bool {
	exist, err := pq.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the PersonQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (pq *PersonQuery) Clone() *PersonQuery {
	if pq == nil {
		return nil
	}
	return &PersonQuery{
		config:               pq.config,
		ctx:                  pq.ctx.Clone(),
		order:                append([]person.OrderOption{}, pq.order...),
		inters:               append([]Interceptor{}, pq.inters...),
		predicates:           append([]predicate.Person{}, pq.predicates...),
		withDeals:            pq.withDeals.Clone(),
		withTags:             pq.withTags.Clone(),
		withFormSubmissions:  pq.withFormSubmissions.Clone(),
		withPlans:            pq.withPlans.Clone(),
		withCalls:            pq.withCalls.Clone(),
		withTasks:            pq.withTasks.Clone(),
		withIssues:           pq.withIssues.Clone(),
		withAppointments:     pq.withAppointments.Clone(),
		withTracks:           pq.withTracks.Clone(),
		withMessageHistories: pq.withMessageHistories.Clone(),
		withAssignees:        pq.withAssignees.Clone(),
		withJob:              pq.withJob.Clone(),
		withSource:           pq.withSource.Clone(),
		withCreator:          pq.withCreator.Clone(),
		withReferredBy:       pq.withReferredBy.Clone(),
		withTagPerson:        pq.withTagPerson.Clone(),
		withAssignment:       pq.withAssignment.Clone(),
		// clone intermediate query.
		sql:       pq.sql.Clone(),
		path:      pq.path,
		modifiers: append([]func(*sql.Selector){}, pq.modifiers...),
	}
}

// WithDeals tells the query-builder to eager-load the nodes that are connected to
// the "deals" edge. The optional arguments are used to configure the query builder of the edge.
func (pq *PersonQuery) WithDeals(opts ...func(*DealQuery)) *PersonQuery {
	query := (&DealClient{config: pq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	pq.withDeals = query
	return pq
}

// WithTags tells the query-builder to eager-load the nodes that are connected to
// the "tags" edge. The optional arguments are used to configure the query builder of the edge.
func (pq *PersonQuery) WithTags(opts ...func(*TagQuery)) *PersonQuery {
	query := (&TagClient{config: pq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	pq.withTags = query
	return pq
}

// WithFormSubmissions tells the query-builder to eager-load the nodes that are connected to
// the "form_submissions" edge. The optional arguments are used to configure the query builder of the edge.
func (pq *PersonQuery) WithFormSubmissions(opts ...func(*FormSubmissionQuery)) *PersonQuery {
	query := (&FormSubmissionClient{config: pq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	pq.withFormSubmissions = query
	return pq
}

// WithPlans tells the query-builder to eager-load the nodes that are connected to
// the "plans" edge. The optional arguments are used to configure the query builder of the edge.
func (pq *PersonQuery) WithPlans(opts ...func(*InstallmentPlanQuery)) *PersonQuery {
	query := (&InstallmentPlanClient{config: pq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	pq.withPlans = query
	return pq
}

// WithCalls tells the query-builder to eager-load the nodes that are connected to
// the "calls" edge. The optional arguments are used to configure the query builder of the edge.
func (pq *PersonQuery) WithCalls(opts ...func(*CallQuery)) *PersonQuery {
	query := (&CallClient{config: pq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	pq.withCalls = query
	return pq
}

// WithTasks tells the query-builder to eager-load the nodes that are connected to
// the "tasks" edge. The optional arguments are used to configure the query builder of the edge.
func (pq *PersonQuery) WithTasks(opts ...func(*TaskQuery)) *PersonQuery {
	query := (&TaskClient{config: pq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	pq.withTasks = query
	return pq
}

// WithIssues tells the query-builder to eager-load the nodes that are connected to
// the "issues" edge. The optional arguments are used to configure the query builder of the edge.
func (pq *PersonQuery) WithIssues(opts ...func(*IssueQuery)) *PersonQuery {
	query := (&IssueClient{config: pq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	pq.withIssues = query
	return pq
}

// WithAppointments tells the query-builder to eager-load the nodes that are connected to
// the "appointments" edge. The optional arguments are used to configure the query builder of the edge.
func (pq *PersonQuery) WithAppointments(opts ...func(*AppointmentQuery)) *PersonQuery {
	query := (&AppointmentClient{config: pq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	pq.withAppointments = query
	return pq
}

// WithTracks tells the query-builder to eager-load the nodes that are connected to
// the "tracks" edge. The optional arguments are used to configure the query builder of the edge.
func (pq *PersonQuery) WithTracks(opts ...func(*TrackQuery)) *PersonQuery {
	query := (&TrackClient{config: pq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	pq.withTracks = query
	return pq
}

// WithMessageHistories tells the query-builder to eager-load the nodes that are connected to
// the "message_histories" edge. The optional arguments are used to configure the query builder of the edge.
func (pq *PersonQuery) WithMessageHistories(opts ...func(*MessageHistoryQuery)) *PersonQuery {
	query := (&MessageHistoryClient{config: pq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	pq.withMessageHistories = query
	return pq
}

// WithAssignees tells the query-builder to eager-load the nodes that are connected to
// the "assignees" edge. The optional arguments are used to configure the query builder of the edge.
func (pq *PersonQuery) WithAssignees(opts ...func(*UserQuery)) *PersonQuery {
	query := (&UserClient{config: pq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	pq.withAssignees = query
	return pq
}

// WithJob tells the query-builder to eager-load the nodes that are connected to
// the "job" edge. The optional arguments are used to configure the query builder of the edge.
func (pq *PersonQuery) WithJob(opts ...func(*TermQuery)) *PersonQuery {
	query := (&TermClient{config: pq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	pq.withJob = query
	return pq
}

// WithSource tells the query-builder to eager-load the nodes that are connected to
// the "source" edge. The optional arguments are used to configure the query builder of the edge.
func (pq *PersonQuery) WithSource(opts ...func(*TermQuery)) *PersonQuery {
	query := (&TermClient{config: pq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	pq.withSource = query
	return pq
}

// WithCreator tells the query-builder to eager-load the nodes that are connected to
// the "creator" edge. The optional arguments are used to configure the query builder of the edge.
func (pq *PersonQuery) WithCreator(opts ...func(*UserQuery)) *PersonQuery {
	query := (&UserClient{config: pq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	pq.withCreator = query
	return pq
}

// WithReferredBy tells the query-builder to eager-load the nodes that are connected to
// the "referred_by" edge. The optional arguments are used to configure the query builder of the edge.
func (pq *PersonQuery) WithReferredBy(opts ...func(*PersonReferralQuery)) *PersonQuery {
	query := (&PersonReferralClient{config: pq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	pq.withReferredBy = query
	return pq
}

// WithTagPerson tells the query-builder to eager-load the nodes that are connected to
// the "tag_person" edge. The optional arguments are used to configure the query builder of the edge.
func (pq *PersonQuery) WithTagPerson(opts ...func(*TagPersonQuery)) *PersonQuery {
	query := (&TagPersonClient{config: pq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	pq.withTagPerson = query
	return pq
}

// WithAssignment tells the query-builder to eager-load the nodes that are connected to
// the "assignment" edge. The optional arguments are used to configure the query builder of the edge.
func (pq *PersonQuery) WithAssignment(opts ...func(*PersonAssignmentQuery)) *PersonQuery {
	query := (&PersonAssignmentClient{config: pq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	pq.withAssignment = query
	return pq
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		DeletedAt time.Time `json:"deleted_at,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.Person.Query().
//		GroupBy(person.FieldDeletedAt).
//		Aggregate(ent.Count()).
//		Scan(ctx, &v)
func (pq *PersonQuery) GroupBy(field string, fields ...string) *PersonGroupBy {
	pq.ctx.Fields = append([]string{field}, fields...)
	grbuild := &PersonGroupBy{build: pq}
	grbuild.flds = &pq.ctx.Fields
	grbuild.label = person.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		DeletedAt time.Time `json:"deleted_at,omitempty"`
//	}
//
//	client.Person.Query().
//		Select(person.FieldDeletedAt).
//		Scan(ctx, &v)
func (pq *PersonQuery) Select(fields ...string) *PersonSelect {
	pq.ctx.Fields = append(pq.ctx.Fields, fields...)
	sbuild := &PersonSelect{PersonQuery: pq}
	sbuild.label = person.Label
	sbuild.flds, sbuild.scan = &pq.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a PersonSelect configured with the given aggregations.
func (pq *PersonQuery) Aggregate(fns ...AggregateFunc) *PersonSelect {
	return pq.Select().Aggregate(fns...)
}

func (pq *PersonQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range pq.inters {
		if inter == nil {
			return fmt.Errorf("ent: uninitialized interceptor (forgotten import ent/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, pq); err != nil {
				return err
			}
		}
	}
	for _, f := range pq.ctx.Fields {
		if !person.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
		}
	}
	if pq.path != nil {
		prev, err := pq.path(ctx)
		if err != nil {
			return err
		}
		pq.sql = prev
	}
	return nil
}

func (pq *PersonQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*Person, error) {
	var (
		nodes       = []*Person{}
		_spec       = pq.querySpec()
		loadedTypes = [17]bool{
			pq.withDeals != nil,
			pq.withTags != nil,
			pq.withFormSubmissions != nil,
			pq.withPlans != nil,
			pq.withCalls != nil,
			pq.withTasks != nil,
			pq.withIssues != nil,
			pq.withAppointments != nil,
			pq.withTracks != nil,
			pq.withMessageHistories != nil,
			pq.withAssignees != nil,
			pq.withJob != nil,
			pq.withSource != nil,
			pq.withCreator != nil,
			pq.withReferredBy != nil,
			pq.withTagPerson != nil,
			pq.withAssignment != nil,
		}
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*Person).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &Person{config: pq.config}
		nodes = append(nodes, node)
		node.Edges.loadedTypes = loadedTypes
		return node.assignValues(columns, values)
	}
	if len(pq.modifiers) > 0 {
		_spec.Modifiers = pq.modifiers
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, pq.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	if query := pq.withDeals; query != nil {
		if err := pq.loadDeals(ctx, query, nodes,
			func(n *Person) { n.Edges.Deals = []*Deal{} },
			func(n *Person, e *Deal) { n.Edges.Deals = append(n.Edges.Deals, e) }); err != nil {
			return nil, err
		}
	}
	if query := pq.withTags; query != nil {
		if err := pq.loadTags(ctx, query, nodes,
			func(n *Person) { n.Edges.Tags = []*Tag{} },
			func(n *Person, e *Tag) { n.Edges.Tags = append(n.Edges.Tags, e) }); err != nil {
			return nil, err
		}
	}
	if query := pq.withFormSubmissions; query != nil {
		if err := pq.loadFormSubmissions(ctx, query, nodes,
			func(n *Person) { n.Edges.FormSubmissions = []*FormSubmission{} },
			func(n *Person, e *FormSubmission) { n.Edges.FormSubmissions = append(n.Edges.FormSubmissions, e) }); err != nil {
			return nil, err
		}
	}
	if query := pq.withPlans; query != nil {
		if err := pq.loadPlans(ctx, query, nodes,
			func(n *Person) { n.Edges.Plans = []*InstallmentPlan{} },
			func(n *Person, e *InstallmentPlan) { n.Edges.Plans = append(n.Edges.Plans, e) }); err != nil {
			return nil, err
		}
	}
	if query := pq.withCalls; query != nil {
		if err := pq.loadCalls(ctx, query, nodes,
			func(n *Person) { n.Edges.Calls = []*Call{} },
			func(n *Person, e *Call) { n.Edges.Calls = append(n.Edges.Calls, e) }); err != nil {
			return nil, err
		}
	}
	if query := pq.withTasks; query != nil {
		if err := pq.loadTasks(ctx, query, nodes,
			func(n *Person) { n.Edges.Tasks = []*Task{} },
			func(n *Person, e *Task) { n.Edges.Tasks = append(n.Edges.Tasks, e) }); err != nil {
			return nil, err
		}
	}
	if query := pq.withIssues; query != nil {
		if err := pq.loadIssues(ctx, query, nodes,
			func(n *Person) { n.Edges.Issues = []*Issue{} },
			func(n *Person, e *Issue) { n.Edges.Issues = append(n.Edges.Issues, e) }); err != nil {
			return nil, err
		}
	}
	if query := pq.withAppointments; query != nil {
		if err := pq.loadAppointments(ctx, query, nodes,
			func(n *Person) { n.Edges.Appointments = []*Appointment{} },
			func(n *Person, e *Appointment) { n.Edges.Appointments = append(n.Edges.Appointments, e) }); err != nil {
			return nil, err
		}
	}
	if query := pq.withTracks; query != nil {
		if err := pq.loadTracks(ctx, query, nodes,
			func(n *Person) { n.Edges.Tracks = []*Track{} },
			func(n *Person, e *Track) { n.Edges.Tracks = append(n.Edges.Tracks, e) }); err != nil {
			return nil, err
		}
	}
	if query := pq.withMessageHistories; query != nil {
		if err := pq.loadMessageHistories(ctx, query, nodes,
			func(n *Person) { n.Edges.MessageHistories = []*MessageHistory{} },
			func(n *Person, e *MessageHistory) { n.Edges.MessageHistories = append(n.Edges.MessageHistories, e) }); err != nil {
			return nil, err
		}
	}
	if query := pq.withAssignees; query != nil {
		if err := pq.loadAssignees(ctx, query, nodes,
			func(n *Person) { n.Edges.Assignees = []*User{} },
			func(n *Person, e *User) { n.Edges.Assignees = append(n.Edges.Assignees, e) }); err != nil {
			return nil, err
		}
	}
	if query := pq.withJob; query != nil {
		if err := pq.loadJob(ctx, query, nodes, nil,
			func(n *Person, e *Term) { n.Edges.Job = e }); err != nil {
			return nil, err
		}
	}
	if query := pq.withSource; query != nil {
		if err := pq.loadSource(ctx, query, nodes, nil,
			func(n *Person, e *Term) { n.Edges.Source = e }); err != nil {
			return nil, err
		}
	}
	if query := pq.withCreator; query != nil {
		if err := pq.loadCreator(ctx, query, nodes, nil,
			func(n *Person, e *User) { n.Edges.Creator = e }); err != nil {
			return nil, err
		}
	}
	if query := pq.withReferredBy; query != nil {
		if err := pq.loadReferredBy(ctx, query, nodes, nil,
			func(n *Person, e *PersonReferral) { n.Edges.ReferredBy = e }); err != nil {
			return nil, err
		}
	}
	if query := pq.withTagPerson; query != nil {
		if err := pq.loadTagPerson(ctx, query, nodes,
			func(n *Person) { n.Edges.TagPerson = []*TagPerson{} },
			func(n *Person, e *TagPerson) { n.Edges.TagPerson = append(n.Edges.TagPerson, e) }); err != nil {
			return nil, err
		}
	}
	if query := pq.withAssignment; query != nil {
		if err := pq.loadAssignment(ctx, query, nodes,
			func(n *Person) { n.Edges.Assignment = []*PersonAssignment{} },
			func(n *Person, e *PersonAssignment) { n.Edges.Assignment = append(n.Edges.Assignment, e) }); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

func (pq *PersonQuery) loadDeals(ctx context.Context, query *DealQuery, nodes []*Person, init func(*Person), assign func(*Person, *Deal)) error {
	fks := make([]driver.Value, 0, len(nodes))
	nodeids := make(map[int]*Person)
	for i := range nodes {
		fks = append(fks, nodes[i].ID)
		nodeids[nodes[i].ID] = nodes[i]
		if init != nil {
			init(nodes[i])
		}
	}
	if len(query.ctx.Fields) > 0 {
		query.ctx.AppendFieldOnce(deal.FieldPersonID)
	}
	query.Where(predicate.Deal(func(s *sql.Selector) {
		s.Where(sql.InValues(s.C(person.DealsColumn), fks...))
	}))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		fk := n.PersonID
		node, ok := nodeids[fk]
		if !ok {
			return fmt.Errorf(`unexpected referenced foreign-key "person_id" returned %v for node %v`, fk, n.ID)
		}
		assign(node, n)
	}
	return nil
}
func (pq *PersonQuery) loadTags(ctx context.Context, query *TagQuery, nodes []*Person, init func(*Person), assign func(*Person, *Tag)) error {
	edgeIDs := make([]driver.Value, len(nodes))
	byID := make(map[int]*Person)
	nids := make(map[int]map[*Person]struct{})
	for i, node := range nodes {
		edgeIDs[i] = node.ID
		byID[node.ID] = node
		if init != nil {
			init(node)
		}
	}
	query.Where(func(s *sql.Selector) {
		joinT := sql.Table(person.TagsTable)
		s.Join(joinT).On(s.C(tag.FieldID), joinT.C(person.TagsPrimaryKey[1]))
		s.Where(sql.InValues(joinT.C(person.TagsPrimaryKey[0]), edgeIDs...))
		columns := s.SelectedColumns()
		s.Select(joinT.C(person.TagsPrimaryKey[0]))
		s.AppendSelect(columns...)
		s.SetDistinct(false)
	})
	if err := query.prepareQuery(ctx); err != nil {
		return err
	}
	qr := QuerierFunc(func(ctx context.Context, q Query) (Value, error) {
		return query.sqlAll(ctx, func(_ context.Context, spec *sqlgraph.QuerySpec) {
			assign := spec.Assign
			values := spec.ScanValues
			spec.ScanValues = func(columns []string) ([]any, error) {
				values, err := values(columns[1:])
				if err != nil {
					return nil, err
				}
				return append([]any{new(sql.NullInt64)}, values...), nil
			}
			spec.Assign = func(columns []string, values []any) error {
				outValue := int(values[0].(*sql.NullInt64).Int64)
				inValue := int(values[1].(*sql.NullInt64).Int64)
				if nids[inValue] == nil {
					nids[inValue] = map[*Person]struct{}{byID[outValue]: {}}
					return assign(columns[1:], values[1:])
				}
				nids[inValue][byID[outValue]] = struct{}{}
				return nil
			}
		})
	})
	neighbors, err := withInterceptors[[]*Tag](ctx, query, qr, query.inters)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		nodes, ok := nids[n.ID]
		if !ok {
			return fmt.Errorf(`unexpected "tags" node returned %v`, n.ID)
		}
		for kn := range nodes {
			assign(kn, n)
		}
	}
	return nil
}
func (pq *PersonQuery) loadFormSubmissions(ctx context.Context, query *FormSubmissionQuery, nodes []*Person, init func(*Person), assign func(*Person, *FormSubmission)) error {
	fks := make([]driver.Value, 0, len(nodes))
	nodeids := make(map[int]*Person)
	for i := range nodes {
		fks = append(fks, nodes[i].ID)
		nodeids[nodes[i].ID] = nodes[i]
		if init != nil {
			init(nodes[i])
		}
	}
	if len(query.ctx.Fields) > 0 {
		query.ctx.AppendFieldOnce(formsubmission.FieldPersonID)
	}
	query.Where(predicate.FormSubmission(func(s *sql.Selector) {
		s.Where(sql.InValues(s.C(person.FormSubmissionsColumn), fks...))
	}))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		fk := n.PersonID
		if fk == nil {
			return fmt.Errorf(`foreign-key "person_id" is nil for node %v`, n.ID)
		}
		node, ok := nodeids[*fk]
		if !ok {
			return fmt.Errorf(`unexpected referenced foreign-key "person_id" returned %v for node %v`, *fk, n.ID)
		}
		assign(node, n)
	}
	return nil
}
func (pq *PersonQuery) loadPlans(ctx context.Context, query *InstallmentPlanQuery, nodes []*Person, init func(*Person), assign func(*Person, *InstallmentPlan)) error {
	fks := make([]driver.Value, 0, len(nodes))
	nodeids := make(map[int]*Person)
	for i := range nodes {
		fks = append(fks, nodes[i].ID)
		nodeids[nodes[i].ID] = nodes[i]
		if init != nil {
			init(nodes[i])
		}
	}
	if len(query.ctx.Fields) > 0 {
		query.ctx.AppendFieldOnce(installmentplan.FieldPersonID)
	}
	query.Where(predicate.InstallmentPlan(func(s *sql.Selector) {
		s.Where(sql.InValues(s.C(person.PlansColumn), fks...))
	}))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		fk := n.PersonID
		node, ok := nodeids[fk]
		if !ok {
			return fmt.Errorf(`unexpected referenced foreign-key "person_id" returned %v for node %v`, fk, n.ID)
		}
		assign(node, n)
	}
	return nil
}
func (pq *PersonQuery) loadCalls(ctx context.Context, query *CallQuery, nodes []*Person, init func(*Person), assign func(*Person, *Call)) error {
	fks := make([]driver.Value, 0, len(nodes))
	nodeids := make(map[int]*Person)
	for i := range nodes {
		fks = append(fks, nodes[i].ID)
		nodeids[nodes[i].ID] = nodes[i]
		if init != nil {
			init(nodes[i])
		}
	}
	if len(query.ctx.Fields) > 0 {
		query.ctx.AppendFieldOnce(call.FieldPersonID)
	}
	query.Where(predicate.Call(func(s *sql.Selector) {
		s.Where(sql.InValues(s.C(person.CallsColumn), fks...))
	}))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		fk := n.PersonID
		node, ok := nodeids[fk]
		if !ok {
			return fmt.Errorf(`unexpected referenced foreign-key "person_id" returned %v for node %v`, fk, n.ID)
		}
		assign(node, n)
	}
	return nil
}
func (pq *PersonQuery) loadTasks(ctx context.Context, query *TaskQuery, nodes []*Person, init func(*Person), assign func(*Person, *Task)) error {
	fks := make([]driver.Value, 0, len(nodes))
	nodeids := make(map[int]*Person)
	for i := range nodes {
		fks = append(fks, nodes[i].ID)
		nodeids[nodes[i].ID] = nodes[i]
		if init != nil {
			init(nodes[i])
		}
	}
	if len(query.ctx.Fields) > 0 {
		query.ctx.AppendFieldOnce(task.FieldPersonID)
	}
	query.Where(predicate.Task(func(s *sql.Selector) {
		s.Where(sql.InValues(s.C(person.TasksColumn), fks...))
	}))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		fk := n.PersonID
		if fk == nil {
			return fmt.Errorf(`foreign-key "person_id" is nil for node %v`, n.ID)
		}
		node, ok := nodeids[*fk]
		if !ok {
			return fmt.Errorf(`unexpected referenced foreign-key "person_id" returned %v for node %v`, *fk, n.ID)
		}
		assign(node, n)
	}
	return nil
}
func (pq *PersonQuery) loadIssues(ctx context.Context, query *IssueQuery, nodes []*Person, init func(*Person), assign func(*Person, *Issue)) error {
	fks := make([]driver.Value, 0, len(nodes))
	nodeids := make(map[int]*Person)
	for i := range nodes {
		fks = append(fks, nodes[i].ID)
		nodeids[nodes[i].ID] = nodes[i]
		if init != nil {
			init(nodes[i])
		}
	}
	if len(query.ctx.Fields) > 0 {
		query.ctx.AppendFieldOnce(issue.FieldPersonID)
	}
	query.Where(predicate.Issue(func(s *sql.Selector) {
		s.Where(sql.InValues(s.C(person.IssuesColumn), fks...))
	}))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		fk := n.PersonID
		node, ok := nodeids[fk]
		if !ok {
			return fmt.Errorf(`unexpected referenced foreign-key "person_id" returned %v for node %v`, fk, n.ID)
		}
		assign(node, n)
	}
	return nil
}
func (pq *PersonQuery) loadAppointments(ctx context.Context, query *AppointmentQuery, nodes []*Person, init func(*Person), assign func(*Person, *Appointment)) error {
	fks := make([]driver.Value, 0, len(nodes))
	nodeids := make(map[int]*Person)
	for i := range nodes {
		fks = append(fks, nodes[i].ID)
		nodeids[nodes[i].ID] = nodes[i]
		if init != nil {
			init(nodes[i])
		}
	}
	if len(query.ctx.Fields) > 0 {
		query.ctx.AppendFieldOnce(appointment.FieldPersonID)
	}
	query.Where(predicate.Appointment(func(s *sql.Selector) {
		s.Where(sql.InValues(s.C(person.AppointmentsColumn), fks...))
	}))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		fk := n.PersonID
		node, ok := nodeids[fk]
		if !ok {
			return fmt.Errorf(`unexpected referenced foreign-key "person_id" returned %v for node %v`, fk, n.ID)
		}
		assign(node, n)
	}
	return nil
}
func (pq *PersonQuery) loadTracks(ctx context.Context, query *TrackQuery, nodes []*Person, init func(*Person), assign func(*Person, *Track)) error {
	fks := make([]driver.Value, 0, len(nodes))
	nodeids := make(map[int]*Person)
	for i := range nodes {
		fks = append(fks, nodes[i].ID)
		nodeids[nodes[i].ID] = nodes[i]
		if init != nil {
			init(nodes[i])
		}
	}
	if len(query.ctx.Fields) > 0 {
		query.ctx.AppendFieldOnce(track.FieldPersonID)
	}
	query.Where(predicate.Track(func(s *sql.Selector) {
		s.Where(sql.InValues(s.C(person.TracksColumn), fks...))
	}))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		fk := n.PersonID
		node, ok := nodeids[fk]
		if !ok {
			return fmt.Errorf(`unexpected referenced foreign-key "person_id" returned %v for node %v`, fk, n.ID)
		}
		assign(node, n)
	}
	return nil
}
func (pq *PersonQuery) loadMessageHistories(ctx context.Context, query *MessageHistoryQuery, nodes []*Person, init func(*Person), assign func(*Person, *MessageHistory)) error {
	fks := make([]driver.Value, 0, len(nodes))
	nodeids := make(map[int]*Person)
	for i := range nodes {
		fks = append(fks, nodes[i].ID)
		nodeids[nodes[i].ID] = nodes[i]
		if init != nil {
			init(nodes[i])
		}
	}
	if len(query.ctx.Fields) > 0 {
		query.ctx.AppendFieldOnce(messagehistory.FieldPersonID)
	}
	query.Where(predicate.MessageHistory(func(s *sql.Selector) {
		s.Where(sql.InValues(s.C(person.MessageHistoriesColumn), fks...))
	}))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		fk := n.PersonID
		node, ok := nodeids[fk]
		if !ok {
			return fmt.Errorf(`unexpected referenced foreign-key "person_id" returned %v for node %v`, fk, n.ID)
		}
		assign(node, n)
	}
	return nil
}
func (pq *PersonQuery) loadAssignees(ctx context.Context, query *UserQuery, nodes []*Person, init func(*Person), assign func(*Person, *User)) error {
	edgeIDs := make([]driver.Value, len(nodes))
	byID := make(map[int]*Person)
	nids := make(map[int]map[*Person]struct{})
	for i, node := range nodes {
		edgeIDs[i] = node.ID
		byID[node.ID] = node
		if init != nil {
			init(node)
		}
	}
	query.Where(func(s *sql.Selector) {
		joinT := sql.Table(person.AssigneesTable)
		s.Join(joinT).On(s.C(user.FieldID), joinT.C(person.AssigneesPrimaryKey[1]))
		s.Where(sql.InValues(joinT.C(person.AssigneesPrimaryKey[0]), edgeIDs...))
		columns := s.SelectedColumns()
		s.Select(joinT.C(person.AssigneesPrimaryKey[0]))
		s.AppendSelect(columns...)
		s.SetDistinct(false)
	})
	if err := query.prepareQuery(ctx); err != nil {
		return err
	}
	qr := QuerierFunc(func(ctx context.Context, q Query) (Value, error) {
		return query.sqlAll(ctx, func(_ context.Context, spec *sqlgraph.QuerySpec) {
			assign := spec.Assign
			values := spec.ScanValues
			spec.ScanValues = func(columns []string) ([]any, error) {
				values, err := values(columns[1:])
				if err != nil {
					return nil, err
				}
				return append([]any{new(sql.NullInt64)}, values...), nil
			}
			spec.Assign = func(columns []string, values []any) error {
				outValue := int(values[0].(*sql.NullInt64).Int64)
				inValue := int(values[1].(*sql.NullInt64).Int64)
				if nids[inValue] == nil {
					nids[inValue] = map[*Person]struct{}{byID[outValue]: {}}
					return assign(columns[1:], values[1:])
				}
				nids[inValue][byID[outValue]] = struct{}{}
				return nil
			}
		})
	})
	neighbors, err := withInterceptors[[]*User](ctx, query, qr, query.inters)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		nodes, ok := nids[n.ID]
		if !ok {
			return fmt.Errorf(`unexpected "assignees" node returned %v`, n.ID)
		}
		for kn := range nodes {
			assign(kn, n)
		}
	}
	return nil
}
func (pq *PersonQuery) loadJob(ctx context.Context, query *TermQuery, nodes []*Person, init func(*Person), assign func(*Person, *Term)) error {
	ids := make([]int, 0, len(nodes))
	nodeids := make(map[int][]*Person)
	for i := range nodes {
		if nodes[i].JobID == nil {
			continue
		}
		fk := *nodes[i].JobID
		if _, ok := nodeids[fk]; !ok {
			ids = append(ids, fk)
		}
		nodeids[fk] = append(nodeids[fk], nodes[i])
	}
	if len(ids) == 0 {
		return nil
	}
	query.Where(term.IDIn(ids...))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		nodes, ok := nodeids[n.ID]
		if !ok {
			return fmt.Errorf(`unexpected foreign-key "job_id" returned %v`, n.ID)
		}
		for i := range nodes {
			assign(nodes[i], n)
		}
	}
	return nil
}
func (pq *PersonQuery) loadSource(ctx context.Context, query *TermQuery, nodes []*Person, init func(*Person), assign func(*Person, *Term)) error {
	ids := make([]int, 0, len(nodes))
	nodeids := make(map[int][]*Person)
	for i := range nodes {
		if nodes[i].SourceID == nil {
			continue
		}
		fk := *nodes[i].SourceID
		if _, ok := nodeids[fk]; !ok {
			ids = append(ids, fk)
		}
		nodeids[fk] = append(nodeids[fk], nodes[i])
	}
	if len(ids) == 0 {
		return nil
	}
	query.Where(term.IDIn(ids...))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		nodes, ok := nodeids[n.ID]
		if !ok {
			return fmt.Errorf(`unexpected foreign-key "source_id" returned %v`, n.ID)
		}
		for i := range nodes {
			assign(nodes[i], n)
		}
	}
	return nil
}
func (pq *PersonQuery) loadCreator(ctx context.Context, query *UserQuery, nodes []*Person, init func(*Person), assign func(*Person, *User)) error {
	ids := make([]int, 0, len(nodes))
	nodeids := make(map[int][]*Person)
	for i := range nodes {
		fk := nodes[i].UserID
		if _, ok := nodeids[fk]; !ok {
			ids = append(ids, fk)
		}
		nodeids[fk] = append(nodeids[fk], nodes[i])
	}
	if len(ids) == 0 {
		return nil
	}
	query.Where(user.IDIn(ids...))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		nodes, ok := nodeids[n.ID]
		if !ok {
			return fmt.Errorf(`unexpected foreign-key "user_id" returned %v`, n.ID)
		}
		for i := range nodes {
			assign(nodes[i], n)
		}
	}
	return nil
}
func (pq *PersonQuery) loadReferredBy(ctx context.Context, query *PersonReferralQuery, nodes []*Person, init func(*Person), assign func(*Person, *PersonReferral)) error {
	fks := make([]driver.Value, 0, len(nodes))
	nodeids := make(map[int]*Person)
	for i := range nodes {
		fks = append(fks, nodes[i].ID)
		nodeids[nodes[i].ID] = nodes[i]
	}
	if len(query.ctx.Fields) > 0 {
		query.ctx.AppendFieldOnce(personreferral.FieldReferredID)
	}
	query.Where(predicate.PersonReferral(func(s *sql.Selector) {
		s.Where(sql.InValues(s.C(person.ReferredByColumn), fks...))
	}))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		fk := n.ReferredID
		node, ok := nodeids[fk]
		if !ok {
			return fmt.Errorf(`unexpected referenced foreign-key "referred_id" returned %v for node %v`, fk, n.ID)
		}
		assign(node, n)
	}
	return nil
}
func (pq *PersonQuery) loadTagPerson(ctx context.Context, query *TagPersonQuery, nodes []*Person, init func(*Person), assign func(*Person, *TagPerson)) error {
	fks := make([]driver.Value, 0, len(nodes))
	nodeids := make(map[int]*Person)
	for i := range nodes {
		fks = append(fks, nodes[i].ID)
		nodeids[nodes[i].ID] = nodes[i]
		if init != nil {
			init(nodes[i])
		}
	}
	if len(query.ctx.Fields) > 0 {
		query.ctx.AppendFieldOnce(tagperson.FieldPersonID)
	}
	query.Where(predicate.TagPerson(func(s *sql.Selector) {
		s.Where(sql.InValues(s.C(person.TagPersonColumn), fks...))
	}))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		fk := n.PersonID
		node, ok := nodeids[fk]
		if !ok {
			return fmt.Errorf(`unexpected referenced foreign-key "person_id" returned %v for node %v`, fk, n.ID)
		}
		assign(node, n)
	}
	return nil
}
func (pq *PersonQuery) loadAssignment(ctx context.Context, query *PersonAssignmentQuery, nodes []*Person, init func(*Person), assign func(*Person, *PersonAssignment)) error {
	fks := make([]driver.Value, 0, len(nodes))
	nodeids := make(map[int]*Person)
	for i := range nodes {
		fks = append(fks, nodes[i].ID)
		nodeids[nodes[i].ID] = nodes[i]
		if init != nil {
			init(nodes[i])
		}
	}
	if len(query.ctx.Fields) > 0 {
		query.ctx.AppendFieldOnce(personassignment.FieldPersonID)
	}
	query.Where(predicate.PersonAssignment(func(s *sql.Selector) {
		s.Where(sql.InValues(s.C(person.AssignmentColumn), fks...))
	}))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		fk := n.PersonID
		node, ok := nodeids[fk]
		if !ok {
			return fmt.Errorf(`unexpected referenced foreign-key "person_id" returned %v for node %v`, fk, n.ID)
		}
		assign(node, n)
	}
	return nil
}

func (pq *PersonQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := pq.querySpec()
	if len(pq.modifiers) > 0 {
		_spec.Modifiers = pq.modifiers
	}
	_spec.Node.Columns = pq.ctx.Fields
	if len(pq.ctx.Fields) > 0 {
		_spec.Unique = pq.ctx.Unique != nil && *pq.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, pq.driver, _spec)
}

func (pq *PersonQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(person.Table, person.Columns, sqlgraph.NewFieldSpec(person.FieldID, field.TypeInt))
	_spec.From = pq.sql
	if unique := pq.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if pq.path != nil {
		_spec.Unique = true
	}
	if fields := pq.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, person.FieldID)
		for i := range fields {
			if fields[i] != person.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
		if pq.withJob != nil {
			_spec.Node.AddColumnOnce(person.FieldJobID)
		}
		if pq.withSource != nil {
			_spec.Node.AddColumnOnce(person.FieldSourceID)
		}
		if pq.withCreator != nil {
			_spec.Node.AddColumnOnce(person.FieldUserID)
		}
	}
	if ps := pq.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := pq.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := pq.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := pq.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (pq *PersonQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(pq.driver.Dialect())
	t1 := builder.Table(person.Table)
	columns := pq.ctx.Fields
	if len(columns) == 0 {
		columns = person.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if pq.sql != nil {
		selector = pq.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if pq.ctx.Unique != nil && *pq.ctx.Unique {
		selector.Distinct()
	}
	for _, m := range pq.modifiers {
		m(selector)
	}
	for _, p := range pq.predicates {
		p(selector)
	}
	for _, p := range pq.order {
		p(selector)
	}
	if offset := pq.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := pq.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// Modify adds a query modifier for attaching custom logic to queries.
func (pq *PersonQuery) Modify(modifiers ...func(s *sql.Selector)) *PersonSelect {
	pq.modifiers = append(pq.modifiers, modifiers...)
	return pq.Select()
}

// PersonGroupBy is the group-by builder for Person entities.
type PersonGroupBy struct {
	selector
	build *PersonQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (pgb *PersonGroupBy) Aggregate(fns ...AggregateFunc) *PersonGroupBy {
	pgb.fns = append(pgb.fns, fns...)
	return pgb
}

// Scan applies the selector query and scans the result into the given value.
func (pgb *PersonGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, pgb.build.ctx, ent.OpQueryGroupBy)
	if err := pgb.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*PersonQuery, *PersonGroupBy](ctx, pgb.build, pgb, pgb.build.inters, v)
}

func (pgb *PersonGroupBy) sqlScan(ctx context.Context, root *PersonQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(pgb.fns))
	for _, fn := range pgb.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*pgb.flds)+len(pgb.fns))
		for _, f := range *pgb.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*pgb.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := pgb.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// PersonSelect is the builder for selecting fields of Person entities.
type PersonSelect struct {
	*PersonQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (ps *PersonSelect) Aggregate(fns ...AggregateFunc) *PersonSelect {
	ps.fns = append(ps.fns, fns...)
	return ps
}

// Scan applies the selector query and scans the result into the given value.
func (ps *PersonSelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, ps.ctx, ent.OpQuerySelect)
	if err := ps.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*PersonQuery, *PersonSelect](ctx, ps.PersonQuery, ps, ps.inters, v)
}

func (ps *PersonSelect) sqlScan(ctx context.Context, root *PersonQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(ps.fns))
	for _, fn := range ps.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*ps.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := ps.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// Modify adds a query modifier for attaching custom logic to queries.
func (ps *PersonSelect) Modify(modifiers ...func(s *sql.Selector)) *PersonSelect {
	ps.modifiers = append(ps.modifiers, modifiers...)
	return ps
}
