# syntax = docker/dockerfile:1.4

FROM golang:1.21 as builder

WORKDIR /app

COPY --link . .

RUN --mount=type=cache,target=/go/pkg/mod go mod download

RUN --mount=type=cache,target=/go/pkg/mod \
    --mount=type=cache,target=/root/.cache/go-build \
    CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o bcare ./api

FROM gcr.io/distroless/static

# Copy execute file
WORKDIR /go/bin
COPY --from=builder /app/bcare bcare

COPY api/etc/ etc/

# Execute app
ENTRYPOINT ["./bcare"]
