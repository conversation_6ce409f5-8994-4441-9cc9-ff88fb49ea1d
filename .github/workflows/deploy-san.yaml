name: Deploy San

on:
  push:
    branches:
      - 'sd/uat'
      - 'sd/release'
  workflow_dispatch: {}

env:
  SSH_HOST: sd.blazy.vn
  SSH_PORT: 24700
  SSH_USERNAME: root
  APP_NAME: sandental
  APP_PORT: 9024
  DB_NAME: bcare_sandental
  DB_USER: ${{ secrets.DB_USER }}
  DB_PASS: ${{ secrets.DB_PASS }}
  OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
  ANTHROPIC_API_KEY: ${{ secrets.ANTHROPIC_API_KEY }}

jobs:
  deploy:
    name: Deploy
    runs-on: ubuntu-latest
    concurrency:
      group: deploy-san
      cancel-in-progress: true
    steps:
      - name: Check out
        uses: actions/checkout@v4

      - name: Set up Go
        uses: actions/setup-go@v4
        with:
          go-version: 'stable'

      - name: Install SSH Key
        uses: shimataro/ssh-key-action@v2
        with:
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          known_hosts: unnecessary

      - name: Adding Known Hosts
        run: ssh-keyscan -p ${{ env.SSH_PORT }} -H ${{ env.SSH_HOST }} >> ~/.ssh/known_hosts

      - name: Install dependencies
        run: go mod download

      - name: Build
        run: go build -o ./dist/bcare ./api

      - name: Copy config
        run: cp -a api/etc/. dist/etc/

      - name: Check deploy directory
        run: |
          if ssh -p ${{ env.SSH_PORT }} ${{ env.SSH_USERNAME }}@${{ env.SSH_HOST }} "[ ! -d '/opt/bcare/${{ env.APP_NAME }}' ]"; then
            echo "Directory /opt/bcare/${{ env.APP_NAME }} does not exist"  
          else 
            echo "Directory /opt/bcare/${{ env.APP_NAME }} already exists"
          fi

      - name: Create deploy directory
        if: ${{ failure() }}
        run: |
          ssh -p ${{ env.SSH_PORT }} ${{ env.SSH_USERNAME }}@${{ env.SSH_HOST }} "sudo mkdir -p /opt/bcare/${{ env.APP_NAME }} && sudo chown -R ${{ env.SSH_USERNAME }}:${{ env.SSH_USERNAME }} /opt/bcare/${{ env.APP_NAME }}"

      - name: Create storage directory for logs
        run: |
          ssh -p ${{ env.SSH_PORT }} ${{ env.SSH_USERNAME }}@${{ env.SSH_HOST }} "mkdir -p /opt/bcare/${{ env.APP_NAME }}/storage/logs && chmod -R 755 /opt/bcare/${{ env.APP_NAME }}/storage"

      - name: Deploy with rsync
        run: rsync -avz -e "ssh -p ${{ env.SSH_PORT }}" ./dist/ ${{ env.SSH_USERNAME }}@${{ env.SSH_HOST }}:/opt/bcare/${{ env.APP_NAME }}

      - name: Create systemd service file
        run: |
          ssh -p ${{ env.SSH_PORT }} ${{ env.SSH_USERNAME }}@${{ env.SSH_HOST }} "echo '[Unit]
          Description=${{ env.APP_NAME }} Bcare Service

          [Service]
          WorkingDirectory=/opt/bcare/${{ env.APP_NAME }}
          Environment=\"DB_USER=${{ env.DB_USER }}\"
          Environment=\"DB_NAME=${{ env.DB_NAME }}\"
          Environment=\"DB_PASS=${{ secrets.UPCARE_DB_PASS }}\"
          Environment=\"APP_PORT=${{ env.APP_PORT }}\"
          Environment=\"OPENAI_API_KEY=${{ env.OPENAI_API_KEY }}\"
          Environment=\"ANTHROPIC_API_KEY=${{ env.ANTHROPIC_API_KEY }}\"
          ExecStart=/opt/bcare/${{ env.APP_NAME }}/bcare -f etc/${{ env.APP_NAME }}.yaml
          Restart=always
          StandardOutput=append:/opt/bcare/${{ env.APP_NAME }}/storage/logs/journal.log
          StandardError=append:/opt/bcare/${{ env.APP_NAME }}/storage/logs/journal.error.log

          [Install]
          WantedBy=multi-user.target' | sudo tee /etc/systemd/system/${{ env.APP_NAME }}.service"

      - name: Reload systemd
        run: ssh -p ${{ env.SSH_PORT }} ${{ env.SSH_USERNAME }}@${{ env.SSH_HOST }} "sudo systemctl daemon-reload"

      - name: Restart service
        run: ssh -p ${{ env.SSH_PORT }} ${{ env.SSH_USERNAME }}@${{ env.SSH_HOST }} "sudo systemctl restart ${{ env.APP_NAME }}"

      - name: Enable service
        run: ssh -p ${{ env.SSH_PORT }} ${{ env.SSH_USERNAME }}@${{ env.SSH_HOST }} "sudo systemctl enable ${{ env.APP_NAME }}"
