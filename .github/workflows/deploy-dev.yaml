name: Deploy Bcare

on:
  push:
    branches:
      - 'dev'
  workflow_dispatch: {}

env:
  SSH_HOST: ec2-13-229-90-169.ap-southeast-1.compute.amazonaws.com
  SSH_PORT: 22
  SSH_USERNAME: ec2-user


jobs:
  deploy:
    name: Deploy
    runs-on: ubuntu-latest
    concurrency:
      group: deploy-dev
      cancel-in-progress: true
    steps:
      - name: Check out
        uses: actions/checkout@v4

      - name: Set up Go
        uses: actions/setup-go@v4
        with:
          go-version: 'stable'

      - name: Install SSH Key
        uses: shimataro/ssh-key-action@v2
        with:
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          known_hosts: unnecessary

      - name: Adding Known Hosts
        run: ssh-keyscan -H ${{ env.SSH_HOST }} >> ~/.ssh/known_hosts

      - name: Install dependencies
        run: go mod download

      - name: Build
        run: go build -o ./dist/bcare ./api

      - name: Copy config
        run: cp -a api/etc/. dist/etc/

      - name: Check deploy directory
        run: |
          if ssh ${{ env.SSH_USERNAME }}@${{ env.SSH_HOST }} "[ ! -d '/opt/bcare' ]"; then
            echo "Directory /opt/bcare does not exist"  
          else 
            echo "Directory /opt/bcare already exists"
          fi

      - name: Create deploy directory
        if: ${{ failure() }}
        run: |
          ssh ${{ env.SSH_USERNAME }}@${{ env.SSH_HOST }} "sudo mkdir -p /opt/bcare && sudo chown -R ${{ env.SSH_USERNAME }}:${{ env.SSH_USERNAME }} /opt/bcare"

      - name: Deploy with rsync
        run: rsync -avz ./dist/ ${{ env.SSH_USERNAME }}@${{ env.SSH_HOST }}:/opt/bcare

      - name: Copy systemd service
        run: rsync -avz bcare.service ${{ env.SSH_USERNAME }}@${{env.SSH_HOST}}:/etc/systemd/system/bcare.service

      - name: Restart service
        run: ssh ${{ env.SSH_USERNAME }}@${{ env.SSH_HOST }} "sudo systemctl daemon-reload"

      - name: Restart service
        run: ssh ${{ env.SSH_USERNAME }}@${{ env.SSH_HOST }} "sudo systemctl set-environment DB_PASS=${{ secrets.DB_PASS }} DB_NAME=bcare APP_PORT=8888 && sudo systemctl restart bcare"

      - name: Enable service
        run: ssh ${{ env.SSH_USERNAME }}@${{ env.SSH_HOST }} "sudo systemctl enable bcare"
