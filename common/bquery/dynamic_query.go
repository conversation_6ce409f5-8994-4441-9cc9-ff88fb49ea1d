// Package bquery provides utilities for constructing dynamic SQL queries.
// It supports joins, filters, aggregations, sorting, and pagination.
package bquery

import (
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"entgo.io/ent/dialect/sql"
	"github.com/zeromicro/go-zero/core/logx"
)

// DynamicQuery represents a complete dynamic query structure.
type DynamicQuery struct {
	Table        string         `json:"table"`
	Filters      []Filter       `json:"filters"`
	GroupBy      []string       `json:"group_by"`
	Aggregations []Aggregation  `json:"aggregations"`
	Sort         []SortCriteria `json:"sort"`
	Limit        int            `json:"limit"`
	Offset       int            `json:"offset"`
	Selects      []string       `json:"selects"`
	Joins        []JoinInfo     `json:"joins"`
}

// Filter represents a single filter condition.
type Filter struct {
	Field      string      `json:"field"`
	Operator   string      `json:"operator"`
	Value      interface{} `json:"value"`
	Logic      string      `json:"logic,omitempty"`
	Conditions []Filter    `json:"conditions,omitempty"`
	Function   string      `json:"function,omitempty"`
}

// JoinInfo represents information for a join operation.
type JoinInfo struct {
	Table string `json:"table"`
	Alias string `json:"alias,omitempty"`
	C1    string `json:"c1"`
	C2    string `json:"c2"`
	Type  string `json:"type"`
}

// Aggregation represents an aggregation operation.
type Aggregation struct {
	Field    string               `json:"field"`
	Function string               `json:"function"`
	Alias    string               `json:"alias"`
	Over     *WindowSpecification `json:"over"`
}

// SortCriteria represents sorting information.
type SortCriteria struct {
	Field string `json:"field"`
	Order string `json:"order"`
}

// WindowSpecification represents the SQL window specification for OVER() clause.
type WindowSpecification struct {
	PartitionBy []string       `json:"partition_by"`
	OrderBy     []SortCriteria `json:"order_by"`
}

func (ws WindowSpecification) IsEmpty() bool {
	return len(ws.PartitionBy) == 0 && len(ws.OrderBy) == 0
}

// ApplyDynamicQuery applies the dynamic query to the given selector.
// It returns a function that modifies the selector based on the DynamicQuery parameters.
func ApplyDynamicQuery(dq *DynamicQuery) func(s *sql.Selector) {
	return func(s *sql.Selector) {
		s.From(sql.Table(dq.Table))

		// Apply joins
		if len(dq.Joins) > 0 {
			if err := applyJoins(s, dq.Joins, dq.Table); err != nil {
				logx.Errorf("apply joins error: %v", err)
			}
		}

		// Build select fields
		selectFields, err := buildSelectFields(dq)
		if err != nil {
			logx.Errorf("build select fields error: %v", err)
		} else {
			// Apply select
			if len(selectFields) > 0 {
				s.Select(selectFields...)
			}
		}

		// Apply filters
		if len(dq.Filters) > 0 {
			pred, err := buildFilters(dq.Filters)
			if err == nil {
				s.Where(pred)
			} else {
				logx.Errorf("build filters error: %v", err)
			}
		}

		// Apply group by
		if len(dq.GroupBy) > 0 {
			groupByFields, err := buildGroupByFields(dq)
			if err != nil {
				logx.Errorf("build group by fields error: %v", err)
			} else {
				s.GroupBy(groupByFields...)
			}
		}

		// Apply sort
		if len(dq.Sort) > 0 && len(dq.Aggregations) == 0 {
			if err := applySorting(s, dq); err != nil {
				logx.Errorf("apply sorting error: %v", err)
			}
		}

		// Apply limit and offset
		if dq.Limit > 0 {
			s.Limit(dq.Limit)
		}
		if dq.Offset > 0 {
			s.Offset(dq.Offset)
		}
	}
}

// applyJoins applies the join operations to the selector.
// It returns an error if any join conditions are invalid.
func applyJoins(s *sql.Selector, joins []JoinInfo, mainTable string) error {
	for _, join := range joins {
		// Validate join type
		joinType := strings.ToUpper(join.Type)
		var joinSelector *sql.Selector

		joinedTable := sql.Table(join.Table)

		if join.Alias != "" {
			joinedTable = joinedTable.As(join.Alias)
		}

		switch joinType {
		case "LEFT":
			joinSelector = s.LeftJoin(joinedTable)
		case "INNER":
			joinSelector = s.Join(joinedTable)
		case "RIGHT":
			joinSelector = s.RightJoin(joinedTable)
		case "FULL":
			joinSelector = s.FullJoin(joinedTable)
		default:
			logx.Errorf("unsupported join type: %s", join.Type)
			continue
		}

		// Validate join conditions
		if strings.TrimSpace(join.C1) == "" || strings.TrimSpace(join.C2) == "" {
			logx.Errorf("join conditions C1 and C2 must be non-empty for table: %s", join.Table)
			continue
		}

		// Process C1
		c1Parts := strings.Split(join.C1, ".")
		var c1 string
		if len(c1Parts) == 2 {
			c1 = fmt.Sprintf(`"%s"."%s"`, c1Parts[0], c1Parts[1])
		} else {
			c1 = fmt.Sprintf(`"%s"."%s"`, mainTable, join.C1)
		}

		// Process C2
		c2Parts := strings.Split(join.C2, ".")
		var c2 string
		if len(c2Parts) == 2 {
			c2 = fmt.Sprintf(`"%s"."%s"`, c2Parts[0], c2Parts[1])
		} else {
			joinedAlias := join.Alias
			if joinedAlias == "" {
				joinedAlias = join.Table
			}
			c2 = fmt.Sprintf(`"%s"."%s"`, joinedAlias, join.C2)
		}

		// Apply the On method with processed columns
		joinSelector.On(c1, c2)
	}
	return nil
}

// buildSelectFields constructs the fields to be selected in the query.
// It includes explicit selects, aggregations, and group-by fields.
func buildSelectFields(dq *DynamicQuery) ([]string, error) {
	var selectFields []string

	// Nếu không có trường select nào được chỉ định, tự động chọn tất cả các trường từ bảng chính
	if len(dq.Selects) == 0 && len(dq.Aggregations) == 0 {
		selectFields = append(selectFields, fmt.Sprintf(`"%s".*`, dq.Table))
	}

	// Add explicit selects
	for _, field := range dq.Selects {
		// Kiểm tra nếu field là dạng table/alias.* để chọn tất cả các cột từ bảng đó
		if strings.HasSuffix(field, ".*") {
			selectFields = append(selectFields, fmt.Sprintf(`"%s".*`, strings.TrimSuffix(field, ".*")))
		} else {
			selectFields = append(selectFields, buildFieldReference(field))
		}
	}

	// Add aggregations
	for _, agg := range dq.Aggregations {
		aggFunc, err := buildAggregation(agg)
		if err != nil {
			logx.Errorf("build aggregation error: %v", err)
			continue
		}
		selectFields = append(selectFields, aggFunc)
	}

	// Add group by fields if not already included
	for _, field := range dq.GroupBy {
		formattedField := buildFieldReference(field)
		if !contains(selectFields, formattedField) {
			selectFields = append(selectFields, formattedField)
		}
	}

	return selectFields, nil
}

// buildGroupByFields constructs the group by fields.
// It returns a slice of field strings or an error if any field is invalid.
func buildGroupByFields(dq *DynamicQuery) ([]string, error) {
	groupByFields := make([]string, 0, len(dq.GroupBy))
	for _, field := range dq.GroupBy {
		groupByFields = append(groupByFields, buildFieldReference(field))
	}
	return groupByFields, nil
}

// buildAggregation constructs the SQL aggregation function with alias.
// buildAggregation constructs the SQL aggregation function with alias.
func buildAggregation(agg Aggregation) (string, error) {
	var aggregation string

	if agg.Field == "*" {
		// Special case for COUNT(*)
		switch strings.ToUpper(agg.Function) {
		case "COUNT":
			aggregation = "COUNT(*)"
		default:
			return "", fmt.Errorf("unsupported aggregation function for field '*': %s", agg.Function)
		}
	} else {
		field := buildFieldReference(agg.Field)
		switch strings.ToUpper(agg.Function) {
		case "SUM":
			aggregation = sql.Sum(field)
		case "AVG":
			aggregation = sql.Avg(field)
		case "COUNT":
			aggregation = sql.Count(field)
		case "MIN":
			aggregation = sql.Min(field)
		case "MAX":
			aggregation = sql.Max(field)
		default:
			return "", fmt.Errorf("unsupported aggregation function: %s", agg.Function)
		}
	}

	// Always handle OVER clause
	if agg.Over != nil && agg.Over.IsEmpty() {
		aggregation = fmt.Sprintf("%s OVER ()", aggregation)
	} else if agg.Over != nil {
		windowClause := buildWindowClause(*agg.Over)
		aggregation = fmt.Sprintf("%s OVER (%s)", aggregation, windowClause)
	}

	return sql.As(aggregation, agg.Alias), nil
}

// buildFilters constructs the WHERE clause predicates based on the provided filters.
// It returns a combined predicate or an error if any filter is invalid.
func buildFilters(filters []Filter) (*sql.Predicate, error) {
	var predicates []*sql.Predicate
	for _, filter := range filters {
		pred, err := buildFilter(filter)
		if err != nil {
			return nil, err
		}
		if pred != nil {
			predicates = append(predicates, pred)
		}
	}
	return sql.And(predicates...), nil
}

// buildFilter constructs a single predicate based on the provided filter.
// It supports nested conditions with logical operators.
func buildFilter(filter Filter) (*sql.Predicate, error) {
	if len(filter.Conditions) > 0 {
		var predicates []*sql.Predicate
		for _, condition := range filter.Conditions {
			pred, err := buildFilter(condition)
			if err != nil {
				return nil, err
			}
			if pred != nil {
				predicates = append(predicates, pred)
			}
		}
		if strings.ToUpper(filter.Logic) == "OR" {
			return sql.Or(predicates...), nil
		}
		return sql.And(predicates...), nil
	}

	field := buildFieldReference(filter.Field)

	// Apply SQL functions if specified
	if filter.Function != "" {
		field = applyFunction(field, filter.Function)
	}

	// Process the value (e.g., parse time strings)
	value := processValue(filter.Value)

	// Construct the predicate based on the operator
	switch strings.ToUpper(filter.Operator) {
	case "EQ":
		return sql.EQ(field, value), nil
	case "NEQ":
		return sql.NEQ(field, value), nil
	case "GT":
		return sql.GT(field, value), nil
	case "GTE":
		return sql.GTE(field, value), nil
	case "LT":
		return sql.LT(field, value), nil
	case "LTE":
		return sql.LTE(field, value), nil
	case "IN":
		strValue, ok := value.(string)
		if !ok {
			return nil, fmt.Errorf("invalid value type for IN operator: %T", value)
		}
		// Loại bỏ dấu ngoặc đơn
		trimmed := strings.Trim(strValue, "()")
		// Tách chuỗi thành mảng các giá trị
		strValues := strings.Split(trimmed, ",")

		// Chuyển đổi các chuỗi thành giá trị thích hợp
		// Giả sử giá trị là số nguyên int
		values := make([]interface{}, len(strValues))
		for i, v := range strValues {
			// Trim khoảng trắng nếu có
			v = strings.TrimSpace(v)

			// Chuyển đổi sang số nguyên
			intVal, err := strconv.Atoi(v)
			if err != nil {
				// Nếu không phải số, giữ nguyên giá trị chuỗi
				values[i] = v
			} else {
				values[i] = intVal
			}
		}

		return sql.In(field, values...), nil
	case "NOTIN":
		strValue, ok := value.(string)
		if !ok {
			return nil, fmt.Errorf("invalid value type for IN operator: %T", value)
		}
		// Loại bỏ dấu ngoặc đơn
		trimmed := strings.Trim(strValue, "()")
		// Tách chuỗi thành mảng các giá trị
		strValues := strings.Split(trimmed, ",")

		// Chuyển đổi các chuỗi thành giá trị thích hợp
		// Giả sử giá trị là số nguyên int
		values := make([]interface{}, len(strValues))
		for i, v := range strValues {
			// Trim khoảng trắng nếu có
			v = strings.TrimSpace(v)

			// Chuyển đổi sang số nguyên
			intVal, err := strconv.Atoi(v)
			if err != nil {
				// Nếu không phải số, giữ nguyên giá trị chuỗi
				values[i] = v
			} else {
				values[i] = intVal
			}
		}

		return sql.In(field, values...), nil
	case "LIKE":
		strValue, ok := value.(string)
		if !ok {
			return nil, fmt.Errorf("invalid value type for LIKE operator: %T", value)
		}
		return sql.P(func(b *sql.Builder) {
			b.WriteString(fmt.Sprintf("f_unaccent(%s) LIKE '%%' || f_unaccent(", field))
			b.Arg(strValue)
			b.WriteString(") || '%'")
		}), nil
	case "NOTLIKE":
		strValue, ok := value.(string)
		if !ok {
			return nil, fmt.Errorf("invalid value type for NOT LIKE operator: %T", value)
		}
		return sql.Not(sql.P(func(b *sql.Builder) {
			b.WriteString(fmt.Sprintf("f_unaccent(%s) LIKE '%%' || f_unaccent(", field))
			b.Arg(strValue)
			b.WriteString(") || '%'")
		})), nil
	case "BETWEEN":
		values, ok := value.([]interface{})
		if !ok || len(values) != 2 {
			return nil, fmt.Errorf("invalid value type for BETWEEN operator: %T", value)
		}
		return sql.And(sql.GTE(field, values[0]), sql.LTE(field, values[1])), nil
	case "ISNULL":
		return sql.IsNull(field), nil
	case "ISNOTNULL":
		return sql.Not(sql.IsNull(field)), nil
	case "TODAY":
		return sql.EQ(applyFunction(field, "DATE"), sql.Raw("CURRENT_DATE")), nil
	case "CONTAINS":
		strValue, ok := value.(string)
		if !ok {
			return nil, fmt.Errorf("invalid value type for CONTAINS operator: %T", value)
		}
		return sql.P(func(b *sql.Builder) {
			b.WriteString(fmt.Sprintf("%s::jsonb", field))
			b.WriteString(" @> ")
			b.WriteString(fmt.Sprintf("'%s'", strValue))
		}), nil
	case "JSON_CONTAINS_ANY":
		return buildJsonContainsAnyPredicate(field, value)
	default:
		return nil, fmt.Errorf("unsupported operator: %s", filter.Operator)
	}
}

// Helper function to build conditions for jsonb_path_exists
func buildJsonPathConditions(value interface{}) ([]string, error) {
	switch v := value.(type) {
	case string:
		numbers, err := ParseJSONArrayString(v)
		if err != nil {
			return nil, fmt.Errorf("invalid JSON array string: %v", err)
		}
		conditions := make([]string, len(numbers))
		for i, num := range numbers {
			conditions[i] = fmt.Sprintf("@ == %d", num)
		}
		return conditions, nil

	case []interface{}:
		conditions := make([]string, len(v))
		for i, val := range v {
			conditions[i] = fmt.Sprintf("@ == %v", val)
		}
		return conditions, nil

	default:
		return nil, fmt.Errorf("invalid value type for JSON_CONTAINS_ANY operator: %T", value)
	}
}

// Helper function to build the complete predicate
func buildJsonContainsAnyPredicate(field string, value interface{}) (*sql.Predicate, error) {
	conditions, err := buildJsonPathConditions(value)
	if err != nil {
		return nil, err
	}

	jsonPathCondition := strings.Join(conditions, " || ")

	return sql.P(func(b *sql.Builder) {
		b.WriteString("jsonb_path_exists(")
		b.WriteString(fmt.Sprintf("%s::jsonb", field))
		b.WriteString(", '$[*] ? (")
		b.WriteString(jsonPathCondition)
		b.WriteString(")')")
	}), nil
}

// parseJSONArrayString converts a JSON array string to []int64
func ParseJSONArrayString(jsonStr string) ([]int64, error) {
	// Remove leading/trailing whitespace
	jsonStr = strings.TrimSpace(jsonStr)

	// Validate basic JSON array format
	if !strings.HasPrefix(jsonStr, "[") || !strings.HasSuffix(jsonStr, "]") {
		return nil, fmt.Errorf("invalid JSON array format")
	}

	// Parse JSON string
	var numbers []int64
	err := json.Unmarshal([]byte(jsonStr), &numbers)
	if err != nil {
		return nil, fmt.Errorf("failed to parse JSON array: %v", err)
	}

	return numbers, nil
}

// applyFunction applies the specified SQL function to the field.
// It returns the modified field string.
func applyFunction(field string, function string) string {
	switch strings.ToUpper(function) {
	case "TO_CHAR":
		return fmt.Sprintf("TO_CHAR(%s, 'YYYY-MM-DD')", field)
	case "DATE":
		return fmt.Sprintf("DATE(%s)", field)
	case "YEAR":
		return fmt.Sprintf("EXTRACT(YEAR FROM %s)", field)
	case "MONTH":
		return fmt.Sprintf("EXTRACT(MONTH FROM %s)", field)
	case "DAY":
		return fmt.Sprintf("EXTRACT(DAY FROM %s)", field)
	case "LOWER":
		return fmt.Sprintf("LOWER(%s)", field)
	case "UPPER":
		return fmt.Sprintf("UPPER(%s)", field)
	case "UNACCENT":
		return fmt.Sprintf("f_unaccent(%s)", field)
	default:
		return field
	}
}

// processValue processes the input value.
// It parses time strings formatted as "time(<RFC3339>)" into time.Time objects.
func processValue(value interface{}) interface{} {
	if strValue, ok := value.(string); ok {
		if strings.HasPrefix(strValue, "time(") && strings.HasSuffix(strValue, ")") {
			stringTime := strValue[5 : len(strValue)-1]
			parsedTime, err := time.Parse(time.RFC3339, stringTime)
			if err == nil {
				return parsedTime
			}
		}
	}
	return value
}

// applySorting applies the ORDER BY clause based on the provided sort criteria.
// It returns an error if any sort criteria are invalid.
func applySorting(s *sql.Selector, dq *DynamicQuery) error {
	for _, sort := range dq.Sort {
		field := buildFieldReference(sort.Field)

		order := strings.ToUpper(sort.Order)
		switch order {
		case "DESC":
			s.OrderBy(sql.Desc(field))
		case "ASC":
			s.OrderBy(sql.Asc(field))
		default:
			logx.Errorf("unsupported sort order: %s. Defaulting to ASC.", sort.Order)
			s.OrderBy(sql.Asc(field))
		}
	}
	return nil
}

// buildFieldReference constructs the field reference string.
// It handles both simple fields and fields with table/alias prefixes.
func buildFieldReference(field string) string {
	parts := strings.Split(field, ".")
	if len(parts) == 2 {
		return fmt.Sprintf(`"%s"."%s"`, parts[0], parts[1])
	}
	return fmt.Sprintf(`"%s"`, field)
}

// buildWindowClause builds the SQL window clause for the OVER() statement.
func buildWindowClause(over WindowSpecification) string {
	// Check if the window specification is effectively empty
	if len(over.PartitionBy) == 0 && len(over.OrderBy) == 0 {
		return "" // No PARTITION BY or ORDER BY
	}

	var parts []string

	if len(over.PartitionBy) > 0 {
		partitionByFields := make([]string, len(over.PartitionBy))
		for i, field := range over.PartitionBy {
			partitionByFields[i] = buildFieldReference(field)
		}
		parts = append(parts, fmt.Sprintf("PARTITION BY %s", strings.Join(partitionByFields, ", ")))
	}

	if len(over.OrderBy) > 0 {
		orderByFields := make([]string, len(over.OrderBy))
		for i, sort := range over.OrderBy {
			field := buildFieldReference(sort.Field)
			order := strings.ToUpper(sort.Order)
			if order == "DESC" {
				orderByFields[i] = fmt.Sprintf("%s DESC", field)
			} else {
				orderByFields[i] = fmt.Sprintf("%s ASC", field)
			}
		}
		parts = append(parts, fmt.Sprintf("ORDER BY %s", strings.Join(orderByFields, ", ")))
	}

	return strings.Join(parts, " ")
}

// contains checks if a slice of strings contains a specific item.
// It returns true if the item is found, false otherwise.
func contains(slice []string, item string) bool {
	for _, a := range slice {
		if a == item {
			return true
		}
	}
	return false
}
