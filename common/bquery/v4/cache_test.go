package v4

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func TestCache_GetSet(t *testing.T) {
	cache := NewQueryCache()

	// Test set and get
	key := "test_key"
	entry := &CacheEntry{
		QueryHash:         "hash123",
		ParamTypes:        []string{"int", "string"},
		ValidatedSQL:      "SELECT * FROM users WHERE id = $1 AND name = $2",
		TableDependencies: []string{"users"},
		ExpiresAt:         time.Now().Add(time.Hour),
	}

	cache.Set(key, entry)

	// Test get
	cachedEntry, found := cache.Get(key)
	assert.True(t, found)
	assert.Equal(t, entry.QueryHash, cachedEntry.QueryHash)
	assert.Equal(t, entry.ValidatedSQL, cachedEntry.ValidatedSQL)
}

func TestCache_Miss(t *testing.T) {
	cache := NewQueryCache()

	// Test cache miss
	_, found := cache.Get("nonexistent_key")
	assert.False(t, found)
}

func TestCache_ExpiredEntry(t *testing.T) {
	cache := NewQueryCache()

	key := "expired_key"
	entry := &CacheEntry{
		QueryHash:         "hash_expired",
		ParamTypes:        []string{"int"},
		ValidatedSQL:      "SELECT * FROM users WHERE id = $1",
		TableDependencies: []string{"users"},
		ExpiresAt:         time.Now().Add(-time.Minute), // Already expired
	}

	cache.Set(key, entry)

	// Test expired entry
	_, found := cache.Get(key)
	assert.False(t, found, "Expired entry should not be found")
}

func TestCache_Invalidate(t *testing.T) {
	cache := NewQueryCache()

	// Add entries related to users table
	entry1 := &CacheEntry{
		QueryHash:         "hash_users_1",
		TableDependencies: []string{"users"},
		ValidatedSQL:      "SELECT * FROM users",
		ExpiresAt:         time.Now().Add(time.Hour),
	}

	entry2 := &CacheEntry{
		QueryHash:         "hash_orders_1",
		TableDependencies: []string{"orders"},
		ValidatedSQL:      "SELECT * FROM orders",
		ExpiresAt:         time.Now().Add(time.Hour),
	}

	entry3 := &CacheEntry{
		QueryHash:         "hash_users_orders",
		TableDependencies: []string{"users", "orders"},
		ValidatedSQL:      "SELECT * FROM users JOIN orders ON users.id = orders.user_id",
		ExpiresAt:         time.Now().Add(time.Hour),
	}

	cache.Set("users_query", entry1)
	cache.Set("orders_query", entry2)
	cache.Set("users_orders_query", entry3)

	// Invalidate users table
	cache.Clear()

	// users_query should be invalidated
	_, found1 := cache.Get("users_query")
	assert.False(t, found1)

	// users_orders_query should be invalidated
	_, found3 := cache.Get("users_orders_query")
	assert.False(t, found3)

	// orders_query should should be invalidated
	_, found2 := cache.Get("orders_query")
	assert.False(t, found2)
}
