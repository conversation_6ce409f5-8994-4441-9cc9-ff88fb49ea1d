package v4

import (
	"time"

	"github.com/maypok86/otter"
)

// CacheEntry represents a cached SQL query
type CacheEntry struct {
	QueryHash         string
	ParamTypes        []string
	ValidatedSQL      string
	TableDependencies []string
	ExpiresAt         time.Time
}

// QueryCache provides caching for validated SQL queries
type QueryCache struct {
	// Không cần mutex vì otter đã thread-safe
	cache otter.CacheWithVariableTTL[string, *CacheEntry]
}

// NewQueryCache creates a new query cache
func NewQueryCache() *QueryCache {
	// Initialize otter cache
	cache, _ := otter.MustBuilder[string, *CacheEntry](10_000).
		CollectStats().
		Cost(func(key string, value *CacheEntry) uint32 {
			// Simple cost function - each entry costs 1
			return 1
		}).
		WithVariableTTL().
		Build()

	return &QueryCache{
		cache: cache,
	}
}

// Get retrieves a cache entry by key
func (c *QueryCache) Get(key string) (*CacheEntry, bool) {
	// Otter đã xử lý TTL tự động, không cần kiểm tra ExpiresAt
	entry, found := c.cache.Get(key)
	return entry, found
}

// Set adds or updates a cache entry
func (c *QueryCache) Set(key string, entry *CacheEntry) {
	// Calculate TTL from now to expiry time
	ttl := entry.ExpiresAt.Sub(time.Now())

	if ttl <= 0 {
		// Don't cache expired entries
		return
	}

	c.cache.Set(key, entry, ttl)
}

// Invalidate removes all cache entries that depend on the specified table
// Nếu flushAll=true, xóa toàn bộ cache thay vì chỉ xóa các entry liên quan đến table
func (c *QueryCache) Invalidate(table string, flushAll bool) {
	// Nếu flushAll=true, xóa toàn bộ cache
	if flushAll {
		c.cache.Clear()
		return
	}

	// Đơn giản hóa: Sử dụng slice để lưu trữ các khóa cần xóa
	var keysToDelete []string

	// Duyệt qua tất cả các mục trong cache
	c.cache.Range(func(key string, value *CacheEntry) bool {
		for _, dep := range value.TableDependencies {
			if dep == table {
				keysToDelete = append(keysToDelete, key)
				break
			}
		}
		return true // Tiếp tục duyệt
	})

	// Xóa các khóa đã thu thập
	for _, key := range keysToDelete {
		c.cache.Delete(key)
	}
}

// Clear removes all entries from the cache
func (c *QueryCache) Clear() {
	c.cache.Clear()
}

// Stats returns cache statistics
func (c *QueryCache) Stats() any {
	return c.cache.Stats()
}
