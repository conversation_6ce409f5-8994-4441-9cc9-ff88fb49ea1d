package v4

import (
	"github.com/pganalyze/pg_query_go/v6"
)

// SQLParser interface defines the methods for parsing and analyzing SQL queries
type SQLParser interface {
	Parse(rawSQL string) (*pg_query.ParseResult, error)
	Normalize(rawSQL string) (string, error)
	GetTableNames(parseResult *pg_query.ParseResult) ([]string, error)
	GetCTEDepth(parseResult *pg_query.ParseResult) (int, error)
	GetFunctionCalls(parseResult *pg_query.ParseResult) ([]string, error)
	GetColumnReferences(parseResult *pg_query.ParseResult) ([]ColumnReference, error)
	GetTableAliases(result *pg_query.ParseResult) (map[string]string, error)
}

// PostgresParser implements the SQLParser interface for PostgreSQL
type PostgresParser struct{}

// NewPostgresParser creates a new PostgreSQL parser
func NewPostgresParser() SQLParser {
	return &PostgresParser{}
}

// Parse parses a SQL query into a pg_query.ParseResult
func (p *PostgresParser) Parse(rawSQL string) (*pg_query.ParseResult, error) {
	return pg_query.Parse(rawSQL)
}

// Normalize standardizes a SQL query by removing literals and replacing with placeholders
func (p *PostgresParser) Normalize(rawSQL string) (string, error) {
	// Use pg_query's normalize function
	result, err := pg_query.Normalize(rawSQL)
	if err != nil {
		return "", err
	}
	return result, nil
}

// GetTableNames extracts all table names referenced in a query
func (p *PostgresParser) GetTableNames(parseResult *pg_query.ParseResult) ([]string, error) {
	if parseResult == nil {
		return nil, nil
	}

	tableNames := make(map[string]struct{})
	cteNames := make(map[string]struct{})

	// Extract table names and CTE names from parse result
	for _, stmt := range parseResult.Stmts {
		p.extractCTENames(stmt.Stmt, cteNames)
		p.extractTableNamesFromNode(stmt.Stmt, tableNames)
	}

	// Remove CTE names from table names
	for cteName := range cteNames {
		delete(tableNames, cteName)
	}

	// Convert to slice
	result := make([]string, 0, len(tableNames))
	for tableName := range tableNames {
		result = append(result, tableName)
	}

	return result, nil
}

// extractCTENames extracts all CTE names from a query
func (p *PostgresParser) extractCTENames(node *pg_query.Node, cteNames map[string]struct{}) {
	if node == nil {
		return
	}

	switch n := node.GetNode().(type) {
	case *pg_query.Node_SelectStmt:
		if n.SelectStmt.WithClause != nil {
			p.extractCTENames(&pg_query.Node{Node: &pg_query.Node_WithClause{WithClause: n.SelectStmt.WithClause}}, cteNames)
		}
	case *pg_query.Node_WithClause:
		for _, cte := range n.WithClause.Ctes {
			if cteNode, ok := cte.GetNode().(*pg_query.Node_CommonTableExpr); ok {
				cteNames[cteNode.CommonTableExpr.Ctename] = struct{}{}
				// Recursively check for nested CTEs
				if cteNode.CommonTableExpr.Ctequery != nil {
					p.extractCTENames(cteNode.CommonTableExpr.Ctequery, cteNames)
				}
			}
		}
	}
}

// extractTableNamesFromNode recursively traverses the query to extract table names
func (p *PostgresParser) extractTableNamesFromNode(node *pg_query.Node, tableNames map[string]struct{}) {
	if node == nil {
		return
	}

	// Handle different node types
	switch n := node.GetNode().(type) {
	case *pg_query.Node_SelectStmt:
		selectStmt := n.SelectStmt

		// Xử lý phép toán tập hợp (UNION, INTERSECT, EXCEPT)
		if selectStmt.Op != pg_query.SetOperation_SETOP_NONE {
			if selectStmt.Larg != nil {
				p.extractTableNamesFromNode(&pg_query.Node{Node: &pg_query.Node_SelectStmt{SelectStmt: selectStmt.Larg}}, tableNames)
			}
			if selectStmt.Rarg != nil {
				p.extractTableNamesFromNode(&pg_query.Node{Node: &pg_query.Node_SelectStmt{SelectStmt: selectStmt.Rarg}}, tableNames)
			}
			return // Không xử lý FromClause ở đây vì đã xử lý trong Larg/Rarg
		}

		p.extractTableNamesFromFromClause(selectStmt.FromClause, tableNames)

		if selectStmt.WithClause != nil {
			p.extractTableNamesFromNode(&pg_query.Node{Node: &pg_query.Node_WithClause{WithClause: selectStmt.WithClause}}, tableNames)
		}
	case *pg_query.Node_WithClause:
		withClause := n.WithClause
		for _, cte := range withClause.Ctes {
			if cteStmt, ok := cte.GetNode().(*pg_query.Node_CommonTableExpr); ok {
				if cteStmt.CommonTableExpr.Ctequery != nil {
					p.extractTableNamesFromNode(cteStmt.CommonTableExpr.Ctequery, tableNames)
				}
			}
		}
	}
}

// extractTableNamesFromFromClause processes FROM clause to extract table names
func (p *PostgresParser) extractTableNamesFromFromClause(fromClause []*pg_query.Node, tableNames map[string]struct{}) {
	for _, item := range fromClause {
		if item == nil {
			continue
		}

		switch n := item.GetNode().(type) {
		case *pg_query.Node_RangeVar:
			// Direct table reference
			// Sửa: Truy cập Relname trực tiếp vì nó là string, không phải con trỏ
			tableNames[n.RangeVar.Relname] = struct{}{}
		case *pg_query.Node_JoinExpr:
			// Join expression
			joinExpr := n.JoinExpr
			if joinExpr.Larg != nil {
				p.extractTableNamesFromFromClause([]*pg_query.Node{joinExpr.Larg}, tableNames)
			}
			if joinExpr.Rarg != nil {
				p.extractTableNamesFromFromClause([]*pg_query.Node{joinExpr.Rarg}, tableNames)
			}
		case *pg_query.Node_RangeSubselect:
			// Subquery
			if n.RangeSubselect.Subquery != nil {
				p.extractTableNamesFromNode(n.RangeSubselect.Subquery, tableNames)
			}
		}
	}
}

// GetCTEDepth calculates the maximum depth of CTE references in a query
func (p *PostgresParser) GetCTEDepth(parseResult *pg_query.ParseResult) (int, error) {
	if parseResult == nil || len(parseResult.Stmts) == 0 {
		return 0, nil
	}

	// Find WITH clause
	stmt := parseResult.Stmts[0].Stmt
	selectStmt, ok := stmt.GetNode().(*pg_query.Node_SelectStmt)
	if !ok || selectStmt.SelectStmt.WithClause == nil {
		return 0, nil
	}

	withClause := selectStmt.SelectStmt.WithClause
	if len(withClause.Ctes) == 0 {
		return 0, nil
	}

	// Build a map of CTE names for reference checking
	cteNames := make(map[string]struct{})
	for _, cte := range withClause.Ctes {
		if cteNode, ok := cte.GetNode().(*pg_query.Node_CommonTableExpr); ok {
			// Sửa: Truy cập Ctename trực tiếp vì nó là string, không phải con trỏ
			cteNames[cteNode.CommonTableExpr.Ctename] = struct{}{}
		}
	}

	// Calculate dependencies between CTEs
	cteDependencies := make(map[string][]string)
	for _, cte := range withClause.Ctes {
		if cteNode, ok := cte.GetNode().(*pg_query.Node_CommonTableExpr); ok {
			// Sửa: Truy cập Ctename trực tiếp
			cteName := cteNode.CommonTableExpr.Ctename
			// Find which other CTEs this one depends on
			deps := p.findCTEDependencies(cteNode.CommonTableExpr.Ctequery, cteNames)
			cteDependencies[cteName] = deps
		}
	}

	// Calculate the maximum depth
	maxDepth := 1
	for cteName := range cteDependencies {
		depth := p.calculateCTEDepth(cteName, cteDependencies, make(map[string]bool))
		if depth > maxDepth {
			maxDepth = depth
		}
	}

	return maxDepth, nil
}

// findCTEDependencies finds which CTEs are referenced in a query node
func (p *PostgresParser) findCTEDependencies(node *pg_query.Node, cteNames map[string]struct{}) []string {
	if node == nil {
		return nil
	}

	deps := make([]string, 0)

	// Check if this is a reference to a CTE
	if rangeVar, ok := node.GetNode().(*pg_query.Node_RangeVar); ok {
		// Sửa: Truy cập Relname trực tiếp
		cteName := rangeVar.RangeVar.Relname
		if _, ok := cteNames[cteName]; ok {
			deps = append(deps, cteName)
		}
	}

	// Check SELECT statement
	if selectStmt, ok := node.GetNode().(*pg_query.Node_SelectStmt); ok {
		for _, fromItem := range selectStmt.SelectStmt.FromClause {
			if rangeVar, ok := fromItem.GetNode().(*pg_query.Node_RangeVar); ok {
				// Sửa: Truy cập Relname trực tiếp
				cteName := rangeVar.RangeVar.Relname
				if _, ok := cteNames[cteName]; ok {
					deps = append(deps, cteName)
				}
			}
		}
	}

	return deps
}

// calculateCTEDepth calculates the maximum depth of a CTE by following its dependencies
func (p *PostgresParser) calculateCTEDepth(cteName string, dependencies map[string][]string, visited map[string]bool) int {
	if visited[cteName] {
		return 0 // Break cycles
	}

	visited[cteName] = true

	deps := dependencies[cteName]
	if len(deps) == 0 {
		return 1 // No dependencies
	}

	maxDepth := 0
	for _, dep := range deps {
		depth := p.calculateCTEDepth(dep, dependencies, visited)
		if depth > maxDepth {
			maxDepth = depth
		}
	}

	return maxDepth + 1
}

// ColumnReference represents a column reference in a query
type ColumnReference struct {
	Table  string
	Column string
}

// GetColumnReferences extracts all column references from a query
func (p *PostgresParser) GetColumnReferences(parseResult *pg_query.ParseResult) ([]ColumnReference, error) {
	if parseResult == nil {
		return nil, nil
	}

	columnRefs := make([]ColumnReference, 0)

	// Extract column references from parse result
	for _, stmt := range parseResult.Stmts {
		p.extractColumnReferences(stmt.Stmt, &columnRefs)
	}

	return columnRefs, nil
}

// extractColumnReferences recursively traverses the query to extract column references
func (p *PostgresParser) extractColumnReferences(node *pg_query.Node, columnRefs *[]ColumnReference) {
	if node == nil {
		return
	}

	switch n := node.GetNode().(type) {
	case *pg_query.Node_ColumnRef:
		colRef := n.ColumnRef
		table := ""
		column := ""

		// Extract table and column names from the fields
		if len(colRef.Fields) == 1 {
			// Only column name
			if stringVal, ok := colRef.Fields[0].GetNode().(*pg_query.Node_String_); ok {
				column = stringVal.String_.Sval
			}
		} else if len(colRef.Fields) >= 2 {
			// Table and column names
			if stringVal, ok := colRef.Fields[0].GetNode().(*pg_query.Node_String_); ok {
				table = stringVal.String_.Sval
			}
			if stringVal, ok := colRef.Fields[1].GetNode().(*pg_query.Node_String_); ok {
				column = stringVal.String_.Sval
			}
		}

		if column != "" {
			*columnRefs = append(*columnRefs, ColumnReference{
				Table:  table,
				Column: column,
			})
		}

	case *pg_query.Node_SelectStmt:
		// Process target list (SELECT columns)
		for _, target := range n.SelectStmt.TargetList {
			p.extractColumnReferences(target, columnRefs)
		}

		// Process WHERE clause
		if n.SelectStmt.WhereClause != nil {
			p.extractColumnReferences(n.SelectStmt.WhereClause, columnRefs)
		}

		// Process FROM clause
		for _, from := range n.SelectStmt.FromClause {
			p.extractColumnReferences(from, columnRefs)
		}

		// Process GROUP BY clause
		for _, group := range n.SelectStmt.GroupClause {
			p.extractColumnReferences(group, columnRefs)
		}

		// Process HAVING clause
		if n.SelectStmt.HavingClause != nil {
			p.extractColumnReferences(n.SelectStmt.HavingClause, columnRefs)
		}

		// Process ORDER BY clause
		for _, sort := range n.SelectStmt.SortClause {
			p.extractColumnReferences(sort, columnRefs)
		}

	case *pg_query.Node_CaseExpr:
		caseExpr := n.CaseExpr
		// Xử lý biểu thức CASE
		if caseExpr.Arg != nil {
			p.extractColumnReferences(caseExpr.Arg, columnRefs)
		}
		for _, when := range caseExpr.Args {
			if whenCase, ok := when.GetNode().(*pg_query.Node_CaseWhen); ok {
				// Xử lý điều kiện WHEN
				p.extractColumnReferences(whenCase.CaseWhen.Expr, columnRefs)
				// Xử lý kết quả THEN
				p.extractColumnReferences(whenCase.CaseWhen.Result, columnRefs)
			}
		}
		// Xử lý mệnh đề ELSE
		if caseExpr.Defresult != nil {
			p.extractColumnReferences(caseExpr.Defresult, columnRefs)
		}

	case *pg_query.Node_ResTarget:
		// Process result target
		if n.ResTarget.Val != nil {
			p.extractColumnReferences(n.ResTarget.Val, columnRefs)
		}

	case *pg_query.Node_AExpr:
		// Process expression parts
		if n.AExpr.Lexpr != nil {
			p.extractColumnReferences(n.AExpr.Lexpr, columnRefs)
		}
		if n.AExpr.Rexpr != nil {
			p.extractColumnReferences(n.AExpr.Rexpr, columnRefs)
		}

	case *pg_query.Node_BoolExpr:
		// Process boolean expression args
		for _, arg := range n.BoolExpr.Args {
			p.extractColumnReferences(arg, columnRefs)
		}

	case *pg_query.Node_FuncCall:
		// Process function call args
		for _, arg := range n.FuncCall.Args {
			p.extractColumnReferences(arg, columnRefs)
		}

	case *pg_query.Node_JoinExpr:
		// Process join expression
		if n.JoinExpr.Larg != nil {
			p.extractColumnReferences(n.JoinExpr.Larg, columnRefs)
		}
		if n.JoinExpr.Rarg != nil {
			p.extractColumnReferences(n.JoinExpr.Rarg, columnRefs)
		}
		if n.JoinExpr.Quals != nil {
			p.extractColumnReferences(n.JoinExpr.Quals, columnRefs)
		}

	case *pg_query.Node_SortBy:
		// Process sort by
		if n.SortBy.Node != nil {
			p.extractColumnReferences(n.SortBy.Node, columnRefs)
		}

	case *pg_query.Node_SubLink:
		// Process subquery
		if n.SubLink.Subselect != nil {
			p.extractColumnReferences(n.SubLink.Subselect, columnRefs)
		}
	}
}

// GetFunctionCalls extracts all function calls from a query
func (p *PostgresParser) GetFunctionCalls(parseResult *pg_query.ParseResult) ([]string, error) {
	if parseResult == nil {
		return nil, nil
	}

	functionCalls := make(map[string]struct{})

	// Extract function calls from parse result
	for _, stmt := range parseResult.Stmts {
		p.extractFunctionCalls(stmt.Stmt, functionCalls)
	}

	// Convert to slice
	result := make([]string, 0, len(functionCalls))
	for funcName := range functionCalls {
		result = append(result, funcName)
	}

	return result, nil
}

// extractFunctionCalls recursively traverses the query to extract function calls
func (p *PostgresParser) extractFunctionCalls(node *pg_query.Node, functionCalls map[string]struct{}) {
	if node == nil {
		return
	}

	switch n := node.GetNode().(type) {
	case *pg_query.Node_FuncCall:
		// Extract function name
		if len(n.FuncCall.Funcname) > 0 {
			if stringVal, ok := n.FuncCall.Funcname[len(n.FuncCall.Funcname)-1].GetNode().(*pg_query.Node_String_); ok {
				functionCalls[stringVal.String_.Sval] = struct{}{}
			}
		}

		// Process function arguments recursively
		for _, arg := range n.FuncCall.Args {
			p.extractFunctionCalls(arg, functionCalls)
		}

	case *pg_query.Node_CoalesceExpr:
		functionCalls["coalesce"] = struct{}{}
		// Xử lý các arguments của coalesce
		for _, arg := range n.CoalesceExpr.Args {
			p.extractFunctionCalls(arg, functionCalls)
		}

	case *pg_query.Node_SelectStmt:
		// Process target list (SELECT columns)
		for _, target := range n.SelectStmt.TargetList {
			p.extractFunctionCalls(target, functionCalls)
		}

		// Process WHERE clause
		if n.SelectStmt.WhereClause != nil {
			p.extractFunctionCalls(n.SelectStmt.WhereClause, functionCalls)
		}

		// Process FROM clause
		for _, from := range n.SelectStmt.FromClause {
			p.extractFunctionCalls(from, functionCalls)
		}

		// Process GROUP BY clause
		for _, group := range n.SelectStmt.GroupClause {
			p.extractFunctionCalls(group, functionCalls)
		}

		// Process HAVING clause
		if n.SelectStmt.HavingClause != nil {
			p.extractFunctionCalls(n.SelectStmt.HavingClause, functionCalls)
		}

		// Process ORDER BY clause
		for _, sort := range n.SelectStmt.SortClause {
			p.extractFunctionCalls(sort, functionCalls)
		}

		// Process WITH clause (CTEs)
		if n.SelectStmt.WithClause != nil {
			p.extractFunctionCalls(&pg_query.Node{Node: &pg_query.Node_WithClause{WithClause: n.SelectStmt.WithClause}}, functionCalls)
		}

	case *pg_query.Node_WithClause:
		// Process all CTEs
		for _, cte := range n.WithClause.Ctes {
			if cteNode, ok := cte.GetNode().(*pg_query.Node_CommonTableExpr); ok {
				if cteNode.CommonTableExpr.Ctequery != nil {
					p.extractFunctionCalls(cteNode.CommonTableExpr.Ctequery, functionCalls)
				}
			}
		}

	case *pg_query.Node_ResTarget:
		// Process result target
		if n.ResTarget.Val != nil {
			p.extractFunctionCalls(n.ResTarget.Val, functionCalls)
		}

	case *pg_query.Node_AExpr:
		// Process expression parts
		if n.AExpr.Lexpr != nil {
			p.extractFunctionCalls(n.AExpr.Lexpr, functionCalls)
		}
		if n.AExpr.Rexpr != nil {
			p.extractFunctionCalls(n.AExpr.Rexpr, functionCalls)
		}

	case *pg_query.Node_BoolExpr:
		// Process boolean expression args
		for _, arg := range n.BoolExpr.Args {
			p.extractFunctionCalls(arg, functionCalls)
		}

	case *pg_query.Node_JoinExpr:
		// Process join expression
		if n.JoinExpr.Larg != nil {
			p.extractFunctionCalls(n.JoinExpr.Larg, functionCalls)
		}
		if n.JoinExpr.Rarg != nil {
			p.extractFunctionCalls(n.JoinExpr.Rarg, functionCalls)
		}
		if n.JoinExpr.Quals != nil {
			p.extractFunctionCalls(n.JoinExpr.Quals, functionCalls)
		}

	case *pg_query.Node_SortBy:
		// Process sort by
		if n.SortBy.Node != nil {
			p.extractFunctionCalls(n.SortBy.Node, functionCalls)
		}

	case *pg_query.Node_SubLink:
		// Process subquery
		if n.SubLink.Subselect != nil {
			p.extractFunctionCalls(n.SubLink.Subselect, functionCalls)
		}
	}
}

func (p *PostgresParser) GetTableAliases(result *pg_query.ParseResult) (map[string]string, error) {
	aliases := make(map[string]string)
	for _, stmt := range result.Stmts {
		if selectStmt, ok := stmt.Stmt.GetNode().(*pg_query.Node_SelectStmt); ok {
			if err := p.extractAliasesFromFromClause(selectStmt.SelectStmt.FromClause, aliases); err != nil {
				return nil, err
			}
		}
	}
	return aliases, nil
}

func (p *PostgresParser) extractAliasesFromFromClause(fromClause []*pg_query.Node, aliases map[string]string) error {
	for _, from := range fromClause {
		if rangeVar, ok := from.GetNode().(*pg_query.Node_RangeVar); ok {
			if rangeVar.RangeVar.Alias != nil && rangeVar.RangeVar.Alias.Aliasname != "" {
				aliases[rangeVar.RangeVar.Alias.Aliasname] = rangeVar.RangeVar.Relname
			} else {
				aliases[rangeVar.RangeVar.Relname] = rangeVar.RangeVar.Relname
			}
		} else if joinExpr, ok := from.GetNode().(*pg_query.Node_JoinExpr); ok {
			if err := p.extractAliasesFromFromClause([]*pg_query.Node{joinExpr.JoinExpr.Larg}, aliases); err != nil {
				return err
			}
			if err := p.extractAliasesFromFromClause([]*pg_query.Node{joinExpr.JoinExpr.Rarg}, aliases); err != nil {
				return err
			}
		}
	}
	return nil
}
