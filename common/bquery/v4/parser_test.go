package v4

import (
	"sort"
	"strings"
	"testing"

	"github.com/pganalyze/pg_query_go/v6"
	"github.com/stretchr/testify/assert"
)

func TestPostgresParser_Parse(t *testing.T) {
	parser := NewPostgresParser()

	tests := []struct {
		name    string
		sql     string
		wantErr bool
	}{
		{
			name:    "Simple SELECT query",
			sql:     "SELECT * FROM users",
			wantErr: false,
		},
		{
			name:    "Invalid SQL",
			sql:     "SELECT * FROMM users",
			wantErr: true,
		},
		{
			name:    "Complex query with joins",
			sql:     "SELECT u.name, o.order_date FROM users u JOIN orders o ON u.id = o.user_id WHERE u.active = true",
			wantErr: false,
		},
		{
			name:    "Query with CTEs",
			sql:     "WITH active_users AS (SELECT * FROM users WHERE active = true) SELECT * FROM active_users",
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := parser.Parse(tt.sql)
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
			}
		})
	}
}

func TestPostgresParser_Normalize(t *testing.T) {
	parser := NewPostgresParser()

	tests := []struct {
		name     string
		sql      string
		expected string
		wantErr  bool
	}{
		{
			name:     "Query with literals",
			sql:      "SELECT * FROM users WHERE id = 123 AND name = 'John'",
			expected: "SELECT * FROM users WHERE id = $1 AND name = $2",
			wantErr:  false,
		},
		{
			name:     "Query with date literals",
			sql:      "SELECT * FROM orders WHERE order_date > '2023-01-01'",
			expected: "SELECT * FROM orders WHERE order_date > $1",
			wantErr:  false,
		},
		{
			name:     "Invalid SQL",
			sql:      "SELECT * FROMM users",
			expected: "",
			wantErr:  true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			normalized, err := parser.Normalize(tt.sql)
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expected, normalized)
			}
		})
	}
}

func TestPostgresParser_GetTableNames(t *testing.T) {
	parser := NewPostgresParser()

	tests := []struct {
		name           string
		sql            string
		expectedTables []string
		wantErr        bool
	}{
		{
			name:           "Simple SELECT query",
			sql:            "SELECT * FROM users",
			expectedTables: []string{"users"},
			wantErr:        false,
		},
		{
			name:           "Query with multiple tables",
			sql:            "SELECT u.name, o.order_date FROM users u JOIN orders o ON u.id = o.user_id",
			expectedTables: []string{"users", "orders"},
			wantErr:        false,
		},
		{
			name:           "Query with subquery",
			sql:            "SELECT * FROM (SELECT id FROM customers) AS c",
			expectedTables: []string{"customers"},
			wantErr:        false,
		},
		{
			name:           "Query with CTEs",
			sql:            "WITH active_users AS (SELECT * FROM users WHERE active = true) SELECT * FROM active_users JOIN profiles ON active_users.id = profiles.user_id",
			expectedTables: []string{"users", "profiles"},
			wantErr:        false,
		},
		{
			name: "Complex query with multiple CTEs",
			sql: `
				WITH 
				recent_orders AS (
					SELECT * FROM orders WHERE order_date > '2023-01-01'
				),
				active_customers AS (
					SELECT c.* FROM customers c
					JOIN recent_orders ro ON c.id = ro.customer_id
					WHERE c.status = 'active'
				)
				SELECT ac.name, p.product_name, ro.order_date
				FROM active_customers ac
				JOIN recent_orders ro ON ac.id = ro.customer_id
				JOIN products p ON ro.product_id = p.id
			`,
			expectedTables: []string{"orders", "customers", "products"},
			wantErr:        false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := parser.Parse(tt.sql)
			if err != nil {
				t.Fatalf("Failed to parse SQL: %v", err)
			}

			tables, err := parser.GetTableNames(result)
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)

				// Sort both slices for consistent comparison
				sort.Strings(tables)
				sort.Strings(tt.expectedTables)

				assert.Equal(t, tt.expectedTables, tables)
			}
		})
	}
}

func TestPostgresParser_GetCTEDepth(t *testing.T) {
	parser := NewPostgresParser()

	tests := []struct {
		name          string
		sql           string
		expectedDepth int
		wantErr       bool
	}{
		{
			name:          "Query without CTEs",
			sql:           "SELECT * FROM users",
			expectedDepth: 0,
			wantErr:       false,
		},
		{
			name:          "Query with single CTE",
			sql:           "WITH active_users AS (SELECT * FROM users WHERE active = true) SELECT * FROM active_users",
			expectedDepth: 1,
			wantErr:       false,
		},
		{
			name: "Query with dependent CTEs",
			sql: `
				WITH 
				base_users AS (
					SELECT * FROM users WHERE active = true
				),
				premium_users AS (
					SELECT * FROM base_users WHERE subscription_type = 'premium'
				)
				SELECT * FROM premium_users
			`,
			expectedDepth: 2,
			wantErr:       false,
		},
		{
			name: "Query with complex CTE dependencies",
			sql: `
				WITH 
				base_data AS (
					SELECT * FROM raw_data
				),
				filtered_data AS (
					SELECT * FROM base_data WHERE value > 100
				),
				aggregated_data AS (
					SELECT category, SUM(value) as total 
					FROM filtered_data 
					GROUP BY category
				),
				ranked_data AS (
					SELECT *, RANK() OVER (ORDER BY total DESC) as rank
					FROM aggregated_data
				)
				SELECT * FROM ranked_data WHERE rank <= 10
			`,
			expectedDepth: 4,
			wantErr:       false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := parser.Parse(tt.sql)
			if err != nil {
				t.Fatalf("Failed to parse SQL: %v", err)
			}

			depth, err := parser.GetCTEDepth(result)
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedDepth, depth)
			}
		})
	}
}

func TestPostgresParser_GetColumnReferences(t *testing.T) {
	parser := NewPostgresParser()

	tests := []struct {
		name               string
		sql                string
		expectedReferences []ColumnReference
		wantErr            bool
	}{
		{
			name: "Simple column references",
			sql:  "SELECT id, name FROM users WHERE age > 18",
			expectedReferences: []ColumnReference{
				{Table: "", Column: "id"},
				{Table: "", Column: "name"},
				{Table: "", Column: "age"},
			},
			wantErr: false,
		},
		{
			name: "Table qualified column references",
			sql:  "SELECT u.id, u.name FROM users u WHERE u.age > 18",
			expectedReferences: []ColumnReference{
				{Table: "u", Column: "id"},
				{Table: "u", Column: "name"},
				{Table: "u", Column: "age"},
			},
			wantErr: false,
		},
		{
			name: "Mixed references with joins",
			sql:  "SELECT u.id, name, o.order_id FROM users u JOIN orders o ON u.id = o.user_id WHERE status = 'active'",
			expectedReferences: []ColumnReference{
				{Table: "u", Column: "id"},
				{Table: "", Column: "name"},
				{Table: "o", Column: "order_id"},
				{Table: "u", Column: "id"},
				{Table: "o", Column: "user_id"},
				{Table: "", Column: "status"},
			},
			wantErr: false,
		},
		{
			name: "Complex query with subqueries",
			sql: `
				SELECT u.id, u.name, 
				(SELECT COUNT(*) FROM orders o WHERE o.user_id = u.id) as order_count
				FROM users u
				WHERE u.status = 'active'
				GROUP BY u.id, u.name
				HAVING COUNT(u.id) > 0
				ORDER BY u.name
			`,
			expectedReferences: []ColumnReference{
				{Table: "u", Column: "id"},
				{Table: "u", Column: "name"},
				{Table: "o", Column: "user_id"},
				{Table: "u", Column: "id"},
				{Table: "u", Column: "status"},
				{Table: "u", Column: "id"},
				{Table: "u", Column: "name"},
				{Table: "u", Column: "id"},
				{Table: "u", Column: "name"},
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := parser.Parse(tt.sql)
			if err != nil {
				t.Fatalf("Failed to parse SQL: %v", err)
			}

			refs, err := parser.GetColumnReferences(result)
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)

				// Verify all expected references are present
				// Note: The exact order might vary, so we check if all expected references are included
				for _, expected := range tt.expectedReferences {
					found := false
					for _, actual := range refs {
						if actual.Table == expected.Table && actual.Column == expected.Column {
							found = true
							break
						}
					}
					assert.True(t, found, "Expected to find column reference %+v", expected)
				}
			}
		})
	}
}

func TestPostgresParser_GetFunctionCalls(t *testing.T) {
	parser := NewPostgresParser()

	tests := []struct {
		name              string
		sql               string
		expectedFunctions []string
		wantErr           bool
	}{
		{
			name:              "Query with COUNT function",
			sql:               "SELECT COUNT(*) FROM users",
			expectedFunctions: []string{"count"},
			wantErr:           false,
		},
		{
			name:              "Query with multiple functions",
			sql:               "SELECT MAX(age), MIN(age), AVG(salary) FROM employees",
			expectedFunctions: []string{"max", "min", "avg"},
			wantErr:           false,
		},
		{
			name:              "Query with nested functions",
			sql:               "SELECT ROUND(AVG(salary), 2) FROM employees",
			expectedFunctions: []string{"round", "avg"},
			wantErr:           false,
		},
		{
			name: "Complex query with multiple function calls",
			sql: `
				SELECT 
					u.id,
					u.name,
					count(o.id) as order_count,
					coalesce(u.nickname, 'Anonymous') as display_name,
					sum(o.amount) as total_spent,
					round(avg(o.amount), 2) as avg_order,
					date_trunc('month', max(o.order_date)) as last_order_month
				FROM users u
				LEFT JOIN orders o ON u.id = o.user_id
				WHERE u.created_at > now() - interval '1 year'
				GROUP BY u.id, u.name, u.nickname
				HAVING count(o.id) > 0
				ORDER BY sum(o.amount) DESC
			`,
			expectedFunctions: []string{"coalesce", "count", "sum", "round", "avg", "date_trunc", "max", "now"},
			wantErr:           false,
		},
		{
			name: "Query with window functions",
			sql: `
				SELECT 
					department,
					employee_name,
					salary,
					RANK() OVER (PARTITION BY department ORDER BY salary DESC) as salary_rank,
					DENSE_RANK() OVER (PARTITION BY department ORDER BY salary DESC) as dense_rank,
					ROW_NUMBER() OVER (PARTITION BY department ORDER BY hire_date) as seniority,
					LEAD(salary, 1) OVER (PARTITION BY department ORDER BY salary DESC) as next_lower_salary
				FROM employees
			`,
			expectedFunctions: []string{"rank", "dense_rank", "row_number", "lead"},
			wantErr:           false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := parser.Parse(tt.sql)
			if err != nil {
				t.Fatalf("Failed to parse SQL: %v", err)
			}

			functions, err := parser.GetFunctionCalls(result)
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)

				// Chuyển đổi tất cả tên hàm về chữ thường để so sánh
				for i := range functions {
					functions[i] = strings.ToLower(functions[i])
				}

				expectedLower := make([]string, len(tt.expectedFunctions))
				for i, f := range tt.expectedFunctions {
					expectedLower[i] = strings.ToLower(f)
				}

				// Sort both slices for consistent comparison
				sort.Strings(functions)
				sort.Strings(expectedLower)

				assert.Equal(t, expectedLower, functions)
			}
		})
	}
}

func TestPostgresParser_ComplexQueries(t *testing.T) {
	parser := NewPostgresParser()

	tests := []struct {
		name        string
		sql         string
		checkTables bool
		tables      []string
		checkFuncs  bool
		functions   []string
	}{
		{
			name: "Complex analytical query with window functions",
			sql: `
				WITH monthly_sales AS (
					SELECT 
						DATE_TRUNC('month', order_date) as month,
						product_id,
						category_id,
						SUM(quantity * price) as revenue
					FROM orders o
					JOIN order_items oi ON o.id = oi.order_id
					JOIN products p ON oi.product_id = p.id
					WHERE order_date >= '2022-01-01' AND order_date < '2023-01-01'
					GROUP BY DATE_TRUNC('month', order_date), product_id, category_id
				),
				ranked_products AS (
					SELECT 
						month,
						product_id,
						revenue,
						category_id,
						RANK() OVER (PARTITION BY month, category_id ORDER BY revenue DESC) as rank_in_category
					FROM monthly_sales
				)
				SELECT 
					rp.month,
					c.name as category_name,
					p.name as product_name,
					rp.revenue,
					rp.rank_in_category,
					SUM(rp.revenue) OVER (PARTITION BY rp.month, rp.category_id) as category_total,
					ROUND(rp.revenue * 100.0 / SUM(rp.revenue) OVER (PARTITION BY rp.month, rp.category_id), 2) as category_percentage
				FROM ranked_products rp
				JOIN products p ON rp.product_id = p.id
				JOIN categories c ON rp.category_id = c.id
				WHERE rp.rank_in_category <= 3
				ORDER BY rp.month, c.name, rp.rank_in_category
			`,
			checkTables: true,
			tables:      []string{"orders", "order_items", "products", "categories"},
			checkFuncs:  true,
			functions:   []string{"date_trunc", "sum", "rank", "round"},
		},
		{
			name: "Recursive CTE query",
			sql: `
				WITH RECURSIVE employee_hierarchy AS (
					-- Base case: top-level employees (no manager)
					SELECT id, name, manager_id, 1 as level
					FROM employees
					WHERE manager_id IS NULL
					
					UNION ALL
					
					-- Recursive case: employees with managers
					SELECT e.id, e.name, e.manager_id, eh.level + 1
					FROM employees e
					JOIN employee_hierarchy eh ON e.manager_id = eh.id
				)
				SELECT 
					eh.id,
					eh.name,
					eh.level,
					m.name as manager_name,
					COUNT(s.id) as subordinates_count
				FROM employee_hierarchy eh
				LEFT JOIN employees m ON eh.manager_id = m.id
				LEFT JOIN employees s ON s.manager_id = eh.id
				GROUP BY eh.id, eh.name, eh.level, m.name
				ORDER BY eh.level, eh.name
			`,
			checkTables: true,
			tables:      []string{"employees"},
			checkFuncs:  true,
			functions:   []string{"COUNT"},
		},
		{
			name: "Query with JSON operations",
			sql: `
				SELECT 
					u.id,
					u.name,
					u.preferences->>'theme' as theme,
					u.preferences->>'language' as language,
					JSONB_ARRAY_LENGTH(u.saved_items) as saved_items_count,
					JSONB_AGG(DISTINCT p.category) as interested_categories
				FROM users u
				LEFT JOIN user_actions ua ON u.id = ua.user_id
				LEFT JOIN products p ON ua.product_id = p.id
				WHERE u.active = true
				AND (u.preferences->>'notifications')::boolean = true
				GROUP BY u.id, u.name, u.preferences, u.saved_items
				HAVING COUNT(ua.id) > 5
			`,
			checkTables: true,
			tables:      []string{"users", "user_actions", "products"},
			checkFuncs:  true,
			functions:   []string{"JSONB_ARRAY_LENGTH", "JSONB_AGG", "COUNT"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := parser.Parse(tt.sql)
			assert.NoError(t, err)
			assert.NotNil(t, result)

			if tt.checkTables {
				tables, err := parser.GetTableNames(result)
				assert.NoError(t, err)

				// Sort both slices for consistent comparison
				sort.Strings(tables)
				sort.Strings(tt.tables)

				assert.Equal(t, tt.tables, tables)
			}

			// Trong phần kiểm tra functions:
			if tt.checkFuncs {
				functions, err := parser.GetFunctionCalls(result)
				assert.NoError(t, err)

				// Chuyển đổi tất cả tên hàm về chữ thường để so sánh
				lowerFunctions := make([]string, len(functions))
				for i, f := range functions {
					lowerFunctions[i] = strings.ToLower(f)
				}

				// Check if all expected functions are present
				for _, expectedFunc := range tt.functions {
					found := false
					expectedLower := strings.ToLower(expectedFunc)
					for _, actualFunc := range lowerFunctions {
						if actualFunc == expectedLower {
							found = true
							break
						}
					}
					assert.True(t, found, "Expected function %s not found", expectedFunc)
				}
			}
		})
	}
}

func TestPostgresParser_EdgeCases(t *testing.T) {
	parser := NewPostgresParser()

	tests := []struct {
		name     string
		sql      string
		testFunc func(t *testing.T, result *pg_query.ParseResult, parser SQLParser)
	}{
		{
			name: "Empty query",
			sql:  "",
			testFunc: func(t *testing.T, result *pg_query.ParseResult, parser SQLParser) {
				tables, err := parser.GetTableNames(result)
				assert.NoError(t, err)
				assert.Empty(t, tables)

				funcs, err := parser.GetFunctionCalls(result)
				assert.NoError(t, err)
				assert.Empty(t, funcs)

				cols, err := parser.GetColumnReferences(result)
				assert.NoError(t, err)
				assert.Empty(t, cols)
			},
		},
		{
			name: "Query with schema-qualified tables",
			sql:  "SELECT * FROM public.users JOIN auth.credentials ON public.users.id = auth.credentials.user_id",
			testFunc: func(t *testing.T, result *pg_query.ParseResult, parser SQLParser) {
				tables, err := parser.GetTableNames(result)
				assert.NoError(t, err)

				// Note: The parser might extract tables with or without schema qualification
				// This test checks if both tables are present in some form
				foundUsers := false
				foundCredentials := false

				for _, table := range tables {
					if table == "users" || table == "public.users" {
						foundUsers = true
					}
					if table == "credentials" || table == "auth.credentials" {
						foundCredentials = true
					}
				}

				assert.True(t, foundUsers, "Should find users table")
				assert.True(t, foundCredentials, "Should find credentials table")
			},
		},
		{
			name: "Query with CASE expressions",
			sql: `
				SELECT 
					id,
					name,
					CASE 
						WHEN age < 18 THEN 'Minor'
						WHEN age BETWEEN 18 AND 65 THEN 'Adult'
						ELSE 'Senior'
					END as age_group
				FROM users
			`,
			testFunc: func(t *testing.T, result *pg_query.ParseResult, parser SQLParser) {
				cols, err := parser.GetColumnReferences(result)
				assert.NoError(t, err)

				// Check for 'age' column reference
				foundAge := false
				for _, col := range cols {
					if col.Column == "age" {
						foundAge = true
						break
					}
				}

				assert.True(t, foundAge, "Should find age column reference")
			},
		},
		{
			name: "Query with UNION, INTERSECT, EXCEPT",
			sql: `
				(SELECT id, name FROM users WHERE type = 'customer')
				UNION
				(SELECT id, name FROM admins)
				EXCEPT
				(SELECT id, name FROM blocked_accounts)
			`,
			testFunc: func(t *testing.T, result *pg_query.ParseResult, parser SQLParser) {
				tables, err := parser.GetTableNames(result)
				assert.NoError(t, err)

				expectedTables := []string{"users", "admins", "blocked_accounts"}
				sort.Strings(tables)
				sort.Strings(expectedTables)

				assert.Equal(t, expectedTables, tables)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := parser.Parse(tt.sql)
			if err != nil {
				// Some edge cases might legitimately fail to parse
				return
			}

			tt.testFunc(t, result, parser)
		})
	}
}
