package v4

import (
	pg_query "github.com/pganalyze/pg_query_go/v6"
	"strings"
)

// hasAggregateFunction kiểm tra xem một SELECT statement có sử dụng hàm tổng hợp không
func (e *RuleEngine) hasAggregateFunction(selectStmt *pg_query.SelectStmt) bool {
	if selectStmt == nil || selectStmt.TargetList == nil {
		return false
	}

	// Kiểm tra từng mục trong target list (các cột được chọn)
	for _, target := range selectStmt.TargetList {
		if resTarget, ok := target.GetNode().(*pg_query.Node_ResTarget); ok {
			if resTarget.ResTarget.Val != nil {
				if e.isAggregateExpr(resTarget.ResTarget.Val) {
					return true
				}
			}
		}
	}

	// Kiểm tra HAVING clause (thường chứa hàm tổng hợp)
	if selectStmt.HavingClause != nil {
		if e.isAggregateExpr(selectStmt.HavingClause) {
			return true
		}
	}

	return false
}

// isAggregateExpr kiểm tra đệ quy xem một biểu thức có chứa hàm tổng hợp không
func (e *RuleEngine) isAggregateExpr(expr *pg_query.Node) bool {
	if expr == nil {
		return false
	}

	// Kiểm tra nếu là hàm
	if funcCall, ok := expr.GetNode().(*pg_query.Node_FuncCall); ok {
		// Lấy tên hàm
		if funcCall.FuncCall.Funcname != nil && len(funcCall.FuncCall.Funcname) > 0 {
			if nameNode, ok := funcCall.FuncCall.Funcname[0].GetNode().(*pg_query.Node_String_); ok {
				funcName := strings.ToLower(nameNode.String_.Sval)

				// Danh sách các hàm tổng hợp phổ biến
				aggregateFuncs := map[string]bool{
					"count":      true,
					"sum":        true,
					"avg":        true,
					"min":        true,
					"max":        true,
					"array_agg":  true,
					"string_agg": true,
					"jsonb_agg":  true,
					"json_agg":   true,
					"stddev":     true,
					"variance":   true,
					"every":      true,
					"any":        true,
					"some":       true,
				}

				if _, isAggregate := aggregateFuncs[funcName]; isAggregate {
					return true
				}
			}
		}

		// Kiểm tra cờ AGGREF
		if funcCall.FuncCall.AggOrder != nil || funcCall.FuncCall.AggStar || funcCall.FuncCall.AggFilter != nil {
			return true
		}
	}

	// Kiểm tra đệ quy các biểu thức con
	switch n := expr.GetNode().(type) {
	case *pg_query.Node_AExpr:
		return (n.AExpr.Lexpr != nil && e.isAggregateExpr(n.AExpr.Lexpr)) ||
			(n.AExpr.Rexpr != nil && e.isAggregateExpr(n.AExpr.Rexpr))

	case *pg_query.Node_BoolExpr:
		for _, arg := range n.BoolExpr.Args {
			if e.isAggregateExpr(arg) {
				return true
			}
		}

	case *pg_query.Node_CaseExpr:
		if n.CaseExpr.Arg != nil && e.isAggregateExpr(n.CaseExpr.Arg) {
			return true
		}
		for _, when := range n.CaseExpr.Args {
			if e.isAggregateExpr(when) {
				return true
			}
		}
		if n.CaseExpr.Defresult != nil && e.isAggregateExpr(n.CaseExpr.Defresult) {
			return true
		}

	case *pg_query.Node_CaseWhen:
		return (n.CaseWhen.Expr != nil && e.isAggregateExpr(n.CaseWhen.Expr)) ||
			(n.CaseWhen.Result != nil && e.isAggregateExpr(n.CaseWhen.Result))

	case *pg_query.Node_CoalesceExpr:
		for _, arg := range n.CoalesceExpr.Args {
			if e.isAggregateExpr(arg) {
				return true
			}
		}

	case *pg_query.Node_NullTest:
		return n.NullTest.Arg != nil && e.isAggregateExpr(n.NullTest.Arg)

	case *pg_query.Node_BooleanTest:
		return n.BooleanTest.Arg != nil && e.isAggregateExpr(n.BooleanTest.Arg)
	}

	return false
}
