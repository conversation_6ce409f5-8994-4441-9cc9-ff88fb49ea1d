package v4

import (
	"errors"
	"fmt"
	"strings"

	"github.com/pganalyze/pg_query_go/v6"
)

// Error definitions
var (
	ErrEmptyQuery           = errors.New("empty query")
	ErrDangerousOperation   = errors.New("query contains dangerous operation")
	ErrWriteOperation       = errors.New("write operation not allowed")
	ErrTableNotAllowed      = errors.New("table not allowed")
	ErrColumnNotAllowed     = errors.New("column not allowed")
	ErrCTEDepthExceeded     = errors.New("CTE depth exceeded allowed limit")
	ErrJSONBFieldNotAllowed = errors.New("JSONB field access not allowed")
	ErrParamTypeMismatch    = errors.New("parameter type mismatch")
	ErrFunctionNotAllowed   = errors.New("function not allowed")
	ErrRowLimitExceeded     = errors.New("row limit exceeded allowed maximum")
	ErrMissingLimit         = errors.New("query requires LIMIT clause")
)

// AccessMode defines the access mode for tables and columns
type AccessMode int

const (
	// DenyList means items in the list are denied, everything else is allowed
	DenyList AccessMode = iota
	// AllowList means only items in the list are allowed, everything else is denied
	AllowList
	// WildcardAny đại diện cho bất kỳ chuỗi nào (tương tự * trong glob)
	WildcardAny = "*"
)

// Pattern represents a pattern that can be used for matching
type Pattern struct {
	value     string
	isPattern bool
}

// NewPattern creates a new pattern from a string
func NewPattern(value string) Pattern {
	return Pattern{
		value:     value,
		isPattern: strings.Contains(value, WildcardAny),
	}
}

// Matches checks if a string matches this pattern
func (p Pattern) Matches(s string) bool {
	if !p.isPattern {
		return p.value == s
	}

	// Simple wildcard handling
	parts := strings.Split(p.value, WildcardAny)

	// Special case: pattern is just "*"
	if len(parts) == 0 || (len(parts) == 1 && parts[0] == "") {
		return true
	}

	// Check each part of the pattern
	current := 0
	for i, part := range parts {
		if part == "" {
			continue
		}

		// First part must match the beginning
		if i == 0 && !strings.HasPrefix(s, part) {
			return false
		}

		// Last part must match the end
		if i == len(parts)-1 && !strings.HasSuffix(s, part) {
			return false
		}

		// Find this part in the string
		pos := strings.Index(s[current:], part)
		if pos == -1 {
			return false
		}

		// Move current position
		current += pos + len(part)
	}

	return true
}

// AuditConfig holds configuration for the rule engine
type AuditConfig struct {
	// Table access configuration
	TableAccessMode AccessMode
	TablesList      map[Pattern]bool

	// Column access configuration
	ColumnAccessMode AccessMode
	ColumnsList      map[Pattern]map[Pattern]bool // map[tablePattern]map[columnPattern]bool

	// JSONB field access configuration
	AllowedJSONBFields map[Pattern]map[Pattern]bool // map[tablePattern]map[columnPattern]bool

	// Function access configuration
	FunctionAccessMode AccessMode
	FunctionsList      map[Pattern]bool

	// CTE configuration
	AllowedCTEDepth int

	// Operation permissions
	AllowWriteOperations bool
	AllowReadOperations  bool

	// Row limit configuration
	MaxRowLimit  int  // Maximum number of rows that can be returned
	EnforceLimit bool // Whether to enforce LIMIT clause in queries
	DefaultLimit int  // Default LIMIT value to suggest if not specified

}

// DefaultAuditConfig returns a default configuration that only allows SELECT operations
func DefaultAuditConfig() *AuditConfig {
	return &AuditConfig{
		TableAccessMode:      AllowList,
		TablesList:           make(map[Pattern]bool),
		ColumnAccessMode:     AllowList,
		ColumnsList:          make(map[Pattern]map[Pattern]bool),
		AllowedJSONBFields:   make(map[Pattern]map[Pattern]bool),
		FunctionAccessMode:   AllowList,
		FunctionsList:        make(map[Pattern]bool),
		AllowedCTEDepth:      5,
		AllowWriteOperations: false,
		AllowReadOperations:  true,
		MaxRowLimit:          1000,
		EnforceLimit:         true,
		DefaultLimit:         100,
	}
}

// RuleEngine implements SQL query validation rules
type RuleEngine struct {
	config *AuditConfig
	parser SQLParser
}

// NewRuleEngine creates a new rule engine with the given configuration
func NewRuleEngine(config *AuditConfig) *RuleEngine {
	return &RuleEngine{
		config: config,
		parser: NewPostgresParser(),
	}
}

// Validate checks if a parsed SQL query meets the security and safety rules
func (e *RuleEngine) Validate(result *pg_query.ParseResult) error {
	if result == nil || len(result.Stmts) == 0 {
		return ErrEmptyQuery
	}

	for _, stmt := range result.Stmts {
		// Check statement type for dangerous operations
		if isDangerousOperation(stmt.Stmt) {
			return ErrDangerousOperation
		}

		// Check for write operations
		if isWriteOperation(stmt.Stmt) {
			if !e.config.AllowWriteOperations {
				return ErrWriteOperation
			}
		} else if isReadOperation(stmt.Stmt) {
			if !e.config.AllowReadOperations {
				return fmt.Errorf("read operations not allowed")
			}

			// Kiểm tra giới hạn số hàng cho các truy vấn SELECT
			if err := e.validateRowLimit(stmt.Stmt); err != nil {
				return err
			}
		}

		// Check table permissions
		tables, err := e.parser.GetTableNames(result)
		if err != nil {
			return fmt.Errorf("error extracting table names: %w", err)
		}

		for _, table := range tables {
			if !e.isTableAllowed(table) {
				return fmt.Errorf("%w: %s", ErrTableNotAllowed, table)
			}
		}

		// Check column permissions
		columns, err := e.parser.GetColumnReferences(result)
		if err != nil {
			return fmt.Errorf("error extracting column references: %w", err)
		}

		for _, col := range columns {
			if !e.isColumnAllowed(col.Table, col.Column) {
				return fmt.Errorf("%w: %s.%s", ErrColumnNotAllowed, col.Table, col.Column)
			}
		}

		// Check function permissions
		functions, err := e.parser.GetFunctionCalls(result)
		if err != nil {
			return fmt.Errorf("error extracting function calls: %w", err)
		}

		for _, function := range functions {
			if !e.isFunctionAllowed(function) {
				return fmt.Errorf("%w: %s", ErrFunctionNotAllowed, function)
			}
		}

		// Check CTE depth
		cteDepth, err := e.parser.GetCTEDepth(result)
		if err != nil {
			return fmt.Errorf("error checking CTE depth: %w", err)
		}

		if cteDepth > e.config.AllowedCTEDepth {
			return ErrCTEDepthExceeded
		}

		// Validate JSONB field access
		if err := e.validateJSONBAccess(stmt.Stmt); err != nil {
			return err
		}
	}

	return nil
}

// validateRowLimit kiểm tra giới hạn số hàng trả về cho câu truy vấn SELECT
func (e *RuleEngine) validateRowLimit(stmt *pg_query.Node) error {
	if stmt == nil {
		return nil
	}

	selectStmt, ok := stmt.GetNode().(*pg_query.Node_SelectStmt)
	if !ok {
		return nil // Không phải SELECT statement
	}

	// Kiểm tra xem truy vấn có sử dụng hàm tổng hợp không
	if e.hasAggregateFunction(selectStmt.SelectStmt) {
		return nil // Bỏ qua kiểm tra LIMIT cho truy vấn có hàm tổng hợp
	}

	// Kiểm tra xem có GROUP BY không (cũng là dạng tổng hợp)
	if selectStmt.SelectStmt.GroupClause != nil && len(selectStmt.SelectStmt.GroupClause) > 0 {
		return nil // Bỏ qua kiểm tra LIMIT cho truy vấn có GROUP BY
	}

	// Kiểm tra xem có LIMIT hay không
	limitSpecified := selectStmt.SelectStmt.LimitCount != nil

	// Nếu bắt buộc có LIMIT nhưng không có
	if e.config.EnforceLimit && !limitSpecified {
		return fmt.Errorf("%w: please add LIMIT clause (suggested default: LIMIT %d)",
			ErrMissingLimit, e.config.DefaultLimit)
	}

	// Nếu có LIMIT, kiểm tra giá trị
	if limitSpecified {
		// Kiểm tra nếu LIMIT là một hằng số
		if constNode, ok := selectStmt.SelectStmt.LimitCount.GetNode().(*pg_query.Node_AConst); ok {
			// Sử dụng GetIval() thay vì truy cập trực tiếp
			if intVal := constNode.AConst.GetIval(); intVal != nil {
				limitValue := intVal.Ival

				// Kiểm tra nếu giá trị LIMIT vượt quá giới hạn cho phép
				if int(limitValue) > e.config.MaxRowLimit {
					return fmt.Errorf("%w: LIMIT %d exceeds maximum allowed (%d)",
						ErrRowLimitExceeded, limitValue, e.config.MaxRowLimit)
				}
			}
		} else {
			// LIMIT không phải là hằng số (có thể là tham số hoặc biểu thức)
			// Trong trường hợp này, chúng ta không thể biết giá trị chính xác
			// Có thể xem xét thêm logic kiểm tra tham số nếu cần
			//return fmt.Errorf("%w: LIMIT must be a constant value, not a parameter or expression",
			//	ErrMissingLimit)
		}
	}

	// Kiểm tra các subquery trong SELECT
	return e.validateSubqueryRowLimits(stmt)
}

// validateSubqueryRowLimits kiểm tra giới hạn số hàng cho các subquery
func (e *RuleEngine) validateSubqueryRowLimits(node *pg_query.Node) error {
	if node == nil {
		return nil
	}

	// Kiểm tra nếu node là một subquery
	if subLink, ok := node.GetNode().(*pg_query.Node_SubLink); ok {
		if subSelect, ok := subLink.SubLink.Subselect.GetNode().(*pg_query.Node_SelectStmt); ok {
			// Kiểm tra LIMIT trong subquery
			limitSpecified := subSelect.SelectStmt.LimitCount != nil

			if e.config.EnforceLimit && !limitSpecified {
				return fmt.Errorf("%w: subquery requires LIMIT clause (suggested default: LIMIT %d)",
					ErrMissingLimit, e.config.DefaultLimit)
			}

			if limitSpecified {
				if constNode, ok := subSelect.SelectStmt.LimitCount.GetNode().(*pg_query.Node_AConst); ok {
					if intVal := constNode.AConst.GetIval(); intVal != nil {
						limitValue := intVal.Ival
						if int(limitValue) > e.config.MaxRowLimit {
							return fmt.Errorf("%w: subquery LIMIT %d exceeds maximum allowed (%d)",
								ErrRowLimitExceeded, limitValue, e.config.MaxRowLimit)
						}
					}
				}
			}

			// Kiểm tra đệ quy các subquery lồng nhau
			return e.validateSubqueryRowLimits(subLink.SubLink.Subselect)
		}
	}

	// Kiểm tra các node con theo loại node
	switch n := node.GetNode().(type) {
	case *pg_query.Node_SelectStmt:
		// Kiểm tra target list
		if n.SelectStmt.TargetList != nil {
			for _, target := range n.SelectStmt.TargetList {
				if err := e.validateSubqueryRowLimits(target); err != nil {
					return err
				}
			}
		}
		// Kiểm tra where clause
		if n.SelectStmt.WhereClause != nil {
			if err := e.validateSubqueryRowLimits(n.SelectStmt.WhereClause); err != nil {
				return err
			}
		}
		// Kiểm tra having clause
		if n.SelectStmt.HavingClause != nil {
			if err := e.validateSubqueryRowLimits(n.SelectStmt.HavingClause); err != nil {
				return err
			}
		}
		// Kiểm tra from clause
		if n.SelectStmt.FromClause != nil {
			for _, from := range n.SelectStmt.FromClause {
				if err := e.validateSubqueryRowLimits(from); err != nil {
					return err
				}
			}
		}

	case *pg_query.Node_AExpr:
		// Kiểm tra biểu thức bên trái và phải
		if n.AExpr.Lexpr != nil {
			if err := e.validateSubqueryRowLimits(n.AExpr.Lexpr); err != nil {
				return err
			}
		}
		if n.AExpr.Rexpr != nil {
			if err := e.validateSubqueryRowLimits(n.AExpr.Rexpr); err != nil {
				return err
			}
		}

	case *pg_query.Node_BoolExpr:
		// Kiểm tra các biểu thức boolean
		for _, arg := range n.BoolExpr.Args {
			if err := e.validateSubqueryRowLimits(arg); err != nil {
				return err
			}
		}

	case *pg_query.Node_ResTarget:
		// Kiểm tra result target
		if n.ResTarget.Val != nil {
			if err := e.validateSubqueryRowLimits(n.ResTarget.Val); err != nil {
				return err
			}
		}
	}

	return nil
}

// isTableAllowed checks if access to a table is allowed
func (e *RuleEngine) isTableAllowed(tableName string) bool {
	allowed := false

	if e.config.TableAccessMode == AllowList {
		// In AllowList mode, table must match at least one allowed pattern
		for pattern, isAllowed := range e.config.TablesList {
			if isAllowed && pattern.Matches(tableName) {
				allowed = true
				break
			}
		}
	} else { // DenyList
		// In DenyList mode, table must not match any denied pattern
		allowed = true
		for pattern, isDenied := range e.config.TablesList {
			if isDenied && pattern.Matches(tableName) {
				allowed = false
				break
			}
		}
	}

	return allowed
}

// isColumnAllowed checks if access to a column is allowed
func (e *RuleEngine) isColumnAllowed(tableName, columnName string) bool {
	if e.config.ColumnAccessMode == AllowList {
		// If table is specified, check table.column
		if tableName != "" {
			for tablePattern, columns := range e.config.ColumnsList {
				if tablePattern.Matches(tableName) {
					for columnPattern, isAllowed := range columns {
						if isAllowed && columnPattern.Matches(columnName) {
							return true
						}
					}
				}
			}
			return false
		}

		// If no table specified, check if column is allowed in any table
		for _, columns := range e.config.ColumnsList {
			for columnPattern, isAllowed := range columns {
				if isAllowed && columnPattern.Matches(columnName) {
					return true
				}
			}
		}
		return false
	} else { // DenyList
		// If table is specified, check if table.column is denied
		if tableName != "" {
			for tablePattern, columns := range e.config.ColumnsList {
				if tablePattern.Matches(tableName) {
					for columnPattern, isDenied := range columns {
						if isDenied && columnPattern.Matches(columnName) {
							return false
						}
					}
				}
			}
			return true
		}

		// If no table specified, check if column is denied in any table
		for _, columns := range e.config.ColumnsList {
			for columnPattern, isDenied := range columns {
				if isDenied && columnPattern.Matches(columnName) {
					return false
				}
			}
		}
		return true
	}
}

// isFunctionAllowed checks if a function call is allowed
func (e *RuleEngine) isFunctionAllowed(functionName string) bool {
	if e.config.FunctionAccessMode == AllowList {
		for pattern, isAllowed := range e.config.FunctionsList {
			if isAllowed && pattern.Matches(functionName) {
				return true
			}
		}
		return false
	} else { // DenyList
		for pattern, isDenied := range e.config.FunctionsList {
			if isDenied && pattern.Matches(functionName) {
				return false
			}
		}
		return true
	}
}

// Helper functions for validation

// isDangerousOperation checks if a statement is a dangerous operation (DROP, TRUNCATE, etc.)
func isDangerousOperation(stmt *pg_query.Node) bool {
	if stmt == nil {
		return false
	}

	switch stmt.GetNode().(type) {
	case *pg_query.Node_DropStmt:
		return true
	case *pg_query.Node_TruncateStmt:
		return true
	case *pg_query.Node_GrantStmt:
		return true
	case *pg_query.Node_CreateRoleStmt:
		return true
	case *pg_query.Node_AlterRoleStmt:
		return true
	case *pg_query.Node_AlterTableStmt:
		return true
	case *pg_query.Node_CreateStmt:
		return true
	case *pg_query.Node_IndexStmt:
		return true
	case *pg_query.Node_RenameStmt:
		return true
	case *pg_query.Node_RuleStmt:
		return true
	case *pg_query.Node_ViewStmt:
		return true
	case *pg_query.Node_VacuumStmt:
		return true
	case *pg_query.Node_ExplainStmt:
		return true
	case *pg_query.Node_ClusterStmt:
		return true
	case *pg_query.Node_CheckPointStmt:
		return true
	}

	return false
}

// isWriteOperation checks if a statement modifies data (INSERT, UPDATE, DELETE)
func isWriteOperation(stmt *pg_query.Node) bool {
	if stmt == nil {
		return false
	}

	switch stmt.GetNode().(type) {
	case *pg_query.Node_InsertStmt:
		return true
	case *pg_query.Node_UpdateStmt:
		return true
	case *pg_query.Node_DeleteStmt:
		return true
	case *pg_query.Node_CopyStmt:
		return true
	}

	return false
}

// isReadOperation checks if a statement is a read operation (SELECT)
func isReadOperation(stmt *pg_query.Node) bool {
	if stmt == nil {
		return false
	}

	switch stmt.GetNode().(type) {
	case *pg_query.Node_SelectStmt:
		selectStmt := stmt.GetNode().(*pg_query.Node_SelectStmt)
		// Ensure it's not a data-modifying CTE
		if selectStmt.SelectStmt.WithClause != nil {
			for _, cte := range selectStmt.SelectStmt.WithClause.Ctes {
				if cteSelect, ok := cte.GetNode().(*pg_query.Node_CommonTableExpr); ok {
					if cteSelect.CommonTableExpr.Ctequery != nil {
						if ctestmt, ok := cteSelect.CommonTableExpr.Ctequery.GetNode().(*pg_query.Node_SelectStmt); ok {
							if ctestmt.SelectStmt.Op != pg_query.SetOperation_SETOP_NONE {
								// Data-modifying CTEs like WITH x AS (INSERT...)
								return false
							}
						}
					}
				}
			}
		}
		return true
	}

	return false
}

// ValidateParameters checks if parameters in the query match the expected types
func (e *RuleEngine) ValidateParameters(parseResult *pg_query.ParseResult, params []interface{}) error {
	if parseResult == nil || len(parseResult.Stmts) == 0 {
		return nil
	}

	// If params is null, we'll allow any query as we can't validate
	if params == nil {
		return nil
	}

	// Find all parameter references in the query
	paramRefs := make(map[int]bool)
	maxParam := 0

	// Traverse the AST to find all parameter references
	for _, stmt := range parseResult.Stmts {
		if err := e.findParamReferences(stmt.Stmt, paramRefs, &maxParam); err != nil {
			return err
		}
	}

	// Check if max parameter index exceeds the number of provided parameters
	if maxParam != len(params) {
		return fmt.Errorf("%w: query references $%d but %d parameters provided",
			ErrParamTypeMismatch, maxParam, len(params))
	}

	// Check for gaps in parameter numbering (e.g., using $1 and $3 but not $2)
	for i := 1; i <= maxParam; i++ {
		if !paramRefs[i] {
			return fmt.Errorf("%w: parameter $%d is not used in query but parameters up to $%d are referenced",
				ErrParamTypeMismatch, i, maxParam)
		}
	}

	return nil
}

// findParamReferences recursively traverses the AST to find all parameter references
func (e *RuleEngine) findParamReferences(node *pg_query.Node, paramRefs map[int]bool, maxParam *int) error {
	if node == nil {
		return nil
	}

	// Check if this is a parameter reference (ParamRef node)
	if paramRef, ok := node.GetNode().(*pg_query.Node_ParamRef); ok {
		paramNum := int(paramRef.ParamRef.Number)
		paramRefs[paramNum] = true
		if paramNum > *maxParam {
			*maxParam = paramNum
		}
		return nil
	}

	// Recursively process different node types
	switch n := node.GetNode().(type) {
	case *pg_query.Node_SelectStmt:
		// Process SELECT statement parts
		if n.SelectStmt.TargetList != nil {
			for _, target := range n.SelectStmt.TargetList {
				if err := e.findParamReferences(target, paramRefs, maxParam); err != nil {
					return err
				}
			}
		}
		if n.SelectStmt.WhereClause != nil {
			if err := e.findParamReferences(n.SelectStmt.WhereClause, paramRefs, maxParam); err != nil {
				return err
			}
		}
		if n.SelectStmt.HavingClause != nil {
			if err := e.findParamReferences(n.SelectStmt.HavingClause, paramRefs, maxParam); err != nil {
				return err
			}
		}
		if n.SelectStmt.FromClause != nil {
			for _, from := range n.SelectStmt.FromClause {
				if err := e.findParamReferences(from, paramRefs, maxParam); err != nil {
					return err
				}
			}
		}
		if n.SelectStmt.GroupClause != nil {
			for _, group := range n.SelectStmt.GroupClause {
				if err := e.findParamReferences(group, paramRefs, maxParam); err != nil {
					return err
				}
			}
		}
		if n.SelectStmt.SortClause != nil {
			for _, sort := range n.SelectStmt.SortClause {
				if err := e.findParamReferences(sort, paramRefs, maxParam); err != nil {
					return err
				}
			}
		}
		if n.SelectStmt.LimitCount != nil {
			if err := e.findParamReferences(n.SelectStmt.LimitCount, paramRefs, maxParam); err != nil {
				return err
			}
		}
		if n.SelectStmt.LimitOffset != nil {
			if err := e.findParamReferences(n.SelectStmt.LimitOffset, paramRefs, maxParam); err != nil {
				return err
			}
		}
		// Process CTE (WITH clause)
		if n.SelectStmt.WithClause != nil {
			for _, cte := range n.SelectStmt.WithClause.Ctes {
				if err := e.findParamReferences(cte, paramRefs, maxParam); err != nil {
					return err
				}
			}
		}
		// Process VALUES lists if present
		if n.SelectStmt.ValuesLists != nil {
			for _, valuesList := range n.SelectStmt.ValuesLists {
				if list, ok := valuesList.GetNode().(*pg_query.Node_List); ok {
					for _, item := range list.List.Items {
						if err := e.findParamReferences(item, paramRefs, maxParam); err != nil {
							return err
						}
					}
				}
			}
		}

	case *pg_query.Node_AExpr:
		// Process expression parts
		if n.AExpr.Lexpr != nil {
			if err := e.findParamReferences(n.AExpr.Lexpr, paramRefs, maxParam); err != nil {
				return err
			}
		}
		if n.AExpr.Rexpr != nil {
			if err := e.findParamReferences(n.AExpr.Rexpr, paramRefs, maxParam); err != nil {
				return err
			}
		}
		// Process name list (for operators like IN)
		if n.AExpr.Name != nil {
			for _, name := range n.AExpr.Name {
				if err := e.findParamReferences(name, paramRefs, maxParam); err != nil {
					return err
				}
			}
		}

	case *pg_query.Node_BoolExpr:
		// Process boolean expression args
		for _, arg := range n.BoolExpr.Args {
			if err := e.findParamReferences(arg, paramRefs, maxParam); err != nil {
				return err
			}
		}

	case *pg_query.Node_ResTarget:
		// Process result target
		if n.ResTarget.Val != nil {
			if err := e.findParamReferences(n.ResTarget.Val, paramRefs, maxParam); err != nil {
				return err
			}
		}

	case *pg_query.Node_FuncCall:
		// Process function call args
		for _, arg := range n.FuncCall.Args {
			if err := e.findParamReferences(arg, paramRefs, maxParam); err != nil {
				return err
			}
		}
		// Process function call agg_order
		for _, order := range n.FuncCall.AggOrder {
			if err := e.findParamReferences(order, paramRefs, maxParam); err != nil {
				return err
			}
		}
		// Process function call agg_filter
		if n.FuncCall.AggFilter != nil {
			if err := e.findParamReferences(n.FuncCall.AggFilter, paramRefs, maxParam); err != nil {
				return err
			}
		}
		// Process window definition if present
		if n.FuncCall.Over != nil {
			// Xử lý PartitionClause
			for _, partition := range n.FuncCall.Over.PartitionClause {
				if err := e.findParamReferences(partition, paramRefs, maxParam); err != nil {
					return err
				}
			}
			// Xử lý OrderClause
			for _, order := range n.FuncCall.Over.OrderClause {
				if err := e.findParamReferences(order, paramRefs, maxParam); err != nil {
					return err
				}
			}
			// Xử lý StartOffset
			if n.FuncCall.Over.StartOffset != nil {
				if err := e.findParamReferences(n.FuncCall.Over.StartOffset, paramRefs, maxParam); err != nil {
					return err
				}
			}
			// Xử lý EndOffset
			if n.FuncCall.Over.EndOffset != nil {
				if err := e.findParamReferences(n.FuncCall.Over.EndOffset, paramRefs, maxParam); err != nil {
					return err
				}
			}
		}

	case *pg_query.Node_RangeVar:
		// Process range variable
		// Usually doesn't contain parameters, but included for completeness

	case *pg_query.Node_JoinExpr:
		// Process join expression
		if n.JoinExpr.Larg != nil {
			if err := e.findParamReferences(n.JoinExpr.Larg, paramRefs, maxParam); err != nil {
				return err
			}
		}
		if n.JoinExpr.Rarg != nil {
			if err := e.findParamReferences(n.JoinExpr.Rarg, paramRefs, maxParam); err != nil {
				return err
			}
		}
		if n.JoinExpr.Quals != nil {
			if err := e.findParamReferences(n.JoinExpr.Quals, paramRefs, maxParam); err != nil {
				return err
			}
		}

	case *pg_query.Node_SortBy:
		// Process sort by
		if n.SortBy.Node != nil {
			if err := e.findParamReferences(n.SortBy.Node, paramRefs, maxParam); err != nil {
				return err
			}
		}

	case *pg_query.Node_SubLink:
		// Process subquery
		if n.SubLink.Subselect != nil {
			if err := e.findParamReferences(n.SubLink.Subselect, paramRefs, maxParam); err != nil {
				return err
			}
		}
		// Process test expr
		if n.SubLink.Testexpr != nil {
			if err := e.findParamReferences(n.SubLink.Testexpr, paramRefs, maxParam); err != nil {
				return err
			}
		}

	case *pg_query.Node_CaseExpr:
		// Process CASE expression
		if n.CaseExpr.Arg != nil {
			if err := e.findParamReferences(n.CaseExpr.Arg, paramRefs, maxParam); err != nil {
				return err
			}
		}
		// Process WHEN clauses
		for _, when := range n.CaseExpr.Args {
			if err := e.findParamReferences(when, paramRefs, maxParam); err != nil {
				return err
			}
		}
		// Process default result (ELSE)
		if n.CaseExpr.Defresult != nil {
			if err := e.findParamReferences(n.CaseExpr.Defresult, paramRefs, maxParam); err != nil {
				return err
			}
		}

	case *pg_query.Node_CaseWhen:
		// Process WHEN condition
		if n.CaseWhen.Expr != nil {
			if err := e.findParamReferences(n.CaseWhen.Expr, paramRefs, maxParam); err != nil {
				return err
			}
		}
		// Process THEN result
		if n.CaseWhen.Result != nil {
			if err := e.findParamReferences(n.CaseWhen.Result, paramRefs, maxParam); err != nil {
				return err
			}
		}

	case *pg_query.Node_CommonTableExpr:
		// Process CTE
		if n.CommonTableExpr.Ctequery != nil {
			if err := e.findParamReferences(n.CommonTableExpr.Ctequery, paramRefs, maxParam); err != nil {
				return err
			}
		}

	case *pg_query.Node_WithClause:
		// Process WITH clause
		for _, cte := range n.WithClause.Ctes {
			if err := e.findParamReferences(cte, paramRefs, maxParam); err != nil {
				return err
			}
		}

	case *pg_query.Node_List:
		// Process list nodes (used in various places)
		for _, item := range n.List.Items {
			if err := e.findParamReferences(item, paramRefs, maxParam); err != nil {
				return err
			}
		}

	case *pg_query.Node_AArrayExpr:
		// Process ARRAY constructor
		for _, element := range n.AArrayExpr.Elements {
			if err := e.findParamReferences(element, paramRefs, maxParam); err != nil {
				return err
			}
		}

	case *pg_query.Node_WindowDef:
		// Process window definition
		if n.WindowDef.PartitionClause != nil {
			for _, partition := range n.WindowDef.PartitionClause {
				if err := e.findParamReferences(partition, paramRefs, maxParam); err != nil {
					return err
				}
			}
		}
		if n.WindowDef.OrderClause != nil {
			for _, order := range n.WindowDef.OrderClause {
				if err := e.findParamReferences(order, paramRefs, maxParam); err != nil {
					return err
				}
			}
		}
		if n.WindowDef.StartOffset != nil {
			if err := e.findParamReferences(n.WindowDef.StartOffset, paramRefs, maxParam); err != nil {
				return err
			}
		}
		if n.WindowDef.EndOffset != nil {
			if err := e.findParamReferences(n.WindowDef.EndOffset, paramRefs, maxParam); err != nil {
				return err
			}
		}

	case *pg_query.Node_TypeCast:
		// Process type cast
		if n.TypeCast.Arg != nil {
			if err := e.findParamReferences(n.TypeCast.Arg, paramRefs, maxParam); err != nil {
				return err
			}
		}

	case *pg_query.Node_ColumnRef:
		// Process column reference (usually doesn't contain params)

	/*case *pg_query.Node_AConst:
	// Process constant (usually doesn't contain params)
	// But string constants might contain parameter references in function calls
	if strVal, ok := n.AConst.Val.(*pg_query.Node_Sval); ok {
		// Check if this is a string that might contain parameter references
		// This is a heuristic approach for function calls with parameters in strings
		// like INTERVAL '$1 days'
		str := strVal.Sval.Sval
		if len(str) > 0 && str[0] == '$' {
			// Try to parse the parameter number
			paramStr := ""
			for i := 1; i < len(str); i++ {
				if str[i] >= '0' && str[i] <= '9' {
					paramStr += string(str[i])
				} else {
					break
				}
			}
			if len(paramStr) > 0 {
				if paramNum, err := strconv.Atoi(paramStr); err == nil {
					paramRefs[paramNum] = true
					if paramNum > *maxParam {
						*maxParam = paramNum
					}
				}
			}
		}
	}*/

	case *pg_query.Node_NullTest:
		// Process IS NULL / IS NOT NULL
		if n.NullTest.Arg != nil {
			if err := e.findParamReferences(n.NullTest.Arg, paramRefs, maxParam); err != nil {
				return err
			}
		}

	case *pg_query.Node_BooleanTest:
		// Process IS TRUE / IS FALSE / etc.
		if n.BooleanTest.Arg != nil {
			if err := e.findParamReferences(n.BooleanTest.Arg, paramRefs, maxParam); err != nil {
				return err
			}
		}

	case *pg_query.Node_CoalesceExpr:
		// Process COALESCE expression
		for _, arg := range n.CoalesceExpr.Args {
			if err := e.findParamReferences(arg, paramRefs, maxParam); err != nil {
				return err
			}
		}

	case *pg_query.Node_MinMaxExpr:
		// Process GREATEST/LEAST expressions
		for _, arg := range n.MinMaxExpr.Args {
			if err := e.findParamReferences(arg, paramRefs, maxParam); err != nil {
				return err
			}
		}

	case *pg_query.Node_XmlExpr:
		// Process XML expressions
		for _, arg := range n.XmlExpr.Args {
			if err := e.findParamReferences(arg, paramRefs, maxParam); err != nil {
				return err
			}
		}

	case *pg_query.Node_JsonExpr:
		// Process JSON expressions
		if n.JsonExpr.FormattedExpr != nil {
			if err := e.findParamReferences(n.JsonExpr.FormattedExpr, paramRefs, maxParam); err != nil {
				return err
			}
		}
	}

	return nil
}

// Helper method to add allowed tables
func (c *AuditConfig) AddAllowedTable(tablePattern string) {
	c.TablesList[NewPattern(tablePattern)] = true
}

// Helper method to add denied tables
func (c *AuditConfig) AddDeniedTable(tablePattern string) {
	c.TablesList[NewPattern(tablePattern)] = true
}

// Helper method to add allowed columns for a table
func (c *AuditConfig) AddAllowedColumn(tablePattern, columnPattern string) {
	tablePatternObj := NewPattern(tablePattern)
	if _, exists := c.ColumnsList[tablePatternObj]; !exists {
		c.ColumnsList[tablePatternObj] = make(map[Pattern]bool)
	}
	c.ColumnsList[tablePatternObj][NewPattern(columnPattern)] = true
}

// Helper method to add allowed JSONB field
func (c *AuditConfig) AddAllowedJSONBField(tablePattern, columnPattern string) {
	// Thêm vào AllowedJSONBFields
	tablePatternObj := NewPattern(tablePattern)
	if _, exists := c.AllowedJSONBFields[tablePatternObj]; !exists {
		c.AllowedJSONBFields[tablePatternObj] = make(map[Pattern]bool)
	}
	c.AllowedJSONBFields[tablePatternObj][NewPattern(columnPattern)] = true

	// Tự động thêm column vào danh sách allowed columns
	// Lấy phần đầu của columnPattern (trước dấu chấm đầu tiên nếu có)
	parts := strings.SplitN(columnPattern, ".", 2)
	baseColumn := parts[0]
	c.AddAllowedColumn(tablePattern, baseColumn)
}

// Helper method to add denied columns for a table
func (c *AuditConfig) AddDeniedColumn(tablePattern, columnPattern string) {
	tablePatternObj := NewPattern(tablePattern)
	if _, exists := c.ColumnsList[tablePatternObj]; !exists {
		c.ColumnsList[tablePatternObj] = make(map[Pattern]bool)
	}
	c.ColumnsList[tablePatternObj][NewPattern(columnPattern)] = true
}

// Helper method to add allowed function
func (c *AuditConfig) AddAllowedFunction(functionPattern string) {
	c.FunctionsList[NewPattern(functionPattern)] = true
}

// Helper method to add denied function
func (c *AuditConfig) AddDeniedFunction(functionPattern string) {
	c.FunctionsList[NewPattern(functionPattern)] = true
}

// validateJSONBAccess kiểm tra các truy cập JSONB trong câu lệnh SQL
func (e *RuleEngine) validateJSONBAccess(stmt *pg_query.Node) error {
	if stmt == nil {
		return nil
	}

	// Kiểm tra nếu là SELECT statement
	selectStmt, ok := stmt.GetNode().(*pg_query.Node_SelectStmt)
	if !ok {
		return nil
	}

	// Xây dựng map alias -> table name từ mệnh đề FROM
	aliasMap := make(map[string]string)
	if selectStmt.SelectStmt.FromClause != nil {
		for _, fromItem := range selectStmt.SelectStmt.FromClause {
			e.buildAliasMap(fromItem, aliasMap)
		}
	}

	// Kiểm tra mệnh đề WHERE cho các toán tử JSONB
	whereClause := selectStmt.SelectStmt.WhereClause
	if whereClause == nil {
		return nil
	}

	return e.validateJSONBExpr(whereClause, aliasMap)
}

// buildAliasMap xây dựng mapping từ alias đến tên bảng thật
func (e *RuleEngine) buildAliasMap(fromItem *pg_query.Node, aliasMap map[string]string) {
	if fromItem == nil {
		return
	}

	switch node := fromItem.GetNode().(type) {
	case *pg_query.Node_RangeVar:
		// Xử lý bảng đơn
		tableName := node.RangeVar.Relname
		if node.RangeVar.Alias != nil && node.RangeVar.Alias.Aliasname != "" {
			// Nếu có alias, thêm vào map
			aliasMap[node.RangeVar.Alias.Aliasname] = tableName
		}
		// Luôn thêm tên bảng như một key để xử lý trường hợp không có alias
		aliasMap[tableName] = tableName

	case *pg_query.Node_JoinExpr:
		// Xử lý JOIN expressions
		if node.JoinExpr.Larg != nil {
			e.buildAliasMap(node.JoinExpr.Larg, aliasMap)
		}
		if node.JoinExpr.Rarg != nil {
			e.buildAliasMap(node.JoinExpr.Rarg, aliasMap)
		}

	case *pg_query.Node_RangeSubselect:
		// Xử lý subquery trong FROM
		if node.RangeSubselect.Alias != nil && node.RangeSubselect.Alias.Aliasname != "" {
			// Subquery có alias riêng, không map đến bảng cụ thể
			// Có thể xử lý sâu hơn nếu cần
		}
	}
}

// validateJSONBExpr kiểm tra biểu thức có chứa toán tử JSONB
func (e *RuleEngine) validateJSONBExpr(expr *pg_query.Node, aliasMap map[string]string) error {
	if expr == nil {
		return nil
	}

	// Kiểm tra A_Expr có thể chứa toán tử JSONB như -> hoặc ->>
	if aExpr, ok := expr.GetNode().(*pg_query.Node_AExpr); ok {
		// Kiểm tra tên toán tử cho các toán tử JSONB
		for _, name := range aExpr.AExpr.Name {
			if nameStr, ok := name.GetNode().(*pg_query.Node_String_); ok {
				if nameStr.String_ != nil && (strings.Contains(nameStr.String_.Sval, "->") || strings.Contains(nameStr.String_.Sval, "->>")) {
					// Đây là toán tử JSONB
					// Kiểm tra nếu toán hạng bên trái được phép truy cập JSONB
					if err := e.validateJSONBField(aExpr.AExpr.Lexpr, aliasMap); err != nil {
						return err
					}
				}
			}
		}

		// Kiểm tra đệ quy cả biểu thức bên trái và phải
		if aExpr.AExpr.Lexpr != nil {
			if err := e.validateJSONBExpr(aExpr.AExpr.Lexpr, aliasMap); err != nil {
				return err
			}
		}
		if aExpr.AExpr.Rexpr != nil {
			if err := e.validateJSONBExpr(aExpr.AExpr.Rexpr, aliasMap); err != nil {
				return err
			}
		}
	}

	// Kiểm tra đệ quy cho biểu thức phức tạp
	if boolExpr, ok := expr.GetNode().(*pg_query.Node_BoolExpr); ok {
		for _, arg := range boolExpr.BoolExpr.Args {
			if err := e.validateJSONBExpr(arg, aliasMap); err != nil {
				return err
			}
		}
	}

	return nil
}

// validateJSONBField kiểm tra xem truy cập JSONB field có được phép không
func (e *RuleEngine) validateJSONBField(expr *pg_query.Node, aliasMap map[string]string) error {
	if expr == nil {
		return nil
	}

	// Trích xuất thông tin cột và bảng từ truy cập JSONB
	column := ""
	tableOrAlias := ""

	// Hầu hết truy cập JSONB sẽ thông qua ColumnRef
	if colRef, ok := expr.GetNode().(*pg_query.Node_ColumnRef); ok {
		fields := colRef.ColumnRef.Fields
		if len(fields) == 1 {
			// Chỉ có tên cột, không có qualifier bảng
			if namefield, ok := fields[0].GetNode().(*pg_query.Node_String_); ok && namefield.String_ != nil {
				column = namefield.String_.Sval
			}
		} else if len(fields) >= 2 {
			// Có qualifier bảng (có thể là tên bảng thật hoặc alias)
			if tablefield, ok := fields[0].GetNode().(*pg_query.Node_String_); ok && tablefield.String_ != nil {
				tableOrAlias = tablefield.String_.Sval
			}
			if colfield, ok := fields[1].GetNode().(*pg_query.Node_String_); ok && colfield.String_ != nil {
				column = colfield.String_.Sval
			}
		}
	} else if aExpr, ok := expr.GetNode().(*pg_query.Node_AExpr); ok {
		// Xử lý biểu thức JSONB lồng nhau như (data->'field')->>'subfield'
		return e.validateJSONBField(aExpr.AExpr.Lexpr, aliasMap)
	}

	// Nếu không trích xuất được tên cột, cho phép (có thể không đủ thông tin)
	if column == "" {
		return nil
	}

	// Xác định tên bảng thật từ alias nếu có
	tableName := tableOrAlias
	if realTable, exists := aliasMap[tableOrAlias]; exists && tableOrAlias != "" {
		tableName = realTable
	}

	// Kiểm tra xem cột này có được phép truy cập JSONB không
	if tableName != "" {
		// Kiểm tra cụ thể table.column
		for tablePattern, fields := range e.config.AllowedJSONBFields {
			if tablePattern.Matches(tableName) {
				for columnPattern, isAllowed := range fields {
					if isAllowed && columnPattern.Matches(column) {
						return nil
					}
				}
			}
		}
	} else {
		// Kiểm tra tất cả bảng cho cột này
		for _, fields := range e.config.AllowedJSONBFields {
			for columnPattern, isAllowed := range fields {
				if isAllowed && columnPattern.Matches(column) {
					return nil
				}
			}
		}
	}

	// Nếu không tìm thấy, kiểm tra xem cột này có phải là column thông thường được phép không
	// Điều này giúp giải quyết vấn đề khi một cột được cho phép qua ColumnsList nhưng không có trong AllowedJSONBFields
	if tableName != "" && e.isColumnAllowed(tableName, column) {
		return nil
	}

	return fmt.Errorf("%w: field '%s' is not allowed for JSONB access", ErrJSONBFieldNotAllowed, column)
}
