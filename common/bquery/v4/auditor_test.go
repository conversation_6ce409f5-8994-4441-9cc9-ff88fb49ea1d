package v4

import (
	"errors"
	"fmt"
	"testing"

	"github.com/pganalyze/pg_query_go/v6"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// Mock SQLParser for testing
type MockSQLParser struct {
	TableNames       []string
	ColumnReferences []ColumnReference
	FunctionCalls    []string
	CTEDepth         int
	TableNamesErr    error
	ColumnRefsErr    error
	FunctionCallsErr error
	CTEDepthErr      error
	Aliases          map[string]string // Thêm trường này
}

func (m *MockSQLParser) GetTableAliases(result *pg_query.ParseResult) (map[string]string, error) {
	if m.Aliases != nil {
		return m.Aliases, nil
	}
	return make(map[string]string), nil
}

func (m *MockSQLParser) Parse(rawSQL string) (*pg_query.ParseResult, error) {
	//TODO implement me
	panic("implement me")
}

func (m *MockSQLParser) Normalize(rawSQL string) (string, error) {
	//TODO implement me
	panic("implement me")
}

func (m *MockSQLParser) GetTableNames(result *pg_query.ParseResult) ([]string, error) {
	return m.TableNames, m.TableNamesErr
}
func (m *MockSQLParser) GetColumnReferences(result *pg_query.ParseResult) ([]ColumnReference, error) {
	return m.ColumnReferences, m.ColumnRefsErr
}

func (m *MockSQLParser) GetFunctionCalls(result *pg_query.ParseResult) ([]string, error) {
	return m.FunctionCalls, m.FunctionCallsErr
}

func (m *MockSQLParser) GetCTEDepth(result *pg_query.ParseResult) (int, error) {
	return m.CTEDepth, m.CTEDepthErr
}

func TestPattern_Matches(t *testing.T) {
	tests := []struct {
		pattern string
		input   string
		matches bool
	}{
		{"*", "anything", true},
		{"public.*", "public.users", true},
		{"public.*", "private.users", false},
		{"*.users", "public.users", true},
		{"*.users", "public.products", false},
		{"public.user*", "public.users", true},
		{"public.user*", "public.user_details", true},
		{"public.user*", "public.products", false},
		{"exact_match", "exact_match", true},
		{"exact_match", "not_exact_match", false},
	}

	for _, tt := range tests {
		t.Run(fmt.Sprintf("pattern:%s, input:%s", tt.pattern, tt.input), func(t *testing.T) {
			p := NewPattern(tt.pattern)
			assert.Equal(t, tt.matches, p.Matches(tt.input))
		})
	}
}

func TestRuleEngine_Validate(t *testing.T) {
	// Setup
	defaultConfig := DefaultAuditConfig()

	tests := []struct {
		name        string
		config      *AuditConfig
		query       string
		expectedErr error
		mockParser  *MockSQLParser
	}{
		{
			name:        "Empty query",
			config:      defaultConfig,
			query:       "",
			expectedErr: ErrEmptyQuery,
		},
		{
			name:        "Dangerous operation",
			config:      defaultConfig,
			query:       "DROP TABLE users;",
			expectedErr: ErrDangerousOperation,
		},
		{
			name:        "Write operation not allowed",
			config:      defaultConfig,
			query:       "INSERT INTO users (name) VALUES ('test');",
			expectedErr: ErrWriteOperation, // Expect the write operation error
		},
		{
			name: "Write operation allowed",
			config: &AuditConfig{
				TableAccessMode:      AllowList,
				TablesList:           map[Pattern]bool{NewPattern("users"): true},
				ColumnAccessMode:     AllowList,
				ColumnsList:          map[Pattern]map[Pattern]bool{},
				AllowedJSONBFields:   map[Pattern]map[Pattern]bool{},
				FunctionAccessMode:   AllowList,
				FunctionsList:        map[Pattern]bool{},
				AllowedCTEDepth:      3,
				AllowWriteOperations: true,
				AllowReadOperations:  true,
			},
			query:       "INSERT INTO users (name) VALUES ('test');",
			expectedErr: nil,
		},
		{
			name:        "Table not allowed",
			config:      defaultConfig,
			query:       "SELECT * FROM forbidden_table;",
			expectedErr: ErrTableNotAllowed,
			mockParser: &MockSQLParser{
				TableNames: []string{"forbidden_table"},
			},
		},
		{
			name:   "Table allowed",
			config: defaultConfig,
			query:  "SELECT * FROM allowed_table;",
			mockParser: &MockSQLParser{
				TableNames: []string{"allowed_table"},
			},
			expectedErr: nil,
		},
		{
			name:   "Column not allowed",
			config: defaultConfig,
			query:  "SELECT forbidden_column FROM allowed_table;",
			mockParser: &MockSQLParser{
				TableNames: []string{"allowed_table"},
				ColumnReferences: []ColumnReference{
					{Table: "allowed_table", Column: "forbidden_column"},
				},
			},
			expectedErr: ErrColumnNotAllowed,
		},
		{
			name:   "Column allowed",
			config: defaultConfig,
			query:  "SELECT allowed_column FROM allowed_table;",
			mockParser: &MockSQLParser{
				TableNames: []string{"allowed_table"},
				ColumnReferences: []ColumnReference{
					{Table: "allowed_table", Column: "allowed_column"},
				},
			},
			expectedErr: nil,
		},
		{
			name: "Function not allowed",
			config: &AuditConfig{
				TableAccessMode:      AllowList,
				TablesList:           map[Pattern]bool{NewPattern("*"): true},
				ColumnAccessMode:     AllowList,
				ColumnsList:          map[Pattern]map[Pattern]bool{NewPattern("*"): {NewPattern("*"): true}},
				AllowedJSONBFields:   map[Pattern]map[Pattern]bool{},
				FunctionAccessMode:   DenyList, // Use DenyList
				FunctionsList:        map[Pattern]bool{NewPattern("forbidden_function"): true},
				AllowedCTEDepth:      3,
				AllowWriteOperations: false,
				AllowReadOperations:  true,
			},
			query: "SELECT forbidden_function();",
			mockParser: &MockSQLParser{
				TableNames:       []string{},
				ColumnReferences: []ColumnReference{},
				FunctionCalls:    []string{"forbidden_function"},
			},
			expectedErr: ErrFunctionNotAllowed, // Expect function to be denied

		},

		{
			name:        "CTE depth exceeded",
			config:      defaultConfig,
			query:       "WITH RECURSIVE t AS (SELECT 1 UNION ALL SELECT 1 FROM t) SELECT * FROM t;",
			expectedErr: ErrCTEDepthExceeded,
			mockParser: &MockSQLParser{
				CTEDepth: 4, // Mock CTE depth
			},
		},
		{
			name: "JSONB field not allowed",
			config: func() *AuditConfig {
				c := DefaultAuditConfig()
				c.AddAllowedTable("users") // Allow the 'users' table
				return c
			}(),
			query:       "SELECT * FROM users WHERE data->>'forbidden_field' = 'value';",
			expectedErr: ErrColumnNotAllowed,
		},
		{
			name: "JSONB field allowed",
			config: func() *AuditConfig {
				c := DefaultAuditConfig()
				c.AddAllowedTable("users")              // Allow the 'users' table
				c.AddAllowedJSONBField("users", "data") // Already present, kept for clarity
				return c
			}(),
			query:       "SELECT * FROM users WHERE data->>'allowed_field' = 'value';",
			expectedErr: nil, // No error since 'data' is allowed
		},
		{
			name: "Read operation not allowed",
			config: &AuditConfig{
				TableAccessMode:      AllowList,
				TablesList:           map[Pattern]bool{},
				ColumnAccessMode:     AllowList,
				ColumnsList:          map[Pattern]map[Pattern]bool{},
				AllowedJSONBFields:   map[Pattern]map[Pattern]bool{},
				FunctionAccessMode:   AllowList,
				FunctionsList:        map[Pattern]bool{},
				AllowedCTEDepth:      3,
				AllowWriteOperations: false,
				AllowReadOperations:  false, // read not allowed
			},
			query:       "SELECT * FROM users;",
			expectedErr: errors.New("read operations not allowed"),
		},
		{
			name: "SELECT with data modifying CTE not allowed",
			config: &AuditConfig{
				AllowReadOperations:  true,
				AllowWriteOperations: false, // Disallow write ops
			},
			query:       "WITH t as (INSERT INTO users (name) VALUES ('test')) SELECT * FROM t", // Data-modifying CTE
			expectedErr: errors.New(""),                                                         //parse error expected,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Update config with table/column/function lists for specific cases
			if tt.name == "Table allowed" {
				tt.config.AddAllowedTable("allowed_table")
			}

			if tt.name == "Column allowed" {
				tt.config.AddAllowedTable("allowed_table")
				tt.config.AddAllowedColumn("allowed_table", "allowed_column")
			}

			if tt.name == "Function allowed" {
				tt.config.AddAllowedFunction("allowed_function")
			}

			engine := NewRuleEngine(tt.config)

			if tt.mockParser != nil {
				engine.parser = tt.mockParser
			}

			parsedQuery, _ := pg_query.Parse(tt.query) // Ignore parse errors here, covered by separate test

			err := engine.Validate(parsedQuery)

			if tt.expectedErr != nil {
				// Special case for read operation not allowed
				if tt.expectedErr.Error() == "read operations not allowed" {
					assert.EqualError(t, err, tt.expectedErr.Error())
					return
				}
				// Special case for data-modifying CTE in SELECT, expect parse err
				if tt.name == "SELECT with data modifying CTE not allowed" {
					assert.Error(t, err)
					return
				}
				assert.ErrorContains(t, err, tt.expectedErr.Error())
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestRuleEngine_ValidateParameters(t *testing.T) {
	tests := []struct {
		name          string
		query         string
		params        []interface{}
		expectedError error
	}{
		{
			name:          "No parameters",
			query:         "SELECT * FROM users",
			params:        []interface{}{},
			expectedError: nil,
		},
		{
			name:          "One parameter",
			query:         "SELECT * FROM users WHERE id = $1",
			params:        []interface{}{1},
			expectedError: nil,
		},
		{
			name:          "Multiple parameters",
			query:         "SELECT * FROM users WHERE id = $1 AND name = $2",
			params:        []interface{}{1, "test"},
			expectedError: nil,
		},
		{
			name:          "Parameter index exceeds provided parameters",
			query:         "SELECT * FROM users WHERE id = $2",
			params:        []interface{}{1},
			expectedError: ErrParamTypeMismatch,
		},
		{
			name:          "Gaps in parameter numbering",
			query:         "SELECT * FROM users WHERE id = $1 AND name = $3",
			params:        []interface{}{1, "test", "test2"},
			expectedError: ErrParamTypeMismatch,
		},
		{
			name:          "Null parameters",
			query:         "SELECT * FROM users WHERE id = $1",
			params:        nil,
			expectedError: nil,
		},
		{
			name:          "Parameter used in multiple places",
			query:         "SELECT * FROM users WHERE id = $1 or id = $1",
			params:        []interface{}{1},
			expectedError: nil,
		},
		{
			name:          "Parameters in different clauses",
			query:         "SELECT * FROM users WHERE id = $1 AND name LIKE $2 LIMIT $3",
			params:        []interface{}{1, "%test%", 10},
			expectedError: nil,
		},
		{
			name:          "Parameters in function calls",
			query:         "SELECT * FROM users WHERE created_at > NOW() - INTERVAL '$1 days'",
			params:        []interface{}{7},
			expectedError: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			engine := NewRuleEngine(DefaultAuditConfig())
			parseResult, err := pg_query.Parse(tt.query)
			require.NoError(t, err)

			err = engine.ValidateParameters(parseResult, tt.params)

			if tt.expectedError != nil {
				assert.ErrorContains(t, err, tt.expectedError.Error())
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestRuleEngine_validateJSONBAccess(t *testing.T) {
	config := DefaultAuditConfig()
	config.AddAllowedJSONBField("users", "data") // Allow JSONB access on users.data

	tests := []struct {
		name        string
		query       string
		config      *AuditConfig
		expectedErr error
	}{
		{
			name:        "No JSONB access",
			query:       "SELECT * FROM users WHERE id = 1",
			config:      config,
			expectedErr: nil,
		},
		{
			name:        "Allowed JSONB field",
			query:       "SELECT * FROM users WHERE data->>'name' = 'John'",
			config:      config,
			expectedErr: nil, // Allowed because 'data' is allowed on 'users'
		},
		{
			name:        "Disallowed JSONB field",
			query:       "SELECT * FROM users WHERE other_data->>'name' = 'John'",
			config:      config,
			expectedErr: ErrJSONBFieldNotAllowed,
		},
		{
			name:        "Nested JSONB access",
			query:       "SELECT * FROM users WHERE (data->'address')->>'city' = 'New York'",
			config:      config,
			expectedErr: nil, // Allowed because outer 'data' is allowed. Inner field is not validated
		},
		{
			name:        "JSONB access with table alias",
			query:       "SELECT * FROM users u WHERE u.data->>'name' = 'John'",
			config:      config,
			expectedErr: nil,
		},
		{
			name:        "JSONB access without table qualifier",
			query:       "SELECT * FROM users WHERE data->>'name' = 'John'",
			config:      config,
			expectedErr: nil,
		},
		{
			name:        "Multiple JSONB operators in WHERE clause",
			query:       `SELECT * FROM users WHERE data->>'name' = 'John' AND data->>'age' > '30'`,
			config:      config,
			expectedErr: nil, // Allowed because both use "data" field
		},
		{
			name:        "JSONB access in complex expression",
			query:       `SELECT * FROM users WHERE data->>'age' > '30' AND (data->'address'->>'city' = 'New York' OR data->>'country' = 'USA')`,
			config:      config,
			expectedErr: nil, // Allowed because all top level are 'data' field
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			engine := NewRuleEngine(tt.config)
			stmt, err := pg_query.Parse(tt.query)
			require.NoError(t, err)

			err = engine.validateJSONBAccess(stmt.Stmts[0].Stmt)

			if tt.expectedErr != nil {
				assert.ErrorContains(t, err, tt.expectedErr.Error())
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestRuleEngine_FindParamReferences(t *testing.T) {
	tests := []struct {
		name          string
		query         string
		expectedRefs  map[int]bool
		expectedMax   int
		expectedError bool
	}{
		{
			name:         "Simple query with one parameter",
			query:        "SELECT * FROM users WHERE id = $1",
			expectedRefs: map[int]bool{1: true},
			expectedMax:  1,
		},
		{
			name:         "Query with multiple parameters",
			query:        "SELECT * FROM users WHERE id = $1 AND name = $2",
			expectedRefs: map[int]bool{1: true, 2: true},
			expectedMax:  2,
		},
		{
			name:         "Query with unordered parameters",
			query:        "SELECT * FROM users WHERE name = $2 AND id = $1",
			expectedRefs: map[int]bool{1: true, 2: true},
			expectedMax:  2,
		},
		{
			name:         "Query with repeated parameters",
			query:        "SELECT * FROM users WHERE id = $1 OR id = $1",
			expectedRefs: map[int]bool{1: true},
			expectedMax:  1,
		},
		{
			name:         "Query with IN clause",
			query:        "SELECT * FROM users WHERE id IN ($1, $2, $3)",
			expectedRefs: map[int]bool{1: true, 2: true, 3: true},
			expectedMax:  3,
		},
		{
			name:         "Query with CASE WHEN",
			query:        "SELECT CASE WHEN id > $1 THEN $2 ELSE $3 END FROM users",
			expectedRefs: map[int]bool{1: true, 2: true, 3: true},
			expectedMax:  3,
		},
		{
			name:         "Query with CTE",
			query:        "WITH temp AS (SELECT * FROM users WHERE id = $1) SELECT * FROM temp WHERE name = $2",
			expectedRefs: map[int]bool{1: true, 2: true},
			expectedMax:  2,
		},
		/*{
			name:         "Query with recursive CTE",
			query:        "WITH RECURSIVE temp(id) AS (SELECT $1 UNION ALL SELECT id+$2 FROM temp WHERE id < $3) SELECT * FROM temp",
			expectedRefs: map[int]bool{1: true, 2: true, 3: true},
			expectedMax:  3,
		},*/
		{
			name:         "Query with subquery",
			query:        "SELECT * FROM users WHERE id IN (SELECT user_id FROM orders WHERE amount > $1) AND status = $2",
			expectedRefs: map[int]bool{1: true, 2: true},
			expectedMax:  2,
		},
		{
			name:         "Query with JOIN",
			query:        "SELECT * FROM users u JOIN orders o ON u.id = o.user_id AND o.amount > $1 WHERE u.status = $2",
			expectedRefs: map[int]bool{1: true, 2: true},
			expectedMax:  2,
		},
		{
			name:         "Query with parameters in GROUP BY, ORDER BY, LIMIT, OFFSET",
			query:        "SELECT * FROM users GROUP BY created_at HAVING COUNT(*) > $1 ORDER BY id LIMIT $2 OFFSET $3",
			expectedRefs: map[int]bool{1: true, 2: true, 3: true},
			expectedMax:  3,
		},
		/*	{
			name:         "Query with function calls containing parameters",
			query:        "SELECT * FROM users WHERE created_at > NOW() - INTERVAL '$1 days' AND updated_at > DATE_TRUNC('day', NOW() - INTERVAL '$2 hours')",
			expectedRefs: map[int]bool{1: true, 2: true},
			expectedMax:  2,
		},*/
		{
			name: "Complex query with multiple features",
			query: `WITH active_users AS (
                            SELECT * FROM users 
                            WHERE status = $1 AND created_at > $2
                          )
                          SELECT u.id, COUNT(o.id) as order_count
                          FROM active_users u
                          LEFT JOIN orders o ON u.id = o.user_id AND o.amount BETWEEN $3 AND $4
                          WHERE u.type IN ($5, $6, $7)
                          GROUP BY u.id
                          HAVING COUNT(o.id) > $8
                          ORDER BY CASE WHEN $9 = 'asc' THEN order_count END ASC,
                                   CASE WHEN $9 = 'desc' THEN order_count END DESC
                          LIMIT $10 OFFSET $11`,
			expectedRefs: map[int]bool{1: true, 2: true, 3: true, 4: true, 5: true, 6: true, 7: true, 8: true, 9: true, 10: true, 11: true},
			expectedMax:  11,
		},
		{
			name:         "Query with JSON operations",
			query:        "SELECT * FROM users WHERE data->$1->$2 = $3",
			expectedRefs: map[int]bool{1: true, 2: true, 3: true},
			expectedMax:  3,
		},
		{
			name:         "Query with ARRAY constructor",
			query:        "SELECT * FROM users WHERE tags = ARRAY[$1, $2, $3]",
			expectedRefs: map[int]bool{1: true, 2: true, 3: true},
			expectedMax:  3,
		},
		{
			name:         "Query with window functions",
			query:        "SELECT *, ROW_NUMBER() OVER (PARTITION BY dept ORDER BY salary DESC) FROM employees WHERE dept = $1 AND salary > $2",
			expectedRefs: map[int]bool{1: true, 2: true},
			expectedMax:  2,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			engine := NewRuleEngine(DefaultAuditConfig())

			// Parse the query
			parseResult, err := pg_query.Parse(tt.query)
			require.NoError(t, err, "Failed to parse query")

			// Initialize param references map and max param
			paramRefs := make(map[int]bool)
			maxParam := 0

			// Call findParamReferences for each statement
			for _, stmt := range parseResult.Stmts {
				err = engine.findParamReferences(stmt.Stmt, paramRefs, &maxParam)
				if tt.expectedError {
					assert.Error(t, err)
					return
				}
				require.NoError(t, err)
			}

			// Verify the results
			assert.Equal(t, tt.expectedRefs, paramRefs, "Parameter references don't match")
			assert.Equal(t, tt.expectedMax, maxParam, "Max parameter doesn't match")
		})
	}
}
