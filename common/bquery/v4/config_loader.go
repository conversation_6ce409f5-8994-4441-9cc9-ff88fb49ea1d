package v4

import (
	"fmt"
	"os"
	"strings"

	"gopkg.in/yaml.v3"
)

// YAMLConfig là cấu trúc YAML để người dùng dễ dàng cấu hình
type YAMLConfig struct {
	// C<PERSON>u hình chung
	AllowWriteOperations bool `yaml:"allowWriteOperations"`
	AllowReadOperations  bool `yaml:"allowReadOperations"`
	AllowedCTEDepth      int  `yaml:"allowedCTEDepth"`

	// Cấu hình bảng
	TableAccess string   `yaml:"tableAccess"` // "deny" hoặc "allow"
	Tables      []string `yaml:"tables"`      // Danh sách mẫu tên bảng

	// Cấu hình cột
	ColumnAccess string              `yaml:"columnAccess"` // "deny" hoặc "allow"
	Columns      map[string][]string `yaml:"columns"`      // map[tablePattern][]columnPattern

	// C<PERSON>u hình truy cập JSONB
	JSONBFields map[string][]string `yaml:"jsonbFields"` // map[tablePattern][]columnPattern

	// Cấu hình hàm
	FunctionAccess string   `yaml:"functionAccess"` // "deny" hoặc "allow"
	Functions      []string `yaml:"functions"`      // Danh sách mẫu tên hàm

	MaxRowLimit  int  `yaml:"maxRowLimit"`
	EnforceLimit bool `yaml:"enforceLimit"`
	DefaultLimit int  `yaml:"defaultLimit"`
}

// LoadConfigFromYAML đọc cấu hình từ file YAML và chuyển đổi thành AuditConfig
func LoadConfigFromYAML(filePath string) (*AuditConfig, error) {
	// Đọc file YAML
	yamlData, err := os.ReadFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("không thể đọc file cấu hình: %w", err)
	}

	// Parse YAML vào cấu trúc tạm
	var yamlConfig YAMLConfig
	if err := yaml.Unmarshal(yamlData, &yamlConfig); err != nil {
		return nil, fmt.Errorf("không thể parse YAML: %w", err)
	}

	// Tạo cấu hình mặc định
	config := DefaultAuditConfig()

	// Cấu hình chung
	config.AllowWriteOperations = yamlConfig.AllowWriteOperations
	config.AllowReadOperations = yamlConfig.AllowReadOperations

	// Đặt giá trị mặc định cho AllowedCTEDepth nếu không được cấu hình
	if yamlConfig.AllowedCTEDepth > 0 {
		config.AllowedCTEDepth = yamlConfig.AllowedCTEDepth
	}

	// Cấu hình truy cập bảng
	if strings.ToLower(yamlConfig.TableAccess) == "deny" {
		config.TableAccessMode = DenyList
	} else {
		config.TableAccessMode = AllowList
	}

	// Thêm các mẫu bảng
	for _, tablePattern := range yamlConfig.Tables {
		pattern := NewPattern(tablePattern)
		config.TablesList[pattern] = true
	}

	// Cấu hình truy cập cột
	if strings.ToLower(yamlConfig.ColumnAccess) == "deny" {
		config.ColumnAccessMode = DenyList
	} else {
		config.ColumnAccessMode = AllowList
	}

	// Thêm các mẫu cột
	for tablePattern, columnPatterns := range yamlConfig.Columns {
		for _, columnPattern := range columnPatterns {
			if config.ColumnAccessMode == AllowList {
				config.AddAllowedColumn(tablePattern, columnPattern)
			} else {
				config.AddDeniedColumn(tablePattern, columnPattern)
			}
		}
	}

	// Cấu hình truy cập JSONB
	for tablePattern, fieldPatterns := range yamlConfig.JSONBFields {
		for _, fieldPattern := range fieldPatterns {
			// Nếu cả bảng và field đều là wildcard "*",
			// chúng ta cần thêm tất cả các bảng và cột vào danh sách
			if tablePattern == "*" && fieldPattern == "*" {
				// Đặc biệt xử lý trường hợp tất cả field JSONB được cho phép
				wildcardPattern := NewPattern("*")
				if _, exists := config.AllowedJSONBFields[wildcardPattern]; !exists {
					config.AllowedJSONBFields[wildcardPattern] = make(map[Pattern]bool)
				}
				config.AllowedJSONBFields[wildcardPattern][wildcardPattern] = true
			} else {
				config.AddAllowedJSONBField(tablePattern, fieldPattern)
			}
		}
	}

	// Cấu hình truy cập hàm
	if strings.ToLower(yamlConfig.FunctionAccess) == "deny" {
		config.FunctionAccessMode = DenyList
	} else {
		config.FunctionAccessMode = AllowList
	}

	// Thêm các mẫu hàm
	for _, funcPattern := range yamlConfig.Functions {
		pattern := NewPattern(funcPattern)
		config.FunctionsList[pattern] = true
	}

	return config, nil
}
