Hi<PERSON><PERSON> rồ<PERSON>, tôi sẽ đơn giản hóa và chỉ tập trung vào việc giới hạn số lượng bản ghi. Đ<PERSON><PERSON> là phiên bản đơn giản hơn:

```go
// Thêm error definition mới
var (
    ErrTooManyRows = errors.New("query would return too many rows")
)

// Mở rộng AuditConfig để thêm các giới hạn về số lượng bản ghi
type AuditConfig struct {
    // Các trường hiện tại...
    TableAccessMode      AccessMode
    TablesList           map[string]bool
    ColumnAccessMode     AccessMode
    ColumnsList          map[string]map[string]bool
    AllowedJSONBFields   map[string]map[string]bool
    FunctionAccessMode   AccessMode
    FunctionsList        map[string]bool
    AllowedCTEDepth      int
    AllowWriteOperations bool
    AllowReadOperations  bool
    
    // Giới hạn số lượng bản ghi
    MaxRowLimit          int  // Số lượng bản ghi tối đa có thể trả về
    EnforceLimit         bool // Bắt buộc phải có LIMIT trong câu truy vấn
    DefaultLimit         int  // Giá trị LIMIT mặc định nếu không được chỉ định
}

// Cập nhật DefaultAuditConfig để thêm giá trị mặc định cho giới hạn số bản ghi
func DefaultAuditConfig() *AuditConfig {
    return &AuditConfig{
        TableAccessMode:      AllowList,
        TablesList:           make(map[string]bool),
        ColumnAccessMode:     AllowList,
        ColumnsList:          make(map[string]map[string]bool),
        AllowedJSONBFields:   make(map[string]map[string]bool),
        FunctionAccessMode:   AllowList,
        FunctionsList:        make(map[string]bool),
        AllowedCTEDepth:      3,
        AllowWriteOperations: false,
        AllowReadOperations:  true,
        
        // Giá trị mặc định cho giới hạn số bản ghi
        MaxRowLimit:          1000,
        EnforceLimit:         true,
        DefaultLimit:         100,
    }
}

// Mở rộng phương thức Validate để kiểm tra giới hạn số bản ghi
func (e *RuleEngine) Validate(result *pg_query.ParseResult) error {
    // Các kiểm tra hiện tại...
    if result == nil || len(result.Stmts) == 0 {
        return ErrEmptyQuery
    }

    for _, stmt := range result.Stmts {
        // Các kiểm tra hiện tại...
        
        // Thêm kiểm tra giới hạn số bản ghi
        if err := e.validateRowLimit(stmt.Stmt); err != nil {
            return err
        }
    }

    return nil
}

// Kiểm tra giới hạn số bản ghi
func (e *RuleEngine) validateRowLimit(stmt *pg_query.Node) error {
    if stmt == nil {
        return nil
    }
    
    // Chỉ kiểm tra cho SELECT statements
    selectStmt, ok := stmt.GetNode().(*pg_query.Node_SelectStmt)
    if !ok {
        return nil
    }
    
    // Kiểm tra nếu có LIMIT
    hasLimit := selectStmt.SelectStmt.LimitCount != nil
    
    // Nếu bắt buộc phải có LIMIT nhưng không có
    if e.config.EnforceLimit && !hasLimit {
        return fmt.Errorf("query must include a LIMIT clause")
    }
    
    // Nếu có LIMIT, kiểm tra giá trị
    if hasLimit {
        // Chỉ kiểm tra nếu LIMIT là một hằng số
        if constLimit, ok := selectStmt.SelectStmt.LimitCount.GetNode().(*pg_query.Node_AConst); ok {
            if intVal, ok := constLimit.AConst.Val.GetNode().(*pg_query.Node_Integer); ok {
                limitValue := int(intVal.Integer.Ival)
                
                if limitValue > e.config.MaxRowLimit {
                    return fmt.Errorf("%w: LIMIT %d exceeds maximum allowed (%d)", 
                        ErrTooManyRows, limitValue, e.config.MaxRowLimit)
                }
            }
        }
    }
    
    return nil
}

// Thêm phương thức để kiểm tra và gợi ý LIMIT
func (e *RuleEngine) CheckAndSuggestLimit(query string) (string, bool, error) {
    if !e.config.EnforceLimit {
        return query, false, nil
    }
    
    // Parse query
    result, err := e.parser.Parse(query)
    if err != nil {
        return "", false, err
    }
    
    needsLimit := false
    
    // Kiểm tra xem có cần thêm LIMIT không
    for _, stmt := range result.Stmts {
        if selectStmt, ok := stmt.Stmt.GetNode().(*pg_query.Node_SelectStmt); ok {
            if selectStmt.SelectStmt.LimitCount == nil {
                needsLimit = true
                break
            }
        }
    }
    
    if needsLimit {
        // Trả về gợi ý thêm LIMIT
        suggestion := fmt.Sprintf("Consider adding 'LIMIT %d' to your query", e.config.DefaultLimit)
        return suggestion, true, nil
    }
    
    return query, false, nil
}
```

Giải thích:

1. Tôi đã đơn giản hóa và chỉ tập trung vào việc giới hạn số lượng bản ghi với 3 tham số:
   - `MaxRowLimit`: Số lượng bản ghi tối đa có thể trả về
   - `EnforceLimit`: Bắt buộc phải có mệnh đề LIMIT
   - `DefaultLimit`: Giá trị LIMIT mặc định để gợi ý

2. Phương thức `validateRowLimit` kiểm tra:
   - Nếu `EnforceLimit` được bật, kiểm tra xem query có mệnh đề LIMIT không
   - Nếu có LIMIT, kiểm tra giá trị không vượt quá `MaxRowLimit`

3. Thêm phương thức `CheckAndSuggestLimit` để kiểm tra và gợi ý thêm LIMIT khi cần thiết, thay vì cố gắng sửa đổi query.

Cách tiếp cận này đơn giản hơn và tập trung vào vấn đề cốt lõi là giới hạn số lượng bản ghi trả về.