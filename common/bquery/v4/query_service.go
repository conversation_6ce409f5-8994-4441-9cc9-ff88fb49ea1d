package v4

import (
	"context"
	"fmt"
	"github.com/zeromicro/go-zero/core/logx"
	"hash/fnv"
	"reflect"
	"time"

	"github.com/pganalyze/pg_query_go/v6"
)

// RuleValidator định nghĩa interface cho việc xác thực truy vấn SQL
type RuleValidator interface {
	Validate(parseResult *pg_query.ParseResult) error
	ValidateParameters(parseResult *pg_query.ParseResult, params []interface{}) error
}

// CacheProvider định nghĩa interface cho cache truy vấn
type CacheProvider interface {
	Get(key string) (*CacheEntry, bool)
	Set(key string, entry *CacheEntry)
	Clear()
	Stats() any
}

// QueryService cung cấp API cao cấp để xác thực và chuẩn bị các truy vấn SQL
type QueryService struct {
	parser  SQLParser
	auditor RuleValidator
	cache   CacheProvider
}

// NewQueryService tạo một service truy vấn mới
func NewQueryService(auditConfig *AuditConfig) *QueryService {
	return &QueryService{
		parser:  NewPostgresParser(),
		auditor: NewRuleEngine(auditConfig),
		cache:   NewQueryCache(),
	}
}

// PrepareQuery xác thực truy vấn SQL và trả về query string và args để sử dụng với bob
func (s *QueryService) PrepareQuery(sql string, params ...interface{}) (string, []interface{}, error) {
	// 1. Tạo khóa cache
	cacheKey, err := s.generateCacheKey(sql, params)
	if err != nil {
		return "", nil, fmt.Errorf("failed to generate cache key: %w", err)
	}

	// Cắt SQL query thành 30 ký tự cho logging
	truncatedSQL := sql
	if len(sql) > 30 {
		truncatedSQL = sql[:30] + "..."
	}

	// 2. Kiểm tra cache
	if entry, found := s.cache.Get(cacheKey); found {
		// Cache hit - ghi log và trả về prepared statement và params
		logx.Info("Query cache hit. ", "sql: ", truncatedSQL)
		return entry.ValidatedSQL, params, nil
	}

	// Cache miss - ghi log
	logx.Info("Query cache miss. ", "sql: ", truncatedSQL)

	// 3. Cache miss - Phân tích SQL để xác thực
	parseResult, err := s.parser.Parse(sql)
	if err != nil {
		return "", nil, fmt.Errorf("SQL syntax error: %w", err)
	}

	// 4. Xác thực truy vấn
	if err := s.auditor.Validate(parseResult); err != nil {
		return "", nil, err
	}

	// 5. Xác thực tham số
	if err := s.auditor.ValidateParameters(parseResult, params); err != nil {
		return "", nil, err
	}

	// 6. Lưu truy vấn đã xác thực vào cache
	tables, _ := extractTableDependencies(parseResult)

	// Trích xuất kiểu tham số cho xác thực schema
	paramTypes := make([]string, len(params))

	for i, param := range params {
		if param == nil {
			paramTypes[i] = "nil"
		} else {
			paramTypes[i] = reflect.TypeOf(param).String()
		}
	}

	s.cache.Set(cacheKey, &CacheEntry{
		QueryHash:         cacheKey,
		ParamTypes:        paramTypes,
		ValidatedSQL:      sql,
		TableDependencies: tables,
		ExpiresAt:         time.Now().Add(300 * time.Minute),
	})

	// 7. Trả về query và params để sử dụng với bob
	return sql, params, nil
}

// PrepareNonQuery xác thực truy vấn không trả về dữ liệu và trả về query string và args
func (s *QueryService) PrepareNonQuery(ctx context.Context, sql string, params ...interface{}) (string, []interface{}, error) {
	// Xác thực truy vấn trước khi thực thi
	parseResult, err := s.parser.Parse(sql)
	if err != nil {
		return "", nil, fmt.Errorf("SQL syntax error: %w", err)
	}

	if err := s.auditor.Validate(parseResult); err != nil {
		return "", nil, err
	}

	if err := s.auditor.ValidateParameters(parseResult, params); err != nil {
		return "", nil, err
	}

	// Trả về query và params để sử dụng với bob
	return sql, params, nil
}

// GetStats trả về thống kê cache
func (s *QueryService) GetStats() interface{} {
	return s.cache.Stats()
}

// ClearCache xóa cache truy vấn
func (s *QueryService) ClearCache() {
	s.cache.Clear()
}

// Helper functions

// generateCacheKey được chuyển thành phương thức của QueryService
func (s *QueryService) generateCacheKey(sql string, params []interface{}) (string, error) {
	// Sử dụng parser đã có sẵn trong service
	normalizedSQL, err := s.parser.Normalize(sql)
	if err != nil {
		return "", fmt.Errorf("failed to normalize SQL: %w", err)
	}

	// Sử dụng FNV-1a với SQL đã chuẩn hóa
	h := fnv.New64a()
	h.Write([]byte(normalizedSQL))

	// Thêm kiểu tham số vào hash (không phải giá trị)
	for _, param := range params {
		h.Write([]byte(fmt.Sprintf("%T", param)))
	}

	// Chuyển đổi hash thành chuỗi hex
	return fmt.Sprintf("%x", h.Sum(nil)), nil
}

// extractTableDependencies lấy tất cả tên bảng từ truy vấn đã phân tích
func extractTableDependencies(parseResult *pg_query.ParseResult) ([]string, error) {
	if parseResult == nil {
		return nil, nil
	}

	parser := NewPostgresParser()
	return parser.GetTableNames(parseResult)
}
