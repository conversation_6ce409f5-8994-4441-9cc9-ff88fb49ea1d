package v4

import (
	"context"
	"strings"
	"testing"

	pg_query "github.com/pganalyze/pg_query_go/v6"
	"github.com/stretchr/testify/assert"
)

// Test case cho việc khởi tạo QueryService
func TestNewQueryService(t *testing.T) {
	// Arrange
	auditConfig := &AuditConfig{
		TableAccessMode: AllowList,
		TablesList:      map[Pattern]bool{NewPattern("users"): true},
		AllowedCTEDepth: 2,
		AllowedJSONBFields: map[Pattern]map[Pattern]bool{
			NewPattern("users"): {
				NewPattern("name"):  true,
				NewPattern("email"): true,
			},
		},
		AllowReadOperations: true,
	}

	// Act
	queryService := NewQueryService(auditConfig)

	// Assert
	assert.NotNil(t, queryService, "Expected QueryService to be created")
	assert.NotNil(t, queryService.parser, "Expected parser to be initialized")
	assert.NotNil(t, queryService.auditor, "Expected auditor to be initialized")
	assert.NotNil(t, queryService.cache, "Expected cache to be initialized")
}

// MockParser triển khai SQLParser interface cho testing
type MockParser struct {
	parseFunc         func(sql string) (*pg_query.ParseResult, error)
	getTableNamesFunc func(parseResult *pg_query.ParseResult) ([]string, error)
}

func (m *MockParser) GetTableAliases(result *pg_query.ParseResult) (map[string]string, error) {
	//TODO implement me
	panic("implement me")
}

func (m *MockParser) GetFunctionCalls(parseResult *pg_query.ParseResult) ([]string, error) {
	panic("implement me")
}

func (m *MockParser) GetColumnReferences(parseResult *pg_query.ParseResult) ([]ColumnReference, error) {
	panic("implement me")
}

func (m *MockParser) Normalize(rawSQL string) (string, error) {
	panic("implement me")
}

func (m *MockParser) GetCTEDepth(parseResult *pg_query.ParseResult) (int, error) {
	panic("implement me")
}

func (m *MockParser) Parse(sql string) (*pg_query.ParseResult, error) {
	return m.parseFunc(sql)
}

func (m *MockParser) GetTableNames(parseResult *pg_query.ParseResult) ([]string, error) {
	return m.getTableNamesFunc(parseResult)
}

// MockRuleEngine triển khai RuleValidator interface cho testing
type MockRuleEngine struct {
	validateFunc           func(parseResult *pg_query.ParseResult) error
	validateParametersFunc func(parseResult *pg_query.ParseResult, params []interface{}) error
}

func (m *MockRuleEngine) Validate(parseResult *pg_query.ParseResult) error {
	return m.validateFunc(parseResult)
}

func (m *MockRuleEngine) ValidateParameters(parseResult *pg_query.ParseResult, params []interface{}) error {
	return m.validateParametersFunc(parseResult, params)
}

// MockCacheProvider triển khai CacheProvider interface cho testing
type MockCacheProvider struct {
	getFunc    func(key string) (*CacheEntry, bool)
	setFunc    func(key string, entry *CacheEntry)
	clearFunc  func()
	statsFunc  func() any
	cacheStore map[string]*CacheEntry
}

func NewMockCacheProvider() *MockCacheProvider {
	return &MockCacheProvider{
		cacheStore: make(map[string]*CacheEntry),
	}
}

func (m *MockCacheProvider) Get(key string) (*CacheEntry, bool) {
	if m.getFunc != nil {
		return m.getFunc(key)
	}
	entry, found := m.cacheStore[key]
	return entry, found
}

func (m *MockCacheProvider) Set(key string, entry *CacheEntry) {
	if m.setFunc != nil {
		m.setFunc(key, entry)
		return
	}
	m.cacheStore[key] = entry
}

func (m *MockCacheProvider) Clear() {
	if m.clearFunc != nil {
		m.clearFunc()
		return
	}
	m.cacheStore = make(map[string]*CacheEntry)
}

func (m *MockCacheProvider) Stats() any {
	if m.statsFunc != nil {
		return m.statsFunc()
	}
	return map[string]int{"entries": len(m.cacheStore)}
}

// Test cho PrepareQuery
func TestPrepareQuery(t *testing.T) {
	// Arrange
	parseCount := 0
	auditCount := 0

	// Mock parser để đếm số lần parse
	mockParser := &MockParser{
		parseFunc: func(sql string) (*pg_query.ParseResult, error) {
			parseCount++
			return &pg_query.ParseResult{}, nil
		},
		getTableNamesFunc: func(parseResult *pg_query.ParseResult) ([]string, error) {
			return []string{"users"}, nil
		},
	}

	// Mock auditor để đếm số lần validate
	mockAuditor := &MockRuleEngine{
		validateFunc: func(parseResult *pg_query.ParseResult) error {
			auditCount++
			return nil
		},
		validateParametersFunc: func(parseResult *pg_query.ParseResult, params []interface{}) error {
			return nil
		},
	}

	mockCache := NewMockCacheProvider()

	auditConfig := &AuditConfig{
		TableAccessMode:     AllowList,
		TablesList:          map[Pattern]bool{NewPattern("users"): true},
		AllowReadOperations: true,
	}

	// Tạo query service với các mock components
	queryService := NewQueryService(auditConfig)
	queryService.parser = mockParser
	queryService.auditor = mockAuditor
	queryService.cache = mockCache

	// Act - Prepare same query twice
	query := "SELECT id FROM users WHERE id = $1"
	sql1, args1, err1 := queryService.PrepareQuery(query, 1)
	sql2, args2, err2 := queryService.PrepareQuery(query, 1)

	// Assert
	assert.NoError(t, err1)
	assert.NoError(t, err2)
	assert.Equal(t, query, sql1)
	assert.Equal(t, query, sql2)
	assert.Equal(t, []interface{}{1}, args1)
	assert.Equal(t, []interface{}{1}, args2)

	// Kiểm tra xem parse và audit chỉ được thực hiện một lần do cache
	assert.Equal(t, 1, parseCount, "Expected SQL to be parsed only once due to caching")
	assert.Equal(t, 1, auditCount, "Expected query to be audited only once due to caching")
}

// Test cho PrepareQuery với validation failure
func TestPrepareQueryValidationFailure(t *testing.T) {
	// Arrange
	mockParser := &MockParser{
		parseFunc: func(sql string) (*pg_query.ParseResult, error) {
			return &pg_query.ParseResult{}, nil
		},
	}

	mockAuditor := &MockRuleEngine{
		validateFunc: func(parseResult *pg_query.ParseResult) error {
			return ErrTableNotAllowed
		},
	}

	auditConfig := &AuditConfig{
		TableAccessMode:     AllowList,
		TablesList:          map[Pattern]bool{NewPattern("users"): true},
		AllowReadOperations: true,
	}

	queryService := NewQueryService(auditConfig)
	queryService.parser = mockParser
	queryService.auditor = mockAuditor

	// Act
	_, _, err := queryService.PrepareQuery(
		"SELECT * FROM unauthorized_table",
	)

	// Assert
	assert.Error(t, err)
	assert.Equal(t, ErrTableNotAllowed, err)
}

// ValidationError là kiểu lỗi tùy chỉnh cho các lỗi xác thực
type ValidationError struct {
	Message string
}

func (e *ValidationError) Error() string {
	return e.Message
}

// Test cho PrepareNonQuery
func TestPrepareNonQuery(t *testing.T) {
	// Arrange
	mockParser := &MockParser{
		parseFunc: func(sql string) (*pg_query.ParseResult, error) {
			return &pg_query.ParseResult{}, nil
		},
	}

	mockAuditor := &MockRuleEngine{
		validateFunc: func(parseResult *pg_query.ParseResult) error {
			return &ValidationError{Message: "write operation not allowed"}
		},
		validateParametersFunc: func(parseResult *pg_query.ParseResult, params []interface{}) error {
			return nil
		},
	}

	auditConfig := &AuditConfig{
		TableAccessMode:      AllowList,
		TablesList:           map[Pattern]bool{NewPattern("users"): true},
		AllowReadOperations:  true,
		AllowWriteOperations: false,
	}

	queryService := NewQueryService(auditConfig)
	queryService.parser = mockParser
	queryService.auditor = mockAuditor

	// Act
	_, _, err := queryService.PrepareNonQuery(
		context.Background(),
		"UPDATE users SET name = $1 WHERE id = $2",
		"New Name",
		1,
	)

	// Assert
	assert.Error(t, err)
	assert.True(t, strings.Contains(err.Error(), "write operation not allowed"))
}

// Test cho cache functionality
func TestQueryCaching(t *testing.T) {
	// Arrange
	mockCache := NewMockCacheProvider()

	mockParser := &MockParser{
		parseFunc: func(sql string) (*pg_query.ParseResult, error) {
			return &pg_query.ParseResult{}, nil
		},
		getTableNamesFunc: func(parseResult *pg_query.ParseResult) ([]string, error) {
			return []string{"users"}, nil
		},
	}

	mockAuditor := &MockRuleEngine{
		validateFunc: func(parseResult *pg_query.ParseResult) error {
			return nil
		},
		validateParametersFunc: func(parseResult *pg_query.ParseResult, params []interface{}) error {
			return nil
		},
	}

	auditConfig := &AuditConfig{
		TableAccessMode:     AllowList,
		TablesList:          map[Pattern]bool{NewPattern("users"): true},
		AllowReadOperations: true,
	}

	queryService := NewQueryService(auditConfig)
	queryService.parser = mockParser
	queryService.auditor = mockAuditor
	queryService.cache = mockCache

	// Act
	query := "SELECT * FROM users WHERE id = $1"
	sql1, args1, err1 := queryService.PrepareQuery(query, 1)
	statsBefore := queryService.GetStats()
	sql2, args2, err2 := queryService.PrepareQuery(query, 1)
	queryService.ClearCache()
	statsAfterClear := queryService.GetStats()

	// Assert
	assert.NoError(t, err1)
	assert.NoError(t, err2)
	assert.Equal(t, query, sql1)
	assert.Equal(t, query, sql2)
	assert.Equal(t, []interface{}{1}, args1)
	assert.Equal(t, []interface{}{1}, args2)

	entriesBefore := statsBefore.(map[string]int)["entries"]
	entriesAfterClear := statsAfterClear.(map[string]int)["entries"]

	assert.Equal(t, 1, entriesBefore, "Expected one entry in cache")
	assert.Equal(t, 0, entriesAfterClear, "Expected zero entries after clearing cache")
}
