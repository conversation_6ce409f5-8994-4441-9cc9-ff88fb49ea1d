package bqueryv2

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
)

// Parse parses the JSON request body into the provided DynamicQuery struct
func Parse(r *http.Request, query *DynamicQuery) error {
	// Kiểm tra content type
	contentType := r.Header.Get("Content-Type")
	if !strings.Contains(strings.ToLower(contentType), "application/json") {
		return fmt.Errorf("content type must be application/json")
	}

	// Đọc body
	body, err := io.ReadAll(r.Body)
	if err != nil {
		return fmt.Errorf("failed to read request body: %w", err)
	}
	defer r.Body.Close()

	// Kiểm tra nếu body rỗng
	if len(body) == 0 {
		return fmt.Errorf("request body is empty")
	}

	// Decode JSON
	err = json.Unmarshal(body, query)
	if err != nil {
		return fmt.Errorf("failed to parse JSON: %w", err)
	}

	// Xác thực cấu trúc của query
	if err := validateQuery(query); err != nil {
		return err
	}

	return nil
}

// validateQ<PERSON>y thực hiện kiểm tra cơ bản trên cấu trúc query
func validateQuery(query *DynamicQuery) error {
	if query.Table == "" {
		return fmt.Errorf("table name is required")
	}

	// Xác thực các biểu thức logic trong where_logic nếu có
	if query.WhereLogic != nil {
		if err := validateLogicalExpression(query.WhereLogic); err != nil {
			return fmt.Errorf("invalid where_logic: %w", err)
		}
	}

	// Xác thực các biểu thức logic trong having_logic nếu có
	if query.HavingLogic != nil {
		if err := validateLogicalExpression(query.HavingLogic); err != nil {
			return fmt.Errorf("invalid having_logic: %w", err)
		}
	}

	// Xác thực joins nếu có
	for i, join := range query.Joins {
		if join.Table == "" && join.Subquery == nil {
			return fmt.Errorf("join at index %d must specify either table or subquery", i)
		}

		// CROSS JOIN không cần điều kiện
		if strings.ToUpper(join.Type) != "CROSS" {
			if join.OnLogic == nil && len(join.Using) == 0 {
				return fmt.Errorf("join at index %d must specify either on_logic or using", i)
			}
		}

		if join.OnLogic != nil {
			if err := validateLogicalExpression(join.OnLogic); err != nil {
				return fmt.Errorf("invalid on_logic in join at index %d: %w", i, err)
			}
		}
	}

	// Có thể thêm nhiều kiểm tra khác tùy theo yêu cầu

	return nil
}
