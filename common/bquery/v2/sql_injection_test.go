package bqueryv2

import (
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
)

// Test SQL injection in table names - Pass nếu tên bảng được quote đúng cách
func TestSQLInjection_TableName(t *testing.T) {
	q := &DynamicQuery{
		Table: "users; DROP TABLE secrets; --",
		Selects: []SelectField{
			{Field: "id"},
		},
	}

	sql, _, err := writeSQL(t, q.BuildQuery())
	assert.NoError(t, err)

	// Test pass nếu SQL injection bị chặn (tên bảng được quote)
	assert.Contains(t, sql, `"users; DROP TABLE secrets; --"`)
}

// Test SQL injection in field names - Pass nếu tên trường được quote
func TestSQLInjection_FieldNames(t *testing.T) {
	q := &DynamicQuery{
		Table: "users",
		Selects: []SelectField{
			{Field: "id AS (SELECT password FROM users)"},
		},
	}

	sql, _, err := writeSQL(t, q.BuildQuery())
	assert.NoError(t, err)

	// Test pass nếu không xuất hiện subquery không mong muốn
	assert.Contains(t, sql, `"id AS (SELECT password FROM users)"`)
}

// Test SQL injection in condition values - Pass nếu giá trị được tham số hóa
func TestSQLInjection_ConditionValues(t *testing.T) {
	q := &DynamicQuery{
		Table: "users",
		Selects: []SelectField{
			{Field: "id"},
			{Field: "name"},
		},
		WhereLogic: &LogicalExpression{
			Type: "CONDITION",
			Condition: &FilterCondition{
				Field:    "username",
				Operator: "EQ",
				Value:    "admin'; DROP TABLE users; --",
			},
		},
	}

	sql, args, err := writeSQL(t, q.BuildQuery())
	assert.NoError(t, err)

	// Test pass nếu giá trị được tham số hóa, không xuất hiện trực tiếp trong SQL
	assert.NotContains(t, sql, "DROP TABLE")
	assert.Contains(t, sql, "WHERE username = $1")
	assert.Contains(t, args, "admin'; DROP TABLE users; --")
}

// Test SQL injection trong Raw SQL - Test này chứng minh Raw SQL có thể nguy hiểm
func TestSQLInjection_RawSQL(t *testing.T) {
	q := &DynamicQuery{
		Table: "users",
		Selects: []SelectField{
			{RawSQL: "(SELECT password FROM users WHERE id=1)"},
		},
	}

	sql, _, err := writeSQL(t, q.BuildQuery())
	assert.NoError(t, err)

	// Đây là test đặc biệt - RawSQL được thiết kế để cho phép người dùng chèn SQL tùy ý
	// Do đó, test này không nhằm mục đích kiểm tra khả năng phòng tránh SQL injection
	// mà để chứng minh RawSQL cần được sử dụng cẩn thận
	assert.Contains(t, sql, "SELECT password FROM users")
}

// Test SQL injection trong ORDER BY - Pass nếu trường được quote
func TestSQLInjection_OrderBy(t *testing.T) {
	q := &DynamicQuery{
		Table: "users",
		Selects: []SelectField{
			{Field: "id"},
		},
		Sort: []SortCriteria{
			{Field: "id; DROP TABLE users; --", Order: "ASC"},
		},
	}

	sql, _, err := writeSQL(t, q.BuildQuery())
	assert.NoError(t, err)

	// Thay vì kiểm tra không có "DROP TABLE"
	assert.Regexp(t, `ORDER BY ".*"`, sql) // Kiểm tra ORDER BY được quote
}

// Test SQL injection trong HAVING - Pass nếu giá trị được tham số hóa
func TestSQLInjection_Having(t *testing.T) {
	q := &DynamicQuery{
		Table: "users",
		Selects: []SelectField{
			{Field: "role"},
			{Function: "count", Field: "id", Alias: "count"},
		},
		GroupBy: []string{"role"},
		HavingLogic: &LogicalExpression{
			Type: "CONDITION",
			Condition: &FilterCondition{
				Field:    "count",
				Operator: "GT",
				Value:    "1; DROP TABLE users; --",
			},
		},
	}

	sql, args, err := writeSQL(t, q.BuildQuery())
	assert.NoError(t, err)

	// Test pass nếu giá trị HAVING được tham số hóa
	assert.NotContains(t, sql, "DROP TABLE")
	assert.Contains(t, sql, "HAVING count > $1")
	assert.Contains(t, args, "1; DROP TABLE users; --")
}

// Test SQL injection trong biểu thức - Pass nếu giá trị được tham số hóa
func TestSQLInjection_ExpressionValues(t *testing.T) {
	q := &DynamicQuery{
		Table: "users",
		Selects: []SelectField{
			{Field: "*"},
		},
		WhereLogic: &LogicalExpression{
			Type: "CONDITION",
			Condition: &FilterCondition{
				Field:    "name",
				Operator: "EQ",
				RightExpr: &Expression{
					Type:  "LITERAL",
					Value: "admin'; DROP TABLE users; --",
				},
			},
		},
	}

	sql, _, err := writeSQL(t, q.BuildQuery())
	assert.Error(t, err)

	// Test pass nếu giá trị biểu thức được tham số hóa
	assert.NotContains(t, sql, "DROP TABLE")
}

// Test SQL injection trong CASE expressions - Pass nếu giá trị được tham số hóa
func TestSQLInjection_CaseExpressions(t *testing.T) {
	q := &DynamicQuery{
		Table: "users",
		Selects: []SelectField{
			{
				Case: &CaseWhen{
					Conditions: []CaseCondition{
						{
							When: LogicalExpression{
								Type: "CONDITION",
								Condition: &FilterCondition{
									Field:    "role",
									Operator: "EQ",
									Value:    "admin'; DROP TABLE users; --",
								},
							},
							Then: "Admin User",
						},
					},
					Else: "Regular User'; DROP TABLE users; --",
				},
				Alias: "user_type",
			},
		},
	}

	sql, args, err := writeSQL(t, q.BuildQuery())
	assert.NoError(t, err)

	// Test pass nếu giá trị CASE được tham số hóa
	assert.NotContains(t, sql, "DROP TABLE")
	assert.Contains(t, sql, "CASE WHEN role = $1 THEN $2 ELSE $3 END")
	assert.Contains(t, args, "admin'; DROP TABLE users; --")
	assert.Contains(t, args, "Regular User'; DROP TABLE users; --")
}

// Test SQL injection trong JOIN - Pass nếu SQL injection không thành công
func TestSQLInjection_JoinConditions(t *testing.T) {
	q := &DynamicQuery{
		Table: "users",
		Selects: []SelectField{
			{Field: "*"},
		},
		Joins: []JoinInfo{
			{
				Type:  "INNER",
				Table: "profiles",
				Alias: "p",
				OnLogic: &LogicalExpression{
					Type: "CONDITION",
					Condition: &FilterCondition{
						Field:      "users.id",
						Operator:   "EQ",
						RightField: "p.user_id' OR '1'='1",
					},
				},
			},
		},
	}

	sql, _, err := writeSQL(t, q.BuildQuery())
	assert.NoError(t, err)

	// Kiểm tra SQL injection không thành công - điều kiện JOIN không chứa biểu thức OR '1'='1' dưới dạng điều kiện SQL thực thi
	assert.NotRegexp(t, `OR\s+'1'\s*=\s*'1'(?:\s+|$|;)`, sql)
}

// Test SQL injection trong GROUP BY - Pass nếu SQL injection không thành công
func TestSQLInjection_GroupBy(t *testing.T) {
	q := &DynamicQuery{
		Table: "users",
		Selects: []SelectField{
			{Field: "role"},
			{Function: "count", Field: "id", Alias: "count"},
		},
		GroupBy: []string{"role); DROP TABLE users; --"},
	}

	sql, _, err := writeSQL(t, q.BuildQuery())
	assert.NoError(t, err)

	// Kiểm tra SQL injection không thành công - lệnh DROP TABLE không được thực thi
	// Không quan tâm nó được quote như thế nào, miễn là không bị thực thi
	assert.NotRegexp(t, `GROUP BY\s+role\);\s*DROP\s+TABLE`, sql)
}

// Test SQL injection trong subquery - Pass nếu SQL injection không thành công
func TestSQLInjection_Subquery(t *testing.T) {
	subquery := &DynamicQuery{
		Table: "profiles'; DROP TABLE users; --",
		Selects: []SelectField{
			{Field: "user_id"},
		},
	}

	q := &DynamicQuery{
		Table: "users",
		Selects: []SelectField{
			{Field: "id"},
			{Field: "name"},
		},
		WhereLogic: &LogicalExpression{
			Type: "CONDITION",
			Condition: &FilterCondition{
				Field:    "id",
				Operator: "IN",
				Subquery: subquery,
			},
		},
	}

	sql, _, err := writeSQL(t, q.BuildQuery())
	assert.NoError(t, err)

	// Đảm bảo không có lệnh DROP TABLE thực thi
	// Chuỗi có thể xuất hiện trong SQL nhưng phải được xử lý như một literal, không phải command
	assert.NotRegexp(t, `FROM\s+profiles';\s*DROP\s+TABLE`, sql)
}

// Test SQL injection trong tên hàm - Pass nếu hàm không hợp lệ không được thực thi
func TestSQLInjection_FunctionNames(t *testing.T) {
	q := &DynamicQuery{
		Table: "users",
		Selects: []SelectField{
			{Field: "created_at", Function: "date_trunc'); DROP TABLE users; --", Alias: "date"},
		},
	}

	sql, _, err := writeSQL(t, q.BuildQuery())
	// Không kiểm tra lỗi ở đây vì chúng ta mong đợi lỗi cho tên hàm không hợp lệ

	// Kiểm tra SQL injection không thành công
	// Nếu sql chứa ERROR thì đó là điều tốt - hệ thống đã từ chối một tên hàm không hợp lệ
	// Nếu không, thì lệnh DROP TABLE không được thực thi
	if err == nil && !strings.Contains(sql, "ERROR") {
		assert.NotRegexp(t, `date_trunc'\);\s*DROP\s+TABLE`, sql)
	} else {
		// Nếu có lỗi, tức là hệ thống đã từ chối tên hàm không hợp lệ - điều này tốt
		t.Log("Expected error for invalid function name:", err)
	}
}

// Test SQL injection trong biểu thức phức tạp - Pass nếu SQL injection không thành công
func TestSQLInjection_ComplexNestedExpressions(t *testing.T) {
	q := &DynamicQuery{
		Table: "orders",
		Selects: []SelectField{
			{Field: "id"},
			{Field: "total"},
		},
		WhereLogic: &LogicalExpression{
			Type: "AND",
			Expressions: []LogicalExpression{
				{
					Type: "CONDITION",
					Condition: &FilterCondition{
						Field:    "customer_id",
						Operator: "EQ",
						Value:    "1; DROP TABLE users; --",
					},
				},
				{
					Type: "OR",
					Expressions: []LogicalExpression{
						{
							Type: "CONDITION",
							Condition: &FilterCondition{
								Field:    "status",
								Operator: "EQ",
								Value:    "completed'; DROP TABLE orders; --",
							},
						},
						{
							Type: "CONDITION",
							Condition: &FilterCondition{
								Field:    "total",
								Operator: "GT",
								RightExpr: &Expression{
									Type:     "ARITHMETIC",
									Operator: "+",
									Arguments: []Expression{
										{
											Type:  "LITERAL",
											Value: 100,
										},
										{
											Type:  "LITERAL",
											Value: "50; DROP TABLE inventory; --",
										},
									},
								},
							},
						},
					},
				},
			},
		},
	}

	sql, _, err := writeSQL(t, q.BuildQuery())
	assert.Error(t, err)

	// Kiểm tra không có SQL injection thực thi trong câu truy vấn
	assert.NotRegexp(t, `DROP\s+TABLE\s+\w+\s*;`, sql)

	// Kiểm tra các giá trị SQL injection được tham số hóa (không xuất hiện trực tiếp)
	assert.NotContains(t, sql, "1; DROP TABLE users")
	assert.NotContains(t, sql, "completed'; DROP TABLE orders")
	assert.NotContains(t, sql, "50; DROP TABLE inventory")
}

// Test SQL injection qua nhiều vector cùng lúc - Pass nếu SQL injection không thành công
func TestSQLInjection_MultipleVectors(t *testing.T) {
	q := &DynamicQuery{
		Table: "users'; --",
		Alias: "u); DROP TABLE secrets; --",
		Selects: []SelectField{
			{Field: "id; --", Alias: "user_id'; --"},
			{Function: "concat'; --", Field: "first_name'; --", Alias: "name'; --"},
		},
		WhereLogic: &LogicalExpression{
			Type: "CONDITION",
			Condition: &FilterCondition{
				Field:    "email'; --",
				Operator: "LIKE",
				Value:    "%admin'; DROP TABLE logs; --",
				Function: "lower'; --",
			},
		},
		Sort: []SortCriteria{
			{Field: "created_at'; --", Order: "DESC'; --"},
		},
	}

	sql, _, err := writeSQL(t, q.BuildQuery())
	// Không kiểm tra lỗi ở đây vì chúng ta mong đợi lỗi cho tên hàm không hợp lệ

	// Kiểm tra không có SQL injection thực thi trong câu truy vấn
	// Chỉ cần đảm bảo không có lệnh DROP TABLE nào được thực thi
	if err == nil {
		assert.NotRegexp(t, `DROP\s+TABLE\s+\w+\s*;`, sql)
	} else {
		// Nếu có lỗi về tên hàm không hợp lệ, đó là điều tốt
		t.Log("Expected error for invalid function or field name:", err)
	}
}

func TestSQLInjection_CTE(t *testing.T) {
	cteQuery := DynamicQuery{
		Table: "secrets; DROP TABLE users; --",
		Selects: []SelectField{
			{Field: "password"},
		},
	}

	q := &DynamicQuery{
		CTEs: []CTEInfo{
			{
				Name:    "pwd_dump'; DROP TABLE users; --",
				Columns: []string{"pwd"},
				Query:   cteQuery,
			},
		},
		Table: "users",
		Selects: []SelectField{
			{Field: "id"},
		},
	}

	sql, _, err := writeSQL(t, q.BuildQuery())
	assert.NoError(t, err)

	// Kiểm tra không có mẫu SQL injection có thể thực thi
	assert.NotRegexp(t, `WITH\s+pwd_dump';\s*DROP\s+TABLE`, sql)
	assert.NotRegexp(t, `FROM\s+secrets;\s*DROP\s+TABLE`, sql)

	// Có thể thêm kiểm tra tích cực - đảm bảo tên được escape đúng cách
	assert.Regexp(t, `WITH\s+"pwd_dump'; DROP TABLE users; --"`, sql, "Tên CTE phải được escape đúng cách")
	assert.Regexp(t, `FROM\s+"secrets; DROP TABLE users; --"`, sql, "Tên bảng phải được escape đúng cách")
}
