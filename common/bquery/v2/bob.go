package bqueryv2

import (
	"fmt"
	"github.com/stephenafamo/bob/mods"
	"strconv"
	"strings"
	"unicode"

	"github.com/stephenafamo/bob"
	"github.com/stephenafamo/bob/clause"
	"github.com/stephenafamo/bob/dialect/psql"
	"github.com/stephenafamo/bob/dialect/psql/dialect"
	"github.com/stephenafamo/bob/dialect/psql/fm"
	"github.com/stephenafamo/bob/dialect/psql/sm"
	"github.com/stephenafamo/bob/dialect/psql/wm"
)

// SafeMode controls whether to disable potentially unsafe features
// It's a package-level variable that can be set by users
var SafeMode = false

// SetSafeMode enables or disables safe mode globally
func SetSafeMode(enabled bool) {
	SafeMode = enabled
}

// validFunctions maps allowed SQL functions (to prevent SQL injection via function names)
var validFunctions = map[string]bool{
	"COUNT":      true,
	"SUM":        true,
	"AVG":        true,
	"MIN":        true,
	"MAX":        true,
	"LOWER":      true,
	"UPPER":      true,
	"INITCAP":    true,
	"TO_CHAR":    true,
	"TO_DATE":    true,
	"DATE_TRUNC": true,
	"EXTRACT":    true,
	"COALESCE":   true,
	"NULLIF":     true,
	"CONCAT":     true,
	"LENGTH":     true,
	"TRIM":       true,
	"SUBSTRING":  true,
	"RANK":       true,
	"ROW_NUMBER": true,
	"DENSE_RANK": true,
}

// isValidFunction checks if a function name is in the allowed list
func isValidFunction(name string) bool {
	return validFunctions[strings.ToUpper(name)]
}

// safeFunction returns a sanitized function name or empty string if invalid
func safeFunction(name string) string {
	if isValidFunction(name) {
		return name
	}
	return ""
}

// parseFunctionArgs parses the arguments of SQL functions dynamically
func parseFunctionArgs(argsStr string) []any {
	// Placeholder implementation, consider using a proper parser
	parts := strings.Split(argsStr, ",")
	args := make([]any, len(parts))
	for i, part := range parts {
		part = strings.TrimSpace(part)
		if strings.HasPrefix(part, "'") && strings.HasSuffix(part, "'") {
			// String literal
			args[i] = psql.Raw(part)
		} else if strings.HasPrefix(part, "\"") && strings.HasSuffix(part, "\"") {
			// Quoted identifier
			args[i] = psql.Raw(part)
		} else if _, err := strconv.ParseFloat(part, 64); err == nil {
			// Number
			args[i] = psql.Raw(part)
		} else {
			// Assume it's a column or expression
			args[i] = psql.Raw(part)
		}
	}
	return args
}

// buildLogicalExpression converts the logical expression tree to a bob.Expression
func buildLogicalExpression(expr *LogicalExpression) bob.Expression {
	if expr == nil {
		return nil
	}

	switch strings.ToUpper(expr.Type) {
	case "CONDITION":
		if expr.Condition == nil {
			return nil
		}
		return buildFilterCondition(*expr.Condition)

	case "AND":
		if len(expr.Expressions) == 0 {
			return nil
		}

		childExpressions := make([]bob.Expression, 0, len(expr.Expressions))
		for _, childExpr := range expr.Expressions {
			builtExpr := buildLogicalExpression(&childExpr)
			if builtExpr != nil {
				childExpressions = append(childExpressions, builtExpr)
			}
		}

		if len(childExpressions) == 0 {
			return nil
		}
		if len(childExpressions) == 1 {
			return childExpressions[0]
		}
		return psql.And(childExpressions...)

	case "OR":
		if len(expr.Expressions) == 0 {
			return nil
		}

		childExpressions := make([]bob.Expression, 0, len(expr.Expressions))
		for _, childExpr := range expr.Expressions {
			builtExpr := buildLogicalExpression(&childExpr)
			if builtExpr != nil {
				childExpressions = append(childExpressions, builtExpr)
			}
		}

		if len(childExpressions) == 0 {
			return nil
		}
		if len(childExpressions) == 1 {
			return childExpressions[0]
		}
		return psql.Or(childExpressions...)
	}

	return nil
}

// buildExpression constructs an expression from Expression structure
func buildExpression(expr *Expression) bob.Expression {
	if expr == nil {
		return nil
	}

	switch strings.ToUpper(expr.Type) {
	case "LITERAL":
		return handleLiteral(expr)
	case "COLUMN":
		return handleColumn(expr)
	case "FUNCTION":
		return handleFunction(expr)
	case "ARITHMETIC":
		return handleArithmetic(expr)
	}

	return nil
}

func handleLiteral(expr *Expression) bob.Expression {
	switch strings.ToUpper(expr.LiteralType) {
	case "RAW":
		return psql.Raw(fmt.Sprint(expr.Value))
	case "STRING":
		return psql.Arg(fmt.Sprint(expr.Value))
	case "NUMBER":
		return psql.Arg(expr.Value)
	case "BOOLEAN":
		return psql.Arg(expr.Value)
	case "NULL":
		return psql.Raw("NULL")
	default:
		// Mặc định xử lý như parameter để đảm bảo an toàn
		return psql.Arg(expr.Value)
	}
}

func handleColumn(expr *Expression) bob.Expression {
	if strings.Contains(expr.ColumnRef, ".") {
		return psql.Raw(expr.ColumnRef)
	}
	return psql.Quote(expr.ColumnRef)
}

func handleFunction(expr *Expression) bob.Expression {
	funcName := safeFunction(expr.Function)
	if funcName == "" {
		// Return nil or a safe default if function is not allowed
		return psql.Raw("NULL")
	}

	args := make([]any, 0, len(expr.Arguments))
	for _, arg := range expr.Arguments {
		args = append(args, buildExpression(&arg))
	}
	return psql.F(funcName, args...)()
}

func handleArithmetic(expr *Expression) bob.Expression {
	if len(expr.Arguments) != 2 {
		// Arithmetic expressions need exactly 2 operands
		return nil
	}

	left := buildExpression(&expr.Arguments[0])
	right := buildExpression(&expr.Arguments[1])

	// Whitelist các toán tử hợp lệ
	validOps := map[string]bool{"+": true, "-": true, "*": true, "/": true, "%": true}
	if !validOps[expr.Operator] {
		return nil
	}

	// Use Raw with proper formatting
	return psql.Raw(fmt.Sprintf("(%s %s %s)", left, expr.Operator, right))
}

// buildFilterCondition builds a single condition
func buildFilterCondition(condition FilterCondition) bob.Expression {
	// Handle the left expression
	var leftExpr bob.Expression

	// Xử lý vế trái theo thứ tự ưu tiên
	switch {
	case condition.LeftExprRaw != "":
		if SafeMode && !SafeModeFeaturesAllowed("raw_conditions") {
			return psql.Raw("FALSE") // Block trong SafeMode
		}
		if !isAllowedRawExpression(condition.LeftExprRaw) {
			return psql.Raw("FALSE") // Block biểu thức không an toàn
		}
		leftExpr = psql.Raw(condition.LeftExprRaw)

	case condition.LeftExpr != nil:
		leftExpr = buildExpression(condition.LeftExpr)
		if leftExpr == nil {
			return nil
		}

	case condition.Field != "":
		// Handle table-qualified columns without adding extra quotes
		if strings.Contains(condition.Field, ".") {
			leftExpr = psql.Raw(condition.Field)
		} else {
			leftExpr = psql.Quote(condition.Field)
		}

		// Apply function if specified
		if condition.Function != "" {
			funcName := safeFunction(condition.Function)
			if funcName == "" {
				// Return nil or a safe default if function is not allowed
				return psql.Raw("FALSE") // Return a safe condition that won't match anything
			}

			// Apply the function to the field
			leftExpr = psql.F(funcName, leftExpr)()
		}

	default:
		return nil // Không có vế trái
	}

	// Determine right-hand expression
	var rightExpr bob.Expression

	// Xử lý vế phải theo thứ tự ưu tiên
	switch {
	case condition.RightExprRaw != "":
		if SafeMode && !SafeModeFeaturesAllowed("raw_conditions") {
			return psql.Raw("FALSE") // Block trong SafeMode
		}
		if !isAllowedRawExpression(condition.RightExprRaw) {
			return psql.Raw("FALSE") // Block biểu thức không an toàn
		}
		rightExpr = psql.Raw(condition.RightExprRaw)

	case condition.RightExpr != nil:
		rightExpr = buildExpression(condition.RightExpr)
		if rightExpr == nil {
			return nil
		}

	case condition.RightField != "":
		// Xử lý RightField an toàn
		parts := strings.Split(condition.RightField, ".")
		if len(parts) > 1 {
			// Nếu có dấu chấm, chỉ chấp nhận tối đa 2 phần (table.column)
			if len(parts) > 2 {
				return psql.Raw("FALSE") // Trả về điều kiện không khớp nếu định dạng không hợp lệ
			}

			// Kiểm tra xem các phần có chứa ký tự đặc biệt không
			for _, part := range parts {
				if !isValidIdentifier(part) {
					return psql.Raw("FALSE") // Trả về điều kiện không khớp nếu có ký tự không hợp lệ
				}
			}

			// Nếu hợp lệ, sử dụng dạng table.column
			rightExpr = psql.Raw(condition.RightField)
		} else {
			// Nếu không có dấu chấm, kiểm tra tính hợp lệ của định danh
			if !isValidIdentifier(condition.RightField) {
				return psql.Raw("FALSE") // Trả về điều kiện không khớp nếu có ký tự không hợp lệ
			}
			rightExpr = psql.Quote(condition.RightField)
		}

	case condition.Subquery != nil:
		if SafeMode && !SafeModeFeaturesAllowed("subqueries") {
			return psql.Raw("FALSE") // Block subqueries in SafeMode if not allowed
		}
		rightExpr = condition.Subquery.BuildQuery()

	default:
		rightExpr = psql.Arg(condition.Value)
	}

	// Build the condition based on operator
	switch strings.ToUpper(condition.Operator) {
	case "EQ":
		return dialect.NewExpression(leftExpr).EQ(rightExpr)
	case "NEQ":
		return dialect.NewExpression(leftExpr).NE(rightExpr)
	case "GT":
		return dialect.NewExpression(leftExpr).GT(rightExpr)
	case "LT":
		return dialect.NewExpression(leftExpr).LT(rightExpr)
	case "GTE":
		return dialect.NewExpression(leftExpr).GTE(rightExpr)
	case "LTE":
		return dialect.NewExpression(leftExpr).LTE(rightExpr)
	case "IN":
		if len(condition.GroupedIn) > 0 {
			groups := make([]bob.Expression, len(condition.GroupedIn))
			for i, group := range condition.GroupedIn {
				groups[i] = psql.ArgGroup(group...)
			}
			return dialect.NewExpression(leftExpr).In(groups...)
		}

		return dialect.NewExpression(leftExpr).In(rightExpr)
	case "LIKE":
		return dialect.NewExpression(leftExpr).Like(rightExpr)
	case "ILIKE":
		return dialect.NewExpression(leftExpr).ILike(rightExpr)
	case "ISNULL":
		return dialect.NewExpression(leftExpr).IsNull()
	case "ISNOTNULL":
		return dialect.NewExpression(leftExpr).IsNotNull()
	default:
		return nil
	}
}

// isValidIdentifier checks if an SQL identifier is valid
func isValidIdentifier(id string) bool {
	// Only allow letters, numbers, underscores, and some common special characters in SQL identifiers
	for _, char := range id {
		if !unicode.IsLetter(char) && !unicode.IsDigit(char) && char != '_' {
			return false
		}
	}
	return true
}

// isAllowedRawExpression kiểm tra xem biểu thức raw có an toàn không
func isAllowedRawExpression(expr string) bool {
	// Kiểm tra các pattern nguy hiểm
	dangerousPatterns := []string{
		"DELETE", "UPDATE", "INSERT", "DROP", "ALTER", "CREATE",
		"TRUNCATE", "GRANT", "REVOKE", "EXECUTE", "--", "/*",
		"UNION", "INTO OUTFILE", "INFORMATION_SCHEMA",
	}

	exprUpper := strings.ToUpper(expr)
	for _, pattern := range dangerousPatterns {
		if strings.Contains(exprUpper, pattern) {
			return false
		}
	}

	// Có thể thêm các kiểm tra khác ở đây

	return true
}

// buildCaseExpression builds a CASE expression
func buildCaseExpression(caseWhen *CaseWhen) bob.Expression {
	if caseWhen == nil {
		return nil
	}

	caseChain := psql.Case()

	for _, cond := range caseWhen.Conditions {
		whereExpr := buildLogicalExpression(&cond.When)
		caseChain = caseChain.When(whereExpr, psql.Arg(cond.Then))
	}

	if caseWhen.Else != nil {
		return caseChain.Else(psql.Arg(caseWhen.Else))
	}

	return caseChain.End()
}

func (q *DynamicQuery) BuildQuery() bob.Query {
	if err := q.Validate(); err != nil {
		// Create a query that will fail with a clear error message when executed
		return psql.Select(sm.Columns(psql.Raw(fmt.Sprintf("ERROR: %s", err.Error()))))
	}

	var modifiers []bob.Mod[*dialect.SelectQuery]

	if len(q.CTEs) > 0 && (!SafeMode || SafeModeFeaturesAllowed("ctes")) {
		for _, cte := range q.CTEs {
			// Don't manually quote, use psql.Quote for CTE names
			withMod := sm.With(psql.Quote(cte.Name).String(), cte.Columns...).As(cte.Query.BuildQuery())
			modifiers = append(modifiers, withMod)
		}
	}

	// Handle DISTINCT/DISTINCT ON
	if q.Distinct != nil && *q.Distinct {
		if len(q.DistinctOn) > 0 {
			// Quote DISTINCT ON fields
			distinctFields := make([]any, len(q.DistinctOn))
			for i, field := range q.DistinctOn {
				if strings.Contains(field, ".") {
					distinctFields[i] = psql.Raw(field)
				} else {
					distinctFields[i] = psql.Quote(field)
				}
			}
			modifiers = append(modifiers, sm.Distinct(distinctFields...))
		} else {
			modifiers = append(modifiers, sm.Distinct())
		}
	}

	// Handle SELECT fields
	selectCols := make([]any, 0)

	// Process regular fields, CASE expressions and Raw SQL
	for _, sel := range q.Selects {
		if sel.RawSQL != "" && (!SafeMode || SafeModeFeaturesAllowed("raw_sql")) {
			expr := psql.Raw(sel.RawSQL)
			selectCols = append(selectCols, expr)
		} else if sel.Case != nil {
			expr := buildCaseExpression(sel.Case)
			if sel.Alias != "" {
				expr = dialect.NewExpression(expr).As(sel.Alias)
			}
			selectCols = append(selectCols, expr)
		} else if sel.Function != "" {
			var expr bob.Expression

			// Sanitize function name to prevent injection
			funcName := safeFunction(sel.Function)
			if funcName == "" {
				// Skip this field if function is invalid
				continue
			}

			args := parseFunctionArgs(sel.Field)
			expr = psql.F(funcName, args...)()

			if sel.Alias != "" {
				expr = dialect.NewExpression(expr).As(sel.Alias)
			}
			selectCols = append(selectCols, expr)
		} else if sel.Alias != "" {
			// Handle fields with aliases
			if strings.Contains(sel.Field, ".") {
				expr := psql.Raw(sel.Field)
				selectCols = append(selectCols, expr.As(sel.Alias))
			} else {
				expr := psql.Quote(sel.Field)
				selectCols = append(selectCols, expr.As(sel.Alias))
			}
		} else {
			if sel.Field == "*" {
				selectCols = append(selectCols, psql.Raw("*"))
			} else if strings.Contains(sel.Field, ".") {
				// Handle table.column format - don't add quotes
				selectCols = append(selectCols, psql.Raw(sel.Field))
			} else {
				// Normal case - add quotes
				selectCols = append(selectCols, psql.Quote(sel.Field))
			}
		}
	}

	// Process aggregations
	for _, agg := range q.Aggregations {
		var expr bob.Expression

		// Sanitize function name
		funcName := safeFunction(agg.Function)
		if funcName == "" {
			// Skip this aggregation if function is invalid
			continue
		}

		var field bob.Expression
		if agg.Field == "*" {
			field = psql.Raw("*")
		} else if strings.Contains(agg.Field, ".") {
			field = psql.Raw(agg.Field)
		} else {
			field = psql.Quote(agg.Field)
		}

		// Special handling for RANK
		if strings.ToUpper(funcName) == "RANK" || strings.ToUpper(funcName) == "ROW_NUMBER" || strings.ToUpper(funcName) == "DENSE_RANK" {
			field = psql.Raw("") // Set field to empty string for window functions with no args
		}

		// Create the function with or without DISTINCT
		var f mods.Moddable[*dialect.Function]
		if agg.Distinct != nil && *agg.Distinct {
			// Create the function and set Distinct to true
			fn := dialect.NewFunction(funcName, field)
			fn.Distinct = true
			f = func(fnMods ...bob.Mod[*dialect.Function]) *dialect.Function {
				for _, mod := range fnMods {
					mod.Apply(fn)
				}
				return fn
			}
		} else {
			f = psql.F(funcName, field)
		}

		// Handle window functions
		if agg.Over != nil && !agg.Over.IsEmpty() && (!SafeMode || SafeModeFeaturesAllowed("window_functions")) {
			var winMods []bob.Mod[*clause.Window]

			if agg.Over.BasedOn != "" {
				winMods = append(winMods, wm.BasedOn(agg.Over.BasedOn))
			}

			for _, p := range agg.Over.PartitionBy {
				if strings.Contains(p, ".") {
					winMods = append(winMods, wm.PartitionBy(psql.Raw(p)))
				} else {
					winMods = append(winMods, wm.PartitionBy(psql.Quote(p)))
				}
			}

			// Handle OrderBy from window specification
			for _, sort := range agg.Over.OrderBy {
				var orderExpr bob.Expression
				if strings.Contains(sort.Field, ".") {
					orderExpr = psql.Raw(sort.Field)
				} else {
					orderExpr = psql.Quote(sort.Field)
				}

				// Create OrderBy for window specification
				orderMod := wm.OrderBy(orderExpr)

				// Determine sort direction
				if strings.ToUpper(sort.Order) == "DESC" {
					orderMod = orderMod.Desc()
				} else {
					orderMod = orderMod.Asc() // Default to ASC
				}

				// Add collation if specified
				if sort.Collate != "" && (!SafeMode || SafeModeFeaturesAllowed("collation")) {
					orderMod = orderMod.Collate(sort.Collate)
				}

				winMods = append(winMods, orderMod)
			}

			expr = f(fm.Over(winMods...))
		} else {
			expr = f()
		}

		if agg.Alias != "" {
			expr = dialect.NewExpression(expr).As(agg.Alias)
		}

		selectCols = append(selectCols, expr)
	}

	modifiers = append(modifiers, sm.Columns(selectCols...))

	// Handle FROM with subquery support
	if q.Table != "" {
		fromChain := sm.From(psql.Quote(q.Table))
		if q.Alias != "" {
			fromChain = fromChain.As(q.Alias)
		}
		modifiers = append(modifiers, fromChain)
	}

	// Handle JOINs
	for _, join := range q.Joins {
		var joinMod bob.Mod[*dialect.SelectQuery]

		var joinSource interface{}
		if join.Subquery != nil && (!SafeMode || SafeModeFeaturesAllowed("subqueries")) {
			joinSource = join.Subquery.BuildQuery()
		} else {
			joinSource = psql.Quote(join.Table)
		}

		switch strings.ToUpper(join.Type) {
		case "LEFT":
			joinChain := sm.LeftJoin(joinSource)
			if join.Alias != "" {
				joinChain = joinChain.As(join.Alias)
			}

			if len(join.Using) > 0 {
				joinMod = joinChain.Using(join.Using...)
			} else if join.OnLogic != nil {
				joinMod = joinChain.On(buildLogicalExpression(join.OnLogic))
			}

		case "RIGHT":
			joinChain := sm.RightJoin(joinSource)
			if join.Alias != "" {
				joinChain = joinChain.As(join.Alias)
			}

			if len(join.Using) > 0 {
				joinMod = joinChain.Using(join.Using...)
			} else if join.OnLogic != nil {
				joinMod = joinChain.On(buildLogicalExpression(join.OnLogic))
			}

		case "INNER":
			joinChain := sm.InnerJoin(joinSource)
			if join.Alias != "" {
				joinChain = joinChain.As(join.Alias)
			}

			if len(join.Using) > 0 {
				joinMod = joinChain.Using(join.Using...)
			} else if join.OnLogic != nil {
				joinMod = joinChain.On(buildLogicalExpression(join.OnLogic))
			}

		case "FULL":
			joinChain := sm.FullJoin(joinSource)
			if join.Alias != "" {
				joinChain = joinChain.As(join.Alias)
			}

			if len(join.Using) > 0 {
				joinMod = joinChain.Using(join.Using...)
			} else if join.OnLogic != nil {
				joinMod = joinChain.On(buildLogicalExpression(join.OnLogic))
			}

		case "CROSS":
			crossJoin := sm.CrossJoin(joinSource)
			if join.Alias != "" {
				joinMod = crossJoin.As(join.Alias)
			} else {
				joinMod = crossJoin
			}
		}

		if joinMod != nil {
			modifiers = append(modifiers, joinMod)
		}
	}

	// Handle WHERE
	if q.WhereLogic != nil {
		whereExpr := buildLogicalExpression(q.WhereLogic)
		if whereExpr != nil {
			modifiers = append(modifiers, sm.Where(whereExpr))
		}
	}

	// Handle GROUP BY
	if len(q.GroupBy) > 0 {
		for _, g := range q.GroupBy {
			var groupExpr any
			if strings.Contains(g, ".") {
				groupExpr = psql.Raw(g)
			} else {
				groupExpr = psql.Quote(g)
			}
			modifiers = append(modifiers, sm.GroupBy(groupExpr))
		}
	}

	// Handle HAVING
	if q.HavingLogic != nil {
		havingExpr := buildLogicalExpression(q.HavingLogic)
		if havingExpr != nil {
			modifiers = append(modifiers, sm.Having(havingExpr))
		}
	}

	// Handle ORDER BY with collation and explicit direction
	for _, sort := range q.Sort {
		var orderExpr bob.Expression
		if strings.Contains(sort.Field, ".") {
			orderExpr = psql.Raw(sort.Field)
		} else {
			orderExpr = psql.Quote(sort.Field)
		}

		orderBy := sm.OrderBy(orderExpr)
		if sort.Collate != "" && (!SafeMode || SafeModeFeaturesAllowed("collation")) {
			orderBy = orderBy.Collate(sort.Collate)
		}
		switch strings.ToUpper(sort.Order) {
		case "DESC":
			orderBy = orderBy.Desc()
		case "ASC", "":
			orderBy = orderBy.Asc()
		default:
			orderBy = orderBy.Asc() // Default to ASC for safety
		}
		modifiers = append(modifiers, orderBy)
	}

	// Handle LIMIT and OFFSET
	if q.Limit > 0 {
		modifiers = append(modifiers, sm.Limit(q.Limit))
	}
	if q.Offset > 0 {
		modifiers = append(modifiers, sm.Offset(q.Offset))
	}

	// Handle locking if specified
	if q.Lock != nil && (!SafeMode || SafeModeFeaturesAllowed("locking")) {
		switch q.Lock.Type {
		case "UPDATE":
			lockClause := sm.ForUpdate(q.Lock.Of...)
			if q.Lock.WaitMode == "SKIP LOCKED" {
				lockClause = lockClause.SkipLocked()
			} else if q.Lock.WaitMode == "NOWAIT" {
				lockClause = lockClause.NoWait()
			}
			modifiers = append(modifiers, lockClause)
		case "SHARE":
			lockClause := sm.ForShare(q.Lock.Of...)
			if q.Lock.WaitMode == "SKIP LOCKED" {
				lockClause = lockClause.SkipLocked()
			} else if q.Lock.WaitMode == "NOWAIT" {
				lockClause = lockClause.NoWait()
			}
			modifiers = append(modifiers, lockClause)
		}
	}

	// Handle UNIONs
	for _, union := range q.UnionQueries {
		if !SafeMode || SafeModeFeaturesAllowed("unions") {
			switch strings.ToUpper(union.Type) {
			case "UNION":
				modifiers = append(modifiers, sm.Union(union.Query.BuildQuery()))
			case "UNION ALL":
				modifiers = append(modifiers, sm.UnionAll(union.Query.BuildQuery()))
			}
		}
	}

	// Create final query with all modifiers
	return psql.Select(modifiers...)
}
