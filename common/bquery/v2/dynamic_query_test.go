package bqueryv2

import (
	"bytes"
	"context"
	"testing"

	"github.com/stephenafamo/bob"
	"github.com/stephenafamo/bob/dialect/psql/dialect"
	"github.com/stretchr/testify/assert"
	pg_query "github.com/wasilibs/go-pgquery"
)

func formatter(s string) (string, error) {
	aTree, err := pg_query.Parse(s)
	if err != nil {
		return "", err
	}
	return pg_query.Deparse(aTree)
}

// Helper function to write SQL and args
func writeSQL(t *testing.T, query bob.Query) (string, []any, error) {
	t.Helper()

	var buf bytes.Buffer
	args, err := query.WriteSQL(context.Background(), &buf, dialect.Dialect, 1)
	if err != nil {
		return "", nil, err
	}

	// Try to format the SQL if it's valid
	rawSQL := buf.String()
	formattedSQL, err := formatter(rawSQL)
	if err != nil {
		return rawSQL, args, err
	}

	return formattedSQL, args, nil
}

func TestBuildQuery_BasicSelect(t *testing.T) {
	q := &DynamicQuery{Table: "users", Alias: "u"}
	sql, args, err := writeSQL(t, q.BuildQuery())
	assert.NoError(t, err)
	assert.Empty(t, args)

	expected, err := formatter(`SELECT * FROM users u`)
	assert.NoError(t, err)
	assert.Equal(t, expected, sql)
}

func TestBuildQuery_SelectWithFields(t *testing.T) {
	q := &DynamicQuery{
		Table:   "users",
		Alias:   "u",
		Selects: []SelectField{{Field: "id"}, {Field: "name"}},
	}
	sql, args, err := writeSQL(t, q.BuildQuery())
	assert.NoError(t, err)
	assert.Empty(t, args)

	expected, err := formatter(`SELECT id, name FROM users u`)
	assert.NoError(t, err)
	assert.Equal(t, expected, sql)
}

func TestBuildQuery_Distinct(t *testing.T) {
	q := &DynamicQuery{Table: "users", Distinct: BoolPtr(true)}
	sql, args, err := writeSQL(t, q.BuildQuery())
	assert.NoError(t, err)
	assert.Empty(t, args)

	expected, err := formatter(`SELECT DISTINCT * FROM users`)
	assert.NoError(t, err)
	assert.Equal(t, expected, sql)
}

func TestBuildQuery_DistinctOn(t *testing.T) {
	q := &DynamicQuery{Table: "users", Distinct: BoolPtr(true), DistinctOn: []string{"name"}}
	sql, args, err := writeSQL(t, q.BuildQuery())
	assert.NoError(t, err)
	assert.Empty(t, args)

	expected, err := formatter(`SELECT DISTINCT ON (name) * FROM users`)
	assert.NoError(t, err)
	assert.Equal(t, expected, sql)
}

func TestBuildQuery_FilterEQ(t *testing.T) {
	q := &DynamicQuery{
		Table: "users",
		WhereLogic: &LogicalExpression{
			Type: "CONDITION",
			Condition: &FilterCondition{
				Field:    "age",
				Operator: "EQ",
				Value:    30,
			},
		},
	}
	sql, args, err := writeSQL(t, q.BuildQuery())
	assert.NoError(t, err)
	assert.Equal(t, []any{30}, args)

	expected, err := formatter(`SELECT * FROM users WHERE age = $1`)
	assert.NoError(t, err)
	assert.Equal(t, expected, sql)
}

func TestBuildQuery_FilterIN(t *testing.T) {
	q := &DynamicQuery{
		Table: "users",
		WhereLogic: &LogicalExpression{
			Type: "CONDITION",
			Condition: &FilterCondition{
				Field:    "id",
				Operator: "IN",
				Value:    []int{1, 2, 3},
			},
		},
	}
	sql, args, err := writeSQL(t, q.BuildQuery())
	assert.NoError(t, err)
	assert.Equal(t, []any{[]int{1, 2, 3}}, args)

	expected, err := formatter(`SELECT * FROM users WHERE id IN ($1)`)
	assert.NoError(t, err)
	assert.Equal(t, expected, sql)
}

func TestBuildQuery_FilterNestedAND(t *testing.T) {
	q := &DynamicQuery{
		Table: "users",
		WhereLogic: &LogicalExpression{
			Type: "AND",
			Expressions: []LogicalExpression{
				{
					Type: "CONDITION",
					Condition: &FilterCondition{
						Field:    "age",
						Operator: "GT",
						Value:    18,
					},
				},
				{
					Type: "CONDITION",
					Condition: &FilterCondition{
						Field:    "age",
						Operator: "LT",
						Value:    65,
					},
				},
			},
		},
	}
	sql, args, err := writeSQL(t, q.BuildQuery())
	assert.NoError(t, err)
	assert.Equal(t, []any{18, 65}, args)

	expected, err := formatter(`SELECT * FROM users WHERE age > $1 AND age < $2`)
	assert.NoError(t, err)
	assert.Equal(t, expected, sql)
}

func TestBuildQuery_GroupBy(t *testing.T) {
	q := &DynamicQuery{Table: "users", GroupBy: []string{"department"}}
	sql, args, err := writeSQL(t, q.BuildQuery())
	assert.NoError(t, err)
	assert.Empty(t, args)

	expected, err := formatter(`SELECT * FROM users GROUP BY department`)
	assert.NoError(t, err)
	assert.Equal(t, expected, sql)
}

func TestBuildQuery_AggregationAVG(t *testing.T) {
	q := &DynamicQuery{
		Table: "users",
		Aggregations: []Aggregation{
			{Field: "age", Function: "AVG", Alias: "avg_age"},
		},
	}
	sql, args, err := writeSQL(t, q.BuildQuery())
	assert.NoError(t, err)
	assert.Empty(t, args)

	expected, err := formatter(`SELECT avg(age) AS avg_age FROM users`)
	assert.NoError(t, err)
	assert.Equal(t, expected, sql)
}

func TestBuildQuery_AggregationCountDistinct(t *testing.T) {
	q := &DynamicQuery{
		Table: "users",
		Aggregations: []Aggregation{
			{Field: "age", Function: "COUNT", Alias: "count_distinct_age", Distinct: BoolPtr(true)},
		},
	}
	sql, args, err := writeSQL(t, q.BuildQuery())
	assert.NoError(t, err)
	assert.Empty(t, args)

	expected, err := formatter(`SELECT count(DISTINCT age) AS count_distinct_age FROM users`)
	assert.NoError(t, err)
	assert.Equal(t, expected, sql)
}

func TestBuildQuery_AggregationWindowFunction(t *testing.T) {
	q := &DynamicQuery{
		Table: "users",
		Aggregations: []Aggregation{
			{
				Field:    "salary",
				Function: "RANK",
				Alias:    "rank",
				Over: &WindowSpecification{
					PartitionBy: []string{"department"},
					OrderBy:     []SortCriteria{{Field: "salary", Order: "DESC"}},
				},
			},
		},
	}
	sql, args, err := writeSQL(t, q.BuildQuery())
	assert.NoError(t, err)
	assert.Empty(t, args)

	expected, err := formatter(`SELECT rank() OVER (PARTITION BY department ORDER BY salary DESC) AS rank FROM users`)
	assert.NoError(t, err)
	assert.Equal(t, expected, sql)
}

func TestBuildQuery_Sort(t *testing.T) {
	q := &DynamicQuery{
		Table: "users",
		Sort: []SortCriteria{
			{Field: "name", Order: "ASC"},
			{Field: "age", Order: "DESC", Collate: "en_US"},
		},
	}
	sql, args, err := writeSQL(t, q.BuildQuery())
	assert.NoError(t, err)
	assert.Empty(t, args)

	expected, err := formatter(`SELECT * FROM users ORDER BY name ASC, age COLLATE "en_US" DESC`)
	assert.NoError(t, err)
	assert.Equal(t, expected, sql)
}

func TestBuildQuery_LimitOffset(t *testing.T) {
	q := &DynamicQuery{Table: "users", Limit: 10, Offset: 20}
	sql, args, err := writeSQL(t, q.BuildQuery())
	assert.NoError(t, err)
	assert.Empty(t, args)

	expected, err := formatter(`SELECT * FROM users LIMIT 10 OFFSET 20`)
	assert.NoError(t, err)
	assert.Equal(t, expected, sql)
}

func TestBuildQuery_SelectWithFunction(t *testing.T) {
	q := &DynamicQuery{
		Table: "users",
		Selects: []SelectField{
			{Field: "name", Function: "UPPER", Alias: "upper_name"},
		},
	}
	sql, args, err := writeSQL(t, q.BuildQuery())
	assert.NoError(t, err)
	assert.Empty(t, args)

	expected, err := formatter(`SELECT upper(name) AS upper_name FROM users`)
	assert.NoError(t, err)
	assert.Equal(t, expected, sql)
}

func TestBuildQuery_SelectWithCase(t *testing.T) {
	q := &DynamicQuery{
		Table: "users",
		Selects: []SelectField{
			{
				Case: &CaseWhen{
					Conditions: []CaseCondition{
						{
							When: LogicalExpression{
								Type: "CONDITION",
								Condition: &FilterCondition{
									Field:    "age",
									Operator: "LT",
									Value:    18,
								},
							},
							Then: "Minor",
						},
						{
							When: LogicalExpression{
								Type: "CONDITION",
								Condition: &FilterCondition{
									Field:    "age",
									Operator: "GTE",
									Value:    18,
								},
							},
							Then: "Adult",
						},
					},
					Else: "Unknown",
				},
				Alias: "age_group",
			},
		},
	}
	sql, args, err := writeSQL(t, q.BuildQuery())
	assert.NoError(t, err)
	assert.Equal(t, []any{18, "Minor", 18, "Adult", "Unknown"}, args)

	expected, err := formatter(`SELECT CASE WHEN age < $1 THEN $2 WHEN age >= $3 THEN $4 ELSE $5 END AS age_group FROM users`)
	assert.NoError(t, err)
	assert.Equal(t, expected, sql)
}

func TestBuildQuery_InnerJoin(t *testing.T) {
	q := &DynamicQuery{
		Table: "users",
		Joins: []JoinInfo{
			{
				Table: "departments",
				Alias: "d",
				Type:  "INNER",
				OnLogic: &LogicalExpression{
					Type: "CONDITION",
					Condition: &FilterCondition{
						Field:      "users.dept_id",
						Operator:   "EQ",
						RightField: "d.id", // Sử dụng RightField thay vì Value
					},
				},
			},
		},
	}
	sql, args, err := writeSQL(t, q.BuildQuery())
	assert.NoError(t, err)
	assert.Empty(t, args)

	expected, err := formatter(`SELECT * FROM users JOIN departments d ON users.dept_id = d.id`)
	assert.NoError(t, err)
	assert.Equal(t, expected, sql)
}

func TestBuildQuery_LeftJoinUsing(t *testing.T) {
	q := &DynamicQuery{
		Table: "users",
		Joins: []JoinInfo{
			{Table: "departments", Type: "LEFT", Using: []string{"dept_id"}},
		},
	}
	sql, args, err := writeSQL(t, q.BuildQuery())
	assert.NoError(t, err)
	assert.Empty(t, args)

	expected, err := formatter(`SELECT * FROM users LEFT JOIN departments USING (dept_id)`)
	assert.NoError(t, err)
	assert.Equal(t, expected, sql)
}

func TestBuildQuery_CTE(t *testing.T) {
	cte := CTEInfo{
		Name: "active_users",
		Query: DynamicQuery{
			Table: "users",
			WhereLogic: &LogicalExpression{
				Type: "CONDITION",
				Condition: &FilterCondition{
					Field:    "status",
					Operator: "EQ",
					Value:    "active",
				},
			},
		},
	}
	q := &DynamicQuery{
		Table: "active_users",
		CTEs:  []CTEInfo{cte},
	}
	sql, args, err := writeSQL(t, q.BuildQuery())
	assert.NoError(t, err)
	assert.Equal(t, []any{"active"}, args)

	expected, err := formatter(`WITH active_users AS (SELECT * FROM users WHERE status = $1) SELECT * FROM active_users`)
	assert.NoError(t, err)
	assert.Equal(t, expected, sql)
}

func TestBuildQuery_LockUpdate(t *testing.T) {
	q := &DynamicQuery{
		Table: "users",
		Lock:  &LockInfo{Type: "UPDATE", WaitMode: "NOWAIT"},
	}
	sql, args, err := writeSQL(t, q.BuildQuery())
	assert.NoError(t, err)
	assert.Empty(t, args)

	expected, err := formatter(`SELECT * FROM users FOR UPDATE NOWAIT`)
	assert.NoError(t, err)
	assert.Equal(t, expected, sql)
}

func TestBuildQuery_UnionAll(t *testing.T) {
	unionQuery := DynamicQuery{Table: "archived_users"}
	q := &DynamicQuery{
		Table: "users",
		UnionQueries: []UnionInfo{
			{Type: "UNION ALL", Query: unionQuery},
		},
	}
	sql, args, err := writeSQL(t, q.BuildQuery())
	assert.NoError(t, err)
	assert.Empty(t, args)

	expected, err := formatter(`SELECT * FROM users UNION ALL SELECT * FROM archived_users`)
	assert.NoError(t, err)
	assert.Equal(t, expected, sql)
}

func TestBuildQuery_Integration(t *testing.T) {
	q := &DynamicQuery{
		Table:    "users",
		Alias:    "u",
		Distinct: BoolPtr(true),
		WhereLogic: &LogicalExpression{
			Type: "CONDITION",
			Condition: &FilterCondition{
				Field:    "age",
				Operator: "GT",
				Value:    18,
			},
		},
		GroupBy: []string{"department"},
		Aggregations: []Aggregation{
			{Field: "salary", Function: "SUM", Alias: "total_salary"},
		},
		Sort: []SortCriteria{
			{Field: "total_salary", Order: "DESC"},
		},
		Limit: 10,
	}
	sql, args, err := writeSQL(t, q.BuildQuery())
	assert.NoError(t, err)
	assert.Equal(t, []any{18}, args)

	expected, err := formatter(`SELECT DISTINCT sum(salary) AS total_salary FROM users u WHERE age > $1 GROUP BY department ORDER BY total_salary DESC LIMIT 10`)
	assert.NoError(t, err)
	assert.Equal(t, expected, sql)
}

func TestBuildQuery_JoinWithSubquery(t *testing.T) {
	subquery := &DynamicQuery{
		Table:   "departments",
		Selects: []SelectField{{Field: "id"}, {Field: "name"}},
		WhereLogic: &LogicalExpression{
			Type: "CONDITION",
			Condition: &FilterCondition{
				Field:    "active",
				Operator: "EQ",
				Value:    true,
			},
		},
	}

	q := &DynamicQuery{
		Table: "users",
		Joins: []JoinInfo{
			{
				Subquery: subquery,
				Alias:    "d",
				Type:     "INNER",
				OnLogic: &LogicalExpression{
					Type: "CONDITION",
					Condition: &FilterCondition{
						Field:      "users.dept_id",
						Operator:   "EQ",
						RightField: "d.id",
					},
				},
			},
		},
	}

	sql, args, err := writeSQL(t, q.BuildQuery())
	assert.NoError(t, err)
	assert.Equal(t, []any{true}, args)

	expected, err := formatter(`SELECT * FROM users JOIN (SELECT id, name FROM departments WHERE active = $1) d ON users.dept_id = d.id`)
	assert.NoError(t, err)
	assert.Equal(t, expected, sql)
}

func TestBuildQuery_FilterWithSubquery(t *testing.T) {
	subquery := &DynamicQuery{
		Table:   "departments",
		Selects: []SelectField{{Field: "id"}},
		WhereLogic: &LogicalExpression{
			Type: "CONDITION",
			Condition: &FilterCondition{
				Field:    "name",
				Operator: "LIKE",
				Value:    "%Sales%",
			},
		},
	}

	q := &DynamicQuery{
		Table: "users",
		WhereLogic: &LogicalExpression{
			Type: "CONDITION",
			Condition: &FilterCondition{
				Field:    "dept_id",
				Operator: "IN",
				Subquery: subquery,
			},
		},
	}

	sql, args, err := writeSQL(t, q.BuildQuery())
	assert.NoError(t, err)
	assert.Equal(t, []any{"%Sales%"}, args)

	expected, err := formatter(`SELECT * FROM users WHERE dept_id IN (SELECT id FROM departments WHERE name LIKE $1)`)
	assert.NoError(t, err)
	assert.Equal(t, expected, sql)
}

func TestBuildQuery_SelectWithRawSQL(t *testing.T) {
	q := &DynamicQuery{
		Table: "users",
		Selects: []SelectField{
			{Field: "id"},
			{RawSQL: "COUNT(*) OVER () as total_count"},
		},
	}

	sql, args, err := writeSQL(t, q.BuildQuery())
	assert.NoError(t, err)
	assert.Empty(t, args)

	expected, err := formatter(`SELECT id, count(*) OVER () AS total_count FROM users`)
	assert.NoError(t, err)
	assert.Equal(t, expected, sql)
}

func TestBuildQuery_FilterWithFunction(t *testing.T) {
	q := &DynamicQuery{
		Table: "users",
		WhereLogic: &LogicalExpression{
			Type: "CONDITION",
			Condition: &FilterCondition{
				Field:    "name",
				Function: "UPPER",
				Operator: "EQ",
				Value:    "JOHN",
			},
		},
	}

	sql, args, err := writeSQL(t, q.BuildQuery())
	assert.NoError(t, err)
	assert.Equal(t, []any{"JOHN"}, args)

	expected, err := formatter(`SELECT * FROM users WHERE upper(name) = $1`)
	assert.NoError(t, err)
	assert.Equal(t, expected, sql)
}
