package bqueryv2

// LogicalExpression represents a complete logical expression tree
type LogicalExpression struct {
	Type        string              `json:"type"`                  // "AND", "OR", or "CONDITION"
	Condition   *FilterCondition    `json:"condition,omitempty"`   // Single condition (when Type="CONDITION")
	Expressions []LogicalExpression `json:"expressions,omitempty"` // Child expressions (when Type="AND" or "OR")
}

// FilterCondition represents a single filter condition
type FilterCondition struct {
	Field     string          `json:"field,omitempty"`
	Operator  string          `json:"operator,omitempty"`
	Value     interface{}     `json:"value,omitempty"`
	Function  string          `json:"function,omitempty"`
	GroupedIn [][]interface{} `json:"grouped_in,omitempty"`

	// Tách riêng field cho SQL phức tạp ở vế phải
	RightField   string        `json:"right_field,omitempty"`    // Cho column đơn giản
	RightExprRaw string        `json:"right_expr_raw,omitempty"` // Cho SQL phức tạp
	RightExpr    *Expression   `json:"right_expr,omitempty"`     // Cho biểu thức cấu trúc
	Subquery     *DynamicQuery `json:"subquery,omitempty"`       // Cho subquery

	// Tương tự cho left side
	LeftExprRaw string      `json:"left_expr_raw,omitempty"` // Cho SQL phức tạp ở vế trái
	LeftExpr    *Expression `json:"left_expr,omitempty"`     // Cho biểu thức cấu trúc ở vế trái
}

// Expression represents an expression (arithmetic, function, column, or literal)
type Expression struct {
	Type        string       `json:"type"`                   // "ARITHMETIC", "FUNCTION", "COLUMN", "LITERAL"
	LiteralType string       `json:"literal_type,omitempty"` // "STRING", "NUMBER", "RAW", "BOOLEAN", "NULL"
	Operator    string       `json:"operator,omitempty"`     // "+", "-", "*", "/"
	Function    string       `json:"function,omitempty"`
	Arguments   []Expression `json:"arguments,omitempty"`
	ColumnRef   string       `json:"column_ref,omitempty"`
	Value       interface{}  `json:"value,omitempty"`
}

// DynamicQuery represents a complete dynamic query structure
type DynamicQuery struct {
	Table        string             `json:"table"`
	Alias        string             `json:"alias,omitempty"`
	Distinct     *bool              `json:"distinct,omitempty"`
	DistinctOn   []string           `json:"distinct_on,omitempty"`
	WhereLogic   *LogicalExpression `json:"where_logic,omitempty"`
	HavingLogic  *LogicalExpression `json:"having_logic,omitempty"`
	GroupBy      []string           `json:"group_by"`
	Aggregations []Aggregation      `json:"aggregations"`
	Sort         []SortCriteria     `json:"sort"`
	Limit        int                `json:"limit"`
	Offset       int                `json:"offset"`
	Selects      []SelectField      `json:"selects"`
	Joins        []JoinInfo         `json:"joins"`
	CTEs         []CTEInfo          `json:"ctes,omitempty"`
	Lock         *LockInfo          `json:"lock,omitempty"`
	UnionQueries []UnionInfo        `json:"unions,omitempty"`
}

// SelectField allows specifying information for SELECT
type SelectField struct {
	Field    string    `json:"field"`
	Function string    `json:"function,omitempty"`
	Alias    string    `json:"alias,omitempty"`
	Format   string    `json:"format,omitempty"`
	Case     *CaseWhen `json:"case,omitempty"`
	RawSQL   string    `json:"raw_sql,omitempty"` // High injection risk in SafeMode
}

// CaseWhen represents a CASE expression
type CaseWhen struct {
	Conditions []CaseCondition `json:"conditions"`
	Else       interface{}     `json:"else,omitempty"`
}

type CaseCondition struct {
	When LogicalExpression `json:"when"`
	Then interface{}       `json:"then"`
}

// JoinInfo represents join information
type JoinInfo struct {
	Table    string             `json:"table"`
	Alias    string             `json:"alias,omitempty"`
	Type     string             `json:"type"`
	OnLogic  *LogicalExpression `json:"on_logic,omitempty"` // Logical expression for ON conditions
	Using    []string           `json:"using,omitempty"`
	Subquery *DynamicQuery      `json:"subquery,omitempty"` // Potential risk in SafeMode
}

// Aggregation represents aggregation operations
type Aggregation struct {
	Field    string               `json:"field"`
	Function string               `json:"function"`
	Alias    string               `json:"alias"`
	Distinct *bool                `json:"distinct,omitempty"`
	Over     *WindowSpecification `json:"over,omitempty"` // Potential risk in SafeMode
}

// SortCriteria represents sorting information
type SortCriteria struct {
	Field   string `json:"field"`
	Order   string `json:"order"`
	Collate string `json:"collate,omitempty"` // Potential risk in SafeMode
}

// WindowSpecification represents window clause
type WindowSpecification struct {
	Name        string         `json:"name,omitempty"`
	PartitionBy []string       `json:"partition_by"`
	OrderBy     []SortCriteria `json:"order_by"`
	BasedOn     string         `json:"based_on,omitempty"`
}

// CTEInfo represents WITH queries
type CTEInfo struct {
	Name    string       `json:"name"`
	Columns []string     `json:"columns,omitempty"`
	Query   DynamicQuery `json:"query"`
}

// LockInfo represents FOR UPDATE/SHARE
type LockInfo struct {
	Type     string   `json:"type"`
	Of       []string `json:"of,omitempty"`
	WaitMode string   `json:"wait_mode"`
}

// UnionInfo represents UNION operations
type UnionInfo struct {
	Type  string       `json:"type"`
	Query DynamicQuery `json:"query"`
}

func BoolPtr(b bool) *bool {
	return &b
}

// IsEmpty checks if a window specification is empty
func (w *WindowSpecification) IsEmpty() bool {
	return w == nil || (len(w.PartitionBy) == 0 && len(w.OrderBy) == 0 && w.BasedOn == "")
}
