package bqueryv2

import (
	"bytes"
	"net/http"
	"net/http/httptest"
	"testing"
)

func TestParse(t *testing.T) {
	tests := []struct {
		name        string
		contentType string
		body        string
		wantErr     bool
		errMsg      string
	}{
		{
			name:        "Valid JSON",
			contentType: "application/json",
			body:        `{"table":"users","selects":[{"field":"id"},{"field":"name"}]}`,
			wantErr:     false,
		},
		{
			name:        "Invalid content type",
			contentType: "text/plain",
			body:        `{"table":"users"}`,
			wantErr:     true,
			errMsg:      "content type must be application/json",
		},
		{
			name:        "Empty body",
			contentType: "application/json",
			body:        "",
			wantErr:     true,
			errMsg:      "request body is empty",
		},
		{
			name:        "Invalid JSON",
			contentType: "application/json",
			body:        `{"table":"users"`,
			wantErr:     true,
			errMsg:      "failed to parse JSON",
		},
		{
			name:        "Missing table name",
			contentType: "application/json",
			body:        `{"selects":[{"field":"id"}]}`,
			wantErr:     true,
			errMsg:      "table name is required",
		},
		{
			name:        "Invalid where_logic",
			contentType: "application/json",
			body:        `{"table":"users","where_logic":{"type":"UNKNOWN"}}`,
			wantErr:     true,
			errMsg:      "invalid where_logic",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Tạo request giả lập
			req := httptest.NewRequest(http.MethodPost, "/", bytes.NewBufferString(tt.body))
			req.Header.Set("Content-Type", tt.contentType)

			// Gọi hàm Parse
			query := &DynamicQuery{}
			err := Parse(req, query)

			// Kiểm tra kết quả
			if (err != nil) != tt.wantErr {
				t.Errorf("Parse() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if err != nil && tt.errMsg != "" && err.Error() != tt.errMsg {
				if !bytes.Contains([]byte(err.Error()), []byte(tt.errMsg)) {
					t.Errorf("Parse() error message = %v, want to contain %v", err.Error(), tt.errMsg)
				}
			}

			// Kiểm tra giá trị của query nếu không có lỗi
			if err == nil {
				if query.Table == "" {
					t.Error("Parse() should set Table field")
				}
			}
		})
	}
}

func TestParseWithComplexQuery(t *testing.T) {
	// Tạo một JSON phức tạp hơn để test
	complexJSON := `{
		"table": "users",
		"alias": "u",
		"distinct": true,
		"selects": [
			{"field": "id", "alias": "user_id"},
			{"field": "name", "alias": "user_name"}
		],
		"where_logic": {
			"type": "AND",
			"expressions": [
				{
					"type": "CONDITION",
					"condition": {
						"field": "age",
						"operator": ">",
						"value": 18
					}
				},
				{
					"type": "OR",
					"expressions": [
						{
							"type": "CONDITION",
							"condition": {
								"field": "city",
								"operator": "=",
								"value": "Hanoi"
							}
						},
						{
							"type": "CONDITION",
							"condition": {
								"field": "city",
								"operator": "=",
								"value": "HCM"
							}
						}
					]
				}
			]
		},
		"joins": [
			{
				"table": "orders",
				"alias": "o",
				"type": "LEFT",
				"on_logic": {
					"type": "CONDITION",
					"condition": {
						"field": "u.id",
						"operator": "=",
						"right_field": "o.user_id"
					}
				}
			}
		],
		"group_by": ["u.id"],
		"having_logic": {
			"type": "CONDITION",
			"condition": {
				"field": "COUNT(o.id)",
				"operator": ">",
				"value": 5
			}
		},
		"sort": [
			{"field": "u.name", "order": "ASC"}
		],
		"limit": 10,
		"offset": 0
	}`

	// Tạo request giả lập
	req := httptest.NewRequest(http.MethodPost, "/", bytes.NewBufferString(complexJSON))
	req.Header.Set("Content-Type", "application/json")

	// Gọi hàm Parse
	query := &DynamicQuery{}
	err := Parse(req, query)

	// Kiểm tra kết quả
	if err != nil {
		t.Errorf("Parse() error = %v", err)
		return
	}

	// Kiểm tra các giá trị được parse
	if query.Table != "users" {
		t.Errorf("Parse() table = %v, want %v", query.Table, "users")
	}

	if query.Alias != "u" {
		t.Errorf("Parse() alias = %v, want %v", query.Alias, "u")
	}

	if query.Distinct == nil || !*query.Distinct {
		t.Errorf("Parse() distinct = %v, want %v", query.Distinct, true)
	}

	if len(query.Selects) != 2 {
		t.Errorf("Parse() selects length = %v, want %v", len(query.Selects), 2)
	}

	if len(query.Joins) != 1 {
		t.Errorf("Parse() joins length = %v, want %v", len(query.Joins), 1)
	}

	if query.Limit != 10 {
		t.Errorf("Parse() limit = %v, want %v", query.Limit, 10)
	}
}

func TestParseWithComplexCTEsQuery(t *testing.T) {
	// Tạo một JSON phức tạp với CTEs để test
	complexCTEsJSON := `{
  "ctes": [
    {
      "name": "doctor_assignments",
      "columns": ["*", "rn"],
      "query": {
        "table": "person_assignment",
        "alias": "pa",
        "selects": [
          { "field": "pa.*" }
        ],
        "aggregations": [
          {
            "function": "ROW_NUMBER",
            "field": "",
            "alias": "rn",
            "over": {
              "partition_by": ["pa.person_id"],
              "order_by": [
                { "field": "pa.id", "order": "ASC" }
              ]
            }
          }
        ],
        "where_logic": {
          "type": "CONDITION",
          "condition": {
            "field": "pa.role",
            "operator": "EQ",
            "value": "doctor"
          }
        },
        "group_by": [],
        "joins": [],
        "sort": [],
        "limit": 1,
        "offset": 0
      }
    },
    {
      "name": "sale_assignments",
      "columns": ["*", "rn"],
      "query": {
        "table": "person_assignment",
        "alias": "pa",
        "selects": [
          { "field": "pa.*" }
        ],
        "aggregations": [
          {
            "function": "ROW_NUMBER",
            "field": "",
            "alias": "rn",
            "over": {
              "partition_by": ["pa.person_id"],
              "order_by": [
                { "field": "pa.id", "order": "ASC" }
              ]
            }
          }
        ],
        "where_logic": {
          "type": "CONDITION",
          "condition": {
            "field": "pa.role",
            "operator": "EQ",
            "value": "sale"
          }
        },
        "group_by": [],
        "joins": [],
        "sort": [],
        "limit": 1,
        "offset": 0
      }
    }
  ],
  "table": "person",
  "alias": "p",
  "selects": [
    { "field": "p.id" },
    { "field": "p.full_name" },
    { "field": "doctor_user.name", "alias": "doctor_name" },
    { "field": "sale_user.name", "alias": "sale_name" },
    {
      "case": {
        "conditions": [
          {
            "when": {
              "type": "CONDITION",
              "condition": {
                "field": "stage_parent.id",
                "operator": "IS NULL",
                "value": "null"
              }
            },
            "then": { "type": "COLUMN", "column_ref": "stage.name" }
          }
        ],
        "else": {
          "type": "CONCAT",
          "arguments": [
            { "type": "COLUMN", "column_ref": "stage_parent.name" },
            { "type": "LITERAL", "value": " - " },
            { "type": "COLUMN", "column_ref": "stage.name" }
          ]
        }
      },
      "alias": "stage_name"
    }
  ],
  "joins": [
    {
      "table": "doctor_assignments",
      "alias": "doc_assign",
      "type": "LEFT",
      "on_logic": {
        "type": "AND",
        "expressions": [
          {
            "type": "CONDITION",
            "condition": {
              "field": "doc_assign.person_id",
              "operator": "EQ",
              "right_field": "p.id"
            }
          },
          {
            "type": "CONDITION",
            "condition": {
              "field": "doc_assign.rn",
              "operator": "EQ",
              "value": "1"
            }
          }
        ]
      }
    },
    {
      "table": "user",
      "alias": "doctor_user",
      "type": "LEFT",
      "on_logic": {
        "type": "CONDITION",
        "condition": {
          "field": "doctor_user.id",
          "operator": "EQ",
          "right_field": "doc_assign.user_id"
        }
      }
    },
    {
      "table": "sale_assignments",
      "alias": "sale_assign",
      "type": "LEFT",
      "on_logic": {
        "type": "AND",
        "expressions": [
          {
            "type": "CONDITION",
            "condition": {
              "field": "sale_assign.person_id",
              "operator": "EQ",
              "right_field": "p.id"
            }
          },
          {
            "type": "CONDITION",
            "condition": {
              "field": "sale_assign.rn",
              "operator": "EQ",
              "value": "1"
            }
          }
        ]
      }
    },
    {
      "table": "user",
      "alias": "sale_user",
      "type": "LEFT",
      "on_logic": {
        "type": "CONDITION",
        "condition": {
          "field": "sale_user.id",
          "operator": "EQ",
          "right_field": "sale_assign.user_id"
        }
      }
    },
    {
      "table": "stage",
      "alias": "stage",
      "type": "LEFT",
      "on_logic": {
        "type": "CONDITION",
        "condition": {
          "field": "stage.id",
          "operator": "EQ",
          "right_field": "p.stage_id"
        }
      }
    },
    {
      "table": "stage",
      "alias": "stage_parent",
      "type": "LEFT",
      "on_logic": {
        "type": "CONDITION",
        "condition": {
          "field": "stage_parent.id",
          "operator": "EQ",
          "right_field": "stage.parent_stage_id"
        }
      }
    }
  ],
  "where_logic": {
    "type": "AND",
    "expressions": [
      {
        "type": "CONDITION",
        "condition": {
          "field": "p.status",
          "operator": "NEQ",
          "value": "-1"
        }
      },
      {
        "type": "CONDITION",
        "condition": {
          "field": "p.deleted_at",
          "operator": "IS NULL",
          "value": "null"
        }
      }
    ]
  },
  "limit": 20,
  "offset": 0
}`

	// Tạo request giả lập
	req := httptest.NewRequest(http.MethodPost, "/", bytes.NewBufferString(complexCTEsJSON))
	req.Header.Set("Content-Type", "application/json")

	// Thiết lập SafeMode = false để cho phép CTEs
	oldSafeMode := SafeMode
	SafeMode = false
	defer func() { SafeMode = oldSafeMode }()

	// Gọi hàm Parse
	query := &DynamicQuery{}
	err := Parse(req, query)

	// Kiểm tra kết quả
	if err != nil {
		t.Errorf("Parse() error = %v", err)
		return
	}

	// Kiểm tra các giá trị được parse
	if query.Table != "person" {
		t.Errorf("Parse() table = %v, want %v", query.Table, "person")
	}

	if query.Alias != "p" {
		t.Errorf("Parse() alias = %v, want %v", query.Alias, "p")
	}

	// Kiểm tra CTEs
	if len(query.CTEs) != 2 {
		t.Errorf("Parse() CTEs length = %v, want %v", len(query.CTEs), 2)
	}

	// Kiểm tra CTE đầu tiên
	if query.CTEs[0].Name != "doctor_assignments" {
		t.Errorf("Parse() first CTE name = %v, want %v", query.CTEs[0].Name, "doctor_assignments")
	}

	// Kiểm tra số lượng columns trong CTE
	if len(query.CTEs[0].Columns) != 2 {
		t.Errorf("Parse() first CTE columns length = %v, want %v", len(query.CTEs[0].Columns), 2)
	}

	// Kiểm tra query trong CTE
	if query.CTEs[0].Query.Table != "person_assignment" {
		t.Errorf("Parse() first CTE query table = %v, want %v", query.CTEs[0].Query.Table, "person_assignment")
	}

	// Kiểm tra số lượng selects
	if len(query.Selects) != 5 {
		t.Errorf("Parse() selects length = %v, want %v", len(query.Selects), 5)
	}

	// Kiểm tra CASE expression
	if query.Selects[4].Case == nil {
		t.Errorf("Parse() should have a CASE expression in the 5th select field")
	} else if query.Selects[4].Alias != "stage_name" {
		t.Errorf("Parse() CASE alias = %v, want %v", query.Selects[4].Alias, "stage_name")
	}

	// Kiểm tra số lượng joins
	if len(query.Joins) != 6 {
		t.Errorf("Parse() joins length = %v, want %v", len(query.Joins), 6)
	}

	// Kiểm tra điều kiện WHERE
	if query.WhereLogic == nil || query.WhereLogic.Type != "AND" {
		t.Errorf("Parse() should have an AND where_logic")
	}

	if len(query.WhereLogic.Expressions) != 2 {
		t.Errorf("Parse() where_logic expressions length = %v, want %v", len(query.WhereLogic.Expressions), 2)
	}

	// Kiểm tra limit và offset
	if query.Limit != 20 {
		t.Errorf("Parse() limit = %v, want %v", query.Limit, 20)
	}

	if query.Offset != 0 {
		t.Errorf("Parse() offset = %v, want %v", query.Offset, 0)
	}
}

func TestParseWithFullComplexQuery(t *testing.T) {
	// Tạo một JSON phức tạp đầy đủ để test
	fullComplexJSON := `{
  "ctes": [
    {
      "name": "doctor_assignments",
      "columns": ["*", "rn"],
      "query": {
        "table": "person_assignment",
        "alias": "pa",
        "selects": [
          { "field": "pa.*" }
        ],
        "aggregations": [
          {
            "function": "ROW_NUMBER",
            "field": "",
            "alias": "rn",
            "over": {
              "partition_by": ["pa.person_id"],
              "order_by": [
                { "field": "pa.id", "order": "ASC" }
              ]
            }
          }
        ],
        "where_logic": {
          "type": "CONDITION",
          "condition": {
            "field": "pa.role",
            "operator": "EQ",
            "value": "doctor"
          }
        }
      }
    },
    {
      "name": "sale_assignments",
      "columns": ["*", "rn"],
      "query": {
        "table": "person_assignment",
        "alias": "pa",
        "selects": [
          { "field": "pa.*" }
        ],
        "aggregations": [
          {
            "function": "ROW_NUMBER",
            "field": "",
            "alias": "rn",
            "over": {
              "partition_by": ["pa.person_id"],
              "order_by": [
                { "field": "pa.id", "order": "ASC" }
              ]
            }
          }
        ],
        "where_logic": {
          "type": "CONDITION",
          "condition": {
            "field": "pa.role",
            "operator": "EQ",
            "value": "sale"
          }
        }
      }
    },
    {
      "name": "appointments",
      "columns": ["*", "rn"],
      "query": {
        "table": "appointment",
        "alias": "ap",
        "selects": [
          { "field": "ap.*" }
        ],
        "aggregations": [
          {
            "function": "ROW_NUMBER",
            "field": "",
            "alias": "rn",
            "over": {
              "partition_by": ["ap.person_id"],
              "order_by": [
                { "field": "ap.start_time", "order": "DESC" }
              ]
            }
          }
        ]
      }
    },
    {
      "name": "deals",
      "columns": ["*", "rn"],
      "query": {
        "table": "deal",
        "alias": "d",
        "selects": [
          { "field": "d.*" }
        ],
        "aggregations": [
          {
            "function": "ROW_NUMBER",
            "field": "",
            "alias": "rn",
            "over": {
              "partition_by": ["d.person_id"],
              "order_by": [
                { "field": "d.created_at", "order": "ASC" }
              ]
            }
          }
        ]
      }
    }
  ],
  "table": "person",
  "alias": "p",
  "selects": [
    { "field": "p.id" },
    { "field": "p.full_name" },
    { "field": "p.phone" },
    { "field": "p.email" },
    {
      "function": "JSON_EXTRACT_PATH_TEXT",
      "field": "p.person_field",
      "parameters": ["code"],
      "alias": "person_code"
    },
    { "field": "source_term.name", "alias": "person_source" },
    { "field": "creator.name", "alias": "creator_name" },
    { "field": "doctor_user.name", "alias": "doctor_name" },
    { "field": "app.start_time", "alias": "appointment_time" },
    { "field": "sale_user.name", "alias": "sale_name" },
    {
      "case": {
        "conditions": [
          {
            "when": {
              "type": "CONDITION",
              "condition": {
                "field": "stage_parent.id",
                "operator": "IS NULL",
                "value": "null"
              }
            },
            "then": { "type": "COLUMN", "column_ref": "stage.name" }
          }
        ],
        "else": {
          "type": "CONCAT",
          "arguments": [
            { "type": "COLUMN", "column_ref": "stage_parent.name" },
            { "type": "LITERAL", "value": " - " },
            { "type": "COLUMN", "column_ref": "stage.name" }
          ]
        }
      },
      "alias": "stage_name"
    }
  ],
  "joins": [
    {
      "table": "term",
      "alias": "source_term",
      "type": "LEFT",
      "on_logic": {
        "type": "CONDITION",
        "condition": {
          "field": "source_term.id",
          "operator": "EQ",
          "right_field": "p.source_id"
        }
      }
    },
    {
      "table": "user",
      "alias": "creator",
      "type": "LEFT",
      "on_logic": {
        "type": "CONDITION",
        "condition": {
          "field": "creator.id",
          "operator": "EQ",
          "right_field": "p.user_id"
        }
      }
    },
    {
      "table": "doctor_assignments",
      "alias": "doc_assign",
      "type": "LEFT",
      "on_logic": {
        "type": "AND",
        "expressions": [
          {
            "type": "CONDITION",
            "condition": {
              "field": "doc_assign.person_id",
              "operator": "EQ",
              "right_field": "p.id"
            }
          },
          {
            "type": "CONDITION",
            "condition": {
              "field": "doc_assign.rn",
              "operator": "EQ",
              "value": "1"
            }
          }
        ]
      }
    },
    {
      "table": "user",
      "alias": "doctor_user",
      "type": "LEFT",
      "on_logic": {
        "type": "CONDITION",
        "condition": {
          "field": "doctor_user.id",
          "operator": "EQ",
          "right_field": "doc_assign.user_id"
        }
      }
    },
    {
      "table": "sale_assignments",
      "alias": "sale_assign",
      "type": "LEFT",
      "on_logic": {
        "type": "AND",
        "expressions": [
          {
            "type": "CONDITION",
            "condition": {
              "field": "sale_assign.person_id",
              "operator": "EQ",
              "right_field": "p.id"
            }
          },
          {
            "type": "CONDITION",
            "condition": {
              "field": "sale_assign.rn",
              "operator": "EQ",
              "value": "1"
            }
          }
        ]
      }
    },
    {
      "table": "user",
      "alias": "sale_user",
      "type": "LEFT",
      "on_logic": {
        "type": "CONDITION",
        "condition": {
          "field": "sale_user.id",
          "operator": "EQ",
          "right_field": "sale_assign.user_id"
        }
      }
    },
    {
      "table": "appointments",
      "alias": "app",
      "type": "LEFT",
      "on_logic": {
        "type": "AND",
        "expressions": [
          {
            "type": "CONDITION",
            "condition": {
              "field": "app.person_id",
              "operator": "EQ",
              "right_field": "p.id"
            }
          },
          {
            "type": "CONDITION",
            "condition": {
              "field": "app.rn",
              "operator": "EQ",
              "value": "1"
            }
          }
        ]
      }
    },
    {
      "table": "deals",
      "alias": "d",
      "type": "LEFT",
      "on_logic": {
        "type": "AND",
        "expressions": [
          {
            "type": "CONDITION",
            "condition": {
              "field": "d.person_id",
              "operator": "EQ",
              "right_field": "p.id"
            }
          },
          {
            "type": "CONDITION",
            "condition": {
              "field": "d.rn",
              "operator": "EQ",
              "value": "1"
            }
          }
        ]
      }
    },
    {
      "table": "stage",
      "alias": "stage",
      "type": "LEFT",
      "on_logic": {
        "type": "CONDITION",
        "condition": {
          "field": "stage.id",
          "operator": "EQ",
          "right_field": "d.stage_id"
        }
      }
    },
    {
      "table": "stage",
      "alias": "stage_parent",
      "type": "LEFT",
      "on_logic": {
        "type": "CONDITION",
        "condition": {
          "field": "stage_parent.id",
          "operator": "EQ",
          "right_field": "stage.parent_stage_id"
        }
      }
    }
  ],
  "where_logic": {
    "type": "AND",
    "expressions": [
      {
        "type": "CONDITION",
        "condition": {
          "field": "p.status",
          "operator": "NEQ",
          "value": "-1"
        }
      },
      {
        "type": "CONDITION",
        "condition": {
          "field": "p.deleted_at",
          "operator": "IS NULL",
          "value": "null"
        }
      }
    ]
  },
  "limit": 20,
  "offset": 0
}`

	// Tạo request giả lập
	req := httptest.NewRequest(http.MethodPost, "/", bytes.NewBufferString(fullComplexJSON))
	req.Header.Set("Content-Type", "application/json")

	// Thiết lập SafeMode = false để cho phép CTEs
	oldSafeMode := SafeMode
	SafeMode = false
	defer func() { SafeMode = oldSafeMode }()

	// Gọi hàm Parse
	query := &DynamicQuery{}
	err := Parse(req, query)

	// Kiểm tra kết quả
	if err != nil {
		t.Errorf("Parse() error = %v", err)
		return
	}

	// Kiểm tra các giá trị được parse
	if query.Table != "person" {
		t.Errorf("Parse() table = %v, want %v", query.Table, "person")
	}

	// Kiểm tra CTEs
	if len(query.CTEs) != 4 {
		t.Errorf("Parse() CTEs length = %v, want %v", len(query.CTEs), 4)
	}

	// Kiểm tra tên của các CTEs
	expectedCTENames := []string{"doctor_assignments", "sale_assignments", "appointments", "deals"}
	for i, name := range expectedCTENames {
		if query.CTEs[i].Name != name {
			t.Errorf("Parse() CTE[%d] name = %v, want %v", i, query.CTEs[i].Name, name)
		}
	}

	// Kiểm tra số lượng selects
	if len(query.Selects) != 11 {
		t.Errorf("Parse() selects length = %v, want %v", len(query.Selects), 11)
	}

	// Kiểm tra JSON_EXTRACT_PATH_TEXT function
	foundJsonExtract := false
	for _, sel := range query.Selects {
		if sel.Function == "JSON_EXTRACT_PATH_TEXT" && sel.Alias == "person_code" {
			foundJsonExtract = true
			break
		}
	}
	if !foundJsonExtract {
		t.Errorf("Parse() should have a JSON_EXTRACT_PATH_TEXT function in selects")
	}

	// Kiểm tra số lượng joins
	if len(query.Joins) != 10 {
		t.Errorf("Parse() joins length = %v, want %v", len(query.Joins), 10)
	}

	// Kiểm tra join với CTEs
	cteJoins := []string{"doctor_assignments", "sale_assignments", "appointments", "deals"}
	for _, cteName := range cteJoins {
		found := false
		for _, join := range query.Joins {
			if join.Table == cteName {
				found = true
				break
			}
		}
		if !found {
			t.Errorf("Parse() should have a join with CTE '%s'", cteName)
		}
	}

	// Kiểm tra limit
	if query.Limit != 20 {
		t.Errorf("Parse() limit = %v, want %v", query.Limit, 20)
	}
}
