package bqueryv2

import (
	"bytes"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
)

func TestParseAndBuildQuery_Integration(t *testing.T) {
	// Tạo một JSON request mẫu với payload person
	jsonBody := `{
  "table": "core.person",
  "alias": "p",
  "selects": [
    {"field": "p.id"},
    {"field": "p.deleted_at"},
    {"field": "p.status"},
    {"field": "p.version"},
    {"field": "p.created_at"},
    {"field": "p.updated_at"},
    {"field": "p.full_name"},
    {"field": "p.date_of_birth"},
    {"field": "p.gender"},
    {"field": "p.province_id"},
    {"field": "p.district_id"},
    {"field": "p.ward_id"},
    {"field": "p.address_number"},
    {"field": "p.phone"},
    {"field": "p.email"},
    {"field": "p.phone_confirm"},
    {"field": "p.mail_confirm"},
    {"field": "p.job_id"},
    {"field": "p.source_id"},
    {"field": "p.user_id"},
    {"field": "p.person_field"},
    {"raw_sql": "p.person_field ->> 'code'", "alias": "person_code"},
    {"field": "source_term.name", "alias": "person_source"},
    {"field": "creator.name", "alias": "creator_name"},
    {"field": "treatment_term.name", "alias": "treatment_name"}
  ],
  "joins": [
    {
      "table": "core.term",
      "alias": "source_term",
      "type": "LEFT",
      "on_logic": {
        "type": "CONDITION",
        "condition": {
          "field": "source_term.id",
          "operator": "EQ",
          "right_field": "p.source_id"
        }
      }
    },
    {
      "table": "core.user",
      "alias": "creator",
      "type": "LEFT",
      "on_logic": {
        "type": "CONDITION",
        "condition": {
          "field": "creator.id",
          "operator": "EQ",
          "right_field": "p.user_id"
        }
      }
    },
    {
      "table": "core.term",
      "alias": "treatment_term",
      "type": "LEFT",
      "on_logic": {
        "type": "CONDITION",
        "condition": {
          "field": "treatment_term.id",
          "operator": "EQ",
          "right_expr_raw": "(p.person_field->>'treatment_id')::bigint"
        }
      }
    }
  ],
  "where_logic": {
    "type": "AND",
    "expressions": [
      {
        "type": "CONDITION",
        "condition": {
          "field": "p.status",
          "operator": "NEQ",
          "value": -1
        }
      },
      {
        "type": "CONDITION",
        "condition": {
          "field": "p.deleted_at",
          "operator": "ISNULL"
        }
      }
    ]
  },
  "group_by": [],
  "aggregations": [],
  "sort": [],
  "offset": 0,
  "limit": 20
}`

	// Tạo request giả lập
	req := httptest.NewRequest(http.MethodPost, "/", bytes.NewBufferString(jsonBody))
	req.Header.Set("Content-Type", "application/json")

	// Parse request thành DynamicQuery
	query := &DynamicQuery{}
	err := Parse(req, query)

	// Kiểm tra kết quả parse
	if err != nil {
		t.Fatalf("Parse() error = %v", err)
	}

	// Build câu SQL từ DynamicQuery
	sql, _, err := writeSQL(t, query.BuildQuery())

	fmt.Println("#######")
	fmt.Println(sql)
	fmt.Println("#######")

}
