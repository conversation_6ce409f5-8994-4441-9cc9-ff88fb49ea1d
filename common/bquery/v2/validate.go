package bqueryv2

import (
	"fmt"
	"strings"
)

// Map of features that are considered high-risk for SQL injection
var highRiskFeatures = map[string]bool{
	"raw_sql":          true,  // Raw SQL expressions have the highest injection risk
	"raw_conditions":   true,  // Raw expressions in conditions are high risk
	"ctes":             false, // CTEs are moderately risky but well-controlled in this implementation
	"subqueries":       false, // Subqueries are moderately risky but well-controlled in this implementation
	"window_functions": false, // Window functions are lower risk but complex
	"collation":        true,  // Collation can be risky as it may allow injection in some DB engines
	"locking":          false, // Locking clauses are lower risk
	"unions":           false, // Unions are moderately risky but well-controlled
}

// SafeModeFeaturesAllowed checks if a specific feature is allowed in safe mode
// Can be extended to support a more granular permission system
func SafeModeFeaturesAllowed(feature string) bool {
	isHighRisk, exists := highRiskFeatures[feature]
	// If feature isn't in the map or isn't high risk, allow it
	return !exists || !isHighRisk
}

// validateExpression kiểm tra tính hợp lệ của biểu thức
func validateExpression(expr *Expression) error {
	if expr == nil {
		return nil
	}

	switch strings.ToUpper(expr.Type) {
	case "LITERAL":
		return validateLiteral(expr)
	case "FUNCTION":
		if !isValidFunction(expr.Function) {
			return fmt.Errorf("invalid function: %s", expr.Function)
		}

		// Kiểm tra từng argument
		for i, arg := range expr.Arguments {
			if err := validateExpression(&arg); err != nil {
				return fmt.Errorf("invalid argument %d: %w", i, err)
			}
		}

		return nil
	case "ARITHMETIC":
		if len(expr.Arguments) != 2 {
			return fmt.Errorf("arithmetic expressions require exactly 2 arguments")
		}

		validOps := map[string]bool{"+": true, "-": true, "*": true, "/": true, "%": true}
		if !validOps[expr.Operator] {
			return fmt.Errorf("invalid arithmetic operator: %s", expr.Operator)
		}

		// Kiểm tra từng operand
		for i, arg := range expr.Arguments {
			if err := validateExpression(&arg); err != nil {
				return fmt.Errorf("invalid operand %d: %w", i, err)
			}
		}

		return nil
	case "COLUMN":
		// Kiểm tra tên cột
		if expr.ColumnRef == "" {
			return fmt.Errorf("column reference cannot be empty")
		}

		// Kiểm tra an toàn cho tên cột
		parts := strings.Split(expr.ColumnRef, ".")
		for _, part := range parts {
			if !isValidIdentifier(part) {
				return fmt.Errorf("invalid column identifier: %s", part)
			}
		}

		return nil
	default:
		return fmt.Errorf("invalid expression type: %s", expr.Type)
	}
}

func validateLiteral(expr *Expression) error {
	switch strings.ToUpper(expr.LiteralType) {
	case "RAW":
		if SafeMode && !isSafeRawLiteral(expr.Value) {
			return fmt.Errorf("unsafe raw literal in safe mode: %v", expr.Value)
		}
		return nil
	case "STRING", "NUMBER", "BOOLEAN", "NULL":
		return nil
	default:
		return fmt.Errorf("invalid literal type: %s", expr.LiteralType)
	}
}

func isSafeRawLiteral(value interface{}) bool {
	strVal := fmt.Sprint(value)
	// Cho phép các giá trị raw an toàn như *, NULL
	safeValues := map[string]bool{
		"*":    true,
		"NULL": true,
	}
	return safeValues[strVal]
}

// validateRawExpressions kiểm tra tất cả raw expressions trong logical expression
func validateRawExpressions(expr *LogicalExpression) error {
	if expr == nil {
		return nil
	}

	switch strings.ToUpper(expr.Type) {
	case "CONDITION":
		if expr.Condition == nil {
			return nil
		}

		// Kiểm tra LeftExprRaw
		if expr.Condition.LeftExprRaw != "" {
			if !SafeModeFeaturesAllowed("raw_conditions") {
				return fmt.Errorf("raw expressions not allowed in safe mode")
			}

			if !isAllowedRawExpression(expr.Condition.LeftExprRaw) {
				return fmt.Errorf("potentially unsafe raw expression: %s", expr.Condition.LeftExprRaw)
			}
		}

		// Kiểm tra RightExprRaw
		if expr.Condition.RightExprRaw != "" {
			if !SafeModeFeaturesAllowed("raw_conditions") {
				return fmt.Errorf("raw expressions not allowed in safe mode")
			}

			if !isAllowedRawExpression(expr.Condition.RightExprRaw) {
				return fmt.Errorf("potentially unsafe raw expression: %s", expr.Condition.RightExprRaw)
			}
		}

		return nil

	case "AND", "OR":
		for i, childExpr := range expr.Expressions {
			if err := validateRawExpressions(&childExpr); err != nil {
				return fmt.Errorf("expression #%d: %w", i, err)
			}
		}
		return nil

	default:
		return nil
	}
}

func validateLogicalExpression(expr *LogicalExpression) error {
	if expr == nil {
		return nil
	}

	switch strings.ToUpper(expr.Type) {
	case "CONDITION":
		if expr.Condition == nil {
			return fmt.Errorf("condition is missing")
		}

		// Kiểm tra left expression
		if expr.Condition.LeftExpr != nil {
			if err := validateExpression(expr.Condition.LeftExpr); err != nil {
				return fmt.Errorf("left expression: %w", err)
			}
		}

		// Kiểm tra right expression
		if expr.Condition.RightExpr != nil {
			if err := validateExpression(expr.Condition.RightExpr); err != nil {
				return fmt.Errorf("right expression: %w", err)
			}
		}

		// Kiểm tra raw expressions
		if SafeMode {
			if expr.Condition.LeftExprRaw != "" && !SafeModeFeaturesAllowed("raw_conditions") {
				return fmt.Errorf("raw expressions not allowed in safe mode")
			}

			if expr.Condition.RightExprRaw != "" && !SafeModeFeaturesAllowed("raw_conditions") {
				return fmt.Errorf("raw expressions not allowed in safe mode")
			}
		}

		return nil

	case "AND", "OR":
		for i, childExpr := range expr.Expressions {
			if err := validateLogicalExpression(&childExpr); err != nil {
				return fmt.Errorf("expression #%d: %w", i, err)
			}
		}
		return nil

	default:
		return fmt.Errorf("invalid logical expression type: %s", expr.Type)
	}
}

// Validate checks if the query is properly configured
func (q *DynamicQuery) Validate() error {
	// Check if we have at least a table or a CTE
	if q.Table == "" && len(q.CTEs) == 0 && len(q.UnionQueries) == 0 {
		return fmt.Errorf("query must have either a table, CTE, or union query")
	}

	// Check if we have at least one field to select
	// Add a default SELECT * if nothing is specified
	if len(q.Selects) == 0 && len(q.Aggregations) == 0 {
		// Add SELECT * as default
		q.Selects = []SelectField{
			{Field: "*", Alias: ""},
		}
	}

	// Validate each join
	for i, join := range q.Joins {
		if join.Table == "" && join.Subquery == nil {
			return fmt.Errorf("join #%d: must specify either a table or subquery", i)
		}

		if join.Table != "" && join.Subquery != nil {
			return fmt.Errorf("join #%d: cannot specify both table and subquery", i)
		}

		if len(join.Using) == 0 && join.OnLogic == nil && join.Type != "CROSS" {
			return fmt.Errorf("join #%d: must specify either USING columns or ON conditions", i)
		}
	}

	// Validate function names
	for i, sel := range q.Selects {
		if sel.Function != "" && !isValidFunction(sel.Function) {
			return fmt.Errorf("select field #%d: invalid function name '%s'", i, sel.Function)
		}

		// Check raw SQL usage with SafeMode
		if sel.RawSQL != "" && SafeMode && !SafeModeFeaturesAllowed("raw_sql") {
			return fmt.Errorf("raw SQL is not allowed in safe mode")
		}

		// Thêm kiểm tra cho các biểu thức
		if sel.Case != nil {
			for j, cond := range sel.Case.Conditions {
				if err := validateLogicalExpression(&cond.When); err != nil {
					return fmt.Errorf("select field #%d, case condition #%d: %w", i, j, err)
				}
			}
		}
	}

	for i, agg := range q.Aggregations {
		if !isValidFunction(agg.Function) {
			return fmt.Errorf("aggregation #%d: invalid function name '%s'", i, agg.Function)
		}

		// Check window function usage with SafeMode
		if agg.Over != nil && !agg.Over.IsEmpty() && SafeMode && !SafeModeFeaturesAllowed("window_functions") {
			return fmt.Errorf("window functions are not allowed in safe mode")
		}
	}

	// Check other high-risk features when SafeMode is enabled
	if SafeMode {
		if len(q.CTEs) > 0 && !SafeModeFeaturesAllowed("ctes") {
			return fmt.Errorf("CTEs are not allowed in safe mode")
		}

		for i, join := range q.Joins {
			if join.Subquery != nil && !SafeModeFeaturesAllowed("subqueries") {
				return fmt.Errorf("join #%d: subqueries are not allowed in safe mode", i)
			}
		}

		for i, sort := range q.Sort {
			if sort.Collate != "" && !SafeModeFeaturesAllowed("collation") {
				return fmt.Errorf("sort criteria #%d: collation is not allowed in safe mode", i)
			}
		}

		if q.Lock != nil && !SafeModeFeaturesAllowed("locking") {
			return fmt.Errorf("locking clauses are not allowed in safe mode")
		}

		if len(q.UnionQueries) > 0 && !SafeModeFeaturesAllowed("unions") {
			return fmt.Errorf("union queries are not allowed in safe mode")
		}
	}

	// Kiểm tra các biểu thức trong WHERE/HAVING
	if q.WhereLogic != nil {
		if err := validateLogicalExpression(q.WhereLogic); err != nil {
			return fmt.Errorf("where clause: %w", err)
		}

		// Kiểm tra raw expressions
		if SafeMode {
			if err := validateRawExpressions(q.WhereLogic); err != nil {
				return fmt.Errorf("where clause: %w", err)
			}
		}
	}

	if q.HavingLogic != nil {
		if err := validateLogicalExpression(q.HavingLogic); err != nil {
			return fmt.Errorf("having clause: %w", err)
		}

		// Kiểm tra raw expressions
		if SafeMode {
			if err := validateRawExpressions(q.HavingLogic); err != nil {
				return fmt.Errorf("having clause: %w", err)
			}
		}
	}

	return nil
}
