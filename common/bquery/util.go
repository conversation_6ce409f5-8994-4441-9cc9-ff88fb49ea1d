package bquery

import (
	"context"
	rawSql "database/sql" // Thêm import này
	"entgo.io/ent/dialect/sql"
	"fmt"
	"github.com/stephenafamo/bob"
	"github.com/zeromicro/go-zero/core/logx"
	"io"
	"os"
	"strings"
)

func Paging(pageSize, page int) (limit, offset int) {
	if pageSize != 0 {
		if page < 1 {
			page = 1
		}
		offset = (page - 1) * pageSize
		limit = pageSize
		return
	}

	return 1000, 0
}

func Ordering(order string) func(selector *sql.Selector) {
	parts := strings.Split(order, " ")
	if len(parts) != 2 {
		return func(s *sql.Selector) {
			s.OrderBy(
				sql.Desc("created_at"),
				sql.Desc("id"),
			)
		}
	}

	field := parts[0]
	direction := sql.OrderDesc()
	if strings.EqualFold(parts[1], "asc") {
		direction = sql.OrderAsc()
	}

	return sql.OrderByField(field, direction).ToFunc()
}

func RunSQLScript(db *rawSql.DB, scriptPath string) error {
	content, err := os.ReadFile(scriptPath)
	if err != nil {
		return err
	}

	_, err = db.Exec(string(content))
	if err != nil {
		return err
	}

	logx.Infof("SQL: %s executed successfully", scriptPath)
	return nil
}

// GetQueryString extracts the SQL query string and arguments from a query object
func GetQueryString(ctx context.Context, query interface{}) (string, []any, error) {
	var sqlBuilder strings.Builder

	// Check if the query implements WriteQuery method
	if q, ok := query.(interface {
		WriteQuery(context.Context, io.Writer, int) ([]any, error)
	}); ok {
		args, err := q.WriteQuery(ctx, &sqlBuilder, 1)
		if err != nil {
			return "", nil, fmt.Errorf("failed to build query: %w", err)
		}
		return sqlBuilder.String(), args, nil
	}

	// If it doesn't implement WriteQuery, check if it implements WriteSQL
	if q, ok := query.(interface {
		WriteSQL(context.Context, io.Writer, bob.Dialect, int) ([]any, error)
	}); ok {
		// This is a fallback and might not work correctly without dialect information
		// You might need to adjust this part based on your actual implementation
		args, err := q.WriteSQL(ctx, &sqlBuilder, nil, 1)
		if err != nil {
			return "", nil, fmt.Errorf("failed to build query: %w", err)
		}
		return sqlBuilder.String(), args, nil
	}

	return "", nil, fmt.Errorf("query does not implement WriteQuery or WriteSQL")
}
