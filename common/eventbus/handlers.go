package eventbus

import (
	"bcare/common/bauth"
	"bcare/common/berr"
	"context"
)

type (
	HandlerFn func(ctx context.Context, ev Event) error

	handler struct {
		handler              HandlerFn
		resourceTypes        map[string]bool
		eventTypes           map[string]bool
		resourceTypePatterns []string // Patterns like "ent_*", "task_*", etc.
		eventTypePatterns    []string // Patterns for event types
		constraints          constraintSet
		weight               int
	}

	// @todo add sorting interface
	HandlerSet []*handler

	HandlerRegOp func(t *handler)

	eventInvokerSettable interface {
		SetInvoker(bauth.Identifiable)
	}
)

// Match matches handler with resource event
func (t handler) Match(re Event) bool {
	if re == nil {
		return false
	}

	resourceType := re.ResourceType()
	if len(resourceType) == 0 {
		return false
	}

	// Check resource type matching
	hasResourceFilter := len(t.resourceTypes) > 0 || len(t.resourceTypePatterns) > 0
	if hasResourceFilter {
		// Check exact match first (O(1))
		if !t.resourceTypes[resourceType] {
			// If no exact match, check patterns
			if !t.matchesPattern(resourceType, t.resourceTypePatterns) {
				return false
			}
		}
	}

	eventType := re.EventType()
	if len(eventType) == 0 {
		return false
	}

	// Check event type matching
	hasEventFilter := len(t.eventTypes) > 0 || len(t.eventTypePatterns) > 0
	if hasEventFilter {
		// Check exact match first (O(1))
		if !t.eventTypes[eventType] {
			// If no exact match, check patterns
			if !t.matchesPattern(eventType, t.eventTypePatterns) {
				return false
			}
		}
	}

	for _, c := range t.constraints {
		// Should match all constraints
		if !re.Match(c) {
			return false
		}
	}

	return true
}

func (t handler) Handle(ctx context.Context, ev Event) error {
	defer berr.Recover()

	if eis, ok := ev.(eventInvokerSettable); ok {
		eis.SetInvoker(bauth.GetIdentityFromContext(ctx))
	}

	return t.handler(ctx, ev)
}

func NewHandler(h HandlerFn, ops ...HandlerRegOp) *handler {
	var t = &handler{
		resourceTypes: make(map[string]bool),
		eventTypes:    make(map[string]bool),
		handler:       h,
	}

	for _, op := range ops {
		op(t)
	}

	return t
}

// matchesPattern checks if a string matches any of the given patterns
// Supports simple prefix patterns like "ent_*", "task_*"
// For performance, we only support prefix matching with * at the end
func (t handler) matchesPattern(str string, patterns []string) bool {
	for _, pattern := range patterns {
		if len(pattern) == 0 {
			continue
		}

		// Simple prefix matching for patterns ending with *
		if pattern[len(pattern)-1] == '*' {
			prefix := pattern[:len(pattern)-1]
			if len(str) >= len(prefix) && str[:len(prefix)] == prefix {
				return true
			}
		} else if str == pattern {
			// Exact match
			return true
		}
	}
	return false
}

func For(rr ...string) HandlerRegOp {
	return func(t *handler) {
		for _, r := range rr {
			t.resourceTypes[r] = true
		}
	}
}

// ForPattern registers handler for resource type patterns
// Supports patterns like "ent_*" for prefix matching
func ForPattern(patterns ...string) HandlerRegOp {
	return func(t *handler) {
		t.resourceTypePatterns = append(t.resourceTypePatterns, patterns...)
	}
}

func On(ee ...string) HandlerRegOp {
	return func(t *handler) {
		for _, e := range ee {
			t.eventTypes[e] = true
		}
	}
}

// OnPattern registers handler for event type patterns
// Supports patterns like "after*" for prefix matching
func OnPattern(patterns ...string) HandlerRegOp {
	return func(t *handler) {
		t.eventTypePatterns = append(t.eventTypePatterns, patterns...)
	}
}

func Constraint(c ConstraintMatcher) HandlerRegOp {
	return func(t *handler) {
		t.constraints = append(t.constraints, c)
	}
}

func Weight(weight int) HandlerRegOp {
	return func(t *handler) {
		t.weight = weight
	}
}

// handler sorting:

func (set HandlerSet) Len() int           { return len(set) }
func (set HandlerSet) Swap(i, j int)      { set[i], set[j] = set[j], set[i] }
func (set HandlerSet) Less(i, j int) bool { return set[i].weight < set[j].weight }
