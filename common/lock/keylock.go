package keylock

import (
	"fmt"
	"strings"
	"sync"
	"time"
)

// Global is a KeyLock instance that can be used throughout the application
var (
	Global = NewKeyLock()
)

// KeyLock provides a mechanism for locking based on string keys
type KeyLock struct {
	locks sync.Map // map[string]*lockEntry
}

// lockEntry represents a lock and its reference count
type lockEntry struct {
	mutex    *sync.Mutex
	refCount int
	mu       sync.Mutex // protects refCount
}

// NewKeyLock creates a new KeyLock instance
func NewKeyLock() *KeyLock {
	return &KeyLock{}
}

// Lock attempts to acquire a lock for the given key components.
// It returns a function to unlock and an error if the lock couldn't be acquired within the timeout.
func (kl *KeyLock) Lock(timeout time.Duration, keyComponents ...interface{}) (unlock func(), err error) {
	key := generateKey(keyComponents...)
	entry := kl.getLockEntry(key)

	// Attempt to acquire the lock with timeout
	acquired := make(chan struct{})
	go func() {
		entry.mutex.Lock()
		close(acquired)
	}()

	select {
	case <-acquired:
		return func() {
			entry.mutex.Unlock()
			kl.releaseLock(key)
		}, nil
	case <-time.After(timeout):
		return nil, fmt.Errorf("failed to acquire lock for key '%s' within %v", key, timeout)
	}
}

// getLockEntry retrieves or creates a lockEntry for the given key
func (kl *KeyLock) getLockEntry(key string) *lockEntry {
	actual, _ := kl.locks.LoadOrStore(key, &lockEntry{
		mutex:    &sync.Mutex{},
		refCount: 1,
	})
	entry := actual.(*lockEntry)

	// If the lock already exists, increment the refCount
	if actual != nil {
		entry.mu.Lock()
		entry.refCount++
		entry.mu.Unlock()
	}

	return entry
}

// releaseLock decrements the reference count and removes the lockEntry if no longer needed
func (kl *KeyLock) releaseLock(key string) {
	actual, exists := kl.locks.Load(key)
	if !exists {
		return
	}

	entry := actual.(*lockEntry)
	entry.mu.Lock()
	entry.refCount--
	shouldDelete := entry.refCount == 0
	entry.mu.Unlock()

	if shouldDelete {
		kl.locks.Delete(key)
	}
}

// generateKey creates a unique string key from the given components
func generateKey(keyComponents ...interface{}) string {
	var sb strings.Builder
	for _, component := range keyComponents {
		sb.WriteString(toString(component))
		sb.WriteString(":") // delimiter to separate components
	}
	return sb.String()
}

// toString converts any value to its string representation
func toString(v interface{}) string {
	if v == nil {
		return "nil"
	}
	switch val := v.(type) {
	case string:
		return val
	case fmt.Stringer:
		return val.String()
	case int, int8, int16, int32, int64,
		uint, uint8, uint16, uint32, uint64,
		float32, float64, bool:
		return fmt.Sprintf("%v", val)
	default:
		return fmt.Sprintf("%v", val)
	}
}
