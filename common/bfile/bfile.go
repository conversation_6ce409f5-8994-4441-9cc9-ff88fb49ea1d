package bfile

import (
	"fmt"
	"github.com/blazy-vn/slug"
	"io"
	"mime/multipart"
	"net/http"
	"os"
	"path/filepath"
	"strings"

	"github.com/disintegration/imaging"
)

const maxFileSize = 20 * 1024 * 1024 // 5MB
const maxWidth = 1920

var allowedExtensions = map[string]struct{}{
	".jpg":  {},
	".jpeg": {},
	".png":  {},
	".gif":  {},
	".bmp":  {}, // Image formats
	".pdf":  {},
	".doc":  {},
	".docx": {},
	".xls":  {},
	".xlsx": {},
	".ppt":  {},
	".pptx": {}, // Office formats
	".txt":  {},
	".csv":  {},
	".zip":  {},
	".rar":  {}, // Text and archive formats
}

// Others: image, audio, text, document, video, archive
var allowedMIMETypes = map[string]string{
	"image/jpeg":         "image",
	"image/png":          "image",
	"image/gif":          "image",
	"image/bmp":          "image",
	"application/pdf":    "document",
	"application/msword": "document",
	"application/vnd.openxmlformats-officedocument.wordprocessingml.document": "document",
	"application/vnd.ms-excel": "document",
	"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":         "document",
	"application/vnd.ms-powerpoint":                                             "document",
	"application/vnd.openxmlformats-officedocument.presentationml.presentation": "document",
	"text/plain":                   "text",
	"text/csv":                     "text",
	"application/zip":              "archive",
	"application/x-rar-compressed": "archive",
}

func GetKindFromMIMEType(mimeType string) string {
	if kind, ok := allowedMIMETypes[mimeType]; ok {
		return kind
	}
	return "unknown"
}

var imageExtensions = map[string]struct{}{
	".jpg":  {},
	".jpeg": {},
	".png":  {},
	".gif":  {},
	".bmp":  {},
	".tiff": {},
	".webp": {},
}
var imageMIMETypes = map[string]struct{}{
	"image/jpeg": {},
	"image/png":  {},
	"image/gif":  {},
	"image/bmp":  {},
	"image/tiff": {},
	"image/webp": {},
}

func GetMimeTypeFromFile(out *multipart.File) string {
	// Lưu lại vị trí ban đầu của con trỏ đọc
	originalPosition, _ := (*out).Seek(0, io.SeekCurrent)
	defer (*out).Seek(originalPosition, io.SeekStart) // Đảm bảo đặt lại vị trí con trỏ về ban đầu sau khi thoát khỏi hàm

	// Only the first 512 bytes are used to sniff the content type.
	buffer := make([]byte, 512)

	_, _ = (*out).Seek(io.SeekStart, 0)
	_, err := (*out).Read(buffer)
	if err != nil {
		return ""
	}

	// Use the net/http package's handy DetectContentType function. Always returns a valid
	// content-type by returning "application/octet-stream" if no others seemed to match.
	contentType := http.DetectContentType(buffer)

	return contentType
}

func GenerateFileName(complex string) string {
	var extension = filepath.Ext(complex)
	var name = complex[0 : len(complex)-len(extension)]
	slug.MaxLength = 70
	cleanString := slug.MakeLang(name, "en")

	return cleanString + extension
}

func ValidateFileType(file *multipart.File, fileHeader *multipart.FileHeader) (string, error) {
	//ext := strings.ToLower(filepath.Ext(fileHeader.Filename))
	mimeType := fileHeader.Header.Get("Content-Type")
	//mimeType := GetMimeTypeFromFile(file) -> Cannot detect txt file

	// Check file size
	if fileHeader.Size > int64(maxFileSize) {
		return mimeType, fmt.Errorf("file size exceeds the limit of %d bytes", maxFileSize)
	}

	// Check MIME type
	if isAllowedMIMEType(mimeType) {
		return mimeType, nil
	}

	return mimeType, fmt.Errorf("MIME type '%s' is not allowed", mimeType)
}

func ValidateFileExtension(fileName string) error {
	ext := strings.ToLower(filepath.Ext(fileName))
	if isAllowedExtension(ext) {
		return nil
	}
	return fmt.Errorf("file extension '%s' is not allowed", ext)
}

func SaveFileToDisk(src io.Reader, dst string) error {
	// Create a new file at the destination path
	out, err := os.Create(dst)
	if err != nil {
		return err
	}
	defer out.Close()

	// Copy the contents of the source reader to the new file
	_, err = io.Copy(out, src)
	if err != nil {
		return err
	}

	return nil
}

// IsImageFile If mimetype is provided, no need to extract it from header
func IsImageFile(file *multipart.File, fileHeader *multipart.FileHeader, mimeType string) bool {
	ext := strings.ToLower(filepath.Ext(fileHeader.Filename))
	if _, ok := imageExtensions[ext]; !ok {
		return false
	}

	if mimeType == "" {
		mimeType = GetMimeTypeFromFile(file)
	}
	if _, ok := imageMIMETypes[mimeType]; !ok {
		return false
	}

	return true
}

//func OptimizeImage1(f io.Reader) (io.Reader, error) {
//	// Start libvips
//	vips.Startup(nil)
//	defer vips.Shutdown()
//
//	// Load input image
//	inImage, err := vips.NewImageFromReader(f)
//	if err != nil {
//		return nil, fmt.Errorf("failed to load input image: %v", err)
//	}
//
//	err = inImage.RemoveICCProfile()
//	if err != nil {
//		return nil, fmt.Errorf("failed to remove ICC profile: %v", err)
//	}
//
//	// Lấy kích thước ban đầu của hình ảnh
//	width, _ := inImage.Width(), inImage.Height()
//	// Chỉ thay đổi kích thước nếu chiều rộng lớn hơn maxWidth
//	if width > maxWidth {
//		err = inImage.Thumbnail(maxWidth, 0, vips.InterestingCentre)
//		if err != nil {
//			return nil, fmt.Errorf("failed to resize image: %v", err)
//		}
//	}
//
//	// Export optimized image
//	exportParams := vips.NewPngExportParams()
//	exportParams.Quality = 80
//	exportParams.StripMetadata = true
//
//	optimizedBytes, _, err := inImage.ExportPng(exportParams)
//	if err != nil {
//		return nil, fmt.Errorf("failed to export optimized image: %v", err)
//	}
//
//	return bytes.NewReader(optimizedBytes), nil
//}

func OptimizeImage(file multipart.File, outputPath string) error {
	// Load input image
	src, err := imaging.Decode(file)
	if err != nil {
		return fmt.Errorf("failed to decode input image: %v", err)
	}

	// Check if the width is greater than maxWidth
	if src.Bounds().Dx() > maxWidth {
		src = imaging.Resize(src, maxWidth, 0, imaging.Lanczos)
	}

	// Save optimized image to disk
	err = imaging.Save(src, outputPath, imaging.JPEGQuality(80))
	if err != nil {
		return fmt.Errorf("failed to save optimized image: %v", err)
	}

	return nil
}

func isAllowedExtension(ext string) bool {
	_, ok := allowedExtensions[ext]
	return ok
}

func isAllowedMIMEType(mimeType string) bool {
	_, ok := allowedMIMETypes[mimeType]
	return ok
}
