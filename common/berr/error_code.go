package berr

const OK uint32 = 0

const (
	RequestParamError uint32 = 1004 // Lỗi chung về tham số yêu cầu (sai định dạng, thiếu...)
	ValidationFailed  uint32 = 1009 // Lỗi xác thực nghiệp vụ (vd: email đã tồn tại nhưng không phải duplicate key DB)

	TokenExpireError     uint32 = 1005 // Token hết hạn
	TokenGenerateError   uint32 = 1006 // Lỗi tạo token
	AuthenticationFailed uint32 = 1010 // Lỗi xác thực chung (sai pass, user không tồn tại...)

	PermissionDenied uint32 = 1007 // Không có quyền thực hiện (Đ<PERSON>i tên từ OperationNotAllowed cho rõ nghĩa)

	NotFound uint32 = 1011 // Không tìm thấy tài nguyên (chung)

	DuplicateEntry uint32 = 1012 // Xung đột dữ liệu (vd: unique key constraint)

	TooManyRequest uint32 = 1008 // Yêu cầu qu<PERSON> nhiều
)

const (
	ServerCommonError    uint32 = 1003 // Lỗi chung phía server không xác định
	DBError              uint32 = 1001 // Lỗi cơ sở dữ liệu chung
	DBUpdateAffectedZero uint32 = 1002 // Lỗi cập nhật DB không ảnh hưởng dòng nào (Đổi tên từ DbUpdateAffectedZeroError)
	CreateFailed         uint32 = 1013 // Lỗi khi tạo bản ghi (sau khi đã qua validation)
	UpdateFailed         uint32 = 1014 // Lỗi khi cập nhật bản ghi
	DeleteFailed         uint32 = 1015 // Lỗi khi xóa bản ghi
	ListFailed           uint32 = 1016 // Lỗi khi lấy danh sách bản ghi
	OperationFailed      uint32 = 1017 // Lỗi thực hiện một thao tác chung nào đó phía server
	CopyFailed           uint32 = 1018 // Lỗi khi sao chép dữ liệu (DTO -> Ent, Ent -> DTO...)

	ExternalServiceCallFailed uint32 = 1019 // Lỗi khi gọi dịch vụ bên ngoài
)
