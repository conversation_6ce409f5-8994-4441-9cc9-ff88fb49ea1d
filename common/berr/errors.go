package berr

import (
	"bytes"
	"fmt"
	"runtime"
)

// --- Predefined Global Error Variables ---

var (
	// --- Client Errors ---
	ErrRequestParam         = newCodeError(RequestParamError, "Request parameter error", 2)
	ErrValidationFailed     = newCodeError(ValidationFailed, "Input validation failed", 2)
	ErrTokenExpire          = newCodeError(TokenExpireError, "Token was expired", 2)
	ErrTokenGenerate        = newCodeError(TokenGenerateError, "Token generate failed", 2)
	ErrAuthenticationFailed = newCodeError(AuthenticationFailed, "Authentication failed", 2)
	ErrPermissionDenied     = newCodeError(PermissionDenied, "Permission denied", 2) // Renamed
	ErrNotFound             = newCodeError(NotFound, "Resource not found", 2)
	ErrDuplicateEntry       = newCodeError(DuplicateEntry, "Duplicate entry", 2)
	ErrTooManyRequest       = newCodeError(TooManyRequest, "Too many requests", 2)

	// --- Server Errors ---
	ErrServerCommon              = newCodeError(ServerCommonError, "Something went wrong", 2)
	ErrDB                        = newCodeError(DBError, "Database error", 2)
	ErrDBUpdateAffectedZero      = newCodeError(DBUpdateAffectedZero, "Database update affected 0 rows", 2) // Renamed
	ErrCreateFailed              = newCodeError(CreateFailed, "Failed to create record", 2)
	ErrUpdateFailed              = newCodeError(UpdateFailed, "Failed to update record", 2)
	ErrDeleteFailed              = newCodeError(DeleteFailed, "Failed to delete record", 2)
	ErrListFailed                = newCodeError(ListFailed, "Failed to list records", 2)
	ErrOperationFailed           = newCodeError(OperationFailed, "Operation failed", 2)
	ErrCopyFailed                = newCodeError(CopyFailed, "Failed to copy data", 2)
	ErrExternalServiceCallFailed = newCodeError(ExternalServiceCallFailed, "External service call failed", 2)
)

// --- CodeError Struct ---

type CodeError struct {
	Code      uint32
	Msg       string
	Operation string // Context về nơi xảy ra lỗi (vd: "UserModel.CreateUser")
	Err       error  // Lỗi gốc (nếu có)
	Location  string // Nơi định nghĩa hoặc nơi Wrap/Op/New được gọi gần nhất
}

// --- Helper Functions ---

// newCodeError initializes a CodeError, capturing the definition location.
func newCodeError(code uint32, msg string, skip int) *CodeError {
	// Tăng skip lên 1 để bỏ qua chính newCodeError
	_, file, line, ok := runtime.Caller(skip + 1)
	location := "unknown definition location"
	if ok {
		location = fmt.Sprintf("%s:%d", file, line)
	}
	return &CodeError{
		Code:     code,
		Msg:      msg,
		Location: location,
	}
}

// NewErrCodeMsg creates a new CodeError with a specific code and message.
// Location will point to the caller of NewErrCodeMsg.
func NewErrCodeMsg(errCode uint32, errMsg string) *CodeError {
	// skip = 1 để newCodeError lấy vị trí của hàm gọi NewErrCodeMsg
	ce := newCodeError(errCode, errMsg, 1)

	// Cố gắng tìm message mặc định nếu errMsg rỗng
	if errMsg == "" {
		switch errCode {
		case RequestParamError:
			ce.Msg = ErrRequestParam.Msg
		case ValidationFailed:
			ce.Msg = ErrValidationFailed.Msg
		case TokenExpireError:
			ce.Msg = ErrTokenExpire.Msg
		case TokenGenerateError:
			ce.Msg = ErrTokenGenerate.Msg
		case AuthenticationFailed:
			ce.Msg = ErrAuthenticationFailed.Msg
		case PermissionDenied:
			ce.Msg = ErrPermissionDenied.Msg
		case NotFound:
			ce.Msg = ErrNotFound.Msg
		case DuplicateEntry:
			ce.Msg = ErrDuplicateEntry.Msg
		case TooManyRequest:
			ce.Msg = ErrTooManyRequest.Msg
		case ServerCommonError:
			ce.Msg = ErrServerCommon.Msg
		case DBError:
			ce.Msg = ErrDB.Msg
		case DBUpdateAffectedZero:
			ce.Msg = ErrDBUpdateAffectedZero.Msg
		case CreateFailed:
			ce.Msg = ErrCreateFailed.Msg
		case UpdateFailed:
			ce.Msg = ErrUpdateFailed.Msg
		case DeleteFailed:
			ce.Msg = ErrDeleteFailed.Msg
		case ListFailed:
			ce.Msg = ErrListFailed.Msg
		case OperationFailed:
			ce.Msg = ErrOperationFailed.Msg
		case CopyFailed:
			ce.Msg = ErrCopyFailed.Msg
		case ExternalServiceCallFailed:
			ce.Msg = ErrExternalServiceCallFailed.Msg
			// Mặc định không thay đổi message nếu không khớp (hoặc dùng ErrServerCommon nếu muốn)
			// default: ce.Msg = ErrServerCommon.Msg // Bỏ comment nếu muốn default này
		}
	}
	return ce
}

// NewErrMsg creates a new CodeError with a custom message and default ServerCommonError code.
// Location will point to the caller of NewErrMsg.
func NewErrMsg(errMsg string) *CodeError {
	// skip = 1 để newCodeError lấy vị trí của hàm gọi NewErrMsg
	return newCodeError(ServerCommonError, errMsg, 1)
}

// Wrap adds an underlying error and updates the Location to the call site of Wrap.
// It returns a new *CodeError instance.
func (e *CodeError) Wrap(err error, msgArgs ...interface{}) *CodeError {
	if e == nil {
		newErr := *ErrServerCommon
		newErr.Err = err
		newErr.Msg = fmt.Sprintf("Wrap called on nil CodeError: %s", newErr.Msg)
		if len(msgArgs) > 0 {
			originalMsgPart := fmt.Sprintf(ErrServerCommon.Msg, msgArgs...)
			newErr.Msg = fmt.Sprintf("Wrap called on nil CodeError: %s", originalMsgPart)
		}
		// skip = 1 để newCodeError lấy vị trí của hàm gọi Wrap
		_, file, line, ok := runtime.Caller(1)
		if ok {
			newErr.Location = fmt.Sprintf("%s:%d", file, line)
		}
		return &newErr
	}

	clonedErr := *e // Clone để không thay đổi lỗi gốc dùng chung
	clonedErr.Err = err

	// skip = 1 để lấy vị trí của hàm gọi Wrap
	_, file, line, ok := runtime.Caller(1)
	if ok {
		clonedErr.Location = fmt.Sprintf("%s:%d", file, line) // Cập nhật Location
	}

	if len(msgArgs) > 0 {
		clonedErr.Msg = fmt.Sprintf(e.Msg, msgArgs...) // Định dạng lại message
	}

	return &clonedErr
}

// Op adds operational context (e.g., function name). Does not change Location.
// It returns a new *CodeError instance.
func (e *CodeError) Op(op string) *CodeError {
	if e == nil {
		newErr := *ErrServerCommon
		newErr.Msg = fmt.Sprintf("Op '%s' called on nil CodeError", op)
		newErr.Operation = op
		// Giữ Location mặc định của ErrServerCommon
		return &newErr
	}

	clonedErr := *e // Clone
	clonedErr.Operation = op
	// Không thay đổi Location
	return &clonedErr
}

// --- Methods for CodeError ---

// GetErrCode returns the error code.
func (e *CodeError) GetErrCode() uint32 {
	if e == nil {
		return ServerCommonError
	}
	return e.Code
}

// GetErrMsg returns the user-facing error message.
func (e *CodeError) GetErrMsg() string {
	if e == nil {
		return ErrServerCommon.Msg
	}
	return e.Msg
}

// Error returns a detailed string representation suitable for logging.
func (e *CodeError) Error() string {
	if e == nil {
		return "[CodeError is nil]"
	}
	var buf bytes.Buffer

	if e.Operation != "" {
		fmt.Fprintf(&buf, "[%s] ", e.Operation)
	}

	fmt.Fprintf(&buf, "[Code: %d] %s", e.Code, e.Msg)

	// Nối lỗi gốc nếu có
	if e.Err != nil {
		buf.WriteString(" <- ")
		if innerCodeErr, ok := e.Err.(*CodeError); ok {
			buf.WriteString(innerCodeErr.Error()) // Đệ quy
		} else {
			buf.WriteString(e.Err.Error())
		}
	}
	// Có thể thêm Location vào log nếu muốn:
	// if e.Location != "" {
	//     fmt.Fprintf(&buf, " (at %s)", e.Location)
	// }
	return buf.String()
}

// Unwrap returns the underlying error for errors.Is/As.
func (e *CodeError) Unwrap() error {
	if e == nil {
		return nil
	}
	return e.Err
}

// Is allows errors.Is(err, ErrNotFound) etc. It compares error codes.
func (e *CodeError) Is(target error) bool {
	if e == nil || target == nil {
		return e == target
	}

	// Nếu target là *CodeError, so sánh Code
	if targetCodeErr, ok := target.(*CodeError); ok {
		return e.Code == targetCodeErr.Code
	}

	// Fallback: Nếu target không phải *CodeError, không thể so sánh Is dựa trên code
	return false
}
