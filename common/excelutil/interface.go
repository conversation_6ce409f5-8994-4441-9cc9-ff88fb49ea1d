package excelutil

import "github.com/xuri/excelize/v2"

type CellStyle struct {
	Row   int
	Col   int
	Style *excelize.Style
}

type ConditionalStyle struct {
	Condition func(value interface{}) bool
	Style     *excelize.Style
}

type ColumnConfig struct {
	Header           string
	Width            float64
	Formatter        func(value interface{}) string
	ConditionalStyle *ConditionalStyle
}

type ExportConfig struct {
	FileName    string
	SheetName   string
	Columns     []ColumnConfig
	HeaderStyle *excelize.Style
}

type Exporter interface {
	GetHeaders() []string
	GetData() ([][]string, error)
	GetFileName() string
	GetCellStyles() []CellStyle
}

type ConfigurableExporter interface {
	GetConfig() ExportConfig
	GetRows() ([]map[string]interface{}, error)
}
