package excelutil

import "github.com/xuri/excelize/v2"

var (
	PositiveDifferenceStyle = &excelize.Style{
		Fill: excelize.Fill{
			Type:    "pattern",
			Color:   []string{"FFE6E6"},
			Pattern: 1,
		},
		Font: &excelize.Font{
			Color: "#CC0000",
		},
	}

	NegativeDifferenceStyle = &excelize.Style{
		Fill: excelize.Fill{
			Type:    "pattern",
			Color:   []string{"E6F7E6"},
			Pattern: 1,
		},
		Font: &excelize.Font{
			Color: "#00AA00",
		},
	}

	DefaultHeaderStyle = &excelize.Style{
		Font:      &excelize.Font{Bold: true},
		Fill:      excelize.Fill{Type: "pattern", Color: []string{"E0E0E0"}, Pattern: 1},
		Alignment: &excelize.Alignment{Horizontal: "center"},
	}
)

func CreateDifferenceConditionalStyle() *ConditionalStyle {
	return &ConditionalStyle{
		Condition: func(value interface{}) bool {
			if f, ok := value.(float64); ok {
				return f != 0
			}
			return false
		},
		Style: PositiveDifferenceStyle,
	}
}

func GetDifferenceStyle(difference float64) *excelize.Style {
	if difference > 0 {
		return PositiveDifferenceStyle
	} else if difference < 0 {
		return NegativeDifferenceStyle
	}
	return nil
}
