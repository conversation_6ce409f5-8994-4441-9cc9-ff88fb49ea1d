package excelutil

import "strconv"

func FloatFormatter(precision int) func(interface{}) string {
	return func(value interface{}) string {
		if f, ok := value.(float64); ok {
			return strconv.FormatFloat(f, 'f', precision, 64)
		}
		return ""
	}
}

func IntFormatter(value interface{}) string {
	if i, ok := value.(int); ok {
		return strconv.Itoa(i)
	}
	return ""
}

func StringFormatter(value interface{}) string {
	if s, ok := value.(string); ok {
		return s
	}
	return ""
}

func DifferenceFormatter(value interface{}) string {
	if f, ok := value.(float64); ok {
		formatted := strconv.FormatFloat(f, 'f', 2, 64)
		if f > 0 {
			return "+" + formatted
		}
		return formatted
	}
	return ""
}
