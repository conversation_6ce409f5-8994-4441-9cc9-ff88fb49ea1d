package excelutil

import (
	"fmt"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"time"

	"github.com/xuri/excelize/v2"
)

// Generate tạo ra một file Excel từ một Exporter, lưu vào server và ghi vào HTTP response.
func Generate(w io.Writer, exp Exporter, storagePath string) error {
	f := excelize.NewFile()
	sheetName := "Sheet1"

	// 1. Ghi Headers
	headers := exp.GetHeaders()
	for i, header := range headers {
		cell, _ := excelize.CoordinatesToCellName(i+1, 1)
		if err := f.SetCellValue(sheetName, cell, header); err != nil {
			return fmt.Errorf("failed to set header %s: %w", header, err)
		}
	}

	// Set header style (bold)
	headerStyle, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{Bold: true},
		Fill: excelize.Fill{Type: "pattern", Color: []string{"E0E0E0"}, Pattern: 1},
	})
	if err == nil {
		startCell, _ := excelize.CoordinatesToCellName(1, 1)
		endCell, _ := excelize.CoordinatesToCellName(len(headers), 1)
		f.SetCellStyle(sheetName, startCell, endCell, headerStyle)
	}

	// 2. Ghi Data
	data, err := exp.GetData()
	if err != nil {
		return fmt.Errorf("failed to get data for export: %w", err)
	}

	for rowIndex, rowData := range data {
		for colIndex, cellValue := range rowData {
			cell, _ := excelize.CoordinatesToCellName(colIndex+1, rowIndex+2)
			if err := f.SetCellValue(sheetName, cell, cellValue); err != nil {
				// Log lỗi ở đây nhưng vẫn tiếp tục
				fmt.Printf("failed to write cell %s: %v\n", cell, err)
			}
		}
	}

	// 3. Áp dụng custom cell styles
	cellStyles := exp.GetCellStyles()
	for _, cellStyle := range cellStyles {
		if cellStyle.Style != nil {
			styleID, err := f.NewStyle(cellStyle.Style)
			if err != nil {
				fmt.Printf("failed to create style: %v\n", err)
				continue
			}
			cell, _ := excelize.CoordinatesToCellName(cellStyle.Col, cellStyle.Row)
			if err := f.SetCellStyle(sheetName, cell, cell, styleID); err != nil {
				fmt.Printf("failed to apply style to cell %s: %v\n", cell, err)
			}
		}
	}

	// 4. Auto-resize columns
	for i := 1; i <= len(headers); i++ {
		colName, _ := excelize.ColumnNumberToName(i)
		f.SetColWidth(sheetName, colName, colName, 15)
	}

	// 5. Lưu file vào server trước (nếu có storagePath)
	if storagePath != "" {
		if err := os.MkdirAll(storagePath, 0755); err != nil {
			fmt.Printf("Warning: failed to create storage directory: %v\n", err)
		} else {
			fileName := fmt.Sprintf("%s_%d.xlsx", exp.GetFileName(), time.Now().Unix())
			filePath := filepath.Join(storagePath, fileName)
			if err := f.SaveAs(filePath); err != nil {
				fmt.Printf("Warning: failed to save file to server: %v\n", err)
			} else {
				fmt.Printf("Excel file saved to server: %s\n", filePath)
			}
		}
	}

	// 6. Ghi file vào HTTP response
	if err := f.Write(w); err != nil {
		return fmt.Errorf("failed to write excel file to writer: %w", err)
	}

	return nil
}

// GenerateAndSave tạo file Excel từ Exporter và lưu vào server
// Trả về đường dẫn file đã lưu và số lượng rows thực tế
func GenerateAndSave(exp Exporter, storagePath string) (filePath string, actualCount int64, err error) {
	// 1. Kiểm tra và tạo thư mục lưu trữ
	if storagePath == "" {
		return "", 0, fmt.Errorf("storage path is empty")
	}
	if err := os.MkdirAll(storagePath, 0755); err != nil {
		return "", 0, fmt.Errorf("failed to create storage directory: %w", err)
	}

	// 2. Tạo tên file với timestamp
	fileName := fmt.Sprintf("%s_%d.xlsx", exp.GetFileName(), time.Now().Unix())
	filePath = filepath.Join(storagePath, fileName)

	// 3. Tạo Excel file
	f := excelize.NewFile()
	defer func() {
		if closeErr := f.Close(); closeErr != nil {
			fmt.Printf("Error closing excelize file: %v\n", closeErr)
		}
	}()

	sheetName := "Sheet1"

	// 4. Ghi Headers với style
	headers := exp.GetHeaders()
	headerStyle, err := f.NewStyle(&excelize.Style{
		Font:      &excelize.Font{Bold: true},
		Fill:      excelize.Fill{Type: "pattern", Color: []string{"E0E0E0"}, Pattern: 1},
		Alignment: &excelize.Alignment{Horizontal: "center"},
	})
	if err != nil {
		headerStyle = 0 // Use default style if failed to create
	}

	for i, header := range headers {
		cell, _ := excelize.CoordinatesToCellName(i+1, 1)
		if err := f.SetCellValue(sheetName, cell, header); err != nil {
			return "", 0, fmt.Errorf("failed to set header %s: %w", header, err)
		}
		// Apply header style
		if headerStyle != 0 {
			f.SetCellStyle(sheetName, cell, cell, headerStyle)
		}
		// Set column width
		colName, _ := excelize.ColumnNumberToName(i + 1)
		f.SetColWidth(sheetName, colName, colName, 15)
	}

	// 5. Ghi Data
	data, err := exp.GetData()
	if err != nil {
		return "", 0, fmt.Errorf("failed to get data for export: %w", err)
	}

	actualCount = 0
	for rowIndex, rowData := range data {
		for colIndex, cellValue := range rowData {
			cell, _ := excelize.CoordinatesToCellName(colIndex+1, rowIndex+2)
			if err := f.SetCellValue(sheetName, cell, cellValue); err != nil {
				// Log lỗi nhưng tiếp tục
				fmt.Printf("failed to write cell %s: %v\n", cell, err)
			}
		}
		actualCount++
	}

	// 6. Lưu file
	if err := f.SaveAs(filePath); err != nil {
		return "", actualCount, fmt.Errorf("failed to save excel file: %w", err)
	}

	return filePath, actualCount, nil
}

// WriteExcelResponse là một hàm helper để thiết lập HTTP headers và gọi Generate (không lưu server).
func WriteExcelResponse(w http.ResponseWriter, r *http.Request, exp Exporter) {
	w.Header().Set("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	w.Header().Set("Content-Disposition", fmt.Sprintf("attachment; filename=%s.xlsx", exp.GetFileName()))

	if err := Generate(w, exp, ""); err != nil {
		// Log lỗi và trả về lỗi cho client
		http.Error(w, "Could not generate Excel file: "+err.Error(), http.StatusInternalServerError)
	}
}

func WriteExcelResponseWithStorage(w http.ResponseWriter, r *http.Request, exp Exporter, storagePath string) {
	w.Header().Set("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	w.Header().Set("Content-Disposition", fmt.Sprintf("attachment; filename=%s.xlsx", exp.GetFileName()))

	if err := Generate(w, exp, storagePath); err != nil {
		http.Error(w, "Could not generate Excel file: "+err.Error(), http.StatusInternalServerError)
	}
}

func GenerateFromConfig(w io.Writer, config ExportConfig, rows []map[string]interface{}, storagePath string) error {
	f := excelize.NewFile()
	sheetName := config.SheetName
	if sheetName == "" {
		sheetName = "Sheet1"
	}

	// If the sheet name is different from default "Sheet1", rename it
	if sheetName != "Sheet1" {
		if err := f.SetSheetName("Sheet1", sheetName); err != nil {
			return fmt.Errorf("failed to set sheet name %s: %w", sheetName, err)
		}
	}

	headerStyle := config.HeaderStyle
	if headerStyle == nil {
		headerStyle = DefaultHeaderStyle
	}

	headerStyleID, err := f.NewStyle(headerStyle)
	if err != nil {
		headerStyleID = 0
	}

	for i, col := range config.Columns {
		cell, _ := excelize.CoordinatesToCellName(i+1, 1)
		if err := f.SetCellValue(sheetName, cell, col.Header); err != nil {
			return fmt.Errorf("failed to set header %s: %w", col.Header, err)
		}
		if headerStyleID != 0 {
			f.SetCellStyle(sheetName, cell, cell, headerStyleID)
		}
		if col.Width > 0 {
			colName, _ := excelize.ColumnNumberToName(i + 1)
			f.SetColWidth(sheetName, colName, colName, col.Width)
		}
	}

	for rowIndex, row := range rows {
		for colIndex, col := range config.Columns {
			cell, _ := excelize.CoordinatesToCellName(colIndex+1, rowIndex+2)
			value := row[col.Header]

			var cellValue string
			if col.Formatter != nil {
				cellValue = col.Formatter(value)
			} else {
				cellValue = fmt.Sprintf("%v", value)
			}

			if err := f.SetCellValue(sheetName, cell, cellValue); err != nil {
				fmt.Printf("failed to write cell %s: %v\n", cell, err)
			}

			if col.ConditionalStyle != nil && col.ConditionalStyle.Condition(value) {
				var style *excelize.Style
				if f, ok := value.(float64); ok {
					style = GetDifferenceStyle(f)
				}
				if style != nil {
					styleID, err := f.NewStyle(style)
					if err == nil {
						f.SetCellStyle(sheetName, cell, cell, styleID)
					}
				}
			}
		}
	}

	if storagePath != "" {
		if err := os.MkdirAll(storagePath, 0755); err != nil {
			fmt.Printf("Warning: failed to create storage directory: %v\n", err)
		} else {
			fileName := fmt.Sprintf("%s_%d.xlsx", config.FileName, time.Now().Unix())
			filePath := filepath.Join(storagePath, fileName)
			if err := f.SaveAs(filePath); err != nil {
				fmt.Printf("Warning: failed to save file to server: %v\n", err)
			} else {
				fmt.Printf("Excel file saved to server: %s\n", filePath)
			}
		}
	}

	if err := f.Write(w); err != nil {
		return fmt.Errorf("failed to write excel file to writer: %w", err)
	}

	return nil
}

func WriteConfigurableExcelResponse(w http.ResponseWriter, r *http.Request, exp ConfigurableExporter, storagePath string) {
	config := exp.GetConfig()
	rows, err := exp.GetRows()
	if err != nil {
		http.Error(w, "Could not get data: "+err.Error(), http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	w.Header().Set("Content-Disposition", fmt.Sprintf("attachment; filename=%s.xlsx", config.FileName))

	if err := GenerateFromConfig(w, config, rows, storagePath); err != nil {
		http.Error(w, "Could not generate Excel file: "+err.Error(), http.StatusInternalServerError)
	}
}
