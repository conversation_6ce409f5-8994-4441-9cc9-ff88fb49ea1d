package cast

// Anys converts any kinds of values to a []any slice
func Anys[C any](in ...C) (out []any) {
	out = make([]any, len(in))

	for i := range in {
		out[i] = in[i]
	}

	return
}

// Anyss converts any kinds of values to a []string slice
func Anyss(value any) []string {
	switch v := value.(type) {
	case []any:
		result := make([]string, 0, len(v))
		for _, item := range v {
			if str, ok := item.(string); ok {
				result = append(result, str)
			}
		}
		return result
	case []string:
		return v
	default:
		return nil
	}
}
