package cast

import (
	"bytes"
	"encoding/json"
	"fmt"
	"github.com/modern-go/reflect2"
	"reflect"
	"sync"
)

// Atom casts a value to a map[string]any
func Atom(out *map[string]any, in any) (err error) {
	if reflect2.IsNil(in) {
		*out = nil
		return nil
	}

	var bytes []byte
	switch aux := in.(type) {
	case []byte:
		bytes = aux
	case string:
		bytes = []byte(aux)
	case map[string]any:
		*out = aux
		return nil
	default:
		// Nếu đầu vào là một struct, marshal nó thành byte
		bytes, err = json.Marshal(aux)
		if err != nil {
			return fmt.Errorf("failed to marshal struct: %w", err)
		}
	}

	err = json.Unmarshal(bytes, out)
	if err != nil {
		return fmt.Errorf("can not cast to map[string]any: %w", err)
	}

	return nil
}

type Serializer interface {
	MarshalSimpleTime() ([]byte, error)
	MarshalJSON() ([]byte, error)
}

// TODO refactor to optimize performance
func Atot(out interface{}, in Serializer) (err error) {
	if in == nil {
		return nil
	}

	bytes, err := in.MarshalSimpleTime()
	if err != nil {
		return fmt.Errorf("failed to marshal input: %w", err)
	}

	outValue := reflect.ValueOf(out)
	if outValue.Kind() != reflect.Ptr || outValue.IsNil() {
		return fmt.Errorf("out must be a non-nil pointer to a struct")
	}

	err = json.Unmarshal(bytes, outValue.Interface())
	if err != nil {
		return fmt.Errorf("can not cast to struct: %w", err)
	}

	return nil
}

var bufferPool = sync.Pool{
	New: func() interface{} {
		return new(bytes.Buffer)
	},
}

// ConvertViaJson copies data from a source (like an Ent entity implementing json.Marshaler)
// to a destination struct using JSON marshaling and unmarshaling.
// It utilizes a sync.Pool for buffer reuse to improve performance.
func ConvertViaJson(out interface{}, in interface{}) error {
	// Check if input is nil early
	if in == nil || (reflect.ValueOf(in).Kind() == reflect.Ptr && reflect.ValueOf(in).IsNil()) {
		return nil // Nothing to copy
	}

	// Check if output is a non-nil pointer
	outValue := reflect.ValueOf(out)
	if outValue.Kind() != reflect.Ptr || outValue.IsNil() {
		return fmt.Errorf("out must be a non-nil pointer")
	}

	// Get buffer from pool
	buffer := bufferPool.Get().(*bytes.Buffer)
	defer bufferPool.Put(buffer)
	buffer.Reset() // Ensure buffer is clean

	// Encode input to buffer
	encoder := json.NewEncoder(buffer)
	if err := encoder.Encode(in); err != nil {
		return fmt.Errorf("failed to marshal input: %w", err)
	}

	// Decode buffer to output
	decoder := json.NewDecoder(buffer)
	if err := decoder.Decode(outValue.Interface()); err != nil {
		return fmt.Errorf("failed to unmarshal to output: %w", err)
	}

	return nil
}

func EntViewToResp(out interface{}, in any) error {
	if in == nil {
		return nil
	}

	outValue := reflect.ValueOf(out)
	if outValue.Kind() != reflect.Ptr || outValue.IsNil() {
		return fmt.Errorf("out must be a non-nil pointer to a struct")
	}

	buffer := bufferPool.Get().(*bytes.Buffer)
	defer bufferPool.Put(buffer)
	buffer.Reset()

	err := json.NewEncoder(buffer).Encode(in)
	if err != nil {
		return fmt.Errorf("failed to marshal input: %w", err)
	}

	err = json.NewDecoder(buffer).Decode(outValue.Interface())
	if err != nil {
		return fmt.Errorf("can not cast to struct: %w", err)
	}

	return nil
}

// AtomNoZero casts a value to a map[string]any and removes zero values
func AtomNoZero(out *map[string]any, in any) (err error) {
	err = Atom(out, in)
	if err != nil {
		return err
	}

	// Duyệt qua *out và xóa các giá trị zero
	newMap := make(map[string]any)
	for k, v := range *out {
		if !isZero(v) {
			newMap[k] = v
		}
	}
	*out = newMap

	return nil
}

func isZero(v any) bool {
	rv := reflect.ValueOf(v)

	// Kiểm tra xem giá trị có hợp lệ không
	if !rv.IsValid() {
		return true // Coi giá trị không hợp lệ là zero
	}

	// Xử lý trường hợp con trỏ nil
	if rv.Kind() == reflect.Ptr && rv.IsNil() {
		return true
	}

	// Gọi IsZero() chỉ khi giá trị hợp lệ
	return rv.IsZero()
}

func MapToStruct(m map[string]interface{}, out interface{}) error {
	if m == nil {
		return nil
	}

	data, err := json.Marshal(m)
	if err != nil {
		return err
	}

	return json.Unmarshal(data, out)
}
