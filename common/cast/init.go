package cast

import (
	"fmt"
	"time"

	"github.com/jinzhu/copier"
)

var (
	location, _ = time.LoadLocation("Asia/Ho_Chi_Minh")
	timeLayout  = time.RFC3339Nano
	timeLayouts = []string{
		timeLayout,
		"2006-01-02T15:04:05.999999999",
		"2006-01-02 15:04:05.999999999",
		"2006-01-02 15:04:05",
		"2006-01-02",
	}
)

// timeToStringConverter chuyển đổi time.Time thành string
var timeToStringConverter = copier.TypeConverter{
	SrcType: time.Time{},
	DstType: copier.String,
	Fn: func(src interface{}) (interface{}, error) {
		t, ok := src.(time.Time)
		if !ok {
			return nil, fmt.Errorf("source value is not a time.Time")
		}
		if !t.Is<PERSON>ero() {
			return t.Format(timeLayout), nil
		}
		return "", nil
	},
}

// stringToTimeConverter chuyển đổi string thành time.Time
var stringToTimeConverter = copier.TypeConverter{
	SrcType: copier.String,
	DstType: time.Time{},
	Fn: func(src interface{}) (interface{}, error) {
		return parseTime(src, false)
	},
}

// stringToTimePtrConverter chuyển đổi string thành *time.Time
var stringToTimePtrConverter = copier.TypeConverter{
	SrcType: copier.String,
	DstType: &time.Time{},
	Fn: func(src interface{}) (interface{}, error) {
		return parseTime(src, true)
	},
}

var pointerStringToPointerTimeConverter = copier.TypeConverter{
	SrcType: (*string)(nil),
	DstType: (*time.Time)(nil),
	Fn: func(src interface{}) (interface{}, error) {
		if src == nil {
			return nil, nil
		}

		strPtr := src.(*string)
		if strPtr == nil {
			return nil, nil
		}

		timeValue, err := parseTime(*strPtr, false)
		if err != nil {
			return nil, err
		}

		// Kiểm tra nếu timeValue là nil
		if timeValue == nil {
			return nil, nil
		}

		// Ép kiểu an toàn
		t, ok := timeValue.(time.Time)
		if !ok {
			return nil, fmt.Errorf("không thể chuyển đổi thành time.Time")
		}

		return &t, nil
	},
}

var pointerStringToTimeConverter = copier.TypeConverter{
	SrcType: (*string)(nil),
	DstType: time.Time{},
	Fn: func(src interface{}) (interface{}, error) {
		if src == nil {
			return time.Time{}, nil
		}

		strPtr := src.(*string)
		if strPtr == nil {
			return time.Time{}, nil
		}

		timeValue, err := parseTime(*strPtr, false)
		if err != nil {
			return time.Time{}, err
		}

		// Kiểm tra nếu timeValue là nil
		if timeValue == nil {
			return time.Time{}, nil
		}

		// Ép kiểu an toàn
		t, ok := timeValue.(time.Time)
		if !ok {
			return time.Time{}, fmt.Errorf("không thể chuyển đổi thành time.Time")
		}

		return t, nil
	},
}

// pointerStringToStringConverter chuyển đổi *string thành string, với nil trở thành chuỗi rỗng
var pointerStringToStringConverter = copier.TypeConverter{
	SrcType: (*string)(nil),
	DstType: copier.String,
	Fn: func(src interface{}) (interface{}, error) {
		if src == nil {
			return "", nil
		}

		strPtr := src.(*string)
		if strPtr == nil {
			return "", nil
		}

		return *strPtr, nil
	},
}

// intPtrZeroToNilConverter chuyển đổi *int thành *int, với 0 trở thành nil
var intPtrZeroToNilConverter = copier.TypeConverter{
	SrcType: (*int)(nil),
	DstType: (*int)(nil),
	Fn: func(src interface{}) (interface{}, error) {
		if src == nil {
			return nil, nil
		}
		ptr, ok := src.(*int)
		if !ok {
			return nil, fmt.Errorf("source value is not a pointer to int")
		}
		if ptr != nil && *ptr == 0 {
			return nil, nil
		}
		return ptr, nil
	},
}

// zeroToNilConverter chuyển đổi int thành *int, với 0 trở thành nil
var zeroToNilConverter = copier.TypeConverter{
	SrcType: copier.Int,
	DstType: (*int)(nil),
	Fn: func(src interface{}) (interface{}, error) {
		if src == nil {
			return nil, nil
		}
		val, ok := src.(int)
		if !ok {
			return nil, fmt.Errorf("source value is not an int")
		}
		if val == 0 {
			return nil, nil
		}
		return &val, nil
	},
}

// emptyStringToNilConverter chuyển đổi string thành *string, với "" trở thành nil
var emptyStringToNilConverter = copier.TypeConverter{
	SrcType: copier.String,
	DstType: (*string)(nil),
	Fn: func(src interface{}) (interface{}, error) {
		if src == nil {
			return nil, nil
		}
		val, ok := src.(string)
		if !ok {
			return nil, fmt.Errorf("source value is not a string")
		}
		if val == "" {
			return nil, nil
		}
		return &val, nil
	},
}

var sliceStringConverter = copier.TypeConverter{
	SrcType: []string{},
	DstType: []string{},
	Fn: func(src interface{}) (interface{}, error) {
		s, ok := src.([]string)
		if !ok {
			return nil, fmt.Errorf("source value is not a []string")
		}
		return s, nil
	},
}

// Các Option cho copier
var (
	// TimeToString TODO xem xét loại bỏ
	TimeToString = copier.Option{
		DeepCopy:   true,
		Converters: []copier.TypeConverter{timeToStringConverter},
	}
	PayloadToEnt = copier.Option{
		DeepCopy: true,
		Converters: []copier.TypeConverter{
			stringToTimeConverter,
			stringToTimePtrConverter,
			pointerStringToPointerTimeConverter,
			pointerStringToTimeConverter,
			pointerStringToStringConverter,
			intPtrZeroToNilConverter,
			zeroToNilConverter,
			emptyStringToNilConverter,
			sliceStringConverter,
		},
	}
	ConvertInt = copier.Option{
		DeepCopy:   true,
		Converters: []copier.TypeConverter{intPtrZeroToNilConverter},
	}
)

// parseTime là hàm helper để phân tích chuỗi thời gian
func parseTime(src interface{}, isPtr bool) (interface{}, error) {
	s, ok := src.(string)
	if !ok {
		return nil, fmt.Errorf("source value is not a string")
	}
	if s == "" {
		return nil, nil
	}

	var t time.Time
	var err error

	for _, layout := range timeLayouts {
		t, err = time.Parse(layout, s)
		if err == nil {
			// Nếu parse thành công, chuyển đổi sang múi giờ HCM
			t = t.In(location)
			break
		}
	}

	if err != nil {
		// Nếu không parse được với bất kỳ layout nào, thử parse với location
		for _, layout := range timeLayouts {
			t, err = time.ParseInLocation(layout, s, location)
			if err == nil {
				break
			}
		}
	}

	if err != nil {
		return nil, fmt.Errorf("failed to parse string as time: %v", err)
	}

	if isPtr {
		return &t, nil
	}
	return t, nil
}

// ReqToEnt converts API request structs to Ent structs, handling type conversions.
func ReqToEnt(toValue interface{}, fromValue interface{}) error {
	return copier.CopyWithOption(toValue, fromValue, PayloadToEnt)
}

// ReqToModelInput converts API request structs to Model input structs.
// Uses basic copy for now, assumes direct field mapping.
func ReqToModelInput(toValue interface{}, fromValue interface{}) error {
	return copier.CopyWithOption(toValue, fromValue, PayloadToEnt)
}

// InputToEnt converts Model input structs to Ent structs.
// Uses the same converters as ReqToEnt to ensure consistency.
func InputToEnt(toValue interface{}, fromValue interface{}) error {
	return copier.CopyWithOption(toValue, fromValue, PayloadToEnt)
}

func CopyInputToEnt(toValue interface{}, fromValue interface{}) error {
	return copier.Copy(toValue, fromValue)
}

// ModelOutputToResp converts Model output structs to API response structs.
// Uses basic copy for now, assumes direct field mapping.
func ModelOutputToResp(toValue interface{}, fromValue interface{}) error {
	return copier.Copy(toValue, fromValue)
}
