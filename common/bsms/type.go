package bsms

type ZnsTemplate struct {
	Phone        string       `json:"phone"`
	TemplateID   string       `json:"template_id"`
	TemplateData TemplateData `json:"template_data"`
}

type TemplateData struct {
	ScheduleTime        string `json:"schedule_time"`
	PriceNeedToPaid     string `json:"price_need_to_paid"`
	Price               string `json:"price"`
	CustomerName        string `json:"customer_name"`
	CustomerID          string `json:"customer_ID"`
	CustomerPaymentTime string `json:"customer_payment_time"`
}

type ZnsAccessTokenResponse struct {
	AccessToken  string `json:"access_token"`
	RefreshToken string `json:"refresh_token"`
	ExpiresIn    string `json:"expires_in"`
}

type SmsAccessTokenResponse struct {
	Error   int       `json:"error"`
	Message string    `json:"message"`
	Data    TokenData `json:"data"`
}

type TokenData struct {
	AccessToken  string `json:"access_token"`
	RefreshToken string `json:"refresh_token"`
	ExpiredAt    int64  `json:"expired_at"`
}

type SmsSetting struct {
	SmsApiUrl    string `json:"sms_api_url"`
	SmsApiV2Url  string `json:"sms_api_v2_url"`
	SmsUsername  string `json:"sms_username"`
	SmsPassword  string `json:"sms_password"`
	SmsBrandName string `json:"sms_brand_name"`
	AccessToken  string `json:"access_token"`
	RefreshToken string `json:"refresh_token"`
}

type ZnsSetting struct {
	SecretKey       string `json:"secret_key"`
	AppID           string `json:"app_id"`
	UrlTemplate     string `json:"url_template"`
	UrlRefreshToken string `json:"url_refresh_token"`
	AccessToken     string `json:"access_token"`
	RefreshToken    string `json:"refresh_token"`
}

type EmailSetting struct {
	MIME       string `json:"mime"`
	SmtpServer string `json:"smtp_server"`
	SmtpPort   string `json:"smtp_port"`
	Email      string `json:"email"`
	Password   string `json:"password"`
}

type OtpSetting struct {
	Enabled            bool      `json:"enabled"`
	Length             int       `json:"length"`
	ExpireMinutes      int       `json:"expire_minutes"`
	WhitelistIp        []string  `json:"whitelist_ip"`
	DynamicIpAllowlist []string  `json:"dynamic_ip_allowlist"`
	Receivers          Receivers `json:"receivers"`
}

type Receivers struct {
	Type        string   `json:"type"`
	SingleEmail string   `json:"single_email"`
	EmailList   []string `json:"email_list"`
}
