package bsms

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"log"
	"net/http"
	"net/url"
	"strings"
	"sync"
)

type ZnsService struct {
	ZnsSetting
	accessToken string
}

var oneZns sync.Once
var znsInstance *ZnsService

func NewZnsService(setting ZnsSetting) *ZnsService {
	oneZns.Do(func() {
		znsInstance = &ZnsService{
			ZnsSetting: setting,
		}
		//znsInstance.refreshAccessToken()
	})

	return znsInstance
}

func (p *ZnsService) RefreshAccessToken() ZnsSetting {
	accessToken, err := p.GetAccessToken()
	if err != nil {
		log.Println("Error refreshing access token:", err)
		return p.ZnsSetting
	}
	p.ZnsSetting.AccessToken = accessToken.AccessToken
	p.ZnsSetting.RefreshToken = accessToken.RefreshToken
	return p.ZnsSetting
}

func FormatPhone(phone string) string {
	if len(phone) == 10 && phone[0] == '0' {
		phone = strings.TrimLeft(phone, "0")
		return "84" + phone
	}

	return phone
}

func (p *ZnsService) SendMessage(message *ZnsTemplate) (err error, errCode int, msgId string) {
	message.Phone = FormatPhone(message.Phone)

	err, errCode, msgId = p.sendZns(message)
	if err != nil {
		return err, 1, ""
	}

	if errCode != 0 {
		return fmt.Errorf("znsProvider send message error, code: %d", errCode), errCode, ""
	}

	return nil, 0, msgId
}

func (p *ZnsService) sendZns(data *ZnsTemplate) (err error, errCode int, msgId string) {
	var result struct {
		Error   int
		Message string
		Data    struct {
			MsgId       string `json:"msg_id"`
			SentTime    string `json:"sent_time"`
			SendingMode string `json:"sending_mode"`
			Quota       interface{}
		}
	}

	requestJSON, err := json.Marshal(data)
	if err != nil {
		return err, 1, ""
	}

	req, err := http.NewRequest("POST", p.ZnsSetting.UrlTemplate, bytes.NewBuffer(requestJSON))
	if err != nil {
		return err, 1, ""
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("access_token", p.ZnsSetting.AccessToken)

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return err, 1, ""
	}
	defer resp.Body.Close()

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return err, 1, ""
	}

	err = json.Unmarshal(body, &result)
	if err != nil {
		return err, 1, ""
	}

	return nil, result.Error, result.Data.MsgId
}

func (p *ZnsService) GetAccessToken() (accessToken ZnsAccessTokenResponse, err error) {
	data := url.Values{}
	data.Set("refresh_token", p.ZnsSetting.RefreshToken)
	data.Add("app_id", p.ZnsSetting.AppID)
	data.Add("grant_type", "refresh_token")

	client := &http.Client{}
	req, err := http.NewRequest("POST", p.ZnsSetting.UrlRefreshToken, strings.NewReader(data.Encode()))
	if err != nil {
		return
	}

	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	req.Header.Set("secret_key", p.ZnsSetting.SecretKey)

	resp, err := client.Do(req)
	if err != nil {
		return
	}
	defer resp.Body.Close()

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return
	}

	err = json.Unmarshal(body, &accessToken)
	if err != nil {
		return
	}
	return
}
