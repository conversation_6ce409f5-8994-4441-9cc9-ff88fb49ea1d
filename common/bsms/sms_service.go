package bsms

import (
	"bytes"
	"encoding/json"
	"github.com/zeromicro/go-zero/core/logx"
	"io"
	"io/ioutil"
	"log"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"sync"
)

type SmsService struct {
	SmsSetting
}

var once sync.Once
var instance *SmsService

func NewSmsService(setting SmsSetting) *SmsService {
	once.Do(func() {
		instance = &SmsService{
			SmsSetting: setting,
		}
	})

	return instance
}

func TrimZero(s string) string {
	if !strings.HasPrefix(s, "00") {
		return s
	}

	s = strings.TrimPrefix(s, "0")
	return TrimZero(s)
}

func (p *SmsService) GetSmsUrl(phone, content string) (string, error) {
	parameters := url.Values{}
	u, err := url.Parse(p.SmsApiUrl)
	if err != nil {
		return "", err
	}

	phone = TrimZero(phone)

	parameters.Add("u", p.SmsUsername)
	parameters.Add("pwd", p.AccessToken)
	parameters.Add("from", p.SmsBrandName)
	parameters.Add("phone", phone)
	parameters.Add("sms", content)

	u.RawQuery = parameters.Encode()
	return u.String(), nil
}

func (p *SmsService) SendMessage(phone string, content string) (error, int) {
	requestUrl, err := p.GetSmsUrl(phone, content)
	if err != nil {
		logx.Error(err)
		return err, 1
	}

	resp, err := http.Get(requestUrl)
	if err != nil {
		logx.Error(err)
		return err, 1
	}
	defer resp.Body.Close()

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return err, 1
	}
	errorCodeStr := string(body)
	errorCode, _ := strconv.Atoi(strings.TrimLeft(errorCodeStr, "\t"))

	return nil, errorCode
}

func (p *SmsService) GetAccessToken() (accessToken SmsAccessTokenResponse, err error) {
	// Tạo cấu trúc cho JSON data
	requestData := struct {
		Username string `json:"username"`
		Type     string `json:"type"`
	}{
		Username: p.SmsSetting.SmsUsername, // Giả sử bạn đã có trường này trong ZnsSetting
		Type:     "refresh_token",
	}

	// Chuyển đổi cấu trúc thành JSON
	jsonData, err := json.Marshal(requestData)
	if err != nil {
		return
	}

	// Tạo request
	client := &http.Client{}
	req, err := http.NewRequest("POST", p.SmsSetting.SmsApiV2Url, bytes.NewBuffer(jsonData))
	if err != nil {
		return
	}

	// Thiết lập headers
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Refresh-Token", p.SmsSetting.RefreshToken)

	// Thực hiện request
	resp, err := client.Do(req)
	if err != nil {
		return
	}
	defer resp.Body.Close()

	// Đọc response
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return
	}

	// Parse JSON response vào accessToken
	err = json.Unmarshal(body, &accessToken)
	if err != nil {
		return
	}

	return
}

func (p *SmsService) RefreshAccessToken() SmsSetting {
	accessToken, err := p.GetAccessToken()
	if err != nil {
		log.Println("Error refreshing access token:", err)
		return p.SmsSetting
	}
	p.SmsSetting.AccessToken = accessToken.Data.AccessToken
	p.SmsSetting.RefreshToken = accessToken.Data.RefreshToken
	return p.SmsSetting
}
