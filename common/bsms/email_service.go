package bsms

import (
	"crypto/tls"
	"fmt"
	"github.com/zeromicro/go-zero/core/logx"
	"net/smtp"
	"strings"
	"sync"
	"time"
)

type EmailService struct {
	EmailSetting
}

var onceEmail sync.Once
var emailInstance *EmailService

func NewEmailService(setting EmailSetting) *EmailService {
	onceEmail.Do(func() {
		emailInstance = &EmailService{
			EmailSetting: setting,
		}
	})
	return emailInstance
}

func (e *EmailService) SendEmail(to []string, subject, content string) error {
	// Create authentication
	auth := smtp.PlainAuth("", e.Email, e.Password, e.SmtpServer)

	// Setup headers
	headers := make(map[string]string)
	headers["From"] = e.Email
	headers["To"] = strings.Join(to, ",")
	headers["Subject"] = subject
	headers["MIME-Version"] = e.MIME
	headers["Content-Type"] = "text/html; charset=\"utf-8\""

	// Construct message
	message := ""
	for key, value := range headers {
		message += fmt.Sprintf("%s: %s\r\n", key, value)
	}
	message += "\r\n" + content

	// Server address
	serverAddress := fmt.Sprintf("%s:%s", e.SmtpServer, e.SmtpPort)

	// Configure TLS
	tlsConfig := &tls.Config{
		InsecureSkipVerify: true,
		ServerName:         e.SmtpServer,
	}

	// Create the connection
	client, err := smtp.Dial(serverAddress)
	if err != nil {
		logx.Errorf("Failed to create SMTP connection: %v", err)
		return err
	}
	defer client.Close()

	// Start TLS
	if err = client.StartTLS(tlsConfig); err != nil {
		logx.Errorf("Failed to start TLS: %v", err)
		return err
	}

	// Auth
	if err = client.Auth(auth); err != nil {
		logx.Errorf("Failed to auth: %v", err)
		return err
	}

	// Set the sender
	if err = client.Mail(e.Email); err != nil {
		logx.Errorf("Failed to set sender: %v", err)
		return err
	}

	// Set the recipients
	for _, recipient := range to {
		if err = client.Rcpt(recipient); err != nil {
			logx.Errorf("Failed to set recipient: %v", err)
			return err
		}
	}

	// Send the email body
	writer, err := client.Data()
	if err != nil {
		logx.Errorf("Failed to create mail writer: %v", err)
		return err
	}

	_, err = writer.Write([]byte(message))
	if err != nil {
		logx.Errorf("Failed to write mail content: %v", err)
		return err
	}

	err = writer.Close()
	if err != nil {
		logx.Errorf("Failed to close writer: %v", err)
		return err
	}

	return nil
}

func (e *EmailService) SendEmailOtp(to, otp, userName string) error {
	// Prepare email content
	emailTemplate := `
<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Yêu Cầu Cấp OTP</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.5;
            background-color: #f9f9f9;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        .email-container {
            background: #ffffff;
            max-width: 500px;
            width: 100%%;
            border-radius: 8px;
            border: 1px solid #dddddd;
            padding: 20px;
            font-size: 14px;
            color: #333333;
        }
        .otp {
            font-size: 18px;
            font-weight: bold;
            color: #0d6efd;
            text-align: center;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="email-container">
        <p>Admin,</p>
        <p>Tài khoản <strong>%s</strong> yêu cầu đăng nhập lúc %s.</p>
        <p>Mã OTP để cấp cho người dùng:</p>
        <div class="otp">%s</div>
        <p>Vui lòng xử lý ngay.</p>
    </div>
</body>
</html>`
	currentTime := time.Now().Format("15:04:05 02/01/2006")
	emailContent := fmt.Sprintf(emailTemplate,
		userName,
		currentTime,
		otp,
	)

	if err := e.SendSimpleEmail(
		to,
		"Mã xác nhận đăng nhập",
		emailContent,
	); err != nil {
		return fmt.Errorf("failed to send OTP: %v", err)
	}
	return nil
}

// SendSimpleEmail is a simplified version for sending basic emails
func (e *EmailService) SendSimpleEmail(to string, subject, content string) error {
	recipients := []string{to}
	return e.SendEmail(recipients, subject, content)
}

// ValidateEmail performs basic email validation
func (e *EmailService) ValidateEmail(email string) bool {
	return strings.Contains(email, "@") && strings.Contains(email, ".")
}
