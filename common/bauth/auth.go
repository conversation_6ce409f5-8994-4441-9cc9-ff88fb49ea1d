package bauth

import (
	"github.com/casbin/casbin/v2"
	"github.com/casbin/casbin/v2/persist"
	"github.com/zeromicro/go-zero/core/logx"
)

type Authorizer struct {
	Enforcer      *casbin.Enforcer
	adapter       persist.Adapter
	defaultPolicy string
}

type AuthorizerOption func(opt *Authorizer)

// WithAdapter returns a custom user unique identity option.
func WithAdapter(a persist.Adapter) AuthorizerOption {
	return func(opt *Authorizer) {
		opt.adapter = a
	}
}

func WithDefaultPolicy(policy string) AuthorizerOption {
	return func(opt *Authorizer) {
		opt.defaultPolicy = policy
	}
}

func MustNewAuthorizer(model string, opts ...AuthorizerOption) *Authorizer {
	auth := &Authorizer{}
	for _, opt := range opts {
		opt(auth)
	}

	//fileAdapter := fileadapter.NewAdapter(auth.defaultPolicy)
	enforcer, err := casbin.NewEnforcer(model, auth.adapter)
	if err != nil {
		logx.Error(err)
		panic("cannot initialize authorize service")
	}

	//Save policy to DB
	enforcer.SetAdapter(auth.adapter)
	err = enforcer.SavePolicy()
	if err != nil {
		logx.Errorf("cant convert policy from csv to db: %v", err)
	}

	_, _ = enforcer.AddRoleForUser("1", "admin")
	_, _ = enforcer.AddPermissionForUser("staff", "product_get", "true")
	_, _ = enforcer.AddPermissionForUser("staff", "product_list", "true")

	enforcer.EnableAutoSave(true)

	//enforcer.AddFunction("pceval", PolicyConditionEvaluate)
	auth.Enforcer = enforcer

	testRoot, reason, _ := enforcer.EnforceEx("1", "user::delete", struct {
		Name string
		Age  int
	}{Name: "Lam", Age: 3})
	logx.Infof("Root granted: %v - Reason: %v", testRoot, reason)

	return auth
}

func (c *Authorizer) Enforce(rvals ...interface{}) (bool, error) {
	return c.Enforcer.Enforce(rvals...)
}

func (c *Authorizer) AddPolicy(params ...interface{}) (bool, error) {
	return c.Enforcer.AddPolicy(params...)
}

func (c *Authorizer) RemovePolicy(params ...interface{}) (bool, error) {
	return c.Enforcer.RemovePolicy(params...)
}

func (c *Authorizer) AddRoleForUser(user string, role string) (bool, error) {
	return c.Enforcer.AddRoleForUser(user, role)
}

func (c *Authorizer) DeleteRoleForUser(user string, role string) (bool, error) {
	return c.Enforcer.DeleteRoleForUser(user, role)
}

func (c *Authorizer) GetRolesForUser(name string) ([]string, error) {
	return c.Enforcer.GetRolesForUser(name)
}
