package bauth

import (
	"bcare/common/ctxdata"
	"context"
	"strconv"
)

// Identifiable interface defines methods that an identity object must implement
type Identifiable interface {
	GetID() string
}

// ContextIdentity implements Identifiable interface using context user ID
type ContextIdentity struct {
	ctx context.Context
}

// GetID implements Identifiable interface
func (ci *ContextIdentity) GetID() string {
	return strconv.Itoa(ctxdata.GetUidFromCtx(ci.ctx))
}

// NewContextIdentity creates a new ContextIdentity
func NewContextIdentity(ctx context.Context) Identifiable {
	return &ContextIdentity{ctx: ctx}
}

// GetIdentityFromContext retrieves or creates an Identifiable object from context
func GetIdentityFromContext(ctx context.Context) Identifiable {
	if ctx == nil {
		return nil
	}
	// Try to get existing identity from context
	if id, ok := ctx.Value("identity").(Identifiable); ok {
		return id
	}
	// Create new identity if not exists
	return NewContextIdentity(ctx)
}
