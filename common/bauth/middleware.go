package bauth

import (
	"bcare/common/berr"
	"bcare/common/bresult"
	"github.com/casbin/casbin/v2"
	"github.com/zeromicro/go-zero/core/logx"
	"net/http"
)

type (
	// AuthorizerMiddleware stores the casbin handler
	AuthorizerMiddleware struct {
		enforcer *casbin.Enforcer
		uidField string
		domain   string
	}
	// AuthorizerMiddlewareOption represents an option.
	AuthorizerMiddlewareOption func(opt *AuthorizerMiddleware)
)

// WithUidField returns a custom user unique identity option.
func WithUidField(uidField string) AuthorizerMiddlewareOption {
	return func(opt *AuthorizerMiddleware) {
		opt.uidField = uidField
	}
}

// WithDomain returns a custom domain option.
func WithDomain(domain string) AuthorizerMiddlewareOption {
	return func(opt *AuthorizerMiddleware) {
		opt.domain = domain
	}
}

// NewAuthorizerMiddleware returns the authorizer, uses a Casbin enforcer as input
func NewAuthorizerMiddleware(auth *Authorizer, opts ...AuthorizerMiddlewareOption) func(http.Handler) http.Handler {
	a := &AuthorizerMiddleware{enforcer: auth.Enforcer}
	// init an AuthorizerMiddleware
	a.init(opts...)

	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(writer http.ResponseWriter, request *http.Request) {
			if !a.CheckPermission(request) {
				bresult.HttpResult(request, writer, nil, berr.NewErrCodeMsg(berr.PermissionDenied, "you are not authorized to perform this action"))
				return
			}
			next.ServeHTTP(writer, request)
		})
	}
}

func (a *AuthorizerMiddleware) init(opts ...AuthorizerMiddlewareOption) {
	a.uidField = "id"
	a.domain = "domain"
	for _, opt := range opts {
		opt(a)
	}
}

// GetUid gets the uid from the JWT Claims.
func (a *AuthorizerMiddleware) GetUid(r *http.Request) (string, bool) {
	uid, ok := r.Context().Value(a.uidField).(string)
	return uid, ok
}

// GetDomain returns the domain from the request.
func (a *AuthorizerMiddleware) GetDomain(r *http.Request) (string, bool) {
	domain, ok := r.Context().Value(a.domain).(string)
	return domain, ok
}

// CheckPermission checks the user/method/path combination from the request.
// Returns true (permission granted) or false (permission forbidden)
func (a *AuthorizerMiddleware) CheckPermission(r *http.Request) bool {
	uid, ok := a.GetUid(r)
	if !ok {
		return false
	}
	method := r.Method
	path := r.URL.Path
	var (
		allowed = false
		err     error
	)
	domain, withDomain := a.GetDomain(r)

	if withDomain {
		allowed, err = a.enforcer.Enforce(uid, domain, path, method)
	} else {
		allowed, err = a.enforcer.Enforce(uid, path, method)
	}

	if err != nil {
		logx.WithContext(r.Context()).Errorf("[AUTHORIZER] enforce err %s", err.Error())
	}
	return allowed
}

// RequirePermission returns the 403 Forbidden to the client.
func (a *AuthorizerMiddleware) RequirePermission(writer http.ResponseWriter) {
	writer.WriteHeader(http.StatusForbidden)
}
