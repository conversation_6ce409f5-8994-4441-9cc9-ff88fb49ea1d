package butils

import "time"

func ParseStringToTime(dateFormat, dateRequest string) (time.Time, error) {
	loc, _ := time.LoadLocation("Asia/Ho_Chi_Minh")
	date, e := time.ParseInLocation(dateFormat, dateRequest, loc)
	return date, e
}

// GetDateRange processes and returns the date range for queries
func GetDateRange(startDateStr, endDateStr string) (time.Time, time.Time) {
	startDate := time.Time{}
	endDate := time.Time{} // Default: next 7 days

	if startDateStr != "" {
		parsedStart, err := time.Parse(time.RFC3339Nano, startDateStr)
		if err == nil {
			startDate = parsedStart
		}
	}

	if endDateStr != "" {
		parsedEnd, err := time.Parse(time.RFC3339Nano, endDateStr)
		if err == nil {
			endDate = parsedEnd.AddDate(0, 0, 0)
		}
	}

	return startDate, endDate
}
