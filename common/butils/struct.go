package butils

import (
	"encoding/json"
	"fmt"
	"log"
	"reflect"
	"strings"
)

func StructToMap(obj interface{}) (map[string]interface{}, error) {
	var result map[string]interface{}
	if obj == nil {
		return make(map[string]interface{}), nil
	}

	// Check if obj is already a map
	if m, ok := obj.(map[string]interface{}); ok {
		result = make(map[string]interface{})
		for k, v := range m {
			result[k] = v
		}
	} else {
		// Use reflection to check the type
		val := reflect.ValueOf(obj)
		if val.Kind() == reflect.Ptr {
			if val.IsNil() {
				return make(map[string]interface{}), nil
			}
			val = val.Elem()
		}

		// If it's not a struct or map, return error or handle as single value
		if val.Kind() != reflect.Struct && val.Kind() != reflect.Map {
			return nil, fmt.Errorf("cannot convert %T to map[string]interface{}, expected struct or map", obj)
		}

		data, err := json.Marshal(obj)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal object: %w", err)
		}
		err = json.Unmarshal(data, &result)
		if err != nil {
			return nil, fmt.Errorf("failed to unmarshal json data: %w", err)
		}
	}

	fieldsToRemove := []string{
		"created_at",
		"updated_at",
		"edges",
		"version",
		"stage_history",
		"history",
	}
	for _, field := range fieldsToRemove {
		delete(result, field)
	}

	return result, nil
}

// DiffStructs so sánh hai struct và trả về hai map các trường đã thay đổi:
// beforeMap chứa giá trị cũ, afterMap chứa giá trị mới.
func DiffStructs(oldObj, newObj interface{}) (beforeMap map[string]interface{}, afterMap map[string]interface{}, err error) {
	beforeMap = make(map[string]interface{})
	afterMap = make(map[string]interface{})
	if oldObj == nil || newObj == nil {
		log.Printf("Warning: Diffing with nil object(s). Old: %v, New: %v", oldObj == nil, newObj == nil)
		return beforeMap, afterMap, nil
	}

	oldVal := reflect.ValueOf(oldObj)
	newVal := reflect.ValueOf(newObj)
	if oldVal.Kind() == reflect.Ptr {
		if oldVal.IsNil() {
			return beforeMap, afterMap, nil
		}
		oldVal = oldVal.Elem()
	}
	if newVal.Kind() == reflect.Ptr {
		if newVal.IsNil() {
			return beforeMap, afterMap, nil
		}
		newVal = newVal.Elem()
	}

	// Nếu đây là struct bên ngoài, xử lý các trường của nó
	if oldVal.Kind() == reflect.Struct && newVal.Kind() == reflect.Struct && oldVal.Type() == newVal.Type() {
		return diffStructsFlattened(oldVal, newVal, "")
	}

	// Nếu không phải struct hoặc không cùng kiểu, báo lỗi
	err = fmt.Errorf("cannot diff objects of different types or non-structs (Old: %s, New: %s)", oldVal.Type(), newVal.Type())
	return beforeMap, afterMap, err
}

func diffStructsFlattened(oldVal, newVal reflect.Value, prefix string) (beforeMap map[string]interface{}, afterMap map[string]interface{}, err error) {
	beforeMap = make(map[string]interface{})
	afterMap = make(map[string]interface{})

	typ := oldVal.Type()
	fieldsToIgnore := map[string]bool{"Edges": true, "UpdatedAt": true, "CreatedAt": true, "Version": true, "StageHistory": true, "History": true}

	for i := 0; i < oldVal.NumField(); i++ {
		field := typ.Field(i)
		fieldName := field.Name
		if !field.IsExported() || fieldsToIgnore[fieldName] {
			continue
		}

		oldFieldVal := oldVal.Field(i)
		newFieldVal := newVal.Field(i)

		// Lấy tên trường từ tag json
		jsonTag := field.Tag.Get("json")
		fieldKey := fieldName
		if jsonTag != "" && jsonTag != "-" {
			parts := strings.Split(jsonTag, ",")
			if len(parts) > 0 && parts[0] != "" {
				fieldKey = parts[0]
			}
		}

		// Xử lý các kiểu dữ liệu cơ bản và con trỏ đến kiểu cơ bản
		oldFieldInterface, newFieldInterface := getFieldValues(oldFieldVal, newFieldVal)
		if !reflect.DeepEqual(oldFieldInterface, newFieldInterface) {
			// Nếu là struct, cần đệ quy và làm phẳng các trường con
			if isNestedStruct(oldFieldVal) && isNestedStruct(newFieldVal) {
				// Đệ quy vào struct con, nhưng bỏ qua prefix để loại bỏ tên struct
				nestedBefore, nestedAfter, _ := diffStructsFlattened(getStructValue(oldFieldVal), getStructValue(newFieldVal), "")

				// Copy trực tiếp các giá trị từ struct con vào map kết quả
				for k, v := range nestedBefore {
					beforeMap[k] = v
				}
				for k, v := range nestedAfter {
					afterMap[k] = v
				}
			} else {
				// Các kiểu dữ liệu không phải struct
				beforeMap[fieldKey] = oldFieldInterface
				afterMap[fieldKey] = newFieldInterface
			}
		}
	}

	return beforeMap, afterMap, nil
}

// Kiểm tra xem một trường có phải là struct hoặc con trỏ đến struct không
func isNestedStruct(val reflect.Value) bool {
	if val.Kind() == reflect.Struct {
		return true
	}
	if val.Kind() == reflect.Ptr && !val.IsNil() && val.Elem().Kind() == reflect.Struct {
		return true
	}
	return false
}

// Lấy giá trị reflect.Value của struct (xử lý cả trường hợp con trỏ)
func getStructValue(val reflect.Value) reflect.Value {
	if val.Kind() == reflect.Ptr && !val.IsNil() {
		return val.Elem()
	}
	return val
}

// Lấy giá trị interface{} của một trường (xử lý cả trường hợp con trỏ)
func getFieldValues(oldVal, newVal reflect.Value) (interface{}, interface{}) {
	var oldInterface, newInterface interface{}

	if oldVal.Kind() == reflect.Ptr {
		if !oldVal.IsNil() {
			oldInterface = oldVal.Elem().Interface()
		}
	} else {
		oldInterface = oldVal.Interface()
	}

	if newVal.Kind() == reflect.Ptr {
		if !newVal.IsNil() {
			newInterface = newVal.Elem().Interface()
		}
	} else {
		newInterface = newVal.Interface()
	}

	return oldInterface, newInterface
}
