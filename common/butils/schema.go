package butils

import (
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"time"
)

type PostgresConfig struct {
	Host     string
	Port     string
	User     string
	Password string
	SourceDB string // Database to backup from (bcare_up)
	TargetDB string // Database to restore to (bcare_up_test)
}

// BackupPostgreSQL creates a backup of PostgreSQL database in plain SQL format
func BackupPostgreSQL(config PostgresConfig, backupDir string) error {
	if config.SourceDB == "" {
		return fmt.Errorf("source database name cannot be empty")
	}

	// Create backup directory if not exists
	if err := os.MkdirAll(backupDir, os.ModePerm); err != nil {
		return fmt.Errorf("failed to create backup directory: %v", err)
	}

	// Generate backup filename with timestamp
	timestamp := time.Now().Format("2006-01-02_15-04-05")
	filename := filepath.Join(backupDir, fmt.Sprintf("%s_%s.sql", config.SourceDB, timestamp))

	// Set PostgreSQL password as environment variable
	env := os.Environ()
	env = append(env, fmt.Sprintf("PGPASSWORD=%s", config.Password))

	// Construct pg_dump command with plain SQL format
	cmd := exec.Command("pg_dump",
		"-h", config.Host,
		"-p", config.Port,
		"-U", config.User,
		"-F", "p", // Plain text SQL format
		"--clean",     // Include DROP DATABASE command
		"--if-exists", // Add IF EXISTS to DROP commands
		"-v",          // Verbose mode
		"-f", filename,
		config.SourceDB,
	)

	// Set environment variables
	cmd.Env = env

	// Execute backup command
	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("backup failed: %v, output: %s", err, string(output))
	}

	fmt.Printf("Backup successfully created at: %s\n", filename)
	return nil
}

// RestorePostgreSQL restores a PostgreSQL database from a SQL backup file
func RestorePostgreSQL(config PostgresConfig, backupFile string) error {
	if config.TargetDB == "" {
		return fmt.Errorf("target database name cannot be empty")
	}

	// Check if backup file exists
	if _, err := os.Stat(backupFile); os.IsNotExist(err) {
		return fmt.Errorf("backup file does not exist: %s", backupFile)
	}

	// Set PostgreSQL password as environment variable
	env := os.Environ()
	env = append(env, fmt.Sprintf("PGPASSWORD=%s", config.Password))

	// First, connect to 'postgres' database to handle database creation/dropping
	dropCmd := exec.Command("psql",
		"-h", config.Host,
		"-p", config.Port,
		"-U", config.User,
		"-d", "postgres",
		"-c", fmt.Sprintf(`DROP DATABASE IF EXISTS "%s";`, config.TargetDB),
	)
	dropCmd.Env = env
	if output, err := dropCmd.CombinedOutput(); err != nil {
		return fmt.Errorf("failed to drop database: %v, output: %s", err, string(output))
	}

	createCmd := exec.Command("psql",
		"-h", config.Host,
		"-p", config.Port,
		"-U", config.User,
		"-d", "postgres",
		"-c", fmt.Sprintf(`CREATE DATABASE "%s" WITH TEMPLATE template0 LC_COLLATE 'en_US.UTF-8' LC_CTYPE 'en_US.UTF-8';`, config.TargetDB),
	)
	createCmd.Env = env
	if output, err := createCmd.CombinedOutput(); err != nil {
		return fmt.Errorf("failed to create database: %v, output: %s", err, string(output))
	}

	// Now restore the database using psql
	restoreCmd := exec.Command("psql",
		"-h", config.Host,
		"-p", config.Port,
		"-U", config.User,
		"-d", config.TargetDB,
		"-f", backupFile,
	)
	restoreCmd.Env = env

	// Execute restore command
	output, err := restoreCmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("restore failed: %v, output: %s", err, string(output))
	}

	fmt.Printf("Database restored successfully to %s from: %s\n", config.TargetDB, backupFile)
	return nil
}
