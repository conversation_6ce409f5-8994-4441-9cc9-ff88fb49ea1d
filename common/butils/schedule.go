package butils

import (
	"github.com/robfig/cron/v3"
	"log"
	"time"
)

type CronInfo struct {
	Minute     int
	Hour       int
	DayOfMonth string
	Month      string
	DayOfWeek  string
}

func CalculateNextRunTime(cronExpression string, fromDate time.Time, endDate *time.Time) time.Time {
	//var cronParser cron.Parser
	parser := cron.NewParser(cron.Minute | cron.Hour | cron.Dom | cron.Month | cron.Dow)
	schedule, err := parser.Parse(cronExpression)
	if err != nil {
		log.Printf("Error parsing cron expression %s: %v", cronExpression, err)
		return time.Now().Add(24 * time.Hour)
	}

	nextRun := schedule.Next(fromDate)

	if endDate != nil && nextRun.After(*endDate) {
		return time.Time{}
	}

	return nextRun
}
