package butils

import (
	"bcare/common/ctxdata"
	"context"
	"github.com/casbin/casbin/v2"
	"github.com/leekchan/accounting"
	"strconv"
)

func Contains(slice []string, str string) bool {
	for _, v := range slice {
		if v == str {
			return true
		}
	}
	return false
}

func IntContains(slice []int, str int) bool {
	for _, v := range slice {
		if v == str {
			return true
		}
	}
	return false
}

func FormatMoney(n int, symbol string) string {
	ac := accounting.Accounting{
		Symbol:         symbol,
		Precision:      0,
		Format:         "%v%s",
		FormatNegative: "- %v %s",
		FormatZero:     "0 %s",
	}

	return ac.FormatMoney(n)
}

func MaskPhone(phone string, ctx context.Context, Enforcer *casbin.Enforcer) string {
	currentUserId := ctxdata.GetUidFromCtx(ctx)

	hasRole, _ := Enforcer.HasRoleForUser(strconv.Itoa(currentUserId), "admin")
	if len(phone) >= 4 && !hasRole {
		phone = "**** " + phone[len(phone)-4:]
	}
	return phone
}
