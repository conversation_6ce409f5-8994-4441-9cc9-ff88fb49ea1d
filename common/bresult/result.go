package bresult

import (
	"bcare/common/berr"
	"errors"
	"net/http"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/rest/httpx"
)

// Http<PERSON><PERSON>ult writes the result or error to the response writer.
// It always writes HTTP 200 OK status code.
// Application errors are encoded in the response body using berr codes.
func HttpResult(r *http.Request, w http.ResponseWriter, resp interface{}, err error) {
	if err == nil {
		OkResult(r, w, resp)
	} else {
		ErrorResult(r, w, err)
	}
}

func OkResult(r *http.Request, w http.ResponseWriter, resp interface{}) {
	dataToSend := resp
	if resp == nil {
		dataToSend = NullJson{}
	}
	httpx.WriteJson(w, http.StatusOK, Success(dataToSend))
}

// ErrorResult writes an error response (HTTP 200 OK with error body).
// It logs internal error details but only returns defined code and message to the client.
func ErrorResult(r *http.Request, w http.ResponseWriter, err error) {
	var codeErr *berr.CodeError
	errCode := berr.ErrServerCommon.GetErrCode()
	errMsg := berr.ErrServerCommon.GetErrMsg()
	logMsg := "Error handled in ErrorResult"

	if errors.As(err, &codeErr) {
		errCode = codeErr.GetErrCode()
		errMsg = codeErr.GetErrMsg()

		fields := []logx.LogField{
			logx.Field("req_path", r.URL.Path),
			logx.Field("code", errCode),
			logx.Field("err_msg", codeErr.Msg),
		}
		if codeErr.Operation != "" {
			fields = append(fields, logx.Field("operation", codeErr.Operation))
		}
		if codeErr.Location != "" {
			fields = append(fields, logx.Field("location", codeErr.Location))
		}

		internalErr := codeErr.Unwrap()
		fullErrMsg := codeErr.Error()

		logger := logx.WithContext(r.Context()).WithCallerSkip(2).WithFields(fields...) // *** Thêm WithCallerSkip(2) ***

		if internalErr != nil {
			logger.Errorf("Application error: %s", fullErrMsg) // Dùng logger đã tạo
		} else {
			logger.Errorf("Application error (no wrapped): %s", codeErr.Msg) // Dùng logger đã tạo
		}

	} else {
		logMsg = "Unhandled internal error"
		internalErr := err

		logx.WithContext(r.Context()).
			WithCallerSkip(2).
			WithFields(logx.Field("req_path", r.URL.Path)).
			Errorf("%s: %+v", logMsg, internalErr)

		errCode = berr.ErrServerCommon.GetErrCode()
		// errMsg = berr.ErrServerCommon.GetErrMsg()
		// Temporary expose underlying error
		errMsg = internalErr.Error()
	}

	httpx.WriteJson(w, http.StatusOK, Error(errCode, errMsg))
}

func ParamErrorResult(r *http.Request, w http.ResponseWriter, err error) {
	errCode := berr.ErrRequestParam.GetErrCode()
	errMsg := berr.ErrRequestParam.GetErrMsg()

	logx.WithContext(r.Context()).
		WithCallerSkip(2).
		WithFields(
			logx.Field("req_path", r.URL.Path),
			logx.Field("error_details", err.Error()),
		).
		Error("Request parameter/validation error")

	httpx.WriteJson(w, http.StatusOK, Error(errCode, errMsg))
}
