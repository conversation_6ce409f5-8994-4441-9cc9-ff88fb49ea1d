package bresult

import (
	"bcare/common/berr"
)

type ResponseSuccess struct {
	Code    uint32      `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data"`
}

type ResponseError struct {
	Code    uint32 `json:"code"`
	Message string `json:"message"`
}

type NullJson struct{}

func Success(data interface{}) *ResponseSuccess {
	return &ResponseSuccess{berr.OK, "Success", data}
}

func Error(errCode uint32, errMsg string) *ResponseError {
	return &ResponseError{errCode, errMsg}
}
