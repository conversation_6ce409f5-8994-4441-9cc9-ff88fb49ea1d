// Code generated by BobGen psql v0.38.0. DO NOT EDIT.
// This file is meant to be re-generated in place and/or deleted at any time.

package bob

import (
	"context"
	"database/sql"
	"fmt"
	"io"
	"time"

	"github.com/stephenafamo/bob"
	"github.com/stephenafamo/bob/dialect/psql"
	"github.com/stephenafamo/bob/dialect/psql/dialect"
	"github.com/stephenafamo/bob/dialect/psql/dm"
	"github.com/stephenafamo/bob/dialect/psql/sm"
	"github.com/stephenafamo/bob/dialect/psql/um"
	"github.com/stephenafamo/bob/expr"
	"github.com/stephenafamo/bob/mods"
	"github.com/stephenafamo/bob/orm"
	"github.com/stephenafamo/bob/types/pgtypes"
)

// OperationMaterial is an object representing the database table.
type OperationMaterial struct {
	ID          int64           `db:"id,pk" `
	Version     int64           `db:"version" `
	CreatedAt   time.Time       `db:"created_at" `
	UpdatedAt   time.Time       `db:"updated_at" `
	Quantity    float64         `db:"quantity" `
	OperationID sql.Null[int64] `db:"operation_id" `
	MaterialID  sql.Null[int64] `db:"material_id" `

	R operationMaterialR `db:"-" `
}

// OperationMaterialSlice is an alias for a slice of pointers to OperationMaterial.
// This should almost always be used instead of []*OperationMaterial.
type OperationMaterialSlice []*OperationMaterial

// OperationMaterials contains methods to work with the operation_material table
var OperationMaterials = psql.NewTablex[*OperationMaterial, OperationMaterialSlice, *OperationMaterialSetter]("", "operation_material")

// OperationMaterialsQuery is a query on the operation_material table
type OperationMaterialsQuery = *psql.ViewQuery[*OperationMaterial, OperationMaterialSlice]

// operationMaterialR is where relationships are stored.
type operationMaterialR struct {
	Material  *Material  // operation_material.operation_material_material_material
	Operation *Operation // operation_material.operation_material_operation_operation
}

type operationMaterialColumnNames struct {
	ID          string
	Version     string
	CreatedAt   string
	UpdatedAt   string
	Quantity    string
	OperationID string
	MaterialID  string
}

var OperationMaterialColumns = buildOperationMaterialColumns("operation_material")

type operationMaterialColumns struct {
	tableAlias  string
	ID          psql.Expression
	Version     psql.Expression
	CreatedAt   psql.Expression
	UpdatedAt   psql.Expression
	Quantity    psql.Expression
	OperationID psql.Expression
	MaterialID  psql.Expression
}

func (c operationMaterialColumns) Alias() string {
	return c.tableAlias
}

func (operationMaterialColumns) AliasedAs(alias string) operationMaterialColumns {
	return buildOperationMaterialColumns(alias)
}

func buildOperationMaterialColumns(alias string) operationMaterialColumns {
	return operationMaterialColumns{
		tableAlias:  alias,
		ID:          psql.Quote(alias, "id"),
		Version:     psql.Quote(alias, "version"),
		CreatedAt:   psql.Quote(alias, "created_at"),
		UpdatedAt:   psql.Quote(alias, "updated_at"),
		Quantity:    psql.Quote(alias, "quantity"),
		OperationID: psql.Quote(alias, "operation_id"),
		MaterialID:  psql.Quote(alias, "material_id"),
	}
}

type operationMaterialWhere[Q psql.Filterable] struct {
	ID          psql.WhereMod[Q, int64]
	Version     psql.WhereMod[Q, int64]
	CreatedAt   psql.WhereMod[Q, time.Time]
	UpdatedAt   psql.WhereMod[Q, time.Time]
	Quantity    psql.WhereMod[Q, float64]
	OperationID psql.WhereNullMod[Q, int64]
	MaterialID  psql.WhereNullMod[Q, int64]
}

func (operationMaterialWhere[Q]) AliasedAs(alias string) operationMaterialWhere[Q] {
	return buildOperationMaterialWhere[Q](buildOperationMaterialColumns(alias))
}

func buildOperationMaterialWhere[Q psql.Filterable](cols operationMaterialColumns) operationMaterialWhere[Q] {
	return operationMaterialWhere[Q]{
		ID:          psql.Where[Q, int64](cols.ID),
		Version:     psql.Where[Q, int64](cols.Version),
		CreatedAt:   psql.Where[Q, time.Time](cols.CreatedAt),
		UpdatedAt:   psql.Where[Q, time.Time](cols.UpdatedAt),
		Quantity:    psql.Where[Q, float64](cols.Quantity),
		OperationID: psql.WhereNull[Q, int64](cols.OperationID),
		MaterialID:  psql.WhereNull[Q, int64](cols.MaterialID),
	}
}

var OperationMaterialErrors = &operationMaterialErrors{
	ErrUniqueOperationMaterialPkey: &UniqueConstraintError{
		schema:  "",
		table:   "operation_material",
		columns: []string{"id"},
		s:       "operation_material_pkey",
	},
}

type operationMaterialErrors struct {
	ErrUniqueOperationMaterialPkey *UniqueConstraintError
}

// OperationMaterialSetter is used for insert/upsert/update operations
// All values are optional, and do not have to be set
// Generated columns are not included
type OperationMaterialSetter struct {
	ID          *int64           `db:"id,pk" `
	Version     *int64           `db:"version" `
	CreatedAt   *time.Time       `db:"created_at" `
	UpdatedAt   *time.Time       `db:"updated_at" `
	Quantity    *float64         `db:"quantity" `
	OperationID *sql.Null[int64] `db:"operation_id" `
	MaterialID  *sql.Null[int64] `db:"material_id" `
}

func (s OperationMaterialSetter) SetColumns() []string {
	vals := make([]string, 0, 7)
	if s.ID != nil {
		vals = append(vals, "id")
	}

	if s.Version != nil {
		vals = append(vals, "version")
	}

	if s.CreatedAt != nil {
		vals = append(vals, "created_at")
	}

	if s.UpdatedAt != nil {
		vals = append(vals, "updated_at")
	}

	if s.Quantity != nil {
		vals = append(vals, "quantity")
	}

	if s.OperationID != nil {
		vals = append(vals, "operation_id")
	}

	if s.MaterialID != nil {
		vals = append(vals, "material_id")
	}

	return vals
}

func (s OperationMaterialSetter) Overwrite(t *OperationMaterial) {
	if s.ID != nil {
		t.ID = *s.ID
	}
	if s.Version != nil {
		t.Version = *s.Version
	}
	if s.CreatedAt != nil {
		t.CreatedAt = *s.CreatedAt
	}
	if s.UpdatedAt != nil {
		t.UpdatedAt = *s.UpdatedAt
	}
	if s.Quantity != nil {
		t.Quantity = *s.Quantity
	}
	if s.OperationID != nil {
		t.OperationID = *s.OperationID
	}
	if s.MaterialID != nil {
		t.MaterialID = *s.MaterialID
	}
}

func (s *OperationMaterialSetter) Apply(q *dialect.InsertQuery) {
	q.AppendHooks(func(ctx context.Context, exec bob.Executor) (context.Context, error) {
		return OperationMaterials.BeforeInsertHooks.RunHooks(ctx, exec, s)
	})

	q.AppendValues(bob.ExpressionFunc(func(ctx context.Context, w io.Writer, d bob.Dialect, start int) ([]any, error) {
		vals := make([]bob.Expression, 7)
		if s.ID != nil {
			vals[0] = psql.Arg(*s.ID)
		} else {
			vals[0] = psql.Raw("DEFAULT")
		}

		if s.Version != nil {
			vals[1] = psql.Arg(*s.Version)
		} else {
			vals[1] = psql.Raw("DEFAULT")
		}

		if s.CreatedAt != nil {
			vals[2] = psql.Arg(*s.CreatedAt)
		} else {
			vals[2] = psql.Raw("DEFAULT")
		}

		if s.UpdatedAt != nil {
			vals[3] = psql.Arg(*s.UpdatedAt)
		} else {
			vals[3] = psql.Raw("DEFAULT")
		}

		if s.Quantity != nil {
			vals[4] = psql.Arg(*s.Quantity)
		} else {
			vals[4] = psql.Raw("DEFAULT")
		}

		if s.OperationID != nil {
			vals[5] = psql.Arg(*s.OperationID)
		} else {
			vals[5] = psql.Raw("DEFAULT")
		}

		if s.MaterialID != nil {
			vals[6] = psql.Arg(*s.MaterialID)
		} else {
			vals[6] = psql.Raw("DEFAULT")
		}

		return bob.ExpressSlice(ctx, w, d, start, vals, "", ", ", "")
	}))
}

func (s OperationMaterialSetter) UpdateMod() bob.Mod[*dialect.UpdateQuery] {
	return um.Set(s.Expressions()...)
}

func (s OperationMaterialSetter) Expressions(prefix ...string) []bob.Expression {
	exprs := make([]bob.Expression, 0, 7)

	if s.ID != nil {
		exprs = append(exprs, expr.Join{Sep: " = ", Exprs: []bob.Expression{
			psql.Quote(append(prefix, "id")...),
			psql.Arg(s.ID),
		}})
	}

	if s.Version != nil {
		exprs = append(exprs, expr.Join{Sep: " = ", Exprs: []bob.Expression{
			psql.Quote(append(prefix, "version")...),
			psql.Arg(s.Version),
		}})
	}

	if s.CreatedAt != nil {
		exprs = append(exprs, expr.Join{Sep: " = ", Exprs: []bob.Expression{
			psql.Quote(append(prefix, "created_at")...),
			psql.Arg(s.CreatedAt),
		}})
	}

	if s.UpdatedAt != nil {
		exprs = append(exprs, expr.Join{Sep: " = ", Exprs: []bob.Expression{
			psql.Quote(append(prefix, "updated_at")...),
			psql.Arg(s.UpdatedAt),
		}})
	}

	if s.Quantity != nil {
		exprs = append(exprs, expr.Join{Sep: " = ", Exprs: []bob.Expression{
			psql.Quote(append(prefix, "quantity")...),
			psql.Arg(s.Quantity),
		}})
	}

	if s.OperationID != nil {
		exprs = append(exprs, expr.Join{Sep: " = ", Exprs: []bob.Expression{
			psql.Quote(append(prefix, "operation_id")...),
			psql.Arg(s.OperationID),
		}})
	}

	if s.MaterialID != nil {
		exprs = append(exprs, expr.Join{Sep: " = ", Exprs: []bob.Expression{
			psql.Quote(append(prefix, "material_id")...),
			psql.Arg(s.MaterialID),
		}})
	}

	return exprs
}

// FindOperationMaterial retrieves a single record by primary key
// If cols is empty Find will return all columns.
func FindOperationMaterial(ctx context.Context, exec bob.Executor, IDPK int64, cols ...string) (*OperationMaterial, error) {
	if len(cols) == 0 {
		return OperationMaterials.Query(
			SelectWhere.OperationMaterials.ID.EQ(IDPK),
		).One(ctx, exec)
	}

	return OperationMaterials.Query(
		SelectWhere.OperationMaterials.ID.EQ(IDPK),
		sm.Columns(OperationMaterials.Columns().Only(cols...)),
	).One(ctx, exec)
}

// OperationMaterialExists checks the presence of a single record by primary key
func OperationMaterialExists(ctx context.Context, exec bob.Executor, IDPK int64) (bool, error) {
	return OperationMaterials.Query(
		SelectWhere.OperationMaterials.ID.EQ(IDPK),
	).Exists(ctx, exec)
}

// AfterQueryHook is called after OperationMaterial is retrieved from the database
func (o *OperationMaterial) AfterQueryHook(ctx context.Context, exec bob.Executor, queryType bob.QueryType) error {
	var err error

	switch queryType {
	case bob.QueryTypeSelect:
		ctx, err = OperationMaterials.AfterSelectHooks.RunHooks(ctx, exec, OperationMaterialSlice{o})
	case bob.QueryTypeInsert:
		ctx, err = OperationMaterials.AfterInsertHooks.RunHooks(ctx, exec, OperationMaterialSlice{o})
	case bob.QueryTypeUpdate:
		ctx, err = OperationMaterials.AfterUpdateHooks.RunHooks(ctx, exec, OperationMaterialSlice{o})
	case bob.QueryTypeDelete:
		ctx, err = OperationMaterials.AfterDeleteHooks.RunHooks(ctx, exec, OperationMaterialSlice{o})
	}

	return err
}

// primaryKeyVals returns the primary key values of the OperationMaterial
func (o *OperationMaterial) primaryKeyVals() bob.Expression {
	return psql.Arg(o.ID)
}

func (o *OperationMaterial) pkEQ() dialect.Expression {
	return psql.Quote("operation_material", "id").EQ(bob.ExpressionFunc(func(ctx context.Context, w io.Writer, d bob.Dialect, start int) ([]any, error) {
		return o.primaryKeyVals().WriteSQL(ctx, w, d, start)
	}))
}

// Update uses an executor to update the OperationMaterial
func (o *OperationMaterial) Update(ctx context.Context, exec bob.Executor, s *OperationMaterialSetter) error {
	v, err := OperationMaterials.Update(s.UpdateMod(), um.Where(o.pkEQ())).One(ctx, exec)
	if err != nil {
		return err
	}

	o.R = v.R
	*o = *v

	return nil
}

// Delete deletes a single OperationMaterial record with an executor
func (o *OperationMaterial) Delete(ctx context.Context, exec bob.Executor) error {
	_, err := OperationMaterials.Delete(dm.Where(o.pkEQ())).Exec(ctx, exec)
	return err
}

// Reload refreshes the OperationMaterial using the executor
func (o *OperationMaterial) Reload(ctx context.Context, exec bob.Executor) error {
	o2, err := OperationMaterials.Query(
		SelectWhere.OperationMaterials.ID.EQ(o.ID),
	).One(ctx, exec)
	if err != nil {
		return err
	}
	o2.R = o.R
	*o = *o2

	return nil
}

// AfterQueryHook is called after OperationMaterialSlice is retrieved from the database
func (o OperationMaterialSlice) AfterQueryHook(ctx context.Context, exec bob.Executor, queryType bob.QueryType) error {
	var err error

	switch queryType {
	case bob.QueryTypeSelect:
		ctx, err = OperationMaterials.AfterSelectHooks.RunHooks(ctx, exec, o)
	case bob.QueryTypeInsert:
		ctx, err = OperationMaterials.AfterInsertHooks.RunHooks(ctx, exec, o)
	case bob.QueryTypeUpdate:
		ctx, err = OperationMaterials.AfterUpdateHooks.RunHooks(ctx, exec, o)
	case bob.QueryTypeDelete:
		ctx, err = OperationMaterials.AfterDeleteHooks.RunHooks(ctx, exec, o)
	}

	return err
}

func (o OperationMaterialSlice) pkIN() dialect.Expression {
	if len(o) == 0 {
		return psql.Raw("NULL")
	}

	return psql.Quote("operation_material", "id").In(bob.ExpressionFunc(func(ctx context.Context, w io.Writer, d bob.Dialect, start int) ([]any, error) {
		pkPairs := make([]bob.Expression, len(o))
		for i, row := range o {
			pkPairs[i] = row.primaryKeyVals()
		}
		return bob.ExpressSlice(ctx, w, d, start, pkPairs, "", ", ", "")
	}))
}

// copyMatchingRows finds models in the given slice that have the same primary key
// then it first copies the existing relationships from the old model to the new model
// and then replaces the old model in the slice with the new model
func (o OperationMaterialSlice) copyMatchingRows(from ...*OperationMaterial) {
	for i, old := range o {
		for _, new := range from {
			if new.ID != old.ID {
				continue
			}
			new.R = old.R
			o[i] = new
			break
		}
	}
}

// UpdateMod modifies an update query with "WHERE primary_key IN (o...)"
func (o OperationMaterialSlice) UpdateMod() bob.Mod[*dialect.UpdateQuery] {
	return bob.ModFunc[*dialect.UpdateQuery](func(q *dialect.UpdateQuery) {
		q.AppendHooks(func(ctx context.Context, exec bob.Executor) (context.Context, error) {
			return OperationMaterials.BeforeUpdateHooks.RunHooks(ctx, exec, o)
		})

		q.AppendLoader(bob.LoaderFunc(func(ctx context.Context, exec bob.Executor, retrieved any) error {
			var err error
			switch retrieved := retrieved.(type) {
			case *OperationMaterial:
				o.copyMatchingRows(retrieved)
			case []*OperationMaterial:
				o.copyMatchingRows(retrieved...)
			case OperationMaterialSlice:
				o.copyMatchingRows(retrieved...)
			default:
				// If the retrieved value is not a OperationMaterial or a slice of OperationMaterial
				// then run the AfterUpdateHooks on the slice
				_, err = OperationMaterials.AfterUpdateHooks.RunHooks(ctx, exec, o)
			}

			return err
		}))

		q.AppendWhere(o.pkIN())
	})
}

// DeleteMod modifies an delete query with "WHERE primary_key IN (o...)"
func (o OperationMaterialSlice) DeleteMod() bob.Mod[*dialect.DeleteQuery] {
	return bob.ModFunc[*dialect.DeleteQuery](func(q *dialect.DeleteQuery) {
		q.AppendHooks(func(ctx context.Context, exec bob.Executor) (context.Context, error) {
			return OperationMaterials.BeforeDeleteHooks.RunHooks(ctx, exec, o)
		})

		q.AppendLoader(bob.LoaderFunc(func(ctx context.Context, exec bob.Executor, retrieved any) error {
			var err error
			switch retrieved := retrieved.(type) {
			case *OperationMaterial:
				o.copyMatchingRows(retrieved)
			case []*OperationMaterial:
				o.copyMatchingRows(retrieved...)
			case OperationMaterialSlice:
				o.copyMatchingRows(retrieved...)
			default:
				// If the retrieved value is not a OperationMaterial or a slice of OperationMaterial
				// then run the AfterDeleteHooks on the slice
				_, err = OperationMaterials.AfterDeleteHooks.RunHooks(ctx, exec, o)
			}

			return err
		}))

		q.AppendWhere(o.pkIN())
	})
}

func (o OperationMaterialSlice) UpdateAll(ctx context.Context, exec bob.Executor, vals OperationMaterialSetter) error {
	if len(o) == 0 {
		return nil
	}

	_, err := OperationMaterials.Update(vals.UpdateMod(), o.UpdateMod()).All(ctx, exec)
	return err
}

func (o OperationMaterialSlice) DeleteAll(ctx context.Context, exec bob.Executor) error {
	if len(o) == 0 {
		return nil
	}

	_, err := OperationMaterials.Delete(o.DeleteMod()).Exec(ctx, exec)
	return err
}

func (o OperationMaterialSlice) ReloadAll(ctx context.Context, exec bob.Executor) error {
	if len(o) == 0 {
		return nil
	}

	o2, err := OperationMaterials.Query(sm.Where(o.pkIN())).All(ctx, exec)
	if err != nil {
		return err
	}

	o.copyMatchingRows(o2...)

	return nil
}

type operationMaterialJoins[Q dialect.Joinable] struct {
	typ       string
	Material  modAs[Q, materialColumns]
	Operation modAs[Q, operationColumns]
}

func (j operationMaterialJoins[Q]) aliasedAs(alias string) operationMaterialJoins[Q] {
	return buildOperationMaterialJoins[Q](buildOperationMaterialColumns(alias), j.typ)
}

func buildOperationMaterialJoins[Q dialect.Joinable](cols operationMaterialColumns, typ string) operationMaterialJoins[Q] {
	return operationMaterialJoins[Q]{
		typ: typ,
		Material: modAs[Q, materialColumns]{
			c: MaterialColumns,
			f: func(to materialColumns) bob.Mod[Q] {
				mods := make(mods.QueryMods[Q], 0, 1)

				{
					mods = append(mods, dialect.Join[Q](typ, Materials.Name().As(to.Alias())).On(
						to.ID.EQ(cols.MaterialID),
					))
				}

				return mods
			},
		},
		Operation: modAs[Q, operationColumns]{
			c: OperationColumns,
			f: func(to operationColumns) bob.Mod[Q] {
				mods := make(mods.QueryMods[Q], 0, 1)

				{
					mods = append(mods, dialect.Join[Q](typ, Operations.Name().As(to.Alias())).On(
						to.ID.EQ(cols.OperationID),
					))
				}

				return mods
			},
		},
	}
}

// Material starts a query for related objects on material
func (o *OperationMaterial) Material(mods ...bob.Mod[*dialect.SelectQuery]) MaterialsQuery {
	return Materials.Query(append(mods,
		sm.Where(MaterialColumns.ID.EQ(psql.Arg(o.MaterialID))),
	)...)
}

func (os OperationMaterialSlice) Material(mods ...bob.Mod[*dialect.SelectQuery]) MaterialsQuery {
	pkMaterialID := make(pgtypes.Array[sql.Null[int64]], len(os))
	for i, o := range os {
		pkMaterialID[i] = o.MaterialID
	}
	PKArgExpr := psql.Select(sm.Columns(
		psql.F("unnest", psql.Cast(psql.Arg(pkMaterialID), "bigint[]")),
	))

	return Materials.Query(append(mods,
		sm.Where(psql.Group(MaterialColumns.ID).OP("IN", PKArgExpr)),
	)...)
}

// Operation starts a query for related objects on operation
func (o *OperationMaterial) Operation(mods ...bob.Mod[*dialect.SelectQuery]) OperationsQuery {
	return Operations.Query(append(mods,
		sm.Where(OperationColumns.ID.EQ(psql.Arg(o.OperationID))),
	)...)
}

func (os OperationMaterialSlice) Operation(mods ...bob.Mod[*dialect.SelectQuery]) OperationsQuery {
	pkOperationID := make(pgtypes.Array[sql.Null[int64]], len(os))
	for i, o := range os {
		pkOperationID[i] = o.OperationID
	}
	PKArgExpr := psql.Select(sm.Columns(
		psql.F("unnest", psql.Cast(psql.Arg(pkOperationID), "bigint[]")),
	))

	return Operations.Query(append(mods,
		sm.Where(psql.Group(OperationColumns.ID).OP("IN", PKArgExpr)),
	)...)
}

func (o *OperationMaterial) Preload(name string, retrieved any) error {
	if o == nil {
		return nil
	}

	switch name {
	case "Material":
		rel, ok := retrieved.(*Material)
		if !ok {
			return fmt.Errorf("operationMaterial cannot load %T as %q", retrieved, name)
		}

		o.R.Material = rel

		if rel != nil {
			rel.R.OperationMaterials = OperationMaterialSlice{o}
		}
		return nil
	case "Operation":
		rel, ok := retrieved.(*Operation)
		if !ok {
			return fmt.Errorf("operationMaterial cannot load %T as %q", retrieved, name)
		}

		o.R.Operation = rel

		if rel != nil {
			rel.R.OperationMaterials = OperationMaterialSlice{o}
		}
		return nil
	default:
		return fmt.Errorf("operationMaterial has no relationship %q", name)
	}
}

type operationMaterialPreloader struct {
	Material  func(...psql.PreloadOption) psql.Preloader
	Operation func(...psql.PreloadOption) psql.Preloader
}

func buildOperationMaterialPreloader() operationMaterialPreloader {
	return operationMaterialPreloader{
		Material: func(opts ...psql.PreloadOption) psql.Preloader {
			return psql.Preload[*Material, MaterialSlice](orm.Relationship{
				Name: "Material",
				Sides: []orm.RelSide{
					{
						From: TableNames.OperationMaterials,
						To:   TableNames.Materials,
						FromColumns: []string{
							ColumnNames.OperationMaterials.MaterialID,
						},
						ToColumns: []string{
							ColumnNames.Materials.ID,
						},
					},
				},
			}, Materials.Columns().Names(), opts...)
		},
		Operation: func(opts ...psql.PreloadOption) psql.Preloader {
			return psql.Preload[*Operation, OperationSlice](orm.Relationship{
				Name: "Operation",
				Sides: []orm.RelSide{
					{
						From: TableNames.OperationMaterials,
						To:   TableNames.Operations,
						FromColumns: []string{
							ColumnNames.OperationMaterials.OperationID,
						},
						ToColumns: []string{
							ColumnNames.Operations.ID,
						},
					},
				},
			}, Operations.Columns().Names(), opts...)
		},
	}
}

type operationMaterialThenLoader[Q orm.Loadable] struct {
	Material  func(...bob.Mod[*dialect.SelectQuery]) orm.Loader[Q]
	Operation func(...bob.Mod[*dialect.SelectQuery]) orm.Loader[Q]
}

func buildOperationMaterialThenLoader[Q orm.Loadable]() operationMaterialThenLoader[Q] {
	type MaterialLoadInterface interface {
		LoadMaterial(context.Context, bob.Executor, ...bob.Mod[*dialect.SelectQuery]) error
	}
	type OperationLoadInterface interface {
		LoadOperation(context.Context, bob.Executor, ...bob.Mod[*dialect.SelectQuery]) error
	}

	return operationMaterialThenLoader[Q]{
		Material: thenLoadBuilder[Q](
			"Material",
			func(ctx context.Context, exec bob.Executor, retrieved MaterialLoadInterface, mods ...bob.Mod[*dialect.SelectQuery]) error {
				return retrieved.LoadMaterial(ctx, exec, mods...)
			},
		),
		Operation: thenLoadBuilder[Q](
			"Operation",
			func(ctx context.Context, exec bob.Executor, retrieved OperationLoadInterface, mods ...bob.Mod[*dialect.SelectQuery]) error {
				return retrieved.LoadOperation(ctx, exec, mods...)
			},
		),
	}
}

// LoadMaterial loads the operationMaterial's Material into the .R struct
func (o *OperationMaterial) LoadMaterial(ctx context.Context, exec bob.Executor, mods ...bob.Mod[*dialect.SelectQuery]) error {
	if o == nil {
		return nil
	}

	// Reset the relationship
	o.R.Material = nil

	related, err := o.Material(mods...).One(ctx, exec)
	if err != nil {
		return err
	}

	related.R.OperationMaterials = OperationMaterialSlice{o}

	o.R.Material = related
	return nil
}

// LoadMaterial loads the operationMaterial's Material into the .R struct
func (os OperationMaterialSlice) LoadMaterial(ctx context.Context, exec bob.Executor, mods ...bob.Mod[*dialect.SelectQuery]) error {
	if len(os) == 0 {
		return nil
	}

	materials, err := os.Material(mods...).All(ctx, exec)
	if err != nil {
		return err
	}

	for _, o := range os {
		for _, rel := range materials {
			if o.MaterialID.V != rel.ID {
				continue
			}

			rel.R.OperationMaterials = append(rel.R.OperationMaterials, o)

			o.R.Material = rel
			break
		}
	}

	return nil
}

// LoadOperation loads the operationMaterial's Operation into the .R struct
func (o *OperationMaterial) LoadOperation(ctx context.Context, exec bob.Executor, mods ...bob.Mod[*dialect.SelectQuery]) error {
	if o == nil {
		return nil
	}

	// Reset the relationship
	o.R.Operation = nil

	related, err := o.Operation(mods...).One(ctx, exec)
	if err != nil {
		return err
	}

	related.R.OperationMaterials = OperationMaterialSlice{o}

	o.R.Operation = related
	return nil
}

// LoadOperation loads the operationMaterial's Operation into the .R struct
func (os OperationMaterialSlice) LoadOperation(ctx context.Context, exec bob.Executor, mods ...bob.Mod[*dialect.SelectQuery]) error {
	if len(os) == 0 {
		return nil
	}

	operations, err := os.Operation(mods...).All(ctx, exec)
	if err != nil {
		return err
	}

	for _, o := range os {
		for _, rel := range operations {
			if o.OperationID.V != rel.ID {
				continue
			}

			rel.R.OperationMaterials = append(rel.R.OperationMaterials, o)

			o.R.Operation = rel
			break
		}
	}

	return nil
}

func attachOperationMaterialMaterial0(ctx context.Context, exec bob.Executor, count int, operationMaterial0 *OperationMaterial, material1 *Material) (*OperationMaterial, error) {
	setter := &OperationMaterialSetter{
		MaterialID: func() *sql.Null[int64] {
			v := sql.Null[int64]{V: material1.ID, Valid: true}
			return &v
		}(),
	}

	err := operationMaterial0.Update(ctx, exec, setter)
	if err != nil {
		return nil, fmt.Errorf("attachOperationMaterialMaterial0: %w", err)
	}

	return operationMaterial0, nil
}

func (operationMaterial0 *OperationMaterial) InsertMaterial(ctx context.Context, exec bob.Executor, related *MaterialSetter) error {
	material1, err := Materials.Insert(related).One(ctx, exec)
	if err != nil {
		return fmt.Errorf("inserting related objects: %w", err)
	}

	_, err = attachOperationMaterialMaterial0(ctx, exec, 1, operationMaterial0, material1)
	if err != nil {
		return err
	}

	operationMaterial0.R.Material = material1

	material1.R.OperationMaterials = append(material1.R.OperationMaterials, operationMaterial0)

	return nil
}

func (operationMaterial0 *OperationMaterial) AttachMaterial(ctx context.Context, exec bob.Executor, material1 *Material) error {
	var err error

	_, err = attachOperationMaterialMaterial0(ctx, exec, 1, operationMaterial0, material1)
	if err != nil {
		return err
	}

	operationMaterial0.R.Material = material1

	material1.R.OperationMaterials = append(material1.R.OperationMaterials, operationMaterial0)

	return nil
}

func attachOperationMaterialOperation0(ctx context.Context, exec bob.Executor, count int, operationMaterial0 *OperationMaterial, operation1 *Operation) (*OperationMaterial, error) {
	setter := &OperationMaterialSetter{
		OperationID: func() *sql.Null[int64] {
			v := sql.Null[int64]{V: operation1.ID, Valid: true}
			return &v
		}(),
	}

	err := operationMaterial0.Update(ctx, exec, setter)
	if err != nil {
		return nil, fmt.Errorf("attachOperationMaterialOperation0: %w", err)
	}

	return operationMaterial0, nil
}

func (operationMaterial0 *OperationMaterial) InsertOperation(ctx context.Context, exec bob.Executor, related *OperationSetter) error {
	operation1, err := Operations.Insert(related).One(ctx, exec)
	if err != nil {
		return fmt.Errorf("inserting related objects: %w", err)
	}

	_, err = attachOperationMaterialOperation0(ctx, exec, 1, operationMaterial0, operation1)
	if err != nil {
		return err
	}

	operationMaterial0.R.Operation = operation1

	operation1.R.OperationMaterials = append(operation1.R.OperationMaterials, operationMaterial0)

	return nil
}

func (operationMaterial0 *OperationMaterial) AttachOperation(ctx context.Context, exec bob.Executor, operation1 *Operation) error {
	var err error

	_, err = attachOperationMaterialOperation0(ctx, exec, 1, operationMaterial0, operation1)
	if err != nil {
		return err
	}

	operationMaterial0.R.Operation = operation1

	operation1.R.OperationMaterials = append(operation1.R.OperationMaterials, operationMaterial0)

	return nil
}
