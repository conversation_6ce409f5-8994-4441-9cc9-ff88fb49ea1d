// Code generated by BobGen psql v0.38.0. DO NOT EDIT.
// This file is meant to be re-generated in place and/or deleted at any time.

package bob

import (
	"context"
	"database/sql"
	"fmt"
	"io"
	"time"

	"github.com/stephenafamo/bob"
	"github.com/stephenafamo/bob/dialect/psql"
	"github.com/stephenafamo/bob/dialect/psql/dialect"
	"github.com/stephenafamo/bob/dialect/psql/dm"
	"github.com/stephenafamo/bob/dialect/psql/sm"
	"github.com/stephenafamo/bob/dialect/psql/um"
	"github.com/stephenafamo/bob/expr"
	"github.com/stephenafamo/bob/mods"
	"github.com/stephenafamo/bob/orm"
	"github.com/stephenafamo/bob/types/pgtypes"
)

// Material is an object representing the database table.
type Material struct {
	ID                     int64               `db:"id,pk" `
	DeletedAt              sql.Null[time.Time] `db:"deleted_at" `
	Status                 int16               `db:"status" `
	Version                int64               `db:"version" `
	CreatedAt              time.Time           `db:"created_at" `
	UpdatedAt              time.Time           `db:"updated_at" `
	Code                   sql.Null[string]    `db:"code" `
	Name                   string              `db:"name" `
	Unit                   string              `db:"unit" `
	PackagingSpecification sql.Null[string]    `db:"packaging_specification" `
	Description            sql.Null[string]    `db:"description" `
	CostPrice              sql.Null[float64]   `db:"cost_price" `
	Kind                   string              `db:"kind" `

	R materialR `db:"-" `
}

// MaterialSlice is an alias for a slice of pointers to Material.
// This should almost always be used instead of []*Material.
type MaterialSlice []*Material

// Materials contains methods to work with the material table
var Materials = psql.NewTablex[*Material, MaterialSlice, *MaterialSetter]("", "material")

// MaterialsQuery is a query on the material table
type MaterialsQuery = *psql.ViewQuery[*Material, MaterialSlice]

// materialR is where relationships are stored.
type materialR struct {
	MaterialUsages     MaterialUsageSlice     // material_usage.material_usage_material_material
	OperationMaterials OperationMaterialSlice // operation_material.operation_material_material_material
}

type materialColumnNames struct {
	ID                     string
	DeletedAt              string
	Status                 string
	Version                string
	CreatedAt              string
	UpdatedAt              string
	Code                   string
	Name                   string
	Unit                   string
	PackagingSpecification string
	Description            string
	CostPrice              string
	Kind                   string
}

var MaterialColumns = buildMaterialColumns("material")

type materialColumns struct {
	tableAlias             string
	ID                     psql.Expression
	DeletedAt              psql.Expression
	Status                 psql.Expression
	Version                psql.Expression
	CreatedAt              psql.Expression
	UpdatedAt              psql.Expression
	Code                   psql.Expression
	Name                   psql.Expression
	Unit                   psql.Expression
	PackagingSpecification psql.Expression
	Description            psql.Expression
	CostPrice              psql.Expression
	Kind                   psql.Expression
}

func (c materialColumns) Alias() string {
	return c.tableAlias
}

func (materialColumns) AliasedAs(alias string) materialColumns {
	return buildMaterialColumns(alias)
}

func buildMaterialColumns(alias string) materialColumns {
	return materialColumns{
		tableAlias:             alias,
		ID:                     psql.Quote(alias, "id"),
		DeletedAt:              psql.Quote(alias, "deleted_at"),
		Status:                 psql.Quote(alias, "status"),
		Version:                psql.Quote(alias, "version"),
		CreatedAt:              psql.Quote(alias, "created_at"),
		UpdatedAt:              psql.Quote(alias, "updated_at"),
		Code:                   psql.Quote(alias, "code"),
		Name:                   psql.Quote(alias, "name"),
		Unit:                   psql.Quote(alias, "unit"),
		PackagingSpecification: psql.Quote(alias, "packaging_specification"),
		Description:            psql.Quote(alias, "description"),
		CostPrice:              psql.Quote(alias, "cost_price"),
		Kind:                   psql.Quote(alias, "kind"),
	}
}

type materialWhere[Q psql.Filterable] struct {
	ID                     psql.WhereMod[Q, int64]
	DeletedAt              psql.WhereNullMod[Q, time.Time]
	Status                 psql.WhereMod[Q, int16]
	Version                psql.WhereMod[Q, int64]
	CreatedAt              psql.WhereMod[Q, time.Time]
	UpdatedAt              psql.WhereMod[Q, time.Time]
	Code                   psql.WhereNullMod[Q, string]
	Name                   psql.WhereMod[Q, string]
	Unit                   psql.WhereMod[Q, string]
	PackagingSpecification psql.WhereNullMod[Q, string]
	Description            psql.WhereNullMod[Q, string]
	CostPrice              psql.WhereNullMod[Q, float64]
	Kind                   psql.WhereMod[Q, string]
}

func (materialWhere[Q]) AliasedAs(alias string) materialWhere[Q] {
	return buildMaterialWhere[Q](buildMaterialColumns(alias))
}

func buildMaterialWhere[Q psql.Filterable](cols materialColumns) materialWhere[Q] {
	return materialWhere[Q]{
		ID:                     psql.Where[Q, int64](cols.ID),
		DeletedAt:              psql.WhereNull[Q, time.Time](cols.DeletedAt),
		Status:                 psql.Where[Q, int16](cols.Status),
		Version:                psql.Where[Q, int64](cols.Version),
		CreatedAt:              psql.Where[Q, time.Time](cols.CreatedAt),
		UpdatedAt:              psql.Where[Q, time.Time](cols.UpdatedAt),
		Code:                   psql.WhereNull[Q, string](cols.Code),
		Name:                   psql.Where[Q, string](cols.Name),
		Unit:                   psql.Where[Q, string](cols.Unit),
		PackagingSpecification: psql.WhereNull[Q, string](cols.PackagingSpecification),
		Description:            psql.WhereNull[Q, string](cols.Description),
		CostPrice:              psql.WhereNull[Q, float64](cols.CostPrice),
		Kind:                   psql.Where[Q, string](cols.Kind),
	}
}

var MaterialErrors = &materialErrors{
	ErrUniqueMaterialPkey: &UniqueConstraintError{
		schema:  "",
		table:   "material",
		columns: []string{"id"},
		s:       "material_pkey",
	},
}

type materialErrors struct {
	ErrUniqueMaterialPkey *UniqueConstraintError
}

// MaterialSetter is used for insert/upsert/update operations
// All values are optional, and do not have to be set
// Generated columns are not included
type MaterialSetter struct {
	ID                     *int64               `db:"id,pk" `
	DeletedAt              *sql.Null[time.Time] `db:"deleted_at" `
	Status                 *int16               `db:"status" `
	Version                *int64               `db:"version" `
	CreatedAt              *time.Time           `db:"created_at" `
	UpdatedAt              *time.Time           `db:"updated_at" `
	Code                   *sql.Null[string]    `db:"code" `
	Name                   *string              `db:"name" `
	Unit                   *string              `db:"unit" `
	PackagingSpecification *sql.Null[string]    `db:"packaging_specification" `
	Description            *sql.Null[string]    `db:"description" `
	CostPrice              *sql.Null[float64]   `db:"cost_price" `
	Kind                   *string              `db:"kind" `
}

func (s MaterialSetter) SetColumns() []string {
	vals := make([]string, 0, 13)
	if s.ID != nil {
		vals = append(vals, "id")
	}

	if s.DeletedAt != nil {
		vals = append(vals, "deleted_at")
	}

	if s.Status != nil {
		vals = append(vals, "status")
	}

	if s.Version != nil {
		vals = append(vals, "version")
	}

	if s.CreatedAt != nil {
		vals = append(vals, "created_at")
	}

	if s.UpdatedAt != nil {
		vals = append(vals, "updated_at")
	}

	if s.Code != nil {
		vals = append(vals, "code")
	}

	if s.Name != nil {
		vals = append(vals, "name")
	}

	if s.Unit != nil {
		vals = append(vals, "unit")
	}

	if s.PackagingSpecification != nil {
		vals = append(vals, "packaging_specification")
	}

	if s.Description != nil {
		vals = append(vals, "description")
	}

	if s.CostPrice != nil {
		vals = append(vals, "cost_price")
	}

	if s.Kind != nil {
		vals = append(vals, "kind")
	}

	return vals
}

func (s MaterialSetter) Overwrite(t *Material) {
	if s.ID != nil {
		t.ID = *s.ID
	}
	if s.DeletedAt != nil {
		t.DeletedAt = *s.DeletedAt
	}
	if s.Status != nil {
		t.Status = *s.Status
	}
	if s.Version != nil {
		t.Version = *s.Version
	}
	if s.CreatedAt != nil {
		t.CreatedAt = *s.CreatedAt
	}
	if s.UpdatedAt != nil {
		t.UpdatedAt = *s.UpdatedAt
	}
	if s.Code != nil {
		t.Code = *s.Code
	}
	if s.Name != nil {
		t.Name = *s.Name
	}
	if s.Unit != nil {
		t.Unit = *s.Unit
	}
	if s.PackagingSpecification != nil {
		t.PackagingSpecification = *s.PackagingSpecification
	}
	if s.Description != nil {
		t.Description = *s.Description
	}
	if s.CostPrice != nil {
		t.CostPrice = *s.CostPrice
	}
	if s.Kind != nil {
		t.Kind = *s.Kind
	}
}

func (s *MaterialSetter) Apply(q *dialect.InsertQuery) {
	q.AppendHooks(func(ctx context.Context, exec bob.Executor) (context.Context, error) {
		return Materials.BeforeInsertHooks.RunHooks(ctx, exec, s)
	})

	q.AppendValues(bob.ExpressionFunc(func(ctx context.Context, w io.Writer, d bob.Dialect, start int) ([]any, error) {
		vals := make([]bob.Expression, 13)
		if s.ID != nil {
			vals[0] = psql.Arg(*s.ID)
		} else {
			vals[0] = psql.Raw("DEFAULT")
		}

		if s.DeletedAt != nil {
			vals[1] = psql.Arg(*s.DeletedAt)
		} else {
			vals[1] = psql.Raw("DEFAULT")
		}

		if s.Status != nil {
			vals[2] = psql.Arg(*s.Status)
		} else {
			vals[2] = psql.Raw("DEFAULT")
		}

		if s.Version != nil {
			vals[3] = psql.Arg(*s.Version)
		} else {
			vals[3] = psql.Raw("DEFAULT")
		}

		if s.CreatedAt != nil {
			vals[4] = psql.Arg(*s.CreatedAt)
		} else {
			vals[4] = psql.Raw("DEFAULT")
		}

		if s.UpdatedAt != nil {
			vals[5] = psql.Arg(*s.UpdatedAt)
		} else {
			vals[5] = psql.Raw("DEFAULT")
		}

		if s.Code != nil {
			vals[6] = psql.Arg(*s.Code)
		} else {
			vals[6] = psql.Raw("DEFAULT")
		}

		if s.Name != nil {
			vals[7] = psql.Arg(*s.Name)
		} else {
			vals[7] = psql.Raw("DEFAULT")
		}

		if s.Unit != nil {
			vals[8] = psql.Arg(*s.Unit)
		} else {
			vals[8] = psql.Raw("DEFAULT")
		}

		if s.PackagingSpecification != nil {
			vals[9] = psql.Arg(*s.PackagingSpecification)
		} else {
			vals[9] = psql.Raw("DEFAULT")
		}

		if s.Description != nil {
			vals[10] = psql.Arg(*s.Description)
		} else {
			vals[10] = psql.Raw("DEFAULT")
		}

		if s.CostPrice != nil {
			vals[11] = psql.Arg(*s.CostPrice)
		} else {
			vals[11] = psql.Raw("DEFAULT")
		}

		if s.Kind != nil {
			vals[12] = psql.Arg(*s.Kind)
		} else {
			vals[12] = psql.Raw("DEFAULT")
		}

		return bob.ExpressSlice(ctx, w, d, start, vals, "", ", ", "")
	}))
}

func (s MaterialSetter) UpdateMod() bob.Mod[*dialect.UpdateQuery] {
	return um.Set(s.Expressions()...)
}

func (s MaterialSetter) Expressions(prefix ...string) []bob.Expression {
	exprs := make([]bob.Expression, 0, 13)

	if s.ID != nil {
		exprs = append(exprs, expr.Join{Sep: " = ", Exprs: []bob.Expression{
			psql.Quote(append(prefix, "id")...),
			psql.Arg(s.ID),
		}})
	}

	if s.DeletedAt != nil {
		exprs = append(exprs, expr.Join{Sep: " = ", Exprs: []bob.Expression{
			psql.Quote(append(prefix, "deleted_at")...),
			psql.Arg(s.DeletedAt),
		}})
	}

	if s.Status != nil {
		exprs = append(exprs, expr.Join{Sep: " = ", Exprs: []bob.Expression{
			psql.Quote(append(prefix, "status")...),
			psql.Arg(s.Status),
		}})
	}

	if s.Version != nil {
		exprs = append(exprs, expr.Join{Sep: " = ", Exprs: []bob.Expression{
			psql.Quote(append(prefix, "version")...),
			psql.Arg(s.Version),
		}})
	}

	if s.CreatedAt != nil {
		exprs = append(exprs, expr.Join{Sep: " = ", Exprs: []bob.Expression{
			psql.Quote(append(prefix, "created_at")...),
			psql.Arg(s.CreatedAt),
		}})
	}

	if s.UpdatedAt != nil {
		exprs = append(exprs, expr.Join{Sep: " = ", Exprs: []bob.Expression{
			psql.Quote(append(prefix, "updated_at")...),
			psql.Arg(s.UpdatedAt),
		}})
	}

	if s.Code != nil {
		exprs = append(exprs, expr.Join{Sep: " = ", Exprs: []bob.Expression{
			psql.Quote(append(prefix, "code")...),
			psql.Arg(s.Code),
		}})
	}

	if s.Name != nil {
		exprs = append(exprs, expr.Join{Sep: " = ", Exprs: []bob.Expression{
			psql.Quote(append(prefix, "name")...),
			psql.Arg(s.Name),
		}})
	}

	if s.Unit != nil {
		exprs = append(exprs, expr.Join{Sep: " = ", Exprs: []bob.Expression{
			psql.Quote(append(prefix, "unit")...),
			psql.Arg(s.Unit),
		}})
	}

	if s.PackagingSpecification != nil {
		exprs = append(exprs, expr.Join{Sep: " = ", Exprs: []bob.Expression{
			psql.Quote(append(prefix, "packaging_specification")...),
			psql.Arg(s.PackagingSpecification),
		}})
	}

	if s.Description != nil {
		exprs = append(exprs, expr.Join{Sep: " = ", Exprs: []bob.Expression{
			psql.Quote(append(prefix, "description")...),
			psql.Arg(s.Description),
		}})
	}

	if s.CostPrice != nil {
		exprs = append(exprs, expr.Join{Sep: " = ", Exprs: []bob.Expression{
			psql.Quote(append(prefix, "cost_price")...),
			psql.Arg(s.CostPrice),
		}})
	}

	if s.Kind != nil {
		exprs = append(exprs, expr.Join{Sep: " = ", Exprs: []bob.Expression{
			psql.Quote(append(prefix, "kind")...),
			psql.Arg(s.Kind),
		}})
	}

	return exprs
}

// FindMaterial retrieves a single record by primary key
// If cols is empty Find will return all columns.
func FindMaterial(ctx context.Context, exec bob.Executor, IDPK int64, cols ...string) (*Material, error) {
	if len(cols) == 0 {
		return Materials.Query(
			SelectWhere.Materials.ID.EQ(IDPK),
		).One(ctx, exec)
	}

	return Materials.Query(
		SelectWhere.Materials.ID.EQ(IDPK),
		sm.Columns(Materials.Columns().Only(cols...)),
	).One(ctx, exec)
}

// MaterialExists checks the presence of a single record by primary key
func MaterialExists(ctx context.Context, exec bob.Executor, IDPK int64) (bool, error) {
	return Materials.Query(
		SelectWhere.Materials.ID.EQ(IDPK),
	).Exists(ctx, exec)
}

// AfterQueryHook is called after Material is retrieved from the database
func (o *Material) AfterQueryHook(ctx context.Context, exec bob.Executor, queryType bob.QueryType) error {
	var err error

	switch queryType {
	case bob.QueryTypeSelect:
		ctx, err = Materials.AfterSelectHooks.RunHooks(ctx, exec, MaterialSlice{o})
	case bob.QueryTypeInsert:
		ctx, err = Materials.AfterInsertHooks.RunHooks(ctx, exec, MaterialSlice{o})
	case bob.QueryTypeUpdate:
		ctx, err = Materials.AfterUpdateHooks.RunHooks(ctx, exec, MaterialSlice{o})
	case bob.QueryTypeDelete:
		ctx, err = Materials.AfterDeleteHooks.RunHooks(ctx, exec, MaterialSlice{o})
	}

	return err
}

// primaryKeyVals returns the primary key values of the Material
func (o *Material) primaryKeyVals() bob.Expression {
	return psql.Arg(o.ID)
}

func (o *Material) pkEQ() dialect.Expression {
	return psql.Quote("material", "id").EQ(bob.ExpressionFunc(func(ctx context.Context, w io.Writer, d bob.Dialect, start int) ([]any, error) {
		return o.primaryKeyVals().WriteSQL(ctx, w, d, start)
	}))
}

// Update uses an executor to update the Material
func (o *Material) Update(ctx context.Context, exec bob.Executor, s *MaterialSetter) error {
	v, err := Materials.Update(s.UpdateMod(), um.Where(o.pkEQ())).One(ctx, exec)
	if err != nil {
		return err
	}

	o.R = v.R
	*o = *v

	return nil
}

// Delete deletes a single Material record with an executor
func (o *Material) Delete(ctx context.Context, exec bob.Executor) error {
	_, err := Materials.Delete(dm.Where(o.pkEQ())).Exec(ctx, exec)
	return err
}

// Reload refreshes the Material using the executor
func (o *Material) Reload(ctx context.Context, exec bob.Executor) error {
	o2, err := Materials.Query(
		SelectWhere.Materials.ID.EQ(o.ID),
	).One(ctx, exec)
	if err != nil {
		return err
	}
	o2.R = o.R
	*o = *o2

	return nil
}

// AfterQueryHook is called after MaterialSlice is retrieved from the database
func (o MaterialSlice) AfterQueryHook(ctx context.Context, exec bob.Executor, queryType bob.QueryType) error {
	var err error

	switch queryType {
	case bob.QueryTypeSelect:
		ctx, err = Materials.AfterSelectHooks.RunHooks(ctx, exec, o)
	case bob.QueryTypeInsert:
		ctx, err = Materials.AfterInsertHooks.RunHooks(ctx, exec, o)
	case bob.QueryTypeUpdate:
		ctx, err = Materials.AfterUpdateHooks.RunHooks(ctx, exec, o)
	case bob.QueryTypeDelete:
		ctx, err = Materials.AfterDeleteHooks.RunHooks(ctx, exec, o)
	}

	return err
}

func (o MaterialSlice) pkIN() dialect.Expression {
	if len(o) == 0 {
		return psql.Raw("NULL")
	}

	return psql.Quote("material", "id").In(bob.ExpressionFunc(func(ctx context.Context, w io.Writer, d bob.Dialect, start int) ([]any, error) {
		pkPairs := make([]bob.Expression, len(o))
		for i, row := range o {
			pkPairs[i] = row.primaryKeyVals()
		}
		return bob.ExpressSlice(ctx, w, d, start, pkPairs, "", ", ", "")
	}))
}

// copyMatchingRows finds models in the given slice that have the same primary key
// then it first copies the existing relationships from the old model to the new model
// and then replaces the old model in the slice with the new model
func (o MaterialSlice) copyMatchingRows(from ...*Material) {
	for i, old := range o {
		for _, new := range from {
			if new.ID != old.ID {
				continue
			}
			new.R = old.R
			o[i] = new
			break
		}
	}
}

// UpdateMod modifies an update query with "WHERE primary_key IN (o...)"
func (o MaterialSlice) UpdateMod() bob.Mod[*dialect.UpdateQuery] {
	return bob.ModFunc[*dialect.UpdateQuery](func(q *dialect.UpdateQuery) {
		q.AppendHooks(func(ctx context.Context, exec bob.Executor) (context.Context, error) {
			return Materials.BeforeUpdateHooks.RunHooks(ctx, exec, o)
		})

		q.AppendLoader(bob.LoaderFunc(func(ctx context.Context, exec bob.Executor, retrieved any) error {
			var err error
			switch retrieved := retrieved.(type) {
			case *Material:
				o.copyMatchingRows(retrieved)
			case []*Material:
				o.copyMatchingRows(retrieved...)
			case MaterialSlice:
				o.copyMatchingRows(retrieved...)
			default:
				// If the retrieved value is not a Material or a slice of Material
				// then run the AfterUpdateHooks on the slice
				_, err = Materials.AfterUpdateHooks.RunHooks(ctx, exec, o)
			}

			return err
		}))

		q.AppendWhere(o.pkIN())
	})
}

// DeleteMod modifies an delete query with "WHERE primary_key IN (o...)"
func (o MaterialSlice) DeleteMod() bob.Mod[*dialect.DeleteQuery] {
	return bob.ModFunc[*dialect.DeleteQuery](func(q *dialect.DeleteQuery) {
		q.AppendHooks(func(ctx context.Context, exec bob.Executor) (context.Context, error) {
			return Materials.BeforeDeleteHooks.RunHooks(ctx, exec, o)
		})

		q.AppendLoader(bob.LoaderFunc(func(ctx context.Context, exec bob.Executor, retrieved any) error {
			var err error
			switch retrieved := retrieved.(type) {
			case *Material:
				o.copyMatchingRows(retrieved)
			case []*Material:
				o.copyMatchingRows(retrieved...)
			case MaterialSlice:
				o.copyMatchingRows(retrieved...)
			default:
				// If the retrieved value is not a Material or a slice of Material
				// then run the AfterDeleteHooks on the slice
				_, err = Materials.AfterDeleteHooks.RunHooks(ctx, exec, o)
			}

			return err
		}))

		q.AppendWhere(o.pkIN())
	})
}

func (o MaterialSlice) UpdateAll(ctx context.Context, exec bob.Executor, vals MaterialSetter) error {
	if len(o) == 0 {
		return nil
	}

	_, err := Materials.Update(vals.UpdateMod(), o.UpdateMod()).All(ctx, exec)
	return err
}

func (o MaterialSlice) DeleteAll(ctx context.Context, exec bob.Executor) error {
	if len(o) == 0 {
		return nil
	}

	_, err := Materials.Delete(o.DeleteMod()).Exec(ctx, exec)
	return err
}

func (o MaterialSlice) ReloadAll(ctx context.Context, exec bob.Executor) error {
	if len(o) == 0 {
		return nil
	}

	o2, err := Materials.Query(sm.Where(o.pkIN())).All(ctx, exec)
	if err != nil {
		return err
	}

	o.copyMatchingRows(o2...)

	return nil
}

type materialJoins[Q dialect.Joinable] struct {
	typ                string
	MaterialUsages     modAs[Q, materialUsageColumns]
	OperationMaterials modAs[Q, operationMaterialColumns]
}

func (j materialJoins[Q]) aliasedAs(alias string) materialJoins[Q] {
	return buildMaterialJoins[Q](buildMaterialColumns(alias), j.typ)
}

func buildMaterialJoins[Q dialect.Joinable](cols materialColumns, typ string) materialJoins[Q] {
	return materialJoins[Q]{
		typ: typ,
		MaterialUsages: modAs[Q, materialUsageColumns]{
			c: MaterialUsageColumns,
			f: func(to materialUsageColumns) bob.Mod[Q] {
				mods := make(mods.QueryMods[Q], 0, 1)

				{
					mods = append(mods, dialect.Join[Q](typ, MaterialUsages.Name().As(to.Alias())).On(
						to.MaterialID.EQ(cols.ID),
					))
				}

				return mods
			},
		},
		OperationMaterials: modAs[Q, operationMaterialColumns]{
			c: OperationMaterialColumns,
			f: func(to operationMaterialColumns) bob.Mod[Q] {
				mods := make(mods.QueryMods[Q], 0, 1)

				{
					mods = append(mods, dialect.Join[Q](typ, OperationMaterials.Name().As(to.Alias())).On(
						to.MaterialID.EQ(cols.ID),
					))
				}

				return mods
			},
		},
	}
}

// MaterialUsages starts a query for related objects on material_usage
func (o *Material) MaterialUsages(mods ...bob.Mod[*dialect.SelectQuery]) MaterialUsagesQuery {
	return MaterialUsages.Query(append(mods,
		sm.Where(MaterialUsageColumns.MaterialID.EQ(psql.Arg(o.ID))),
	)...)
}

func (os MaterialSlice) MaterialUsages(mods ...bob.Mod[*dialect.SelectQuery]) MaterialUsagesQuery {
	pkID := make(pgtypes.Array[int64], len(os))
	for i, o := range os {
		pkID[i] = o.ID
	}
	PKArgExpr := psql.Select(sm.Columns(
		psql.F("unnest", psql.Cast(psql.Arg(pkID), "bigint[]")),
	))

	return MaterialUsages.Query(append(mods,
		sm.Where(psql.Group(MaterialUsageColumns.MaterialID).OP("IN", PKArgExpr)),
	)...)
}

// OperationMaterials starts a query for related objects on operation_material
func (o *Material) OperationMaterials(mods ...bob.Mod[*dialect.SelectQuery]) OperationMaterialsQuery {
	return OperationMaterials.Query(append(mods,
		sm.Where(OperationMaterialColumns.MaterialID.EQ(psql.Arg(o.ID))),
	)...)
}

func (os MaterialSlice) OperationMaterials(mods ...bob.Mod[*dialect.SelectQuery]) OperationMaterialsQuery {
	pkID := make(pgtypes.Array[int64], len(os))
	for i, o := range os {
		pkID[i] = o.ID
	}
	PKArgExpr := psql.Select(sm.Columns(
		psql.F("unnest", psql.Cast(psql.Arg(pkID), "bigint[]")),
	))

	return OperationMaterials.Query(append(mods,
		sm.Where(psql.Group(OperationMaterialColumns.MaterialID).OP("IN", PKArgExpr)),
	)...)
}

func (o *Material) Preload(name string, retrieved any) error {
	if o == nil {
		return nil
	}

	switch name {
	case "MaterialUsages":
		rels, ok := retrieved.(MaterialUsageSlice)
		if !ok {
			return fmt.Errorf("material cannot load %T as %q", retrieved, name)
		}

		o.R.MaterialUsages = rels

		for _, rel := range rels {
			if rel != nil {
				rel.R.Material = o
			}
		}
		return nil
	case "OperationMaterials":
		rels, ok := retrieved.(OperationMaterialSlice)
		if !ok {
			return fmt.Errorf("material cannot load %T as %q", retrieved, name)
		}

		o.R.OperationMaterials = rels

		for _, rel := range rels {
			if rel != nil {
				rel.R.Material = o
			}
		}
		return nil
	default:
		return fmt.Errorf("material has no relationship %q", name)
	}
}

type materialPreloader struct{}

func buildMaterialPreloader() materialPreloader {
	return materialPreloader{}
}

type materialThenLoader[Q orm.Loadable] struct {
	MaterialUsages     func(...bob.Mod[*dialect.SelectQuery]) orm.Loader[Q]
	OperationMaterials func(...bob.Mod[*dialect.SelectQuery]) orm.Loader[Q]
}

func buildMaterialThenLoader[Q orm.Loadable]() materialThenLoader[Q] {
	type MaterialUsagesLoadInterface interface {
		LoadMaterialUsages(context.Context, bob.Executor, ...bob.Mod[*dialect.SelectQuery]) error
	}
	type OperationMaterialsLoadInterface interface {
		LoadOperationMaterials(context.Context, bob.Executor, ...bob.Mod[*dialect.SelectQuery]) error
	}

	return materialThenLoader[Q]{
		MaterialUsages: thenLoadBuilder[Q](
			"MaterialUsages",
			func(ctx context.Context, exec bob.Executor, retrieved MaterialUsagesLoadInterface, mods ...bob.Mod[*dialect.SelectQuery]) error {
				return retrieved.LoadMaterialUsages(ctx, exec, mods...)
			},
		),
		OperationMaterials: thenLoadBuilder[Q](
			"OperationMaterials",
			func(ctx context.Context, exec bob.Executor, retrieved OperationMaterialsLoadInterface, mods ...bob.Mod[*dialect.SelectQuery]) error {
				return retrieved.LoadOperationMaterials(ctx, exec, mods...)
			},
		),
	}
}

// LoadMaterialUsages loads the material's MaterialUsages into the .R struct
func (o *Material) LoadMaterialUsages(ctx context.Context, exec bob.Executor, mods ...bob.Mod[*dialect.SelectQuery]) error {
	if o == nil {
		return nil
	}

	// Reset the relationship
	o.R.MaterialUsages = nil

	related, err := o.MaterialUsages(mods...).All(ctx, exec)
	if err != nil {
		return err
	}

	for _, rel := range related {
		rel.R.Material = o
	}

	o.R.MaterialUsages = related
	return nil
}

// LoadMaterialUsages loads the material's MaterialUsages into the .R struct
func (os MaterialSlice) LoadMaterialUsages(ctx context.Context, exec bob.Executor, mods ...bob.Mod[*dialect.SelectQuery]) error {
	if len(os) == 0 {
		return nil
	}

	materialUsages, err := os.MaterialUsages(mods...).All(ctx, exec)
	if err != nil {
		return err
	}

	for _, o := range os {
		o.R.MaterialUsages = nil
	}

	for _, o := range os {
		for _, rel := range materialUsages {
			if o.ID != rel.MaterialID {
				continue
			}

			rel.R.Material = o

			o.R.MaterialUsages = append(o.R.MaterialUsages, rel)
		}
	}

	return nil
}

// LoadOperationMaterials loads the material's OperationMaterials into the .R struct
func (o *Material) LoadOperationMaterials(ctx context.Context, exec bob.Executor, mods ...bob.Mod[*dialect.SelectQuery]) error {
	if o == nil {
		return nil
	}

	// Reset the relationship
	o.R.OperationMaterials = nil

	related, err := o.OperationMaterials(mods...).All(ctx, exec)
	if err != nil {
		return err
	}

	for _, rel := range related {
		rel.R.Material = o
	}

	o.R.OperationMaterials = related
	return nil
}

// LoadOperationMaterials loads the material's OperationMaterials into the .R struct
func (os MaterialSlice) LoadOperationMaterials(ctx context.Context, exec bob.Executor, mods ...bob.Mod[*dialect.SelectQuery]) error {
	if len(os) == 0 {
		return nil
	}

	operationMaterials, err := os.OperationMaterials(mods...).All(ctx, exec)
	if err != nil {
		return err
	}

	for _, o := range os {
		o.R.OperationMaterials = nil
	}

	for _, o := range os {
		for _, rel := range operationMaterials {
			if o.ID != rel.MaterialID.V {
				continue
			}

			rel.R.Material = o

			o.R.OperationMaterials = append(o.R.OperationMaterials, rel)
		}
	}

	return nil
}

func insertMaterialMaterialUsages0(ctx context.Context, exec bob.Executor, materialUsages1 []*MaterialUsageSetter, material0 *Material) (MaterialUsageSlice, error) {
	for i := range materialUsages1 {
		materialUsages1[i].MaterialID = &material0.ID
	}

	ret, err := MaterialUsages.Insert(bob.ToMods(materialUsages1...)).All(ctx, exec)
	if err != nil {
		return ret, fmt.Errorf("insertMaterialMaterialUsages0: %w", err)
	}

	return ret, nil
}

func attachMaterialMaterialUsages0(ctx context.Context, exec bob.Executor, count int, materialUsages1 MaterialUsageSlice, material0 *Material) (MaterialUsageSlice, error) {
	setter := &MaterialUsageSetter{
		MaterialID: &material0.ID,
	}

	err := materialUsages1.UpdateAll(ctx, exec, *setter)
	if err != nil {
		return nil, fmt.Errorf("attachMaterialMaterialUsages0: %w", err)
	}

	return materialUsages1, nil
}

func (material0 *Material) InsertMaterialUsages(ctx context.Context, exec bob.Executor, related ...*MaterialUsageSetter) error {
	if len(related) == 0 {
		return nil
	}

	var err error

	materialUsages1, err := insertMaterialMaterialUsages0(ctx, exec, related, material0)
	if err != nil {
		return err
	}

	material0.R.MaterialUsages = append(material0.R.MaterialUsages, materialUsages1...)

	for _, rel := range materialUsages1 {
		rel.R.Material = material0
	}
	return nil
}

func (material0 *Material) AttachMaterialUsages(ctx context.Context, exec bob.Executor, related ...*MaterialUsage) error {
	if len(related) == 0 {
		return nil
	}

	var err error
	materialUsages1 := MaterialUsageSlice(related)

	_, err = attachMaterialMaterialUsages0(ctx, exec, len(related), materialUsages1, material0)
	if err != nil {
		return err
	}

	material0.R.MaterialUsages = append(material0.R.MaterialUsages, materialUsages1...)

	for _, rel := range related {
		rel.R.Material = material0
	}

	return nil
}

func insertMaterialOperationMaterials0(ctx context.Context, exec bob.Executor, operationMaterials1 []*OperationMaterialSetter, material0 *Material) (OperationMaterialSlice, error) {
	for i := range operationMaterials1 {
		operationMaterials1[i].MaterialID = func() *sql.Null[int64] {
			v := sql.Null[int64]{V: material0.ID, Valid: true}
			return &v
		}()
	}

	ret, err := OperationMaterials.Insert(bob.ToMods(operationMaterials1...)).All(ctx, exec)
	if err != nil {
		return ret, fmt.Errorf("insertMaterialOperationMaterials0: %w", err)
	}

	return ret, nil
}

func attachMaterialOperationMaterials0(ctx context.Context, exec bob.Executor, count int, operationMaterials1 OperationMaterialSlice, material0 *Material) (OperationMaterialSlice, error) {
	setter := &OperationMaterialSetter{
		MaterialID: func() *sql.Null[int64] {
			v := sql.Null[int64]{V: material0.ID, Valid: true}
			return &v
		}(),
	}

	err := operationMaterials1.UpdateAll(ctx, exec, *setter)
	if err != nil {
		return nil, fmt.Errorf("attachMaterialOperationMaterials0: %w", err)
	}

	return operationMaterials1, nil
}

func (material0 *Material) InsertOperationMaterials(ctx context.Context, exec bob.Executor, related ...*OperationMaterialSetter) error {
	if len(related) == 0 {
		return nil
	}

	var err error

	operationMaterials1, err := insertMaterialOperationMaterials0(ctx, exec, related, material0)
	if err != nil {
		return err
	}

	material0.R.OperationMaterials = append(material0.R.OperationMaterials, operationMaterials1...)

	for _, rel := range operationMaterials1 {
		rel.R.Material = material0
	}
	return nil
}

func (material0 *Material) AttachOperationMaterials(ctx context.Context, exec bob.Executor, related ...*OperationMaterial) error {
	if len(related) == 0 {
		return nil
	}

	var err error
	operationMaterials1 := OperationMaterialSlice(related)

	_, err = attachMaterialOperationMaterials0(ctx, exec, len(related), operationMaterials1, material0)
	if err != nil {
		return err
	}

	material0.R.OperationMaterials = append(material0.R.OperationMaterials, operationMaterials1...)

	for _, rel := range related {
		rel.R.Material = material0
	}

	return nil
}
