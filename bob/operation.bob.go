// Code generated by BobGen psql v0.38.0. DO NOT EDIT.
// This file is meant to be re-generated in place and/or deleted at any time.

package bob

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"io"
	"time"

	"github.com/stephenafamo/bob"
	"github.com/stephenafamo/bob/dialect/psql"
	"github.com/stephenafamo/bob/dialect/psql/dialect"
	"github.com/stephenafamo/bob/dialect/psql/dm"
	"github.com/stephenafamo/bob/dialect/psql/sm"
	"github.com/stephenafamo/bob/dialect/psql/um"
	"github.com/stephenafamo/bob/expr"
	"github.com/stephenafamo/bob/mods"
	"github.com/stephenafamo/bob/orm"
	"github.com/stephenafamo/bob/types"
	"github.com/stephenafamo/bob/types/pgtypes"
)

// Operation is an object representing the database table.
type Operation struct {
	ID          int64                                 `db:"id,pk" `
	DeletedAt   sql.Null[time.Time]                   `db:"deleted_at" `
	Status      int16                                 `db:"status" `
	Version     int64                                 `db:"version" `
	CreatedAt   time.Time                             `db:"created_at" `
	UpdatedAt   time.Time                             `db:"updated_at" `
	Name        string                                `db:"name" `
	Description sql.Null[string]                      `db:"description" `
	Duration    sql.Null[int64]                       `db:"duration" `
	Group       sql.Null[types.JSON[json.RawMessage]] `db:"group" `

	R operationR `db:"-" `
}

// OperationSlice is an alias for a slice of pointers to Operation.
// This should almost always be used instead of []*Operation.
type OperationSlice []*Operation

// Operations contains methods to work with the operation table
var Operations = psql.NewTablex[*Operation, OperationSlice, *OperationSetter]("", "operation")

// OperationsQuery is a query on the operation table
type OperationsQuery = *psql.ViewQuery[*Operation, OperationSlice]

// operationR is where relationships are stored.
type operationR struct {
	MaterialUsages     MaterialUsageSlice     // material_usage.material_usage_operation_operation
	OperationMaterials OperationMaterialSlice // operation_material.operation_material_operation_operation
}

type operationColumnNames struct {
	ID          string
	DeletedAt   string
	Status      string
	Version     string
	CreatedAt   string
	UpdatedAt   string
	Name        string
	Description string
	Duration    string
	Group       string
}

var OperationColumns = buildOperationColumns("operation")

type operationColumns struct {
	tableAlias  string
	ID          psql.Expression
	DeletedAt   psql.Expression
	Status      psql.Expression
	Version     psql.Expression
	CreatedAt   psql.Expression
	UpdatedAt   psql.Expression
	Name        psql.Expression
	Description psql.Expression
	Duration    psql.Expression
	Group       psql.Expression
}

func (c operationColumns) Alias() string {
	return c.tableAlias
}

func (operationColumns) AliasedAs(alias string) operationColumns {
	return buildOperationColumns(alias)
}

func buildOperationColumns(alias string) operationColumns {
	return operationColumns{
		tableAlias:  alias,
		ID:          psql.Quote(alias, "id"),
		DeletedAt:   psql.Quote(alias, "deleted_at"),
		Status:      psql.Quote(alias, "status"),
		Version:     psql.Quote(alias, "version"),
		CreatedAt:   psql.Quote(alias, "created_at"),
		UpdatedAt:   psql.Quote(alias, "updated_at"),
		Name:        psql.Quote(alias, "name"),
		Description: psql.Quote(alias, "description"),
		Duration:    psql.Quote(alias, "duration"),
		Group:       psql.Quote(alias, "group"),
	}
}

type operationWhere[Q psql.Filterable] struct {
	ID          psql.WhereMod[Q, int64]
	DeletedAt   psql.WhereNullMod[Q, time.Time]
	Status      psql.WhereMod[Q, int16]
	Version     psql.WhereMod[Q, int64]
	CreatedAt   psql.WhereMod[Q, time.Time]
	UpdatedAt   psql.WhereMod[Q, time.Time]
	Name        psql.WhereMod[Q, string]
	Description psql.WhereNullMod[Q, string]
	Duration    psql.WhereNullMod[Q, int64]
	Group       psql.WhereNullMod[Q, types.JSON[json.RawMessage]]
}

func (operationWhere[Q]) AliasedAs(alias string) operationWhere[Q] {
	return buildOperationWhere[Q](buildOperationColumns(alias))
}

func buildOperationWhere[Q psql.Filterable](cols operationColumns) operationWhere[Q] {
	return operationWhere[Q]{
		ID:          psql.Where[Q, int64](cols.ID),
		DeletedAt:   psql.WhereNull[Q, time.Time](cols.DeletedAt),
		Status:      psql.Where[Q, int16](cols.Status),
		Version:     psql.Where[Q, int64](cols.Version),
		CreatedAt:   psql.Where[Q, time.Time](cols.CreatedAt),
		UpdatedAt:   psql.Where[Q, time.Time](cols.UpdatedAt),
		Name:        psql.Where[Q, string](cols.Name),
		Description: psql.WhereNull[Q, string](cols.Description),
		Duration:    psql.WhereNull[Q, int64](cols.Duration),
		Group:       psql.WhereNull[Q, types.JSON[json.RawMessage]](cols.Group),
	}
}

var OperationErrors = &operationErrors{
	ErrUniqueOperationPkey: &UniqueConstraintError{
		schema:  "",
		table:   "operation",
		columns: []string{"id"},
		s:       "operation_pkey",
	},
}

type operationErrors struct {
	ErrUniqueOperationPkey *UniqueConstraintError
}

// OperationSetter is used for insert/upsert/update operations
// All values are optional, and do not have to be set
// Generated columns are not included
type OperationSetter struct {
	ID          *int64                                 `db:"id,pk" `
	DeletedAt   *sql.Null[time.Time]                   `db:"deleted_at" `
	Status      *int16                                 `db:"status" `
	Version     *int64                                 `db:"version" `
	CreatedAt   *time.Time                             `db:"created_at" `
	UpdatedAt   *time.Time                             `db:"updated_at" `
	Name        *string                                `db:"name" `
	Description *sql.Null[string]                      `db:"description" `
	Duration    *sql.Null[int64]                       `db:"duration" `
	Group       *sql.Null[types.JSON[json.RawMessage]] `db:"group" `
}

func (s OperationSetter) SetColumns() []string {
	vals := make([]string, 0, 10)
	if s.ID != nil {
		vals = append(vals, "id")
	}

	if s.DeletedAt != nil {
		vals = append(vals, "deleted_at")
	}

	if s.Status != nil {
		vals = append(vals, "status")
	}

	if s.Version != nil {
		vals = append(vals, "version")
	}

	if s.CreatedAt != nil {
		vals = append(vals, "created_at")
	}

	if s.UpdatedAt != nil {
		vals = append(vals, "updated_at")
	}

	if s.Name != nil {
		vals = append(vals, "name")
	}

	if s.Description != nil {
		vals = append(vals, "description")
	}

	if s.Duration != nil {
		vals = append(vals, "duration")
	}

	if s.Group != nil {
		vals = append(vals, "group")
	}

	return vals
}

func (s OperationSetter) Overwrite(t *Operation) {
	if s.ID != nil {
		t.ID = *s.ID
	}
	if s.DeletedAt != nil {
		t.DeletedAt = *s.DeletedAt
	}
	if s.Status != nil {
		t.Status = *s.Status
	}
	if s.Version != nil {
		t.Version = *s.Version
	}
	if s.CreatedAt != nil {
		t.CreatedAt = *s.CreatedAt
	}
	if s.UpdatedAt != nil {
		t.UpdatedAt = *s.UpdatedAt
	}
	if s.Name != nil {
		t.Name = *s.Name
	}
	if s.Description != nil {
		t.Description = *s.Description
	}
	if s.Duration != nil {
		t.Duration = *s.Duration
	}
	if s.Group != nil {
		t.Group = *s.Group
	}
}

func (s *OperationSetter) Apply(q *dialect.InsertQuery) {
	q.AppendHooks(func(ctx context.Context, exec bob.Executor) (context.Context, error) {
		return Operations.BeforeInsertHooks.RunHooks(ctx, exec, s)
	})

	q.AppendValues(bob.ExpressionFunc(func(ctx context.Context, w io.Writer, d bob.Dialect, start int) ([]any, error) {
		vals := make([]bob.Expression, 10)
		if s.ID != nil {
			vals[0] = psql.Arg(*s.ID)
		} else {
			vals[0] = psql.Raw("DEFAULT")
		}

		if s.DeletedAt != nil {
			vals[1] = psql.Arg(*s.DeletedAt)
		} else {
			vals[1] = psql.Raw("DEFAULT")
		}

		if s.Status != nil {
			vals[2] = psql.Arg(*s.Status)
		} else {
			vals[2] = psql.Raw("DEFAULT")
		}

		if s.Version != nil {
			vals[3] = psql.Arg(*s.Version)
		} else {
			vals[3] = psql.Raw("DEFAULT")
		}

		if s.CreatedAt != nil {
			vals[4] = psql.Arg(*s.CreatedAt)
		} else {
			vals[4] = psql.Raw("DEFAULT")
		}

		if s.UpdatedAt != nil {
			vals[5] = psql.Arg(*s.UpdatedAt)
		} else {
			vals[5] = psql.Raw("DEFAULT")
		}

		if s.Name != nil {
			vals[6] = psql.Arg(*s.Name)
		} else {
			vals[6] = psql.Raw("DEFAULT")
		}

		if s.Description != nil {
			vals[7] = psql.Arg(*s.Description)
		} else {
			vals[7] = psql.Raw("DEFAULT")
		}

		if s.Duration != nil {
			vals[8] = psql.Arg(*s.Duration)
		} else {
			vals[8] = psql.Raw("DEFAULT")
		}

		if s.Group != nil {
			vals[9] = psql.Arg(*s.Group)
		} else {
			vals[9] = psql.Raw("DEFAULT")
		}

		return bob.ExpressSlice(ctx, w, d, start, vals, "", ", ", "")
	}))
}

func (s OperationSetter) UpdateMod() bob.Mod[*dialect.UpdateQuery] {
	return um.Set(s.Expressions()...)
}

func (s OperationSetter) Expressions(prefix ...string) []bob.Expression {
	exprs := make([]bob.Expression, 0, 10)

	if s.ID != nil {
		exprs = append(exprs, expr.Join{Sep: " = ", Exprs: []bob.Expression{
			psql.Quote(append(prefix, "id")...),
			psql.Arg(s.ID),
		}})
	}

	if s.DeletedAt != nil {
		exprs = append(exprs, expr.Join{Sep: " = ", Exprs: []bob.Expression{
			psql.Quote(append(prefix, "deleted_at")...),
			psql.Arg(s.DeletedAt),
		}})
	}

	if s.Status != nil {
		exprs = append(exprs, expr.Join{Sep: " = ", Exprs: []bob.Expression{
			psql.Quote(append(prefix, "status")...),
			psql.Arg(s.Status),
		}})
	}

	if s.Version != nil {
		exprs = append(exprs, expr.Join{Sep: " = ", Exprs: []bob.Expression{
			psql.Quote(append(prefix, "version")...),
			psql.Arg(s.Version),
		}})
	}

	if s.CreatedAt != nil {
		exprs = append(exprs, expr.Join{Sep: " = ", Exprs: []bob.Expression{
			psql.Quote(append(prefix, "created_at")...),
			psql.Arg(s.CreatedAt),
		}})
	}

	if s.UpdatedAt != nil {
		exprs = append(exprs, expr.Join{Sep: " = ", Exprs: []bob.Expression{
			psql.Quote(append(prefix, "updated_at")...),
			psql.Arg(s.UpdatedAt),
		}})
	}

	if s.Name != nil {
		exprs = append(exprs, expr.Join{Sep: " = ", Exprs: []bob.Expression{
			psql.Quote(append(prefix, "name")...),
			psql.Arg(s.Name),
		}})
	}

	if s.Description != nil {
		exprs = append(exprs, expr.Join{Sep: " = ", Exprs: []bob.Expression{
			psql.Quote(append(prefix, "description")...),
			psql.Arg(s.Description),
		}})
	}

	if s.Duration != nil {
		exprs = append(exprs, expr.Join{Sep: " = ", Exprs: []bob.Expression{
			psql.Quote(append(prefix, "duration")...),
			psql.Arg(s.Duration),
		}})
	}

	if s.Group != nil {
		exprs = append(exprs, expr.Join{Sep: " = ", Exprs: []bob.Expression{
			psql.Quote(append(prefix, "group")...),
			psql.Arg(s.Group),
		}})
	}

	return exprs
}

// FindOperation retrieves a single record by primary key
// If cols is empty Find will return all columns.
func FindOperation(ctx context.Context, exec bob.Executor, IDPK int64, cols ...string) (*Operation, error) {
	if len(cols) == 0 {
		return Operations.Query(
			SelectWhere.Operations.ID.EQ(IDPK),
		).One(ctx, exec)
	}

	return Operations.Query(
		SelectWhere.Operations.ID.EQ(IDPK),
		sm.Columns(Operations.Columns().Only(cols...)),
	).One(ctx, exec)
}

// OperationExists checks the presence of a single record by primary key
func OperationExists(ctx context.Context, exec bob.Executor, IDPK int64) (bool, error) {
	return Operations.Query(
		SelectWhere.Operations.ID.EQ(IDPK),
	).Exists(ctx, exec)
}

// AfterQueryHook is called after Operation is retrieved from the database
func (o *Operation) AfterQueryHook(ctx context.Context, exec bob.Executor, queryType bob.QueryType) error {
	var err error

	switch queryType {
	case bob.QueryTypeSelect:
		ctx, err = Operations.AfterSelectHooks.RunHooks(ctx, exec, OperationSlice{o})
	case bob.QueryTypeInsert:
		ctx, err = Operations.AfterInsertHooks.RunHooks(ctx, exec, OperationSlice{o})
	case bob.QueryTypeUpdate:
		ctx, err = Operations.AfterUpdateHooks.RunHooks(ctx, exec, OperationSlice{o})
	case bob.QueryTypeDelete:
		ctx, err = Operations.AfterDeleteHooks.RunHooks(ctx, exec, OperationSlice{o})
	}

	return err
}

// primaryKeyVals returns the primary key values of the Operation
func (o *Operation) primaryKeyVals() bob.Expression {
	return psql.Arg(o.ID)
}

func (o *Operation) pkEQ() dialect.Expression {
	return psql.Quote("operation", "id").EQ(bob.ExpressionFunc(func(ctx context.Context, w io.Writer, d bob.Dialect, start int) ([]any, error) {
		return o.primaryKeyVals().WriteSQL(ctx, w, d, start)
	}))
}

// Update uses an executor to update the Operation
func (o *Operation) Update(ctx context.Context, exec bob.Executor, s *OperationSetter) error {
	v, err := Operations.Update(s.UpdateMod(), um.Where(o.pkEQ())).One(ctx, exec)
	if err != nil {
		return err
	}

	o.R = v.R
	*o = *v

	return nil
}

// Delete deletes a single Operation record with an executor
func (o *Operation) Delete(ctx context.Context, exec bob.Executor) error {
	_, err := Operations.Delete(dm.Where(o.pkEQ())).Exec(ctx, exec)
	return err
}

// Reload refreshes the Operation using the executor
func (o *Operation) Reload(ctx context.Context, exec bob.Executor) error {
	o2, err := Operations.Query(
		SelectWhere.Operations.ID.EQ(o.ID),
	).One(ctx, exec)
	if err != nil {
		return err
	}
	o2.R = o.R
	*o = *o2

	return nil
}

// AfterQueryHook is called after OperationSlice is retrieved from the database
func (o OperationSlice) AfterQueryHook(ctx context.Context, exec bob.Executor, queryType bob.QueryType) error {
	var err error

	switch queryType {
	case bob.QueryTypeSelect:
		ctx, err = Operations.AfterSelectHooks.RunHooks(ctx, exec, o)
	case bob.QueryTypeInsert:
		ctx, err = Operations.AfterInsertHooks.RunHooks(ctx, exec, o)
	case bob.QueryTypeUpdate:
		ctx, err = Operations.AfterUpdateHooks.RunHooks(ctx, exec, o)
	case bob.QueryTypeDelete:
		ctx, err = Operations.AfterDeleteHooks.RunHooks(ctx, exec, o)
	}

	return err
}

func (o OperationSlice) pkIN() dialect.Expression {
	if len(o) == 0 {
		return psql.Raw("NULL")
	}

	return psql.Quote("operation", "id").In(bob.ExpressionFunc(func(ctx context.Context, w io.Writer, d bob.Dialect, start int) ([]any, error) {
		pkPairs := make([]bob.Expression, len(o))
		for i, row := range o {
			pkPairs[i] = row.primaryKeyVals()
		}
		return bob.ExpressSlice(ctx, w, d, start, pkPairs, "", ", ", "")
	}))
}

// copyMatchingRows finds models in the given slice that have the same primary key
// then it first copies the existing relationships from the old model to the new model
// and then replaces the old model in the slice with the new model
func (o OperationSlice) copyMatchingRows(from ...*Operation) {
	for i, old := range o {
		for _, new := range from {
			if new.ID != old.ID {
				continue
			}
			new.R = old.R
			o[i] = new
			break
		}
	}
}

// UpdateMod modifies an update query with "WHERE primary_key IN (o...)"
func (o OperationSlice) UpdateMod() bob.Mod[*dialect.UpdateQuery] {
	return bob.ModFunc[*dialect.UpdateQuery](func(q *dialect.UpdateQuery) {
		q.AppendHooks(func(ctx context.Context, exec bob.Executor) (context.Context, error) {
			return Operations.BeforeUpdateHooks.RunHooks(ctx, exec, o)
		})

		q.AppendLoader(bob.LoaderFunc(func(ctx context.Context, exec bob.Executor, retrieved any) error {
			var err error
			switch retrieved := retrieved.(type) {
			case *Operation:
				o.copyMatchingRows(retrieved)
			case []*Operation:
				o.copyMatchingRows(retrieved...)
			case OperationSlice:
				o.copyMatchingRows(retrieved...)
			default:
				// If the retrieved value is not a Operation or a slice of Operation
				// then run the AfterUpdateHooks on the slice
				_, err = Operations.AfterUpdateHooks.RunHooks(ctx, exec, o)
			}

			return err
		}))

		q.AppendWhere(o.pkIN())
	})
}

// DeleteMod modifies an delete query with "WHERE primary_key IN (o...)"
func (o OperationSlice) DeleteMod() bob.Mod[*dialect.DeleteQuery] {
	return bob.ModFunc[*dialect.DeleteQuery](func(q *dialect.DeleteQuery) {
		q.AppendHooks(func(ctx context.Context, exec bob.Executor) (context.Context, error) {
			return Operations.BeforeDeleteHooks.RunHooks(ctx, exec, o)
		})

		q.AppendLoader(bob.LoaderFunc(func(ctx context.Context, exec bob.Executor, retrieved any) error {
			var err error
			switch retrieved := retrieved.(type) {
			case *Operation:
				o.copyMatchingRows(retrieved)
			case []*Operation:
				o.copyMatchingRows(retrieved...)
			case OperationSlice:
				o.copyMatchingRows(retrieved...)
			default:
				// If the retrieved value is not a Operation or a slice of Operation
				// then run the AfterDeleteHooks on the slice
				_, err = Operations.AfterDeleteHooks.RunHooks(ctx, exec, o)
			}

			return err
		}))

		q.AppendWhere(o.pkIN())
	})
}

func (o OperationSlice) UpdateAll(ctx context.Context, exec bob.Executor, vals OperationSetter) error {
	if len(o) == 0 {
		return nil
	}

	_, err := Operations.Update(vals.UpdateMod(), o.UpdateMod()).All(ctx, exec)
	return err
}

func (o OperationSlice) DeleteAll(ctx context.Context, exec bob.Executor) error {
	if len(o) == 0 {
		return nil
	}

	_, err := Operations.Delete(o.DeleteMod()).Exec(ctx, exec)
	return err
}

func (o OperationSlice) ReloadAll(ctx context.Context, exec bob.Executor) error {
	if len(o) == 0 {
		return nil
	}

	o2, err := Operations.Query(sm.Where(o.pkIN())).All(ctx, exec)
	if err != nil {
		return err
	}

	o.copyMatchingRows(o2...)

	return nil
}

type operationJoins[Q dialect.Joinable] struct {
	typ                string
	MaterialUsages     modAs[Q, materialUsageColumns]
	OperationMaterials modAs[Q, operationMaterialColumns]
}

func (j operationJoins[Q]) aliasedAs(alias string) operationJoins[Q] {
	return buildOperationJoins[Q](buildOperationColumns(alias), j.typ)
}

func buildOperationJoins[Q dialect.Joinable](cols operationColumns, typ string) operationJoins[Q] {
	return operationJoins[Q]{
		typ: typ,
		MaterialUsages: modAs[Q, materialUsageColumns]{
			c: MaterialUsageColumns,
			f: func(to materialUsageColumns) bob.Mod[Q] {
				mods := make(mods.QueryMods[Q], 0, 1)

				{
					mods = append(mods, dialect.Join[Q](typ, MaterialUsages.Name().As(to.Alias())).On(
						to.OperationID.EQ(cols.ID),
					))
				}

				return mods
			},
		},
		OperationMaterials: modAs[Q, operationMaterialColumns]{
			c: OperationMaterialColumns,
			f: func(to operationMaterialColumns) bob.Mod[Q] {
				mods := make(mods.QueryMods[Q], 0, 1)

				{
					mods = append(mods, dialect.Join[Q](typ, OperationMaterials.Name().As(to.Alias())).On(
						to.OperationID.EQ(cols.ID),
					))
				}

				return mods
			},
		},
	}
}

// MaterialUsages starts a query for related objects on material_usage
func (o *Operation) MaterialUsages(mods ...bob.Mod[*dialect.SelectQuery]) MaterialUsagesQuery {
	return MaterialUsages.Query(append(mods,
		sm.Where(MaterialUsageColumns.OperationID.EQ(psql.Arg(o.ID))),
	)...)
}

func (os OperationSlice) MaterialUsages(mods ...bob.Mod[*dialect.SelectQuery]) MaterialUsagesQuery {
	pkID := make(pgtypes.Array[int64], len(os))
	for i, o := range os {
		pkID[i] = o.ID
	}
	PKArgExpr := psql.Select(sm.Columns(
		psql.F("unnest", psql.Cast(psql.Arg(pkID), "bigint[]")),
	))

	return MaterialUsages.Query(append(mods,
		sm.Where(psql.Group(MaterialUsageColumns.OperationID).OP("IN", PKArgExpr)),
	)...)
}

// OperationMaterials starts a query for related objects on operation_material
func (o *Operation) OperationMaterials(mods ...bob.Mod[*dialect.SelectQuery]) OperationMaterialsQuery {
	return OperationMaterials.Query(append(mods,
		sm.Where(OperationMaterialColumns.OperationID.EQ(psql.Arg(o.ID))),
	)...)
}

func (os OperationSlice) OperationMaterials(mods ...bob.Mod[*dialect.SelectQuery]) OperationMaterialsQuery {
	pkID := make(pgtypes.Array[int64], len(os))
	for i, o := range os {
		pkID[i] = o.ID
	}
	PKArgExpr := psql.Select(sm.Columns(
		psql.F("unnest", psql.Cast(psql.Arg(pkID), "bigint[]")),
	))

	return OperationMaterials.Query(append(mods,
		sm.Where(psql.Group(OperationMaterialColumns.OperationID).OP("IN", PKArgExpr)),
	)...)
}

func (o *Operation) Preload(name string, retrieved any) error {
	if o == nil {
		return nil
	}

	switch name {
	case "MaterialUsages":
		rels, ok := retrieved.(MaterialUsageSlice)
		if !ok {
			return fmt.Errorf("operation cannot load %T as %q", retrieved, name)
		}

		o.R.MaterialUsages = rels

		for _, rel := range rels {
			if rel != nil {
				rel.R.Operation = o
			}
		}
		return nil
	case "OperationMaterials":
		rels, ok := retrieved.(OperationMaterialSlice)
		if !ok {
			return fmt.Errorf("operation cannot load %T as %q", retrieved, name)
		}

		o.R.OperationMaterials = rels

		for _, rel := range rels {
			if rel != nil {
				rel.R.Operation = o
			}
		}
		return nil
	default:
		return fmt.Errorf("operation has no relationship %q", name)
	}
}

type operationPreloader struct{}

func buildOperationPreloader() operationPreloader {
	return operationPreloader{}
}

type operationThenLoader[Q orm.Loadable] struct {
	MaterialUsages     func(...bob.Mod[*dialect.SelectQuery]) orm.Loader[Q]
	OperationMaterials func(...bob.Mod[*dialect.SelectQuery]) orm.Loader[Q]
}

func buildOperationThenLoader[Q orm.Loadable]() operationThenLoader[Q] {
	type MaterialUsagesLoadInterface interface {
		LoadMaterialUsages(context.Context, bob.Executor, ...bob.Mod[*dialect.SelectQuery]) error
	}
	type OperationMaterialsLoadInterface interface {
		LoadOperationMaterials(context.Context, bob.Executor, ...bob.Mod[*dialect.SelectQuery]) error
	}

	return operationThenLoader[Q]{
		MaterialUsages: thenLoadBuilder[Q](
			"MaterialUsages",
			func(ctx context.Context, exec bob.Executor, retrieved MaterialUsagesLoadInterface, mods ...bob.Mod[*dialect.SelectQuery]) error {
				return retrieved.LoadMaterialUsages(ctx, exec, mods...)
			},
		),
		OperationMaterials: thenLoadBuilder[Q](
			"OperationMaterials",
			func(ctx context.Context, exec bob.Executor, retrieved OperationMaterialsLoadInterface, mods ...bob.Mod[*dialect.SelectQuery]) error {
				return retrieved.LoadOperationMaterials(ctx, exec, mods...)
			},
		),
	}
}

// LoadMaterialUsages loads the operation's MaterialUsages into the .R struct
func (o *Operation) LoadMaterialUsages(ctx context.Context, exec bob.Executor, mods ...bob.Mod[*dialect.SelectQuery]) error {
	if o == nil {
		return nil
	}

	// Reset the relationship
	o.R.MaterialUsages = nil

	related, err := o.MaterialUsages(mods...).All(ctx, exec)
	if err != nil {
		return err
	}

	for _, rel := range related {
		rel.R.Operation = o
	}

	o.R.MaterialUsages = related
	return nil
}

// LoadMaterialUsages loads the operation's MaterialUsages into the .R struct
func (os OperationSlice) LoadMaterialUsages(ctx context.Context, exec bob.Executor, mods ...bob.Mod[*dialect.SelectQuery]) error {
	if len(os) == 0 {
		return nil
	}

	materialUsages, err := os.MaterialUsages(mods...).All(ctx, exec)
	if err != nil {
		return err
	}

	for _, o := range os {
		o.R.MaterialUsages = nil
	}

	for _, o := range os {
		for _, rel := range materialUsages {
			if o.ID != rel.OperationID.V {
				continue
			}

			rel.R.Operation = o

			o.R.MaterialUsages = append(o.R.MaterialUsages, rel)
		}
	}

	return nil
}

// LoadOperationMaterials loads the operation's OperationMaterials into the .R struct
func (o *Operation) LoadOperationMaterials(ctx context.Context, exec bob.Executor, mods ...bob.Mod[*dialect.SelectQuery]) error {
	if o == nil {
		return nil
	}

	// Reset the relationship
	o.R.OperationMaterials = nil

	related, err := o.OperationMaterials(mods...).All(ctx, exec)
	if err != nil {
		return err
	}

	for _, rel := range related {
		rel.R.Operation = o
	}

	o.R.OperationMaterials = related
	return nil
}

// LoadOperationMaterials loads the operation's OperationMaterials into the .R struct
func (os OperationSlice) LoadOperationMaterials(ctx context.Context, exec bob.Executor, mods ...bob.Mod[*dialect.SelectQuery]) error {
	if len(os) == 0 {
		return nil
	}

	operationMaterials, err := os.OperationMaterials(mods...).All(ctx, exec)
	if err != nil {
		return err
	}

	for _, o := range os {
		o.R.OperationMaterials = nil
	}

	for _, o := range os {
		for _, rel := range operationMaterials {
			if o.ID != rel.OperationID.V {
				continue
			}

			rel.R.Operation = o

			o.R.OperationMaterials = append(o.R.OperationMaterials, rel)
		}
	}

	return nil
}

func insertOperationMaterialUsages0(ctx context.Context, exec bob.Executor, materialUsages1 []*MaterialUsageSetter, operation0 *Operation) (MaterialUsageSlice, error) {
	for i := range materialUsages1 {
		materialUsages1[i].OperationID = func() *sql.Null[int64] {
			v := sql.Null[int64]{V: operation0.ID, Valid: true}
			return &v
		}()
	}

	ret, err := MaterialUsages.Insert(bob.ToMods(materialUsages1...)).All(ctx, exec)
	if err != nil {
		return ret, fmt.Errorf("insertOperationMaterialUsages0: %w", err)
	}

	return ret, nil
}

func attachOperationMaterialUsages0(ctx context.Context, exec bob.Executor, count int, materialUsages1 MaterialUsageSlice, operation0 *Operation) (MaterialUsageSlice, error) {
	setter := &MaterialUsageSetter{
		OperationID: func() *sql.Null[int64] {
			v := sql.Null[int64]{V: operation0.ID, Valid: true}
			return &v
		}(),
	}

	err := materialUsages1.UpdateAll(ctx, exec, *setter)
	if err != nil {
		return nil, fmt.Errorf("attachOperationMaterialUsages0: %w", err)
	}

	return materialUsages1, nil
}

func (operation0 *Operation) InsertMaterialUsages(ctx context.Context, exec bob.Executor, related ...*MaterialUsageSetter) error {
	if len(related) == 0 {
		return nil
	}

	var err error

	materialUsages1, err := insertOperationMaterialUsages0(ctx, exec, related, operation0)
	if err != nil {
		return err
	}

	operation0.R.MaterialUsages = append(operation0.R.MaterialUsages, materialUsages1...)

	for _, rel := range materialUsages1 {
		rel.R.Operation = operation0
	}
	return nil
}

func (operation0 *Operation) AttachMaterialUsages(ctx context.Context, exec bob.Executor, related ...*MaterialUsage) error {
	if len(related) == 0 {
		return nil
	}

	var err error
	materialUsages1 := MaterialUsageSlice(related)

	_, err = attachOperationMaterialUsages0(ctx, exec, len(related), materialUsages1, operation0)
	if err != nil {
		return err
	}

	operation0.R.MaterialUsages = append(operation0.R.MaterialUsages, materialUsages1...)

	for _, rel := range related {
		rel.R.Operation = operation0
	}

	return nil
}

func insertOperationOperationMaterials0(ctx context.Context, exec bob.Executor, operationMaterials1 []*OperationMaterialSetter, operation0 *Operation) (OperationMaterialSlice, error) {
	for i := range operationMaterials1 {
		operationMaterials1[i].OperationID = func() *sql.Null[int64] {
			v := sql.Null[int64]{V: operation0.ID, Valid: true}
			return &v
		}()
	}

	ret, err := OperationMaterials.Insert(bob.ToMods(operationMaterials1...)).All(ctx, exec)
	if err != nil {
		return ret, fmt.Errorf("insertOperationOperationMaterials0: %w", err)
	}

	return ret, nil
}

func attachOperationOperationMaterials0(ctx context.Context, exec bob.Executor, count int, operationMaterials1 OperationMaterialSlice, operation0 *Operation) (OperationMaterialSlice, error) {
	setter := &OperationMaterialSetter{
		OperationID: func() *sql.Null[int64] {
			v := sql.Null[int64]{V: operation0.ID, Valid: true}
			return &v
		}(),
	}

	err := operationMaterials1.UpdateAll(ctx, exec, *setter)
	if err != nil {
		return nil, fmt.Errorf("attachOperationOperationMaterials0: %w", err)
	}

	return operationMaterials1, nil
}

func (operation0 *Operation) InsertOperationMaterials(ctx context.Context, exec bob.Executor, related ...*OperationMaterialSetter) error {
	if len(related) == 0 {
		return nil
	}

	var err error

	operationMaterials1, err := insertOperationOperationMaterials0(ctx, exec, related, operation0)
	if err != nil {
		return err
	}

	operation0.R.OperationMaterials = append(operation0.R.OperationMaterials, operationMaterials1...)

	for _, rel := range operationMaterials1 {
		rel.R.Operation = operation0
	}
	return nil
}

func (operation0 *Operation) AttachOperationMaterials(ctx context.Context, exec bob.Executor, related ...*OperationMaterial) error {
	if len(related) == 0 {
		return nil
	}

	var err error
	operationMaterials1 := OperationMaterialSlice(related)

	_, err = attachOperationOperationMaterials0(ctx, exec, len(related), operationMaterials1, operation0)
	if err != nil {
		return err
	}

	operation0.R.OperationMaterials = append(operation0.R.OperationMaterials, operationMaterials1...)

	for _, rel := range related {
		rel.R.Operation = operation0
	}

	return nil
}
