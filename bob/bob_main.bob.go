// Code generated by BobGen psql v0.38.0. DO NOT EDIT.
// This file is meant to be re-generated in place and/or deleted at any time.

package bob

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"hash/maphash"

	"github.com/lib/pq"
	"github.com/stephenafamo/bob"
	"github.com/stephenafamo/bob/clause"
	"github.com/stephenafamo/bob/dialect/psql"
	"github.com/stephenafamo/bob/dialect/psql/dialect"
	"github.com/stephenafamo/bob/orm"
)

var TableNames = struct {
	Materials          string
	MaterialUsages     string
	Operations         string
	OperationMaterials string
}{
	Materials:          "material",
	MaterialUsages:     "material_usage",
	Operations:         "operation",
	OperationMaterials: "operation_material",
}

var ColumnNames = struct {
	Materials          materialColumnNames
	MaterialUsages     materialUsageColumnNames
	Operations         operationColumnNames
	OperationMaterials operationMaterialColumnNames
}{
	Materials: materialColumnNames{
		ID:                     "id",
		DeletedAt:              "deleted_at",
		Status:                 "status",
		Version:                "version",
		CreatedAt:              "created_at",
		UpdatedAt:              "updated_at",
		Code:                   "code",
		Name:                   "name",
		Unit:                   "unit",
		PackagingSpecification: "packaging_specification",
		Description:            "description",
		CostPrice:              "cost_price",
		Kind:                   "kind",
	},
	MaterialUsages: materialUsageColumnNames{
		ID:             "id",
		DeletedAt:      "deleted_at",
		Status:         "status",
		Version:        "version",
		CreatedAt:      "created_at",
		UpdatedAt:      "updated_at",
		OperationKey:   "operation_key",
		Kind:           "kind",
		QuotedQuantity: "quoted_quantity",
		UsedQuantity:   "used_quantity",
		Note:           "note",
		AttachmentID:   "attachment_id",
		MaterialID:     "material_id",
		OperationID:    "operation_id",
		UserID:         "user_id",
	},
	Operations: operationColumnNames{
		ID:          "id",
		DeletedAt:   "deleted_at",
		Status:      "status",
		Version:     "version",
		CreatedAt:   "created_at",
		UpdatedAt:   "updated_at",
		Name:        "name",
		Description: "description",
		Duration:    "duration",
		Group:       "group",
	},
	OperationMaterials: operationMaterialColumnNames{
		ID:          "id",
		Version:     "version",
		CreatedAt:   "created_at",
		UpdatedAt:   "updated_at",
		Quantity:    "quantity",
		OperationID: "operation_id",
		MaterialID:  "material_id",
	},
}

var (
	SelectWhere     = Where[*dialect.SelectQuery]()
	UpdateWhere     = Where[*dialect.UpdateQuery]()
	DeleteWhere     = Where[*dialect.DeleteQuery]()
	OnConflictWhere = Where[*clause.ConflictClause]() // Used in ON CONFLICT DO UPDATE
)

func Where[Q psql.Filterable]() struct {
	Materials          materialWhere[Q]
	MaterialUsages     materialUsageWhere[Q]
	Operations         operationWhere[Q]
	OperationMaterials operationMaterialWhere[Q]
} {
	return struct {
		Materials          materialWhere[Q]
		MaterialUsages     materialUsageWhere[Q]
		Operations         operationWhere[Q]
		OperationMaterials operationMaterialWhere[Q]
	}{
		Materials:          buildMaterialWhere[Q](MaterialColumns),
		MaterialUsages:     buildMaterialUsageWhere[Q](MaterialUsageColumns),
		Operations:         buildOperationWhere[Q](OperationColumns),
		OperationMaterials: buildOperationMaterialWhere[Q](OperationMaterialColumns),
	}
}

var Preload = getPreloaders()

type preloaders struct {
	Material          materialPreloader
	MaterialUsage     materialUsagePreloader
	Operation         operationPreloader
	OperationMaterial operationMaterialPreloader
}

func getPreloaders() preloaders {
	return preloaders{
		Material:          buildMaterialPreloader(),
		MaterialUsage:     buildMaterialUsagePreloader(),
		Operation:         buildOperationPreloader(),
		OperationMaterial: buildOperationMaterialPreloader(),
	}
}

var (
	SelectThenLoad = getThenLoaders[*dialect.SelectQuery]()
	InsertThenLoad = getThenLoaders[*dialect.InsertQuery]()
	UpdateThenLoad = getThenLoaders[*dialect.UpdateQuery]()
)

type thenLoaders[Q orm.Loadable] struct {
	Material          materialThenLoader[Q]
	MaterialUsage     materialUsageThenLoader[Q]
	Operation         operationThenLoader[Q]
	OperationMaterial operationMaterialThenLoader[Q]
}

func getThenLoaders[Q orm.Loadable]() thenLoaders[Q] {
	return thenLoaders[Q]{
		Material:          buildMaterialThenLoader[Q](),
		MaterialUsage:     buildMaterialUsageThenLoader[Q](),
		Operation:         buildOperationThenLoader[Q](),
		OperationMaterial: buildOperationMaterialThenLoader[Q](),
	}
}

func thenLoadBuilder[Q orm.Loadable, T any](name string, f func(context.Context, bob.Executor, T, ...bob.Mod[*dialect.SelectQuery]) error) func(...bob.Mod[*dialect.SelectQuery]) orm.Loader[Q] {
	return func(queryMods ...bob.Mod[*dialect.SelectQuery]) orm.Loader[Q] {
		return orm.Loader[Q](func(ctx context.Context, exec bob.Executor, retrieved any) error {
			loader, isLoader := retrieved.(T)
			if !isLoader {
				return fmt.Errorf("object %T cannot load %q", retrieved, name)
			}

			err := f(ctx, exec, loader, queryMods...)

			// Don't cause an issue due to missing relationships
			if errors.Is(err, sql.ErrNoRows) {
				return nil
			}

			return err
		})
	}
}

var (
	SelectJoins = getJoins[*dialect.SelectQuery]()
	UpdateJoins = getJoins[*dialect.UpdateQuery]()
	DeleteJoins = getJoins[*dialect.DeleteQuery]()
)

type joinSet[Q interface{ aliasedAs(string) Q }] struct {
	InnerJoin Q
	LeftJoin  Q
	RightJoin Q
}

func (j joinSet[Q]) AliasedAs(alias string) joinSet[Q] {
	return joinSet[Q]{
		InnerJoin: j.InnerJoin.aliasedAs(alias),
		LeftJoin:  j.LeftJoin.aliasedAs(alias),
		RightJoin: j.RightJoin.aliasedAs(alias),
	}
}

type joins[Q dialect.Joinable] struct {
	Materials          joinSet[materialJoins[Q]]
	MaterialUsages     joinSet[materialUsageJoins[Q]]
	Operations         joinSet[operationJoins[Q]]
	OperationMaterials joinSet[operationMaterialJoins[Q]]
}

func buildJoinSet[Q interface{ aliasedAs(string) Q }, C any, F func(C, string) Q](c C, f F) joinSet[Q] {
	return joinSet[Q]{
		InnerJoin: f(c, clause.InnerJoin),
		LeftJoin:  f(c, clause.LeftJoin),
		RightJoin: f(c, clause.RightJoin),
	}
}

func getJoins[Q dialect.Joinable]() joins[Q] {
	return joins[Q]{
		Materials:          buildJoinSet[materialJoins[Q]](MaterialColumns, buildMaterialJoins),
		MaterialUsages:     buildJoinSet[materialUsageJoins[Q]](MaterialUsageColumns, buildMaterialUsageJoins),
		Operations:         buildJoinSet[operationJoins[Q]](OperationColumns, buildOperationJoins),
		OperationMaterials: buildJoinSet[operationMaterialJoins[Q]](OperationMaterialColumns, buildOperationMaterialJoins),
	}
}

type modAs[Q any, C interface{ AliasedAs(string) C }] struct {
	c C
	f func(C) bob.Mod[Q]
}

func (m modAs[Q, C]) Apply(q Q) {
	m.f(m.c).Apply(q)
}

func (m modAs[Q, C]) AliasedAs(alias string) bob.Mod[Q] {
	m.c = m.c.AliasedAs(alias)
	return m
}

func randInt() int64 {
	out := int64(new(maphash.Hash).Sum64())

	if out < 0 {
		return -out % 10000
	}

	return out % 10000
}

// ErrUniqueConstraint captures all unique constraint errors by explicitly leaving `s` empty.
var ErrUniqueConstraint = &UniqueConstraintError{s: ""}

type UniqueConstraintError struct {
	// schema is the schema where the unique constraint is defined.
	schema string
	// table is the name of the table where the unique constraint is defined.
	table string
	// columns are the columns constituting the unique constraint.
	columns []string
	// s is a string uniquely identifying the constraint in the raw error message returned from the database.
	s string
}

func (e *UniqueConstraintError) Error() string {
	return e.s
}

func (e *UniqueConstraintError) Is(target error) bool {
	err, ok := target.(*pq.Error)
	if !ok {
		return false
	}
	return err.Code == "23505" && (e.s == "" || err.Constraint == e.s)
}
