// Code generated by BobGen psql v0.38.0. DO NOT EDIT.
// This file is meant to be re-generated in place and/or deleted at any time.

package queries

import (
	"context"
	_ "embed"
	"io"
	"iter"
	"time"

	"github.com/shopspring/decimal"
	"github.com/stephenafamo/bob"
	"github.com/stephenafamo/bob/dialect/psql"
	"github.com/stephenafamo/bob/dialect/psql/dialect"
	"github.com/stephenafamo/bob/orm"
	"github.com/stephenafamo/scan"
)

//go:embed cash_flow_report.bob.sql
var formattedQueries_cash_flow_report string

var cashFlowReportSQL = formattedQueries_cash_flow_report[152:3171]

func CashFlowReport(FromDate time.Time, ToDate time.Time) orm.ModQuery[*dialect.SelectQuery, CashFlowReportRow, []CashFlowReportRow] {
	var expressionTypArgs cashFlowReport

	expressionTypArgs.FromDate = psql.Arg(FromDate)
	expressionTypArgs.ToDate = psql.Arg(ToDate)

	return orm.ModQuery[*dialect.SelectQuery, CashFlowReportRow, []CashFlowReportRow]{
		Query: orm.Query[orm.ModExpression[*dialect.SelectQuery], CashFlowReportRow, []CashFlowReportRow]{
			ExecQuery: orm.ExecQuery[orm.ModExpression[*dialect.SelectQuery]]{
				BaseQuery: bob.BaseQuery[orm.ModExpression[*dialect.SelectQuery]]{
					Expression: expressionTypArgs,
					Dialect:    dialect.Dialect,
					QueryType:  bob.QueryTypeSelect,
				},
			}, Scanner: scan.StructMapper[CashFlowReportRow](),
		},
	}
}

type CashFlowReportRow struct {
	TotalIncome           decimal.Decimal `db:"total_income"`
	TotalExpense          decimal.Decimal `db:"total_expense"`
	NetAmount             int             `db:"net_amount"`
	PendingApprovalCount  int64           `db:"pending_approval_count"`
	PendingApprovalAmount decimal.Decimal `db:"pending_approval_amount"`
	TotalCashAll          decimal.Decimal `db:"total_cash_all"`
	TotalCreditCardAll    decimal.Decimal `db:"total_credit_card_all"`
	TotalMposAll          decimal.Decimal `db:"total_mpos_all"`
	TotalBankAll          decimal.Decimal `db:"total_bank_all"`
	TotalMomoAll          decimal.Decimal `db:"total_momo_all"`
	PendingCash           decimal.Decimal `db:"pending_cash"`
	PendingCreditCard     decimal.Decimal `db:"pending_credit_card"`
	PendingMpos           decimal.Decimal `db:"pending_mpos"`
	PendingBank           decimal.Decimal `db:"pending_bank"`
	PendingMomo           decimal.Decimal `db:"pending_momo"`
}

type cashFlowReport struct {
	FromDate bob.Expression
	ToDate   bob.Expression
}

func (o cashFlowReport) args() iter.Seq[orm.ArgWithPosition] {
	return func(yield func(arg orm.ArgWithPosition) bool) {
		if !yield(orm.ArgWithPosition{
			Name:       "fromDate",
			Start:      401,
			Stop:       403,
			Expression: o.FromDate,
		}) {
			return
		}

		if !yield(orm.ArgWithPosition{
			Name:       "fromDate",
			Start:      456,
			Stop:       458,
			Expression: o.FromDate,
		}) {
			return
		}

		if !yield(orm.ArgWithPosition{
			Name:       "toDate",
			Start:      467,
			Stop:       469,
			Expression: o.ToDate,
		}) {
			return
		}

		if !yield(orm.ArgWithPosition{
			Name:       "toDate",
			Start:      522,
			Stop:       524,
			Expression: o.ToDate,
		}) {
			return
		}

		if !yield(orm.ArgWithPosition{
			Name:       "fromDate",
			Start:      1153,
			Stop:       1155,
			Expression: o.FromDate,
		}) {
			return
		}

		if !yield(orm.ArgWithPosition{
			Name:       "fromDate",
			Start:      1208,
			Stop:       1210,
			Expression: o.FromDate,
		}) {
			return
		}

		if !yield(orm.ArgWithPosition{
			Name:       "toDate",
			Start:      1219,
			Stop:       1221,
			Expression: o.ToDate,
		}) {
			return
		}

		if !yield(orm.ArgWithPosition{
			Name:       "toDate",
			Start:      1274,
			Stop:       1276,
			Expression: o.ToDate,
		}) {
			return
		}

		if !yield(orm.ArgWithPosition{
			Name:       "fromDate",
			Start:      1798,
			Stop:       1800,
			Expression: o.FromDate,
		}) {
			return
		}

		if !yield(orm.ArgWithPosition{
			Name:       "fromDate",
			Start:      1856,
			Stop:       1859,
			Expression: o.FromDate,
		}) {
			return
		}

		if !yield(orm.ArgWithPosition{
			Name:       "toDate",
			Start:      1868,
			Stop:       1871,
			Expression: o.ToDate,
		}) {
			return
		}

		if !yield(orm.ArgWithPosition{
			Name:       "toDate",
			Start:      1927,
			Stop:       1930,
			Expression: o.ToDate,
		}) {
			return
		}
	}
}

func (o cashFlowReport) raw(from, to int) string {
	return cashFlowReportSQL[from:to]
}

func (o cashFlowReport) expr(from, to int) bob.Expression {
	return orm.ArgsToExpression(cashFlowReportSQL, from, to, o.args())
}

func (o cashFlowReport) Apply(q *dialect.SelectQuery) {
	q.AppendCTE(o.expr(5, 2521))
	q.AppendSelect(o.expr(2533, 2928))
	q.SetTable(o.expr(2934, 3019))
}

func (o cashFlowReport) WriteSQL(ctx context.Context, w io.Writer, d bob.Dialect, start int) ([]any, error) {
	return o.expr(0, len(cashFlowReportSQL)).WriteSQL(ctx, w, d, start)
}
