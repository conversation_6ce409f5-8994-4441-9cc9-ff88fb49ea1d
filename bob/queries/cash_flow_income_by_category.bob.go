// Code generated by BobGen psql v0.38.0. DO NOT EDIT.
// This file is meant to be re-generated in place and/or deleted at any time.

package queries

import (
	"context"
	_ "embed"
	"io"
	"iter"
	"time"

	"github.com/stephenafamo/bob"
	"github.com/stephenafamo/bob/dialect/psql"
	"github.com/stephenafamo/bob/dialect/psql/dialect"
	"github.com/stephenafamo/bob/orm"
	"github.com/stephenafamo/scan"
)

//go:embed cash_flow_income_by_category.bob.sql
var formattedQueries_cash_flow_income_by_category string

var cashFlowIncomeByCategorySQL = formattedQueries_cash_flow_income_by_category[162:689]

func CashFlowIncomeByCategory(FromDate time.Time, ToDate time.Time) orm.ModQuery[*dialect.SelectQuery, CashFlowIncomeByCategoryRow, []CashFlowIncomeByCategoryRow] {
	var expressionTypArgs cashFlowIncomeByCategory

	expressionTypArgs.FromDate = psql.Arg(FromDate)
	expressionTypArgs.ToDate = psql.Arg(ToDate)

	return orm.ModQuery[*dialect.SelectQuery, CashFlowIncomeByCategoryRow, []CashFlowIncomeByCategoryRow]{
		Query: orm.Query[orm.ModExpression[*dialect.SelectQuery], CashFlowIncomeByCategoryRow, []CashFlowIncomeByCategoryRow]{
			ExecQuery: orm.ExecQuery[orm.ModExpression[*dialect.SelectQuery]]{
				BaseQuery: bob.BaseQuery[orm.ModExpression[*dialect.SelectQuery]]{
					Expression: expressionTypArgs,
					Dialect:    dialect.Dialect,
					QueryType:  bob.QueryTypeSelect,
				},
			}, Scanner: scan.StructMapper[CashFlowIncomeByCategoryRow](),
		},
	}
}

type CashFlowIncomeByCategoryRow struct {
	CategoryID   int64  `db:"category_id"`
	CategoryName string `db:"category_name"`
	Amount       int    `db:"amount"`
	Count        int64  `db:"count"`
}

type cashFlowIncomeByCategory struct {
	FromDate bob.Expression
	ToDate   bob.Expression
}

func (o cashFlowIncomeByCategory) args() iter.Seq[orm.ArgWithPosition] {
	return func(yield func(arg orm.ArgWithPosition) bool) {
		if !yield(orm.ArgWithPosition{
			Name:       "fromDate",
			Start:      347,
			Stop:       349,
			Expression: o.FromDate,
		}) {
			return
		}

		if !yield(orm.ArgWithPosition{
			Name:       "fromDate",
			Start:      402,
			Stop:       404,
			Expression: o.FromDate,
		}) {
			return
		}

		if !yield(orm.ArgWithPosition{
			Name:       "toDate",
			Start:      415,
			Stop:       417,
			Expression: o.ToDate,
		}) {
			return
		}

		if !yield(orm.ArgWithPosition{
			Name:       "toDate",
			Start:      470,
			Stop:       472,
			Expression: o.ToDate,
		}) {
			return
		}
	}
}

func (o cashFlowIncomeByCategory) raw(from, to int) string {
	return cashFlowIncomeByCategorySQL[from:to]
}

func (o cashFlowIncomeByCategory) expr(from, to int) bob.Expression {
	return orm.ArgsToExpression(cashFlowIncomeByCategorySQL, from, to, o.args())
}

func (o cashFlowIncomeByCategory) Apply(q *dialect.SelectQuery) {
	q.AppendSelect(o.expr(12, 120))
	q.SetTable(o.expr(139, 255))
	q.AppendWhere(o.expr(262, 473))
	q.AppendGroup(o.expr(483, 506))
	q.AppendOrder(o.expr(516, 527))
}

func (o cashFlowIncomeByCategory) WriteSQL(ctx context.Context, w io.Writer, d bob.Dialect, start int) ([]any, error) {
	return o.expr(0, len(cashFlowIncomeByCategorySQL)).WriteSQL(ctx, w, d, start)
}
