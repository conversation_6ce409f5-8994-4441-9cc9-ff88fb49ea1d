// Code generated by BobGen psql v0.38.0. DO NOT EDIT.
// This file is meant to be re-generated in place and/or deleted at any time.

package queries

import (
	"context"
	_ "embed"
	"encoding/json"
	"io"
	"iter"
	"time"

	"github.com/stephenafamo/bob"
	"github.com/stephenafamo/bob/dialect/psql"
	"github.com/stephenafamo/bob/dialect/psql/dialect"
	"github.com/stephenafamo/bob/orm"
	"github.com/stephenafamo/bob/types"
	"github.com/stephenafamo/scan"
)

//go:embed material_usage_detail_report.bob.sql
var formattedQueries_material_usage_detail_report string

var materialUsageDetailReportSQL = formattedQueries_material_usage_detail_report[163:4277]

func MaterialUsageDetailReport(From time.Time, To time.Time, Arg3 int64, Arg4 int64, Arg5 int64, Arg6 string, Limit int32, Offset int32) orm.ModQuery[*dialect.SelectQuery, MaterialUsageDetailReportRow, []MaterialUsageDetailReportRow] {
	var expressionTypArgs materialUsageDetailReport

	expressionTypArgs.From = psql.Arg(From)
	expressionTypArgs.To = psql.Arg(To)
	expressionTypArgs.Arg3 = psql.Arg(Arg3)
	expressionTypArgs.Arg4 = psql.Arg(Arg4)
	expressionTypArgs.Arg5 = psql.Arg(Arg5)
	expressionTypArgs.Arg6 = psql.Arg(Arg6)
	expressionTypArgs.Limit = psql.Arg(Limit)
	expressionTypArgs.Offset = psql.Arg(Offset)

	return orm.ModQuery[*dialect.SelectQuery, MaterialUsageDetailReportRow, []MaterialUsageDetailReportRow]{
		Query: orm.Query[orm.ModExpression[*dialect.SelectQuery], MaterialUsageDetailReportRow, []MaterialUsageDetailReportRow]{
			ExecQuery: orm.ExecQuery[orm.ModExpression[*dialect.SelectQuery]]{
				BaseQuery: bob.BaseQuery[orm.ModExpression[*dialect.SelectQuery]]{
					Expression: expressionTypArgs,
					Dialect:    dialect.Dialect,
					QueryType:  bob.QueryTypeSelect,
				},
			}, Scanner: scan.StructMapper[MaterialUsageDetailReportRow](),
		},
	}
}

type MaterialUsageDetailReportRow struct {
	PersonName       string                      `db:"person_name"`
	PersonID         int64                       `db:"person_id"`
	CreatedAt        string                      `db:"created_at"`
	OperationDetails types.JSON[json.RawMessage] `db:"operation_details"`
}

type materialUsageDetailReport struct {
	From   bob.Expression
	To     bob.Expression
	Arg3   bob.Expression
	Arg4   bob.Expression
	Arg5   bob.Expression
	Arg6   bob.Expression
	Limit  bob.Expression
	Offset bob.Expression
}

func (o materialUsageDetailReport) args() iter.Seq[orm.ArgWithPosition] {
	return func(yield func(arg orm.ArgWithPosition) bool) {
		if !yield(orm.ArgWithPosition{
			Name:       "from",
			Start:      467,
			Stop:       469,
			Expression: o.From,
		}) {
			return
		}

		if !yield(orm.ArgWithPosition{
			Name:       "to",
			Start:      498,
			Stop:       500,
			Expression: o.To,
		}) {
			return
		}

		if !yield(orm.ArgWithPosition{
			Name:       "arg3",
			Start:      519,
			Stop:       521,
			Expression: o.Arg3,
		}) {
			return
		}

		if !yield(orm.ArgWithPosition{
			Name:       "arg3",
			Start:      544,
			Stop:       546,
			Expression: o.Arg3,
		}) {
			return
		}

		if !yield(orm.ArgWithPosition{
			Name:       "arg4",
			Start:      573,
			Stop:       575,
			Expression: o.Arg4,
		}) {
			return
		}

		if !yield(orm.ArgWithPosition{
			Name:       "arg4",
			Start:      609,
			Stop:       611,
			Expression: o.Arg4,
		}) {
			return
		}

		if !yield(orm.ArgWithPosition{
			Name:       "arg5",
			Start:      638,
			Stop:       640,
			Expression: o.Arg5,
		}) {
			return
		}

		if !yield(orm.ArgWithPosition{
			Name:       "arg5",
			Start:      673,
			Stop:       675,
			Expression: o.Arg5,
		}) {
			return
		}

		if !yield(orm.ArgWithPosition{
			Name:       "arg6",
			Start:      702,
			Stop:       704,
			Expression: o.Arg6,
		}) {
			return
		}

		if !yield(orm.ArgWithPosition{
			Name:       "arg6",
			Start:      756,
			Stop:       759,
			Expression: o.Arg6,
		}) {
			return
		}

		if !yield(orm.ArgWithPosition{
			Name:       "arg6",
			Start:      806,
			Stop:       809,
			Expression: o.Arg6,
		}) {
			return
		}

		if !yield(orm.ArgWithPosition{
			Name:       "arg6",
			Start:      866,
			Stop:       869,
			Expression: o.Arg6,
		}) {
			return
		}

		if !yield(orm.ArgWithPosition{
			Name:       "arg6",
			Start:      916,
			Stop:       919,
			Expression: o.Arg6,
		}) {
			return
		}

		if !yield(orm.ArgWithPosition{
			Name:       "limit",
			Start:      1013,
			Stop:       1016,
			Expression: o.Limit,
		}) {
			return
		}

		if !yield(orm.ArgWithPosition{
			Name:       "offset",
			Start:      1035,
			Stop:       1038,
			Expression: o.Offset,
		}) {
			return
		}

		if !yield(orm.ArgWithPosition{
			Name:       "arg4",
			Start:      1476,
			Stop:       1479,
			Expression: o.Arg4,
		}) {
			return
		}

		if !yield(orm.ArgWithPosition{
			Name:       "arg4",
			Start:      1513,
			Stop:       1516,
			Expression: o.Arg4,
		}) {
			return
		}

		if !yield(orm.ArgWithPosition{
			Name:       "arg5",
			Start:      1533,
			Stop:       1536,
			Expression: o.Arg5,
		}) {
			return
		}

		if !yield(orm.ArgWithPosition{
			Name:       "arg5",
			Start:      1569,
			Stop:       1572,
			Expression: o.Arg5,
		}) {
			return
		}

		if !yield(orm.ArgWithPosition{
			Name:       "arg6",
			Start:      1589,
			Stop:       1592,
			Expression: o.Arg6,
		}) {
			return
		}

		if !yield(orm.ArgWithPosition{
			Name:       "arg6",
			Start:      1633,
			Stop:       1636,
			Expression: o.Arg6,
		}) {
			return
		}

		if !yield(orm.ArgWithPosition{
			Name:       "arg6",
			Start:      1686,
			Stop:       1689,
			Expression: o.Arg6,
		}) {
			return
		}

		if !yield(orm.ArgWithPosition{
			Name:       "arg6",
			Start:      1729,
			Stop:       1732,
			Expression: o.Arg6,
		}) {
			return
		}
	}
}

func (o materialUsageDetailReport) raw(from, to int) string {
	return materialUsageDetailReportSQL[from:to]
}

func (o materialUsageDetailReport) expr(from, to int) bob.Expression {
	return orm.ArgsToExpression(materialUsageDetailReportSQL, from, to, o.args())
}

func (o materialUsageDetailReport) Apply(q *dialect.SelectQuery) {
	q.AppendCTE(o.expr(5, 3553))
	q.AppendSelect(o.expr(3566, 3908))
	q.SetTable(o.expr(3914, 4005))
	q.AppendGroup(o.expr(4015, 4087))
	q.AppendOrder(o.expr(4097, 4114))
}

func (o materialUsageDetailReport) WriteSQL(ctx context.Context, w io.Writer, d bob.Dialect, start int) ([]any, error) {
	return o.expr(0, len(materialUsageDetailReportSQL)).WriteSQL(ctx, w, d, start)
}
