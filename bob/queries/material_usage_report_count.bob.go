// Code generated by BobGen psql v0.38.0. DO NOT EDIT.
// This file is meant to be re-generated in place and/or deleted at any time.

package queries

import (
	"context"
	_ "embed"
	"io"
	"iter"
	"time"

	"github.com/stephenafamo/bob"
	"github.com/stephenafamo/bob/dialect/psql"
	"github.com/stephenafamo/bob/dialect/psql/dialect"
	"github.com/stephenafamo/bob/orm"
	"github.com/stephenafamo/scan"
)

//go:embed material_usage_report_count.bob.sql
var formattedQueries_material_usage_report_count string

var materialUsageReportCountSQL = formattedQueries_material_usage_report_count[162:849]

func MaterialUsageReportCount(From time.Time, To time.Time, Arg3 int64, Arg4 int64, Arg5 string) orm.ModQuery[*dialect.SelectQuery, int64, []int64] {
	var expressionTypArgs materialUsageReportCount

	expressionTypArgs.From = psql.Arg(From)
	expressionTypArgs.To = psql.Arg(To)
	expressionTypArgs.Arg3 = psql.Arg(Arg3)
	expressionTypArgs.Arg4 = psql.Arg(Arg4)
	expressionTypArgs.Arg5 = psql.Arg(Arg5)

	return orm.ModQuery[*dialect.SelectQuery, int64, []int64]{
		Query: orm.Query[orm.ModExpression[*dialect.SelectQuery], int64, []int64]{
			ExecQuery: orm.ExecQuery[orm.ModExpression[*dialect.SelectQuery]]{
				BaseQuery: bob.BaseQuery[orm.ModExpression[*dialect.SelectQuery]]{
					Expression: expressionTypArgs,
					Dialect:    dialect.Dialect,
					QueryType:  bob.QueryTypeSelect,
				},
			}, Scanner: scan.ColumnMapper[int64]("total_records"),
		},
	}
}

type MaterialUsageReportCountRow struct {
	TotalRecords int64 `db:"total_records"`
}

type materialUsageReportCount struct {
	From bob.Expression
	To   bob.Expression
	Arg3 bob.Expression
	Arg4 bob.Expression
	Arg5 bob.Expression
}

func (o materialUsageReportCount) args() iter.Seq[orm.ArgWithPosition] {
	return func(yield func(arg orm.ArgWithPosition) bool) {
		if !yield(orm.ArgWithPosition{
			Name:       "from",
			Start:      276,
			Stop:       278,
			Expression: o.From,
		}) {
			return
		}

		if !yield(orm.ArgWithPosition{
			Name:       "to",
			Start:      309,
			Stop:       311,
			Expression: o.To,
		}) {
			return
		}

		if !yield(orm.ArgWithPosition{
			Name:       "arg3",
			Start:      326,
			Stop:       328,
			Expression: o.Arg3,
		}) {
			return
		}

		if !yield(orm.ArgWithPosition{
			Name:       "arg3",
			Start:      362,
			Stop:       364,
			Expression: o.Arg3,
		}) {
			return
		}

		if !yield(orm.ArgWithPosition{
			Name:       "arg4",
			Start:      387,
			Stop:       389,
			Expression: o.Arg4,
		}) {
			return
		}

		if !yield(orm.ArgWithPosition{
			Name:       "arg4",
			Start:      422,
			Stop:       424,
			Expression: o.Arg4,
		}) {
			return
		}

		if !yield(orm.ArgWithPosition{
			Name:       "arg5",
			Start:      447,
			Stop:       449,
			Expression: o.Arg5,
		}) {
			return
		}

		if !yield(orm.ArgWithPosition{
			Name:       "arg5",
			Start:      495,
			Stop:       497,
			Expression: o.Arg5,
		}) {
			return
		}

		if !yield(orm.ArgWithPosition{
			Name:       "arg5",
			Start:      554,
			Stop:       556,
			Expression: o.Arg5,
		}) {
			return
		}

		if !yield(orm.ArgWithPosition{
			Name:       "arg5",
			Start:      603,
			Stop:       606,
			Expression: o.Arg5,
		}) {
			return
		}
	}
}

func (o materialUsageReportCount) raw(from, to int) string {
	return materialUsageReportCountSQL[from:to]
}

func (o materialUsageReportCount) expr(from, to int) bob.Expression {
	return orm.ArgsToExpression(materialUsageReportCountSQL, from, to, o.args())
}

func (o materialUsageReportCount) Apply(q *dialect.SelectQuery) {
	q.AppendSelect(o.expr(7, 12))
	q.SetTable(o.expr(40, 687))
}

func (o materialUsageReportCount) WriteSQL(ctx context.Context, w io.Writer, d bob.Dialect, start int) ([]any, error) {
	return o.expr(0, len(materialUsageReportCountSQL)).WriteSQL(ctx, w, d, start)
}
