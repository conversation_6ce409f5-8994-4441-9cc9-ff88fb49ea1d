-- CashFlowExpenseByCategoryDepartment
SELECT 
    cfi.category_id,
    COALESCE(cat.name, 'Unknown') AS category_name,
    COALESCE(cfi.department_id, 0) AS department_id,
    COALESCE(dept.name, 'Unknown') AS department_name,
    SUM(cfi.amount) AS amount /* :int */,
    COUNT(*) AS count /* :int */
FROM cash_flow_item AS cfi
JOIN cash_flow AS cf ON cfi.cash_flow_id = cf.id
LEFT JOIN term AS cat ON cfi.category_id = cat.id
LEFT JOIN department AS dept ON cfi.department_id = dept.id
WHERE cf.state = 'PAID' 
    AND cf.type = 'expense'
    AND cf.deleted_at IS NULL
    AND ($1/* fromDate:time.Time */ = '1970-01-01 00:00:00'::timestamp OR cf.paid_at >= $1/* fromDate:time.Time */)
    AND ($2/* toDate:time.Time */ = '1970-01-01 00:00:00'::timestamp OR cf.paid_at <= $2/* toDate:time.Time */)
GROUP BY cfi.category_id, cat.name, COALESCE(cfi.department_id, 0), dept.name
ORDER BY category_name, amount DESC; 