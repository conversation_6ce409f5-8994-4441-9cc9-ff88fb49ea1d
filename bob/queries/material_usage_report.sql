-- MaterialUsageReport
SELECT m.name                           AS "name",
       m.id                             AS "id",
       m.code                           AS "code",
       m.unit                           AS "unit",
       COUNT(distinct mu.attachment_id) AS "total_attachment" /* :int */,
       SUM(mu.quoted_quantity)          AS "total_quoted_quantity" /* :float64 */,
       SUM(mu.used_quantity)            AS "total_used_quantity" /* :float64 */
FROM material_usage AS mu
         INNER JOIN material AS m ON mu.material_id = m.id
         LEFT JOIN operation AS o ON mu.operation_id = o.id
WHERE mu.deleted_at IS NULL
  AND (mu.created_at > $1/* from:time.Time */)
  AND (mu.created_at < $2/* to:time.Time */)
  AND ($3::bigint/* operationId:int */ = 0 OR mu.operation_id = $3::bigint/* operationId:int */)
  AND ($4::bigint/* materialId:int */ = 0 OR mu.material_id = $4::bigint/* materialId:int */)
  AND ($5::text/* keyword:string */ = '' OR (
         o.name ILIKE '%' || $5::text/* keyword:string */ || '%'
          OR mu.operation_key ILIKE '%' || $5::text/* keyword:string */ || '%'
          OR m.name ILIKE '%' || $5::text/* keyword:string */ || '%'
      ))
GROUP BY m.id, m.code, m.name, m.unit
ORDER BY m.name LIMIT NULLIF($6/* limit */, 0)
OFFSET NULLIF($7/* offset */, 0);