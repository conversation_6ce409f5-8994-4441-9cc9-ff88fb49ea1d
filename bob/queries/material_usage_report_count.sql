-- MaterialUsageReportCount
SELECT COUNT(*) AS "total_records"
FROM (SELECT 1
      FROM material_usage AS mu
               INNER JOIN material AS m ON mu.material_id = m.id
               LEFT JOIN operation AS o ON mu.operation_id = o.id
      WHERE mu.deleted_at IS NULL
        AND (mu.created_at > $1/* from:time.Time */)
        AND (mu.created_at < $2/* to:time.Time */)
        AND ($3::bigint/* operationId:int */ = 0 OR mu.operation_id = $3::bigint/* operationId:int */)
        AND ($4::bigint/* materialId:int */ = 0 OR mu.material_id = $4::bigint/* materialId:int */)
        AND ($5::text/* keyword:string */ = '' OR (
         o.name ILIKE '%' || $5::text/* keyword:string */ || '%'
          OR mu.operation_key ILIKE '%' || $5::text/* keyword:string */ || '%'
          OR m.name ILIKE '%' || $5::text/* keyword:string */ || '%'
      ))
      GROUP BY m.id, m.code, m.name, m.unit) AS result_set;
