// Code generated by BobGen psql v0.38.0. DO NOT EDIT.
// This file is meant to be re-generated in place and/or deleted at any time.

package queries

import (
	"context"
	"database/sql"
	_ "embed"
	"io"
	"iter"
	"time"

	"github.com/stephenafamo/bob"
	"github.com/stephenafamo/bob/dialect/psql"
	"github.com/stephenafamo/bob/dialect/psql/dialect"
	"github.com/stephenafamo/bob/orm"
	"github.com/stephenafamo/scan"
)

//go:embed material_usage_report.bob.sql
var formattedQueries_material_usage_report string

var materialUsageReportSQL = formattedQueries_material_usage_report[157:1179]

func MaterialUsageReport(From time.Time, To time.Time, Arg3 int64, Arg4 int64, Arg5 string, Limit int32, Offset int32) orm.ModQuery[*dialect.SelectQuery, MaterialUsageReportRow, []MaterialUsageReportRow] {
	var expressionTypArgs materialUsageReport

	expressionTypArgs.From = psql.Arg(From)
	expressionTypArgs.To = psql.Arg(To)
	expressionTypArgs.Arg3 = psql.Arg(Arg3)
	expressionTypArgs.Arg4 = psql.Arg(Arg4)
	expressionTypArgs.Arg5 = psql.Arg(Arg5)
	expressionTypArgs.Limit = psql.Arg(Limit)
	expressionTypArgs.Offset = psql.Arg(Offset)

	return orm.ModQuery[*dialect.SelectQuery, MaterialUsageReportRow, []MaterialUsageReportRow]{
		Query: orm.Query[orm.ModExpression[*dialect.SelectQuery], MaterialUsageReportRow, []MaterialUsageReportRow]{
			ExecQuery: orm.ExecQuery[orm.ModExpression[*dialect.SelectQuery]]{
				BaseQuery: bob.BaseQuery[orm.ModExpression[*dialect.SelectQuery]]{
					Expression: expressionTypArgs,
					Dialect:    dialect.Dialect,
					QueryType:  bob.QueryTypeSelect,
				},
			}, Scanner: scan.StructMapper[MaterialUsageReportRow](),
		},
	}
}

type MaterialUsageReportRow struct {
	Name                string           `db:"name"`
	ID                  int64            `db:"id"`
	Code                sql.Null[string] `db:"code"`
	Unit                string           `db:"unit"`
	TotalAttachment     int              `db:"total_attachment"`
	TotalQuotedQuantity float64          `db:"total_quoted_quantity"`
	TotalUsedQuantity   float64          `db:"total_used_quantity"`
}

type materialUsageReport struct {
	From   bob.Expression
	To     bob.Expression
	Arg3   bob.Expression
	Arg4   bob.Expression
	Arg5   bob.Expression
	Limit  bob.Expression
	Offset bob.Expression
}

func (o materialUsageReport) args() iter.Seq[orm.ArgWithPosition] {
	return func(yield func(arg orm.ArgWithPosition) bool) {
		if !yield(orm.ArgWithPosition{
			Name:       "from",
			Start:      597,
			Stop:       599,
			Expression: o.From,
		}) {
			return
		}

		if !yield(orm.ArgWithPosition{
			Name:       "to",
			Start:      624,
			Stop:       626,
			Expression: o.To,
		}) {
			return
		}

		if !yield(orm.ArgWithPosition{
			Name:       "arg3",
			Start:      635,
			Stop:       637,
			Expression: o.Arg3,
		}) {
			return
		}

		if !yield(orm.ArgWithPosition{
			Name:       "arg3",
			Start:      671,
			Stop:       673,
			Expression: o.Arg3,
		}) {
			return
		}

		if !yield(orm.ArgWithPosition{
			Name:       "arg4",
			Start:      690,
			Stop:       692,
			Expression: o.Arg4,
		}) {
			return
		}

		if !yield(orm.ArgWithPosition{
			Name:       "arg4",
			Start:      725,
			Stop:       727,
			Expression: o.Arg4,
		}) {
			return
		}

		if !yield(orm.ArgWithPosition{
			Name:       "arg5",
			Start:      744,
			Stop:       746,
			Expression: o.Arg5,
		}) {
			return
		}

		if !yield(orm.ArgWithPosition{
			Name:       "arg5",
			Start:      792,
			Stop:       794,
			Expression: o.Arg5,
		}) {
			return
		}

		if !yield(orm.ArgWithPosition{
			Name:       "arg5",
			Start:      851,
			Stop:       853,
			Expression: o.Arg5,
		}) {
			return
		}

		if !yield(orm.ArgWithPosition{
			Name:       "arg5",
			Start:      900,
			Stop:       903,
			Expression: o.Arg5,
		}) {
			return
		}

		if !yield(orm.ArgWithPosition{
			Name:       "limit",
			Start:      993,
			Stop:       996,
			Expression: o.Limit,
		}) {
			return
		}

		if !yield(orm.ArgWithPosition{
			Name:       "offset",
			Start:      1015,
			Stop:       1018,
			Expression: o.Offset,
		}) {
			return
		}
	}
}

func (o materialUsageReport) raw(from, to int) string {
	return materialUsageReportSQL[from:to]
}

func (o materialUsageReport) expr(from, to int) bob.Expression {
	return orm.ArgsToExpression(materialUsageReportSQL, from, to, o.args())
}

func (o materialUsageReport) Apply(q *dialect.SelectQuery) {
	q.SetLimit(psql.Raw("NULLIF($6/* limit */, 0)"))
	q.SetOffset(psql.Raw("NULLIF($7/* offset */, 0)"))
	q.AppendSelect(o.expr(7, 399))
	q.SetTable(o.expr(406, 545))
	q.AppendWhere(o.expr(552, 925))
	q.AppendGroup(o.expr(935, 963))
	q.AppendOrder(o.expr(973, 979))
}

func (o materialUsageReport) WriteSQL(ctx context.Context, w io.Writer, d bob.Dialect, start int) ([]any, error) {
	return o.expr(0, len(materialUsageReportSQL)).WriteSQL(ctx, w, d, start)
}
