-- Code generated by BobGen psql v0.38.0. DO NOT EDIT.
-- This file is meant to be re-generated in place and/or deleted at any time.

-- CashFlowReport
WITH paid_summary AS (
    SELECT
        cf.type,
        cfi.category_id,
        COALESCE(t.name, 'Unknown') AS category_name,
        SUM(cfi.amount) AS total_amount ,
        COUNT(DISTINCT cf.id) AS count 
FROM cash_flow AS cf
    JOIN cash_flow_item AS cfi ON cf.id = cfi.cash_flow_id
    LEFT JOIN term AS t ON cfi.category_id = t.id
WHERE cf.state = 'PAID'
  AND cf.deleted_at IS NULL
  AND ($1 = '1970-01-01 00:00:00'::timestamp OR cf.paid_at >= $2)
  AND ($3 = '1970-01-01 00:00:00'::timestamp OR cf.paid_at <= $4)
GROUP BY cf.type, cfi.category_id, t.name
    ),
    payment_methods_summary AS (
SELECT
    cf.type,
    SUM(CASE WHEN cf.type = 'income' THEN cf.cash ELSE -cf.cash END) AS total_cash ,
    SUM(CASE WHEN cf.type = 'income' THEN cf.credit_card ELSE -cf.credit_card END) AS total_credit_card ,
    SUM(CASE WHEN cf.type = 'income' THEN cf.mpos ELSE -cf.mpos END) AS total_mpos ,
    SUM(CASE WHEN cf.type = 'income' THEN cf.bank ELSE -cf.bank END) AS total_bank ,
    SUM(CASE WHEN cf.type = 'income' THEN cf.momo ELSE -cf.momo END) AS total_momo 
FROM cash_flow AS cf
WHERE cf.state = 'PAID'
  AND cf.deleted_at IS NULL
  AND ($5 = '1970-01-01 00:00:00'::timestamp OR cf.paid_at >= $6)
  AND ($7 = '1970-01-01 00:00:00'::timestamp OR cf.paid_at <= $8)
GROUP BY cf.type
    ),
    pending_summary AS (
SELECT
    COUNT(*) AS pending_approval_count ,
    COALESCE(SUM(cf.amount), 0) AS pending_approval_amount ,
    COALESCE(SUM(cf.cash), 0) AS pending_cash ,
    COALESCE(SUM(cf.credit_card), 0) AS pending_credit_card ,
    COALESCE(SUM(cf.mpos), 0) AS pending_mpos ,
    COALESCE(SUM(cf.bank), 0) AS pending_bank ,
    COALESCE(SUM(cf.momo), 0) AS pending_momo 
FROM cash_flow AS cf
WHERE cf.state = 'PENDING'
  AND cf.type = 'expense'
  AND cf.deleted_at IS NULL
  AND ($9 = '1970-01-01 00:00:00'::timestamp OR cf.created_at >= $10)
  AND ($11 = '1970-01-01 00:00:00'::timestamp OR cf.created_at <= $12)
    ),
    totals AS (
SELECT
    COALESCE(SUM(CASE WHEN type = 'income' THEN total_amount ELSE 0 END), 0) AS total_income ,
    COALESCE(SUM(CASE WHEN type = 'expense' THEN total_amount ELSE 0 END), 0) AS total_expense 
FROM paid_summary
    ),
    payment_totals AS (
SELECT
    COALESCE(SUM(total_cash), 0) AS total_cash_all ,
    COALESCE(SUM(total_credit_card), 0) AS total_credit_card_all ,
    COALESCE(SUM(total_mpos), 0) AS total_mpos_all ,
    COALESCE(SUM(total_bank), 0) AS total_bank_all ,
    COALESCE(SUM(total_momo), 0) AS total_momo_all 
FROM payment_methods_summary
    )
SELECT
    t.total_income,
    t.total_expense,
    (t.total_income - t.total_expense) AS net_amount ,
    ps.pending_approval_count,
    ps.pending_approval_amount,
        pt.total_cash_all,
    pt.total_credit_card_all,
    pt.total_mpos_all,
    pt.total_bank_all,
    pt.total_momo_all,
        ps.pending_cash,
    ps.pending_credit_card,
    ps.pending_mpos,
    ps.pending_bank,
    ps.pending_momo
FROM totals t
         CROSS JOIN payment_totals pt
         CROSS JOIN pending_summary ps;
