// Code generated by BobGen psql v0.38.0. DO NOT EDIT.
// This file is meant to be re-generated in place and/or deleted at any time.

package queries

import (
	"context"
	_ "embed"
	"io"
	"iter"
	"time"

	"github.com/stephenafamo/bob"
	"github.com/stephenafamo/bob/dialect/psql"
	"github.com/stephenafamo/bob/dialect/psql/dialect"
	"github.com/stephenafamo/bob/orm"
	"github.com/stephenafamo/scan"
)

//go:embed cash_flow_expense_by_category.bob.sql
var formattedQueries_cash_flow_expense_by_category string

var cashFlowExpenseByCategorySQL = formattedQueries_cash_flow_expense_by_category[163:691]

func CashFlowExpenseByCategory(FromDate time.Time, ToDate time.Time) orm.ModQuery[*dialect.SelectQuery, CashFlowExpenseByCategoryRow, []CashFlowExpenseByCategoryRow] {
	var expressionTypArgs cashFlowExpenseByCategory

	expressionTypArgs.FromDate = psql.Arg(FromDate)
	expressionTypArgs.ToDate = psql.Arg(ToDate)

	return orm.ModQuery[*dialect.SelectQuery, CashFlowExpenseByCategoryRow, []CashFlowExpenseByCategoryRow]{
		Query: orm.Query[orm.ModExpression[*dialect.SelectQuery], CashFlowExpenseByCategoryRow, []CashFlowExpenseByCategoryRow]{
			ExecQuery: orm.ExecQuery[orm.ModExpression[*dialect.SelectQuery]]{
				BaseQuery: bob.BaseQuery[orm.ModExpression[*dialect.SelectQuery]]{
					Expression: expressionTypArgs,
					Dialect:    dialect.Dialect,
					QueryType:  bob.QueryTypeSelect,
				},
			}, Scanner: scan.StructMapper[CashFlowExpenseByCategoryRow](),
		},
	}
}

type CashFlowExpenseByCategoryRow struct {
	CategoryID   int64  `db:"category_id"`
	CategoryName string `db:"category_name"`
	Amount       int    `db:"amount"`
	Count        int64  `db:"count"`
}

type cashFlowExpenseByCategory struct {
	FromDate bob.Expression
	ToDate   bob.Expression
}

func (o cashFlowExpenseByCategory) args() iter.Seq[orm.ArgWithPosition] {
	return func(yield func(arg orm.ArgWithPosition) bool) {
		if !yield(orm.ArgWithPosition{
			Name:       "fromDate",
			Start:      348,
			Stop:       350,
			Expression: o.FromDate,
		}) {
			return
		}

		if !yield(orm.ArgWithPosition{
			Name:       "fromDate",
			Start:      403,
			Stop:       405,
			Expression: o.FromDate,
		}) {
			return
		}

		if !yield(orm.ArgWithPosition{
			Name:       "toDate",
			Start:      416,
			Stop:       418,
			Expression: o.ToDate,
		}) {
			return
		}

		if !yield(orm.ArgWithPosition{
			Name:       "toDate",
			Start:      471,
			Stop:       473,
			Expression: o.ToDate,
		}) {
			return
		}
	}
}

func (o cashFlowExpenseByCategory) raw(from, to int) string {
	return cashFlowExpenseByCategorySQL[from:to]
}

func (o cashFlowExpenseByCategory) expr(from, to int) bob.Expression {
	return orm.ArgsToExpression(cashFlowExpenseByCategorySQL, from, to, o.args())
}

func (o cashFlowExpenseByCategory) Apply(q *dialect.SelectQuery) {
	q.AppendSelect(o.expr(12, 120))
	q.SetTable(o.expr(139, 255))
	q.AppendWhere(o.expr(262, 474))
	q.AppendGroup(o.expr(484, 507))
	q.AppendOrder(o.expr(517, 528))
}

func (o cashFlowExpenseByCategory) WriteSQL(ctx context.Context, w io.Writer, d bob.Dialect, start int) ([]any, error) {
	return o.expr(0, len(cashFlowExpenseByCategorySQL)).WriteSQL(ctx, w, d, start)
}
