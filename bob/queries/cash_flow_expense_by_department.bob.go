// Code generated by BobGen psql v0.38.0. DO NOT EDIT.
// This file is meant to be re-generated in place and/or deleted at any time.

package queries

import (
	"context"
	_ "embed"
	"io"
	"iter"
	"time"

	"github.com/stephenafamo/bob"
	"github.com/stephenafamo/bob/dialect/psql"
	"github.com/stephenafamo/bob/dialect/psql/dialect"
	"github.com/stephenafamo/bob/orm"
	"github.com/stephenafamo/scan"
)

//go:embed cash_flow_expense_by_department.bob.sql
var formattedQueries_cash_flow_expense_by_department string

var cashFlowExpenseByDepartmentSQL = formattedQueries_cash_flow_expense_by_department[165:929]

func CashFlowExpenseByDepartment(FromDate time.Time, ToDate time.Time) orm.ModQuery[*dialect.SelectQuery, CashFlowExpenseByDepartmentRow, []CashFlowExpenseByDepartmentRow] {
	var expressionTypArgs cashFlowExpenseByDepartment

	expressionTypArgs.FromDate = psql.Arg(FromDate)
	expressionTypArgs.ToDate = psql.Arg(ToDate)

	return orm.ModQuery[*dialect.SelectQuery, CashFlowExpenseByDepartmentRow, []CashFlowExpenseByDepartmentRow]{
		Query: orm.Query[orm.ModExpression[*dialect.SelectQuery], CashFlowExpenseByDepartmentRow, []CashFlowExpenseByDepartmentRow]{
			ExecQuery: orm.ExecQuery[orm.ModExpression[*dialect.SelectQuery]]{
				BaseQuery: bob.BaseQuery[orm.ModExpression[*dialect.SelectQuery]]{
					Expression: expressionTypArgs,
					Dialect:    dialect.Dialect,
					QueryType:  bob.QueryTypeSelect,
				},
			}, Scanner: scan.StructMapper[CashFlowExpenseByDepartmentRow](),
		},
	}
}

type CashFlowExpenseByDepartmentRow struct {
	DepartmentID   int64  `db:"department_id"`
	DepartmentName string `db:"department_name"`
	CategoryID     int64  `db:"category_id"`
	CategoryName   string `db:"category_name"`
	Amount         int    `db:"amount"`
	Count          int64  `db:"count"`
}

type cashFlowExpenseByDepartment struct {
	FromDate bob.Expression
	ToDate   bob.Expression
}

func (o cashFlowExpenseByDepartment) args() iter.Seq[orm.ArgWithPosition] {
	return func(yield func(arg orm.ArgWithPosition) bool) {
		if !yield(orm.ArgWithPosition{
			Name:       "fromDate",
			Start:      522,
			Stop:       524,
			Expression: o.FromDate,
		}) {
			return
		}

		if !yield(orm.ArgWithPosition{
			Name:       "fromDate",
			Start:      577,
			Stop:       579,
			Expression: o.FromDate,
		}) {
			return
		}

		if !yield(orm.ArgWithPosition{
			Name:       "toDate",
			Start:      590,
			Stop:       592,
			Expression: o.ToDate,
		}) {
			return
		}

		if !yield(orm.ArgWithPosition{
			Name:       "toDate",
			Start:      645,
			Stop:       647,
			Expression: o.ToDate,
		}) {
			return
		}
	}
}

func (o cashFlowExpenseByDepartment) raw(from, to int) string {
	return cashFlowExpenseByDepartmentSQL[from:to]
}

func (o cashFlowExpenseByDepartment) expr(from, to int) bob.Expression {
	return orm.ArgsToExpression(cashFlowExpenseByDepartmentSQL, from, to, o.args())
}

func (o cashFlowExpenseByDepartment) Apply(q *dialect.SelectQuery) {
	q.AppendSelect(o.expr(12, 230))
	q.SetTable(o.expr(249, 429))
	q.AppendWhere(o.expr(436, 648))
	q.AppendGroup(o.expr(658, 726))
	q.AppendOrder(o.expr(736, 764))
}

func (o cashFlowExpenseByDepartment) WriteSQL(ctx context.Context, w io.Writer, d bob.Dialect, start int) ([]any, error) {
	return o.expr(0, len(cashFlowExpenseByDepartmentSQL)).WriteSQL(ctx, w, d, start)
}
