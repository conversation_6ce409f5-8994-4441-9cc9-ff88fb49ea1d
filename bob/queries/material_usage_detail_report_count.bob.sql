-- Code generated by BobGen psql v0.38.0. DO NOT EDIT.
-- This file is meant to be re-generated in place and/or deleted at any time.

-- MaterialUsageDetailReportCount
SELECT COUNT(DISTINCT a.id) AS total_attachments
FROM material_usage AS mu
         LEFT JOIN operation AS o ON mu.operation_id = o.id
         LEFT JOIN attachment AS a ON mu.attachment_id = a.id
         LEFT JOIN person AS p ON a.person_id = p.id
         LEFT JOIN material AS m ON mu.material_id = m.id
WHERE mu.deleted_at IS NULL
  AND a.id IS NOT NULL
    AND (mu.created_at > $1)
  AND (mu.created_at < $2)
    AND ($3::bigint = 0 OR p.id = $4::bigint)
    AND ($5::bigint = 0 OR mu.operation_id = $6::bigint)
    AND ($7::bigint = 0 OR mu.material_id = $8::bigint)
    AND ($9::text = '' OR (
      p.full_name ILIKE '%' || $10::text || '%'
      OR o.name ILIKE '%' || $11::text || '%'
      OR mu.operation_key ILIKE '%' || $12::text || '%'
      OR m.name ILIKE '%' || $13::text || '%'
  ));
