-- Code generated by BobGen psql v0.38.0. DO NOT EDIT.
-- This file is meant to be re-generated in place and/or deleted at any time.

-- MaterialUsageReport
SELECT m.name                           AS "name",
       m.id                             AS "id",
       m.code                           AS "code",
       m.unit                           AS "unit",
       COUNT(distinct mu.attachment_id) AS "total_attachment" ,
       SUM(mu.quoted_quantity)          AS "total_quoted_quantity" ,
       SUM(mu.used_quantity)            AS "total_used_quantity" 
FROM material_usage AS mu
         INNER JOIN material AS m ON mu.material_id = m.id
         LEFT JOIN operation AS o ON mu.operation_id = o.id
WHERE mu.deleted_at IS NULL
  AND (mu.created_at > $1)
  AND (mu.created_at < $2)
  AND ($3::bigint = 0 OR mu.operation_id = $4::bigint)
  AND ($5::bigint = 0 OR mu.material_id = $6::bigint)
  AND ($7::text = '' OR (
         o.name ILIKE '%' || $8::text || '%'
          OR mu.operation_key ILIKE '%' || $9::text || '%'
          OR m.name ILIKE '%' || $10::text || '%'
      ))
GROUP BY m.id, m.code, m.name, m.unit
ORDER BY m.name LIMIT NULLIF($11, 0)
OFFSET NULLIF($12, 0);
