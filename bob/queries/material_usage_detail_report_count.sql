-- MaterialUsageDetailReportCount
SELECT COUNT(DISTINCT a.id) AS total_attachments
FROM material_usage AS mu
         LEFT JOIN operation AS o ON mu.operation_id = o.id
         LEFT JOIN attachment AS a ON mu.attachment_id = a.id
         LEFT JOIN person AS p ON a.person_id = p.id
         LEFT JOIN material AS m ON mu.material_id = m.id
WHERE mu.deleted_at IS NULL
  AND a.id IS NOT NULL
  -- Filter theo thời gian
  AND (mu.created_at > $1/* from:time.Time */)
  AND (mu.created_at < $2/* to:time.Time */)
  -- Filter theo person
  AND ($3::bigint/* person_id:int */ = 0 OR p.id = $3::bigint/* person_id:int */)
  -- Filter theo operation
  AND ($4::bigint/* operation_id:int */ = 0 OR mu.operation_id = $4::bigint/* operation_id:int */)
  -- Filter theo material
  AND ($5::bigint/* material_id:int */ = 0 OR mu.material_id = $5::bigint/* material_id:int */)
  -- Filter theo keyword
  AND ($6::text/* keyword:string */ = '' OR (
      p.full_name ILIKE '%' || $6::text/* keyword:string */ || '%'
      OR o.name ILIKE '%' || $6::text/* keyword:string */ || '%'
      OR mu.operation_key ILIKE '%' || $6::text/* keyword:string */ || '%'
      OR m.name ILIKE '%' || $6::text/* keyword:string */ || '%'
  ));