-- CashFlowReport
WITH paid_summary AS (
    SELECT
        cf.type,
        cfi.category_id,
        COALESCE(t.name, 'Unknown') AS category_name,
        SUM(cfi.amount) AS total_amount /* :int */,
        COUNT(DISTINCT cf.id) AS count /* :int */
FROM cash_flow AS cf
    JOIN cash_flow_item AS cfi ON cf.id = cfi.cash_flow_id
    LEFT JOIN term AS t ON cfi.category_id = t.id
WHERE cf.state = 'PAID'
  AND cf.deleted_at IS NULL
  AND ($1/* fromDate:time.Time */ = '1970-01-01 00:00:00'::timestamp OR cf.paid_at >= $1/* fromDate:time.Time */)
  AND ($2/* toDate:time.Time */ = '1970-01-01 00:00:00'::timestamp OR cf.paid_at <= $2/* toDate:time.Time */)
GROUP BY cf.type, cfi.category_id, t.name
    ),
    payment_methods_summary AS (
SELECT
    cf.type,
    SUM(CASE WHEN cf.type = 'income' THEN cf.cash ELSE -cf.cash END) AS total_cash /* :int */,
    SUM(CASE WHEN cf.type = 'income' THEN cf.credit_card ELSE -cf.credit_card END) AS total_credit_card /* :int */,
    SUM(CASE WHEN cf.type = 'income' THEN cf.mpos ELSE -cf.mpos END) AS total_mpos /* :int */,
    SUM(CASE WHEN cf.type = 'income' THEN cf.bank ELSE -cf.bank END) AS total_bank /* :int */,
    SUM(CASE WHEN cf.type = 'income' THEN cf.momo ELSE -cf.momo END) AS total_momo /* :int */
FROM cash_flow AS cf
WHERE cf.state = 'PAID'
  AND cf.deleted_at IS NULL
  AND ($1/* fromDate:time.Time */ = '1970-01-01 00:00:00'::timestamp OR cf.paid_at >= $1/* fromDate:time.Time */)
  AND ($2/* toDate:time.Time */ = '1970-01-01 00:00:00'::timestamp OR cf.paid_at <= $2/* toDate:time.Time */)
GROUP BY cf.type
    ),
    pending_summary AS (
SELECT
    COUNT(*) AS pending_approval_count /* :int */,
    COALESCE(SUM(cf.amount), 0) AS pending_approval_amount /* :int */,
    COALESCE(SUM(cf.cash), 0) AS pending_cash /* :int */,
    COALESCE(SUM(cf.credit_card), 0) AS pending_credit_card /* :int */,
    COALESCE(SUM(cf.mpos), 0) AS pending_mpos /* :int */,
    COALESCE(SUM(cf.bank), 0) AS pending_bank /* :int */,
    COALESCE(SUM(cf.momo), 0) AS pending_momo /* :int */
FROM cash_flow AS cf
WHERE cf.state = 'PENDING'
  AND cf.type = 'expense'
  AND cf.deleted_at IS NULL
  AND ($1/* fromDate:time.Time */ = '1970-01-01 00:00:00'::timestamp OR cf.created_at >= $1/* fromDate:time.Time */)
  AND ($2/* toDate:time.Time */ = '1970-01-01 00:00:00'::timestamp OR cf.created_at <= $2/* toDate:time.Time */)
    ),
    totals AS (
SELECT
    COALESCE(SUM(CASE WHEN type = 'income' THEN total_amount ELSE 0 END), 0) AS total_income /* :int */,
    COALESCE(SUM(CASE WHEN type = 'expense' THEN total_amount ELSE 0 END), 0) AS total_expense /* :int */
FROM paid_summary
    ),
    payment_totals AS (
SELECT
    COALESCE(SUM(total_cash), 0) AS total_cash_all /* :int */,
    COALESCE(SUM(total_credit_card), 0) AS total_credit_card_all /* :int */,
    COALESCE(SUM(total_mpos), 0) AS total_mpos_all /* :int */,
    COALESCE(SUM(total_bank), 0) AS total_bank_all /* :int */,
    COALESCE(SUM(total_momo), 0) AS total_momo_all /* :int */
FROM payment_methods_summary
    )
SELECT
    t.total_income,
    t.total_expense,
    (t.total_income - t.total_expense) AS net_amount /* :int */,
    ps.pending_approval_count,
    ps.pending_approval_amount,
    -- Payment methods totals (paid transactions)
    pt.total_cash_all,
    pt.total_credit_card_all,
    pt.total_mpos_all,
    pt.total_bank_all,
    pt.total_momo_all,
    -- Payment methods for pending transactions
    ps.pending_cash,
    ps.pending_credit_card,
    ps.pending_mpos,
    ps.pending_bank,
    ps.pending_momo
FROM totals t
         CROSS JOIN payment_totals pt
         CROSS JOIN pending_summary ps;