-- MaterialUsageDetailReport
WITH base_attachment_ids AS (
    SELECT DISTINCT a.id, MAX(mu.created_at) AS latest_created_at
    FROM material_usage AS mu
             LEFT JOIN operation AS o ON mu.operation_id = o.id
             LEFT JOIN attachment AS a ON mu.attachment_id = a.id
             LEFT JOIN person AS p ON a.person_id = p.id
             LEFT JOIN material AS m ON mu.material_id = m.id
    WHERE mu.deleted_at IS NULL
      AND a.id IS NOT NULL
      -- Filter theo thời gian
      AND (mu.created_at > $1/* from:time.Time */)
      AND (mu.created_at < $2/* to:time.Time */)
      -- Filter theo person
      AND ($3::bigint/* person_id:int */ = 0 OR p.id = $3::bigint/* person_id:int */)
      -- Filter theo operation
      AND ($4::bigint/* operation_id:int */ = 0 OR mu.operation_id = $4::bigint/* operation_id:int */)
      -- Filter theo material
      AND ($5::bigint/* material_id:int */ = 0 OR mu.material_id = $5::bigint/* material_id:int */)
      -- Filter theo keyword
      AND ($6::text/* keyword:string */ = '' OR (
          p.full_name ILIKE '%' || $6::text/* keyword:string */ || '%'
          OR o.name ILIKE '%' || $6::text/* keyword:string */ || '%'
          OR mu.operation_key ILIKE '%' || $6::text/* keyword:string */ || '%'
          OR m.name ILIKE '%' || $6::text/* keyword:string */ || '%'
      ))
    GROUP BY a.id
    ORDER BY latest_created_at DESC
    LIMIT NULLIF($7/* limit */, 0) OFFSET NULLIF($8/* offset */, 0)
    ),

    material_usage_data AS (
SELECT
    mu.id,
    mu.material_id,
    mu.operation_id,
    mu.operation_key,
    mu.attachment_id,
    mu.used_quantity,
    mu.quoted_quantity,
    mu.created_at
FROM material_usage mu
    LEFT JOIN operation AS o ON mu.operation_id = o.id
    LEFT JOIN material AS m ON mu.material_id = m.id
WHERE mu.attachment_id IN (SELECT id FROM base_attachment_ids)
  AND mu.deleted_at IS NULL
-- ÁP DỤNG LẠI CÁC FILTER CONDITIONS
  AND ($4::bigint/* operation_id:int */ = 0 OR mu.operation_id = $4::bigint/* operation_id:int */)
  AND ($5::bigint/* material_id:int */ = 0 OR mu.material_id = $5::bigint/* material_id:int */)
  AND ($6::text/* keyword:string */ = '' OR (
    o.name ILIKE '%' || $6::text/* keyword:string */ || '%'
   OR mu.operation_key ILIKE '%' || $6::text/* keyword:string */ || '%'
   OR m.name ILIKE '%' || $6::text/* keyword:string */ || '%'
    ))
    ),

    material_data AS (
SELECT
    m.id,
    m.name,
    m.unit
FROM material m
WHERE m.id IN (SELECT material_id FROM material_usage_data WHERE material_id IS NOT NULL)
    ),

    operation_data AS (
SELECT
    o.id,
    o.name
FROM operation o
WHERE o.id IN (
    SELECT operation_id
    FROM material_usage_data
    WHERE operation_id IS NOT NULL
    )
    ),

    attachment_data AS (
SELECT
    a.id,
    a.person_id
FROM attachment a
WHERE a.id IN (SELECT id FROM base_attachment_ids)
    ),

    person_data AS (
SELECT
    p.id,
    p.full_name
FROM person p
WHERE p.id IN (SELECT person_id FROM attachment_data)
    ),

    usage_details AS (
SELECT
    p.id AS person_id,
    p.full_name AS person_name,
    mud.attachment_id,
    mud.operation_id,
    COALESCE(o.name, mud.operation_key) AS operation_name,
    m.id AS material_id,
    m.name AS material_name,
    m.unit AS material_unit,
    mud.used_quantity,
    mud.quoted_quantity,
    mud.created_at
FROM material_usage_data mud
    LEFT JOIN material_data m ON mud.material_id = m.id
    LEFT JOIN operation_data o ON mud.operation_id = o.id
    LEFT JOIN attachment_data a ON mud.attachment_id = a.id
    LEFT JOIN person_data p ON a.person_id = p.id
    -- LOẠI BỎ FILTER TRÙNG LẶP VÌ ĐÃ FILTER Ở material_usage_data
    ),

    operation_level_agg AS (
SELECT
    ud.person_id,
    ud.person_name,
    ud.attachment_id,
    ud.operation_name,
    jsonb_agg(
    jsonb_build_object(
    'material_name', ud.material_name,
    'used_quantity', ud.used_quantity,
    'quoted_quantity', ud.quoted_quantity,
    'difference', (ud.used_quantity - ud.quoted_quantity),
    'unit', ud.material_unit,
    'created_at', TO_CHAR(ud.created_at, 'DD/MM/YYYY')
    )
    ) AS material_list_json
FROM usage_details ud
GROUP BY ud.person_id, ud.person_name, ud.attachment_id, ud.operation_name
    )

SELECT
    ola.person_name AS "person_name",
    ola.person_id AS "person_id",
    TO_CHAR(bai.latest_created_at, 'DD/MM/YYYY') AS "created_at",
    jsonb_agg(
            jsonb_build_object(
                    'operation_name', ola.operation_name,
                    'used_materials', ola.material_list_json
            )
    ) AS "operation_details"
FROM operation_level_agg ola
         JOIN base_attachment_ids bai ON ola.attachment_id = bai.id
GROUP BY ola.person_id, ola.person_name, ola.attachment_id, bai.latest_created_at
ORDER BY "person_name" ASC;