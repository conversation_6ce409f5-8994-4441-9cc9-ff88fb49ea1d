-- Code generated by BobGen psql v0.38.0. DO NOT EDIT.
-- This file is meant to be re-generated in place and/or deleted at any time.

-- CashFlowExpenseByCategoryDepartment
SELECT 
    cfi.category_id,
    COALESCE(cat.name, 'Unknown') AS category_name,
    COALESCE(cfi.department_id, 0) AS department_id,
    COALESCE(dept.name, 'Unknown') AS department_name,
    SUM(cfi.amount) AS amount ,
    COUNT(*) AS count 
FROM cash_flow_item AS cfi
JOIN cash_flow AS cf ON cfi.cash_flow_id = cf.id
LEFT JOIN term AS cat ON cfi.category_id = cat.id
LEFT JOIN department AS dept ON cfi.department_id = dept.id
WHERE cf.state = 'PAID' 
    AND cf.type = 'expense'
    AND cf.deleted_at IS NULL
    AND ($1 = '1970-01-01 00:00:00'::timestamp OR cf.paid_at >= $2)
    AND ($3 = '1970-01-01 00:00:00'::timestamp OR cf.paid_at <= $4)
GROUP BY cfi.category_id, cat.name, COALESCE(cfi.department_id, 0), dept.name
ORDER BY category_name, amount DESC;
