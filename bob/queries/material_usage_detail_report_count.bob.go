// Code generated by BobGen psql v0.38.0. DO NOT EDIT.
// This file is meant to be re-generated in place and/or deleted at any time.

package queries

import (
	"context"
	_ "embed"
	"io"
	"iter"
	"time"

	"github.com/stephenafamo/bob"
	"github.com/stephenafamo/bob/dialect/psql"
	"github.com/stephenafamo/bob/dialect/psql/dialect"
	"github.com/stephenafamo/bob/orm"
	"github.com/stephenafamo/scan"
)

//go:embed material_usage_detail_report_count.bob.sql
var formattedQueries_material_usage_detail_report_count string

var materialUsageDetailReportCountSQL = formattedQueries_material_usage_detail_report_count[168:970]

func MaterialUsageDetailReportCount(From time.Time, To time.Time, Arg3 int64, Arg4 int64, Arg5 int64, Arg6 string) orm.ModQuery[*dialect.SelectQuery, int64, []int64] {
	var expressionTypArgs materialUsageDetailReportCount

	expressionTypArgs.From = psql.Arg(From)
	expressionTypArgs.To = psql.Arg(To)
	expressionTypArgs.Arg3 = psql.Arg(Arg3)
	expressionTypArgs.Arg4 = psql.Arg(Arg4)
	expressionTypArgs.Arg5 = psql.Arg(Arg5)
	expressionTypArgs.Arg6 = psql.Arg(Arg6)

	return orm.ModQuery[*dialect.SelectQuery, int64, []int64]{
		Query: orm.Query[orm.ModExpression[*dialect.SelectQuery], int64, []int64]{
			ExecQuery: orm.ExecQuery[orm.ModExpression[*dialect.SelectQuery]]{
				BaseQuery: bob.BaseQuery[orm.ModExpression[*dialect.SelectQuery]]{
					Expression: expressionTypArgs,
					Dialect:    dialect.Dialect,
					QueryType:  bob.QueryTypeSelect,
				},
			}, Scanner: scan.ColumnMapper[int64]("total_attachments"),
		},
	}
}

type MaterialUsageDetailReportCountRow struct {
	TotalAttachments int64 `db:"total_attachments"`
}

type materialUsageDetailReportCount struct {
	From bob.Expression
	To   bob.Expression
	Arg3 bob.Expression
	Arg4 bob.Expression
	Arg5 bob.Expression
	Arg6 bob.Expression
}

func (o materialUsageDetailReportCount) args() iter.Seq[orm.ArgWithPosition] {
	return func(yield func(arg orm.ArgWithPosition) bool) {
		if !yield(orm.ArgWithPosition{
			Name:       "from",
			Start:      384,
			Stop:       386,
			Expression: o.From,
		}) {
			return
		}

		if !yield(orm.ArgWithPosition{
			Name:       "to",
			Start:      411,
			Stop:       413,
			Expression: o.To,
		}) {
			return
		}

		if !yield(orm.ArgWithPosition{
			Name:       "arg3",
			Start:      424,
			Stop:       426,
			Expression: o.Arg3,
		}) {
			return
		}

		if !yield(orm.ArgWithPosition{
			Name:       "arg3",
			Start:      449,
			Stop:       451,
			Expression: o.Arg3,
		}) {
			return
		}

		if !yield(orm.ArgWithPosition{
			Name:       "arg4",
			Start:      470,
			Stop:       472,
			Expression: o.Arg4,
		}) {
			return
		}

		if !yield(orm.ArgWithPosition{
			Name:       "arg4",
			Start:      506,
			Stop:       508,
			Expression: o.Arg4,
		}) {
			return
		}

		if !yield(orm.ArgWithPosition{
			Name:       "arg5",
			Start:      527,
			Stop:       529,
			Expression: o.Arg5,
		}) {
			return
		}

		if !yield(orm.ArgWithPosition{
			Name:       "arg5",
			Start:      562,
			Stop:       564,
			Expression: o.Arg5,
		}) {
			return
		}

		if !yield(orm.ArgWithPosition{
			Name:       "arg6",
			Start:      583,
			Stop:       585,
			Expression: o.Arg6,
		}) {
			return
		}

		if !yield(orm.ArgWithPosition{
			Name:       "arg6",
			Start:      633,
			Stop:       636,
			Expression: o.Arg6,
		}) {
			return
		}

		if !yield(orm.ArgWithPosition{
			Name:       "arg6",
			Start:      679,
			Stop:       682,
			Expression: o.Arg6,
		}) {
			return
		}

		if !yield(orm.ArgWithPosition{
			Name:       "arg6",
			Start:      735,
			Stop:       738,
			Expression: o.Arg6,
		}) {
			return
		}

		if !yield(orm.ArgWithPosition{
			Name:       "arg6",
			Start:      781,
			Stop:       784,
			Expression: o.Arg6,
		}) {
			return
		}
	}
}

func (o materialUsageDetailReportCount) raw(from, to int) string {
	return materialUsageDetailReportCountSQL[from:to]
}

func (o materialUsageDetailReportCount) expr(from, to int) bob.Expression {
	return orm.ArgsToExpression(materialUsageDetailReportCountSQL, from, to, o.args())
}

func (o materialUsageDetailReportCount) Apply(q *dialect.SelectQuery) {
	q.AppendSelect(o.expr(7, 48))
	q.SetTable(o.expr(54, 307))
	q.AppendWhere(o.expr(314, 802))
}

func (o materialUsageDetailReportCount) WriteSQL(ctx context.Context, w io.Writer, d bob.Dialect, start int) ([]any, error) {
	return o.expr(0, len(materialUsageDetailReportCountSQL)).WriteSQL(ctx, w, d, start)
}
