// Code generated by BobGen psql v0.38.0. DO NOT EDIT.
// This file is meant to be re-generated in place and/or deleted at any time.

package queries

import (
	"context"
	_ "embed"
	"io"
	"iter"
	"time"

	"github.com/stephenafamo/bob"
	"github.com/stephenafamo/bob/dialect/psql"
	"github.com/stephenafamo/bob/dialect/psql/dialect"
	"github.com/stephenafamo/bob/orm"
	"github.com/stephenafamo/scan"
)

//go:embed cash_flow_income_by_category_department.bob.sql
var formattedQueries_cash_flow_income_by_category_department string

var cashFlowIncomeByCategoryDepartmentSQL = formattedQueries_cash_flow_income_by_category_department[172:933]

func CashFlowIncomeByCategoryDepartment(FromDate time.Time, ToDate time.Time) orm.ModQuery[*dialect.SelectQuery, CashFlowIncomeByCategoryDepartmentRow, []CashFlowIncomeByCategoryDepartmentRow] {
	var expressionTypArgs cashFlowIncomeByCategoryDepartment

	expressionTypArgs.FromDate = psql.Arg(FromDate)
	expressionTypArgs.ToDate = psql.Arg(ToDate)

	return orm.ModQuery[*dialect.SelectQuery, CashFlowIncomeByCategoryDepartmentRow, []CashFlowIncomeByCategoryDepartmentRow]{
		Query: orm.Query[orm.ModExpression[*dialect.SelectQuery], CashFlowIncomeByCategoryDepartmentRow, []CashFlowIncomeByCategoryDepartmentRow]{
			ExecQuery: orm.ExecQuery[orm.ModExpression[*dialect.SelectQuery]]{
				BaseQuery: bob.BaseQuery[orm.ModExpression[*dialect.SelectQuery]]{
					Expression: expressionTypArgs,
					Dialect:    dialect.Dialect,
					QueryType:  bob.QueryTypeSelect,
				},
			}, Scanner: scan.StructMapper[CashFlowIncomeByCategoryDepartmentRow](),
		},
	}
}

type CashFlowIncomeByCategoryDepartmentRow struct {
	CategoryID     int64  `db:"category_id"`
	CategoryName   string `db:"category_name"`
	DepartmentID   int64  `db:"department_id"`
	DepartmentName string `db:"department_name"`
	Amount         int    `db:"amount"`
	Count          int64  `db:"count"`
}

type cashFlowIncomeByCategoryDepartment struct {
	FromDate bob.Expression
	ToDate   bob.Expression
}

func (o cashFlowIncomeByCategoryDepartment) args() iter.Seq[orm.ArgWithPosition] {
	return func(yield func(arg orm.ArgWithPosition) bool) {
		if !yield(orm.ArgWithPosition{
			Name:       "fromDate",
			Start:      521,
			Stop:       523,
			Expression: o.FromDate,
		}) {
			return
		}

		if !yield(orm.ArgWithPosition{
			Name:       "fromDate",
			Start:      576,
			Stop:       578,
			Expression: o.FromDate,
		}) {
			return
		}

		if !yield(orm.ArgWithPosition{
			Name:       "toDate",
			Start:      589,
			Stop:       591,
			Expression: o.ToDate,
		}) {
			return
		}

		if !yield(orm.ArgWithPosition{
			Name:       "toDate",
			Start:      644,
			Stop:       646,
			Expression: o.ToDate,
		}) {
			return
		}
	}
}

func (o cashFlowIncomeByCategoryDepartment) raw(from, to int) string {
	return cashFlowIncomeByCategoryDepartmentSQL[from:to]
}

func (o cashFlowIncomeByCategoryDepartment) expr(from, to int) bob.Expression {
	return orm.ArgsToExpression(cashFlowIncomeByCategoryDepartmentSQL, from, to, o.args())
}

func (o cashFlowIncomeByCategoryDepartment) Apply(q *dialect.SelectQuery) {
	q.AppendSelect(o.expr(12, 230))
	q.SetTable(o.expr(249, 429))
	q.AppendWhere(o.expr(436, 647))
	q.AppendGroup(o.expr(657, 725))
	q.AppendOrder(o.expr(735, 761))
}

func (o cashFlowIncomeByCategoryDepartment) WriteSQL(ctx context.Context, w io.Writer, d bob.Dialect, start int) ([]any, error) {
	return o.expr(0, len(cashFlowIncomeByCategoryDepartmentSQL)).WriteSQL(ctx, w, d, start)
}
