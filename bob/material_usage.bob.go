// Code generated by BobGen psql v0.38.0. DO NOT EDIT.
// This file is meant to be re-generated in place and/or deleted at any time.

package bob

import (
	"context"
	"database/sql"
	"fmt"
	"io"
	"time"

	"github.com/stephenafamo/bob"
	"github.com/stephenafamo/bob/dialect/psql"
	"github.com/stephenafamo/bob/dialect/psql/dialect"
	"github.com/stephenafamo/bob/dialect/psql/dm"
	"github.com/stephenafamo/bob/dialect/psql/sm"
	"github.com/stephenafamo/bob/dialect/psql/um"
	"github.com/stephenafamo/bob/expr"
	"github.com/stephenafamo/bob/mods"
	"github.com/stephenafamo/bob/orm"
	"github.com/stephenafamo/bob/types/pgtypes"
)

// MaterialUsage is an object representing the database table.
type MaterialUsage struct {
	ID             int64               `db:"id,pk" `
	DeletedAt      sql.Null[time.Time] `db:"deleted_at" `
	Status         int16               `db:"status" `
	Version        int64               `db:"version" `
	CreatedAt      time.Time           `db:"created_at" `
	UpdatedAt      time.Time           `db:"updated_at" `
	OperationKey   sql.Null[string]    `db:"operation_key" `
	Kind           string              `db:"kind" `
	QuotedQuantity float64             `db:"quoted_quantity" `
	UsedQuantity   float64             `db:"used_quantity" `
	Note           sql.Null[string]    `db:"note" `
	AttachmentID   int64               `db:"attachment_id" `
	MaterialID     int64               `db:"material_id" `
	OperationID    sql.Null[int64]     `db:"operation_id" `
	UserID         sql.Null[int64]     `db:"user_id" `

	R materialUsageR `db:"-" `
}

// MaterialUsageSlice is an alias for a slice of pointers to MaterialUsage.
// This should almost always be used instead of []*MaterialUsage.
type MaterialUsageSlice []*MaterialUsage

// MaterialUsages contains methods to work with the material_usage table
var MaterialUsages = psql.NewTablex[*MaterialUsage, MaterialUsageSlice, *MaterialUsageSetter]("", "material_usage")

// MaterialUsagesQuery is a query on the material_usage table
type MaterialUsagesQuery = *psql.ViewQuery[*MaterialUsage, MaterialUsageSlice]

// materialUsageR is where relationships are stored.
type materialUsageR struct {
	Material  *Material  // material_usage.material_usage_material_material
	Operation *Operation // material_usage.material_usage_operation_operation
}

type materialUsageColumnNames struct {
	ID             string
	DeletedAt      string
	Status         string
	Version        string
	CreatedAt      string
	UpdatedAt      string
	OperationKey   string
	Kind           string
	QuotedQuantity string
	UsedQuantity   string
	Note           string
	AttachmentID   string
	MaterialID     string
	OperationID    string
	UserID         string
}

var MaterialUsageColumns = buildMaterialUsageColumns("material_usage")

type materialUsageColumns struct {
	tableAlias     string
	ID             psql.Expression
	DeletedAt      psql.Expression
	Status         psql.Expression
	Version        psql.Expression
	CreatedAt      psql.Expression
	UpdatedAt      psql.Expression
	OperationKey   psql.Expression
	Kind           psql.Expression
	QuotedQuantity psql.Expression
	UsedQuantity   psql.Expression
	Note           psql.Expression
	AttachmentID   psql.Expression
	MaterialID     psql.Expression
	OperationID    psql.Expression
	UserID         psql.Expression
}

func (c materialUsageColumns) Alias() string {
	return c.tableAlias
}

func (materialUsageColumns) AliasedAs(alias string) materialUsageColumns {
	return buildMaterialUsageColumns(alias)
}

func buildMaterialUsageColumns(alias string) materialUsageColumns {
	return materialUsageColumns{
		tableAlias:     alias,
		ID:             psql.Quote(alias, "id"),
		DeletedAt:      psql.Quote(alias, "deleted_at"),
		Status:         psql.Quote(alias, "status"),
		Version:        psql.Quote(alias, "version"),
		CreatedAt:      psql.Quote(alias, "created_at"),
		UpdatedAt:      psql.Quote(alias, "updated_at"),
		OperationKey:   psql.Quote(alias, "operation_key"),
		Kind:           psql.Quote(alias, "kind"),
		QuotedQuantity: psql.Quote(alias, "quoted_quantity"),
		UsedQuantity:   psql.Quote(alias, "used_quantity"),
		Note:           psql.Quote(alias, "note"),
		AttachmentID:   psql.Quote(alias, "attachment_id"),
		MaterialID:     psql.Quote(alias, "material_id"),
		OperationID:    psql.Quote(alias, "operation_id"),
		UserID:         psql.Quote(alias, "user_id"),
	}
}

type materialUsageWhere[Q psql.Filterable] struct {
	ID             psql.WhereMod[Q, int64]
	DeletedAt      psql.WhereNullMod[Q, time.Time]
	Status         psql.WhereMod[Q, int16]
	Version        psql.WhereMod[Q, int64]
	CreatedAt      psql.WhereMod[Q, time.Time]
	UpdatedAt      psql.WhereMod[Q, time.Time]
	OperationKey   psql.WhereNullMod[Q, string]
	Kind           psql.WhereMod[Q, string]
	QuotedQuantity psql.WhereMod[Q, float64]
	UsedQuantity   psql.WhereMod[Q, float64]
	Note           psql.WhereNullMod[Q, string]
	AttachmentID   psql.WhereMod[Q, int64]
	MaterialID     psql.WhereMod[Q, int64]
	OperationID    psql.WhereNullMod[Q, int64]
	UserID         psql.WhereNullMod[Q, int64]
}

func (materialUsageWhere[Q]) AliasedAs(alias string) materialUsageWhere[Q] {
	return buildMaterialUsageWhere[Q](buildMaterialUsageColumns(alias))
}

func buildMaterialUsageWhere[Q psql.Filterable](cols materialUsageColumns) materialUsageWhere[Q] {
	return materialUsageWhere[Q]{
		ID:             psql.Where[Q, int64](cols.ID),
		DeletedAt:      psql.WhereNull[Q, time.Time](cols.DeletedAt),
		Status:         psql.Where[Q, int16](cols.Status),
		Version:        psql.Where[Q, int64](cols.Version),
		CreatedAt:      psql.Where[Q, time.Time](cols.CreatedAt),
		UpdatedAt:      psql.Where[Q, time.Time](cols.UpdatedAt),
		OperationKey:   psql.WhereNull[Q, string](cols.OperationKey),
		Kind:           psql.Where[Q, string](cols.Kind),
		QuotedQuantity: psql.Where[Q, float64](cols.QuotedQuantity),
		UsedQuantity:   psql.Where[Q, float64](cols.UsedQuantity),
		Note:           psql.WhereNull[Q, string](cols.Note),
		AttachmentID:   psql.Where[Q, int64](cols.AttachmentID),
		MaterialID:     psql.Where[Q, int64](cols.MaterialID),
		OperationID:    psql.WhereNull[Q, int64](cols.OperationID),
		UserID:         psql.WhereNull[Q, int64](cols.UserID),
	}
}

var MaterialUsageErrors = &materialUsageErrors{
	ErrUniqueMaterialUsagePkey: &UniqueConstraintError{
		schema:  "",
		table:   "material_usage",
		columns: []string{"id"},
		s:       "material_usage_pkey",
	},
}

type materialUsageErrors struct {
	ErrUniqueMaterialUsagePkey *UniqueConstraintError
}

// MaterialUsageSetter is used for insert/upsert/update operations
// All values are optional, and do not have to be set
// Generated columns are not included
type MaterialUsageSetter struct {
	ID             *int64               `db:"id,pk" `
	DeletedAt      *sql.Null[time.Time] `db:"deleted_at" `
	Status         *int16               `db:"status" `
	Version        *int64               `db:"version" `
	CreatedAt      *time.Time           `db:"created_at" `
	UpdatedAt      *time.Time           `db:"updated_at" `
	OperationKey   *sql.Null[string]    `db:"operation_key" `
	Kind           *string              `db:"kind" `
	QuotedQuantity *float64             `db:"quoted_quantity" `
	UsedQuantity   *float64             `db:"used_quantity" `
	Note           *sql.Null[string]    `db:"note" `
	AttachmentID   *int64               `db:"attachment_id" `
	MaterialID     *int64               `db:"material_id" `
	OperationID    *sql.Null[int64]     `db:"operation_id" `
	UserID         *sql.Null[int64]     `db:"user_id" `
}

func (s MaterialUsageSetter) SetColumns() []string {
	vals := make([]string, 0, 15)
	if s.ID != nil {
		vals = append(vals, "id")
	}

	if s.DeletedAt != nil {
		vals = append(vals, "deleted_at")
	}

	if s.Status != nil {
		vals = append(vals, "status")
	}

	if s.Version != nil {
		vals = append(vals, "version")
	}

	if s.CreatedAt != nil {
		vals = append(vals, "created_at")
	}

	if s.UpdatedAt != nil {
		vals = append(vals, "updated_at")
	}

	if s.OperationKey != nil {
		vals = append(vals, "operation_key")
	}

	if s.Kind != nil {
		vals = append(vals, "kind")
	}

	if s.QuotedQuantity != nil {
		vals = append(vals, "quoted_quantity")
	}

	if s.UsedQuantity != nil {
		vals = append(vals, "used_quantity")
	}

	if s.Note != nil {
		vals = append(vals, "note")
	}

	if s.AttachmentID != nil {
		vals = append(vals, "attachment_id")
	}

	if s.MaterialID != nil {
		vals = append(vals, "material_id")
	}

	if s.OperationID != nil {
		vals = append(vals, "operation_id")
	}

	if s.UserID != nil {
		vals = append(vals, "user_id")
	}

	return vals
}

func (s MaterialUsageSetter) Overwrite(t *MaterialUsage) {
	if s.ID != nil {
		t.ID = *s.ID
	}
	if s.DeletedAt != nil {
		t.DeletedAt = *s.DeletedAt
	}
	if s.Status != nil {
		t.Status = *s.Status
	}
	if s.Version != nil {
		t.Version = *s.Version
	}
	if s.CreatedAt != nil {
		t.CreatedAt = *s.CreatedAt
	}
	if s.UpdatedAt != nil {
		t.UpdatedAt = *s.UpdatedAt
	}
	if s.OperationKey != nil {
		t.OperationKey = *s.OperationKey
	}
	if s.Kind != nil {
		t.Kind = *s.Kind
	}
	if s.QuotedQuantity != nil {
		t.QuotedQuantity = *s.QuotedQuantity
	}
	if s.UsedQuantity != nil {
		t.UsedQuantity = *s.UsedQuantity
	}
	if s.Note != nil {
		t.Note = *s.Note
	}
	if s.AttachmentID != nil {
		t.AttachmentID = *s.AttachmentID
	}
	if s.MaterialID != nil {
		t.MaterialID = *s.MaterialID
	}
	if s.OperationID != nil {
		t.OperationID = *s.OperationID
	}
	if s.UserID != nil {
		t.UserID = *s.UserID
	}
}

func (s *MaterialUsageSetter) Apply(q *dialect.InsertQuery) {
	q.AppendHooks(func(ctx context.Context, exec bob.Executor) (context.Context, error) {
		return MaterialUsages.BeforeInsertHooks.RunHooks(ctx, exec, s)
	})

	q.AppendValues(bob.ExpressionFunc(func(ctx context.Context, w io.Writer, d bob.Dialect, start int) ([]any, error) {
		vals := make([]bob.Expression, 15)
		if s.ID != nil {
			vals[0] = psql.Arg(*s.ID)
		} else {
			vals[0] = psql.Raw("DEFAULT")
		}

		if s.DeletedAt != nil {
			vals[1] = psql.Arg(*s.DeletedAt)
		} else {
			vals[1] = psql.Raw("DEFAULT")
		}

		if s.Status != nil {
			vals[2] = psql.Arg(*s.Status)
		} else {
			vals[2] = psql.Raw("DEFAULT")
		}

		if s.Version != nil {
			vals[3] = psql.Arg(*s.Version)
		} else {
			vals[3] = psql.Raw("DEFAULT")
		}

		if s.CreatedAt != nil {
			vals[4] = psql.Arg(*s.CreatedAt)
		} else {
			vals[4] = psql.Raw("DEFAULT")
		}

		if s.UpdatedAt != nil {
			vals[5] = psql.Arg(*s.UpdatedAt)
		} else {
			vals[5] = psql.Raw("DEFAULT")
		}

		if s.OperationKey != nil {
			vals[6] = psql.Arg(*s.OperationKey)
		} else {
			vals[6] = psql.Raw("DEFAULT")
		}

		if s.Kind != nil {
			vals[7] = psql.Arg(*s.Kind)
		} else {
			vals[7] = psql.Raw("DEFAULT")
		}

		if s.QuotedQuantity != nil {
			vals[8] = psql.Arg(*s.QuotedQuantity)
		} else {
			vals[8] = psql.Raw("DEFAULT")
		}

		if s.UsedQuantity != nil {
			vals[9] = psql.Arg(*s.UsedQuantity)
		} else {
			vals[9] = psql.Raw("DEFAULT")
		}

		if s.Note != nil {
			vals[10] = psql.Arg(*s.Note)
		} else {
			vals[10] = psql.Raw("DEFAULT")
		}

		if s.AttachmentID != nil {
			vals[11] = psql.Arg(*s.AttachmentID)
		} else {
			vals[11] = psql.Raw("DEFAULT")
		}

		if s.MaterialID != nil {
			vals[12] = psql.Arg(*s.MaterialID)
		} else {
			vals[12] = psql.Raw("DEFAULT")
		}

		if s.OperationID != nil {
			vals[13] = psql.Arg(*s.OperationID)
		} else {
			vals[13] = psql.Raw("DEFAULT")
		}

		if s.UserID != nil {
			vals[14] = psql.Arg(*s.UserID)
		} else {
			vals[14] = psql.Raw("DEFAULT")
		}

		return bob.ExpressSlice(ctx, w, d, start, vals, "", ", ", "")
	}))
}

func (s MaterialUsageSetter) UpdateMod() bob.Mod[*dialect.UpdateQuery] {
	return um.Set(s.Expressions()...)
}

func (s MaterialUsageSetter) Expressions(prefix ...string) []bob.Expression {
	exprs := make([]bob.Expression, 0, 15)

	if s.ID != nil {
		exprs = append(exprs, expr.Join{Sep: " = ", Exprs: []bob.Expression{
			psql.Quote(append(prefix, "id")...),
			psql.Arg(s.ID),
		}})
	}

	if s.DeletedAt != nil {
		exprs = append(exprs, expr.Join{Sep: " = ", Exprs: []bob.Expression{
			psql.Quote(append(prefix, "deleted_at")...),
			psql.Arg(s.DeletedAt),
		}})
	}

	if s.Status != nil {
		exprs = append(exprs, expr.Join{Sep: " = ", Exprs: []bob.Expression{
			psql.Quote(append(prefix, "status")...),
			psql.Arg(s.Status),
		}})
	}

	if s.Version != nil {
		exprs = append(exprs, expr.Join{Sep: " = ", Exprs: []bob.Expression{
			psql.Quote(append(prefix, "version")...),
			psql.Arg(s.Version),
		}})
	}

	if s.CreatedAt != nil {
		exprs = append(exprs, expr.Join{Sep: " = ", Exprs: []bob.Expression{
			psql.Quote(append(prefix, "created_at")...),
			psql.Arg(s.CreatedAt),
		}})
	}

	if s.UpdatedAt != nil {
		exprs = append(exprs, expr.Join{Sep: " = ", Exprs: []bob.Expression{
			psql.Quote(append(prefix, "updated_at")...),
			psql.Arg(s.UpdatedAt),
		}})
	}

	if s.OperationKey != nil {
		exprs = append(exprs, expr.Join{Sep: " = ", Exprs: []bob.Expression{
			psql.Quote(append(prefix, "operation_key")...),
			psql.Arg(s.OperationKey),
		}})
	}

	if s.Kind != nil {
		exprs = append(exprs, expr.Join{Sep: " = ", Exprs: []bob.Expression{
			psql.Quote(append(prefix, "kind")...),
			psql.Arg(s.Kind),
		}})
	}

	if s.QuotedQuantity != nil {
		exprs = append(exprs, expr.Join{Sep: " = ", Exprs: []bob.Expression{
			psql.Quote(append(prefix, "quoted_quantity")...),
			psql.Arg(s.QuotedQuantity),
		}})
	}

	if s.UsedQuantity != nil {
		exprs = append(exprs, expr.Join{Sep: " = ", Exprs: []bob.Expression{
			psql.Quote(append(prefix, "used_quantity")...),
			psql.Arg(s.UsedQuantity),
		}})
	}

	if s.Note != nil {
		exprs = append(exprs, expr.Join{Sep: " = ", Exprs: []bob.Expression{
			psql.Quote(append(prefix, "note")...),
			psql.Arg(s.Note),
		}})
	}

	if s.AttachmentID != nil {
		exprs = append(exprs, expr.Join{Sep: " = ", Exprs: []bob.Expression{
			psql.Quote(append(prefix, "attachment_id")...),
			psql.Arg(s.AttachmentID),
		}})
	}

	if s.MaterialID != nil {
		exprs = append(exprs, expr.Join{Sep: " = ", Exprs: []bob.Expression{
			psql.Quote(append(prefix, "material_id")...),
			psql.Arg(s.MaterialID),
		}})
	}

	if s.OperationID != nil {
		exprs = append(exprs, expr.Join{Sep: " = ", Exprs: []bob.Expression{
			psql.Quote(append(prefix, "operation_id")...),
			psql.Arg(s.OperationID),
		}})
	}

	if s.UserID != nil {
		exprs = append(exprs, expr.Join{Sep: " = ", Exprs: []bob.Expression{
			psql.Quote(append(prefix, "user_id")...),
			psql.Arg(s.UserID),
		}})
	}

	return exprs
}

// FindMaterialUsage retrieves a single record by primary key
// If cols is empty Find will return all columns.
func FindMaterialUsage(ctx context.Context, exec bob.Executor, IDPK int64, cols ...string) (*MaterialUsage, error) {
	if len(cols) == 0 {
		return MaterialUsages.Query(
			SelectWhere.MaterialUsages.ID.EQ(IDPK),
		).One(ctx, exec)
	}

	return MaterialUsages.Query(
		SelectWhere.MaterialUsages.ID.EQ(IDPK),
		sm.Columns(MaterialUsages.Columns().Only(cols...)),
	).One(ctx, exec)
}

// MaterialUsageExists checks the presence of a single record by primary key
func MaterialUsageExists(ctx context.Context, exec bob.Executor, IDPK int64) (bool, error) {
	return MaterialUsages.Query(
		SelectWhere.MaterialUsages.ID.EQ(IDPK),
	).Exists(ctx, exec)
}

// AfterQueryHook is called after MaterialUsage is retrieved from the database
func (o *MaterialUsage) AfterQueryHook(ctx context.Context, exec bob.Executor, queryType bob.QueryType) error {
	var err error

	switch queryType {
	case bob.QueryTypeSelect:
		ctx, err = MaterialUsages.AfterSelectHooks.RunHooks(ctx, exec, MaterialUsageSlice{o})
	case bob.QueryTypeInsert:
		ctx, err = MaterialUsages.AfterInsertHooks.RunHooks(ctx, exec, MaterialUsageSlice{o})
	case bob.QueryTypeUpdate:
		ctx, err = MaterialUsages.AfterUpdateHooks.RunHooks(ctx, exec, MaterialUsageSlice{o})
	case bob.QueryTypeDelete:
		ctx, err = MaterialUsages.AfterDeleteHooks.RunHooks(ctx, exec, MaterialUsageSlice{o})
	}

	return err
}

// primaryKeyVals returns the primary key values of the MaterialUsage
func (o *MaterialUsage) primaryKeyVals() bob.Expression {
	return psql.Arg(o.ID)
}

func (o *MaterialUsage) pkEQ() dialect.Expression {
	return psql.Quote("material_usage", "id").EQ(bob.ExpressionFunc(func(ctx context.Context, w io.Writer, d bob.Dialect, start int) ([]any, error) {
		return o.primaryKeyVals().WriteSQL(ctx, w, d, start)
	}))
}

// Update uses an executor to update the MaterialUsage
func (o *MaterialUsage) Update(ctx context.Context, exec bob.Executor, s *MaterialUsageSetter) error {
	v, err := MaterialUsages.Update(s.UpdateMod(), um.Where(o.pkEQ())).One(ctx, exec)
	if err != nil {
		return err
	}

	o.R = v.R
	*o = *v

	return nil
}

// Delete deletes a single MaterialUsage record with an executor
func (o *MaterialUsage) Delete(ctx context.Context, exec bob.Executor) error {
	_, err := MaterialUsages.Delete(dm.Where(o.pkEQ())).Exec(ctx, exec)
	return err
}

// Reload refreshes the MaterialUsage using the executor
func (o *MaterialUsage) Reload(ctx context.Context, exec bob.Executor) error {
	o2, err := MaterialUsages.Query(
		SelectWhere.MaterialUsages.ID.EQ(o.ID),
	).One(ctx, exec)
	if err != nil {
		return err
	}
	o2.R = o.R
	*o = *o2

	return nil
}

// AfterQueryHook is called after MaterialUsageSlice is retrieved from the database
func (o MaterialUsageSlice) AfterQueryHook(ctx context.Context, exec bob.Executor, queryType bob.QueryType) error {
	var err error

	switch queryType {
	case bob.QueryTypeSelect:
		ctx, err = MaterialUsages.AfterSelectHooks.RunHooks(ctx, exec, o)
	case bob.QueryTypeInsert:
		ctx, err = MaterialUsages.AfterInsertHooks.RunHooks(ctx, exec, o)
	case bob.QueryTypeUpdate:
		ctx, err = MaterialUsages.AfterUpdateHooks.RunHooks(ctx, exec, o)
	case bob.QueryTypeDelete:
		ctx, err = MaterialUsages.AfterDeleteHooks.RunHooks(ctx, exec, o)
	}

	return err
}

func (o MaterialUsageSlice) pkIN() dialect.Expression {
	if len(o) == 0 {
		return psql.Raw("NULL")
	}

	return psql.Quote("material_usage", "id").In(bob.ExpressionFunc(func(ctx context.Context, w io.Writer, d bob.Dialect, start int) ([]any, error) {
		pkPairs := make([]bob.Expression, len(o))
		for i, row := range o {
			pkPairs[i] = row.primaryKeyVals()
		}
		return bob.ExpressSlice(ctx, w, d, start, pkPairs, "", ", ", "")
	}))
}

// copyMatchingRows finds models in the given slice that have the same primary key
// then it first copies the existing relationships from the old model to the new model
// and then replaces the old model in the slice with the new model
func (o MaterialUsageSlice) copyMatchingRows(from ...*MaterialUsage) {
	for i, old := range o {
		for _, new := range from {
			if new.ID != old.ID {
				continue
			}
			new.R = old.R
			o[i] = new
			break
		}
	}
}

// UpdateMod modifies an update query with "WHERE primary_key IN (o...)"
func (o MaterialUsageSlice) UpdateMod() bob.Mod[*dialect.UpdateQuery] {
	return bob.ModFunc[*dialect.UpdateQuery](func(q *dialect.UpdateQuery) {
		q.AppendHooks(func(ctx context.Context, exec bob.Executor) (context.Context, error) {
			return MaterialUsages.BeforeUpdateHooks.RunHooks(ctx, exec, o)
		})

		q.AppendLoader(bob.LoaderFunc(func(ctx context.Context, exec bob.Executor, retrieved any) error {
			var err error
			switch retrieved := retrieved.(type) {
			case *MaterialUsage:
				o.copyMatchingRows(retrieved)
			case []*MaterialUsage:
				o.copyMatchingRows(retrieved...)
			case MaterialUsageSlice:
				o.copyMatchingRows(retrieved...)
			default:
				// If the retrieved value is not a MaterialUsage or a slice of MaterialUsage
				// then run the AfterUpdateHooks on the slice
				_, err = MaterialUsages.AfterUpdateHooks.RunHooks(ctx, exec, o)
			}

			return err
		}))

		q.AppendWhere(o.pkIN())
	})
}

// DeleteMod modifies an delete query with "WHERE primary_key IN (o...)"
func (o MaterialUsageSlice) DeleteMod() bob.Mod[*dialect.DeleteQuery] {
	return bob.ModFunc[*dialect.DeleteQuery](func(q *dialect.DeleteQuery) {
		q.AppendHooks(func(ctx context.Context, exec bob.Executor) (context.Context, error) {
			return MaterialUsages.BeforeDeleteHooks.RunHooks(ctx, exec, o)
		})

		q.AppendLoader(bob.LoaderFunc(func(ctx context.Context, exec bob.Executor, retrieved any) error {
			var err error
			switch retrieved := retrieved.(type) {
			case *MaterialUsage:
				o.copyMatchingRows(retrieved)
			case []*MaterialUsage:
				o.copyMatchingRows(retrieved...)
			case MaterialUsageSlice:
				o.copyMatchingRows(retrieved...)
			default:
				// If the retrieved value is not a MaterialUsage or a slice of MaterialUsage
				// then run the AfterDeleteHooks on the slice
				_, err = MaterialUsages.AfterDeleteHooks.RunHooks(ctx, exec, o)
			}

			return err
		}))

		q.AppendWhere(o.pkIN())
	})
}

func (o MaterialUsageSlice) UpdateAll(ctx context.Context, exec bob.Executor, vals MaterialUsageSetter) error {
	if len(o) == 0 {
		return nil
	}

	_, err := MaterialUsages.Update(vals.UpdateMod(), o.UpdateMod()).All(ctx, exec)
	return err
}

func (o MaterialUsageSlice) DeleteAll(ctx context.Context, exec bob.Executor) error {
	if len(o) == 0 {
		return nil
	}

	_, err := MaterialUsages.Delete(o.DeleteMod()).Exec(ctx, exec)
	return err
}

func (o MaterialUsageSlice) ReloadAll(ctx context.Context, exec bob.Executor) error {
	if len(o) == 0 {
		return nil
	}

	o2, err := MaterialUsages.Query(sm.Where(o.pkIN())).All(ctx, exec)
	if err != nil {
		return err
	}

	o.copyMatchingRows(o2...)

	return nil
}

type materialUsageJoins[Q dialect.Joinable] struct {
	typ       string
	Material  modAs[Q, materialColumns]
	Operation modAs[Q, operationColumns]
}

func (j materialUsageJoins[Q]) aliasedAs(alias string) materialUsageJoins[Q] {
	return buildMaterialUsageJoins[Q](buildMaterialUsageColumns(alias), j.typ)
}

func buildMaterialUsageJoins[Q dialect.Joinable](cols materialUsageColumns, typ string) materialUsageJoins[Q] {
	return materialUsageJoins[Q]{
		typ: typ,
		Material: modAs[Q, materialColumns]{
			c: MaterialColumns,
			f: func(to materialColumns) bob.Mod[Q] {
				mods := make(mods.QueryMods[Q], 0, 1)

				{
					mods = append(mods, dialect.Join[Q](typ, Materials.Name().As(to.Alias())).On(
						to.ID.EQ(cols.MaterialID),
					))
				}

				return mods
			},
		},
		Operation: modAs[Q, operationColumns]{
			c: OperationColumns,
			f: func(to operationColumns) bob.Mod[Q] {
				mods := make(mods.QueryMods[Q], 0, 1)

				{
					mods = append(mods, dialect.Join[Q](typ, Operations.Name().As(to.Alias())).On(
						to.ID.EQ(cols.OperationID),
					))
				}

				return mods
			},
		},
	}
}

// Material starts a query for related objects on material
func (o *MaterialUsage) Material(mods ...bob.Mod[*dialect.SelectQuery]) MaterialsQuery {
	return Materials.Query(append(mods,
		sm.Where(MaterialColumns.ID.EQ(psql.Arg(o.MaterialID))),
	)...)
}

func (os MaterialUsageSlice) Material(mods ...bob.Mod[*dialect.SelectQuery]) MaterialsQuery {
	pkMaterialID := make(pgtypes.Array[int64], len(os))
	for i, o := range os {
		pkMaterialID[i] = o.MaterialID
	}
	PKArgExpr := psql.Select(sm.Columns(
		psql.F("unnest", psql.Cast(psql.Arg(pkMaterialID), "bigint[]")),
	))

	return Materials.Query(append(mods,
		sm.Where(psql.Group(MaterialColumns.ID).OP("IN", PKArgExpr)),
	)...)
}

// Operation starts a query for related objects on operation
func (o *MaterialUsage) Operation(mods ...bob.Mod[*dialect.SelectQuery]) OperationsQuery {
	return Operations.Query(append(mods,
		sm.Where(OperationColumns.ID.EQ(psql.Arg(o.OperationID))),
	)...)
}

func (os MaterialUsageSlice) Operation(mods ...bob.Mod[*dialect.SelectQuery]) OperationsQuery {
	pkOperationID := make(pgtypes.Array[sql.Null[int64]], len(os))
	for i, o := range os {
		pkOperationID[i] = o.OperationID
	}
	PKArgExpr := psql.Select(sm.Columns(
		psql.F("unnest", psql.Cast(psql.Arg(pkOperationID), "bigint[]")),
	))

	return Operations.Query(append(mods,
		sm.Where(psql.Group(OperationColumns.ID).OP("IN", PKArgExpr)),
	)...)
}

func (o *MaterialUsage) Preload(name string, retrieved any) error {
	if o == nil {
		return nil
	}

	switch name {
	case "Material":
		rel, ok := retrieved.(*Material)
		if !ok {
			return fmt.Errorf("materialUsage cannot load %T as %q", retrieved, name)
		}

		o.R.Material = rel

		if rel != nil {
			rel.R.MaterialUsages = MaterialUsageSlice{o}
		}
		return nil
	case "Operation":
		rel, ok := retrieved.(*Operation)
		if !ok {
			return fmt.Errorf("materialUsage cannot load %T as %q", retrieved, name)
		}

		o.R.Operation = rel

		if rel != nil {
			rel.R.MaterialUsages = MaterialUsageSlice{o}
		}
		return nil
	default:
		return fmt.Errorf("materialUsage has no relationship %q", name)
	}
}

type materialUsagePreloader struct {
	Material  func(...psql.PreloadOption) psql.Preloader
	Operation func(...psql.PreloadOption) psql.Preloader
}

func buildMaterialUsagePreloader() materialUsagePreloader {
	return materialUsagePreloader{
		Material: func(opts ...psql.PreloadOption) psql.Preloader {
			return psql.Preload[*Material, MaterialSlice](orm.Relationship{
				Name: "Material",
				Sides: []orm.RelSide{
					{
						From: TableNames.MaterialUsages,
						To:   TableNames.Materials,
						FromColumns: []string{
							ColumnNames.MaterialUsages.MaterialID,
						},
						ToColumns: []string{
							ColumnNames.Materials.ID,
						},
					},
				},
			}, Materials.Columns().Names(), opts...)
		},
		Operation: func(opts ...psql.PreloadOption) psql.Preloader {
			return psql.Preload[*Operation, OperationSlice](orm.Relationship{
				Name: "Operation",
				Sides: []orm.RelSide{
					{
						From: TableNames.MaterialUsages,
						To:   TableNames.Operations,
						FromColumns: []string{
							ColumnNames.MaterialUsages.OperationID,
						},
						ToColumns: []string{
							ColumnNames.Operations.ID,
						},
					},
				},
			}, Operations.Columns().Names(), opts...)
		},
	}
}

type materialUsageThenLoader[Q orm.Loadable] struct {
	Material  func(...bob.Mod[*dialect.SelectQuery]) orm.Loader[Q]
	Operation func(...bob.Mod[*dialect.SelectQuery]) orm.Loader[Q]
}

func buildMaterialUsageThenLoader[Q orm.Loadable]() materialUsageThenLoader[Q] {
	type MaterialLoadInterface interface {
		LoadMaterial(context.Context, bob.Executor, ...bob.Mod[*dialect.SelectQuery]) error
	}
	type OperationLoadInterface interface {
		LoadOperation(context.Context, bob.Executor, ...bob.Mod[*dialect.SelectQuery]) error
	}

	return materialUsageThenLoader[Q]{
		Material: thenLoadBuilder[Q](
			"Material",
			func(ctx context.Context, exec bob.Executor, retrieved MaterialLoadInterface, mods ...bob.Mod[*dialect.SelectQuery]) error {
				return retrieved.LoadMaterial(ctx, exec, mods...)
			},
		),
		Operation: thenLoadBuilder[Q](
			"Operation",
			func(ctx context.Context, exec bob.Executor, retrieved OperationLoadInterface, mods ...bob.Mod[*dialect.SelectQuery]) error {
				return retrieved.LoadOperation(ctx, exec, mods...)
			},
		),
	}
}

// LoadMaterial loads the materialUsage's Material into the .R struct
func (o *MaterialUsage) LoadMaterial(ctx context.Context, exec bob.Executor, mods ...bob.Mod[*dialect.SelectQuery]) error {
	if o == nil {
		return nil
	}

	// Reset the relationship
	o.R.Material = nil

	related, err := o.Material(mods...).One(ctx, exec)
	if err != nil {
		return err
	}

	related.R.MaterialUsages = MaterialUsageSlice{o}

	o.R.Material = related
	return nil
}

// LoadMaterial loads the materialUsage's Material into the .R struct
func (os MaterialUsageSlice) LoadMaterial(ctx context.Context, exec bob.Executor, mods ...bob.Mod[*dialect.SelectQuery]) error {
	if len(os) == 0 {
		return nil
	}

	materials, err := os.Material(mods...).All(ctx, exec)
	if err != nil {
		return err
	}

	for _, o := range os {
		for _, rel := range materials {
			if o.MaterialID != rel.ID {
				continue
			}

			rel.R.MaterialUsages = append(rel.R.MaterialUsages, o)

			o.R.Material = rel
			break
		}
	}

	return nil
}

// LoadOperation loads the materialUsage's Operation into the .R struct
func (o *MaterialUsage) LoadOperation(ctx context.Context, exec bob.Executor, mods ...bob.Mod[*dialect.SelectQuery]) error {
	if o == nil {
		return nil
	}

	// Reset the relationship
	o.R.Operation = nil

	related, err := o.Operation(mods...).One(ctx, exec)
	if err != nil {
		return err
	}

	related.R.MaterialUsages = MaterialUsageSlice{o}

	o.R.Operation = related
	return nil
}

// LoadOperation loads the materialUsage's Operation into the .R struct
func (os MaterialUsageSlice) LoadOperation(ctx context.Context, exec bob.Executor, mods ...bob.Mod[*dialect.SelectQuery]) error {
	if len(os) == 0 {
		return nil
	}

	operations, err := os.Operation(mods...).All(ctx, exec)
	if err != nil {
		return err
	}

	for _, o := range os {
		for _, rel := range operations {
			if o.OperationID.V != rel.ID {
				continue
			}

			rel.R.MaterialUsages = append(rel.R.MaterialUsages, o)

			o.R.Operation = rel
			break
		}
	}

	return nil
}

func attachMaterialUsageMaterial0(ctx context.Context, exec bob.Executor, count int, materialUsage0 *MaterialUsage, material1 *Material) (*MaterialUsage, error) {
	setter := &MaterialUsageSetter{
		MaterialID: &material1.ID,
	}

	err := materialUsage0.Update(ctx, exec, setter)
	if err != nil {
		return nil, fmt.Errorf("attachMaterialUsageMaterial0: %w", err)
	}

	return materialUsage0, nil
}

func (materialUsage0 *MaterialUsage) InsertMaterial(ctx context.Context, exec bob.Executor, related *MaterialSetter) error {
	material1, err := Materials.Insert(related).One(ctx, exec)
	if err != nil {
		return fmt.Errorf("inserting related objects: %w", err)
	}

	_, err = attachMaterialUsageMaterial0(ctx, exec, 1, materialUsage0, material1)
	if err != nil {
		return err
	}

	materialUsage0.R.Material = material1

	material1.R.MaterialUsages = append(material1.R.MaterialUsages, materialUsage0)

	return nil
}

func (materialUsage0 *MaterialUsage) AttachMaterial(ctx context.Context, exec bob.Executor, material1 *Material) error {
	var err error

	_, err = attachMaterialUsageMaterial0(ctx, exec, 1, materialUsage0, material1)
	if err != nil {
		return err
	}

	materialUsage0.R.Material = material1

	material1.R.MaterialUsages = append(material1.R.MaterialUsages, materialUsage0)

	return nil
}

func attachMaterialUsageOperation0(ctx context.Context, exec bob.Executor, count int, materialUsage0 *MaterialUsage, operation1 *Operation) (*MaterialUsage, error) {
	setter := &MaterialUsageSetter{
		OperationID: func() *sql.Null[int64] {
			v := sql.Null[int64]{V: operation1.ID, Valid: true}
			return &v
		}(),
	}

	err := materialUsage0.Update(ctx, exec, setter)
	if err != nil {
		return nil, fmt.Errorf("attachMaterialUsageOperation0: %w", err)
	}

	return materialUsage0, nil
}

func (materialUsage0 *MaterialUsage) InsertOperation(ctx context.Context, exec bob.Executor, related *OperationSetter) error {
	operation1, err := Operations.Insert(related).One(ctx, exec)
	if err != nil {
		return fmt.Errorf("inserting related objects: %w", err)
	}

	_, err = attachMaterialUsageOperation0(ctx, exec, 1, materialUsage0, operation1)
	if err != nil {
		return err
	}

	materialUsage0.R.Operation = operation1

	operation1.R.MaterialUsages = append(operation1.R.MaterialUsages, materialUsage0)

	return nil
}

func (materialUsage0 *MaterialUsage) AttachOperation(ctx context.Context, exec bob.Executor, operation1 *Operation) error {
	var err error

	_, err = attachMaterialUsageOperation0(ctx, exec, 1, materialUsage0, operation1)
	if err != nil {
		return err
	}

	materialUsage0.R.Operation = operation1

	operation1.R.MaterialUsages = append(operation1.R.MaterialUsages, materialUsage0)

	return nil
}
