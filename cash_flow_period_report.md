# Cash Flow Period Report API Documentation

## Endpoint: `/v1/cash-flow/period-report`

**Method:** `POST`  
**Authentication:** Required (JWT Token)

### Mô tả
Endpoint này trả về báo cáo cash flow theo khoảng thời gian (tuần/tháng/năm) bao gồm khoảng thời gian hiện tại và 3 khoảng thời gian liền kề trước đó để so sánh.

---

## Request Body

### Cấu trúc Request
```json
{
    "period_type": "week|month|year",
    "date": "YYYY-MM-DD"
}
```

### Parameters

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `period_type` | string | Yes | Loại khoảng thời gian: `week`, `month`, hoặc `year` |
| `date` | string | Yes | Ngày tham chiếu theo format `YYYY-MM-DD` |

### Cách hoạt động của `date`

**Quan trọng:** `date` luôn sử dụng format `YYYY-MM-DD` bất kể `period_type` là gì. Hệ thống sẽ tự động xác định khoảng thời gian chứa ngày đó.

#### Đối với `period_type = "week"`
```json
{
    "period_type": "week",
    "date": "2024-07-20"
}
```
- Hệ thống tìm tuần chứa ngày 20/07/2024 (tuần 17-23/07/2024)
- Trả về: Tuần hiện tại + 3 tuần trước

#### Đối với `period_type = "month"`
```json
{
    "period_type": "month", 
    "date": "2024-07-15"
}
```
- Hệ thống tìm tháng chứa ngày 15/07/2024 (tháng 07/2024)
- Trả về: Tháng hiện tại + 3 tháng trước

#### Đối với `period_type = "year"`
```json
{
    "period_type": "year",
    "date": "2024-07-15" 
}
```
- Hệ thống tìm năm chứa ngày 15/07/2024 (năm 2024)
- Trả về: Năm hiện tại + 3 năm trước

---

## Response Body

### Cấu trúc Response
```json
{
    "current_period": {
        "period": "string",
        "period_label": "string", 
        "start_date": "YYYY-MM-DD",
        "end_date": "YYYY-MM-DD",
        "total_income": 0,
        "total_expense": 0,
        "net_amount": 0,
        "transaction_count": 0,
        "total_cash": 0,
        "total_credit_card": 0,
        "total_mpos": 0,
        "total_bank": 0,
        "total_momo": 0,
        "income_by_category": [...],
        "expense_by_category": [...]
    },
    "previous_periods": [...], // Array of 3 PeriodSummary objects
    "period_type": "week|month|year",
    "generated_at": "2024-07-20T10:30:00Z"
}
```

### Chi tiết các field trong `PeriodSummary`

| Field | Type | Description |
|-------|------|-------------|
| `period` | string | Mã định danh khoảng thời gian (vd: "2024-W30", "2024-07", "2024") |
| `period_label` | string | Nhãn hiển thị cho user (vd: "Tuần 30 (17-23/07/2024)") |
| `start_date` | string | Ngày bắt đầu khoảng thời gian |
| `end_date` | string | Ngày kết thúc khoảng thời gian |
| `total_income` | int | Tổng thu nhập (đơn vị: VND) |
| `total_expense` | int | Tổng chi tiêu (đơn vị: VND) |
| `net_amount` | int | Số dư ròng (thu nhập - chi tiêu) |
| `transaction_count` | int | Tổng số giao dịch |
| `total_cash` | int | Tổng tiền mặt |
| `total_credit_card` | int | Tổng thẻ tín dụng |
| `total_mpos` | int | Tổng MPOS |
| `total_bank` | int | Tổng chuyển khoản |
| `total_momo` | int | Tổng MoMo |
| `income_by_category` | array | Phân tích thu nhập theo danh mục |
| `expense_by_category` | array | Phân tích chi tiêu theo danh mục |

---

## Ví dụ thực tế

### 1. Xem báo cáo tuần hiện tại
```javascript
// Request
const request = {
    period_type: "week",
    date: "2024-07-20" // Tuần 17-23/07/2024
};

// Response
{
    "current_period": {
        "period": "2024-W30",
        "period_label": "Tuần 30 (17-23/07/2024)",
        "start_date": "2024-07-17",
        "end_date": "2024-07-23",
        "total_income": ********,
        "total_expense": 8000000,
        "net_amount": 7000000,
        "transaction_count": 25,
        // ... other fields
    },
    "previous_periods": [
        {
            "period": "2024-W29",
            "period_label": "Tuần 29 (10-16/07/2024)",
            // ... data for week 29
        },
        {
            "period": "2024-W28", 
            "period_label": "Tuần 28 (03-09/07/2024)",
            // ... data for week 28
        },
        {
            "period": "2024-W27",
            "period_label": "Tuần 27 (26/06-02/07/2024)", 
            // ... data for week 27
        }
    ],
    "period_type": "week",
    "generated_at": "2024-07-20T10:30:00Z"
}
```

### 2. Xem báo cáo tháng
```javascript
// Request
const request = {
    period_type: "month",
    date: "2024-07-01" // Tháng 07/2024
};

// Response  
{
    "current_period": {
        "period": "2024-07",
        "period_label": "Tháng 7/2024",
        "start_date": "2024-07-01",
        "end_date": "2024-07-31",
        // ... data
    },
    "previous_periods": [
        {
            "period": "2024-06",
            "period_label": "Tháng 6/2024",
            // ... data for June 2024
        },
        {
            "period": "2024-05",
            "period_label": "Tháng 5/2024", 
            // ... data for May 2024
        },
        {
            "period": "2024-04",
            "period_label": "Tháng 4/2024",
            // ... data for April 2024  
        }
    ],
    "period_type": "month",
    "generated_at": "2024-07-20T10:30:00Z"
}
```

### 3. Xem báo cáo năm
```javascript
// Request
const request = {
    period_type: "year",
    date: "2024-07-01" // Năm 2024
};

// Response
{
    "current_period": {
        "period": "2024", 
        "period_label": "Năm 2024",
        "start_date": "2024-01-01",
        "end_date": "2024-12-31",
        // ... data
    },
    "previous_periods": [
        {
            "period": "2023",
            "period_label": "Năm 2023",
            // ... data for 2023
        },
        {
            "period": "2022",
            "period_label": "Năm 2022",
            // ... data for 2022  
        },
        {
            "period": "2021",
            "period_label": "Năm 2021",
            // ... data for 2021
        }
    ],
    "period_type": "year",
    "generated_at": "2024-07-20T10:30:00Z"
}
```

---

## Gợi ý implement Frontend

### 1. Date Picker cho từng period_type

```javascript
// Đối với week: user chọn bất kỳ ngày nào trong tuần
const weekRequest = {
    period_type: "week",
    date: selectedDate.toISOString().split('T')[0] // YYYY-MM-DD
};

// Đối với month: user chọn tháng/năm, FE convert thành ngày đầu tháng  
const monthRequest = {
    period_type: "month",
    date: `${year}-${month.padStart(2, '0')}-01` // vd: "2024-07-01"
};

// Đối với year: user chọn năm, FE convert thành ngày đầu năm
const yearRequest = {
    period_type: "year", 
    date: `${year}-01-01` // vd: "2024-01-01"
};
```

### 2. Display Data

```javascript
// Hiển thị period_label thay vì period cho user-friendly
<h3>{response.current_period.period_label}</h3>

// Tạo chart/table so sánh với previous periods
const chartData = [
    response.current_period,
    ...response.previous_periods
].map(period => ({
    label: period.period_label,
    income: period.total_income,
    expense: period.total_expense,
    net: period.net_amount
}));
```

---

## Error Handling

### Các lỗi có thể xảy ra:

1. **Invalid period_type**: Truyền period_type không hợp lệ
2. **Invalid date format**: Date không đúng format YYYY-MM-DD  
3. **Date too far in future**: Ngày quá xa trong tương lai
4. **Authentication failed**: Token không hợp lệ

### Response lỗi:
```json
{
    "error": "invalid_period_type",
    "message": "period_type must be one of: week, month, year"
}
``` 