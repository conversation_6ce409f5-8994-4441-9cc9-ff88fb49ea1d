**Guideline Phát Triển và Refactor Module (Strict Standard - Dựa trên `tag` module)**

**M<PERSON><PERSON> Tiêu:**

*   Xây dựng module mới theo đúng kiến trúc chuẩn: `Logic` -> `Model` -> `Ent`.
*   <PERSON><PERSON><PERSON> bảo tính nhất quán tuyệt đối về Error <PERSON>ling, Logging, Data Conversion.
*   Tối đa hóa tái sử dụng Core Logic và tách biệt rõ ràng trách nhiệm các layer.
*   Code sạch, dễ bảo trì, dễ kiểm thử.

**Nguyên Tắc Cốt Lõi:**

1.  **Layer `logic` (Controller/Adapter/Orchestrator):**
    *   Chịu trách nhiệm xử lý API request/response flow, Authorization.
    *   **Điều phối (orchestrate)** các lời gọi đến một hoặc nhiều `Model` khác nhau để hoàn thành một yêu cầu nghiệ<PERSON> vụ.
    *   **Fetching Data từ Multiple Models:** Logic layer chịu trách nhiệm fetch data từ các model khác nhau và truyền cho model chính xử lý.
    *   **KHÔNG** chứa business logic.
    *   **KHÔNG** gọi `ent` trực tiếp.

2.  **Layer `model` (Service/Repository):**
    *   Chứa **toàn bộ** Core Business Logic và Data Access Logic **liên quan trực tiếp đến thực thể (entity) mà nó quản lý**.
    *   **Nhận data từ tham số thay vì tự fetch:** Model method nên nhận data cần thiết từ tham số thay vì tự fetch từ model khác.
    *   **Method Design:** Cung cấp cả method cho single entity và multiple entities khi cần thiết.
    *   Giao tiếp qua DTOs.
    *   **KHÔNG** phụ thuộc HTTP context hay `types`.
    *   **CẤM TUYỆT ĐỐI:** Gọi sang một `Model` khác (ví dụ: `XyzModel` không được gọi `AbcModel`).

3.  **Error Handling:** Chỉ sử dụng các lỗi chuẩn từ `common/berr`. `Model` layer wrap và trả về `*berr.CodeError`. `Logic` layer chỉ việc trả lỗi đó ra.

4.  **Logging:** Rất hạn chế và có chủ đích. `Model` log hành động thay đổi dữ liệu thành công. `bresult` log lỗi chi tiết.

5.  **Data Conversion:** Sử dụng nhất quán các helper trong `common/cast`.

**Tham Chiếu BẮT BUỘC:**

*   **Model Layer (Logic nghiệp vụ & Data Access):** Tham khảo cấu trúc và cách implement trong `api/internal/model/example.xml` và `api/internal/model/deal_model.go`.
*   **Logic Layer (API Flow & Orchestration):** Tham khảo cách implement trong `api/internal/logic/tag/example.xml` và `api/internal/logic/deal/`.
*   **Error Types & Usage:** Tham khảo định nghĩa lỗi và cách dùng `Wrap`/`Op` trong `common/berr/errors.go`.
*   **Ent Schema:** Tham khảo schema của module tương ứng tại `ent/schema/module_name.go` để biết cấu trúc dữ liệu và quan hệ.
*   **API Definitions:** Tham khảo file định nghĩa API tại `api/doc/module_name.api` để biết cấu trúc request/response (`types.go` được auto-generate từ đây).

**Quy Trình Phát Triển Module Mới (Strict)**

*(Giả sử Ent schema tại `ent/schema/xyz.go` đã tồn tại)*

1.  **Bước 1: Định nghĩa DTOs (`api/internal/dto/xyz_dto.go`)**
    *   **BẮT BUỘC:** Tạo các struct `*Input` và `*Output` DTOs (ví dụ: `CreateXyzInput`, `XyzOutput`, `ListXyzInput`, `ListXyzOutput`,...).
    *   **Mô phỏng:** Cấu trúc và mục đích phải **hoàn toàn tương tự** như trong `api/internal/model/example.xml`.
    *   **Nguyên tắc DTO:** Dùng con trỏ cho các trường update trong `Update*Input`, bao gồm `Modified []string`. `*Output` DTO phản ánh dữ liệu cốt lõi trả về.
    *   **Relations trong DTO:** Khi entity có relations, thêm các field tương ứng trong `*Output` DTO (ví dụ: `Plans []InstallmentPlanOutput`).

2.  **Bước 2: Implement Model Layer (`api/internal/model/xyz_model.go`)**
    *   **Struct & Constructor:** Tạo `XyzModel` và `NewXyzModel` theo đúng cấu trúc tham chiếu trong `api/internal/model/example.xml` (bao gồm Logger, DbClient).
    *   **Implement Methods:**
        *   **Signature & `op`:** Tuân thủ signature chuẩn và khai báo `op := "XyzModel.MethodName"` ở đầu mỗi method.
        *   **Input Mapping:** Dùng `cast.InputToEnt`. Xử lý lỗi bằng `berr.ErrCopyFailed.Wrap(err).Op(op)`.
        *   **Business Logic:** **Toàn bộ** logic nghiệp vụ và validation **liên quan trực tiếp đến thực thể `Xyz`** PHẢI đặt tại đây.
        *   **Method Design Patterns:**
            *   **Single vs Multiple:** Cung cấp cả method cho single entity (`EnrichEntityWithRelations(entity, relatedData)`) và multiple entities (`EnrichEntitiesWithRelations(entities, relatedData)`).
            *   **Data Dependencies:** Method nên nhận related data từ tham số thay vì tự fetch (ví dụ: `MapDealsWithRelations(deals, discountUsages)` thay vì tự fetch discountUsages).
            *   **Helper Methods:** Tạo helper methods để xử lý logic phức tạp và tái sử dụng (ví dụ: `mapEntityRelations`, `calculateFinancials`).
        *   **Error Handling (DB & Logic):**
            *   **BẮT BUỘC:** Mọi lỗi trả về PHẢI là `*berr.CodeError` đã được `.Wrap(err)` và `.Op(op)`.
            *   **BẮT BUỘC:** Sử dụng các lỗi `berr` chuẩn (tham khảo `common/berr/errors.go` và cách dùng trong `tag_model.go`). **KHÔNG** định nghĩa lỗi mới dạng `var Err...`.
            *   Xử lý lỗi `ent` theo các `berr` tương ứng (NotFound, DBError, CreateFailed, UpdateFailed, DeleteFailed, ListFailed, DuplicateEntry...).
        *   **Logging:**
            *   **BẮT BUỘC:** Chỉ log ở mức `Info` sau khi **thành công** các thao tác CUD (Create, Update, Delete) quan trọng. Nội dung log PHẢI bao gồm `op`, entity ID, user ID (nếu có). Ví dụ: `m.Logger.WithContext(ctx).Infof("%s - Xyz %d action by user %d", op, entityID, userID)`.
            *   **BẮT BUỘC:** Chỉ log ở mức `Error` khi xử lý lỗi `cast.ConvertViaJson` (Ent -> Output DTO).
            *   **CẤM:** Log lỗi trước khi trả về lỗi đó (việc này `bresult` sẽ làm).
            *   **CẤM:** Log các bước xử lý logic thông thường.
        *   **Output Mapping:** Dùng `cast.ConvertViaJson`. Xử lý lỗi như mô tả ở trên (Log Error, trả về `berr.ErrCopyFailed`).
        *   **QUY TẮC QUAN TRỌNG:** **CẤM TUYỆT ĐỐI** gọi sang Model khác từ bên trong một Model. Mọi tương tác giữa các model phải được điều phối ở `Logic` layer.

3.  **Bước 3: Tích hợp Model vào ServiceContext (`api/internal/svc/service_context.go`)**
    *   **BẮT BUỘC:** Thêm field `XyzModel *model.XyzModel`.
    *   **BẮT BUỘC:** Khởi tạo trong `NewServiceContext`.

4.  **Bước 4: Implement Logic Layer (`api/internal/logic/xyz/..._logic.go`)**
    *   **Trách nhiệm:** Authorization, điều phối Model, mapping Request/Response, data fetching orchestration.
    *   **Authorization:** Kiểm tra quyền bằng `svcCtx.Auth` **ngay đầu** hàm. Trả về `berr.ErrPermissionDenied` nếu không có quyền.
    *   **Input Mapping:** Dùng `cast.ReqToModelInput`. Xử lý lỗi bằng `berr.ErrRequestParam`.
    *   **Data Orchestration Patterns:**
        *   **Sequential Fetching:** Fetch data từ multiple models một cách tuần tự khi cần thiết.
        *   **Parallel Fetching:** Sử dụng goroutines để fetch data parallel khi có thể.
        *   **Data Aggregation:** Tổng hợp data từ nhiều sources trước khi truyền cho model chính xử lý.
        *   **Example Pattern:**
            ```go
            // 1. Fetch main entities
            entities, err := l.svcCtx.MainModel.ListEntities(ctx, input)
            
            // 2. Extract IDs for related data
            entityIDs := extractIDs(entities)
            
            // 3. Fetch related data từ other models
            relatedData, err := l.svcCtx.RelatedModel.ListByEntityIDs(ctx, entityIDs)
            
            // 4. Pass to main model for processing
            err = l.svcCtx.MainModel.EnrichEntitiesWithRelations(entities, relatedData)
            ```
    *   **Điều Phối (Orchestration) Giữa Các Model:**
        *   Đây là nơi duy nhất được phép gọi đến nhiều `Model` khác nhau để hoàn thành một tác vụ.
        *   **Anti-pattern:** Model A gọi Model B.
        *   **Correct pattern:** Logic layer gọi Model A và Model B, sau đó tổng hợp kết quả.
    *   **Error Handling:** **KHÔNG** wrap lỗi trả về từ Model. Chỉ `if err != nil { return nil, err }`.
    *   **Output Mapping:** Dùng `cast.ModelOutputToResp` (hoặc `cast.ConvertViaJson`). Xử lý lỗi bằng `berr.ErrServerCommon`.
    *   **Logging:** **CẤM** log nghiệp vụ hoặc lỗi ở layer này.
    *   **Transaction:** Nếu cần transaction bao nhiều model calls, dùng `bquery.WithTx` tại đây và truyền `*ent.Tx` xuống các phương thức Model liên quan.

5.  **Bước 5: Định nghĩa Authorization Rules**
    *   Implement các check `Can...` cụ thể cho module trong `api/internal/auth/` theo chuẩn mực như `api/internal/auth/tag.go`

**PATTERNS REFACTORING CHO MODULE CŨ:**

1.  **Tách Biệt Data Fetching:**
    *   **Before:** Model tự fetch data từ model khác
    *   **After:** Logic layer fetch data và truyền vào Model method
    
2.  **Method Signature Optimization:**
    *   **Before:** `EnrichWithRelations(ctx, entities)` - Model tự fetch related data
    *   **After:** `EnrichWithRelations(entities, relatedData)` - Nhận related data từ tham số
    
3.  **Single vs Multiple Entity Methods:**
    *   Cung cấp cả single: `EnrichEntityWithRelations(entity, relatedData)`
    *   Và multiple: `EnrichEntitiesWithRelations(entities, relatedData)`
    
4.  **Helper Method Extraction:**
    *   Tách logic phức tạp thành helper methods trong cùng Model
    *   Tái sử dụng logic chung giữa single và multiple entity methods

5.  **DTO Enhancement:**
    *   Thêm relation fields vào Output DTOs khi cần thiết
    *   Đảm bảo DTO structure phản ánh đúng business requirements

**QUY TẮC NGHIÊM NGẶT DÀNH CHO LLM KHI REFACTOR/GENERATE CODE:**

*   **CẤM TUYỆT ĐỐI** để lại bất kỳ dòng comment nào trong code được generate hoặc refactor. Code phải tự diễn giải thông qua cấu trúc, tên biến, tên hàm rõ ràng và tuân thủ guideline này.
*   Luôn tuân thủ việc sử dụng `berr`, `cast`, `op` và các quy tắc logging đã nêu.
*   Luôn tham chiếu đến code mẫu trong các file `example.xml` và `deal_model.go`.
*   **Luôn đảm bảo một `Model` không gọi sang `Model` khác.** Việc điều phối phải được thực hiện ở `Logic` layer.
*   **Ưu tiên data orchestration ở Logic layer** thay vì để Model tự fetch data.
*   **Thiết kế method signature nhận data từ tham số** thay vì tự fetch trong method.

---
**Checklist Nhanh:**

*   [ ] DTOs (`dto/*_dto.go`) đã được định nghĩa chuẩn với relation fields khi cần thiết?
*   [ ] Model (`model/*_model.go`) implement đúng cấu trúc, có Logger, DbClient?
*   [ ] Model methods có `op`, dùng `cast`, xử lý lỗi bằng `berr` chuẩn + `Wrap` + `Op`?
*   [ ] Model methods nhận related data từ tham số thay vì tự fetch?
*   [ ] Model có cả single và multiple entity methods khi cần thiết?
*   [ ] Model chỉ log CUD thành công (Info) và lỗi convert output (Error)?
*   [ ] **Model KHÔNG gọi chéo sang model khác?**
*   [ ] Model đã được thêm vào ServiceContext?
*   [ ] Logic (`logic/*/*_logic.go`) có check Auth, map DTOs, orchestrate data fetching, gọi Model, map Response?
*   [ ] **Logic thực hiện data orchestration (fetch từ multiple models) trước khi truyền cho Model?**
*   [ ] Logic KHÔNG chứa business logic, KHÔNG gọi Ent, KHÔNG log thừa, KHÔNG wrap lỗi từ Model?
*   [ ] Code KHÔNG chứa bất kỳ comment nào?
*   [ ] **Refactoring tuân thủ patterns: data fetching separation, method signature optimization, helper extraction?**