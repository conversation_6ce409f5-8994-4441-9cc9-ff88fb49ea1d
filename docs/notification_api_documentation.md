# Notification System Documentation

## Overview
The notification system provides real-time user notifications with support for various entity types, flexible categorization, and WebSocket delivery. Built on event-driven architecture with proper authorization and persistence.

## API Endpoints

All endpoints require JWT authentication (`Bearer token`) and use prefix `/v1/notification`.

### 1. Create Notification
```http
POST /v1/notification/add
Authorization: Bearer <token>
Content-Type: application/json

{
  "user_id": 123,
  "type": "task_assigned",
  "message": "Bạn đã được phân công task mới: Khám răng định kỳ",
  "entity_id": 456,
  "entity_type": "task",
  "sender_id": 789,
  "metadata": {
    "task_name": "Kh<PERSON>m răng định kỳ",
    "due_date": "2024-01-15",
    "priority": "high"
  }
}
```

### 2. List Notifications
```http
POST /v1/notification/list
{
  "user_id": 123,
  "page": 1,
  "limit": 20,
  "is_read": false,
  "sort_by": "created_at",
  "order": "desc"
}
```

### 3. Get Unread Count
```http
POST /v1/notification/unread-count
{
  "user_id": 123
}
```

### 4. Mark Notifications as Read
```http
POST /v1/notification/read
{
  "user_id": 123,
  "notification_ids": [1, 2, 3]
}
```

### 5. Mark All as Read
```http
POST /v1/notification/read-all
{
  "user_id": 123
}
```

### 6. Delete Notification
```http
POST /v1/notification/delete
{
  "user_id": 123,
  "notification_id": 456
}
```

## Data Models

### Notification Object
```typescript
interface Notification {
  id: number;
  user_id: number;
  type: string;                    // e.g., "task_assigned", "deal_stage_changed"
  message: string;                 // Pre-rendered Vietnamese message
  entity_id?: number;              // Related entity ID (deal, task, etc.)
  entity_type?: string;            // "deal", "task", "person", etc.
  is_read: boolean;
  created_at: string;              // ISO timestamp
  read_at?: string;                // ISO timestamp when marked as read
  sender_id?: number;              // User who triggered the notification
  metadata?: Record<string, any>;  // Additional data for FE processing
}
```

## Notification Types

### Predefined Categories
```typescript
type NotificationType = 
  | "info"        // General information
  | "success"     // Positive outcomes
  | "warning"     // Attention needed
  | "reminder"    // Time-based alerts
  | "assignment"  // Work assignments
  | "update"      // Entity changes
  | "alert";      // Urgent notifications
```

### Entity Types
```typescript
type EntityType = 
  | "deal"
  | "task"
  | "person"
  | "appointment"
  | "bill"
  | "payment"
  | "attachment";
```

### Semantic Types (Recommended)
Use entity + action pattern for specific notification types:
- `task_assigned` → Assignment category
- `deal_stage_changed` → Update category  
- `appointment_reminder` → Reminder category
- `payment_overdue` → Alert category

## Real-time WebSocket Events

When notifications are created, users automatically receive WebSocket messages:

```typescript
// WebSocket event structure
{
  event: "notification",
  data: {
    id: 123,
    type: "task_assigned",
    message: "Bạn đã được phân công task mới",
    entity_id: 456,
    entity_type: "task",
    is_read: false,
    metadata: { /* additional data */ },
    created_at: "2024-01-15T10:30:00Z"
  }
}
```

## Frontend Implementation Guide

### 1. WebSocket Connection
```javascript
// Connect to WebSocket for real-time notifications
const ws = new WebSocket('wss://your-domain/ws');
ws.onmessage = (event) => {
  const message = JSON.parse(event.data);
  if (message.event === 'notification') {
    handleNewNotification(message.data);
  }
};
```

### 2. Notification Management
```javascript
// Fetch notifications with pagination
async function fetchNotifications(page = 1, unreadOnly = false) {
  const response = await fetch('/v1/notification/list', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      user_id: currentUserId,
      page,
      limit: 20,
      is_read: unreadOnly ? false : undefined
    })
  });
  return response.json();
}

// Mark notifications as read
async function markAsRead(notificationIds) {
  await fetch('/v1/notification/read', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      user_id: currentUserId,
      notification_ids: notificationIds
    })
  });
}
```

### 3. UI Components
```jsx
// Notification badge with unread count
function NotificationBadge() {
  const [unreadCount, setUnreadCount] = useState(0);
  
  useEffect(() => {
    fetchUnreadCount();
  }, []);
  
  return (
    <div className="notification-badge">
      <BellIcon />
      {unreadCount > 0 && (
        <span className="badge">{unreadCount}</span>
      )}
    </div>
  );
}

// Notification list with categorization
function NotificationList({ notifications }) {
  const getCategoryStyle = (type) => {
    if (type.includes('assignment')) return 'blue';
    if (type.includes('success') || type.includes('complete')) return 'green';
    if (type.includes('alert') || type.includes('overdue')) return 'red';
    if (type.includes('reminder')) return 'orange';
    return 'gray';
  };
  
  return (
    <div className="notification-list">
      {notifications.map(notification => (
        <div 
          key={notification.id}
          className={`notification-item ${!notification.is_read ? 'unread' : ''}`}
          onClick={() => handleNotificationClick(notification)}
        >
          <div className={`category-indicator ${getCategoryStyle(notification.type)}`} />
          <div className="content">
            <p>{notification.message}</p>
            <span className="timestamp">{formatTime(notification.created_at)}</span>
          </div>
        </div>
      ))}
    </div>
  );
}
```

## Best Practices

### 1. Message Formatting
- Messages are pre-rendered in Vietnamese on the backend
- Include entity names and context for clarity
- Keep messages concise but informative

### 2. Metadata Usage
Use metadata for:
- Deep linking: `{"route": "/deals/123", "tab": "details"}`
- Additional context: `{"priority": "high", "due_date": "2024-01-15"}`
- UI hints: `{"color": "red", "icon": "warning"}`

### 3. Performance Optimization
- Implement pagination for notification lists
- Use WebSocket for real-time updates instead of polling
- Cache unread count locally and update via WebSocket
- Mark notifications as read when user views them

### 4. Error Handling
```javascript
try {
  await markAsRead(notificationIds);
} catch (error) {
  console.error('Failed to mark notifications as read:', error);
  // Show user-friendly error message
}
```

### 5. Accessibility
- Provide keyboard navigation for notification list
- Use semantic HTML and ARIA labels
- Support screen readers for notification content
- Include visual indicators for notification categories

### BELOW IS THE REAL API TYPES IN BCARE-V2 AND BCARE-TYPES-V2

/**
* @description
* @param req
  */
  export function notificationAdd(req: types.NotificationAddRequest) {
  return service.post<types.GenericResponse<types.NotificationResponse>>(`/v1/notification/add`, req)
  }

/**
* @description
* @param req
  */
  export function notificationDelete(req: types.DeleteNotificationReq) {
  return service.post<null>(`/v1/notification/delete`, req)
  }

/**
* @description "Lấy danh sách thông báo cho người dùng đã đăng nhập"
* @param req
  */
  export function notificationList(req: types.ListNotificationsReq) {
  return service.post<types.GenericResponse<types.ListNotificationsResp>>(`/v1/notification/list`, req)
  }

/**
* @description
* @param req
  */
  export function notificationMarkAsRead(req: types.MarkNotificationsAsReadReq) {
  return service.post<null>(`/v1/notification/read`, req)
  }

/**
* @description
* @param req
  */
  export function notificationMarkAllAsRead(req: types.MarkAllNotificationsAsReadReq) {
  return service.post<null>(`/v1/notification/read-all`, req)
  }

/**
* @description
* @param req
  */
  export function notificationGetUnread(req: types.GetUnreadNotificationCountReq) {
  return service.post<types.GenericResponse<types.GetUnreadNotificationCountResp>>(`/v1/notification/unread-count`, req)
  }

/**
* @description
* @param req
  */
  export function operationAdd(req: types.OperationAddRequest) {
  return service.post<types.GenericResponse<types.OperationResponse>>(`/v1/operation/add`, req)
  }

export interface GetUnreadNotificationCountReq {
user_id: number
}

export interface GetUnreadNotificationCountResp {
unread_count: number
}

export interface ListNotificationsReq {
user_id: number
page?: number
limit?: number
is_read?: boolean
sort_by?: string
order?: string
}

export interface ListNotificationsResp {
data: Array<Notification>
total: number
total_page: number
}


export interface MarkAllNotificationsAsReadReq {
user_id: number
}

export interface MarkNotificationsAsReadReq {
user_id: number
notification_ids: Array<number>
}


export interface Notification {
id: number
user_id: number
type: string
message: string
entity_id?: number
entity_type?: string
is_read: boolean
created_at: string
read_at?: string
sender_id?: number
metadata?: { [key: string]: any }
}

export interface NotificationAddRequest {
user_id: number
type: string
message: string
entity_id?: number
entity_type?: string
sender_id?: number
metadata?: { [key: string]: any }
}

export interface NotificationResponse {
id: number
user_id: number
type: string
message: string
entity_id?: number
entity_type?: string
is_read: boolean
created_at: string
read_at?: string
sender_id?: number
metadata?: { [key: string]: any }
}
